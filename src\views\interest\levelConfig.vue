<template>
  <div class="app-container">
    <!--查询-->
    <div style="margin-bottom: 30px;"></div>
    <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">{{ textMap.create }}</el-button>
    <div style="margin-bottom: 30px;"></div>
    <!--列表-->
    <el-table style="width: 100%" :data="tableData" v-loading.body="tableLoading" element-loading-text="Loading" border
      fit highlight-current-row>
      <el-table-column prop="id" label="id"></el-table-column>
      <el-table-column prop="minimum" label="最小词数"></el-table-column>
      <el-table-column label="操作">
        <template slot-scope="scope">
          <el-tooltip content="编辑" placement="top">
            <el-button @click="handleUpdate(scope.$index, scope.row)" size="medium" type="info" icon="el-icon-edit" circle
              plain></el-button>
          </el-tooltip>
          <el-tooltip content="删除" placement="top" v-if="!hasAdminRole(scope.row)">
            <el-button @click="handleDelete(scope.$index, scope.row)" size="medium" type="danger" icon="el-icon-delete"
              circle plain></el-button>
          </el-tooltip>
          <el-popover trigger="hover" placement="top" v-else style="display: inline-block;">
            <el-alert type="warning" :closable="false" title="权限说明">
              <div>为保证管理员在系统中的最高权限</div>
              <div>不允许编辑管理员自身的权限</div>
              <div>不允许删除管理员角色</div>
            </el-alert>
            <div slot="reference">
              <el-tag style="margin-left: 10px;" type="info">权限说明</el-tag>
            </div>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column prop="maximum" label="最大词数"></el-table-column>
      <el-table-column prop="levelCount" label="关数"></el-table-column>
    </el-table>
    <div style="margin-bottom: 30px;"></div>
    <!--弹出窗口：编辑角色-->
    <el-dialog :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible" width="30%" :close-on-click-modal='false'>
      <el-form :rules="rules" ref="dataForm" :model="temp" label-position="left" label-width="150px">
        <el-form-item label="最小词数" prop="minimum">
          <el-input v-model="temp.minimum" type="number" @blur="minInteger(temp.minimum)"></el-input>
        </el-form-item>
        <el-form-item label="最大词数" prop="maximum">
          <el-input v-model="temp.maximum" type="number" @blur="maxInteger(temp.maximum)"></el-input>
        </el-form-item>
        <el-form-item label="关卡数 (最大8关)" prop="levelCount">
          <el-input v-model="temp.levelCount" type="number" @blur="leveCount(temp.levelCount)"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button v-if="dialogStatus == 'create'" type="primary" @click="createData">创建</el-button>
        <el-button v-else type="primary" @click="updateData">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>

import levelApi from '@/api/levelConfig'
import { parseTime, resetTemp } from '@/utils'
import { pageParamNames, confirm, root } from '@/utils/constants'
export default {
  name: 'funReviewConfig',
  data() {
    return {
      tableLoading: false,
      tableData: [],
      dialogFormVisible: false,
      dialogStatus: '',
      temp: {
        idx: null,
        minimum: null,
        maximum: null,
        levelCount: null,
      },
      textMap: {
        update: '编辑配置',
        create: '新增配置'
      },
      rules: {
        minimum: [{ required: true, message: '必填', trigger: 'blur' }],
        maximum: [{ required: true, message: '必填', trigger: 'blur' }],
        levelCount: [{ required: true, message: '必填', trigger: 'blur' }]
      },
    }
  },

  created() {
    this.fetchData()
  },

  methods: {
    parseTime,
    hasAdminRole(row) {
      return row && row.rval == root.rval
    },
    //查询
    fetchData() {
      this.tableLoading = true
      levelApi.levelList().then(res => {
        this.tableData = res.data.data    //获取数据并且倒叙
        this.tableLoading = false
      })
    },

    //新增
    handleCreate() {
      resetTemp(this.temp)
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => this.$refs['dataForm'].clearValidate())
    },
    createData() {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) return;
        levelApi.addConfig(this.temp).then((res) => {
          if (res.success) {
            this.$message.success("添加成功");
            this.dialogFormVisible = false
            this.fetchData();
          }
        })
      })
    },
    //判断关数
    leveCount(e) {
      if (e !== '' && e !== undefined && e !== "") {
        let boolean = new RegExp("^[1-9][0-9]*$").test(e);
        if (!boolean) {
          this.$message.warning("请输入正整数");
          this.temp.levelCount = "";
        } else {
          if (e > 8) {
            this.$message.warning("请输入小于8的正整数");
            this.temp.levelCount = "";
          }
        }

      }
    },

    //判断正整数
    minInteger(e) {
      if (e !== '' && e !== undefined && e !== "") {
        let boolean = new RegExp("^[1-9][0-9]*$").test(e);
        if (!boolean) {
          this.$message.warning("请输入正整数");
          this.temp.minimum = "";
        }
      }
    },

    //判断正整数
    maxInteger(e) {
      if (e !== '' && e !== undefined && e !== "") {
        let boolean = new RegExp("^[1-9][0-9]*$").test(e);
        if (!boolean) {
          this.$message.warning("请输入正整数");
          this.temp.maximum = "";
        }
      }
    },

    BlurText1(e) {
      if (e != undefined && e != '' && e != "" && e != null) {
        let boolean = new RegExp("^[1-9][0-9]*$").test(e);
        if (!boolean) {
          this.$message.warning("请输入正整数");
          this.form.waringNumber = "";
        };
      }
    },
    //更新
    handleUpdate(idx, row) {
      this.temp = Object.assign({}, row) // copy obj
      this.temp.idx = idx
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => this.$refs['dataForm'].clearValidate())
    },
    updateData() {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) return;
        const tempData = Object.assign({}, this.temp)//copy obj
        levelApi.updateConfig(tempData).then(res => {
          this.tableData.splice(tempData.idx, 1, tempData)
          this.dialogFormVisible = false
          this.$message.success('更新角色信息成功')
        })
      })
    },

    // 删除
    handleDelete(idx, row) {
      this.$confirm('确定要删除配置吗？', '提示', confirm).then(() => {
        levelApi.deleteConfig(row.id).then(res => {
          this.tableData.splice(idx, 1)
          this.dialogFormVisible = false
          this.$message.success('删除成功')
        })
      }).catch(() => {
        this.$message.info('已取消操作')
      })
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped></style>
