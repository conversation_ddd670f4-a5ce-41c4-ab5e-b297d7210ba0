<template>
  <div class="app-container">
      <el-switch
        v-model="isSelfDeliver"
        inactive-text="自行交付"
        @change="saveorupdate()"
      >
      </el-switch>
  </div>
</template>

<script>
import schooldeliverconfigApi from "@/api/schooldeliverconfig";

export default {
  name: "deliverConfig",
  data(){
    return {
      isSelfDeliver: false,
      merchantCode: "",

    };
  },
  created() {
    this.getIsSelfDeliver();
  },
  methods: {
    getIsSelfDeliver() {
      schooldeliverconfigApi.getIsSelfDeliver().then(res => {
        if (res.success){
          this.isSelfDeliver = res.data
        }
      }).catch(err =>{})
    },
    saveorupdate(){
      let loginMerchantCode = window.localStorage.getItem("loginMerchantCode");
      schooldeliverconfigApi.saveorupdate(loginMerchantCode,this.isSelfDeliver).then(res => {
        if(res.success){
          this.$message.success("成功");
        }
      }).catch(err =>{})
    }
  }
}
</script>

