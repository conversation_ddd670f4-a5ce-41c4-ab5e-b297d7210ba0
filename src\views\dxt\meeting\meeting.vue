<template>
  <div class="app-container">
    <!-- 新增按钮 -->

    <el-form :inline="true" class="container-card" label-width="110px" label-position="left">
      <el-form-item label="会议名称：">
        <el-input v-model="dataQuery.meetingName" placeholder="请输入会议名称：" clearable />
      </el-form-item>
      <el-form-item label="会议城市：">
        <el-input v-model="dataQuery.city" placeholder="请输入会议城市：" clearable />
      </el-form-item>
      <el-form-item label="会议编号：">
        <el-input v-model="dataQuery.meetingId" placeholder="请输入会议编号：" clearable />
      </el-form-item>
      <el-form-item label="主讲人编号：">
        <el-input v-model="dataQuery.presenterId" placeholder="请输入主讲人编号：" clearable />
      </el-form-item>
      <el-form-item style="text-align: right">
        <el-button type="primary" icon="el-icon-search" size="mini" @click="fetchData()">搜索</el-button>
      </el-form-item>
    </el-form>
    <el-row>
      <el-col :span="24" style="text-align: right;margin-bottom: 30px;">
        <el-button type="success" icon="el-icon-plus" size="mini" @click="clickAdd">新增会议</el-button>
      </el-col>
    </el-row>

    <el-table v-loading="tableLoading" stripe :data="tableData" border style="width: 100%;margin-bottom: 30px;">
      <el-table-column prop="id" label="会议编号" align="center" width="180" />
      <el-table-column prop="meetingName" label="会议名" align="center" :show-overflow-tooltip="true" />
      <el-table-column label="操作" align="center" width="350" fixed="right">
        <template slot-scope="scope">
          <el-tooltip content="编辑" placement="top">
            <el-button size="mini" icon="el-icon-edit" type="warning" @click="handleUpdate(scope.row.id)">编辑</el-button>
          </el-tooltip>
          <el-tooltip content="修改成交人数" placement="top">
            <el-button size="mini" icon="el-icon-edit" type="warning" @click="handleEcho(scope.row.id)">修改成交人数</el-button>
          </el-tooltip>
          <el-tooltip content="删除" placement="top">
            <el-button size="mini" type="danger" icon="el-icon-delete" @click="handleDelete(scope.row.id)">删除</el-button>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column prop="typeName" label="会议类型" align="center">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.sponsorType === 'Admin'">总部</el-tag>
          <el-tag v-if="scope.row.sponsorType === 'Agent'">市级服务商</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="periods" label="期数" align="center" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <el-tag>第{{ scope.row.periods }}期</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="personInChargeName" label="会议负责人姓名" align="center" :show-overflow-tooltip="true" />
      <el-table-column prop="personInChargePhone" label="会议负责人手机号" align="center" :show-overflow-tooltip="true" />
      <el-table-column prop="presenterId" label="主讲人编号" align="center" :show-overflow-tooltip="true" />
      <el-table-column prop="lecturerName" label="讲师名称" align="center" :show-overflow-tooltip="true" />
      <el-table-column prop="address" label="详细地址" align="center" :show-overflow-tooltip="true" />
      <el-table-column prop="city" label="主办城市" align="center" :show-overflow-tooltip="true" />
      <el-table-column prop="refundCode" label="退款码" align="center" :show-overflow-tooltip="true" />
      <el-table-column prop="price" label="报名费" align="center">
        <template slot-scope="scope">
          <span style="color: #ff0000">{{ scope.row.price }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="startTime" label="会议开始时间" align="center" :show-overflow-tooltip="true" />
      <el-table-column prop="endTime" label="会议截止时间" align="center" :show-overflow-tooltip="true" />
      <el-table-column prop="closeTime" label="报名截止时间" align="center" :show-overflow-tooltip="true" />
      <el-table-column prop="createTime" label="创建时间" align="center" :show-overflow-tooltip="true" />
    </el-table>
    <!-- 分页 -->
    <el-col :span="20">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>

    <!-- 添加弹窗 -->
    <el-dialog title="修改会议的成交人" :visible.sync="dialogVisibleBoolean" width="60%" :close-on-click-modal="false"
      @close="close">

      <el-form :ref="'updateData'" :model="updateData" label-position="left" label-width="120px" style="width: 50%;">
        <el-form-item label="会议编号" prop="id">
          <el-input v-model="updateData.id" disabled />
        </el-form-item>
        <el-form-item label="成交人数:" prop="clinchPeopleNumber">
          <el-input v-model="updateData.clinchPeopleNumber" oninput="value=value.replace(/[^\d]/g,'')" maxlength="8" />
        </el-form-item>
        <el-form-item label="成交率:" prop="turnoverRate">
          <el-input v-model="updateData.turnoverRate" disabled />
        </el-form-item>
        <el-form-item label="会议总结" prop="outcome">
          <el-input v-model="updateData.outcome" type="textarea" />
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="updateDataFun('updateData')">修改</el-button>
        <el-button size="mini" @click="close">关闭</el-button>
      </div>
    </el-dialog>
    <!-- 修改或者新增弹窗 -->
    <el-dialog :title="addOrUpdate ? '新增会议' : '修改会议'" :visible.sync="dialogVisible" width="70%" :close-on-click-modal="false"
      @close="close">
      <el-form :ref="addOrUpdate ? 'addMeetingData' : 'updateMeetingData'" :rules="rules"
        :model="addOrUpdate ? addMeetingData : updateMeetingData" label-position="left" label-width="120px">
        <el-tabs v-model="activeName" type="border-card">
          <el-tab-pane label="会议信息" name="first">
            <el-row>
              <el-form-item v-if="!addOrUpdate" label="会议编号" prop="id">
                <el-input v-model="updateMeetingData.id" disabled />
              </el-form-item>
              <el-col :span="14">
                <el-form-item label="会议名称" prop="meetingName">
                  <el-input v-if="addOrUpdate" v-model="addMeetingData.meetingName" style="width: 200px" maxlength="30" />
                  <el-input v-if="!addOrUpdate" v-model="updateMeetingData.meetingName" style="width: 200px"
                    maxlength="30" />
                </el-form-item>
                <el-form-item label="会议时间" prop="timeFrame">
                  <el-date-picker v-if="addOrUpdate" v-model="addMeetingData.timeFrame" type="datetimerange"
                    range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                    value-format="yyyy-MM-dd HH:mm:ss" />
                  <el-date-picker v-if="!addOrUpdate" v-model="updateMeetingData.timeFrame" type="datetimerange"
                    range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                    value-format="yyyy-MM-dd HH:mm:ss" />
                </el-form-item>

                <el-form-item label="主办方" prop="merchantCode">
                  <el-select v-if="addOrUpdate" v-model="addMeetingData.merchantCode" value-key="value" placeholder="请选择">
                    <el-option v-for="(item, index) in merchantCodeType" :key="index" :label="item.label"
                      :value="item.value" />
                  </el-select>
                  <el-select v-if="!addOrUpdate" v-model="updateMeetingData.merchantCode" value-key="value"
                    placeholder="请选择">
                    <el-option v-for="(item, index) in merchantCodeType" :key="index" :label="item.label"
                      :value="item.value" />
                  </el-select>
                </el-form-item>
                <el-form-item label="主讲老师" prop="presenterId">
                  <el-select v-if="addOrUpdate" v-model="addMeetingData.presenterId" value-key="value" placeholder="请选择">
                    <el-option v-for="(item, index) in presenterType" :key="index" :label="item.label"
                      :value="item.value" />
                  </el-select>
                  <el-select v-if="!addOrUpdate" v-model="updateMeetingData.presenterId" value-key="value"
                    placeholder="请选择">
                    <el-option v-for="(item, index) in presenterType" :key="index" :label="item.label"
                      :value="item.value" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="10">
                <el-form-item label="会议类型" prop="sponsorType">
                  <el-select v-if="addOrUpdate" v-model="addMeetingData.sponsorType" value-key="value" placeholder="请选择"
                    @change="changeAddOrUpdate()">
                    <el-option v-for="(item, index) in meetingType" :key="index" :label="item.label" :value="item.value" />
                  </el-select>
                  <el-select v-if="!addOrUpdate" v-model="updateMeetingData.sponsorType" value-key="value"
                    placeholder="请选择">
                    <el-option v-for="(item, index) in meetingType" :key="index" :label="item.label" :value="item.value" />
                  </el-select>
                </el-form-item>
                <el-form-item label="报名人数" prop="comingPeopleNumber">
                  <el-input v-if="addOrUpdate" v-model="addMeetingData.comingPeopleNumber"
                    oninput="value=value.replace(/[^\d]/g,'')" maxlength="20" disabled />
                  <el-input v-if="!addOrUpdate" v-model="updateMeetingData.comingPeopleNumber"
                    oninput="value=value.replace(/[^\d]/g,'')" maxlength="20" disabled />
                </el-form-item>
                <el-form-item label="分享人数" prop="shareNumber">
                  <el-input v-if="addOrUpdate" v-model="addMeetingData.shareNumber"
                    oninput="value=value.replace(/[^\d]/g,'')" disabled />
                  <el-input v-if="!addOrUpdate" v-model="updateMeetingData.shareNumber"
                    oninput="value=value.replace(/[^\d]/g,'')" disabled />
                </el-form-item>
                <el-form-item label="推荐人数" prop="referrerNumber">
                  <el-input v-if="addOrUpdate" v-model="addMeetingData.referrerNumber"
                    oninput="value=value.replace(/[^\d]/g,'')" disabled />
                  <el-input v-if="!addOrUpdate" v-model="updateMeetingData.referrerNumber"
                    oninput="value=value.replace(/[^\d]/g,'')" disabled />
                </el-form-item>
              </el-col>
              <el-col :span="11">
                <el-form-item label="会议成交人数" prop="clinchPeopleNumber">
                  <el-input v-if="addOrUpdate" v-model="addMeetingData.clinchPeopleNumber"
                    oninput="value=value.replace(/[^\d]/g,'')" disabled />
                  <el-input v-if="!addOrUpdate" v-model="updateMeetingData.clinchPeopleNumber"
                    oninput="value=value.replace(/[^\d]/g,'')" disabled />
                </el-form-item>
                <el-form-item label="成交率" prop="turnoverRate">
                  <el-input v-if="addOrUpdate" v-model="addMeetingData.turnoverRate"
                    oninput="value=value.replace(/[^\d]/g,'')" disabled />
                  <el-input v-if="!addOrUpdate" v-model="updateMeetingData.turnoverRate"
                    oninput="value=value.replace(/[^\d]/g,'')" disabled />
                </el-form-item>
                <el-form-item label="报名费(元)" prop="price">
                  <el-input v-if="addOrUpdate" v-model="addMeetingData.price" />
                  <el-input v-if="!addOrUpdate" v-model="updateMeetingData.price" />
                </el-form-item>

                <el-form-item label="是否退还报名费" prop="isBack">
                  <el-switch v-if="addOrUpdate" v-model="addMeetingData.isBack" active-color="#13ce66" />
                  <el-switch v-if="!addOrUpdate" v-model="updateMeetingData.isBack" active-color="#13ce66" />
                </el-form-item>

              </el-col>
              <el-col :span="2" style="height: 1px;" />
              <el-col :span="11">
                <el-form-item label="负责人姓名" prop="personInChargeName">
                  <el-input v-if="addOrUpdate" v-model="addMeetingData.personInChargeName" />
                  <el-input v-if="!addOrUpdate" v-model="updateMeetingData.personInChargeName" disabled />
                </el-form-item>
                <el-form-item label="负责人手机号" prop="personInChargePhone">
                  <el-input v-if="addOrUpdate" v-model="addMeetingData.personInChargePhone"
                    oninput="value=value.replace(/[^\d]/g,'')" />
                  <el-input v-if="!addOrUpdate" v-model="updateMeetingData.personInChargePhone"
                    oninput="value=value.replace(/[^\d]/g,'')" disabled />
                </el-form-item>
                <el-form-item label="第几期" prop="periods">
                  <el-input v-if="addOrUpdate" v-model="addMeetingData.periods" oninput="value=value.replace(/[^\d]/g,'')"
                    maxlength="8" />
                  <el-input v-if="!addOrUpdate" v-model="updateMeetingData.periods"
                    oninput="value=value.replace(/[^\d]/g,'')" maxlength="8" />
                </el-form-item>
                <el-form-item label="报名截止时间" prop="closeTime">
                  <el-date-picker v-model="time" type="date" placeholder="选择日期时间" align="right"
                    :picker-options="pickerOptions">
                  </el-date-picker>
                </el-form-item>
                <el-form-item label="主办的城市" prop="city">
                  <el-select v-if="addOrUpdate" filterable v-model="addMeetingData.city" value-key="value"
                    placeholder="请选择">
                    <el-option v-for="(item, index) in cityType" :key="index" :label="item.label" :value="item.label" />
                  </el-select>
                  <el-select v-if="!addOrUpdate" filterable v-model="updateMeetingData.city" value-key="value"
                    placeholder="请选择">
                    <el-option v-for="(item, index) in cityType" :key="index" :label="item.label" :value="item.label" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <div>
              <el-form-item label="讲师" prop="lecturerName">
                <el-input v-if="addOrUpdate" v-model="addMeetingData.lecturerName" />
                <el-input v-if="!addOrUpdate" v-model="updateMeetingData.lecturerName" />
              </el-form-item>
              <el-form-item label="详细地址" prop="titleSub">
                <el-input v-if="addOrUpdate" v-model="addMeetingData.address" maxlength="30" />
                <el-input v-if="!addOrUpdate" v-model="updateMeetingData.address" maxlength="30" />
              </el-form-item>
              <el-form-item ref="file_Rule" label="会议主图：" prop="contractPhoto">
                <el-col :xs="24" :span="20">
                  <el-upload ref="clearupload1" v-loading="uploadLoading" list-type="picture-card" action
                    element-loading-text="图片上传中" :limit="1" :on-exceed="justPictureNum"
                    :file-list="!addOrUpdate ? fileDetailList : fileDetailList.name" :http-request="uploadPrHttp"
                    :on-preview="handlePictureCardPreview" :on-remove="handleRemoveDetail">
                    <i class="el-icon-plus" />
                  </el-upload>
                </el-col>
              </el-form-item>
              <el-form-item ref="file_Rule" label="分享图：" prop="contractPhoto">
                <el-col :xs="24" :span="20">
                  <el-upload ref="clearupload" v-loading="uploadLoading01" multiple list-type="picture-card" action=""
                    element-loading-text="图片上传中" :limit="8"
                    :file-list="!addOrUpdate ? fileDetailList01 : fileDetailList01.name" :http-request="uploadDetailHttp"
                    :on-preview="handlePictureCardPreview" :on-remove="handleRemoveDetail01">
                    <i class="el-icon-plus" />
                  </el-upload>
                </el-col>
              </el-form-item>
              <el-row>
                <el-col style="height: 20px" />
              </el-row>
              <el-row>
                <el-col :span="4">
                  <span style="font-weight: bold;">规则</span>
                </el-col>
                <el-col :span="20">
                  <Tinymce ref="editor" v-model="contenthtml" :height="400" />
                </el-col>
              </el-row>
            </div>
          </el-tab-pane>
        </el-tabs>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="warning" @click="close">取消</el-button>

        <el-button v-if="addOrUpdate" size="mini" type="success" @click="addActiveFun('addMeetingData')">新增</el-button>
        <el-button v-if="!addOrUpdate" size="mini" type="primary"
          @click="updateMeetingDataFun('updateMeetingData')">修改</el-button>
      </div>
    </el-dialog>

    <el-dialog :visible.sync="dialogUploadVisible" :close-on-click-modal="false">
      <img width="100%" :src="dialogImageUrl" alt>
    </el-dialog>
  </div>
</template>

<script>

import Tinymce from '@/components/Tinymce'
import { ossPrClient } from '@/api/alibaba'
import merchantAccountFlowApi from "@/api/merchantAccountFlow";
import presenterListApi from '@/api/dxt/presenterList'
import meetingApi from "@/api/dxt/meetingApi";
import { pageParamNames } from '@/utils/constants'
import { isvalidPhone, idCard, numberOfPrice } from "@/utils/validate";
export default {
  components: {
    Tinymce
  },
  data() {
    //手机号验证
    var validPhone = (rule, value, callback) => {
      if (!value) {
        callback(new Error("请输入电话号码"));
      } else if (value.length < 11) {
        callback(new Error("请输入正确的11位手机号码"));
      } else {
        callback();
      }
    };
    //身份证表达式
    var numberOfPriceList = (rule, value, callback) => {
      if (!value) {
        callback(new Error("请输入报名费"));
      } else if (!numberOfPrice(value)) {
        callback(new Error("请输入整数和小数"));
      } else {
        callback();
      }
    };
    return {
      pickerOptions: {
        shortcuts: [{
          text: '今天',
          onClick(picker) {
            picker.$emit('pick', new Date());
          }
        }, {
          text: '昨天',
          onClick(picker) {
            const date = new Date();
            date.setTime(date.getTime() - 3600 * 1000 * 24);
            picker.$emit('pick', date);
          }
        }, {
          text: '一周前',
          onClick(picker) {
            const date = new Date();
            date.setTime(date.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', date);
          }
        }]
      },
      time: '',
      fileDetailList01: [],
      tableLoading: false,
      addMeetingDataType: false,//上架按钮禁用
      isActiveTrueShow: true,//已上架
      isActiveFalseShow: false,//未上架
      dataQuery: {
      },
      activeType: [], // 会议类型
      presenterType: [],
      updateData: {
        turnoverRate: '',
        clinchPeopleNumber: '',
        outcome: ''
      },
      cityType: [],//举办的城市
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      contenthtml: '', // 富文本编译器
      tableData: [],
      productList: [], // 商品下拉列表数据

      dialogVisible: false, // 修改弹窗是否展示
      addOrUpdate: true, // 是新增还是修改
      activeName: 'first', // tab默认第一个
      addMeetingData: {}, // 新增会议
      updateMeetingData: {}, // 修改数据
      updateDataRules: {
        id: [
          {
            required: true,
            message: '必填',
            trigger: 'blur'
          }
        ],

        clinchPeopleNumber: [
          {
            required: true,
            message: '必填',
            trigger: 'blur'
          }
        ],
      },
      rules: {
        // 表单提交规则
        meetingName: [
          {
            required: true,
            message: '必填',
            trigger: 'blur'
          }
        ],

        sponsorType: [
          {
            required: true,
            message: '必填',
            trigger: 'blur'
          }
        ],
        merchantCode: [
          {
            required: true,
            message: '必填',
            trigger: 'blur'
          }
        ],
        presenterId: [
          {
            required: true,
            message: '必填',
            trigger: 'blur'
          }
        ],
        price: [
          {
            required: true,
            message: '必填',
            trigger: 'blur'
          }
          ,
          // {
          //   validator: numberOfPriceList,
          //   trigger: "blur",
          // },
        ],
        isBack: [
          {
            required: true,
            message: '必填',
            trigger: 'change'
          }
        ],
        //
        periods: [
          {
            required: true,
            message: '必填',
            trigger: 'blur'
          }
        ],
        city: [
          {
            required: true,
            message: '必填',
            trigger: 'blur'
          }
        ],
        lecturerName: [
          {
            required: true,
            message: '必填',
            trigger: 'blur'
          }
        ],
        address: [
          {
            required: true,
            message: '必填',
            trigger: 'blur'
          }
        ],
        personInChargeName: [
          {
            required: true,
            message: '必填',
            trigger: 'blur'
          }
        ],
        personInChargePhone: [
          {
            required: true,
            message: '必填',
            trigger: 'blur'
          }
        ],
      },
      merchantCodeType: [],//市级服务商
      fileListPending: [], // 待处理已上传图片信息
      fileList: [], // 上传图片已有图片列表

      fileDetailListPending: [], // 待处理已上传图片信息
      fileDetailList: [], // 上传图片已有图片列表
      meetingType: [
        { value: 'Admin', label: '总部' },
        { value: 'Agent', label: '市级服务商' }
      ],
      dialogUploadVisible: false,
      dialogImageUrl: '', // 上传图片预览
      uploadLoading01: false,
      uploadLoading: false, // 上传图片加载按钮
      fullscreenLoading: false, // 保存啥的加载
      dialogVisibleBoolean: false,
      content: '',
      isUploadSuccess: true, // 是否上传成功
      isShowRelevance: true // 新增或修改是否展示关联产品
    }
  },
  created() {
    ossPrClient()
    this.fetchData()
    this.getpresenterType();
    this.getDAreaList();
  },
  methods: {
    // 获取UUid
    getUUid: function () {
      const s = []
      const hexDigits = '0123456789abcdef'
      for (let i = 0; i < 36; i++) {
        s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1)
      }
      s[14] = '4'
      s[19] = hexDigits.substr((s[19] & 0x3) | 0x8, 1)
      s[8] = s[13] = s[18] = s[23] = '-'
      const UUid = s.join('')
      return UUid
    },
    //判断主办方是哪个
    changeAddOrUpdate() {
      if (this.addMeetingData.sponsorType === "Admin") {
        this.merchantCodeType = [
          { value: '100082', label: '100082-安徽省合肥市' }
        ]
      } else {
        this.getAgentList()
      }
    },
    // 获取市级服务商
    getAgentList() {
      merchantAccountFlowApi.getSelectResult("Agent").then((res) => {
        this.merchantCodeType = res.data.data;
      });
    },

    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val
      this.fetchData()
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val
      this.fetchData()
    },
    //获取主讲老师类型
    getpresenterType() {
      presenterListApi.presenterType().then(res => {
        this.presenterType = res.data;
      })
    },
    getDAreaList() {
      meetingApi.getDAreaList().then(res => {
        this.cityType = res.data;
      })
    },
    // 查询
    fetchData() {
      const that = this
      that.tableLoading = true
      meetingApi
        .meetingList(
          that.tablePage.currentPage,
          that.tablePage.size,
          that.dataQuery
        )
        .then((res) => {
          that.tableData = res.data.data
          that.tableLoading = false
          // 设置后台返回的分页参数
          pageParamNames.forEach((name) =>
            that.$set(that.tablePage, name, parseInt(res.data[name]))
          )
        })
    },

    // 删除会议
    handleDelete(id) {
      this.$confirm('您确定要删除此条会议？', '提示', confirm)
        .then(() => {
          meetingApi.deleteMeeting(id).then(() => {
            this.$message.success('删除会议成功')
            this.fetchData()
          })
        })
        .catch(() => {
          this.$message.info('已取消操作')
         })
     },
    // 新增会议板块
    // 点击新增按钮
    clickAdd() {
      this.addMeetingDataType = false;
      this.getAgentList()
      this.addMeetingData = {
        id: '',
        meetingName: '',
        sponsorType: '',
        merchantCode: '',
        presenterId: '',
        timeFrame: '',
        comingPeopleNumber: '',
        shareNumber: '',
        referrerNumber: '',
        clinchPeopleNumber: '',
        turnoverRate: '',
        price: '',
        isBack: false,
        personInChargeName: '',
        periods: '',
        city: '',
        personInChargePhone: '',
        lecturerName: '',
        address: ''
      }
      this.dialogVisible = true
      this.addOrUpdate = true

      if (this.fileDetailList.length !== 0) {
        this.$refs.clearupload1.clearFiles()
      }
      if (this.fileDetailList01.length !== 0) {
        this.$refs.clearupload.clearFiles()
      }
      this.fileDetailList01 = [];
      this.fileDetailList = [];
      this.time = '';
      // this.activeName= 'first',   //tab默认第一个
      this.$nextTick(() => this.$refs['addMeetingData'].clearValidate())
      this.$refs.editor.setContent('')
    },
    /* 新增行*/
    // 新增赠品行初始执行事件
    addRawValue(data) {
      data.forEach((item) => {
        for (const key in item) {
          if (key !== 'nameFreebie' && key !== 'idFreebie') {
            // 第一次点击新增得到的数据
            if (item.hasOwnProperty(key)) {
              if (item[key].edit === undefined) {
                item[key] = {
                  value: item[key],
                  edit: false
                }
              }
            }
          }
        }
      })
    },

    // 删除上传图片
    handleRemove(file, fileList) {
      const that = this
      if (!that.addOrUpdate) {
        that.fileList = fileList
      } else {
        for (let a = 0; a < that.fileDetailList4.length; a++) {
          if (that.fileDetailList4[a].substring(7, 17) == parseInt(file.uid / 1000)) {
            that.fileDetailList4.splice(a, 1)
          }
        }
      }
    },

    // 删除上传图片
    handleRemoveDetail(file, fileList) {
      const that = this
      if (!that.addOrUpdate) {
        that.fileDetailList = fileList
      } else {
        for (let a = 0; a < that.fileDetailList4.length; a++) {
          if (that.fileDetailList4[a].substring(7, 17) == parseInt(file.uid / 1000)) {
            that.fileDetailList4.splice(a, 1)
          }
        }
      }
    },
    handleRemoveDetail01(file, fileList) {
      const that = this
      if (!that.addOrUpdate) {
        that.fileDetailList01 = fileList
      } else {
        for (let a = 0; a < that.fileDetailListPending.length; a++) {
          that.fileDetailListPending[a].uid === file.uid
            ? that.fileDetailList01.splice(a, 1)
            : ''
        }
      }
    },
    // 上传图片预览
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogUploadVisible = true
    },
    // 上传图片数量超限
    justPictureNum(file, fileList) {
      this.$message.warning(`当前限制选择1个文件`)
    },
    // 上传图片请求
    uploadPrHttp({ file }) {
      this.uploadLoading = true
      const that = this
      const fileName = 'manage/' + Date.parse(new Date())
      that.$nextTick(function () {
        ossPrClient()
          .put(fileName, file)
          .then(({ res, url, name }) => {
            if (res && res.status === 200) {
              console.log(`阿里云OSS上传图片成功回调1`, res, url, name)
              if (!that.addOrUpdate) {
                that.fileDetailList.push({ uid: file.uid, url: url })
              } else {
                // 新增上传图片
                that.fileDetailList.push({ name })
                that.addMeetingData.imagePath = name
              }
              that.uploadLoading = false
              that.$nextTick(() => {
                that.uploadLoading = false
              })
            }
          })
          .catch((err) => {
            that.$message.error('上传图片失败请检查网络或者刷新页面')
            console.log(`阿里云OSS上传图片失败回调`, err)
          })
      })
    },

    uploadDetailHttp({ file }) {
      this.uploadLoading01 = true
      const that = this
      const fileName = 'manage/' + Date.parse(new Date())
      that.$nextTick(function () {
        ossPrClient()
          .put(fileName, file)
          .then(({ res, url, name }) => {
            if (res && res.status === 200) {
              console.log(`阿里云OSS上传图片成功回调1`, res, url, name)
              if (!that.addOrUpdate) {
                that.fileDetailList01.push({ uid: file.uid, url: url })
              } else {
                // 新增上传图片
                that.fileDetailList01.push(name)
              }
              that.$nextTick(() => {
                that.uploadLoading01 = false
              })
            }
          })
          .catch((err) => {
            that.$message.error('上传图片失败请检查网络或者刷新页面')
            console.log(`阿里云OSS上传图片失败回调`, err)
          })
      })
    },
    // 新增会议提交后台
    addActiveFun(ele) {
      const that = this
      if (that.fileDetailList.length <= 0) {
        that.$message.error("会议主图不能为空");
        return false;
      }
      if (that.fileDetailList01.length <= 0) {
        that.$message.error("分享图不能为空");
        return false;
      }
      if (that.addMeetingData.city == "") {
        that.$message.error("市不能为空");
        return false;
      }
      if (that.addMeetingData.timeFrame.length <= 0) {
        that.$message.error("会议时间开始时间和结束时间不能为空");
        return false;
      }
      if (that.time === "" || that.time === '' || that.time == undefined) {
        that.$message.error("会议截止时间不能为空");
        return false;
      }
      that.addMeetingData.meetingDetails = that.$refs.editor.value
      if (that.addMeetingData.meetingDetails === "" || that.addMeetingData.meetingDetails === '' || that.addMeetingData.meetingDetails === undefined) {
        that.$message.error("会议规则不能为空");
        return false;
      }
      var a = that.addMeetingData.timeFrame
      that.addMeetingData.startTime = a[0]
      that.addMeetingData.endTime = a[1]
      var d = new Date(that.time);
      d = d.getFullYear() + '-' + this.p((d.getMonth() + 1)) + '-' + this.p(d.getDate()) + ' ' + this.p(d.getHours()) + ':' + this.p(d.getMinutes()) + ':' + this.p(d.getSeconds());
      that.addMeetingData.closeTime = d
      that.addMeetingData.sharePicture = that.fileDetailList01;
      that.$refs[ele].validate((valid) => {
        // 表单验证
        if (valid) {
          const loading = this.$loading({
            lock: true,
            text: '新增提交会议',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          })
          console.log(that.addMeetingData)

          meetingApi
            .meetingAdd(that.addMeetingData)
            .then(() => {
              that.dialogVisible = false
              that.addMeetingDataType = false;
              loading.close()
              that.$nextTick(() => that.fetchData())
              that.$message.success('新增会议成功')
            })
            .catch((err) => {
              if (err === 'error') {
                that.$message.error('新增会议失败')
                loading.close()
              }
            })
        } else {
          that.$message.error('请填写必填项')
          console.log('error submit!!')
          // loading.close();
          return false
        }
      })
    },
    p(s) {
      return s < 10 ? '0' + s : s
    },
    handleEcho(id) {
      this.dialogVisibleBoolean = true;
      meetingApi.echoMeeting(id).then((res) => {
        this.updateData.id = res.data.id
        this.updateData.clinchPeopleNumber = res.data.clinchPeopleNumber;
        this.updateData.turnoverRate = res.data.turnoverRate;
        this.updateData.outcome = res.data.outcome;
      })
    },
    // 编辑修改会议
    // 点击编辑按钮
    handleUpdate(id) {
      const that = this
      if (that.fileDetailList.length !== 0) {
        that.$refs.clearupload.clearFiles()
      }
      if (that.fileDetailList01.length !== 0) {
        that.$refs.clearupload.clearFiles()
      }
      that.fileDetailList01 = [];

      that.dialogVisible = true
      that.addOrUpdate = false
      meetingApi.echoMeeting(id).then((res) => {
        console.log(res)
        that.updateMeetingData = res.data
        const arr = [res.data.startTime, res.data.endTime]
        that.contenthtml = that.updateMeetingData.meetingDetails // 副文本赋值
        this.$refs.editor.setContent(that.updateMeetingData.meetingDetails)
        this.$set(this.updateMeetingData, 'timeFrame', arr)
        that.updateMeetingData.timeFrame = arr
        that.time = that.updateMeetingData.closeTime;
        if (that.updateMeetingData.sharePicture) {
          for (let i = 0; i < that.updateMeetingData.sharePicture.length; i++) {
            that.fileDetailList01.push({
              url: that.aliUrl + that.updateMeetingData.sharePicture[i],
            });
          }
        } else {
          that.fileDetailList01 = [];
        }
        if (
          that.updateMeetingData.imagePath !== null &&
          that.updateMeetingData.imagePath.length > 1
        ) {
          that.fileDetailList = [
            {
              url: that.aliUrl + that.updateMeetingData.imagePath
            }
          ]
        } else {
          that.fileDetailList = []
        }
      })
    },
    //修改成交人数
    updateDataFun(ele) {
      const that = this;
      if (that.updateData.id == '') {
        that.$message.info("会议编号不能为空");
        return false
      }
      if (that.updateData.clinchPeopleNumber == '' || that.updateData.clinchPeopleNumber === "" || that.updateData.clinchPeopleNumber == undefined) {
        that.$message.info("成交人数不能为空");
        return false
      }
      const foot = {
        numberOfTransactions: that.updateData.clinchPeopleNumber,
        outcome: that.updateData.outcome,
        meetingId: that.updateData.id
      }
      meetingApi.updateMeeting(foot).then(res => {
        that.fetchData();
        that.dialogVisibleBoolean = false
      }, s => {
        if (s === "error") {

        }
      })
    },
    // 修改会议提交
    updateMeetingDataFun(ele) {
      const that = this
      if (that.fileDetailList.length <= 0) {
        that.$message.error("会议主图不能为空");
        return false;
      }
      if (that.fileDetailList01.length <= 0) {
        that.$message.error("分享图不能为空");
        return false;
      }
      if (that.updateMeetingData.city == "") {
        that.$message.error("市不能为空");
        return false;
      }
      if (that.updateMeetingData.timeFrame.length <= 0) {
        that.$message.error("会议时间开始时间和结束时间不能为空");
        return false;
      }
      if (that.time === "" || that.time === '' || that.time == undefined) {
        that.$message.error("会议截止时间不能为空");
        return false;
      }
      that.$refs[ele].validate((valid) => {
        // 表单验证
        if (valid) {
          that.updateMeetingData.meetingDetails = that.$refs.editor.value
          var a = that.updateMeetingData.timeFrame
          that.updateMeetingData.startTime = a[0]
          that.updateMeetingData.endTime = a[1]
          var d = new Date(that.time);
          d = d.getFullYear() + '-' + this.p((d.getMonth() + 1)) + '-' + this.p(d.getDate()) + ' ' + this.p(d.getHours()) + ':' + this.p(d.getMinutes()) + ':' + this.p(d.getSeconds());
          that.updateMeetingData.closeTime = d
          that.updateMeetingData.sharePicture = [];
          that.imagePath = '';
          console.log(that.fileDetailList)
          for (let i = 0; i < that.fileDetailList.length; i++) {
            let index = that.fileDetailList[i].url.lastIndexOf("manage");
            that.updateMeetingData.imagePath = that.fileDetailList[i].url.substring(
              index,
              that.fileDetailList[0].url.length
            )
          }
          for (let i = 0; i < that.fileDetailList01.length; i++) {
            let index = that.fileDetailList01[i].url.lastIndexOf("manage");
            that.updateMeetingData.sharePicture.push(
              that.fileDetailList01[i].url.substring(
                index,
                that.fileDetailList01[i].url.length
              )
            );
          }
          const loading = this.$loading({
            lock: true,
            text: '修改会议信息提交',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          })
          console.log(that.updateMeetingData)
          that.updateMeetingData.timeStart = that.updateMeetingData.timeFrame[0]
          that.updateMeetingData.timeEnd = that.updateMeetingData.timeFrame[1]
          meetingApi
            .updateMeetingData(that.updateMeetingData)
            .then(() => {
              that.dialogVisible = false
              loading.close()
              that.$nextTick(() => that.fetchData())
              that.$message.success('修改会议成功')

              if (that.fileDetailList.length !== 0) {
                that.$refs.clearupload.clearFiles()
              }
              if (that.fileDetailList01.length !== 0) {
                that.$refs.clearupload.clearFiles()
              }
              that.fileDetailList01 = [];

            })
            .catch((err) => {
              if (err === 'error') {
                that.$message.error('修改会议失败')
                loading.close()
              }
            })
        } else {
          console.log('error submit!!')
          // loading.close();
          return false
        }
      })
    },

    // base64转blob
    toBlob(urlData, fileType) {
      const bytes = window.atob(urlData)
      let n = bytes.length
      const u8arr = new Uint8Array(n)
      while (n--) {
        u8arr[n] = bytes.charCodeAt(n)
      }
      return new Blob([u8arr], {
        type: fileType
      })
    },

    // 关闭弹窗
    close() {
      this.dialogVisible = false;
      this.dialogVisibleBoolean = false;
    }
  }
}
</script>
