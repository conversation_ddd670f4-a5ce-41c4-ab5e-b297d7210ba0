<template>
  <div class="app-container">
    <el-form :inline="true" ref="queryForm" class="container-card" label-width="120px">
      <el-row>
        <el-form-item label="姓名：">
          <el-input v-model="form.name" placeholder="请输入" clearable />
        </el-form-item>
        <!-- <el-form-item label="商户名称：">
            <el-input v-model="form.merchantName" placeholder="请输入" clearable /> 
        </el-form-item> -->
        <el-form-item>
          <el-button icon="el-icon-refresh" size="small" @click="resetQuery()">重置</el-button>
          <el-button icon="el-icon-search" size="small" type="primary" @click="search()">搜索</el-button>
        </el-form-item>
      </el-row>
    </el-form>

    <div class="SearchForm">
      <!-- 添加
      <div class="btn-add" style="margin-bottom: 10px">
        <el-button size="small" type="primary" icon="el-icon-plus">添加</el-button >
      </div> -->
      <!-- 表格 -->
      <el-table class="common-table" v-loading.body="tableLoading" :data="tableData" ref="tableColumn" border>
        <el-table-column type="index" width="110" label="序号"></el-table-column>
        <el-table-column prop="realname" min-width="130" label="姓名"></el-table-column>
        <el-table-column prop="nickname" min-width="140" label="学员信息"></el-table-column>
        <el-table-column prop="roles" min-width="210" label="角色">
          <template slot-scope="scope">
            {{ scope.row.roles | rolesName }}
          </template>
        </el-table-column>
        <el-table-column prop="id" label="操作" min-width="200">
          <template slot-scope="scope">
            <el-button type="text" size="mini" @click="handleDetail(scope.row)">查看</el-button>
          </template>
        </el-table-column>
      </el-table>
      <traineeDetail ref="traineeDetail" v-if="dialogFrom.visible" :dialog-param="dialogFrom" @closeDialog="closeDialog"></traineeDetail>
    </div>
    <!-- 分页 -->
    <el-col :span="24" style="margin-bottom: 20px">
      <el-pagination
        :current-page="tablePage.currentPage"
        :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="tablePage.totalItems"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-col>
  </div>
</template>

<script>
  import traineeDetail from './components/traineeDetail.vue';
  import courseApi from '@/api/training/course';
  // studentStatistics
  export default {
    name: 'traineeStatistics',
    components: {
      traineeDetail
    },
    filters: {
      rolesName(list) {
        let nameList = [];
        list.forEach((element) => {
          nameList.push(element.name);
        });
        return nameList.join(',');
      }
    },
    data() {
      return {
        tableLoading: false,
        dialogFrom: {
          visible: false,
          type: 'add'
        },
        // 分页
        tablePage: {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: 0
        },
        form: {
          courseName: '',
          type: '全部'
        },
        tableData: [{ nickname: '123' }]
      };
    },
    created() {
      this.getStudentStatis();
    },
    methods: {
      getStudentStatis() {
        this.tableLoading = true;
        let param = { pageSize: this.tablePage.size, pageNum: this.tablePage.currentPage };
        param = { ...this.form, ...param };
        courseApi
          .studentStatistics(param)
          .then((res) => {
            this.tableData = res.data.data;
            this.tablePage.totalItems = Number(res.data.totalItems);
            this.$nextTick(() => {
              this.$refs.tableColumn.doLayout();
            });
          })
          .finally(() => {
            this.tableLoading = false;
          });
      },
      // 分页
      handleSizeChange(val) {
        this.tablePage.currentPage = 1;
        this.tablePage.size = val;
        this.getStudentStatis();
      },
      handleCurrentChange(val) {
        this.tablePage.currentPage = val;
        this.getStudentStatis();
      },
      // 重置
      resetQuery() {
        this.form = {};
        this.tablePage.currentPage = 1;
        this.getStudentStatis();
      },

      // 搜索
      search() {
        this.tablePage.currentPage = 1;
        this.getStudentStatis();
      },
      // 查看
      handleDetail(info) {
        this.dialogFrom.visible = true;
        this.dialogFrom.type = 'add';
        this.$nextTick(() => {
          this.$refs.traineeDetail.getRecordPage(info);
        });
      },
      closeDialog() {
        this.dialogFrom.visible = false;
      }
    }
  };
</script>

<style>
  .SearchForm {
    width: 100%;
    margin-bottom: 15px;
    background: #fff;
    border-radius: 6px;
    padding: 5px 0;
    padding-left: 5px;
    box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.2);
  }
  .container-card {
    padding: 15px 15px 15px 15px;
    margin-bottom: 15px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  }

  .period-table td,
  .period-table th {
    text-align: center;
  }

  @media (max-width: 767px) {
    .el-message-box {
      width: 80% !important;
    }
  }

  .el-tooltip__popper {
    max-width: 800px;
  }
</style>
