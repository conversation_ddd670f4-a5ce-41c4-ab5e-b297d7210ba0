import request from "@/utils/request";

export default {
  //招商数据
  initData(type) {
    return request({
      url: '/znyy/elasticSearch/getInvestmentData',
      method: 'GET',
      params: {
        type: type
      }
    })
  },
  //招商流水数据
  initFlowData(type,dataType){
    return request({
      url: '/znyy/elasticSearch/getInvestmentFlowData',
      method: 'GET',
      params: {
        type: type,
        dataType: dataType
      }
    })
  },
  //招生数据
  initAdmissionsData(type){
    return request({
      url: '/znyy/elasticSearch/getAdmissionsData',
      method: 'GET',
      params: {
        type: type
      }
    })
  },  //招生数据
  getClassFlowData(type){
    return request({
      url: '/znyy/elasticSearch/getClassFlowData',
      method: 'GET',
      params: {
        type: type
      }
    })
  },
}
