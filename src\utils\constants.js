/** 分页查询时，后台返回的数据中分页参数的命名，返回示例：
 *
 * {
 *
 * "msg":"ok",
 * "code":1,
 * "succ":true,
 * "oper":"default",
 * "page":{
 *          "current":1,
 *          "pages":2,
 *          "records":[],
 *          "size":3,
 *          "total":5
 *        }
 * }
 * */
export const pageParamNames = ['currentPage', 'totalPage', 'totalItems'];
export const pageParamNamestwo = ['currentPage', 'totalPage', 'totalItems'];

export const permType = {
  MENU: 1,
  BUTTON: 2,
  API: 3
};

/**
 * 下拉选择框数据：权限类型
 *
 */
export const permTypeOptions = [
  { value: permType.MENU, label: '菜单' },
  { value: permType.BUTTON, label: '按钮' },
  { value: permType.API, label: '接口' }
];

/**
 * 权限类型
 * @type {Map<any, any>}
 */
export const permTypeMap = new Map([
  [permType.MENU, '菜单'],
  [permType.BUTTON, '按钮'],
  [permType.API, '测试']
]);

export const confirm = {
  confirmButtonText: '确定',
  cancelButtonText: '取消',
  type: 'warning'
};

export const dxSource = 'ZNYY##BROWSER##WEB';

// export const baseUrl = "http://192.168.5.116:8081/";
// export const baseUrl = "http://192.168.5.84:8081";
// export const baseUrl = "http://192.168.5.85:8081";
// export const baseUrl = "http://192.168.5.55:8081/";
// export const baseUrl = 'http://192.168.5.99:8081/'; //许健
// export const baseUrl = "https://applet.dxznjy.com/";
// export const baseUrl = 'http://192.168.40.33:8081/';
// export const baseUrl = "http://gaoaoapi.ngrok.dxznjy.com";
export const baseUrl = 'https://uat-gateway.dxznjy.com';
// 线上
// export const baseUrl = 'https://189test.ngrok.dxznjy.com'; //测试环境
// export const baseUrl = "https://applet.dxznjy.com/";
// 良浩本地
// export const baseUrl = "http://gcj.ngrok.dxznjy.com";
// export const baseUrl =  "https://wyx.ngrok.dxznjy.com";
// export const baseUrl = 'https://gateway.dxznjy.com';

export const root = {
  rval: 'root',
  pval: '*'
};
