/**
 * 市级服务商相关接口
 */
import request from '@/utils/request'

export default {
  // 新增
  addAgent(data) {
    return request({
      url: '/znyy/agent',
      method: 'POST',
      data
    })
  },
  // 修改回显
  echoAgent(id) {
    return request({
      url: '/znyy/agent/modifyEcho/' + id,
      method: 'PUT'
    })
  },
  // 编辑
  updateAgent(data) {
    return request({
      url: '/znyy/agent/updateContent',
      method: 'PUT',
      data
    })
  },
  // 获取银行分类列表
  categoryType(bankType) {
    return request({
      url: '/znyy/bvstatus/' + bankType,
      method: 'GET'
    })
  },
  // 加载符近的托管中心
  loadOtherExperienceStores(lat, lon) {
    return request({
      url: '/znyy/dealer/load/dealer?lat=' + lat + '&lon=' + lon,
      method: 'GET'
    })
  },


}
