<template>
  <div>
    <!-- 表格 -->
    <el-table v-loading="tableLoading" :data="luyouclassCard" style="width: 100%" id="out-table" :header-cell-style="getRowClass" :cell-style="{ 'text-align': 'center' }">
      <el-table-column width="50" align="center"></el-table-column>
      <el-table-column prop="deliverMerchant" v-if="isAdmin" min-width="110" label="交付中心编号" header-align="center" />
      <el-table-column prop="deliverName" v-if="isAdmin" min-width="130" label="交付中心名称" header-align="center" />
      <el-table-column prop="name" label="姓名" width="80" header-align="center"></el-table-column>
      <el-table-column prop="merchantName" label="学员来源" width="230" header-align="center"></el-table-column>
      <el-table-column prop="studentCode" label="学员编号" width="150" header-align="center"></el-table-column>
      <el-table-column prop="courseType" :formatter="courseType" label="课程分类" width="120" header-align="center"></el-table-column>
      <!-- <el-table-column prop="contentType" label="课程内容" header-align="center" width="180" show-overflow-tooltip></el-table-column> -->
      <!--      <el-table-column prop="haveCourseHours" label="剩余可排课时" header-align="center" width="120"></el-table-column>-->
      <el-table-column prop="teachingType" :formatter="teachingType" label="授课方式" header-align="center"></el-table-column>
      <el-table-column prop="date,format,time" label="上课时间" width="180" header-align="center">
        <template slot-scope="scope">
          <div>{{ scope.row.date }}</div>
          <div>
            {{ scope.row.format }} {{ scope.row.time }}
            <span v-if="scope.row.endTime">-</span>
            {{ scope.row.endTime }}
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="hours" label="使用学时" header-align="center">
        <template slot-scope="scope">{{ scope.row.hours }}/时</template>
      </el-table-column>
      <el-table-column prop="teacher" label="教练 老师" header-align="center"></el-table-column>
      <el-table-column prop="studyStatus" :formatter="studyStatus" label="状态" header-align="center"></el-table-column>
    </el-table>
    <!-- 分页器 -->
    <el-row type="flex" justify="center" align="middle" style="height: 60px">
      <!-- 3个变量：每页数量、页码数、总数  -->
      <!-- 2个事件：页码切换事件、每页数量切换事件-->
      <el-pagination
        v-if="tableIshow"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="searchNum.pageNum"
        :page-sizes="[10, 20, 30, 40, 50]"
        :page-size="searchNum.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      ></el-pagination>
    </el-row>
  </div>
</template>

<script>
  import { getTimetable } from '@/api/studyroom/studentList';

  export default {
    name: 'classCard',
    props: {
      query: {
        type: Object,
        default: undefined
      }
    },
    data() {
      return {
        stuudentCode: null,
        dataLookerStyle: false,
        qjstartTime: '',
        qjendTime: '',
        tableIshow: true,
        redLeave: [],
        lookstyle: false,
        LeaveStyle: false,
        classCardstyle: false, //抽屉状态
        LeaveId: '',
        searchNum: {
          courseType: '',
          name: '',
          endTime: '',
          startTime: '',
          teacherName: '',
          studentCode: '',
          studyStatus: '',
          lastStudyTime: '',
          deliverClass: '',
          pageNum: 1,
          pageSize: 10
        }, //搜索参数
        tableLoading: false,
        direction: 'rtl', //超哪边打开
        teacher: '',
        timeAll: [],
        contentType: '',
        total: null,
        luyouclassCard: [],
        leaveApplication: {
          id: '',
          type: 1
        },
        getFeedback: {
          //详情的参数
          id: '',
          type: '1'
        },
        isAdmin: false,
        exportLoading: false
      };
    },
    created() {
      this.initData();
    },
    methods: {
      async initData() {
        this.tableLoading = true;
        this.searchNum.studentCode = this.query.studentCode;
        this.searchNum.deliverClass = this.query.deliverClass;
        let { data } = await getTimetable(this.searchNum);
        this.tableLoading = false;
        this.total = Number(data.totalItems);
        this.luyouclassCard = data.data;
      },
      LeaveDialog(v) {
        this.LeaveStyle = v;
      },
      // 转文字
      teachingType(val) {
        if (val.teachingType == 1) {
          return '远程';
        } else if (val.teachingType == 2) {
          return '线下';
        } else if (val.teachingType == 3) {
          return '远程和线下';
        } else {
          return '暂无';
        }
      },
      courseType(val) {
        if (val.courseType == 1) {
          return '鼎英语';
        }
      },
      status(val) {
        if (val.status == 1) {
          return '正常';
        } else if (val.status == 2) {
          return '请假';
        } else if (val.status == 3) {
          return '请假已处理';
        }
      },
      studyStatus(val) {
        if (val.studyStatus == 0) {
          return '未上课';
        } else if (val.studyStatus == 2) {
          return '已上课';
        }
      },
      // 动态class
      getRowClass({ rowIndex, columnIndex }) {
        if (rowIndex == 0) {
          return 'background:#f5f7fa';
        }
      },
      // 分页
      handleSizeChange(val) {
        this.searchNum.pageSize = val;
        this.initData();
      },
      handleCurrentChange(val) {
        this.searchNum.pageNum = val;
        this.initData();
      }
    }
  };
</script>

<style scoped>
  body {
    background-color: #f5f7fa;
  }

  .normal {
    color: rgb(28, 179, 28);
  }

  .error {
    color: rgba(234, 36, 36, 1);
  }

  body {
    background-color: #f5f7fa;
  }
</style>

<style lang="scss" scoped>
  .frame {
    margin-top: 0.5vh;
    background-color: rgba(255, 255, 255);
  }

  // .el-button--success {
  //   color: #ffffff;
  //   background-color: #6ed7c4;
  //   border-color: #6ed7c4;
  // }
</style>
