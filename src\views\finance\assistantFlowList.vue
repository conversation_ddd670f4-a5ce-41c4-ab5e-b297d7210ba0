<template>
  <div class="app-container">
    <el-form :inline="true" class="container-card" label-width="110px" label-position="left">

      <el-row>
        <el-col :span="8" :xs="24">
          <el-form-item label="交易流水号：">
            <el-input id="flowCode" v-model="dataQuery.flowCode" name="id" placeholder="请输入交易流水号：" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="学员编号：">
            <el-input id="studentCode" v-model="dataQuery.studentCode" name="id" placeholder="请输入学员编号：" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="学员名称：">
            <el-input id="realName" v-model="dataQuery.realName" name="id" placeholder="请输入学员名称：：" clearable />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="8" :xs="24">
          <el-form-item label="变动方式：">
            <el-select v-model="dataQuery.direction" placeholder="全部" style="width: 185px;" clearable>
              <el-option v-for="item in [{ value: 1, label: '入' }, { value: 0, label: '出' }]" :key="item.value"
                :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="变动类型：">
            <el-select v-model="dataQuery.flowType" filterable value-key="value" placeholder="请选择" clearable>
              <el-option v-for="(item, index) in financeAccountsType" :key="index" :label="item.label"
                :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">

        </el-col>
      </el-row>

      <el-row>
        <el-col :span="16" :xs="24">
          <el-form-item>
            <el-form-item label="变动时间：">
              <el-date-picker style="width: 100%;" v-model="RegTime" type="daterange" value-format="yyyy-MM-dd HH:mm:ss"
                unlink-panels range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" clearable>
              </el-date-picker>
            </el-form-item>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="fetchData01()">搜索</el-button>
            <el-button icon="el-icon-refresh" @click="rest()">重置</el-button>
          </el-form-item>
        </el-col>
      </el-row>


      <!-- <el-form-item label="商户类型：">
        <el-select v-model="dataQuery.merchantRoleTag" placeholder="全部" style="width: 185px;">
          <el-option v-for="item in [{value:'Agent',label:'市级服务商'},{value:'Dealer',label:'托管中心'},{value:'Market',label:'散户'}]" :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="变动方式：">
       <el-select v-model="dataQuery.direction" placeholder="全部" style="width: 185px;">
          <el-option v-for="item in [{value:1,label:'入'},{value:0,label:'出'}]" :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="变动类型：">
        <el-select v-model="dataQuery.accountType" filterable value-key="value" placeholder="请选择" >
            <el-option v-for="(item,index) in financeAccountsType" :key="index" :label="item.label" :value="item.value" />
          </el-select>
      </el-form-item> -->


    </el-form>
    <el-form :inline="true" style="margin-bottom: 20px;">
      <el-button type="warning" icon="el-icon-document-copy" @click="exportFlow()"
        v-loading="exportLoading">导出</el-button>
    </el-form>

    <el-table class="period-table" v-loading="tableLoading" :data="tableData" style="width: 100%;margin-bottom: 20px;"
      row-key="id" border default-expand-all :tree-props="{ list: 'children', hasChildren: 'true' }">
      <el-table-column prop="flowCode" label="交易流水号" width="180" :show-overflow-tooltip="true"></el-table-column>
      <el-table-column prop="studentCode" label="学员编号" width="100"></el-table-column>
      <el-table-column prop="address" label="操作" v-if="checkPermission(['admin'])" header-align="center" width="200">
        <template v-slot="{ row }">
          <el-button type="warning" size="mini" @click="rollback(row)"
            v-if="row.status !== '10' && row.direction == false">退回
          </el-button>
          <el-button type="info" disabled size="mini" v-if="row.status === '10' && row.direction == false">已退回
          </el-button>
          <!-- <el-button type="success" size="mini" @click="LeaveBtn(row)">查看详情</el-button>
          <el-button type="primary" size="mini" v-else @click="paikeBtn(row)">调课</el-button>
          <el-button type="success" size="mini" @click="lookBtn(row)">数据查看</el-button>
          <el-button type="danger" size="mini" v-if="row.studyStatus === 0" @click="deleteStudy(row)">删除</el-button> -->
        </template>
      </el-table-column>
      <!-- <el-table-column prop="merchantName" label="学员名称" width="200" :show-overflow-tooltip="true"></el-table-column> -->
      <el-table-column prop="realName" label="学员名称"></el-table-column>
      <el-table-column prop="typeOfChange" label="变动方式">
      </el-table-column>
      <el-table-column prop="flowTypeName" label="变动类型"></el-table-column>
      <el-table-column prop="beforeHours" label="变动前学时（节）" width="140"></el-table-column>
      <el-table-column prop="userHours" label="变动学时（节）" width="120"></el-table-column>
      <el-table-column prop="afterHours" label="变动后学时（节）" width="140"></el-table-column>
      <el-table-column prop="startTime" label="变动时间" width="160" :show-overflow-tooltip="true"></el-table-column>
      <el-table-column prop="remark" label="变动描述" width="360" :show-overflow-tooltip="true"></el-table-column>
    </el-table>
    <!-- 分页 -->
    <el-col :span="24" style="overflow-x: auto;" :xs="24">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>
<!--    <el-col v-if="currentAdmin.schoolType !== 3" :span="24" class="mt20">-->
<!--      <span> 本次总计变动前学时：{{ classHoursBeforeTheChange }}节，变动学时：{{-->
<!--        changeOfClassHours-->
<!--      }}节，变动后学时：{{ classHoursAfterTheChange }}节</span>-->
<!--    </el-col>-->
  </div>
</template>

<script>
import
merchantAccountFlowApi
  from "@/api/merchantAccountFlow";
import Tinymce from "@/components/Tinymce";
import enTypes from '@/api/bstatus'
import checkPermission from '@/utils/permission'

import {
  pageParamNames
} from "@/utils/constants";
export default {
  name: 'assistantFlowList',
  data() {
    return {
      tableLoading: false,
      exportLoading: false,
      classHoursBeforeTheChange: 0,
      changeOfClassHours: 0,
      classHoursAfterTheChange: 0,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      tableData: [{
        name: 1,
        title: '1111'
      }],
      currentAdmin: {},
      RegTime: [],
      dataQuery: {},
      dialogVisible: false,
      updateMarketDate: {},
      addMarketDate: {},
      showLoginAccount: false,
      financeAccountsType: []
    };
  },
  created() {
    this.fetchData();
    //获取变动类型
    this.getFinanceAccountsType();
  },
  methods: {
    checkPermission,
    getCurrentAdmin() {
      schoolList.getCurrentAdmin().then((res) => {
        this.currentAdmin = res.data
      })
    },
    //重置
    rest() {
      this.dataQuery.flowCode = '';
      this.dataQuery.studentCode = '';
      this.dataQuery.realName = '';
      this.dataQuery.direction = '';
      this.dataQuery.flowType = '';
      this.RegTime = '';
      this.initData01();
    },

    fetchData01() {
      this.tablePage = {
        currentPage: 1,
        size: this.tablePage.size,
        totalPage: null,
        totalItems: null
      }
      this.fetchData();
    },
    // 查询提现列表
    fetchData() {
      const that = this;
      that.tableLoading = true
      var a = that.RegTime;
      if (a != null) {
        that.dataQuery.startDate = a[0];
        console.log(that.dataQuery.startDate = a[0]);
        that.dataQuery.endDate = a[1];
      } else {
        that.dataQuery.startDate = '';
        that.dataQuery.endDate = '';
      }
      merchantAccountFlowApi
        .studentCourseFlowList(
          that.tablePage.currentPage,
          that.tablePage.size,
          that.dataQuery
        )
        .then((res) => {
          that.tableData = res.data.data;
          that.tableLoading = false;
          // 设置后台返回的分页参数
          pageParamNames.forEach((name) =>
            that.$set(that.tablePage, name, parseInt(res.data[name]))
          );
        });
      // merchantAccountFlowApi.detailsOfStudentCourseFlowChanges(that.tablePage.currentPage,
      //   that.tablePage.size,
      //   that.dataQuery).then(res => {
      //     that.classHoursBeforeTheChange = res.data.classHoursBeforeTheChange;
      //     that.changeOfClassHours = res.data.changeOfClassHours;
      //     that.classHoursAfterTheChange = res.data.classHoursAfterTheChange;
      //   })
    },
    rollback(row) {
      let message = '确定为学员：' + row.realName + '。编号：' + row.studentCode + '。退回' + row.userHours + '节'
      if (parseFloat(row.beforeSelfDeliverHours) > 0) {
        message += '交付学时' + row.userHours + '节?'
      } else {
        message += '?'
      }
      this.$confirm(message, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        schoolApi.rollback(row.id).then((res) => {
          this.$message({
            type: 'success',
            message: '退回成功'
          })
        }).catch(() => {
          this.$message({
            type: 'error',
            message: '退課失败'
          })
        })
          .catch(() => {
            this.$message({
              type: 'info',
              message: '已取消退課'
            })
          })
      })
    },
    //新增操作
    clickAdd() {
      this.dialogVisible = true;
    },
    // 打开修改登陆账号
    openLogin() {
      this.showLoginAccount = true
    },
    closeLogin() {
      this.showLoginAccount = false
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val;
      this.fetchData();
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val;
      this.fetchData();
    },
    //导出
    exportFlow() {
      const that = this;
      that.exportLoading = true;
      merchantAccountFlowApi.studentSimpleExecl(that.dataQuery).then(res => {
        const url = window.URL.createObjectURL(res);
        const link = document.createElement("a");
        link.style.display = "none";
        link.href = url; // 获取服务器端的文件名
        link.setAttribute("download", "课程流水表.xls");
        document.body.appendChild(link);
        link.click();
        that.exportLoading = false;
      }).catch(e => {
        that.exportLoading = false;
      });
    },
    //获取变动类型
    getFinanceAccountsType() {
      var enType = "CourseAccountsType";
      enTypes.getEnumerationAggregation(enType).then(res => {
        this.financeAccountsType = res.data;
      })
    },
  }
};
</script>

<style>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.period-table td,
.period-table th {
  text-align: center;
}

.mt20 {
  margin-top: 20px;
}
</style>
