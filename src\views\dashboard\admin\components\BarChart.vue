<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import  echarts from 'echarts'
require('echarts/theme/macarons') // echarts theme
import resize from './mixins/resize'

const animationDuration = 6000

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '300px'
    },
    chartData: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler(val) {
        this.setOptions(val)
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el, 'macarons')
      this.setOptions(this.chartData)
    },
    setOptions({ expectedData, xAxisData} = {}) {
      this.chart.setOption({
        // tooltip: {
        //   trigger: 'axis',
        //   axisPointer: { // 坐标轴指示器，坐标轴触发有效
        //     type: 'line' // 默认为直线，可选为：'line' | 'shadow'
        //   }
        // },
        grid: {
          top: 50,
          left: '10%',
          right: '2%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: [{
          type: 'category',
          data: xAxisData,
          axisTick: {
            alignWithLabel: true
          },
          axisLine:{
            lineStyle:{
              color:'#000000'
            }
          }
        }],
        yAxis: [{
          type: 'value',
          axisTick: {
            show: true
          },
          axisLine:{
            lineStyle:{
              color:'#000000'
            }
          }
        }],
        series: [{
          name: '初中一般水平',
          itemStyle: {
            normal: {
              label:{
                // show:true
              },
              //这里是重点
              color: function (params) {
                  var colorList = ['#d48265', '#749f83'];
                  return colorList[params.dataIndex]
              },
            }
          },
          type: 'bar',
          barWidth: '50%',
          data: expectedData,
          animationDuration
        },
        // {
        //   name: '现实掌握词汇量',
        //   itemStyle: {
        //     normal: {
        //       label:{
        //         // show:true
        //       },
        //       color: '#749f83',
        //     }
        //   },
        //   type: 'bar',
        //   // stack: 'vistors',
        //   barWidth: '30%',
        //   data: actualData,
        //   animationDuration
        // },
        ]
      })
    }
  }
}
</script>
