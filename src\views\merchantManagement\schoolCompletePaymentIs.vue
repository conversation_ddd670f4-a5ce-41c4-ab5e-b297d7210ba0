<template>
  <div class="app-container" v-loading="fullscreenLoading" element-loading-text="获取合同签署状态中..." element-loading-spinner="el-icon-loading" element-loading-background="rgba(255,255,255)">
    <el-row class="mt-2">
      <el-col :span="20" :offset="2">
        <el-steps :active="steps - 1" finish-status="success" process-status="finish">
          <el-step title="签署合同">
            <template slot="description">
              查看手机短信访问链接或
              <br />
              扫码签署开通合同
            </template>
          </el-step>
          <el-step title="选择完款方式">
            <template slot="description">
              选择以何种方式完款
              <br />
              开通门店
            </template>
          </el-step>
          <el-step title="进行完款" description="完款进行中"></el-step>
          <el-step title="门店开通成功" description="已经完成了"></el-step>
        </el-steps>
      </el-col>
    </el-row>
    <!-- 第一步 -->
    <el-row type="flex" justify="center" v-if="steps == 1">
      <el-col :span="6" style="text-align: center" class="mt-6" v-for="item in QRcodeList[QRcodeIndex]">
        <p style="line-height: 30px">
          {{ item.tips }}
          <br />
          {{ item.title }}
        </p>
        <div class="QR_code" @click="open(item)" v-loading="codeLoading">
        <!-- <div class="QR_code" v-loading="codeLoading"> -->
          <div :class="['QR_code_success', { deactive: !item.isSign }]">
            <i class="el-icon-success"></i>
            <p>签署成功</p>
          </div>
          <el-tooltip class="item" effect="dark" content="点击复制签署链接">
            <img @error="retry($event)" :src="item.img" @click="fallbackCopyText(item.signUrl ? item.signUrl : item.img)"></img>
          </el-tooltip>
        </div>
      </el-col>
    </el-row>
    <!-- 第二步 -->
    <el-row type="flex" justify="center" v-if="steps == 2">
      <el-col :span="6" style="display: grid; justify-content: center; justify-items: center" class="mt-6" v-for="item in paymentList">
        <div :class="['school_complete_paymentIs_box', { active: item.isSelect }]" @click="checkPayment(item)">
          <i class="el-icon-success"></i>

          <div class="school_complete_paymentIs">
            <img :src="item.img" alt="" draggable="false" />
          </div>
          <p>{{ item.title }}</p>
        </div>
        <p class="mt-2">{{ item.tips }}</p>
      </el-col>
    </el-row>
    <!-- 第三步 -->
    <el-row type="flex" justify="center" v-if="steps == 3">
      <!-- <el-col :span="24" :offset="0" style="display: grid; align-content: space-evenly; justify-items: center">
        <el-row type="flex" justify="center" class="mt-4">请完成门店开通支付</el-row>
        <el-row type="flex" justify="center" class="mt-4">
          <el-button @click="isPaymentIsComplete(true)" class="button_d">已完成支付</el-button>
          <el-button type="primary" @click="goPay" class="button_d">去支付</el-button>
        </el-row>
      </el-col> -->
    </el-row>
    <!-- 第四步 -->
    <el-row v-if="steps >= 4" style="height: 250px; flex-wrap: wrap" align="center" type="flex">
      <el-col :span="24" :offset="0" style="display: grid; align-content: space-evenly; justify-items: center">
        <el-row type="flex" justify="center" v-if="steps >= 4">
          <i class="el-icon-success create-success"></i>
        </el-row>
        <el-row type="flex" justify="center" v-if="steps >= 4">恭喜您，门店开通成功！</el-row>
      </el-col>
    </el-row>
    <!-- 下提示/操作 -->
    <el-divider class="mt-4"></el-divider>
    <el-row type="flex" justify="center">
      <el-col :span="4" :xs="20" type="flex" justify="center" style="display: flex; justify-content: space-between" class="mt-2">
        <el-button @click="goBack()" class="button_d button_center">返回</el-button>
        <el-button type="primary" @click="nextStep" class="button_d button_center" v-if="steps <= 3">下一步</el-button>
        <!-- <el-button type="primary" @click="goBack" class="button_d" v-else>蓝色的返回</el-button> -->
      </el-col>
    </el-row>
    <el-row class="mt-2 info" v-if="steps <= 1">全部合同签署成功后，如未跳转，请点击下一步按钮</el-row>
    <el-row class="mt-2 info" v-else-if="steps == 2">点击选择完款方式，点击另一个可以切换</el-row>
    <el-row class="mt-2 info" v-else-if="steps >= 4">{{ surplustime }}秒后自动跳转</el-row>
    <!-- 合同购买 -->
    <purchaseContract ref="spurchaseDialogVisible" />
    <!-- 抵扣完款确认框 -->
    <el-dialog :visible.sync="dialogDeductionVisible" width="25%" center>
      <p class="dialog-dialogDeleteVisible-title">请确认是否分配学习管理系统数并支付{{ merchantDeduction }}元技术服务费，为以下合伙人开通学习管理系统账号，开通后将无法撤回</p>
      <p class="dialog-dialogDeleteVisible-tip" style="color: #f00">{{ schoolInfo.merchantName || '' }}</p>
      <el-row align="middle" justify="center" type="flex" class="mt-20">
        <el-button @click="dialogDeductionVisible = false" class="button-d">取消</el-button>
        <el-col :span="2"></el-col>
        <el-button @click="Payment()" type="primary" class="button-d">确认</el-button>
      </el-row>
    </el-dialog>
    <!-- 支付确认框 -->
    <el-dialog class="pay-dialog" title="请完成门店开通支付" :visible.sync="dialogPayVisible" width="30%" :close-on-click-modal="false" :close-on-press-escape="false" :show-close="false">
      <span></span>
      <span slot="footer">
        <el-button @click="isPaymentIsComplete(true)" class="button_d">已完成支付</el-button>
        <el-button type="primary" @click="reselectPay" class="button_d">未完成支付</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import { ossPrClient } from '@/api/alibaba';
import orderApi from '@/api/schoolCompletePaymentIs.js';
import schoolApi from '@/api/schoolList';
import areasschoolApi from '@/api/areasSchoolList';
import { dxSource, pageParamNames } from '@/utils/constants';
import checkPermission from '@/utils/permission';
import ls from '@/api/sessionStorage';
import purchaseContract from '@/components/purchaseContract/index.vue'; // 合同购买组件
import store from '@/store';

export default {
  name: 'schoolCompletePaymentIs',
  components: {
    purchaseContract
  },
  data() {
    return {
      fullscreenLoading: true, // 全屏加载
      retryCount: 3, // 图片加载失败重试次数
      merchantDeduction: 0, // 抵扣完款金额
      dialogDeductionVisible: false, // 抵扣完款确认框
      dialogPayVisible: false, // 支付确认框
      paymentIsComplete: false, // 是否完款
      surplustime: 0, // 剩余时间
      st: null, // 剩余时间定时器
      steps: 1, // 当前步骤
      QRcodeIndex: 0, // 当前签署第几页合同 ，大于等于 合同列表-QRcodeIndex 的长度表示已经全部签署完成
      // 合同列表
      QRcodeTitleList: [], // 合同列表
      // 合同列表
      QRcodeList: [
        [{ img: '', tips: '邀请门店负责人使用手机签署', title: '《委托回本合同》', isSign: false }],
        [
          { isFirstParty: 0, img: '', tips: '请您使用手机扫码签署', title: '《鼎校智能学习管理系统分销、零售合同》', isSign: false },
          { isFirstParty: 1, img: '', tips: '邀请门店负责人，使用手机扫码签署', title: '《鼎校智能学习管理系统分销、零售合同》', isSign: false }
        ],
        [
          { isFirstParty: 0, img: '', tips: '邀请绑定的推广大使门店负责人，使用手机扫码签署', title: '《课程推广合作协议》', isSign: false },
          { isFirstParty: 1, img: '', tips: '邀请门店负责人，使用手机扫码签署', title: '《课程推广合作协议》', isSign: false }
        ]
      ],
      paymentList: [
        { type: 1, title: '线上支付完款', tips: '在线扫码支付完款所需费用', img: 'http://dx-applet-mall.oss-cn-shanghai.aliyuncs.com/manage/1744005088000', isSelect: false },
        {
          type: 2,
          title: '账号开通抵扣',
          tips: '使用学习管理系统数抵扣开通名额，一键完成开通流程',
          img: 'https://document.dxznjy.com/course/d8e1944c0c554f438cad10252532be2d.png',
          isSelect: false
        }
      ],
      paymentMethod: null, // 完款方式
      schoolInfo: {}, // 门店信息
      codeLoading: false, // 电子签加载
      signLoading: false, // 合同签署加载
      st0: null // 委托回本定时器/完款定时器/合同签署定时器
    };
  },
  computed: {
    ...mapGetters(['setpayUrl', 'token']),
    // 当前页 是否全部签署完成
    isAllSign() {
      return this.QRcodeList[this.QRcodeIndex].every((item) => item.isSign);
    },
    // 是否 最后一页
    lastPage() {
      return this.QRcodeIndex + 1 >= this.QRcodeList.length;
    }
  },
  watch: {
    // // 页码改变
    // QRcodeIndex() {
    //   if (this.isAllSign) {
    //     console.log('去下一页1', this.QRcodeIndex);

    //     this.clearEsignInterval(this.st0); // 清空定时器
    //     this.nextStep();
    //   }
    // },
    // 监听当前页是否全部签署完成
    isAllSign() {
      console.log('改变', this.isAllSign, this.QRcodeIndex);
      if (this.isAllSign) {
        // console.log('去下一页2', this.QRcodeIndex);
        this.clearEsignInterval(this.st0); // 清空定时器
        this.nextStep();
      }
    }
  },
  created() {
    ossPrClient();
    /**
     * id 门店id
     * signingStatus 委托回本合同签署状态:0已签署1未签署2无需签署//未签署需要签署
     * coursePromotionState 课程推广合作协议签署状态:2已签署其他未签署
     */
    let id = this.$route.query.id
    let signingStatus = this.$route.query.signingStatus
    if (id === undefined || signingStatus === undefined) {
      this.$message.error('参数错误');
      this.$router.back();
      return;
    }
    this.init(id, signingStatus);
  },
  // activated() {
  //   console.log(this.schoolInfo, 'this.schoolInfo');
  //   this.steps = 1; // 初始化
  //   ossPrClient();
  //   this.init(ls.getItem('schoolId'));
  // },
  methods: {
    // // 完美
    // perfect() {
    //   let that = this;
    //   console.log(this.schoolInfo);

    //   this.steps = 5;
    // },
    // 初始化
    init(id, signingStatus) {
      const that = this;
      this.codeLoading = true; // 加载中
      //signingStatus 委托回本合同签署状态:0已签署1未签署2无需签署//未签署需要签署
      if (signingStatus == 0 || signingStatus == 2) {
        ++this.QRcodeIndex;// 下一页
        that.QRcodeList[0][0].isSign = true;// 委托回本合同签署完成
      }
      schoolApi.schoolDetailV2(id).then(async (res) => {
        let loginMerchantCode = localStorage.getItem('loginMerchantCode');

        if (res.data.refereeCode == loginMerchantCode) {
          that.QRcodeList[2][0].tips = '请您使用手机扫码签署';
        }
        // 保存门店信息
        that.schoolInfo = res.data;
        // 获取完款状态
        that.paymentIsComplete = res.data.paymentIsComplete == 1;
        // 完款直接退出
        if (that.paymentIsComplete) {
          console.log("🚀 ~ schoolApi.schoolDetailV2 ~ '您已完款,无需再次操作':", '您已完款,无需再次操作');
          that.$message.success('您已完款,无需再次操作');
          that.$router.go(-1);
          return;
        }
        // 课程推广合作协议签署完成
        // if (res.data.ambassadorsStatus == 2) {
        //   console.log('res.data.esignReconciliationStatus == 2');

        //   this.QRcodeList[2].forEach((item) => (item.isSign = true)); // 课程推广合作协议签署完成
        // }
        // 保存 推广大使合同 信息
        this.QRcodeTitleList[2] = {
          signFlowId: res.data.signFlowId,
          templateType: res.data.templateType,
          signSource: res.data.signSource
        };
        // if (this.paymentIsComplete) return (this.steps = 2);
        ////委托回本合同签署状态:0已签署1未签署2无需签署//未签署需要签署
        if (res.data.paybackPlanType == 1 || res.data.signingStatus == 0 || res.data.signingStatus == 2) {
          this.getContractStatus(true);
          // console.log('🚀 ~ schoolApi.schoolDetailV2 ~ this.getContractStatus(true);:', this.getContractStatus(true));
        } else {
          this.fullscreenLoading = false; // 加载完成
          // 获取《委托回本计划合同》e签宝二维码 并 轮询 查询《委托回本计划合同》是否签署
          this.getEsignCode();
          this.getContractStatus();
        }
      });
    },
    // 复制
    fallbackCopyText(text) {
      const textArea = document.createElement('textarea');
      textArea.value = text;
      textArea.style.position = 'fixed'; // 避免滚动到底部
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();

      try {
        const successful = document.execCommand('copy');
        this.$message({
          message: successful ? '复制成功！' : '复制失败',
          type: successful ? 'success' : 'warning',
          duration: 1500
        });
        // console.log(successful ? '复制成功！' : '复制失败');
      } catch (err) {
        console.error('无法复制:', err);
      }
      document.body.removeChild(textArea);
    },
    // 返回
    goBack() {
      this.$router.go(-1);
    },
    // 查询 学习系统合同 信息
    getContractStatus(start = false) {
      let that = this;
      orderApi.getContractStatus({ merchantCode: this.schoolInfo.merchantCode }).then((res) => {
        console.log(res.data, 'res.data');
        // flowId: item.signFlowId, templateType: item.templateType, signSource: item.signSource
        if (res.data.esignReconciliationStatus == 2) {
          this.QRcodeList[1].forEach((item) => (item.isSign = true)); // 学习系统合同签署完成
        }
        // 保存 学习系统合同 信息
        this.QRcodeTitleList[1] = {
          signFlowId: res.data.signFlowId,
          templateType: res.data.templateType,
          signSource: res.data.signSource
        };
        if (start) {
          // 先判断本页是否全部签署完成
          if (this.QRcodeList[this.QRcodeIndex].every((item) => item.isSign)) {
            // console.log('3夏夜也完成直接去下一页', this.QRcodeIndex);
            if (this.st0) this.clearEsignInterval(this.st0); // 清空定时器
            this.QRcodeIndex++
            this.signInterval(); // 开启轮询
          } else {
            this.signInterval(); // 开启轮询
          }
        }
      });
    },
    // 轮询 -获取签署链接 及 签署状态
    signInterval() {
      let that = this;
      // 删除已存在的定时器
      if (this.st0) this.clearEsignInterval(this.st0);
      this.codeLoading = true; // 加载中
      // 轮询(获取签署状态)
      that.getSignStatus(false, 1);

      that.st0 = setInterval(() => {
        // 获取签署状态 及 获取签署链接
        that.getSignStatus();
      }, 15000);

      // 10分钟后清除定时器
      setTimeout(() => {
        that.clearEsignInterval(that.st0);
      }, 10 * 60 * 1000);
    },
    // 获取签署链接 及 签署状态
    getSignStatus(tip = false, isQrLink = 0) {
      // console.log('🚀 ~ getEsignStatus ~ tip:', this.codeLoading);
      let that = this;
      if (this.signLoading && tip) {
        that.$message.warning('请勿重复点击，正在加载中');
        return;
      }
      that.signLoading = true; // 加载中
      if (this.codeLoading && tip) {
        that.$message.warning('请先等待合同码加载完成');
        return;
      }
      let item = this.QRcodeTitleList[this.QRcodeIndex];
      orderApi.fetchContractQrLink({ flowId: item.signFlowId, templateType: item.templateType, signSource: item.signSource, isQrLink }).then((res) => {
        // 如果还在加载
        if (this.fullscreenLoading) {
          let isAllSign = res.data.every((result) => {
            return result.isContract == 0 || result.signStatus == 2;// 存在合同且签署成功
          });
          // 不是全部签署完成，弹窗显示，关闭加载
          if (!isAllSign) {
            this.fullscreenLoading = false; // 加载完成 
          }
        }
        res.data.some((result) => {
          /**
           * mobile 手机号
           * isFirstParty 是否甲方：1-否 0-是
           * signStatus 签署状态1-待签署 2-签署成功 3-签署失败
           * qrUrl 二维码地址
           * signUrl 原始地址
           * isContract 是否存在合同: 0-否 1-是
           */
          // this.QRcodeList[this.QRcodeIndex].forEach((item, i) => {
          for (let i = that.QRcodeList[that.QRcodeIndex].length - 1; i >= 0; i--) {
            let item = that.QRcodeList[that.QRcodeIndex][i];
            // 如果类型相同
            if (item.isFirstParty == result.isFirstParty) {
              // item.img = result.value.qrUrl;
              // 图片有值就赋值
              if (result.qrUrl) {
                this.retryCount = 5; // 重试次数
                item.img = result.qrUrl
              }
              // 签署链接不一样 就赋值
              item.signUrl != result.signUrl && result.signUrl != '' ? (item.signUrl = result.signUrl) : '';
              // 签署成功修改状态
              if (result.signStatus == 2) {
                item.isSign = true;
              } else if (tip) {
                // 如果是手动下一步，只要有一个签署失败就提示 并退出some遍历
                that.$message.warning('请完成本页的合同签署再进行下一步');
                tip = false;
                return true;
              }
            }
          }
          // });
          return false; // 防止中断
        });
        that.codeLoading = false; // 加载中
        that.signLoading = false; // 加载中
      }).catch((err) => {
        that.codeLoading = false; // 加载中
        that.signLoading = false; // 加载中
        if (this.fullscreenLoading) this.fullscreenLoading = false; // 加载完成 
      });
    },
    // 下一步
    async nextStep() {
      let that = this;
      // 判断是否全部签署完成
      if ((this.lastPage && this.isAllSign) || this.steps > 1) {
        if (this.fullscreenLoading) this.fullscreenLoading = false; // 加载完成 
        if (this.steps == 1) {
          this.steps++;
          this.$message.success('全部合同签署成功');
          return;
        };
        if (this.steps == 3) return this.isPaymentIsComplete(true);

        if (this.steps == 2)
          if (!this.paymentMethod) {
            return this.$alert('请选择完款方式', '提示');
          } else {
            // console.log(this.paymentMethod, 'this.paymentMethod');

            if (this.paymentMethod == 2) {
              // 获取商户抵扣完款应付金额
              let res = await orderApi.getSchoolCompletePaymentIs({ merchantCode: this.schoolInfo.merchantCode, paymentType: this.paymentMethod });
              this.merchantDeduction = res.data.merchantDeduction;
              if (res.data.merchantDeduction > 0) {
                return (this.dialogDeductionVisible = true);
              }
              return;
            }
            return this.Payment();
          }
        if (this.steps == 4) {
          that.surplustime = 10; //开始倒计时
          that.st = setInterval(() => {
            that.surplustime--;
            if (that.surplustime == 0) {
              that.$router.go(-1);
            }
          }, 1000);
          return;
        }
        return;
      }
      // 判断当页是否全部签署完成
      // console.log('去下一页4', this.QRcodeIndex, this.isAllSign);
      if (!this.isAllSign) {
        if (this.QRcodeIndex == 0) return this.getEsignStatus(true);
        if (this.QRcodeIndex >= 1) return this.getSignStatus(true);
      } else {
        // const loading = this.$loading({
        //   lock: true,
        //   text: 'Loading',
        //   spinner: 'el-icon-loading',
        //   background: 'rgba(0, 0, 0, 0.7)'
        // });
        ++this.QRcodeIndex;
        // 页码改变
        if (this.QRcodeList[this.QRcodeIndex].every((item) => item.isSign)) {
          console.log('3夏夜也完成直接去下一页', this.QRcodeIndex);
          if (this.st0) this.clearEsignInterval(this.st0); // 清空定时器
          this.nextStep();
        } else {
          this.signInterval(); // 开启轮询
        }
        // setTimeout(() => {
        //   loading.close();
        // }, 200);
      }
      //   this.$router.push({ name: 'schoolCompletePaymentIs' });
    },
    // 确认完款方式
    Payment() {
      let that = this;
      // let amount;
      // if (this.paymentMethod == 1) {
      //   amount = 1;
      // } else {
      //   amount = 1000;
      // }
      orderApi
        .goPay({ merchantCode: this.schoolInfo.merchantCode, type: this.paymentMethod })
        .then((res) => {
          console.log(res.data, '支付');
          this.dialogDeductionVisible = false; // 关闭弹窗
          this.steps++;
          this.payData = res.data;
          that.sourceOrderId = res.data.sourceOrderId; // 订单id
          this.goPay();
          store.dispatch('getSystem'); // 刷新系统数
        })
        .catch((err) => {
          // console.log(err, '支付失败');
          this.dialogDeductionVisible = false; // 关闭弹窗
          // let magList = err.message.split('@');
          // let type = magList[1];
          // // console.log(type, magList);

          // // if (type === '1' || type === '2') {
          // //   // console.log('余合同数量不足');
          // //   // this.$confirm('您的剩余合同数量不足，如需创建俱乐部，请购买合同后重试', '提示', {
          // //   //   confirmButtonText: '去购买',
          // //   //   cancelButtonText: '取消',
          // //   //   type: 'none'
          // //   // })
          // //   //   .then(() => {
          // //   //     this.$refs.spurchaseDialogVisible.open();
          // //   //   })
          // //   //   .catch(() => {
          // //   //     this.$message({
          // //   //       type: 'info',
          // //   //       message: '已取消删除'
          // //   //     });
          // //   //   });
          // //   that.$message.error(magList[0]);

          // //   return false;
          // // }
          // that.$message.error(magList[0]);

          // 俱乐部前去采购
          if (checkPermission(['Operations'])) {
            this.$confirm('您的剩余学习管理系统数不足，如需完款门店，请采购后重试', '提示', {
              confirmButtonText: '去采购',
              cancelButtonText: '取消',
              type: 'none'
            })
              .then(() => {
                that.$router.push({
                  path: '/purchase/purchaseApply'
                });
              })
              .catch(() => { });
          } else if (checkPermission(['admin'])) {
            // that.$message.error(magList[0]);
            that.$message.warning('您的下级俱乐部剩余学习管理系统数不足，请联系下级俱乐部采购后重试');
          } else {
            // that.$message.error(magList[0]);
            that.$message.error(err);
          }
        });
    },
    reselectPay() {
      this.steps = 2;
      this.paymentMethod = null;
      this.paymentList.forEach((item) => {
        item.isSelect = false;
      });
      this.clearEsignInterval(this.st0); // 清除定时器
      this.dialogPayVisible = false; // 关闭支付弹窗
    },
    // 去支付
    goPay() {
      const split = dxSource.split('##'); //["ZNYY", "BROWSER", "WEB"]
      this.payData.dxSource = this.payData.registerCallIInfo.appSource + '##' + split[1] + '##' + split[2];
      let params = JSON.stringify(this.payData);
      let herf = window.location.href;
      const lastSlashIndex = herf.lastIndexOf('/');
      herf = herf.slice(0, lastSlashIndex) + '/schoolList';
      let req = 'token=' + this.token + '&params=' + params + '&back=' + herf;

      //需要编码两遍，避免出现+号等
      var encode = Base64.encode(Base64.encode(req));
      window.open(this.setpayUrl + 'product?' + encode, '_blank'); //新窗口打开
      this.getIsPaymentIsComplete(); // 开始轮询
      this.dialogPayVisible = true; // 打开支付弹窗
    },
    open(item) {
      item.isSign = !item.isSign;
    },
    // 选择完款方式
    checkPayment(item) {
      // console.log(item);
      this.paymentList.forEach((item) => {
        item.isSelect = false;
      });
      item.isSelect = true;
      this.paymentMethod = item.type;
      console.log(item);
    },
    // 轮询获取完款状态
    getIsPaymentIsComplete() {
      const that = this;
      if (that.st0) {
        that.clearEsignInterval(that.st0);
      }
      // 定时器轮询
      that.st0 = setInterval(that.isPaymentIsComplete, 15000);
      // 超时清除定时器
      const timeoutId = setTimeout(() => {
        that.clearEsignInterval(that.st0);
      }, 600000);
    },
    // 是否完款
    async isPaymentIsComplete(tip = false) {
      const that = this;
      schoolApi
        .schoolDetailV2(that.schoolInfo.id)
        .then(async (res) => {
          if (res.data.paymentIsComplete == 1) {
            that.clearEsignInterval(that.st0); // 清除定时器
            this.dialogPayVisible = false; // 关闭支付弹窗
            this.steps++;
            this.nextStep();
          } else {
            if (tip) {
              this.$alert('暂未查询到支付结果，请以实际支付扣款结果为准', '提示', {
                confirmButtonText: '确定'
              }).then(() => {
                this.goPay();
              });
            }
          }
        })
        .catch((err) => {
          that.$message.error(err.message ? err.message : err.data.message);
        });
      // let { data } = await orderApi.judgeOrderStatus({ sourceOrderId: this.sourceOrderId });
      // if (data == 3) {
      //   that.clearEsignInterval(that.st0); // 清除定时器
      //   this.dialogPayVisible = false; // 关闭支付弹窗
      //   this.steps++;
      //   this.nextStep();
      // } else {
      //   if (tip) {
      //     this.$alert('暂未查询到支付结果，请以实际支付扣款结果为准', '提示', {
      //       confirmButtonText: '确定'
      //     });
      //   }
      // }
    },
    // 获取《委托回本计划合同》e签宝二维码 并 轮询 查询《委托回本计划合同》是否签署
    async getEsignCode() {
      const that = this;
      // console.log(this.schoolInfo.id,"code");
      this.codeLoading = true;
      if (!this.conDialogVisible) {
        this.conDialogVisible = true;
        setTimeout(() => window.scrollTo(0, 0), 0);
      }
      //获取e签宝二维码
      let res = await areasschoolApi.getEsignCode(this.schoolInfo.id); // 重新获取二维码
      if (!res.data) {
        res = await schoolApi.getEsignCode(this.schoolInfo.id); // 没有则获取二维码
      }
      // console.log(res.data, '二维码');

      this.QRcodeList[0][0].img = res.data;
      this.codeLoading = false;
      //查询轮询时间
      schoolApi.getEsignPollingTime(that.schoolInfo.id).then((res) => {
        // 定时器轮询
        that.st0 = setInterval(that.getEsignStatus, 15000);
        // 超时清除定时器
        let s = res.data.time * 60 * 1000;
        const timeoutId = setTimeout(() => {
          that.clearEsignInterval(that.st0);
        }, s);
      });
    },
    // 查询《委托回本计划合同》是否签署
    getEsignStatus(tip = false) {
      const that = this;
      if (this.signLoading && tip) {
        that.$message.warning('请勿重复点击，正在加载中');
        return;
      }
      that.signLoading = true;
      if (this.codeLoading && tip) {
        that.$message.warning('请先等待合同码加载完成');
        return;
      }
      schoolApi
        .getEsignStatus(that.schoolInfo.id)
        .then((res) => {
          that.signLoading = false;
          if (res.data.status == 0) {
            if (tip) {
              this.$alert('请使用手机扫码，签署《委托回本计划合同》', '提示', {
                confirmButtonText: '确定'
              });
            }
          } else {
            that.$message.success('签署成功!');
            // 成功清除定时器
            that.clearEsignInterval(that.st0);
            // 改变状态
            this.QRcodeList[0][0].isSign = true;
          }
        })
        .catch((err) => {
          that.$message.error(err.message ? err.message : err.data.message);
        });
    },
    //清除轮询定时器
    clearEsignInterval(st) {
      clearInterval(st);
      st = null;
    },
    /**
    * @arguments_1 img元素实例,this
    * @arguments_2 重试次数
    * @return void
    */
    retry(event) {
      const imgElement = event.target
      let that = this;

      console.log(`图片加载失败,将会重试${this.retryCount}次`);
      if (this.retryCount-- > 0) {
        // 重定向图片路径, 引起重新加载
        imgElement.src = imgElement.src + '?t=' + Date.now(); // 强制刷新
      } else {
        console.log('图片加载失败, 请检查网络或联系管理员');
        if (this.retryTimer) that.clearEsignInterval(this.retryTimer); // 清除旧定时器
        // 次数超过后取消错误监听, 重置重试次数
        this.retryTimer = setTimeout(() => {
          that.retryCount = 5;
        }, 1000);
      }
    },
  },
  beforeDestroy() {
    if (this.st) this.clearEsignInterval(this.st);
    if (this.st0) this.clearEsignInterval(this.st0);
  }
};
</script>

<style lang="scss" scoped>
.mt-2 {
  margin-top: 20px;
}
.mt-4 {
  margin-top: 40px;
}
.mt-6 {
  margin-top: 60px;
}
p {
  margin: 0;
  color: #666;
}
::v-deep .is-finish {
  .is-text {
    color: #fff;
    background-color: #1890ff;
  }
}
:deep(.el-step__description) {
  color: #a7a2a2; /* 设置描述文字颜色 */
}
:deep(.el-step__description.is-wait) {
  color: #c0c4cc;
}
.button_d {
  width: 100px !important;
}
.button_center {
  margin: 0 auto;
}
.school_complete_paymentIs_box {
  user-select: none;
  -webkit-user-drag: none;
  position: relative;
  width: 300px;
  height: 300px;
  text-align: center;
  display: grid;
  grid-template-rows: 250px;
  place-items: center;
  border-radius: 10px;

  i {
    display: none;
  }
  P {
    font-weight: 600;
  }
  &:hover {
    background-color: #efefef;
  }
  &.active {
    background-color: #efefef;

    i {
      top: 0;
      right: 0;
      position: absolute;
      display: block;
      font-size: 46px;
      color: #67c23a;
    }
  }
}
.school_complete_paymentIs {
  position: relative;
  width: 220px;
  height: 140px;
  img {
    width: 220px;
    display: block;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
}
.QR_code {
  position: relative;
  width: 200px;
  height: 200px;
  margin: 20px auto;
  .QR_code_success {
    width: 200px;
    height: 200px;
    position: absolute;
    z-index: 1;
    top: 0;
    left: 0;
    background-color: rgba(255, 255, 255, 0.85);

    display: grid;
    grid-template-rows: repeat(auto-fit, 50px);
    align-content: center;
    align-items: center;
    justify-items: center;
    i {
      font-size: 35px;
      color: #67c23a;
    }

    &.deactive {
      display: none;
    }
  }
  img {
    width: 100%;
    height: 100%;
  }
}
.create-success {
  font-size: 60px;
  color: #20b759;
}
.info {
  color: #666;
  text-align: center;
}
::v-deep.el-dialog__wrapper {
  text-align: center;
  white-space: nowrap;
  overflow: auto;
  &:after {
    content: '';
    display: inline-block;
    vertical-align: middle;
    height: 100%;
  }
  .el-dialog {
    margin: 30px auto !important;
    display: inline-block;
    vertical-align: middle;
    text-align: center;
    white-space: normal;
  }
}

// 抵扣完款确认框
.dialog-dialogDeleteVisible-title {
  font-size: 16px;
}
.dialog-dialogDeleteVisible-tip {
  font-size: 16px;
  margin: 20px 0 30px;
  text-align: center;
}
.pay-dialog {
  :deep(.el-dialog__header) {
    text-align: left;
  }
}
</style>
