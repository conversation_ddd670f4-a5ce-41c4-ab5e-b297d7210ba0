<template>
  <el-dialog title="绑定合伙人" :visible.sync="dialogVisible" width="800px" @close="handleClose">
    <div class="main">
      <div class="not-selected">
        <div class="title">未选择</div>
        <div class="search-area">
          <el-input size="mini" placeholder="请输入门店名称/手机号" v-model="keywords" clearable></el-input>
          <el-button type="primary" size="mini" class="search-btn" @click="searchData">搜索</el-button>
        </div>
        <div class="store-list" v-if="storeList.length > 0">
          <div v-for="(item, index) in storeList" :key="index" :class="{ 'store-item': true, disabled: item.bindingStatus === 1 }">
            <div class="store-info">
              <div class="store-name">{{ item.merchantName }}</div>
              <div class="store-attach">{{ item.realName }}</div>
              <div class="store-phone">{{ item.name }}</div>
            </div>
            <div v-if="item.bindingStatus === 0" class="el-icon-circle-plus-outline" @click="addStore(item, index)"></div>
            <div v-else class="hasGroup">已绑定群聊</div>
          </div>
          <!-- 加载更多 -->
          <div class="load-more" v-if="storeList.length > 0">
            <template v-if="!dataLoading">
              <el-button v-if="!lastPage" type="text" size="mini" @click="loadMore">点击加载更多</el-button>
              <span v-else-if="lastPage && listPage.totalPage > 1">没有更多了</span>
            </template>
            <span v-else>加载中···</span>
          </div>
        </div>
        <div class="store-list" v-else>
          <p class="no-more">暂无数据</p>
        </div>
      </div>
      <div class="el-icon-right"></div>
      <div class="selected">
        <div class="title">已选择</div>
        <div class="store-list">
          <div class="store-item" v-for="(item, index) in inStoreList" :key="index">
            <div class="store-info">
              <div class="store-name">{{ item.merchantName }}</div>
              <div class="store-attach">{{ item.realName }}</div>
              <div class="store-phone">{{ item.name }}</div>
            </div>
            <div class="el-icon-remove-outline" @click="removeStore(item, index)"></div>
          </div>
        </div>
      </div>
    </div>

    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button :loading="bindLoading" type="primary" @click="submitBind">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  import groupChatApi from '@/api/newPartner/groupChat';
  export default {
    props: {
      dialogVisible: {
        type: Boolean,
        default: false
      },
      targetGroupChatId: {
        type: String,
        default: ''
      }
    },
    data() {
      return {
        keywords: '',
        storeList: [],
        initialStoreList: [],
        inStoreList: [],
        listPage: {
          currentPage: 1,
          pageSize: 20,
          totalPage: 0
        },
        dataLoading: false,
        selectedIndexList: [],
        bindLoading: false
      };
    },
    computed: {
      lastPage() {
        return this.listPage.currentPage === this.listPage.totalPage;
      }
    },
    methods: {
      handleClose(action) {
        this.storeList = [];
        this.inStoreList = [];
        this.initialStoreList = [];
        this.selectedIndexList = [];
        this.listPage.currentPage = 1;
        this.listPage.totalPage = 0;
        this.keywords = '';
        this.$emit('closeBindDialog', action);
      },
      addStore(item, index) {
        this.storeList.splice(index, 1);
        this.inStoreList.push(item);
        // 保存被选择的门店在初始列表中的位置
        const index2 = this.initialStoreList.findIndex((el) => el.id === item.id);
        this.selectedIndexList.push(index2);
      },
      removeStore(item, index) {
        const index1 = this.initialStoreList.findIndex((el) => el.id === item.id);
        if (index1 !== -1) {
          const index2 = this.selectedIndexList.findIndex((el) => el === index1);
          this.selectedIndexList.splice(index2, 1);
          // 获取被移除的合伙人在未选择合伙人列表的位置
          const beforeLen = index1 - this.selectedIndexList.filter((el) => el < index1).length;
          this.storeList.splice(beforeLen, 0, item);
        }
        this.inStoreList.splice(index, 1);
      },
      searchData() {
        if (!this.keywords) {
          this.$message.warning('请先输入门店名称/手机号！');
          return;
        }
        const params = {
          pageNum: this.listPage.currentPage,
          pageSize: this.listPage.pageSize,
          roleTag: 'School', // 门店角色
          commonName: this.keywords
          // groupChatId: this.targetGroupChatId //群聊管理列表id
        };
        this.dataLoading = true;
        if (this.listPage.currentPage === 1) {
          this.storeList = [];
        }
        groupChatApi
          .getStoreListByTag(params)
          .then((res) => {
            if (res.success && res.code == 20000) {
              const filterData = res.data.data.filter((el) => {
                return this.inStoreList.findIndex((el2) => el2.id === el.id) === -1;
              });
              // .filter((el) => el.bindingStatus === 0);
              if (this.listPage.currentPage > 1) {
                this.storeList = this.storeList.concat(filterData);
              } else {
                this.storeList = filterData;
                this.selectedIndexList = [];
              }
              this.initialStoreList = this.storeList.slice();
              this.listPage.totalPage = Number(res.data.totalPage);
              this.listPage.currentPage = Number(res.data.currentPage);
            }
          })
          .finally(() => {
            this.dataLoading = false;
          });
      },
      // 加载下一页数据
      loadMore() {
        this.listPage.currentPage++;
        this.searchData();
      },
      // 提交绑定合伙人数据
      submitBind() {
        this.bindLoading = true;
        const merchantCodeList = this.inStoreList.map((item) => item.merchantCode);
        const params = {
          merchantCodeList: merchantCodeList,
          targetGroupChatId: this.targetGroupChatId
        };
        groupChatApi
          .bindPartner(params)
          .then((res) => {
            if (res.success && res.code == 20000) {
              this.$message.success('绑定成功！');
              this.handleClose('update');
            }
          })
          .finally(() => {
            this.bindLoading = false;
          });
      }
    }
  };
</script>

<style lang="scss" scoped>
  .main {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .not-selected,
  .selected {
    border: 1px solid #ccc;
    border-radius: 10px;
    padding: 10px;
    box-sizing: border-box;
    width: 300px;
    height: 440px;
    display: flex;
    flex-direction: column;
  }
  .store-list {
    flex: 1;
    overflow-y: auto;
    /* 滚动条样式 */
    &::-webkit-scrollbar {
      width: 4px;
    }
    /* 滑块样式 */
    &::-webkit-scrollbar-thumb {
      background-color: #ccc;
      border-radius: 4px;
    }
    /* 滚动条轨道样式 */
    &::-webkit-scrollbar-track {
      background-color: #f2f2f2;
      border-radius: 4px;
    }
  }
  .not-selected .title,
  .selected .title {
    font-size: 16px;
    text-align: center;
    margin-bottom: 10px;
    color: #333;
  }
  .search-area {
    display: flex;
    justify-content: flex-start;
    margin-bottom: 16px;
  }
  .search-btn {
    margin-left: 10px;
  }
  .search-area .el-input--medium .el-input__inner {
    height: 30px !important;
    line-height: 30px !important;
  }
  .store-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border: 1px solid #dcdfe6;
    border-radius: 10px;
    padding: 10px;
    box-sizing: border-box;
    margin-bottom: 10px;
  }
  .store-item.disabled {
    background-color: #eee;
  }
  .store-phone,
  .store-attach {
    display: inline-block;
    margin-top: 10px;
  }
  .store-phone {
    margin-left: 10px;
  }
  .el-icon-circle-plus-outline,
  .el-icon-remove-outline {
    font-size: 20px;
    flex-shrink: 0;
    margin-left: 10px;
  }
  .hasGroup {
    width: 40px;
    text-align: center;
    font-size: 12px;
  }
  .el-icon-circle-plus-outline {
    color: rgb(207, 18, 18);
  }
  .el-icon-right {
    font-size: 36px;
    margin-left: 20px;
    margin-right: 20px;
  }
  .load-more,
  .no-more {
    text-align: center;
  }
</style>
