<template>
  <div class="app-container">
    <!-- 搜索 -->
    <el-form :inline="true" ref="form" class="SearchForm" style="padding-top: 30px">
      <el-row>
        <el-col :span="8" :xs="24">
          <el-form-item label="课程所属:">
            <span>{{ courseName }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="听力类型:">
            <el-select style="width: 100%" v-model="dataQuery.listeningCategory" filterable value-key="value" placeholder="请选择">
              <el-option v-for="(item, index) in listeningCategoryList" :key="index" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="排序:">
            <el-input placeholder="请输入数字" @input="handleInput" type="number" v-model="dataQuery.listeningOrder"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8" :xs="24">
          <el-form-item label="听力分类:" v-show="dataQuery.listeningCategory === 1">
            <el-select style="width: 100%" v-model="dataQuery.listeningType" filterable value-key="value" placeholder="请选择">
              <el-option v-for="(item, index) in listeningCategoryClassificationList" :key="index" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="听力名称:">
            <el-input placeholder="请输入听力名称" clearable v-model="dataQuery.listeningName" @keyup.enter.native="fetchData()"></el-input>
          </el-form-item>
        </el-col>
        <el-col :style="{ display: 'flex', justifyContent: 'flex-end', marginTop: '-58px', paddingRight: '25px' }">
          <el-button type="primary" icon="el-icon-search" @click="fetchDataSearch()">搜索</el-button>
          <el-button type="info" icon="el-icon-refresh" @click="fetchDataReset()">重置</el-button>
        </el-col>
      </el-row>
    </el-form>
    <div class="SearchForm">
      <!-- 添加 -->
      <div class="btn-add">
        <el-button size="small" type="success" @click="goBack()">返回全能听力课程列表</el-button>
        <el-button size="small" type="primary" icon="el-icon-plus" @click="addArticleHandle()">添加</el-button>
      </div>
      <!-- 表格 -->
      <el-table class="course-table" :data="tableData" stripe border :default-sort="{ prop: 'addTime', order: 'descending' }" v-loading="tableLoading">
        <el-table-column prop="materialsCode" label="听力编号"></el-table-column>
        <el-table-column prop="listeningCategoryDesc" label="听力类型"></el-table-column>
        <el-table-column prop="listeningTypeDesc" label="听力分类"></el-table-column>
        <el-table-column prop="listeningName" label="听力名称"></el-table-column>
        <el-table-column label="操作" width="300">
          <template slot-scope="scope">
            <el-button type="primary" size="large" @click="openTestQuestion(scope.row)">制作试题</el-button>
            <el-button type="success" size="large" @click="editListenHandle(scope.row.id)">编辑</el-button>
            <el-button type="danger" size="large" @click="deleteReading(scope.row.id)">删除</el-button>
          </template>
        </el-table-column>
        <el-table-column prop="listeningOrder" label="排序"></el-table-column>
      </el-table>
    </div>
    <!-- 分页 -->
    <el-col :span="20">
      <el-pagination
        :current-page.sync="tablePage.currentPage"
        :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="tablePage.totalItems"
        @size-change="handleSizeChange"
        :page-size="tablePage.size"
        @current-change="handleCurrentChange"
      />
    </el-col>
    <!-- 添加编辑弹窗 -->
    <el-dialog center :close-on-click-modal="false" :close-on-press-escape="false" :title="dialogTitle" :visible.sync="dialogVisible" width="55%" @close="dialogClose('ruleForm')">
      <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="100px" class="demo-ruleForm">
        <el-form-item label="听力排序：" prop="listeningOrder">
          <el-input v-model="ruleForm.listeningOrder" type="number" @input="handleInput" placeholder="请输入听力排序"></el-input>
        </el-form-item>
        <el-form-item label="听力类型：" prop="listeningCategory">
          <el-select style="width: 100%" v-model="ruleForm.listeningCategory" @change="changeListType" filterable value-key="value" placeholder="请选择听力类型">
            <el-option v-for="(item, index) in listeningCategoryList" :key="index" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="听力分类：" prop="listeningType" v-if="ruleForm.listeningCategory === 1">
          <el-select style="width: 100%" v-model="ruleForm.listeningType" filterable value-key="value" placeholder="请选择听力分类">
            <el-option v-for="(item, index) in listeningCategoryClassificationList" :key="index" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="听力名称：" prop="listeningName">
          <el-input v-model="ruleForm.listeningName" maxlength="50" show-word-limit placeholder="请输入听力名称"></el-input>
        </el-form-item>
        <el-form-item label="真题年份：" prop="listeningYear" v-if="ruleForm.listeningCategory === 2">
          <el-input
            v-model="ruleForm.listeningYear"
            type="tel"
            @input="ruleForm.listeningYear = String(ruleForm.listeningYear).replace(/[^\d]/g, '').slice(0, 4)"
            placeholder="请输入真题年份"
          ></el-input>
        </el-form-item>
        <el-form-item label="听力英文：" prop="listeningContent">
          <el-input type="textarea" v-model="ruleForm.listeningContent" placeholder="请输入听力英文"></el-input>
        </el-form-item>
        <el-form-item label="听力中文：" prop="listeningContentTranslation">
          <el-input type="textarea" v-model="ruleForm.listeningContentTranslation" placeholder="请输入听力中文"></el-input>
        </el-form-item>
        <el-form-item label="音频" prop="listenAudio">
          <my-upload
            @videoSucceed="videoSucceed"
            @uploadVideo="uploadVideohandle"
            :videoTableData="videoTableData"
            :videoShow="videoShow"
            ref="uploadVideoRef"
            v-model="ruleForm.listenAudio"
          />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogClose('ruleForm')">取 消</el-button>
        <el-button type="primary" @click="dialogConfirm('ruleForm')">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
  import listeningListAPI from '@/api/omnipotentListening/listeningAPI';
  import { pageParamNames } from '@/utils/constants';
  import MyUpload from '@/components/Upload/uploadAudio.vue';
  export default {
    components: { MyUpload },
    data() {
      return {
        // 分页
        tablePage: {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null
        },
        tableLoading: false,
        listeningCategoryList: [
          { value: 1, label: '练习模式' },
          { value: 2, label: '真题模式' }
        ],
        listeningCategoryClassificationList: [
          { value: 1, label: '短对话' },
          { value: 2, label: '长对话' },
          { value: 3, label: '独白' }
        ],
        dataQuery: {
          listeningCategory: '', // 听力类型
          listeningOrder: '', // 排序
          listeningType: '', // 听力分类
          listeningName: '' // 听力名称
        },
        isRouterAlive: true, //局部刷新
        tableData: [], //表格数据
        dialogTitle: '',
        dialogVisible: false,
        ruleForm: {
          listeningOrder: '',
          listeningCategory: '',
          listeningType: '',
          listeningName: '',
          listeningYear: '',
          listeningContent: '',
          listeningContentTranslation: '',
          listenAudio: []
        },
        videoTableData: [],
        rules: {
          listeningOrder: [{ required: true, message: '请输入听力排序', trigger: 'blur' }],
          listeningCategory: [{ required: true, message: '请输入听力分类型', trigger: 'blur' }],
          listeningType: [{ required: true, message: '请输入听力分类', trigger: 'blur' }],
          listeningName: [{ required: true, message: '请输入听力名称', trigger: 'blur' }],
          listeningYear: [{ required: true, message: '请输入真题年份', trigger: 'blur' }],
          listeningContent: [{ required: true, message: '请输入听力内容', trigger: 'blur' }],
          listeningContentTranslation: [{ required: true, message: '请输入听力中文', trigger: 'blur' }]
          // listenAudio: [{ required: true, message: '请上传听力文件', trigger: 'blur' }]
        },
        operationType: '', // 新增编辑标识符
        courseName: '',
        courseId: '',
        editId: '',
        videoShow: true,
        uploadVideoLoading: null
      };
    },
    created() {
      this.fetchData();
    },

    methods: {
      fetchDataSearch() {
        this.tablePage = {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null
        };
        this.fetchData();
      },
      fetchDataReset() {
        (this.dataQuery = {
          listeningCategory: '', // 听力类型
          listeningOrder: '', // 排序
          listeningType: '', // 听力分类
          listeningName: '' // 听力名称
        }),
          this.fetchDataSearch();
      },

      handleInput() {
        if (Number(this.ruleForm.listeningOrder) > 100) {
          this.$message.warning('输入排序不能大于 100');
          this.ruleForm.listeningOrder = '';
        } else if (Number(this.ruleForm.listeningOrder) < 1 && this.ruleForm.listeningOrder !== '') {
          this.$message.warning('输入排序不能小于 1');
          this.ruleForm.listeningOrder = '';
        }

        if (Number(this.dataQuery.listeningOrder) > 100) {
          this.$message.warning('输入排序不能大于 100');
          this.dataQuery.listeningOrder = '100';
        } else if (Number(this.dataQuery.listeningOrder) < 1 && this.dataQuery.listeningOrder !== '') {
          this.$message.warning('输入排序不能小于 1');
          this.dataQuery.listeningOrder = '1';
        }
      },
      changeListType() {
        this.ruleForm.listeningType = '';
        this.ruleForm.listeningYear = '';
      },

      // 处理视频成功
      videoSucceed(data) {
        console.log('视频上传成功回调', data);
        if (data) {
          data.forEach((item) => {
            if (item) {
              this.ruleForm.listenAudio.push({
                fileName: item.fileName,
                filePath: item.filePath,
                id: item.id,
                progress: item.progress,
                duration: item.duration
              });
            }
          });
        }
      },
      uploadVideohandle(value) {
        this.uploadVideoLoading = value;
        if (value === 'delete') {
          this.videoTableData = [];
          this.ruleForm.listenAudio = [];
        }
      },
      // 查询表格列表
      fetchData() {
        const that = this;
        that.tableLoading = true;
        that.courseName = window.localStorage.getItem('courseName');
        that.courseId = window.localStorage.getItem('courseId');
        let params = {
          courseId: that.courseId,
          listeningCategory: this.dataQuery.listeningCategory,
          listeningOrder: this.dataQuery.listeningOrder,
          listeningType: this.dataQuery.listeningType,
          listeningName: this.dataQuery.listeningName,
          pageSize: that.tablePage.size,
          pageNum: that.tablePage.currentPage
        };
        listeningListAPI.listListeningMaterials(params).then((res) => {
          that.tableData = res.data.data;
          that.tableLoading = false;
          pageParamNames.forEach((name) => that.$set(that.tablePage, name, parseInt(res.data[name] || 0)));
        });
      },
      // 新增听力
      addArticleHandle() {
        this.dialogTitle = '添加听力';
        this.ruleForm = {
          listeningOrder: '',
          listeningCategory: '',
          listeningType: '',
          listeningName: '',
          listeningYear: '',
          listeningContent: '',
          listeningContentTranslation: '',
          listenAudio: []
        };
        this.operationType = 'add'; // 设置操作类型为新增
        this.dialogVisible = true;
      },
      // 编辑听力
      editListenHandle(id) {
        this.editId = id;
        this.dialogTitle = '编辑听力';
        this.uploadVideoLoading = 'success';
        this.operationType = 'edit'; // 设置操作类型为编辑
        listeningListAPI
          .findListeningMaterials({ id: id })
          .then((res) => {
            this.ruleForm = res.data;
            this.ruleForm.listenAudio = []; // 确保清空原有数据
            this.ruleForm.listenAudio.push({
              fileName: res.data.listeningAudioName,
              filePath: res.data.listeningAudioUrl,
              duration: res.data.listeningAudioTime,
              id: res.data.listeningAudioId,
              progress: 100
            });
            this.videoTableData = this.ruleForm.listenAudio;
          })
          .then(() => {
            this.dialogVisible = true;
          });
      },
      // 弹窗验证
      dialogConfirm(formName) {
        this.$refs[formName].validate((valid) => {
          if (valid) {
            if (this.uploadVideoLoading === null) {
              this.$message.warning('请先上传音频文件');
              return false;
            } else if (this.uploadVideoLoading === 'uploading') {
              this.$message.warning('文件正在上传中，请耐心等待上传完成后再进行操作');
              return false;
            } else if (this.uploadVideoLoading === 'success') {
              let textToast = this.operationType === 'add' ? '新增听力' : '编辑听力';
              const loading = this.$loading({
                lock: true,
                text: textToast,
                spinner: 'el-icon-loading',
                background: 'rgba(0, 0, 0, 0.7)'
              });
              const mappedAudio = this.ruleForm.listenAudio.map((item) => ({
                fileName: item.fileName,
                id: item.id,
                filePath: item.filePath,
                duration: item.duration
              }));
              console.log('mappedAudio', mappedAudio);
              let params = {
                courseId: this.courseId,
                listeningOrder: this.ruleForm.listeningOrder,
                listeningCategory: this.ruleForm.listeningCategory,
                listeningType: this.ruleForm.listeningType,
                listeningName: this.ruleForm.listeningName,
                listeningYear: Number(this.ruleForm.listeningYear),
                listeningContent: this.ruleForm.listeningContent,
                listeningContentTranslation: this.ruleForm.listeningContentTranslation,
                listeningAudioName: mappedAudio[0].fileName,
                listeningAudioTime: String(mappedAudio[0].duration),
                listeningAudioId: mappedAudio[0].id,
                listeningAudioUrl: mappedAudio[0].filePath
              };
              console.log('params', params);
              if (this.operationType === 'add') {
                listeningListAPI
                  .addListeningMaterials(params)
                  .then((res) => {
                    if (res.success) {
                      this.$message.success('添加成功！');
                      loading.close();
                      this.dialogClose(formName);
                      this.fetchData();
                    }
                  })
                  .catch((err) => {
                    loading.close();
                  });
              } else if (this.operationType === 'edit') {
                listeningListAPI
                  .editListeningMaterials({ ...params, id: this.editId })
                  .then((res) => {
                    if (res.success) {
                      this.$message.success('编辑成功！');
                      loading.close();
                      this.dialogClose(formName);
                      this.fetchData();
                    }
                  })
                  .catch((err) => {
                    loading.close();
                  });
              }
            }
          } else {
            return false;
          }
        });
      },
      // 关闭弹窗 重置表单
      dialogClose(formName) {
        this.dialogVisible = false;
        this.videoShow = false;
        this.videoTableData = [];
        this.uploadVideoLoading = null;
        this.$refs[formName].resetFields();
      },
      // 制作试题
      openTestQuestion(row) {
        const that = this;
        window.localStorage.setItem('listeningName', row.listeningName);
        window.localStorage.setItem('materialsId', row.id);
        window.localStorage.setItem('courseId', row.courseId);
        that.$router.push({
          path: '/course/listenQuestionList',
          query: {
            courseName: row.courseName,
            materialsId: row.id,
            courseId: row.courseId
          }
        });
      },

      //删除听力
      deleteReading(id) {
        const that = this;
        this.$confirm('确定操作吗?', '删除数据', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            listeningListAPI.deleteListeningMaterials({ id: id }).then((res) => {
              that.$message.success('删除成功！');
              that.fetchData();
            });
          })
          .catch((err) => {});
      },

      // 分页
      handleSizeChange(val) {
        this.tablePage.size = val;
        this.fetchData();
      },
      handleCurrentChange(val) {
        this.tablePage.currentPage = val;
        this.fetchData();
      },
      //局部刷新
      reload() {
        this.isRouterAlive = false;
        this.$nextTick(function () {
          this.isRouterAlive = true;
        });
      },
      // 返回超级阅读课程列表
      goBack() {
        this.$store.dispatch('delVisitedViews', this.$route);
        this.$router.push({
          path: '/course/newListenCourseList'
        });
      }
    }
  };
</script>
<style src="@wangeditor/editor/dist/css/style.css"></style>
<style>
  .lh36 {
    line-height: 36px;
    font-size: 14px;
  }

  .SearchForm {
    width: 100%;
    margin-bottom: 15px;
    background: #fff;
    border-radius: 6px;
    padding: 5px 0;
    padding-left: 5px;
    box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.2);
  }

  .btn-add {
    padding: 5px;
    margin: 15px 0;
  }

  .red {
    color: red;
  }

  .green {
    color: green;
  }

  .course-table {
    text-align: center;
  }

  .course-table td,
  .course-table th {
    padding: 5px 0;
    text-align: center;
  }

  .course-table button {
    padding: 2px;
  }

  .icon-stop {
    float: left;
    display: inline-block;
    width: 12px;
    height: 12px;
    /* background: url("../../icons/stop.png") no-repeat top center/contain; */
  }

  .mt22 {
    margin-top: 22px;
  }

  .blue {
    margin-right: 50px;
    color: #409eff;
  }

  .clearfix {
    color: red;
  }

  @media (max-width: 767px) {
    .el-message-box {
      width: 80% !important;
    }
  }
</style>
