/**
 * 听写能力检测库
 */
import request from '@/utils/request'

export default {
  //课程单词配置新增
  pdCourseConfigSave(data) {
    return request({
      url: '/znyy/pd/pdCourseConfig/save',
      method: 'POST',
      data,
    })
  },
  // 听写检测配置详情
  pdCourseConfigGetInfo() {
    return request({
      url: '/znyy/pd/pdCourseConfig/getInfo',
      method: 'GET',
    })
  },
   // 听写检测配置编辑
   pdCourseConfigUpdate(data) {
    return request({
      url: '/znyy/pd/pdCourseConfig/update',
      method: 'POST',
      data,
    })
  },
    // 课程配置列表
    pageConfigWord(data) {
      return request({
        url: `/znyy/pd/pdCourseConfig/pageConfigWord/${data.currentPage}/${data.size}?studyType=${data.studyType}&word=${data.word}&chinese=${data.chinese}`,
        method: 'GET',
      })
    },
      // 课程配置查询
      getConfigWordById(data) {
        return request({
          url: `/znyy/pd/pdCourseConfig/getConfigWordById`,
          method: 'POST',
          data,
        })
      },
  
  // 课程检测添加单词
  saveBatchWord(data) {
    return request({
      url: `/znyy/pd/pdCourseConfig/saveBatchWord`,
      method: 'POST',
      data,
    })
  },
  // 修改单词
  updateWord(data) {
    return request({
      url: `/znyy/pd/pdCourseConfig/updateWord`,
      method: 'POST',
      data,
    })
  },
 //
   // 删除单词
   deleteWordById(data) {
    return request({
      url: `/znyy/pd/pdCourseConfig/deleteWordById`,
      method: 'POST',
      data,
    })
  },
 
 

  // 课程开通与暂停
  updateStatus(id,status) {
    return request({
      url: '/znyy/course/big/class/openAndSuspension?id=' + id + '&isEnable=' + status,
      method: 'PUT'
    })
  },
  // 获取分类列表
  categoryType() {
    return request({
      url: '/znyy/course/category/check/list',
      method: 'GET'
    })
  },
 
    // 单词音频识别打分
    wordRecognition(data) {
      return request({
        url: '/znyy/pd/mobile/wordRecognition',
        method: 'POST',
        data
      })
    },
    


}
