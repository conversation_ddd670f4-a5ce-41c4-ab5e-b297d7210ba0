/**
 * 会议相关接口
 */
import request from '@/utils/request'

export default {
  // 新增
  meetingAdd(data) {
    return request({
      url: '/dxt/meeting/add',
      method: 'POST',
      data
    })
  },
  // 修改回显
  echoMeeting(id) {
    return request({
      url: '/dxt/meeting/get/detail/' + id,
      method: 'GET'
    })
  },
  // 编辑
  updateMeetingData(data) {
    return request({
      url: '/dxt/meeting/update',
      method: 'PUT',
      data
    })
  },
  deleteMeeting(id){
    return request({
      url:'/dxt/meeting/delete/'+id,
      method: 'DELETE'
    })
  },
  // 获取会议名称
  getMeetingName() {
    return request({
      url: '/dxt/meeting/get/selete',
      method: 'GET'
    })
  },
  getDAreaList(){
    return request({
      url: '/dxt/DArea/get/select',
      method: 'GET'
    })
  },
  // 分页查询
  meetingList(pageNum, pageSize, data) {
    return request({
      url: '/dxt/meeting/list/' + pageNum + '/' + pageSize,
      method: 'GET',
      params: data
    })
  },
updateMeeting(data){
    return request({
      url:'/dxt/meeting/update/clinch/people',
      method: 'POST',
      data
    })
}

}
