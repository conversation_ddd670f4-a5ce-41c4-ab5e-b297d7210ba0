<template>
  <el-dialog title="查看试卷" :visible.sync="dialogVisible" width="50%" top="5vh" @close="handleClose">
    <div v-loading="paperLoading">
      <h1 class="paper-title">
        {{ paperDetailForm.examName }}
      </h1>
      <p class="paper-desc">
        试卷说明：本试卷共{{ paperDetailForm.examSingleNum }}道单选题，{{ paperDetailForm.examMultiNum }}道多选题，{{ paperDetailForm.examTofNum }}道判断题，满分{{
          paperDetailForm.examFullMarks
        }}，考试时间{{ paperDetailForm.examTimeLimit }}分钟
      </p>
      <ul class="questions">
        <li class="question" v-for="(item, index) in paperDetailForm.questionList" :key="index">
          <p class="title">
            <span class="index">{{ index + 1 }}、</span>
            <span class="type">【{{ item.questionType == '1' ? '单选' : item.questionType == '2' ? '多选' : '判断' }}】</span>
            <span class="text">{{ item.questionName }}</span>
          </p>
          <!-- 单选、判断 -->
          <el-radio-group disabled :value="getSelectedOptions(item.optionList)[0]" v-if="item.questionType == '1' || item.questionType == '3'">
            <el-radio v-for="(el, idx) in item.optionList" :key="idx" :label="el.questionOptionDescription">
              {{ el.questionOptionDescription }}、{{ el.questionOptionContent }}
            </el-radio>
          </el-radio-group>
          <!-- 多选 -->
          <el-checkbox-group disabled :value="getSelectedOptions(item.optionList)" v-else-if="item.questionType == '2'">
            <el-checkbox v-for="(el, idx) in item.optionList" :key="idx" :label="el.questionOptionDescription">
              {{ el.questionOptionDescription }}、{{ el.questionOptionContent }}
            </el-checkbox>
          </el-checkbox-group>
          <!-- 判断 -->
          <!-- <el-radio-group disabled :value="getSelectedOptions(item.optionList)[0]" v-else-if="item.questionType == '3'">
                        <el-radio label="A">{{el.questionOptionContent}}</el-radio>
                        <el-radio label="B">{{el.questionOptionContent}}</el-radio>
                    </el-radio-group> -->

          <p class="question-result">
            <span>结果：</span>
            <span :class="item.userAnswerIsCorrect ? 'istrue' : 'isfalse'">{{ item.userAnswerIsCorrect ? '回答正确' : '回答错误' }}</span>
          </p>
        </li>
      </ul>
    </div>
  </el-dialog>
</template>

<script>
  import examApi from '@/api/training/exam';

  export default {
    name: 'PaperDetail',
    props: {
      dialogVisible: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        paperDetailForm: {},
        paperLoading: false
      };
    },
    methods: {
      open(examRecordId) {
        this.paperLoading = true;
        examApi
          .examRecordDetail({ examRecordId })
          .then((res) => {
            this.paperDetailForm = res.data;
          })
          .catch((err) => {
            console.log(err);
          })
          .finally(() => {
            this.paperLoading = false;
          });
      },
      handleClose() {
        this.$emit('closeDialog');
      },
      getSelectedOptions(array) {
        const filterArray = array.filter((el) => el.userAnswerIsSelect);
        return filterArray.map((a) => {
          return a.questionOptionDescription;
        });
      }
    }
  };
</script>

<style lang="less" scoped>
  .paper-title {
    text-align: center;
  }
  .paper-desc {
    background-color: #fafafa;
    padding: 10px 20px;
    box-sizing: border-box;
    font-size: 17px;
    color: #333;
  }
  .questions {
    list-style: none;
    padding: 0 20px;
  }
  .question-result {
    background-color: rgb(233, 247, 222);
    padding: 5px 10px;
    box-sizing: border-box;
  }
  .istrue {
    color: rgb(55, 167, 55);
  }
  .isfalse {
    color: rgb(243, 9, 9);
  }

  ::v-deep .el-radio,
  ::v-deep .el-radio-group,
  ::v-deep .el-checkbox {
    display: block;
  }
  ::v-deep .el-radio,
  ::v-deep .el-checkbox {
    padding: 5px 10px;
    box-sizing: border-box;
  }
  ::v-deep .el-radio__input.is-disabled + span.el-radio__label,
  ::v-deep .el-checkbox__input.is-disabled + span.el-checkbox__label {
    color: #666;
  }
</style>
