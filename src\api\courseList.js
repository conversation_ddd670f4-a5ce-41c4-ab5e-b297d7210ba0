/**
 * 课程相关接口
 */
import request from '@/utils/request'

export default {
  // 课程分页查询
  courseListNew(pageNum, pageSize, data) {
    return request({
      url: '/znyy/course/list/new/' + pageNum + '/' + pageSize,
      method: 'GET',
      params: data
    })
  },
  courseList(pageNum, pageSize, data) {
    return request({
      url: '/znyy/course/list/' + pageNum + '/' + pageSize,
      method: 'GET',
      params: data
    })
  },
  //获取单词音频
  fetchWordList(courseWordCo) {
    return request({
      url: '/znyy/course/generate/word/voice',
      method: 'GET',
      params: courseWordCo,
    })
  },
  fetchOss() {
    return request({
      url: '/znyy/alibaba/oss/privateRead/sts',
      method: 'GET',
    })
  },
  addWordReplace(data) {
    return request({
      url: '/znyy/course/word/replaceWordVoice',
      method: 'POST',
      data,
    })
  },
  // 课程新增
  addCourse(data) {
    return request({
      url: '/znyy/course',
      method: 'POST',
      data
    })
  },
  // 课程编辑
  updateCourse(data) {
    return request({
      url: '/znyy/course/edit',
      method: 'PUT',
      data
    })
  },
  // 课程查询
  queryActive(id) {
    return request({
      url: '/znyy/course/detail/' + id,
      method: 'GET'
    })
  },
  //根据课程分类查子类
  checkClassification(categoryCode) {
    return request({
      url: '/znyy/course/big/class/type/' + categoryCode,
      method: 'GET'
    })
  },
  // 课程开通与暂停
  updateStatus(id, status) {
    return request({
      url: '/znyy/course/activationAndSuspension?id=' + id + '&isEnable=' + status,
      method: 'PUT'
    })
  },
  getCurrentTime() {
    var _this = this;
    let yy = new Date().getFullYear();
    let mm = new Date().getMonth() + 1;
    let dd = new Date().getDate();
    let hh = new Date().getHours();
    let mf = new Date().getMinutes() < 10 ? '0' + new Date().getMinutes() : new Date().getMinutes();
    let ss = new Date().getSeconds() < 10 ? '0' + new Date().getSeconds() : new Date().getSeconds();
    _this.gettime = yy + '-' + mm + '-' + dd + ' ' + hh + ':' + mf + ':' + ss;
    return _this.gettime
  },

  courseWordExport(data) {
    //
    // console.log('7777777777777777')
    // console.log(data)
    // console.log('7777777777777777')
    return request({
      url: '/znyy/course/courseWordExport',
      method: 'GET',
      responseType: 'blob',
      params: data
    })
  }
}
