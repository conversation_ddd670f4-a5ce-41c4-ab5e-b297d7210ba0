<template>
  <div class="dashboard-editor-container">
    <!-- 首页信息 -->
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
      <el-row id="container" :gutter="30" v-show="dashboardShow">
        <el-col :span="4">
          <el-card shadow="always" body-style="padding:5px">
            <div class="ibox-title">
              <span class="label label-info pull-right">个</span>
              <h5>服务商</h5>
            </div>
            <div class="ibox-content">
              <span style="font-size: 30px">{{ agentCount }}</span>
            </div>
          </el-card>
        </el-col>
        <el-col :span="4">
          <el-card shadow="always" body-style="padding:5px">
            <div class="ibox-title">
              <span class="label label-info pull-right">个</span>
              <h5>托管中心</h5>
            </div>
            <div class="ibox-content">
              <span style="font-size: 30px">{{ dealerCount }}</span>
            </div>
          </el-card>
        </el-col>
        <el-col :span="4">
          <el-card shadow="always" body-style="padding:5px">
            <div class="ibox-title">
              <span class="label label-info pull-right">个</span>
              <h5>门店</h5>
            </div>
            <div class="ibox-content">
              <span style="font-size: 30px">{{ schoolCount }}</span>
            </div>
          </el-card>
        </el-col>
        <el-col :span="4">
          <el-card shadow="always" body-style="padding:5px">
            <div class="ibox-title">
              <span class="label label-info pull-right">个</span>
              <h5>新增商户</h5>
            </div>
            <div class="ibox-content">
              <span style="font-size: 30px">
                {{ newAddMerchant }}
                <small>(待审核)</small>
              </span>
            </div>
          </el-card>
        </el-col>
        <el-col :span="4">
          <el-card shadow="always" body-style="padding:5px">
            <div class="ibox-title">
              <span class="label label-info pull-right">人</span>
              <h5>咨询师</h5>
            </div>
            <div class="ibox-content">
              <span style="font-size: 30px">{{ vipMember }}</span>
            </div>
          </el-card>
        </el-col>
        <el-col :span="4">
          <el-card shadow="always" body-style="padding:5px">
            <div class="ibox-title">
              <span class="label label-danger pull-right">笔</span>
              <h5>年审信息</h5>
            </div>
            <div class="ibox-content">
              <span style="font-size: 30px">{{ num }}</span>
            </div>
          </el-card>
        </el-col>
      </el-row>
      <div v-show="dashboardShow">
        <investment-data-form></investment-data-form>
        <investment-flow-form></investment-flow-form>
        <admissions-data-form></admissions-data-form>
        <class-flow-data-form></class-flow-data-form>
      </div>
      <!-- v-if="isJLB && isJlbLogo" -->
      <!-- :disabled="fisrtSet" -->
      <is-logo v-if="JlbInfo.logoEnable && JlbInfo.roleTag == 'Operations'"></is-logo>
    </div>

    <el-dialog class="remark-dialog" title="提示" :visible.sync="dialogVisible" width="30%" :close-on-click-modal="false">
      <span>{{ remark }}</span>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="dialogVisible = false">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
  import IsLogo from './IsLogo';
  import { mapGetters } from 'vuex';
  import PanThumb from '@/components/PanThumb';
  // import GithubCorner from "@/components/GithubCorner";
  import homeApi from '@/api/homeApi';
  import investmentDataForm from '@/views/dashboard/editor/form/InvestmentData';
  import InvestmentFlowForm from '@/views/dashboard/editor/form/investmentFlow';
  import admissionsDataForm from '@/views/dashboard/editor/form/admissionsData';
  import ClassFlowDataForm from '@/views/dashboard/editor/form/classFlowData';
  import schoolApi from '@/api/areasSchoolList';
  import authApi from '@/api/auth';

  export default {
    name: 'DashboardEditor',
    components: {
      ClassFlowDataForm,
      InvestmentFlowForm,
      investmentDataForm,
      admissionsDataForm,
      PanThumb,
      // GithubCorner,
      IsLogo
    },
    data() {
      return {
        emptyGif: 'https://wpimg.wallstcn.com/0e03b7da-db9e-4819-ba10-9016ddfdaed3',
        vipMember: '',
        dashboardShow: true,
        isJLB: false,
        dashboardFalse: true,
        newAddMerchant: '',
        schoolCount: '',
        dealerCount: '',
        fisrtSet: false,
        agentCount: '',
        dialogVisible: false, //弹框显示
        num: 0, //审核显示
        remark: '', //审核信息
        currentAdmin: '', // 门店信息
        avatar: ''
      };
    },
    async created() {
      await this.getRoleTag();
      this.fetchData();
      this.findJlbId();
    },
    mounted() {},
    methods: {
      async findJlbId() {
        this.fileList = [];
        let { data } = await authApi.getJlbInfo();
        this.fileList = [{ name: '默认', url: data.avatar || this.avatar }];
        this.form.avatar = data.avatar || this.avatar;
        this.form.customName = data.customName;
      },
      // 俱乐部定制logo 提交
      async onSubmit() {
        await this.$refs.form.validate();
        if (!this.form.avatar) return this.$message.warning('请上传自定logo');
        this.form.id = this.JlbInfo.id;
        await dealerListApi.customMerchantInfo(this.form);
        this.$message.success('操作成功');
        setTimeout(() => {
          location.reload();
        }, 500);
      },
      handleSuccess(res) {
        this.form.avatar = res;
      },
      handleRemove() {
        this.fileList = [];
        this.form.avatar = '';
      },
      fetchData() {
        homeApi.getStatistics().then((res) => {
          this.vipMember = res.data.vipMember;
          this.newAddMerchant = res.data.newAddMerchant;
          this.schoolCount = res.data.schoolCount;
          this.dealerCount = res.data.dealerCount;
          this.agentCount = res.data.agentCount;
          this.num = res.data.num;
          this.remark = res.data.remark;
          if (this.currentAdmin.schoolType != 3) {
            this.dialogVisible = res.data.itAboutToExpire;
          }
        });

        //显示
        // console.log(this.$store.getters.roles);
        if (this.$store.getters.roles.length > 0) {
          this.$store.getters.roles.forEach((element) => {
            // if (element.val === "Agent" || element.val === "Dealer" || element.val === "School" || element.val==="Division" ||element.val==="Company") {
            //   this.dashboardShow = false;
            //
            // }else{
            //   this.dashboardShow = true;
            // }
            if (element.val == 'julebu') {
              this.isJLB = true;
            } else {
              false;
            }
            if (element.val === 'admin' || element.val === 'channelManager') {
              this.dashboardShow = true;
            } else {
              this.dashboardShow = false;
            }
            if (element.val === 'Promoter') {
              this.dashboardFalse = false;
            }
          });
        }
      },
      // 关闭
      // handleClose() {},

      // 获取当前门店信息
      getRoleTag() {
        schoolApi.getCurrentAdmin().then((res) => {
          this.currentAdmin = res.data;
        });
      }
    },
    computed: {
      ...mapGetters(['name', 'avatar', 'roles', 'JlbInfo'])
    }
  };
</script>

<style lang="scss">
  .emptyGif {
    display: block;
    width: 45%;
    margin: 0 auto;
  }
  // .isLogo {
  //   height: 80vh;
  //   .logoTitle {
  //     font-size: 20px;
  //     margin-bottom: 20px;
  //   }
  // }
  .dashboard-editor-container {
    background-color: #e3e3e3;
    color: #676a6c;
    min-height: 100vh;
    padding: 20px 20px 0px;

    .pan-info-roles {
      font-size: 12px;
      font-weight: 700;
      color: #333;
      display: block;
    }

    .info-container {
      position: relative;
      margin-left: 190px;
      height: 150px;
      line-height: 200px;

      .display_name {
        font-size: 48px;
        line-height: 48px;
        color: #212121;
        position: absolute;
        top: 25px;
      }
    }

    .ibox-content {
      background-color: #ffffff;
      color: inherit;
      padding: 20px;
      border-color: #e7eaec;
      -webkit-border-image: none;
      -o-border-image: none;
      border-image: none;
      border-style: solid solid none;
      border-width: 1px 0px;

      h1 {
        font-weight: normal;
      }
    }

    .ibox-title {
      -moz-border-bottom-colors: none;
      -moz-border-left-colors: none;
      -moz-border-right-colors: none;
      -moz-border-top-colors: none;
      background-color: #ffffff;
      border-color: #e7eaec;
      -webkit-border-image: none;
      -o-border-image: none;
      border-image: none;
      border-style: solid solid none;
      border-width: 0px 0px 0;
      color: inherit;
      margin-bottom: 0;
      padding: 14px 15px 7px;
      min-height: 40px;
    }

    .ibox {
      margin-bottom: 25px;
      background-color: #fff;
      border: 1px solid #ddd;
      border-radius: 4px;
      height: 100%;
      -webkit-box-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);
    }

    .label {
      display: inline;
      padding: 0.2em 0.6em 0.3em;
      font-size: 75%;
      font-weight: 700;
      line-height: 1;
      color: #fff;
      text-align: center;
      white-space: nowrap;
      vertical-align: baseline;
      border-radius: 0.25em;
    }

    .ibox-title .label {
      float: left;
      margin-left: 4px;
    }

    .label-info {
      background-color: #5bc0de;
    }

    .pull-right {
      float: right !important;
    }

    .label {
      background-color: #d1dade;
      color: #5e5e5e;
      font-size: 10px;
      font-weight: 600;
      padding: 3px 8px;
      text-shadow: none;
    }

    .label-info,
    .badge-info {
      background-color: #23c6c8;
      color: #ffffff;
    }

    .ibox-title h5 {
      display: inline-block;
      font-size: 14px;
      margin: 0 0 7px;
      padding: 0;
      text-overflow: ellipsis;
      float: left;
      font-weight: normal;
    }

    .label-danger,
    .badge-danger {
      background-color: #ed5565;
      color: #ffffff;
    }

    small {
      font-weight: 400;
      line-height: 1;
      color: #777;
      font-size: 60%;
    }
  }

  @media screen and (max-width: 767px) {
    .dashboard-editor-container {
      padding: 20px 20px 0;
    }

    .remark-dialog .el-dialog {
      width: 70% !important;
    }
  }
</style>
