// 群聊管理相关接口
import request from '@/utils/request';

export default {
  // 群聊列表分页查询
  groupChatList(data) {
    return request({
      url: '/znyy/group-chat/page',
      method: 'GET',
      params: data
    });
  },
  //   新增群聊
  addGroupChat(data) {
    return request({
      url: '/znyy/group-chat/create',
      method: 'POST',
      data
    });
  },
  // 编辑群聊
  updateGruopChat(data) {
    return request({
      url: '/znyy/group-chat/update',
      method: 'POST',
      data
    });
  },
  // 通过Tag查询用户信息
  getUserListByTag(roleTag) {
    return request({
      url: '/znyy/group-chat/userinfo',
      method: 'GET',
      params: { roleTag }
    });
  },
  // 通过Tag查询用户(门店)信息-分页
  getStoreListByTag(params) {
    return request({
      url: '/znyy/group-chat/userinfo/page',
      method: 'GET',
      params: params
    });
  },
  // 绑定合伙人
  bindPartner(data) {
    return request({
      url: '/znyy/group-chat/partner/binding',
      method: 'PUT',
      data
    });
  },
  //   合伙人详情列表
  partnerList(data) {
    return request({
      url: '/znyy/group-chat/partner/page',
      method: 'GET',
      params: data
    });
  },
  //   合伙人移除群聊
  removePartner(data) {
    return request({
      url: '/znyy/group-chat/partner/remove',
      method: 'DELETE',
      params: data
    });
  },
  //   群聊查询（未建群）
  queryChatList(data) {
    return request({
      url: '/znyy/group-chat/query',
      method: 'GET',
      params: data
    });
  },
  //   合伙人更换群聊
  changePartnerChat(data) {
    return request({
      url: '/znyy/group-chat/partner/change',
      method: 'PUT',
      params: data
    });
  }
};
