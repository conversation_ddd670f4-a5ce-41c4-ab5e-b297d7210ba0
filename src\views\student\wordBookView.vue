<template>
  <div class="app-container">
    <el-form :inline="true" class="container-card" label-width="110px" label-position="left"
      style="padding: 30px 30px 10px;">
      <el-row>
        <el-col :span="8" :xs="24">
          <el-form-item label="学员编号：">
            <el-input v-model="dataQuery.studentCode" placeholder="请输入学员编号" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="上次学习时间：">
            <el-date-picker style="width:90%" value-format="yyyy-MM-dd HH:mm:ss" type="daterange" v-model="value1"
              align="right" unlink-panels range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
              @change="dateVal">
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item>
            <el-button type="primary" size="small" icon="el-icon-search" @click="fetchData01()">搜索</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-form :inline="true" style="margin-bottom: 20px;">
      <el-button type="warning" size="small" icon="el-icon-document-copy" @click="exportFlow">导出</el-button>
    </el-form>
    <el-table class="common-table" :data="tableData" style="width: 100%;margin-bottom: 20px;" row-key="id" border
      default-expand-all :tree-props="{ list: 'children', hasChildren: 'true' }">
      <el-table-column prop="studentCode" label="学员编号" sortable></el-table-column>
      <el-table-column prop="word" label="单词" sortable></el-table-column>
      <el-table-column prop="translation" label="翻译" sortable></el-table-column>
      <el-table-column prop="scheduleCode" label="进度编号" sortable></el-table-column>
      <el-table-column prop="lastStudyTime" label="上次学习时间" sortable width="300" show-overflow-tooltip></el-table-column>
    </el-table>
    <!-- 分页 -->
    <el-col :span="24" style="overflow-x: auto;" :xs="24">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>
  </div>
</template>

<script>
import wordApi from "@/api/wordBookView";
import Tinymce from "@/components/Tinymce";
import { pageParamNames } from "@/utils/constants";
export default {
  name: 'wordBookView',
  data() {
    return {
      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      exportLoading: false,
      tableData: [],
      dataQuery: {
        studentCode: '',
        startDate: '',
        endDate: ''
      },
      value1: ''
    }
  },
  created() {
    this.fetchData01();
  },
  methods: {
    fetchData01() {
      this.tablePage = {
        currentPage: 1,
        size: this.tablePage.size,
        totalPage: null,
        totalItems: null
      }
      this.fetchData();
    },
    // 获取起始时间
    dateVal(e) {
      // console.log(e[0]);
      this.dataQuery.startDate = e[0]
      this.dataQuery.endDate = e[1]
    },
    // 查询提现列表
    fetchData() {
      const that = this;
      // that.tableLoading =  false
      wordApi.wordBook(that.tablePage.currentPage, that.tablePage.size, that.dataQuery).then(res => {
        that.tableData = res.data.data
        // that.tableLoading = false
        // 设置后台返回的分页参数
        pageParamNames.forEach(name => that.$set(that.tablePage, name, parseInt(res.data[name])))
      })
    },
    exportFlow() {
      const that = this;
      //  that.exportLoading = false;
      //
      wordApi.wordExport(that.dataQuery).then(res => {
        //           this.$notify.error({
        //               title: "操作失败",
        //               message: "文件下载失败"
        //             });
        const url = window.URL.createObjectURL(res);
        const link = document.createElement("a");
        link.style.display = "none";
        link.href = url; // 获取服务器端的文件名
        link.setAttribute("download", "单词本查看列表.xls");
        document.body.appendChild(link);
        link.click();
        //  that.exportLoading = false;
      })
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val;
      this.fetchData();
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val;
      this.fetchData();
    },
  }
}
</script>

<style>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.period-table td,
.period-table th {
  text-align: center;
}

.mt20 {
  margin-top: 20px;
}

.red {
  color: red;
}

.green {
  color: green;
}
</style>
