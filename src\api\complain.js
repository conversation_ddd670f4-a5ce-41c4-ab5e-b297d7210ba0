/**
 * 教练 投诉相关接口
 */
import request from '@/utils/request'

export default {
  // 课程分页查询
  complainList(pageNum, pageSize, data) {
    return request({
      url: '/cousys/web/complain/list/' + pageNum + '/' + pageSize,
      method: 'GET',
      params: data
    })
  },
  // 详情
  complainDetail(id) {
    return request({
      url: '/cousys/web/complain/detail/' + id,
      method: 'GET'
    })
  },
  // 提交
  complainUpdate(id, data) {
    return request({
      url: '/cousys/web/complain/update/' + id,
      method: 'GET',
      params: data
    })
  },
  // 删除
  complainDel(id) {
    return request({
      url: '/cousys/web/complain/delete/' + id,
      method: 'GET'
    })
  },
}
