<template>
  <div class="app-container2">
    <h3 style="width: 810px; margin: 40px auto; font-weight: normal">文章打印</h3>
    <div id="print" style="margin-top: 20px" ref="print">
      <input type="hidden" id="WordPrintCode" value="P212270" />
      <div class="first-center clearfix" style="text-align: center">
        <span style="font-size: 20px">
          <label style="width: 120px; font-weight: normal">姓名：{{ printData.realName }}</label>
        </span>
        <span style="font-size: 20px" v-if="printData.courseContentType !== 'SuperRead'"><label style="width: 120px; font-weight: normal">教练 ：</label></span>
        <span style="font-size: 20px" v-if="printData.courseContentType !== 'SuperRead'"><label style="width: 120px; font-weight: normal">日期：</label></span>
        <span style="font-size: 20px" v-if="printData.courseContentType !== 'SuperRead'"><label style="width: 120px; font-weight: normal">成绩：</label></span>
      </div>
      <hr />
      <div class="first-center clearfix" style="text-align: center">
        <span style="font-size: 20px">
          <label style="width: 150px; font-weight: normal">类型：{{ printData.courseContentType == 'SuperRead' ? '新阅读理解' : printData.courseContentTypeName }}</label>
        </span>
        <span style="font-size: 20px" v-if="printData.courseContentType == 'Reading'">
          <label style="width: 150px; font-weight: normal">
            难度：
            <i v-for="(item, index) in level" :key="`star${index}`" style="font-style: normal">★</i>
          </label>
        </span>
        <span style="font-size: 20px" v-if="printData.courseContentType == 'Reading'">
          <label style="width: 200px; font-weight: normal">参考学时：{{ suggestedTime }}分钟</label>
        </span>
      </div>
      <div style="width: 810px; margin: 0 auto" class="aaa">
        <div
          style="margin-top: 20px; padding: 5px; min-height: 1000px; width: 800px; font-size: 18px; line-height: 28px; background-color: transparent; border: 1px solid #7676764d"
          v-html="topicContent"
        ></div>
        <!-- <textarea
          id="textarea"
          style="margin-top: 20px; padding: 5px; min-height: 1000px; width: 800px; font-size: 18px; line-height: 28px; background-color: transparent"
          disabled="disabled"
          v-model=""
        ></textarea> -->
        <!-- {{ topicContent}} -->
      </div>
      <div style="height: 20px"></div>
      <div style="width: 810px; margin: 0 auto" class="aaa" v-if="printData.courseContentType == 'SuperRead'">
        <textarea
          id="textarea"
          style="margin-top: 20px; padding: 5px; min-height: 1000px; width: 800px; font-size: 18px; line-height: 28px; background-color: transparent"
          disabled="disabled"
          v-model="translateContent"
        ></textarea>
        <!-- {{ topicContent}} -->
      </div>
      <div class="choose" v-if="printData.courseContentType == 'Reading'">
        <div style="margin-top: 5px" v-for="(item, index) in printData.courseReadingAnswerCos" :key="`courseReadingAnswerCos${index}`">
          <span style="font-size: 16px">{{ item.fillNumber }}.{{ item.questionText }}</span>
          <div style="font-size: 16px">
            <table style="width: 100%; height: auto">
              <tbody>
                <tr style="height: 25px">
                  <td style="width: 50%; padding-left: 15px" v-if="item.answerForA != ''">A.{{ item.answerForA }}</td>
                  <td style="width: 50%" v-if="item.answerForB != ''">B.{{ item.answerForB }}</td>
                </tr>
                <tr style="height: 25px">
                  <td style="width: 50%; padding-left: 15px" v-if="item.answerForC != ''">C.{{ item.answerForC }}</td>
                  <td style="width: 50%" v-if="item.answerForD != ''">D.{{ item.answerForD }}</td>
                </tr>
                <tr style="height: 25px" v-if="item.answerForE != ''">
                  <td style="width: 50%; padding-left: 15px">E.{{ item.answerForE }}</td>
                  <td style="width: 50%">F.{{ item.answerForF }}</td>
                </tr>
                <tr style="height: 25px" v-if="item.answerForG != ''">
                  <td style="width: 50%; padding-left: 15px">G.{{ item.answerForG }}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
      <div class="choose" v-if="printData.courseContentType == 'SuperRead'">
        <div style="margin-top: 5px" v-for="(item, index) in printData.questionDetailDtoList" :key="`questionDetailDtoList${index}`">
          <span style="font-size: 16px">{{ item.questionNumber }}.{{ item.questionText }}</span>
          <div style="font-size: 16px">
            <table style="width: 100%; height: auto">
              <tbody>
                <tr style="height: 25px" v-for="(val, idx) in item.optionDtoList" :key="`optionDto${idx}`">
                  <td style="width: 50%; padding-left: 15px">{{ val.choiceOption }}.{{ val.content }}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>

      <div class="choose clearfix" v-if="printData.courseContentType == 'SuperRead'">
        <div>
          <div class="first2" style="font-size: 15px" v-for="(item, index) in printData.questionDetailDtoList" :key="`courseReadingAnswerCos${index}`">
            <span>正确答案 {{ item.fillNumber }}.{{ item.correctAnswer }}</span>
            <ul>
              <li>解析：{{ item.analysis }}</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
    <div class="buttom clearfix">
      <a style="background: #3dab93" @click="printBtn()">打印</a>
      <a style="background: #f0ad4e; margin-left: 20px" @click="goback()">返回</a>
    </div>
    <div class="choose clearfix" v-if="printData.courseContentType != 'SuperRead'">
      <div class="first2" style="font-size: 15px" v-for="(item, index) in printData.courseReadingAnswerCos" :key="`courseReadingAnswerCos${index}`">
        <span>正确答案 {{ item.fillNumber }}.{{ item.correctAnswer }}</span>
        <ul>
          <li>解析：{{ item.answerRemark }}</li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script>
  import printApi from '@/api/studentTestPrintReading';
  import printCloseApi from '@/api/studentPrintClose';
  import ls from '@/api/sessionStorage';
  export default {
    data() {
      return {
        // 打印数据
        printData: [],
        level: 0,
        suggestedTime: 0,
        topicContent: '',
        translateContent: '',
        dataQuary: {
          wordPrintCode: ''
        }
      };
    },
    created() {
      this.dataQuary.wordPrintCode = ls.getItem('wordPrintCode');
      this.fetchData(this.dataQuary);
    },
    methods: {
      // 返回
      goback() {
        this.$store.dispatch('delVisitedViews', this.$route);
        this.$router.go(-1); //返回上一层
      },
      // 显示学员测试结果
      fetchData(dataQuary) {
        const that = this;
        printApi.studentList(dataQuary).then((res) => {
          that.printData = res.data;
          if (res.data.courseContentType == 'SuperRead') {
            that.topicContent = res.data.superReadArticleDto.content;
            that.translateContent = res.data.superReadArticleDto.translateContent;
            that.topicContent = that.topicContent.replaceAll('@=', '<span style="text-decoration: underline;">');
            that.topicContent = that.topicContent.replaceAll('@*', '</span>');
            that.topicContent = that.topicContent.replaceAll('\n', '<p/>');
            console.log(that.topicContent, '11111111');
          } else {
            that.level = parseInt(res.data.courseReadingCo.level);
            that.suggestedTime = res.data.courseReadingCo.suggestedTime;
            that.topicContent = res.data.courseReadingCo.topicContent;
            that.topicContent = that.topicContent.replaceAll('@=', '<span style="text-decoration: underline;">');
            that.topicContent = that.topicContent.replaceAll('@*', '</span>');
            that.topicContent = that.topicContent.replaceAll('\n', '<p/>');
          }
        });
      },
      getStr(start, end, str) {
        const regex = new RegExp(`${start}(.*?)${end}`);
        const result = regex.exec(str);
        console.log(result);
        const substr = result ? result[1] : '';
        return substr;
      },
      // 打印
      printBtn() {
        this.$print(this.$refs.print);
        printCloseApi.editEnable(ls.getItem('wordPrintCode')).then((res) => {});
      }
    }
  };
</script>

<style scoped>
  .app-container2 {
    padding: 20px;
    color: #676a6c;
    background-color: #f3f3f4;
  }

  .clearfix {
    zoom: 1;
  }

  .clearfix:before,
  .clearfix:after {
    content: '';
    line-height: 0;
    display: table;
  }

  .clearfix:after {
    clear: both;
  }

  .first-top,
  .first-center {
    width: 800px;
    margin: 0 auto;
    margin-top: 40px;
    font-size: 16px;
  }

  .first-top span {
    display: block;
    float: left;
    width: 25%;
    text-align: left;
  }

  .first-center span {
    display: block;
    float: left;
    width: 25%;
    text-align: center;
    margin-top: -10px !important;
  }

  .choose {
    width: 800px;
    margin: 10px auto;
    margin-top: 60px;
  }

  .choose ul li {
    margin-top: 10px;
    list-style: none;
    line-height: 24px;
  }

  .choose ul {
    margin-left: 10px;
  }

  .buttom {
    width: 240px;
    margin: 0 auto;
    margin-top: 10px;
    float: right;
    color: #fff;
    cursor: pointer;
  }

  .buttom a {
    display: block;
    float: left;
    color: #fff;
    width: 90px;
    height: 35px;
    border-radius: 5px;
    text-align: center;
    line-height: 35px;
  }
  .firt {
    width: 100%;
  }
</style>
