<template>
  <div class="app-container">
    <el-form :inline="true" class="container-card" label-width="96px" label-position="left">
      <el-form-item>
        <el-form-item label="录入时间：" clearable>
          <el-date-picker style="width: 100%;" value-format="yyyy-MM-dd" clearable v-model="regTime" type="daterange"
                          align="right" unlink-panels range-separator="至"
                          start-placeholder="开始时间" end-placeholder="结束时间"
          >
          </el-date-picker>
        </el-form-item>
      </el-form-item>
      <el-form-item label="家长姓名：">
        <el-input v-model="dataQuery.realName" placeholder="输入家长姓名" clearable/>
      </el-form-item>
      <el-form-item label="联系电话：">
        <el-input v-model="dataQuery.phoneNumber" placeholder="输入家长联系电话" clearable/>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="getList()">搜索</el-button>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-re" @click="resetQuery()">重置</el-button>
      </el-form-item>
    </el-form>

    <el-table class="common-table" v-loading="tableLoading" :data="tableData" style="width: 100%;margin-bottom: 20px;"
              row-key="id" border default-expand-all :tree-props="{list: 'children', hasChildren: 'true'}">
      <el-table-column prop="memberCode" label="家长编码" sortable></el-table-column>
      <el-table-column prop="realName" label="家长姓名" sortable></el-table-column>
      <el-table-column prop="withdrawnBonus" label="操作">
        <template slot-scope="scope">
          <el-button  type="success" size="mini" @click="showOpen(scope.row.memberCode)">查看</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="gender" label="性别" sortable></el-table-column>
      <el-table-column prop="phoneNumber" label="联系电话" sortable></el-table-column>
      <el-table-column prop="regTime" label="录入时间" sortable></el-table-column>
      <el-table-column prop="totalCourse" label="报课学时" sortable></el-table-column>
      <el-table-column prop="studentCount" label="学员数量" sortable></el-table-column>
    </el-table>

    <!-- 分页 -->
    <el-col :span="20" style="overflow-x: auto;" :xs="24">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
                     layout="total, sizes, prev, pager, next, jumper"
                     :total="tablePage.totalItems" @size-change="handleSizeChange" @current-change="handleCurrentChange"/>
    </el-col>

    <!-- 查看更多弹窗 -->
    <el-dialog title="家长详情" :visible.sync="showDialog" width="70%" :close-on-click-modal="false" @close="closeShow">

      <el-form :model="parentDetail" label-position="left" label-width="110px" style="width: 100%">

          <el-form-item :xs="24">
            <el-col :span="6">
              家长姓名：{{ parentDetail.realName }}
            </el-col>
            <el-col :span="6">
              性别：{{ parentDetail.gender }}
            </el-col>
            <el-col :span="6">
              联系方式：{{ parentDetail.phoneNumber }}
            </el-col>
            <el-col :span="6">
              录入时间：{{ parentDetail.regTime }}
            </el-col>
            <el-col :span="6">
              支付金额{{ parentDetail.totalPrice }} 元
            </el-col>
            <el-col :span="6">
              报课学时：{{ parentDetail.totalCourse }} 学时
            </el-col>
          </el-form-item>

          <el-form-item>
            <el-col>
              家庭住址：{{ parentDetail.homeAddress }}
            </el-col>
            <el-col>
              工作地址：{{ parentDetail.companyAddress }}
            </el-col>
          </el-form-item>

        <span style="font-size:18px">学员信息</span>

          <el-form-item >
            <el-col v-for="(item,index) in parentDetail.studentInfoVoList" :key="index">
              <el-form-item >
                <el-col :span="3">
                  学员姓名： {{ item.studentName }}
                </el-col>
                <el-col :span="3">
                  学员年级： {{ item.grade }}
                </el-col>
                <el-col :span="3">
                  学员学校： {{ item.school }}
                </el-col>
                <el-col :span="3">
                  学校类别： {{ item.schoolType }}
                </el-col>
                <el-col :span="3">
                  英语成绩： {{ item.englishScore }}
                </el-col>
                <el-col :span="3">
                  班级排名： {{ item.classRank }}
                </el-col>
                <el-col :span="3">
                  学校排名： {{ item.schoolRank }}
                </el-col>
              </el-form-item>
            </el-col>
          </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="closeShow">关闭</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>

import cousysApi from '@/api/cousys'
import { pageParamNames } from '@/utils/constants'

export default {
  data() {
    return {
      tableLoading: false,
      showDialog: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      props: {
        value: 'name',
        label: 'name'
      },
      tableData: [],
      parentDetail:{},
      dataQuery: {
        realName: '',
        startTime: '',
        endTime: '',
        phoneNumber: ''
      },
      address: [],
      addressOptions: [],
      regTime: [],
    }
  },
  created() {
    this.getList()
    cousysApi.getAllRegion().then(res => {
      this.addressOptions = res.data
    })
  },
  methods: {
    //获取列表
    getList() {
      const that = this
      if (that.regTime !== '' && that.regTime !== null && that.regTime !== undefined) {
        if (that.regTime.length > 0) {
          that.dataQuery.startTime = that.regTime[0]
          that.dataQuery.endTime = that.regTime[1]
        } else {
          that.dataQuery.startTime = ''
          that.dataQuery.endTime = ''
        }
      } else {
        that.dataQuery.startTime = ''
        that.dataQuery.endTime = ''
      }
      that.tableLoading = true
      var b = this.address
      if (b != null) {
        that.dataQuery.province = b[0]
        that.dataQuery.city = b[1]
        that.dataQuery.area = b[2]
      } else {
        that.dataQuery.province = null
        that.dataQuery.city = null
        that.dataQuery.area = null
      }
      that.tableLoading = true
      cousysApi.getParentCentre(that.tablePage.currentPage, that.tablePage.size, that.dataQuery).then(res => {
        that.tableData = res.data.data
        console.log(res.data.data)
        that.tableLoading = false
        // 设置后台返回的分页参数
        pageParamNames.forEach(name => this.$set(this.tablePage, name, parseInt(res.data[name])))
      })
    },

    //撤销权限
    deletePermission(merchantCode) {
      this.$confirm('确定撤销吗?', '撤销权限', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        cousysApi.deletePermission(merchantCode).then(res => {
          this.getList()
        }).catch(err => {
        })
      }).catch(err => {
      })
    },

    //重置按钮
    resetQuery() {
      this.inputTime = []
      this.dataQuery = {
        status: '',
        gender: '',
        phoneNumber: ''
      }
      this.getList()
    },

    //查看更多
    showOpen(memberCode) {
      cousysApi.getParentCentreDetail(memberCode).then(res => {
        this.parentDetail=res.data
        this.showDialog=true;
      }).catch(err => {})
    },

    closeShow(){
      this.showDialog=false;
    },

    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val
      this.getList()
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val
      this.getList()
    }
  }
}
</script>

<style>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

@media screen and (max-width: 767px) {
  .recharge-dialog .el-dialog {
    width: 90% !important;
  }

  .el-message-box {
    width: 80% !important;
  }
}
</style>
