<template>
  <div class="app-container">
    <!--查询  -->
    <el-row>
      <el-input style="width: 200px; margin-right: 15px" v-model="tableQuery.loginName" placeholder="登录名"></el-input>
      <el-input style="width: 200px; margin-right: 15px" v-model="tableQuery.realName" placeholder="用户姓名"></el-input>
      <el-input style="width: 200px; margin-right: 15px" v-model="tableQuery.merchantName" placeholder="商户名"></el-input>
      <el-select style="width: 200px" v-model="tableQuery.roleTag" filterable value-key="value" placeholder="请选择">
        <el-option
          v-for="(item, index) in [
            { value: 'Agent', label: '市级服务商' },
            { value: 'Dealer', label: '托管中心' },
            { value: 'School', label: '门店' }
          ]"
          :key="index"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
      <span style="margin-right: 15px"></span>
      <el-tooltip class="item" content="搜索" placement="top">
        <el-button icon="el-icon-search" circle @click="fetchData()" v-perm="'b:user:query'"></el-button>
      </el-tooltip>
      <el-button icon="el-icon-refresh" @click="rest()">重置</el-button>
    </el-row>
    <div style="margin-bottom: 30px"></div>
    <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate" v-perm="'b:user:add'">
      {{ textMap.create }}
    </el-button>
    <div style="margin-bottom: 30px"></div>
    <!--列表-->
    <el-table style="width: 100%" :data="tableData" v-loading.body="tableLoading" element-loading-text="加载中" border fit highlight-current-row>
      <el-table-column prop="id" label="用户id" width="120"></el-table-column>
      <el-table-column prop="username" label="登录名" width="150"></el-table-column>
      <el-table-column label="操作">
        <template slot-scope="scope">
          <el-tooltip content="修改密码" placement="top">
            <el-button @click="handleUpdate(scope.$index, scope.row)" size="medium" type="info" icon="el-icon-edit" circle plain></el-button>
          </el-tooltip>
          <el-tooltip content="修改角色" placement="top" v-if="!hasAdminRole(scope.row)">
            <el-button @click="handleUpdateUserRoles(scope.$index, scope.row)" size="medium" type="warning" icon="el-icon-star-off" circle plain></el-button>
          </el-tooltip>
          <!--          <el-tooltip content="删除" placement="top" v-if="!hasAdminRole(scope.row)">-->
          <!--            <el-button @click="handleDelete(scope.$index,scope.row)" size="medium" type="danger" icon="el-icon-delete"-->
          <!--                       circle plain></el-button>-->
          <!--          </el-tooltip>-->
          <el-popover trigger="hover" placement="top" v-else style="display: inline-block">
            <el-alert type="warning" :closable="false" title="权限说明">
              <div>为保证管理员在系统中的最高权限</div>
              <div>不允许编辑管理员自身的角色</div>
              <div>不允许删除管理员账号</div>
            </el-alert>
            <div slot="reference">
              <el-tag style="margin-left: 10px" type="info">权限说明</el-tag>
            </div>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column prop="realname" label="用户姓名" width="150"></el-table-column>
      <el-table-column prop="merchantName" label="商户名" width="310"></el-table-column>
      <el-table-column label="角色">
        <template slot-scope="scope">
          <el-tag style="margin: 2px" v-for="role in scope.row.roles" :key="role.val">{{ role.name }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="time" label="创建时间">
        <template slot-scope="scope">
          <span v-text="parseTime(scope.row.createTime)"></span>
        </template>
      </el-table-column>
      <!-- <el-table-column prop="time" label="更新时间">
        <template slot-scope="scope">
          <span v-text="parseTime(scope.row.updated)"></span>
        </template>
      </el-table-column> -->
    </el-table>
    <div style="margin-bottom: 30px"></div>
    <!--分页-->
    <el-pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="tablePage.currentPage"
      :page-sizes="[10, 20, 30, 40, 50]"
      layout="total, sizes, prev, pager, next, jumper"
      :total="tablePage.totalItems"
    ></el-pagination>
    <!--弹出窗口：新增用户-->
    <el-dialog :title="textMap[dialogStatus]" :visible.sync="newDialogFormVisible" @close="closeCreat()" width="50%" :close-on-click-modal="false">
      <el-form :rules="NewRules" ref="dataForm" :model="addData" label-position="left" label-width="120px">
        <el-form-item label="登录名" prop="userName">
          <el-input v-model="addData.userName" placeholder="请输入手机号"></el-input>
        </el-form-item>

        <el-form-item label="用户名称" prop="realName">
          <el-input v-model="addData.realName" placeholder="请输入用户名称"></el-input>
        </el-form-item>

        <el-form-item label="商户名称" prop="merchantName">
          <el-input v-model="addData.merchantName" placeholder="请输入用户名称"></el-input>
        </el-form-item>

        <el-form-item label="角色" prop="roles" label-width="120px">
          <el-select @change="handleRoleChange" v-model="addData.roleIds" multiple placeholder="请选择" style="width: 100%">
            <el-option v-for="item in roleOptions" :key="item.value" :label="item.label" :value="item.val"></el-option>
          </el-select>
          <!-- <el-input type="password" v-model="addData.roles" ></el-input> -->
        </el-form-item>
        <el-form-item label="城市" prop="city" v-if="isCityBranch">
          <el-cascader style="width: 300px" :options="regionData" v-model="selectedOptions" :props="{ value: 'label' }"></el-cascader>
        </el-form-item>

        <el-form-item label="密码" prop="password">
          <el-input type="password" v-model="addData.password" placeholder="请输入密码"></el-input>
        </el-form-item>

        <el-form-item label="确认密码" prop="pwd2">
          <el-input type="password" v-model="addData.pwd2" placeholder="请再次输入密码"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="closeCreat()">取消</el-button>
        <el-button type="primary" @click="addUser">确定</el-button>
      </div>
      <!-- <p style="color: #00b878">设置</p> -->
    </el-dialog>
    <!--弹出窗口：编辑用户-->
    <el-dialog :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible" width="60%" :close-on-click-modal="false">
      <el-form :rules="rules" ref="dataForm" :model="temp" label-position="left" label-width="120px">
        <el-form-item label="昵称" prop="nickname">
          <el-input v-model="temp.nickname"></el-input>
        </el-form-item>

        <!-- <el-form-item label="密码" prop="password">
          <el-input type="password" v-model="password" placeholder="请输入密码"></el-input>
        </el-form-item> -->
        <el-form-item label="确认密码" prop="test">
          <el-input v-model="temp.test" placeholder="请输入密码"></el-input>
        </el-form-item>
        <el-form-item label="确认密码" prop="PIN">
          <el-input type="password" v-model="temp.PIN" placeholder="请确认密码"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button v-if="dialogStatus == 'create'" type="primary" @click="createData">创建</el-button>
        <el-button v-else type="primary" @click="updateData">确定</el-button>
      </div>
      <p style="color: #00b878">设置</p>
    </el-dialog>
    <!--弹出窗口：修改用户角色-->
    <el-dialog title="修改用户的角色" :visible.sync="editRolesDialogVisible" width="60%" :close-on-click-modal="false">
      <div>
        <el-checkbox :indeterminate="isIndeterminate" v-model="checkAll" @change="handleCheckAllChange">全选</el-checkbox>
        <div style="margin: 15px 0"></div>
        <el-checkbox-group v-model="updateUserRolesData.rids">
          <el-checkbox class="role-checkbox" v-for="role in roleOptions" :label="role.id" :key="role.id">
            {{ role.val }}
          </el-checkbox>
        </el-checkbox-group>
        <div v-if="editRolesDialogVisible && hasUpdateCityBranch" style="display: flex; align-items: center; margin-top: 20px">
          <div>城市：</div>
          <el-cascader style="width: 300px" :options="regionData" v-model="updateCitySelectedOptions" :props="{ value: 'label' }"></el-cascader>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="closeEditRolesDialog">取消</el-button>
        <el-button type="primary" :loading="updateUserRolesLoading" @click="checkUpdateUserRolesData">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import optionApi from '@/api/option';
  import userApi from '@/api/user';
  import { parseTime, resetTemp } from '@/utils';
  import { root, confirm, pageParamNames } from '@/utils/constants';
  import debounce from 'lodash/debounce';
  import { isvalidPhone } from '@/utils/validate';
  import { regionData } from 'element-china-area-data';
  export default {
    name: 'UserManage',

    data() {
      var validPhone = (rule, value, callback) => {
        if (!value) {
          callback(new Error('请输入电话号码'));
        } else if (!isvalidPhone(value)) {
          callback(new Error('请输入正确的11位手机号码'));
        } else {
          callback();
        }
      };

      let validateName = (rule, value, callback) => {
        if (this.dialogStatus == 'create' && value === '') {
          callback(new Error('必填'));
        } else {
          callback();
        }
      };

      let validatePass = (rule, value, callback) => {
        if (value === '') {
          callback(new Error('请输入密码'));
        } else {
          if (this.password2 !== '') {
            this.$refs.dataForm.validateField('password2');
          }
          callback();
        }
        // if (value === "") {
        //   callback(new Error("请再次输入密码"));
        // } else if (value != this.password2) {
        //   callback(new Error("两次输入密码不一致!"));
        // } else {
        //   callback();
        // }
      };

      let validatePass3 = (rule, value, callback) => {
        if (value === '') {
          callback(new Error('请再次输入密码'));
        } else if (value != this.temp.PIN) {
          callback(new Error('两次输入密码不一致!'));
        } else {
          callback();
        }
      };
      let validatePass2 = (rule, value, callback) => {
        if (value === '') {
          callback(new Error('请再次输入密码'));
        } else if (value != this.addData.password) {
          callback(new Error('两次输入密码不一致!'));
        } else {
          callback();
        }
      };
      // 城市分公司城市
      let validateCity = (rule, value, callback) => {
        if (this.isCityBranch) {
          if (this.selectedOptions && this.selectedOptions.length > 0) {
            return callback();
          }
          callback(new Error('请选择城市'));
        } else {
          callback();
        }
      };

      return {
        isCityBranch: false, // 是否有城市分公司
        selectedOptions: [],
        regionData,
        test: null,
        parseTime: parseTime,
        tableLoading: false,
        tableData: [],
        tableQuery: {
          loginName: null,
          realName: null,
          merchantName: null
        },
        // tablePage: {
        //   current: null,
        //   pages: null,
        //   size: null,
        //   total: null
        // },
        tablePage: {
          currentPage: null,
          totalPage: null,
          size: 10,
          totalItems: null
        },
        dialogFormVisible: false,
        newDialogFormVisible: false,
        editRolesDialogVisible: false,
        dialogStatus: '',
        // temp: {
        //   idx: null, //tableData中的下标
        //   uid: null,
        //   uname: null,
        //   nick: null,
        //   pwd: null,
        //   pwd2: null,
        //   created: null,
        //   updated: null
        // },
        temp: {
          idx: null, //tableData中的下标
          username: null,
          test: null,
          nickname: null,
          PIN: null
        },
        addData: {
          userName: '',
          realName: '',
          merchantName: '',
          roleIds: [],
          password: null,
          pwd2: null,
          province: '',
          city: ''
        },
        inputValue: '',

        password2: null, //新建用户确认密码
        textMap: {
          update: '编辑用户',
          create: '新增用户'
        },
        rules: {
          username: [{ validator: validateName, trigger: 'blur' }],
          test: [
            {
              required: true,
              message: '请填写密码',
              trigger: 'blur'
            },
            { validator: validatePass, trigger: 'blur' }
          ],
          PIN: [{ validator: validatePass3, trigger: 'change' }]
        },
        NewRules: {
          userName: [
            {
              required: true,
              message: '请填写手机号',
              trigger: 'blur'
            },
            {
              validator: validPhone,
              trigger: 'blur'
            }
          ],
          realName: [
            {
              required: true,
              message: '请填写用户名称',
              trigger: 'blur'
            }
            // {
            //   validator: validPhone,
            //   trigger: "blur",
            // },
          ],
          merchantName: [
            {
              required: true,
              message: '请填写商户名称',
              trigger: 'blur'
            }
            // {
            //   validator: validPhone,
            //   trigger: "blur",
            // },
          ],
          password: [{ validator: validatePass, trigger: 'blur' }],
          pwd2: [{ validator: validatePass2, trigger: 'change' }],
          city: [{ required: true, validator: validateCity, trigger: 'change' }]
          // roles: [
          //   {
          //     required: true,
          //     message: "请选择校色",
          //     trigger: "blur",
          //   },
          //   {
          //     validator: validPhone,
          //     trigger: "blur",
          //   },
          // ],
        },
        checkAll: false,
        isIndeterminate: true,
        //所有角色(管理员除外)
        roleOptions: [],
        roleMap: new Map(),
        // 更新用户的角色的数据
        updateUserRolesLoading: false,
        updateUserRolesData: {
          idx: null,
          id: null,
          province: undefined,
          city: undefined,
          rids: []
        },
        updateCitySelectedOptions: [],
        updateCityItem: {}
      };
    },

    computed: {
      hasUpdateCityBranch() {
        const result = this.updateUserRolesData.rids.some((rid) => rid == this.updateCityItem.id);
        if (!result) {
          this.updateUserRolesData.province = undefined;
          this.updateUserRolesData.city = undefined;
        }
        return result;
      }
    },

    created() {
      this.initData();
      this.fetchData();
      this.setDelete(1, this.regionData, 2);
      console.log('🚀 ~ created ~ regionData:', this.regionData);
    },

    watch: {
      //延时查询
      'tableQuery.nick': debounce(function () {
        this.fetchData();
      }, 500)
    }, //watch

    methods: {
      handleRoleChange(res) {
        let targetNames = res;
        const newArray = this.roleOptions.filter((item) => targetNames.includes(item.val));
        // 是否有城市分公司
        this.isCityBranch = newArray.some((item) => item.ext == 'CityCompany');
      },

      // 超过3级,不能选中,子级分类最多4级
      /**
       * count: 当前层级
       * data: 当前层级的数据
       * maxNum: 最多不能超过几级
       */
      setDelete(count, data, maxNum) {
        if (count >= maxNum) {
          //最多几级就写几
          data.forEach((v) => {
            v.children = null; // 超过设定的最大级数,直接删除
          });
        } else {
          data.forEach((v) => {
            v.count = count; // 设置最外层数据的初始count

            if (v.children && v.children.length) {
              if (v.children[0].label == '市辖区') {
                v.children = null; // 市辖区直接删除
                // v.children[0].label = v.label;
              } else {
                v.count++;
                this.setDelete(v.count, v.children, maxNum); // 子级循环时把这一层数据的count传入
              }
            }
          });
        }
      },
      initData() {
        //所有角色选项
        var that = this;
        optionApi.listRoleOptions().then((res) => {
          res.data.forEach((obj) => {
            //
            if (obj.value != root.rval) {
              //排除管理员
              var rd = { id: obj.value, val: obj.label, ext: obj.ext };
              that.roleOptions.push(rd);

              that.roleMap.set(obj.value, obj.label, obj.ext);
              //  return console.log(that.roleMap);
              if (rd.ext == 'CityCompany') {
                that.updateCityItem = { id: obj.value, val: obj.label, ext: obj.ext };
              }
            }
          });
          console.log(that.roleOptions, 'that.roleOptions');
        });
      },

      hasAdminRole(row) {
        if (row && row.roles) {
          return row.roles.some((role) => role.rval == root.rval);
        }
        return false;
      },

      //全选
      handleCheckAllChange(val) {
        let allRids = this.roleOptions.map((role) => role.id);
        this.updateUserRolesData.rids = val ? allRids : [];
        this.isIndeterminate = false;
      },

      //分页
      handleSizeChange(val) {
        this.tablePage.size = val;
        this.fetchData();
      },
      handleCurrentChange(val) {
        this.tablePage.currentPage = val;
        this.fetchData();
      },

      //查询
      fetchData() {
        this.tableLoading = true;
        userApi.queryUser(this.tableQuery, this.tablePage).then((res) => {
          console.log(res);
          this.tableData = res.data.data.reverse();
          this.tableLoading = false;
          pageParamNames.forEach((name) => this.$set(this.tablePage, name, Number(res.data[name])));
        });
      },
      bindCheck() {
        console.log(this.updateUserRolesData);
      },
      //更新
      handleUpdate(idx, row) {
        this.temp = Object.assign({}, row); // copy obj
        // this.temp.idx = idx
        console.log(idx);
        console.log(row);

        this.temp.password = '';
        this.temp.password2 = '';
        this.dialogStatus = 'update';
        this.dialogFormVisible = true;
        this.$nextTick(() => this.$refs['dataForm'].clearValidate());
        console.log(this.temp);
      },
      // 编辑
      updateData() {
        // if (this.test != this.password2) {
        //   this.$message.error("");
        // }
        this.$refs['dataForm'].validate((valid) => {
          if (!valid) return;
          var tempData = Object.assign({}, this.temp); //copy obj=
          // return console.log(tempData);
          var data = {
            password: this.temp.PIN,
            userName: tempData.username
          };
          userApi.editNewUser(data).then((res) => {
            // 表格中更新时间
            // tempData.updated = res.data.updated
            this.dialogFormVisible = false;
            this.$message.success('更新成功');
          });
        });
      },
      closeCreat() {
        this.newDialogFormVisible = false;
        this.isCityBranch = false;
        this.selectedOptions = [];
        this.addData = {
          phone: '',
          userName: '',
          Businesses: '',
          roles: [],
          password: null,
          pwd2: null,
          province: '',
          city: ''
        };
      },
      // 新增
      addUser() {
        if (this.isCityBranch) {
          if (this.selectedOptions.length == 0) {
            this.$message.error('城市分公司角色需要选择城市');
            return;
          } else {
            this.addData.province = this.selectedOptions[0];
            this.addData.city = this.selectedOptions[1] || this.selectedOptions[0];
          }
        } else {
          this.addData.province = '';
          this.addData.city = '';
        }
        this.$refs['dataForm'].validate((valid) => {
          if (!valid) return;

          this.findMatchingNames(this.addData.roleIds);
          // return console.log(this.addData.roleIds);

          // var creatData = Object.assign({}, this.addData)//copy obj=

          userApi
            .addNewUser(this.addData)
            .then((res) => {
              // 表格中更新时间
              // return console.log(res);
              // tempData.updated = res.data.updated
              this.closeCreat();
              this.$message.success('更新成功');
            })
            .catch((err) => {
              this.addData.roleIds = this.addRoleIds; //  新增失败后恢复原值
            });
        });
      },
      findMatchingNames(arr2) {
        this.addRoleIds = arr2; // 保存新增角色原始值
        const nameMap = {};
        const matchingIds = [];

        for (const item of this.roleOptions) {
          nameMap[item.val] = item.id;
        }

        for (const val of arr2) {
          if (nameMap[val]) {
            matchingIds.push(nameMap[val]);
          }
        }
        this.addData.roleIds = matchingIds;
        return console.log(matchingIds);
      },

      //更新用户的角色
      handleUpdateUserRoles(idx, row) {
        this.updateUserRolesLoading = false;
        // 显示用户的角色
        this.updateUserRolesData = {
          idx: idx,
          id: row.id,
          province: row.province,
          city: row.city,
          rids: row.roles.map((role) => role.val)
        };
        if (row.province && row.city) {
          this.updateCitySelectedOptions = row.province == row.city ? [row.province] : [row.province, row.city];
        } else {
          this.updateCitySelectedOptions = [];
        }
        console.log('this.updateUserRolesData', this.updateUserRolesData);
        console.log('this.updateCitySelectedOptions', this.updateCitySelectedOptions);

        // 显示弹窗
        this.editRolesDialogVisible = true;
      },
      checkUpdateUserRolesData() {
        const noRolesSelected = this.updateUserRolesData && this.updateUserRolesData.rids && this.updateUserRolesData.rids.length == 0;

        if (noRolesSelected) {
          this.$confirm('当前没有选中任何角色，会清除该用户已有的角色, 是否继续?', '提示', confirm)
            .then(() => {
              this.invokeUpdateUserRolesApi();
            })
            .catch(() => {
              this.$message('已取消编辑用户角色');
            });
        } else {
          this.invokeUpdateUserRolesApi();
        }
      },
      closeEditRolesDialog() {
        this.editRolesDialogVisible = false;
        this.updateUserRolesLoading = false;
        this.updateCitySelectedOptions = [];
      },
      bindCheck() {},
      invokeUpdateUserRolesApi() {
        // console.log('修改用户角色')
        if (this.hasUpdateCityBranch) {
          if (this.updateCitySelectedOptions.length == 0) {
            this.$message.error('请选择城市');
            return;
          }
          this.updateUserRolesData.province = this.updateCitySelectedOptions[0];
          this.updateUserRolesData.city = this.updateCitySelectedOptions[1] || this.updateCitySelectedOptions[0];
        }
        console.log('this.updateUserRolesData', this.updateUserRolesData);
        // if (this.updateUserRolesData.rids.length >= 2) {
        //   this.$message.info("只限制选择一个用户角色");
        //   return false;
        // }
        this.updateUserRolesLoading = true;
        userApi
          .updateUserRoles(this.updateUserRolesData)
          .then((res) => {
            let newRoles = this.updateUserRolesData.rids.map((rid) => {
              let rname = this.roleMap.get(rid);
              if (rname) return { rid, rname };
            });
            this.tableData[this.updateUserRolesData.idx].roles = newRoles;
            this.editRolesDialogVisible = false;
            this.updateUserRolesLoading = false;
            this.updateCitySelectedOptions = [];
            this.fetchData();
            this.$message.success('更新成功');
          })
          .catch(() => {
            this.updateUserRolesLoading = false;
          });
      },

      //删除
      handleDelete(idx, row) {
        this.$confirm('您确定要永久删除该用户？', '提示', confirm)
          .then(() => {
            userApi.deleteUser({ id: row.id }).then((res) => {
              this.tableData.splice(idx, 1);
              --this.tablePage.totalItems;
              this.dialogFormVisible = false;
              this.$message.success('删除成功');
            });
          })
          .catch(() => {
            this.$message.info('已取消操作');
          });
      },

      //新增
      handleCreate() {
        resetTemp(this.temp);
        this.dialogStatus = 'create';
        this.newDialogFormVisible = true;
        this.$nextTick(() => {
          this.$refs['dataForm'].clearValidate();
        });
      },

      createData() {
        this.$refs['dataForm'].validate((valid) => {
          if (!valid) return;
          userApi.addUser(this.temp).then((res) => {
            this.temp.uid = res.data.uid; //后台传回来新增记录的id
            this.temp.created = res.data.created; //后台传回来新增记录的时间
            this.temp.roles = [];
            this.tableData.unshift(Object.assign({}, this.temp));
            ++this.tablePage.totalPage;
            this.dialogFormVisible = false;
            this.$message.success('添加成功');
            this.fetchData();
          });
        });
      },

      //重置
      rest() {
        this.tableQuery.loginName = '';
        this.tableQuery.realName = '';
        this.tableQuery.merchantName = '';
        this.tableQuery.roleTag = '';
        this.fetchData();
      }
    }
  };
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
  .role-checkbox {
    margin-left: 0px;
    margin-right: 15px;
  }
</style>
