<template>
  <!-- 流程图 -->
  <div class="form-single-fragment" style="position: relative;">
    <el-row>
      <ProcessViewer :xml="taskProcessXml" :finishedInfo="finishedInfo"
                     :allCommentList="flowTaskCommentList"
                     style="height: 655px"/>
    </el-row>
  </div>
</template>

<script>
import '@/api/activiti/staticDict/flowStaticDict.js';
import flowOperationAPi from "@/api/activiti/flowOperation";
import flowOperationApi from "@/api/activiti/flowOperation";
import ProcessViewer from '@/views/activiti/workFlow/components/ProcessViewer';

export default {
  name: 'formTaskProcessViewer',
  props: {
    processDefinitionId: {
      type: String,
      required: true
    },
    processInstanceId: {
      type: String
    }
  },
  components: {
    ProcessViewer
  },
  data() {
    return {
      finishedInfo: undefined,
      taskProcessXml: undefined,
      flowTaskCommentList: []
    }
  },
  methods: {
    getTaskHighlightData() {
      if (this.processInstanceId == null || this.processInstanceId === '') {
        return;
      }
      let params = {
        processInstanceId: this.processInstanceId
      }
      flowOperationAPi.viewHighlightFlowData(params).then(res => {
        // 已完成节点
        this.finishedInfo = res.data;
      }).catch(e => {
      });
    },


    loadProcessCommentList() {
      this.flowTaskCommentList = [];
      if (this.processInstanceId == null || this.processInstanceId === '') {
        return;
      }
      flowOperationApi.listFlowTaskComment({
        processInstanceId: this.processInstanceId
      }).then(res => {
        this.flowTaskCommentList = res.data;
      }).catch(e => {
      });
    },

    getTaskProcessXml() {
      let params = {
        processDefinitionId: this.processDefinitionId
      }
      flowOperationAPi.viewProcessBpmn(params).then(res => {
        // 当前流程实例xml
        this.taskProcessXml = res.data;
      }).catch(e => {
      });
    }
  },
  mounted() {
    this.getTaskHighlightData();
    this.getTaskProcessXml();
    this.loadProcessCommentList();
  }
}
</script>

<style>
</style>
