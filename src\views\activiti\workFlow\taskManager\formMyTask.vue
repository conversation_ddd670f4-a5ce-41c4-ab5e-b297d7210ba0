<template>
  <!-- 待办任务 -->
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="88px">
      <el-form-item label="登录账号">
        <el-input class="filter-item" v-model="queryParams.name" :clearable="true" placeholder="登录账号" />
      </el-form-item>
      <el-form-item label="手机号">
        <el-input class="filter-item" v-model="queryParams.mobile" :clearable="true" placeholder="手机号" />
      </el-form-item>
      <el-form-item label="商户名称">
        <el-input class="filter-item" v-model="queryParams.merchantName" :clearable="true" placeholder="商户名称" />
      </el-form-item>
      <el-form-item label="账号角色">
        <el-input class="filter-item" v-model="queryParams.roleTag" :clearable="true" placeholder="账号角色" />
      </el-form-item>
      <el-form-item>
        <el-button type="success" icon="el-icon-search" size="mini" @click="getPageList1()">查询</el-button>
      </el-form-item>
    </el-form>
    <el-row>
      <el-col :span="24">
        <el-table ref="flowEntry" :data="tableList" size="mini" header-cell-class-name="table-header-gray">
          <el-table-column label="序号" header-align="center" align="center" type="index" width="55px"
            :index="tableList.getTableIndex" />
          <el-table-column label="流程名称" prop="processDefinitionName" />
          <el-table-column label="操作">
            <template slot-scope="scope">
              <el-button class="table-btn primary" size="mini" type="text" @click="onSubmit(scope.row)">办理</el-button>
            </template>
          </el-table-column>
          <el-table-column label="登录账号" prop="name" />
          <el-table-column label="手机号" prop="mobile" />
          <el-table-column label="商户名称" prop="merchantName" />
          <el-table-column label="账号角色" prop="roleTag" />
          <el-table-column label="当前任务节点" prop="taskName" />
          <el-table-column label="任务发起人" prop="createMerchantName" />
          <el-table-column label="任务发起时间" prop="processInstanceStartTime" />
        </el-table>
        <!-- 分页 -->
        <el-col :span="20">
          <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
            layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
            @current-change="handleCurrentChange" />
        </el-col>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import '@/api/activiti/staticDict/flowStaticDict.js';
import { DropdownWidget, TableWidget } from '@/utils/widget.js';
import { pageParamNames } from "@/utils/constants";
import flowOperationApi from '@/api/activiti/flowOperation';

export default {
  name: 'formMyTask',
  data() {
    return {
      queryParams: {
        taskName: undefined,
        mobile: undefined,
        merchantName: undefined,
        roleTag: undefined,
        name: undefined
      },
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      tableList: [],
    }
  },
  created() {
    this.getPageList()
  },
  methods: {
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val
      this.getPageList()
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val
      this.getPageList()

    },
    getPageList1() {
      this.tablePage = {
        currentPage: 1,
        size: this.tablePage.size,
        totalPage: null,
        totalItems: null
      }
      this.getPageList();
    },
    getPageList() {
      this.queryParams.pageNum = this.tablePage.currentPage;
      this.queryParams.pageSize = this.tablePage.size;
      flowOperationApi.listRuntimeTask(this.queryParams).then(res => {

        this.tableList = res.data.data;
        this.loading = false;
        // 设置后台返回的分页参数
        this.tablePage.currentPage = res.data.currentPage;
        this.tablePage.size = res.data.size;
        this.tablePage.totalItems = parseInt(res.data.totalItems);
        this.tablePage.totalPage = parseInt(res.data.totalPage);
        //pageParamNames.forEach(name => this.$set(this.tablePage, name, parseInt(res.data[name])))
      });
    },
    onSubmit(row) {
      let params = {
        processInstanceId: row.processInstanceId,
        processDefinitionId: row.processDefinitionId,
        taskId: row.taskId
      }
      flowOperationApi.viewRuntimeTaskInfo(params).then(res => {

        if (res.data) {
          this.$router.push({
            path: res.data.routerName || '/taskManager/handlerFlowTask',
            query: {
              isRuntime: true,
              taskId: row.taskId,
              processDefinitionKey: row.processDefinitionKey,
              processInstanceId: row.processInstanceId,
              processDefinitionId: row.processDefinitionId,
              formId: res.data.formId,
              routerName: res.data.routerName,
              readOnly: res.data.readOnly,
              taskName: row.taskName,
              flowEntryName: row.processDefinitionName,
              processInstanceInitiator: row.processInstanceInitiator,
              // 过滤掉加签操作，加签只有在已完成任务里可以操作
              operationList: (res.data.operationList || []).filter(item => {
                return item.type !== this.SysFlowTaskOperationType.CO_SIGN && item.type !== this.SysFlowTaskOperationType.REVOKE;
              }),
              variableList: res.data.variableList
            }
          });
        }
      }).catch(e => {
      });
    },
  },
}
</script>

<style></style>
