<template>
  <div>
    <el-form :inline="true" class="container-card" label-width="110px" label-position="left">
      <el-row>
        <el-col :span="12" :xs="24">

          <el-form-item>
            <el-form-item class="time" label="导出时间：">
              <el-date-picker style="width: 100%;" v-model="RegTime" type="daterange" value-format="yyyy-MM-dd HH:mm:ss"
                align="right" unlink-panels range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" clearable>
              </el-date-picker>
            </el-form-item>
          </el-form-item>
          <el-col :span="8" :xs="24">
            <el-form-item class="select" label="是否下载：">
              <el-select v-model="dataQuery.download" placeholder="全部" clearable>
                <el-option v-for="item in
                  [{ value: '1', label: '已下载' },
                  { value: '0', label: '未下载' }
                  ]" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-col>
        <el-col :span="4" :xs="24" style="text-align: right">
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="fetchData01()">搜索</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div class="tableBorder">
      <div class="table">
        <el-table class="period-table" v-loading="tableLoading" :data="tableData" style="margin-bottom: 20px;"
          row-key="id" border default-expand-all :tree-props="{ list: 'children', hasChildren: 'true' }">
          <el-table-column sortable prop="id" label="编号" width="200" :show-overflow-tooltip="true"></el-table-column>
          <el-table-column sortable prop="excelName" label="导出位置" width="300"
            :show-overflow-tooltip="true"></el-table-column>
          <el-table-column sortable prop="creationTime" label="创建时间" width="200"></el-table-column>
          <el-table-column prop="merchantId" label="操作人" width="100"></el-table-column>
          <el-table-column prop="completionTime" label="文件上传完成时间" width="200"></el-table-column>
          <el-table-column prop="download" label="是否下载" width="100">
            <template slot-scope="scope">
              <span v-if="scope.row.download === 1" class="green">已下载</span>
              <span v-if="scope.row.download === 0" class="red">未下载</span>
              <!--            <br>-->
              <!--            <el-button v-if="scope.row.download === 1" type="text" @click="open(scope.row.checkReason)" showCancelButton="false">查看详情</el-button>-->
            </template>
          </el-table-column>
          <el-table-column prop="lastDownloadTime" label="最近下载时间" width="200">
            <template slot-scope="scope">
              <span v-if="scope.row.download === 1" class="green">{{ scope.row.lastDownloadTime }}</span>
              <span v-if="scope.row.download === 0" class="red">未下载</span>
            </template>
          </el-table-column>

          <el-table-column label="下载" width="120">
            <template slot-scope="scope">
              <el-button :plain="true" @click="download(scope.row.id, scope.row.downloadUrl, scope.row.excelName)"
                style="background-color: #1890ff;color: white">下载
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="table">
        <!-- 分页 -->
        <el-col :span="24" style="overflow-x: auto;" :xs="24">
          <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
            layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
            @current-change="handleCurrentChange" />
        </el-col>
      </div>
    </div>

  </div>
</template>

<script>
import merchantAccountFlowApi from '@/api/merchantAccountFlow'
import { pageParamNames } from '@/utils/constants'

export default {
  name: 'merchantFlowExcelList',

  data() {
    return {
      excelName: '',
      dialogFormVisible: false,
      form: {
        name: '',
        region: '',
        date1: '',
        date2: '',
        delivery: false,
        type: [],
        resource: '',
        desc: ''
      },
      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      tableData: [{
        name: 1,
        title: '1111'
      }],
      changeOut: 0,//变动金额出
      changeIn: 0,//变动入
      dataQuery: {},
      dialogVisible: false,
      updateMarketDate: {},
      addMarketDate: {},
      showLoginAccount: false,
      financeAccountsType: [],//变动类型
      RegTime: '',
      //弹出导出数据的表单查询时间
      selectRrgTime: '',
      fundsSheet: false,
      exportLoading: false//商户流水导出
    }

  },
  methods: {

    fetchData01() {
      const that = this
      console.log(that.RegTime)
      const a = that.RegTime
      if (a != null) {
        that.dataQuery.startDate = a[0]
        that.dataQuery.endDate = a[1]
      } else {
        that.dataQuery.startDate = ''
        that.dataQuery.endDate = ''
      }
      if (that.excelName != null) {
        that.dataQuery.excelName = that.excelName
      }

      merchantAccountFlowApi.merchantAccountFlow(
        that.tablePage.currentPage,
        that.tablePage.size,
        that.dataQuery
      ).then(rec => {
        const that = this
        that.tableData = rec.data.data
        for (let i = 0; i < that.tableData.length; i++) {
          if (that.tableData[i].excelName.substring(0, 13) == 'runningWater/') {
            that.tableData[i].excelName = that.tableData[i].excelName.substring(13)
          }
        }
        that.tableLoading = false
        pageParamNames.forEach(name => that.$set(that.tablePage, name, parseInt(rec.data[name])))
      })
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val
      this.fetchData01()
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val
      this.fetchData01()
    },
    download(id, downloadUrl, excelName) {
      merchantAccountFlowApi
        .excelDownload(id).then(rec => {
          this.fetchData01()
        })
      const url = downloadUrl
      const link = document.createElement('a')
      link.style.display = excelName;
      link.href = url //获取服务器端的文件名
      link.setAttribute('download', excelName)
      document.body.appendChild(link)
      console.log(link)
      link.click()
      this.$message({
        message: '正在下载请稍后',
        type: 'success'
      })
    }
  },
  created() {
    this.fetchData01()
  }

}
</script>

<style scoped>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.period-table td,
.period-table th {
  text-align: center;
}

.el-table--medium th,
.el-table--medium td {
  padding: 10px 0;
  text-align: center;
}

.mt20 {
  margin-top: 20px;
}

.table {
  margin-left: 40px;
}

.tableBorder {
  text-align: center;
}

.select {
  display: inline-block;
  width: 500px;
}

.time {
  width: 600px;
}

.tableBorder[data-v-31c92740] {
  text-align: left;
}
</style>
