<template>
  <div class="app-container">
    <el-form :inline="true" ref="queryForm" class="container-card" label-width="110px">
      <el-form-item label="语言：">
        <el-select v-model="dataQuery.languageType" placeholder="全部" clearable>
          <el-option v-for="item in [{ label: '中文', value: 'cn' }, { label: '英文', value: 'en' }]" :key="item.value"
            :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handlePage()">搜索</el-button>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-re" @click="resetQuery()">重置</el-button>
      </el-form-item>

    </el-form>
    <el-col :span="24" style="margin-bottom: 30px">
      <el-button type="success" icon="el-icon-plus" size="mini" @click="handleAdd()">新增类型</el-button>
      <el-button type="warning" icon="el-icon-back" size="mini" @click="goBack()">返回</el-button>
    </el-col>

    <el-table class="common-table" v-loading="tableLoading" :data="tableData" style="width: 100%;margin-bottom: 20px;"
      row-key="id" border default-expand-all>
      <el-table-column prop="typeId" label="ID"></el-table-column>
      <el-table-column prop="name" label="类型名称"></el-table-column>
      <el-table-column label="操作">
        <template slot-scope="scope">
          <el-button type="primary" size="mini" @click="handleEdit(scope.row)">编辑</el-button>
          <el-button type="danger" size="mini" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="languageType" label="语言">
        <template slot-scope="scope">
          {{ scope.row.languageType === 'cn' ? '中文' : '英文' }}
        </template>
      </el-table-column>

    </el-table>
    <!-- 分页 -->
    <el-col :span="24" style="margin-bottom: 20px">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>

    <!-- 添加弹窗 -->
    <el-dialog :title="`${form.typeId ? '新建' : '编辑'}类型`" :visible.sync="open" width="63%" :close-on-click-modal="false"
      @close="close">
      <el-form ref="form" :model="form" :rules="rules" label-width="120px" style="width: 70%;">
        <el-form-item label="语言：" prop="languageType">
          <el-select v-model="form.languageType" placeholder="全部" clearable>
            <el-option v-for="item in [{ label: '中文', value: 'cn' }, { label: '英文', value: 'en' }]" :key="item.value"
              :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="名称：" prop="name">
          <el-input v-bind.sync="form.name" v-model="form.name" maxlength="5" show-word-limit />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="submitForm()">确定</el-button>
        <el-button size="mini" @click="close">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import spokenTestApi from '@/api/studyroom/spokenTest'
import { pageParamNames } from '@/utils/constants'

export default {
  data() {
    return {
      planDates: [],
      btn: false,
      btn1: false,
      open: false,
      scheduleId: null,
      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      tableData: [],
      dataQuery: {
        languageType: ''
      },
      form: {},
      // 表单校验
      rules: {
        name: [
          { required: true, message: '请输入名称', trigger: 'blur' }
        ],
        languageType: [
          { required: true, message: '请选择语言', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.getPageList()
  },
  methods: {
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.form.name.length > 5) {
            this.$message.error('最多5个！')
            return false;
          }
          if (this.form.typeId) {
            spokenTestApi.updateType(this.form).then(response => {
              this.$message.success('提交成功！')
              this.open = false
              this.getPageList()
            })
          } else {
            spokenTestApi.createType(this.form).then(response => {
              this.$message.success('提交成功！')
              this.open = false
              this.getPageList()
            })
          }
        }
      })
    },
    goBack() {
      this.$store.dispatch('delVisitedViews', this.$route)
      this.$router.go(-1)
    },
    /** 编辑按钮*/
    handleEdit(row) {
      this.reset()
      spokenTestApi.typeDetail(row.typeId).then(res => {
        this.form = res.data
        this.open = true
      })
    },
    //删除
    handleDelete(row) {
      this.$confirm('确定要删除吗?', '删除类型', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        spokenTestApi.typeDelete(row.typeId).then(res => {
          this.$message.success('删除成功！')
          this.getPageList();
        })
      })
    },
    getPageList() {
      this.tableLoading = true
      this.dataQuery.pageNum = this.tablePage.currentPage
      this.dataQuery.pageSize = this.tablePage.size
      spokenTestApi.listType(this.dataQuery).then(res => {
        this.tableData = res.data.data
        this.tableLoading = false
        // 设置后台返回的分页参数
        pageParamNames.forEach(name => this.$set(this.tablePage, name, parseInt(res.data[name])))
      })
    },

    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
    },
    handlePage() {
      this.tablePage = {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
        this.getPageList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dataQuery = {
        languageType: ''
      }
      this.getPageList()
    },
    close() {
      this.open = false
      this.reset()
    },
    reset() {
      this.form = {
        name: '',
        languageType: ''
      }
      this.planDates = ''
      if (this.$refs['form']) {
        this.$refs['form'].resetFields()
      }
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val
      this.getPageList()
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val
      this.getPageList()
    }
  }
}
</script>

<style>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.period-table td,
.period-table th {
  text-align: center;
}

@media (max-width: 767px) {
  .el-message-box {
    width: 80% !important;
  }
}
</style>
