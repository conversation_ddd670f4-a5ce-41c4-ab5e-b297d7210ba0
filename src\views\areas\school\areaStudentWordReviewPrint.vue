<template>
  <div class="app-container">
    <el-form :inline="true" class="container-card" label-width="110px" label-position="left">
      <el-row>
        <el-col :span="8" :xs="24">
          <el-form-item label="学员编号：">
            <el-input v-model="dataQuery.studentCode" placeholder="请输入学员编号" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="登录账号：">
            <el-input v-model="dataQuery.loginName" placeholder="请输入登录账号" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="学员手机号：">
            <el-input v-model="dataQuery.memberPhone" placeholder="请输入学员手机号" clearable />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="8">
          <el-form-item label="添加时间：">
            <el-date-picker value-format="yyyy-MM-dd hh:mm:ss" v-model="regTime" type="daterange" align="right" unlink-panels range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" @change="dateVal">
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="姓名：">
            <el-input v-model="dataQuery.realName" placeholder="请输入姓名" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item>
            <el-button type="primary" size="small" icon="el-icon-search" @click="fetchData01()">搜索</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <!-- <el-col :span="24" style="margin-bottom: 30px;">
      <el-button type="warning" icon="el-icon-document-copy" @click="openBigRecharge()" size="mini">学员充值学时</el-button>
    </el-col> -->

    <el-table class="common-table" v-loading="tableLoading" :data="tableData" style="width: 100%;margin-bottom: 20px;" row-key="id" border default-expand-all :tree-props="{list: 'children', hasChildren: 'true'}">
      <el-table-column prop="studentCode" label="学员编号"></el-table-column>
      <el-table-column prop="loginName" label="登录账号"></el-table-column>
      <el-table-column prop="withdrawnBonus" label="操作" width="340">
        <template slot-scope="scope">
          <!-- <el-button type="success" size="mini" icon="el-icon-edit-outline" @click="jumpOpenCourse(scope.row.studentCode)">开通课程</el-button>
          <el-button type="primary" size="mini" icon="el-icon-sell" @click="openRecharge(scope.row.studentCode)">充值学时</el-button>
          <el-button type="danger" size="mini" icon="el-icon-sold-out" @click="openBackRecharge(scope.row.studentCode)">退课</el-button> -->
          <el-button type="warning" size="mini" icon="el-icon-view" @click="enterChildrenList(scope.row.studentCode,scope.row.realName)">打印21天抗遗忘
          </el-button>
          <el-button type="warning" size="mini" icon="el-icon-view" @click="enterChildrenList1(scope.row.studentCode,scope.row.realName)">21天抗遗忘记录
          </el-button>
        </template>
      </el-table-column>
      <el-table-column prop="realName" label="姓名"></el-table-column>
      <el-table-column prop="memberPhone" label="学员手机号">
        <template #header>
          <span>学员手机号</span>
          <i :class="phoneShowStatus=='0'?'el-icon-lock':'el-icon-unlock'" style="color: #1890ff; margin-left: 5px; font-size: 15px" @click="phoneShow" v-if="currentRole == 'School'"></i>
        </template>
        <template slot-scope="scope">
          <span>
            {{ scope.row.memberPhone }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="gradeName" label="年级"></el-table-column>
      <el-table-column prop="school" label="学校" show-overflow-tooltip></el-table-column>
      <!-- <el-table-column prop="totalCourseHours" label="已购课时（节）" width="150"></el-table-column>
      <el-table-column prop="haveCourseHours" label="剩余课时（节）" width="150"></el-table-column> -->
      <el-table-column prop="addTime" label="添加时间"></el-table-column>

    </el-table>

    <el-dialog class="recharge-dialog" title="充值" :visible.sync="showRecharge" width="70%" :close-on-click-modal="false">
      <el-form v-model="recharge" label-position="left" label-width="150px" style="width: 100%;">
        <el-form-item label="门店剩余课时(节)：" prop="course">
          <el-input v-model="recharge.course" readonly="readonly" disabled />
        </el-form-item>
        <el-form-item label="充值账户：" prop="studentCode">
          <el-input v-model="recharge.studentCode" />
        </el-form-item>
        <el-form-item label="充值时长：" prop="courseLength">
          <el-input v-model="recharge.courseLength" type="number" maxlength="20" isNumber2="true" min="1" @blur="blurCourseLength(recharge.courseLength)" />
        </el-form-item>
        <el-form-item label="学时单价：" prop="coursePrice">
          <el-input v-model="recharge.coursePrice" @blur="blurCoursePrice(recharge.coursePrice)" type="number" maxlength="20" isNumber2="true" min="1" />
        </el-form-item>
        <el-form-item label="合计金额 ：" prop="sumCoursePrice">
          <el-input v-model="recharge.sumCoursePrice" disabled />
        </el-form-item>
        <el-form-item label="到账课时 ：" prop="rechargeCourse">
          <el-input v-model="recharge.toAccountCourse" readonly="readonly" disabled />
        </el-form-item>
        <el-form-item label="充值说明 ：" prop="remark">
          <el-input v-model="recharge.remark" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="openDialogVisible()">确定</el-button>
      </div>
    </el-dialog>

    <el-dialog title="退课" :visible.sync="showBackRecharge" width="70%" :close-on-click-modal="false">
      <el-form v-model="backRecharge" label-position="left" label-width="120px" style="width: 100%;">
        <el-form-item label="学员编号：" prop="course">
          <el-col :xs="24" :span="18">
            <el-input v-model="backRecharge.studentCode" readonly="readonly" disabled />
          </el-col>
        </el-form-item>
        <el-form-item label="剩余学时(节)：" prop="haveCourseHours">
          <el-col :xs="24" :span="18">
            <el-input v-model="backRecharge.haveCourseHours" readonly="readonly" disabled />
          </el-col>
        </el-form-item>
        <el-form-item label="退学时(节)：" prop="tuiHours">
          <el-col :xs="24" :span="18">
            <el-input v-model="backRecharge.tuiHours" type="number" maxlength="20" isNumber2="true" min="1" />
          </el-col>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" :disabled="disabledA" @click="submitBackRecharge()">确定</el-button>
      </div>
    </el-dialog>

    <el-dialog class="remark-dialog" title="交易密码确认" :visible.sync="dialogVisible" width="20%" :close-on-click-modal="false">
      <el-input v-model="secondPassWord" show-password></el-input>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" :disabled="disabledF" @click="submitRecharge(secondPassWord)">确认
        </el-button>
      </div>
    </el-dialog>

    <!-- 分页 -->
    <el-col :span="24" style="overflow-x: auto;" :xs="24">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]" layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    </el-col>
    <ShowPhone :dialogVisible.sync="dialogVisiblePhone" :phoneShowStatus.sync="phoneShowStatus" @fetchData="fetchData"></ShowPhone>
  </div>
</template>

<script>
import studentApi from '@/api/areasStudentCourseList'
import Tinymce from '@/components/Tinymce'
import {
  pageParamNames
} from '@/utils/constants'
import merchantAccountFlowApi from '@/api/merchantAccountFlow'
import ls from '@/api/sessionStorage'
import ShowPhone from '@/components/ShowPhone/index.vue';
import userApi from '@/api/user';

export default {
  name: 'areaStudentWordReviewPrint',
  components: { ShowPhone },
  data() {
    return {
      showBackRecharge: false,
      showRecharge: false,
      recharge: {
        course: 0,
        toAccountCourse: 0,
        sumCoursePrice: 0
      },
      backRecharge: {},
      tableLoading: false,
      disabledF: false,
      disabledA: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      tableData: [],
      dataQuery: {
        studentCode: '',
        startDate: '',
        endDate: '',
        isEnable: '',
        isFormal: '',
        loginName: '',
        realName: '',
        memberCode: '',
        memberPhone: '',
        restrictedUse: '',
      },
      dialogVisible: false,
      updateMarketDate: {},
      addMarketDate: {},
      showLoginAccount: false,
      secondPassWord: '',
      regTime: '',
      dialogVisiblePhone: false,
      phoneShowStatus: localStorage.getItem('phoneShowStatus'),
      currentPhone: '',
      // 当前权限
      currentRole: window.localStorage.getItem('roleTag'),
    }
  },
  async created() {
    if (this.currentRole == 'School') {
      await this.getPhoneNum()
    }
    this.getAccount()
    this.fetchData(this.phoneShowStatus)
  },
  methods: {

    openDialogVisible() {
      if (!this.recharge.course) {
        this.$message.error('门店剩余课时不能为空')
        return false
      }
      if (this.recharge.course <= 0) {
        this.$message.error('门店剩余课时不能为0')
        return false
      }
      if (!this.recharge.courseLength) {
        this.$message.error('充值时长不能为空')
        return false
      }
      if (this.recharge.courseLength <= 0) {
        this.$message.error('充值时长不能0')
        return false
      }
      if (!this.recharge.coursePrice) {
        this.$message.error('课时单价不能为空')
        return false
      }
      if (this.recharge.coursePrice <= 0) {
        this.$message.error('课时单价不能为0')
        return false
      }
      this.dialogVisible = true
      this.secondPassWord = ''
    },

    submitBackRecharge() {
      const that = this
      that.disabledA = true
      if (!that.backRecharge.studentCode) {
        that.$message.error('学员编号不能为空')
        return false
      }
      if (!that.backRecharge.haveCourseHours) {
        that.$message.error('剩余课时不能为空')
        return false
      }

      if (that.backRecharge.haveCourseHours <= 0) {
        that.$message.error('剩余课时不能为0')
        return false
      }
      if (!that.backRecharge.tuiHours) {
        that.$message.error('退课时不能为空')
        return false
      }

      // this.$confirm('确定操作吗?', '退课时', {
      //   confirmButtonText: '确定',
      //   cancelButtonText: '取消',
      //   type: 'warning'
      // }).then(() => {
      studentApi.submitBackRecharge(this.backRecharge).then(res => {
        that.showBackRecharge = false
        that.disabledA = false
        that.$nextTick(() => that.fetchData(this.phoneShowStatus))
        that.$message.success('退课时成功')
      }).catch(err => {
        debugger
        console.log(err)
      })
      //})
    },

    submitRecharge(secondPassWord) {
      this.disabledF = true
      if (!secondPassWord) {
        this.$message.error('二级密码不能为空')
        return false
      }
      if (!this.recharge.course) {
        this.$message.error('门店剩余课时不能为空')
        return false
      }
      if (!this.recharge.studentCode) {
        this.$message.error('学员账户不能空')
        return false
      }
      if (this.recharge.course <= 0) {
        this.$message.error('门店剩余课时不能为0')
        return false
      }
      if (!this.recharge.courseLength) {
        this.$message.error('充值时长不能为空')
        return false
      }
      if (this.recharge.courseLength <= 0) {
        this.$message.error('充值时长不能0')
        return false
      }
      if (!this.recharge.coursePrice) {
        this.$message.error('课时单价不能为空')
        return false
      }
      if (this.recharge.coursePrice <= 0) {
        this.$message.error('课时单价不能为0')
        return false
      }

      merchantAccountFlowApi.checkSecondPwd(secondPassWord).then(res => {
        if (!res.success) {
          this.$message.error('二级密码验证失败')
          this.disabledF = false
          return false
        }

        // this.$confirm('确定操作吗?', '充值时长', {
        //   confirmButtonText: '确定',
        //   cancelButtonText: '取消',
        //   type: 'warning'
        // }).then(() => {
        studentApi.submitRecharge(this.recharge).then(res => {
          if (!res.success) {
            this.$message.error(res.message)
            return false
          }
          this.disabledF = false
          this.showRecharge = false
          this.dialogVisible = false
          this.fetchData01()
          this.$message.success('充值成功!')

        })
      })
      // })
    },

    blurCourseLength(courseLength) {
      this.recharge.toAccountCourse = courseLength
    },
    blurCoursePrice(coursePrice) {
      if (coursePrice !== '' && coursePrice !== 0 && coursePrice !== '') {
        this.recharge.sumCoursePrice = coursePrice * this.recharge.toAccountCourse
      }
    },
    getAccount() {
      studentApi.getSchoolAccount().then(res => {
        if (res.data != null) {
          this.recharge.course = res.data.course
        }
      })
    },
    openBigRecharge() {
      this.showRecharge = true
      this.recharge = {
        studentCode: '',
        course: this.recharge.course,
        coursePrice: 0,
        courseLength: 0,
        sumCoursePrice: 0,
        toAccountCourse: 0,
        remark: ''
      }
    },
    openBackRecharge(studentCode) {

      this.backRecharge.studentCode = studentCode
      //查询退课
      studentApi.getBackRecharge(studentCode).then(res => {
        this.backRecharge.haveCourseHours = res.data.haveCourseHours
        this.showBackRecharge = true
      })
      this.backRecharge = {
        studentCode: this.backRecharge.studentCode,
        haveCourseHours: this.backRecharge.haveCourseHours,
        tuiHours: 0
      }
    },
    openRecharge(studentCode) {
      this.recharge.studentCode = studentCode
      this.showRecharge = true
      this.recharge = {
        studentCode: studentCode,
        course: this.recharge.course,
        courseLength: 0,
        coursePrice: 0,
        sumCoursePrice: 0,
        toAccountCourse: 0,
        remark: ''
      }
    },

    fetchData01() {
      this.tablePage = {
        currentPage: 1,
        size: this.tablePage.size,
        totalPage: null,
        totalItems: null
      }
      this.fetchData(this.phoneShowStatus)
    },
    // 获取起始时间
    dateVal(e) {
      // console.log(e[0]);
      this.dataQuery.startTime = e[0]
      this.dataQuery.endTime = e[1]
    },
    async fetchData(phoneShowStatus) {
      const that = this
      var a = that.regTime
      if (a != null) {
        that.dataQuery.startRegTime = a[0]
        that.dataQuery.endRegTime = a[1]
      }
      that.tableLoading = true
      studentApi.studentList(that.tablePage.currentPage, that.tablePage.size, that.dataQuery, phoneShowStatus == '0' ? '' : that.currentPhone).then(res => {
        that.tableData = res.data.data
        that.tableLoading = false
        // 设置后台返回的分页参数
        pageParamNames.forEach(name => that.$set(that.tablePage, name, parseInt(res.data[name])))
      })
    },
    // 获取当前登录用户手机号
    async getPhoneNum() {
      const res = await userApi.getPhone();
      this.currentPhone = res.data.phone;
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val
      this.fetchData(this.phoneShowStatus)
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val
      this.fetchData(this.phoneShowStatus)
    },
    //进入21天抗遗忘打印
    enterChildrenList(studentCode, realName) {
      const that = this
      ls.setItem('printReviewStudentCode', studentCode)
      ls.setItem('printReviewRealName', realName)
      that.$router.push({
        path: '/student/studentWordReviewPrint',
        query: {
          studentCode: studentCode
        }
      })
    },
    //进入查看
    enterChildrenList1(studentCode, realName) {
      const that = this
      ls.setItem('printReviewStudentCode', studentCode)
      ls.setItem('printReviewRealName', realName)
      that.$router.push({
        path: '/student/studentWordReviewList',
        query: {
          studentCode: studentCode
        }
      })
    },
    // 手机号码显示隐藏
    phoneShow() {
      console.log(this.phoneShowStatus);
      if (this.tableData.length == 0) return;
      if (this.phoneShowStatus == '0') {
        console.log(this.phoneShowStatus, 11111);
        this.dialogVisiblePhone = true;
      } else {
        this.phoneShowStatus = '0';
        localStorage.setItem('phoneShowStatus', '0');
        this.fetchData(this.phoneShowStatus);
      }
    }
  }
}
</script>

<style>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.period-table td,
.period-table th {
  text-align: center;
}

.mt20 {
  margin-top: 20px;
}

.red {
  color: red;
}

.green {
  color: green;
}

@media screen and (max-width: 767px) {
  .recharge-dialog .el-dialog {
    width: 90% !important;
  }

  .el-message-box {
    width: 80% !important;
  }
}
</style>
