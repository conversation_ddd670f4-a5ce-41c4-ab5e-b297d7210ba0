<template>
  <div class="app-container">
    <el-form :model="dataQuery" ref="queryForm" :inline="true">
      <el-form-item label="ID查询：">
        <el-input v-model="dataQuery.id" type="number" clearable placeholder="请输入ID"></el-input>
      </el-form-item>
      <el-form-item label="维度：">
        <el-select v-model="dataQuery.dimensionId" clearable placeholder="请选择维度">
          <el-option v-for="item in dimensionList" :key="item.id" :value="item.id" :label="item.name"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="学段：">
        <el-select v-model="dataQuery.grade" clearable placeholder="请选择学段">
          <el-option v-for="item in gradeList" :key="item" :value="item" :label="item"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="题型：">
        <el-select v-model="dataQuery.questionType" clearable>
          <el-option v-for="item in questionTypeList" :key="item.code" :value="item.code" :label="item.msg"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="时间查询：">
        <el-date-picker
          v-model="timeDate"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="类型：">
        <el-select v-model="dataQuery.type" placeholder="类型" clearable>
          <el-option v-for="item in calculationType" :key="item.code" :value="item.code" :label="item.msg"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" style="marginRight: 5px" @click="query">查询</el-button>
        <el-button type="primary" style="marginRight: 5px" @click="resetQuery">重置</el-button>
        <el-button type="primary" @click="addQuestion">添加</el-button>
      </el-form-item>
    </el-form>
    <el-table class="common-table" v-loading="listLoading" :data="tableData" style="width: 100%; margin-bottom: 20px"
      row-key="id" border default-expand-all>
      <el-table-column prop="id" label="题目ID" />
      <el-table-column label="操作" align="center" width="220px">
        <template slot-scope="{ row }">
          <el-button size="mini" @click="editQuestion(row)">编辑</el-button>
          <el-button size="mini" type="danger" @click="deleteQuestion(row)" class="link-left">删除</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="grade" label="学段"/>
      <el-table-column prop="dimension" label="维度"/>
      <el-table-column prop="questionType" label="题型" :formatter="questionName"/>
      <el-table-column prop="customInfo" label="题干" show-overflow-tooltip>
        <template slot-scope="scope">
          <div v-html="scope.row.stem"></div>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间" />
      <el-table-column prop="type" label="类型" width="120px" :formatter="calculationTypeFormatter"/>
    </el-table>
    <!-- 分页 -->
    <el-col :span="24" style="margin-bottom: 20px">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>

    <el-dialog title="添加题型" :visible.sync="addOpen" width="70%" @close="cancel">
      <el-form ref="addForm" :model="form" :rules="rules" label-width="120px" style="width: 70%;">
        <el-form-item label="选择学段：" prop="grade">
          <el-select v-model="form.grade"  @change="choseGradeChange()">
            <el-option v-for="(item, index) in gradeList" :key="index" :label="item" :value="item">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="选择维度：" prop="dimensionId">
          <el-select v-model="form.dimensionId" @change="dimensionIdChange()">
            <el-option v-for="(item, index) in gradeDimensionList" :key="index" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="选择题型：" prop="questionType">
          <el-select v-model="form.questionType" @change="questionTypeChange()">
            <el-option v-for="(item, index) in questionTypeList" :key="index" :label="item.msg" :value="item.code">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>

import courseQuestionApi from "@/api/abacusMentalCalc/courseQuestion";;
import { pageParamNames } from "@/utils/constants";
import '/public/components/ueditor/themes/iframe.css'
import categoryApi from "@/api/abacusMentalCalc/category";
import {mapGetters, mapState} from "vuex";

export default {
  data() {
    return {
      dataQuery: {
        id: null,
        type: null,
        dimensionId: null,
        grade: null,
        startTime: null,
        endTime: null,
        questionType: null,
      },
      listLoading: true,
      tableData: [],

      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null,
      },
      total: 0,

      timeDate:null,

      gradeDimensionList: [],
      dimensionList: [],
      questionTypeList: [],
      calculationType:[],
      gradeList:[],

      addOpen:false,
      form:{},
      // 表单校验
      rules: {
        dimensionId: [{ required: true, message: '请选择', trigger: 'blur' }],
        grade: [{ required: true, message: '请选择', trigger: 'blur' }],
        questionType: [{ required: true, message: '请选择', trigger: 'blur' }],
      }
    };
  },
  created() {
    this.getAllGrade();
    this.getAllDimension()
    this.getQuestionType()
    this.getCalculationType()

    this.getPageList()
  },
  methods: {
    questionName(row, column, cellValue, index) {
      for (let item of this.questionTypeList) {
        if (item.code === cellValue) {
          return item.msg
        }
      }
      return '无'
    },
    calculationTypeFormatter(row, column, cellValue, index) {
      for (let item of this.calculationType) {
        if (item.code === cellValue) {
          return item.msg
        }
      }
      return '无'
    },

    /////弹框 start
    choseGradeChange(){
      if(this.form.dimension){
        this.form.dimension = ""
        this.form.dimensionId = ""
      }
      categoryApi.getDimensionForGrade(this.form.grade,null).then(res => {
        this.gradeDimensionList = res.data
      });
      this.$forceUpdate()
    },
    dimensionIdChange(){
      let result = this.gradeDimensionList.find((item) => {
        return item.id === this.form.dimensionId;
      });
      this.form.dimension = result.name
      this.$forceUpdate()
    },
    questionTypeChange(){
      let result = this.questionTypeList.find((item) => {
        return item.code === this.form.questionType;
      });
      this.form.questionTypeName = result.msg
      this.$forceUpdate()
    },
    addQuestion(){
      this.addOpen = true
    },
    cancel(){
      this.addOpen = false
      this.$refs['addForm'].resetFields()
    },
    submitForm(){
      let url = this.trainUrlFormat(this.questionUrlEnum, this.form.questionType);
      this.$router.push({ path: url, query: { formData: this.form } });
    },
    /////弹框 end

    getAllGrade(){
      categoryApi.getGrade().then(res => {
        this.gradeList = res.data
      });
    },
    getAllDimension(){
      categoryApi.getAllDimensionHaveRepeat().then(res => {
        this.dimensionList = res.data
      });
    },
    getQuestionType(){
      courseQuestionApi.getQuestionType().then(res => {
        this.questionTypeList = res.data
      });
    },
    getCalculationType(){
      courseQuestionApi.getCalculationType().then(res => {
        this.calculationType = res.data
      });
    },
    query() {
      this.tablePage.currentPage = 1;
      this.getPageList()
    },
    getPageList() {
      this.listLoading = true
      this.dataQuery.pageNum = this.tablePage.currentPage;
      this.dataQuery.pageSize = this.tablePage.size;
      if(this.timeDate && this.timeDate.length > 0){
        this.dataQuery.startTime = this.getFormatTime(this.timeDate[0]);
        this.dataQuery.endTime = this.getFormatTime(this.timeDate[1]);
      }
      courseQuestionApi.questionList(this.dataQuery).then((res) => {
        this.tableData = res.data.data == null ? []:res.data.data;
        this.listLoading = false;
        // 设置后台返回的分页参数
        pageParamNames.forEach((name) =>
          this.$set(this.tablePage, name, parseInt(res.data[name]))
        );
      });
    },
    getFormatTime(time){
      let date = new Date(time);
      let year = date.getFullYear();
      let month = date.getMonth() + 1;  // getMonth()返回值是0~11
      let day = date.getDate();
      let hour = date.getHours();
      let minute = date.getMinutes();
      let second = date.getSeconds();

      let result = year + "-" + (month < 10 ? "0" + month : month) + "-" + (day < 10 ? "0" + day : day) + " " + (hour < 10 ? "0" + hour : hour) + ":" + (minute < 10 ? "0" + minute : minute) + ":" + (second < 10 ? "0" + second : second);
      return result
    },
    editQuestion(row) {
      let url = this.trainUrlFormat(this.questionUrlEnum, row.questionType);
      this.$router.push({ path: url, query: { formData: {id:row.id} } });
    },
    deleteQuestion(row) {
      let _this = this
      this.$confirm('是否删除该题目？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        courseQuestionApi.questionDelete(row.id).then(re => {
          if (re.success) {
            _this.$message.success(re.message)
            _this.getPageList()
          } else {
            _this.$message.error(re.message)
          }
        })
      })
    },
    resetQuery(){
      this.tablePage.currentPage = 1;
      this.timeDate = null;
      this.dataQuery={
          id: null,
          type: null,
          grade: null,
          dimensionId: null,
          startTime: null,
          endTime: null,
          questionType: null,
      }
      this.getPageList()
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val;
      this.getPageList();
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val;
      this.getPageList();
    },
  },
  computed: {
    ...mapGetters('enumItem', ['trainUrlFormat']),
    ...mapState('enumItem', {
      questionUrlEnum: state => state.zhuXinSuan.questionUrlEnum
    }),
  }
}
</script>

<style lang="less" scoped>
/deep/.el-dialog__header {
  border-bottom: 1px solid #dfdfdf;
}
</style>

<!--onequestion:-->
<!--"UNDERSTANDING_NUMBERS": "UNDERSTANDING_NUMBERS",//认识数字题,-->
<!--"LISTENING_CONCENTRATION": "LISTENING_CONCENTRATION",//音频答案题 根据音频说出答案判断对错-->
<!--"NUMBER_BALL_TRANSLATION": "NUMBER_BALL_TRANSLATION",//"拨珠系统题 算盘直接展示数字键盘输入正确数字-->

<!--onlyscore-->
<!--"NUMBERS": "NUMBERS",//数字输入题 输入答案判断对错-->

<!--questionanswer-->
<!--"CALCULATION": "CALCULATION",//算式答案题 说出答案判断对错-->
<!--"PLUCKING_BEADS": "PLUCKING_BEADS",//全盘拨珠题 ; 拨完算盘弹出键盘输入结果-->
<!--"PLUCKING_BEADS_FORMAL": "PLUCKING_BEADS_FORMAL",//拨珠演示题型 有拨珠6-->
<!--"PLUCKING_BEADS_CALCULATION": "PLUCKING_BEADS_CALCULATION",//拨珠算式题 -无拨珠6-->

<!--videoandio-->
<!--"LISTENING": "LISTENING",//音频讲解 ;听音频-->
<!--"VIDEO": "VIDEO",//视频讲解题 -;看视频-->

<!--倒计时-->
<!--"NUMBER_CONCENTRATION": "NUMBER_CONCENTRATION",//数码秒计题 数字显示几秒小时输入显示数字-->

<!--对补数字-->
<!--"COMPLEMENT_NUMBER": "COMPLEMENT_NUMBER",//对补数数字题型-->
<!--对补图片-->
<!--"COMPLEMENT_NUMBER_PHOTO": "COMPLEMENT_NUMBER_PHOTO",//对补数图片题型-->

<!--不需要-->
<!--"STUDY_ABACUS": "STUDY_ABACUS",//步骤讲解题 学习算盘-->
