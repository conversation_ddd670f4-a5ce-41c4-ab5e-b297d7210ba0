<template>
  <div class="app-container">
    <el-form :inline="true" ref="queryForm" class="container-card" label-width="110px">
      <el-form-item label="学段：">
        <el-select v-model="dataQuery.gradeId" placeholder="学段" @change="levelChange" clearable>
          <el-option v-for="item in gradeList" :key="item.id" :value="item.id" :label="item.name"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="维度：">
        <el-select v-model="dataQuery.subjectId" clearable>
          <el-option v-for="item in subjectFilter" :key="item.id" :value="item.id" :label="item.name"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-re" @click="resetQuery()">重置</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="getPageList()">搜索</el-button>
      </el-form-item>
    </el-form>
    <el-col :span="24" style="margin-bottom: 30px">
      <el-button type="success" icon="el-icon-plus" size="mini" @click="addBtn()">新增</el-button>
    </el-col>
    <el-table class="common-table" v-loading="tableLoading" :data="tableData" style="width: 100%;margin-bottom: 20px;"
      row-key="id" border default-expand-all>
      <el-table-column prop="gradeId" label="学段名称" :formatter="gradeListFormatter" />
      <el-table-column prop="subjectId" label="维度名称" :formatter="subjectFormatter" />
      <el-table-column prop="withdrawnBonus" label="操作" width="480">
        <template slot-scope="scope">
          <el-button type="success" size="mini" icon="el-icon-edit-outline" @click="editBtn(scope.row)">编辑 </el-button>
          <el-button type="danger" size="mini" icon="el-icon-delete" @click="delBtn(scope.row)">删除
          </el-button>
        </template>
      </el-table-column>
      <el-table-column prop="count" label="节点数量" />
    </el-table>
    <!-- 分页 -->
    <el-col :span="24" style="margin-bottom: 20px">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>

    <!-- 修改或者新增弹窗 -->
    <el-dialog title="编辑知识点" :visible.sync="open" width="70%" @close="close">
      <el-form ref="form" :model="form" :rules="rules" label-width="120px" style="width: 70%;">
        <el-form-item label="选择学段：" prop="gradeId" required>
          <el-select v-model="form.gradeId" placeholder="选择学段" @change="formLevelChange" clearable>
            <el-option v-for="item in gradeList" :key="item.id" :value="item.id" :label="item.name"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="选择维度：" prop="subjectId" required>
          <el-select v-model="form.subjectId" placeholder="选择维度" @change="subjectChange">
            <el-option v-for="item in subjectFilter" :key="item.id" :value="item.id" :label="item.name"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="知识点树：" required>
          <el-tree :data="treeData" :props="props" node-key="id" default-expand-all :expand-on-click-node="false">
            <span class="custom-tree-node" slot-scope="{ node, data }">
              <span>{{ node.label }}</span>
              <span style="margin-left: 30px;">
                <el-button type="text" size="mini" @click="() => addTree(data.id)">
                  添加
                </el-button>
                <el-button type="text" size="mini" @click="() => updateTree(data)">
                  修改
                </el-button>
              </span>
            </span>
          </el-tree>
        </el-form-item>
        <el-form-item>
          <el-button type="success" @click="addTree(0)">添加根节点</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>

    <el-dialog :visible.sync="treeOpen" width="70%" @close="treeClose">
      <el-form ref="treeForm" :model="treeForm" :rules="rules" label-width="120px" style="width: 70%;">
        <el-form-item label="知识点名称：" prop="name">
          <el-input v-model="treeForm.name" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="treeOpen = false">取 消</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import subjectApi from '@/api/paper/subject'
import knowledgeApi from '@/api/paper/knowledge'
import { pageParamNames } from '@/utils/constants'
import { mapGetters, mapState } from 'vuex'

export default {
  name: 'subject',
  data() {
    return {
      gradeList: [],
      treeSaveData: {},
      props: {
        label: "name",
        children: "child"
      },
      treeData: [],
      dataQuery: {
        gradeId: null,
        subjectId: null
      },
      subjectFilter: null,
      subjectList: [],
      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      treeOpen: false,
      open: false,
      tableData: [],
      form: {},
      treeForm: {},
      // 表单校验
      rules: {
        gradeId: [
          { required: true, message: '请选择学段', trigger: 'blur' }
        ],
        subjectId: [
          { required: true, message: '请选择维度', trigger: 'blur' }
        ],
        name: [
          { required: true, message: '请输入知识点名称', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.initGrade();
    this.initSubject()
    this.getPageList()
  },
  methods: {
    initGrade() {
      subjectApi.gradeListAll().then(res => {
        this.gradeList = res.data;
      })
    },
    subjectChange() {
      knowledgeApi.treeList(this.form).then(res => {
        this.treeData = res.data;
      })
    },

    addTree(parentId) {
      this.treeForm = {
        name: ''
      }
      this.$refs['form'].validate(valid => {
        if (valid) {
          this.form.id = null;
          this.form.parentId = parentId;
          this.treeOpen = true;
        }
      })
    },
    treeClose() {
      this.treeOpen = false
      this.treeForm = {
        name: ''
      }
    },

    updateTree(data) {
      if (this.$refs['treeForm']) {
        this.$refs['treeForm'].clearValidate();
      }
      this.treeForm = {
        name: data.name
      };
      this.form.id = data.id;
      console.log(this.form);
      this.treeOpen = true;
    },
    initSubject() {
      subjectApi.pageList().then(res => {
        this.subjectList = res.data.data
      })
    },
    levelChange() {
      this.dataQuery.subjectId = null
      this.subjectFilter = this.subjectList.filter(data => data.gradeId === this.dataQuery.gradeId)
    },
    formLevelChange() {
      this.form.subjectId = null
      this.subjectFilter = this.subjectList.filter(data => data.gradeId === this.form.gradeId)
    },
    gradeListFormatter(row, column, cellValue, index) {
      return this.subjectFormat(this.gradeList, cellValue)
    },
    subjectFormatter(row, column, cellValue, index) {
      return this.subjectFormat(this.subjectList, cellValue)
    },
    addBtn() {
      this.reset()
      this.open = true
      this.treeData = [];
    },
    delBtn(row) {
      this.$confirm('确定要删除该数据吗?', '删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        knowledgeApi.deleteBySubjectId(row.subjectId).then(res => {
          this.$nextTick(() => this.getPageList())
          this.$message.success('删除成功!')
        }).catch(err => {

        })
      }).catch(err => {

      })
    },
    editBtn(row) {
      this.reset()
      this.form.gradeId = row.gradeId
      this.subjectFilter = this.subjectList.filter(data => data.gradeId === this.form.gradeId)
      this.form.subjectId = row.subjectId
      this.subjectChange();
      this.open = true
    },
    submitForm() {
      this.$refs['treeForm'].validate(valid => {
        if (valid) {
          this.form.name = this.treeForm.name;
          knowledgeApi.create(this.form).then(response => {
            this.$message.success('提交成功！')
            this.subjectChange();
            this.treeOpen = false;
            this.getPageList()
          })
        }
      })
    },
    getPageList() {
      this.tableLoading = true
      this.dataQuery.pageNum = this.tablePage.currentPage
      this.dataQuery.pageSize = this.tablePage.size
      knowledgeApi.pageList(this.dataQuery).then(res => {
        console.log(res.data.data)
        this.tableData = res.data.data
        this.tableLoading = false
        // 设置后台返回的分页参数
        pageParamNames.forEach(name => this.$set(this.tablePage, name, parseInt(res.data[name])))
      })
    },
    handlePage() {
      this.tablePage = {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
        this.getPageList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dataQuery = {
        gradeId: null,
        subjectId: null
      }
      this.getPageList()
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val
      this.getPageList()
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val
      this.getPageList()
    },
    close() {
      this.open = false
      this.reset()
      this.$refs['form'].clearValidate();
      if (this.$refs['treeForm']) {
        this.$refs['treeForm'].clearValidate();
      }
    },
    reset() {
      this.form = {
        gradeId: null,
        subjectId: null,
        parentId: 0
      }
    }
  },
  computed: {
    ...mapGetters('enumItem', ['enumFormat', 'subjectFormat']),
    ...mapState('enumItem', {
      // gradeList: state => state.question.gradeList
    })
  }
}
</script>

<style>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.period-table td,
.period-table th {
  text-align: center;
}

@media (max-width: 767px) {
  .el-message-box {
    width: 80% !important;
  }
}

.el-tooltip__popper {
  max-width: 800px;
}
</style>
