/**
 * 学员管理相关接口
 */
import request from '@/utils/request'

export default {
  // 分页查询
  offSiteList(data) {
    return request({
      url: '/znyy/offsite/order',
      method: 'GET',
      params: data
    })
  },
  // 重试充值
  retry(data) {
    return request({
      url: '/znyy/offsite/retry/recharge',
      method: 'PUT',
      params: {'orderId': data}
    })
  },
// 重试充值
  confirm(data) {
    return request({
      url: '/znyy/offsite/confirm/finalPayment',
      method: 'PUT',
      params: {'orderId': data}
    })
  },
  //指定商户
  assignMerchant(orderId,merchantCode){
    return request({
      url: '/znyy/promoter/assign/merchant?orderId='+orderId+'&merchantCode='+merchantCode,
      method: 'PUT'
    })
  }

}
