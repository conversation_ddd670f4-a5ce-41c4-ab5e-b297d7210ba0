<template>
  <div class="app-container">
    <el-form :inline="true" class="container-card" label-width="110px" label-position="left">
      <el-row>
        <el-col :span="8" :xs="24">
          <el-form-item label="学员编号：">
            <el-input v-model="dataQuery.studentCode" placeholder="请输入学员编号" clearable/>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="登录账号：">
            <el-input v-model="dataQuery.loginName" placeholder="请输入登录账号" clearable/>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="姓名：">
            <el-input v-model="dataQuery.realName" placeholder="请输入姓名" clearable/>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="16" :xs="24">
          <el-form-item label="能力水平：">
            <el-select v-model="dataQuery.studyRank" placeholder="全部" style="width: 185px;">
              <el-option v-for="(item,index) in studyRank" :key="index" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item>
            <el-button type="primary" size="small" icon="el-icon-search" @click="fetchData01()">搜索</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <el-table class="common-table" v-loading="tableLoading" :data="tableData" style="width: 100%;margin-bottom: 20px;" row-key="id" border
              default-expand-all :tree-props="{list: 'children', hasChildren: 'true'}">
      <el-table-column prop="studentCode" label="学员编号" width="120"  sortable></el-table-column>
      <el-table-column prop="loginName" label="登录账号" width="120"  sortable></el-table-column>
      <el-table-column prop="withdrawnBonus" label="操作" width="120">
        <template slot-scope="scope">
          <el-button type="success" size="mini" icon="el-icon-edit-outline" @click="jumpOpenCourse(scope.row.id)">打印</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="realName" label="姓名" sortable ></el-table-column>
      <el-table-column prop="wordLevel" label="词汇量"  width="90" sortable></el-table-column>
      <el-table-column prop="addTime" label="开始时间" width="160"  sortable></el-table-column>
      <el-table-column prop="endTime" label="结束时间" width="160" sortable></el-table-column>
      <el-table-column prop="title" label="能力水平" width="120" sortable></el-table-column>
      <el-table-column prop="levelDescription" label="测试结果"  width="400" sortable show-overflow-tooltip></el-table-column>
      
    </el-table>
    <!-- 分页 -->
    <el-col :span="24" style="overflow-x: auto;" :xs="24">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]" layout="total, sizes, prev, pager, next, jumper"
                     :total="tablePage.totalItems" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    </el-col>
  </div>
</template>

<script>
  import testResultApi from "@/api/areasStudentTestResultList";
  import Tinymce from "@/components/Tinymce";
  import {  pageParamNames  } from "@/utils/constants";
  import ls from '@/api/sessionStorage'

  export default {
    data() {
      return {
        tableLoading: false,
        // 分页
        tablePage: {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null
        },
        tableData: [],
        dataQuery: {
          studentCode:'',
          merchantCode:'',
          startDate:'',
          endDate:'',
          loginName:'',
          realName:''
        },
        studyRank:[],
        value1:'',
        exportLoading:false,
      };
    },
    created() {
      this.fetchData();
      this.getStudyRank();
    },
    methods: {
      // 获取起始时间
      dateVal(e) {
        // console.log(e[0]);
        this.dataQuery.startDate = e[0]
        this.dataQuery.endDate = e[1]
      },

      getStudyRank(){
        testResultApi.getStudyRank().then(res => {
          this.studyRank = res.data;
        })
      },
      fetchData01(){
         this.tablePage= {
          currentPage: 1,
          size: this.tablePage.size,
          totalPage: null,
          totalItems: null
        }
        this.fetchData();
      },
      // 查询提现列表
      fetchData() {
        const that = this;
        that.tableLoading = true
        testResultApi.testResultList(that.tablePage.currentPage, that.tablePage.size, that.dataQuery).then(res => {
          that.tableData = res.data.data
          that.tableLoading = false
          // 设置后台返回的分页参数
          pageParamNames.forEach(name => that.$set(that.tablePage, name, parseInt(res.data[name])))
        })
      },
      // 分页
      handleSizeChange(val) {
        this.tablePage.size = val;
        this.fetchData();
      },
      handleCurrentChange(val) {
        this.tablePage.currentPage = val;
        this.fetchData();
      },
      // 跳转到测试结果
      jumpOpenCourse(id){
        const that = this
        ls.setItem('testId', id);
        that.$router.push({
          path:'/student/studentWordsTest',
          query:{
            id:id
          }
        })
      },
    }
  };
</script>

<style>
  .container-card {
    padding: 15px 15px 15px 15px;
    margin-bottom: 15px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  }
  .period-table td,
  .period-table th{
    text-align: center;
  }
  .mt20{
    margin-top: 20px;
  }
  .red{
    color: red;
  }
  .green{
    color: green;
  }
</style>
