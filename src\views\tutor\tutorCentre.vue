<template>
  <div class="app-container">
    <el-form :inline="true" class="container-card" label-width="96px" label-position="left">
      <el-form-item label="教练 名称：">
        <el-input v-model="dataQuery.tutorName" placeholder="请输入教练 名称" clearable />
      </el-form-item>
      <el-form-item label="性别:">
        <el-select v-model="dataQuery.gender" filterable value-key="value" placeholder="请选择" clearable>
          <el-option v-for="(item,index) in [{ value :1,label:'男'},{value: 0, label: '女'}]" :key="index"
            :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="手机号：">
        <el-input v-model="dataQuery.phoneNumber" placeholder="请输入教练 手机号" clearable />
      </el-form-item>
      <el-form-item>
        <el-form-item label="录入时间：" clearable>
          <el-date-picker style="width: 100%;" value-format="yyyy-MM-dd" clearable v-model="inputTime" type="daterange"
            align="right" unlink-panels range-separator="至" start-placeholder="开始时间" end-placeholder="结束时间">
          </el-date-picker>
        </el-form-item>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="getList()">搜索</el-button>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-re" @click="resetQuery()">重置</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="success" icon="el-icon-re" @click="addTutor()">新建教练 </el-button>
      </el-form-item>
    </el-form>

    <el-table class="common-table" v-loading="tableLoading" :data="tableData" style="width: 100%;margin-bottom: 20px;"
      row-key="id" border default-expand-all :tree-props="{list: 'children', hasChildren: 'true'}">
      <el-table-column prop="tutorId" label="教练 ID" sortable></el-table-column>
      <el-table-column prop="tutorName" label="教练 名称" sortable></el-table-column>
      <el-table-column prop="withdrawnBonus" label="操作">
        <template slot-scope="scope">
          <el-button type="success" size="mini" icon="el-icon-edit-outline"
            @click="openEdit(scope.row.tutorId,scope.row.tutorType)">查看</el-button>
          <el-button v-if="scope.row.checkStatus==='Pass'" size="mini" type="warning"
            @click="editStatus(scope.row.tutorId,'Stop')">停课</el-button>
          <el-button v-if="scope.row.checkStatus==='Stop'" size="mini" type="warning"
            @click="editStatus(scope.row.tutorId,'Pass')">复课</el-button>
          <el-button size="mini" type="danger" @click="deleteTutor(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="gender" label="教练 性别" sortable></el-table-column>
      <el-table-column prop="phoneNumber" label="教练 手机号" sortable></el-table-column>
      <el-table-column prop="courseTime" label="已授学时" sortable></el-table-column>
      <el-table-column prop="inputTime" label="录入时间" sortable></el-table-column>
      <el-table-column prop="status" label="状态" sortable></el-table-column>
    </el-table>

    <!--    新建教练 弹窗-->
    <el-dialog title="新增教练 " :visible.sync="showAddTutor" width="60%" :close-on-click-modal="false" @close="close">
      <el-form :ref="'tutorAdd'" :rules="addTutorSingle" :model="tutorAdd" label-position="left" label-width="110px"
        style="width: 100%">
        <el-form-item label="教练 姓名：" prop="tutorNme">
          <el-col :xs="24" :span="12">
            <el-input resize="none" :rows="4" v-model="tutorAdd.tutorNme"></el-input>
          </el-col>
        </el-form-item>

        <el-form-item label="教练 性别：" prop="gender">
          <el-select v-model="tutorAdd.gender" filterable value-key="value" placeholder="请选择" id="gender"
            @change="check(tutorAdd.gender)" clearable>
            <el-option v-for="(item,index) in [
                 { value: '1', label: '男' },
                 { value: '0', label: '女' }]" :key="index" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>

        <el-form-item label="教练 手机号：" prop="phoneNumber">
          <el-col :xs="24" :span="12">
            <el-input resize="none" :rows="4" v-model="tutorAdd.phoneNumber"></el-input>
          </el-col>
        </el-form-item>

        <el-form-item label="教练 类型：" prop="tutorType">
          <el-select v-model="tutorAdd.tutorType" filterable value-key="value" placeholder="请选择" id="tutorType"
            @change="check(tutorAdd.tutorType)" clearable>
            <el-option v-for="(item,index) in tutorTypeList" :key="index" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="addTutorCommit(tutorAdd)">确定</el-button>
        <el-button size="mini" @click="closeEdit">关闭</el-button>
      </div>
    </el-dialog>

    <!--  大学员教练 详情  -->
    <el-dialog title="教练 详情" :visible.sync="showStudentEdit" width="90%" :close-on-click-modal="false" @close="close">
      <el-form :ref="tutorInfoVo" :model="tutorInfoVo" label-position="left" label-width="150px" style="width: 100%">
        <el-form-item label="id" prop="id" v-show="false">
          <el-col :xs="24" :span="12">
            <el-input v-model="tutorInfoVo.id"></el-input>
          </el-col>
        </el-form-item>

        <el-form-item>
          <el-col :xs="24" :span="4">
            教练 姓名：{{ tutorInfoVo.tutorName }}
          </el-col>
          <el-col :xs="24" :span="4">
            教练 职业：大学员
          </el-col>
          <el-col :xs="24" :span="4">
            联系方式：{{ tutorInfoVo.phoneNumber }}
          </el-col>
          <el-col :xs="24" :span="4">
            现居住地：{{ tutorInfoVo.address }}
          </el-col>
          <el-col :xs="24" :span="4">
            授课范围：{{ tutorInfoVo.jobScope }}公里
          </el-col>
          <el-col :xs="24" :span="4">
            出生日期：{{ tutorInfoVo.dateOfBirth }}
          </el-col>
        </el-form-item>

        <el-form-item>
          <el-col :xs="24" :span="4">
            在读学校：{{ tutorInfoVo.schoolName }}
          </el-col>
          <el-col :xs="24" :span="4">
            辅导员姓名：{{ tutorInfoVo.teacherName }}
          </el-col>
          <el-col :xs="24" :span="4">
            辅导员电话：{{ tutorInfoVo.teacherNumber }}
          </el-col>
          <el-col :xs="24" :span="4">
            紧急联系人：{{ tutorInfoVo.emergencyContact }}
          </el-col>
          <el-col :xs="24" :span="4">
            紧急联系人电话：{{ tutorInfoVo.emergencyContactNumber }}
          </el-col>
        </el-form-item>

        <el-form-item>
          学校地址：{{ tutorInfoVo.schoolAddress }}
        </el-form-item>

        <el-form-item>
          授课版本：{{ tutorInfoVo.courseType }}
        </el-form-item>

        <el-form-item>
          <el-col :xs="24" :span="24" v-for="item in tutorInfoVo.tutorMonthJobTimeVoList" style="margin-top: 15px">
            <el-form-item>
              {{ item.monthYear }}月：
            </el-form-item>
            <el-form-item label-width="1px">
              <el-col :span="8">
                工作日： {{ item.workdayJobTimeList }}
              </el-col>
              <el-col :span="8">
                双休日： {{ item.weekendJobTimeList }}
              </el-col>
              <el-col :span="8">
                节假日： {{ item.festivalJobTimeList }}
              </el-col>
            </el-form-item>
          </el-col>
        </el-form-item>

        <el-form-item>
          身份证：
          <el-image v-for="img in tutorInfoVo.idCardList" style="width: 300px; height: 260px;margin-right: 40px"
            :src="aliUrl+img" :preview-src-list="tutorInfoVo.idCardList"></el-image>
        </el-form-item>

        <el-form-item>
          学员证：
          <el-image v-for="img in tutorInfoVo.studentCardList" style="width: 300px; height: 260px;margin-right: 40px"
            :src="aliUrl+img" :preview-src-list="tutorInfoVo.studentCardList"></el-image>
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer" v-if="tutorInfoVo.checkStatus==='Waiting'">
        <el-button size="medium" type="primary" @click="editStatus(tutorInfoVo.tutorId,'Pass')">同意</el-button>
        <el-button size="medium" type="danger" @click="editStatus(tutorInfoVo.tutorId,'Fail')">拒绝</el-button>
        <el-button size="medium" @click="closeEdit">关闭</el-button>
      </div>
    </el-dialog>

    <!--    自由人教练 详情-->
    <el-dialog title="教练 详情" :visible.sync="showUnemployedEdit" width="90%" :close-on-click-modal="false"
      @close="close">
      <el-form :ref="tutorInfoVo" :model="tutorInfoVo" label-position="left" label-width="150px" style="width: 100%">
        <el-form-item label="id" prop="id" v-show="false">
          <el-col :xs="24" :span="12">
            <el-input v-model="tutorInfoVo.id"></el-input>
          </el-col>
        </el-form-item>

        <el-form-item>
          <el-col :xs="24" :span="4">
            教练 姓名：{{ tutorInfoVo.tutorName }}
          </el-col>
          <el-col :xs="24" :span="4">
            教练 职业：自由人
          </el-col>
          <el-col :xs="24" :span="4">
            联系方式：{{ tutorInfoVo.phoneNumber }}
          </el-col>
          <el-col :xs="24" :span="4">
            现居住地：{{ tutorInfoVo.address }}
          </el-col>
          <el-col :xs="24" :span="4">
            授课范围：{{ tutorInfoVo.jobScope }}公里
          </el-col>
          <el-col :xs="24" :span="4">
            出生日期：{{ tutorInfoVo.dateOfBirth }}
          </el-col>
        </el-form-item>

        <el-form-item>
          <el-col :xs="24" :span="4">
            直系亲属：{{ tutorInfoVo.firstFamilyName }}
          </el-col>
          <el-col :xs="24" :span="4">
            直系亲属电话：{{ tutorInfoVo.firstFamilyNumber }}
          </el-col>
          <el-col :xs="24" :span="4">
            直系亲属：{{ tutorInfoVo.secondFamilyName }}
          </el-col>
          <el-col :xs="24" :span="4">
            直系亲属电话：{{ tutorInfoVo.secondFamilyNumber }}
          </el-col>
        </el-form-item>

        <el-form-item>
          授课版本：{{ tutorInfoVo.courseType }}
        </el-form-item>

        <el-form-item>
          <el-col :xs="24" :span="24" v-for="item in tutorInfoVo.tutorMonthJobTimeVoList" style="margin-top: 15px">
            <el-form-item>
              {{ item.monthYear }}月：
            </el-form-item>
            <el-form-item label-width="1px">
              <el-col :span="8">
                工作日： {{ item.workdayJobTimeList }}
              </el-col>
              <el-col :span="8">
                双休日： {{ item.weekendJobTimeList }}
              </el-col>
              <el-col :span="8">
                节假日： {{ item.festivalJobTimeList }}
              </el-col>
            </el-form-item>
          </el-col>
        </el-form-item>

        <el-form-item>
          身份证：
          <el-image v-for="img in tutorInfoVo.idCardList" style="width: 300px; height: 260px;margin-right: 40px"
            :src="aliUrl+img" :preview-src-list="tutorInfoVo.idCardList"></el-image>
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer" v-if="tutorInfoVo.checkStatus==='Waiting'">
        <el-button size="medium" type="primary" @click="editStatus(tutorInfoVo.tutorId,'Pass')">同意</el-button>
        <el-button size="medium" type="danger" @click="editStatus(tutorInfoVo.tutorId,'Fail')">拒绝</el-button>
        <el-button size="medium" @click="closeEdit">关闭</el-button>
      </div>
    </el-dialog>

    <!--    在职教练 详情-->
    <el-dialog title="教练 详情" :visible.sync="showJobEdit" width="90%" :close-on-click-modal="false" @close="close">
      <el-form :ref="tutorInfoVo" :model="tutorInfoVo" label-position="left" label-width="150px" style="width: 100%">
        <el-form-item label="id" prop="id" v-show="false">
          <el-col :xs="24" :span="12">
            <el-input v-model="tutorInfoVo.id"></el-input>
          </el-col>
        </el-form-item>

        <el-form-item>
          <el-col :xs="24" :span="4">
            教练 姓名：{{ tutorInfoVo.tutorName }}
          </el-col>
          <el-col :xs="24" :span="4">
            教练 职业：自由人
          </el-col>
          <el-col :xs="24" :span="4">
            联系方式：{{ tutorInfoVo.phoneNumber }}
          </el-col>
          <el-col :xs="24" :span="4">
            现居住地：{{ tutorInfoVo.address }}
          </el-col>
          <el-col :xs="24" :span="4">
            授课范围：{{ tutorInfoVo.jobScope }}公里
          </el-col>
          <el-col :xs="24" :span="4">
            出生日期：{{ tutorInfoVo.dateOfBirth }}
          </el-col>
        </el-form-item>

        <el-form-item>
          <el-col :xs="24" :span="4">
            紧急联系人：{{ tutorInfoVo.emergencyContact }}
          </el-col>
          <el-col :xs="24" :span="4">
            紧急联系人电话：{{ tutorInfoVo.emergencyContactNumber }}
          </el-col>
          <el-col :xs="24" :span="4">
            毕业院校：{{ tutorInfoVo.schoolOfGraduation }}
          </el-col>
          <el-col :xs="24" :span="4">
            职业：{{ tutorInfoVo.vocation }}
          </el-col>
          <el-col :xs="24" :span="4">
            领导姓名：{{ tutorInfoVo.leaderName }}
          </el-col>
          <el-col :xs="24" :span="4">
            领导电话：{{ tutorInfoVo.leaderNumber }}
          </el-col>
          <el-col :xs="24" :span="4">
            公司地址：{{ tutorInfoVo.companyAddress }}
          </el-col>
        </el-form-item>

        <el-form-item>
          授课版本：{{ tutorInfoVo.courseType }}
        </el-form-item>

        <el-form-item>
          <el-col :xs="24" :span="24" v-for="item in tutorInfoVo.tutorMonthJobTimeVoList" style="margin-top: 15px">
            <el-form-item>
              {{ item.monthYear }}月：
            </el-form-item>
            <el-form-item label-width="1px">
              <el-col :span="8">
                工作日： {{ item.workdayJobTimeList }}
              </el-col>
              <el-col :span="8">
                双休日： {{ item.weekendJobTimeList }}
              </el-col>
              <el-col :span="8">
                节假日： {{ item.festivalJobTimeList }}
              </el-col>
            </el-form-item>
          </el-col>
        </el-form-item>

        <el-form-item>
          身份证：
          <el-image v-for="img in tutorInfoVo.idCardList" style="width: 300px; height: 260px;margin-right: 40px"
            :src="aliUrl+img" :preview-src-list="tutorInfoVo.idCardList"></el-image>
        </el-form-item>

        <el-form-item>
          工作合同：
          <el-image v-for="img in tutorInfoVo.workContractList" style="width: 300px; height: 260px;margin-right: 40px"
            :src="aliUrl+img" :preview-src-list="tutorInfoVo.workContractList"></el-image>
        </el-form-item>

        <el-form-item>
          学位证：
          <el-image v-for="img in tutorInfoVo.certificateOfDegreeList"
            style="width: 300px; height: 260px;margin-right: 40px" :src="aliUrl+img"
            :preview-src-list="tutorInfoVo.certificateOfDegreeList"></el-image>
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer" v-if="tutorInfoVo.checkStatus==='Waiting'">
        <el-button size="medium" type="primary" @click="editStatus(tutorInfoVo.tutorId,'Pass')">同意</el-button>
        <el-button size="medium" type="danger" @click="editStatus(tutorInfoVo.tutorId,'Fail')">拒绝</el-button>
        <el-button size="medium" @click="closeEdit">关闭</el-button>
      </div>
    </el-dialog>

    <!-- 分页 -->
    <el-col :span="20" style="overflow-x: auto;" :xs="24">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>
  </div>
</template>

<script>

import tutorApi from '@/api/tutor'
import { pageParamNames } from '@/utils/constants'
import cousysApi from '@/api/cousys'

export default {
  data() {
    return {
      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      tableData: [],
      showStudentEdit: false,
      showUnemployedEdit: false,
      showJobEdit: false,
      showAddTutor: false,
      dataQuery: {
        status: '',
        gender: '',
        phoneNumber: ''
      },
      inputTime: [],
      tutorInfoVo: {},
      //新增教练 
      tutorAdd: {
        tutorType: '',
        tutorNme: '',
        gender: '',
        phoneNumber: '',
      },
      //新增学员验证
      addTutorSingle: {
        tutorType: [{
          required: true,
          message: '必填',
          trigger: 'change'
        }],
        tutorNme: [{
          required: true,
          message: '必填',
          trigger: 'blur'
        }],
        gender: [{
          required: true,
          message: '必填',
          trigger: 'change'
        }],
        phoneNumber: [{
          required: true,
          message: '必填',
          trigger: 'blur'
        }]
      },
      //教练 类型列表
      tutorTypeList: [],
    }
  },
  created() {
    this.getList()
    this.getTutorTypeList()
  },
  methods: {
    closeEdit() {
      this.showStudentEdit = false;
      this.showUnemployedEdit = false;
      this.showJobEdit = false;
      this.showAddTutor = false;
      this.tutorAdd = {};
    },
    //获取教练 类型列表
    getTutorTypeList() {
      const that = this
      tutorApi.getTutorTypeList().then(res => {
        that.tutorTypeList = res.data
      })
    },
    //获取列表
    getList() {
      const that = this
      if (that.inputTime != '' && that.inputTime != null && that.inputTime != undefined) {
        if (that.inputTime.length > 0) {
          that.dataQuery.startTime = that.inputTime[0]
          that.dataQuery.endTime = that.inputTime[1]
        } else {
          that.dataQuery.startTime = ''
          that.dataQuery.endTime = ''
        }
      } else {
        that.dataQuery.startTime = ''
        that.dataQuery.endTime = ''
      }
      that.tableLoading = true
      tutorApi.getTutorList(that.tablePage.currentPage, that.tablePage.size, that.dataQuery).then(res => {
        that.tableData = res.data.data
        console.log(res.data.data)
        that.tableLoading = false
        // 设置后台返回的分页参数
        pageParamNames.forEach(name => this.$set(this.tablePage, name, parseInt(res.data[name])))
      })
    },

    //重置按钮
    resetQuery() {
      this.inputTime = []
      this.dataQuery = {
        status: '',
        gender: '',
        phoneNumber: '',
      }
      this.getList()
    },
    //新建教练 
    addTutor() {
      this.showAddTutor = true
      this.getList()
    },
    addTutorCommit(tutorAdd) {
      const that = this
      tutorApi.addTutorCommit(tutorAdd).then((res) => { })
      that.showAddTutor = false;
      that.getList()
    },
    //查看按钮
    openEdit(tutorId, tutorType) {
      this.reset()
      if (tutorType === 'Student') {//如果是大学员教练 
        tutorApi.getStudentTutorInfo(tutorId).then(response => {
          this.tutorInfoVo = response.data
          this.showStudentEdit = true
        })
      } else if (tutorType === 'Job') {
        tutorApi.getJobInfo(tutorId).then(response => {
          this.tutorInfoVo = response.data
          this.showJobEdit = true
        })
      } else if (tutorType === 'Unemployed') {
        tutorApi.getUnemployedInfo(tutorId).then(response => {
          this.tutorInfoVo = response.data
          this.showUnemployedEdit = true
        })
      }
    },
    //删除按钮
    deleteTutor(tutorId) {
      this.$confirm('确定要删除该教练 吗?', '删除教练 ', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        tutorApi.deleteTutorInfo(tutorId).then(res => {
          this.getList()
          this.$message.success('删除成功!')
        }).catch(err => {
        })
      }).catch(err => {
      })
    },
    //编辑教练 
    editStatus(tutorId, status) {
      this.$confirm('确定审核吗?', '审核状态', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let editData = {};
        editData.tutorId = tutorId
        editData.status = status
        tutorApi.editStatus(editData).then(res => {
          this.getList()
          this.$message.success('修改成功!')
        }).catch(err => {
        })
      }).catch(err => {
      })
    },
    reset() {
      this.tutorInfoVo = {}
    },
    cancel() {
      this.showStudentEdit = false
      this.showUnemployedEdit = false
      this.showJobEdit = false
      this.showAddTutor = false
      this.reset()
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val
      this.getList()
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val
      this.getList()
    }
  },
}
</script>

<style>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

@media screen and (max-width: 767px) {
  .recharge-dialog .el-dialog {
    width: 90% !important;
  }

  .el-message-box {
    width: 80% !important;
  }
}
</style>
