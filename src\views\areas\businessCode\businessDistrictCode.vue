<template>
  <div class="app-container">
    <!-- 新增按钮 -->
    
   <el-form :inline="true" class="container-card" label-width="96px" label-position="left">
      <el-row >
       <el-col :span="8" :xs="24">
         <el-form-item label="绑定的账号：">
           <el-input id="loginName" v-model="rangData.loginName" name="id" placeholder="请输入绑定的账号：" clearable/>
         </el-form-item>
       </el-col>
        <el-col :span="8" style="text-align: right;">
        <el-button type="primary" icon="el-icon-search" @click="fetchData()" size="mini">搜索</el-button>
      </el-col>
      <el-col :span="8" style="text-align: right;">
        <el-button type="primary" icon="el-icon-search" v-loading="exportLoading" @click="excel()" size="mini">导出</el-button>
      </el-col>
      </el-row>
   </el-form>
     <!-- <el-row class="app-container">
      <el-col :span="24">
        <el-col :span="4" class="marginbottom" style="line-height: 36px;">请输入绑定的数量</el-col>
        <el-col :span="10" style="padding-left: 10px">
          <el-form :model="signleCodeAddData">
            <el-input id="name" v-model="signleCodeAddData.count" name="id" placeholder="请输入绑定的数量" />
          </el-form>
        </el-col>
        <el-col :span="10" style="text-align: right">
          <el-button type="danger" @click="create(count)" size="mini">生成</el-button>
        </el-col>
      </el-col>
    </el-row> -->
    
    <el-form :inline="true" class="container-card" label-width="96px" label-position="left">
      <el-row :span="24">
       <el-col :span="8" :xs="24">
         <el-form-item label="绑定码的数量">
           <el-input id="statNum" v-model="rangCodeAddData.count" name="id" placeholder="请输入绑定码的数量" clearable/>
         </el-form-item>
       </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="商户的手机号">
            <el-input id="endNum" v-model="rangCodeAddData.loginName" name="id" placeholder="请输入商户的手机号:" clearable/>
          </el-form-item>
        </el-col>
        <el-col :span="8" style="text-align: right;">
        <el-button type="primary"  @click="bindCode()" size="mini">绑定商户</el-button>
      </el-col>
      </el-row>
      </el-form> 
    <el-table v-loading="tableLoading" :data="tableData" border stripe style="width: 100%;margin-bottom: 30px;">
        <el-table-column prop="activeId" label="所属活动id" align="center"  />
         <el-table-column prop="codeType" label="码的类型" align="center"  />
          <el-table-column prop="superiorId" label="绑定商户的id" align="center"  />
         <el-table-column label="是否绑定商户" prop="isBind" align="center">
          <template slot-scope="scope">
            <el-button :type="scope.row.isBind? 'primary':'danger'" size="mini" plain disabled>{{ scope.row.isBind? '已绑定':'未绑定' }}</el-button>
          </template>
        </el-table-column>
        <el-table-column label="是否禁用" prop="isEnable" align="center">
          <template slot-scope="scope">
            <el-button :type="scope.row.isEnable==1? 'primary':'danger'" size="mini" plain disabled>{{ scope.row.isEnable==1? '未禁用':'禁用' }}</el-button>
          </template>
        </el-table-column>
        <!-- <el-table-column label="二维码" prop="codePath" align="center">
          <template slot-scope="scope"><img width="40px" :src="scope.row.codePath"></template>
        </el-table-column> -->
        <el-table-column prop="codeValue" label="二维码的值" align="center"  />
        <el-table-column prop="memberName" label="绑定商户的姓名" align="center" :show-overflow-tooltip="true"/>
        <el-table-column prop="loginName" label="绑定商户的联系方式" align="center" :show-overflow-tooltip="true"/>
        <el-table-column prop="scanningTimes" label="被扫次数" align="center" :show-overflow-tooltip="true"/>
  <el-table-column prop="createTime" label="创建时间" align="center" :show-overflow-tooltip="true"/>
    </el-table>
    <!-- 分页 -->
    <el-col :span="20">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]" layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    </el-col>
  
    

  </div>

</template>

<script>

import rangCode from '@/api/rangCode'
import {
  pageParamNames
} from '@/utils/constants'
// import { number } from 'echarts/lib/export'

export default {
  data() {
    return {
      qrCodeCreate: {
        merchantCode: ''
      },
      signleCodeAddData:{
      count:''
      },
      rangCodeAddData:{
        count:'',
        loginName:''
      }
      ,rangData:{
        loginName:''    },
      id:'',
      singleCodeData:{
        startDate:'',
        endDate:''
      },
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      optionsAddressProps: {
        checkStrictly: true,
        value: 'name',
        label: 'name',
        children: 'children'
      },
      address: '',
      addressArray: [],
      optionsAddress: [],
      showAddressSelect: false,
      addressCreateLoad: false,
      queryMerchantCode: '',
      tableLoading: false,
      tableData: [],
      exportLoading:false,
      dialogRefereeVisible:false,   //生成0元订单弹窗

      dialogCodeVisible: false, // 校区二维码列表弹窗是否显示
      schoolCodeList: []
    }
  },
  created() {
    this.fetchData()
  },
  methods: {
    create(ele) {
      const that = this
      if(that.signleCodeAddData.count==''|| that.signleCodeAddData.count==null || that.signleCodeAddData.count==""){
        that.$message.warning("生成码的数量不能为空");
        return false;
      }
      rangCode.addRangCode(that.signleCodeAddData.count).then(res => {
       that.$message.success("生成成功");
       that.fetchData();
      })
    },
    bindCode(){
      const that = this;
       if(that.rangCodeAddData.count==''|| that.rangCodeAddData.count==null || that.rangCodeAddData.count=="" || that.rangCodeAddData.count==0){
        that.$message.warning("码的数量不能为空或零");
        return false;
      }
      if(that.rangCodeAddData.loginName==''|| that.rangCodeAddData.loginName==null || that.rangCodeAddData.loginName=="" || that.rangCodeAddData.loginName==0){
        that.$message.warning("商户账号不能为空");
        return false;
      }
     rangCode.bindmerchatnCode(that.rangCodeAddData).then(res=>{
       that.$message.success("绑定成功");
       that.fetchData();
     })
    }
    ,
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val
      this.fetchData()
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val
      this.fetchData()
    },
    // 查询
    fetchData() {
        const that = this;
        that.tableLoading = true;
        that.rangCodeAddData.count = '';
        that.signleCodeAddData.count='';
        that.rangCodeAddData.loginName=''
      rangCode.pageList(that.tablePage.currentPage, that.tablePage.size,that.rangData).then(res => {
          console.log(res.data);
        that.tableData = res.data.data
        that.tableLoading = false
        // 设置后台返回的分页参数
        pageParamNames.forEach(name => that.$set(that.tablePage, name, parseInt(res.data[name])))
      })
    },
    excel(){
             const that = this;
        that.exportLoading = true;
        rangCode.excelRangCode(that.rangData).then((response) => {
          console.log(response);
          if (!response) {
            this.$notify.error({
              title: "操作失败",
              message: "文件下载失败",
            });
          }
          const url = window.URL.createObjectURL(response);
          const link = document.createElement("a");
          link.style.display = "none";
          link.href = url; // 获取服务器端的文件名
          link.setAttribute("download", "体验课二维码列表.xls");
          document.body.appendChild(link);
          link.click();
          this.exportLoading = false;
        });
        
    }
    ,
    detail(codeValue,codePath,batchNumber,numberCodeGroups,name,loginName) {
      const that = this
     that.$router.push({
          path: '/businessDistrict/group-detail',
          query :{
           codeValue: codeValue,
           codePath: codePath,
           batchNumber : batchNumber,
           numberCodeGroups : numberCodeGroups,
           name:name,
           loginName:loginName
          }
        });
    }
  }

}

</script>

<style>
  .container-card {
    padding: 15px 15px 15px 15px;
    margin-bottom: 15px ;
    box-shadow: 0 2px 4px rgba(0, 0, 0, .12), 0 0 6px rgba(0, 0, 0, .04)
  }

</style>
