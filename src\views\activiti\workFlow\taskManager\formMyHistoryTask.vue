<template>
  <!-- 历史任务 -->
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="88px">
      <el-form-item label="登录账号">
        <el-input class="filter-item" v-model="queryParams.name" :clearable="true" placeholder="登录账号" />
      </el-form-item>
      <el-form-item label="手机号">
        <el-input class="filter-item" v-model="queryParams.mobile" :clearable="true" placeholder="手机号" />
      </el-form-item>
      <el-form-item label="商户名称">
        <el-input class="filter-item" v-model="queryParams.merchantName" :clearable="true" placeholder="商户名称" />
      </el-form-item>
      <el-form-item label="账号角色">
        <el-input class="filter-item" v-model="queryParams.roleTag" :clearable="true" placeholder="账号角色" />
      </el-form-item>
      <el-form-item>
        <el-button type="success" icon="el-icon-search" size="mini" @click="getPageList1()">查询</el-button>
      </el-form-item>
    </el-form>
    <el-row>
      <el-col :span="24">
        <el-table ref="teacher" :data="tableList" size="mini" header-cell-class-name="table-header-gray">
          <el-table-column label="序号" header-align="center" align="center" type="index" width="55px"
            :index="tableList.getTableIndex" />
          <el-table-column label="流程名称" prop="processDefinitionName" />
          <el-table-column label="操作" width="100px">
            <template slot-scope="scope">
              <el-button class="table-btn success" size="mini" type="text" @click="onFlowDetail(scope.row)">详情</el-button>
            </template>
          </el-table-column>
          <el-table-column label="登录账号" prop="name" />
          <el-table-column label="手机号" prop="mobile" />
          <el-table-column label="商户名称" prop="merchantName" />
          <el-table-column label="账号角色" prop="roleTag" />
          <el-table-column label="任务发起人" prop="startName" />
          <el-table-column label="任务发起时间" prop="startTime" />
          <el-table-column label="任务结束时间" prop="endTime" />
        </el-table>
        <!-- 分页 -->
        <el-col :span="20">
          <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
            layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
            @current-change="handleCurrentChange" />
        </el-col>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import '@/api/activiti/staticDict/flowStaticDict.js';
import { pageParamNames } from "@/utils/constants";
import flowOperationApi from '@/api/activiti/flowOperation';
import { TableWidget } from '@/utils/widget.js';

export default {
  name: 'formMyHistoryTask',
  props: {
  },
  data() {
    return {
      queryParams: {
        taskName: undefined,
        mobile: undefined,
        merchantName: undefined,
        roleTag: undefined,
        name: undefined
      },
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      tableList: [],
    }
  },
  created() {
    this.getPageList();
  },
  methods: {
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val
      this.getPageList()
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val
      this.getPageList()

    },
    getPageList1() {
      this.tablePage = {
        currentPage: 1,
        size: this.tablePage.size,
        totalPage: null,
        totalItems: null
      }
      this.getPageList();
    },
    /**
     * 获取所有流程实例
     */
    getPageList() {
      this.queryParams.pageNum = this.tablePage.currentPage;
      this.queryParams.pageSize = this.tablePage.size;
      flowOperationApi.listHistoricProcessInstance(this.queryParams).then(res => {
        this.tableList = res.data.data;
        this.loading = false;
        // 设置后台返回的分页参数
        // 设置后台返回的分页参数
        this.tablePage.currentPage = res.data.currentPage;
        this.tablePage.size = res.data.size;
        this.tablePage.totalItems = parseInt(res.data.totalItems);
        this.tablePage.totalPage = parseInt(res.data.totalPage);
        //pageParamNames.forEach(name => this.$set(this.tablePage, name, parseInt(res.data[name])))
      });
    },
    loadMyHistoryTaskVerify() {
      this.formFilterCopy.processDefinitionName = this.formFilter.processDefinitionName;
      this.formFilterCopy.createDate = Array.isArray(this.formFilter.createDate) ? [...this.formFilter.createDate] : [];
      return true;
    },
    onFlowDetail(row) {
      let params = {
        processInstanceId: row.processInstanceId
      }

      flowOperationApi.viewInitialHistoricTaskInfo(params).then(res => {
        if (res.data) {
          this.$router.push({
            path: res.data.routerName || '/taskManager/handlerFlowTask',
            query: {
              processDefinitionKey: row.processDefinitionKey,
              taskId: null,
              processInstanceId: row.processInstanceId,
              processDefinitionId: row.processDefinitionId,
              formId: res.data.formId,
              routerName: res.data.routerName,
              readOnly: true,
              flowEntryName: row.processDefinitionName,
              processInstanceInitiator: row.processInstanceInitiator
            }
          });
        }
      }).catch(e => { });
    },
  },
}
</script>

<style></style>
