<template>
  <div class="app-container">
    <!-- 搜索 -->
    <el-form
      :inline="true"
      ref="form"
      class="SearchForm"
      style="padding: 20px 30px 0"
    >
      <el-row>
        <el-col :span="8" :xs="24">
          <el-form-item label="单词:">
            <el-input
              v-model="searchData.word"
              @keyup.enter.native="fetchData()"
              style="width: 200px"
              placeholder="请输入单词"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item label="中文:">
            <el-input
              v-model="searchData.chinese"
              @keyup.enter.native="fetchData()"
              style="width: 200px"
              placeholder="请输入中文"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="单词分类:">
            <el-select
              v-model="searchData.studyType"
              filterable
              value-key="value"
              placeholder="请选择"
              clearable
            >
              <el-option
                v-for="(item, index) in enTypeData"
                :key="index"
                :label="item.label"
                :value="item.code"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item>
            <el-button
              type="warning"
              size="small"
              icon="el-icon-search"
              @click="searchTable()"
              >搜索</el-button
            >
            <el-button size="small" icon="el-icon-refresh" @click="rest()"
              >重置</el-button
            >
          </el-form-item>
        </el-col>
        <!-- <el-col :span="8" :xs="24">
          <el-form-item label="现学阶段:">
            <el-select v-model="searchData.studyStage" filterable value-key="value" placeholder="请选择" clearable>
              <el-option v-for="(item, index) in enStageData" :key="index" :label="item.label" :value="item.code" />
            </el-select>
          </el-form-item>
        </el-col> -->
      </el-row>
    </el-form>

    <div class="SearchForm">
      <!-- 添加 -->
      <div class="btn-add">
        <el-button
          size="small"
          type="primary"
          icon="el-icon-plus"
          @click="dialogVisible = true"
          >添加</el-button
        >
        <el-button
          size="small"
          type="primary"
          icon="el-icon-plus"
          @click="wordSetting"
          >配置设置</el-button
        >
        <!-- <el-button size="small" type="primary" icon="el-icon-upload2" @click="uploadVideo">音频上传</el-button> -->
      </div>
      <!-- 表格 -->
      <el-table
        class="common-table"
        :data="tableData"
        stripe
        border
        :default-sort="{ prop: 'date', order: 'descending' }"
        v-loading="tableLoading"
      >
        <el-table-column
          prop="wordCode"
          label="测试题库编号"
          width="180"
          sortable
        ></el-table-column>
        <el-table-column
          prop="word"
          label="单词"
          width="180"
          sortable
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          prop="chinese"
          label="中文"
          width="180"
          sortable
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column prop="id" label="操作" sortable width="300">
          <template slot-scope="scope">
            <el-button
              type="success"
              size="small"
              icon="el-icon-edit-outline"
              @click="handleUpdate(scope.row.id)"
              >编辑</el-button
            >
            <el-button
              type="danger"
              icon="el-icon-close"
              size="small"
              @click="onDel(scope.row.id)"
              >删除</el-button
            >
          </template>
        </el-table-column>
        <el-table-column prop="studyType" label="单词分类" sortable>
          <template slot-scope="scope">
            <span>{{
              enTypeData.filter((f) => f.code === scope.row.studyType)[0]?.label
            }}</span>
          </template>
        </el-table-column>
        <!-- <el-table-column prop="studyStage" label="现学阶段" sortable>
          <template slot-scope="scope">
            <span>{{  enStageData.filter(f => f.code === scope.row.studyStage)[0]?.label  }}</span>
          </template>
        </el-table-column> -->
        <el-table-column
          prop="createTime"
          label="添加时间"
          width="180"
          sortable
          :show-overflow-tooltip="true"
        ></el-table-column>
        <el-table-column
          prop="updateTime"
          label="最后编辑时间"
          width="180"
          sortable
          :show-overflow-tooltip="true"
        ></el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <el-col :span="20">
      <el-pagination
        :current-page="tablePage.currentPage"
        :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="totalItems"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-col>

    <!-- 新增单词 -->
    <el-dialog
      title="添加单词 "
      :visible.sync="dialogVisible"
      width="70%"
      :close-on-click-modal="false"
      @close="addDialogClose"
    >
      <el-form
        ref="query"
        :rules="rules"
        :model="query"
        label-position="left"
        label-width="120px"
        style="width: 100%"
      >
        <el-form-item label="单词" prop="file">
          <div style="display: flex">
            <!-- :on-success="handleReadWordSuccess" -->
            <div class="upload_box">
              <el-upload
                class="upload-demo"
                action="#"
                :http-request="uploadDetailHttp"
                :show-file-list="true"
                :on-remove="handleRemoveReadWord"
                :on-exceed="handleExceed"
                :before-upload="beforeWordUpload"
              >
                <div class="el-upload__text">
                  <el-link
                    icon="el-icon-upload2"
                    :underline="false"
                    style="color: #1890ff; margin-right: 8px"
                    class="upload_link"
                    >excel文件上传</el-link
                  >
                </div>
              </el-upload>
              <el-link
                class="download_link"
                style="color: #13ce66"
                :underline="false"
                icon="el-icon-download"
                href="https://document.dxznjy.com/applet/zhimi/config/tx_word.xls"
                >模板下载</el-link
              >
            </div>
          </div>
        </el-form-item>
      </el-form>
      <!-- 中英文标识 -->
      <div>
        <el-table :data="enTableData" :rules="enWordRules" border>
          <el-table-column label="英文标识" prop="code"></el-table-column>
          <el-table-column label="中文" prop="label"></el-table-column>
        </el-table>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="addDialogClose">关闭</el-button>
        <el-button size="small" type="primary" @click="addActiveFun"
          >确定</el-button
        >
      </div>
    </el-dialog>
    <!-- 编辑单词 -->
    <el-dialog
      title="编辑单词 "
      :visible.sync="editDialogVisible"
      width="70%"
      :close-on-click-modal="false"
      @close="editDialogClose"
    >
      <el-form
        ref="editForm"
        :rules="editRules"
        :model="editForm"
        label-position="left"
        label-width="120px"
        style="width: 100%"
      >
        <el-form-item label="单词" prop="word">
          <el-col :xs="24" :lg="18">
            <el-input
              style="width: 202px"
              v-model="editForm.word"
              placeholder="请输入单词"
            ></el-input>
          </el-col>
        </el-form-item>
        <el-form-item label="单词类型" prop="studyType">
          <el-col :xs="24" :lg="18">
            <el-select v-model="editForm.studyType" placeholder="请选择">
              <el-option
                v-for="item in enTypeData"
                :label="item.label"
                :value="item.code"
              ></el-option>
            </el-select>
          </el-col>
        </el-form-item>
        <el-form-item label="中文" prop="studyType">
          <el-col :xs="24" :lg="18">
            <el-input
              v-model="editForm.chinese"
              style="width: 202px"
              placeholder="请输入中文"
              clearable
            ></el-input>
          </el-col>
        </el-form-item>
        <!-- <el-form-item label="学习阶段" prop="studyStage">
          <el-col :xs="24" :lg="18">
            <el-select v-model="editForm.studyStage" placeholder="请选择">
                <el-option v-for="item in enStageData" :label="item.label" :value="item.code"></el-option>
            </el-select>
          </el-col>
        </el-form-item> -->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="editDialogClose">关闭</el-button>
        <el-button size="small" type="primary" @click="submitEdit"
          >确定</el-button
        >
      </div>
    </el-dialog>

    <!-- 听写能力测试配置 -->
    <el-dialog
      :title="'听写能力测试配置'"
      :visible.sync="dialogWordTest"
      width="70%"
      :close-on-click-modal="false"
      @close="closeWordReplace"
    >
      <el-form
        ref="form"
        :rules="formRules"
        :model="form"
        label-position="left"
        label-width="160px"
        style="width: 100%"
      >
        <el-form-item label="测试倒计时是否开启" prop="isEnable">
          <template>
            <span class="marginRight">关</span>
            <el-switch
              v-model="form.isEnable"
              inactive-color="#ff4949"
            ></el-switch>
            <span class="marginLeft">开</span>
          </template>
        </el-form-item>
        <el-form-item label="" v-if="!form.isEnable"
          ><span class="countdown"
            >提示：关闭计时学员需要做完所有的词库单词才能结束一轮测评</span
          ></el-form-item
        >
        <!-- <el-form-item label="课程类型">
          <el-select v-model="form.courseType" placeholder="请选择课程类型">
                <el-option v-for="item in courseTypeList" :label="item.label" :value="item.value"></el-option>
            </el-select>
        </el-form-item> -->
        <el-form-item label="测试总时长" prop="duration" v-if="form.isEnable">
          <template>
            <el-row>
              <el-col :xs="24" :span="4">
                <el-input
                  v-model="form.duration"
                  placeholder="请输入时间"
                ></el-input>
              </el-col>
              <span class="marginLeft">分钟</span>
            </el-row>
          </template>
        </el-form-item>
        <el-form-item
          label="测试读时长"
          prop="readDuration"
          v-if="form.isEnable"
        >
          <template>
            <el-row>
              <el-col :xs="24" :span="4">
                <el-input
                  v-model="form.readDuration"
                  placeholder="请输入时间"
                ></el-input>
              </el-col>
              <span class="marginLeft">分钟</span>
            </el-row>
          </template>
        </el-form-item>
        <el-form-item label="标准评分" prop="suggestedScore">
          <el-row>
            <el-col :xs="24" :span="4">
              <el-input
                v-model="form.suggestedScore"
                placeholder="请输入标准评分"
              ></el-input>
            </el-col>
            <el-dialog :visible.sync="visible" width="30%" append-to-body>
              <span>
                <div class="wordSuggestedScore">
                  <el-row class="wordSuggestedScore_row">
                    <el-col :span="4">单词:</el-col>
                    <el-col :span="12">
                      <el-input
                        v-model="mobileWord"
                        placeholder="请输入"
                      ></el-input>
                    </el-col>
                  </el-row>
                  <el-row class="wordSuggestedScore_row" style="margin: 16px">
                    <el-col :span="4">音频:</el-col>
                    <!-- :on-remove="wordHandleRemove" -->
                    <el-col :span="12">
                      <!-- :http-request="wordUploadHttp" -->
                      <el-upload
                        multiple
                        v-loading="wordUploadLoading"
                        ref="uploadVideo"
                        class="upload-demo"
                        :show-file-list="true"
                        :file-list="wordAudioFileList"
                        action=""
                        :http-request="wordHandleSuccess"
                        :before-upload="wordBeforeUpload"
                        :limit="1"
                      >
                        <!-- :on-success="wordHandleSuccess" -->
                        <div class="el-upload__text">
                          <el-button
                            size="small"
                            type="primary"
                            :disabled="mobileWord ? false : true"
                            ><i class="el-icon-microphone"></i
                            >点击上传</el-button
                          >
                        </div>
                      </el-upload>
                    </el-col>
                  </el-row>
                  <!-- <div style="display: flex;justify-content: center;align-items: center;">
                  <span style="width: 60px;"></span>
                </div>
                <div style="display: flex;justify-content: center;align-items: center;">
                  <span style="width: 60px;">音频:</span>
                
                </div> -->
                  <div>标准评分：{{ form.suggestedScore }}</div>
                  <div style="color: #1890ff; margin-top: 16px">
                    你的评分: {{ suggestedScore }}
                  </div>
                </div>
                <div style="text-align: right; margin: 0">
                  <el-button size="mini" @click="visibleClose()"
                    >取消</el-button
                  >
                  <el-button type="primary" size="mini" @click="visibleClose()"
                    >确定</el-button
                  >
                </div>
              </span>
              <!-- <span slot="footer">
                <el-button @click="visible = false">取消</el-button>
                <el-button type="primary" @click="visible = false"
                  >确定</el-button
                >
              </span> -->
            </el-dialog>

            <!-- <el-popover
              placement="bottom"
              v-if="dialogWordTest"
              width="360"
              v-model="visible"
                slot="reference"
            > -->
            <el-button
              v-if="form.suggestedScore"
              size="small"
              @click="playAudio"
              type="primary"
              style="margin-left: 8px"
              >点击试听</el-button
            >
            <!-- </el-popover> -->
          </el-row>
        </el-form-item>
        <el-form-item label="单词数量" prop="wordNumber" v-if="form.isEnable">
          <el-row>
            <el-col :xs="24" :span="4">
              <!-- <el-input v-model="form.wordNumber" :min="1" placeholder="请输入单词数量"></el-input> -->
              <el-input-number
                v-model="form.wordNumber"
                :min="1"
                label="请输入单词数量"
              ></el-input-number>
            </el-col>
          </el-row>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="closeWordReplace">关闭</el-button>
        <el-button size="small" type="primary" @click="addSetCourse"
          >确定</el-button
        >
      </div>
    </el-dialog>
    <el-dialog
      title="音频上传"
      :visible.sync="uploadDialog"
      width="70%"
      :close-on-click-modal="false"
      @close="uploadDialogClose"
    >
      <el-table
        :data="uploadData"
        style="width: 80%"
        class="common-table"
        stripe
        border
        :default-sort="{ prop: 'date', order: 'descending' }"
        v-loading="uploadTableLoading"
        :span-method="videoSpanMethod"
      >
        <el-table-column label="单词" prop="word"></el-table-column>
        <el-table-column label="上传状态" prop="status">
          <template slot-scope="scope">
            <i
              v-if="scope.row.status === 1"
              class="el-icon-circle-check"
              style="color: #13ce66; font-size: 16px"
            ></i>
            <!-- <i v-else class="el-icon-circle-close" style="color: #ff4949;font-size: 16px;"></i> -->
          </template>
        </el-table-column>
        <el-table-column prop="id" label="批量上传" sortable width="300">
          <template slot-scope="scope">
            <!--  :http-request="videoUploadFile" :on-success="handleWordVideoSuccess" :on-exceed="handleVideoExceed" :on-preview="handlePreview"  :auto-upload="false"  -->
            <!-- :http-request="videoUploadFile" -->
            <el-upload
              multiple
              ref="uploadVideo"
              class="upload-demo"
              :show-file-list="true"
              :file-list="audioFileList"
              action="https://jsonplaceholder.typicode.com/posts/"
              :on-success="handleWordVideoSuccess"
              :on-remove="handleRemoveVideoWord"
              :on-change="videoHandleUpload"
              :before-upload="beforeWordVideoUpload"
            >
              <div class="el-upload__text">
                <el-button size="small" type="primary"
                  ><i class="el-icon-plus"></i>上传</el-button
                >
              </div>
            </el-upload>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>
<script>
import dictationWordTestListApi from "@/api/dictationWordTestList";
import enTypes from "@/api/bstatus";
import { mapGetters } from "vuex";
import UploadFile from "@/components/Upload/UploadFile.vue";
import { ossPrClient } from "@/api/alibaba";
export default {
  name: "courseList",
  components: { UploadFile },
  data() {
    return {
      totalItems: 0,
      courseTypeList: [{ value: -1, label: "听写检测" }],
      enTypeData: [
        { label: "单音节", code: "DYJ1", type: "PD_WORD_TYPE" },
        { label: "多音节", code: "DYJ2", type: "PD_WORD_TYPE" },
        { label: "前后缀", code: "QHZ", type: "PD_WORD_TYPE" },
        // {label:'变形',code:'BX',type:'PD_WORD_TYPE'},
      ],
      enStageData: [
        // {label:'国外1-2年级',code:'OT12',type:'PD_WORD_STAGE'},
        // {label:'国外3-4年级',code:'OT34',type:'PD_WORD_STAGE'},
        // {label:'国外4年级',code:'OT4',type:'PD_WORD_STAGE'},
      ],
      enWordRules: {
        word: [{ required: true, message: "请上传单词", trigger: "blur" }],
      },
      enTableData: [],
      //搜索栏
      searchData: {
        studyType: "", //学习类型
        // studyStage:'',//阶段
        word: "", //单词
        chinese: "", //中文
      },
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
      },
      tableLoading: false,
      uploadTableLoading: false,
      roleName: "",
      role: "",
      //新增
      query: {
        file: "",
      },
      //编辑
      editForm: {
        id: "",
        word: "",
        studyType: "",
        chinese: "", //中文
        //  studyStage:'',
      },
      editRules: {
        word: [{ required: true, message: "请输入单词", trigger: "blur" }],
        wordType: [
          { required: true, message: "请选择单词分类", trigger: "blur" },
        ],
        studyType: [
          { required: true, message: "请选择现学阶段", trigger: "blur" },
        ],
      },
      editDialogVisible: false,
      tableData: [], //表格数据
      dialogVisible: false, // 修改弹窗是否展示
      dialogWordTest: false,
      uploadDialog: false, //上传弹窗
      uploadData: [
        { word: "red", status: 0, id: "0000000" },
        { word: "blue", status: 0, id: "1111111" },
        { word: "white", status: 0, id: "2222222" },
      ], //上传弹窗单词
      //配置
      formRules: {
        isEnable: [{ required: true, message: "必填", trigger: "blur" }],
        duration: [{ required: true, message: "必填", trigger: "blur" }],
        readDuration: [{ required: true, message: "必填", trigger: "blur" }],
        suggestedScore: [{ required: true, message: "必填", trigger: "blur" }],
        wordNumber: [{ required: true, message: "必填", trigger: "blur" }],
      },
      form: {
        isEnable: true, //倒计时开启
        courseType: "-1", //课程类型
        duration: "", //总时长
        readDuration: "", //读时长
        suggestedScore: "", //评分
        wordNumber: "", //数量
      },
      mobileWord: "", //测试单词
      suggestedScore: "", //音频测试评分
      visible: false,
      //新增单词
      rules: {
        // 表单提交规则
        studyType: [
          { required: true, message: "请选择现学阶段", trigger: "blur" },
        ],
        writeFile: [
          { required: true, message: "请上传拼写文件", trigger: "blur" },
        ],
        readFile: [
          { required: true, message: "请上传发音文件", trigger: "blur" },
        ],
      },
      //编辑详情
      editFormDetail: {},
      //单词分类
      categoryType: [
        { value: 2, label: "发音单词" },
        { value: 3, label: "拼写单词" },
      ], //单词分类
      courseStageType: [], //课程学段类型
      audioFileList: [], //已上传音频列表
      wordUploadLoading: false,
      wordAudioFileList: [],
    };
  },
  computed: {
    ...mapGetters(["roles"]),
  },
  created() {
    //获取列表
    this.fetchData();
    //获取学段下拉框
    this.getStady();
    ossPrClient();
    var jsonData = JSON.stringify(this.roles);
    var s = JSON.stringify(this.roles).includes("admin");
    var obj = eval("(" + jsonData + ")");
    this.roleName = s;
    console.log(this.roleName);
    this.role = obj[0].val;
    console.log(obj[0].val);
  },
  methods: {
    visibleClose() {
      this.visible = false;
      this.mobileWord = ""; //测试单词
      this.suggestedScore = ""; //音频测试评分
    },
    isAudio(file) {
      return /\.(mp3|wav|mid|wma|ra|vqf|mov|amr)$/.test(file.name);
    },
    wordBeforeUpload(file) {
      // const isFileName = this.vowelConsonantList.filter(f => f.wordSyllable === file.name.split('.')[0]).length > 0
      const isFileName = this.mobileWord === file.name.split(".")[0];
      if (!this.isAudio(file)) {
        this.$message.warning("只能上传音频文件！");
        return false;
      } else if (!isFileName) {
        this.$message.warning("只能上传该单词文件！");
        return false;
      }
    },
    wordHandleRemove(file, fileList) {
      // if(file && file.name){
      //     this.vowelConsonantList.forEach(f => {
      //         if(f.wordSyllable === file.name.split('.')[0]){
      //         f.status = '';
      //         f.wordSyllableAudioUrl = ''
      //         }
      //     })
      // }
      this.vowelUploadLoading = false;
    },
    wordHandleSuccess({ file }) {
      console.log(file);
      const formData = new FormData();
      formData.append("file", file);
      formData.append("word", this.mobileWord);
      console.log(formData, "formDataformDataformData");
      dictationWordTestListApi.wordRecognition(formData).then((res) => {
        if (res.code === 20000) {
          this.suggestedScore = res.data.data.suggestedScore;
          console.log(res.data.data, "11212");
        }
      });
      console.log(file, "上传成功");

      // this.vowelAudioStatus(fileList,1)
      // this.audioFileList = fileList
    },
    wordUploadHttp({ file }) {
      // this.vowelUploadLoading = true
      const fileName = `manage/${Date.parse(new Date())}.${
        file.name.split(".")[1]
      }`;
      console.log(fileName, "fileName");
      this.$nextTick(function () {
        if (ossPrClient() && ossPrClient().put) {
          ossPrClient()
            .put(fileName, file)
            .then(({ res, url, name }) => {
              if (res && res.status === 200) {
                console.log(`阿里云OSS上传图片成功回调1`, res, url, name);
                console.log(url, "url099999999");

                // this.videoForm.summaryUrl = url
                // this.vowelAudioStatus(file,url)
                this.wordUploadLoading = false;
                this.$nextTick(() => {
                  this.wordUploadLoading = false;
                });
              }
            })
            .catch((err) => {
              this.wordUploadLoading = false;
              //  that.$message.error('上传图片失败请检查网络或者刷新页面')
              // this.summaryLoading = false
              console.log(`阿里云OSS上传图片失败回调`, err);
            });
        }
      });
    },
    // 点击试听
    playAudio() {
      this.visible = true;
    },
    //删除
    onDel(id) {
      this.$confirm("确定操作吗?", "删除单词", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          const formData = new FormData();
          formData.append("id", id);
          dictationWordTestListApi.deleteWordById(formData).then((res) => {
            if (res.code === 20000) {
              this.$message.success("删除成功");
              if (
                this.tableData.length <= 1 &&
                this.tablePage.currentPage > 1
              ) {
                this.tablePage.currentPage--;
              }
              this.fetchData();
            }
          });
        })
        .catch((err) => {});
    },
    isExcel(file) {
      return /\.(xlsx|xls)$/.test(file.name);
    },

    // 拼读移除
    handleRemoveReadWord(file, fileList) {
      this.readFild = {};
    },
    //新增 发音上传
    //上传元辅音文件解析
    uploadDetailHttp({ file }) {
      const that = this;
      // this.wordFile =`https://document.dxznjy.com/applet/zhimi/usercode_${file.uid}.xls`;

      const fileType = file.type.substring(file.type.lastIndexOf("/") + 1);
      const fileName = `manage/${Date.parse(new Date())}.${fileType}`;
      that.$nextTick(function () {
        ossPrClient()
          .put(fileName, file)
          .then(({ res, url, name }) => {
            if (res && res.status === 200) {
              console.log(`阿里云OSS上传图片成功回调1`, file, res, url, name);
              this.query.file = file;

              that.$nextTick(() => {});
            }
          })
          .catch((err) => {
            that.$message.error("上传图片失败请检查网络或者刷新页面");
            console.log(`阿里云OSS上传图片失败回调`, err);
          });
      });
    },
    handleReadWordSuccess(response, file, fileList) {
      if (file && file.raw) {
        this.query.file = file.raw;
      }
    },
    handleRemoveWriteWord(file, fileList) {
      this.WriteFile = {};
    },
    //新增 拼写上传
    // handleWriteWordSuccess(response, file, fileList){
    //   this.query.writeFile = file.raw
    //   // if(file && file.raw && file.uid){
    //   //   this.query.writeFile =`https://document.dxznjy.com/applet/zhimi/usercode_${Date.parse(new Date())}.xls`
    //   // }
    // },
    //上传音频列表批量上传操作合并
    videoSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 2) {
        if (rowIndex % this.uploadData.length === 0) {
          return {
            rowspan: this.uploadData.length,
            colspan: 1,
          };
        } else {
          return {
            rowspan: 0,
            colspan: 0,
          };
        }
      }
    },

    //音频上传,移除状态显示
    audioStatus(file, status) {
      if (Array.isArray(file)) {
        if (status === 1) {
          this.uploadData.forEach((res) => {
            file.forEach((f) => {
              if (res.word === f.name.split(".")[0]) {
                console.log(res.word, "word");
                res.status = status;
              }
            });
          });
        }
      } else if (file.name) {
        console.log(file, "");
        this.uploadData.forEach((f) => {
          if (f.word === file.name.split(".")[0]) {
            f.status = status;
          }
        });
        // this.uploadData.filter(res => res.word === fileList.name.split('.')[0])?.status = status
      }
    },
    //音频移除
    handleRemoveVideoWord(file, fileList) {
      this.audioStatus(file, 0);
      this.audioFileList.filter((f) =>
        f.name === file.name
          ? this.audioFileList.splice(this.audioFileList.indexOf(f), 1)
          : this.audioFileList
      );
    },
    //音频上传成功
    handleWordVideoSuccess(response, file, fileList) {
      this.audioStatus(fileList, 1);
      this.audioFileList = fileList;
    },
    //音频文件限制
    // handleVideoExceed(files,fileList){
    //   console.log(files,fileList,'音频文件限制');
    // },
    videoHandleUpload(file, fileList) {
      // if(Array.isArray(fileList) ){
      //     this.audioFileList.forEach(res => {
      //       fileList.forEach(f => {
      //         if(res.name === f.name){
      //           console.log(f,'ffffff');
      //          this.audioFileList = this.audioFileList.splice(this.audioFileList.indexOf(res),1)
      //         }
      //       })
      //     })
      //  }
    },
    //上传到服务器
    videoUploadFile(param) {
      console.log(param, "上传到服务器");
    },
    isAudio(file) {
      return /\.(mp3|wav|mid|wma|ra|vqf|mov|amr)$/.test(file.name);
    },
    //音频上传
    beforeWordVideoUpload(file) {
      const isFileName =
        this.uploadData.filter((f) => f.word === file.name.split(".")[0])
          .length > 0;
      if (!this.isAudio(file)) {
        this.$message.error("只能上传音频文件！");
        this.wordUploadLoading = false;
        return false;
      } else if (!isFileName) {
        this.$message.error("只能上传检测库内单词文件！");
        this.wordUploadLoading = false;
        return false;
      }
    },
    //拼读上传
    beforeWordUpload(file) {
      if (!this.isExcel(file)) {
        this.$message.error("只能上传excel文件！");
        return false;
      }
    },
    //限制文件
    handleExceed(files, fileList) {
      this.$message.warning(
        `当前限制选择 1个文件，本次选择了 ${files.length} 个文件，共选择了 ${
          files.length + fileList.length
        } 个文件`
      );
    },
    //重置
    rest() {
      for (let searchVal in this.searchData) {
        this.searchData[searchVal] = "";
      }
      this.tablePage.currentPage = 1;
      this.fetchData();
    },
    // 查询+搜索课程列表
    searchTable() {
      this.tablePage.currentPage = 1;
      this.fetchData();
    },
    //列表查询
    fetchData() {
      this.tableLoading = true;
      const data = { ...this.searchData, ...this.tablePage };
      console.log(data, "data9888888");
      dictationWordTestListApi.pageConfigWord(data).then((res) => {
        this.tableData = res.data.data;
        this.enTableData = this.enTypeData.concat(this.enStageData);
        this.totalItems = Number(res.data.totalItems);
        this.tableLoading = false;
      });
    },
    //局部刷新
    reload() {
      this.isRouterAlive = false;
      this.$nextTick(function () {
        this.isRouterAlive = true;
      });
    },
    //音频上传
    uploadVideo() {
      this.uploadDialog = true;
    },
    //配置 获取配置详情
    wordSetting() {
      this.dialogWordTest = true;
      dictationWordTestListApi.pdCourseConfigGetInfo().then((res) => {
        if (res.code === 20000 && res.data) {
          const formData = res.data;
          const isEnable = res.data.isEnable === 1 ? true : false;
          this.form = { ...formData, isEnable };
          delete this.form.writeDuration;
        }
      });
    },
    //新增/修改配置
    addSetCourse() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          const form = {
            ...this.form,
            isEnable: this.form.isEnable ? "1" : "0",
          };
          console.log("form", form);
          form.wordNumber = form.isEnable == "1" ? form.wordNumber : "";
          if (!form.id) {
            dictationWordTestListApi.pdCourseConfigSave(form).then((res) => {
              if ((res.code = 20000)) {
                console.log(res, "res");
                this.dialogWordTest = false;
                this.$message.success("新增成功");
                this.fetchData();
              }
            });
          } else {
            dictationWordTestListApi.pdCourseConfigUpdate(form).then((res) => {
              if ((res.code = 20000)) {
                this.dialogWordTest = false;
                this.$message.success("修改成功");
                this.fetchData();
              }
            });
          }
        }
      });
    },
    // 新增课程提交
    addActiveFun() {
      this.$refs["query"].validate((valid) => {
        console.log(this.query, "query123");
        if (valid) {
          let formData = new FormData();
          formData.append("file", this.query.file);
          dictationWordTestListApi.saveBatchWord(formData).then((res) => {
            console.log(res, "res123");
            this.dialogVisible = false;
            this.$message.success("新增成功");
            this.fetchData();
          });
        }
      });
    },
    //编辑单词
    submitEdit() {
      this.$refs["editForm"].validate((valid) => {
        if (valid) {
          dictationWordTestListApi.updateWord(this.editForm).then((res) => {
            if (res.code === 20000) {
              this.editDialogVisible = false;
              this.$message.success("修改成功");
              this.fetchData();
            }
            console.log(res, "res0909099");
          });
        }
      });
    },
    // 点击编辑按钮
    handleUpdate(id) {
      this.editDialogVisible = true;
      const formData = new FormData();
      formData.append("id", id);
      //获取详情
      dictationWordTestListApi.getConfigWordById(formData).then((res) => {
        if (res.code === 20000) {
          const { id, word, studyType, chinese } = res.data.data;
          this.editForm = { ...this.editForm, id, word, studyType, chinese };
        }
      });
    },
    //获取学段下拉框
    getStady() {
      var enType = "CourseStage";
      enTypes.getEnumerationAggregation(enType).then((res) => {
        this.courseStageType = res.data;
      });
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val;
      this.fetchData();
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val;
      this.fetchData();
    },
    // 新增弹窗
    addDialogClose() {
      this.dialogVisible = false;
      this.$refs["query"].resetFields();
    },
    //
    // 编辑单词弹窗
    editDialogClose() {
      this.editDialogVisible = false;
      this.$refs["editForm"].resetFields();
    },
    reset() {
      this.wordReplaceData = {
        word: null,
      };
      if (this.$refs["form"]) {
        this.$refs["form"].resetFields();
      }
    },
    uploadDialogClose() {
      this.uploadDialog = false;
      this.$refs.uploadVideo.clearFiles();
    },
    // 关闭弹窗
    closeWordReplace() {
      this.dialogWordTest = false;
      this.reset();
    },
  },
};
</script>

<style lang="less" scope="scope">
// .upload-demo{
//   margin: 8px;
// }
.wordSuggestedScore {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.wordSuggestedScore_row {
  width: 100%;
  display: flex;
  align-items: center;
}
.upload_link {
  color: #1890ff;
}

.download_link {
  // height: 32px;
  // margin: 0;
  // padding: 0 15px;
  color: #13ce66;
}

.lh36 {
  line-height: 36px;
  font-size: 14px;
}
.marginRight {
  margin-right: 8px;
}
.marginLeft {
  margin-left: 8px;
}
.download {
  padding: 0 15px !important;
  margin: 0 !important;
  color: white !important;
}
.SearchForm {
  width: 100%;
  margin-bottom: 15px;
  background: #fff;
  border-radius: 6px;
  padding: 5px 0;
  padding-left: 5px;
  box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.2);
}

.btn-add {
  padding: 5px;
}
.red {
  color: red;
}
.green {
  color: green;
}

@media (max-width: 767px) {
  .el-message-box {
    width: 80% !important;
  }
}

/* ::v-deep .el-icon-circle-close{
  color:red !important;
} */
.coverimg {
  text-align: center !important;
  padding: 50px;
}
.imgDialog {
  .el-dialog {
    width: 30%;
  }

  .el-dialog__body {
    text-align: center !important;
  }
}
</style>
