<template>
  <el-form :model="dataQuery" :ref="queryRef" :inline="true">
    <el-form-item label="视频ID：" prop="id">
      <el-input
        v-model="dataQuery.id"
        type="number"
        @keydown.native="handleNumberInputE"
        @change="handleChangeNumber"
        clearable
        placeholder="请输入视频ID"
        class="id-input"
      ></el-input>
    </el-form-item>
    <el-form-item label="视频名称：" prop="name">
      <el-input v-model="dataQuery.videoName" clearable placeholder="请输入视频名称" v-trim></el-input>
    </el-form-item>
    <el-form-item label="学段/学科：" prop="grade">
      <el-cascader v-model="dataQuery.grade" :options="gradeList" :props="{ checkStrictly: true, expandTrigger: 'hover' }" @change="handleGradeQueryChange"></el-cascader>
    </el-form-item>
    <el-form-item label="版本：" prop="versionId">
      <el-select v-model="dataQuery.versionId" clearable>
        <el-option v-for="(item, index) in myVersionList" :key="index" :label="item.label" :value="item.value"></el-option>
      </el-select>
    </el-form-item>
    <el-form-item>
      <el-button type="primary" @click="queryData">查询</el-button>
      <el-button type="primary" @click="resetQuery">重置</el-button>
    </el-form-item>
  </el-form>
</template>

<script>
  import courseApi from '@/api/studyExamPassed/course';
  export default {
    name: 'CourseVideoConfigQueryItem',
    props: {
      queryRef: {
        type: String,
        default: 'queryForm'
      },
      dataQuery: {
        type: Object,
        default: () => {
          return {
            id: '',
            videoName: '',
            grade: [],
            versionId: ''
          };
        }
      },
      gradeList: {
        type: Array,
        default: () => {
          return [];
        }
      },
      versionList: {
        type: Array,
        default: () => {
          return [];
        }
      }
    },
    data() {
      return {
        myVersionList: []
      };
    },
    watch: {
      versionList(val) {
        this.myVersionList = val;
      }
    },
    mounted() {},
    methods: {
      handleGradeQueryChange(grade) {
        courseApi.getVersionList(this.dataQuery.curriculumId, grade[0], grade[1]).then((res) => {
          this.myVersionList = res.data;
        });
        this.myVersionList = [];
        this.dataQuery.versionId = '';
      },
      queryData() {
        this.$emit('queryData', this.dataQuery);
      },
      resetQuery() {
        this.myVersionList = this.versionList;
        this.$emit('resetQuery');
      },
      handleNumberInputE(event) {
        if (event.key == 'e' || event.key == 'E') {
          event.returnValue = false;
          return false;
        }
        return true;
      },

      // id输入框输入数字时，限制小于20位，且不允许输入非数字
      handleChangeNumber() {
        if (this.dataQuery.id) {
          let newValue = '';
          if (this.dataQuery.id.length > 19) {
            newValue = this.dataQuery.id.substring(0, 19);
          } else if (/[^\d]/g.test(this.dataQuery.id)) {
            newValue = this.dataQuery.id.replaceAll(/[^\d]/g, '');
          }
          newValue && this.$set(this.dataQuery, 'id', newValue);
        }
      }
    }
  };
</script>

<style lang="less" scoped>
  /* 取消[type='number']的input的上下箭头 */
  /deep/.id-input input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
  }

  /deep/.id-input input::-webkit-outer-spin-button {
    -webkit-appearance: none !important;
  }

  /deep/.id-input input[type='number'] {
    -moz-appearance: textfield;
    appearance: none;
  }
</style>
