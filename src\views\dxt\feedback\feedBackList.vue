<template>
  <div class="app-container">
    <el-form :inline="true" class="container-card" label-width="110px" label-position="left">
      <el-form-item label="会议编号：">
        <el-input v-model="dataQuery.meetingId" placeholder="请输入会议编号：" clearable />
      </el-form-item>
      <el-form-item style="text-align: right">
        <el-button type="primary" icon="el-icon-search" size="mini" @click="fetchData()">搜索</el-button>
      </el-form-item>
    </el-form>
    <!-- 新增按钮 -->

    <el-table v-loading="tableLoading" stripe :data="tableData" border style="width: 100%;margin-bottom: 30px;">
      <el-table-column prop="id" label="反馈意见编号" align="center" />
      <el-table-column prop="meetingId" label="会议编号" align="center" :show-overflow-tooltip="true" />
      <el-table-column prop="memberId" label="人员编号" align="center" :show-overflow-tooltip="true" />
      <el-table-column prop="name" label="人员姓名" align="center" :show-overflow-tooltip="true" />
      <el-table-column prop="phone" label="人员手机号" align="center" :show-overflow-tooltip="true" />
      <el-table-column prop="feedbackContent" label="反馈意见" align="center">
        <template slot-scope="scope">
          <el-button type="primary" size="mini" @click="showDetail(scope.row.feedbackContent)">查看</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间" align="center" :show-overflow-tooltip="true" />
    </el-table>
    <!-- 分页 -->
    <el-col :span="20">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>
    <el-dialog :visible.sync="detailDialogVisible" :close-on-click-modal="false" title="反馈意见">
      <div class="goods-detail-box" v-html="goodsDetail" />
    </el-dialog>
  </div>
</template>

<script>

import Tinymce from '@/components/Tinymce'
import { ossPrClient } from '@/api/alibaba'

import meetingApi from '@/api/dxt/meetingApi'

import feedbackApi from '@/api/dxt/feedback'

import { pageParamNames } from '@/utils/constants'

export default {
  components: {
    Tinymce
  },
  data() {
    return {
      pickerOptions: {
        shortcuts: [{
          text: '今天',
          onClick(picker) {
            picker.$emit('pick', new Date());
          }
        }, {
          text: '昨天',
          onClick(picker) {
            const date = new Date();
            date.setTime(date.getTime() - 3600 * 1000 * 24);
            picker.$emit('pick', date);
          }
        }, {
          text: '一周前',
          onClick(picker) {
            const date = new Date();
            date.setTime(date.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', date);
          }
        }]
      },
      time: '',
      fileDetailList01: [],
      detailDialogVisible: false,//详情弹框
      goodsDetail: '',
      tableLoading: false,
      dataQuery: {
        meetingId: '',
      },
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      meetingNameList: [],
      tableData: [],
      productList: [], // 商品下拉列表数据
      dialogVisible: false, // 修改弹窗是否展示
      addOrUpdate: true, // 是新增还是修改
      activeName: 'first', // tab默认第一个
      addMeetingInvitation: {}, // 新增主讲邀请语
      updateMeetingInvitation: {}, // 修改数据
      rules: {
        // 表单提交规则
        meetingId: [
          {
            required: true,
            message: '必填',
            trigger: 'blur'
          }
        ],
        contetn: [
          {
            required: true,
            message: '必填',
            trigger: 'blur'
          }
        ]
      },
      fileListPending: [], // 待处理已上传图片信息
      fileList: [], // 上传图片已有图片列表
      fileDetailListPending: [], // 待处理已上传图片信息
      fileDetailList: [], // 上传图片已有图片列表
      dialogUploadVisible: false,
      dialogImageUrl: '', // 上传图片预览
      uploadLoading01: false,
      uploadLoading: false, // 上传图片加载按钮
      fullscreenLoading: false, // 保存啥的加载

      content: '',
      isUploadSuccess: true, // 是否上传成功
      isShowRelevance: true // 新增或修改是否展示关联产品
    }
  },
  created() {
    ossPrClient()
    this.fetchData()

  },
  methods: {
    // 获取UUid
    getUUid: function () {
      const s = []
      const hexDigits = '0123456789abcdef'
      for (let i = 0; i < 36; i++) {
        s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1)
      }
      s[14] = '4'
      s[19] = hexDigits.substr((s[19] & 0x3) | 0x8, 1)
      s[8] = s[13] = s[18] = s[23] = '-'
      const UUid = s.join('')
      return UUid
    },
    //获取字段列表
    getMeetingName() {
      meetingApi.getMeetingName().then((res) => {
        console.log(res)
        this.meetingNameList = res.data;
      })
    },
    //查看内容详情
    showDetail(content) {
      this.goodsDetail = content;
      this.detailDialogVisible = true;
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val
      this.fetchData()
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val
      this.fetchData()
    },
    // 查询
    fetchData() {
      const that = this
      that.tableLoading = true
      feedbackApi
        .feedbackList(
          that.tablePage.currentPage,
          that.tablePage.size,
          that.dataQuery
        )
        .then((res) => {
          that.tableData = res.data.data
          that.tableLoading = false
          // 设置后台返回的分页参数
          pageParamNames.forEach((name) =>
            that.$set(that.tablePage, name, parseInt(res.data[name]))
          )
        })
    },

    // 点击新增按钮


    p(s) {
      return s < 10 ? '0' + s : s
    },
    //



    // base64转blob
    toBlob(urlData, fileType) {
      const bytes = window.atob(urlData)
      let n = bytes.length
      const u8arr = new Uint8Array(n)
      while (n--) {
        u8arr[n] = bytes.charCodeAt(n)
      }
      return new Blob([u8arr], {
        type: fileType
      })
    },


    // 关闭弹窗
    close() {
      this.dialogVisible = false
    }
  }
}
</script>
<style>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.goods-detail-box img {
  max-width: 20%;
  max-height: 50%;
}
</style>

