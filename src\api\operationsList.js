/**
 * 托管中心列表相关接口
 */
import request from '@/utils/request';

export default {
  //托管中心强制通过审批
  coerceSuccess(id) {
    return request({
      url: '/znyy/operations/center/coerceSuccess/' + id,
      method: 'POST'
    });
  },

  // 托管中心流程图开启
  startAndTakeUserTaskByAddDealer(data) {
    return request({
      url: '/activiti/flowOnlineOperation/startAndTakeUserTask/addOperations',
      method: 'POST',
      data
    });
  },
  // 托管中心新增
  addDealerList(data, confirm) {
    return request({
      url: '/znyy/operations/center',
      method: 'POST',
      params: { confirm: confirm },
      data: data
    });
  },
  // 托管中心新增
  addDealerListV2(data, confirm) {
    return request({
      url: '/znyy/operations/v2/save',
      method: 'POST',
      params: { confirm: confirm },
      data: data
    });
  },
  //获取托管中心级别
  getSelectResult() {
    return request({
      url: '/znyy/operations/center/rank',
      method: 'GET'
    });
  },
  // 托管中心级别分页查询
  dealerList(pageNum, pageSize, data) {
    return request({
      url: '/znyy/operations/center/list/' + pageNum + '/' + pageSize,
      method: 'GET',
      params: data
    });
  },

  // 托管中心级别分页查询 新
  dealerListV2(data) {
    return request({
      url: '/znyy/operations/v2/list',
      method: 'GET',
      params: data
    });
  },
  // 托管中心级别分页查询 新
  addBusinessName(data) {
    return request({
      url: '/znyy/operations/v2/addBusinessName',
      method: 'POST',
      params: data
    });
  },
  businessLicense(data) {
    return request({
      url: '/znyy/operations/v2/businessLicense',
      method: 'GET',
      params: data
    });
  },
  // 托管中心编辑
  updateDealer(data) {
    return request({
      url: '/znyy/operations/center',
      method: 'PUT',
      data
    });
  },
  //定制俱乐部logo
  customMerchantInfo(data) {
    return request({
      url: '/znyy/operations/center/customMerchantInfo',
      method: 'POST',
      params: data
    });
  },
  //超级俱乐部是否定制logo

  avatarEnable(data) {
    return request({
      url: '/znyy/operations/center/avatarEnable',
      method: 'POST',
      params: data
    });
  },
  // 托管中心查询
  queryActive(id) {
    return request({
      url: '/znyy/operations/center/check/detail/' + id,
      method: 'GET'
    });
  },
  // 托管中心查询V2
  queryActiveV2(id) {
    return request({
      url: '/znyy/operations/v2/operationsDetail',
      method: 'GET',
      params: { id }
    });
  },
  // 托管中心查询V2
  merchantPhoneCheck(params) {
    return request({
      url: '/znyy/operations/v2/operationsV2PhoneCheck',
      method: 'GET',
      params
    });
  },
  // 托管中心开通与暂停
  dealerStatus(id, status) {
    return request({
      url: '/znyy/operations/center/activationAndSuspension?id=' + id + '&isEnable=' + status,
      method: 'PUT'
    });
  },
  updatePaymentIsComplete(id, paymentIsComplete) {
    return request({
      url: '/znyy/operations/center/updatePaymentIsComplete?id=' + id + '&paymentIsComplete=' + paymentIsComplete,
      method: 'PUT'
    });
  },
  //托管中心开通和暂停二级推荐分润
  updateReProfitRank(id, reProfitRank) {
    return request({
      url: '/znyy/operations/center/update/profit?id=' + id + '&reProfitRank=' + reProfitRank,
      method: 'PUT'
    });
  },
  //
  //托管中心审核
  isCheckStatus(data) {
    return request({
      url: '/znyy/operations/center/check',
      method: 'PUT',
      data
    });
  },
  // 导出
  simpleMarketExecl(listQuery) {
    return request({
      // url: '/znyy/operations/center/to/excel', // 旧
      url: '/znyy/operations/v2/export',
      method: 'GET',
      responseType: 'blob',
      params: listQuery
    });
  },
  simpleMarketExecl1(listQuery) {
    return request({
      url: '/znyy/operate/operations/center/query',
      method: 'GET',
      responseType: 'blob',
      params: listQuery
    });
  },
  getCurrentTime() {
    var _this = this;
    let yy = new Date().getFullYear();
    let mm = new Date().getMonth() + 1;
    let dd = new Date().getDate();
    let hh = new Date().getHours();
    let mf = new Date().getMinutes() < 10 ? '0' + new Date().getMinutes() : new Date().getMinutes();
    let ss = new Date().getSeconds() < 10 ? '0' + new Date().getSeconds() : new Date().getSeconds();
    _this.gettime = yy + '-' + mm + '-' + dd + ' ' + hh + ':' + mf + ':' + ss;
    return _this.gettime;
  },
  getRefCode() {
    return request({
      url: '/znyy/areas/operations/center/get/ref',
      method: 'GET'
    });
  }
};
