/**
 * 分润级别接口
 */
import request from '@/utils/request'

export default {
  // 直充分润等级
  profintRankList(pageNum, pageSize) {
    return request({
      url: '/znyy/profit/rank/page/' + pageNum + '/' + pageSize,
      method: 'GET'
    })
  },
  // 直充分润级别新增/修改
  addProfitRank(data) {
    return request({
      url: '/znyy/profit/rank/add',
      method: 'POST',
      data
    })
  },
  //获取直充分润等级
  queryProfitRank(id) {
    return request({
      url: '/znyy/profit/rank/check/' + id,
      method: 'GET'
    })
  },
  //删除直冲分润等级
  deleteProfitRank(id){
   return request({
       url:'/znyy/profit/rank/delete/'+id,
       method: 'DELETE'
   })
  },
  getCurrentTime() {
    var _this = this;
    let yy = new Date().getFullYear();
    let mm = new Date().getMonth() + 1;
    let dd = new Date().getDate();
    let hh = new Date().getHours();
    let mf = new Date().getMinutes() < 10 ? '0' + new Date().getMinutes() : new Date().getMinutes();
    let ss = new Date().getSeconds() < 10 ? '0' + new Date().getSeconds() : new Date().getSeconds();
    _this.gettime = yy + '-' + mm + '-' + dd + ' ' + hh + ':' + mf + ':' + ss;
    return _this.gettime
  }
}
