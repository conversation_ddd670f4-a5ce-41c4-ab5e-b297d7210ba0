<template>
  <div class="app-container">
    <el-card>
      <div class="process">
        <div id="First" class="processlist">
          <div class="add">
            <div class="first">
              <img src="https://document.dxznjy.com/manage/1608021395000" />
            </div>
            <div>选择账户类型</div>
          </div>
          <div class="line"></div>
        </div>
        <div id="Second" class="processlist">
          <div class="add">
            <div class="first">
              <el-image :src="secondImg"></el-image>
            </div>
            <div>账户信息完善</div>
          </div>
          <div class="line"></div>
        </div>
        <div id="Third" class="processlist">
          <div class="add">
            <div class="first">
              <el-image :src="thirdImg"></el-image>
            </div>
            <div>绑定手机号</div>
          </div>
          <div class="line ml-10"></div>
        </div>
        <div id="Fourth" class="processlist">
          <div class="add">
            <div class="first">
              <el-image :src="fourthImg"></el-image>
            </div>
            <div>绑定银行卡</div>
          </div>
          <div class="line ml-10"></div>
        </div>
        <div id="Fifth" class="processlist">
          <div class="add">
            <div class="first">
              <el-image :src="fifthImg"></el-image>
            </div>
            <div>电子签约</div>
          </div>
        </div>
      </div>
      <!-- 选择账户 -->
      <div id="chooseType" v-show="chooseTypeShow">
        <div class="top-add">
          <div class="shuline"></div>
          <h3>选择账户类型</h3>
        </div>
        <div class="mt-40">
          <el-row :span="24">
            <el-col :span="8">
              <label class="col-sm-3 control-label"
                >账户类型<strong class="text-danger"> * </strong>：</label
              >
            </el-col>
            <el-col :span="12">
              <div class="col-sm-9">
                <el-radio
                  v-model="radio"
                  label="3"
                  class="required"
                  :disabled="disabled"
                  >个人</el-radio
                >
                <el-radio
                  v-model="radio"
                  label="2"
                  class="required"
                  :disabled="disabled"
                  >企业</el-radio
                >
              </div>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <div style="color: #ff0000; margin: 15px; display: inline-block">
                (*选择之后将无法修改请谨慎选择)
              </div>
            </el-col>
          </el-row>
        </div>
        <el-button
          type="primary"
          @click="chooseCertType('1')"
          class="next"
          style="margin-left: 250px"
          >下一步</el-button
        >
      </div>
      <div v-show="personalAuthenticationShow">
        <!-- 个人账户信息 -->
        <div id="personalInfo" v-show="formpanelShow">
          <div class="top-add">
            <div class="shuline"></div>
            <h3>完善账户信息</h3>
          </div>
          <div class="mt-40">
            <div class="form-group form-group-sm">
              <el-row>
                <el-col :span="4">
                  <label
                    >真实姓名<strong class="text-danger"> * </strong>：</label
                  >
                </el-col>
                <el-col :span="10">
                  <el-input
                    type="text"
                    v-model="realName"
                    placeholder="真实姓名"
                    maxlength="20"
                    class="form-control input-width required"
                  ></el-input>
                </el-col>
                <el-col :span="8" :offset="1">
                  <span style="color: red; font-size: 12px">
                    *请填写真实信息后面绑定银行卡使用</span
                  >
                </el-col>
              </el-row>
            </div>

            <div class="form-group form-group-sm" style="margin: 20px 0">
              <el-row>
                <el-col :span="4">
                  <label class="col-sm-3 control-label"
                    >身份证号<strong class="text-danger"> * </strong>：</label
                  >
                </el-col>
                <el-col :span="10">
                  <el-input
                    type="text"
                    v-model="idCard"
                    placeholder="身份证号"
                    maxlength="18"
                    class="form-control input-width required"
                    isIdentity="true"
                  ></el-input>
                </el-col>
              </el-row>
            </div>
            <div class="form-group form-group-sm">
              <el-row>
                <el-col :span="4">
                  <label class="col-sm-3 control-label"
                    >手机号<strong class="text-danger"> * </strong>：</label
                  >
                </el-col>
                <el-col :span="10">
                  <el-input
                    type="text"
                    v-model="mobile"
                    placeholder="手机号"
                    maxlength="11"
                    class="form-control input-width required"
                    isIdentity="true"
                  ></el-input>
                </el-col>
              </el-row>
            </div>
          </div>
          <div class="step">
            <el-button type="primary" @click="perpStep('1')" class="next"
              >暂不认证</el-button
            >
            <el-button type="primary" @click="chooseCertType('2')" class="next"
              >下一步</el-button
            >
          </div>
        </div>
        <!-- 个人绑定手机号 -->
        <div id="pBindPhone" v-show="pBindPhoneShow">
          <div class="top-add">
            <div class="shuline"></div>
            <h3>绑定手机号</h3>
          </div>
          <div class="mt-40">
            <div class="form-group form-group-sm">
              <el-row>
                <el-col :span="4">
                  <label class="col-sm-3 control-label"
                    >手机号<strong class="text-danger"> * </strong>：</label
                  >
                </el-col>
                <el-col :span="10">
                  <el-input
                    type="text"
                    v-model="mobile"
                    placeholder="手机号"
                    maxlength="20"
                    class="form-control input-width required"
                  ></el-input>
                </el-col>
                <el-col :span="4" :offset="1">
                  <el-button
                    type="primary"
                    size="mini"
                    id="getSms"
                    @click="getSmsClick('1', mobile)"
                    :disabled="disabledSmsClick"
                    >发送验证码</el-button
                  >
                </el-col>
              </el-row>
            </div>
            <div class="form-group form-group-sm" style="margin: 20px 0">
              <el-row>
                <el-col :span="4">
                  <label class="col-sm-3 control-label"
                    >验证码<strong class="text-danger"> * </strong>：</label
                  >
                </el-col>
                <el-col :span="10">
                  <el-input
                    type="text"
                    v-model="smsCode"
                    placeholder="验证码"
                    maxlength="20"
                    class="form-control input-width required"
                  ></el-input>
                </el-col>
                <el-col
                  :span="6"
                  v-show="!show"
                  class="count"
                  style="text-align: center; line-height: 40px"
                  >{{ count }} s</el-col
                >
              </el-row>
            </div>
            <div class="step">
              <el-button type="primary" @click="perpStep('1')" class="next"
                >暂不认证</el-button
              >
              <el-button
                type="primary"
                @click="chooseCertType('3')"
                class="next"
                >下一步</el-button
              >
            </div>
          </div>
        </div>
        <!-- 个人绑定银行卡 -->
        <div id="pBindBanK" v-show="pBindBanKShow">
          <div class="top-add">
            <div class="shuline"></div>
            <h3>绑定银行卡</h3>
          </div>
          <div class="mt-40">
            <div class="form-group form-group-sm">
              <el-row>
                <el-col :span="4">
                  <label class="col-sm-3 control-label"
                    >银行卡<strong class="text-danger"> * </strong>：</label
                  >
                </el-col>

                <div class="col-sm-9">
                  <el-col :span="10" :offset="1">
                    <el-input
                      type="text"
                      v-model="bankCard"
                      placeholder="银行卡号"
                      maxlength="20"
                      class="form-control input-width required"
                    ></el-input>
                  </el-col>

                  <span></span>
                </div>
              </el-row>
            </div>
            <div class="form-group form-group-sm" style="margin: 20px 0">
              <el-row>
                <el-col :span="4">
                  <label class="col-sm-3 control-label"
                    >姓名<strong class="text-danger"> * </strong>：</label
                  >
                </el-col>
                <div class="col-sm-9">
                  <el-col :span="10" :offset="1">
                    <el-input
                      type="text"
                      v-model="pRealName"
                      placeholder="姓名"
                      maxlength="20"
                      class="form-control input-width required"
                    ></el-input>
                  </el-col>
                </div>
              </el-row>
            </div>
            <div class="form-group form-group-sm">
              <el-row>
                <el-col :span="4">
                  <label class="col-sm-3 control-label"
                    >身份证号<strong class="text-danger"> * </strong>：</label
                  >
                </el-col>
                <div class="col-sm-9">
                  <el-col :span="10" :offset="1">
                    <el-input
                      type="text"
                      v-model="idCard"
                      placeholder="身份证号"
                      maxlength="20"
                      class="form-control input-width required"
                    ></el-input>
                  </el-col>
                </div>
              </el-row>
            </div>
            <div class="form-group form-group-sm" style="margin: 20px 0">
              <el-row>
                <el-col :span="4">
                  <label class="col-sm-3 control-label"
                    >银行卡上的手机号<strong class="text-danger"> * </strong
                    >：</label
                  >
                </el-col>
                <div class="col-sm-9">
                  <el-col :span="10" :offset="1">
                    <el-input
                      type="text"
                      v-model="pMobile"
                      placeholder="银行卡上的手机号"
                      maxlength="20"
                      class="form-control input-width required"
                    ></el-input>
                  </el-col>
                  <el-col :span="4" :offset="1">
                    <el-button
                      type="primary"
                      size="mini"
                      id="getSms"
                      @click="bindBankInfo('1', pMobile)"
                      :disabled="disabledgetSms"
                      >获取验证码</el-button
                    >
                  </el-col>
                </div>
              </el-row>
            </div>
            <div class="form-group form-group-sm">
              <el-row>
                <el-col :span="4">
                  <label class="col-sm-3 control-label"
                    >验证码<strong class="text-danger"> * </strong>：</label
                  >
                </el-col>
                <div class="col-sm-9">
                  <el-col :span="10" :offset="1">
                    <el-input
                      type="text"
                      v-model="pSmsCode"
                      placeholder="验证码"
                      maxlength="20"
                      class="form-control input-width required"
                    ></el-input>
                  </el-col>
                  <el-col
                    :span="6"
                    v-show="!show"
                    class="count"
                    style="text-align: center; line-height: 40px"
                    >{{ countCode }} s</el-col
                  >
                </div>
              </el-row>
            </div>
          </div>
          <div class="step">
            <el-button type="primary" @click="perpStep('1')" class="next"
              >暂不认证</el-button
            >
            <el-button
              type="primary"
              @click="confrimBindBankInfo()"
              class="next"
              >下一步</el-button
            >
          </div>
        </div>
        <!-- 电子签约 -->
        <div id="P_Sign" v-show="psignShow">
          <div class="top-add">
            <div class="shuline"></div>
            <h3>电子签约</h3>
          </div>
          <div class="mt-40">
            <el-button type="primary" @click="perpStep('1')" class="next"
              >暂不认证</el-button
            >
            <el-button type="primary" @click="goSign()" class="next"
              >去签约</el-button
            >
          </div>
        </div>
      </div>

      <div v-show="enterpriseCertificationShow">
        <!-- 企业完善账户信息 -->
        <div class="" id="CompanyInfo" v-show="companyInfoShow">
          <div class="top-add">
            <div class="shuline"></div>
            <h3>完善账户信息</h3>
          </div>

          <div class="form-group form-group-sm">
            <el-row>
              <el-col :span="6">
                <label class="col-sm-3 control-label"
                  >公司名称<strong class="text-danger"> * </strong>：</label
                >
              </el-col>
              <div class="col-sm-9">
                <el-col :span="10" :offset="1">
                  <el-input
                    type="text"
                    v-model="companyName"
                    placeholder="企业名称"
                    maxlength="20"
                    class="form-control input-width required"
                  ></el-input>
                </el-col>
                <span></span>
              </div>
            </el-row>
          </div>
          <div class="form-group form-group-sm" style="margin: 20px 0">
            <el-row>
              <el-col :span="6">
                <label class="col-sm-3 control-label"
                  >公司地址<strong class="text-danger"> * </strong>：</label
                >
              </el-col>

              <div class="col-sm-9">
                <el-col :span="10" :offset="1">
                  <el-input
                    type="text"
                    v-model="companyAddress"
                    placeholder="公司地址"
                    maxlength="20"
                    class="form-control input-width required"
                  ></el-input>
                </el-col>

                <span></span>
              </div>
            </el-row>
          </div>
          <div class="form-group form-group-sm">
            <el-row>
              <el-col :span="6">
                <label class="col-sm-3 control-label"
                  >法人名称<strong class="text-danger"> * </strong>：</label
                >
              </el-col>

              <div class="col-sm-9">
                <el-col :span="10" :offset="1">
                  <el-input
                    type="text"
                    v-model="legalName"
                    placeholder="法人名称"
                    maxlength="20"
                    class="form-control input-width required"
                  ></el-input>
                </el-col>
                <span></span>
              </div>
            </el-row>
          </div>

          <div class="form-group form-group-sm" style="margin: 20px 0">
            <el-row>
              <el-col :span="6">
                <label class="col-sm-3 control-label"
                  >法人身份证号<strong class="text-danger"> * </strong>：</label
                >
              </el-col>

              <div class="col-sm-9">
                <el-col :span="10" :offset="1">
                  <el-input
                    type="text"
                    v-model="cIdCard"
                    placeholder="法人身份证号"
                    maxlength="20"
                    class="form-control input-width required"
                  ></el-input>
                </el-col>
                <span></span>
              </div>
            </el-row>
          </div>
          <div class="form-group form-group-sm">
            <el-row>
              <el-col :span="6">
                <label class="col-sm-3 control-label"
                  >法人手机号<strong class="text-danger"> * </strong>：</label
                >
              </el-col>

              <div class="col-sm-9">
                <el-col :span="10" :offset="1">
                  <el-input
                    type="text"
                    v-model="cMobile"
                    placeholder="法人手机号"
                    maxlength="20"
                    class="form-control input-width required"
                  ></el-input>
                </el-col>
                <span></span>
              </div>
            </el-row>
          </div>
          <div class="form-group form-group-sm" style="margin: 20px 0">
            <el-row>
              <el-col :span="6">
                <label class="col-sm-3 control-label"
                  >企业对公账户<strong class="text-danger"> * </strong>：</label
                >
              </el-col>

              <div class="col-sm-9">
                <el-col :span="10" :offset="1">
                  <el-input
                    type="text"
                    v-model="cBankCard"
                    placeholder="企业对公账户"
                    maxlength="20"
                    class="form-control input-width required"
                  ></el-input>
                </el-col>
                <span></span>
              </div>
            </el-row>
          </div>
          <div class="form-group form-group-sm">
            <el-row>
              <el-col :span="6">
                <label class="col-sm-3 control-label"
                  >银行名称<strong class="text-danger"> * </strong>：</label
                >
              </el-col>
              <div class="col-sm-9">
                <el-col :span="10" :offset="1">
                  <el-input
                    type="text"
                    v-model="cBankName"
                    placeholder="银行名称"
                    maxlength="20"
                    class="form-control input-width required"
                  ></el-input>
                </el-col>
                <span></span>
              </div>
            </el-row>
          </div>
          <div class="form-group form-group-sm" style="margin: 20px 0">
            <el-row>
              <el-col :span="6">
                <label class="col-sm-3 control-label"
                  >统一社会信用证书<strong class="text-danger"> * </strong
                  >：</label
                >
              </el-col>
              <div class="col-sm-9">
                <el-col :span="10" :offset="1">
                  <el-input
                    type="text"
                    v-model="cUniCredit"
                    placeholder="统一社会信用证书"
                    maxlength="20"
                    class="form-control input-width required"
                  ></el-input>
                </el-col>
                <span></span>
              </div>
            </el-row>
          </div>
          <div class="form-group form-group-sm" style="margin: 20px 0">
            <el-row>
              <el-col :span="6">
                <label class="col-sm-3 control-label"
                  >对公账户许可证<strong class="text-danger"> * </strong
                  >：</label
                >
              </el-col>
              <div class="col-sm-9">
                <el-col :span="10" :offset="1">
                  <el-input
                    type="text"
                    v-model="bankSettlement"
                    placeholder="对公账户许可证"
                    maxlength="20"
                    class="form-control input-width required"
                  ></el-input>
                </el-col>
                <span></span>
              </div>
            </el-row>
          </div>
          <div class="form-group form-group-sm" style="margin: 20px 0">
            <el-row>
              <el-col :span="6">
                <label class="col-sm-3 control-label"
                  >营业执照<strong class="text-danger"> * </strong>：</label
                >
              </el-col>
              <div class="col-sm-9">
                <el-col :span="10" :offset="1">
                  <el-upload
                    ref="clearupload"
                    v-loading="uploadLoading"
                    list-type="picture-card"
                    action=""
                    element-loading-text="图片上传中"
                    :limit="1"
                    :on-exceed="justPictureNum"
                    :file-list="fileDetailList"
                    :http-request="uploadDetailHttp"
                    :on-preview="handlePictureCardPreview"
                    :on-remove="handleRemoveDetail"
                  >
                    <i class="el-icon-plus" />
                  </el-upload>
                  <!-- <el-input
                    type="text"
                    v-model="uniCreditImg"
                    placeholder="营业执照"
                    maxlength="20"
                    class="form-control input-width required"
                  ></el-input> -->
                </el-col>
                <span></span>
              </div>
            </el-row>
          </div>

          <div class="form-group form-group-sm">
            <el-row>
              <el-col :span="6">
                <label class="col-sm-3 control-label"
                  >身份证正面<strong class="text-danger"> * </strong>：</label
                >
              </el-col>
              <div class="col-sm-9">
                <el-col :span="10" :offset="1">
                  <el-upload
                    ref="clearupload"
                    v-loading="uploadLoading01"
                    list-type="picture-card"
                    action=""
                    element-loading-text="图片上传中"
                    :limit="1"
                    :on-exceed="justPictureNum"
                    :file-list="fileDetailList01"
                    :http-request="uploadDetailHttp01"
                    :on-preview="handlePictureCardPreview"
                    :on-remove="handleRemoveDetail01"
                  >
                    <i class="el-icon-plus" />
                  </el-upload>
                  <!-- <el-input
                    type="text"
                    v-model="uniCreditImg"
                    placeholder="营业执照"
                    maxlength="20"
                    class="form-control input-width required"
                  ></el-input> -->
                </el-col>
                <span></span>
              </div>
            </el-row>
          </div>
          <div class="form-group form-group-sm" style="margin: 20px 0">
            <el-row>
              <el-col :span="6">
                <label class="col-sm-3 control-label"
                  >身份证反面<strong class="text-danger"> * </strong>：</label
                >
              </el-col>
              <div class="col-sm-9">
                <el-col :span="10" :offset="1">
                  <el-upload
                    ref="clearupload"
                    v-loading="uploadLoading02"
                    list-type="picture-card"
                    action=""
                    element-loading-text="图片上传中"
                    :limit="1"
                    :on-exceed="justPictureNum"
                    :file-list="fileDetailList02"
                    :http-request="uploadDetailHttp02"
                    :on-preview="handlePictureCardPreview"
                    :on-remove="handleRemoveDetail02"
                  >
                    <i class="el-icon-plus" />
                  </el-upload>
                  <!-- <el-input
                    type="text"
                    v-model="uniCreditImg"
                    placeholder="营业执照"
                    maxlength="20"
                    class="form-control input-width required"
                  ></el-input> -->
                </el-col>
                <span></span>
              </div>
            </el-row>
          </div>

          <div id="isCheck" v-show="false" style="margin: 20px 0">
            您的企业信息正在审核中，请等审核通过后再进行下一步操作
          </div>
          <div class="step clearfix">
            <el-button type="primary" @click="perpStep('1')" class="next"
              >暂不认证</el-button
            >
            <el-button type="primary" @click="chooseCertType('4')" class="next"
              >下一步</el-button
            >
          </div>
        </div>
        <!-- 企业绑定手机号 -->
        <div id="C_BindP" v-show="cBindPShow">
          <div class="top-add">
            <div class="shuline"></div>
            <h3>绑定手机号</h3>
          </div>
          <div class="mt-40">
            <div class="form-group form-group-sm">
              <el-row>
                <el-col :span="4">
                  <label class="col-sm-3 control-label"
                    >法人名称<strong class="text-danger"> * </strong>：</label
                  >
                </el-col>
                <div class="col-sm-9">
                  <el-col :span="10" :offset="1">
                    <el-input
                      type="text"
                      v-model="legalName"
                      placeholder="法人名称"
                      maxlength="20"
                      class="form-control input-width required"
                    ></el-input>
                  </el-col>
                  <span></span>
                </div>
              </el-row>
            </div>
            <div class="form-group form-group-sm" style="margin: 20px 0">
              <el-row>
                <el-col :span="4">
                  <label class="col-sm-3 control-label"
                    >法人身份证号<strong class="text-danger"> * </strong
                    >：</label
                  >
                </el-col>
                <div class="col-sm-9">
                  <el-col :span="10" :offset="1">
                    <el-input
                      type="text"
                      v-model="cIdCard"
                      placeholder="法人身份证号"
                      maxlength="20"
                      class="form-control input-width required"
                    ></el-input>
                  </el-col>
                  <span></span>
                </div>
              </el-row>
            </div>
            <div class="form-group form-group-sm">
              <el-row>
                <el-col :span="4">
                  <label class="col-sm-3 control-label"
                    >手机号<strong class="text-danger"> * </strong>：</label
                  >
                </el-col>
                <div class="col-sm-9">
                  <el-col :span="10" :offset="1">
                    <el-input
                      type="text"
                      v-model="mobile"
                      placeholder="手机号"
                      maxlength="20"
                      class="form-control input-width required"
                    ></el-input>
                  </el-col>
                  <el-col :span="4" :offset="1">
                    <el-button
                      type="primary"
                      size="mini"
                      id="getSms"
                      @click="getSmsClick('1', mobile)"
                      :disabled="disabledSmsClick"
                      >发送验证码</el-button
                    >
                  </el-col>
                  <span></span>
                </div>
              </el-row>
            </div>
            <div class="form-group form-group-sm" style="margin: 20px 0">
              <el-row>
                <el-col :span="5">
                  <label class="col-sm-3 control-label"
                    >验证码<strong class="text-danger"> * </strong>：</label
                  >
                </el-col>
                <el-col :span="10">
                  <el-input
                    type="text"
                    v-model="smsCode"
                    placeholder="验证码"
                    maxlength="20"
                    class="form-control input-width required"
                  ></el-input>
                </el-col>
                <el-col
                  :span="6"
                  v-show="!show"
                  class="count"
                  style="text-align: center; line-height: 40px"
                  >{{ count }} s</el-col
                >
              </el-row>
            </div>
          </div>
          <div class="step">
            <el-button type="primary" @click="perpStep('1')" class="next"
              >暂不认证</el-button
            >
            <el-button
              type="primary"
              :disabled="buttonDisAbled"
              @click="chooseCertType('6')"
              class="next"
              >下一步</el-button
            >
          </div>
        </div>
        <!-- 绑定银行卡 -->
        <div id="C_BindB" v-show="cBindBShow">
          <div class="top-add">
            <div class="shuline"></div>
            <h3>绑定银行卡</h3>
          </div>
          <div class="mt-40">
            <div class="form-group form-group-sm">
              <el-row>
                <el-col :span="4">
                  <label class="col-sm-3 control-label"

                    >企业对私账户<strong class="text-danger"> * </strong
                    >：</label
                  >
                </el-col>
                <div class="col-sm-9">
                  <el-col :span="10" :offset="1">
                    <el-input
                      type="text"

                      v-model="cBankCard1"
                      placeholder="企业对私账户"
                      maxlength="20"
                      class="form-control input-width required"
                    ></el-input>
                  </el-col>
                  <span></span>
                </div>
              </el-row>
            </div>
            <div class="form-group form-group-sm" style="margin: 20px 0">
              <el-row>
                <el-col :span="4">
                  <label class="col-sm-3 control-label"
                    >银行预留手机号<strong class="text-danger"> * </strong
                    >：</label
                  >
                </el-col>
                <div class="col-sm-9">
                  <el-col :span="10" :offset="1">
                    <el-input
                      type="text"
                      v-model="bankMoblie"
                      placeholder="银行预留手机号"
                      maxlength="20"
                      class="form-control input-width required"
                    ></el-input>
                  </el-col>
                  <el-col :span="4" :offset="1">
                    <el-button
                      type="primary"
                      size="mini"
                      id="getSms"
                      @click="bindBankInfo01('1', mobile)"
                      :disabled="disabledgetSms"
                      >获取验证码</el-button
                    >
                  </el-col>
                  <span></span>
                </div>
              </el-row>
            </div>
            <div class="form-group form-group-sm">
              <el-row>
                <el-col :span="4">
                  <label class="col-sm-3 control-label"
                    >验证码<strong class="text-danger"> * </strong>：</label
                  >
                </el-col>
                <div class="col-sm-9">
                  <el-col :span="10" :offset="1">
                    <el-input
                      type="text"
                      v-model="pSmsCode"
                      placeholder="验证码"
                      maxlength="20"
                      class="form-control input-width required"
                    ></el-input>
                  </el-col>
                  <el-col
                    :span="6"
                    v-show="!show"
                    class="count"
                    style="text-align: center; line-height: 40px"
                    >{{ countCode }} s</el-col
                  >
                </div>
              </el-row>
            </div>
          </div>
          <div class="step">
            <el-button type="primary" @click="perpStep('1')" class="next"
              >暂不认证</el-button
            >
            <el-button type="primary" @click="chooseCertType('7')" class="next"
              >下一步</el-button
            >
          </div>
        </div>
        <!-- 电子签约 -->
        <div id="C_Sign" v-show="cSignShow">
          <div class="top-add">
            <div class="shuline"></div>
            <h3>电子签约</h3>
          </div>
          <div class="mt-40">
            <el-button type="primary" @click="perpStep('1')" class="next"
              >暂不认证</el-button
            >
            <el-button type="primary" @click="goSign()" class="next"
              >去签约</el-button

            >
          </div>
        </div>
      </div>
    </el-card>
    <el-dialog
      :visible.sync="dialogUploadVisible"
      :close-on-click-modal="false"
    >
      <img width="100%" :src="dialogImageUrl" alt />
    </el-dialog>
  </div>
</template>

<script>
import authenticationApi from "@/api/authentication";
import { ossPrClient } from "@/api/alibaba";

export default {
  data() {
    var validatePass = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请输入密码"));
      } else {
        if (this.dataQuery.rNewPassWord !== "") {
          this.$refs.dataQuery.validateField("rNewPassWord");
        }
        callback();
      }
    };
    var validatePass2 = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请再次输入密码"));
      } else if (value !== this.dataQuery.newPassWord) {
        callback(new Error("两次输入密码不一致!"));
      } else {
        callback();
      }
    };
    var validatePass3 = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请输入密码"));
      } else {
        if (this.dataQuery.rNewSecondPwd !== "") {
          this.$refs.dataQuery.validateField("rNewSecondPwd");
        }
        callback();
      }
    };
    var validatePass4 = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请再次输入密码"));
      } else if (value !== this.dataQuery.newSecondPwd) {
        callback(new Error("两次输入密码不一致!"));
      } else {
        callback();
      }
    };
    return {
      dataQuery: {},
      radio: "3", //个人1企业
      chooseTypeShow: true, //选择账户类型显示

      formpanelShow: false, //完善个人账户信息
      accountType: [], //扣除方式
      merchantCodeType: [], //账户方式
      queryTrueShow: true,
      dialogVisible: false, //密码弹框
      checkSecondPwdShow: false, //密码成功
      pBindPhoneShow: false, //个人绑定手机号
      pBindBanKShow: false, //个人绑定银行卡
      psignShow: false, //个人去签约
      personalAuthenticationShow: false, //个人认证
      enterpriseCertificationShow: false, //企业认证
      buttonDisAbled: false, //按钮禁用
      companyInfoShow: false, //完善账户信息
      cBindPShow: false, //企业绑定手机号
      cBindBShow: false, //企业绑定银行卡
      cSignShow: false, //企业电子签约

      secondPassWord: "",
      backCourse: "", //剩余课时
      symoney: "", //账户余额
      //个人信息开始

      realName: "", //真实姓名
      mobile: "", //手机号
      disabledSmsClick: false, //点击事件
      bankCard: "", //银行卡号
      idCard: "", //身份证号
      pIdCard: "", //银行卡上的身份证号
      pMobile: "", //银行卡上的手机号
      pRealName: "", //银行卡上的姓名
      pSmsCode: "", //银行卡绑定的验证码
      countCode: "", //绑定银行卡的验证码
      disabledgetSms: false, //绑定银行卡的验证
      //个人信息结束
      //企业信息开始
      companyName: "", //公司名称
      companyAddress: "", //公司地址
      legalName: "", //法人名称
      cIdCard: "", //身份证号
      cMobile: "", //法人手机号
      cBankCard: "", //对公账号
      cBankName: "", //银行名称,
      cUniCredit: "", //统一社会信用证书
      uniCreditImg: "", //营业执照
      bankSettlement: "", //对公账户许可证
      idCardFrontImg: "", //身份证正面照
      idCardBackImg: "", //身份证反面照
      phone: "", //手机号
      bankMoblie: "",

      cBankCard1:"",//对私账户
      //企业信息结束
      show: true,
      count: "",
      timer: null,
      disabled: true,
      //https://document.dxznjy.com/manage/*************
      //http://dx-applet-mall.oss-cn-shanghai.aliyuncs.com/manage/*************
      //http://dx-applet-mall.oss-cn-shanghai.aliyuncs.com/manage/*************
      //http://dx-applet-mall.oss-cn-shanghai.aliyuncs.com/manage/*************
      secondImg:
        "http://dx-applet-mall.oss-cn-shanghai.aliyuncs.com/manage/*************", //账户信息完善
      thirdImg:
        "http://dx-applet-mall.oss-cn-shanghai.aliyuncs.com/manage/*************",
      fourthImg:
        "http://dx-applet-mall.oss-cn-shanghai.aliyuncs.com/manage/*************",
      fifthImg:
        "http://dx-applet-mall.oss-cn-shanghai.aliyuncs.com/manage/*************",
      fileListPending: [], // 待处理已上传图片信息
      fileList: [], // 上传图片已有图片列表

      fileDetailListPending: [], // 待处理已上传图片信息
      fileDetailList: [], // 上传图片已有图片列表

      dialogUploadVisible: false,
      dialogImageUrl: "", // 上传图片预览

      uploadLoading: false, // 上传图片加载按钮
      fullscreenLoading: false, // 保存啥的加载

      content: "",
      isUploadSuccess: true, // 是否上传成功
      uploadLoading01: false,
      fileDetailList01: [],
      uploadLoading02: false,
      fileDetailList02: [],
      smsCode: "",
      // false: true,
      rules: {
        newPassWord: [
          { required: true, message: "请输入密码", trigger: "blur" },
          {
            min: 6,
            max: 16,
            message: "长度在 6 到 16 个字符",
            trigger: "blur",
          },
          { validator: validatePass, trigger: "blur" },
        ],
        rNewPassWord: [
          { required: true, message: "请确认密码", trigger: "blur" },
          {
            min: 6,
            max: 16,
            message: "长度在 6 到 16 个字符",
            trigger: "blur",
          },
          { validator: validatePass2, trigger: "blur", required: true },
        ],
        newSecondPwd: [
          { required: true, message: "请输入密码", trigger: "blur" },
          {
            min: 6,
            max: 16,
            message: "长度在 6 到 16 个字符",
            trigger: "blur",
          },
          { validator: validatePass3, trigger: "blur" },
        ],
        rNewSecondPwd: [
          { required: true, message: "请确认密码", trigger: "blur" },
          {
            min: 6,
            max: 16,
            message: "长度在 6 到 16 个字符",
            trigger: "blur",
          },
          { validator: validatePass4, trigger: "blur", required: true },
        ],
        password: [
          { required: true, message: "请输入旧密码", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    //检查是否认证成功
    this.queryYstMemberInfo();
    ossPrClient();
  },
  methods: {

      //获取会员信息
      getMemberInformation(){

      }
      ,
    //检查是否认证成功
    queryYstMemberInfo() {
      const that = this;
      authenticationApi.queryYstMemberInfo().then((res) => {
        console.log(res);
        if (res.data.data.userCode === "" || res.data.data.userCode === null) {
          that.chooseTypeShow = true;
          that.disabled = false;
          that.personalAuthenticationShow = false;
          that.enterpriseCertificationShow = false;
        } else {
          if (res.data.data.signContractStatus === 1) {
            // 回到首页
            that.$message.info("已签约不能重复签约");
            that.$router.push({ path: "/" });
          } else {
            console.log(res.data.data.certStatus);
            if (res.data.data.userRole === "Merchant") {
              //企业认证
              that.chooseTypeShow = true;
              that.personalAuthenticationShow = false;
              that.enterpriseCertificationShow = true;
              if (
                res.data.data.certStatus === "" ||
                res.data.data.certStatus === "0"
              ) {
                that.disabled = true;
                that.radio = "2";
                that.companyInfoShow = false;
                that.chooseTypeShow = true;
                that.cBindPShow = false;
                that.cBindBShow = false;
                that.cSignShow = false;
                that.buttonDisAbled = true;
              } else if (res.data.data.certStatus === "2") {
                that.getInfo();
                that.buttonDisAbled = true;
                //企业信息回显
                that.fileDetailList.push({
                  url: that.aliUrl + res.data.data.uniCreditImg,
                });
                that.fileDetailList01.push({
                  url: that.aliUrl + res.data.data.idCardFrontImg,
                });
                that.fileDetailList02.push({
                  url: that.aliUrl + res.data.data.idCardBackImg,
                });

                that.companyInfoShow = true;
                that.chooseTypeShow = false;
                that.cBindPShow = false;
                that.cBindBShow = false;
                that.cSignShow = false;

                that.companyName = res.data.data.companyName;
                that.companyAddress = res.data.data.companyAddress;
                that.legalName = res.data.data.legalName;
                that.cIdCard = res.data.data.idCard;
                that.cMobile = res.data.data.mobile;
                that.cBankCard = res.data.data.bankCard;
                that.cBankName = res.data.data.bankName;
                that.cUniCredit = res.data.data.uniCredit;
                that.uniCreditImg = res.data.data.uniCreditImg;
                that.idCardFrontImg = res.data.data.idCardFrontImg;
                that.idCardBackImg = res.data.data.idCardBackImg;
                that.bankSettlement = res.data.data.bankSettLement;
              } else if (res.data.data.certStatus === "1") {
                if (
                  (res.data.data.bindPhoneStatus === 0 ||
                    res.data.data.bindPhoneStatus === null ||
                    res.data.data.bindPhoneStatus === "") &&
                  (res.data.data.bindCardStatus === 0 ||
                    res.data.data.bindCardStatus === null ||
                    res.data.data.bindCardStatus === "") &&
                  (res.data.data.signContractStatus === 0 ||
                    res.data.data.signContractStatus === null ||
                    res.data.data.signContractStatus === "")
                ) {
                  that.getPhone();
                  that.chooseTypeShow = false;
                  that.companyInfoShow = false;
                  that.cBindPShow = true;
                  that.cBindBShow = false;
                  that.cSignShow = false;
                } else if (
                  res.data.data.bindPhoneStatus === 1 &&
                  (res.data.data.bindCardStatus === 0 ||
                    res.data.data.bindCardStatus === null ||
                    res.data.bindCardStatus === "") &&
                  (res.data.data.signContractStatus === 0 ||
                    res.data.data.signContractStatus === null ||
                    res.data.data.signContractStatus === "")
                ) {
                  //显示绑定银行卡
                  that.chooseTypeShow = false;
                  that.companyInfoShow = false;
                  that.cBindPShow = false;
                  that.cBindBShow = true;
                  that.cSignShow = false;
                  that.getBandk();
                } else if (
                  res.data.data.bindPhoneStatus === 1 &&
                  res.data.data.bindCardStatus === 1 &&
                  (res.data.data.signContractStatus === 0 ||
                    res.data.data.signContractStatus === null ||
                    res.data.data.signContractStatus === "")
                ) {
                  //电子签约
                  that.chooseTypeShow = false;
                  that.companyInfoShow = false;
                  that.cBindPShow = false;
                  that.cBindBShow = false;
                  that.cSignShow = true;

                  that.sgain();
                }
              }
            } else {
              that.chooseTypeShow = true;
              that.personalAuthenticationShow = true;
              that.enterpriseCertificationShow = false;
              //个人认证
              if (
                res.data.data.certStatus === "" ||
                res.data.data.certStatus === "0"
              ) {
                that.disabled = true;
                that.radio = "3";
                that.formpanelShow = false;
                that.chooseTypeShow = true;
                that.pBindPhoneShow = false;
                that.pBindBanKShow = false;
                that.psignShow = false;
              } else if (res.data.data.certStatus === "1") {
                if (
                  (res.data.data.bindPhoneStatus === 0 ||
                    res.data.data.bindPhoneStatus === null ||
                    res.data.data.bindPhoneStatus === "") &&
                  (res.data.data.bindCardStatus === 0 ||
                    res.data.data.bindCardStatus === null ||
                    res.data.data.bindCardStatus === "") &&
                  (res.data.data.signContractStatus === 0 ||
                    res.data.data.signContractStatus === null ||
                    res.data.data.signContractStatus === "")
                ) {
                  //绑定手机号
                  that.chooseTypeShow = false;
                  that.formpanelShow = false;
                  that.pBindPhoneShow = true;
                  that.pBindBanKShow = false;
                  that.psignShow = false;
                  that.getPhone();
                } else if (
                  res.data.data.bindPhoneStatus === 1 &&
                  (res.data.data.bindCardStatus === 0 ||
                    res.data.data.bindCardStatus === null ||
                    res.data.bindCardStatus === "") &&
                  (res.data.data.signContractStatus === 0 ||
                    res.data.data.signContractStatus === null ||
                    res.data.data.signContractStatus === "")
                ) {
                  //绑定银行卡
                  that.chooseTypeShow = false;
                  that.formpanelShow = false;
                  that.pBindPhoneShow = false;
                  that.pBindBanKShow = true;
                  that.psignShow = false;
                  that.getBandk();
                } else if (
                  res.data.data.bindPhoneStatus === 1 &&
                  res.data.data.bindCardStatus === 1 &&
                  (res.data.data.signContractStatus === 0 ||
                    res.data.data.signContractStatus === null ||
                    res.data.data.signContractStatus === "")
                ) {
                  //电子签约
                  that.chooseTypeShow = false;
                  that.formpanelShow = false;
                  that.pBindPhoneShow = false;
                  that.pBindBanKShow = false;
                  that.psignShow = true;

                  that.sgain();
                }
              }
            }
          }
        }
      });
    },

    //下一步
    chooseCertType(ctype) {
      const that = this;
      if (ctype === "1") {
        if (that.radio === 0) {
          that.$message.info("请选择类型");
          return false;
        }
        if (that.radio === "3") {
           that.$message.info("请在小程序上进行个人实名认证");
           return  false;
          authenticationApi.createMember(that.radio).then((res) => {
            that.chooseTypeShow = false;
            that.personalAuthenticationShow = true;
            that.formpanelShow = true;
            that.pBindPhoneShow = false;
            that.pBindBanKShow = false;
            that.psignShow = false;
            that.getInfo();
          });
        } else {
          authenticationApi.createMember(that.radio).then((res) => {
            that.chooseTypeShow = false;
            that.enterpriseCertificationShow = true;
            that.companyInfoShow = true;
            that.cBindPShow = false;
            that.cBindBShow = false;
            that.cSignShow = false;
            that.getInfo();
          });
        }
      } else if (ctype === "2") {
        if (!that.realName) {
          that.$message.info("请填写真实姓名");
          return false;
        }
        if (!that.mobile) {
          that.$message.info("请填写手机号");
          return false;
        }
        if (!that.idCard) {
          that.$message.info("请填写身份证号");
        }
        const data = {
          realName: that.realName,
          idCard: that.idCard,
          type: that.radio,
        };


        authenticationApi.personalRealNameAuthentication(data).then((res) => {
         that.chooseTypeShow = false;
        that.formpanelShow = false;
        that.pBindPhoneShow = true;
        that.pBindBanKShow = false;
        that.psignShow = false;
        that.disabledSmsClick = false;
          that.getPhone();
        });
      } else if (ctype === "3") {
        //手机号绑定
        if (!that.mobile) {
          that.$message.info("手机号不能为空");
          return false;
        }
        if (!that.smsCode) {
          that.$message.info("验证码不能为空");
        }
        authenticationApi
          .individualBindingMobilePhoneNumber(that.smsCode, that.mobile)
          .then((res) => {
            that.chooseTypeShow = false;
            that.formpanelShow = false;
            that.pBindPhoneShow = false;
            that.pBindBanKShow = true;
            that.psignShow = false;
            that.getBandk();
          });
      } else if (ctype === "4") {
        if (!that.companyName) {
          that.$message.info("公司名称不能为空");
          return false;
        }
        if (!that.companyAddress) {
          that.$message.info("公司地址");
          return false;
        }
        if (!that.legalName) {
          that.$message.info("法人姓名");
          return false;
        }
        if (!that.cIdCard) {
          that.$message.info("法人姓名不能为空");
          return false;
        }
        if (!that.cMobile) {
          that.$message.info("法人手机号不能为空");
          return false;
        }
        if (!that.cBankCard) {
          that.$message.info("企业对公账户不能为空");
          return false;
        }
        if (!that.cBankName) {
          that.$message.info("银行名称不能为空");
          return false;
        }
        if (!that.cUniCredit) {
          that.$message.info("统一社会信用证书不能为空");
          return false;
        }
        if (!that.bankSettlement) {
          that.$message.info("对公账户许可证不能为空");
          return false;
        }
        if (!that.idCardFrontImg) {
          that.$message.info("身份证正面照不能为空");
          return false;
        }
        if (!that.idCardBackImg) {
          that.$message.info("身份证反面照不能为空");
          return false;
        }
        if (that.uniCreditImg === null || that.uniCreditImg === "") {
          that.$message.info("营业执照不能为空");
          return false;
        }
        const data = {
          companyName: that.companyName, //公司名称
          companyAddress: that.companyAddress, //公司地址
          legalName: that.legalName, //法人姓名
          idCard: that.cIdCard, //法人身份证号
          mobile: that.cMobile, //法人手机号
          bankCard: that.cBankCard, //企业对公账户
          bankName: that.cBankName, //银行名称
          uniCredit: that.cUniCredit, //统一社会信用证书
          uniCreditImg: that.uniCreditImg, //营业执照
          bankSettlement: that.bankSettlement, //对公账户许可证
          idCardFrontImg: that.idCardFrontImg, //身份证正面
          idCardBackImg: that.idCardBackImg, //身份证反面
        };
        authenticationApi.enterpriseRealNameCertification(data).then((res) => {
          that.$message.success("提交成功，审核成功");
          that.chooseTypeShow = false;
          that.companyInfoShow = false;
          that.cBindPShow = true;
          that.cBindBShow = false;
          that.psignShow = false;
          that.getPhone();
        });
      } else if (ctype === "5") {
        that.chooseTypeShow = false;
        that.companyInfoShow = false;
        that.cBindPShow = true;
        that.cBindBShow = false;
        that.psignShow = false;
        //企业绑定手机号
        //   if(!that.smsCode){
        //       that.$message.info("验证码不能为空");
        //       return false;
        //   }
        //   if(!that.mobile){
        //       that.$message.info("手机号不能为空");
        //       return false;
        //   }
        //   authenticationApi.individualBindingMobilePhoneNumber(that.smsCode,that.mobile).then(res=>{
        //       console.log(res)
        //   })
        //   that.chooseTypeShow = false;
        //  that.companyInfoShow = false;
        //  that.cBindPShow = false;
        //  that.cBindBShow = true;
        //  that.psignShow = false;
      } else if (ctype === "6") {
        //企业手机号绑定
        //企业绑定手机号
        if (!that.smsCode) {
          that.$message.info("验证码不能为空");
          return false;
        }
        if (!that.mobile) {
          that.$message.info("手机号不能为空");
          return false;
        }
        authenticationApi
          .individualBindingMobilePhoneNumber(that.smsCode, that.mobile)
          .then((res) => {
            that.chooseTypeShow = false;
            that.companyInfoShow = false;
            that.cBindPShow = false;
            that.cBindBShow = true;
            that.psignShow = false;
            that.getBandk();
          });
      } else if (ctype === "7") {
        that.confrimBindBankInfo01();
      }
    },
    //上一步
    perpStep(ctype) {
      const that = this;
      that.$router.push({ path: "/" });
      //   if (ctype === "1") {
      //     that.chooseTypeShow = true;
      //     that.formpanelShow = false;
      //     that.pBindPhoneShow = false;
      //     that.pBindBanKShow = false;
      //     that.psignShow = false;
      //   }
    },
    //发送验证码
    getSmsClick(ctype, mobile) {
      this.disabledSmsClick = true;
      if (!this.mobile) {
        this.$message.info("手机号不能为空");
        this.disabledSmsClick = false;
        return false;
      }
      authenticationApi.sendSmg(this.mobile).then((res) => {
        this.$message.success("短信验证码发送成功，请注意查收");

        const TIME_COUNT = 60;
        if (!this.timer) {
          this.count = TIME_COUNT;
          this.show = false;
          this.timer = setInterval(() => {
            if (this.count > 0 && this.count <= TIME_COUNT) {
              this.count--;
            } else {
              this.show = true;
              this.disabledSmsClick = false;
              clearInterval(this.timer);
              this.timer = null;
            }
          }, 1000);
        }
      });
    },
    //获取银行卡请求
    bindBankInfo() {
      const that = this;

      if (!that.pMobile) {
        that.$message.info("请输入银行上的手机号");
        return false;
      }
      if (!that.bankCard) {
        that.$message.info("请输入银行上的手机号");
        return false;
      }
      that.disabledgetSms = true;
      authenticationApi
        .requestToBindBankCard(that.bankCard, that.pMobile)
        .then((res) => {
          that.$message.success("短信验证码发送成功，请注意查收");
          const TIME_COUNT = 60;
          if (!that.timer) {
            that.countCode = TIME_COUNT;
            that.show = false;
            that.timer = setInterval(() => {
              if (that.countCode > 0 && that.countCode <= TIME_COUNT) {
                that.countCode--;
              } else {
                that.show = true;
                that.disabledgetSms = false;
                clearInterval(this.timer);
                that.timer = null;
              }
            }, 1000);
          }
        });
    },
    //企业获取银行卡请求
    bindBankInfo01() {
      const that = this;

      if (!that.cBankCard1) {
        that.$message.info("请输入对私账号");
        return false;
      }
      if (!that.bankMoblie) {
        that.$message.info("请输入银行上的手机号");
        return false;
      }

      that.disabledgetSms = true;
      authenticationApi
        .requestToBindBankCard(that.cBankCard1, that.bankMoblie)
        .then((res) => {
          that.$message.success("短信验证码发送成功，请注意查收");
          const TIME_COUNT = 60;
          if (!that.timer) {
            that.countCode = TIME_COUNT;
            that.show = false;
            that.timer = setInterval(() => {
              if (that.countCode > 0 && that.countCode <= TIME_COUNT) {
                that.countCode--;
              } else {
                that.show = true;
                that.disabledgetSms = false;
                clearInterval(this.timer);
                that.timer = null;
              }
            }, 1000);
          }
        });
    },
    //签约
    goSign() {
      //电子签约
      authenticationApi.electronicContract().then((res) => {
        console.log(res+"123");
      window.localStorage.setItem("link",res.data.data);
        this.$router.push({
        path: "/system/link"
      });
       // window.location.href = res.data.data;
         //this.$message.success("签约请求发送成功，请注意");
      });
    },
    //提交绑定银行卡
    confrimBindBankInfo() {
      const that = this;

      if (!that.bankCard) {
        that.$message.info("银行开号不能为空");
        return false;
      }
      if (!that.pMobile) {
        that.$message.info("手机号不能为空");
        return false;
      }
      if (!that.pSmsCode) {
        that.$message.info("验证码不能为空");
        return false;
      }
      authenticationApi
        .confirmBindingBankCard(that.bankCard, that.pMobile, that.pSmsCode)
        .then((res) => {
          that.chooseTypeShow = false;
          that.formpanelShow = false;
          that.pBindPhoneShow = false;
          that.pBindBanKShow = false;
          that.psignShow = true;

          that.sgain();
        });
    },
    confrimBindBankInfo01() {
      const that = this;

      if (!that.cBankCard1) {

        that.$message.info("银行开号不能为空");
        return false;
      }
      if (!that.bankMoblie) {
        that.$message.info("手机号不能为空");
        return false;
      }
      if (!that.pSmsCode) {
        that.$message.info("验证码不能为空");
        return false;
      }
      authenticationApi

        .confirmBindingBankCard(that.cBankCard1, that.bankMoblie, that.pSmsCode)

        .then((res) => {
          that.chooseTypeShow = false;
          that.companyInfoShow = false;
          that.cBindPShow = false;
          that.cBindBShow = false;
          that.cSignShow = true;

          that.sgain();
        });
    },
    //修改密码
    save(ele) {
      this.$refs[ele].validate((valid) => {
        // 表单验证
        if (valid) {
          systemApi.updatePassWord(this.dataQuery).then((res) => {
            this.dataQuery = {};
            this.$message.success("修改成功");
            this.$store.dispatch("LogOut").then(() => {
              location.reload(); // In order to re-instantiate the vue-router object to avoid bugs
            });
          });
        } else {
          console.log("error submit!!");
          //loading.close();
          return false;
        }
      });
    },
    //删除
    deletePassPwd(ele) {
      this.dataQuery = {};
    },
    //上传图片开始
    // 删除上传图片
    handleRemoveDetail(file, fileList) {
      const that = this;

      // if (!that.addOrUpdate) {
      //that.fileDetailList = fileList;
      // } else {
      that.uniCreditImg = "";

      for (let a = 0; a < that.fileDetailListPending.length; a++) {
        that.fileDetailListPending[a].uid === file.uid
          ? that.fileDeatiList.splice(a, 1)
          : "";
      }
      // }
    },
    handleRemoveDetail01(file, fileList) {
      const that = this;

      // if (!that.addOrUpdate) {
      //that.fileDetailList = fileList;
      // } else {
      that.idCardFrontImg = "";

      for (let a = 0; a < that.fileDetailListPending.length; a++) {
        that.fileDetailListPending[a].uid === file.uid
          ? that.fileDeatiList.splice(a, 1)
          : "";
      }
      // }
    },
    handleRemoveDetail02(file, fileList) {
      const that = this;

      // if (!that.addOrUpdate) {
      //that.fileDetailList = fileList;
      // } else {
      that.idCardBackImg = "";

      for (let a = 0; a < that.fileDetailListPending.length; a++) {
        that.fileDetailListPending[a].uid === file.uid
          ? that.fileDeatiList.splice(a, 1)
          : "";
      }
      // }
    },
    // 上传图片预览
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url;
      this.dialogUploadVisible = true;
    },
    // 上传图片数量超限
    justPictureNum(file, fileList) {
      this.$message.warning(`当前限制选择1个文件`);
    },
    // 上传图片请求
    uploadDetailHttp({ file }) {
      this.uploadLoading = true;
      const that = this;
      const fileName = "manage/" + Date.parse(new Date());
      that.$nextTick(function () {
        ossPrClient()
          .put(fileName, file)
          .then(({ res, url, name }) => {
            if (res && res.status === 200) {
              console.log(`阿里云OSS上传图片成功回调1`, res, url, name);
              // if (!that.addOrUpdate) {
              //   that.fileList.push({
              //     uid: file.uid,
              //     url: url,
              //   });
              //   } else { // 新增上传图片
              that.fileList.push({
                name,
              });
              that.uniCreditImg = name;
              //   }
              that.$nextTick(() => {
                that.uploadLoading = false;
              });
            }
          })
          .catch((err) => {
            that.$message.error("上传图片失败请检查网络或者刷新页面");
            console.log(`阿里云OSS上传图片失败回调`, err);
          });
      });
    },
    // 上传图片请求
    uploadDetailHttp01({ file }) {
      this.uploadLoading01 = true;
      const that = this;
      const fileName = "manage/" + Date.parse(new Date());
      that.$nextTick(function () {
        ossPrClient()
          .put(fileName, file)
          .then(({ res, url, name }) => {
            if (res && res.status === 200) {
              console.log(`阿里云OSS上传图片成功回调1`, res, url, name);
              // if (!that.addOrUpdate) {
              //   that.fileList.push({
              //     uid: file.uid,
              //     url: url,
              //   });
              //   } else { // 新增上传图片
              that.fileList.push({
                name,
              });
              that.idCardFrontImg = name;
              //   }
              that.$nextTick(() => {
                that.uploadLoading01 = false;
              });
            }
          })
          .catch((err) => {
            that.$message.error("上传图片失败请检查网络或者刷新页面");
            console.log(`阿里云OSS上传图片失败回调`, err);
          });
      });
    },
    //显示完善个人信息
    getInfo() {
        const that=this;
      that.secondImg =
        "http://dx-applet-mall.oss-cn-shanghai.aliyuncs.com/manage/*************";
      that.thirdImg =
        "http://dx-applet-mall.oss-cn-shanghai.aliyuncs.com/manage/*************";
      that.fourthImg =
        "http://dx-applet-mall.oss-cn-shanghai.aliyuncs.com/manage/*************";
      that.fifthImg =
        "http://dx-applet-mall.oss-cn-shanghai.aliyuncs.com/manage/*************";
    },
    //显示绑定手机号
    getPhone() {
        const that=this;
      that.secondImg =
        "http://dx-applet-mall.oss-cn-shanghai.aliyuncs.com/manage/*************";
      that.thirdImg =
        "http://dx-applet-mall.oss-cn-shanghai.aliyuncs.com/manage/*************";
      that.fourthImg =
        "http://dx-applet-mall.oss-cn-shanghai.aliyuncs.com/manage/*************";
      that.fifthImg =
        "http://dx-applet-mall.oss-cn-shanghai.aliyuncs.com/manage/*************";
    },
    //显示绑定银行卡
    getBandk() {
        const that=this;
      that.secondImg =
        "http://dx-applet-mall.oss-cn-shanghai.aliyuncs.com/manage/1608694668000";
      that.thirdImg =
        "http://dx-applet-mall.oss-cn-shanghai.aliyuncs.com/manage/1608694633000";
      that.fourthImg =
        "http://dx-applet-mall.oss-cn-shanghai.aliyuncs.com/manage/1608694576000";
      that.fifthImg =
        "http://dx-applet-mall.oss-cn-shanghai.aliyuncs.com/manage/*************";
    },

    //显示签约
    sgain(){
      const that=this;
       that.secondImg =
        "http://dx-applet-mall.oss-cn-shanghai.aliyuncs.com/manage/1608694668000";
      that.thirdImg =
        "http://dx-applet-mall.oss-cn-shanghai.aliyuncs.com/manage/1608694633000";
      that.fourthImg =
        "http://dx-applet-mall.oss-cn-shanghai.aliyuncs.com/manage/1608694576000";

      that.fifthImg="http://dx-applet-mall.oss-cn-shanghai.aliyuncs.com/manage/1608946600000";
    },

    // 上传图片请求
    uploadDetailHttp02({ file }) {
      this.uploadLoading02 = true;
      const that = this;
      const fileName = "manage/" + Date.parse(new Date());
      that.$nextTick(function () {
        ossPrClient()
          .put(fileName, file)
          .then(({ res, url, name }) => {
            if (res && res.status === 200) {
              console.log(`阿里云OSS上传图片成功回调1`, res, url, name);
              // if (!that.addOrUpdate) {
              //   that.fileList.push({
              //     uid: file.uid,
              //     url: url,
              //   });
              //   } else { // 新增上传图片
              that.fileList.push({
                name,
              });
              that.idCardBackImg = name;
              //   }
              that.$nextTick(() => {
                that.uploadLoading02 = false;
              });
            }
          })
          .catch((err) => {
            that.$message.error("上传图片失败请检查网络或者刷新页面");
            console.log(`阿里云OSS上传图片失败回调`, err);
          });
      });
    },
    //上传图片结束
  },
};
</script>


<style scoped>
.recharge-form input {
  width: 185px;
}
.recharge-form textarea {
  width: 400px;
}
.recharge-form button {
  padding: 10px 40px;
}

.form-group form-group-sm {
  padding: 40px 10px;
}
.first {
  width: 60px;
  height: 60px;
  background: #fff2d5;
  border-radius: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0px auto 10px auto;
}

.process img {
  display: block;
  width: 40px;
  height: 50px;
  margin: 0 auto;
}

.process {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  color: #333;
  margin-top: 10px;
}

.processlist {
  display: flex;
  align-items: center;
  justify-content: center;
}

.line {
  width: 60px;
  height: 1px;
  border-bottom: 1px dashed #fead00;
  margin-top: -20px;
  margin-right: 15px;
}

.ml-10 {
  margin-left: 10px;
}

.unchecked .line {
  border-bottom: 1px dashed #b2b2b2 !important;
}

.unchecked .first {
  background: #f3f3f3 !important;
}

.unchecked {
  color: #b1b1b1 !important;
}

#chooseType,
#personalInfo,
#pBindPhone,
#pBindBanK,
#P_Sign,
#CompanyInfo,
#C_BindP,
#C_BindB,
#C_Sign {
  width: 760px;
  margin: 0 auto;
  margin-top: 40px;
  padding: 30px;
  border-radius: 5px;
  border: 1px solid #f9f9f9;
}

.mt-40 {
  margin-top: 30px;
}

.next {
  width: 100px;
  height: 40px;
  border-radius: 5px;
  background: #1c84c6;
  color: #fff;
  border: none;
  margin-top: 50px;
}

.previous {
  width: 100px;
  height: 40px;
  border-radius: 5px;
  background: #f8ac59;
  color: #fff;
  border: none;
  margin-top: 50px;
  margin-right: 20px;
}

.step {
  width: 220px;
  margin-left: 160px;
  margin-top: 20px;
}

#SubmitPersonl {
  margin-left: 220px;
}

.shuline {
  width: 5px;
  height: 20px;
  background: #1c84c6;
  border-radius: 20px;
  margin-right: 10px;
}

.top-add {
  display: flex;
  align-items: center;
  color: #1c84c6;
}

.row {
  margin-left: 0px !important;
  margin-right: 0px !important;
}
</style>
