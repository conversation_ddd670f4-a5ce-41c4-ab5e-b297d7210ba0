/**
 * 单词水平相关接口
 */
import request from '@/utils/request'

export default {
  memberPage(pageNum, pageSize, data) {
    return request({
      url: '/znyy/member/query/page/' + pageNum + '/' + pageSize,
      method: 'GET',
      params: data
    })
  },
  memberRank(){
    return request({
      url: '/znyy/member/get/rank',
      method: 'GET',
    })
  },
  memberDetail(id){
    return request({
      url: '/znyy/member/get/detail/'+id,
      method: 'GET',
    })
  },
  updateStatus(id) {
    return request({
      url: '/znyy/member/update/enable/status/' + id,
      method: 'PUT'
    })
  },

  getProvince(id){
     return request({
      url: '/znyy/barea/list/'+id,
        method: 'GET',
     })
   },

  updateMember(data) {
    return request({
      url: '/znyy/member/edit',
      method: 'PUT',
      data
    })
  },
  updateMemberRef(id,referrerCode) {
    return request({
      url: '/znyy/member/edit/referrer/'+id+"/"+referrerCode,
      method: 'PUT',
    })
  },
  exportMember(data){
    return request({
      url: '/znyy/member/to/excel',
      method: 'GET',
      params: data,
      responseType: 'blob',
    })
  }
}
