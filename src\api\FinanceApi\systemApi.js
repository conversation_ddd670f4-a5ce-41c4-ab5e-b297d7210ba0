import request from '@/utils/request'
// 渠道 ,甄选 分润配置
export const getProfitConfigPage = (data) => {
    return request({
        url: '/znyy/profit/config/page',
        method: 'GET',
        params: data
    })
}
//渠道配置编辑
export const profitConfigEdit = (data) => {
    return request({
        url: '/znyy/profit/config/edit',
        method: 'POST',
        data,
    })
}
//甄选删除
export const profitConfigDelete = (data) => {
    return request({
        url: '/znyy/profit/config/delete',
        method: 'POST',
        data,
    })
}
//商品列表
export const goodsList = (data) => {
    return request({ url: '/zxAdminCourse/web/goods/list', method: 'get', params: data });
}

//分页查询商品分类列表/zxAdminCourse/goods/category/list
export const goodsCategoryList = (data) => {
    return request({ url: '/zxAdminCourse/web/goods/category/list', method: 'get', params: data });
}


//添加课程进行分佣
export const addCourseConfig = (data) => {
    return request({
        // url: '/zxAdminCourse/web/commissionConfig/courseConfigBatchAdd',
        url: '/znyy/profit/config/save',
        method: 'post',
        data
    });
}
//购买合同

export const getContractPay = (data) => {
    return request({

        url: '/znyy/merchant/contract/pay',
        method: 'get',
        params: data
    });
}
