<template>
  <div class="app-container">
    <el-form :model="dataQuery" ref="queryForm" :inline="true">
      <el-form-item label="ID查询：" prop="id">
        <el-input v-model="dataQuery.id" type="number" clearable placeholder="请输入ID"></el-input>
      </el-form-item>
      <el-form-item label="课程名称：" prop="name">
        <el-input v-model="dataQuery.name" clearable placeholder="请输入课程名称"></el-input>
      </el-form-item>
      <el-form-item label="学段：" prop="formal">
        <el-select v-model="dataQuery.grade" clearable>
          <el-option v-for="item in gradeList" :key="item" :label="item" :value="item">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="课程类型：" prop="formal">
        <el-select v-model="dataQuery.formal" clearable>
          <el-option v-for="(item, index) in courseTypeList" :key="index" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="时间查询：">
        <el-date-picker
          v-model="timeDate"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="queryData">查询</el-button>
        <el-button type="primary" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-col :span="24" style="margin-bottom: 30px">
      <el-button type="success" icon="el-icon-plus" size="mini" @click="handleAdd()">新增</el-button>
    </el-col>
    <el-table class="common-table" v-loading="tableLoading" :data="tableData" style="width: 100%;margin-bottom: 20px;"
      row-key="id" border default-expand-all>
      <el-table-column prop="id" label="ID"></el-table-column>
      <el-table-column prop="courseName" label="课程名称" show-overflow-tooltip></el-table-column>
      <el-table-column prop="withdrawnBonus" label="操作" width="480">
        <template slot-scope="scope">
          <el-button type="success" size="mini" icon="el-icon-edit-outline" @click="handleUpdate(scope.row.id)">编辑</el-button>
          <el-button type="success" size="mini" icon="el-icon-edit-outline" @click="addQuesToCourse(scope.row.id)">课程题型</el-button>
          <el-button type="success" size="mini" icon="el-icon-edit-outline" @click="subQues(scope.row.id)">附加题型</el-button>
          <el-button type="danger" size="mini" icon="el-icon-delete" @click="handleDelete(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="grade" label="学段" />
      <el-table-column prop="num" label="题数" />
      <el-table-column prop="formal" label="课程类型" >
        <template slot-scope="scope">
          {{ scope.row.formal==0?"评估课程":(scope.row.formal==1?"体验课程":"正式课程")}}
        </template>
      </el-table-column>
      <el-table-column  prop="orderNum" label="排序" />
      <el-table-column  prop="createTime" label="添加时间" />
    </el-table>
    <!-- 分页 -->
    <el-col :span="24" style="margin-bottom: 20px">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>

    <!-- 修改或者新增弹窗 -->
    <el-dialog :title="isEdit?'编辑课程':'添加课程'" :visible.sync="open" width="70%" @close="cancel">
      <el-form ref="form" :model="form" :rules="rules" label-width="120px" style="width: 70%;">
        <el-form-item label="课程名称：" prop="courseName">
          <el-input v-model="form.courseName" />
        </el-form-item>
        <el-form-item label="课程类型：" prop="formal">
          <el-select v-model="form.formal">
            <el-option v-for="(item, index) in courseTypeList" :key="index" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="选择学段：" prop="grade">
          <el-select v-model="form.grade">
            <el-option v-for="(item, index) in gradeList" :key="index" :label="item" :value="item">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="排序" prop="orderNum">
          <el-input-number v-model="form.orderNum" controls-position="right" :min="1" :step="1" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import courseQuestionApi from "@/api/abacusMentalCalc/courseQuestion";
import { pageParamNames } from '@/utils/constants'
import categoryApi from "@/api/abacusMentalCalc/category";

export default {
  data() {
    return {
      timeDate:null,
      dataQuery: {
        id:"",
        name:"",
        grade:null,
        formal:null,
        startTime:"",
        endTime:""
      },

      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },

      tableData: [],

      open: false,
      isEdit:false,

      gradeList: [],
      courseTypeList:[
        {
          label:"评估课程",
          value:0
        },
        // {
        //   label:"体验课程",
        //   value:1
        // },
        {
          label:"正式课程",
          value:2
        },
      ],

      form: {},
      // 表单校验
      rules: {
        courseName: [{ required: true, message: '请输入名称', trigger: 'blur' }],
        orderNum: [{ required: true, message: '请输入', trigger: 'blur' }],
        grade: [{ required: true, message: '请选择', trigger: 'blur' }],
        formal: [{ required: true, message: '请选择', trigger: 'blur' }],
      }
    }
  },
  created() {
    this.getAllGrade();
    this.getList()
  },
  methods: {
    getAllGrade(){
      categoryApi.getGrade().then(res => {
        this.gradeList = res.data
      });
    },
    handleDelete(id) {
      this.$confirm('确定要删除吗？', '删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        courseQuestionApi.courseDelete(id).then(res => {
          this.$message.success('删除成功！')
          this.getList()
        })
      })
    },

    //分页查询
    getList() {
      this.loading = true
      this.dataQuery.pageNum = this.tablePage.currentPage
      this.dataQuery.pageSize = this.tablePage.size
      if(this.timeDate && this.timeDate.length > 0){
        this.dataQuery.startTime = this.getFormatTime(this.timeDate[0]);
        this.dataQuery.endTime = this.getFormatTime(this.timeDate[1]);
      }
      courseQuestionApi.courseList(this.dataQuery).then(res => {
        this.tableData = res.data.data
        this.loading = false
        // 设置后台返回的分页参数
        pageParamNames.forEach(name =>
          this.$set(this.tablePage, name, parseInt(res.data[name]))
        )
      })
    },
    getFormatTime(time){
      let date = new Date(time);
      let year = date.getFullYear();
      let month = date.getMonth() + 1;  // getMonth()返回值是0~11
      let day = date.getDate();
      let hour = date.getHours();
      let minute = date.getMinutes();
      let second = date.getSeconds();

      let result = year + "-" + (month < 10 ? "0" + month : month) + "-" + (day < 10 ? "0" + day : day) + " " + (hour < 10 ? "0" + hour : hour) + ":" + (minute < 10 ? "0" + minute : minute) + ":" + (second < 10 ? "0" + second : second);
      return result
    },
    queryData(){
      this.tablePage.currentPage = 1
      this.getList()
    },
    resetQuery(){
      this.timeDate = null;
      this.tablePage.currentPage = 1
      this.dataQuery= {
          id:"",
          name:"",
          grade:null,
          formal:null,
          startTime:"",
          endTime:""
      }
      this.getList()
    },

    handleUpdate(id) {
      this.reset()
      courseQuestionApi.courseDetails(id).then(res => {
        this.form = res.data
        this.isEdit = true
        this.open = true
      })
    },
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          this.form.type = "COURSE"
          courseQuestionApi.courseAdd(this.form).then(response => {
            this.$message.success('提交成功！')
            this.open = false
            this.getList()
          })
        }
      })
    },
    handleAdd() {
      this.reset()
      this.isEdit = false
      this.open = true
    },

    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        courseName: '',
        orderNum: null,
        grade: '',
        formal: null,
      }
    },

    addQuesToCourse(id){
      this.$router.push({ path: "/abacusMentalCalc/question", query: { id: id } });
    },

    subQues(id) {
      this.$router.push({ path: "/abacusMentalCalc/subQuestion", query: { id: id } });
    },

    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val
      this.getList()
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val
      this.getList()
    }
  },
}
</script>
