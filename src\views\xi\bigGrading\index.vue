<template>
  <div class="app-container">
    <el-form :inline="true" ref="queryForm" class="container-card" label-width="110px">
      <el-form-item label="段位名称：">
        <el-input v-model="dataQuery.gradingName" placeholder="请输入名称" clearable />
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-re" @click="resetQuery()">重置</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="search()">搜索</el-button>
      </el-form-item>
    </el-form>
    <el-col :span="24" style="margin-bottom: 30px">
      <el-button type="success" icon="el-icon-plus" size="mini" @click="addBtn()">新增</el-button>
    </el-col>
    <el-table class="common-table" v-loading="tableLoading" :data="tableData" style="width: 100%;margin-bottom: 20px;"
      row-key="id" border default-expand-all>
      <el-table-column prop="orderNum" label="序号" width="50"></el-table-column>
      <el-table-column prop="id" label="ID"></el-table-column>
      <el-table-column prop="withdrawnBonus" label="操作" width="220">
        <template slot-scope="scope">
          <el-button type="primary" size="mini" @click="editBtn(scope.row.id)">编辑
          </el-button>
          <el-button type="danger" size="mini" @click="handleDelete(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="gradingName" label="段位名称" show-overflow-tooltip></el-table-column>
      <el-table-column prop="gradingSmallPhoto" label="段位标识图片" align="center">
        <template slot-scope="scope">
          <el-image style="width: 60px;height: 60px" :src="scope.row.gradingIdentPhoto"
            :preview-src-list="[scope.row.gradingIdentPhoto]" v-if="scope.row.gradingIdentPhoto != ''" class="img-lg" />
        </template>
      </el-table-column>
      <el-table-column prop="gradingSmallPhoto" label="段位标识未点亮图片" align="center">
        <template slot-scope="scope">
          <el-image style="width: 60px;height: 60px" :src="scope.row.gradingIdentNoLightPhoto"
            :preview-src-list="[scope.row.gradingIdentNoLightPhoto]" v-if="scope.row.gradingIdentNoLightPhoto != ''"
            class="img-lg" />
        </template>
      </el-table-column>
      <el-table-column prop="gradingDanmedalPhoto" label="段位奖章图片" align="center">
        <template slot-scope="scope">
          <el-image style="width: 60px;height: 60px" :src="scope.row.gradingDanmedalPhoto"
            :preview-src-list="[scope.row.gradingDanmedalPhoto]" v-if="scope.row.gradingDanmedalPhoto != ''"
            class="img-lg" />
        </template>
      </el-table-column>
      <el-table-column prop="gradingSmallPhoto" label="段位小图片" align="center">
        <template slot-scope="scope">
          <el-image :src="scope.row.gradingSmallPhoto" :preview-src-list="[scope.row.gradingSmallPhoto]" class="img-lg" />
        </template>
      </el-table-column>
      <el-table-column prop="gradingBigPhoto" label="段位大图片" align="center">
        <template slot-scope="scope">
          <el-image style="width: 150px; height: 70px" :src="scope.row.gradingBigPhoto"
            :preview-src-list="[scope.row.gradingBigPhoto]" class="img-lg" />
        </template>
      </el-table-column>
      <el-table-column prop="personalCenterPhoto" label="个人中心段位背景图" align="center">
        <template slot-scope="scope">
          <el-image style="width: 150px; height: 70px" :src="scope.row.personalCenterPhoto"
            :preview-src-list="[scope.row.personalCenterPhoto]" v-if="scope.row.personalCenterPhoto != ''" class="img-lg" />
        </template>
      </el-table-column>
      <el-table-column prop="gradingBackboardPhoto" label="段位背板图" align="center">
        <template slot-scope="scope">
          <el-image style="width: 150px; height: 70px" :src="scope.row.gradingBackboardPhoto"
            :preview-src-list="[scope.row.gradingBackboardPhoto]" v-if="scope.row.gradingBackboardPhoto != ''"
            class="img-lg" />
        </template>
      </el-table-column>
      <el-table-column prop="schoolSeasonBackPhoto" label="学季背景" align="center">
        <template slot-scope="scope">
          <el-image style="width: 150px; height: 70px" :src="scope.row.schoolSeasonBackPhoto"
            :preview-src-list="[scope.row.schoolSeasonBackPhoto]" v-if="scope.row.schoolSeasonBackPhoto != ''"
            class="img-lg" />
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <el-col :span="24" style="margin-bottom: 20px">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>

    <!-- 修改或者新增弹窗 -->
    <el-dialog title="编辑段位" :visible.sync="open" width="70%" @close="close">
      <el-form ref="form" :model="form" :rules="rules" label-width="120px" style="width: 70%;">
        <el-form-item label="段位名称：" prop="gradingName">
          <el-input v-model="form.gradingName" />
        </el-form-item>
        <el-form-item label="段位标识图片" prop="gradingIdentPhoto">
          <my-upload @handleSuccess="handleSuccess3" @handleRemove="handleRemove3" :file-list="fileList3" :showTip="false"
            :limit="1" :fullUrl="true" />
          <el-input type="hidden" v-model="form.gradingIdentPhoto"></el-input>
        </el-form-item>
        <el-form-item label="段位标识未点亮图片" prop="gradingIdentNoLightPhoto">
          <my-upload @handleSuccess="handleSuccess4" @handleRemove="handleRemove4" :file-list="fileList4" :showTip="false"
            :limit="1" :fullUrl="true" />
          <el-input type="hidden" v-model="form.gradingIdentNoLightPhoto"></el-input>
        </el-form-item>
        <el-form-item label="段位奖章图片" prop="gradingDanmedalPhoto">
          <my-upload @handleSuccess="handleSuccess6" @handleRemove="handleRemove6" :file-list="fileList6" :showTip="false"
            :limit="1" :fullUrl="true" />
          <el-input type="hidden" v-model="form.gradingDanmedalPhoto"></el-input>
        </el-form-item>
        <el-form-item label="段位小图片" prop="gradingSmallPhoto">
          <my-upload @handleSuccess="handleSuccess1" @handleRemove="handleRemove1" :file-list="fileList1" :showTip="false"
            :limit="1" :fullUrl="true" />
          <el-input type="hidden" v-model="form.gradingSmallPhoto"></el-input>
        </el-form-item>
        <el-form-item label="段位大图片" prop="gradingBigPhoto">
          <my-upload @handleSuccess="handleSuccess2" @handleRemove="handleRemove2" :file-list="fileList2" :showTip="false"
            :limit="1" :fullUrl="true" />
          <el-input type="hidden" v-model="form.gradingBigPhoto"></el-input>
        </el-form-item>
        <el-form-item label="个人中心段位背景图" prop="personalCenterPhoto">
          <my-upload @handleSuccess="handleSuccess5" @handleRemove="handleRemove5" :file-list="fileList5" :showTip="false"
            :limit="1" :fullUrl="true" />
          <el-input type="hidden" v-model="form.personalCenterPhoto"></el-input>
        </el-form-item>
        <el-form-item label="段位背板图" prop="gradingBackboardPhoto">
          <my-upload @handleSuccess="handleSuccess7" @handleRemove="handleRemove7" :file-list="fileList7" :showTip="false"
            :limit="1" :fullUrl="true" />
          <el-input type="hidden" v-model="form.gradingBackboardPhoto"></el-input>
        </el-form-item>
        <el-form-item label="学季背景" prop="schoolSeasonBackPhoto">
          <my-upload @handleSuccess="handleSuccess8" @handleRemove="handleRemove8" :file-list="fileList8" :showTip="false"
            :limit="1" :fullUrl="true" />
          <el-input type="hidden" v-model="form.schoolSeasonBackPhoto"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import bigGradingApi from '@/api/xi/bigGrading'
import { pageParamNames } from '@/utils/constants'
import MyUpload from "@/components/Upload/MyUpload";


export default {
  components: { MyUpload },
  name: 'bigGrading',
  data() {
    return {
      fileList1: [],
      fileList2: [],
      fileList3: [],
      fileList4: [],
      fileList5: [],
      fileList6: [],
      fileList7: [],
      dataQuery: {
        name: ''
      },
      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      open: false,
      tableData: [],
      form: {},
      // 表单校验
      rules: {
        gradingCode: [{ required: true, message: '请输入段位编号', trigger: 'blur' }],
        gradingName: [{ required: true, message: '请输入段位名称', trigger: 'blur' }],
        gradingSmallPhoto: [{ required: true, message: '段位小图片不能为空', trigger: 'blur' }],
        gradingBigPhoto: [{ required: true, message: '段位大图片不能为空', trigger: 'blur' }],
      }
    }
  },
  created() {
    this.getPageList()
  },
  methods: {
    search() {
      this.tablePage = {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      }
      this.getPageList()
    },
    handleDelete(id) {
      this.$confirm('确定要删除吗？', '删除段位', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        bigGradingApi.delete(id).then(res => {
          this.$message.success('删除成功！')
          this.getPageList()
        })
      })
    },
    addBtn() {
      this.reset();
      this.open = true;
    },
    editBtn(id) {
      this.reset();
      bigGradingApi.detail(id).then(res => {
        this.form = res.data;
        if (this.form.gradingSmallPhoto) {
          this.fileList1.push({ url: this.form.gradingSmallPhoto })
        }
        if (this.form.gradingBigPhoto) {
          this.fileList2.push({ url: this.form.gradingBigPhoto })
        }
        if (this.form.gradingIdentPhoto) {
          this.fileList3.push({ url: this.form.gradingIdentPhoto })
        }
        if (this.form.gradingIdentNoLightPhoto) {
          this.fileList4.push({ url: this.form.gradingIdentNoLightPhoto })
        }
        if (this.form.personalCenterPhoto) {
          this.fileList5.push({ url: this.form.personalCenterPhoto })
        }
        if (this.form.gradingDanmedalPhoto) {
          this.fileList6.push({ url: this.form.gradingDanmedalPhoto })
        }
        if (this.form.gradingBackboardPhoto) {
          this.fileList7.push({ url: this.form.gradingBackboardPhoto })
        }
        this.witheredFile = []
        this.open = true;
      })
    },
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          console.log(this.form);
          bigGradingApi.saveOrUpdate(this.form).then(response => {
            this.$message.success('提交成功！')
            this.open = false
            this.getPageList()
          })
        }
      })
    },
    getPageList() {
      this.tableLoading = true
      this.dataQuery.pageNum = this.tablePage.currentPage
      this.dataQuery.pageSize = this.tablePage.size
      bigGradingApi.list(this.dataQuery).then(res => {
        this.tableData = res.data.data
        this.tableLoading = false
        // 设置后台返回的分页参数
        pageParamNames.forEach(gradingName =>
          this.$set(this.tablePage, gradingName, parseInt(res.data[gradingName]))
        )
      })
    },
    handlePage() {
      (this.tablePage = {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      }),
        this.getPageList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dataQuery = {
        gradingName: ''
      }
      this.getPageList()
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val
      this.getPageList()
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val
      this.getPageList()
    },
    close() {
      this.open = false
      this.reset()
    },
    handleSuccess1(url) {
      this.form.gradingSmallPhoto = url;
    },
    handleRemove1() {
      this.form.gradingSmallPhoto = '';
    },
    handleSuccess2(url) {
      this.form.gradingBigPhoto = url;
    },
    handleRemove2() {
      this.form.gradingBigPhoto = '';
    },
    handleSuccess3(url) {
      this.form.gradingIdentPhoto = url;
    },
    handleRemove3() {
      this.form.gradingIdentPhoto = '';
    },
    handleSuccess4(url) {
      this.form.gradingIdentNoLightPhoto = url;
    },
    handleRemove4() {
      this.form.gradingIdentNoLightPhoto = '';
    },
    handleSuccess5(url) {
      this.form.personalCenterPhoto = url;
    },
    handleRemove5() {
      this.form.personalCenterPhoto = '';
    },
    handleSuccess6(url) {
      this.form.gradingDanmedalPhoto = url;
    },
    handleRemove6() {
      this.form.gradingDanmedalPhoto = '';
    },
    handleSuccess7(url) {
      this.form.gradingBackboardPhoto = url;
    },
    handleRemove7() {
      this.form.gradingBackboardPhoto = '';
    },
    handleSuccess8(url) {
      this.form.schoolSeasonBackPhoto = url;
    },
    handleRemove8() {
      this.form.schoolSeasonBackPhoto = '';
    },
    reset() {
      this.form = {
        gradingCode: '',
        gradingName: '',
        personalCenterPhoto: '',
        gradingIdentNoLightPhoto: '',
        gradingIdentPhoto: '',
        gradingDanmedalPhoto: '',
        gradingBackboardPhoto: '',
        schoolSeasonBackPhoto: '',
        gradingSmallPhoto: null,
        gradingBigPhoto: null
      }
      this.fileList1 = []
      this.fileList2 = []
      this.fileList3 = []
      this.fileList4 = []
      this.fileList5 = []
      this.fileList6 = []
      this.fileList7 = []
      this.fileList8 = []
      if (this.$refs['form']) {
        this.$refs['form'].resetFields();
      }
    }
  },
}
</script>

<style>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.period-table td,
.period-table th {
  text-align: center;
}

@media (max-width: 767px) {
  .el-message-box {
    width: 80% !important;
  }
}

.el-tooltip__popper {
  max-width: 800px;
}
</style>
