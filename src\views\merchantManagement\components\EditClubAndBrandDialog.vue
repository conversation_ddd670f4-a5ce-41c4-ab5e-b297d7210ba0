<template>
  <div>
    <el-dialog title="修改所属品牌" center :visible.sync="dialogVisible" width="700px" @close="handleOuterClose" :close-on-press-escape="false" :close-on-click-modal="false">
      <el-form ref="channerManagerRef" :label-position="labelPosition" :model="form" label-width="180px" :rules="rules">
        <el-form-item label="俱乐部名称" prop="merchantName">
          <el-input disabled v-model="form.merchantName"></el-input>
        </el-form-item>
        <el-form-item label="原所属品牌" prop="brandName">
          <el-input disabled v-model="form.brandName"></el-input>
        </el-form-item>
        <el-form-item label="原渠道合作伙伴" prop="operationsName">
          <el-input disabled v-model="form.operationsName"></el-input>
        </el-form-item>
        <el-form-item label="新所属品牌编号" prop="newBrandCode">
          <el-input v-model.trim="form.newBrandCode" maxlength="30" placeholder="请输入所属品牌编号" show-word-limit @blur="handleRefereeCodeBlur"></el-input>
        </el-form-item>
        <el-form-item label="新所属品牌名称" prop="newOperationsName">
          <el-input disabled v-model="form.newOperationsName"></el-input>
        </el-form-item>
        <el-form-item label="新渠道合作伙伴编号" prop="newChannelCode">
          <el-input v-model.trim="form.newChannelCode" placeholder="请输入新渠道合作伙伴编号" maxlength="30" show-word-limit @blur="handleChannelCodeBlur"></el-input>
        </el-form-item>
        <el-form-item label="新渠道合作伙伴名称" prop="newRefereeName">
          <el-input disabled v-model="form.newRefereeName"></el-input>
        </el-form-item>
        <div style="color: red; text-align: center; margin-bottom: 20px">提示：新渠道合作伙伴需要为新所属品牌下已开通的俱乐部或新所属品牌编号</div>
        <div class="dialog-footer">
          <el-button @click="handleOuterClose">取 消</el-button>
          <el-button :loading="loading" type="primary" @click="handleConfirm">确 定</el-button>
        </div>
      </el-form>
    </el-dialog>
    <InabilityReason
      v-if="dialogReasonVisible"
      :dialogReasonVisible.sync="dialogReasonVisible"
      @handleSubmit="handleSubmit"
      :showTitleStatus="showTitleStatus"
      :workStep="workStep"
      :reasonType="reasonType"
      :reasonContent="reasonContent"
      :isSubmitLoading="isSubmitLoading"
      @close="handleOuterClose"
    />
  </div>
</template>

<script>
  import InabilityReason from '@/views/merchantManagement/components/InabilityReason.vue';
  import dealerListApi from '@/api/operationsList';
  import schoolApi from '@/api/schoolList';

  export default {
    name: 'EditChannelManager',
    components: {
      InabilityReason
    },
    props: {
      isShowDialog: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        form: {
          merchantName: '', //门店名称
          operationsName: '', //原所属俱乐部名称
          refereeName: '', //推广大使名称
          refereeCode: '', //推广大使编号
          newChannelCode: '', //新推广大使编号
          newRefereeName: '', //新推广大使名称
          newOperationsName: '', //新俱乐部名称
          newBrandCode: '' //新俱乐部编号
        },
        merchantCode: '',
        merchantId: '',
        loading: false,
        labelPosition: 'left',
        rules: {
          newBrandCode: [{ required: true, message: '新所属品牌编号不为空', trigger: ['blur', 'change'] }],
          newOperationsName: [{ required: true, message: '新所属品牌名称不为空', trigger: ['blur', 'change'] }],
          newChannelCode: [{ required: true, message: '新渠道合作伙伴编号不为空', trigger: ['blur', 'change'] }],
          newRefereeName: [{ required: true, message: '新渠道合作伙伴名称不为空', trigger: ['blur', 'change'] }]
        },
        dialogReasonVisible: false,
        reasonContent: [],
        workStep: 1,
        showTitleStatus: 1,
        isSubmitLoading: false,
        reasonType: 0
      };
    },
    computed: {
      dialogVisible: {
        get() {
          return this.isShowDialog;
        },
        set(val) {
          this.$emit('update:isShowCityDialog', val);
        }
      }
    },

    methods: {
      async handleRefereeCodeBlur() {
        try {
          if (!this.form.newBrandCode) return;
          const res = await schoolApi.getMerchantNameApi({ merchantCode: this.form.newBrandCode });
          if (res.code === 20000) {
            this.$set(this.form, 'newOperationsName', res.data);
          }
        } catch (error) {
          console.log(`🚀🥶💩🚀~ handleRefereeCodeBlur ~ error ~ L112: `, error);
          this.$set(this.form, 'newOperationsName', '');
        }
      },

      async handleChannelCodeBlur() {
        try {
          if (!this.form.newChannelCode) return;
          const res = await schoolApi.getMerchantNameApi({ merchantCode: this.form.newChannelCode });
          if (res.code === 20000) {
            this.$set(this.form, 'newRefereeName', res.data);
          }
        } catch (error) {
          console.log(`🚀🥶💩🚀~ handleChannelCodeBlur ~ error ~ L126: `, error);
          this.$set(this.form, 'newRefereeName', '');
        }
      },

      async handleSubmit() {
        try {
          this.isSubmitLoading = true;
          const params = {
            merchantCode: this.form.merchantCode,
            newBrandCode: this.form.newBrandCode,
            newChannelCode: this.form.newChannelCode
          };
          const res = await dealerListApi.confirmChangeBrand(params);
          if (res.code === 20000) {
            this.$message.success('操作成功');
            this.loading = false;
            this.isSubmitLoading = false;
            this.dialogReasonVisible = false;
            // this.reset();
            this.$emit('close', true);
          }
        } catch (error) {
          console.log(`🚀🥶💩🚀~  ~ error ~ L141: `, error);
          this.isSubmitLoading = false;
          this.dialogReasonVisible = false;
          this.loading = false;
        }
      },

      handleOuterClose(val) {
        if (val === 'closeDialog') {
          this.dialogReasonVisible = false;
          return;
        }
        this.reset();
        this.$emit('handleCancel');
      },

      async handleConfirm() {
        if (this.loading) return;
        try {
          await this.$refs.channerManagerRef.validate(async (valid) => {
            if (!valid) return;
            this.loading = true;
            let params = {
              merchantCode: this.form.merchantCode,
              newBrandCode: this.form.newBrandCode,
              newChannelCode: this.form.newChannelCode
            };
            const res = await dealerListApi.checkNewBrandAndChanel(params);
            if (!res.data.canChange) {
              this.dialogReasonVisible = true;
              this.reasonContent = res.data.reasons || [];
              this.workStep = res.data.step;
              this.showTitleStatus = 3;
              this.loading = false;
              return;
            }
            this.loading = false;
            this.dialogReasonVisible = true;
            this.workStep = 4;
            this.reasonType = 2;
          });
        } catch (error) {
          console.log('🚀 ~ handleConfirm ~ error:', error);
          this.$message.error('操作失败');
        } finally {
          this.loading = false;
        }
      },

      setData(data) {
        Object.assign(this.form, data);
      },

      reset() {
        this.form = {
          merchantName: '',
          operationsName: '',
          refereeName: '',
          refereeCode: '',
          newChannelCode: '',
          newRefereeName: '',
          newOperationsName: '',
          newBrandCode: ''
        };
        this.$refs.channerManagerRef.resetFields();
      }
    }
  };
</script>

<style scoped>
  .dialog-footer {
    text-align: center;
  }
</style>
