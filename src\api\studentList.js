/**
 * 学员管理相关接口
 */
import request from '@/utils/request';

export default {
  // 分页查询
  studentList(pageNum, pageSize, data, phone) {
    return request({
      url: '/znyy/student/list/learner/' + pageNum + '/' + pageSize,
      method: 'GET',
      params: data,
      headers: {
        'User-Phone': phone
      }
    });
  },

  //待完善上课信息表
  selStudentContactInfoPage(data, phone) {
    return request({
      url: '/deliver/web/student/contact/info/selStudentContactInfoPage',
      method: 'GET',
      params: data,
      headers: {
        'User-Phone': phone
      }
    });
  },

  //获取上课信息表详情
  getStudentContactInfoDetail(data) {
    return request({
      url: '/deliver/web/student/contact/info/getStudentContactInfoDetail',
      method: 'GET',
      params: data
    });
  },

  //提交上课信息表
  submitStudentContactInfo(data) {
    return request({
      url: '/deliver/web/student/contact/info/submitStudentContactInfo',
      method: 'POST',
      data
    });
  },
  //修改上课信息表
  updateStudentContactInfo(data) {
    return request({
      url: '/deliver/web/student/contact/info/updateStudentContactInfo',
      method: 'POST',
      data
    });
  },

  //上课信息表获取试课词汇量
  getTestVocabularyet(data) {
    return request({
      url: '/znyy/course/sel/last/test',
      method: 'get',
      params: data
    });
  },

  // 复习时间表
  selStudentReviewTimeInfoList(data) {
    return request({
      url: '/deliver/web/student/reviewTime/info/selStudentContactInfoPage',
      method: 'GET',
      params: data
    });
  },

  // 获取复习时间表详情
  getStudentReviewTimeInfoDetail(data) {
    return request({
      url: '/deliver/web/student/reviewTime/info/getStudentReviewTimeInfoDetail',
      method: 'GET',
      params: data
    });
  },

  // 填写复习时间表
  submitStudentReviewTime(data) {
    return request({
      url: '/deliver/web/student/reviewTime/info/submitStudentReviewTime',
      method: 'POST',
      data
    });
  },

  //获取学员剩余鼎英语课时和剩余交付课时
  queryTopStudentCourse(merchantCode, studentCode) {
    return request({
      url: '/znyy/student/list/queryTopStudentCourse?merchantCode=' + merchantCode + '&studentCode=' + studentCode,
      method: 'GET'
    });
  },

  //课时转换
  courseTransition(data) {
    return request({
      url: '/znyy/student/list/courseTransition',
      method: 'GET',
      params: data
    });
  },

  // 开通与暂停
  updateStatus(id, status) {
    return request({
      url: '/znyy/student/list/updateStatus?id=' + id + '&isEnable=' + status,
      method: 'PUT'
    });
  },
  archive(studentCode) {
    return request({
      url: '/znyy/archive?studentCode=' + studentCode,
      method: 'PUT'
    });
  },
  unarchive(studentCode) {
    return request({
      url: '/znyy/archive/unarchive?studentCode=' + studentCode,
      method: 'PUT'
    });
  },
  // 更新同步课堂
  moduleStatus(id, status) {
    return request({
      url: '/znyy/student/list/openSynchronousClassroom?id=' + id + '&useModule=' + status,
      method: 'PUT'
    });
  },
  // 更新区域限制
  areaStatus(id, status) {
    return request({
      url: '/znyy/student/list/updateAreaRestrictions?id=' + id + '&restrictedUse=' + status,
      method: 'PUT'
    });
  },
  // 导出
  studentExport(listQuery) {
    return request({
      url: '/znyy/student/list/export/list',
      method: 'GET',
      responseType: 'blob',
      params: listQuery
    });
  },
  //为学员指定学管师
  assignLearnTube(data) {
    return request({
      url: '/znyy/student/list/assignLearnTube',
      method: 'PUT',
      params: data
    });
  },
  handOverToSuperior(data) {
    return request({
      url: '/znyy/student/list/handOverToSuperior',
      method: 'PUT',
      params: data
    });
  },
  initQrCode(data) {
    return request({
      url: '/deliver/web/student/contact/info/getSubscribeQrCode',
      method: 'GET',
      params: data
    });
  },

  getParentByMobile(data) {
    return request({
      url: '/deliver/web/student/contact/info/getParentByMobile',
      method: 'GET',
      params: data
    });
  },
  // 明文显示手机号码
  getShowPhone(smsCode, phone) {
    return request({
      url: '/znyy/student/list/checkDXCode?smsCode=' + smsCode + '&phone=' + phone,
      method: 'GET'
    });
  },
  // 查询是否需要充值试课奖励
  getPayExpReward(data) {
    return request({
      url: '/deliver/web/experience/getPayExpRewardForZx',
      method: 'GET',
      params: data
    });
  },
  // 充值试课奖励
  expRewardSave(data) {
    return request({
      url: '/znyy/areas/student/charge/expReward/save/zx',
      method: 'POST',
      data
    });
  }
};
