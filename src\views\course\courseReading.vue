<template>
  <div class="app-container">
    <!-- 搜索 -->
    <el-form :inline="true" ref="form" class="SearchForm">
      <el-form-item label="所属课程:">
        <router-link :to="{ path: 'courseList' }" class="blue lh36">{{ courseName }}</router-link>
      </el-form-item>
      <el-form-item label="文章标题:">
        <el-col :span="16">
          <el-input placeholder="请输入文章标题：" clearable v-model="dataQuery.topicTitle"
            @keyup.enter.native="fetchData()"></el-input>
        </el-col>
      </el-form-item>
      <el-form-item label="题目难度:">
        <el-select v-model="dataQuery.level" filterable value-key="value" placeholder="请选择">
          <el-option v-for="(item, index) in options" :key="index" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-button type="warning" icon="el-icon-search" @click="fetchData01()">搜索</el-button>
    </el-form>

    <div class="SearchForm">
      <!-- 添加 -->
      <div class="btn-add">
        <el-button size="small" type="success" @click="goBack()">返回课程列表</el-button>
        <el-button size="small" type="primary" icon="el-icon-plus" @click="clickAdd">添加</el-button>
      </div>
      <!-- 表格 -->
      <el-table class="course-table" :data="tableData" stripe border
        :default-sort="{ prop: 'addTime', order: 'descending' }">
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column prop="topicTitle" label="文章标题" sortable></el-table-column>
        <el-table-column prop="showName" label="显示名称" sortable></el-table-column>
        <el-table-column prop="id" label="操作" sortable width="300">
          <template slot-scope="scope">
            <el-button type="primary" size="mini" icon="el-icon-view" @click="openDetail(scope.row.id)">查看详情</el-button>
            <el-button type="success" size="mini" icon="el-icon-edit-outline" @click="openEdit(scope.row.id)">编辑</el-button>
            <el-button type="danger" size="mini" icon="el-icon-delete" @click="deleteReading(scope.row.id)">删除</el-button>
          </template>
        </el-table-column>
        <el-table-column prop="suggestedTime" label="建议时间（5分钟）" sortable>
        </el-table-column>
        <el-table-column prop="level" label="题目难度" sortable>
          <template slot-scope="scope">
            <el-tag v-if="scope.row.level == 1">★</el-tag>
            <el-tag v-if="scope.row.level == 2">★★</el-tag>
            <el-tag v-if="scope.row.level == 3">★★★</el-tag>
            <el-tag v-if="scope.row.level == 4">★★★★</el-tag>
            <el-tag v-if="scope.row.level == 5">★★★★★</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="sort" label="排序" sortable>
        </el-table-column>
        <el-table-column prop="isEnable" label="状态" sortable>
          <template slot-scope="scope">
            <span class="green" v-if="scope.row.isEnable === 1">开通</span>
            <span class="red" v-else>暂停</span>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <el-col :span="20">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>


  </div>
</template>

<script>
import courseReadingApi from "@/api/courseReading";
import {
  pageParamNames
} from "@/utils/constants";
export default {
  data() {
    return {
      categoryType: [], // 所属分类

      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null,
      },
      tableLoading: false,
      dataQuery: {
        level: "",
        topicTitle: ""
      },
      isRouterAlive: true, //局部刷新
      activeType: [], // 活动类型
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null,
      },
      tableData: [], //表格数据
      dialogVisible: false, // 修改弹窗是否展示


      rulesWordBook: {},

      addOrUpdate: true, // 是新增还是修改
      addReadingData: {}, // 新增阅读理解
      updateReadingData: {}, //修改阅读理解
      rules: {},
      radio: "0", //单选框状态 值必须是字符串
      memberId: undefined,
      courseName: "",
      courseCode: "",
      fd: "",
      //增加内容
      rulseReading: {
        topicTitle: [{
          required: true,
          message: "必填",
          trigger: "blur",
        },],
        showName: [{
          required: true,
          message: "必填",
          trigger: "blur",
        },],
        topicContent: [{
          required: true,
          message: "必填",
          trigger: "blur",
        },],
        suggestedTime: [{
          required: true,
          message: "必填",
          trigger: "blur",
        },],
        sort: [{
          required: true,
          message: "必填",
          trigger: "blur",
        },],
        level: [{
          required: true,
          message: "必填",
          trigger: "blur",
        },],
      },
      items: [{
        fillNumber: '',
        questionText: "",
        answerForA: "",
        answerForB: "",
        answerForC: "",
        answerForD: "",
        correctAnswer: "",
        answerRemark: ""
      }],
      rulesAnswers: {
        fillNumber: [{
          required: true,
          message: "必填",
          trigger: "blur",
        },],
        questionText: [{
          required: true,
          message: "必填",
          trigger: "blur",
        },],
        answerForA: [{
          required: true,
          message: "必填",
          trigger: "blur",
        },],
        answerForB: [{
          required: true,
          message: "必填",
          trigger: "blur",
        },],
        answerForC: [{
          required: true,
          message: "必填",
          trigger: "blur",
        },],
        answerForD: [{
          required: true,
          message: "必填",
          trigger: "blur",
        },],
        correctAnswer: [{
          required: true,
          message: "必填",
          trigger: "blur",
        },],
        answerRemark: [{
          required: true,
          message: "必填",
          trigger: "blur",
        },],

      },

      options: [{
        value: 1,
        label: '★'
      }, {
        value: 2,
        label: '★★'
      }, {
        value: 3,
        label: '★★★'
      }, {
        value: 4,
        label: '★★★★'
      }, {
        value: 5,
        label: '★★★★★'
      }],
    };
  },
  created() {
    this.fetchData();

  },
  methods: {
    fetchData01() {
      this.tablePage = {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      }
      this.fetchData();
    },
    // 查询表格列表
    fetchData() {
      const that = this;
      that.tableLoading = true;
      window.localStorage.getItem("courseContentTypeReading");
      that.courseName = window.localStorage.getItem("courseNameReading");
      that.courseCode = window.localStorage.getItem("courseCodeReading");
      that.addReadingData.courseCode = window.localStorage.getItem("courseCodeReading");
      that.dataQuery.courseCode = window.localStorage.getItem("courseCodeReading");
      courseReadingApi
        .courseReadingList(
          that.tablePage.currentPage,
          that.tablePage.size,
          that.dataQuery
        )
        .then((res) => {
          that.tableData = res.data.data;
          that.tableLoading = false;
          // 设置后台返回的分页参数
          pageParamNames.forEach((name) =>
            that.$set(that.tablePage, name, parseInt(res.data[name]))
          );
        });
    },


    //局部刷新
    reload() {
      this.isRouterAlive = false;
      this.$nextTick(function () {
        this.isRouterAlive = true;
      });
    },
    //添加操作
    clickAdd() {
      window.localStorage.setItem("courseReadingListCode", this.courseCode);
      window.localStorage.setItem("ourseReadingListCourseName", "增加");
      window.localStorage.setItem("addOrUpdateReadingList", JSON.stringify(true));
      window.localStorage.setItem("addOrUpdateReadingListId", "");
      this.$router.push({
        path: "/course/courseReadingList",
        query: {
          courseCode: this.courseCode,
          courseName: this.courseName,
          addOrUpdate: true
        }
      })
    },
    //查看详情
    openDetail(id) {
      window.localStorage.setItem("courseReadingDetailsCode", this.courseCode);
      window.localStorage.setItem("ourseReadingDetailsCourseName", this.courseName);
      window.localStorage.setItem("addOrUpdateReading", JSON.stringify(false));
      window.localStorage.setItem("addOrUpdateReadingId", id);
      this.$router.push({
        path: "/course/courseReadingDetails",
        query: {
          courseCode: this.courseCode,
          courseName: this.courseName,
          addOrUpdate: false,
          id: id
        }
      })

    },

    //删除阅读理解和完型填空
    deleteReading(id) {
      const that = this;
      this.$confirm('确定操作吗?', '删除数据', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        courseReadingApi.deleteReading(id).then(res => {
          that.fetchData01();
          that.$message.success('删除成功!')
        }).catch(err => {

        })
      }).catch(err => {

      })

    },

    // 打开编辑阅读理解和完型填空
    openEdit(id) {
      window.localStorage.setItem("courseReadingListCode", this.courseCode);
      window.localStorage.setItem("ourseReadingListCourseName", "编辑");
      window.localStorage.setItem("addOrUpdateReadingList", JSON.stringify(false));
      window.localStorage.setItem("addOrUpdateReadingListId", id);
      this.$router.push({
        path: "/course/courseReadingList",
        query: {
          courseCode: this.courseCode,
          courseName: "编辑",
          addOrUpdate: false,
          id: id
        }
      })

    },

    // 状态改变事件
    change(radio) {
      if (radio == "1") {
        this.addReadingData.isEnable = 1;
      } else {
        this.addReadingData.isEnable = 0;
      }
    },

    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val;
      this.fetchData();
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val;
      this.fetchData();
    },
    // 关闭弹窗
    close() {
      this.dialogVisible = false;
    },

    // 返回课程列表
    goBack() {
      this.$router.push({
        path: "/course/courseList",
      });
    },
    //新增
    handleAddFilterAttr() {
      this.filterProductAttrList.push({
        value: null,
        key: Date.now(),
      });
    },

  },
};
</script>

<style>
.lh36 {
  line-height: 36px;
  font-size: 14px;
}

.SearchForm {
  width: 100%;
  margin-bottom: 15px;
  background: #fff;
  border-radius: 6px;
  padding: 5px 0;
  padding-left: 5px;
  box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.2);
}

.btn-add {
  padding: 5px;
}

.red {
  color: red;
}

.green {
  color: green;
}

.course-table {
  text-align: center;
}

.course-table td,
.course-table th {
  padding: 5px 0;
  text-align: center;
}

.course-table button {
  padding: 2px;
}

.icon-stop {
  float: left;
  display: inline-block;
  width: 12px;
  height: 12px;
  background: url("../../icons/stop.png") no-repeat top center/contain;
}

.mt22 {
  margin-top: 22px;
}

.blue {
  margin-right: 50px;
  color: #409eff;
}

.clearfix {
  color: red;
}

@media (max-width:767px) {
  .el-message-box {
    width: 80% !important;
  }
}</style>
