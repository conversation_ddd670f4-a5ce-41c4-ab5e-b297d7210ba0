/**
 * 系统配置相关接口
 */
import request from '@/utils/request'
import { get } from 'jquery'

export default {
  // 分页查询
  systemList(pageNum, pageSize, data) {
    return request({
      url: '/znyy/bSysConfig/list/' + pageNum + '/' + pageSize,
      method: 'GET',
      params: data
    })
  },
  coursePriceList(pageNum, pageSize, data) {
    return request({
      url: '/znyy/bSysConfig/list/course/price/' + pageNum + '/' + pageSize,
      method: 'GET',
      params: data
    })
  },
  coursePriceLevel() {
    return request({
      url: '/znyy/bSysConfig/list/course/level/price',
      method: 'GET',
    })
  },
  // 新增
  addsystem(data) {
    return request({
      url: '/znyy/bSysConfig',
      method: 'POST',
      data
    })
  },
  getStudentCourseHours(params){
    return request({
      url: '/znyy/areas/student/getStudentCourseHours',
      method: 'GET',
      params
    })
  },
 
  // 新增
  addsystemCoursePrice(data) {
    return request({
      url: '/znyy/bSysConfig/saveCoursePrice',
      method: 'POST',
      data
    })
  },
  // 编辑
  updatesystem(data) {
    return request({
      url: '/znyy/appversion',
      method: 'PUT',
      data
    })
  },
  // 修改回显
  queryActive(id) {
    return request({
      url: '/znyy/bSysConfig/seeDetails/' + id,
      method: 'GET'
    })
  },
  // 修改回显
  queryDetail(configGroup) {
    return request({
      url: '/znyy/bSysConfig/detail',
      method: 'GET',
      params:{'configGroup':configGroup}
    })
  },
  // 修改回显
  queryCourseMax(city) {
    return request({
      url: '/znyy/bSysConfig/course/price/max',
      method: 'GET',
      params:{'city':city}
    })
  },
  // 修改回显
  queryCoursePrice(id) {
    return request({
      url: '/znyy/bSysConfig/seeDetails/course/price/' + id,
      method: 'GET'
    })
  },
  // 删除
  deletesystem(id) {
    return request({
      url: '/znyy/bSysConfig/delete/' + id,
      method: 'DELETE'
    })
  },
  // 删除
  deleteCourse(id) {
    return request({
      url: '/znyy/bSysConfig/course/price/' + id,
      method: 'DELETE'
    })
  },
  //修改密码
  updatePassWord(data){
    return request({
      url: '/znyy/bSysConfig/update/pwd',
      method: 'PUT',
      data
    })
  },

  // 新增复习包配置
  addReviewKit(data){
    return request({
      url: '/znyy/bSysConfig/addOrUpdateReviewPacketConfig',
      method: 'POST',
      data
    })
  },

  // 复习包列表
  reviewKitList(){
    return request({
      url: '/znyy/bSysConfig/getReviewPacketConfig',
      method: 'GET'
    })
  },

  // 复习包编辑
  editreviewKit(data){
    return request({
      url: '/znyy/bSysConfig/addOrUpdateReviewPacketConfig',
      method: 'POST',
      data
    })
  },

  // 删除
  deleteReviewKit(data) {
    return request({
      url: '/znyy/bSysConfig/deleteReviewPacketConfig',
      method: 'DELETE',
      params:data
    })
  }
}
