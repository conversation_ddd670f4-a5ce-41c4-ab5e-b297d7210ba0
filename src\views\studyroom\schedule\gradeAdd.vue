<template>
  <el-dialog :title="title" :visible.sync="visible" width="60%" top="5vh" append-to-body @close="close">
    <el-form :model="form" ref="form" :rules="rules" :inline="true" label-width="78px">
      <el-form-item label="规划名称" prop="className">
        <el-input
          v-model="form.className"
          placeholder="规划名称"
          clearable
          size="small"/>
      </el-form-item>
      <el-form-item label="学段" prop="level">
        <el-select v-model="form.level" :disabled="isEdit">
          <el-option v-for="(item,index) in [{label:'小学',value:'1'},{label:'初中',value:'2'},{label:'高中',value:'3'}]"
                     :key="index" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="类别" prop="courseType">
        <el-select v-model="form.courseType" :disabled="isEdit">
          <el-option v-for="(item,index) in [{label:'系统课',value:'1'},{label:'非系统课',value:'2'}]" :key="index"
                     :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="日期" prop="weeks">
        <el-select
          :disabled="isEdit"
          v-model="form.weeks"
          multiple
          :collapse-tags="!isEdit"
          placeholder="请选择">
          <el-option
            v-for="item in dateOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="时间" required>
        <el-row>
          <el-col :span="6">
            <el-time-select
              size="small"
              :disabled="isEdit"
              v-model="time1"
              value-format="HH:mm"
              :picker-options="{
              start: '00:00',
              step: '00:01',
              end: '23:58'
              }"></el-time-select>
          </el-col>
          <el-col :span="1">--</el-col>
          <el-col :span="6">
            <el-time-select
              :disabled="isEdit"
              size="small"
              v-model="time2"
              value-format="HH:mm"
              :picker-options="{
              start: '00:00',
              step: '00:01',
              end: '23:59',
              minTime: time1
            }"></el-time-select>
          </el-col>
        </el-row>
      </el-form-item>
      <el-form-item label="地区" prop="address" v-if="addressBtn">
        <el-cascader :options="addressOptions" v-model="form.address" filterable></el-cascader>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="submitForm" v-loading.fullscreen.lock="loading">确 定</el-button>
      <el-button @click="visible = false">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>
  import studyScheduleApi from "@/api/studyroom/studySchedule";
  import committeemanApi from "@/api/studyroom/committeeman";
  export default {
    name: "gradeAdd",
    data() {
      return {
        addressOptions:[],
        time1: '',
        time2: '',
        isEdit: false,
        addressBtn: false,
        title: '',
        loading:false,
        // 遮罩层
        visible: false,
        form: {},
        dateOptions: [{value: '1', label: '周一'}, {value: '2', label: '周二'}, {value: '3', label: '周三'},
          {value: '4', label: '周四'}, {value: '5', label: '周五'}, {value: '6', label: '周六'}, {value: '7', label: '周日'},
        ],
        rules: {
          courseType: [
            {required: true, message: "请选择分类", trigger: "blur"}
          ],
          level: [
            {required: true, message: "请选择阶段等级", trigger: "blur"}
          ],
          className: [
            {required: true, message: "请输入规划名称", trigger: "blur"}
          ],
          weeks: [
            {required: true, message: "请选择日期", trigger: "blur"}
          ],
          startTime: [
            {required: true, message: "请选择时间", trigger: "blur"}
          ],
          endTime: [
            {required: true, message: "请选择时间", trigger: "blur"}
          ],
          address: [
            {required: true, message: "请选择地区", trigger: "blur"}
          ],
        }
      };
    },
    created(){
      this.btnShow();
      committeemanApi.getAllRegion().then(res=>{
        let temp= [];
        res.data.map( i => {
          temp.push({
            label:i.name,
            value:i.name,
            id:i.id,
            children:i.children.map((item)=>{
              return{
                label:item.name,
                value:item.name,
                id:item.id,
                leaf:true
              }
            })
          })
        });
        this.addressOptions=temp;
      });
    },
    methods: {
      btnShow() {
        this.$store.getters.roles.forEach(element => {
          if (element.val==='admin'){
            this.addressBtn=true;
          }else {
            this.addressBtn=false;
          }

        });
      },
      getDetail(id) {
        studyScheduleApi.getSchedule(id).then(res => {
          if (res.data.joinUserCount > 0) {
            this.isEdit = true;
          }
          this.form = res.data;
          if (this.form.weeks) {
            var arr = this.form.weeks.split(",");
            this.form.weeks = [];
            this.form.weeks = arr;
          }
          this.form.courseType += "";
          this.time1 = this.form.times.split("-")[0];
          this.time2 = this.form.times.split("-")[1];
          if (this.form.province&&this.form.city){
            this.form.address=[this.form.province,this.form.city];
          }
        })
      },

      submitForm() {
        if (this.time1 === '' || this.time1 == null || this.time2 === '' || this.time2 == null) {
          this.$message.error("请输入必填项！");
          return false;
        }
        this.$refs["form"].validate(valid => {
          if (valid) {
            if (this.form.address){
              this.form.province=this.form.address[0];
              this.form.city=this.form.address[1];
            }
            this.form.startTime = this.time1;
            this.form.endTime = this.time2;
            this.loading=true;
            if (this.form.id != null) {
              studyScheduleApi.updateSchedule(this.form).then(res => {
                this.$message.success("提交成功！")
                this.visible = false;
                this.loading=false;
                this.$emit("ok");
              }).catch(err=>{
                this.loading=false;
              })
            } else {
              studyScheduleApi.addSchedule(this.form).then(response => {
                this.$message.success("提交成功！")
                this.visible = false;
                this.loading=false;
                this.$emit("ok");
              }).catch(err=>{
                this.loading=false;
              });
            }
          }
        });
      },
      show(id) {
        this.reset();
        this.visible = true;
        if (id) {
          this.title = '编辑规划';
          this.getDetail(id);
        } else {
          this.title = '新增规划';
          this.isEdit = false;
        }
      },
      close() {
        this.reset();
      },
      reset() {
        this.form = {
          className: null,
          courseType: null,
          level: null,
          weeks: null,
          startTime: '',
          endTime: '',
          address:[],
        };
        this.time1 = '';
        this.time2 = '';
        if (this.$refs['form']) {
          this.$refs['form'].resetFields();
        }
      }
    }
  };
</script>

<style>
  .user_green {
    background: #02a490;
    color: #FFFFFF;
  }

  .user_red {
    background: #ec808d;
    color: #FFFFFF;
  }

  .border_user {
    text-align: center;

  }

  .border1_bottom {
    border-bottom: 1px solid #333333;
  }

  .border1_right {
    border-top: 1px solid #333333;
    border-left: 1px solid #333333;
    border-right: 1px solid #333333;
  }
</style>
