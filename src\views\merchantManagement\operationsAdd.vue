<template>
  <div class="app-container">
    <div v-if="!addOrUpdate">
      <el-row>
        <el-col :xs="24" :lg="18">
          <!-- 添加或修改弹窗 -->
          <el-form ref="updateMarketDate" :rules="rulesAdd" :model="updateMarketDate" label-position="right" label-width="160px" style="width: 100%">
            <el-form-item label="登录账号：">
              <template>
                <el-col :xs="24" :span="18">
                  <el-input v-model="updateMarketDate.name" disabled />
                </el-col>
                <!-- <el-button v-if="!addOrUpdate" type="success" style="margin-left: 20px" @click="openLogin(updateMarketDate.name, updateMarketDate.id)">修改登录账号</el-button> -->
              </template>
            </el-form-item>
            <el-form-item label="超级俱乐部id：" prop="id" v-show="false">
              <el-col :xs="24" :span="18">
                <el-input disabled v-model="updateMarketDate.id" />
              </el-col>
            </el-form-item>
            <el-form-item label="超级俱乐部名称：" prop="merchantName">
              <el-col :xs="24" :span="18">
                <el-input :disabled="!isAdminOrMerchantManager" v-model="updateMarketDate.merchantName" placeholder="请输入省+市+商圈/路名/学校名称+超级俱乐部" />
                <span>（例：安徽省合肥市包河区平安国际金融超级俱乐部）</span>
              </el-col>
            </el-form-item>
            <el-form-item label="总负责人：" prop="realName">
              <el-col :xs="24" :span="18">
                <el-input :disabled="!isAdminOrMerchantManager" v-model="updateMarketDate.realName" :maxlength="50" />
              </el-col>
            </el-form-item>

            <el-form-item label="身份证号码：" prop="idCard">
              <el-col :xs="24" :span="18">
                <el-input :disabled="!isAdminOrMerchantManager" v-model="updateMarketDate.idCard" />
              </el-col>
            </el-form-item>
            <el-form-item label="个人职业：" prop="occupation">
              <el-col :xs="24" :span="18">
                <el-select v-model="updateMarketDate.occupation" placeholder="请选择" style="width: 100%">
                  <el-option v-for="item in occupationList" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-col>
            </el-form-item>
            <el-form-item label="渠道合作伙伴编号：" prop="operationsCode">
              <el-col :xs="24" :span="18">
                <el-input disabled v-model="updateMarketDate.operationsCode" />
              </el-col>
            </el-form-item>
            <el-form-item prop="businessName">
              <template v-slot:label>
                <span>
                  企业名称
                  <el-tooltip class="item" effect="dark" content="企业名称支持公司或者个体商户" placement="bottom">
                    <i class="el-icon-question"></i>
                  </el-tooltip>
                  :
                </span>
              </template>
              <el-col :xs="24" :span="18">
                <el-input disabled v-model="updateMarketDate.businessName" :maxlength="50" />
              </el-col>
            </el-form-item>
            <!-- <el-form-item label="学习管理系统初始数" prop="floorNum">
            <el-col :span="16">
              <el-input disabled v-model="updateMarketDate.operationsFreeSchNum" oninput="value=value.replace(/[^\d]/g,'')" />
            </el-col>
            <el-col :span="5" :offset="1">套</el-col>
          </el-form-item> -->
            <el-form-item label="身份证照片:" prop="idCardPhoto">
              <el-col :span="20">
                <el-upload
                  :disabled="!isAdminOrMerchantManager"
                  ref="clearupload"
                  v-loading="uploadLoadingIdCard"
                  list-type="picture-card"
                  action=""
                  element-loading-text="图片上传中"
                  :limit="10"
                  :on-exceed="justPictureNumIdCard"
                  :file-list="!addOrUpdate ? fileIdCard : fileIdCard"
                  :http-request="uploadIdCardDetailHttp"
                  :on-preview="handlePictureCardPreview"
                  :on-remove="handleRemoveDetailIdCard"
                >
                  <i class="el-icon-plus" />
                </el-upload>
              </el-col>
              <el-col :xs="24" :span="2">(*支持多张)</el-col>
            </el-form-item>
            <el-form-item label="业务开展地：" prop="province">
              <el-col :xs="24" :span="18">
                <el-cascader
                  :disabled="isAdmin ? false : true"
                  :options="regionData"
                  v-model="selectedOptions"
                  :props="{ value: 'label' }"
                  placeholder="请选择"
                  style="width: 100%"
                  @change="handleRegionChange"
                ></el-cascader>
              </el-col>
            </el-form-item>
            <el-form-item label="经营地址：" prop="address">
              <el-col :xs="24" :span="18">
                <el-input v-model="address" v-show="false" />
                <el-input :disabled="isAdmin ? false : true" v-model="updateMarketDate.address" placeholder="请选择" />
              </el-col>
            </el-form-item>
            <el-form-item label="孩子的年龄：" prop="childAge">
              <el-col :xs="24" :span="18">
                <el-input v-model="updateMarketDate.childAge" placeholder="请输入" :maxlength="50" />
              </el-col>
            </el-form-item>
            <el-form-item label="所在的行业：" prop="industry">
              <el-col :xs="24" :span="18">
                <el-input v-model="updateMarketDate.industry" placeholder="请输入" />
              </el-col>
            </el-form-item>
            <el-form-item label="特长或能力：" prop="ability">
              <el-col :xs="24" :span="18">
                <el-input v-model="updateMarketDate.ability" placeholder="请输入" />
              </el-col>
            </el-form-item>
            <el-form-item label="从事行业：" prop="engagedIndustry">
              <el-col :xs="24" :span="18">
                <el-input v-model="updateMarketDate.engagedIndustry" placeholder="请输入" />
              </el-col>
            </el-form-item>
            <el-form-item label="行业资源：" prop="industryResources">
              <el-col :xs="24" :span="18">
                <el-input v-model="updateMarketDate.industryResources" placeholder="请输入" />
              </el-col>
            </el-form-item>
            <el-form-item label="年收入：" prop="annualIncome">
              <el-col :xs="24" :span="18">
                <el-input v-model="updateMarketDate.annualIncome" placeholder="请输入" :maxlength="50" />
              </el-col>
            </el-form-item>
            <el-form-item label="个人资源：" prop="personalResources">
              <el-col :xs="24" :span="18">
                <el-input v-model="updateMarketDate.personalResources" placeholder="请输入" />
              </el-col>
            </el-form-item>
            <el-form-item label="个人成就：" prop="personalAchievements">
              <el-col :xs="24" :span="18">
                <el-input v-model="updateMarketDate.personalAchievements" placeholder="请输入" />
              </el-col>
            </el-form-item>
            <el-form-item label="通过何种方式了解鼎校：" prop="knowChannel">
              <el-col :xs="24" :span="18">
                <el-input v-model="updateMarketDate.knowChannel" placeholder="请输入" />
              </el-col>
            </el-form-item>
            <el-form-item label="是否参加过春笋行动：" prop="attendedSpringMeeting">
              <el-col :xs="24" :span="18">
                <el-select v-model="updateMarketDate.attendedSpringMeeting" placeholder="请选择">
                  <el-option label="是" value="1"></el-option>
                  <el-option label="否" value="0"></el-option>
                </el-select>
              </el-col>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>

      <div slot="footer" class="dialog-footer" style="margin: 30px 0 30px 140px">
        <!-- <el-button v-if="addOrUpdate" size="mini" type="primary" @click="addActiveFun('addMarketDate')">新增</el-button> -->
        <el-button
          v-if="
            (updateMarketDate.isCheck == 0 ||
              updateMarketDate.isCheck == -4 ||
              updateMarketDate.isCheck == 2 ||
              merchantCode == 'A0001' ||
              roleTag == 'DeliveryService' ||
              isAdminOrMerchantManager) &&
            !isResubmit
          "
          size="mini"
          type="primary"
          @click="updateActiveFun('updateMarketDate', false)"
        >
          修改
        </el-button>
        <el-button v-if="isResubmit" size="mini" type="primary" @click="updateActiveFun('updateMarketDate', true)">重提</el-button>
        <el-button size="mini" @click="close">关闭</el-button>
      </div>
    </div>
    <div v-else>
      <!-- 新增 -->
      <div style="width: 800px; margin: 20px auto">
        <el-steps :active="active" finish-status="success" process-status="finish">
          <el-step title="步骤 1" description="请完成基础信息填写"></el-step>
          <el-step title="步骤 2" description="请完成补充信息填写"></el-step>
          <el-step title="步骤 3" description="创建成功"></el-step>
        </el-steps>
      </div>
      <el-row>
        <el-col :xs="20" :offset="2" :lg="18">
          <div style="margin-bottom: 20px; display: flex; align-items: center">
            <span style="width: 4px; height: 16px; background-color: #4095e5; margin-right: 4px"></span>
            {{ active == 0 ? '基础信息' : '补充信息' }}
          </div>
          <div v-if="active == 0">
            <el-form ref="addMarketDate1" :rules="rules1" :model="addMarketDate" label-position="right" label-width="140px" style="width: 100%">
              <el-form-item label="登录账号：" prop="name">
                <template>
                  <el-col :xs="24" :span="18">
                    <el-input v-model="addMarketDate.name" />
                  </el-col>
                </template>
              </el-form-item>
              <el-form-item label="超级俱乐部名称：" prop="merchantName">
                <el-col :xs="24" :span="18">
                  <el-input v-model="addMarketDate.merchantName" placeholder="请输入省+市+商圈/路名/学校名称+超级俱乐部" />
                  <span>（例：安徽省合肥市包河区平安国际金融超级俱乐部）</span>
                </el-col>
              </el-form-item>
              <el-form-item label="身份证号码：" prop="idCard">
                <el-col :xs="24" :span="18">
                  <el-input v-model="addMarketDate.idCard" />
                </el-col>
              </el-form-item>
            </el-form>
          </div>
          <div v-if="active == 1">
            <el-form ref="addMarketDate" :rules="rules" :model="addMarketDate" label-position="right" label-width="160px" style="width: 100%">
              <el-form-item label="总负责人：" prop="realName">
                <el-col :xs="24" :span="18">
                  <el-input v-model="addMarketDate.realName" :disabled="addDisable" placeholder="请填写真实姓名" :maxlength="50" />
                </el-col>
              </el-form-item>
              <el-form-item label="渠道合作伙伴编号：" prop="merchantPartnerCode">
                <el-col :xs="24" :span="18">
                  <el-input :disabled="addDisable" v-model="addMarketDate.merchantPartnerCode" placeholder="请填写渠道合作伙伴编号" />
                </el-col>
              </el-form-item>
              <el-form-item prop="secondPartyEnterpriseName">
                <template v-slot:label>
                  <span>
                    企业名称
                    <el-tooltip class="item" effect="dark" content="企业名称支持公司或者个体工商户" placement="bottom">
                      <i class="el-icon-question"></i>
                    </el-tooltip>
                    :
                  </span>
                </template>
                <el-col :xs="24" :span="18">
                  <!-- <el-input
                    :disabled="addDisable"
                    placeholder="请输入该俱乐部的企业名称，如未创建可先在此输入名称后再注册该名称的企业"
                    v-model="addMarketDate.secondPartyEnterpriseName"
                  /> -->
                  <el-autocomplete
                    style="width: 100%"
                    :disabled="addDisable"
                    clearable
                    @clear="setBlur()"
                    @input="handle"
                    :fetch-suggestions="querySearch"
                    :trigger-on-focus="false"
                    @select="handleSelect"
                    class="inline-input"
                    v-model="addMarketDate.secondPartyEnterpriseName"
                    placeholder="请输入该俱乐部的企业名称，后续将用于合同签署校验，请如实选择"
                    :maxlength="50"
                  ></el-autocomplete>
                </el-col>
              </el-form-item>
              <el-form-item label="身份证照片:" prop="idCardPhoto">
                <el-col :span="20">
                  <el-upload
                    ref="clearupload"
                    v-loading="uploadLoadingIdCard"
                    list-type="picture-card"
                    action=""
                    element-loading-text="图片上传中"
                    :limit="10"
                    :on-exceed="justPictureNumIdCard"
                    :file-list="!addOrUpdate ? fileIdCard : fileIdCard"
                    :http-request="uploadIdCardDetailHttp"
                    :on-preview="handlePictureCardPreview"
                    :on-remove="handleRemoveDetailIdCard"
                  >
                    <i class="el-icon-plus" />
                  </el-upload>
                </el-col>
                <el-col :xs="24" :span="2">(*支持多张)</el-col>
              </el-form-item>
              <el-form-item label="个人职业：" prop="occupation">
                <el-col :xs="24" :span="18">
                  <el-select v-model="addMarketDate.occupation" placeholder="请选择" style="width: 100%" clearable>
                    <el-option v-for="item in occupationList" :key="item.value" :label="item.label" :value="item.value"></el-option>
                  </el-select>
                </el-col>
              </el-form-item>
              <el-form-item label="业务开展地：" prop="province">
                <el-col :xs="24" :span="18">
                  <el-cascader
                    :options="regionData"
                    clearable
                    v-model="selectedOptions"
                    :props="{ value: 'label' }"
                    style="width: 100%"
                    placeholder="请选择"
                    @change="handleRegionChange"
                  ></el-cascader>
                </el-col>
              </el-form-item>
              <el-form-item label="经营地址：" prop="address">
                <el-col :xs="24" :span="18">
                  <el-input :maxlength="100" v-if="addOrUpdate" v-model="address" v-show="false" />
                  <el-input :maxlength="100" v-if="!addOrUpdate" v-model="address" v-show="false" />
                  <el-input :maxlength="100" v-if="addOrUpdate" v-model="addMarketDate.address" placeholder="请输入" />
                  <el-input :maxlength="100" v-if="!addOrUpdate" v-model="updateMarketDate.address" placeholder="请输入" />
                </el-col>
              </el-form-item>
              <el-form-item label="孩子的年龄：" prop="childAge">
                <el-col :xs="24" :span="18">
                  <el-input v-model="addMarketDate.childAge" placeholder="请输入" :maxlength="50" />
                </el-col>
              </el-form-item>
              <el-form-item label="所在的行业：" prop="industry">
                <el-col :xs="24" :span="18">
                  <el-input v-model="addMarketDate.industry" placeholder="请输入" :maxlength="50" />
                </el-col>
              </el-form-item>
              <el-form-item label="特长或能力：" prop="ability">
                <el-col :xs="24" :span="18">
                  <el-input v-model="addMarketDate.ability" placeholder="请输入" :maxlength="100" />
                </el-col>
              </el-form-item>
              <el-form-item label="从事行业：" prop="engagedIndustry">
                <el-col :xs="24" :span="18">
                  <el-input v-model="addMarketDate.engagedIndustry" placeholder="请输入" />
                </el-col>
              </el-form-item>
              <el-form-item label="行业资源：" prop="industryResources">
                <el-col :xs="24" :span="18">
                  <el-input v-model="addMarketDate.industryResources" placeholder="请输入" />
                </el-col>
              </el-form-item>
              <el-form-item label="年收入：" prop="annualIncome">
                <el-col :xs="24" :span="18">
                  <el-input v-model="addMarketDate.annualIncome" placeholder="请输入" :maxlength="50" />
                </el-col>
              </el-form-item>
              <el-form-item label="个人资源：" prop="personalResources">
                <el-col :xs="24" :span="18">
                  <el-input v-model="addMarketDate.personalResources" placeholder="请输入" />
                </el-col>
              </el-form-item>
              <el-form-item label="个人成就：" prop="personalAchievements">
                <el-col :xs="24" :span="18">
                  <el-input v-model="addMarketDate.personalAchievements" placeholder="请输入" />
                </el-col>
              </el-form-item>
              <el-form-item label="通过何种方式了解鼎校：" prop="knowChannel">
                <el-col :xs="24" :span="18">
                  <el-input v-model="addMarketDate.knowChannel" placeholder="请输入" />
                </el-col>
              </el-form-item>
              <el-form-item label="是否参加过春笋行动：" prop="attendedSpringMeeting">
                <el-col :xs="24" :span="18">
                  <el-select v-model="addMarketDate.attendedSpringMeeting" placeholder="请选择" style="width: 100%">
                    <el-option label="是" value="1"></el-option>
                    <el-option label="否" value="0"></el-option>
                  </el-select>
                </el-col>
              </el-form-item>
            </el-form>
          </div>
          <div style="display: flex; margin-top: 20px; justify-content: center">
            <el-button type="primary" size="small" @click="next">{{ active == 0 ? '下一步' : '新增' }}</el-button>
            <el-button size="small" @click="back">返回</el-button>
          </div>
        </el-col>
      </el-row>
    </div>
    <el-dialog :visible.sync="dialogUploadVisible" :close-on-click-modal="false">
      <img width="100%" :src="dialogImageUrl" alt />
    </el-dialog>
    <!-- 修改登陆账号弹窗 -->
    <el-dialog title="修改登录账号" :visible.sync="showLoginName" width="30%" :close-on-click-modal="false" @close="closeLoginname">
      <el-form :ref="'updateLoginName'" :rules="rulesLoginName" :model="updateLoginName" label-position="left" label-width="120px" style="width: 80%">
        <el-form-item label="原登录账号：" prop="oldName">
          <span>{{ updateLoginName.oldName }}</span>
        </el-form-item>
        <el-form-item label="id:" prop="id" v-show="false">
          <el-input v-model="updateLoginName.id" />
        </el-form-item>
        <el-form-item label="新登录账号：" prop="name">
          <el-input v-model="updateLoginName.name" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="updateDealerLoginName('updateLoginName')">确定</el-button>
        <el-button size="mini" @click="closeLoginname">关闭</el-button>
      </div>
    </el-dialog>
    <!-- 合同购买 -->
    <purchaseContract ref="spurchaseDialogVisible" />
  </div>
</template>

<script>
  import purchaseContract from '@/components/purchaseContract/index.vue'; // 合同购买组件
  import { regionData } from 'element-china-area-data';
  import dealerListApi from '@/api/operationsList';
  import schoolApi from '@/api/areasSchoolList';
  import marketApi from '@/api/marketList';
  import { ossPrClient } from '@/api/alibaba';
  import { isvalidPhone, idCard } from '@/utils/validate';

  export default {
    //name: 'dealerAdd',
    components: {
      purchaseContract
    },
    data() {
      //手机号验证
      var validPhone = (rule, value, callback) => {
        if (!value) {
          callback(new Error('请输入电话号码'));
        } else if (!isvalidPhone(value)) {
          callback(new Error('请输入正确的11位手机号码'));
        } else {
          callback();
        }
      };
      //身份证表达式
      var isIdCard = (rule, value, callback) => {
        if (!value) {
          callback(new Error('请输入身份证号码'));
        } else if (!idCard(value)) {
          callback(new Error('请输入正确的18位身份证号'));
        } else {
          callback();
        }
      };
      //身份证照片校验
      var validateIdCardPhoto = (rule, value, callback) => {
        if (this.fileIdCard.length === 0) {
          callback(new Error('请上传身份证照片'));
        } else {
          callback();
        }
      };
      //业务开展地校验
      var validateProvince = (rule, value, callback) => {
        if (!this.selectedOptions || this.selectedOptions.length === 0) {
          callback(new Error('请选择业务开展地'));
        } else {
          callback();
        }
      };
      const self = this;
      return {
        active: 0,
        roleTag: '',
        showPrepaidMoney: false,
        // 地图搜索分页相关参数
        pageNum: 1,
        pageSize: 5,
        result: [],
        addDisable: false,
        currentResult: -1,
        isResubmit: false,
        regionData,
        selectedOptions: [],
        disabled: true,
        tableLoading: false,
        showLoginName: false, //登录账号
        updateLoginName: {}, //修改账号
        rulesLoginName: {
          name: [
            {
              required: true,
              message: '必填',
              trigger: 'change'
            },
            {
              validator: validPhone,
              trigger: 'blur'
            }
          ]
        },
        id: 0,
        // 分页
        tablePage: {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null
        },
        ADD: true,
        tableData: [],
        dataQuery: {},
        dialogVisible: false,
        updateMarketDate: {
          areaCoverRange: 500,
          merchantType: '3',
          childAge: undefined,
          annualIncome: undefined
        }, //修改数据对象
        addMarketDate: {
          areaCoverRange: 500,
          merchantType: '3',
          paymentIsComplete: '1',
          childAge: undefined,
          annualIncome: undefined
        }, //增加数据对象
        showLoginAccount: false,
        addOrUpdate: true,
        rules1: {
          name: [
            { required: true, message: '请输入登录账号', trigger: 'blur' },
            { validator: validPhone, trigger: 'blur' }
          ],
          merchantName: [{ required: true, message: '请输入俱乐部名称', trigger: 'blur' }],
          idCard: [
            { required: true, message: '请输入身份证号码', trigger: 'blur' },
            { validator: isIdCard, trigger: 'blur' }
          ]
          // signupDate: [{ required: true, message: '必填', trigger: 'change' }]
        },
        rulesAdd: {
          // TODO: 6.13隐藏
          // secondPartyEnterpriseName: [{ required: true, message: '必填', trigger: 'blur' }],
          name: [
            { required: true, message: '请输入登录账号', trigger: 'blur' },
            { validator: validPhone, trigger: 'blur' }
          ],
          merchantName: [{ required: true, message: '请输入俱乐部名称', trigger: 'blur' }],
          realName: [{ required: true, message: '请填写真实姓名', trigger: 'blur' }],
          idCard: [
            { required: true, message: '请输入身份证号码', trigger: 'blur' },
            { validator: isIdCard, trigger: 'blur' }
          ],
          paymentIsComplete: [{ required: true, message: '必填', trigger: 'blur' }],
          areaCoverRange: [{ required: true, message: '必填', trigger: 'change' }],
          // signupDate: [{ required: true, message: '必填', trigger: 'change' }],
          idCardPhoto: [
            {
              required: true,
              message: '请上传身份证照片',
              trigger: 'change'
            },
            {
              validator: validateIdCardPhoto,
              trigger: 'change'
            }
          ],
          shopPhoto: [{ required: true, message: '必填', trigger: 'change' }],
          province: [
            {
              required: true,
              message: '请选择业务开展地',
              trigger: 'change'
            },
            {
              validator: validateProvince,
              trigger: 'change'
            }
          ],
          city: [{ required: true, message: '必填', trigger: 'change' }],
          area: [{ required: true, message: '必填', trigger: 'change' }],
          address: [{ required: true, message: '请填写详细经营地址', trigger: 'blur' }],
          // description: [{ required: true, message: '必填', trigger: 'change' }],
          operationsCode: [{ required: true, message: '渠道合作伙伴编号不为空', trigger: 'blur' }],
          occupation: [{ required: true, message: '请选择个人职业', trigger: 'change' }]
        },
        rules: {
          // TODO: 6.13隐藏
          // secondPartyEnterpriseName: [{ required: true, message: '必填', trigger: 'blur' }],
          name: [
            { required: true, message: '请输入登录账号', trigger: 'blur' },
            { validator: validPhone, trigger: 'blur' }
          ],
          merchantName: [{ required: true, message: '请输入俱乐部名称', trigger: 'blur' }],
          realName: [{ required: true, message: '请填写真实姓名', trigger: 'blur' }],
          idCard: [{ required: true, message: '请输入身份证号码', trigger: 'blur' }],
          paymentIsComplete: [{ required: true, message: '必填', trigger: 'blur' }],
          areaCoverRange: [{ required: true, message: '必填', trigger: 'change' }],
          // signupDate: [{ required: true, message: '必填', trigger: 'change' }],
          idCardPhoto: [
            {
              required: true,
              message: '请上传身份证照片',
              trigger: 'change'
            },
            {
              validator: validateIdCardPhoto,
              trigger: 'change'
            }
          ],
          shopPhoto: [{ required: true, message: '必填', trigger: 'change' }],
          province: [
            {
              required: true,
              message: '请选择业务开展地',
              trigger: 'change'
            },
            {
              validator: validateProvince,
              trigger: 'change'
            }
          ],
          city: [{ required: true, message: '必填', trigger: 'change' }],
          area: [{ required: true, message: '必填', trigger: 'change' }],
          address: [{ required: true, message: '请填写详细经营地址', trigger: 'blur' }],
          // description: [{ required: true, message: '必填', trigger: 'change' }],
          merchantPartnerCode: [{ required: true, message: '渠道合作伙伴编号不为空', trigger: 'blur' }],
          occupation: [{ required: true, message: '请选择个人职业', trigger: 'change' }]
        },
        value1: '',
        fileListPending: [], // 待处理已上传图片信息
        fileList: [], // 上传图片已有图片列表
        radio: '3',
        fileDetailListPending: [], // 待处理已上传图片信息
        rankType: [], //超级俱乐部级别
        name: '',
        bankType: [], //银行集合
        dialogUploadVisible: false,
        dialogImageUrl: '',
        //证件照图片
        fileIdCard: [],
        uploadLoadingIdCard: false,
        //证件照结束
        uploadLoadingShop: false,
        // address: "",
        filelistShop: [],
        //支付图片
        merchantCode: '',
        //门店照开始
        //门店照结束
        //地图开始
        slotWindow: {
          position: [121.5163285, 31.********]
        },
        addDealerShow: true,
        addDealerShow01: true,
        addDealerShow02: true,
        markers: [
          // [121.59996, 31.197646],
          // [121.40018, 31.197622],
          // [121.69991, 31.207649]
        ],
        //搜索结果标注
        markers2: [],
        windows: [],
        window: '',
        searchOption: {
          city: '全国',
          citylimit: false //是否限制城市内搜索
        },
        zoom: 12,
        lng: 0,
        lat: 0,
        address: '',
        province: '',
        city: '',
        district: '',
        loaded: false,
        occupationList: []
      };
    },
    computed: {
      // 是管理员或者新渠道管理员
      isAdminOrMerchantManager() {
        return this.roleTag == 'admin' || this.roleTag == 'MerchantManager';
      },
      // 是管理员
      isAdmin() {
        return this.roleTag == 'admin';
      }
    },
    created() {
      this.addOrUpdate = this.$route.query.addOrUpdate;
      this.isResubmit = this.$route.query.isResubmit;
      this.getRoleTag();
      this.getOccupation();
      //编辑回显
      this.updateDealer();
      if (!this.addOrUpdate) {
        this.setTitle('超级俱乐部编辑');
      } else {
        this.setTitle('超级俱乐部新增');
      }
    },

    mounted() {
      ossPrClient();
    },
    methods: {
      handleRegionChange(value) {
        if (value && value.length > 0) {
          this.$nextTick(() => {
            if (this.addOrUpdate) {
              this.$refs.addMarketDate.clearValidate(['province']);
            } else {
              this.$refs.updateMarketDate.clearValidate(['province']);
            }
          });
        }
      },
      // 获取个人职业
      getOccupation() {
        dealerListApi.getPersonalOccupation('occupation').then((res) => {
          if (res.data) {
            this.occupationList = res.data.map((item) => ({
              label: item.label,
              value: item.label
            }));
          } else {
            this.occupationList = [];
          }
        });
      },
      setBlur() {
        //  在点击由 clearable 属性生成的清空按钮时，主动触发失去焦点，解决‘fetch-suggestions’输入建议不提示的bug
        document.activeElement.blur();
      },
      // 清空输入框页面重置
      handle(val) {
        if (val === '') {
          // this.getData(); // 页面重置的代码
        }
      },
      // 过滤项目和class
      async querySearch(queryString, cb) {
        if (queryString && queryString.length > 0) {
          let data1 = {
            keyword: queryString,
            pageNo: 1,
            pageSize: 10
          };

          try {
            const data = await dealerListApi.businessLicense(data1); // search定义在data里
            // 赋值给建议列表，渲染到页面

            var list = data.data.data.data;
            // 如果list.length等于0，则代表没有匹配到结果。手动给list添加一条提示信息
            if (!queryString) {
              list.push({
                id: '-1',
                value: '无匹配结果'
              });
              // 调用 callback 返回建议列表的数据

              cb(list);
            } else {
              list = list.map((item) => {
                return {
                  value: `${item.companyName}`,
                  id: `${item.creditNo}`
                };
              });
              list = list.filter((item) => {
                return item.value.indexOf(queryString) > -1;
              });
              // 调用 callback 返回建议列表的数据
              cb(list);
            }
          } catch (error) {
            console.log(error);
          }
        }
      },
      handleSelect(item) {
        console.log(item, '....................');
      },
      back() {
        this.$router.back();
      },
      async first() {
        await this.$refs.addMarketDate1.validate();
        let { data } = await dealerListApi.merchantPhoneCheck({ phone: this.addMarketDate.name });
        if (data) {
          this.addMarketDate.secondPartyEnterpriseName = data.secondPartyEnterpriseName;
          this.addMarketDate.merchantPartnerCode = data.operationsCode;
          this.addMarketDate.realName = data.realName;
          this.addDisable = true;
        }

        this.active++;
      },
      next() {
        let data1 = {
          keyword: this.addMarketDate.secondPartyEnterpriseName,
          pageNo: 1,
          pageSize: 10
        };

        if (this.active == 0) {
          this.first();
        } else {
          // 如果填写了企业名称，需判断企业名称是否正确
          // if (this.addMarketDate.secondPartyEnterpriseName) {
          //   dealerListApi.businessLicense(data1).then((res) => {
          //     let company = res.data.data.data.some((item) => {
          //       return item.companyName == this.addMarketDate.secondPartyEnterpriseName;
          //     });
          //     if (!company) {
          //       this.$message.error('请输入正确的企业名称');
          //       return;
          //     } else {
          //       this.addActiveFun('addMarketDate');
          //     }
          //   });
          // } else {
          this.addActiveFun('addMarketDate');
          // }
        }
      },
      purchaseSuccess() {},
      changePaymentIsComplete(paymentIsComplete) {
        console.log('changePaymentIsComplete', paymentIsComplete);
        if (paymentIsComplete == 0) {
          this.showPrepaidMoney = true;
        } else {
          this.showPrepaidMoney = false;
        }
      }, // 动态设置标签页标题
      setTitle(title) {
        let i = 0;
        let visitedViews = this.$store.getters.visitedViews;
        visitedViews.forEach((route, index) => {
          if (this.$route.path == route.path) {
            i = index;
          }
        });
        this.$route.meta.title = title;
        visitedViews[i].title = title;
      },
      // 状态改变事件
      changeRadio(radio) {
        if (radio == '3') {
          this.addMarketDate.merchantType = '3';
        } else {
          this.addMarketDate.merchantType = '2';
        }
      },
      changeupDate() {
        if (radio == '3') {
          this.updateMarketDate.merchantType = '3';
        } else {
          this.updateMarketDate.merchantType = '2';
        }
      },
      //编辑回显
      updateDealer() {
        const that = this;
        that.addOrUpdate = JSON.parse(window.localStorage.getItem('addOrUpdateDealer'));
        that.id = window.localStorage.getItem('dealerId');

        if (!that.addOrUpdate) {
          that.updateMarketDate.id = window.localStorage.getItem('dealerId');
          dealerListApi.queryActiveV2(that.id).then((res) => {
            that.updateMarketDate = res.data;
            if (res.data.childAge == null || res.data.childAge == '') {
              that.updateMarketDate.childAge = undefined;
            }
            if (res.data.annualIncome == null || res.data.annualIncome == '') {
              that.updateMarketDate.annualIncome = undefined;
            }
            if (res.data.attendedSpringMeeting !== undefined && res.data.attendedSpringMeeting !== null) {
              that.updateMarketDate.attendedSpringMeeting = String(res.data.attendedSpringMeeting);
            }
            that.selectedOptions = [res.data.province, res.data.city, res.data.area];
            if (that.selectedOptions[0] == that.selectedOptions[1]) {
              that.selectedOptions[1] = '市辖区';
            }
            if (res.data.idCardPhoto !== null && res.data.idCardPhoto.length >= 1) {
              for (let i = 0; i < res.data.idCardPhoto.length; i++) {
                that.fileIdCard.push({
                  url: that.aliUrl + res.data.idCardPhoto[i]
                });
              }
            } else {
              that.fileIdCard = [];
            }
            if (res.data.shopPhoto !== null && res.data.shopPhoto.length >= 1) {
              for (let i = 0; i < res.data.shopPhoto.length; i++) {
                that.filelistShop.push({
                  url: that.aliUrl + res.data.shopPhoto[i]
                });
              }
            }
          });
        } else {
        }
      },
      addMarker: function () {
        let lng = 121.5 + Math.round(Math.random() * 1000) / 10000;
        let lat = 31.197646 + Math.round(Math.random() * 500) / 10000;
        this.markers.push([lng, lat]);
      },
      //鼠标滑过
      markerMouse(e) {
        infoWindow.setContent(e.target.content);
        infoWindow.open(map, e.target.getPosition());
      },
      //TODO
      close() {
        const that = this;
        // that.$router.push({
        //   path: "/merchantManagement/dealerList",
        // });
        // 关闭当前标签页
        that.$store.dispatch('delVisitedViews', this.$route);
        that.$router.go(-1);

        that.addMarketDate = {};
        that.updateMarketDate = {};
        that.addOrUpdate = true;
        that.$refs.clearupload.clearFiles();
      },
      //鼠标滑过
      markerOut(e) {
        map.clearInfoWindow();
      },

      //新增操作
      clickAdd() {
        this.$refs.clearupload.clearFiles();
        this.dialogVisible = true;
      },
      // 打开修改登陆账号
      openLogin(name, id) {
        this.showLoginName = true;
        this.updateLoginName.id = id;
        this.updateLoginName.oldName = name;
      },
      //修改账号
      updateDealerLoginName(ele) {
        const that = this;
        that.$refs[ele].validate((valid) => {
          if (valid) {
            marketApi.markertUpdateLogin(that.updateLoginName).then(() => {
              that.$nextTick(() => that.fetchData());
              that.updateMarketDate.name = this.updateLoginName.name;
              that.updateLoginName.name = '';
              that.showLoginName = false;
              that.$message.success('修改登录账号成功');
            });
          }
        });
      },
      //修改账号关闭
      closeLoginname() {
        this.showLoginName = false;
      },
      getRoleTag() {
        schoolApi.getCurrentAdmin().then((res) => {
          console.log(res.data.merchantCode + 'wyy');
          this.roleTag = res.data.roleTag;
          this.merchantCode = res.data.merchantCode;
        });
      },
      //新增操作超级俱乐部
      addActiveFun(ele) {
        const that = this;
        if (!that.addMarketDate.name) {
          that.$message.error('登录账号不能为空');
          return false;
        }
        if (!that.addMarketDate.merchantName) {
          that.$message.error('超级俱乐部名称不能为空');
          return false;
        }
        if (!that.addMarketDate.idCard) {
          that.$message.error('教学负责人不能为空');
          return false;
        }
        // if (that.fileIdCard.length <= 0) {
        //   that.$message.error('身份证照片不能为空');
        //   return false;
        // }
        that.addMarketDate.province = this.selectedOptions[0];
        if (this.selectedOptions[1] == '市辖区') {
          that.addMarketDate.city = this.selectedOptions[0];
        } else {
          that.addMarketDate.city = this.selectedOptions[1];
        }
        this.addMarketDate.area = this.selectedOptions[2];

        // 在提交前先检查身份证照片和业务开展地是否已有数据
        if (this.fileIdCard.length > 0) {
          this.$refs.addMarketDate.clearValidate(['idCardPhoto']);
        }
        if (this.selectedOptions && this.selectedOptions.length > 0) {
          this.$refs.addMarketDate.clearValidate(['province']);
        }

        that.addMarketDate.idCardPhoto = that.fileIdCard;
        that.addMarketDate.shopPhoto = that.filelistShop;

        that.$refs[ele].validate((valid) => {
          // 表单验证
          if (valid) {
            const loading = this.$loading({
              lock: true,
              text: '新增超级俱乐部',
              spinner: 'el-icon-loading',
              background: 'rgba(0, 0, 0, 0.7)'
            });
            dealerListApi
              .addDealerListV2(that.addMarketDate)
              .then(async (res) => {
                if (res.data && res.data < 2) {
                  if (res.data > 0) {
                    await this.$confirm('该俱乐部有合伙人账户，您填写的上级编号与所属俱乐部编号都不会生效，是否继续？');
                  } else {
                    await this.$confirm('该俱乐部没有合伙人账户，是否继续？', {
                      type: 'warning'
                    });
                  }
                }

                that.dialogVisible = false;
                that.$message.success('新增俱乐部成功');
                that.$router.push({
                  path: '/merchantManagement/operationsList'
                });
                loading.close();
                that.addMarketDate = {};
                that.fileIdCard = [];
                that.filelistShop = [];
                that.fileDeatiList4 = [];
              })
              .catch((err) => {
                if (err === 'error') return loading.close();
                let res = err.message.split('@');
                let type = res[1];
                if (type == 3) {
                  this.$confirm('您的剩余合同数量不足，如需创建俱乐部，请购买合同后重试', '提示', {
                    confirmButtonText: '去购买',
                    cancelButtonText: '取消',
                    type: 'none'
                  })
                    .then(() => {
                      this.$refs.spurchaseDialogVisible.open();
                    })
                    .catch(() => {
                      this.$message({
                        type: 'info',
                        message: '已取消'
                      });
                    });
                  loading.close();
                } else if (type == 1) {
                  this.$alert(res[0], '提示', {
                    confirmButtonText: '确定',
                    // cancelButtonText: '取消',
                    type: 'none'
                  });
                }
                loading.close();
              });
          } else {
            console.log('error submit!!');
            //loading.close();
            return false;
          }
        });
      },
      formatDate(current_datetime) {
        return (
          current_datetime.getFullYear() +
          '-' +
          (current_datetime.getMonth() + 1) +
          '-' +
          current_datetime.getDate() +
          ' ' +
          current_datetime.getHours() +
          ':' +
          current_datetime.getMinutes() +
          ':' +
          current_datetime.getSeconds()
        );
      },
      //修改操作
      updateActiveFun(ele, isResubmit) {
        const that = this;
        that.updateMarketDate.province = this.selectedOptions[0];
        if (this.selectedOptions[1] == '市辖区') {
          that.updateMarketDate.city = this.selectedOptions[0];
        } else {
          that.updateMarketDate.city = this.selectedOptions[1];
        }
        this.updateMarketDate.area = this.selectedOptions[2];
        // if (that.fileIdCard.length <= 0) {
        //   that.$message.error('身份证照片不能为空');
        //   return false;
        // }
        // 在提交前先检查身份证照片和业务开展地是否已有数据
        if (this.fileIdCard.length > 0) {
          this.$refs.updateMarketDate.clearValidate(['idCardPhoto']);
        }
        if (this.selectedOptions && this.selectedOptions.length > 0) {
          this.$refs.updateMarketDate.clearValidate(['province']);
        }
        that.updateMarketDate.idCardPhoto = [];
        for (var i = 0; i < that.fileIdCard.length; i++) {
          let idCardIndex = that.fileIdCard[i].url.lastIndexOf('manage');
          that.updateMarketDate.idCardPhoto.push(that.fileIdCard[i].url.substring(idCardIndex, that.fileIdCard[i].url.length));
        }
        that.updateMarketDate.shopPhoto = [];
        for (var i = 0; i < that.filelistShop.length; i++) {
          let shopindex = that.filelistShop[i].url.lastIndexOf('manage');
          that.updateMarketDate.shopPhoto.push(that.filelistShop[i].url.substring(shopindex, that.filelistShop[i].url.length));
        }
        if (this.isResubmit) {
          that.rules.name = [];
        }
        delete that.updateMarketDate.name;
        console.log('🚀🥶💩~ that.$refs[ele]', that.$refs[ele]);
        console.log('🚀🥶💩~ ele', ele);
        that.$refs[ele].validate((valid) => {
          // 表单验证
          if (valid) {
            const loading = this.$loading({
              lock: true,
              text: '修改超级俱乐部',
              spinner: 'el-icon-loading',
              background: 'rgba(0, 0, 0, 0.7)'
            });
            if (that.updateMarketDate.expireDate) that.updateMarketDate.expireDate = that.updateMarketDate.expireDate.replace('T', ' ');
            // if (that.updateMarketDate.signupDate) that.updateMarketDate.signupDate = that.updateMarketDate.signupDate.replace('T', ' ');
            dealerListApi
              .addDealerListV2(that.updateMarketDate)
              .then(
                (res) => {
                  that.dialogVisible = false;
                  loading.close();
                  that.fileIdCard = [];
                  that.filelistShop = [];
                  if (isResubmit) {
                    dealerListApi
                      .startAndTakeUserTaskByAddDealer({
                        approvalType: 'agree',
                        masterData: {
                          open_type: 'addOperations',
                          relation_id: res.data,
                          create_time: this.formatDate(new Date())
                        }
                      })
                      .then(() => {
                        that.$message.success('超级俱乐部审批流程开启~');
                      });
                  }
                  that.$router.push({
                    path: '/merchantManagement/operationsList'
                  });
                  that.$message.success('修改超级俱乐部成功');
                },
                (s) => {
                  if (s == 'error') {
                    loading.close();
                  }
                }
              )
              .catch((err) => {
                loading.close();
              });
          } else {
            console.log('error submit!!');
            //loading.close();
            return false;
          }
        });
      },

      //上传图片

      // 上传图片预览
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url;
        this.dialogUploadVisible = true;
      },

      //身份证照片开始
      //证件照上传
      uploadIdCardDetailHttp({ file }) {
        this.uploadLoadingIdCard = true;
        const that = this;
        const fileName = 'manage/' + Date.parse(new Date());
        that.$nextTick(function () {
          ossPrClient()
            .put(fileName, file)
            .then(({ res, url, name }) => {
              if (res && res.status === 200) {
                console.log(`阿里云OSS上传图片成功回调1`, res, url, name);
                if (!that.addOrUpdate) {
                  console.log(222222, file.uid);

                  that.fileIdCard.push({
                    uid: file.uid,
                    url: url
                  });
                } else {
                  // 新增上传图片
                  that.fileIdCard.push(name);
                }
                that.$nextTick(() => {
                  that.uploadLoadingIdCard = false;
                  // 清除身份证照片的校验信息
                  if (that.addOrUpdate) {
                    that.$refs.addMarketDate.clearValidate(['idCardPhoto']);
                  } else {
                    that.$refs.updateMarketDate.clearValidate(['idCardPhoto']);
                  }
                });
              }
            })
            .catch((err) => {
              that.$message.error('上传图片失败请检查网络或者刷新页面');
              console.log(`阿里云OSS上传图片失败回调`, err);
            });
        });
      },
      //照片限制
      // 上传图片数量超限
      justPictureNumIdCard(file, fileList) {
        this.$message.warning(`当前限制选择10个文件`);
      },
      handleRemoveDetailIdCard(file, fileList) {
        const that = this;
        if (!that.addOrUpdate) {
          that.fileIdCard = fileList;
        } else {
          for (let a = 0; a < that.fileIdCard.length; a++) {
            if (that.fileIdCard[a].substring(7, 17) == parseInt(file.uid / 1000)) {
              that.fileIdCard.splice(a, 1);
            }
          }
        }
        if (that.fileIdCard.length === 0) {
          that.$nextTick(() => {
            // 根据当前模式选择正确的表单引用
            const formRef = that.addOrUpdate ? 'addMarketDate' : 'updateMarketDate';
            that.$refs[formRef].validateField('idCardPhoto');
          });
        }
      }
      //门店照开始
      // uploadDetailHttpShop({ file }) {
      //   this.uploadLoadingShop = true;
      //   const that = this;
      //   const fileName = 'manage/' + Date.parse(new Date());
      //   that.$nextTick(function () {
      //     ossPrClient()
      //       .put(fileName, file)
      //       .then(({ res, url, name }) => {
      //         if (res && res.status === 200) {
      //           console.log(`阿里云OSS上传图片成功回调1`, res, url, name);
      //           if (!that.addOrUpdate) {
      //             that.filelistShop.push({
      //               uid: file.uid,
      //               url: url
      //             });
      //           } else {
      //             // 新增上传图片
      //             that.filelistShop.push(name);
      //           }
      //           that.$nextTick(() => {
      //             that.uploadLoadingShop = false;
      //           });
      //         }
      //       })
      //       .catch((err) => {
      //         that.$message.error('上传图片失败请检查网络或者刷新页面');
      //         console.log(`阿里云OSS上传图片失败回调`, err);
      //       });
      //   });
      // },
      //照片限制
      // 上传图片数量超限
      // justPictureNumShop(file, fileList) {
      //   this.$message.warning(`当前限制选择10个文件`);
      // },
      // handleRemoveDetailShop(file, fileList) {
      //   const that = this;
      //   if (!that.addOrUpdate) {
      //     that.filelistShop = fileList;
      //   } else {
      //     for (let a = 0; a < that.filelistShop.length; a++) {
      //       if (that.filelistShop[a].substring(7, 17) == parseInt(file.uid / 1000)) {
      //         that.filelistShop.splice(a, 1);
      //       }
      //     }
      //   }
      // }
      //门店照结束
    }
  };
</script>

<style lang="scss" scoped>
  .map-box {
    position: relative;
  }

  .search-box {
    position: absolute !important;
    top: 10px;
    left: 10px;
  }

  .container-card {
    padding: 15px 15px 15px 15px;
    margin-bottom: 15px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  }

  .red {
    color: red;
  }

  .green {
    color: green;
  }

  .blue {
    color: blue;
  }

  .prompt {
    padding: 10px;
  }
  ::v-deep .is-finish {
    .is-text {
      color: #fff;
      background-color: #1890ff;
    }
  }
  .result {
    position: absolute;
    top: 0;
    left: 100%;
    width: 300px;
    /* height: 450px; */
    margin-left: 10px;
    background-color: #ffffff;
    border: 1px solid silver;
  }

  .result-list {
    display: flex;
    align-items: center;
    /* margin-bottom: 10px; */
    line-height: 1.6;
    overflow: hidden;
    cursor: pointer;
  }

  .result label {
    display: block;
    width: 19px;
    height: 33px;
    margin-right: 10px;
    line-height: 20px;
    text-align: center;
    font-size: 12px;
    color: #ffffff;
    background: url('https://webapi.amap.com/theme/v1.3/markers/n/mark_bs.png') no-repeat center/cover;
  }

  .result-list.active label {
    background: url('http://webapi.amap.com/theme/v1.3/markers/n/mark_r.png') no-repeat center/cover;
  }

  .list-right {
    flex: 1;
  }

  .result .name {
    font-size: 14px;
    color: #565656;
  }

  .result .address {
    color: #999;
  }

  .search-table {
    height: 380px;
    margin-bottom: 10px !important;
  }

  .search-table th {
    display: none;
  }

  .search-table td {
    padding: 5px 0 !important;
    border-bottom: none;
  }

  .el-vue-search-box-container {
    width: 90% !important;
  }

  .el-date-editor.el-input {
    width: 100% !important;
  }

  .age-input-number .el-input__inner,
  .age-input-number input {
    text-align: left !important;
  }

  ::v-deep .age-input-number .el-input__inner {
    text-align: left !important;
  }

  ::v-deep .age-input-number input {
    text-align: left !important;
  }

  @media screen and (max-width: 767px) {
    .app-container {
      /* padding: 20px 10px; */
    }

    .result {
      display: none;
    }
  }
</style>
