<template>
  <div class="app-container">

    <div class="SearchForm">

      <div class="btn-add"  style="margin-bottom: 20px">
        <el-button :disabled="hasPassword" size="small"  type="primary" icon="el-icon-plus" @click="clickAdd(1)">添加</el-button>
      </div>

      <!-- 表格 -->
      <el-table class="common-table"  :data="tableData" v-loading="tableLoading">
        <el-table-column prop="passWord" label="授权码" sortable></el-table-column>
        <el-table-column prop="id" label="操作" sortable>
          <template>
            <el-button type="success" size="mini" icon="el-icon-edit-outline" @click="clickAdd(0)">编辑</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <el-dialog title="配置授权码" :visible.sync="showEdit" width="45%" :close-on-click-modal="false" @close="close">
      <el-form :ref="'addPwd'"  label-position="left"
               label-width="80px" style="width: 100%">
        <el-form-item label="授权码" prop="addPwd">
          <el-col :xs="24" :span="12">
            <el-input v-model="addPwd" clearable @keyup.native="inputChange($event)"
                      @keydown.native="inputChange($event)"></el-input>
          </el-col>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="setPassWord(addPwd)">确定</el-button>
        <el-button size="mini" @click="closeEdit">关闭</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import grammarApi from "@/api/grammar";
import {
  pageParamNames
} from "@/utils/constants";
export default {
  data() {
    return {
      tableLoading: false,
      updateSingle: {
        addPwd: [{
          required: true,
          message: '必填',
          trigger: 'blur'
        }]
      },
      isRouterAlive: true, //局部刷新
      tableData: [], //表格数据
      showEdit: false, //编辑弹窗
      hasPassword: false,//没有设置密码
      addPwd: '',//新增数据
    };
  },
  created() {
    this.fetchData();
  },
  methods: {
    // 查询表格列表
    fetchData() {
      const that = this;
      grammarApi.getPwd().then((res) => {
        that.tableData = res.data;
        that.tableLoading = false;
        if (that.tableData.length>0){
          that.hasPassword=true;
        }
      });
    },
    //添加操作
    clickAdd(isAdd) {
      if (isAdd===1){
        if (this.tableData.length===0){//新增数据
             this.showEdit = true;
        }else {
            alert("已有授权码，请编辑")
          }
      }else {
        this.addPwd = this.tableData[0].passWord;
        this.showEdit = true;
      }
    },
    setPassWord(ele) {
      const that = this
      grammarApi.setPassWord(ele).then((res) => {
        that.$nextTick(() => that.fetchData());
      });
      that.showEdit = false;
      that.reload();
    },
    closeEdit() {
      this.showEdit = false;
    },
    //局部刷新
    reload() {
      this.isRouterAlive = false;
      this.$nextTick(function() {
        this.isRouterAlive = true;
      });
    },
    //限制输入中文
    inputChange(e) {
      const o = e.target;
      o.value = o.value.replace(/[\u4E00-\u9FA5]/g, ''); // 清除除了中文以外的输入的字符
      this.name = o.value;
    },
    // 关闭弹窗
    close() {
      this.dialogVisible = false;
    },
  },
};
</script>

<style>
.lh36 {
  line-height: 36px;
  font-size: 14px;
}

.SearchForm {
  width: 100%;
  margin-bottom: 15px;
  background: #fff;
  border-radius: 6px;
  padding: 5px 0;
  padding-left: 5px;
  box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.2);
}

.btn-add {
  padding: 5px;
}

.red {
  color: red;
}

.green {
  color: green;
}

.course-table {
  text-align: center;
}

.course-table td,
.course-table th {
  padding: 5px 0;
  text-align: center;
}

.course-table button {
  padding: 2px;
}

.icon-stop {
  float: left;
  display: inline-block;
  width: 12px;
  height: 12px;
  background: url("../../icons/stop.png") no-repeat top center/contain;
}

.mt22 {
  margin-top: 22px;
}

.blue {
  margin-right: 50px;
  color: #409eff;
}
@media (max-width:767px) {
  .el-message-box{
    width: 80%!important;
  }
}
</style>
