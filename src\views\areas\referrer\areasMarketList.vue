<template>
  <div class="app-container">
    <el-form
      :inline="true"
      class="container-card"
      label-width="96px"
      label-position="left"
    >
      <el-form-item label="商户编号：">
        <el-input
          id="merchantCode"
          v-model="dataQuery.merchantCode"
          name="id"
          placeholder="请输入商户编号"
        />
      </el-form-item>

      <el-form-item label="负责人：">
        <el-input
          id="realName"
          v-model="dataQuery.realName"
          name="id"
          placeholder="请输入负责人"
        />
      </el-form-item>


      <el-form-item label="添加时间：">
        <el-date-picker
          v-model="regTime"
          type="daterange"
          align="right"
          unlink-panels
          value-format="yyyy-MM-dd HH:mm:ss"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="状态：">
        <el-select
          v-model="dataQuery.isEnable"
          placeholder="请选择"
          style="width: 185px"
        >
          <el-option
            v-for="item in [
              { value: 1, label: '开通' },
              { value: 0, label: '暂停' },
            ]"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="级别：">
        <el-select
          v-model="dataQuery.adminRank"
          placeholder="请选择"
          style="width: 185px"
        >
          <el-option
            v-for="item in [
              { value: 'A', label: 'A' },
              { value: 'B', label: 'B' },
               { value: 'C', label: 'C' },
              { value: 'D', label: 'D' },
               { value: 'E', label: 'E' },
              { value: 'F', label: 'F' },
            ]"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </el-form-item>


      <el-form-item label="所在地区：">
        <el-input
          id="name"
          v-model="dataQuery.address"
          name="id"
          placeholder="请输入推荐人所在地区"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="fetchData()"
        >搜索</el-button
        >
      </el-form-item>
    </el-form>

    <el-table
      v-loading="tableLoading"
      :data="tableData"
      style="width: 100%; margin-bottom: 20px"
      row-key="id"
      border
      default-expand-all
      :tree-props="{ list: 'children', hasChildren: 'true' }"
    >
      <el-table-column prop="merchantCode" label="编号" ></el-table-column>
      <el-table-column prop="name" label="登陆账号"></el-table-column>
      <el-table-column prop="merchantName" label="名称"></el-table-column>
      <el-table-column prop="realName" label="负责人"></el-table-column>
      <el-table-column prop="address" label="所在地区"></el-table-column>
      <el-table-column prop="address" label="推荐商户"></el-table-column>
      <el-table-column prop="rank" label="级别"></el-table-column>
      <el-table-column prop="sumChargeMoney" label="累计充值金额（元）"></el-table-column>
      <el-table-column prop="SumRebateMoney" label="累计返点金额（元）"></el-table-column>
      <el-table-column prop="accountMoney" label="账户余额（元）"></el-table-column>
      <el-table-column prop="withdrawMoneyOl" label="线上余额（元）"></el-table-column>
      <el-table-column prop="subDealerCount" label="下级托管中心"></el-table-column>
      <el-table-column prop="subSchoolCount" label="下级门店"></el-table-column>
      <el-table-column prop="tJCount" label="推荐商户"></el-table-column>
      <el-table-column prop="isCheck" label="审核状态"></el-table-column>
      <el-table-column prop="isEnableStatus" label="账户状态"></el-table-column>
      <el-table-column prop="regTime" label="添加时间"></el-table-column>
    </el-table>

    <!-- 分页 -->
    <el-col :span="20">
      <el-pagination
        :current-page="tablePage.currentPage"
        :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="tablePage.totalItems"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-col>
  </div>
</template>

<script>
  import Tinymce from "@/components/Tinymce";
  import marketApi from "@/api/areasMarketList";
  import memberApi from "@/api/member";
  import { pageParamNames } from "@/utils/constants";
  export default {
    data() {
      return {
        tableLoading: false,
        showLoginName: false, //修改账号弹框
        updateLoginName: {}, //修改账号数据
        rulesLoginName: {}, //修改账号表单验证
        // 分页
        tablePage: {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null,
        },
        addOrUpdate: false,
        dataQuery: {},
        dialogVisible: false,
        showLoginAccount: false, //修改登陆账号
        province: [],
        regTime: [], //增加时间
        city: [],
        areaList: [],
        tableData: [],
        updateMarketDate: {},
        addMarketDate: {},
        exportLoading: false,
        provinceStr: "", //省
        cityStr: "", //市
        areaStr: "", //区
        rulersupdate: {},
        rules: {
          name: [
            {
              required: true,
              message: "必填",
              trigger: "change",
            },
          ],
          merchantName: [
            {
              required: true,
              message: "必填",
              trigger: "change",
            },
          ],
          realName: [
            {
              required: true,
              message: "必填",
              trigger: "blur",
            },
          ],
          idCard: [
            {
              required: true,
              message: "必填",
              trigger: "blur",
            },
          ],
          description: [
            {
              required: true,
              message: "必填",
              trigger: "blur",
            },
          ],
          address: [
            {
              required: true,
              message: "必填",
              trigger: "blur",
            },
          ],
        },
      };
    },
    created() {
      this.fetchData();
      //获取省
      //this.getProvince();
    },
    methods: {
      // 查询提现列表
      fetchData() {
        const that = this;
        var a = that.regTime;
        if (a != null) {
          that.dataQuery.startRegTime = a[0];
          that.dataQuery.endRegTime = a[1];
        }
        that.tableLoading = true;
        marketApi
          .marketList(
            that.tablePage.currentPage,
            that.tablePage.size,
            that.dataQuery
          )
          .then((res) => {
            that.tableData = res.data.data;
            // console.log(res)
            that.tableLoading = false;
            // 设置后台返回的分页参数
            pageParamNames.forEach((name) =>
              that.$set(that.tablePage, name, parseInt(res.data[name]))
            );
          });
      },




      // 分页
      handleSizeChange(val) {
        this.tablePage.size = val;
        this.fetchData();
      },
      handleCurrentChange(val) {
        this.tablePage.currentPage = val;
        this.fetchData();
      },



    },
  };
</script>

<style>
  .container-card {
    padding: 15px 15px 15px 15px;
    margin-bottom: 15px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  }
</style>
