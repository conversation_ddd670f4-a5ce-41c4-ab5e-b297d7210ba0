<template>
  <div class="app-container">
    <el-form :model="query" ref="form" label-width="80px" :inline="true" size="small">
      <el-form-item label="月份">
        <el-date-picker v-model="query.month" type="month" placeholder="选择月"></el-date-picker>
      </el-form-item>
      <el-form-item label="门店编号">
        <el-input v-model="query.code"></el-input>
      </el-form-item>
      <el-form-item label="门店名称">
        <el-input v-model="query.name"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="serach">搜索</el-button>
        <el-button @click="reset">重置</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="list" stripe :header-cell-style="{ background: '#eef1f6', color: '#606266' }">
      <el-table-column v-for="col in columns" :prop="col.id" :key="col.id" :label="col.label" :width="col.width"></el-table-column>
    </el-table>
    <el-row>
      <el-col :span="20">
        <el-pagination
          :current-page="query.page"
          :page-sizes="[10, 20, 30, 40, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="query.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </el-col>
    </el-row>
  </div>
</template>

<script>
  export default {
    data() {
      return {
        query: {
          pageNum: 1,
          pageSize: 10,
          total: 0
        },
        list: [],
        columns: [
          { id: 'month', label: '月份' },
          { id: 'code', label: '门店编号' },
          { id: 'name', label: '门店名称' },
          { id: 'total', label: '总订单数' },
          { id: 'totalAmount', label: '负责人' },
          { id: 'totalPayment', label: '所属俱乐部' },
          { id: 'totalPayment', label: '已售学时（节）' },
          { id: 'totalPayment', label: '账期内余额（元）' },
          { id: 'totalPayment', label: '账期外余额（元）' },
          { id: 'totalPayment', label: '可提现金额（元）' },
          { id: 'totalPayment', label: '门店添加时间' },
          { id: 'totalPayment', label: '到期时间时间' },
          { id: 'totalPayment', label: '退费申请时间' }
        ]
      };
    },

    created() {},

    methods: {
      serach() {},
      reset() {},

      handleSizeChange(val) {
        this.query.pageSize = val;
        // this.index();
      },
      handleCurrentChange(val) {
        this.query.page = val;
        //   this.index();
      }
    }
  };
</script>

<style>
  .container-card {
    padding: 15px 15px 15px 15px;
    margin-bottom: 15px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  }

  .red {
    color: red;
  }

  .green {
    color: green;
  }

  .blue {
    color: blue;
  }

  @media (max-width: 767px) {
    .el-message-box {
      width: 80% !important;
    }
  }
</style>
