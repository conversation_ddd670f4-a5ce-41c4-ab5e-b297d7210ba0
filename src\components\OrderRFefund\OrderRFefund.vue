<template>
  <el-dialog :visible.sync="visible" :width="dialogWidth" @close="handleClose" append-to-body :close-on-click-modal="false" class="reminderDialog transferDropDialog__wrapper">
    <!-- 弹窗标题 -->
    <div class="transferDropDialog-title">
      {{ dialogTitle }}
    </div>

    <!-- 弹窗内容（表单） -->
    <div class="transferDropDialog-content">
      <el-form label-position="left" :model="refundForm" :rules="refundRules" ref="refundFormRef" label-width="160px">
        <!-- 退学时数 -->
        <el-form-item label="退学时数:" :label-style="{ fontWeight: 'bold' }">
          {{ refundData.tuiCourse || 0 }}
        </el-form-item>

        <!-- 退交付学时数 -->
        <el-form-item label="退交付学时数:" :label-style="{ fontWeight: 'bold' }">
          {{ refundData.tuiDeliverHour || 0 }}
        </el-form-item>

        <!-- 退自行交付学时数 -->
        <el-form-item label="退自行交付学时数:" :label-style="{ fontWeight: 'bold' }">
          {{ refundData.tuiSelfDeliverHour || 0 }}
        </el-form-item>

        <!-- 您需退款金额 -->
        <el-form-item label="您需退款金额(元):" :label-style="{ fontWeight: 'bold' }">
          {{ refundData.totalRefundAmount || 0 }}
        </el-form-item>

        <!-- 系统退款金额（重复字段已保留原结构，可根据实际需求调整） -->
        <el-form-item label="系统退款金额(元):" :label-style="{ fontWeight: 'bold' }">
          {{ refundData.notAgentRefundAmount || 0 }}
        </el-form-item>

        <!-- 家长手机号（脱敏展示） -->
        <el-form-item label="家长手机号:" :label-style="{ fontWeight: 'bold' }">
          {{ refundData.buyPhone }}
        </el-form-item>

        <!-- 验证码输入 -->
        <el-form-item label="验证码:" prop="smsCode" :error="verifyCodeError" :label-style="{ fontWeight: 'bold' }">
          <el-input v-model="refundForm.smsCode" placeholder="请输入验证码" style="width: 400px" maxlength="6" @input="handleCodeInput">
            <el-button slot="append" :class="[countdown > 0 ? '' : 'btn-send-code']" :disabled="countdown > 0" @click="sendVerifyCode">
              {{ countdown > 0 ? `${countdown}s后重发` : '发送验证码' }}
            </el-button>
          </el-input>
        </el-form-item>
      </el-form>
    </div>

    <!-- 弹窗底部按钮 -->
    <div slot="footer" class="dialog-footer">
      <el-button size="mini" @click="handleClose">取消</el-button>
      <el-button size="mini" type="primary" @click="handleSubmit" :loading="loading">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
  export default {
    name: 'OrderRFefund', // 组件名
    props: {
      visible: {
        type: Boolean,
        required: true,
        default: false
      },
      // 弹窗宽度（默认36%）
      dialogWidth: {
        type: String,
        default: '36%'
      },
      // 弹窗标题（支持自定义，默认原标题）
      dialogTitle: {
        type: String,
        default: '1年以上订单，门店需先线下退款给家长，再通过家长验证码线上申请退款'
      },
      // 退款相关数据（父组件传入，包含学时、金额、手机号等）
      refundData: {},
      // 提交按钮是否禁用（父组件可控制额外禁用逻辑）
      // submitDisabled: {
      //   type: Boolean,
      //   default: false
      // },

      RefundSelfDeliver: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        loading: false, // 提交按钮loading状态
        refundForm: {
          smsCode: '' // 验证码输入值
        },
        refundRules: {
          // 验证码校验规则
          smsCode: [
            { required: true, message: '请输入验证码', trigger: 'blur' },
            { pattern: /^\d{4}$/, message: '请输入正确的验证码', trigger: 'blur' }
          ]
        },
        countdown: 0, // 验证码倒计时（秒）
        verifyCodeError: '', // 验证码自定义错误信息（如“验证码无效”）
        refundFormRef: null // 表单引用
      };
    },
    // computed: {
    //   // 最终提交按钮禁用状态（倒计时中/父组件禁用/表单未填）
    //   isSubmitDisabled() {
    //     return this.countdown > 0 || this.submitDisabled;
    //   }
    // },
    methods: {
      /**
       * 发送验证码
       */
      sendVerifyCode() {
        let params = {
          orderId: this.refundData.orderId, // this.refundData.orderId,
          amount: this.refundData.notAgentRefundAmount
        };
        // 调用父组件传入的验证码发送接口（通过事件通知父组件）
        this.$emit('send-verify-code', params, (success) => {
          if (success) {
            this.$message.success('验证码已发送至手机，请注意查收');
            // 启动倒计时（60秒）
            this.countdown = 60;
            const timer = setInterval(() => {
              this.countdown--;
              if (this.countdown <= 0) {
                clearInterval(timer);
              }
            }, 1000);
          } else {
            // this.$message.error('验证码发送失败，请稍后重试');
          }
        });
      },
      handleSendCode() {
        // 调用发送验证码API
        sendVerifyCodeApi(this.refundData.parentPhone).then((res) => {
          if (res.success) {
            // 发送成功，开始倒计时
            this.$refs.verifyCode.startCountdown();
            this.$message.success('验证码发送成功');
          } else {
            this.$message.error(res.message || '验证码发送失败');
          }
        });
      },

      /**
       * 验证码输入处理（清空自定义错误）
       */
      handleCodeInput() {
        this.verifyCodeError = '';
      },

      /**
       * 提交退款申请
       */
      handleSubmit() {
        // 表单基础校验
        this.$refs.refundFormRef.validate((isValid) => {
          if (!isValid) return;
          this.loading = true;
          // 校验通过，组装提交数据并通知父组件
          const submitData = {
            smsCode: this.refundForm.smsCode // 验证码
          };

          // 触发提交事件，由父组件处理实际接口请求
          this.$emit('submit-refund', submitData, (isSuccess, errorMsg = '') => {
            if (isSuccess) {
              let message = this.RefundSelfDeliver ? '系统退款中，请到退款列表查看后续退款进度' : '已提交交付中心审核';
              this.$message.success(message);
              this.handleClose(); // 提交成功后关闭弹窗
            } else {
              this.loading = false;
              // 显示自定义错误（如验证码无效）
              this.verifyCodeError = errorMsg || '退款申请提交失败，请稍后重试';
            }
          });
        });
      },

      /**
       * 关闭弹窗（重置状态 + 通知父组件）
       */
      handleClose() {
        // 重置表单状态
        this.refundForm.smsCode = '';
        this.verifyCodeError = '';
        this.countdown = 0; // 清除倒计时
        if (this.$refs.refundFormRef) {
          this.$refs.refundFormRef.resetFields();
        }
        this.loading = false; // 重置加载状态
        this.$emit('close-dialog');
        // this.$emit('update:visible', false);
      }
    }
  };
</script>

<style scoped>
  .transferDropDialog__wrapper {
    /* 弹窗基础样式 */
  }

  .transferDropDialog-title {
    font-size: 16px;
    font-weight: bold;
    color: #333;
    margin-bottom: 20px;
    line-height: 1.5;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .transferDropDialog-content {
    margin-bottom: 10px;
  }

  .dialog-footer {
    text-align: right;
  }

  .transferDropDialog-content {
    margin-bottom: 10px;
  }

  .dialog-footer {
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: right;
    padding-right: 10px;
  }

  /* 适配验证码输入框 */
  ::v-deep .el-input--suffix .el-input__inner {
    padding-right: 120px !important;
  }
  .btn-send-code {
    background-color: #1890ff !important;
    color: #fff !important;
  }
  ::v-deep .el-form-item__label {
    font-weight: bold;
  }
</style>
