<template>
  <div class="app-container">
    <el-form :inline="true" class="container-card" label-width="110px" label-position="left">
      <el-form-item label="定金支付状态：">
        <el-select v-model="dataQuery.natureStatus" clearable filterable placeholder="请选择,可以搜索">
          <el-option v-for="item in payStatusOption" :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="fetchData()">搜索</el-button>
      </el-form-item>
    </el-form>

    <el-table class="common-table" :data="tableData" style="margin-bottom: 20px;" row-key="id" stripe border
      :tree-props="{ list: 'children', hasChildren: 'true' }" v-loading="tableLoading">
      <el-table-column prop="name" label="姓名"></el-table-column>
      <el-table-column prop="idCard" label="身份证号"></el-table-column>
      <el-table-column prop="withdrawnBonus" label="操作" width="150px">
        <template slot-scope="scope">
          <!-- 更新状态 -->
          <el-button type="warning" size="mini" icon="el-icon-switch-button"
            v-if="scope.row.type == '开店' && scope.row.natureStatus == '待确认尾款支付'" @click="confirm(scope.row)">确认尾款到账
          </el-button>
        </template>
      </el-table-column>
      <el-table-column prop="phone" label="手机号"></el-table-column>
      <!--      <el-table-column prop="referralCode" label="推荐人编号"></el-table-column>-->
      <el-table-column prop="referralPhone" label="推荐人手机号"></el-table-column>
      <el-table-column prop="level" label="店等级"></el-table-column>
      <el-table-column prop="payReal" label="实付款金额"></el-table-column>
      <el-table-column prop="allinpayFree" label="手续费"></el-table-column>
      <el-table-column prop="payTime" label="支付时间"></el-table-column>
      <el-table-column prop="status" label="支付状态"></el-table-column>
      <el-table-column prop="type" label="类型"></el-table-column>
      <el-table-column prop="cityName" label="城市"></el-table-column>
      <el-table-column prop="natureStatus" label="状态"></el-table-column>
      <el-table-column prop="remark" label="备注"></el-table-column>
    </el-table>

    <!-- 分页 -->
    <el-col :span="24" style="overflow-x: auto;" :xs="24">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>
  </div>
</template>

<script>

import checkPermission from '@/utils/permission'
import cardApi from "@/api/cardApi";
import bvAdminApi from "@/api/bvAdminApi";
import Tinymce from "@/components/Tinymce";
import { pageParamNames } from "@/utils/constants";
import offSiteApi from "@/api/offSiteApi";
import store from '@/store'
import { baseUrl } from "@/utils/constants";

export default {
  name: 'studentList',
  watch: {
    dataQuery: {
      handler(n, o) {
        this.tablePage.currentPage = 1
      },
      immediate: true,
      deep: true
    }
  },
  data() {
    return {
      baseUrl: baseUrl,
      myheard: {
        'x-www-iap-assertion': store.getters.token,
        'www-cid': 'dx_znyy_resource'
      },
      claimPrice: 0,
      claimAllMany: 0,
      showClaim: false,
      claimTypeId: -1,
      claimCount: 0,
      payOption: [
        { label: '正常支付', value: '1' },
        { label: '分笔支付', value: '2' },
        { label: '定金支付', value: '3' },
      ],
      options: [
        { label: '开店订单', value: '1' },
        { label: '充值订单', value: '2' },
      ],
      payStatusOption: [
        { label: '未支付', value: '11' },
        { label: '待确认', value: '12' },
        { label: '已确认', value: '1' },
      ],
      typeValue: '',
      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      typeText: [],
      tableData: {},
      dataQuery: {
        nature: '',
        endDate: '',
        isEnable: '',
        isFormal: '',
        loginName: '',
        realName: '',
        memberCode: '',
        memberPhone: '',
        restrictedUse: ''
      },
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }]
      },
      useTime: '',
      bvAdminOption: []
    };
  },
  created() {
    this.fetchData();
    this.getCardType();
    this.getBvAdminOption();

  },
  methods: {
    confirm(value) {
      this.$confirm('确定已付清尾款?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        offSiteApi.confirm(value.id).then(res => {
          if (!res.success) {
            this.$message.error(res.message);
          } else {
            this.fetchData();
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消'
        });
      });


    },
    checkPermission,
    handleClose(done) {
      this.$confirm('确认关闭？')
        .then(_ => {
          this.popUps_show = false;
        })
        .catch(_ => {
        });
    },

    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val;
      this.fetchData();
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val;
      this.fetchData();
    },
    // 查询提现列表
    fetchData() {
      const that = this;
      that.tableLoading = true
      that.dataQuery.pageNum = that.tablePage.currentPage
      that.dataQuery.pageSize = that.tablePage.size
      that.dataQuery.noRef = true;
      that.dataQuery.type = 1;
      that.dataQuery.nature = 3;
      offSiteApi.offSiteList(that.dataQuery).then(res => {
        console.log(res);
        that.tableData = res.data.data
        that.tableLoading = false
        // 设置后台返回的分页参数
        pageParamNames.forEach(name => that.$set(that.tablePage, name, parseInt(res.data[name])))
      })
    },
  }
};
</script>

<style>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.period-table td,
.period-table th {
  text-align: center;
}

.mt20 {
  margin-top: 20px;
}

.red {
  color: red;
}

.green {
  color: green;
}

.yellow {
  color: darkorange;
}

@media (max-width: 767px) {
  .el-message-box {
    width: 80% !important;
  }
}
</style>
