<template>
  <div>
    <el-form
      label-position="right"
      label-width="120px"
      style="width: 100%"
    >
      <el-form-item label="登录账号：" prop="name">
        <el-col :xs="24" >
          <el-input v-model="form.name" readonly/>
        </el-col>
      </el-form-item>
      <el-form-item label="门店名称：" prop="merchantName">
        <el-col :xs="24" >
          <el-input
            v-model="form.merchantName"
            readonly
            placeholder="请输入：市+商圈/路名/学校名称+门店"
          />
        </el-col>
      </el-form-item>
      <el-form-item label="负责人：" prop="realName">
        <el-col :xs="24" >
          <el-input v-model="form.realName" readonly/>
        </el-col>
      </el-form-item>
      <el-form-item label="身份证：" prop="idCard">
        <el-col :xs="24" >
          <el-input v-model="form.idCard" readonly/>
        </el-col>
      </el-form-item>
      <el-form-item label="上级编号：" prop="refereeCode">
        <el-col :xs="24" >
          <el-input v-model="form.refereeCode" readonly/>
        </el-col>
      </el-form-item>
      <el-form-item label="推荐人：" prop="marketPartner">
        <el-col :xs="24" >
          <el-input v-model="form.marketPartner" readonly/>
        </el-col>
      </el-form-item>
      <el-form-item label="是否教学交付能力：" prop="canTeach">
        <template>
          <el-radio v-model="form.canTeach" disabled :label="true">
            开通
          </el-radio>
          <el-radio v-model="form.canTeach" disabled :label="false">
            暂停
          </el-radio>
        </template>
      </el-form-item>
      <el-form-item label="签约时间：" v-show="false" prop="">
        <el-col :xs="24" >
          <el-date-picker
            readonly
            v-model="form.signupDate"
            value-format="yyyy-MM-dd HH:mm:ss"
            type="datetime"
            placeholder="选择日期"
          >
          </el-date-picker>
        </el-col>
      </el-form-item>
      <el-form-item label="到期时间：" v-show="false" prop="">
        <el-col :xs="24" >
          <el-date-picker
            readonly
            v-model="form.expireDate"
            value-format="yyyy-MM-dd HH:mm:ss"
            type="datetime"
            placeholder="选择日期"
          >
          </el-date-picker>
        </el-col>
      </el-form-item>
      <el-form-item label="合同照片:">
        <el-col :span="20">
          <el-image
            style="width: 100px; height: 100px"
            :src="form.contractPhoto[0]"
            :preview-src-list="form.contractPhoto">
          </el-image>
        </el-col>
      </el-form-item>
      <el-form-item label="证件照片:">
        <el-col :span="20">
          <el-image
            style="width: 100px; height: 100px"
            :src="form.idCardPhoto[0]"
            :preview-src-list="form.idCardPhoto">
          </el-image>
        </el-col>
      </el-form-item>
      <el-form-item label="门店环境照片:">
        <el-col :span="20">
          <el-image
            style="width: 100px; height: 100px"
            :src="form.shopPhoto[0]"
            :preview-src-list="form.shopPhoto">
          </el-image>
        </el-col>
      </el-form-item>
      <el-form-item label="所在地区：" prop="province">
        <el-col :xs="24" >
          <el-row :gutter="10">
            <el-col :xs="24" :span="8">
              <el-input
                readonly
                placeholder="安徽省"
                v-model="form.province"
              />
            </el-col>
            <el-col :xs="24" :span="8">
              <el-input
                readonly
                placeholder="合肥市"
                v-model="form.city"
              />
            </el-col>
            <el-col :xs="24" :span="8">
              <el-input
                readonly
                placeholder="包河区"
                v-model="form.area"
              />
            </el-col>
          </el-row>
        </el-col>
      </el-form-item>
      <el-form-item label="地址：" prop="address">
        <el-col :xs="24" :span="12">
          <el-input v-model="form.address" readonly/>
        </el-col>
      </el-form-item>
      <el-form-item label="开户金额" prop="openMoney">
        <el-col :span="7">
          <el-input
            readonly
            v-model="form.openMoney"
            type="number"
            maxlength="20"
            isNumber2="true"
            min="1"
          />
        </el-col>
      </el-form-item>
      <el-form-item label="门店简介：" prop="name">
        <el-col :xs="24">
          <el-input
            readonly
            type="textarea"
            resize="none"
            :rows="4"
            v-model="form.description"
          />
        </el-col>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: "FormAddDirectSchool",
  props: {
    // 表单
    form: {
      type: Object
    },
  },
}
</script>

<style scoped>

</style>
