<template>
  <div class="app-container">
    <!-- 搜索 -->
    <el-form :inline="true" ref="SearchForm" :model="dataQuery" class="SearchForm">
      <el-row style="padding: 20px 20px 0 20px">
        <el-col :span="8" :xs="24">
          <el-form-item label="课程类型名称:">
            <el-input v-model.trim="dataQuery.enName" placeholder="请输入课程类型名称" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24" style="text-align: right">
          <el-button type="primary" icon="el-icon-search" @click="fetchData()">搜索</el-button>
          <el-button type="" plain icon="el-icon-search" @click="reset()">重置</el-button>
        </el-col>
      </el-row>
    </el-form>

    <div class="SearchForm">
      <!-- 添加 -->
      <div class="btn-add" style="margin-bottom: 10px">
        <el-button size="small" type="primary" icon="el-icon-plus" @click="clickAdd">新增课程类型</el-button>
      </div>
      <!-- 表格 -->
      <el-table class="common-table" :data="tableData" stripe border v-loading="tableLoading">
        <el-table-column prop="enName" label="课程类型" width="150px"></el-table-column>
        <el-table-column prop="enCode" label="课程码"></el-table-column>
        <el-table-column prop="usageScenario" label="运行平台" :formatter="usageScenarioFormatter"></el-table-column>
        <!-- <el-table-column label="操作" width="300">
          <template slot-scope="scope">
            <el-button type="primary" size="mini" icon="el-icon-edit-outline" @click="handleUpdate(scope.row.id)">编辑</el-button>
          </template>
        </el-table-column> -->
        <!-- <el-table-column prop="addTime" label="最近修改时间"></el-table-column> -->
        <el-table-column prop="isEnable" label="课程模式">
          <template slot-scope="scope">
            <span v-if="scope.row.extraSecond == 1">1v多</span>
            <span v-else>1v1</span>
          </template>
        </el-table-column>
        <el-table-column prop="isEnable" label="是否启用">
          <template slot-scope="scope">
            <!-- <span class="green" v-if="scope.row.isEnable == 1">是</span>
            <span class="red" v-else>否</span> -->
            <el-switch
              @change="changeStatus(scope.row)"
              v-model="scope.row.isEnable"
              :active-value="1"
              :inactive-value="0"
              active-text="是"
              inactive-text="否"
              active-color="#13ce66"
            ></el-switch>
          </template>
        </el-table-column>
        <el-table-column prop="isEnable" label="交付方式">
          <template slot-scope="scope">
            <span v-if="scope.row.extraFirst == 1">线上交付</span>
            <span v-else-if="scope.row.extraFirst == 0">线下交付</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <el-col :span="20">
      <el-pagination
        :current-page="dataQuery.pageNum"
        :page-sizes="[10, 20, 30, 40, 50]"
        :page-size="dataQuery.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-col>

    <!-- 编辑或者添加弹窗 -->
    <el-dialog :title="addCourseData.id ? '编辑课程类型' : '添加课程类型'" :visible.sync="dialogVisible" width="30%" :close-on-click-modal="false" @close="close">
      <el-form ref="addCourseData" :model="addCourseData" label-position="left" label-width="120px" style="width: 100%" :rules="rules">
        <el-form-item label="课程类型" prop="enName">
          <el-col :xs="24" :lg="18">
            <el-input v-model="addCourseData.enName" />
          </el-col>
        </el-form-item>
        <el-form-item label="课程昵称" prop="productName">
          <el-col :xs="24" :lg="18">
            <el-input v-model="addCourseData.productName" />
          </el-col>
        </el-form-item>
        <el-form-item label="课程码" prop="enCode">
          <el-col :xs="24" :lg="18">
            <el-input v-model="addCourseData.enCode" />
          </el-col>
        </el-form-item>
        <!-- <el-form-item label="是否启用" prop="isEnable">
          <el-radio-group v-model="addCourseData.isEnable">
            <el-radio :label="1">是</el-radio>
            <el-radio :label="0">否</el-radio>
          </el-radio-group>
        </el-form-item> -->
        <el-form-item label="运行平台" prop="usageScenario" v-if="addCourseData.extraFirst == 1">
          <el-checkbox-group v-model="addCourseData.usageScenario" @change="runLandChange">
            <el-checkbox label="pad"></el-checkbox>
            <el-checkbox label="pc"></el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="课程模式" prop="extraSecond">
          <el-radio-group v-model="addCourseData.extraSecond" :disabled="!!addCourseData.id">
            <el-radio label="1">1v多</el-radio>
            <el-radio label="0">1v1</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="交付方式" prop="extraFirst">
          <el-radio-group v-model="addCourseData.extraFirst" :disabled="!!addCourseData.id">
            <el-radio label="1">线上交付</el-radio>
            <el-radio label="0">线下交付</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" plain @click="close" style="width: 100px">取消</el-button>
        <el-button size="mini" type="primary" @click="onSubmit()" style="width: 100px">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import { getCourseCateList, getCourseCateByName, getCourseCateDetail, addCourseCate, editCourseCate, changeCourseStatus } from '@/api/courseCate';
  import { pageParamNames } from '@/utils/constants';
  export default {
    name: 'courseCate',
    data() {
      return {
        // 分页
        tablePage: {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null
        },
        total: 0,
        tableLoading: false,
        total: 0,
        dataQuery: {
          pageNum: 1,
          pageSize: 10,
          enName: ''
        },
        tableData: [], //表格数据
        dialogVisible: false, // 修改弹窗是否展示
        // 新增课程
        addCourseData: {
          enName: '',
          enCode: '',
          isEnable: 0,
          productName: '',
          usageScenario: [],
          extraFirst: '',
          extraSecond: ''
        },

        rules: {
          enName: [{ required: true, message: '请输入课程类型名称', trigger: 'blur' }],
          enCode: [{ required: true, message: '请输入课程码', trigger: 'blur' }],
          productName: [{ required: true, message: '请输入课程码', trigger: 'blur' }]
        }
      };
    },
    created() {
      this.initData();
    },
    methods: {
      // 修改状态
      changeStatus(row) {
        // console.log('purchase/procurementAllocation', row);
        /**
         * id 课程id
         * isEnable 0禁用 1启用
         */
        changeCourseStatus({
          id: row.id,
          isEnable: row.isEnable
        })
          .then((res) => {
            if (res.data) {
              this.$message({
                message: '修改成功',
                type: 'success'
              });
              this.initData();
            } else {
              this.$alert('该课程类型未完成分润配置，请前往鼎校甄选管理端配置后重试启用', '提示', {
                confirmButtonText: '确定'
              })
                .then(() => {
                  this.initData();
                })
                .catch(() => {
                  this.initData();
                });
              // this.$confirm('该课程分类未完成分润配置，请配置后重试启用', '提示', {
              //   confirmButtonText: '确定',
              //   cancelButtonText: '取消',
              //   type: 'none'
              // })
              //   .then(() => {
              //     // 跳转到系统配置页面
              //     this.$router.push({
              //       path: '/finance/systemConfiguration'
              //     });
              //     this.initData();
              //   })
              //   .catch(() => {
              //     this.initData();
              //   });
            }
          })
          .catch((err) => {});
      },
      //运行平台
      runLandChange(value) {
        console.log(111);
        console.log(value);
      },
      usageScenarioFormatter(row, column, cellValue, index) {
        if (cellValue) {
          let list = JSON.parse(cellValue);
          let text = '';
          for (let i = 0; i < list.length; i++) {
            text += list[i];
            text += i === list.length - 1 ? '' : '、';
          }

          return text.length == 0 ? '无' : text;
        }
        return '无';
      },
      // 初始化列表
      async initData() {
        const that = this;
        that.tableLoading = true;
        // that.dataQuery.pageNum = that.tablePage.currentPage
        // that.dataQuery.pageSize = that.tablePage.size
        const { data } = await getCourseCateList(this.dataQuery);
        // console.log(data, '1111111111111111111111')
        that.tableData = data.data;
        that.total = Number(data.totalItems);
        // 设置后台返回的分页参数
        that.tableLoading = false;
        // pageParamNames.forEach(name => that.$set(that.tablePage, name, parseInt(data[name])))
      },
      // 查询+搜索课程列表
      fetchData() {
        const that = this;
        that.tableLoading = true;
        that.dataQuery.pageNum = 1;
        getCourseCateByName(that.dataQuery)
          .then((res) => {
            that.tableData = res.data.data;
            // console.log(res)
            // that.tableData = data.data;
            that.total = Number(res.data.totalItems);
            // 设置后台返回的分页参数
            that.tableLoading = false;
            console.log(that.tableLoading);
            // 设置后台返回的分页参数
            // pageParamNames.forEach((name) => that.$set(that.tablePage, name, parseInt(res.data[name])));
          })
          .catch((err) => {
            that.tableLoading = false;
          });
      },
      //添加操作
      clickAdd() {
        // this.$nextTick(() => this.$refs['addCourseData'].resetField())
        this.addCourseData = {
          enName: '',
          enCode: '',
          isEnable: 0,
          productName: '',
          usageScenario: [],
          extraFirst: '',
          extraSecond: ''
        };
        this.dialogVisible = true;
      },
      // 新增课程提交
      onSubmit() {
        let data = {};
        data = this.addCourseData.id
          ? {
              id: this.addCourseData.id,
              enName: this.addCourseData.enName,
              productName: this.addCourseData.productName,
              isEnable: this.addCourseData.isEnable,
              enCode: this.addCourseData.enCode,
              extraFirst: this.addCourseData.extraFirst,
              extraSecond: this.addCourseData.extraSecond
            }
          : {
              enName: this.addCourseData.enName,
              productName: this.addCourseData.productName,
              isEnable: this.addCourseData.isEnable,
              enCode: this.addCourseData.enCode,
              extraFirst: this.addCourseData.extraFirst,
              extraSecond: this.addCourseData.extraSecond
            };
        console.log(this.addCourseData.usageScenario);
        data.usageScenario = JSON.stringify(this.addCourseData.usageScenario);
        this.$refs.addCourseData.validate(async (vaild) => {
          if (vaild) {
            this.addCourseData.id ? await editCourseCate(data) : await addCourseCate(data);
            this.$message.success('操作成功');
            this.dataQuery.pageNum = 1;
            this.initData();
            this.close();
          } else {
            return this.$message.error({ showClose: true, message: '请按规范填写数据', type: 'error' });
          }
        });
      },
      // 点击编辑按钮
      async handleUpdate(id) {
        const res = await getCourseCateDetail(id);
        // console.log(res)
        this.addCourseData = res.data;
        if (this.addCourseData.usageScenario) {
          this.addCourseData.usageScenario = JSON.parse(this.addCourseData.usageScenario);
        } else {
          this.addCourseData.usageScenario = [];
        }
        this.dialogVisible = true;
      },

      // 分页
      handleSizeChange(val) {
        this.dataQuery.pageNum = 1;
        this.dataQuery.pageSize = val;
        this.initData();
      },
      handleCurrentChange(val) {
        this.dataQuery.pageNum = val;
        this.initData();
      },
      reset() {
        this.dataQuery = {
          pageNum: 1,
          pageSize: 10,
          enName: ''
        };
        this.initData();
      },
      // 关闭弹窗
      close() {
        this.addCourseData = {
          categoryName: '',
          isEnable: 1,
          usageScenario: []
        };
        this.dialogVisible = false;
      }
    }
  };
</script>

<style lang="scss" scoped>
  .SearchForm {
    width: 100%;
    margin-bottom: 15px;
    background: #fff;
    border-radius: 6px;
    padding: 5px 0;
    padding-left: 5px;
    box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.2);
  }

  .btn-add {
    padding: 5px;
  }

  .red {
    color: red;
  }

  .green {
    color: green;
  }
  ::v-deep .el-dialog__title {
    font-weight: 600 !important;
    font-size: 15px !important;
  }

  ::v-deep .el-dialog__headerbtn .el-dialog__close {
    border-radius: 50%;
    background: #ccc;
  }
</style>
