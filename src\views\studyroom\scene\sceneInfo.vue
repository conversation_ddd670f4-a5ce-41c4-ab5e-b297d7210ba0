<template>
  <div class="app-container">
    <el-row>
      <el-col :span="24" style="text-align: left;margin-bottom: 30px;">
        <el-button type="success" icon="el-icon-plus" size="mini" @click="clickAdd">新增场景</el-button>
      </el-col>
    </el-row>
    <el-table v-loading="tableLoading" :data="tableData" border style="width: 100%;margin-bottom: 30px;">
      <el-table-column prop="id" label="场景ID" align="center" />
      <el-table-column prop="picUrl" label="场景图片" align="center">
        <template slot-scope="scope">
          <el-image :src="scope.row.picUrl" :preview-src-list="[scope.row.picUrl]" class="img-lg"/>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="350" fixed="right">
        <template slot-scope="scope">
          <el-tooltip content="编辑" placement="top">
            <el-button type="text" size="mini" @click="changeEnabled(scope.row)" v-if="scope.row.enabled" style="color: red">下架</el-button>
            <el-button type="text" size="mini" @click="changeEnabled(scope.row)" v-if="!scope.row.enabled">上架</el-button>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column prop="useNum" label="背景使用人次" align="center" />
      <el-table-column prop="createTime" label="创建时间" align="center" />
    </el-table>
    <!-- 分页 -->
    <el-col :span="20">
      <el-pagination
        :current-page="tablePage.currentPage"
        :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="tablePage.totalItems"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-col>

    <!-- 修改或者新增弹窗 -->
    <el-dialog title="新增场景" :visible.sync="open" width="70%" @close="close">
      <el-form ref="form" :rules="rules" :model="form" label-position="left" label-width="120px">
        <el-form-item label="图片" prop="picUrl">
          <my-upload @handleSuccess="handleSuccess" @handleRemove="handleRemove" :file-list="fileList" :showTip="false"/>
          <el-input type="hidden" v-model="form.picUrl"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import { pageParamNames } from '@/utils/constants'
  import sceneInfoApi from '@/api/studyroom/sceneInfo'
  import MyUpload from "../../../components/Upload/MyUpload";
  export default {
    components: {MyUpload},
    data() {
      return {
        fileList:[],
        open:false,
        tableLoading:false,
        tableData:[],
        form: {},
        // 分页
        tablePage: {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null
        },
        rules: {
          // 表单提交规则
          picUrl: [
            {
              required: true,
              message: '请上传场景图片',
              trigger: 'blur'
            }
          ],
        },
      }
    },
    created() {
      this.getPageList();
    },
    methods: {
      changeEnabled(row) {
        let msg = row.enabled ? '下架' : '上架';
        let enable=row.enabled?0:1;
        this.$confirm('确定要' + msg + '吗?', '修改状态', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          sceneInfoApi.changeEnable(row.id, enable).then(res => {
            this.$nextTick(() => this.getPageList());
            this.$message.success('修改成功!')
          }).catch(err => {

          })
        }).catch(err => {

        })
      },
      submitForm(){
        this.$refs["form"].validate(valid => {
          console.log(this.form);
          if (valid) {
            sceneInfoApi.createScene(this.form).then(response => {
              this.$message.success("提交成功！")
              this.open = false;
              this.getPageList()
            });
          }
        });
      },
      getPageList(){
        this.tableLoading = true;
        sceneInfoApi.sceneList(this.tablePage.currentPage, this.tablePage.size).then(res => {
          this.tableData = res.data.data;
          this.tableData.forEach(i=>{
            i.picUrl = this.aliUrl+ i.picUrl;
          });
          console.log(this.tableData);
          this.tableLoading = false;
          // 设置后台返回的分页参数
          pageParamNames.forEach(name => this.$set(this.tablePage, name, parseInt(res.data[name])))
        })
      },
      clickAdd(){
        this.reset();
        this.open=true;
      },
      handleSuccess(url){
        console.log(this.fileList)
        console.log("handleSuccess")
        console.log(url)
        this.form.picUrl=url;
      },
      handleRemove(file){
        console.log("handleRemove");
        console.log(file);
        this.form.picUrl='';

      },
      // 分页
      handleSizeChange(val) {
        this.tablePage.size = val
        this.getPageList()
      },
      handleCurrentChange(val) {
        this.tablePage.currentPage = val
        this.getPageList()
      },
      // 关闭弹窗
      close() {
        this.reset();
      },
      reset() {
        this.fileList=[];
        this.form = {
          picUrl: '',
        };
        if (this.$refs['form']) {
          this.$refs['form'].resetFields();
        }
      }
    }
  }
</script>

<style>
  .img-lg{
    width: 100px;
    height: 100px
  }
</style>
