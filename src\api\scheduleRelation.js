/**
 * 教练 投诉相关接口
 */
import request from '@/utils/request'

export default {
  // 分页查询
  scheduleList(pageNum, pageSize, data) {
    return request({
      url: '/cousys/web/schedule/list/' + pageNum + '/' + pageSize,
      method: 'GET',
      params: data
    })
  },
  ///审核状态列表
  checkStatus() {
    return request({
      url: '/cousys/web/schedule/check/status/list',
      method: 'GET'
    })
  },
  ///课程状态列表
  courseStatus() {
    return request({
      url: '/cousys/web/schedule/course/status/list',
      method: 'GET'
    })
  },
  // 详情
  scheduleDetail(id) {
    return request({
      url: '/cousys/web/schedule/get/info',
      method: 'GET',
      params: {
        courseOrderId: id
      }
    })
  },
}

