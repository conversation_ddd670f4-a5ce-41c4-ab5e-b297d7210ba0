import request from '@/utils/request';
export default {
  // 培训中心2.0接口
  // 查询课程分类接口
  queryCourseCategory(data) {
    return request({
      url: '/train/web/dict/queryCourseCategory',
      method: 'GET',
      params: data
    });
  },

  // 根据课程分类code获取课程列表接口
  coursePageByCategoryCode(data) {
    return request({
      url: '/train/web/dict/coursePageByCategoryCode',
      method: 'GET',
      params: data
    });
  },

  // 根据二级课程分类code获取三级分类及下课程列表
  categoryCoursePage(data) {
    return request({
      url: '/train/web/dict/categoryCoursePage',
      method: 'GET',
      params: data
    });
  },

  // 查询已绑定课程数据
  courseRelation(data) {
    return request({
      url: '/train/web/dict/courseRelation',
      method: 'GET',
      params: data
    });
  },

  //查询该角色相关数据
  getUserCourselnfo(data) {
    return request({
      url: '/train/web/exam/course/getUserCourseInfo',
      method: 'GET',
      params: data
    });
  },
  // 课程中心-课程详情
  Detail(data) {
    return request({
      url: '/train/web/exam/training/course/detail',
      method: 'GET',
      params: data
    });
  },

  // 课程详情-新增反馈
  addFeed(data) {
    return request({
      url: '/train/web/exam/feedback/add',
      method: 'POST',
      data
    });
  },

  // 培训中心之前接口
  // 分页查询
  courseList(data) {
    return request({
      url: '/train/web/exam/course/page',
      method: 'GET',
      params: data
    });
  },
  // /train/web/exam/training/page
  trainingCourseList(data) {
    return request({
      url: '/train/web/exam/training/page',
      method: 'GET',
      params: data
    });
  },
  //课程删除
  courseDelete(data) {
    return request({
      url: '/train/web/exam/course/delete',
      method: 'POST',
      params: data
    });
  },
  // 课程新增/编辑
  courseCreateOrUpdate(data) {
    return request({
      url: '/train/web/exam/course/createOrUpdate',
      method: 'POST',
      data
    });
  },

  // 课程详情之前的/train/web/exam/course/detail
  courseDetail(data) {
    return request({
      url: '/train/web/exam/course/detail',
      method: 'GET',
      params: data
    });
  },

  //首页统计
  statisticsHomePage(data) {
    return request({
      url: '/train/web/exam/statistics/home_page',
      method: 'GET',
      params: data
    });
  },
  // /train/web/exam/statistics/record_page
  statisticsRecordPage(data) {
    return request({
      url: '/train/web/exam/statistics/record_page',
      method: 'GET',
      params: data
    });
  },

  //课程学习状态
  trainingProgress(data) {
    data.roleTag = window.localStorage.getItem('roleTag');
    return request({
      url: '/train/web/exam/training/progress',
      method: 'POST',
      data
    });
  },
  //单个课程考试记录
  trainingRecord(data) {
    data.roleTag = window.localStorage.getItem('roleTag');
    return request({
      url: '/train/web/exam/training/record',
      method: 'GET',
      params: data
    });
  },
  //学习统计 /
  studentStatistics(data) {
    return request({
      url: '/train/web/exam/statistics/student_statistics',
      method: 'GET',
      params: data
    });
  },
  //查询学习记录 //train/web/exam/statistics/record_page
  studentRecordPage(data) {
    return request({
      url: '/train/web/exam/statistics/record_page',
      method: 'GET',
      params: data
    });
  },
  //课程分类
  // /train/web/dict/createOrUpdate
  dictCreateOrUpdate(data) {
    return request({
      url: '/train/web/dict/createOrUpdate',
      method: 'POST',
      data
    });
  },
  //字典查询 /train/web/dict/queryByType
  dictQueryByType(data) {
    return request({
      url: '/train/web/dict/queryByType',
      method: 'GET',
      params: data
    });
  },
  // 附件新增/删除 ​/paper​/web​/exam​/course​/attachment
  // /train/web/exam/course/attachment
  courseAttachment(data) {
    return request({
      url: '/train/web/exam/course/attachment',
      method: 'POST',
      data
    });
  },
  //查询所以角色/train/web/dict/queryRoles
  dictQueryRoles(data) {
    return request({
      url: '/train/web/dict/queryRoles',
      method: 'GET',
      params: data
    });
  },
  //课程分类查询 /train/web/dict/page
  courseTypeList(data) {
    return request({
      url: '/train/web/dict/page',
      method: 'GET',
      params: data
    });
  },
  //删除课程分类/train/web/dict/delete
  courseTypeDelete(data) {
    return request({
      url: '/train/web/dict/delete',
      method: 'POST',
      params: data
    });
  },
  // /train/web/dict/queryCourses
  queryCoursesDict(data) {
    return request({
      url: '/train/web/dict/queryCourses',
      method: 'GET',
      params: data
    });
  },
  // /train/web/exam/training/attachment
  trainingAttachment(data) {
    data.roleTag = window.localStorage.getItem('roleTag');
    return request({
      url: '/train/web/exam/training/attachment',
      method: 'POST',
      data
    });
  },

  //playVideo更新课程情况接口
  lessonProgress(data) {
    data.roleTag = window.localStorage.getItem('roleTag');
    return request({
      url: '/train/web/exam/training/lesson_progress',
      method: 'POST',
      data
    });
  },
  // paper/web/exam/training/record_page
  trainingRecordPage(data) {
    data.roleTag = window.localStorage.getItem('roleTag');
    return request({
      url: '/train/web/exam/training/record_page',
      method: 'GET',
      params: data
    });
  },

  // ---------------
  // 查询课程分类-树状
  queryCategoryTree(data) {
    return request({
      url: '/train/web/dict/queryCourseCategory',
      method: 'GET',
      params: data
    });
  },
  // 课程分类新增-修改
  categoryManage(data) {
    return request({
      url: '/train/web/dict/courseCategoryManage',
      method: 'POST',
      data
    });
  },
  // 删除课程分类
  categoryDelete(data) {
    return request({
      url: '/train/web/dict/categoryDelete',
      method: 'POST',
      data
    });
  },
  // 课程分类对应课程列表
  coursePageByCategoryCode(data) {
    return request({
      url: '/train/web/dict/coursePageByCategoryCode',
      method: 'GET',
      params: data
    });
  },
  // 课程分类自定义排序
  courseCategorySort(data) {
    return request({
      url: '/train/web/dict/courseCategorySort',
      method: 'POST',
      data
    });
  },
  // 课程分类添加课程--获取可添加课程列表分页查询
  getAddCoursePage(data) {
    return request({
      url: '/train/web/dict/addCoursePage',
      method: 'GET',
      params: data
    });
  },
  // 课程分类-添加课程
  addCourse(data) {
    return request({
      url: '/train/web/dict/addCourse',
      method: 'POST',
      data
    });
  },
  // 课程分类绑定课程--删除
  categoryCourseRelationDelete(data) {
    return request({
      url: '/train/web/dict/categoryCourseRelationDelete',
      method: 'POST',
      params: data
    });
  },
  // 课程分类绑定课程--自定义排序
  categoryCourseRelationSort(data) {
    return request({
      url: '/train/web/dict/categoryCourseRelationSort',
      method: 'POST',
      data
    });
  },
  // 学习反馈分页查询
  courseFeedbackPage(data) {
    return request({
      url: '/train/web/exam/feedback/page',
      method: 'GET',
      params: data
    });
  },
  // 查询学习预期对应反馈数
  courseLearningCount(data) {
    return request({
      url: '/train/web/exam/feedback/learningCount',
      method: 'GET',
      params: data
    });
  },
  // 新增-修改反馈备注
  feedbackRemark(data) {
    return request({
      url: '/train/web/exam/feedback/remark',
      method: 'POST',
      data
    });
  },
  // 查询该用户角色是否所有必修课程都已学完
  checkCourseCompleted(data) {
    return request({
      url: '/train/web/exam/course/checkCourseCompleted',
      method: 'GET',
      params: data
    });
  },
  // 免密登录跳转学习中心前置接口
  noSecretDirect(data) {
    return request({
      url: '/train/web/common/noSecret/direct?loginUserName=' + data.loginUserName + '&roleTag=' + data.roleTag,
      method: 'POST'
    });
  }
};
