<template>
  <div>
    <el-row>
      <el-col :span="6" :offset="0">
        <el-tree ref="tree" :data="categoryTreeData" :props="propsTree" @node-click="categoryClick" v-loading="treeLoading">
          <span class="custom-tree-node" slot-scope="{ node, data }">
            <span>{{ node.label }}</span>
            <span v-if="data.number">{{ data.number }}</span>
          </span>
        </el-tree>
      </el-col>
      <el-col :span="18" :offset="0">
        <el-row>
          <el-col :span="20" :offset="4"></el-col>
          <el-col :span="4" :offset="0">
            <el-checkbox v-model="allCheck" @change="allclick">全选</el-checkbox>
          </el-col>
        </el-row>
        <el-table v-loading="tableLoading" :data="tableData" ref="multipleTable" @selection-change="handleSelectionChange" row-key="id">
          <el-table-column type="selection" reserve-selection width="55"></el-table-column>
          <el-table-column type="index" label="序号" center></el-table-column>
          <el-table-column label="知识点" center prop="knowledgeName"></el-table-column>
        </el-table>
        <el-pagination @current-change="handlePageChange" :current-page="tablePage.currentPage" :page-size="tablePage.pageSize" :total="tablePage.totalItems"></el-pagination>
      </el-col>
    </el-row>
  </div>
</template>

<script>
  import forStudent from '@/api/testPaper/management';
  import { type } from 'jquery';
  export default {
    name: 'Knowledge-point',
    props: {
      // 课程id
      curriculumId: {
        type: String,
        default: '1356667161917542400'
      },
      // 知识点id
      knowledgePointIds: {
        type: Array,
        default: () => []
      },
      //学科id
      disciplineId: {
        type: String,
        default: ''
      }
    },
    data() {
      return {
        tablePage: {
          currentPage: 1,
          pageSize: 10,
          totalItems: 0
        },
        tableLoading: false,
        allCheck: false,
        categoryTreeData: [],
        currentNodeKey: '', // 当前点击节点的id
        currentNodeLevel: '', // 当前点击节点的分类层级
        currentNodeData: {}, // 当前点击节点的数据
        total: 0,
        multipleSelection: [],
        list: [],
        yuAllcheck: [],
        tableData: [],
        data: [],
        temp: false,
        propsTree: {
          children: 'children',
          label: 'label'
        }
      };
    },
    created() {
      this.getCategoryTree();
      this.multipleSelection = this.knowledgePointIds;
    },
    methods: {
      // 全选
      async allclick(res) {
        if (res) {
          this.$refs.multipleTable.toggleAllSelection();
          this.multipleSelection = [...this.multipleSelection, ...this.yuAllcheck];
        } else {
          this.yuAllcheck.forEach((e) => {
            let index = this.multipleSelection.findIndex((o) => o.id === e.id);
            if (index !== -1) {
              this.multipleSelection.splice(index, 1);
            }
          });
          this.$refs.multipleTable.clearSelection();
        }
        await this.$nextTick();
      },
      // 获取课程分类树
      getCategoryTree() {
        this.treeLoading = true;
        this.categoryTreeData = [];
        const that = this;
        // let params = { noCount: 0 };
        let data = {
          curriculumId: this.curriculumId,
          nodeLevel: 4
        };
        // 1365386256284794880
        forStudent
          .selectTree(data)
          .then((res) => {
            if (this.disciplineId) {
              //过滤学科
              let result = res.data.filter((item) => item.id == this.disciplineId);
              that.categoryTreeData = that.deepReplace(result);
            } else {
              that.categoryTreeData = that.deepReplace(res.data);
            }
            that.treeLoading = false;
          })
          .catch((err) => {
            that.treeLoading = false;
          });
      },

      // 点击分类节点
      categoryClick(data, node) {
        if (node.level == 3) {
          this.childrenId = node.childNodes[0].data.id;
          this.parentId = node.parent.data.id;
          this.moreParentId = node.parent.parent.data.id;
        } else if (node.level == 4) {
          this.parentId = node.parent.data.id;
          this.moreParentId = node.parent.parent.data.id;
          this.mostParentId = node.parent.parent.parent.data.id;
        }
        this.currentNodeKey = data.id;
        this.currentNodeLevel = data.nodeLevel;
        this.currentNodeData = data;
        this.tablePage.currentPage = 1;
        this.allsubmitForm();
        this.allCheck = false;
        this.submitForm();
      },
      // 查询
      allsubmitForm() {
        let data = { ...this.dataQuery };
        data.pageNum = this.tablePage.currentPage;
        data.pageSize = 10000;
        if (this.currentNodeData.nodeLevel == 1) {
          data.courseSubjectNodeId = this.currentNodeData.id;
        } else if (this.currentNodeData.nodeLevel == 2) {
          data.coursePeriodNodeId = this.currentNodeData.id;
        } else if (this.currentNodeData.nodeLevel == 3) {
          data.chapterNodeId = this.currentNodeData.id;
        } else if (this.currentNodeData.nodeLevel == 4) {
          data.knowledgeSummaryNodeId = this.currentNodeData.id;
        } else {
          data.courseSubjectNodeId = this.form.categoryCode;
        }
        forStudent
          .searchForKnowledge(data)
          .then((res) => {
            if (res.success) {
              this.yuAllcheck = res.data.data || [];
              let temp = this.containsAllObjects(this.multipleSelection, this.yuAllcheck);
              if (temp && this.multipleSelection.length >= this.yuAllcheck.length) {
                this.allCheck = true;
              }
            }
          })
          .catch((err) => {
            this.$message.error('查询失败，请重试！' + err);
          });
      },
      // 查询
      submitForm() {
        this.tableLoading = true;
        let data = { ...this.dataQuery };
        data.pageNum = this.tablePage.currentPage;
        data.pageSize = this.tablePage.pageSize;
        if (this.currentNodeData.nodeLevel == 1) {
          data.courseSubjectNodeId = this.currentNodeData.id;
        } else if (this.currentNodeData.nodeLevel == 2) {
          data.coursePeriodNodeId = this.currentNodeData.id;
        } else if (this.currentNodeData.nodeLevel == 3) {
          data.chapterNodeId = this.currentNodeData.id;
        } else if (this.currentNodeData.nodeLevel == 4) {
          data.knowledgeSummaryNodeId = this.currentNodeData.id;
        } else {
          data.courseSubjectNodeId = this.form.categoryCode;
        }
        forStudent
          .searchForKnowledge(data)
          .then((res) => {
            if (res.success) {
              this.tableData = res.data.data;
              if (this.multipleSelection.length > 0 && res.data.data) {
                this.temp = true;
                this.tableData.forEach((e) => {
                  this.multipleSelection.forEach((o) => {
                    if (e.id === o.id) {
                      this.$nextTick(() => {
                        this.$refs.multipleTable.toggleRowSelection(e, true);
                      });
                    }
                  });
                });
                this.temp = false;
              }
              this.tablePage.totalItems = Number(res.data.totalItems);
              this.tablePage.currentPage = Number(res.data.currentPage);
              this.tableLoading = false;
            }
          })
          .catch((err) => {
            this.$message.error('查询失败，请重试！' + err);
            this.tableLoading = false;
          });
      },
      containsAllObjects(a, b) {
        const compareFn = (c, d) => c.id === d.id;
        return b.every((bItem) => a.some((aItem) => compareFn(aItem, bItem)));
      },
      deepReplace(array) {
        if (array instanceof Array && array.length >= 1) {
          return array.map((el) => {
            return {
              id: el.id,
              label: el.nodeName,
              children: this.deepReplace(el.childList),
              ...el
            };
          });
        } else {
          return [];
        }
      },
      handleSelectionChange(val) {
        if (this.multipleSelection.length > 0) {
          if (!this.temp) {
            const setA = new Set(val);
            this.multipleSelection = [...this.multipleSelection, ...val];
            let arr = this.tableData.filter((item) => !setA.has(item));
            this.multipleSelection = this.multipleSelection.filter((item) => !arr.find((e) => item.id === e.id));
            this.multipleSelection = this.uniqueByProp(this.multipleSelection, 'id');
            let temp = this.containsAllObjects(this.multipleSelection, this.tableData);
            if (this.multipleSelection.length >= this.yuAllcheck.length && temp) {
              this.allCheck = true;
            } else {
              this.allCheck = false;
            }
            // if (temp) {
            //   // this.allCheck = true;
            // } else {
            //   this.allCheck = false;
            // }
          }
        } else {
          this.multipleSelection = val;
        }

        console.log(this.multipleSelection);

        if (!this.multipleSelection.length) {
          this.allCheck = false;
        }
      },
      uniqueByProp(arr, prop) {
        return arr.filter((obj, index, self) => index === self.findIndex((o) => o[prop] === obj[prop]));
      },
      handlePageChange(val) {
        this.tablePage.currentPage = val;
        this.submitForm();
      }
    }
  };
</script>

<style scoped lang="scss">
  .custom-tree-node {
    width: 100%;
    display: flex;
    justify-content: space-between;
  }
</style>
