import request from "@/utils/request";

export default{
  // 听力课程分页
  listListeningMaterials(query) {
    return request({
      url: "/dyf/listeningMaterials/list",
      method: "get",
      params: query,
    });
  },

// 听力课程编辑回显
findListeningMaterials(query) {
  return request({
    url: "/dyf/listeningMaterials/find",
    method: "get",
    params: query,
  });
},

// 增加听力
 addListeningMaterials(query) {
  return request({
    url: "/dyf/listeningMaterials/add",
    method: "put",
    data: query,
  });
},

// 听力课程编辑
 editListeningMaterials(data) {
  return request({
    url: "/dyf/listeningMaterials/edit",
    method: "post",
    data
  });
},

 // 听力课程删除
 deleteListeningMaterials(query) {
  return request({
    url: "/dyf/listeningMaterials/deleted",
    method: "delete",
    params: query,
  });
},
}

