import request from '@/utils/request'

export default {
  // 分页查询
  list(data) {
    return request({
      url: '/paper/web/trainCourse/list',
      method: 'GET',
      params: data
    })
  },
//添加
  createOrUpdate(data) {
    return request({
      url: '/paper/web/trainCourse/createOrUpdate',
      method: 'POST',
      data
    })
  },
  detail(id) {
    return request({
      url: '/paper/web/trainCourse/detail',
      method: 'GET',
      params: {
        id: id
      }
    })
  },
  delete(id){
    return request({
      url: '/paper/web/trainCourse/delete',
      method: 'DELETE',
      params: {
        id: id
      }
    })
  }
}
