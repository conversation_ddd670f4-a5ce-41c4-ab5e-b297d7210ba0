<template>
  <el-dialog :title="updateId ? '修改课程' : '添加课程'" :visible.sync="addDialogVisible" width="70%" :close-on-click-modal="false" @close="dialogClose">
    <el-form v-loading="loading" ref="form" :rules="rules" :model="form" label-position="left" label-width="120px" style="width: 100%">
      <el-form-item label="课程名称" prop="courseName">
        <el-col :xs="24" :lg="18">
          <el-input v-model="form.courseName" placeholder="请输入"></el-input>
        </el-col>
      </el-form-item>
      <el-form-item label="课程排序" prop="sortOrder">
        <el-col :xs="24" :lg="18">
          <el-input v-model="form.sortOrder" placeholder="请输入" @input="validateSortNumInput"></el-input>
        </el-col>
      </el-form-item>
      <el-form-item label="等级类型" prop="courseLevel">
        <el-radio-group v-model="form.courseLevel" size="mini">
          <el-radio style="marginright: 16px" v-for="item in courseLevelList" :key="item.label" :label="item.value">{{ item.label }}</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="课程封面上传" prop="coverUrl">
        <el-upload
          ref="upload"
          list-type="picture-card"
          action="#"
          :file-list="fileList"
          :limit="1"
          :on-preview="handlePictureCardPreview"
          :on-remove="handleRemove"
          :class="objClass"
          :on-change="handleChange"
          :http-request="uploadDetailHttp"
          :before-upload="beforeAvatarUpload"
        >
          <i class="el-icon-plus"></i>
        </el-upload>
        <el-dialog :visible.sync="dialogImg" append-to-body class="imgDialog" center>
          <img width="100%" :src="form.coverUrl" style="width: 300px" />
        </el-dialog>
        <div class="tips" style="font-size: 12px">建议尺寸:690*280,支持格式：JPG、PNG格式 支持尺寸：{{ '<' }}300KB</div>
      </el-form-item>
      <el-form-item label="课程介绍">
        <el-col :xs="24" :lg="18">
          <el-input type="textarea" resize="none" :rows="4" v-model="form.courseDescription" />
        </el-col>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-radio-group v-for="item in courceStatus" :label="item.lable" :key="item.value" v-model="form.status" size="mini">
          <el-radio style="margin-left: 16px" :label="item.value">{{ item.label }}</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button size="mini" @click="dialogClose">关闭</el-button>
      <el-button size="mini" type="primary" :disabled="loading" @click="onSubmit">确定</el-button>
      <!-- <el-button v-if="!addOrUpdate" size="mini" type="primary" @click="updateActiveFun('updateCourseData')">修改 -->
    </div>
  </el-dialog>
</template>
<script>
  import CourseAdditionList from '@/api/courseAdditionList';
  import { ossPrClient } from '@/api/alibaba';
  export default {
    props: {
      dialogVisible: {
        type: Boolean,
        default: false
      },
      updateId: {
        type: String,
        default: ''
      }
    },
    data() {
      return {
        loading: false,
        // 课程状态
        addDialogVisible: false,
        id: '',
        form: {
          courseName: '',
          coverUrl: '',
          status: 1,
          courseDescription: '',
          courseLevel: '',
          sortOrder: ''
        }, // 新增课程
        objClass: {
          upLoadShow: true,
          upLoadHide: false
        },
        dialogImg: false,
        rules: {
          // 表单提交规则
          courseName: [{ required: true, message: '请输入', trigger: 'blur' }],
          sortOrder: [{ required: true, message: '请输入', trigger: 'blur' }],
          coverUrl: [{ required: true, message: '请上传封面', trigger: 'blur' }],
          status: [{ required: true, message: '请选择', trigger: 'change' }]
        },
        //课程等级
        courseLevelList: [
          { value: '0', label: '启蒙' },
          { value: '1', label: '基础' },
          { value: '2', label: '进阶' }
        ],
        //课程状态
        courceStatus: [
          { value: 1, label: '开通' },
          { value: 0, label: '暂停' }
        ],
        fileList: [],
        coursedata: null
      };
    },
    created() {
      ossPrClient();
    },
    watch: {
      dialogVisible(val) {
        this.fileList = [];
        this.form = {
          courseName: '',
          coverUrl: '',
          status: 1,
          courseDescription: '',
          courseLevel: '',
          sortOrder: ''
        };
        this.addDialogVisible = val;
      },

      updateId(val) {
        if (val) {
          console.log(val);

          this.coursedata = JSON.parse(val);
          console.log(this.coursedata);

          this.setCourseDetail();
        }
      }
    },
    methods: {
      validateSortNumInput(event) {
        if (!/^[1-9]\d*$/.test(event)) {
          this.$message.error('请输入有效的数字');
          this.form.sortOrder = ''; // 清空输入框
        }
      },
      //获取详情
      setCourseDetail() {
        this.form.coverUrl = '';

        this.fileList = [];
        this.form = this.coursedata;
        this.form.courseLevel = this.form.courseLevel + '';
        this.objClass.upLoadHide = true; //上传图片后置upLoadHide为真，隐藏上传框
        this.objClass.upLoadShow = false;
        this.fileList.push({ url: this.form.coverUrl, name: '封面' });
      },

      dialogClose() {
        this.objClass.upLoadHide = false; //上传图片后置upLoadHide为真，隐藏上传框
        this.objClass.upLoadShow = true;
        this.addDialogVisible = false;
        for (let val in this.form) {
          this.form[val] = '';
        }
        this.$refs['form'].resetFields();
        this.$emit('addDialogClose', false);
        this.coursedata = null;
      },
      onSubmit() {
        this.$refs['form'].validate((valid) => {
          if (valid) {
            this.loading = true;
            const submitApi = this.form.id ? CourseAdditionList.editCourse : CourseAdditionList.addCourse;
            submitApi(this.form)
              .then((res) => {
                if (res.code === 20000) {
                  this.addDialogVisible = false;
                  this.loading = false;
                  this.$message.success(`${this.form.id ? '修改' : '添加'}成功`);
                  this.$emit('addDialogClose', false);
                  this.$refs['form'].resetFields();
                  this.objClass.upLoadHide = false;
                  this.coursedata = null;
                }
              })
              .catch((error) => {
                this.loading = false;
                this.objClass.upLoadHide = false;
                this.coursedata = null;
              });
          }
        });
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url;
        this.dialogImg = true;
      },
      handleRemove(file, fileList) {
        this.objClass.upLoadShow = true; //删除图片后显示上传框
        this.objClass.upLoadHide = false;
        this.form.coverUrl = '';
      },
      handleChange(file, fileList) {
        this.objClass.upLoadHide = true; //上传图片后置upLoadHide为真，隐藏上传框
        this.objClass.upLoadShow = false;
      },
      uploadDetailHttp({ file }) {
        this.uploadLoading = true;
        const that = this;
        const fileType = file.type.substring(file.type.lastIndexOf('/') + 1);
        const fileName = `manage/${Date.parse(new Date())}.${fileType}`;
        that.$nextTick(function () {
          ossPrClient()
            .put(fileName, file)
            .then(({ res, url, name }) => {
              if (res && res.status === 200) {
                console.log(`阿里云OSS上传图片成功回调1`, res, url, name);
                this.form.coverUrl = url;
                that.$nextTick(() => {
                  that.uploadLoading = false;
                });
              }
            })
            .catch((err) => {
              that.$message.error('上传图片失败请检查网络或者刷新页面');
              console.log(`阿里云OSS上传图片失败回调`, err);
            });
        });
      },
      beforeAvatarUpload(file) {
        const imgType = file.type === 'image/jpeg' || file.type === 'image/png';
        const isLt300k = file.size / 1024 < 300;
        if (!imgType) {
          this.$message.error('上传图片只能是 JPG和png 格式!');
          return false;
        }
        if (!isLt300k) {
          this.$message.error('上传图片大小不能超过 300k!');
          return false;
        }
      }
    }
  };
</script>
<style lang="less" scoped>
  .imgDialog {
    .el-dialog {
      width: 30%;
    }

    .el-dialog__body {
      text-align: center !important;
    }
  }
</style>
