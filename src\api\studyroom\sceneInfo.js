
import request from '@/utils/request'

export default {

  // 分页查询
  sceneList(pageNum, pageSize) {
    return request({
      url: '/studyroom/webSceneInfo/list/' + pageNum + '/' + pageSize,
      method: 'GET'
    })
  },
  //修改状态
  changeEnable(id,enabled){
    return request({
      url: '/studyroom/webSceneInfo/changeEnable',
      method: 'PUT',
      params:{
        sceneId:id,
        enabled:enabled
      }
    })
  },
  //修改状态
  createScene(data){
    return request({
      url: '/studyroom/webSceneInfo/create',
      method: 'POST',
      data
    })
  },
}
