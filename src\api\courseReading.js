/**
 * 阅读理解和完型填空相关接口
 */
import request from '@/utils/request'

export default {
  //阅读理解和完型填空分页查询
  courseReadingList(pageNum, pageSize, data) {
    return request({
      url: '/znyy/course/reading/list/' + pageNum + '/' + pageSize,
      method: 'GET',
      params: data
    })
  },
  // 阅读理解和完型填空新增
  addCourseReading(data) {
    return request({
      url: '/znyy/course/reading',
      method: 'POST',
      data
    })
  },
  // 阅读理解和完型填空
  updateWordLevel(data) {
    return request({
      url: '/znyy/course/reading',
      method: 'PUT',
      data
    })
  },
  // 阅读理解和完型填空的回显查询
  queryReading(id) {
    return request({
      url: '/znyy/course/reading/seeDetails/' + id,
      method: 'GET'
    })
  },
  //删除
  deleteCourseReading(id){
    return request({
      url : '/znyy/course/reading/delete/' +id,
      method: 'DELETE'
    })
  },
  //删除阅读理解和完型填空
  deleteReading(id){
return request({
  url : "/znyy/course/reading/tombstone/" +id,
  method : 'DELETE'
})
  },
  getCurrentTime() {
    var _this = this;
    let yy = new Date().getFullYear();
    let mm = new Date().getMonth() + 1;
    let dd = new Date().getDate();
    let hh = new Date().getHours();
    let mf = new Date().getMinutes() < 10 ? '0' + new Date().getMinutes() : new Date().getMinutes();
    let ss = new Date().getSeconds() < 10 ? '0' + new Date().getSeconds() : new Date().getSeconds();
    _this.gettime = yy + '-' + mm + '-' + dd + ' ' + hh + ':' + mf + ':' + ss;
    return _this.gettime
  }
}
