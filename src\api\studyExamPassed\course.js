import request from '@/utils/request';

export default {
  // 课程分页查询
  courseList(data) {
    return request({
      url: '/dyf/web/xktCourse/page',
      method: 'GET',
      params: data
    });
  },

  courseDetail(id) {
    return request({
      url: '/dyf/web/xktCourse/detail',
      method: 'GET',
      params: { id }
    });
  },

  //新增课程
  addCourse(data) {
    return request({
      url: '/dyf/web/xktCourse/save',
      method: 'POST',
      data
    });
  },

  //修改课程
  updateCourse(data) {
    return request({
      url: '/dyf/web/xktCourse/update',
      method: 'POST',
      data
    });
  },

  //删除
  courseDelete(id) {
    return request({
      url: '/dyf/web/xktCourse/delete',
      method: 'DELETE',
      params: { id }
    });
  },

  // 获取课程大类
  getCourseCategory() {
    return request({
      url: '/dyf/web/xktCourse/curriculum',
      method: 'GET'
    });
  },

  // 获取学段及对应学科
  getGradeAndSubjectList(curriculumId, gradeLevel) {
    return new Promise((resolve, reject) => {
      request({
        url: '/dyf/web/xktVideo/options',
        method: 'GET',
        params: { curriculumId, gradeLevel }
      })
        .then((res) => {
          let gradeList = [];
          if (Array.isArray(res.data)) {
            res.data.forEach((each) => {
              let gradeLevel = { label: each.gradeName, value: each.gradeLevel, children: [] };
              if (Array.isArray(each.childrenList)) {
                each.childrenList.forEach((item) => {
                  gradeLevel.children.push({ label: item.name, value: item.id });
                });
              }
              gradeList.push(gradeLevel);
            });
          }
          resolve({ data: gradeList });
        })
        .catch((err) => {
          reject(err);
        });
    });
  },

  getVersionList(curriculumId, gradeLevel, subjectId) {
    return new Promise((resolve, reject) => {
      request({
        url: '/dyf/web/xktVideo/options',
        method: 'GET',
        params: { curriculumId, gradeLevel, subjectId }
      })
        .then((res) => {
          let versionList = [];
          if (Array.isArray(res.data)) {
            res.data.forEach((each) => {
              if (Array.isArray(each.childrenList)) {
                each.childrenList.forEach((i) => {
                  if (Array.isArray(i.childrenList)) {
                    i.childrenList.forEach((j) => {
                      versionList.push({ label: j.name, value: j.id });
                    });
                  }
                });
              }
            });
          }
          versionList = [...new Set(versionList.map(JSON.stringify))].map(JSON.parse);
          resolve({ data: versionList });
        })
        .catch((err) => {
          reject(err);
        });
    });
  },

  //指定课程的绑定视频分页
  courseVideoList(data) {
    return request({
      url: '/dyf/web/xktVideo/pageByCourseId',
      method: 'GET',
      params: data
    });
  },

  //指定课程的未绑定视频分页
  courseAddVideoList(data) {
    return request({
      url: '/dyf/web/xktVideo/pageByUnBindCourseId',
      method: 'GET',
      params: data
    });
  },

  //批量绑定视频到课程
  courseBatchAddVideo(data) {
    return request({
      url: '/dyf/web/xktVideo/batchBindCourse',
      method: 'POST',
      data
    });
  },

  //修改绑定到指定课程的视频的排序号
  changeVideoSortNo(data) {
    return request({
      url: '/dyf/web/xktVideo/update-sort',
      method: 'post',
      params: data
    });
  },

  //移除指定课程绑定的视频
  courseVideoDelete(id) {
    return request({
      url: '/dyf/web/xktVideo/unbind-course',
      method: 'post',
      params: { id }
    });
  },

  // 获取退课视频配置
  getRefundVideoConfig() {
    return request({
      url: '/dyf/web/xktDropCourseConfig/list',
      method: 'GET'
    });
  },

  // 重置默认配置
  resetRefundVideoConfig() {
    return request({
      url: '/dyf/web/xktDropCourseConfig/reset',
      method: 'GET'
    });
  },

  // 保存退课视频配置
  saveRefundVideoConfig(data) {
    return request({
      url: '/dyf/web/xktDropCourseConfig/save',
      method: 'POST',
      data
    });
  },
};
