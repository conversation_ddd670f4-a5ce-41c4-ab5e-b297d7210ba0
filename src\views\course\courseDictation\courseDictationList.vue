<template>
  <div class="app-container">
    <!-- 搜索 -->
    <SearchCourse ref="childRef" @onSearch="onSearch" @rest="rest"></SearchCourse>

    <div class="SearchForm">
      <!-- 添加 -->
      <div class="btn-add">
        <el-button size="small" type="primary" icon="el-icon-plus" @click="getAddForm">添加</el-button>
      </div>
      <!-- 表格 -->
      <el-table class="common-table" :data="tableData" stripe border v-loading="tableLoading">
        <el-table-column prop="courseCode" label="课程编号" width="140" sortable></el-table-column>
        <el-table-column prop="courseName" label="课程名称" width="180" show-overflow-tooltip></el-table-column>
        <el-table-column prop="id" label="操作" width="280">
          <template slot-scope="{ row }">
            <el-button type="success" size="mini" icon="el-icon-edit-outline" @click="handleUpdate(row.id)">编辑</el-button>
            <el-button type="primary" size="mini" @click="courseMake(row.courseCode, row.courseType, row.id, row.courseLevel)">制作课程</el-button>
            <el-button
              :type="row.isEnable === 1 ? 'danger' : 'success'"
              :icon="row.isEnable === 0 ? 'el-icon-video-play' : 'el-icon-video-pause'"
              size="mini"
              @click="courseStatus(row.id, row.isEnable)"
            >
              {{ row.isEnable === 1 ? '暂停' : '开通' }}
            </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="courseCover" label="课程封面" width="160">
          <template slot-scope="scope">
            <div class="demo-image__preview">
              <el-image v-if="scope.row.courseCover" class="table_list_pic" :src="scope.row.courseCover" @click="openImg(scope.row)"></el-image>
              <el-image v-else>
                <div slot="error" class="image-slot">
                  <i class="el-icon-picture-outline"></i>
                </div>
              </el-image>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="categoryCode" label="课程分类">
          <template slot-scope="scope">
            <span>{{ categoryTypeList.filter((f) => f.value === scope.row.categoryCode)[0]?.label }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="courseLevel" label="等级分类">
          <template slot-scope="scope">
            <span>{{ courseLevelList.filter((f) => f.value === scope.row.courseLevel)[0]?.label }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="courseType" label="课程类型">
          <template slot-scope="scope">
            <span>{{ scope.row.courseType === '0' ? '体验课' : '正式课' }}</span>
            <!-- <span>{{ courseTypeList.filter(f =>f.value === scope.row.courseType)[0]?.lable }}</span> -->
          </template>
        </el-table-column>
        <el-table-column prop="merchantName" label="上传人"></el-table-column>
        <el-table-column prop="addTime" label="添加时间" width="180" sortable :show-overflow-tooltip="true"></el-table-column>
        <el-table-column prop="modifyTime" label="最后编辑时间" width="180" sortable :show-overflow-tooltip="true"></el-table-column>
        <el-table-column prop="isEnable" label="状态" width="90" sortable :show-overflow-tooltip="true">
          <template slot-scope="scope">
            <span :class="scope.row.isEnable === 0 ? 'isEnableRed' : 'isEnableGreen'">{{ scope.row.isEnable === 0 ? '暂停' : '开通' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="orderNum" label="序号" width="180" sortable :show-overflow-tooltip="true"></el-table-column>
      </el-table>
    </div>
    <!-- 分页 -->
    <el-col :span="20">
      <el-pagination
        :current-page="tablePage.currentPage"
        :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="totalItems"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-col>
    <el-dialog title="制作课程" :visible.sync="dialogMake" width="70%" :close-on-click-modal="false" @close="dialogMakeClose" :destroy-on-close="true">
      <!-- <AddDictationWord ref="addDictationWord" @submitStepWord="submitStepWord" :makeId="makeId" :makeCourseType="makeCourseType"></AddDictationWord> -->
      <el-form
        ref="videoForm"
        :model="videoForm"
        :rules="courseType === '1' ? videoRules : courseLevel == 2 ? typeRules : ''"
        label-position="left"
        label-width="120px"
        style="width: 100%"
      >
        <el-form-item label="学习视频" prop="studyUrl" v-if="courseType == '0' && courseLevel == 2">
          <div style="position: relative">
            <input
              type="file"
              style="max-width: 200px"
              class="upload"
              accept="video/mp4,video/webm,video/ogg"
              @change="doUpload('studyUrl', 'studyUrlInputer', 5)"
              ref="studyUrlInputer"
              :disabled="imageForm[5].id ? 'disabled' : null"
              multiple
            />
            <el-button type="danger" size="small" @click="delBaseUrl('studyUrl', 'studyUrlInputer', 5)" v-if="imageForm[5].id || videoForm.studyUrl">删除</el-button>
            <div style="position: absolute; background-color: transparent; width: 130px; height: 21px; left: 70px; top: 6px"></div>
            <!-- 禁止点击↑文件名称 -->
          </div>
          <!-- <el-button type="primary" size="small" @click="startBaseUrl('baseUrl',0)" v-if="imageForm[0].id || videoForm.baseUrl">上传</el-button> -->
          <div v-if="videoForm.studyUrl && !imageForm[5].studyUrl">
            <div
              style="width: 148px; height: 148px; background: #f5f7fa; display: flex; align-items: center; justify-content: center"
              v-if="(imageForm[5].progress || imageForm[5].progress === 0) && imageForm[5].progress < 101 && !imageForm[5].failed"
            >
              <el-progress :width="100" type="circle" :percentage="imageForm[5].progress"></el-progress>
            </div>
            <div v-else style="width: 148px; height: 148px; background: #f5f7fa; display: flex; align-items: center; justify-content: center">
              <div v-if="imageForm[5].failed">上传失败，点击删除后重新上传</div>
              <el-image v-else style="width: 148px; height: 148px; background: #f5f7fa">
                <div slot="error" class="course_image_slot">
                  <i class="el-icon-picture-outline"></i>
                  <span>审核中...</span>
                </div>
              </el-image>
            </div>
          </div>
          <div v-if="imageForm[5].studyUrl">
            <el-image :src="imageForm[5].studyUrl" style="width: 148px; height: 148px" fit="cover "></el-image>
            <div>{{ imageForm[5].title }}</div>
          </div>
        </el-form-item>
        <el-form-item label="学前视频" prop="baseUrl">
          <div style="position: relative">
            <input
              type="file"
              style="max-width: 200px"
              class="upload"
              accept="video/mp4,video/webm,video/ogg"
              @change="doUpload('baseUrl', 'baseUrlInputer', 0)"
              ref="baseUrlInputer"
              :disabled="!(courseType === '1') ? 'disabled' : null"
              multiple
            />
            <el-button type="danger" size="small" @click="delBaseUrl('baseUrl', 'baseUrlInputer', 0)" v-if="imageForm[0].id || videoForm.baseUrl">删除</el-button>
            <div style="position: absolute; background-color: transparent; width: 130px; height: 21px; left: 70px; top: 6px"></div>
            <!-- 禁止点击↑文件名称 -->
          </div>
          <!-- <el-button type="primary" size="small" @click="startBaseUrl('baseUrl',0)" v-if="imageForm[0].id || videoForm.baseUrl">上传</el-button> -->
          <div v-if="videoForm.baseUrl && !imageForm[0].baseUrl">
            <div
              style="width: 148px; height: 148px; background: #f5f7fa; display: flex; align-items: center; justify-content: center"
              v-if="(imageForm[0].progress || imageForm[0].progress === 0) && imageForm[0].progress < 101 && !imageForm[0].failed"
            >
              <el-progress :width="100" type="circle" :percentage="imageForm[0].progress"></el-progress>
            </div>
            <div v-else style="width: 148px; height: 148px; background: #f5f7fa; display: flex; align-items: center; justify-content: center">
              <div v-if="imageForm[0].failed">上传失败，点击删除后重新上传</div>
              <el-image v-else style="width: 148px; height: 148px; background: #f5f7fa">
                <div slot="error" class="course_image_slot">
                  <i class="el-icon-picture-outline"></i>
                  <span>审核中...</span>
                </div>
              </el-image>
            </div>
          </div>
          <div v-if="imageForm[0].baseUrl">
            <el-image :src="imageForm[0].baseUrl" style="width: 148px; height: 148px" fit="cover "></el-image>
            <div>{{ imageForm[0].title }}</div>
          </div>
        </el-form-item>
        <el-form-item label="元音视频" prop="vowelUrl">
          <div style="position: relative">
            <input
              type="file"
              class="upload"
              style="max-width: 200px"
              accept="video/mp4,video/webm,video/ogg"
              @change="doUpload('vowelUrl', 'vowelInputer', 1)"
              :disabled="!(courseType === '1') ? 'disabled' : null"
              ref="vowelInputer"
              multiple
            />
            <el-button type="danger" size="small" @click="delBaseUrl('vowelUrl', 'vowelInputer', 1)" v-if="imageForm[1].id || videoForm.vowelUrl">删除</el-button>
            <div style="position: absolute; background-color: transparent; width: 130px; height: 21px; left: 70px; top: 6px"></div>
          </div>

          <div v-if="videoForm.vowelUrl && !imageForm[1].vowelUrl">
            <div
              style="width: 148px; height: 148px; background: #f5f7fa; display: flex; align-items: center; justify-content: center"
              v-if="(imageForm[1].progress || imageForm[1].progress === 0) && imageForm[1].progress < 101 && !imageForm[1].failed"
            >
              <el-progress :width="100" type="circle" :percentage="imageForm[1].progress"></el-progress>
            </div>
            <div v-else style="width: 148px; height: 148px; background: #f5f7fa; display: flex; align-items: center; justify-content: center">
              <div v-if="imageForm[1].failed">上传失败，点击删除后重新上传</div>
              <el-image v-else style="width: 148px; height: 148px; background: #f5f7fa">
                <div slot="error" class="course_image_slot">
                  <i class="el-icon-picture-outline"></i>
                  <span>审核中...</span>
                </div>
              </el-image>
            </div>
          </div>
          <div v-if="imageForm[1].vowelUrl">
            <el-image :src="imageForm[1].vowelUrl" style="width: 148px; height: 148px" fit="cover "></el-image>
            <div>{{ imageForm[1].title }}</div>
          </div>
          <!-- <video
                    v-if="videoForm.vowelUrl"
                    v-bind:src="videoForm.vowelUrl"
                    class="video_upload"
                    :autoplay="false"
                    controls="controls"

                ></video>
                <div class="video_del" @click="vowelUrlDel" v-if="videoForm.vowelUrl">
                    <i class="el-icon-delete" color="white"></i>
                </div>
                <el-upload :disabled="!(courseType === '1')" :show-file-list="false" action="" list-type="picture-card" class="upload-demo"  v-loading="vowelLoading" :http-request="vowelHttp" :before-upload="beforeUpload">
                    <i class="el-icon-plus"></i>
                    <span>视频上传</span>
                </el-upload> -->
        </el-form-item>
        <el-form-item label="辅音视频" prop="consonantUrl">
          <div style="position: relative">
            <input
              type="file"
              class="upload"
              style="max-width: 200px"
              accept="video/mp4,video/webm,video/ogg"
              @change="doUpload('consonantUrl', 'consonantInputer', 2)"
              :disabled="!(courseType === '1') ? 'disabled' : null"
              ref="consonantInputer"
              multiple
            />
            <el-button type="danger" size="small" @click="delBaseUrl('consonantUrl', 'consonantInputer', 2)" v-if="imageForm[2].id || videoForm.consonantUrl">删除</el-button>
            <div style="position: absolute; background-color: transparent; width: 130px; height: 21px; left: 70px; top: 6px"></div>
          </div>

          <div v-if="videoForm.consonantUrl && !imageForm[2].consonantUrl">
            <div
              style="width: 148px; height: 148px; background: #f5f7fa; display: flex; align-items: center; justify-content: center"
              v-if="(imageForm[2].progress || imageForm[2].progress === 0) && imageForm[2].progress < 101 && !imageForm[2].failed"
            >
              <el-progress :width="100" type="circle" :percentage="imageForm[2].progress"></el-progress>
            </div>
            <div v-else style="width: 148px; height: 148px; background: #f5f7fa; display: flex; align-items: center; justify-content: center">
              <div v-if="imageForm[2].failed">上传失败，点击删除后重新上传</div>
              <el-image v-else style="width: 148px; height: 148px; background: #f5f7fa">
                <div slot="error" class="course_image_slot">
                  <i class="el-icon-picture-outline"></i>
                  <span>审核中...</span>
                </div>
              </el-image>
            </div>
          </div>
          <div v-if="imageForm[2].consonantUrl">
            <el-image :src="imageForm[2].consonantUrl" style="width: 148px; height: 148px" fit="cover "></el-image>
            <div>{{ imageForm[2].title }}</div>
          </div>
        </el-form-item>
        <el-form-item label="总结视频" prop="summaryUrl">
          <div style="position: relative">
            <input
              type="file"
              class="upload"
              style="max-width: 200px"
              accept="video/mp4,video/webm,video/ogg"
              @change="doUpload('summaryUrl', 'summaryInputer', 3)"
              :disabled="!(courseType === '1') ? 'disabled' : null"
              ref="summaryInputer"
              multiple
            />
            <el-button type="danger" size="small" @click="delBaseUrl('summaryUrl', 'summaryInputer', 3)" v-if="imageForm[3].id || videoForm.summaryUrl">删除</el-button>
            <div style="position: absolute; background-color: transparent; width: 130px; height: 21px; left: 70px; top: 6px"></div>
          </div>
          <div v-if="videoForm.summaryUrl && !imageForm[3].summaryUrl">
            <div
              style="width: 148px; height: 148px; background: #f5f7fa; display: flex; align-items: center; justify-content: center"
              v-if="(imageForm[3].progress || imageForm[3].progress === 0) && imageForm[3].progress < 101 && !imageForm[3].failed"
            >
              <el-progress :width="100" type="circle" :percentage="imageForm[3].progress"></el-progress>
            </div>
            <div v-else style="width: 148px; height: 148px; background: #f5f7fa; display: flex; align-items: center; justify-content: center">
              <div v-if="imageForm[3].failed">上传失败，点击删除后重新上传</div>
              <el-image v-else style="width: 148px; height: 148px; background: #f5f7fa">
                <div slot="error" class="course_image_slot">
                  <i class="el-icon-picture-outline"></i>
                  <span>审核中...</span>
                </div>
              </el-image>
            </div>
          </div>
          <div v-if="imageForm[3].summaryUrl">
            <el-image :src="imageForm[3].summaryUrl" style="width: 148px; height: 148px" fit="cover "></el-image>
            <div>{{ imageForm[3].title }}</div>
          </div>
        </el-form-item>
        <el-form-item label="复习视频" prop="reviewUrl">
          <div style="position: relative">
            <input
              type="file"
              class="upload"
              style="max-width: 200px"
              accept="video/mp4,video/webm,video/ogg"
              @change="doUpload('reviewUrl', 'reviewUrlInputer', 4)"
              :disabled="!(courseType === '1') ? 'disabled' : null"
              ref="reviewUrlInputer"
              multiple
            />
            <el-button type="danger" size="small" @click="delBaseUrl('reviewUrl', 'reviewUrlInputer', 4)" v-if="imageForm[4].id || videoForm.reviewUrl">删除</el-button>
            <div style="position: absolute; background-color: transparent; width: 130px; height: 21px; left: 70px; top: 6px"></div>
          </div>
          <!-- <el-button type="primary" size="small" @click="startBaseUrl('reviewUrl',4)" v-if="imageForm[4].id || videoForm.reviewUrl" >上传</el-button> -->
          <div v-if="videoForm.reviewUrl && !imageForm[4].reviewUrl">
            <div
              style="width: 148px; height: 148px; background: #f5f7fa; display: flex; align-items: center; justify-content: center"
              v-if="(imageForm[4].progress || imageForm[4].progress === 0) && imageForm[4].progress < 101 && !imageForm[4].failed"
            >
              <el-progress :width="100" type="circle" :percentage="imageForm[4].progress"></el-progress>
            </div>
            <div v-else style="width: 148px; height: 148px; background: #f5f7fa; display: flex; align-items: center; justify-content: center">
              <div v-if="imageForm[4].failed">上传失败，点击删除后重新上传</div>
              <el-image v-else style="width: 148px; height: 148px; background: #f5f7fa">
                <div slot="error" class="course_image_slot">
                  <i class="el-icon-picture-outline"></i>
                  <span>审核中...</span>
                </div>
              </el-image>
            </div>
          </div>
          <div v-if="imageForm[4].reviewUrl">
            <el-image :src="imageForm[4].reviewUrl" style="width: 148px; height: 148px" fit="cover "></el-image>
            <div>{{ imageForm[4].title }}</div>
          </div>
        </el-form-item>
        <el-form-item label="复习讲义" prop="reviewTextUrl" v-if="!(courseType === '1' && courseLevel == 2)">
          <!-- <div class="upload_box"> -->
          <el-row>
            <el-col :span="12">
              <el-upload
                :file-list="reviewUrlFileList"
                :disabled="!(courseType === '1')"
                :accept="'.doc,.docx'"
                :show-file-list="true"
                action=""
                class="upload-demo"
                v-loading="reviewTextUrlLoading"
                :http-request="reviewTextUrlHttp"
                :on-remove="removeReview"
                :before-upload="beforeWordUpload"
              >
                <el-button icon="el-icon-upload" :underline="false" class="upload_link">word文件上传</el-button>
              </el-upload>
            </el-col>
          </el-row>
          <!-- </div> -->
        </el-form-item>
        <el-form-item label="备课资料" prop="lessonUrl" v-if="courseType === '1'">
          <el-row>
            <el-col :span="12">
              <el-upload
                :limit="10"
                :file-list="lessonMaterialsFileList"
                :show-file-list="true"
                :accept="'.pdf'"
                action=""
                class="upload-demo"
                v-loading="lessonUrlLoading"
                :http-request="lessonMaterialsUrlHttp"
                :on-remove="removeLesson"
                :on-exceed="handleExceed"
              >
                <!-- :accept="'.doc,.docx'" :before-upload="beforeWordUpload" -->
                <el-button
                  icon="el-icon-upload"
                  :underline="false"
                  class="upload_link"
                  :class="lessonMaterialsFileList.length >= 10 ? 'upload_disable' : ''"
                  @click="beforePDFUpload"
                >
                  pdf文件上传
                </el-button>
                <!-- <el-button icon="el-icon-upload" :underline="false" class="upload_link" :disabled="lessonMaterialsFileList.length >= 3" @click="beforePDFUpload">
                  pdf文件上传
                </el-button> -->
              </el-upload>
            </el-col>
          </el-row>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="dialogMakeClose">关闭</el-button>
        <el-button size="mini" type="primary" :disabled="btnUpload" @click="addVideoForm">确定</el-button>
      </div>
    </el-dialog>
    <!-- 编辑或者添加弹窗 -->
    <CourseAddForm :dialogVisible="dialogVisible" :updateId="updateId" @addDialogClose="addDialogClose"></CourseAddForm>
    <!-- 图片显示 -->
    <el-dialog title="" :visible.sync="dialogOpenimg" width="30%">
      <div class="coverimg">
        <el-image class="table_list_pic" :src="coverImg"></el-image>
      </div>
    </el-dialog>
  </div>
</template>
<script src="https://s1.videocc.net/library/blueimp-md5/md5-2.18.0.min.js"></script>
<!--引入上传插件js-->
<script src="https://static.polyv.net/file/plug-in-v2/polyv-upload.min.js"></script>
<script>
  import { ossPrClient } from '@/api/alibaba';
  import courseDictationListApi from '@/api/courseDictationList';
  import courseApitwo from '@/api/courseChildren';
  import enTypes from '@/api/bstatus';
  import { mapGetters } from 'vuex';
  import AddDictationWord from '@/views/course/courseDictation/addDictationWord.vue';
  import SearchCourse from '@/views/course/courseDictation/components/searchCourse.vue';
  import CourseAddForm from '@/views/course/courseDictation/components/courseAddForm.vue';
  import md5 from 'js-md5';
  import PlvVideoUpload from '@polyv/vod-upload-js-sdk';
  import { Loading } from 'element-ui';
  export default {
    name: 'courseList',
    components: { AddDictationWord, SearchCourse, CourseAddForm },
    data() {
      return {
        typeData: [],
        courseLevel: '',
        dialogMake: false,
        makeId: '',
        makeCourseType: '',
        preschoolLoading: false, //上传视频
        vowelLoading: false,
        consonantLoading: false,
        summaryLoading: false,
        reviewTextUrlLoading: false,
        lessonUrlLoading: false,
        vodPlayerJs: 'https://player.polyv.net/resp/vod-player/latest/player.js',
        //制作课程-上传视频
        videoForm: {
          courseCode: '',
          baseUrl: '',
          vowelUrl: '',
          consonantUrl: '',
          summaryUrl: '',
          reviewUrl: '',
          reviewTextUrl: '',
          studyUrl: '',
          lessonUrl: []
        },
        typeRules: {
          studyUrl: [{ required: true, message: '请上传学习视频', trigger: 'blur' }]
        },
        videoRules: {
          summaryUrl: [{ required: true, message: '请上传总结视频', trigger: 'blur' }],
          reviewUrl: [{ required: true, message: '请上传复习视频', trigger: 'blur' }],
          reviewTextUrl: [{ required: true, message: '请上传复习讲义', trigger: 'blur' }]
        },
        //课程等级
        courseLevelList: [
          { value: '0', label: '启蒙' },
          { value: '1', label: '基础' },
          { value: '2', label: '进阶' }
        ],
        //课程类型
        courseTypeList: [
          { value: '0', label: '体验课' },
          { value: '1', label: '系统课' }
        ],
        //课程状态
        courceStatus: [
          { value: 1, label: '开通' },
          { value: 0, label: '暂停' }
        ],
        searchData: {
          courseCode: '',
          courseName: '',
          categoryCode: '',
          isEnable: '',
          addStartTime: '',
          courseType: '',
          addEndTime: '',
          courseLevel: ''
        },
        updateId: '',
        btnUpload: false,
        num: [],
        // 分页
        tablePage: {
          currentPage: 1,
          size: 10
        },
        totalItems: 0, //总条数
        tableLoading: false,
        roleName: '',
        role: '',
        //筛选栏
        tableData: [], //表格数据
        dialogVisible: false, // 修改弹窗是否展示
        dialogWordTest: false,
        isRouterAlive: true, //局部刷新
        formRules: {
          isEnable: [{ required: true, message: '必填', trigger: 'blur' }]
        },
        rules: {
          // 表单提交规则
          categoryCode: [{ required: true, message: '请选择', trigger: 'blur' }],
          courseName: [{ required: true, message: '请输入', trigger: 'blur' }],
          courseType: [{ required: true, message: '请选择', trigger: 'blur' }],
          courseLevel: [{ required: true, message: '请选择', trigger: 'change' }],
          courseCover: [{ required: true, message: '请上传封面', trigger: 'change' }],
          courseDescription: [{ required: true, message: '请输入', trigger: 'blur' }],
          isEnable: [{ required: true, message: '请选择', trigger: 'change' }]
        },
        categoryTypeList: [], //课程分类
        // courseStageType: [], //课程学段类型
        coverImg: '',
        dialogOpenimg: false,
        courseType: '',
        id: '',
        videoUpload: null, // 视频上传实例
        userid: '1723c88563', //从点播后台查看获取
        secretkey: 'Jkk4ml1Of8', //从点播后台查看获取
        writeToken: '94064a19-4d28-4ce9-aa05-96d476016215',
        ptime: '',
        imageForm: [
          { name: 'baseUrl', baseUrl: '', id: '', status: '', title: '' },
          { name: 'vowelUrl', vowelUrl: '', id: '', status: '', title: '' },
          {
            name: 'consonantUrl',
            consonantUrl: '',
            id: '',
            status: '',
            title: ''
          },
          { name: 'summaryUrl', summaryUrl: '', id: '', status: '', title: '' },
          { name: 'reviewUrl', reviewUrl: '', id: '', status: '', title: '' },
          { name: 'studyUrl', studyUrl: '', id: '', status: '', title: '' }
        ],
        reviewUrlFileList: [],
        // imageForm:{
        //   baseUrl:'',//学前视频封面图
        //   vowelUrl:'',//元音视频封面图
        //   consonantUrl:'',//辅音视频封面
        //   summaryUrl:''//总结视频
        // }
        loadingInstance: null,
        lessonMaterialsFileList: []
      };
    },
    computed: {
      ...mapGetters(['roles'])
    },

    created() {
      if (window.localStorage.getItem('isJump') && JSON.parse(window.localStorage.getItem('isJump'))) {
        // console.log('跳转', JSON.parse(window.localStorage.getItem('searchData')));
        let searchDataTemp = window.localStorage.getItem('searchData') ? JSON.parse(window.localStorage.getItem('searchData')) : {};
        this.searchData.courseCode = searchDataTemp.courseCode || '';
        this.searchData.courseName = searchDataTemp.courseName || '';
        this.searchData.categoryCode = searchDataTemp.categoryCode || '';
        this.searchData.isEnable = searchDataTemp.isEnable || '';
        this.searchData.addStartTime = searchDataTemp.addStartTime || '';
        this.searchData.courseType = searchDataTemp.courseType || '';
        this.searchData.courseLevel = searchDataTemp.courseLevel || '';
        this.searchData.addEndTime = searchDataTemp.addEndTime || '';
        this.$nextTick(() => {
          this.updateChild();
        });
        window.localStorage.removeItem('isJump');
        this.tablePage = JSON.parse(window.localStorage.getItem('tablePage'));
      }
      this.fetchData();
      this.getCategoryType();
      ossPrClient();
      //获取学段下拉框
      // this.getStady();
      //获取课程类型下拉学段
      // this.getCourseType();
      var jsonData = JSON.stringify(this.roles);
      var s = JSON.stringify(this.roles).includes('admin');
      var obj = eval('(' + jsonData + ')');
      this.roleName = s;
      console.log(this.roleName);
      this.role = obj[0].val;
      console.log(obj[0].val);
      this.videoUpload = new PlvVideoUpload({
        region: 'line1', // (可选)上传线路, 默认line1
        events: {
          Error: (err) => {
            // 错误事件回调
            console.log(err);
            let errMag = `${err.message}`;
            this.$alert(errMag, '标题名称', {
              confirmButtonText: '确定',
              type: 'error'
            });
          },
          UploadComplete: () => {
            // 全部上传任务完成回调
            this.$message({
              message: '上传成功',
              type: 'success'
            });
          }
        }
      });
    },
    mounted() {
      this.autoUpdateUserData(null, this.videoUpload);
    },
    methods: {
      updateChild() {
        this.$refs.childRef.setSearchData(JSON.stringify(this.searchData));
      },
      //单词拼读删除
      handlSetRemoveReadWord() {
        this.readWordUrl = '';
      },
      handleSetReadWordSuccess(response, file, fileList) {},
      //必须是Word文件
      isWord(file) {
        return /\.(doc|docx)$/.test(file.name);
      },
      beforeWordUpload(file) {
        if (!this.isWord(file)) {
          this.$message.error('只能上传word文件！');
          return false;
        }
      },
      //删除复习讲义
      removeReview(file) {
        console.log('remove', file);
        this.videoForm.reviewTextUrl = '';
      },
      //删除备课资料
      removeLesson(file, fileList) {
        console.log(`删除备课资料`, file, fileList, this.lessonMaterialsFileList);
        this.lessonMaterialsFileList = fileList;
        this.videoForm.lessonUrl = fileList.map((item) => {
          return {
            value: item.url,
            key: item.name || item.url.split('/').pop() || '未命名文件'
          };
        });
        console.log(`删除备课资料`, this.videoForm.lessonUrl);
        // this.videoForm.lessonUrl = '';
      },
      autoUpdateUserData(timer, videoUpload) {
        // 启动获取用户信息
        this.getUserData(videoUpload);
        if (timer) {
          clearTimeout(timer);
          timer = null;
        }
        timer = setTimeout(() => {
          this.autoUpdateUserData(timer, videoUpload);
        }, 3 * 50 * 1000);
      },
      getUserData() {
        // 获取用户详细信息
        this.ptime = new Date().getTime();
        let userData = {
          userid: this.userid,
          ptime: this.ptime,
          sign: this.getSignData().sign,
          hash: this.getSignData().hash
        };
        this.videoUpload.updateUserData(userData);
      },
      getSignData() {
        // 加密信息参数
        let hash = md5(this.ptime + this.writeToken);
        let sign = md5(this.secretkey + this.ptime);
        return {
          hash: hash,
          sign: sign
        };
      },
      transformSize(bytes) {
        // 文件大小转换
        const bt = parseInt(bytes);
        let result;
        if (bt === 0) {
          result = '0B';
        } else {
          const k = 1024;
          const sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
          const i = Math.floor(Math.log(bt) / Math.log(k));
          if (typeof i !== 'number') {
            result = '-';
          } else {
            result = (bt / Math.pow(k, i)).toFixed(2) + sizes[i];
          }
        }
        return result;
      },
      loadPlayerScript(callback) {
        console.log(callback, 'callback00');
        if (!window.polyvPlayer) {
          const myScript = document.createElement('script');
          myScript.setAttribute('src', this.vodPlayerJs);
          myScript.onload = callback;
          document.body.appendChild(myScript);
        } else {
          callback();
        }
      },
      loadPlayerBaseUrl() {
        const polyvPlayer = window.polyvPlayer;
        this.player = polyvPlayer({
          wrap: `#baseUrlPlayer`,
          width: 148,
          height: 148,
          vid: this.videoForm.baseUrl
        });
      },
      loadPlayerVowelUrl() {
        const polyvPlayer = window.polyvPlayer;
        this.player = polyvPlayer({
          wrap: `#vowelUrlPlayer`,
          width: 148,
          height: 148,
          vid: this.videoForm.vowelUrl
        });
      },
      loadPlayerConsonantUrl() {
        const polyvPlayer = window.polyvPlayer;
        this.player = polyvPlayer({
          wrap: `#consonantUrlPlayer`,
          width: 148,
          height: 148,
          vid: this.videoForm.consonantUrl
        });
      },
      loadPlayerSummaryUrl() {
        const polyvPlayer = window.polyvPlayer;
        this.player = polyvPlayer({
          wrap: `#summaryUrlPlayer`,
          width: 148,
          height: 148,
          vid: this.videoForm.summaryUrl
        });
      },
      getVideo(item, vid) {
        if (item === 'baseUrl') {
          this.loadPlayerScript(this.loadPlayerBaseUrl);
        } else if (item === 'vowelUrl') {
          this.loadPlayerScript(this.loadPlayerVowelUrl);
        } else if (item === 'consonantUrl') {
          this.loadPlayerScript(this.loadPlayerConsonantUrl);
        } else if (item === 'summaryUrl') {
          this.loadPlayerScript(this.loadPlayerSummaryUrl);
        }
        // this.loadPlayerScript(this.loadPlayer1,item,vid)
      },

      getMakeVideo(courseCode) {
        courseDictationListApi.getVideoUrlByCourseCode(courseCode).then((res) => {
          if (res.code === 20000) {
            this.reviewUrlFileList = [];
            this.lessonMaterialsFileList = [];
            this.videoForm = res.data.data;
            if (this.courseLevel == 2) {
              if (res.data.data.reviewUrl && res.data.data.summaryUrl) {
                this.dialogMake = false;
                this.$router.push({
                  name: 'makeCourseDictationList',
                  query: {
                    courseCode: this.videoForm.courseCode,
                    courseType: this.courseType,
                    id: this.id,
                    courseLevel: this.courseLevel
                  }
                });
                window.localStorage.setItem('searchData', JSON.stringify(this.searchData));
                window.localStorage.setItem('tablePage', JSON.stringify(this.tablePage));
                console.log('this.tablePagetablePagetablePage', this.videoForm);
                return;
              }
            } else {
              if (res.data.data.reviewTextUrl && res.data.data.reviewUrl && res.data.data.summaryUrl) {
                this.dialogMake = false;
                this.$router.push({
                  name: 'makeCourseDictationList',
                  query: {
                    courseCode: this.videoForm.courseCode,
                    courseType: this.courseType,
                    id: this.id,
                    courseLevel: this.courseLevel
                  }
                });
                return;
              }
            }
            if (res.data.data.studyUrl) {
              this.dialogMake = false;
              this.$router.push({
                name: 'makeCourseDictationList',
                query: {
                  courseCode: this.videoForm.courseCode,
                  courseType: this.courseType,
                  id: this.id,
                  courseLevel: this.courseLevel
                }
              });
              return;
            }
            if (res.data.data.reviewTextUrl) {
              const match = res.data.data.reviewTextUrl.match(/.*\/(.+?)\/?$/);
              const fileName = match ? match[1] : '';
              this.reviewUrlFileList.push({
                url: res.data.data.reviewUrl,
                name: fileName
              });
            }
            // 备课资料回显
            if (res.data.data.lessonUrl) {
              this.lessonMaterialsFileList = res.data.data.lessonUrl.map((item) => {
                // const match = item.match(/.*\/(.+?)\/?$/);
                // const fileName = match ? match[1] : '';
                return {
                  url: item.value,
                  name: item.key
                };
              });
            }
            const data = ['baseUrl', 'vowelUrl', 'consonantUrl', 'summaryUrl', 'reviewUrl', 'studyUrl'];
            data.forEach((item, index) => {
              if (res.data.data[item]) {
                courseDictationListApi.getVideoInfo(res.data.data[item]).then((data) => {
                  if (res.code === 20000) {
                    console.log('aaaaaaaa', data.data.data.data[0].basicInfo);
                    this.imageForm[index][item] = data.data.data.data[0].basicInfo.coverURL;
                    this.imageForm.forEach((f) => {
                      console.log(f.name, item);
                      if (f.name === item) {
                        f.status = data.data.data.data[0].basicInfo.status;
                        f.title = data.data.data.data[0].basicInfo.title;
                        // if(f.status === 60 || f.status === 61){
                        //   this.getVideo(item,res.data.data[item])
                        //  }
                      }
                    });
                    console.log(this.imageForm, 'fom');
                  }
                });
              }
            });
          }
        });
      },
      // loadPlayer(item,vid) {
      //   console.log(`#${item}Player`,vid,'vid');
      //   const polyvPlayer = window.polyvPlayer;
      //   this.player = polyvPlayer({
      //     wrap: `#${item}Player`,
      //     width: 800,
      //     height: 533,
      //     vid:vid ,
      //   });
      // },
      delBaseUrl(value, refName, Index) {
        if (this.videoUpload) {
          courseDictationListApi.deleteVideo(this.videoForm[value]).then((res) => {
            if (res.code === 20000) {
              this.$message.success('删除成功');
            }
          });
          console.log(this.imageForm[Index].id);
          this.videoUpload.removeFile(this.imageForm[Index].id);
          // this.videoUpload.clearAll();

          (this.$refs[refName].value = ''), (this.imageForm[Index].id = '');
          this.videoForm[value] = '';
          console.log(this.imageForm[Index].id, 'value23232');
        }
      },
      startBaseUrl(value, Index) {
        // 全部上传
        if (this.videoUpload) {
          // this.videoUpload.resumeFile(this.videoForm[value]);
          this.videoUpload.resumeFile(this.imageForm[Index].id);
          // this.videoUpload.startAll();
        }
      },
      doUpload(value, refName, Index) {
        // this.loadingInstance = Loading.service({
        //   lock: true,
        //   text: '视频上传中...',
        //   spinner: 'el-icon-loading',
        //   background: 'rgba(0, 0, 0, 0.7)'
        // });
        this.btnUpload = true;
        this.num.push({ id: Index, videoStatus: false });
        let inputDOM = this.$refs[refName]; // 通过DOM取文件数据
        let data = Object.values(inputDOM.files);
        console.log('data----------', data);
        const videoFormat = ['video/mp4', 'video/webm', 'video/ogg'];
        if (!videoFormat.includes(data[0].type)) {
          console.log('选择了非视频格式文件', inputDOM);
          this.$refs[refName].value = null;
          this.$message.warning('请选择视频格式文件');
          return;
        } else {
          console.log('选择的是视频格式文件');
        }
        if (data.length > 0) {
          console.log(data, value, refName, Index, '传参');
          data.forEach((file, index, arr) => {
            let fileSetting = {
              // 文件上传相关信息设置
              title: file.name, // 标题
              desc: 'jssdk插件上传', // 描述
              cataid: process.env.VUE_APP_BASE_CATAID, // 上传分类目录ID
              tag: '', // 标签
              luping: 0, // 是否开启视频课件优化处理，对于上传录屏类视频清晰度有所优化：0为不开启，1为开启
              keepsource: 0, // 是否源文件播放（不对视频进行编码）：0为编码，1为不编码
              state: '' //用户自定义信息，如果提交了该字段，会在服务端上传完成回调时透传返回。
            };
            console.log(fileSetting, 'fileSetting', this.videoUpload);
            let uploadManager = this.videoUpload.addFile(
              file, // file 为待上传的文件对象
              {
                FileStarted: (data) => this.onFileStarted(data, value, Index), // 文件开始上传回调
                FileProgress: (data) => this.onFileProgress(data, value, Index), // 文件上传中回调
                FileSucceed: (data) => this.onFileSucceed(data, value, Index), // 文件上传成功回调
                FileFailed: (data) => this.onFileFailed(data, value, Index), // 文件上传失败回调
                FileStopped: (data) => this.onFileStopped(data, value, Index) // 文件上传停止回调
              },
              fileSetting
            );
            console.log(uploadManager, 'uploadManageruploadManager');
            // this.loadPlayerScript(this.loadPlayer);

            this.addTableData(uploadManager, value, Index);
            this.startBaseUrl(value, Index);
          });
        }
      },
      onFileStarted(data, value, Index) {
        console.log('文件上传开始: ', data);
        this.$set(this.imageForm[Index], 'progress', 0);
        this.$set(this.imageForm[Index], 'failed', false);
        // this.tableData.filter((item) => item.id === data.uploaderid)[0].progress = 0
      },
      onFileProgress(data, value, Index) {
        let p = parseInt(data.progress * 100); // 上传的进度条
        // this.$set(this.imageForm[Index], 'progress', p);
        console.log('文件上传中11111: ', data, Index, this.imageForm[Index]);
        this.imageForm[Index].progress = p;
        // this.tableData.filter((item) => item.id === data.uploaderid)[0].progress = p;
      },
      addTableData(data, value, Index) {
        console.log(data.id, Index, 'imageFormimageForm', data);
        this.imageForm[Index].id = data.id;
        this.videoForm[value] = data.id;
        console.log('this.imageForm is ', this.imageForm);
      },
      onFileSucceed(data, value, Index) {
        this.imageForm[Index].progress = 101;
        this.vid = data.fileData.vid;
        console.log('this.vid is', data.fileData, value, Index);
        if (this.vid) {
          setTimeout(() => {
            courseDictationListApi.getVideoInfo(this.vid).then((res) => {
              if (res.code === 20000) {
                this.videoForm[value] = this.vid;
                this.imageForm[Index][value] = res.data.data.data[0].basicInfo && res.data.data.data[0].basicInfo.coverURL;
                this.num.find((e) => e.id == Index).videoStatus = true;
                console.log(this.num);

                if (this.num.find((o) => o.videoStatus == false)) {
                } else {
                  this.btnUpload = false;
                  this.num = [];
                }

                // console.log(this.imageForm.filter(res => res.name === value)?.status,'121212');
                // this.imageForm.filter(item => item.name === value)?.status = res.data.data.data[0].basicInfo.status
                this.$nextTick(() => {
                  // 以服务的方式调用的 Loading 需要异步关闭
                  if (this.loadingInstance) {
                    this.loadingInstance.close();
                  }
                });
              }
            });
          }, 1000);
        }

        console.log(data.fileData.vid, 'vid');
        // this.loadPlayerScript(this.loadPlayer);
      },
      onFileFailed(data, value, Index) {
        console.log('failed文件上传失败: ', data);
        this.$set(this.imageForm[Index], 'failed', true);
        console.log('this.imageForm is ', this.imageForm);
        this.$nextTick(() => {
          // 以服务的方式调用的 Loading 需要异步关闭
          if (this.loadingInstance) {
            this.loadingInstance.close();
          }
        });
      },
      onFileStopped(data) {
        console.log('文件上传停止: ', data);
        if (this.loadingInstance) {
          this.loadingInstance.close();
        }
      },
      //删除总结视频
      summaryUrlDel() {
        this.videoForm.summaryUrl = '';
      },
      //删除辅音视频
      consonantUrlDel() {
        this.videoForm.consonantUrl = '';
      },
      //删除元音视频
      vowelUrlDel() {
        this.videoForm.vowelUrl = '';
      },
      //删除学前视频
      preschoolDel() {
        this.videoForm.baseUrl = '';
      },

      //关闭弹窗
      dialogMakeClose() {
        console.log('close---------', this.$refs.baseUrlInputer, this.videoForm, this.imageForm);
        // this.$refs.baseUrlInputer.value = null
        this.dialogMake = false;
        this.$refs['videoForm'].resetFields();
        for (let val in this.videoForm) {
          this.videoForm[val] = '';
        }
        for (let Index in this.imageForm) {
          this.videoUpload.removeFile(this.imageForm[Index].id);
        }
        this.imageForm = [
          { baseUrl: '', id: '' },
          { vowelUrl: '', id: '' },
          { consonantUrl: '', id: '' },
          { summaryUrl: '', id: '' },
          { reviewUrl: '', id: '' },
          { studyUrl: '', id: '' }
        ];
        this.num = [];
      },
      beforeUpload(file) {
        const isMp4 = file.type === 'video/mp4';
        // 限制文件最大不能超过 50M
        const isLt50M = file.size / 1024 / 1024 < 50;
        if (!isMp4) {
          this.$message.error('视频只能是mp4格式!');
        }
        //   if (!isLt50M) {
        //     this.$message.error("上传头像图片大小不能超过 300MB!");
        //   }
        return isMp4;
      },
      uploadDetailHttp({ file }) {
        const fileName = 'manage/' + Date.parse(new Date()) + '.mp4';
        this.$nextTick(function () {
          if (ossPrClient() && ossPrClient().put) {
            ossPrClient()
              .put(fileName, file)
              .then(({ res, url, name }) => {
                if (res && res.status === 200) {
                  console.log(`阿里云OSS上传图片成功回调1`, res, url, name);
                  if (url) {
                    return url;
                  }
                }
              })
              .catch((err) => {});
          }
        });
      },
      preschoolHttp({ file }) {
        this.preschoolLoading = true;
        const fileName = 'manage/' + Date.parse(new Date()) + '.mp4';
        this.$nextTick(function () {
          if (ossPrClient() && ossPrClient().put) {
            ossPrClient()
              .put(fileName, file)
              .then(({ res, url, name }) => {
                if (res && res.status === 200) {
                  console.log(`阿里云OSS上传图片成功回调1`, res, url, name);

                  this.videoForm.baseUrl = url;
                  this.preschoolLoading = false;
                } else {
                  console.log('失败00000000000');
                }
              })
              .catch((err) => {
                this.preschoolLoading = false;
              });
          }
        });
      },
      vowelHttp({ file }) {
        this.vowelLoading = true;
        const fileName = 'manage/' + Date.parse(new Date()) + '.mp4';
        this.$nextTick(function () {
          if (ossPrClient() && ossPrClient().put) {
            ossPrClient()
              .put(fileName, file)
              .then(({ res, url, name }) => {
                if (res && res.status === 200) {
                  console.log(`阿里云OSS上传图片成功回调1`, res, url, name);
                  this.videoForm.vowelUrl = url;
                  this.vowelLoading = false;
                  this.$nextTick(() => {
                    this.vowelLoading = false;
                  });
                }
              })
              .catch((err) => {
                //  that.$message.error('上传图片失败请检查网络或者刷新页面')
                this.vowelLoading = false;
                console.log(`阿里云OSS上传图片失败回调`, err);
              });
          }
        });
      },
      //复习讲义
      reviewTextUrlHttp({ file }) {
        // this.btnUpload = true;
        this.reviewTextUrlLoading = true;
        const fileName = 'manage/' + Date.parse(new Date()) + file.name.substr(file.name.lastIndexOf('.'));
        // console.log(fileName);
        this.$nextTick(function () {
          if (ossPrClient() && ossPrClient().put) {
            ossPrClient()
              .put(fileName, file)
              .then(({ res, url, name }) => {
                if (res && res.status === 200) {
                  console.log(`阿里云OSS上传图片成功回调1`, res, url, name);
                  this.videoForm.reviewTextUrl = url;
                  console.log(this.videoForm.reviewTextUrl, 'reviewTextUrlreviewTextUrl');
                  this.reviewTextUrlLoading = false;
                  this.$nextTick(() => {
                    this.btnUpload = false;
                    this.reviewTextUrlLoading = false;
                  });
                }
              })
              .catch((err) => {
                this.btnUpload = false;
                this.reviewTextUrlLoading = false;
              });
          }
        });
      },
      // 备课资料
      lessonMaterialsUrlHttp({ file }) {
        console.log(file, 'filefilefile');
        this.lessonUrlLoading = true;
        const fileName = 'manage/' + Date.parse(new Date()) + file.name.substr(file.name.lastIndexOf('.'));
        this.$nextTick(function () {
          if (ossPrClient() && ossPrClient().put) {
            ossPrClient()
              .put(fileName, file)
              .then(({ res, url, name }) => {
                if (res && res.status === 200) {
                  console.log(`阿里云OSS上传pdf成功回调1`, res, url, name);
                  this.lessonMaterialsFileList.push({
                    url: url,
                    name: file.name
                  });
                  // let temp = this.videoForm.lessonUrl ? this.videoForm.lessonUrl : [];
                  let newObj = this.lessonMaterialsFileList.map((item) => {
                    return {
                      value: item.url,
                      key: item.name || item.url.split('/').pop() || '未命名文件'
                    };
                  });
                  console.log(this.lessonMaterialsFileList, 'lessonMaterialsFileList', newObj);
                  // let temp = this.videoForm.lessonUrl ? this.videoForm.lessonUrl : [];
                  this.$set(this.videoForm, 'lessonUrl', newObj);
                  this.lessonUrlLoading = false;
                  this.$nextTick(() => {
                    this.lessonUrlLoading = false;
                  });
                }
              })
              .catch((err) => {
                this.lessonUrlLoading = false;
              });
          }
        });
      },
      //
      beforePDFUpload() {
        console.log('beforePDFUpload');
        console.log('最多上传3个文件', this.videoForm.lessonUrl);
      },
      handleExceed() {
        this.$message.warning('最多上传10个文件');
      },
      consonantHttp({ file }) {
        this.consonantLoading = true;
        const fileName = 'manage/' + Date.parse(new Date()) + '.mp4';
        this.$nextTick(function () {
          if (ossPrClient() && ossPrClient().put) {
            ossPrClient()
              .put(fileName, file)
              .then(({ res, url, name }) => {
                if (res && res.status === 200) {
                  console.log(`阿里云OSS上传图片成功回调1`, res, url, name);
                  this.videoForm.consonantUrl = url;
                  this.consonantLoading = false;
                  this.$nextTick(() => {
                    this.consonantLoading = false;
                  });
                }
              })
              .catch((err) => {
                this.consonantLoading = false;
              });
          }
        });
        // this.uploadDetailHttp({file},this.videoForm.consonantUrl,this.consonantLoading)
      },
      summaryHttp({ file }) {
        this.summaryLoading = true;
        const fileName = 'manage/' + Date.parse(new Date()) + '.mp4';
        this.$nextTick(function () {
          console.log('0000000000');
          if (ossPrClient() && ossPrClient().put) {
            ossPrClient()
              .put(fileName, file)
              .then(({ res, url, name }) => {
                if (res && res.status === 200) {
                  console.log(`阿里云OSS上传图片成功回调1`, res, url, name);
                  this.videoForm.summaryUrl = url;
                  this.summaryLoading = false;
                  this.$nextTick(() => {
                    this.summaryLoading = false;
                  });
                }
              })
              .catch((err) => {
                //  that.$message.error('上传图片失败请检查网络或者刷新页面')
                this.summaryLoading = false;
                console.log(`阿里云OSS上传图片失败回调`, err);
              });
          }
        });
      },
      //新增弹窗关闭
      addDialogClose(val) {
        this.dialogVisible = val;
        this.updateId = '';
        this.fetchData();
      },
      // 获取分类返回类型
      getCategoryType() {
        courseApitwo.categoryType().then((res) => {
          this.categoryTypeList = res.data;
        });
      },
      //获取学段下拉框
      // getStady() {
      //   var enType = "CourseStage";
      //   enTypes.getEnumerationAggregation(enType).then(res => {
      //     this.courseStageType = res.data;
      //   })
      // },
      //上传单词
      submitStepWord(form) {
        this.dialogMake = false;
      },
      addVideoForm() {
        console.log('111111111111111111111111');
        if (this.courseType === '1') {
          // return
          this.$refs['videoForm'].validate((valid) => {
            console.log(valid, 'valid-------------------');
            if (valid) {
              courseDictationListApi.updateVideoUrlByCourseCode(this.videoForm).then((res) => {
                if (res.code === 20000) {
                  console.log('3333333333333333333333333333');
                  this.dialogMake = false;
                  this.$message.success('提交成功');
                  this.$router.push({
                    name: 'makeCourseDictationList',
                    query: {
                      courseCode: this.videoForm.courseCode,
                      courseType: this.courseType,
                      courseLevel: this.courseLevel,
                      id: this.id
                    }
                  });
                  this.fetchData();
                  this.$refs['videoForm'].resetFields();
                }
              });
            }
          });
        } else if (this.courseType == '0' && this.courseLevel == '2') {
          this.$refs['videoForm'].validate((valid) => {
            console.log(this.videoForm, 'valid-------------------');
            if (valid) {
              courseDictationListApi.updateVideoUrlByCourseCode(this.videoForm).then((res) => {
                if (res.code === 20000) {
                  console.log('3333333333333333333333333333');
                  this.dialogMake = false;
                  this.$message.success('提交成功');
                  this.$router.push({
                    name: 'makeCourseDictationList',
                    query: {
                      courseCode: this.videoForm.courseCode,
                      courseType: this.courseType,
                      courseLevel: this.courseLevel,
                      id: this.id
                    }
                  });
                  this.fetchData();
                  this.$refs['videoForm'].resetFields();
                }
              });
            }
          });
        } else {
          console.log('3333333333333333333333333333');
          this.$router.push({
            name: 'makeCourseDictationList',
            query: {
              courseCode: this.videoForm.courseCode,
              courseType: this.courseType,
              id: this.id
            }
          });
        }
        // this.$router.push({name:'makeCourseDictationList',query:{courseCode:this.videoForm.courseCode,courseType:this.courseType,id:this.id}})
      },

      //制作课程
      courseMake(courseCode, courseType, id, courseLevel) {
        window.localStorage.setItem('searchData', JSON.stringify(this.searchData));
        window.localStorage.setItem('tablePage', JSON.stringify(this.tablePage));
        this.dialogMake = true;
        this.videoForm.courseCode = courseCode;
        this.courseType = courseType;
        this.courseLevel = courseLevel;
        this.id = id;
        this.getMakeVideo(courseCode);

        // this.$router.push({name:'makeCourseDictationList',query:{id:id,courseType:courseType}})
      },
      //搜索
      onSearch(data) {
        console.log(this.searchData, 'searchData');
        this.tablePage.currentPage = 1;
        this.searchData = data;
        this.fetchData();
      },
      //重置
      rest(data) {
        console.log(data, 'data');
        this.searchData = data;
      },
      fetchData() {
        this.tableLoading = true;
        const data = { ...this.tablePage, ...this.searchData };
        courseDictationListApi.pdCoursePage(data).then((res) => {
          if (res.code === 20000) {
            this.tableData = res.data.data;
            this.totalItems = Number(res.data.totalItems);
            console.log(res.data.totalItems);
            this.tableLoading = false;
          }
        });
      },
      //添加操作
      getAddForm() {
        this.dialogVisible = true;
        this.updateId = '';
      },
      //局部刷新
      reload() {
        this.isRouterAlive = false;
        this.$nextTick(function () {
          this.isRouterAlive = true;
        });
      },
      //配置
      wordSetting() {
        this.dialogWordTest = true;
      },
      // 点击编辑按钮
      handleUpdate(id) {
        this.dialogVisible = true;
        this.updateId = id;
        console.log(this.updateId);
      },
      // 状态改变事件
      change(radio) {
        if (radio == '1') {
          this.form.isEnable = 1;
        } else {
          this.form.isEnable = 0;
        }
      },
      // 课程开通与暂停
      courseStatus(id, status) {
        this.$confirm('确定操作吗?', '修改状态', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          courseDictationListApi.updateEnable({ id: id, isEnable: status === 1 ? 0 : 1 }).then((res) => {
            if (res.code === 20000) {
              this.$message.success('修改成功');
              this.fetchData();
            }
          });
        });
      },
      // 分页
      handleSizeChange(val) {
        this.tablePage.size = val;
        this.fetchData();
      },
      handleCurrentChange(val) {
        this.tablePage.currentPage = val;
        this.fetchData();
      },
      openImg(row) {
        this.coverImg = row.courseCover;
        this.dialogOpenimg = true;
        console.log();
      }
    }
  };
</script>

<style lang="less" scope="scope">
  ::v-deep .el-upload-list {
    width: auto !important;
  }
  .course_image_slot {
    height: 148px;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #c0c4cc;
  }
  .video_upload_table {
    width: 148px;
    height: 148px;
    border-radius: 8px;
    background-color: black;
  }
  .video_upload {
    position: absolute;
    z-index: 99999;
    width: 148px;
    height: 148px;
    border-radius: 8px;
    background-color: black;
  }
  .video_del {
    margin-left: 130px;
    position: absolute;
    z-index: 99999;
    color: white;
  }
  .isEnableRed {
    color: red;
  }
  .isEnableGreen {
    color: green;
  }
  .lh36 {
    line-height: 36px;
    font-size: 14px;
  }
  .marginRight {
    margin-right: 8px;
  }
  .marginLeft {
    margin-left: 8px;
  }
  .download {
    padding: 0 15px !important;
    margin: 0 !important;
    color: white !important;
  }
  .SearchForm {
    width: 100%;
    margin-bottom: 15px;
    background: #fff;
    border-radius: 6px;
    padding: 5px 0;
    padding-left: 5px;
    box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.2);
  }

  .btn-add {
    padding: 5px;
  }

  .red {
    color: red;
  }

  .green {
    color: green;
  }

  .course-table {
    text-align: center;
  }

  .course-table td,
  .course-table th {
    padding: 5px 0;
    text-align: center;
  }

  .course-table button {
    padding: 2px;
  }

  .mt22 {
    margin-top: 22px;
  }

  @media (max-width: 767px) {
    .el-message-box {
      width: 80% !important;
    }
  }
  /*当upLoadShow为true时，启用如下样式，即上传框的样式，若为false则不启用该样式*/
  .upLoadShow .el-upload {
    width: 10rem !important;
    height: 10rem !important;
    line-height: 10rem !important;
  }
  /*当upLoadHide为true时，启用如下样式，即缩略图的样式，若为false则不启用该样式*/
  .upLoadHide .el-upload-list--picture-card .el-upload-list__item {
    width: 10rem !important;
    height: 10rem !important;
    line-height: 10rem !important;
  }

  /*当upLoadHide为true时，启用如下样式，即上传框的样式，若为false则不启用该样式*/
  .upLoadHide .el-upload {
    display: none;
  }

  .coverimg {
    text-align: center !important;
    padding: 50px;
  }
  .imgDialog {
    .el-dialog {
      width: 30%;
    }

    .el-dialog__body {
      text-align: center !important;
    }
  }
  .upload_disable {
    color: #c0c4cc !important;
    border-color: #c0c4cc !important;
    background-color: transparent !important;
  }
</style>
