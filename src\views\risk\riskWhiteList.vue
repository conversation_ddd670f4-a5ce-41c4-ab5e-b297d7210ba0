<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="被授权方" prop="authAccept">
        <el-input v-model="queryParams.authAccept" placeholder="请输入被授权方" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd">新增
        </el-button>
      </el-col>
    </el-row>

    <el-table v-loading="loading" :data="noticeList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" align="center" prop="id" width="100" />
      <el-table-column label="被授权方" align="center" prop="authAccept" :show-overflow-tooltip="true" />
      <el-table-column label="操作" align="center">
        <template slot-scope="scope">
          <!-- <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)">修改
          </el-button> -->
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)">删除
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="被授权方类型" align="center" prop="acceptType" width="120" />
      <el-table-column label="风险行为类型" align="center" prop="behaviorType" />
      <el-table-column label="创建者" align="center" prop="createBy" />
      <el-table-column label="创建时间" align="center" prop="createTime" width="100" />
      <el-table-column label="备注" align="center" prop="remark" :show-overflow-tooltip="true" width="120" />
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="560px" append-to-body>
      <el-form label-position="top" ref="form" :model="form" :rules="rules" label-width="140px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="被授权方类型" prop="acceptType">
              <el-select v-model="form.acceptType" placeholder="请选择类型">
                <el-option v-for="dict in acceptTypeList" :key="dict.value" :label="dict.name"
                  :value="dict.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="授权风险行为类型" prop="behaviorType">
              <el-select v-model="form.behaviorType" placeholder="请选择类型">
                <el-option v-for="dict in riskBehaviorList" :key="dict.value" :label="dict.name"
                  :value="dict.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="被授权方" prop="authAccept">
              <el-input v-model="form.authAccept" placeholder="请输入被授权方" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input type="textarea" :rows="4" placeholder="请输入备注" :maxlength="200" show-word-limit
                v-model="form.remark">
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import riskWhiteListApi from "@/api/risk/riskWhiteList";
import pagination from '@/components/Pagination/index'

export default {
  name: "riskWhiteList",
  components: {
    pagination
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 公告表格数据
      noticeList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        authAccept: undefined,
        createBy: undefined,
        status: undefined
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        acceptType: [
          { required: true, message: "请选择被授权方类型", trigger: "blur" }
        ],
        authAccept: [
          { required: true, message: "请输入被授权方", trigger: "change" }
        ],
        behaviorType: [
          { required: true, message: "请选择授权风险行为", trigger: "change" }
        ]
      },
      //被授权方类型
      acceptTypeList: [],
      //风险行为类型
      riskBehaviorList: []
    };
  },
  created() {
    this.getAcceptTypeList();
    this.getList();
  },
  methods: {
    //获取被授权方类型
    getAcceptTypeList() {
      riskWhiteListApi.getAcceptType().then(res => {
        this.acceptTypeList = res.data;
      })
      //获取风控行为类型
      riskWhiteListApi.getBehaviorList().then((res) => {
        this.riskBehaviorList = res.data;
      })
    },
    /** 查询白名单列表 */
    getList() {
      this.loading = true;
      riskWhiteListApi.whiteListPage(this.queryParams).then(response => {
        this.noticeList = response.data.data;
        this.total = parseInt(response.data.totalItems);
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        authAccept: undefined,
        acceptType: undefined,
        behaviorType: undefined
      };
      if (this.$refs['form']) {
        this.$refs['form'].resetFields();
      }
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      if (this.$refs['queryForm']) {
        this.$refs['queryForm'].resetFields();
      }
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.noticeId)
      this.single = selection.length != 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加白名单";
    },
    /** 修改按钮操作 */
    /*    handleUpdate(row) {
          this.reset();
          const noticeId = row.noticeId || this.ids
          getNotice(noticeId).then(response => {
            this.form = response.data;
            this.open = true;
            this.title = "修改公告";
          });
        },*/
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate(valid => {
        if (valid) {
          riskWhiteListApi.addRiskWhiteList(this.form).then(response => {
            this.$message.success("新增成功");
            this.open = false;
            this.getList();
          });
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$confirm('确定删除”' + row.authAccept + '“吗?', "删除白名单", {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        riskWhiteListApi.deleteWhiteList(row.id).then(res => {
          this.$message.success('操作成功!')
          this.getList();
          this.getStatistics();
        })
      })
    }
  }
};
</script>
