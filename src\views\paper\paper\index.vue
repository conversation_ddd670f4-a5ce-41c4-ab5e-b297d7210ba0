<template>
  <div class="app-container">
    <el-form :inline="true" ref="queryForm" class="container-card" label-width="100px">
      <el-form-item label="学段：">
        <el-select
          v-model="dataQuery.gradeId"
          placeholder="学段"
          @change="levelChange"
          clearable
        >
          <el-option
            v-for="item in gradeList"
            :key="item.id"
            :value="item.id"
            :label="item.name"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="维度：">
        <el-select v-model="dataQuery.subjectId" clearable placeholder="请先选择学段">
          <el-option v-for="item in subjectFilter" :key="item.id" :value="item.id"
                     :label="item.name"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="试卷类型：">
        <el-select v-model="dataQuery.paperType" clearable>
          <el-option v-for="item in paperTypeList"
                     :key="item.value" :value="item.value" :label="item.label"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="试卷状态：">
        <el-select v-model="dataQuery.status" clearable>
          <el-option v-for="item in statusList"
                     :key="item.value" :value="item.value" :label="item.label"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-re" @click="resetQuery()">重置</el-button>
        <el-button type="primary" icon="el-icon-search" @click="getPageList()">搜索</el-button>
      </el-form-item>
      <el-form-item>
        <router-link :to="{path:'/paper/paperEdit'}" class="link-left">
          <el-button type="primary">添加</el-button>
        </router-link>
      </el-form-item>
    </el-form>
    <el-table class="common-table" v-loading="tableLoading" :data="tableData" style="width: 100%;margin-bottom: 20px;"
              row-key="id" border
              default-expand-all>
      <el-table-column prop="name" label="名称" show-overflow-tooltip/>
      <el-table-column prop="gradeId" label="年级名称" :formatter="gradeListFormatter" width="100"/>
      <el-table-column prop="withdrawnBonus" label="操作" width="220">
        <template slot-scope="scope">
          <el-button v-if="scope.row.status===1" type="danger" size="mini" @click="changeStatus(scope.row.id,2)">撤回
          </el-button>
          <el-button v-if="scope.row.status===0" type="success" size="mini"  @click="changeStatus(scope.row.id,1)">发布
          </el-button>
          <el-button v-if="scope.row.status===0" size="mini" @click="$router.push({path:'/paper/paperEdit',query:{id:scope.row.id}})">编辑
          </el-button>
          <el-button v-if="scope.row.status!==0" size="mini" @click="$router.push({path:'/paper/paperView',query:{id:scope.row.id}})">查看
          </el-button>
        </template>
      </el-table-column>
      <el-table-column prop="subjectId" label="学科名称" :formatter="subjectFormatter" width="100"/>
      <el-table-column prop="paperType" label="试卷类型" :formatter="paperTypeFormatter" width="100"/>
      <el-table-column prop="score" label="总分" width="60"/>
      <el-table-column prop="questionSize" label="题目数" width="80"/>
      <el-table-column prop="suggestTime" label="考试时长(分)" width="120"/>
      <el-table-column prop="createTime" label="创建时间" width="160"/>
      <el-table-column prop="status" label="试卷状态" :formatter="statusFormatter" width="100"/>
      <el-table-column prop="sort" label="排序" width="120">
        <template slot-scope="scope" align="center">
          <el-input v-model="scope.row.sort"
                    @blur="handleSort($event,scope.row)"/>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <el-col :span="24" style="margin-bottom: 20px">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
                     layout="total, sizes, prev, pager, next, jumper"
                     :total="tablePage.totalItems" @size-change="handleSizeChange" @current-change="handleCurrentChange"
      />
    </el-col>

  </div>
</template>

<script>
import paperApi from '@/api/paper/paper'
import subjectApi from '@/api/paper/subject'
import { pageParamNames } from '@/utils/constants'
import { mapGetters, mapState } from 'vuex'

export default {
  name: 'paperList',
  data() {
    var checkValue = (rule, value, callback) => {
      // 只能输入数字（正数和负数，小数整数）
      let reg =/^([-+]?[0-9]+[\d]*(.[0-9]{1,})?)$/
      let res = reg.test(value);
      if(res){
        callback();
      }else{
        callback(new Error(""))
      }
    }
    return {
      gradeList:[],
      tableData:[],
      paperTypeList:[{label: '固定试卷',value:1},{label: '时段试卷',value:2},{label: '任务试卷',value:3}],
      statusList:[{label: '未发布',value:0},{label: '已发布',value:1},{label: '已撤回',value:2}],
      dataQuery: {
        gradeId: null,
        subjectId: null,
        paperType:null,
        status:null
      },
      subjectFilter: null,
      subjectList: [],
      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      rules:{
        sort:[{ required: true,message :"",validator:checkValue ,trigger: "blur"}]
      }
    }
  },
  created() {
    this.initGrade()
    this.initSubject()
    this.getPageList()
  },
  methods: {
    handleSort(e,row){
      let boolean = new RegExp("^[1-9][0-9]*$").test(e.target.value)
      if(!boolean) {
        e.target.value = '';
        this.$message.error('请输入正整数')
      }
      paperApi.changeSort(row.id,e.target.value).then(res=>{
          this.$message.success("修改成功！");
          this.getPageList();
      })
    },
    initGrade(){
      subjectApi.gradeListAll().then(res=>{
        this.gradeList = res.data;
      })
    },
    changeStatus(id,status){
      if (status === 2){
        this.$confirm('此操作将永久撤回该试卷, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          paperApi.changeStatus(id,status).then(res => {
            this.$nextTick(() => this.getPageList())
            this.$message.success('成功')
          });
        })
      }
      if (status === 1){
        paperApi.changeStatus(id,status).then(res => {
          this.$nextTick(() => this.getPageList())
          this.$message.success('成功')
        });
      }
    },
    initSubject() {
      subjectApi.pageList().then(res => {
        this.subjectList = res.data.data
      })
    },
    levelChange() {
      this.dataQuery.subjectId = null
      this.subjectFilter = this.subjectList.filter(data => data.gradeId === this.dataQuery.gradeId)
    },
    gradeListFormatter(row, column, cellValue, index) {
      return this.subjectFormat(this.gradeList, cellValue)
    },
    subjectFormatter(row, column, cellValue, index) {
      return this.subjectFormat(this.subjectList, cellValue)
    },
    paperTypeFormatter(row, column, cellValue, index){
      return this.enumFormat(this.paperTypeList, cellValue)
    },
    statusFormatter(row, column, cellValue, index){
      return this.enumFormat(this.statusList, cellValue)
    },
    getPageList() {
      this.tableLoading = true
      this.dataQuery.pageNum = this.tablePage.currentPage
      this.dataQuery.pageSize = this.tablePage.size
      paperApi.pageList(this.dataQuery).then(res => {
        console.log(res.data.data)
        this.tableData = res.data.data
        this.tableLoading = false
        // 设置后台返回的分页参数
        pageParamNames.forEach(name => this.$set(this.tablePage, name, parseInt(res.data[name])))
      })
    },
    handlePage() {
      this.tablePage = {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
        this.getPageList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dataQuery = {
        grade: null,
        subjectId: null,
        paperType:null,
        status:null
      }
      this.getPageList()
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val
      this.getPageList()
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val
      this.getPageList()
    },
  },
  computed: {
    ...mapGetters('enumItem', ['enumFormat', 'subjectFormat']),
    ...mapState('enumItem', {
      gradeList: state => state.question.gradeList
    })
  }
}
</script>

<style>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.period-table td,
.period-table th {
  text-align: center;
}

@media (max-width: 767px) {
  .el-message-box {
    width: 80% !important;
  }
}

.el-tooltip__popper {
  max-width: 800px;
}
.el-input input{
  text-align: center;
}
</style>
