<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="88px">
      <el-form-item label="流程分类">
        <el-select class="filter-item" v-model="queryParams.categoryId" :clearable="true" filterable placeholder="流程分类"
          :loading="categoryIdWidget.loading" @visible-change="categoryIdWidget.onVisibleChange">
          <el-option v-for="item in categoryIdWidget.dropdownList" :key="item.categoryId" :value="item.categoryId"
            :label="item.name" />
        </el-select>

      </el-form-item>
      <el-form-item label="流程名称">
        <el-input class="filter-item" v-model="queryParams.processDefinitionName" :clearable="true" placeholder="流程名称" />
      </el-form-item>
      <el-form-item label="流程标识">
        <el-input class="filter-item" v-model="queryParams.processDefinitionKey" :clearable="true" placeholder="流程标识" />
      </el-form-item>
      <el-form-item label="发布状态">
        <el-select class="filter-item" v-model="queryParams.status" :clearable="true" filterable placeholder="发布状态">
          <el-option v-for="item in SysFlowEntryPublishedStatus.getList()" :key="item.id" :value="item.id"
            :label="item.name" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="success" icon="el-icon-search" size="mini" @click="getPageList">搜索</el-button>
        <el-button type="primary" size="mini" @click="onAddFlowEntryClick()">
          新建
        </el-button>
      </el-form-item>
    </el-form>

    <el-row>
      <el-col :span="24">
        <el-table ref="flowEntry" :data="tableData" size="mini" header-cell-class-name="table-header-gray">
          <el-table-column label="序号" header-align="center" align="center" type="index" width="55px"
            :index="tableData.getTableIndex" />
          <el-table-column label="流程名称" prop="processDefinitionName"></el-table-column>
          <el-table-column label="操作" fixed="right" width="250px">
            <template slot-scope="scope">
              <!-- <el-button class="table-btn success" @click.stop="onStartFlowEntryClick(scope.row)" type="text" size="mini">
                启动
              </el-button> -->
              <el-button class="table-btn success" @click.stop="onEditFlowEntryClick(scope.row)" type="text" size="mini">
                编辑
              </el-button>
              <el-button @click.stop="onPublishedClick(scope.row)" type="text" size="mini">
                发布
              </el-button>
              <el-button @click.stop="onPublishedEntryListClick(scope.row)" type="text" size="mini">
                版本管理
              </el-button>
              <el-button class="table-btn delete" @click.stop="onDeleteFlowEntryClick(scope.row)" type="text" size="mini">
                删除
              </el-button>
            </template>
          </el-table-column>
          <el-table-column label="流程标识" prop="processDefinitionKey">
          </el-table-column>
          <el-table-column label="流程分类" prop="flowCategory.name">
          </el-table-column>
          <el-table-column label="发布状态" prop="status">
            <template slot-scope="scope">
              <el-tag size="mini"
                :type="scope.row.status === SysFlowEntryPublishedStatus.PUBLISHED ? 'success' : 'warning'">
                {{ SysFlowEntryPublishedStatus.getValue(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="流程主版本" prop="mainFlowEntryPublish" header-align="center" align="center">
            <template slot-scope="scope">
              <el-tag v-if="scope.row.mainFlowEntryPublish" size="mini" type="primary" effect="dark">
                {{ 'V:' + scope.row.mainFlowEntryPublish.publishVersion }}
              </el-tag>
              <el-tag v-if="scope.row.mainFlowEntryPublish" size="mini" effect="dark" style="margin-left: 10px"
                :type="scope.row.mainFlowEntryPublish.activeStatus ? 'success' : 'danger'">
                {{ scope.row.mainFlowEntryPublish.activeStatus ? '激活' : '挂起' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="最近发布时间" prop="lastestPublishTime" />
          <el-table-column label="创建时间" prop="createTime" />
        </el-table>
        <!-- 分页 -->
        <el-col :span="20">
          <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
            layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
            @current-change="handleCurrentChange" />
        </el-col>
      </el-col>
    </el-row>
    <FormEditFlowEntry v-if="showFlowEntryDesign" :flowEntry="currentFlowEntry" @close="onEditFlowEntryClose" />
  </div>
</template>

<script>
import '@/api/activiti/staticDict/flowStaticDict.js'
import { DropdownWidget } from '@/utils/widget.js'
import flowEntryApi from '@/api/activiti/flowEntry'
import flowDictionaryApi from '@/api/activiti/flowDictionary'
import FormEditFlowEntry from './formEditFlowEntry.vue'
import FormPublishedFlowEntry from './formPublishedFlowEntry'
import { pageParamNames } from '@/utils/constants'
import flowOperationApi from '@/api/activiti/flowOperation'

export default {
  name: 'formFlowEntry',
  components: {
    FormEditFlowEntry
  },
  props: {},
  data() {
    return {
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      // 查询参数
      queryParams: {
        pageType: null,
        pageName: null
      },
      tableData: [],
      showFlowEntryDesign: false,
      currentFlowEntry: undefined,
      categoryIdWidget: new DropdownWidget(this.loadCategoryIdDropdownList)
    }
  },
  created() {
    this.getPageList()
  },
  methods: {
    getPageList() {
      this.queryParams.pageNum = this.tablePage.currentPage
      this.queryParams.pageSize = this.tablePage.size
      flowEntryApi.list(this.queryParams).then(res => {
        this.tableData = res.data.data
        this.loading = false
        // 设置后台返回的分页参数
        pageParamNames.forEach(name => this.$set(this.tablePage, name, parseInt(res.data[name])))
      })
    },
    onEditFlowEntryClose() {
      this.showFlowEntryDesign = false
      this.currentFlowEntry = null
    },
    /**
     * 流程分类下拉数据获取函数
     */
    loadCategoryIdDropdownList() {
      return new Promise((resolve, reject) => {
        flowDictionaryApi.list().then(res => {
          resolve(res.data)
        }).catch(e => {
          reject(e)
        })
      })
    },

    /**
     * 启动
     */
    onStartFlowEntryClick(row) {
      let params = {
        processDefinitionKey: row.processDefinitionKey
      }
      flowOperationApi.viewInitialTaskInfo(params).then(res => {
        if (res.data && res.data.taskType === this.SysFlowTaskType.USER_TASK && res.data.assignedMe) {
          this.$router.push({
            path: res.data.routerName || '/taskManager/handlerFlowTask',
            query: {
              processDefinitionKey: row.processDefinitionKey,
              formId: res.data.formId,
              routerName: res.data.routerName,
              readOnly: res.data.readOnly,
              taskName: '启动流程',
              flowEntryName: row.processDefinitionName,
              operationList: (res.data.operationList || []).filter(item => {
                return item.type !== this.SysFlowTaskOperationType.CO_SIGN && item.type !== this.SysFlowTaskOperationType.REVOKE
              }),
              variableList: res.data.variableList
            }
          })
        } else {
          flowOperationApi.startOnly({
            processDefinitionKey: row.processDefinitionKey
          }).then(res => {
            this.$message.success('启动成功！')
          }).catch(e => {
          })
        }
      }).catch(e => {
      })
    },
    /**
     * 新建
     */
    onAddFlowEntryClick() {
      this.$store.dispatch('hiddenMenu', { flag: true })
      this.showFlowEntryDesign = true
    },
    /**
     * 编辑
     */
    onEditFlowEntryClick(row) {
      this.$store.dispatch('hiddenMenu', { flag: true })
      this.currentFlowEntry = row
      this.showFlowEntryDesign = true
    },
    /**
     * 发布
     */
    onPublishedClick(row) {
      this.$confirm('是否发布当前工作流设计？').then(res => {
        if (
          row.entryId == null
        ) {
          this.$message.error('请求失败，发现必填参数为空！')
          return
        }
        let params = {
          entryId: row.entryId
        }
        flowEntryApi.publish(params).then(res => {
          this.$message.success('发布成功')
          this.getPageList()
        }).catch(e => {
        })
      }).catch(e => {
      })
    },
    /**
     * 版本管理
     */
    onPublishedEntryListClick(row) {
      this.$dialog.show('版本管理', FormPublishedFlowEntry, {
        area: ['80vw', '90vh']
      }, {
        flowEntry: row
      }).then(res => {
        this.getPageList()
      }).catch(e => {
        this.getPageList()
      })
    },
    /**
     * 删除
     */
    onDeleteFlowEntryClick(row) {
      if (
        row.entryId == null
      ) {
        this.$message.error('请求失败，发现必填参数为空！')
        return
      }
      let params = {
        entryId: row.entryId
      }

      this.$confirm('是否删除此流程？').then(res => {
        flowEntryApi.delete(params).then(res => {
          this.$message.success('删除成功')
          this.getPageList()
        }).catch(e => {
        })
      }).catch(e => {
      })
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val
      this.getPageList()
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val
      this.getPageList()
    }
  }
}
</script>
