<template>
  <div class="app-container">
    <el-form :inline="true" ref="queryForm" class="container-card" label-width="100px">
      <el-form-item style="display: flex;flex-direction: row-reverse">
        <el-popover placement="bottom" trigger="click">
          <el-button v-for="item in typeList" type="warning" size="mini"
            @click="$router.push({ path: item.path })">{{ item.name }}</el-button>
          <el-button slot="reference" type="primary" class="link-left">添加</el-button>
        </el-popover>
      </el-form-item>
    </el-form>
    <el-table class="common-table" v-loading="tableLoading" :data="tableData" style="width: 100%;margin-bottom: 20px;"
      row-key="id" border default-expand-all>
      <el-table-column prop="paperName" label="所属试卷" show-overflow-tooltip />
      <el-table-column prop="type" label="类型">
        <template scope="{row}">
          <span v-if="row.type === 'SCORE'">分数型</span>
          <span v-else>知识点型</span>
        </template>
      </el-table-column>
      <el-table-column label="操作">
        <template slot-scope="scope">
          <el-button type="danger" size="mini" @click="editReport(scope.row)">编辑</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <el-col :span="24" style="margin-bottom: 20px">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>
  </div>
</template>

<script>
import reportApi from '@/api/paper/report'
import { pageParamNames } from '@/utils/constants'

export default {
  name: 'paperList',
  data() {
    return {
      typeList: [{ name: "分数型", type: "SCORE", path: "/paper/scoreReport" }, { name: "知识点型", type: "KNOWLEDGE", path: "/paper/knowledgeReport" }],
      tableData: [],
      dataQuery: {},
      subjectFilter: null,
      subjectList: [],
      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
    }
  },
  created() {
    this.getPageList()
  },
  methods: {
    editReport(row) {
      this.typeList.map((item) => {
        if (item.type === row.type) {
          this.$router.push({ path: item.path, query: { paperId: row.paperId } });
        }
      });
    },
    getPageList() {
      this.tableLoading = true
      this.dataQuery.pageNum = this.tablePage.currentPage
      this.dataQuery.pageSize = this.tablePage.size
      reportApi.pageList(this.dataQuery).then(res => {
        this.tableData = res.data.data
        this.tableLoading = false
        // 设置后台返回的分页参数
        pageParamNames.forEach(name => this.$set(this.tablePage, name, parseInt(res.data[name])))
      })
    },
    handlePage() {
      this.tablePage = {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
        this.getPageList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dataQuery = {}
      this.getPageList()
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val
      this.getPageList()
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val
      this.getPageList()
    },
  },
}
</script>

<style>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.period-table td,
.period-table th {
  text-align: center;
}

@media (max-width: 767px) {
  .el-message-box {
    width: 80% !important;
  }
}

.el-tooltip__popper {
  max-width: 800px;
}
</style>
