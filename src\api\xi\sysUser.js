import request from '@/utils/request'

export default {
  // 分页查询
  list(data) {
    return request({
      url: '/xi/web/sysUser/list',
      method: 'GET',
      params: data
    })
  },
  roleList() {
    return request({
      url: '/xi/web/sysUser/roleList',
      method: 'GET'
    })
  },
  detail(id){
    return request({
      url: '/xi/web/sysUser',
      method: 'GET',
      params:{
        id:id
      }
    })
  },
//添加
  saveOrUpdate(data) {
    return request({
      url: '/xi/web/sysUser',
      method: 'POST',
      data
    })
  },
  //删除
  delete(id){
    return request({
      url: '/xi/web/sysUser',
      method: 'DELETE',
      params:{
        id:id
      }
    })
  }
}
