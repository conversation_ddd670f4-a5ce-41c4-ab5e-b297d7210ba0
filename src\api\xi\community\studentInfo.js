import request from '@/utils/request'

export default {
  // 分页查询
  list(data) {
    return request({
      url: '/xi/web/studentInfo/list',
      method: 'GET',
      params: data
    })
  },
  detail(id) {
    return request({
      url: '/xi/web/studentInfo',
      method: 'GET',
      params: {
        id: id
      }
    })
  },
  //明细
  flowList(data){
    return request({
      url: '/xi/web/studentAccountsFlow',
      method: 'GET',
      params: data
    })
  }
}
