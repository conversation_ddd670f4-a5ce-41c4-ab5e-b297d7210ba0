<!--音频答案题-->
<template>
  <div class="app-container">
    <el-form :model="form" ref="form" label-width="120px" v-loading="formLoading" :rules="rules">
      <el-form-item label="学段：" prop="grade" required>
        <el-select v-model="form.grade" @change="choseGradeChange()">
          <el-option v-for="(item, index) in gradeList" :key="index" :label="item" :value="item"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="维度：" prop="dimensionId" required>
        <el-select v-model="form.dimensionId" @change="dimensionIdChange()">
          <el-option v-for="(item, index) in gradeDimensionList" :key="index" :label="item.name" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="题型：" prop="questionTypeName" required>
        <el-input v-model="form.questionTypeName" style="width: 200px" disabled />
      </el-form-item>
      <el-form-item label="类型：" prop="type" required>
        <el-select v-model="form.type" placeholder="请选择" @change="typeChange">
          <el-option v-for="item in calculationType" :key="item.code" :value="item.code" :label="item.msg"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="有标题：" required>
        <el-radio-group v-model="form.expandInfo.titleFlag">
          <el-radio :label="false">否</el-radio>
          <el-radio :label="true">是</el-radio>
        </el-radio-group>
        <el-form-item v-show="form.expandInfo.titleFlag">
          <el-input v-model="form.expandInfo.titleInfo" placeholder="输入标题" />
        </el-form-item>
      </el-form-item>
      <el-form-item label="有图片：" required>
        <el-radio-group v-model="form.expandInfo.imgFlag">
          <el-radio :label="false">否</el-radio>
          <el-radio :label="true">是</el-radio>
        </el-radio-group>
        <el-form-item v-show="form.expandInfo.imgFlag">
          <my-upload @handleSuccess="handleSuccess" @handleRemove="handleRemove" :fullUrl="true" :file-list="fileList" :showTip="false" />
          <el-input type="hidden" v-model="form.expandInfo.imgInfo" />
        </el-form-item>
      </el-form-item>
      <!-- <el-form-item label="有音频：" required>
        <el-radio-group v-model="form.expandInfo.audioFlag">
          <el-radio :label="false">否</el-radio>
          <el-radio :label="true">是</el-radio>
        </el-radio-group>
        <el-form-item v-show="form.expandInfo.audioFlag">
          <upload-file
            @handleSuccess="handleAudioSuccess"
            @handleRemove="handleAudioRemove"
            :file-list="audioFileList"
          />
          <el-input type="hidden" v-model="form.expandInfo.audioInfo" />
        </el-form-item>
      </el-form-item> -->
      <el-form-item label="有视频：" required>
        <el-radio-group v-model="form.expandInfo.videoFlag">
          <el-radio :label="false">否</el-radio>
          <el-radio :label="true">是</el-radio>
        </el-radio-group>
        <el-form-item v-show="form.expandInfo.videoFlag">
          <input ref="inputer" type="file" class="upload" @change="doUpload" />
          <el-input type="hidden" v-model="form.expandInfo.videoInfo" />
        </el-form-item>
      </el-form-item>

      <!--心算&珠算编辑-->
      <div v-if="form.id">
        <el-form-item v-if="form.type != 'CALCULATION_ABACUS'" label="题干：" prop="stem" required>
          <el-input v-model="form.stem" style="width: 50%"></el-input>
        </el-form-item>
        <el-form-item label="音频：" required>
          <el-input style="width: 50%" v-model="questionDataList[0].label"></el-input>
        </el-form-item>
        <el-form-item label="题目：" required>
          <el-input style="width: 50%" v-model="questionDataList[0].value.info"></el-input>
        </el-form-item>
        <el-form-item label="答案：" required>
          <el-input style="width: 50%" v-model="questionDataList[0].value.answer"></el-input>
        </el-form-item>
      </div>
      <!--心算添加-->
      <div v-else-if="!form.id && form.type != 'CALCULATION_ABACUS'">
        <el-form-item label="题干：" prop="stem" required>
          <el-input v-model="form.stem" style="width: 50%"></el-input>
        </el-form-item>
        <el-form-item label="音频：" required>
          <el-input style="width: 50%" v-model="questionDataList[0].label"></el-input>
        </el-form-item>
        <el-form-item label="题目：" required>
          <el-input style="width: 50%" v-model="questionDataList[0].value.info"></el-input>
        </el-form-item>
        <el-form-item label="答案：" required>
          <el-input style="width: 50%" v-model="questionDataList[0].value.answer"></el-input>
        </el-form-item>
      </div>
      <!--珠算添加-->
      <div v-else-if="!form.id && form.type == 'CALCULATION_ABACUS'">
        <div v-for="(item, index) of questionDataList" :key="index">
          <el-form-item class="group-top" label="音频：" required>
            <el-input class="music-input" v-model="item.label"></el-input>
            <span v-if="index == 0">
              <el-button type="success" icon="el-icon-plus" style="margin-left: 20px" @click="clickAddQuestion(index)">添加题目</el-button>
            </span>
            <span v-else>
              <el-button type="success" icon="el-icon-plus" style="margin-left: 20px" @click="clickAddQuestion(index)">添加题目</el-button>
              <el-button type="danger" icon="el-icon-delete" @click="removeQuestion(index)">删除题目</el-button>
            </span>
          </el-form-item>
          <el-form-item label="题目：" required>
            <el-input style="width: 80%" v-model="item.value.info"></el-input>
          </el-form-item>
          <el-form-item label="答案：" required>
            <el-input style="width: 80%" v-model="item.value.answer"></el-input>
          </el-form-item>
        </div>
      </div>

      <el-form-item label="分数：" prop="score">
        <el-input-number v-model="form.score" :precision="0" :step="1" :min="0" :max="100"></el-input-number>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="inspectHaveVideo">提交</el-button>
        <el-button @click="resetForm">重置</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
  import Ueditor from '@/components/Ueditor/index.vue';
  import MyUpload from '@/components/Upload/MyUpload.vue';
  import UploadFile from '@/components/Upload/UploadFile.vue';
  import courseQuestionApi from '@/api/abacusMentalCalc/courseQuestion';
  import PlvVideoUpload from '@polyv/vod-upload-js-sdk';
  import categoryApi from '@/api/abacusMentalCalc/category';
  import question from '@/api/paper/question';

  export default {
    components: {
      UploadFile,
      Ueditor,
      MyUpload
    },
    data() {
      return {
        questionDataList: [
          {
            label: '',
            value: {
              info: '',
              answer: ''
            }
          }
        ],

        videoUpload: null, // 视频上传实例
        userid: '1723c88563', //从点播后台查看获取
        secretkey: 'Jkk4ml1Of8', //从点播后台查看获取
        writeToken: '94064a19-4d28-4ce9-aa05-96d476016215', //从点播后台查看获取

        fileList: [],
        audioFileList: [],

        calculationType: [], //课程类型
        gradeDimensionList: [],
        gradeList: [],

        form: {
          id: null,
          answer: '',
          courseType: null,
          customInfo: '',
          dimension: null,
          dimensionId: null,
          grade: '',
          questionType: '',
          score: null,
          stem: '',
          step: '',
          type: null,
          expandInfo: {
            titleFlag: false,
            titleInfo: '',
            imgFlag: false,
            imgInfo: '',
            videoFlag: false,
            videoInfo: '',
            videoName: '',
            audioFlag: false,
            audioInfo: '',
            audioName: '',
            secondFlag: false,
            secondInfo: ''
          }
        },

        formLoading: false,
        rules: {
          stem: [{ required: true, message: '请输入题干', trigger: 'blur' }],
          score: [{ required: true, message: '请输入分数', trigger: 'blur' }]
        }
      };
    },
    mounted() {
      this.autoUpdateUserData(null, this.videoUpload);
    },
    created() {
      this.getCalculationType();
      this.getAllGrade();

      let formData = this.$route.query.formData;
      let _this = this;
      if (formData) {
        for (let key in formData) {
          this.form[key] = formData[key];
        }
        if (formData.id && parseInt(formData.id) !== 0) {
          _this.formLoading = true;
          courseQuestionApi.questionDetail(formData.id).then((re) => {
            _this.form = re.data;
            let customInfo = JSON.parse(this.form.customInfo); //解析customInfo
            customInfo.value = JSON.parse(customInfo.value);
            this.questionDataList.pop();
            this.questionDataList.push(customInfo);
            if (_this.form.expandInfo) {
              if (this.form.expandInfo.imgFlag) {
                this.fileList.push({ url: this.form.expandInfo.imgInfo });
              }
              if (this.form.expandInfo.audioFlag) {
                this.audioFileList.push({
                  name: this.form.expandInfo.audioName,
                  url: this.form.expandInfo.audioInfo.slice(1, -1) //解析audioInfo
                });
              }
            }
            this.getDimensionForGrade();
            _this.formLoading = false;
          });
        } else {
          this.getDimensionForGrade();
        }
      }

      this.videoUpload = new PlvVideoUpload({
        region: 'line1', // (可选)上传线路, 默认line1
        events: {
          Error: (err) => {
            // 错误事件回调
            console.log(err);
            let errMag = `（错误代码：${err.code}）${err.message}`;
            this.$alert(errMag, '标题名称', {
              confirmButtonText: '确定',
              type: 'error'
            });
          },
          UploadComplete: () => {
            // 全部上传任务完成回调
            console.info('上传结束：', this.videoUpload);
            this.$message({
              message: '全部上传任务完成',
              type: 'success'
            });
          }
        }
      });
    },
    methods: {
      clickAddQuestion(index) {
        this.questionDataList.splice(index + 1, 0, { label: '', value: { info: '', answer: '' } });
      },
      removeQuestion(index) {
        this.questionDataList.splice(index, 1);
      },
      typeChange() {
        this.form.stem = '';
      },
      getAllGrade() {
        categoryApi.getGrade().then((res) => {
          this.gradeList = res.data;
        });
      },
      getDimensionForGrade() {
        categoryApi.getDimensionForGrade(this.form.grade, null).then((res) => {
          this.gradeDimensionList = res.data;
        });
      },

      choseGradeChange() {
        if (this.form.dimension) {
          this.form.dimension = null;
          this.form.dimensionId = null;
        }
        this.getDimensionForGrade();
      },
      dimensionIdChange() {
        let result = this.gradeDimensionList.find((item) => {
          return item.id === this.form.dimensionId;
        });
        this.form.dimension = result.name;
      },

      getCalculationType() {
        courseQuestionApi.getCalculationType().then((res) => {
          this.calculationType = res.data;
        });
      },

      assertData() {
        for (let i = 0; i < this.questionDataList.length; i++) {
          let el = this.questionDataList[i];
          if (el.label == '') {
            this.$message.error('音频不可为空！');
            return false;
          }
          if (el.value.info == '') {
            this.$message.error('题目不可为空！');
            return false;
          }
          if (el.value.answer == '') {
            this.$message.error('答案不可为空！');
            return false;
          }
          if (el.label[el.label.length - 1] == '，') {
            //添加与编辑，结尾不可为中文逗号
            this.$message.error('音频结尾不可有中文逗号！');
            return false;
          }
          if (/[^0-9\u4e00-\u9fff，]/.test(el.label) && this.form.type == 'CALCULATION_ABACUS') {
            //添加与编辑，中间空格限制
            this.$message.error('除了中文逗号做间隔，不可使用其他符号！');
            return false;
          }
        }
        if (!this.form.id && this.form.type != 'CALCULATION_ABACUS') {
          //心算添加
          let el = this.questionDataList[0];
          let labelArr = el.label.split(/[,，]/); //音频
          let vInfoArr = el.value.info.split(/[,，]/); //题目
          let vAnswerArr = el.value.answer.split(/[,，]/); //答案
          let arr = [labelArr.length, vInfoArr.length, vAnswerArr.length];
          let maxNum = Math.max(...arr);
          for (let i = 0; i < arr.length; i++) {
            if (arr[i] != maxNum) {
              this.$message.error('题目数量不匹配！一共有：' + maxNum.toString() + '题');
              return false;
            }
          }
        }
        return true;
      },

      beforeSubmit() {
        if (!this.form.id && this.form.type != 'CALCULATION_ABACUS') {
          //心算添加
          let labelArr = this.questionDataList[0].label.split(/[,，]/); //音频
          let vInfoArr = this.questionDataList[0].value.info.split(/[,，]/); //题目
          let vAnswerArr = this.questionDataList[0].value.answer.split(/[,，]/); //答案
          let resultData = [];
          for (let i = 0; i < labelArr.length; i++) {
            let data = {
              label: labelArr[i],
              value: JSON.stringify({
                info: vInfoArr[i],
                answer: vAnswerArr[i]
              })
            };
            resultData.push(data);
          }
          this.form.customInfo = JSON.stringify(resultData);
        } else if (this.form.id && this.form.type != 'CALCULATION_ABACUS') {
          //心算编辑
          let customInfo = {
            label: this.questionDataList[0].label,
            value: JSON.stringify(this.questionDataList[0].value)
          };
          this.form.customInfo = JSON.stringify(customInfo);
        } else if (!this.form.id && this.form.type == 'CALCULATION_ABACUS') {
          //珠算添加
          let customInfo = [];
          for (let i = 0; i < this.questionDataList.length; i++) {
            customInfo.push({
              label: this.questionDataList[i].label,
              value: JSON.stringify(this.questionDataList[i].value)
            });
          }
          this.form.customInfo = JSON.stringify(customInfo);
        } else if (this.form.id && this.form.type == 'CALCULATION_ABACUS') {
          //珠算编辑
          let customInfo = {
            label: this.questionDataList[0].label,
            value: JSON.stringify(this.questionDataList[0].value)
          };
          this.form.customInfo = JSON.stringify(customInfo);
        }
      },

      submitForm() {
        let _this = this;
        this.$refs.form.validate((valid) => {
          if (valid) {
            if (!this.assertData()) return false;
            this.beforeSubmit();
            this.formLoading = true;
            if (this.form.id) {
              courseQuestionApi
                .questionEdit(this.form)
                .then((re) => {
                  if (re.success) {
                    _this.$message.success(re.message);
                    _this.$router.push({
                      path: '/abacusMentalCalc/questionIndex'
                    });
                  } else {
                    _this.$message.error(re.message);
                    this.formLoading = false;
                  }
                })
                .catch((e) => {
                  this.formLoading = false;
                });
            } else {
              courseQuestionApi
                .questionAdd(this.form)
                .then((re) => {
                  if (re.success) {
                    _this.$message.success(re.message);
                    _this.$router.push({
                      path: '/abacusMentalCalc/questionIndex'
                    });
                  } else {
                    _this.$message.error(re.message);
                    this.formLoading = false;
                  }
                })
                .catch((e) => {
                  this.formLoading = false;
                });
            }
          } else {
            return false;
          }
        });
      },
      resetForm() {
        this.fileList = [];
        this.audioFileList = [];
        let lastForm = this.form;
        this.$refs['form'].resetFields();
        this.form = {
          id: lastForm.id,
          answer: '',
          courseType: null,
          customInfo: '',
          dimension: lastForm.dimension,
          dimensionId: lastForm.dimensionId,
          grade: lastForm.grade,
          questionType: lastForm.questionType,
          score: null,
          stem: '',
          questionTypeName: lastForm.questionTypeName,
          step: '',
          type: null,
          expandInfo: {
            titleFlag: false,
            titleInfo: '',
            imgFlag: false,
            imgInfo: '',
            videoFlag: false,
            videoInfo: '',
            videoName: '',
            audioFlag: false,
            audioInfo: '',
            audioName: '',
            secondFlag: false,
            secondInfo: ''
          }
        };
        this.questionDataList = [
          {
            label: '',
            value: {
              info: '',
              answer: ''
            }
          }
        ];
      },

      handleAudioSuccess(url, fileName) {
        this.form.expandInfo.audioInfo = url;
        this.form.expandInfo.audioName = fileName;
      },
      handleAudioRemove() {
        this.form.expandInfo.audioInfo = '';
        this.form.expandInfo.audioName = '';
      },
      handleSuccess(url) {
        this.form.expandInfo.imgInfo = url;
      },
      handleRemove() {
        this.form.expandInfo.imgInfo = '';
      },

      inspectHaveVideo() {
        let inputDOM = this.$refs.inputer; // 通过DOM取文件数据
        let data = Object.values(inputDOM.files);
        if (data.length <= 0) {
          this.submitForm();
        } else {
          if (this.videoUpload) {
            this.videoUpload.startAll();
          }
        }
      },
      autoUpdateUserData(timer, videoUpload) {
        // 启动获取用户信息
        this.getSign();
        if (timer) {
          clearTimeout(timer);
          timer = null;
        }
        timer = setTimeout(() => {
          this.autoUpdateUserData(timer, videoUpload);
        }, 3 * 50 * 1000);
      },
      async getSign() {
        const { data } = await courseQuestionApi.getSign();
        let userData = {
          userid: '1723c88563', // Polyv云点播账号的ID
          ptime: data.ptime, // 时间戳
          sign: data.sign, // 是根据将secretkey和ts按照顺序拼凑起来的字符串进行MD5计算得到的值
          hash: data.hash //
        };
        this.videoUpload.updateUserData(userData);
      },
      doUpload() {
        // 选择文件
        let inputDOM = this.$refs.inputer; // 通过DOM取文件数据
        let data = Object.values(inputDOM.files);
        if (data.length > 0) {
          data.forEach((file, index, arr) => {
            let fileSetting = {
              // 文件上传相关信息设置
              title: file.name, // 标题
              desc: 'jssdk插件上传', // 描述
              cataid: process.env.VUE_APP_BASE_CATAID, // 上传分类目录ID
              tag: '', // 标签
              luping: 0, // 是否开启视频课件优化处理，对于上传录屏类视频清晰度有所优化：0为不开启，1为开启
              keepsource: 0, // 是否源文件播放（不对视频进行编码）：0为编码，1为不编码
              state: '' //用户自定义信息，如果提交了该字段，会在服务端上传完成回调时透传返回。
            };
            let uploadManager = this.videoUpload.addFile(
              file, // file 为待上传的文件对象
              {
                FileStarted: this.onFileStarted, // 文件开始上传回调
                FileProgress: this.onFileProgress, // 文件上传中回调
                FileSucceed: this.onFileSucceed, // 文件上传成功回调
                FileFailed: this.onFileFailed, // 文件上传失败回调
                FileStopped: this.onFileStopped // 文件上传停止回调
              },
              fileSetting
            );
            console.log(uploadManager);
          });
        }
      },
      onFileStarted(data) {
        console.log('文件上传开始: ', data);
        this.loading = this.$loading({
          lock: true,
          text: '视频上传中...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
      },
      onFileProgress(data) {
        console.log('文件上传中: ', data);
      },
      onFileSucceed(data) {
        if (this.loading) {
          this.loading.close();
        }
        console.log('文件上传成功: ', data);
        this.form.expandInfo.videoInfo = data.fileData.vid;
        this.form.expandInfo.videoName = data.fileData.filename;
        this.submitForm();
      },
      onFileFailed(data) {
        if (this.loading) {
          this.loading.close();
        }
        console.log('文件上传失败: ', data);
      },
      onFileStopped(data) {
        if (this.loading) {
          this.loading.close();
        }
        console.log('文件上传停止: ', data);
      }
    }
  };
</script>

<style lang="less" scoped>
  .question-item-label {
    display: flex;
    margin-bottom: 12px;
  }
  .music-input {
    width: 80%;
  }
  .group-top {
    margin-top: 50px;
  }
</style>
