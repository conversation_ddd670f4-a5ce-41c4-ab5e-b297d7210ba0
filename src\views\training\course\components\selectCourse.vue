<!-- 选择课程弹窗-->
<template>
  <el-dialog :visible.sync="coursePage.showDialog" title="选择课程" width="50%" :close-on-click-modal="false" @close="close">
    <el-form :model="courseForm" ref="queryForm" :inline="true">
      <el-form-item label="课程名称" prop="courseName">
        <el-input v-model="courseForm.courseName" clearable placeholder="请输入课程名称" maxlength="30"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="addCourseSeach(1)">查询</el-button>
        <el-button @click="searchResetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-table
      v-loading="listLoading"
      :data="tablePage.tableData"
      ref="multipleTable"
      @selection-change="handleSelectionChange"
      :row-key="getRowKeys"
      border
      fit
      highlight-current-row
      style="width: 100%"
    >
      <el-table-column type="selection" width="40" :reserve-selection="true"></el-table-column>
      <el-table-column prop="courseName" label="课程名称"></el-table-column>
      <el-table-column prop="courseOverview" label="课程简介">
        <template slot-scope="scope">
          <el-popover placement="top-start" width="300" trigger="hover" :disabled="scope.row.courseOverview.length <= 20">
            <div>{{ scope.row.courseOverview }}</div>
            <span slot="reference" v-if="scope.row.courseOverview.length <= 20">{{ scope.row.courseOverview }}</span>
            <span slot="reference" v-if="scope.row.courseOverview.length > 20">{{ scope.row.courseOverview.substr(0, 20) + '...' }}</span>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间" width="220"></el-table-column>
    </el-table>
    <!-- 分页 -->
    <el-col :span="24" style="margin-bottom: 20px">
      <el-pagination
        :current-page="tablePage.currentPage"
        :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="tablePage.totalItems"
        @size-change="handleCourseSizeChange"
        @current-change="handleCourseCurrentChange"
      />
    </el-col>
    <span slot="footer" class="dialog-footer">
      <el-button @click="close">关闭</el-button>
      <el-button type="primary" v-loading="saveLoading" @click="confirmCourseSelect">添加</el-button>
    </span>
  </el-dialog>
</template>

<script>
  import courseApi from '@/api/training/course';
  export default {
    name: 'selectCourse',
    props: {
      coursePage: {
        type: Object,
        default: () => {}
      }
    },
    data() {
      return {
        courseForm: {
          courseName: '',
          categoryCode: ''
        },
        listLoading: false,
        tablePage: {
          tableData: [],
          currentPage: 1,
          pageSize: 10,
          totalItems: 0,
          multipleSelection: []
        },
        saveLoading: false
      };
    },
    created() {},
    methods: {
      open(info) {
        this.courseForm.categoryCode = info;
        this.addCourseSeach();
      },
      addCourseSeach(page) {
        if (typeof page == 'number' && page > 0) {
          this.tablePage.currentPage = page;
        }
        let params = { pageNum: this.tablePage.currentPage, pageSize: this.tablePage.pageSize };
        params = { ...params, ...this.courseForm };
        this.listLoading = true;
        courseApi
          .getAddCoursePage(params)
          .then((res) => {
            this.tablePage.tableData = res.data;
            this.tablePage.totalItems = res.total;
          })
          .finally(() => {
            this.listLoading = false;
          });
      },
      searchResetQuery() {
        this.courseForm.courseName = '';
        this.addCourseSeach();
      },
      confirmCourseSelect() {
        if (this.tablePage.multipleSelection.length == 0) {
          this.$message.error('请先选择课程！');
        } else {
          this.saveLoading = true;
          const paramData = this.tablePage.multipleSelection.map((el) => {
            return {
              categoryCode: this.courseForm.categoryCode,
              courseId: el.id,
              courseCode: el.courseNo,
              status: 0
            };
          });
          let that = this;
          courseApi
            .addCourse(paramData)
            .then((res) => {
              that.$message.success('操作成功！');
              that.saveLoading = false;
              that.close();
              that.$emit('updateTable');
            })
            .finally(() => {
              that.saveLoading = false;
            });
        }
      },
      close() {
        this.tablePage.multipleSelection = [];
        this.$emit('closeDialog');
      },
      handleSelectionChange(val) {
        this.tablePage.multipleSelection = val;
      },
      getRowKeys(row) {
        return row.id;
      },
      handleCourseSizeChange(val) {
        this.tablePage.pageSize = val;
        if (this.tablePage.currentPage > Math.ceil(this.tablePage.totalItems / this.tablePage.pageSize)) {
          this.tablePage.currentPage = Math.ceil(this.tablePage.totalItems / this.tablePage.pageSize);
        }
        this.addCourseSeach();
      },
      handleCourseCurrentChange(val) {
        this.tablePage.currentPage = val;
        this.addCourseSeach();
      }
    }
  };
</script>

<style lang="less" scoped>
  ::v-deep .el-dialog__footer {
    text-align: center;
    .el-button + .el-button {
      margin-left: 50px;
    }
  }
</style>
