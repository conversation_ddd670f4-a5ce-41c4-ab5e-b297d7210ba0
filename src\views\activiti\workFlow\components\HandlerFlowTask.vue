<template>
  <div class="app-container">
    <div class="task-title">
      <div>
        <span class="text">{{ flowEntryName }}</span>
        <el-tag v-if="taskName" effect="dark" size="mini" type="success">
          {{ '当前节点：' + taskName }}
        </el-tag>
        <el-tag v-if="processInstanceInitiator" effect="dark" size="mini" type="info">
          {{ '发起人：' + processInstanceInitiator }}
        </el-tag>
      </div>
    </div>
    <el-row type="flex" justify="space-between" style="margin-bottom: 15px;">
      <el-radio-group size="small" v-model="currentPage" v-if="processInstanceId != null" style="min-width: 300px;">
        <el-radio-button label="formInfo">表单信息</el-radio-button>
        <el-radio-button v-if="processInstanceId != null" label="flowProcess">流程图</el-radio-button>
        <el-radio-button v-if="processInstanceId != null" label="approveInfo">审批记录</el-radio-button>
      </el-radio-group>
      <el-row class="task-operation" type="flex" justify="end" style="width: 100%;">
        <template v-if="$slots.operations">
          <slot name="operations"/>
        </template>
        <template v-else>
          <el-button v-for="(operation, index) in flowOperationList" :key="index"
                     size="small" :type="getButtonType(operation.type) || 'primary'" :plain="operation.plain || false"
                     @click="handlerOperation(operation)">
            {{ operation.label }}
          </el-button>
        </template>
      </el-row>
    </el-row>
    <el-scrollbar class="custom-scroll" :style="{height: (getMainContextHeight - 140) + 'px'}">
      <el-form ref="form" class="full-width-input" style="width: 100%;"
               label-width="100px" size="mini" label-position="right" @submit.native.prevent>
        <!-- 表单信息 -->
        <el-row v-show="currentPage === 'formInfo'" type="flex" :key="formKey">
          <div v-if="formLoading">
            <!--学管师表单-->
            <FormOpenLearnTube v-if="processDefinitionKey === 'openLearnTube'" :form="form"/>
            <!--体验中心审核-->
            <FormOpenExperience v-if="processDefinitionKey === 'openExperience2'" :form="form"/>
            <!--事业部审核-->
            <FormAddDivision v-if="processDefinitionKey === 'addDivision2'" :form="form"/>
            <!--开通托管中心-->
            <FormAddDealer v-if="processDefinitionKey === 'dealerAdd'" :form="form"/>
            <!--直营门店-->
            <FormAddDirectSchool v-if="processDefinitionKey === 'addDirectSchool'" :form="form"/>
            <!--市级服务商-->
            <FormOpenCityAgent v-if="processDefinitionKey === 'openCityAgent'" :form="form"/>
            <!--分公司开通-->
            <FormOpenCompany v-if="processDefinitionKey === 'openCompany'" :form="form"/>
            <!--区县服务商-->
            <FormOpenAreaAgent v-if="processDefinitionKey === 'openAreaAgent'" :form="form"/>
            <!--交付中心-->
            <FormDeliveryCenter v-if="processDefinitionKey === 'openDeliveryCenter'" :form="form"/>
            <!--超级俱乐部-->
            <FormOperations v-if="processDefinitionKey === 'addOperations'" :form="form"/>
          </div>
        </el-row>
        <slot/>
        <!-- 审批记录 -->
        <el-row v-if="currentPage === 'approveInfo'" :gutter="20" style="height: 600px">
          <el-col :span="24">
            <el-table :data="flowTaskCommentList" size="mini" border header-cell-class-name="table-header-gray"
                      :height="(getMainContextHeight - 150) + 'px'">
              <el-table-column label="序号" header-align="center" align="center" type="index" width="55px"/>
              <el-table-column label="流程环节" prop="taskName" width="200px"/>
              <el-table-column label="操作" width="150px">
                <template slot-scope="scope">
                  <el-tag size="mini" :type="getOperationTagType(scope.row.approvalType)" effect="dark">
                    {{ SysFlowTaskOperationType.getValue(scope.row.approvalType) }}
                  </el-tag>
                  <el-tag v-if="scope.row.delegateAssginee" size="mini" type="success" effect="plain"
                          style="margin-left: 10px;">{{ scope.row.delegateAssginee }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="执行人" prop="createUsername" width="200px"/>
              <el-table-column label="审批意见">
                <template slot-scope="scope">
                  <span>{{ scope.row.comment ? scope.row.comment : '' }}</span>
                </template>
              </el-table-column>
              <el-table-column label="处理时间" prop="createTime" width="200px"/>
            </el-table>
          </el-col>
        </el-row>
        <!-- 流程图 -->
        <el-row v-show="currentPage === 'flowProcess'">
          <ProcessViewer :style="{height: (getMainContextHeight - 148) + 'px'}"
                         :xml="processXml"
                         :finishedInfo="finishedInfo"
                         :allCommentList="flowTaskCommentList"
          />
        </el-row>
      </el-form>
    </el-scrollbar>
  </div>
</template>

<script>
import '../package/theme/index.scss';
/* eslint-disable-next-line */
// import FlowOperationController from '@/api/activiti/flowController.js';
import flowOperationApi from '@/api/activiti/flowOperation';
import ProcessViewer from '@/views/activiti/workFlow/components/ProcessViewer.vue';
import learnTubeApi from "@/api/learnTube/learnTube";
import openCommonApi from "@/api/activiti/openCommon";


import FormOpenLearnTube from '@/views/activiti/workFlow/components/FormOpenLearnTube.vue';
import FormOpenExperience from '@/views/activiti/workFlow/components/FormOpenExperience.vue';
import FormAddDivision from '@/views/activiti/workFlow/components/FormAddDivision.vue';
import FormAddDealer from '@/views/activiti/workFlow/components/FormAddDealer.vue';
import FormAddDirectSchool from '@/views/activiti/workFlow/components/FormAddDirectSchool.vue';
import FormOpenCityAgent from '@/views/activiti/workFlow/components/FormOpenCityAgent.vue';
import FormOpenCompany from '@/views/activiti/workFlow/components/FormOpenCompany.vue';
import FormOpenAreaAgent from '@/views/activiti/workFlow/components/FormOpenAreaAgent.vue';
import FormDeliveryCenter from '@/views/activiti/workFlow/components/FormDeliveryCenter.vue';
import FormOperations from '@/views/activiti/workFlow/components/FormOperations.vue';
import experienceApi from "@/api/experience";
import divisionAddApi from "@/api/divisionAdd";
import dealerListApi from "@/api/dealerList";
import schoolApi from "@/api/schoolList";
import agentApi from "@/api/agentAddList";
import companyApi from "@/api/companyList";
import areaAgentListApi from "@/api/areaAgentList";
import deliveryCenterApi from "@/api/delivery/deliveryCenter";

export default {
  name: 'handlerFowTask',
  components: {
    ProcessViewer,
    FormOpenLearnTube,
    FormOpenExperience,
    FormAddDivision,
    FormAddDealer,
    FormAddDirectSchool,
    FormOpenCityAgent,
    FormOpenCompany,
    FormOpenAreaAgent,
    FormDeliveryCenter,
    FormOperations
  },
  data() {
    return {
      formKey: new Date().getTime(),
      currentPage: 'formInfo',
      processXml: undefined,
      finishedInfo: undefined,
      flowTaskCommentList: [],
      processDefinitionKey: undefined,
      form: {},
      //流程实例id
      processInstanceId: undefined,
      // 流程定义id
      processDefinitionId: undefined,
      // 流程名称
      flowEntryName: undefined,
      // 发起人
      processInstanceInitiator: undefined,
      // 当前任务节点名称
      taskName: undefined,
      // 当前任务节点操作列表
      operationList: [],
      //限制组件渲染，等待表单数据加载完成后渲染组件
      formLoading: false,
    }
  },
  created() {
    let query = this.$route.query;
    this.processInstanceId = query.processInstanceId;
    // 流程定义id
    this.processDefinitionId = query.processDefinitionId;
    // 流程名称
    this.flowEntryName = query.flowEntryName;
    // 发起人
    this.processInstanceInitiator = query.processInstanceInitiator;
    // 当前任务节点名称
    this.taskName = query.taskName;
    // 当前任务节点操作列表
    this.operationList = query.operationList || [];
    //流程标识
    this.processDefinitionKey = query.processDefinitionKey;
    //渲染表单详情
    this.processFormInfo();
    // this.initData();
  },

  methods: {
    initData() {
      this.getTaskHighlightData();
      this.getTaskProcessXml();
      this.loadProcessCommentList();
    },

    processFormInfo() {
      //根据流程实例id获取关联表数据
      openCommonApi.getOpenCommonByProcessInstanceId(this.processInstanceId).then(res => {
        let openCommon = res.data
        switch (this.processDefinitionKey) {
          case "openLearnTube": //学管师审核
            learnTubeApi.detail(openCommon.relationId).then(res => {
              this.form = res.data
              this.form.fileList = [];
              this.form.address = this.form.province + this.form.city + this.form.area;
              for (let i = 0; i < this.form.idCardPhoto.length; i++) {
                this.form.fileList.push({'uid': i, 'url': 'https://document.dxznjy.com/' + res.data.idCardPhoto[i]});
              }
              this.formLoading = true
            })
            break;
          case "openExperience2": //体验中心审核
            experienceApi.echoDivision(openCommon.relationId).then((res) => {
              let data = res.data;
              data.center = [data.latitude, data.longitude];
              this.imageUrlDispose(data)
              this.form = data
              this.formLoading = true
            })
            break;
          case "addDivision2": //事业部审核
            divisionAddApi.echoDivision(openCommon.relationId).then((res) => {
              let data = res.data;
              data.center = [data.latitude, data.longitude];
              this.imageUrlDispose(data);
              this.form = data
              this.formLoading = true
            })
            break;
          case "dealerAdd": //开通托管中心
            dealerListApi.queryActive(openCommon.relationId).then(res => {
              let data = res.data;
              this.imageUrlDispose(data);
              this.form = data
              this.formLoading = true
            })
            break;
          case "addDirectSchool": //直营门店
            schoolApi.schoolDetail(openCommon.relationId).then(res => {
              console.log(res)
              let data = res.data;
              this.imageUrlDispose(data);
              this.form = data
              this.formLoading = true
            })
            break;
          case "openCityAgent": //市级服务商
            agentApi.echoAgent(openCommon.relationId).then((res) => {
              let data = res.data;
              this.imageUrlDispose(data);
              this.form = data
              this.formLoading = true
            })
            break;
          case "openAreaAgent": //区县服务商
            areaAgentListApi.echoAgent(openCommon.relationId).then(res => {
              let data = res.data;
              this.imageUrlDispose(data);
              this.form = data
              this.formLoading = true
            })
            break;
          case "openCompany": //分公司开通
            companyApi.echoCompany(openCommon.relationId).then((res) => {
              console.log(res)
              let data = res.data;
              this.imageUrlDispose(data);
              this.form = data
              this.formLoading = true
            })
            break;
          case "openDeliveryCenter": //交付中心
            deliveryCenterApi.echoDeliveryCenter(openCommon.relationId).then(res => {
              let data = res.data.data;
              this.imageUrlDispose(data);
              this.form = data
              this.formLoading = true
            })
            break;
          case "addOperations": //交付中心
            deliveryCenterApi.echoDeliveryCenter(openCommon.relationId).then(res => {
              let data = res.data.data;
              this.imageUrlDispose(data);
              this.form = data
              this.formLoading = true
            })
            break;
        }
      })
    },
    /**
     * 图片url处理，添加完整路径
     * @param data 待处理对象
     */
    imageUrlDispose(data) {
      //回显付款照片
      if (data.paymentPhoto && data.paymentPhoto.length > 0) {
        for (let i = 0; i < data.paymentPhoto.length; i++) {
          data.paymentPhoto[i] = this.aliUrl + data.paymentPhoto[i]
        }
      } else {
        data.paymentPhoto = []
      }
      // 回显合同照片
      if (data.contractPhoto && data.contractPhoto.length > 0) {
        for (let i = 0; i < data.contractPhoto.length; i++) {
          data.contractPhoto[i] = this.aliUrl + data.contractPhoto[i]
        }
      } else {
        data.contractPhoto = []
      }
      // 回显证件照片
      if (data.idCardPhoto && data.idCardPhoto.length > 0) {
        for (let i = 0; i < data.idCardPhoto.length; i++) {
          data.idCardPhoto[i] = this.aliUrl + data.idCardPhoto[i]
        }
      } else {
        data.idCardPhoto = []
      }
      //门店照片
      if (data.shopPhoto && data.shopPhoto.length > 0) {
        for (let i = 0; i < data.shopPhoto.length; i++) {
          data.shopPhoto[i] = this.aliUrl + data.shopPhoto[i]
        }
      } else {
        data.shopPhoto = []
      }
    },

    getMainContextHeight() {
      return 1000;
    },
    onClose() {
      this.$emit('close');
    },
    getButtonType(type) {
      switch (type) {
        case this.SysFlowTaskOperationType.AGREE:
        case this.SysFlowTaskOperationType.LEVEL_AGREE:
        case this.SysFlowTaskOperationType.TRANSFER:
        case this.SysFlowTaskOperationType.CO_SIGN:
        case this.SysFlowTaskOperationType.MULTI_AGREE:
        case this.SysFlowTaskOperationType.MULTI_SIGN:
        case this.SysFlowTaskOperationType.SET_ASSIGNEE:
          return 'primary';
        case this.SysFlowTaskOperationType.SAVE:
          return 'success';
        case this.SysFlowTaskOperationType.REFUSE:
        case this.SysFlowTaskOperationType.PARALLEL_REFUSE:
        case this.SysFlowTaskOperationType.MULTI_REFUSE:
          return 'default';
        case this.SysFlowTaskOperationType.REJECT:
        case this.SysFlowTaskOperationType.REVOKE:
          return 'danger';
        default:
          return 'default';
      }
    },
    getOperationTagType(type) {
      switch (type) {
        case this.SysFlowTaskOperationType.AGREE:
        case this.SysFlowTaskOperationType.MULTI_AGREE:
        case this.SysFlowTaskOperationType.SET_ASSIGNEE:
          return 'success';
        case this.SysFlowTaskOperationType.REFUSE:
        case this.SysFlowTaskOperationType.PARALLEL_REFUSE:
        case this.SysFlowTaskOperationType.MULTI_REFUSE:
          return 'warning';
        case this.SysFlowTaskOperationType.STOP:
        case this.SysFlowTaskOperationType.REJECT:
        case this.SysFlowTaskOperationType.REVOKE:
          return 'danger';
        default:
          return 'primary';
      }
    },
    onStartFlow(operation) {
      this.$emit('start', operation);
    },
    handlerOperation(operation) {
      if (this.processInstanceId == null) {
        this.onStartFlow(operation);
      } else {
        this.$emit('submit', operation);
      }
    },
    getTaskHighlightData() {
      if (this.processInstanceId == null || this.processInstanceId === '') {
        return;
      }
      let params = {
        processInstanceId: this.processInstanceId
      }
      flowOperationApi.viewHighlightFlowData(params).then(res => {
        // 已完成节点
        this.finishedInfo = res.data;
      }).catch(e => {
      });
    },
    getTaskProcessXml() {
      if (this.processDefinitionId == null || this.processDefinitionId === '') {
        return;
      }
      let params = {
        processDefinitionId: this.processDefinitionId
      }
      flowOperationApi.viewProcessBpmn(params).then(res => {
        // 当前流程实例xml
        this.processXml = res.data;
      }).catch(e => {
      });
    },
    loadProcessCommentList() {
      this.flowTaskCommentList = [];
      if (this.processInstanceId == null || this.processInstanceId === '') {
        return;
      }
      flowOperationApi.listFlowTaskComment({
        processInstanceId: this.processInstanceId
      }).then(res => {
        this.flowTaskCommentList = res.data;
      }).catch(e => {
      });
    }
  },
  computed: {
    isReadOnly() {
      return typeof this.readOnly === 'string' ? this.readOnly === 'true' : this.readOnly;
    },
    flowOperationList() {
      let that=this;
      if (Array.isArray(this.operationList)) {

        return this.operationList.map(item => {
          item.processDefinitionKey=that.processDefinitionKey;
          if (item.type === this.SysFlowTaskOperationType.MULTI_SIGN && item.multiSignAssignee != null) {
            let multiSignAssignee = {
              ...item.multiSignAssignee
            }
            multiSignAssignee.assigneeList = item.multiSignAssignee.assigneeList ? multiSignAssignee.assigneeList.split(',') : undefined;
            return {
              ...item,
              multiSignAssignee
            }
          } else {
            return {
              ...item
            }
          }
        });
      } else {
        return [];
      }
    },
    // ...mapGetters(['getMainContextHeight'])
  },
  mounted() {
    this.initData();
  }
}
</script>

<style scoped>
.task-title {
  display: flex;
  justify-content: space-between;
  padding-bottom: 5px;
  margin-bottom: 10px;
  border-bottom: 3px solid #409EFF;
}

.task-title .text {
  height: 28px;
  line-height: 28px;
  font-weight: 600;
  font-size: 16px;
  color: #383838;
}

.task-title .el-tag {
  margin-left: 10px;
}
</style>
