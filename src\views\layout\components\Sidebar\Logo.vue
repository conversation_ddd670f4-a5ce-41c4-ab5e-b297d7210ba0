<template>
  <div class="sidebar-logo-container" :class="{ collapse: collapse }">
    <transition name="sidebarLogoFade">
      <router-link v-if="collapse" key="collapse" class="sidebar-logo-link" to="/">
        <el-image :src="(JlbInfo.logoEnable && JlbInfo.avatar) || avatar" class="sidebar-logo" fit="contain" />

        <h1 class="sidebar-title">{{ JlbInfo.customName }}鼎校甄选</h1>
      </router-link>
      <router-link v-else key="expand" class="sidebar-logo-link" to="/">
        <el-image :src="(JlbInfo.logoEnable && JlbInfo.avatar) || avatar" class="sidebar-logo" fit="contain" />
        <el-tooltip v-if="JlbInfo.customName.length > 3" class="item" effect="dark"
          :content="JlbInfo.customName + '鼎校甄选'" placement="bottom">
          <h1 class="sidebar-title">{{ JlbInfo.customName }}鼎校甄选</h1>
        </el-tooltip>
        <h1 v-else class="sidebar-title">{{ JlbInfo.customName }}鼎校甄选</h1>
      </router-link>
    </transition>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
export default {
  name: "SidebarLogo",
  props: {
    collapse: {
      type: Boolean,
      required: true,
    },
  },
  data() {
    return {};
  },
  computed: {
    ...mapGetters(["JlbInfo", "avatar"]),
  },
};
</script>

<style lang="scss" scoped>
.sidebarLogoFade-enter-active {
  transition: opacity 1.5s;
}

.sidebarLogoFade-enter,
.sidebarLogoFade-leave-to {
  opacity: 0;
}
.item {
  margin: 4px;
}
.sidebar-logo-container {
  position: relative;
  width: 100%;
  height: 60px;
  line-height: 60px;
  background: #304156;
  padding-left: 20px;
  overflow: hidden;

  & .sidebar-logo-link {
    height: 100%;
    width: 100%;

    & .sidebar-logo {
      width: 34px;
      height: 34px;
      border-radius: 17px;
      vertical-align: middle;
      margin-right: 10px;
      background-color: #fff !important;
    }

    & .sidebar-title {
      display: inline-block;
      margin: 0;
      color: #e5e5e5;
      font-weight: 400;
      line-height: 24px;
      font-size: 15px;
      font-family: SourceHanSansSC, SourceHanSansSC;
      vertical-align: middle;
      max-width: 118px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  &.collapse {
    padding: 0 !important;
    text-align: center;
    .sidebar-logo {
      margin-right: 0px;
    }
  }
}
</style>
