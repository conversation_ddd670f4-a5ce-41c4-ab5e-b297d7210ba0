/**
 * 语法包消耗课时
 */
import request from '@/utils/request'

export default {
  // 分页查询
  getList(pageNum, pageSize, data) {
    return request({
      url: '/znyy/grammmar/consume/hours/list/' + pageNum + '/' + pageSize,
      method: 'GET',
      params: data
    })
  },
  addGrammarConsumeHours(data) {
    return request({
      url: '/znyy/grammmar/consume/hours/add',
      method: 'POST',
       data
    })
  },
  //编辑回显
  editEcho(id) {
    return request({
      url: '/znyy/grammmar/consume/hours/detail/'+id,
      method: 'GET'
    })
  },
//修改提交后台
  changeTheData(data){
    return request(
      {
        url:'/znyy/grammmar/consume/hours/update',
        method: 'PUT',
        data
      }
    )
  },
  // 删除
  deleteData(id) {
    return request({
      url: '/znyy/grammmar/consume/hours/delete/' +id,
      method: 'DELETE'
    })
  },
openGrammar(data){
return request(
  {
    url:'/dyf/web/grammar/open',
    method: 'POST',
    data
  }
)
},
  // 分页查询
  getSelect() {
    return request({
      url: '/znyy/grammmar/consume/hours/select',
      method: 'GET'
    })
  },
}
