/**
 * 学员管理相关接口
 */
import request from "@/utils/request";

export default {
  // 分页查询待付款列表
  waitPayCourseList(params) {
    return request({
      url: "/znyy/order/waitPayCourseList",
      method: "GET",
      params,
    });
  },

  //修改学时单价
  modifyWaitPayCourse0rder(data) {
    return request({
      url: "/znyy/order/modifyWaitPayCourseOrder",
      method: "POST",
      params: data,
    });
  },
};
