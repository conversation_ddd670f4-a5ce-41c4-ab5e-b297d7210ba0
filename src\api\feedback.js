/**
 * 意见反馈接口
 */
import request from '@/utils/request'

export default {
  //分页查询
  feedbackList(pageNum, pageSize, data) {
    return request({
      url: '/cousys/web/feedback/list/' + pageNum + '/' + pageSize,
      method: 'GET',
      params: data
    })
  },
  // 详情
  feedbackDetail(id) {
    return request({
      url: '/cousys/web/feedback/detail/' + id,
      method: 'GET'
    })
  },
  //删除
  feedbackDel(id){
    return request({
      url: '/cousys/web/feedback/delete/' + id,
      method: 'GET'
    })
  },
}

