<template>
  <div class="app-container">
    <div class="SearchForm">
      <!-- 搜索 -->
      <el-form :inline="true" ref="form" label-width="120px">
        <el-row>
          <el-col :span="8" :xs="24">
            <el-form-item label="课程包名称:">
              <el-input placeholder="请输入课程包名称" v-model="coursePackage.coursePackageName" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8" :xs="24">
            <el-form-item label="课程包最低价格:">
              <el-input v-model="coursePackage.coursePackageMinPrice" oninput="value=value.replace(/[^\d]/g,'')" />
            </el-form-item>
          </el-col>
          <el-col :span="8" :xs="24">
            <el-form-item label="课程包最高价格:">
              <el-input v-model="coursePackage.coursePackageMaxPrice" oninput="value=value.replace(/[^\d]/g,'')" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8" :xs="24">
            <el-form-item label="最低学时:">
              <el-input v-model="coursePackage.coursePackageMinCoursePrice"
                oninput="value=value.replace(/[^\d]/g,'')" />
            </el-form-item>
          </el-col>
          <el-col :span="8" :xs="24">
            <el-form-item label="最高学时:">
              <el-input v-model="coursePackage.coursePackageMaxCoursePrice"
                oninput="value=value.replace(/[^\d]/g,'')" />
            </el-form-item>
          </el-col>
          <el-col :span="8" :xs="24">
            <el-button type="warning" icon="el-icon-search" @click="fetchCoursePackage()">搜索</el-button>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <div class="SearchForm">
      <!-- 添加 -->
      <div class="btn-add" style="margin-bottom: 10px">
        <el-button size="small" type="primary" icon="el-icon-plus" @click="clickAdd">添加</el-button>
      </div>
      <!--表单-->
      <el-table class="common-table" :data="coursePackageList" stripe border
        :default-sort="{ prop: 'date', order: 'descending' }">
        <el-table-column prop="id" label="课程包编号" width="" sortable></el-table-column>
        <el-table-column prop="name" label="课程包名称" width="" sortable show-overflow-tooltip></el-table-column>
        <el-table-column label="操作" align="center" width="300">
          <template slot-scope="scope">
            <el-button type="success" icon="el-icon-edit-outline" size="mini" @click="editCourse(scope.row)">编辑
            </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="title" label="产品名" width="" sortable show-overflow-tooltip></el-table-column>
        <el-table-column prop="attributeList[0].name" label="课程内容" width="180" sortable
          show-overflow-tooltip></el-table-column>
        <el-table-column prop="coursePrice" label="学时" width="180" sortable show-overflow-tooltip></el-table-column>
        <el-table-column prop="price" label="价格" width="180" sortable show-overflow-tooltip></el-table-column>
      </el-table>

    </div>
    <el-col :span="20">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>

    <el-dialog :title="addOrUpdate ? '添加课程包' : '编辑课程包'" :visible.sync="dialogVisible" width="70%"
      :close-on-click-modal="false" @close="close">

      <el-form :ref="addOrUpdate ? 'addCourseData' : 'updateCourseData'"
        :model="addOrUpdate ? addCourseData : updateCourseData" label-position="left" label-width="120px"
        style="width: 100%;">
        <el-form-item label="课程分类" prop="courseType">
          <el-col :xs="24" :lg="18">
            <el-select style="width: 100%;" v-if="addOrUpdate" v-model="courseTypesValue" filterable value-key="value"
              placeholder="请选择" @change="check(courseTypesValue)">
              <el-option v-for="(item, index) in courseType" :key="index" :label="item.name" :value="item.id" />
            </el-select>
            <el-select style="width: 100%;" v-if="!addOrUpdate" v-model="courseTitle" filterable value-key="value"
              placeholder="请选择">
              <el-option v-for="(item, index) in courseType" :key="index" :label="item.name" :value="item.id" />
            </el-select>
          </el-col>
        </el-form-item>

        <el-form-item label="课程类型" prop="bigClassCode">
          <el-col :xs="24" :lg="18">
            <el-select style="width: 100%;" v-if="addOrUpdate" v-model="bigClassCode" filterable value-key="value"
              placeholder="请选择">
              <el-option v-for="(item, index) in courseCategories" :key="index" :label="item.productName"
                :value="item.id" />
            </el-select>
            <el-select style="width: 100%;" v-if="!addOrUpdate" v-model="updateCourseData.productId" filterable
              value-key="value" placeholder="请选择">
              <el-option v-for="(item, index) in courseCategories" :key="index" :label="item.productName"
                :value="item.id" />
            </el-select>
          </el-col>
        </el-form-item>

        <el-form-item label="课程学段" prop="courseStage">
          <el-col :xs="24" :lg="18">
            <el-select style="width: 100%;" v-if="addOrUpdate" v-model="addCourseData.courseStage" filterable multiple
              value-key="value" placeholder="请选择">
              <el-option v-for="(item) in courseStageType" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
            <el-select style="width: 100%;" v-if="!addOrUpdate" v-model="updateCourseChooseList" filterable multiple
              value-key="value" placeholder="请选择">
              <el-option v-for="(item) in courseStageType" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-col>
        </el-form-item>
        <el-form-item
          v-if="(bigClassCode != 1 && bigClassCode) || (updateCourseData.productId != null && updateCourseData.productId != 1)"
          label="选择单词" prop="thesaurus">
          <el-col :xs="24" :span="18">
            <el-button type="primary" @click="chooseWords()">选择词库</el-button>
          </el-col>
        </el-form-item>

        <el-form-item v-if="bigClassCode != 1 && multipleSelection.length && addOrUpdate" label="" prop="thesaurus">
          <el-col :xs="24" :span="18">
            <div class="choose" v-for="(item, index) in multipleSelection" :key="index">
              {{ item.courseName }}
            </div>
          </el-col>
        </el-form-item>

        <el-form-item v-if="updateCourseData.productId != 1 && openAttributeList.length && !addOrUpdate" label=""
          prop="thesaurus">
          <el-col :xs="24" :span="18">
            <div class="choose" v-for="(item, index) in openAttributeList" :key="index">
              {{ item.courseName }}
            </div>
          </el-col>
        </el-form-item>
        <el-form-item label="课程包名称" prop="name">
          <el-col :xs="24" :span="18">
            <el-input v-if="addOrUpdate" v-model="addCourseData.name"></el-input>
            <el-input v-if="!addOrUpdate" v-model="updateCourseData.name"></el-input>
          </el-col>
        </el-form-item>
        <el-form-item label="课程包报价" prop="courseUnitPrice">
          <el-col :xs="24" :span="18">
            <el-input-number style="width: 100%" v-if="addOrUpdate" v-model="courseUnitPrice" :min="0" :precision="2"
              :step="0.1" :controls="false"></el-input-number>
            <el-input-number style="width: 100%" v-if="!addOrUpdate" v-model="updateCourseData.price" :min="0"
              :precision="2" :step="0.1" :controls="false"></el-input-number>
          </el-col>
          <el-col :xs="24" :span="6">元</el-col>
        </el-form-item>
        <el-form-item label="学时" prop="courseMemberDiscount">
          <el-col :span="18">
            <el-input v-if="addOrUpdate" v-model="addCourseData.courseMemberDiscount"
              oninput="value=value.replace(/[^\d]/g,'')" />
            <el-input v-if="!addOrUpdate" v-model="updateCourseData.coursePrice"
              oninput="value=value.replace(/[^\d]/g,'')" />
          </el-col>
          <el-col :xs="24" :span="6">节</el-col>
        </el-form-item>
        <el-form-item label="课程类型" prop="courseType">
          <template>
            <el-radio disabled v-if="addOrUpdate" v-model="addCourseData.courseType" :label="1">不限制学时消耗</el-radio>
            <el-radio disabled v-if="addOrUpdate" v-model="addCourseData.courseType" :label="0">限制学时消耗</el-radio>
            <el-radio disabled v-if="!addOrUpdate" v-model="updateCourseData.useCourseType"
              :label="1">不限制学时消耗</el-radio>
            <el-radio disabled v-if="!addOrUpdate" v-model="updateCourseData.useCourseType" :label="0">限制学时消耗</el-radio>
          </template>
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button v-if="addOrUpdate" size="mini" type="primary" @click="addActiveFun(true)">新增</el-button>
        <el-button v-if="!addOrUpdate" size="mini" type="primary" @click="addActiveFun(false)">修改
        </el-button>
        <el-button size="mini" @click="close">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import coursePackageList from '@/api/coursePackageList'
import { pageParamNames } from '@/utils/constants'
import enTypes from '@/api/bstatus'

export default {
  name: 'coursePackageList',
  data() {
    return {
      courseId: [],
      courseCategories: [],//课程类型
      thesaurus: [],//课程单词词库
      courseName: '',//课程名称
      courseUnitPrice: 0,//课程价格
      addCourseData: {
        productId: -1,
        courseStage: '',
        courseType: -1,
        sellingPrice: 0
      }, // 新增课程包
      updateCourseData: {}, // 修改数据
      dialogVisible: false, // 修改弹窗是否展示
      addOrUpdate: true, // 是新增还是修改
      courseType: [],//课程类别 “鼎英语”
      courseTypesValue: '',
      bigClassCode: '',
      courseStageType: [], //课程学段类型
      coursePackage: {
        //课程包名
        coursePackageName: '',
        coursePackageMinPrice: null,
        coursePackageMaxPrice: null,
        coursePackageMinCoursePrice: null,
        coursePackageMaxCoursePrice: null
      },
      coursePackageList: [],
      tableData: [], //表格数据
      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      //词库
      wordTablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      wordDataQuery: {},
      multipleSelection: [],
      courseTitle: '1',
      openAttributeList: [],
      updateCourseChooseList: [],
    }
  },
  //创建时调用的方法
  created() {
    this.fetchCoursePackage()
    this.courseCategory()
    this.updateCourseData.useCourseType = 1
    this.addCourseData.courseType = 1
  },

  activated() {
    if (this.$route.params.choose) {
      this.dialogVisible = true;
      if (this.$route.params.choose == 'addWords') {
        this.multipleSelection = JSON.parse(window.localStorage.getItem('multipleSelection'));
      } else {
        this.openAttributeList = JSON.parse(window.localStorage.getItem('multipleSelection'));
      }
      window.localStorage.setItem('multipleSelection', '[]')
    }
  },
  //方法区
  methods: {
    addActiveFun(flag) {
      let data = {}
      if (flag) {
        data.title = this.addCourseData.name
        data.name = this.addCourseData.name
        data.courseMemberDiscount = this.addCourseData.courseMemberDiscount * 100
        data.courseId = this.multipleSelection.map(item => item.courseId.toString());
        data.productId = this.bigClassCode
        data.courseName = this.courseCategories[this.bigClassCode - 1].name
        data.phase = this.addCourseData.courseStage
        data.useCourseType = this.addCourseData.courseType
        data.sellingPrice = this.courseUnitPrice * 100
      } else {
        data.id = this.updateCourseData.id
        data.title = this.updateCourseData.name
        data.name = this.updateCourseData.name
        data.courseMemberDiscount = this.updateCourseData.coursePrice * 100
        data.courseId = this.openAttributeList.map(item => item.courseId);
        data.productId = this.updateCourseData.productId
        data.courseName = this.courseCategories[this.updateCourseData.productId - 1].name
        data.phase = this.updateCourseChooseList;
        data.useCourseType = this.updateCourseData.useCourseType
        data.sellingPrice = this.updateCourseData.price * 100
      }
      coursePackageList.saveCourse(data).then(res => {
        if (res.success) {
          this.dialogVisible = false;
          this.fetchCoursePackage()
          this.courseCategory()
          this.$message.success("更新成功")
        }
      })
    },

    // 编辑详情
    editCourse(code) {
      coursePackageList.getCourseDetail(code.id).then(res => {
        if (res.success) {
          this.updateCourseData = res.data;
          // this.updateCourseData.attributeList.forEach((item) => {
          //   this.updateCourseChooseList.push(item.value);
          // });
          this.openAttributeList = this.updateCourseData.openAttributeList
          this.updateCourseData.coursePrice = this.updateCourseData.coursePrice / 100
          this.updateCourseData.price = this.updateCourseData.price / 100
          console.log(this.openAttributeList)
          this.courseTitle = '1';
          let index = this.updateCourseData.attributeList[0].value.indexOf('#');
          this.updateCourseData.attributeList[0].value = this.updateCourseData.attributeList[0].value.slice(0, index);
          var str = this.updateCourseData.attributeList[0].value.slice(0, index);
          this.updateCourseChooseList = str.replace("[", "").replace("]", "").split(",");
          this.addOrUpdate = false;
          this.dialogVisible = true;
          this.check('1')
        }
      })

    },

    chooseWords() {
      if (this.addCourseData.courseStage == '' && this.addOrUpdate || this.updateCourseData.attributeList && this.updateCourseData.attributeList[0].value == '' && !this.addOrUpdate) {
        return this.$message("请选择学段");
      }
      this.dialogVisible = false;
      this.$router.push({
        path: "/student/areasOpenCourse",
        query: {
          // level:this.addOrUpdate?this.addCourseData.courseStage:this.updateCourseData.attributeList[0].value,
          fromCourse: this.addOrUpdate ? 'addWords' : 'editWords'
        },
      });
    },

    //获取学段下拉框
    getStady() {
      var enType = 'CourseStage'
      enTypes.getEnumerationAggregation(enType).then(res => {
        this.courseStageType = res.data
      })
    },

    //获取课程类型
    courseCategory() {
      coursePackageList.byProductCategory().then(res => {
        this.courseType = res.data
      })
      this.getStady()
    },
    //
    check(id) {
      coursePackageList.byCourseCategory(id).then(res => {
        this.courseCategories = res.data
      })
    },
    // 关闭弹窗
    close() {
      // this.empty()
      this.dialogVisible = false
      if (this.addOrUpdate) {
        this.multipleSelection = [];
      }
    },
    empty() {
      this.courseTypesValue = ""
      this.bigClassCode = ""
      this.addCourseData.courseStage = ""
      this.addCourseData.name = ""
      this.addCourseData.courseMemberDiscount = 0
      this.courseUnitPrice = ""
    },
    //添加课程包
    clickAdd() {
      this.dialogVisible = true
      this.addOrUpdate = true;
    },
    //表单展示
    fetchCoursePackage() {
      if (this.coursePackage.coursePackageMaxCoursePrice != null && this.coursePackage.coursePackageMaxCoursePrice != "" && this.coursePackage.coursePackageMinCoursePrice > this.coursePackage.coursePackageMaxCoursePrice) {
        return this.$message('最低学时不能大于最高学时，请重新选择')
      }
      if (this.coursePackage.coursePackageMaxPrice != null && this.coursePackage.coursePackageMaxPrice != "" && this.coursePackage.coursePackageMinPrice > this.coursePackage.coursePackageMaxPrice) {
        return this.$message('课程包最低价不能大于课程包最高价，请重新选择')
      }
      coursePackageList.courseListeningList(this.tablePage.currentPage, this.tablePage.size, this.coursePackage).then(res => {
        this.coursePackageList = res.data.data
        for (let i = 0; i < this.coursePackageList.length; i++) {
          this.coursePackageList[i].coursePrice = Number.parseFloat(this.coursePackageList[i].coursePrice) / 100 + '学时'
          this.coursePackageList[i].price = this.coursePackageList[i].price / 100 + '元'

        }

        // 设置后台返回的分页参数
        pageParamNames.forEach(name => this.$set(this.tablePage, name, parseInt(res.data[name])))
      })

    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val
      this.fetchCoursePackage()
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val
      this.fetchCoursePackage()
    }
  },

}

</script>

<style scoped>
.common-table {
  margin-top: 20px;
}

.SearchForm {
  width: 100%;
  margin-bottom: 15px;
  background: #fff;
  border-radius: 6px;
  padding: 20px 30px;
  box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.2);
}

.choose {
  display: inline-block;
  margin-right: 12px;
}
</style>
