<!-- 划拼音规则 -->
<template>
  <div class="container_box">
    <el-button type="primary" @click="onAddDrawSyllable" icon="el-icon-plus" style="margin-bottom: 10px" size="mini">添加</el-button>
    <el-table
      :data="drawSyllableData"
      class="common-table"
      stripe
      border
      v-loading="drawSyllableLoading"
      default-expand-all
      :tree-props="{ children: 'wordInfoVoList', hasChildren: 'hasChildren' }"
    >
      <el-table-column label="单词ID" prop="id" width="240"></el-table-column>
      <el-table-column label="单词" prop="word" width="160"></el-table-column>
      <el-table-column label="单词拆分" prop="split" :formatter="formatter"></el-table-column>
      <el-table-column label="音节" prop="syllable" width="160"></el-table-column>
      <el-table-column label="重音" prop="accent" width="160"></el-table-column>
      <!-- <el-table-column v-if="courseLevel == 2" label="次重音" prop="secondaryAccent" width="160"></el-table-column> -->
      <el-table-column label="次重音" prop="secondaryAccent" width="160"></el-table-column>
      <el-table-column label="弱音" prop="">
        <template slot-scope="{ row }">
          {{ formatWeakTone(row.split) }}
        </template>
      </el-table-column>

      <el-table-column label="重音定长短" prop="split">
        <template slot-scope="{ row }">
          {{ formatlongShort(row.split) }}
        </template>
      </el-table-column>
      <el-table-column label="次重音定长短" prop="secondaryLengthCollect"></el-table-column>

      <el-table-column label="类型" prop="split">
        <template slot-scope="{ row }">
          <span v-if="row.isReadSelect" style="margin-right: 5px">拼读</span>
          <span v-if="row.isWriteSelect" style="margin-right: 5px">拼写</span>
          <span v-if="row.isSelect == 1" style="margin-right: 5px">学后读</span>
          <span v-if="row.isSelect == 2" style="margin-right: 5px">学后写</span>
          <span v-if="row.isSelect == 3">学后读写</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" min-width="110px" prop="id" width="350" align="left">
        <template slot-scope="{ row }">
          <div style="text-align: left">
            <el-button type="primary" size="mini" icon="el-icon-edit" @click="editDrawSyllableItem(row)">编辑</el-button>
            <el-button type="danger" size="mini" icon="el-icon-delete" @click="delSyllableItem(row)">删除</el-button>
            <el-button class="botton_focus" size="mini" icon="el-icon-top" v-if="row.previousId" @click="moveUpItem(row)">上移</el-button>
            <el-button class="botton_focus" size="mini" icon="el-icon-bottom" v-if="row.nextId" @click="moveDownItem(row)">下移</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <AddDictationWord
      :drawSyllableDialogVisible="drawSyllableDialogVisible"
      @dialogClose="drawSyllableDialogClose"
      :editWordId="editWordId"
      :listId="listId"
      :wordCode="wordCode"
      :readWriteobj="readWriteobj"
      @submitSuc="submitSuc"
    ></AddDictationWord>
    <!-- <el-dialog :title="`${drawSyllableTitle}划音节规则`" :visible.sync="drawSyllableDialogVisible"  width="70%" :close-on-click-modal="false" @close="drawSyllableDialogClose">
         <div slot="footer" class="dialog-footer">
            <el-button size="mini" @click="drawSyllableDialogClose">关闭</el-button>
            <el-button size="mini" type="primary" @click="onSubmit">提交</el-button>
         </div>
       </el-dialog> -->
  </div>
</template>
<script>
  import AddDictationWord from '@/views/course/courseDictation/addDictationWord.vue';
  import courseDictationListApi from '@/api/courseDictationList';
  export default {
    components: { AddDictationWord },
    data() {
      return {
        drawSyllableData: [],
        drawSyllableTitle: '添加',
        readWriteobj: {},
        drawSyllableLoading: false,
        drawSyllableDialogVisible: false,
        courseCode: this.$route.query.courseCode,
        listId: '',
        editWordId: '',
        courseLevel: this.$route.query.courseLevel
      };
    },
    created() {
      this.getTableData();
    },
    methods: {
      //获取列表数据
      getTableData() {
        this.drawSyllableLoading = true;
        courseDictationListApi.getRuleListByCourseCode(this.courseCode, 4, 1).then((res) => {
          if (res.code === 20000) {
            this.drawSyllableData = [];
            let rootData = res.data.data;
            rootData.forEach((item) => {
              item.wordInfoVoList.forEach((word) => {
                word.courseId = item.id;
                this.drawSyllableData.push(word);
              });
            });
            this.drawSyllableLoading = false;
            console.log('this.drawSyllableData', this.drawSyllableData);
          }
        });
      },
      //格式弱音
      formatWeakTone(weak) {
        console.log('1111111111111-----------------1111111111111');
        console.log(weak);
        let index = weak.indexOf('&');
        console.log(index);
        let weakText = index > 0 ? weak.substring(index + 1) : '';
        console.log(weakText);
        // console.log('weak',weak,weakText);
        return weakText;
      },
      formatter(val) {
        if (val.split.includes('/')) {
          return val.split.split('/')[0];
        } else {
          return val.split.split('&')[0];
        }
      },
      //格式长短音
      formatlongShort(str) {
        let index1 = str.indexOf('/');
        if (index1 < 0) return '';
        let index2 = str.indexOf('&');
        let longText = index2 > 0 ? str.substring(index1 + 1, index2) : str.substring(index1 + 1);
        // console.log(index1,index2,'longText',longText);
        return longText;
      },
      formatSplit(str) {
        let index = str.indexOf('/');
        let splitText = index > 0 ? str.substring(0, index) : str;
        let index2 = splitText.indexOf('&');
        splitText = index2 > 0 ? str.substring(0, index2) : splitText;
        console.log('splitText', splitText);
        return splitText;
      },
      //添加划拼音规则
      onAddDrawSyllable() {
        this.drawSyllableDialogVisible = true;
      },
      //编辑划音节
      editDrawSyllableItem(row) {
        this.drawSyllableDialogVisible = true;
        this.drawSyllableTitle = '编辑';
        this.listId = row.courseId;
        this.editWordId = row.id;
        this.readWriteobj = row;
        this.wordCode = row.wordCode;
      },
      delSyllableItem(row) {
        // console.log('row',row);
        courseDictationListApi.deleteWordById(row.id, '2').then((res) => {
          if (res.code === 20000) {
            this.$message.success('删除成功');
            this.getTableData();
          }
        });
      },
      //关闭提交
      submitSuc() {
        this.listId = '';
        this.wordCode = '';
        this.getTableData();
        this.drawSyllableDialogVisible = false;
      },
      //关闭弹窗
      drawSyllableDialogClose() {
        this.drawSyllableDialogVisible = false;
      },
      // 上移
      moveUpItem(row) {
        console.log('row-------------shangyi', row);
        courseDictationListApi
          .wordUp({
            currentId: row.id,
            nextId: row.previousId
          })
          .then((res) => {
            if (res.code === 20000) {
              this.$message.success(`${row.word}` + '上移成功');
              this.getTableData();
            } else {
              this.$message.error(`${res.message}`);
            }
          });
      },
      // 下移
      moveDownItem(row) {
        courseDictationListApi
          .wordDown({
            currentId: row.id,
            nextId: row.nextId
          })
          .then((res) => {
            if (res.code === 20000) {
              this.$message.success(`${row.word}` + '下移成功');
              this.getTableData();
            } else {
              this.$message.error(`${res.message}`);
            }
          });
      }
    }
  };
</script>
<style lang="less" scoped>
  .botton_focus:focus {
    color: #606266 !important;
    background-color: transparent !important;
    border-color: #dcdfe6 !important;
  }
  .botton_focus:hover {
    color: #1890ff !important;
    background-color: #e8f4ff !important;
    border-color: #badeff !important;
  }
</style>
