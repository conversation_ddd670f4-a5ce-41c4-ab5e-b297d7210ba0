<template>
  <div class="batch-add-questions">
    <el-dialog title="批量导入考题" :visible.sync="dialogOpen" width="40%" :before-close="handleClose">
      <div class="dialog-content">
        <excel-upload :limit="1" :showTip="false" :fileList="fileList" @handleSuccess="handleSuccess"
          @handleRemove="handleRemove"></excel-upload>
        <!-- 导入说明 -->
        <p class="desc">导入说明</p>
        <p class="desc-list">
          <span class="index">1.</span>
          <el-button type="text" size="small" v-loading="exportLoading" @click="importTemplate">下载模板</el-button>
          <!-- <el-link href="/template/questionTemplate.xlsx" :underline="false" download="批量导入考题模板.xlsx" target="_blank" style="color: #1890ff;">下载模板</el-link> -->
        </p>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" v-loading="importLoading" @click="importQuestion">导入</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import ExcelUpload from '@/components/Upload/ExcelUpload.vue';
import examApi from '@/api/training/exam';
export default {
  name: 'batchAddManagement',
  components: {
    ExcelUpload
  },
  props: {
    dialogOpen: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      fileList: [],
      exportLoading: false,
      tempFile: null,
      importLoading: false
    };
  },
  methods: {
    handleClose() {
      this.$emit('closeBatchDialog');
    },
    handleSuccess(res) {
      this.fileList.push(res);
      this.tempFile = res;
    },
    handleRemove(index) {
      if (index != -1) {
        this.fileList.splice(index, 1);
        this.tempFile = null;
        console.log('后===', this.fileList);
      }
    },
    // 下载模板
    importTemplate() {
      const that = this;
      that.exportLoading = true;
      examApi
        .importTemplate({})
        .then((response) => {
          // 创建一个blob对象，供浏览器使用
          const blob = new Blob([response]);

          console.log(response);
          const url = window.URL.createObjectURL(blob);

          const link = document.createElement('a');
          link.href = url;
          link.setAttribute('download', '批量导入考题模板.xls');
          document.body.appendChild(link);
          link.click();

          // 移除链接并释放内存
          link.parentNode.removeChild(link);
          window.URL.revokeObjectURL(url);
          that.exportLoading = false;
        })
        .catch((err) => {
          console.log('下载文件时出错', err);
          this.$notify.error('下载文件出错，请重新下载');
        });
    },
    // 批量导入
    importQuestion() {
      if (!this.tempFile) {
        this.$message.error(`请先上传配置好的导入文件`);
        return false;
      } else {
        // 导入
        const that = this;
        that.importLoading = true;
        const formData = new FormData();
        formData.append('file', that.tempFile.raw);

        examApi
          .importQuestion(formData)
          .then((res) => {
            that.$message.success(res.data);
            that.fileList = [];
            that.tempFile = null;
            that.importLoading = false;
            that.$emit('updateTable');
          })
          .catch((err) => {
            // that.$message.error('导入失败，请重试！')
            that.importLoading = false;
          });
      }
    }
  }
};
</script>

<style scoped>
::v-deep .el-upload.el-upload--picture,
::v-deep .el-upload-dragger {
  width: 100%;
}

::v-deep .el-dialog__body {
  padding: 30px 50px;
}

.dialog-footer {
  text-align: center;
}
</style>
