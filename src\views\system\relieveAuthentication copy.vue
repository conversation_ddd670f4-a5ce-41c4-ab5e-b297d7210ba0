<template>
  <div class="app-container">
    <el-form :inline="true" style="margin-bottom: 20px; background: #fff">
      <el-form-item label="登录账号" prop="startDate">
        <el-input
          size="small"
          v-model="query.number"
          placeholder="请输入"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item label="认证类型" prop="endDate">
        <el-select v-model="query.status" size="small" placeholder="请选择">
          <el-option
            v-for="status in statusList"
            :key="status.value"
            :label="status.label"
            :value="status.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="审核状态" prop="endDate">
        <el-select v-model="query.status" size="small" placeholder="请选择">
          <el-option
            v-for="status in statusList"
            :key="status.value"
            :label="status.label"
            :value="status.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" size="small" @click="handleSearch"
          >查询</el-button
        >
        <el-button size="small" @click="handleSearch">查询</el-button>
      </el-form-item>
    </el-form>

    <el-table
      class="period-table"
      v-loading="tableLoading"
      :data="tableData"
      style="width: 100%; margin-bottom: 20px"
      row-key="id"
    >
      <el-table-column prop="grandName" label="编号"></el-table-column>
      <el-table-column prop="grandName" label="编号"></el-table-column>
      <el-table-column prop="grandName" label="编号"></el-table-column>
      <el-table-column prop="grandName" label="编号"></el-table-column>
    </el-table>

    <!-- 分页 -->
    <el-col :span="20">
      <el-pagination
        :current-page="tablePage.currentPage"
        :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="tablePage.totalItems"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-col>
  </div>
</template>

<script>
export default {
  data() {
    return {
      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null,
      },
      query: {},
      statusList: [
        {
          label: "全部",
          value: 0,
        },
        {
          label: "企业",
          value: 1,
        },
        {
          label: "个人",
          value: 2,
        },
      ],
      tableData: [],
    };
  },
  created() {},
  methods: {},
};
</script>

<style>
.app-container {
  background: #f7f7f7;
}
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}
</style>
