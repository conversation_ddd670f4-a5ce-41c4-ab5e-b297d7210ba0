<template>
  <div class="app-container">
    <el-form :inline="true" ref="queryForm" class="container-card" label-width="110px">
      <el-form-item label="等级标识：">
        <el-input v-model="dataQuery.levelIdent" placeholder="请输入名称" clearable />
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-re" @click="resetQuery()">重置</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="search()">搜索</el-button>
      </el-form-item>
    </el-form>
    <el-col :span="24" style="margin-bottom: 30px">
      <el-button type="success" icon="el-icon-plus" size="mini" @click="addBtn()">新增</el-button>
    </el-col>
    <el-table class="common-table" v-loading="tableLoading" :data="tableData" style="width: 100%;margin-bottom: 20px;"
      row-key="id" border default-expand-all>
      <el-table-column prop="id" label="ID"></el-table-column>
      <el-table-column prop="levelIdent" label="等级标识" />
      <el-table-column prop="withdrawnBonus" label="操作" width="220">
        <template slot-scope="scope">
          <el-button type="primary" size="mini" @click="editBtn(scope.row.id)">编辑
          </el-button>
          <el-button type="danger" size="mini" @click="handleDelete(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="minLevel" label="等级范围下限" show-overflow-tooltip></el-table-column>
      <el-table-column prop="maxLevel" label="等级范围上限" show-overflow-tooltip></el-table-column>
      <el-table-column prop="exp" label="每级所需经验" show-overflow-tooltip></el-table-column>
      <el-table-column prop="expColor" label="经验颜色" show-overflow-tooltip>
        <template slot-scope="scope">
          <div style="width:100%;height:68px" :style="getStyle(scope.row.expColor)">
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="levelIdentifier" label="等级图片" show-overflow-tooltip>
        <template slot-scope="scope">
          <el-image style="width: 60px; height: 50px" :src="scope.row.levelIdentifier"
            :preview-src-list="[scope.row.levelIdentifier]">
          </el-image>
        </template>
      </el-table-column>
      <el-table-column prop="backgroundUrl" label="等级背景" show-overflow-tooltip>
        <template slot-scope="scope">
          <el-image style="width: 100px; height: 50px" :src="scope.row.backgroundUrl"
            :preview-src-list="[scope.row.backgroundUrl]">
          </el-image>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <el-col :span="24" style="margin-bottom: 20px">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>

    <!-- 修改或者新增弹窗 -->
    <el-dialog title="编辑等级" :visible.sync="open" width="70%" @close="close">
      <el-form ref="form" :model="form" :rules="rules" label-width="120px" style="width: 70%;">
        <el-form-item label="等级标识：" prop="levelIdent">
          <el-input-number v-model="form.levelIdent" :min="0" />
        </el-form-item>
        <el-form-item label="等级范围下限：" prop="minLevel">
          <el-input-number v-model="form.minLevel" :min="0" />
        </el-form-item>
        <el-form-item label="等级范围上限：" prop="maxLevel">
          <el-input-number v-model="form.maxLevel" :min="0" />
        </el-form-item>
        <el-form-item label="每级所需经验：" prop="exp">
          <el-input-number v-model="form.exp" :min="0" />
        </el-form-item>
        <el-form-item label="经验颜色：" prop="expColor">
          <el-input v-model="form.expColor" />
        </el-form-item>
        <el-form-item label="等级图片：" prop="">
          <my-upload @handleSuccess="HandleSuccess" @handleRemove="HandleRemove" :fullUrl="true" :file-list="fileList"
            :showTip="false" />
        </el-form-item>
        <el-form-item label="等级背景图片：" prop="">
          <my-upload @handleSuccess="HandleSuccess2" @handleRemove="HandleRemove2" :fullUrl="true" :file-list="fileList2"
            :showTip="false" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import levelApi from '@/api/xi/level'
import { pageParamNames } from '@/utils/constants'
import OneImageUpload from '@/components/Upload/OneImageUpload'
import MyUpload from '@/components/Upload/MyUpload'


export default {
  name: 'grading',
  components: { OneImageUpload, MyUpload },
  data() {
    return {
      fileList: [],
      fileList2: [],
      dataQuery: {
        name: ''
      },
      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      open: false,
      tableData: [],
      form: {},
      // 表单校验
      rules: {
        levelIdent: [{ required: true, message: '请输入等级标识', trigger: 'blur' }],
        minLevel: [{ required: true, message: '请输入等级范围下限', trigger: 'blur' }],
        maxLevel: [{ required: true, message: '请输入等级范围上限', trigger: 'blur' }],
        exp: [{ required: true, message: '请输入每级所需经验', trigger: 'blur' }],
      }
    }
  },
  created() {
    this.getPageList()
  },
  methods: {
    getStyle(color) {
      return 'background:' + color
    },
    HandleSuccess(url) {
      this.form.levelIdentifier = url
    },
    HandleRemove() {
      this.form.levelIdentifier = ''
    },
    HandleSuccess2(url) {
      this.form.backgroundUrl = url
    },
    HandleRemove2() {
      this.form.backgroundUrl = ''
    },
    search() {
      this.tablePage = {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      }
      this.getPageList()
    },
    handleDelete(id) {
      this.$confirm('确定要删除吗？', '删除段位', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        levelApi.delete(id).then(res => {
          this.$message.success('删除成功！')
          this.getPageList()
        })
      })
    },
    addBtn() {
      this.reset();
      this.open = true;
    },
    editBtn(id) {
      this.reset();
      levelApi.detail(id).then(res => {
        this.form = res.data;
        if (res.data.levelIdentifier) {
          this.fileList.push({ url: res.data.levelIdentifier })
        };
        if (res.data.backgroundUrl) {
          this.fileList2.push({ url: res.data.backgroundUrl })
        }
        this.open = true;
      })
    },
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          levelApi.saveOrUpdate(this.form).then(response => {
            this.$message.success('提交成功！')
            this.open = false
            this.getPageList()
          })
        }
      })
    },
    getPageList() {
      this.tableLoading = true
      this.dataQuery.pageNum = this.tablePage.currentPage
      this.dataQuery.pageSize = this.tablePage.size
      levelApi.list(this.dataQuery).then(res => {
        this.tableData = res.data.data
        this.tableLoading = false
        // 设置后台返回的分页参数
        pageParamNames.forEach(name =>
          this.$set(this.tablePage, name, parseInt(res.data[name]))
        )
      })
    },
    handlePage() {
      (this.tablePage = {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      }),
        this.getPageList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dataQuery = {
        name: '',
        enable: null
      }
      this.getPageList()
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val
      this.getPageList()
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val
      this.getPageList()
    },
    close() {
      this.open = false
      this.reset()
    },
    reset() {
      this.form = {
        name: '',
        exp: undefined,
        orderNum: undefined,
      };
      this.fileList = [];
      this.fileList2 = [];
      if (this.$refs['form']) {
        this.$refs['form'].resetFields();
      }
    }
  },
}
</script>

<style>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.period-table td,
.period-table th {
  text-align: center;
}

@media (max-width: 767px) {
  .el-message-box {
    width: 80% !important;
  }
}

.el-tooltip__popper {
  max-width: 800px;
}
</style>
