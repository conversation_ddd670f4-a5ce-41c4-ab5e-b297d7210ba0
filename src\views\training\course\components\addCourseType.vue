<template>
  <div class="add-video-dialog">
    <el-dialog width="40%" :visible.sync="dialogParam.visible" @closed="closeAddDialog()" :close-on-click-modal="false" @submit.native.prevent>
      <div slot="title" class="dialog-title">
        <span v-if="dialogParam.type === 'add'">新建课程分类</span>
        <span v-if="dialogParam.type === 'edit'">编辑课程分类</span>
      </div>
      <!-- <el-form  :model="labelForm" ref="addForm"  label-width="120px" label-position="right"  :rules="rules"  >
        <el-form-item label="课程类型：" prop="dictLabel">
          <el-input style="width: 100%"  placeholder="请输入课程类型"   maxlength="100" v-model.trim="labelForm.dictLabel" ></el-input>
        </el-form-item>
        <el-form-item label="备注：" >
          <el-input style="width: 100%" type="textarea" maxlength="255" show-word-limit rows="5"  placeholder="请输入备注" v-model.trim="labelForm.remark" ></el-input>
        </el-form-item>
      </el-form> -->
      <!-- 底部按钮栏 -->
      <!-- <div slot="footer" class="dialog-footer">
        <el-button type="primary"  size="small" :loading="loading" @click="submitForm()" >确认</el-button >
        <el-button size="small" @click="closeAddDialog()">取消</el-button>
      </div> -->
      <el-form :model="labelForm" ref="addForm" label-width="120px" label-position="right" :rules="rules">
        <el-form-item label="分类级别：" prop="dictLevel">
          <el-radio-group v-model="labelForm.dictLevel" @change="handleLevelChange" :disabled="dialogParam.type === 'edit'">
            <el-radio label="1">一级分类</el-radio>
            <el-radio label="2">二级分类</el-radio>
            <el-radio label="3">三级分类</el-radio>
          </el-radio-group>
        </el-form-item>
        <template v-if="labelForm.dictLevel == 2 || labelForm.dictLevel == 3">
          <el-form-item label="所属分类：" prop="parentCode">
            <el-select v-model="labelForm.parentCode" placeholder="请选择所属分类" :disabled="dialogParam.type === 'edit'">
              <el-option v-for="item in levelOptions" :key="item.dictLabel" :label="item.dictLabel" :value="item.dictCode"></el-option>
            </el-select>
          </el-form-item>
        </template>
        <el-form-item label="分类名称：" prop="dictLabel">
          <el-input style="width: 100%" placeholder="请输入分类名称" v-model.trim="labelForm.dictLabel" maxlength="30"></el-input>
        </el-form-item>
        <!-- 一级分类显示该项 -->
        <template v-if="labelForm.dictLevel == 1">
          <el-form-item label="关联角色：" prop="roleTagList">
            <el-select v-model="labelForm.roleTagList" placeholder="请关联考核角色" multiple style="width: 100%">
              <el-option v-for="item in linkRoles" :key="item.id" :label="item.rname" :value="item.rval"></el-option>
            </el-select>
          </el-form-item>
        </template>
        <el-form-item label="分类介绍：" prop="categoryDescription">
          <el-input
            style="width: 100%"
            type="textarea"
            maxlength="100"
            show-word-limit
            rows="4"
            placeholder="请输入分类介绍"
            v-model.trim="labelForm.categoryDescription"
          ></el-input>
          <span style="color: red">*分类介绍会展示在课程页面，方便客户快速了解课程内容</span>
        </el-form-item>
        <el-form-item label="备注：" prop="remark">
          <el-input style="width: 100%" type="textarea" maxlength="200" show-word-limit rows="7" placeholder="请输入备注" v-model.trim="labelForm.remark"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="saveLoading" @click="submitForm()">保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
  // dictCreateOrUpdate
  import courseApi from '@/api/training/course';
  export default {
    name: 'addCourseType',
    components: {},
    props: {
      dialogParam: {
        type: Object,
        default() {
          return {};
        }
      }
    },
    data() {
      return {
        labelForm: {
          dictLevel: '1',
          parentCode: '',
          dictLabel: '',
          roleTagList: [],
          categoryDescription: '',
          remark: ''
        },
        saveLoading: false,
        rules: {
          // dictLabel: [
          //   { required: true, message: "请输入课程类型", trigger: "blur" },
          // ],
          dictLevel: [{ required: true, message: '请选择分类级别', trigger: 'change' }],
          parentCode: [{ required: true, message: '请选择所属分类', trigger: 'change' }],
          dictLabel: [{ required: true, message: '请输入分类名称', trigger: 'blur' }],
          roleTagList: [{ required: true, message: '请关联考核角色', trigger: 'change' }]
        },
        levelOptions: [],
        linkRoles: [],
        editForm: {
          dictCode: '',
          dictValue: '',
          dictType: '',
          roleTag: '',
          dictSort: 0
        }
      };
    },
    mounted() {
      this.getDictRoles();
    },
    methods: {
      getDictRoles() {
        courseApi.dictQueryRoles().then((res) => {
          this.linkRoles = res.data;
        });
      },
      getLevelOptions() {
        // 获取一级分类、二级分类
        const params = {
          dictLevel: this.labelForm.dictLevel == 2 ? 1 : this.labelForm.dictLevel == 3 ? 2 : ''
        };
        courseApi.queryCategoryTree(params).then((res) => {
          this.levelOptions = res.data;
        });
      },
      open(info) {
        for (const key in this.labelForm) {
          if (Object.prototype.hasOwnProperty.call(info, key)) {
            const element = info[key];
            this.labelForm[key] = element;
          }
        }
        for (const key in this.editForm) {
          if (Object.prototype.hasOwnProperty.call(info, key)) {
            const element = info[key];
            this.editForm[key] = element;
          }
        }

        if (this.labelForm.dictLevel != 1) {
          this.getLevelOptions();
        }
      },
      handleLevelChange(value) {
        this.labelForm = {
          dictLevel: '1',
          parentCode: '',
          dictLabel: '',
          roleTagList: [],
          categoryDescription: '',
          remark: ''
        };
        this.$refs['addForm'].clearValidate();

        this.labelForm.dictLevel = value;
        if (value != 1) {
          this.getLevelOptions();
        }
      },
      // 提交
      submitForm() {
        const that = this;
        this.$refs['addForm'].validate((valid) => {
          if (valid) {
            that.saveLoading = true;
            let params = that.labelForm;
            if (that.dialogParam.type === 'edit') {
              params = Object.assign(params, that.editForm);
            }
            courseApi.categoryManage(params).then((res) => {
              that.$message.success('操作成功！');
              that.closeAddDialog();
              that.$emit('updateTree');
              that.saveLoading = false;
            });
          }
        });
        // this.labelForm.dictType='paper_course_category'
        // this.$refs.addForm.validate((valid) => {
        //   if (valid) {
        //     courseApi.dictCreateOrUpdate(this.labelForm).then(res => {
        //       this.$emit("closeDialog");
        //     })
        //   } else {
        //     console.log("error submit!!");
        //     return false;
        //   }
        // });
      },
      closeAddDialog() {
        this.$emit('closeDialog');
      }
    }
  };
</script>
<style scoped>
  .content {
    line-height: 30px;
    width: 100%;
    height: 30px;
    background-color: #d7d7d7;
    margin-bottom: 20px;
  }
  .contentTxt {
    margin-left: 10px;
  }
  .dialog-footer {
    text-align: center;
  }
</style>
