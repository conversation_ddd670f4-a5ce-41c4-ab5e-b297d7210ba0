<template>
  <div class="app-container">
    <el-form :model="form" ref="form" label-width="100px" v-loading="formLoading" :rules="rules">
      <el-form-item label="分类：" prop="titleType">
        <el-select v-model="form.titleType" clearable :disabled="form.id!=null">
          <el-option v-for="item in titleType" :key="item.value" :value="item.value" :label="item.label"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="题型：" prop="questionType">
        <el-select v-model="form.questionType" clearable :disabled="form.id!=null">
          <el-option v-for="item in questionType" :key="item.value" :value="item.value" :label="item.label"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="题目：" prop="title" required >
        <div>
          <el-upload :file-list="fileList" ref="upload" class="upload-demo" action="https://jsonplaceholder.typicode.com/posts/" :on-change="beforeUpload"
                     multiple :limit="1" :on-exceed="handleExceed" :on-remove="handleRemoveDetail" :auto-upload="false" name="flie">
            <div class="el-upload__text">
              <el-button size="small" type="primary" style>点击上传</el-button>
            </div>
            <div slot="tip" class="el-upload__tip">只能上传execel文件</div>
          </el-upload>
          <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;" href="https://document.dxznjy.com/manage/1649923386546.xlsx">下载模板</el-link>
        </div>
      </el-form-item>
      <el-form-item label="答案：" prop="items" required>
        <el-form-item  :key="index"  v-for="(item,index) in form.items">
          <div class="question-item-label">
            <el-input v-model="item.label=index+1" style="width:100px;marginRight:5px;" readonly/>
            <el-input v-model="item.value" :disabled="form.questionType==='MARK_REPEAT'||form.questionType==='COMPLEX_FILLING'"/>
            <el-button style="marginLeft:5px" type="danger" size="mini" icon="el-icon-delete" @click="questionItemRemove(index)"></el-button>
          </div>
        </el-form-item>
        <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;" href="#">
          前面为第几行，后面为色块对应数字，多个用空格' '隔开。0全白、1左上黑、2右上黑、3右下黑、4左下黑、5全黑
        </el-link>
      </el-form-item>
      <el-form-item label="说明：" prop="questionExplain" required>
        <el-input v-model="form.questionExplain">
          <i @click="inputClick(form,'questionExplain')" slot="suffix" class="el-icon-edit-outline"
             style="line-height: 36px;font-size: 20px;cursor: pointer;"></i>
        </el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="submitForm">提交</el-button>
        <el-button v-show="form.id===null" @click="resetForm">重置</el-button>
        <el-button type="success" @click="questionItemAdd">添加选项</el-button>
      </el-form-item>
    </el-form>
    <el-dialog  :visible.sync="richEditor.dialogVisible"  append-to-body :close-on-click-modal="false" style="width: 100%;height: 100%"   :show-close="false" center>
      <Ueditor @ready="editorReady"/>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="editorConfirm">确 定</el-button>
        <el-button @click="richEditor.dialogVisible = false">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import Ueditor from '@/components/Ueditor'
import complexApi from '@/api/paper/complex-question'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import { mapGetters, mapState } from 'vuex'
export default {
  components: {
    Ueditor
  },
  data () {
    return {
      fileList:[],
      importFrom:{
        file: null,
      },
      randomLength:undefined,
      form: {
        id: null,
        title: '',
        titleType: 'IMG_TEXT',
        questionType: 'DRAG',
        items: [
          { label: '', value: undefined }
        ],
        questionExplain: '',
      },
      formLoading: false,
      rules: {
        title: [
          { required: true, message: '请输入题干', trigger: 'blur' }
        ],
        questionExplain: [
          { required: true, message: '请输入题目说明', trigger: 'blur' }
        ],
        titleType: [
          { required: true, message: '请选择分类', trigger: 'blur' }
        ],
        questionType: [
          { required: true, message: '请选择题型', trigger: 'blur' }
        ],
      },
      richEditor: {
        dialogVisible: false,
        object: null,
        parameterName: '',
        instance: null
      },
    }
  },
  created () {
    let id = this.$route.query.id
    let _this = this
    if (id && parseInt(id) !== 0) {
      _this.formLoading = true
      complexApi.select(id).then(re => {
        _this.form = re.data;
        _this.formLoading = false
      })
    }
  },
  methods: {
    randomStr(){
      if (this.randomLength===undefined||this.randomLength===0){
        this.$message.error("请输入随机字符串的长度！")
        return false;
      }
      complexApi.randomStr(this.randomLength).then(res=>{
        this.form.title=res.data;
      })
    },
    editorReady (instance) {
      this.richEditor.instance = instance
      let currentContent = this.richEditor.object[this.richEditor.parameterName]
      this.richEditor.instance.setContent(currentContent)
      // 光标定位到Ueditor
      this.richEditor.instance.focus(true)
    },
    inputClick (object, parameterName) {
      this.richEditor.object = object
      this.richEditor.parameterName = parameterName
      this.richEditor.dialogVisible = true
    },
    editorConfirm () {
      let content = this.richEditor.instance.getContent()
      this.richEditor.object[this.richEditor.parameterName] = content
      this.richEditor.dialogVisible = false
    },
    questionItemRemove (index) {
      this.form.items.splice(index, 1)
    },
    questionItemAdd () {
      let items = this.form.items
      items.push({ label: '', value: undefined })
    },
    submitForm () {
      let _this = this;
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.formLoading = true
          //上传execel文件
          let file = new FormData();
          file.append("file", this.importFrom.file);
          file.append("map", JSON.stringify(this.form));
          complexApi.edit(file).then(re => {
            if (re.success) {
              _this.$message.success(re.message)
              _this.$router.push({ path: "/paper/complex" });
            } else {
              _this.$message.error(re.message)
              this.formLoading = false
            }
          }).catch(e => {
            this.formLoading = false
          })
        } else {
          return false
        }
      })
    },
    resetForm () {
      let lastId = this.form.id
      this.$refs['form'].resetFields()
      this.form = {
        id: null,
        title: '',
        titleType: 'IMG_TEXT',
        questionType: 'DRAG',
        items: [
          { label: '', value: undefined }
        ],
        questionExplain: '',
      }
      this.form.id = lastId
    },
    //上传模板
    beforeUpload(file) {
      this.importFrom.file = file.raw;
      this.form.title = file.raw.name;
    },
    handleRemoveDetail() {
      this.importFrom.file = "";
      this.form.title = '';
    },
    //限制文件
    handleExceed(files, fileList) {
      this.$message.warning(
        `当前限制选择 1个文件，本次选择了 ${files.length} 个文件，共选择了 ${
          files.length + fileList.length
        } 个文件`
      );
    },
  },
  computed: {
    ...mapGetters('enumItem', ['enumFormat']),
    ...mapState('enumItem', {
      titleType: state => state.complexQuestion.titleType,
      questionType: state => state.complexQuestion.questionType,
    }),
  }
}
</script>

<style lang="less" scoped>
.question-item-label {
  display: flex;
  margin-bottom: 12px;
}
</style>
