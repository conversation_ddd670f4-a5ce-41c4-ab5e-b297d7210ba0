<template>
  <div class="app-container">
    <el-form :model="form" ref="form" label-width="100px" v-loading="formLoading" :rules="rules">
      <el-form-item label="所属类型:" prop="type">
        <el-select v-model="form.type" placeholder="全部" disabled>
          <el-option v-for="(item, index) in trainType" :key="index" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="题型:" prop="questionType">
        <el-select v-model="form.questionType" placeholder="全部" disabled>
          <el-option v-for="(item, index) in trainQuestionType" :key="index" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="选择难度：" prop="difficulty">
        <el-select v-model="form.difficulty" placeholder="全部" clearable @change="handleChange">
          <el-option v-for="(item, index) in difficultyList" :key="index" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item> -->
      <el-form-item label="年级：" prop="grade">
        <el-select v-model="form.grade" placeholder="" clearable>
          <el-option v-for="item in gradeList" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="选择维度：" prop="categoryId">
        <el-select v-model="form.categoryId" placeholder="全部" clearable>
          <el-option v-for="(item, index) in categoryList" :key="index" :label="item.name" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item v-if="form.courseType!=1" label="选择类别：" prop="categoryType">
        <el-select v-model="form.categoryType" :disabled="questionlnCourseFalse&&form.categoryType==1" placeholder="全部" clearable>
          <el-option v-for="(item, index) in typeList" :key="index" :label="item.label" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="题干：" prop="title">
        <el-input placeholder="请输入" v-model="form.title" />
      </el-form-item>
      <div v-for="(item, cusIndex) in form.customInfo" :key="cusIndex">
        <el-form-item label="内容：" required>
          <el-input v-model="item.label" @change="changeLabel" />
        </el-form-item>
        <el-form-item label="答案：" required>
          <el-input v-model="item.value" @change="changeValue" />
        </el-form-item>
      </div>

      <el-form-item label="题数：" prop="score">
        <el-input-number v-model="form.score" :precision="0" :step="1" :min="0" :max="100"></el-input-number>
      </el-form-item>
      <el-form-item label="徽章：" prop="badge">
        <el-input-number v-model="form.badge" :precision="0" :step="1" :min="0" :max="100"></el-input-number>
      </el-form-item>
      <el-form-item label="解析：" prop="analysis">
        <el-input v-model="form.analysis">
          <i @click="inputClick(form, 'analysis')" slot="suffix" class="el-icon-edit-outline" style="line-height: 36px; font-size: 20px; cursor: pointer"></i>
        </el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="submitForm">提交</el-button>
        <el-button v-show="form.id === null" @click="resetForm">重置</el-button>
      </el-form-item>
    </el-form>

    <el-dialog :visible.sync="richEditor.dialogVisible" append-to-body :close-on-click-modal="false" style="width: 100%; height: 100%" :show-close="false" center>
      <Ueditor @ready="editorReady" />
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="editorConfirm">确 定</el-button>
        <el-button @click="richEditor.dialogVisible = false">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import questionApi from '@/api/paper/train/question';
import difficultyApi from '@/api/paper/train/difficulty';
import categoryApi from '@/api/paper/train/category';
import { mapGetters, mapState } from 'vuex';
import MyUpload from '@/components/Upload/MyUpload';
import Ueditor from '@/components/Ueditor';

export default {
  components: {
    MyUpload,
    Ueditor
  },
  data() {
    return {
      richEditor: {
        dialogVisible: false,
        object: null,
        parameterName: '',
        instance: null
      },
      // difficultyInfo: null,
      categoryList: [],
      fileList: [],
      typeList: [
        { label: '正式题', id: 1 },
        { label: '附加题', id: 2 }
      ],
      importFrom: {
        file: null
      },
      form: {
        id: null,
        title: '',
        categoryId: '',
        categoryType: '',
        // difficulty: '',
        // difficultyId: '',
        type: 'VISUAL_',
        questionType: 'VISUAL_READ_SENTENCES',
        customInfo: [{ label: '', value: '' }],
        answer: [],
        isRandom: false,
        analysis: '',
        score: undefined,
        badge: 3,
        courseType: 0,
        grade: ''
      },
      formLoading: false,
      rules: {
        type: [{ required: true, message: '请选择', trigger: 'blur' }],
        questionType: [{ required: true, message: '请选择', trigger: 'blur' }],
        grade: [{ required: true, message: '请选择', trigger: 'blur' }],
        title: [{ required: true, message: '请输入', trigger: 'blur' }],
        difficulty: [{ required: true, message: '请选择', trigger: 'blur' }],
        categoryId: [{ required: true, message: '请选择', trigger: 'blur' }],
        categoryType: [{ required: true, message: '请选择', trigger: 'blur' }],
        score: [{ required: true, message: '请输入题数', trigger: 'blur' }],
        badge: [{ required: true, message: '请输入徽章', trigger: 'blur' }],
        analysis: [{ required: true, message: '请输入解析', trigger: 'blur' }]
      },
      currentAnswerItem: null,
      gradeList: [
        { label: '1-3年级', value: 1 },
        { label: '4-6年级', value: 2 },
        { label: '初高中', value: 3 }
      ],
      questionlnCourseFalse: false,
    };
  },
  created() {
    this.getCategoryList();
    let id = this.$route.query.id;
    this.form.courseType = this.$route.query.courseType ? 1 : 0;
    if (id && parseInt(id) !== 0) {
      this.formLoading = true;
      questionApi.detail(id).then((re) => {
        this.form = re.data;
        this.form.grade = this.form.grade == 0 ? '' : this.form.grade;
        // this.handleChange();
        this.formLoading = false;
      });
      questionApi.checkQuestionlnCourse(id).then((res) => {
        this.questionlnCourseFalse = res.data
      });
    }
  },
  methods: {
    changeLabel(e) {
      // console.log(e, typeof e);
      console.log(e);

      // let str = e.replace(/,/g, '').replace(/ /g, '');
      let str = e.replace(/[^\w\u4e00-\u9fa5\s]/g, '').replace(/ /g, ''); //替换所有符号和空格为空
      if (str.length >= 12) {
        this.$message.warning('最多输入为12个字符');
      }
      let result = str.slice(0, 12).split('').join(',');
      // let result = e.replace(/,/g, '').replace(/ /g, '').split('').join(',');
      this.form.customInfo.forEach((item, index) => {
        item.label = result;
      });
    },
    changeValue(e) {
      // console.log(e, typeof e);
      console.log(e);

      // let str = e.replace(/,/g, '').replace(/ /g, '');
      let str = e.replace(/[^\w\u4e00-\u9fa5\s]/g, '').replace(/ /g, ''); //替换所有符号和空格为空
      if (str.length >= 12) {
        this.$message.warning('最多输入为12个字符');
      }
      let result = str.slice(0, 12).split('').join(',');
      // let result = e.replace(/,/g, '').replace(/ /g, '').split('').join(',');
      this.form.customInfo.forEach((item, index) => {
        item.value = result;
      });
    },
    editorReady(instance) {
      this.richEditor.instance = instance;
      let currentContent = this.richEditor.object[this.richEditor.parameterName];
      this.richEditor.instance.setContent(currentContent);
      // 光标定位到Ueditor
      this.richEditor.instance.focus(true);
    },
    inputClick(object, parameterName) {
      this.richEditor.object = object;
      this.richEditor.parameterName = parameterName;
      this.richEditor.dialogVisible = true;
    },
    editorConfirm() {
      let content = this.richEditor.instance.getContent();
      this.richEditor.object[this.richEditor.parameterName] = content;
      this.richEditor.dialogVisible = false;
    },

    getCategoryList() {
      categoryApi.list().then((res) => {
        this.categoryList = res.data.data;
      });
    },
    /*      handleChange() {
      if (!this.form.difficulty) {
        this.difficultyInfo = null;
        return;
      }
      let query = {};
      query.type = this.form.type;
      query.questionType = this.form.questionType;
      query.difficulty = this.form.difficulty;
      difficultyApi
        .isSetting(query)
        .then((res) => {
          this.difficultyInfo = res.data;
        })
        .catch((e) => {
          this.difficultyInfo = null;
          this.fileList = [];
        });
    }, */
    submitForm() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          if (!this.form.customInfo[0].label) {
            return this.$message.warning('请填写题目内容！');
          }
          if (!this.form.customInfo[0].value) {
            return this.$message.warning('请填写题目答案！');
          }
          this.formLoading = true;
          questionApi
            .saveOrUpdate(this.form)
            .then((re) => {
              if (re.success) {
                this.$message.success(re.message);
                this.formLoading = false;
                if (this.form.courseType == 1) {
                  this.$router.push('/train/trainAfterCourse');
                } else {
                  this.$router.push('/train/visual');
                }
              } else {
                this.$message.error(re.message);
                this.formLoading = false;
              }
            })
            .catch((e) => {
              this.formLoading = false;
            });
        } else {
          return false;
        }
      });
    },
    resetForm() {
      let lastId = this.form.id;
      let lastCourseType = this.form.courseType;
      this.$refs['form'].resetFields();
      this.form = {
        id: null,
        title: '',
        categoryId: '',
        // difficulty: '',
        // difficultyId: '',
        type: 'VISUAL_',
        questionType: 'VISUAL_READ_SENTENCES',
        customInfo: [{ label: '', value: '' }],
        answer: [],
        isRandom: false,
        analysis: '',
        score: undefined,
        badge: 3,
        courseType: 0,
        grade: ''
      };
      this.form.id = lastId;
      this.form.courseType = lastCourseType;
    }
  },
  computed: {
    ...mapGetters('enumItem', ['enumFormat']),
    ...mapState('enumItem', {
      trainType: (state) => state.train.trainType,
      difficultyList: (state) => state.train.difficultyList,
      trainQuestionType: (state) => state.train.trainQuestionType
    })
  }
};
</script>

<style lang="less" scoped>
.question-item-label {
  display: flex;
  margin-bottom: 12px;
}

.drawing-item {
  overflow: hidden;
  white-space: normal;
  word-break: break-all;
}
</style>
