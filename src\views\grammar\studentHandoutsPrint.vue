<template>
  <div class="app-container">
    <div ref="print" >
      <body>
      <form id="print">
        <p v-html="handouts"></p>
      </form>
      </body>
    </div>
    <div class="buttom clearfix">
      <a style="background:#3dab93" @click="print()">打印</a>
      <a style="background:#f0ad4e; margin-left:20px;" @click="goBack()">返回</a>
    </div>
  </div>
</template>

<script>
import grammarApi from "@/api/grammar";
import ls from '@/api/sessionStorage'
export default {
  data() {
    return {
      grammarId: '',
      handoutsPrintId: '',
      handouts:''
    }
  },
  created() {
    this.getPrintClose()
  },
  methods: {
    getPrintClose(){
      grammarApi.getPrintClose( ls.getItem('grammarId')).then(res => {
        this.handouts=res.data
      })
    },
    print(){
      grammarApi.editEnable(ls.getItem('handoutsPrintId')).then(res =>{
      })
      this.$print(this.$refs.print)
    },
    //返回按钮
    goBack(){
      this.$store.dispatch('delVisitedViews', this.$route);
      this.$router.go(-1);
      this.$router.push({
        path: "/grammar/handoutsPrintList"
      });

    }
  },
};
</script>

<style scoped>
.app-container{
  background-color: #f3f3f4;
}

.clearfix {
  zoom: 1;
}
.clearfix:before, .clearfix:after {
  content: "";
  line-height: 0;
  display: table;
}
.clearfix:after {
  clear: both;
}
#print {
  width: 1124px;
  margin: 0 auto;
  position: relative;
  overflow: hidden;
}
.buttom {
  margin: 10px 50px 10px 0;
  color: #fff;
  cursor: pointer;
  text-align: right;
}
.buttom a {
  display: inline-block;
  color: #fff;
  width: 90px;
  height: 35px;
  border-radius: 5px;
  text-align: center;
  line-height: 35px;
}

</style>
