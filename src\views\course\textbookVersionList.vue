<template>
    <div class="app-container">
        <!-- 搜索 -->
        <el-form :inline="true" ref="form" class="SearchForm" style="padding: 40px 30px 20px 30px;">
            <el-row>
                <el-col :span="6" :xs="24">
                    <el-form-item label="教材版本名称:">
                        <el-input v-model="dataQuery.name" @keyup.enter.native="fetchData()" style="width: 200px;"
                            placeholder="请输入" clearable></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="创建时间:">
                        <el-date-picker style="width: 100%;" v-model="RegTime" type="daterange"
                            value-format="yyyy-MM-dd HH:mm:ss" range-separator="至" start-placeholder="开始日期"
                            end-placeholder="结束日期" clearable>
                        </el-date-picker>
                    </el-form-item>
                </el-col>
                <el-col :span="6" :xs="24" style="text-align: right">
                    <el-form-item>
                        <el-button type="primary" icon="el-icon-search" @click="fetchData01()">搜索</el-button>
                        <el-button icon="el-icon-refresh" @click="rest()">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>

        <div class="SearchForm" style="padding: 30px;">
            <!-- 添加 -->
            <div class="btn-add">
                <el-button size="small" type="primary" @click="dialogVisible = true">新增教材</el-button>
            </div>
            <!-- 表格 -->
            <el-table :data="tableData" border v-loading="tableLoading" style="width: 90%">
                <el-table-column prop="name" label="教材版本" align="center"></el-table-column>
                <el-table-column prop="createTime" label="创建时间" align="center" :show-overflow-tooltip="true"></el-table-column>
                <el-table-column prop="id" label="操作" align="center">
                    <template slot-scope="scope">
                        <el-button type="primary" size="mini" icon="el-icon-edit-outline" @click="handleUpdate(scope.row)">编辑</el-button>
                    </template>
                </el-table-column>
                <el-table-column prop="updateTime" label="最近修改时间" align="center" :show-overflow-tooltip="true"></el-table-column>
            </el-table>
        </div>

        <!-- 分页 -->
        <el-col :span="20">
            <el-pagination
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="tablePage.currentPage"
              :page-sizes="[10, 20, 30, 40, 50]"
              :page-size="tablePage.size"
              layout="total, sizes, prev, pager, next, jumper"
              :total="tablePage.totalItems">
            </el-pagination>
        </el-col>

        <el-dialog :title="show?'编辑教材版本':'新增教材版本'" :visible.sync="dialogVisible" width="30%" center :close-on-click-modal="false" :before-close="closeDialog">
            <el-form :rules="rules" ref="form" :model="form" label-width="120px">
                <el-form-item label="教材版本名称：" prop="name">
                    <el-input v-model="form.name" placeholder="请输入" style="width:70%"></el-input>
                </el-form-item>
                <el-form-item label="教材版本编号：" prop="code">
                    <el-input v-model="form.code" placeholder="例如：陕旅版输入:SHANLVBAN" style="width:70%"></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button @click="dialogVisible = false">取 消</el-button>
                    <el-button type="primary" @click="onSubmit">确 定</el-button>
                </el-form-item>
            </el-form>
        </el-dialog>
    </div>
</template>
  
<script>
import courseApi from '@/api/courseList'
import textbookApi from '@/api/textbookVersionList'

import { mapGetters } from 'vuex'
import { pageParamNames } from "@/utils/constants";
import { checkImageSize } from '@/utils'
import axios from 'axios'


export default {
    name: 'courseList',
    data() {
        return {
            tableLoading:false,
            // 分页
            tablePage: {
                currentPage: 1,
                size: 10,
                totalPage: null,
                totalItems: null
            },
            tableData: [], //表格数据
            RegTime: null,
            dialogVisible: false,

            dataQuery: {
                startDate:'',
                endDate:'',
                name:''
            },

            form:{
                code:'',// 教材版本编号
                name: '',// 教材版本名称
            },


            rules: {
                name: [
                    { required: true, message: '请输入教材版本名称', trigger: 'blur' }
                ],
                code: [
                    { required: true, message: '请输入教材编号', trigger: 'blur' }
                ]
            },

            show:false, // 是否为编辑
            id:''

        }
    },
    computed: {
        ...mapGetters([
            'roles'
        ])
    },
    created() {
        this.fetchData();
    },
    methods: {
        //重置
        rest() {
            this.dataQuery.startDate = '';
            this.dataQuery.endDate = '';
            this.dataQuery.name = '';
            this.RegTime = null;
            this.tablePage = {
                currentPage: 1,
                size: 10,
                totalPage: null,
                totalItems: null
            }
            console.log(this.tablePage)
            this.fetchData();
        },

        // 查询+搜索课程列表
        fetchData01() {
            this.tablePage = {
                currentPage: 1,
                size: 10,
                totalPage: null,
                totalItems: null
            }
            console.log(this.tablePage)
            this.fetchData();
        },
        fetchData() {
            const that = this;
            that.tableLoading = true;
            if (that.RegTime != null) {
                that.dataQuery.startDate = that.RegTime[0];
                that.dataQuery.endDate = that.RegTime[1];
            }
            textbookApi.courseList(that.tablePage.currentPage, that.tablePage.size, that.dataQuery).then(res => {
                that.tableData = res.data.data;
                that.tableLoading = false;
                // 设置后台返回的分页参数
                pageParamNames.forEach(name => 
                  that.$set(that.tablePage, name, parseInt(res.data[name]))
                )
            })
        },
        // 新增课程提交
        onSubmit() {
            const that = this
            that.$refs['form'].validate(valid => { // 表单验证
                if (valid) {
                    const loading = this.$loading({
                        lock: true,
                        text: that.show?'编辑教材版本':'新增教材版本',
                        spinner: 'el-icon-loading',
                        background: 'rgba(0, 0, 0, 0.7)'
                    })
                    console.log(that.form)
                    if(!that.show){
                        textbookApi.addTextbook(that.form).then(() => {
                            that.dialogVisible = false;
                            that.form.name='';
                            that.form.code ='';
                            loading.close();
                            that.$nextTick(() => that.fetchData())
                            that.$message.success('新增教材版本成功')
                        }).catch(err => {
                            loading.close()
                        })
                    }else{
                        let data={
                            code:that.form.code,
                            name:that.form.name,
                            id:that.id
                        }
                        textbookApi.updateTextbook(data).then(res => {
                            console.log(res);
                            that.dialogVisible = false;
                            loading.close()
                            that.$nextTick(() => that.fetchData())
                            that.$message.success('编辑教材版本成功')
                        }).catch(err => {
                            console.log(err)
                            loading.close()
                            // return false
                        })
                    }
                } else {
                    console.log('error submit!!')
                    return false
                }
            })
        },
        // 点击编辑按钮
        handleUpdate(row) {
            this.form.name= row.name;
            this.form.code= row.code;
            this.id = row.id;
            this.dialogVisible = true;
            this.show = true;
        },
        // 分页
        handleSizeChange(val) {
            this.tablePage.size = val;
            this.fetchData();
        },
        handleCurrentChange(val) {
            this.tablePage.currentPage = val;
            this.fetchData();
        },

        handleInput(val) {
            this.code = val.replace(/[^a-zA-Z0-9]/g, '')
        },

        closeDialog(){
            this.$refs.form.resetFields();
            this.dialogVisible = false;
            this.form.name='';
            this.form.code='';
            this.show = false;
        },

    }
}
</script>
  
<style>
.lh36 {
    line-height: 36px;
    font-size: 14px;
}

.SearchForm {
    width: 100%;
    margin-bottom: 15px;
    background: #fff;
    border-radius: 6px;
    padding: 5px 0;
    padding-left: 5px;
    box-shadow: 1px 1px 3px rgba(0, 0, 0, .2);
}

.btn-add {
    padding: 5px;
    margin-bottom: 20px;
}

.red {
    color: red;
}

.green {
    color: green;
}

.course-table {
    text-align: center;
}

.course-table td,
.course-table th {
    padding: 5px 0;
    text-align: center;
}

.course-table button {
    padding: 2px;
}

.icon-stop {
    float: left;
    display: inline-block;
    width: 12px;
    height: 12px;
    background: url('../../icons/stop.png') no-repeat top center/contain;
}

.mt22 {
    margin-top: 22px;
}

@media (max-width: 767px) {
    .el-message-box {
        width: 80% !important;
    }
}


/*当upLoadShow为true时，启用如下样式，即上传框的样式，若为false则不启用该样式*/
.upLoadShow .el-upload {
    width: 10rem !important;
    height: 10rem !important;
    line-height: 10rem !important;
}

/*当upLoadHide为true时，启用如下样式，即缩略图的样式，若为false则不启用该样式*/
.upLoadHide .el-upload-list--picture-card .el-upload-list__item {
    width: 10rem !important;
    height: 10rem !important;
    line-height: 10rem !important;
}

/*当upLoadHide为true时，启用如下样式，即上传框的样式，若为false则不启用该样式*/
.upLoadHide .el-upload {
    display: none;
}
</style>
  