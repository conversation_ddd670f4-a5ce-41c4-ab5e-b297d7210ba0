<template>
  <div class="app-container">
    <el-form :model="form" ref="form" label-width="100px" v-loading="formLoading" :rules="rules">
      <el-form-item label="分类：" prop="titleType">
        <el-select v-model="form.titleType" disabled>
          <el-option v-for="item in titleType" :key="item.value" :value="item.value" :label="item.label"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="题型：" prop="questionType">
        <el-select v-model="form.questionType" disabled>
          <el-option v-for="item in questionType" :key="item.value" :value="item.value" :label="item.label"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="题目：" prop="title" required>
        <el-input v-model="form.title">
          <i @click="inputClick(form,'title')" slot="suffix" class="el-icon-edit-outline"
             style="line-height: 36px;font-size: 20px;cursor: pointer;"></i>
        </el-input>
      </el-form-item>
      <el-form-item label="答案：" required>
        <el-form-item :key="index" v-for="(item,index) in form.items">
          <div class="question-item-label">
            <el-input v-model="item.label" style="width:300px;marginRight:5px;"/>
            <el-input-number v-model="item.value" placeholder="出现次数" :min="1" style="marginRight:5px;"/>
            <el-button style="marginLeft:5px" type="danger" size="mini" icon="el-icon-delete"
                       @click="questionItemRemove(index)"
            ></el-button>
          </div>
        </el-form-item>
      </el-form-item>
      <el-form-item label="干扰项：" required>
        <el-form-item :key="index" v-for="(item,index) in form.customItems">
          <div class="question-item-label">
            <el-input v-model="item.label=index+1"  style="width:50px;marginRight:5px;"/>
            <el-input v-model="item.value" placeholder="请输入干扰项" style="width:300px;marginRight:5px;"/>
            <el-button style="marginLeft:5px" type="danger" size="mini" icon="el-icon-delete"
                       @click="customItemRemove(index)"
            ></el-button>
          </div>
        </el-form-item>
      </el-form-item>
      <el-form-item label="说明：" prop="questionExplain" required>
        <el-input v-model="form.questionExplain">
          <i @click="inputClick(form,'questionExplain')" slot="suffix" class="el-icon-edit-outline"
             style="line-height: 36px;font-size: 20px;cursor: pointer;"></i>
        </el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="submitForm">提交</el-button>
        <el-button v-show="form.id===null" @click="resetForm">重置</el-button>
        <el-button type="success" @click="questionItemAdd">添加选项</el-button>
        <el-button type="danger" @click="customItemsAdd">添加干扰项</el-button>
      </el-form-item>
    </el-form>
    <el-dialog :visible.sync="richEditor.dialogVisible" append-to-body :close-on-click-modal="false"
               style="width: 100%;height: 100%" :show-close="false" center
    >
      <Ueditor @ready="editorReady"/>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="editorConfirm">确 定</el-button>
        <el-button @click="richEditor.dialogVisible = false">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import Ueditor from '@/components/Ueditor'
import complexApi from '@/api/paper/complex-question'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import { mapGetters, mapState } from 'vuex'

export default {
  components: {
    Ueditor
  },
  data() {
    return {
      fileList: [],
      importFrom: {
        file: null
      },
      randomLength: undefined,
      form: {
        id: null,
        title: '',
        titleType: 'IMG_TEXT',
        questionType: 'SERIAL_MARK_NUMBER',
        customItems: [
          {
            label: '', value: ''
          }
        ],
        items: [
          { label: '', value: undefined}
        ],
        customInfo:'',
        questionExplain: ''
      },
      formLoading: false,
      rules: {
        title: [
          { required: true, message: '请输入题目', trigger: 'blur' }
        ],
        questionExplain: [
          { required: true, message: '请输入题目说明', trigger: 'blur' }
        ],
        titleType: [
          { required: true, message: '请选择分类', trigger: 'blur' }
        ],
        questionType: [
          { required: true, message: '请选择题型', trigger: 'blur' }
        ]
      },
      richEditor: {
        dialogVisible: false,
        object: null,
        parameterName: '',
        instance: null
      }
    }
  },
  created() {
    let id = this.$route.query.id
    let _this = this
    if (id && parseInt(id) !== 0) {
      _this.formLoading = true
      complexApi.select(id).then(re => {
        _this.form = re.data
        if (this.form.customInfo!==null&&this.form.customInfo!=="") {
          _this.form.customItems = JSON.parse(this.form.customInfo)
        }else {
          _this.form.customItems = [{
            label: '', value: ''
          }]
        }
        _this.formLoading = false
      })
    }
  },
  methods: {
    editorReady(instance) {
      this.richEditor.instance = instance
      let currentContent = this.richEditor.object[this.richEditor.parameterName]
      this.richEditor.instance.setContent(currentContent)
      // 光标定位到Ueditor
      this.richEditor.instance.focus(true)
    },
    inputClick(object, parameterName) {
      this.richEditor.object = object
      this.richEditor.parameterName = parameterName
      this.richEditor.dialogVisible = true
    },
    editorConfirm() {
      let content = this.richEditor.instance.getContent()
      this.richEditor.object[this.richEditor.parameterName] = content
      this.richEditor.dialogVisible = false
    },
    questionItemRemove(index) {
      if (this.form.items.length===1){
        return;
      }
      this.form.items.splice(index, 1)
    },
    customItemRemove(index) {
      if (this.form.customItems.length===1){
        return;
      }
      this.form.customItems.splice(index, 1)
    },
    questionItemAdd() {
      let items = this.form.items
      items.push({
        label: '', value: undefined
      })
    },
    customItemsAdd() {
      let items = this.form.customItems
      items.push({
        label: '', value: ''
      })
    },
    submitForm() {
      let _this = this
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.formLoading = true
          //上传execel文件
          let file = new FormData()
          file.append('file', this.importFrom.file)
          this.form.customInfo = JSON.stringify(this.form.customItems)
          file.append('map', JSON.stringify(this.form))
          complexApi.edit(file).then(re => {
            if (re.success) {
              _this.$message.success(re.message)
              _this.$router.push({ path: '/paper/complex' })
            } else {
              _this.$message.error(re.message)
              this.formLoading = false
            }
          }).catch(e => {
            this.formLoading = false
          })
        } else {
          return false
        }
      })
    },
    resetForm() {
      let lastId = this.form.id
      this.$refs['form'].resetFields()
      this.form = {
        id: null,
        title: '',
        titleType: 'IMG_TEXT',
        questionType: 'SERIAL_MARK_NUMBER',
        customItems: [
          {
            label: '', value: {
              minCount: undefined, maxCount: undefined
            }
          }
        ],
        items: [
          { label: '', value: undefined}
        ],
        customInfo:'',
        questionExplain: ''
      }
      this.form.id = lastId
    },
  },
  computed: {
    ...mapGetters('enumItem', ['enumFormat']),
    ...mapState('enumItem', {
      titleType: state => state.complexQuestion.titleType,
      questionType: state => state.complexQuestion.questionType
    })
  }
}
</script>

<style lang="less" scoped>
.question-item-label {
  display: flex;
  margin-bottom: 12px;
}
</style>
