<!--组合题-->
<template>
  <div class="app-container">
    <el-form :model="form" ref="form" label-width="100px" v-loading="formLoading" :rules="rules">
      <el-form-item label="选择学段：" prop="gradeId" required>
        <el-select v-model="form.gradeId" placeholder="选择学段"  @change="levelChange" clearable>
          <el-option v-for="item in gradeList" :key="item.id" :value="item.id" :label="item.name"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="选择维度：" prop="subjectId" required>
        <el-select v-model="form.subjectId" placeholder="选择维度" @change="getKonwTree">
          <el-option v-for="item in subjectFilter" :key="item.id" :value="item.id" :label="item.name"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="有标题：" required>
        <el-radio-group v-model="form.expandInfo.titleFlag">
          <el-radio :label="false">否</el-radio>
          <el-radio :label="true">是</el-radio>
        </el-radio-group>
        <el-form-item v-show="form.expandInfo.titleFlag">
          <el-input v-model="form.expandInfo.titleInfo" placeholder="输入标题"/>
        </el-form-item>
      </el-form-item>
      <el-form-item label="有图片：" required>
        <el-radio-group v-model="form.expandInfo.imgFlag">
          <el-radio :label="false" >否</el-radio>
          <el-radio :label="true">是</el-radio>
        </el-radio-group>
        <el-form-item v-show="form.expandInfo.imgFlag">
          <my-upload @handleSuccess="handleSuccess" @handleRemove="handleRemove" :fullUrl="true" :file-list="fileList" :showTip="false"/>
          <el-input type="hidden" v-model="form.expandInfo.imgInfo"/>
        </el-form-item>
      </el-form-item>
      <el-form-item label="有音频：" required>
        <el-radio-group v-model="form.expandInfo.audioFlag">
          <el-radio :label="false">否</el-radio>
          <el-radio :label="true">是</el-radio>
        </el-radio-group>
        <el-form-item v-show="form.expandInfo.audioFlag">
          <upload-file @handleSuccess="handleAudioSuccess" @handleRemove="handleAudioRemove" :file-list="audioFileList" />
          <el-input type="hidden" v-model="form.expandInfo.audioInfo"/>
        </el-form-item>
      </el-form-item>
      <el-form-item label="有视频：" required>
        <el-radio-group v-model="form.expandInfo.videoFlag">
          <el-radio :label="false">否</el-radio>
          <el-radio :label="true">是</el-radio>
        </el-radio-group>
        <el-form-item v-show="form.expandInfo.videoFlag">
          <upload-file @handleSuccess="handleVideoSuccess" @handleRemove="handleVideoRemove" :file-list="videoFileList" />
          <el-input type="hidden" v-model="form.expandInfo.videoInfo"/>
        </el-form-item>
      </el-form-item>
      <el-form-item label="题干：" prop="title" required>
        <el-input v-model="form.title">
          <i @click="inputClick(form,'title')" slot="suffix" class="el-icon-edit-outline"
             style="line-height: 36px;font-size: 20px;cursor: pointer;"></i>
        </el-input>
      </el-form-item>
      <el-form-item label="子题" required>
        <el-card class="exampaper-item-box" v-if="questionItems.length!==0">
          <el-form-item :key="questionIndex" :label="'题目'+(questionIndex+1)+'：'"
                        v-for="(questionItem,questionIndex) in questionItems" style="margin-bottom: 15px">
            <el-row>
              <el-col :span="23">
                <QuestionShow :qType="questionItem.questionType" :question="questionItem"/>
              </el-col>
              <el-col :span="1">
                <el-button type="text" size="mini" @click="delQuestion(questionIndex)">删除
                </el-button>
              </el-col>
            </el-row>
          </el-form-item>
        </el-card>
      </el-form-item>
<!--      <el-form-item label="解析：" prop="analysis" required>-->
<!--        <el-input v-model="form.analysis">-->
<!--          <i @click="inputClick(form,'analysis')" slot="suffix" class="el-icon-edit-outline"-->
<!--             style="line-height: 36px;font-size: 20px;cursor: pointer;"></i>-->
<!--        </el-input>-->
<!--      </el-form-item>-->
      <el-form-item label="分数：" prop="score">
        {{form.score}}
      </el-form-item>
<!--      <el-form-item label="难度：" prop="difficulty" required>-->
<!--        <el-rate v-model="form.difficulty" class="question-item-rate"></el-rate>-->
<!--      </el-form-item>-->
<!--      <el-form-item label="知识点：">-->
<!--        <treeselect-->
<!--          v-model="form.knowledgePoints"-->
<!--          :multiple="true"-->
<!--          :value-consists-of="valueConsistsOf"-->
<!--          :customInfo="konwledgeTree"-->
<!--          :normalizer="normalizer"-->
<!--          noOptionsText="无可用知识点"-->
<!--          placeholder="请选择知识点"/>-->
<!--      </el-form-item>-->
      <el-form-item label="计时器：" prop="openTimer" required>
        <el-radio-group v-model="form.openTimer">
          <el-radio :label="false" >关闭</el-radio>
          <el-radio :label="true">开启</el-radio>
        </el-radio-group>
        <el-form-item v-show="form.openTimer" :key="item.label"  v-for="(item,index) in form.openTimerData">
          <div class="question-item-label">
            <el-input v-model="item.min"  onkeyup="value=value.replace(/[^\d]/g,'')" style="width:50px;marginRight:5px;" />分钟-
            <el-input v-model="item.max" style="width:50px;marginRight:5px;" />分钟，完成答题 分值
            <el-select v-model="item.operator" style="width:80px;marginRight:5px;">
              <el-option v-for="item in operatorList" :key="item.value" :value="item.value" :label="item.label"></el-option>
            </el-select>
            <el-input v-model="item.score" style="width:50px;marginRight:5px;" />
          </div>
        </el-form-item>
        <el-button v-show="form.openTimer" icon="el-icon-circle-plus-outline" @click="addOpenTimerData"/>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="submitForm">提交</el-button>
        <el-button @click="resetForm">重置</el-button>
        <el-button type="success" @click="addQuestion()">
          添加子题
        </el-button>
      </el-form-item>
    </el-form>
    <el-dialog :visible.sync="questionPage.showDialog"  width="70%">
      <el-form :model="questionPage.dataQuery" ref="queryForm" :inline="true">
        <el-form-item label="ID：">
          <el-input v-model="questionPage.dataQuery.id"  clearable></el-input>
        </el-form-item>
        <el-form-item label="题型：">
          <el-select v-model="questionPage.dataQuery.questionType" clearable>
            <el-option v-for="item in questionType" :key="item.value" :value="item.value" :label="item.label"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="search">查询</el-button>
        </el-form-item>
      </el-form>
      <el-table v-loading="questionPage.listLoading" :data="questionPage.tableData"
                ref="multipleTable"
                @selection-change="handleSelectionChange" border fit highlight-current-row style="width: 100%">
        <el-table-column type="selection" width="40"></el-table-column>
        <el-table-column type="expand">
          <template slot-scope="props">
            <QuestionShow
              :qType="props.row.questionType"
              :question="props.row"/>
          </template>
        </el-table-column>
        <el-table-column prop="id" label="Id" width="180px"/>
        <el-table-column prop="questionType" label="题型" :formatter="questionTypeFormatter" width="70px"/>
        <el-table-column prop="title" label="题目" show-overflow-tooltip>
          <template slot-scope="scope">
            <div v-html="scope.row.title"></div>
          </template>
        </el-table-column>
        <el-table-column prop="score" label="分数" width="70px"/>
        <el-table-column prop="difficulty" label="难度" width="70px"/>
      </el-table>
      <!-- 分页 -->
      <el-col :span="24" style="margin-bottom: 20px">
        <el-pagination :current-page="questionPage.tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]" layout="total, sizes, prev, pager, next, jumper"
                       :total="questionPage.tablePage.totalItems" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
      </el-col>
      <span slot="footer" class="dialog-footer">
          <el-button @click="questionPage.showDialog = false">取 消</el-button>
          <el-button type="primary" @click="confirmQuestionSelect">确定</el-button>
     </span>
    </el-dialog>

    <el-dialog  :visible.sync="richEditor.dialogVisible"  append-to-body :close-on-click-modal="false" style="width: 100%;height: 100%"   :show-close="false" center>
      <Ueditor @ready="editorReady"/>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="editorConfirm">确 定</el-button>
        <el-button @click="richEditor.dialogVisible = false">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import QuestionShow from '../components/Show'
import Ueditor from '@/components/Ueditor'
import { mapGetters, mapState } from 'vuex'
import questionApi from '@/api/paper/question'
import subjectApi from '@/api/paper/subject'
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import { pageParamNames } from '@/utils/constants'
import MyUpload from "@/components/Upload/MyUpload";
import UploadFile from '@/components/Upload/UploadFile'
export default {
  components: {
    Ueditor,
    QuestionShow,
    Treeselect,
    MyUpload,
    UploadFile
  },
  data() {
    return {
      fileList:[],
      videoFileList:[],
      audioFileList:[],
      score:0,
      questionItems:[],
      subjectList: [],
      gradeList: [],
      valueConsistsOf: "LEAF_PRIORITY",
      normalizer(node) {
        if (node.child==null||node.child.length===0){
          return {
            id: node.id,
            label: node.name,
          }
        } return {
          id: node.id,
          label: node.name,
          children: node.child
        }
      },
      questionPage: {
        multipleSelection: [],
        showDialog: false,
        dataQuery:{
          id: null,
          questionType: null,
          level: null,
          subjectId: null,
        },
        // 分页
        tablePage: {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null
        },
        listLoading: true,
        tableData: [],
        total: 0
      },
      form: {
        id: null,
        questionType: 'COMBINATION',
        isSub: false,
        gradeId: null,
        subjectId: null,
        title: '',
        childQuestionIds:[],
        customInfo: [],
        analysis: '',
        answer: '',
        score: 0,
        mediaType:'NO',
        difficulty: null,
        dxSource:'PAPER',
        status:1,
        knowledgePoints:null,
        openTimer: false,
        openTimerData: [
          { min:'',max:'',operator:'+',score:''}
        ],
        expandInfo: {
          titleFlag: false,
          titleInfo: '',
          imgFlag: false,
          imgInfo: '',
          videoFlag: false,
          videoInfo: '',
          videoName:'',
          audioFlag: false,
          audioInfo: '',
          audioName:'',
        }
      },
      subjectFilter: null,
      formLoading: false,
      rules: {
        gradeId: [{ required: true, message: "请选择学段", trigger: "change" }],
        subjectId: [
          { required: true, message: "请选择维度", trigger: "change" }
        ],
        title: [{ required: true, message: "请输入题干", trigger: "blur" }],
        analysis: [{ required: true, message: "请输入解析", trigger: "blur" }],
        score: [{ required: true, message: "请输入分数", trigger: "blur" }],
        answer: [
          { required: true, message: "请选择正确答案", trigger: "change" }
        ]
      },
      richEditor: {
        dialogVisible: false,
        object: null,
        parameterName: "",
        instance: null
      },
      konwledgeTree: [], // 知识点树数据
    };
  },
  created() {
    this.initGrade();
    this.initSubject();
    let id = this.$route.query.id;
    let _this = this;
    if (id && parseInt(id) !== 0) {
      _this.formLoading = true;
      questionApi.select(id).then(re => {
        _this.form = re.data;
        _this.form.childQuestionIds = [];
        _this.questionItems = re.data.childQuestionVo;
        if (_this.form.knowledgePoints!=null && _this.form.knowledgePoints!=='') {
          this.form.knowledgePoints = _this.form.knowledgePoints.split(',')
        }
        if (_this.form.expandInfo){
          if (this.form.expandInfo.imgFlag){
            this.fileList.push({url:this.form.expandInfo.imgInfo});
          }
          if (this.form.expandInfo.videoFlag){
            this.videoFileList.push({name:this.form.expandInfo.videoName ,url:this.form.expandInfo.videoInfo});
          }
          if (this.form.expandInfo.audioFlag){
            this.audioFileList.push({name:this.form.expandInfo.audioName ,url:this.form.expandInfo.audioInfo});
          }
        }
        this.subjectFilter = this.subjectList.filter(data => data.gradeId === this.form.gradeId)
        _this.formLoading = false;
        this.getKonwTree();
      });
    }
  },
  updated () {
    // 在这里调用toggleSelection选中方法
    this.toggleSelection(this.questionPage.tableData)
  },
  methods: {
    handleAudioSuccess(url,fileName){
      this.form.expandInfo.audioInfo=url;
      this.form.expandInfo.audioName=fileName;
    },
    handleAudioRemove(){
      this.form.expandInfo.audioInfo='';
      this.form.expandInfo.audioName='';
    },
    handleVideoSuccess(url,fileName){
      this.form.expandInfo.videoInfo=url;
      this.form.expandInfo.videoName=fileName;
    },
    handleVideoRemove(){
      this.form.expandInfo.videoInfo='';
      this.form.expandInfo.videoName='';
    },
    handleSuccess(url){
      this.form.expandInfo.imgInfo=url;
    },
    handleRemove(){
      this.form.expandInfo.imgInfo='';
    },
    addOpenTimerData(){
      let items = this.form.openTimerData;
      let p = this.operatorList[items.length].value;
      items.push({ min:'',max:'',operator: p ,score:''})
    },
    initGrade(){
      subjectApi.gradeListAll().then(res=>{
        this.gradeList = res.data;
      })
    },
    delQuestion(questionIndex){
      this.questionItems.splice(questionIndex,1);
      let sum = 0;
      this.questionItems.forEach(a=>{
        sum += a.score
      })
      this.form.score = sum;
    },
    toggleSelection (rows) {
      if (rows){
        rows.forEach(row => {
          this.questionItems.forEach(a=>{
            if (row.id === a.id) {
              this.$refs.multipleTable.toggleRowSelection(row, true) }
          })
        })
      }
    },

    confirmQuestionSelect () {
      let sum = 0;
      this.questionPage.multipleSelection.forEach(q => {
        if (this.questionItems.findIndex(item=> item.id === q.id)===-1){
          questionApi.select(q.id).then(re => {
            this.questionItems.push(re.data)
          })
        }
        sum += q.score
      })
      this.form.score= sum;
      this.questionPage.showDialog = false
    },

    addQuestion () {
      if (!this.form.gradeId){
        return this.$message.error('请选择学段！')
      }
      if (!this.form.subjectId){
        return this.$message.error('请选择维度！')
      }
      this.questionPage.showDialog = true
      this.search()
    },
    search () {
      this.questionPage.dataQuery.subjectId = this.form.subjectId
      this.questionPage.listLoading = true
      this.questionPage.dataQuery.pageNum = this.questionPage.tablePage.currentPage;
      this.questionPage.dataQuery.pageSize = this.questionPage.tablePage.size;
      questionApi.combinationList(this.questionPage.dataQuery).then(res => {
        this.questionPage.tableData = res.data.data;
        this.questionPage.listLoading = false
        // 设置后台返回的分页参数
        pageParamNames.forEach(name => this.$set(this.questionPage.tablePage, name, parseInt(res.data[name])))
      })
    },
    handleSelectionChange (val) {
      this.questionPage.multipleSelection = val
    },
    questionTypeFormatter (row, column, cellValue, index) {
      return this.enumFormat(this.questionType, cellValue)
    },
    // 分页
    handleSizeChange(val) {
      this.questionPage.tablePage.size = val
      this.search()
    },
    handleCurrentChange(val) {
      this.questionPage.tablePage.currentPage = val
      this.search()
    },

    initSubject() {
      subjectApi.pageList().then(res => {
        this.subjectList = res.data.data;
      });
    },

    // 获取知识点树
    getKonwTree() {
      questionApi
        .konwledgeTree(this.form.gradeId, this.form.subjectId)
        .then(re => {
          this.konwledgeTree = re.data;
        });
    },
    levelChange() {
      this.form.subjectId = null;
      this.subjectFilter = this.subjectList.filter(
        data => data.gradeId === this.form.gradeId
      );
    },
    editorReady (instance) {
      this.richEditor.instance = instance
      let currentContent = this.richEditor.object[this.richEditor.parameterName]
      this.richEditor.instance.setContent(currentContent)
      // 光标定位到Ueditor
      this.richEditor.instance.focus(true)
    },
    inputClick (object, parameterName) {
      this.richEditor.object = object
      this.richEditor.parameterName = parameterName
      this.richEditor.dialogVisible = true
    },
    editorConfirm () {
      let content = this.richEditor.instance.getContent()
      this.richEditor.object[this.richEditor.parameterName] = content
      this.richEditor.dialogVisible = false
    },
    // 提交表单
    submitForm() {
      let _this = this;
      // if (_this.form.score == "") {
      //   return _this.$message.error("请添加分数");
      // }
      // if (_this.form.difficulty == 0) {
      //   return _this.$message.error("请添加难度");
      // }
      this.$refs.form.validate(valid => {
        if (valid) {
          this.formLoading = true;
          if (this.form.knowledgePoints){
            this.form.knowledgePoints = this.form.knowledgePoints.join(',')
          }
          this.questionItems.forEach(i=>{
            this.form.childQuestionIds.push({id:i.id})
          })
          questionApi
            .edit(this.form)
            .then(re => {
              if (re.success) {
                _this.$message.success(re.message);
                _this.$router.push({ path: "/paper/index" });
              } else {
                _this.$message.error(re.message);
                this.formLoading = false;
              }
            })
            .catch(e => {
              this.formLoading = false;
            });
        } else {
          return false;
        }
      });
    },
    resetForm() {
      let lastId = this.form.id;
      this.$refs["form"].resetFields();
      this.form = {
        id: null,
        questionType: 'COMBINATION',
        isSub: false,
        gradeId: null,
        subjectId: null,
        title: '',
        childQuestionIds:[],
        customInfo: [],
        analysis: '',
        answer: '',
        score: undefined,
        mediaType:'NO',
        difficulty: null,
        dxSource:'PAPER',
        status:1,
        knowledgePoints:null
      };
      this.form.id = lastId;
      this.questionItems = [];
    },
  },
  computed: {
    ...mapGetters("enumItem", ["enumFormat", "subjectFormat"]),
    ...mapState("enumItem", {
      // gradeList: state => state.question.gradeList,
      questionType: state => state.question.typeList,
      editUrlEnum: state => state.question.editUrlEnum,
      operatorList: state => state.question.operatorList,
    })
  }
};
</script>
