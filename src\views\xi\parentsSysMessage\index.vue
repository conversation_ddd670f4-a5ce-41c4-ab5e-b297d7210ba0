<template>
  <div class="app-container">
    <el-form :inline="true" ref="queryForm" class="container-card" label-width="110px">
      <!-- <el-form-item label="名字：">
          <el-form-item label>
            <el-input v-model="dataQuery.name" placeholder="请输入搜索关键词">
              <i class="el-icon-search el-input__icon" slot="prefix"></i>
            </el-input>
          </el-form-item>
        </el-form-item> -->
      <!-- <el-form-item>
          <el-button icon="el-icon-re" @click="resetQuery()">重置</el-button>
        </el-form-item> -->
      <el-form-item>
        <el-button type="primary" @click="search()">刷新</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="success" icon="el-icon-plus" size="mini" @click="addBtn()">新增</el-button>
      </el-form-item>
    </el-form>

    <el-table class="common-table" v-loading="tableLoading" :data="tableData" style="width: 100%;margin-bottom: 20px;"
      row-key="id" border default-expand-all>
      <el-table-column prop="id" label="ID" />
      <el-table-column prop="title" label="消息标题" />
      <el-table-column prop="content" label="消息内容" />
      <!-- <el-table-column prop="type" label="消息类型">
            <template v-slot="{row}">
                <span v-if="row.type==1">文本</span>
                <span v-if="row.type==2">url</span>
            </template>
        </el-table-column> -->
      <el-table-column prop label="排序" show-overflow-tooltip>
        <i class="el-icon-rank"></i>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <el-col :span="24" style="margin-bottom: 20px">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>

    <!-- 查看弹窗 -->
    <el-dialog :title="banner_titile" :visible.sync="open" width="50%" @close="close">
      <el-form ref="form" :model="form" :rules="rules" label-width="160px">
        <el-form-item label='消息标题' style="width:525px" prop="title">
          <el-input v-model="form.title" placeholder="请输入标题"></el-input>
        </el-form-item>

        <el-form-item label="消息内容" style="width:525px" prop="content">
          <el-input v-model="form.content" placeholder="请输入内容"></el-input>
        </el-form-item>

        <!-- <el-form-item label="消息类型：" prop="type">
          <el-radio v-model="form.type" :label="1">文本</el-radio>
          <el-radio v-model="form.type" :label="2">url</el-radio>
        </el-form-item> -->

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
  
<script>
import { pageParamNames } from "@/utils/constants";
import xiParentsSysMessageApi from "@/api/xi/xiParentsSysMessage";
export default {
  name: "punishconfig",
  data() {
    return {
      tabPosition: "1",
      dataQuery: {
      },
      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      taskList: [],
      open: false,
      tableData: [],
      form: {
        orderNum: ""
      },
      up_options: [],
      banner_titile: "",
      // 表单校验
      rules: {
        title: [{ required: true, message: '请输入标题', trigger: 'blur' }],
        content: [{ required: true, message: '请输入内容', trigger: 'blur' }],
        type: [{ required: true, message: '请选择', trigger: 'blur' }]
      }
    };
  },
  created() {
    this.getPageList();
    xiParentsSysMessageApi.allList().then(resp => {
      this.taskList = resp.data;
    }).catch(err => { });
  },
  methods: {
    // 详情接口
    detailBtn() {
      this.tagFn()
      this.open = true;
      this.banner_titile = "详情";
      xiParentsSysMessageApi.detail(id).then(res => {
        this.form = res.data;
      });
    },
    // 提交
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          xiParentsSysMessageApi.saveOrUpdate(this.form).then(response => {
            this.$message.success("提交成功！");
            this.open = false;
            this.getPageList();
            this.$refs.upload.clearFiles();
          });
        }
      })
    },
    search() {
      this.tablePage = {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      };
      this.getPageList();
    },
    // 单个删除
    singleDelete(id) {
      const that = this;
      this.$confirm("确定操作吗?", "删除状态", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          xiParentsSysMessageApi
            .delete(id)
            .then(res => {
              that.$nextTick(() => that.fetchData());
              that.$message.success("删除成功!");
              this.getPageList();
            })
            .catch(err => { });
        })
        .catch(err => { });
    },
    addBtn() {
      this.reset();
      this.open = true;
      this.banner_titile = "新增";
    },
    // 修改
    editBtn(id) {
      this.open = true;
      this.banner_titile = "修改";
      xiParentsSysMessageApi.detail(id).then(res => {
        this.form = res.data;
      });
    },
    // 列表数据
    getPageList() {
      this.tableLoading = true;
      this.dataQuery.pageNum = this.tablePage.currentPage;
      this.dataQuery.pageSize = this.tablePage.size;
      xiParentsSysMessageApi.list(this.dataQuery).then(res => {
        this.tableData = res.data.data;
        this.tableLoading = false;
        // 设置后台返回的分页参数
        pageParamNames.forEach(name =>
          this.$set(this.tablePage, name, parseInt(res.data[name]))
        );
      });
    },
    handlePage() {
      (this.tablePage = {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      }),
        this.getPageList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dataQuery = {
        type: 1
      };
      this.getPageList();
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val;
      this.getPageList();
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val;
      this.getPageList();
    },
    close() {
      this.open = false;
      this.reset();
    },
    reset() {
      this.form = {
        id: null,
        title: null,
        subTitle: null,
        num: undefined,
        routePath: null,
        sortNum: undefined,
        enable: true
      };
      if (this.$refs["form"]) {
        this.$refs["form"].resetFields();
      }
    }
  }
};
</script>
  
<style scoped>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.period-table td,
.period-table th {
  text-align: center;
}

@media (max-width: 767px) {
  .el-message-box {
    width: 80% !important;
  }
}

.el-tooltip__popper {
  max-width: 800px;
}

.el-upload-list--picture-card {
  display: block;
}

.el-upload {
  border: none;
  width: auto;
  height: 36px;
  line-height: 36px;
}

.el-upload button {
  height: 36px;
}

.el-tooltip__popper {
  max-width: 800px;
}

.el-upload-list {
  position: relative;
}
</style>
  