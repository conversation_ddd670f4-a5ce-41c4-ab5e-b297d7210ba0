<template>
  <div class="app-container">
    <div class="box1">
      <div class="statisticsTitle sRight">
        <div class="sName">急需处理</div>
        <div class="sNum">{{ statistics.exigencyCount }}个</div>
      </div>
      <div class="statisticsTitle sRight">
        <div class="sName">未处理</div>
        <div class="sNum">{{ statistics.untreatedCount }}个</div>
      </div>
      <div class="statisticsTitle">
        <div class="sName">已处理</div>
        <div class="sNum">{{ statistics.processedCount }}个</div>
      </div>
    </div>

    <div class="box2">
      <el-radio-group v-model="queryParams.status" @change="handleQuery">
        <el-radio-button :label="1">处理中</el-radio-button>
        <el-radio-button :label="0">未处理</el-radio-button>
        <el-radio-button :label="2">已完成</el-radio-button>
        <el-radio-button>全部</el-radio-button>
      </el-radio-group>
      <el-input style="width: 20vw; margin-left: 15px" v-model="queryParams.loginName" placeholder="请输入渠道账号"
        suffix-icon="el-icon-search" clearable @keyup.enter.native="handleQuery" />
    </div>

    <el-table @sort-change="sortTable" v-loading="loading" :data="noticeList" style="width: 100%;"
      :default-sort="defaultSort" @selection-change="handleSelectionChange">
      <!--      <el-table-column type="selection" width="55" align="center"/>-->
      <el-table-column label="渠道账号" align="center" min-width="120" prop="loginName">
        <template slot-scope="scope">
          {{ scope.row.loginName.substring(0, scope.row.loginName.length - 1) }}
        </template>
      </el-table-column>
      <el-table-column label="渠道角色" align="center" prop="roleName" />
      <el-table-column label="操作" align="center" min-width="120">
        <template slot-scope="scope">
          <el-button size="mini" type="text" @click="getInfo(scope.row.id)">详情
          </el-button>
          <el-button size="mini" type="text" @click="disposeOpen(scope.row)">处理
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="门店状态" align="center" prop="isEnable">
        <template slot-scope="scope">
          {{ scope.row.isEnable === 1 ? "正常" : "关闭" }}
        </template>
      </el-table-column>
      <el-table-column label="违反类型" align="center" prop="riskType" />
      <el-table-column label="具体行为" align="center" prop="behaviorTypeName" :show-overflow-tooltip="true"
        min-width="220" />
      <el-table-column label="触发时间" align="center" sortable="custom" prop="createTime" min-width="80" />
      <el-table-column label="处理人员" align="center" prop="dispose" width="80" />
      <el-table-column label="处理结果" align="center" prop="result">
        <template slot-scope="scope">
          {{ status(scope.row) }}
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 风控详情对话框 -->
    <el-dialog title="风控详情" center :visible.sync="open" width="680px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="90px" style="margin: 20px 15px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="渠道账号" prop="loginName">
              <el-input readonly v-model="form.loginName" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="角色" prop="roleName">
              <el-input readonly v-model="form.roleName" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="上级账号" prop="parent">
              <el-input readonly v-model="form.parent" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="上级角色" prop="parentRole">
              <div style="display: flex">
                <el-input readonly v-model="form.parentRole" />
                <div @click="openParentRole(form.merchantCode)" class="parentButton">
                  <i class="el-icon-s-operation"></i>
                  更多
                </div>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="负责人" prop="realName">
              <el-input readonly v-model="form.realName" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="账户名" prop="merchantName">
              <el-input readonly v-model="form.merchantName" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="所在地区" prop="address">
              <el-input readonly type="textarea" v-model="form.address" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="违反类型" prop="riskType">
              <el-input readonly v-model="form.riskType" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="触发时间" prop="createTime">
              <el-input readonly v-model="form.createTime" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="行为记录">
              <div class="disposeBox">
                <p v-for="item in form.detailsList" :key="item">{{ item }}</p>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="24" v-if="form.status !== 2">
            <div style="color: red">
              <span>*系统自动关店时间：</span>
              <span>{{ form.deadline }}</span>
            </div>
          </el-col>
          <el-col :span="24" v-if="form.resultImg">
            <el-form-item label-width="100px" label="处理结果图片">
              <el-image class="img" :src="form.resultImg" :preview-src-list="[form.resultImg]"></el-image>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="cancel">确 定</el-button>
        <!--        <el-button @click="cancel">取 消</el-button>-->
      </div>
    </el-dialog>

    <el-dialog title="上级层级" center :visible.sync="openParent" width="500px" append-to-body>
      <el-timeline style="margin: 25px 0 0 30px">
        <el-timeline-item v-for="(item, index) in parentMerList" :key="index" :color="index === 0 ? '#3894ff' : '#7ac756'"
          :timestamp="item.loginName">
          {{ item.merchantName + (!item.realName ? "" : "，负责人：" + item.realName) }}
        </el-timeline-item>
      </el-timeline>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="openParent = false">确 定</el-button>
      </span>
    </el-dialog>

    <el-dialog title="风控处理" center :visible.sync="openDispose" width="880px" append-to-body>
      <riskDispose @riskOpenInfo="getInfo" v-if="openDispose" :risk-id="riskId" :dispose-show="disposeShow" />
      <div slot="footer" class="dialog-footer" v-if="disposeShow">
        <el-button type="danger" @click="notarize(2)">关闭门店</el-button>
        <el-button type="primary" @click="notarize(1)">解除风险</el-button>
      </div>
      <div slot="footer" class="dialog-footer" v-else>
        <el-button type="primary" @click="openDispose = false">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog title="上传处理图片" center :visible.sync="openResultImg" @close="resultClose" width="380px" append-to-body>
      <div class="uploadImg">
        <el-upload v-loading="uploadLoading" ref="clearUpload" action="" list-type="picture-card"
          element-loading-text="图片上传中" :limit="1" :http-request="uploadImage" :on-preview="handlePicture"
          :on-remove="handleRemoveDetailIdCard">
          <i class="el-icon-plus" />
        </el-upload>
        <el-button type="primary" @click="notarize(1)">解除风险</el-button>
      </div>
    </el-dialog>

    <el-dialog :visible.sync="uploadVisible">
      <img width="100%" :src="imageUrl" alt="">
    </el-dialog>
  </div>
</template>

<script>
import riskApi from "@/api/risk/risk";
import pagination from '@/components/Pagination/index'
import riskDispose from '@/views/risk/riskDispose'
import { ossPrClient } from "@/api/alibaba";

export default {
  name: "risk",
  components: {
    pagination,
    riskDispose
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 公告表格数据
      noticeList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        riskRank: 1,
        orderByColumn: 'createTime',
        isAsc: "DESC",
        status: undefined
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        noticeTitle: [
          { required: true, message: "公告标题不能为空", trigger: "blur" }
        ],
        noticeType: [
          { required: true, message: "公告类型不能为空", trigger: "change" }
        ]
      },
      //列表统计数据
      statistics: {
        //急需处理
        exigencyCount: 0,
        //未处理
        untreatedCount: 0,
        //已处理
        processedCount: 0,
      },
      openParent: false,
      //上级渠道列表
      parentMerList: [],
      //处理详情
      openDispose: false,
      //风控id
      riskId: undefined,
      //处理显示
      disposeShow: false,
      //处理结果
      resultInfo: undefined,
      //处理图片上传弹窗
      openResultImg: false,
      //文件上传加载
      uploadLoading: false,
      resultImg: undefined,
      uploadVisible: false,
      imageUrl: {},
      disposeForm: {},
      aliUrl: this.aliUrl,
      defaultSort: {}
    };
  },
  created() {
    this.queryParams.riskRank = this.$route.path.substring(this.$route.path.length - 1)
    this.getList();
    this.getStatistics();
  },
  methods: {
    sortTable(row) {
      this.queryParams.orderByColumn = row.prop;
      this.queryParams.isAsc = row.order === "ascending" ? "ASC" : "DESC";
      this.getList();
    },
    /** 查询风控列表 */
    getList() {
      this.loading = true;
      riskApi.riskPageList(this.queryParams).then(response => {
        this.noticeList = response.data.data;
        this.total = Number(response.data.totalItems);
        this.loading = false;
      });
    },
    //获取列表统计数据
    getStatistics() {
      riskApi.statistics(this.queryParams.riskRank).then(response => {
        this.statistics = response.data
      })
    },
    //查看上级渠道列表
    openParentRole(merchantCode) {
      riskApi.getParentList(merchantCode).then(res => {
        this.parentMerList = res.data
        this.openParent = true
      })
    },
    //风险确认接口
    notarize(result) {
      if (result === 1 && this.disposeForm.riskRank === 3) {
        if (!this.openResultImg) {
          this.openResultImg = true
          return;
        } else {
          if (!this.resultImg) {
            this.$message.error('高危解除风险需要上传处理图片！')
            return;
          }
        }
      }
      this.$confirm('确定操作吗?', result === 1 ? '解除风险' : "关闭门店", {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let data = {
          riskId: this.riskId,
          notarize: result,
          resultImg: this.resultImg
        }
        riskApi.notarize(data).then(res => {
          this.$message.success('操作成功!')
          this.riskId = undefined
          this.openDispose = false
          this.openResultImg = false;
          this.resultImg = undefined
          this.getList();
          this.getStatistics();
        })
      })
    },
    //处理按钮
    disposeOpen(row) {
      this.disposeForm = row
      this.riskId = row.id
      this.openDispose = true
      this.disposeShow = row.status !== 2;
    },
    resultClose() {
      this.$refs.clearUpload.clearFiles()
      this.openResultImg = false;
      this.resultImg = undefined
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        noticeId: undefined,
        noticeTitle: undefined,
        noticeType: undefined,
        noticeContent: undefined,
        status: "0"
      };
      if (this.$refs['form']) {
        this.$refs['form'].resetFields();
      }
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      if (this.queryParams.status === 0 || this.queryParams.status === 1) {
        this.defaultSort = { prop: 'createTime', order: 'ascending' }
      } else {
        this.defaultSort = { prop: 'createTime', order: 'descending' }
      }
      this.sortTable(this.defaultSort)
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.noticeId)
      this.single = selection.length != 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加公告";
    },
    /** 详情按钮操作 */
    getInfo(id) {
      this.reset();
      riskApi.getRiskInfo(id).then(response => {
        let data = response.data;
        if (data.loginName) {
          data.loginName = data.loginName.substring(0, data.loginName.length - 1)
        }
        if (data.parent) {
          data.parent = data.parent.substring(0, data.parent.length - 1)
        }
        if (data.result) {
          data.result = this.status(data)
        }
        if (data.resultImg) {
          data.resultImg = this.aliUrl + data.resultImg
        }
        this.form = data;
        this.open = true;
      });
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.noticeId != undefined) {
            /*            updateNotice(this.form).then(response => {
                          this.$modal.msgSuccess("修改成功");
                          this.open = false;
                          this.getList();
                        });*/
          } else {
            /*            addNotice(this.form).then(response => {
                          this.$modal.msgSuccess("新增成功");
                          this.open = false;
                          this.getList();
                        });*/
          }
        }
      });
    },
    status(row) {
      if (!row.result) {
        return "";
      }
      if (row.result === 1) {
        return "解除风险";
      }
      if (row.result === 2) {
        return "手动关闭";
      }
      if (row.result === 3) {
        return "系统关闭";
      }
    },
    uploadImage({ file }) {
      this.uploadLoading = true
      const fileName = 'manage/' + Date.parse(new Date())
      ossPrClient().put(fileName, file).then(({ res, url, name }) => {
        if (res && res.status === 200) {
          this.resultImg = name;
          this.uploadLoading = false
        }
      }).catch(err => {
        this.$message.error('上传图片失败请检查网络或者刷新页面')
        console.log(`阿里云OSS上传图片失败回调`, err)
        this.uploadLoading = false
      })
    },
    // 上传图片预览
    handlePicture(file) {
      this.imageUrl = file.url
      this.uploadVisible = true
    },
    // 删除上传图片
    handleRemoveDetailIdCard(file, fileList) {
      this.resultImg = undefined
    },
  },
}
</script>
<style lang="scss" scoped>
.box1 {
  display: flex;
  width: 100%;
}

.statisticsTitle {
  width: 100%;
  margin-bottom: 10px;

  .sName {
    color: rgba(146, 146, 146, 100);
    font-size: 13px;
    text-align: center;
    font-family: SourceHanSansSC-regular;
    margin-bottom: 10px;
  }

  .sNum {
    color: rgba(16, 16, 16, 100);
    font-size: 20px;
    text-align: center;
    font-family: SourceHanSansSC-regular;
  }
}

.sRight {
  border-right: 1px solid rgba(217, 217, 217, 100);
}

.box2 {
  display: flex;
  margin: 30px 0;
  justify-content: flex-end;
}

.disposeBox {
  border-radius: 4px;
  border: 1px solid #DCDFE6;
  padding: 0 15px;
}

.parentButton {
  width: 6vw;
  margin-left: 8px;
  color: #007AFF;
  cursor: pointer;

  i {
    font-size: 18px;
  }
}

.uploadImg {
  display: flex;
  flex-direction: column;
  align-items: center;

  button {
    margin-top: 30px;
  }
}

.img {
  height: 100px;

  img {
    object-fit: cover
  }

  ::v-deep .el-icon-circle-close:before {
    color: red !important;
  }
}
</style>
