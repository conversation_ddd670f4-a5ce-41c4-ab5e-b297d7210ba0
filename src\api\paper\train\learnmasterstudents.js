import request from '@/utils/request'

export default {
  list(params) {
    return request({
      url: '/paper/learnMasterStudents/list',
      method: 'get',
      params
    })
  },
  save(data) {
    return request({
      url: '/paper/learnMasterStudents/save',
      method: 'POST',
      data
    })
  },
  delete(id) {
    return request({
      url: '/paper/learnMasterStudents/delete',
      method: 'DELETE',
      params: {
        id: id
      }
    })
  }
}
