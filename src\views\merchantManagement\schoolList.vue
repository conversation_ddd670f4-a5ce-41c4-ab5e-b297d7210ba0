<template>
  <div class="app-container">
    <el-form :inline="true" class="container-card" label-width="170px" label-position="right" ref="dataQuery" :model="dataQuery">
      <el-row type="flex" style="flex-wrap: wrap" :gutter="80">
        <!-- 门店编号： -->
        <el-col :span="8" :xs="24" v-if="hasNoPermissionField('门店编号')">
          <el-form-item label="门店编号：" prop="merchantCode">
            <el-input id="merchantCode" v-model="dataQuery.merchantCode" name="id" placeholder="请输入门店编号" @input="$forceUpdate()" clearable />
          </el-form-item>
        </el-col>
        <!-- 门店名称： -->
        <el-col :span="8" :xs="24" v-if="hasNoPermissionField('门店名称')">
          <el-form-item label="门店名称：" prop="merchantName">
            <el-input id="merchantName" v-model="dataQuery.merchantName" name="id" placeholder="请输入门店名称" @input="$forceUpdate()" clearable />
          </el-form-item>
        </el-col>
        <!-- 登录账号： -->
        <el-col :span="8" :xs="24" v-if="hasNoPermissionField('登录账号')">
          <el-form-item label="登录账号：" prop="name">
            <el-input id="name" v-model="dataQuery.name" name="id" placeholder="请输入登录账号" @input="$forceUpdate()" clearable />
          </el-form-item>
        </el-col>
        <!-- 交付中心名称： -->
        <el-col :span="8" :xs="24" v-if="hasNoPermissionField('主交付中心编号')">
          <el-form-item label="交付中心名称：" prop="schoolBingDevlierName">
            <el-input id="schoolBingDevlierName" v-model.trim="dataQuery.schoolBingDevlierName" @input="$forceUpdate()" name="id" placeholder="请输入交付中心名称" clearable />
          </el-form-item>
        </el-col>
        <!-- 交付中心编号： -->
        <el-col :span="8" :xs="24" v-if="hasNoPermissionField('主交付中心编号')">
          <el-form-item label="交付中心编号：" prop="schoolBingDevlierCode">
            <el-input id="schoolBingDevlierCode" v-model.trim="dataQuery.schoolBingDevlierCode" @input="$forceUpdate()" name="id" placeholder="请输入交付中心编号" clearable />
          </el-form-item>
        </el-col>
        <!-- 交付中心指派状态: -->
        <el-col :span="8" :xs="24" v-if="hasNoPermissionField('主交付中心编号')">
          <el-form-item label="交付中心指派状态:" prop="schoolBing">
            <el-select v-model="dataQuery.schoolBing" placeholder="请选择" @change="$forceUpdate()" clearable>
              <el-option v-for="(item, index) in options" :key="index" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <!-- 状态 -->
        <el-col :span="8" :xs="24" v-if="hasNoPermissionField('账户状态')">
          <el-form-item label="状态：" prop="isEnable">
            <el-select v-model="dataQuery.isEnable" filterable value-key="value" placeholder="请选择" @change="$forceUpdate()" clearable>
              <el-option v-for="(item, index) in [
                  { value: 1, label: '开通' },
                  { value: 0, label: '暂停' },
                  { value: -1, label: '系统关闭' },
                  { value: -2, label: '年审关闭' },
                  { value: -3, label: '终止' },
                  { value: -4, label: '注销' },
                  { value: -5, label: '到期未续费' }
                ]" :key="index" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <!-- 退费状态 -->
        <el-col :span="8" :xs="24" v-if="hasNoPermissionField('退费状态')">
          <el-form-item label="退费状态：" prop="refundStatus">
            <el-select v-model="dataQuery.refundStatus" filterable value-key="value" placeholder="请选择" clearable>
              <el-option v-for="(item, index) in [
                  { value: 0, label: '未申请' },
                  { value: 2, label: '审核中' },
                  { value: 1, label: '已通过' },
                  { value: 3, label: '未通过' }
                ]" :key="index" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <!-- 负责人： -->
        <el-col :span="8" :xs="24" v-if="hasNoPermissionField('负责人')">
          <el-form-item label="负责人：" prop="realName">
            <el-input id="realName" v-model="dataQuery.realName" name="id" placeholder="请输入负责人" @input="$forceUpdate()" clearable />
          </el-form-item>
        </el-col>
        <!-- 推广大使编号： -->
        <el-col :span="8" :xs="24" v-if="hasNoPermissionField('推广大使编号')">
          <el-form-item label="推广大使编号：" prop="refereeCode">
            <el-input id="refereeCode" v-model="dataQuery.refereeCode" name="id" placeholder="请输入推广大使编号" @input="$forceUpdate()" clearable />
          </el-form-item>
        </el-col>

        <!-- <el-row>
        <el-col :span="8" :xs="24">
          <el-form-item label="门店类型:" prop="schoolType">
            <el-select v-model="dataQuery.schoolType" filterable value-key="value" placeholder="请选择" clearable>
              <el-option label="直营门店" :value="1"/>
              <el-option label="加盟门店" :value="2"/>
              <el-option label="超级俱乐部门店" :value="3"/>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row> -->
        <!-- 所在地区： -->
        <el-col :span="8" :xs="24" v-if="hasNoPermissionField('所在地区')">
          <el-form-item label="所在地区：" prop="address">
            <el-input id="address" v-model="dataQuery.address" name="id" placeholder="请输入所在地区" @input="$forceUpdate()" clearable />
          </el-form-item>
        </el-col>
        <!-- 所属俱乐部： -->
        <el-col :span="8" :xs="24" v-if="hasNoPermissionField('所属俱乐部')">
          <el-form-item label="所属俱乐部：" prop="operationsName">
            <el-input id="operationsName" v-model="dataQuery.operationsName" name="id" placeholder="请输入所属俱乐部" @input="$forceUpdate()" clearable />
          </el-form-item>
        </el-col>
        <!-- 完款状态 -->
        <el-col :span="8" :xs="24" v-if="isAdmin">
          <el-form-item label="完款状态：" prop="paymentIsComplete" v-if="checkPermission(['b:merchant:paymentIsComplete:choose'])">
            <el-select v-model="dataQuery.paymentIsComplete" filterable value-key="value" placeholder="请选择完款状态" clearable>
              <el-option label="全部" value=" " />
              <el-option label="已完款" :value="1" />
              <el-option label="等待完款" :value="0" />
            </el-select>
          </el-form-item>
        </el-col>
        <!-- 培训是否缴费： -->
        <el-col :span="8" v-if="hasNoPermissionField('培训是否缴费')">
          <el-form-item label="培训是否缴费：" prop="isPay" v-if="isNeedPay">
            <el-select v-model="dataQuery.isPay" value-key="value" placeholder="请选择" clearable>
              <el-option v-for="(item, index) in [
                  { value: 0, label: '未缴费' },
                  { value: 1, label: '已缴费' },
                  { value: 2, label: '无需缴费' }
                ]" :key="index" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <!-- 回本计划类型 -->
        <el-col :span="8" :xs="24" v-if="hasNoPermissionField('回本计划类型')">
          <el-form-item label="回本计划类型：" prop="paybackPlanType">
            <el-select v-model="dataQuery.paybackPlanType" filterable value-key="value" placeholder="请选择回本计划类型" clearable>
              <el-option v-for="(item, index) in [
                  { value: 0, label: '委托回本' },
                  { value: 1, label: '自行回本' }
                ]" :key="index" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <!-- 所属品牌： -->
        <el-col :span="8" :xs="24" v-if="hasNoPermissionField('所属品牌')">
          <el-form-item label="所属品牌：" prop="belongBrandName">
            <el-input v-model="dataQuery.belongBrandName" name="id" placeholder="请输入所属品牌" clearable />
          </el-form-item>
        </el-col>
        <!-- 课程推广合作协议状态 -->
        <el-col :span="8" :xs="24" v-if="hasNoPermissionField('课程推广合作协议状态')">
          <el-form-item label="课程推广合作协议状态：" prop="coursePromotionState">
            <el-select v-model="dataQuery.coursePromotionState" filterable value-key="value" placeholder="请选择签署状态" clearable>
              <el-option v-for="(item, index) in [
                  { value: 1, label: '未签署' },
                  { value: 2, label: '已签署' }
                ]" :key="index" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <!-- 是否账号开通抵扣 -->
        <el-col :span="8" :xs="24" v-if="hasNoPermissionField('是否账号开通抵扣')">
          <el-form-item label="是否账号开通抵扣：" prop="deductionType">
            <el-select v-model="dataQuery.deductionType" filterable value-key="value" placeholder="请选择" clearable>
              <el-option v-for="(item, index) in [
                  { value: 2, label: '是' },
                  { value: 1, label: '否' }
                ]" :key="index" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <!-- 注册时间： -->
        <el-col :span="8" :xs="24">
          <el-form-item label="注册时间：" prop="regTime">
            <el-date-picker style="width: 260px" v-model="regTime" type="daterange" value-format="yyyy-MM-dd HH:mm:ss" align="right" unlink-panels range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" @change="$forceUpdate()" clearable></el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="5" style="margin-left: auto">
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="fetchData01()">搜索</el-button>
            <el-button icon="el-icon-refresh" @click="rest()">重置</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-row :span="24">
      <!-- <el-button type="warning" icon="el-icon-document-copy" @click="exportSchool()" v-loading="exportLoading" v-if="checkPermission(['b:merchant:schoolList:export'])" size="mini"> -->
      <el-button type="warning" icon="el-icon-document-copy" @click="exportSchool()" v-loading="exportLoading" v-if="isAdmin" size="mini">
        导出
      </el-button>
      <el-button type="success" v-if="checkPermission(['b:merchant:schoolList:save'])" icon="el-icon-plus" @click="openAdd()" size="mini">新增</el-button>
    </el-row>
    <el-row style="margin: 10px 0" v-if="isOperations && unsignCount > 0">
      <div class="card">
        <i class="el-icon-warning"></i>
        &nbsp;您还有
        <el-tag type="danger">{{ unsignCount || 100 }}</el-tag>
        位门店未签署《课程推广合作协议》，为保证系统正常使用，请要求尽快签署
      </div>
    </el-row>
    <el-row style="margin: 10px 0" v-if="isOperations && unsignCount <= 0">
      <div class="card success">
        <i class="el-icon-success"></i>
        <!-- &nbsp;非常棒您的全部门店都已签署《课程推广合作协议》！ -->
        &nbsp; 所有门店已完成《课程推广合作协议》签署
      </div>
    </el-row>
    <el-table class="common-table" v-loading="tableLoading" :data="tableData" max-height="350px" style="width: 100%; margin: 20px 0" row-key="id" border default-expand-all :tree-props="{ list: 'children', hasChildren: 'true' }" size="mini">
      <el-table-column v-for="(item, index) in tableHeader" :key="index" :prop="item.prop" :label="item.label" :width="item.width" :min-width="item.minWidth">
        <!-- <el-table-column prop="refereeCode" label="上级编号" width="180"></el-table-column>-->
        <!-- 渠道管理员专属操作 -->
        <template v-if="item.label == '操作' && isMerchantManager" v-slot="{ row }">
          <!-- <el-row v-if="row.isEnable != -4"> -->
          <el-row>
            <el-col :span="2">
              <el-button v-if="row.paymentIsComplete == 1 && (row.refundStatus == 3 || row.refundStatus == 0)" type="danger" size="mini" icon="el-icon-circle-close" @click="applyRefund(row)">
                申请退费
              </el-button>
            </el-col>
          </el-row>
        </template>
        <!-- 操作 -->
        <template v-else-if="item.label == '操作'" v-slot="{ row }">
          <!-- <el-row v-if="row.isEnable != -4"> -->
          <el-row>
            <el-col :span="5">
              <el-button-group v-if="row.isEnable !== -4">
                <!-- <el-tooltip content="使用剩余的免费门店名额抵用" placement="top" v-if="checkPermission(['b:merchant:copartnerDeductions'])">
                  <el-button
                    type="success"
                    size="mini"
                    icon="el-icon-switch-button"
                    v-if="row.paymentIsComplete === 0 && operationsFreeSchoolNum > 0 && checkPermission(['b:merchant:OperationsVersion'])"
                    @click="allGone(row, 3, false)"
                  >
                    账号开通抵扣
                  </el-button>
                </el-tooltip>
                <el-tooltip content="使用剩余的免费门店名额抵用" placement="top" v-if="checkPermission(['b:merchant:copartnerDRenew'])">
                  <el-button
                    type="success"
                    size="mini"
                    icon="el-icon-switch-button"
                    v-if="row.schoolType === 3 && row.paymentIsComplete !== 0 && operationsFreeSchoolNum > 0 && checkPermission(['b:merchant:OperationsVersion'])"
                    @click="allGone(row, 3, true)"
                  >
                    账号开通抵扣续费
                  </el-button>
                </el-tooltip> -->
                <el-button type="warning" v-if="row.paymentIsComplete == 0" size="mini" icon="el-icon-bank-card" @click="schoolPay(row)">完款</el-button>
                <el-button type="success" v-else size="mini" icon="el-icon-bank-card" @click="schoolPay(row, true)">续费</el-button>
                <el-button v-if="row.coursePromotionState != 2 && row.paymentIsComplete == 1" type="primary" size="mini" icon="el-icon-edit" @click="getEsignCode(row)">
                  签署合同
                </el-button>
              </el-button-group>
            </el-col>
            <el-col :span="3">
              <el-button v-if="row.paymentIsComplete == 1 && (row.isRefund || isAdmin || isMerchantManager) && (row.refundStatus == 3 || row.refundStatus == 0)" type="danger" size="mini" icon="el-icon-circle-close" @click="applyRefund(row)">
                申请退费
              </el-button>
            </el-col>

            <el-col :span="16">
              <el-button-group>
                <el-button type="primary" icon="el-icon-view" v-if="roleTag != 'Agent' && checkPermission(['b:areas:areasSchoolListEdit:edit']) && checkPermission(['b:merchant:OperationsVersion'])" @click="editSchool(row.id)" size="mini">
                  编辑
                </el-button>
                <el-button type="primary" v-if="row.schoolType !== 3 && checkPermission(['b:merchant:areasSchoolListInvert:invert'])" icon="el-icon-view" @click="convertToOperationsSchool(row.merchantCode)" size="mini">
                  转为运营门店
                </el-button>
                <el-button type="warning" size="mini" icon="el-icon-switch-button" v-if="isAdmin&&
                    checkPermission(['b:merchant:schoolList:open']) &&
                    ((row.isEnable === 0 && row.isCheck === 1) || (row.isEnable === 0 && row.flowEndStatus === 1) || row.isEnable === -3 || row.isEnable === -4)
                  " @click="schoolStatus(row.id, row.isEnable, 1)">
                  开通
                </el-button>

                <el-button type="danger" size="mini" icon="el-icon-video-pause" v-if="checkPermission(['b:merchant:schoolList:pause']) && ((row.isEnable === 1 && row.isCheck === 1) || (row.isEnable === 1 && row.flowEndStatus === 1))" @click="schoolStatus(row.id, row.isEnable, 0)">
                  暂停
                </el-button>
                <el-button type="danger" size="mini" icon="el-icon-video-pause" v-if="
                    checkPermission(['b:areas:areasSchoolListTermination:termination']) &&
                    ((row.isEnable === 1 && row.isCheck === 1) || (row.isEnable === 1 && row.flowEndStatus === 1))
                  " @click="schoolStatus(row.id, row.isEnable, -3)">
                  终止
                </el-button>
                <el-button type="danger" size="mini" icon="el-icon-circle-check" v-if="checkPermission(['b:risk:user:clear']) && (row.isEnable === -1 || row.isEnable === -2)" @click="schoolStatus(row.id, row.isEnable, 1)">
                  解封
                </el-button>
                <el-button type="warning" size="mini" icon="el-icon-switch-button" v-if="row.isCheck == 3 && roleTag === 'admin' && (row.companyCode === '' || row.companyCode === undefined)" @click="schoolCheck(row.id)">
                  审核门店
                </el-button>
                <el-button type="warning" size="mini" icon="el-icon-switch-button" v-if="row.isCheck == 3 && roleTag === 'Company'" @click="schoolCheck(row.id)">
                  审核门店
                </el-button>

                <el-button v-if="checkPermission(['b:risk:user:deliverHistory1'])" type="success" size="mini" @click="deliverHistory(row, 1)" :disabled="row.belongDeliverCode == '' ? true : false">
                  主交付中心指派历史
                </el-button>
                <el-button v-if="checkPermission(['b:risk:user:deliverHistory2'])" type="success" size="mini" @click="deliverHistory(row, 0)" :disabled="row.spareBelongDeliverCode == '' ? true : false">
                  备用交付中心指派历史
                </el-button>

                <el-button type="warning" size="mini" icon="el-icon-link" v-if="
                    false &&
                    checkPermission(['b:merchant:areasSchoolList:assignDelivery']) &&
                    ((row.isOldData === 1 && row.isCheck === 1) || (row.isOldData === 0 && row.flowEndStatus === 1))
                  " @click="openAssignDelivery(row.merchantCode)">
                  指定交付中心
                </el-button>
                <el-button type="warning" size="mini" icon="el-icon-link" v-if="
                    false &&
                    checkPermission(['b:merchant:areasSchoolList:liftDelivery']) &&
                    ((row.isOldData === 1 && row.isCheck === 1) || (row.isOldData === 0 && row.flowEndStatus === 1))
                  " @click="liftDelivery(row.merchantCode)">
                  解除交付中心
                </el-button>
              </el-button-group>
            </el-col>
          </el-row>
        </template>

        <!-- 回本计划类型 -->
        <template v-else-if="item.prop == 'paybackPlanType'" v-slot="{ row }">
          <span v-if="row.paybackPlanType == 0">委托回本</span>
          <span v-else-if="row.paybackPlanType == 1">自行回本</span>
        </template>
        <!-- 课程推广合作协议状态 -->
        <template v-else-if="item.prop == 'coursePromotionState'" v-slot="{ row }">
          <span v-if="row.coursePromotionState == 2">已签署</span>
          <span v-else>未签署</span>
        </template>
        <!-- 主交付中心名称 -->
        <template v-else-if="item.prop == 'schoolBingDevlierName'" v-slot="{ row }">
          <span v-if="row.schoolBingDevlierCode">
            {{ row.schoolBingDevlierName }}
            <i class="el-icon-edit" style="color: #409eff; margin-left: 10px" @click="editDelivery(row, true, 1)"></i>
          </span>
          <el-button v-else type="primary" size="mini" @click="editDelivery(row, false, 1)">指派</el-button>
        </template>
        <!-- 备用交付中心编号 -->
        <template v-else-if="item.prop == 'spareBelongDevlierName'" v-slot="{ row }">
          <span v-if="row.spareBelongDevlierName">
            {{ row.spareBelongDevlierName }}
            <i class="el-icon-edit" style="color: #409eff; margin-left: 10px" @click="editDelivery(row, true, 0)"></i>
          </span>
          <el-button v-else type="primary" size="mini" @click="editDelivery(row, false, 0)">指派</el-button>
        </template>
        <!-- 累计充值学时（节） -->
        <template v-else-if="item.prop == 'totalCourseHours'" v-slot="{ row }">
          <span style="margin-left: 10px">{{ row.schoolType === 3 ? '/' : row.totalCourseHours }}</span>
        </template>
        <!-- 剩余学时（节） -->
        <template v-else-if="item.prop == 'haveCourseHours'" v-slot="{ row }">
          <span style="margin-left: 10px">{{ row.schoolType == 3 ? '/' : row.haveCourseHours }}</span>
        </template>
        <!-- 账户状态 -->
        <template v-else-if="item.prop == 'isEnable'" v-slot="{ row }">
          <span v-if="row.isEnable === -1 && row.paymentIsComplete == 1" class="red">系统关闭</span>
          <span v-else-if="row.isEnable === -1 && row.paymentIsComplete == 0" class="red">等待完款</span>
          <span v-else-if="row.expireStatus === 1 && isExpireDate(row.expireDate)" class="red">到期未续费</span>
          <span v-else-if="row.isEnable === 1" class="green">开通</span>
          <span v-else-if="row.isEnable === -2" class="red">年审关闭</span>
          <span v-else-if="row.isEnable === -3" class="red">终止</span>
          <span v-else-if="row.isEnable === -4" class="red">注销</span>
          <span v-else class="red">暂停</span>
        </template>
        <!-- 培训是否缴费 -->
        <template v-else-if="item.prop == 'isPay'" v-slot="{ row }">{{ row.isPay == 0 ? '未缴费' : row.isPay == 1 ? '已缴费' : '无需缴费' }}</template>
        <!-- 渠道管理员 -->
        <template v-else-if="item.prop == 'channelManagerName'" v-slot="{ row }">
          <span>{{ row.channelManagerName ? row.channelManagerName : '' }}</span>
          <el-link v-if="row.channelManagerName" type="primary" @click="showChannelManagerDetail(row)">详情</el-link>
        </template>
        <!-- 门店类型 -->
        <template v-else-if="item.prop == 'schoolType'" v-slot="{ row }">
          <el-tag v-if="row.schoolType === 1">直营门店</el-tag>
          <el-tag v-if="row.schoolType === 2">加盟门店</el-tag>
          <el-tag v-if="row.schoolType === 3">超级俱乐部门店</el-tag>
        </template>
        <!-- 退费状态 -->
        <template v-else-if="item.prop == 'refundStatus'" v-slot="{ row }">
          <el-tag v-if="row.refundStatus == 0">未申请</el-tag>
          <el-tag v-if="row.refundStatus == 2">审核中</el-tag>
          <el-tag v-if="row.refundStatus == 1">已通过</el-tag>
          <el-tag v-if="row.refundStatus == 3">未通过</el-tag>
        </template>
        <!-- 是否账号开通抵扣 -->
        <template v-else-if="item.prop == 'deductionType'" v-slot="{ row }">
          <!-- 未完款不展示，完款没有该字段也是不展示- -->
          <span v-if="row.paymentIsComplete == 0">-</span>
          <span v-else-if="row.deductionType == 2">是</span>
          <span v-else-if="row.deductionType == 1">否</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
    </el-table>

    <!--弹出窗口：指派交付中心-->
    <el-dialog :title="textMap[dialogStatu]" :visible.sync="dialogFormDelivery" width="40%" :close-on-click-modal="false" @close="dialogFormDelivery = false">
      <template>
        <el-input placeholder="请输入编号" style="width: 40%" v-model="searchMerchantCode" clearable></el-input>
        <el-button type="primary" mini style="margin-left: 10px" title="搜索" @click="searchChannel()">搜索</el-button>

        <el-table :data="tableDataDelivery" v-loading="tableLoading2" highlight-current-row ref="singleTable" height="550" border style="width: 100%">
          <el-table-column type="index" width="50"></el-table-column>
          <el-table-column sortable prop="merchantCode" label="编号" width="180"></el-table-column>
          <el-table-column sortable prop="merchantName" label="名称" width="180"></el-table-column>
          <el-table-column label="操作">
            <template v-slot="{ row }">
              <el-button type="primary" mini title="指派至该交付中心" @click="assignDelivery(row.merchantCode)">指派</el-button>
            </template>
          </el-table-column>
          <el-table-column sortable prop="regTime" label="添加时间"></el-table-column>
        </el-table>
      </template>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormDelivery = false">取消</el-button>
      </div>
    </el-dialog>

    <!--渠道管理员信息-->
    <el-dialog title="渠道管理员信息" :visible.sync="dialogVisibleForChannelManager" width="30%">
      <el-form ref="temp" label-position="left" label-width="120px">
        <el-form-item label="名称: ">
          <el-input disabled v-model="channelManager.realName" placeholder="请输入内容"></el-input>
        </el-form-item>
        <el-form-item label="编号: ">
          <el-input disabled v-model="channelManager.channelManagerCode" placeholder="请输入内容"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisibleForChannelManager = false">关 闭</el-button>
      </span>
    </el-dialog>

    <!-- 修改登陆账号弹窗 -->
    <el-dialog title="修改登录账号" :visible.sync="showLoginAccount" width="70%" :close-on-click-modal="false">
      <el-form :ref="addOrUpdate ? 'addMarketDate' : 'updateMarketDate'" :rules="rules" :model="addOrUpdate ? addMarketDate : updateLoginName" label-position="left" label-width="120px" style="width: 80%">
        <el-form-item label="原登录账号：" prop="name">
          <el-input v-if="updateLoginName" v-model="updateLoginName.name" />
        </el-form-item>
        <el-form-item label="新登录账号：" prop="merchantName">
          <el-input v-if="updateLoginName" v-model="updateLoginName.newName" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="updateSchoolLogin()">确定</el-button>
      </div>
    </el-dialog>

    <!-- 分页 -->
    <el-col :span="20">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]" layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    </el-col>

    <el-dialog :visible.sync="dialogUploadVisible" :close-on-click-modal="false">
      <img width="100%" :src="dialogImageUrl" alt />
    </el-dialog>

    <!-- 交付中心指派 -->
    <el-dialog title="指派交付中心" :visible.sync="dialogPopup" width="30%" center :close-on-click-modal="false" :before-close="closeDialog">
      <el-form :rules="rules" ref="form" :model="form" label-width="180px">
        <el-form-item label="交付中心名称：" prop="deliverMerchantCode" v-if="!deliverStatus">
          <el-select v-model="form.deliverMerchantCode" placeholder="请选择" filterable>
            <el-option v-for="item in deliverCenterList" :key="item.id" :label="item.merchantName" :value="item.merchantCode"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="当前交付中心名称：" prop="deliverName" v-if="deliverStatus">
          <el-input style="width: 58%" v-model="deliverName" disabled></el-input>
        </el-form-item>
        <el-form-item label="重新指派交付中心名称：" prop="deliverMerchantCode" v-if="deliverStatus">
          <el-select v-model="form.deliverMerchantCode" placeholder="请选择" filterable>
            <el-option v-for="item in deliverCenterList" :key="item.id" :label="item.merchantName" :value="item.merchantCode"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <div style="margin-top: 50px">
            <el-button @click="closeDialog">取 消</el-button>
            <el-button type="primary" @click="onSubmit">确 定</el-button>
          </div>
        </el-form-item>
      </el-form>
    </el-dialog>

    <!-- 交付中心指派历史 -->
    <el-dialog :title="form.isMain == 1 ? '主交付中心指派历史' : '备用交付中心指派历史'" :visible.sync="dialogHistory" width="30%" center>
      <div>
        <ul class="infinite-list" style="overflow: auto; margin: 30px 0; height: 400px">
          <el-steps direction="vertical" :active="0">
            <!-- <el-step v-for="(item, index) in assignmentList" :key="index" :title="item.createTime"
              icon="iconfont icon-luyin"
              :description="item.lastDeliverMerchantName + '指派交付中心为' + item.thisDeliverMerchantName"></el-step> -->
            <el-step v-for="(item, index) in assignmentList" :key="index" :title="item.createTime" icon="iconfont icon-luyin" :description="item.thisDeliverMerchantName"></el-step>
          </el-steps>
        </ul>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogHistory = false">取 消</el-button>
        <el-button type="primary" @click="dialogHistory = false">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 申请退费门店确认框 -->
    <el-dialog :visible.sync="dialogDeleteVisible" width="25%" center>
      <p class="dialog-dialogDeleteVisible-title">请确认，是否为以下门店发起退费申请?申请退费后，该合伙人将无法再使用该系统</p>
      <p class="red dialog-dialogDeleteVisible-tip">{{ deleteItem.merchantName }}</p>
      <el-row align="middle" justify="center" type="flex" class="mt-20">
        <el-button @click="dialogDeleteVisible = false" class="button-d">取消</el-button>
        <el-col :span="2"></el-col>
        <el-button @click="applyRefund()" type="primary" class="button-d">确认</el-button>
      </el-row>
    </el-dialog>
    <!-- 签署合同代码 -->
    <el-dialog title="签署合同" class="divider-dialog" :visible.sync="esignCodeVisible" width="50%" :before-close="closeEsignCode" :close-on-click-modal="false" :close-on-press-escape="false">
      <el-divider></el-divider>
      <el-row type="flex" justify="center">签署合同后，该门店将成为您的课程推广大使</el-row>
      <!-- 合同 -->
      <el-row type="flex" justify="center">
        <el-col :span="10" style="text-align: center" class="mt-4" v-for="item in QRcodeList">
          <p style="line-height: 30px">
            {{ item.tips }}
            <br />
            {{ item.title }}
          </p>
          <!-- <div class="QR_code" @dblclick="open(item)" @click="getEsignStatus(true)" v-loading="codeLoading"> -->
          <!-- <el-skeleton class="QR_code" animated v-if="codeLoading">
            <template slot="template">
              <el-skeleton-item variant="image" class="QR_code" />
            </template>
          </el-skeleton> -->
          <!-- <div class="QR_code" @click="open(item)" v-show="!codeLoading"> -->
          <div class="QR_code" v-loading="codeLoading">
            <div :class="['QR_code_success', { deactive: !item.isSign }]">
              <i class="el-icon-success"></i>
              <p>签署成功</p>
            </div>
            <el-tooltip class="item" effect="dark" content="点击复制签署链接">
              <img @error="retry($event)" :src="item.img" @click="fallbackCopyText(item.signUrl ? item.signUrl : item.img)"></img>
            </el-tooltip>
          </div>
        </el-col>
      </el-row>
      <el-row type="flex" justify="center" class="mt-2">
        <el-button type="primary" @click="getSignStatus(true)">签署完成</el-button>
      </el-row>
      <el-row class="mt-2 info">签署完成后，请点击该按钮</el-row>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import schoolApi from '@/api/schoolList';
import areaSchoolApi from '@/api/areasSchoolList';
import { ossPrClient } from '@/api/alibaba';
import { dxSource, pageParamNames } from '@/utils/constants';
import ls from '@/api/sessionStorage';
import authenticationApi from '@/api/authentication';
import checkPermission from '@/utils/permission';
import deliveryCenterApi from '@/api/delivery/deliveryCenter';
import { Base64 } from '@/utils/base64';
import store from '@/store';
import schoolList from '@/api/schoolList';

export default {
  name: 'schoolList',
  computed: {
    ...mapGetters(['setpayUrl']),
    isNeedPay() {
      return checkPermission(['admin', 'Operations', 'School', 'merchantManager', 'MerchantManager']) && this.isPayOpen;
    }
  },
  watch: {
    isNeedPay: {
      handler(newVal, oldVal) {
        // console.log("🚀 ~ isNeedPay ~ newVal:", newVal, "oldVal:", oldVal);
        if (newVal) {
          this.tableHeader.push({ label: '培训是否缴费', prop: 'isPay', minWidth: 150 });
        } else {
          this.removeNoPermissionField(['培训是否缴费']);
        }
      },
      immediate: true // 立即执行
    }
  },
  data() {
    const self = this;
    return {
      retryCount: 3, // 图片加载失败重试次数
      unsignCount: 0,// 未签署合同数量
      // 表头 //tip:修改表头字段时，请同步修改created生命周期里的权限控制 及 上方查询条件用到的表头字段
      tableHeader: [
        { label: '推广大使编号', prop: 'refereeCode', minWidth: 150 },
        { label: '推广大使名称', prop: 'refereeName', minWidth: 150 },
        // { label: '操作', prop: 'operate', minWidth: 1100 },
        { label: '门店编号', prop: 'merchantCode', minWidth: 150 },
        { label: '登录账号', prop: 'name', minWidth: 150 },
        { label: '门店名称', prop: 'merchantName', minWidth: 150 },
        { label: '所在地区', prop: 'address', minWidth: 150 },
        { label: '回本计划类型', prop: 'paybackPlanType', minWidth: 150 },
        { label: '负责人', prop: 'realName', minWidth: 150 },
        { label: '主交付中心编号', prop: 'schoolBingDevlierCode', minWidth: 150 },
        { label: '主交付中心名称', prop: 'schoolBingDevlierName', minWidth: 150 },
        { label: '备用交付中心编号', prop: 'spareBelongDevlierCode', minWidth: 150 },
        { label: '备用交付中心名称', prop: 'spareBelongDevlierName', minWidth: 150 },
        { label: '累计充值学时（节）', prop: 'totalCourseHours', minWidth: 150 },
        { label: '已售学时（节）', prop: 'saleCourseHours', minWidth: 150 },
        { label: '剩余学时（节）', prop: 'haveCourseHours', minWidth: 150 },
        { label: '所属俱乐部', prop: 'operationsName', minWidth: 150 },
        { label: '账户余额（元）', prop: 'withdrawMoney', minWidth: 150 },
        { label: '所属交付中心', prop: 'deliveryCenterCode', minWidth: 150 },
        { label: '账户状态', prop: 'isEnable', minWidth: 150 },
        { label: '退费状态', prop: 'refundStatus', minWidth: 150 },
        { label: '渠道管理员', prop: 'channelManagerName', minWidth: 150 },
        { label: '添加时间', prop: 'regTime', minWidth: 150 },
        { label: '到期时间', prop: 'expireDate', minWidth: 150 },
        { label: '门店类型', prop: 'schoolType', minWidth: 150 },
        { label: '俱乐部所在城市', prop: 'operationsCity', minWidth: 150 },
      ],
      codeLoading: false, // 门店编号加载
      signLoading: false, // 签署合同加载
      esignCodeVisible: false, // 签署合同二维码弹窗
      QRcodeTitleList: [], // 合同信息
      // 合同列表
      QRcodeList: [
        {
          isFirstParty: 0,
          img: '',
          tips: '请您使用手机扫码签署',
          title: '《课程推广合作协议》',
          isSign: false
        },
        {
          isFirstParty: 1,
          img: '',
          tips: '邀请该门店负责人，使用手机扫码签署',
          title: '《课程推广合作协议》',
          isSign: false
        }
      ], // 签署合同二维码列表
      currentAdmin: '',
      operationsFreeSchoolNum: window.localStorage.getItem('operationsFreeSchoolNum'),
      token: store.getters.token,
      channelManager: {},
      dialogVisibleForChannelManager: false,
      dialogDeleteVisible: false, // 删除门店弹窗
      deleteItem: {}, // 删除门店信息
      searchMerchantCode: '',
      branchOfficeMerchantCode: '',
      dialogFormDelivery: false,
      textMap: {
        assign: '指定交付中心'
      },
      tableDataDelivery: [],
      tableLoading2: false,
      dialogStatu: 'assign',
      addOrUpdate: false,
      tableLoading: false,
      roleTag: '',
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },

      regTime: [],
      tableData: [],
      dataQuery: {
        merchantCode: '',
        name: '',
        merchantName: '',
        refereeCode: '',
        marketPartner: '',
        realName: '',
        address: '',
        isEnable: '',
        schoolType: '',
        paymentIsComplete: '',
        paybackPlanType: '', // 回本计划类型
        coursePromotionState: '', // 课程推广协议签署状态 1未签署2已签署
        deductionType: '', // 抵扣状态 1:线上 2:抵扣
        refundStatus: '' // 退费状态
      },
      dialogVisible: false,
      exportLoading: false,
      updateMarketDate: {},
      addMarketDate: {},
      showLoginAccount: false,
      radio: '0', //单选框状态 值必须是字符串
      fileListPending: [], // 待处理已上传图片信息
      fileDetailListPending: [], // 待处理已上传图片信息
      fileContractDetailList: [], // 上传图片已有图片列表
      fileIdCardDetailList: [],
      fileShopDetailList: [],

      dialogUploadVisible: false,
      uploadLoading: false, // 上传图片加载按钮
      dialogImageUrl: '', // 上传图片预览
      fileList: [], // 上传图片已有图片列表
      uploadLoadingIdCard: false,
      uploadLoadingShop: false,
      updateLoginName: {},

      //地图开始
      markers: [
        [121.59996, 31.197646],
        [121.40018, 31.197622],
        [121.69991, 31.207649]
      ],
      searchOption: {
        city: '上海',
        citylimit: true
      },
      center: [121.59996, 31.197646],
      zoom: 12,
      lng: 0,
      lat: 0,
      address: '',
      province: '',
      city: '',
      district: '',
      loaded: false,
      circles: [
        {
          center: [117.26696, 31.87869],
          radius: 100,
          fillOpacity: 0.5,
          events: {
            click(e) {
              //alert(e.lnglat);
              let { lng, lat } = e.lnglat;
              self.lng = lng;
              self.lat = lat;
              console.log(e);
              // 这里通过高德 SDK 完成。
              var geocoder = new AMap.Geocoder({
                radius: 1000,
                extensions: 'all'
              });
              geocoder.getAddress([e.lnglat.lng, e.lnglat.lat], function (status, result) {
                if (status === 'complete' && result.info === 'OK') {
                  if (result && result.regeocode) {
                    if (self.addOrUpdate) {
                      // 具体地址
                      self.addMarketDate.longitude = lng;
                      self.addMarketDate.latitude = lat;

                      self.addMarketDate.address = result.regeocode.formattedAddress;
                      // 省
                      self.addMarketDate.province = result.regeocode.addressComponent.province;
                      // 市
                      self.addMarketDate.city = result.regeocode.addressComponent.city;
                      // 区
                      self.addMarketDate.area = result.regeocode.addressComponent.district;
                    } else {
                      self.updateMarketDate.latitude = lat;
                      self.updateMarketDate.longitude = lng;
                      self.updateMarketDate.address = result.regeocode.formattedAddress;
                      // 省
                      self.updateMarketDate.province = result.regeocode.addressComponent.province;
                      // 市
                      self.updateMarketDate.city = result.regeocode.addressComponent.city;
                      // 区
                      self.updateMarketDate.area = result.regeocode.addressComponent.district;
                    }
                    self.$nextTick();
                  }
                } else {
                  //alert('地址获取失败')
                }
              });
            }
          }
        }
      ],
      events: {
        click(e) {
          //alert(e.lnglat);
          let { lng, lat } = e.lnglat;
          self.lng = lng;
          self.lat = lat;
          console.log(e);
          // 这里通过高德 SDK 完成。
          var geocoder = new AMap.Geocoder({
            radius: 1000,
            extensions: 'all'
          });
          geocoder.getAddress([e.lnglat.lng, e.lnglat.lat], function (status, result) {
            if (status === 'complete' && result.info === 'OK') {
              if (result && result.regeocode) {
                if (self.addOrUpdate) {
                  // 具体地址
                  self.addMarketDate.longitude = lng;
                  self.addMarketDate.latitude = lat;

                  self.addMarketDate.address = result.regeocode.formattedAddress;
                  // 省
                  self.addMarketDate.province = result.regeocode.addressComponent.province;
                  // 市
                  self.addMarketDate.city = result.regeocode.addressComponent.city;
                  // 区
                  self.addMarketDate.area = result.regeocode.addressComponent.district;
                } else {
                  self.updateMarketDate.latitude = lat;
                  self.updateMarketDate.longitude = lng;
                  self.updateMarketDate.address = result.regeocode.formattedAddress;
                  // 省
                  self.updateMarketDate.province = result.regeocode.addressComponent.province;
                  // 市
                  self.updateMarketDate.city = result.regeocode.addressComponent.city;
                  // 区
                  self.updateMarketDate.area = result.regeocode.addressComponent.district;
                }
                self.$nextTick();
              }
            } else {
              //alert('地址获取失败')
            }
          });
        }
      },
      isPayOpen: true,
      plugin: [
        {
          enableHighAccuracy: true, //是否使用高精度定位，默认:true
          timeout: 100, //超过10秒后停止定位，默认：无穷大
          maximumAge: 0, //定位结果缓存0毫秒，默认：0
          convert: true, //自动偏移坐标，偏移后的坐标为高德坐标，默认：true
          showButton: true, //显示定位按钮，默认：true
          buttonPosition: 'RB', //定位按钮停靠位置，默认：'LB'，左下角
          showMarker: true, //定位成功后在定位到的位置显示点标记，默认：true
          showCircle: true, //定位成功后用圆圈表示定位精度范围，默认：true
          panToLocation: true, //定位成功后将定位到的位置作为地图中心点，默认：true
          zoomToAccuracy: true, //定位成功后调整地图视野范围使定位位置及精度范围视野内可见，默认：f
          extensions: 'all',
          expandZoomRange: true,
          keyboardEnable: true,
          pName: 'Geolocation',
          campus: [],
          events: {
            init(o) {
              // o 是高德地图定位插件实例
              o.getCurrentPosition((status, result) => {
                console.log(result);
                if (result && result.position) {
                  self.lng = result.position.lng;
                  self.lat = result.position.lat;

                  self.center = [result.position.lng, result.position.lat];
                  self.loaded = true;
                  self.$nextTick();
                }
              });
            }
          }
        }
      ],

      isAdmin: false,
      isAdminManger: false,
      isOperations: false, // 是否是俱乐部
      isZxBrand: false, // 是否是超级品牌
      dialogPopup: false, // 交付中心指派弹窗
      deliverName: '', // 交付中心指派名称
      dialogHistory: false, // 交付中心指派历史弹窗
      deliverStatus: false, // 交付中心指派状态 false未指派

      form: {
        merchantCode: '',
        deliverMerchantCode: '',
        isMain: 1
      },

      deliverCenterList: {}, // 可指派交付中心列表
      rules: {
        deliverMerchantCode: [{ required: true, message: '请选择交付中心', trigger: 'blur' }]
      },

      assignmentList: {}, // 指派交付中心历史
      options: [
        { value: '0', label: '未指派' },
        { value: '1', label: '指派' }
      ]
    };
  },
  created() {
    // 获取当前时间，如果接口开放，则培训缴费相关功能放开
    schoolList.getTrainingOpenTime().then((res) => {
      const openTime = res.data;
      // const isPayOpen = this.dynamicFunction(openTime);
      this.isPayOpen = openTime == 'Y' ? true : false;
    });
    this.isOperations = checkPermission(['Operations']); // 是否超级俱乐部 获取列表数据
    this.fetchData(); // 获取列表数据
    ossPrClient();
    let role = localStorage.getItem('roleTag');
    // console.log(role, '=====================');
    this.isAdminManger = checkPermission(['HeadquartersStoreManagement']); // 总部门店管理员
    this.isCityBranch = checkPermission(['CityCompany']); // 城市分公司
    this.isMerchantManager = checkPermission(['merchantManager', 'MerchantManager']); // 渠道管理员
    this.isZxBrand = checkPermission(['zxBrand']); // 超级品牌
    this.isAdmin = checkPermission(['admin']); // 超级管理员

    if (this.isAdmin || this.isOperations || this.isMerchantManager) {
      this.tableHeader.splice(2, 0, { label: '操作', prop: 'operate', minWidth: 1100 },);
    }



    // console.log(this.isAdmin, '====================');

    // 权限控制
    // 1.1 只有某些字段
    if (this.isCityBranch) return this.removeNoPermissionField(['门店编号', '门店名称', '负责人', '已售学时（节）', '所属俱乐部', '账户状态', '添加时间', '到期时间'], true); // 如果是城市分店管理员 则只有这些字段
    // 1.2 隐藏部分字段
    if (this.isZxBrand || this.isMerchantManager) this.removeNoPermissionField(['操作']);
    if (this.isAdminManger) this.removeNoPermissionField(['登录账号', '操作']); // 如果是总部门店管理员 则隐藏部分字段
    if (checkPermission(['b:merchant:OperationsVersion']) && !checkPermission(['admin'])) this.removeNoPermissionField(['所在地区', '累计充值学时（节）', '剩余学时（节）']);
    if (!checkPermission(['b:risk:user:deliverHistory1'])) this.removeNoPermissionField(['主交付中心编号', '主交付中心名称', '所属交付中心']);
    if (checkPermission(['b:merchant:OperationsVersion'])) this.removeNoPermissionField(['账户余额（元）']);
    if (!checkPermission(['b:risk:user:deliverHistory2'])) this.removeNoPermissionField(['备用交付中心编号', '备用交付中心名称']);
    if (!checkPermission(['b:risk:user:channelManagerName'])) this.removeNoPermissionField(['渠道管理员']);
    if (!checkPermission(['b:merchant:OperationsVersion'])) this.removeNoPermissionField(['到期时间']);
    // 1.3 添加一些字段
    // if (this.isOperations) this.tableHeader.push({ label: '退费状态', prop: 'refundStatus', minWidth: 150 });
    // 课程推广合作协议状态
    if (this.isAdmin || this.isOperations || this.isZxBrand) this.tableHeader.push({ label: '课程推广合作协议状态', prop: 'coursePromotionState', minWidth: 150 });
    // 所属品牌
    if (this.isAdmin || this.isOperations) this.tableHeader.push({ label: '所属品牌', prop: 'belongBrandName', minWidth: 150 });
    // 是否账号开通抵扣
    if (this.isAdmin || this.isOperations || this.isMerchantManager) this.tableHeader.push({ label: '是否账号开通抵扣', prop: 'deductionType', minWidth: 150 });
    // 渠道管理员专属操作
    if (this.isMerchantManager) this.tableHeader.splice(2, 0, { label: '操作', prop: 'operate', minWidth: 150 });
  },

  activated() {
    this.fetchData();
  },
  deactivated() {
    if (this.st0) this.clearEsignInterval(this.st0); //清除定时器
  },
  mounted() {
    authenticationApi.checkAccountBalance().then((res) => {
      this.roleTag = res.data.data.roleTag;
    });
  },
  methods: {
    checkPermission,
    // 查看表头列表是否有该字段
    hasNoPermissionField(field) {
      let has = this.tableHeader.some((i) => {
        if (i.prop == field || i.label == field) {
          return true;
        }
        return false;
      });
      return has;
    },
    // 移除掉没有权限的字段 或只保留 有权限的字段
    /**
     *移除掉没有权限的字段 或只保留 有权限的字段
     * @param array 需要移除或保留的字段
     * @param how true:保留 false:移除
     */
    removeNoPermissionField(array, how = false) {
      this.tableHeader = this.tableHeader.filter((i) => {
        for (let j = 0; j < array.length; j++) {
          if (i.prop == array[j] || i.label == array[j]) {
            return how;
          }
        }
        return !how;
      });
    },
    //判断时间是否到期
    isExpireDate(date) {
      if (!date) {
        return false;
      }
      let data = (new Date(date) * 1000) / 1000;
      let now = Date.now();
      console.log(data, now);

      if (data - now > 0) {
        return false;
      } else {
        return true;
      }
    },
    dateDiff(d1, d2) {
      console.log('🚀 ~ exportFlow ~ 666:', d1.replace(/-/g, '/'));
      d1 = new Date(d1.replace(/-/g, '/'));
      d2 = new Date(d2.replace(/-/g, '/'));
      var obj = {};
      obj.s = Math.floor((d2 - d1) / 1000); //差几秒
      obj.m = Math.floor(obj.s / 60); //差几分钟
      obj.h = Math.floor(obj.m / 60); //差几小时
      obj.D = Math.floor(obj.h / 24); //差几天

      return obj.D;
    },
    exportSchool() {
      const that = this;

      if (this.exportLoading) return;
      // 导出数据校验
      if (!this.regTime || this.regTime.length <= 0) {
        this.$message({
          message: '请先选择注册时间，再进行数据导出，最多可选一个月',
          type: 'warning'
        });
        return;
      } else {
        that.dataQuery.startRegTime = this.regTime[0];
        that.dataQuery.endRegTime = this.regTime[1];
        //同一月内不校验  跨月校验30天
        let a = this.regTime[0].split('-')[1];// 月份
        let b = this.regTime[1].split('-')[1];// 月份
        let c = this.regTime[0].split('-')[0];// 年份
        let d = this.regTime[1].split('-')[0];// 年份
        if (a != b || c != d) {
          if (this.dateDiff(this.regTime[0], this.regTime[1]) > 30) {
            this.$message.error('最多可导出一个月数据，请重新选择时间');
            return;
          }
        }
      }
      // 导出数据
      that.exportLoading = true;
      schoolApi
        .exportSchool(that.dataQuery)
        .then((response) => {
          console.log(response);
          if (!response) {
            this.$notify.error({
              title: '操作失败',
              message: '文件下载失败'
            });
          }
          const url = window.URL.createObjectURL(response);
          const link = document.createElement('a');
          link.style.display = 'none';
          link.href = url; //获取服务器端的文件名
          link.setAttribute('download', '门店列表.xls');
          document.body.appendChild(link);
          link.click();
          that.exportLoading = false;
        })
        .catch((err) => {
          // console.log(err);
          this.$message.error('超出最大导出条数');
          that.exportLoading = false;
        });
    },
    getRoleTag() {
      schoolList.getCurrentAdmin().then((res) => {
        this.currentAdmin = res.data;
      });
    },
    showChannelManagerDetail(row) {
      this.channelManager.realName = row.channelManagerRealName;
      this.channelManager.channelManagerCode = row.channelManagerCode;
      this.dialogVisibleForChannelManager = true;
    },
    //打开指定交付中心窗口
    openAssignDelivery(merchantCode) {
      this.dialogFormDelivery = true;
      this.branchOfficeMerchantCode = merchantCode;
      //查询所有交付中心
      deliveryCenterApi
        .allList()
        .then((res) => {
          this.tableDataDelivery = res.data;
          console.log(this.tableDataDelivery, 'this.tableDataDelivery');
        })
        .catch((err) => { });
    },
    //表格数据选中和跳转到指定位置
    searchChannel() {
      for (let i = 0; i < this.tableDataDelivery.length; i++) {
        if (this.tableDataDelivery[i].merchantCode === this.searchMerchantCode) {
          if (!this.$refs['singleTable']) return; //不存在这个表格则返回
          let elTable = this.$refs['singleTable'].$el;
          if (!elTable) return;
          const scrollParent = elTable.querySelector('.el-table__body-wrapper');
          const targetTop = elTable.querySelectorAll('.el-table__body tr')[i].getBoundingClientRect().top; //该行的位置
          const containerTop = elTable.querySelector('.el-table__body').getBoundingClientRect().top; //body的位置
          //跳转
          scrollParent.scrollTop = targetTop - containerTop; //跳转到存下编辑行的ScrollTop
          this.$refs.singleTable.setCurrentRow(this.tableDataDelivery[i]);
        }
      }
    },
    //1 全额  2 成本  3免费
    allGone(item, goneType, renew) {
      if (!renew && item.isPay == 0) {
        schoolList.getTrainingPayInfo(item.merchantCode).then((res) => {
          const split = dxSource.split('##');
          res.data.dxSource = res.data.registerCallIInfo.appSource + '##' + split[1] + '##' + split[2];
          let params = JSON.stringify(res.data);
          let req = 'token=' + this.token + '&params=' + params + '&back=' + window.location.href;
          //需要编码两遍，避免出现+号等
          var encode = Base64.encode(Base64.encode(req));
          window.open(this.setpayUrl + 'product?' + encode, '_blank');
        });
      } else {
        if (goneType === 3) {
          this.$confirm('此操作将消耗您一个免费名额给该合伙人开通或续费一年, 是否继续?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          })
            .then(() => {
              areaSchoolApi.allGone(item.id, goneType, renew).then((res) => {
                if (renew) {
                  this.$message.success('操作成功');
                  this.fetchData();
                } else {
                  this.$message.success('操作成功');
                  this.fetchData();
                }
              });
            })
            .catch((e) => {
              this.$message({
                type: 'info',
                message: e
              });
            });
          return;
        }
        areaSchoolApi.allGone(item.id, goneType, renew).then((res) => {
          if (checkPermission(['b:merchant:OperationsVersion'])) {
            const split = dxSource.split('##');
            res.data.dxSource = res.data.registerCallIInfo.appSource + '##' + split[1] + '##' + split[2];
            let params = JSON.stringify(res.data);
            let req = 'token=' + this.token + '&params=' + params + '&back=' + window.location.href;
            //需要编码两遍，避免出现+号等
            var encode = Base64.encode(Base64.encode(req));

            window.open(this.setpayUrl + 'product?' + encode, '_blank');
            // window.open("http://192.168.5.35:8000/product?" + encode, "_blank");
          } else {
            this.$message.success('操作成功');
            this.fetchData();
          }
        });
      }
    },
    assignDelivery(merchantCode) {
      this.tableLoading2 = true;
      deliveryCenterApi
        .assignDelivery(this.branchOfficeMerchantCode, merchantCode)
        .then((res) => {
          this.tableLoading2 = false;
          this.$message.success('指派成功');
          this.dialogFormDelivery = false;
          this.fetchData();
        })
        .catch((err) => { });
    },
    liftDelivery(merchantCode) {
      this.$confirm('解除通过该账户绑定的交付中心?')
        .then((_) => {
          deliveryCenterApi
            .liftDelivery(merchantCode)
            .then((res) => {
              this.$message.success('解除绑定成功');
              this.fetchData();
            })
            .catch((err) => { });
        })
        .catch((_) => { });
    },
    // 编辑
    editSchool(id) {
      const that = this;
      ls.setItem('addOrUpdate3', false);
      ls.setItem('areasSchoolId', id);
      that.$router.push({
        path: '/areas/areasSchoolEdit',
        query: {
          id: id,
          addOrUpdate: false
        }
      });
    },
    // 申请退费
    applyRefund(deleteItem = null) {
      const that = this;
      if (deleteItem == null) {
        const loading = this.$loading({
          lock: true,
          text: 'Loading',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        schoolApi
          .applyRefund({ merchantCode: this.deleteItem.merchantCode })
          .then((res) => {
            this.$message.success('申请退费成功');
            this.dialogDeleteVisible = false;
            loading.close();
            this.fetchData();
          })
          .catch((err) => {
            this.dialogDeleteVisible = false;
          });
        return;
      }
      this.deleteItem = deleteItem;
      this.dialogDeleteVisible = true;
    },
    /**
    * @arguments_1 img元素实例,this
    * @arguments_2 重试次数
    * @return void
    */
    retry(event) {
      const imgElement = event.target
      let that = this;

      console.log(`图片加载失败,将会重试${this.retryCount}次`);
      if (this.retryCount-- > 0) {
        // 重定向图片路径, 引起重新加载
        imgElement.src = imgElement.src + '?t=' + Date.now(); // 强制刷新
      } else {
        console.log('图片加载失败, 请检查网络或联系管理员');
        if (this.retryTimer) that.clearEsignInterval(this.retryTimer); // 清除旧定时器
        // 次数超过后取消错误监听, 重置重试次数
        this.retryTimer = setTimeout(() => {
          that.retryCount = 5;
        }, 1000);
      }
    },
    // 复制
    fallbackCopyText(text) {
      const textArea = document.createElement('textarea');
      textArea.value = text;
      textArea.style.position = 'fixed'; // 避免滚动到底部
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();

      try {
        const successful = document.execCommand('copy');
        this.$message({
          message: successful ? '复制成功！' : '复制失败',
          type: successful ? 'success' : 'warning',
          duration: 10000
        });
        // console.log(successful ? '复制成功！' : '复制失败');
      } catch (err) {
        console.error('无法复制:', err);
      }
      document.body.removeChild(textArea);
    },
    //清除轮询定时器
    clearEsignInterval(st) {
      clearInterval(st);
      st = null;
    },
    // 查看二维码
    getEsignCode(row) {
      let that = this;
      // 动态修改提示语
      let loginMerchantCode = localStorage.getItem('loginMerchantCode');
      if (row.refereeCode == loginMerchantCode) {
        that.QRcodeList[0].tips = '请您使用手机扫码签署';
      } else {
        that.QRcodeList[0].tips = '邀请绑定的推广大使门店负责人，使用手机扫码签署';
      }
      that.esignCodeVisible = true; // 显示二维码弹窗
      this.codeLoading = true; // 加载中
      schoolApi.schoolDetailV2(row.id).then(async (res) => {
        // 保存 推广大使合同 信息
        this.QRcodeTitleList = {
          signFlowId: res.data.signFlowId,
          templateType: res.data.templateType,
          signSource: res.data.signSource
        };
        that.esignInterval(); // 轮询 -获取签署链接 及 签署状态
      });
    },
    // 轮询 -获取签署链接 及 签署状态
    esignInterval() {
      let that = this;
      // 删除已存在的定时器
      if (this.st0) this.clearEsignInterval(this.st0);
      this.codeLoading = true; // 加载中
      // 轮询(获取签署状态)
      that.getSignStatus(false, 1);

      that.st0 = setInterval(() => {
        // 获取签署状态 及 获取签署链接
        that.getSignStatus();
      }, 5000);

      // 10分钟后清除定时器
      setTimeout(() => {
        that.clearEsignInterval(that.st0);
      }, 10 * 60 * 1000);
    },
    // 获取签署链接 及 签署状态
    getSignStatus(tip = false, isQrLink = 0) {
      // console.log('🚀 ~ getEsignStatus ~ tip:', this.codeLoading);
      let that = this;
      if (this.codeLoading && tip) {
        that.$message.warning('请先等待合同码加载完成');
        return;
      }
      let item = this.QRcodeTitleList;
      schoolApi
        .fetchContractQrLink({ flowId: item.signFlowId, templateType: item.templateType, signSource: item.signSource, isQrLink })
        .then((res) => {
          // 合同是否已签署完成
          let isAllSign = res.data.every((i) => i.signStatus == 2);
          if (isAllSign) {
            this.$message.success('合同已全部签署完成');
            this.closeEsignCode();
            this.fetchData();
            // this.esignCodeVisible = false; // 关闭二维码弹窗
            // this.QRcodeList.forEach((item) => {
            //   item.img = '';
            //   item.isSign = false;
            // });
            // // 删除已存在的定时器
            // if (this.st0) this.clearEsignInterval(this.st0);
            return;
          }
          // 没有全部签署完，继续轮询
          res.data.forEach((result) => {
            this.codeLoading = false; // 加载完成
            /**
             * mobile 手机号
             * isFirstParty 是否甲方：0-是 1-否
             * signStatus 签署状态1-待签署 2-签署成功 3-签署失败
             * qrUrl 二维码地址
             * signUrl 原始地址
             * isContract 是否存在合同: 0-否 1-是
             */
            this.QRcodeList.forEach((item, i) => {
              // console.log('🚀 ~ this.QRcodeList.forEach ~ QRcodeList:', item, result);

              // 如果类型相同
              if (item.isFirstParty == result.isFirstParty) {
                // item.img = result.value.qrUrl;
                // 图片有值就赋值
                if (result.qrUrl) {
                  this.retryCount = 5; // 重试次数
                  item.img = result.qrUrl
                }
                item.signUrl != result.signUrl && result.signUrl != '' ? (item.signUrl = result.signUrl) : '';
                // 签署成功修改状态
                if (result.signStatus == 2) {
                  item.isSign = true;
                } else if (tip) {
                  that.$message.warning('合同还未全部签署，请先签署');
                  tip = false;
                  return;
                }
              }
            });
          });
        })
        .catch((err) => {
          this.closeEsignCode();
        });
    },
    closeEsignCode() {
      let that = this;
      this.esignCodeVisible = false; // 关闭二维码弹窗
      this.QRcodeList.forEach((item) => {
        item.img = '';
        item.isSign = false;
      });
      // 删除已存在的定时器
      if (this.st0) this.clearEsignInterval(this.st0);
    },
    // 门店完款
    /**
     *
     * @param row
     * @param type 是否是续费，默认是false-完款，true-续费
     */
    schoolPay(row, type = false) {
      const that = this;
      // 下面是续费
      if (type) {
        this.$confirm('是否消耗一个系统数为该门店续费1年?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'none'
        })
          .then(() => {
            console.log(row);

            schoolApi.schoolRenewal({ merchantCode: row.merchantCode }).then((res) => {
              that.$message.success('续费成功');
              that.fetchData();
              // 查询剩余学习系统数量
              store.dispatch('getSystem');
            });
          })
          .catch(() => {
            // this.$message({
            //   type: 'info',
            //   message: '已取消删除'
            // });
          });
        return;
      }
      // 下面是完款
      that.$router.push({
        path: '/merchantManagement/schoolCompletePaymentIs',
        query: {
          id: row.id,
          signingStatus: row.signingStatus
        }
      });
    },
    //重置
    rest() {
      this.$refs.dataQuery.resetFields();
      this.regTime = '';
      this.fetchData01();
    },
    fetchData01() {
      this.tablePage.currentPage = 1;
      this.fetchData();
    },
    // 查询提现列表
    fetchData() {
      const that = this;
      that.tableLoading = true;
      var a = that.regTime;
      if (a != null) {
        that.dataQuery.startRegTime = a[0];
        that.dataQuery.endRegTime = a[1];
      } else {
        that.dataQuery.startRegTime = '';
        that.dataQuery.endRegTime = '';
      }
      schoolApi.schoolPage(that.tablePage.currentPage, that.tablePage.size, that.dataQuery).then((res) => {
        that.tableData = res.data.data;
        that.tableLoading = false;
        // 设置后台返回的分页参数
        pageParamNames.forEach((name) => that.$set(that.tablePage, name, parseInt(res.data[name])));
      });
      if (this.isOperations) {
        schoolApi.fetchUnsignContractCount().then((res) => {
          // console.log(res.data, 'res.data888');
          this.unsignCount = res.data;
        });
      }
    },

    clearAddress() {
      const that = this;
      that.updateMarketDate.address = '';
    },
    convertToOperationsSchool(merchantCode) {
      const that = this;
      this.$confirm('将门店' + merchantCode + '转为超级俱乐部门店吗', '转换门店', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        schoolApi.convertToOperationsSchool(merchantCode).then((res) => {
          if (!res.success) {
            that.$message.error(res.message);
            return;
          }
          that.$nextTick(() => that.fetchData());
          that.$message.success(res.message);
        });
      });
    },
    schoolStatus(id, isEnable, enable) {
      // return console.log(id, isEnable, enable)
      const that = this;
      this.$confirm('确定操作吗?', '修改状态', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        schoolApi.openEnableV2(id, isEnable, enable).then((res) => {
          if (!res.success) {
            that.$message.error(res.message);
            return;
          }
          that.$nextTick(() => that.fetchData());
          that.$message.success(res.message);
        });
      });
    },
    // 状态改变事件
    change(radio) {
      if (radio == '1') {
        this.updateMarketDate.canTeach = true;
      } else {
        this.updateMarketDate.canTeach = false;
      }
    },
    // 门店是否需要续费
    async isNeedRenew() {
      let res = await schoolList.getCurrentAdmin();
      if (res.data.expiry != null && res.data.expiry && res.data.roleTag != 'admin' && res.data.schoolType == 3) {
        this.$message.error('门店到期未续费，不能新增门店，请先完成续费');
        return false;
      }
      return true;
    },
    // 新增操作
    async openAdd() {
      let isShow = await this.isNeedRenew();
      if (!isShow) {
        return;
      }
      const that = this;
      ls.setItem('addOrUpdate3', true);
      that.$router.push({
        path: '/areas/areasSchoolAdd',
        query: {
          addOrUpdate: true
        }
      });
    },

    schoolCheck(id) {
      const that = this;
      window.localStorage.setItem('schoolId', id);
      that.$router.push({
        path: '/merchantManagement/schoolCheck',
        query: {
          id: id
        }
      });
    },

    onSearchResult(pois) {
      let latSum = 0;
      let lngSum = 0;
      if (pois.length > 0) {
        pois.forEach((poi) => {
          let { lng, lat } = poi;
          lngSum += lng;
          latSum += lat;
          this.markers.push([poi.lng, poi.lat]);
        });
        let center = {
          lng: lngSum / pois.length,
          lat: latSum / pois.length
        };
        this.center = [center.lng, center.lat];
      }
    },
    addMarker: function () {
      let lng = 121.5 + Math.round(Math.random() * 1000) / 10000;
      let lat = 31.197646 + Math.round(Math.random() * 500) / 10000;
      this.markers.push([lng, lat]);
    },
    /*    //获取托管中心级别列表
      getSelectResultList() {
        dealerListApi
          .getSelectResult()
          .then((res) => {
            this.rankType = res.data;
          })
          .catch((err) => {});
      },*/

    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val;
      this.fetchData();
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val;
      this.fetchData();
    },
    // 打开修改登陆账号
    openLogin() {
      this.showLoginAccount = true;
    },
    updateSchoolLogin() {
      const that = this;
      that.updateLoginName.name = that.updateLoginName.newName;
      schoolApi.schoolUpdateLogin(that.updateLoginName).then(() => {
        that.showLoginAccount = false;
        that.$nextTick(() => that.fetchData());
        that.$message.success('修改登录账号成功');
      });
    },

    //--合同照片开始
    // 删除上传图片
    handleRemoveDetailContract(file, fileList) {
      const that = this;
      if (!that.addOrUpdate) {
        that.fileContractDetailList = fileList;
      } else {
        for (let a = 0; a < that.fileDetailListPending.length; a++) {
          that.fileDetailListPending[a].uid === file.uid ? that.fileContractDetailList.splice(a, 1) : '';
        }
      }
    },
    // 上传图片预览
    handlePictureCardPreviewContract(file) {
      this.dialogImageUrl = file.url;
      this.dialogUploadVisible = true;
    },
    // 上传图片数量超限
    justPictureNum(file, fileList) {
      this.$message.warning(`当前限制选择1个文件`);
    },

    // 获取可指派交付中心列表
    editDelivery(row, state, isMain) {
      // return console.log(row)
      schoolApi.belongDeliverAndAllDeliver().then((res) => {
        this.deliverCenterList = res.data;
      });
      this.form.merchantCode = row.merchantCode;
      this.form.isMain = isMain;
      if (isMain) {
        this.deliverName = row.schoolBingDevlierName;
      } else {
        this.deliverName = row.spareBelongDevlierName;
      }
      this.deliverStatus = state;
      this.dialogPopup = true;
    },

    // 交付中心历史
    deliverHistory(row, isMain) {
      this.form.isMain = isMain;
      let data = {
        merchantCode: row.merchantCode,
        isMain
      };
      schoolApi.deliverBindRecordList(data).then((res) => {
        // console.log('交付中心历史')
        // console.log(res)
        // console.log('交付中心历史')
        this.assignmentList = res.data;
        console.log('交付中心历史');
        console.log(this.assignmentList);
        console.log('交付中心历史');
      });

      this.dialogHistory = true;
    },

    // 指派交付中心
    onSubmit() {
      const that = this;
      that.$refs['form'].validate((valid) => {
        // 表单验证
        if (valid) {
          const loading = this.$loading({
            lock: true,
            text: '指派交付中心',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          });
          console.log(that.form);
          schoolApi
            .assignDeliverAndAllDeliver(that.form)
            .then((res) => {
              loading.close();
              // if (res.success) {
              //   that.$nextTick(() => {
              //     that.closeDialog();
              //     that.fetchData();
              //   });
              //   that.$message.success('指派交付中心成功');
              // } else {
              //  that.$message.info(res.message.split('@')[0]);
              // }
              that.$nextTick(() => {
                that.closeDialog();
                that.fetchData();
              });
              that.$message.info(res.message.split('@')[0]);
            })
            .catch((err) => {
              loading.close();
            });
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },

    closeDialog() {
      this.$refs.form.resetFields();
      this.dialogPopup = false;
      this.form.merchantCode = '';
      this.form.deliverMerchantCode = '';
    }
  },
  beforeDestroy() {
    if (this.st0) this.clearEsignInterval(this.st0); //清除定时器
  }
};
</script>

<style lang="scss" scoped>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.map-box {
  position: relative;
}

.search-box {
  position: absolute !important;
  top: 10px;
  left: 10px;
}

.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.red {
  color: red;
}

.green {
  color: green;
}

.blue {
  color: blue;
}

.prompt {
  padding: 10px;
}

.result {
  position: absolute;
  top: 0;
  left: 100%;
  width: 300px;
  /* height: 450px; */
  margin-left: 10px;
  background-color: #ffffff;
  border: 1px solid silver;
}

.result-list {
  display: flex;
  align-items: center;
  /* margin-bottom: 10px; */
  line-height: 1.6;
  overflow: hidden;
  cursor: pointer;
}

.result label {
  display: block;
  width: 19px;
  height: 33px;
  margin-right: 10px;
  line-height: 20px;
  text-align: center;
  font-size: 12px;
  color: #ffffff;
  background: url('https://webapi.amap.com/theme/v1.3/markers/n/mark_bs.png') no-repeat center/cover;
}

.result-list.active label {
  background: url('http://webapi.amap.com/theme/v1.3/markers/n/mark_r.png') no-repeat center/cover;
}

.list-right {
  flex: 1;
}

.result .name {
  font-size: 14px;
  color: #565656;
}

.result .address {
  color: #999;
}

.search-table {
  height: 380px;
  margin-bottom: 10px !important;
}

.search-table th {
  display: none;
}

.search-table td {
  padding: 5px 0 !important;
  border-bottom: none;
}

.el-vue-search-box-container {
  width: 90% !important;
}

.el-date-editor.el-input {
  width: 100% !important;
}

@media (max-width: 767px) {
  .el-message-box {
    width: 80% !important;
  }
}

.timeFn {
  position: relative;
}

.serchFn {
  position: absolute;
  right: 231px;
  top: 55px;
}

.el-step.is-vertical {
  ::v-deep .el-step__main {
    padding-bottom: 40px !important;
  }
}

.card {
  background-color: #fef0f0;
  color: #f56c6c;
  // background-color: #f4f4f5;
  // color: #909399;
  padding: 10px 15px;
  border-radius: 5px;
  &.success {
    background-color: #f0f9eb;
    color: #67c23a;
  }
}
.button-d {
  width: 100px;
}
.dialog-dialogDeleteVisible-title {
  font-size: 16px;
}
.dialog-dialogDeleteVisible-tip {
  font-size: 16px;
  margin: 20px 0 30px;
  text-align: center;
}
::v-deep.el-dialog__wrapper {
  text-align: center;
  white-space: nowrap;
  overflow: auto;
  &:after {
    content: '';
    display: inline-block;
    vertical-align: middle;
    height: 100%;
  }
  .el-dialog {
    margin: 30px auto !important;
    display: inline-block;
    vertical-align: middle;
    text-align: center;
    white-space: normal;
  }
}
.mt-2 {
  margin-top: 20px;
}
.mt-4 {
  margin-top: 40px;
}
.mt-6 {
  margin-top: 60px;
}
.QR_code {
  position: relative;
  width: 200px;
  height: 200px;
  margin: 20px auto;
  .QR_code_success {
    z-index: 1;
    width: 200px;
    height: 200px;
    position: absolute;
    top: 0;
    left: 0;
    background-color: rgba(255, 255, 255, 0.85);

    display: grid;
    grid-template-rows: repeat(auto-fit, 50px);
    align-content: center;
    align-items: center;
    justify-items: center;
    i {
      font-size: 35px;
      color: #67c23a;
    }

    &.deactive {
      display: none;
    }
  }
  img {
    width: 100%;
    height: 100%;
  }
}
.create-success {
  font-size: 60px;
  color: #20b759;
}
.info {
  color: #666;
  text-align: center;
}
.divider-dialog {
  :deep(.el-dialog__body) {
    padding: 0px 20px 30px;
  }
}

:deep(.el-divider--horizontal) {
  width: calc(100% + 40px);
  margin: 0 0 24px;

  position: relative;
  left: -20px;
}
</style>
