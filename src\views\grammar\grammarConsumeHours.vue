<template>
  <div class="app-container">
    <el-form :inline="true" class="container-card" label-width="110px" label-position="left">
      <el-form-item label="学段：">
        <el-select v-model="dataQuery.grammarType" value-key="value" placeholder="请选择" style="width: 200px" clearable>
          <el-option v-for="(item, index) in pharseType" :key="index" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="是否开通：">
        <el-select v-model="dataQuery.isEnable" value-key="value" placeholder="请选择" clearable>
          <el-option v-for="(item, index) in [
            { value: '1', label: '开通' },
            { value: '0', label: '关闭' }

          ]" :key="index" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="fetchData01()">搜索</el-button>
      </el-form-item>
    </el-form>
    <el-col :span="24" style="margin-bottom: 30px">
      <el-button type="success" icon="el-icon-plus" size="mini" @click="clickAdd">添加</el-button>
    </el-col>
    <div class="SearchForm">
      <!-- 表格 -->
      <el-table class="common-table" :data="tableData" v-loading="tableLoading" hstyle="width: 100%; margin-bottom: 20px"
        row-key="id" border default-expand-all :tree-props="{ list: 'children', hasChildren: 'true' }">
        <el-table-column prop="grammarType" label="学段" sortable></el-table-column>
        <el-table-column prop="grammarSchoolHours" label="消耗学时"></el-table-column>
        <el-table-column prop="id" label="操作" sortable>
          <template slot-scope="scope">
            <el-button type="success" size="mini" icon="el-icon-edit-outline" @click="openEdit(scope.row.id)">编辑
            </el-button>
            <el-button type="danger" size="mini" icon="el-icon-delete" @click="deleteDate(scope.row.id)">删除</el-button>
          </template>
        </el-table-column>
        <el-table-column prop="isEnable" label="状态" sortable></el-table-column>
      </el-table>
      <el-col :span="24" style="overflow-x: auto;" :xs="24"></el-col>
    </div>
    <el-col :span="24" style="overflow-x: auto;" :xs="24">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>
    <el-dialog :title="addOrUpdate ? '增加语法消耗配置' : '修改语法消耗配置'" :visible.sync="showEdit" width="45%"
      :close-on-click-modal="false" @close="closeEdit">
      <el-form :ref="addOrUpdate ? 'addGrammar' : 'updateGrammar'" :rules="rules"
        :model="addOrUpdate ? addGrammar : updateGrammar" label-position="right" label-width="140px" style="width: 100%">
        <el-form-item label="id" prop="id" v-show="false">
          <el-col :xs="24" :span="12">
            <el-input v-model="updateGrammar.id"></el-input>
          </el-col>
        </el-form-item>
        <el-form-item label="学段:" prop="grammarType">
          <el-col :xs="24" :span="18">
            <el-select style="width: 100%;" v-if="addOrUpdate" v-model="addGrammar.grammarType">
              <el-option v-for="(item, index) in pharseType" :key="index" :label="item.label" :value="item.value" />
            </el-select>
            <el-select style="width: 100%;" v-else v-model="updateGrammar.grammarType">
              <el-option v-for="(item, index) in pharseType" :key="index" :label="item.label" :value="item.value" />
            </el-select>
          </el-col>
        </el-form-item>
        <el-form-item label="消耗学时" prop="grammarSchoolHours">
          <el-col :xs="24" :span="12">
            <el-input v-if="addOrUpdate" v-model="addGrammar.grammarSchoolHours" type="number"></el-input>
            <el-input v-if="!addOrUpdate" v-model="updateGrammar.grammarSchoolHours" type="number"></el-input>
          </el-col>
        </el-form-item>
        <el-form-item label="状态:" prop="isEnable">
          <template>
            <el-radio v-if="addOrUpdate" v-model="addGrammar.isEnable" label="1">开通</el-radio>
            <el-radio v-if="!addOrUpdate" v-model="updateGrammar.isEnable" label="0">关闭</el-radio>
            <el-radio v-if="!addOrUpdate" v-model="updateGrammar.isEnable" label="1">开通</el-radio>
            <el-radio v-if="addOrUpdate" v-model="addGrammar.isEnable" label="0">关闭</el-radio>
          </template>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button v-if="addOrUpdate" size="mini" type="primary" @click="addDate('addGrammar')">增加</el-button>
        <el-button v-if="!addOrUpdate" size="mini" type="primary" @click="editConfig('updateGrammar')">修改</el-button>

        <el-button size="mini" @click="closeEdit">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import grammarConsumeHoursApi from '@/api/GrammarConsumeHours'
import {
  pageParamNames
} from '@/utils/constants'

export default {
  data() {
    return {
      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      addOrUpdate: false,
      rules: {
        grammarType: [{
          required: true,
          message: '必填',
          trigger: 'blur'
        }],
        grammarSchoolHours: [{
          required: true,
          message: '必填',
          trigger: 'blur'
        }],
        isEnable: [{
          required: true,
          message: '必填',
          trigger: 'blur'
        }]
      },
      dataQuery: {
        isEnable: '',
        grammarType: ''
      },
      pharseType: [],
      addGrammar: {},//增加语法消耗学时配置
      tableData: [], //表格数据
      showEdit: false, //编辑弹窗
      updateGrammar: {} // 修改数据
    }
  },
  created() {
    this.getSelect()
    this.fetchData01()
  },
  methods: {
    fetchData01() {
      this.tablePage = {
        currentPage: 1,
        size: this.tablePage.size,
        totalPage: null,
        totalItems: null
      }
      this.fetchData()
    },
    // 查询表格列表
    fetchData() {
      const that = this
      that.tableLoading = true
      grammarConsumeHoursApi.getList(that.tablePage.currentPage, that.tablePage.size, that.dataQuery).then((res) => {
        that.tableData = res.data.data
        that.tableLoading = false
        pageParamNames.forEach((name) =>
          that.$set(that.tablePage, name, parseInt(res.data[name]))
        )
      }, s => {
        if (s === 'error') {
          that.tableLoading = false
        }
      })
    },
    //获取学段
    getSelect() {
      grammarConsumeHoursApi.getSelect().then(res => {
        console.log(res.data)
        this.pharseType = res.data
      })
    },
    // 分页
    handleSizeChange(val) {
      console.log(val)
      this.tablePage.size = val
      this.fetchData()
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val
      this.fetchData()
    },
    //增加按钮
    clickAdd() {
      const that = this
      that.showEdit = true
      that.addOrUpdate = true
      that.addGrammar = {
        grammarType: '',
        grammarSchoolHours: '',
        isEnable: '1',
        id: ''
      }
    },
    //增加提交到后台
    addDate(ele) {
      const that = this
      that.$refs[ele].validate((valid) => {
        // 表单验证
        if (valid) {
          const loading = this.$loading({
            lock: true,
            text: '提交语法消费学时配置',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          })
          grammarConsumeHoursApi.addGrammarConsumeHours(that.addGrammar).then(() => {
            that.showEdit = false
            loading.close()
            that.$nextTick(() => that.fetchData())
            that.$message.success('语法消费学时配置')
          }, s => {
            if (s == 'error') {
              loading.close()
            }
          })
            .catch((err) => {
              // 关闭提示弹框
              loading.close()
            })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    //编辑回显
    openEdit(id) {
      grammarConsumeHoursApi.editEcho(id).then(res => {
        this.updateGrammar = res.data
      })
      this.showEdit = true
      this.addOrUpdate = false
    },
    //删除操作
    deleteDate(id) {
      const that = this
      this.$confirm('确定操作吗?', '删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          grammarConsumeHoursApi
            .deleteData(id)
            .then((res) => {
              that.$nextTick(() => that.fetchData())
              that.$message.success('删除成功!')
            })
            .catch((err) => {
            })
        })
        .catch((err) => {
        })
    },
    //修改提交后台
    editConfig(ele) {
      const that = this
      that.$refs[ele].validate((valid) => {
        // 表单验证
        if (valid) {
          const loading = this.$loading({
            lock: true,
            text: '修改语法包消耗学时配置',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          })
          grammarConsumeHoursApi.changeTheData(that.updateGrammar).then(() => {
            that.showEdit = false
            loading.close()
            that.$nextTick(() => that.fetchData())
            that.$message.success('修改语法消耗学时配置成功')
          }, s => {
            if (s === 'error') {
              loading.close()
            }
          })
            .catch((err) => {
              // 关闭提示弹框
              loading.close()
            })
        } else {
          console.log('error submit!!')
          // loading.close();
          return false
        }
      })
    },
    closeEdit() {
      this.showEdit = false
    }
  }
}
</script>

<style>
.lh36 {
  line-height: 36px;
  font-size: 14px;
}

.SearchForm {
  width: 100%;
  margin-bottom: 15px;
  background: #fff;
  border-radius: 6px;
  padding: 5px 0;
  padding-left: 5px;
  box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.2);
}

.btn-add {
  padding: 5px;
}

.red {
  color: red;
}

.green {
  color: green;
}

.course-table {
  text-align: center;
}

.course-table td,
.course-table th {
  padding: 5px 0;
  text-align: center;
}

.course-table button {
  padding: 2px;
}

.icon-stop {
  float: left;
  display: inline-block;
  width: 12px;
  height: 12px;
  background: url("../../icons/stop.png") no-repeat top center/contain;
}

.mt22 {
  margin-top: 22px;
}

.blue {
  margin-right: 50px;
  color: #409eff;
}

@media (max-width: 767px) {
  .el-message-box {
    width: 80% !important;
  }
}
</style>
