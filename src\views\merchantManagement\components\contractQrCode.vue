<template>
  <!-- 培训缴费签署合同二维码 -->
  <el-dialog center :close-on-click-modal="false" :close-on-press-escape="false" :visible.sync="dialogVisible" width="440px" @close="closeDialog">
    <div class="title" slot="title">
      <h4>请将以下二维码发送给创建的俱乐部负责人</h4>
      <h4>签署《培训费合同》</h4>
    </div>
    <div class="img-area" v-loading="contractEsignLoading">
      <img v-if="contractEsignCode" :src="contractEsignCode" alt="" class="qrcode" />
    </div>
    <span slot="footer" class="dialog-footer" v-if="!contractEsignLoading">
      <el-button type="primary" @click="getContractState">他/她已签署</el-button>
      <!-- <p class="toast">合同如已签署成功，请点击此按钮</p> -->
    </span>
  </el-dialog>
</template>

<script>
  import { mapGetters } from 'vuex';
  import schoolApi from '@/api/schoolList';
  import store from '@/store';
  import { dxSource } from '@/utils/constants';
  import { Base64 } from '@/utils/base64';
  export default {
    name: 'contractQrCode',
    props: {
      dialogVisible: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        contractProcessId: '', // 创建合同流程id
        contractEsignCode: '',
        contractEsignLoading: false,
        intervalId: null,
        storeId: '',
        token: store.getters.token,
        isInPayAction: false // 是否在培训缴费操作进行中
      };
    },
    computed: {
      ...mapGetters(['setpayUrl'])
    },
    watch: {
      isInPayAction(newVal) {
        this.$emit('changePayFlowStatus', newVal);
      }
    },
    methods: {
      closeDialog() {
        if (this.intervalId) {
          this.clearEsignInterval();
        }
        this.isInPayAction = false;
        this.contractEsignCode = '';
        const that = this;
        setTimeout(() => {
          if (that.intervalId) {
            that.clearEsignInterval();
            that.intervalId = null;
          }
          that.$emit('changePayFlowStatus', false);
          that.$emit('closeDialog');
        }, 200);
      },
      getEsignStatus() {
        // 获取合同签署状态(-1未创建，0未完成，1已完成)
        const that = this;
        if (that.isInPayAction) {
          schoolApi.getTrainingContractStatus(this.storeId).then((res) => {
            if (res.data.status == '1' && that.isInPayAction) {
              that.clearEsignInterval();
              that
                .$confirm('合同已签署完成，是否继续前往支付培训费?', '提示', {
                  confirmButtonText: '确定',
                  cancelButtonText: '取消',
                  type: 'success'
                })
                .then((res) => {
                  if (that.isInPayAction) {
                    // 跳转至商户平台
                    schoolApi
                      .getTrainingPayInfo(that.merchantCode)
                      .then((res) => {
                        const split = dxSource.split('##');
                        res.data.dxSource = res.data.registerCallIInfo.appSource + '##' + split[1] + '##' + split[2];
                        let params = JSON.stringify(res.data);
                        let req = 'token=' + this.token + '&params=' + params + '&back=' + window.location.href;
                        //需要编码两遍，避免出现+号等
                        var encode = Base64.encode(Base64.encode(req));
                        window.open(this.setpayUrl + 'product?' + encode, '_blank');
                        setTimeout(() => {
                          that.closeDialog();
                        }, 2000);
                      })
                      .finally(() => {
                        that.isInPayAction = false;
                      });
                  }
                })
                .catch((err) => {
                  that.closeDialog();
                });
            }
          });
        } else {
          that.clearEsignInterval();
        }
      },
      // 获取合同二维码及轮询时间
      getContractInfo(item, status) {
        this.storeId = item.id;
        this.merchantCode = item.merchantCode;
        this.contractEsignLoading = true;
        let that = this;
        // 培训缴费操作是否在进行中， true在，false不在
        if (!that.isInPayAction) {
          that.isInPayAction = true;
          that.clearEsignInterval();
        } else {
          return;
        }
        if (status === '-1') {
          // 未创建-获取培训费合同
          schoolApi.createTraingFeeCode(item.id).then((res) => {
            if (that.isInPayAction) {
              that.contractProcessId = res.data;
              setTimeout(() => {
                const params = {
                  id: item.id,
                  signFlowId: that.contractProcessId,
                  type: 0 // 是否为第一次创建，0是1否
                };
                schoolApi.getContractCode(params).then((res) => {
                  if (that.isInPayAction) {
                    that.contractEsignCode = res.data;
                    that.contractEsignLoading = false;
                    that.getPollingTime();
                  }
                });
              }, 2000);
            }
          });
        } else if (status === '0') {
          // 未完成-重新获取培训费合同
          schoolApi.reCreateTrainingFeeCode(item.id).then((res) => {
            if (that.isInPayAction) {
              let contractData = res.data;
              if (contractData instanceof Object && contractData.value) {
                if (contractData.type == '0') {
                  that.contractProcessId = contractData.value;
                  setTimeout(() => {
                    const params = {
                      id: item.id,
                      signFlowId: that.contractProcessId,
                      type: 1 // 是否为第一次创建，0是1否
                    };
                    schoolApi.getContractCode(params).then((res) => {
                      if (that.isInPayAction) {
                        that.contractEsignCode = res.data;
                        that.contractEsignLoading = false;
                        that.getPollingTime();
                      }
                    });
                  }, 2000);
                } else if (contractData.type == '1') {
                  that.contractEsignCode = contractData.value;
                  that.getPollingTime();
                }
              }
            }
          });
        }
      },
      // 获取轮询时间
      getPollingTime() {
        const that = this;
        if (that.isInPayAction) {
          schoolApi.getContractPolling().then((res) => {
            if (that.isInPayAction) {
              // 定时器轮询
              that.intervalId = setInterval(that.getEsignStatus, 5000);
              // 超时清除定时器
              let s = res.data.time * 60 * 1000;
              const timeoutId = setTimeout(() => {
                that.clearEsignInterval();
              }, s);
            }
          });
        } else {
          that.clearEsignInterval();
        }
      },
      clearEsignInterval() {
        clearInterval(this.intervalId);
        this.intervalId = null;
      },
      // 点击已签署
      getContractState() {
        const that = this;
        schoolApi.getTrainingContractStatus(this.storeId).then((res) => {
          if (res.data.status == 0) {
            that.$message.error('合同还未签署完成！');
          } else {
            schoolApi
              .getTrainingPayInfo(that.merchantCode)
              .then((res) => {
                const split = dxSource.split('##');
                res.data.dxSource = res.data.registerCallIInfo.appSource + '##' + split[1] + '##' + split[2];
                let params = JSON.stringify(res.data);
                let req = 'token=' + this.token + '&params=' + params + '&back=' + window.location.href;
                //需要编码两遍，避免出现+号等
                var encode = Base64.encode(Base64.encode(req));
                setTimeout(() => {
                  window.open(this.setpayUrl + 'product?' + encode, '_blank');
                }, 100);
              })
              .finally(() => {
                that.isInPayAction = false;
              });
          }
        });
      }
    },
    beforeDestroy() {
      if (this.intervalId) {
        clearInterval(this.intervalId);
        this.intervalId = null;
      }
    }
  };
</script>

<style lang="scss" scoped>
  .title {
    text-align: center;
  }
  h5 {
    margin-bottom: 0;
  }
  .img-area {
    width: 250px;
    height: 250px;
    // border: 1px solid #ccc;
    margin: 0 auto;
    .qrcode {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
  }
  .toast {
    font-size: 14px;
    color: #333;
  }
</style>
