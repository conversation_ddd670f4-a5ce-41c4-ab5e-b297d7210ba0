<template>
  <div class="app-container">
    <el-form :inline="true" class="container-card" label-width="110px" label-position="left">
      <el-row>
        <!-- <el-col :span="8" :xs="24">
          <el-form-item label="交易流水号：">
            <el-input id="flowCode" v-model="dataQuery.flowCode" name="id" placeholder="请输入交易流水号：" clearable />
          </el-form-item>
        </el-col> -->
        <el-col :span="8" :xs="24">
          <el-form-item label="商户编号：">
            <el-input id="merchantCode" v-model="dataQuery.merchantCode" name="id" placeholder="请输入商户编号：" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="商户名称：">
            <el-input id="merchantName" v-model="dataQuery.merchantName" name="id" placeholder="请输入服务商名称:" clearable />
          </el-form-item>
        </el-col>
      </el-row>


      <el-row>
        <el-col :span="12" :xs="24">
          <el-form-item>
            <el-form-item label="变动时间：">
              <el-date-picker style="width: 100%;" v-model="RegTime" type="daterange" value-format="yyyy-MM-dd HH:mm:ss"
                align="right" unlink-panels range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" clearable>
              </el-date-picker>
            </el-form-item>
          </el-form-item>
        </el-col>
        <el-col :span="4" :xs="24" style="text-align: right">
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="fetchData01()">搜索</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-form :inline="true" style="margin-bottom: 20px;">
      <!-- <el-button type="warning" icon="el-icon-document-copy" @click="exportFlow()"  v-loading="exportLoading ">导出</el-button> -->
    </el-form>
    <el-table class="period-table" v-loading="tableLoading" :data="tableData" style="width: 100%;margin-bottom: 20px;"
      row-key="id" border default-expand-all :tree-props="{ list: 'children', hasChildren: 'true' }">
      <el-table-column prop="flowCode" label="交易流水号" width="180" :show-overflow-tooltip="true"></el-table-column>
      <el-table-column prop="merchantCode" label="商户编号" width="100"></el-table-column>
      <el-table-column prop="merchantName" label="商户名称" width="200"></el-table-column>
      <el-table-column prop="roleTag" label="商户类型" width="120">
        <template slot-scope="scope">
          <span v-if="scope.row.roleTag === 'Dealer'"> 托管中心</span>
          <span v-if="scope.row.roleTag === 'School'"> 门店</span>
        </template>
      </el-table-column>
      <el-table-column prop="direction" label="变动方式">
        <template>
          <el-tag>
            入
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="accountsTypeName" label="变动类型">
        <template>
          <el-tag>
            充值资金
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="flowBeforeMoney" label="变动前金额（元）" width="150"></el-table-column>
      <el-table-column prop="flowMoney" label="变动金额（元）" width="150"></el-table-column>
      <el-table-column prop="flowAfterMoney" label="变动后金额（元）" width="150"></el-table-column>
      <el-table-column prop="addTime" label="变动时间" width="200" :show-overflow-tooltip="true"></el-table-column>
      <el-table-column prop="description" label="变动描述" width="400" :show-overflow-tooltip="true"></el-table-column>
    </el-table>
    <!-- 分页 -->
    <el-col :span="24" style="overflow-x: auto;" :xs="24">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>
    <!-- <el-col :span="24" class="mt20">
      本次总计 变动金额入：{{changeIn

      }}元，变动金额出：{{
changeOut
      }}元
    </el-col> -->
  </div>
</template>

<script>
import
merchantAccountFlowApi
  from "@/api/merchantAccountFlow";
import Tinymce from "@/components/Tinymce";
import enTypes from '@/api/bstatus'
import {
  pageParamNames
} from "@/utils/constants";
export default {
  name: 'merchantFlowList',
  data() {
    return {
      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      tableData: [{
        name: 1,
        title: '1111'
      }],
      changeOut: 0,//变动金额出
      changeIn: 0,//变动入
      dataQuery: {},
      dialogVisible: false,
      updateMarketDate: {},
      addMarketDate: {},
      showLoginAccount: false,
      financeAccountsType: [],//变动类型
      RegTime: '',
      exportLoading: false,//商户流水导出
    };
  },
  created() {
    this.fetchData();
    this.getFinanceAccountsType();
  },
  methods: {
    fetchData01() {
      this.tablePage = {
        currentPage: 1,
        size: this.tablePage.size,
        totalPage: null,
        totalItems: null
      }
      this.fetchData();
    },
    // 查询提现列表
    fetchData() {
      const that = this;
      that.tableLoading = true
      var a = that.RegTime;
      if (a != null) {
        that.dataQuery.startDate = a[0];
        console.log(that.dataQuery.startDate = a[0]);
        that.dataQuery.endDate = a[1];
      }

      merchantAccountFlowApi
        .merchantAccountFlowListDealer(
          that.tablePage.currentPage,
          that.tablePage.size,
          that.dataQuery
        )
        .then((res) => {
          that.tableData = res.data.data;
          that.tableLoading = false;
          // 设置后台返回的分页参数
          pageParamNames.forEach((name) =>
            that.$set(that.tablePage, name, parseInt(res.data[name]))
          );
        });
      merchantAccountFlowApi.merchantCapitalInquiry(that.tablePage.currentPage,
        that.tablePage.size,
        that.dataQuery).then((res) => {
          // console.log(res);
          that.changeOut = res.data.changeOut;
          that.changeIn = res.data.changeIn;
        })
    },
    //导出
    //导出
    exportFlow() {
      const that = this;
      that.exportLoading = true;
      //
      merchantAccountFlowApi.simpleMerchantExecl(that.dataQuery).then(res => {
        //           this.$notify.error({
        //               title: "操作失败",
        //               message: "文件下载失败"
        //             });
        const url = window.URL.createObjectURL(res);
        const link = document.createElement("a");
        link.style.display = "none";
        link.href = url; // 获取服务器端的文件名
        link.setAttribute("download", "资金商户流水.xls");
        document.body.appendChild(link);
        link.click();
        that.exportLoading = false;
      })
    },
    //新增操作
    clickAdd() {
      this.dialogVisible = true;
    },
    // 打开修改登陆账号
    openLogin() {
      this.showLoginAccount = true
    },
    closeLogin() {
      this.showLoginAccount = false
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val;
      this.fetchData();
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val;
      this.fetchData();
    },
    //获取变动类型
    getFinanceAccountsType() {
      var enType = "FinanceAccountsType";
      enTypes.getEnumerationAggregation(enType).then(res => {
        this.financeAccountsType = res.data;
      })
    },

  }
};
</script>

<style>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.period-table td,
.period-table th {
  text-align: center;
}

.mt20 {
  margin-top: 20px;
}</style>
