/**
 * 托管中心级别列表相关接口
 */
import request from '@/utils/request'

export default {
  // 托管中心级别分页查询
  dealerRankList(pageNum, pageSize) {
    return request({
      url: '/znyy/dealer/rank/list/' + pageNum + '/' + pageSize,
      method: 'GET'
    })
  },
  // 托管中心级别新增
  addDealerLRankist(data) {
    return request({
      url: '/znyy/dealer/rank',
      method: 'POST',
      data
    })
  },
  //获取托管中心级别
  getSelectResult() {
    return request({
        url: '/znyy/dealer/rank',
        method:'GET'
    })
  },
  // 托管中心级别编辑
  updateDealerRank(data) {
    return request({
      url: '/znyy/dealer/rank',
      method: 'PUT',
      data
    })
  },
  // 托管中心级别查询
  queryActive(id) {
    return request({
      url: '/znyy/dealer/rank/getDealerRank/' + id,
      method: 'GET'
    })
  },
  //删除托管中心级别
  deleteDealerRankList(id){
   return request({
       url:'/znyy/dealer/rank/'+id,
       method: 'DELETE'
   })
  },
  getCurrentTime() {
    var _this = this;
    let yy = new Date().getFullYear();
    let mm = new Date().getMonth() + 1;
    let dd = new Date().getDate();
    let hh = new Date().getHours();
    let mf = new Date().getMinutes() < 10 ? '0' + new Date().getMinutes() : new Date().getMinutes();
    let ss = new Date().getSeconds() < 10 ? '0' + new Date().getSeconds() : new Date().getSeconds();
    _this.gettime = yy + '-' + mm + '-' + dd + ' ' + hh + ':' + mf + ':' + ss;
    return _this.gettime
  }
}
