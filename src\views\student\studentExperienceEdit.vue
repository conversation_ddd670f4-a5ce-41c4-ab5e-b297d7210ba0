<template>
  <div class="app-container">
    <el-row>
      <el-col :xs="24" :lg="18">
        <!-- 添加或修改弹窗 -->
        <el-form
          :ref="addOrUpdate ? 'addMarketDate' : 'updateMarketDate'"
          :rules="rules"
          :model="addOrUpdate ? addMarketDate : updateMarketDate"
          label-position="right"
          label-width="140px"
          style="width: 100%"
        >

          <el-form-item label="姓名：">
            <el-col :xs="24" :span="18">
              <el-input v-model="updateMarketDate.realName" disabled/>
            </el-col>
          </el-form-item>


          <el-form-item label="学校：">
            <el-col :xs="24" :span="18">
              <el-input v-model="updateMarketDate.school" disabled/>
            </el-col>
          </el-form-item>

          <el-form-item label="年级：">
            <el-col :xs="24" :span="18">
              <el-input v-model="updateMarketDate.grade" disabled/>
            </el-col>
          </el-form-item>
          <el-form-item label="体验课程：">
            <el-col :xs="24" :span="18">
              <el-input v-model="updateMarketDate.experienceCourse" disabled/>
            </el-col>
          </el-form-item>
          <el-form-item label="体验开始时间：">
            <el-col :xs="24" :span="18">
              <el-input v-model="updateMarketDate.startTime" disabled/>
            </el-col>
          </el-form-item>
          <el-form-item label="体验结束时间：">
            <el-col :xs="24" :span="18">
              <el-input v-model="updateMarketDate.endTime" disabled/>
            </el-col>
          </el-form-item>

          <el-form-item label="学习经历：" prop="skill">
            <el-col :xs="24" :span="18">
              <el-input
                type="textarea"
                resize="none"
                :rows="4" v-model="updateMarketDate.skill"
              />
            </el-col>
          </el-form-item>

          <el-form-item label="学习情况反馈：" prop="feedback">
            <el-col :xs="24" :span="18">
              <el-input
                type="textarea"
                resize="none"
                :rows="4" v-model="updateMarketDate.feedback"
              />
            </el-col>
          </el-form-item>

          <el-form-item label="学习方案：" >
            <el-checkbox-group v-model="checkListXiaoList">
              <el-checkbox label="小学"></el-checkbox>
              <el-checkbox label="单词"></el-checkbox>
              <el-checkbox label="阅读"></el-checkbox>
              <el-checkbox label="语法"></el-checkbox>
            </el-checkbox-group>
            <el-checkbox-group v-model="checkListChuList">
              <el-checkbox label="初中"></el-checkbox>
              <el-checkbox label="单词"></el-checkbox>
              <el-checkbox label="阅读"></el-checkbox>
              <el-checkbox label="语法"></el-checkbox>
            </el-checkbox-group>
            <el-checkbox-group v-model="checkListGaoList">
              <el-checkbox label="高中"></el-checkbox>
              <el-checkbox label="单词"></el-checkbox>
              <el-checkbox label="阅读"></el-checkbox>
              <el-checkbox label="语法"></el-checkbox>
            </el-checkbox-group>
          </el-form-item>



        </el-form>
      </el-col>
    </el-row>

    <div slot="footer" class="dialog-footer" style="margin: 30px 0 30px 140px">
<!--      <el-button-->
<!--        v-if="!addOrUpdate && ((updateMarketDate.isCheck == 0 || updateMarketDate.isCheck == -4 ||updateMarketDate.isCheck == 2)||(merchantCode='A0001'))"-->
      <el-button
        size="mini"
        type="primary"
        @click="updateActiveFun('updateMarketDate')">修改
      </el-button>
      <el-button size="mini" @click="close">关闭</el-button>
    </div>

  </div>
</template>

<script>

import schoolApi from "@/api/areasSchoolList";
import {isvalidPhone, idCard} from "@/utils/validate";
import studentExperienceApi from "@/api/areasStudentExperienceList.js";

export default {

  data() {

    //手机号验证
    var validPhone = (rule, value, callback) => {
      if (!value) {
        callback(new Error("请输入电话号码"));
      } else if (value.length < 11 || value.length > 11) {
        callback(new Error("请输入正确的11位手机号码"));
      } else {
        callback();
      }
    };
    //身份证表达式
    var isIdCard = (rule, value, callback) => {
      if (!value) {
        callback(new Error("请输入身份证号码"));
      } else if (!idCard(value)) {
        callback(new Error("请输入正确的18位身份证号"));
      } else {
        callback();
      }
    };
    const self = this;
    return {
      checkListXiaoList: [],
      checkListChuList: [],
      checkListGaoList: [],
      // 地图搜索分页相关参数
      pageNum: 1,
      pageSize: 5,
      result: [],
      currentResult: -1,

      disabled: true,
      tableLoading: false,
      showLoginName: false, //登录账号
      updateLoginName: {}, //修改账号
      rulesLoginName: {
        name: [
          {
            required: true,
            message: "必填",
            trigger: "change",
          },
          {
            validator: validPhone,
            trigger: "blur",
          },
        ],
      },
      id: 0,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null,
      },
      ADD: true,
      tableData: [],
      dataQuery: {},

      updateMarketDate: {
        areaCoverRange: 500,
        merchantType: "3",
      }, //修改数据对象
      addMarketDate: {
        areaCoverRange: 500,
        merchantType: "3",

      }, //增加数据对象
      showLoginAccount: false,
      addOrUpdate: true,
      rules: {
        program: [
          {
            required: true,
            message: "必填",
            trigger: "blur",
          },
        ],
        feedback: [
          {
            required: true,
            message: "必填",
            trigger: "blur",
          },
        ],
        skill: [
          {
            required: true,
            message: "必填",
            trigger: "blur",
          },
        ],

      },
      value1: "",
      raio: "3",
      name: "",
      dialogUploadVisible: false,
      dialogImageUrl: "",


      filelistShop: [],

      merchantCode: "",


      currentWindow: {
        position: [0, 0],
        content: "",
        events: {},
        visible: false,
      },

      markers: [],
      //搜索结果标注
      markers2: [],
      windows: [],
      window: "",
      zoom: 12,
      lng: 0,
      lat: 0,
      address: "",
      province: "",
      city: "",
      district: "",
      loaded: false,

    };
  },
  created() {
    this.addOrUpdate = this.$route.query.addOrUpdate;

    this.getRoleTag();
    //编辑回显
    this.updateDealer();

  },

  mounted() {
  },
  methods: {

    //编辑回显
    updateDealer() {
      const that = this;
      that.id = window.localStorage.getItem("experienceId");
      studentExperienceApi.editDetail(that.id).then((res) => {
        console.log(res);
        that.updateMarketDate = res.data;
        console.log(that.updateMarketDate.programXiao)
        if (that.updateMarketDate.programXiao!=null){
          that.checkListXiaoList=that.updateMarketDate.programXiaoList
        }
        if (that.updateMarketDate.programChu!=null){
          that.checkListChuList=that.updateMarketDate.programChuList
        }
        if (that.updateMarketDate.programGao!=null){
          that.checkListGaoList=that.updateMarketDate.programGaoList
        }
      });

    },

    addMarker: function () {
      let lng = 121.5 + Math.round(Math.random() * 1000) / 10000;
      let lat = 31.197646 + Math.round(Math.random() * 500) / 10000;
      this.markers.push([lng, lat]);
    },


    close() {
      const that = this;
      that.$store.dispatch("delVisitedViews", this.$route);
      that.$router.go(-1);
      that.addMarketDate = {};
      that.updateMarketDate = {};
      that.addOrUpdate = true;
      that.$refs.clearupload.clearFiles();
    },


    getRoleTag() {
      schoolApi.getCurrentAdmin().then((res) => {
        console.log(res.data.merchantCode + "wyy");
        this.roleTag = res.data.roleTag;
        this.merchantCode = res.data.merchantCode;
      });
    },

    //修改操作
    updateActiveFun(ele) {

      const that = this;

      if (that.updateMarketDate.skill == "") {
        that.$message.error("学习经历不能为空");
        return false;
      }
      if (that.updateMarketDate.feedback == "") {
        that.$message.error("学习情况反馈不能为空");
        return false;
      }

      that.updateMarketDate.checkListXiaoList=this.checkListXiaoList;
      that.updateMarketDate.checkListChuList=this.checkListChuList;
      that.updateMarketDate.checkListGaoList=this.checkListGaoList;

        studentExperienceApi
          .addOrUpdate(that.updateMarketDate)
          .then((res) => {


            that.$router.push({
              path: "/student/areasStudentExperience",
            });
            that.$message.success("修改成功");
          }, s => {
            if (s == 'error') {
              loading.close();
            }
          })
          .catch((err) => {
            loading.close();
          });

    },


    req_post(val) {
      console.log(val);
    },


    changeSizeHandler(size) {
      this.pageSize = size;
    },
    handleCurrentChange(currentPage) {
      this.pageNum = currentPage;
    },
  },
};
</script>

<style>
.map-box {
  position: relative;
}

.search-box {
  position: absolute !important;
  top: 10px;
  left: 10px;
}

.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.red {
  color: red;
}

.green {
  color: green;
}

.blue {
  color: blue;
}

.prompt {
  padding: 10px;
}

.result {
  position: absolute;
  top: 0;
  left: 100%;
  width: 300px;
  /* height: 450px; */
  margin-left: 10px;
  background-color: #ffffff;
  border: 1px solid silver;
}

.result-list {
  display: flex;
  align-items: center;
  /* margin-bottom: 10px; */
  line-height: 1.6;
  overflow: hidden;
  cursor: pointer;
}

.result label {
  display: block;
  width: 19px;
  height: 33px;
  margin-right: 10px;
  line-height: 20px;
  text-align: center;
  font-size: 12px;
  color: #ffffff;
  background: url("https://webapi.amap.com/theme/v1.3/markers/n/mark_bs.png") no-repeat center/cover;
}

.result-list.active label {
  background: url("http://webapi.amap.com/theme/v1.3/markers/n/mark_r.png") no-repeat center/cover;
}

.list-right {
  flex: 1;
}

.result .name {
  font-size: 14px;
  color: #565656;
}

.result .address {
  color: #999;
}

.search-table {
  height: 380px;
  margin-bottom: 10px !important;
}

.search-table th {
  display: none;
}

.search-table td {
  padding: 5px 0 !important;
  border-bottom: none;
}

.el-vue-search-box-container {
  width: 90% !important;
}

.el-date-editor.el-input {
  width: 100% !important;
}

@media screen and (max-width: 767px) {
  .app-container {
    /* padding: 20px 10px; */
  }

  .result {
    display: none;
  }
}
</style>
