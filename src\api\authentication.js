/**
 * 实名认证相关接口
 */
import request from '@/utils/request'

export default {
  // 新增
  addAgent(data) {
    return request({
      url: '/znyy/agent',
      method: 'POST',
      data
    })
  },
  // 修改回显
  echoAgent(id) {
    return request({
      url: '/znyy/agent/modifyEcho/' + id,
      method: 'PUT'
    })
  },
  // 编辑
  updateAgent(data) {
    return request({
      url: '/znyy/agent/updateContent',
      method: 'PUT',
      data
    })
  },
  // 获取银行分类列表
  categoryType(bankType) {
    return request({
      url: '/znyy/bvstatus/' + bankType,
      method: 'GET'
    })
  },
  // 加载符近的托管中心
  loadOtherExperienceStores(lat, lon) {
    return request({
      url: '/znyy/dealer/load/dealer?lat=' + lat + '&lon=' + lon,
      method: 'GET'
    })
  },

  //实名认证创建会员
createMember(type){
  return request({
    url: '/znyy/bSysConfig/verified/createMember/'+type,
    method: 'POST'
  })
},

//发送短信
sendSmg(mobile){
  return request({
    url: '/znyy/bSysConfig/sendMessages/'+ mobile,
    method: 'GET'
  })
},
//获取实名认证状态
queryYstMemberInfo(){
  return request({
    url: '/znyy/bSysConfig/query/certification/info',
    method: 'GET'
  })
},
//个人实名认证
personalRealNameAuthentication(data){
  return request({
    url: '/znyy/bSysConfig/personalRealNameAuthentication',
    method: 'POST',
    data
  })
},
//个人绑定手机号
individualBindingMobilePhoneNumber(code,phone){
  return request({
    url: '/znyy/bSysConfig/bingPhone?code=' + code + '&phone=' + phone,
    method: 'GET'
  })
}
,
//请求绑定银行卡

requestToBindBankCard(backCard,phone){
  return request({
    url: '/znyy/bSysConfig/apply/bind/bank/card?backCard=' + backCard + '&phone=' + phone,
    method: 'GET'
  })
},
//企业实名认证
enterpriseRealNameCertification(data){
  return request({
    url: '/znyy/bSysConfig/set/company/info',
    method: 'POST',
    data
  })
},
//确定绑定银行卡
confirmBindingBankCard(cardNo,phone,smsCode){
  return request({
    url: '/znyy/bSysConfig/confirm/bind/bank/info?cardNo=' + cardNo + '&phone=' + phone+ '&smsCode=' + smsCode,
    method: 'GET'
  })
},
//电子签约

electronicContract(){
  return request({
    url: '/znyy/bSysConfig/sing/contract',
    method: 'GET'
  })
},
//获取会员信息
getMemberInformation(){
  return request({
    url: '/znyy/bSysConfig/getMemberInformation',
    method: 'GET'
  })
},
//获取会员审核信息
getReviewInformation(){
  return request({
    url: '/znyy/bSysConfig/getReviewInformation',
    method: 'GET'
  })
},
//查看余额
checkAccountBalance(){
  return request({
    url: '/znyy/bSysConfig/checkAccountBalance',
    method: 'GET'
  })
},
//获取当前登录角色
  getLoginRoleTag(){
    return request({
      url: '/znyy/bSysConfig/getLoginRoleTag',
      method: 'GET'
    })
  }
}
