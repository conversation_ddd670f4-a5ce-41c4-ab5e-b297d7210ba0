<template>
  <div class="app-container totalBox">
    <div class="container_box">
      <el-button type="success" style="margin-bottom: 10px" @click="goBack" icon="el-icon-back" size="mini">返回课程列表</el-button>
      <el-table :data="tableData" class="common-table" stripe border v-loading="tableLoading">
        <el-table-column prop="courseCode" label="课程ID" width="160" sortable></el-table-column>
        <el-table-column prop="studyUrl" label="学习视频" width="160" sortable>
          <template>
            <div class="video_box" v-if="videoForm.studyUrl">
              <div v-if="!imageForm[5].studyUrl || !imageForm[5].status">
                <el-image style="width: 148px; height: 148px; background: #f5f7fa">
                  <div slot="error" class="course_image_slot">
                    <i class="el-icon-picture-outline"></i>
                    <span>审核中...</span>
                  </div>
                </el-image>
              </div>
              <div v-if="imageForm[5].studyUrl && imageForm[5].status" @click="previewVideo('studyUrl')">
                <el-image :src="imageForm[5].studyUrl" style="width: 148px; height: 148px" fit="cover "></el-image>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="baseUrl" label="学前视频" width="160" sortable>
          <template>
            <div class="video_box" v-if="videoForm.baseUrl">
              <div v-if="!imageForm[0].baseUrl || !imageForm[0].status">
                <el-image style="width: 148px; height: 148px; background: #f5f7fa">
                  <div slot="error" class="course_image_slot">
                    <i class="el-icon-picture-outline"></i>
                    <span>审核中...</span>
                  </div>
                </el-image>
              </div>
              <div v-if="imageForm[0].baseUrl && imageForm[0].status" @click="previewVideo('baseUrl')">
                <el-image :src="imageForm[0].baseUrl" style="width: 148px; height: 148px" fit="cover "></el-image>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="vowelUrl" label="元音视频" width="160" sortable>
          <template slot-scope="scope">
            <div v-if="scope.row.vowelUrl">
              <div v-if="(videoForm.vowelUrl && !imageForm[1].vowelUrl) || !imageForm[1].status">
                <el-image style="width: 148px; height: 148px; background: #f5f7fa">
                  <div slot="error" class="course_image_slot">
                    <i class="el-icon-picture-outline"></i>
                    <span>审核中...</span>
                  </div>
                </el-image>
              </div>
              <div v-if="imageForm[1].vowelUrl && imageForm[1].status" @click="previewVideo('vowelUrl')">
                <el-image :src="imageForm[1].vowelUrl" style="width: 148px; height: 148px" fit="cover "></el-image>
              </div>
              <!-- <el-image :src="scope.row.vowelUrl" style="width: 148px; height: 148px"  fit="cover "></el-image> -->
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="consonantUrl" label="辅音视频" width="160" sortable>
          <template slot-scope="scope">
            <div v-if="scope.row.consonantUrl">
              <div v-if="(videoForm.consonantUrl && !imageForm[2].consonantUrl) || !imageForm[2].status">
                <el-image style="width: 148px; height: 148px; background: #f5f7fa">
                  <div slot="error" class="course_image_slot">
                    <i class="el-icon-picture-outline"></i>
                    <span>审核中...</span>
                  </div>
                </el-image>
              </div>
              <div v-if="imageForm[2].consonantUrl && imageForm[2].status" @click="previewVideo('consonantUrl')">
                <el-image :src="imageForm[2].consonantUrl" style="width: 148px; height: 148px" fit="cover "></el-image>
              </div>
              <!-- <el-image :src="scope.row.consonantUrl" style="width: 148px; height: 148px"  fit="cover "></el-image> -->
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="consonantUrl" label="总结视频" width="160" sortable>
          <template slot-scope="scope">
            <div v-if="scope.row.summaryUrl">
              <div v-if="(videoForm.summaryUrl && !imageForm[3].summaryUrl) || !imageForm[3].status">
                <el-image style="width: 148px; height: 148px; background: #f5f7fa">
                  <div slot="error" class="course_image_slot">
                    <i class="el-icon-picture-outline"></i>
                    <span>审核中...</span>
                  </div>
                </el-image>
              </div>
              <div v-if="imageForm[3].summaryUrl && imageForm[3].status" @click="previewVideo('summaryUrl')">
                <el-image :src="imageForm[3].summaryUrl" style="width: 148px; height: 148px" fit="cover "></el-image>
              </div>
              <!-- <el-image :src="scope.row.summaryUrl" style="width: 148px; height: 148px"  fit="cover "></el-image> -->
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="consonantUrl" label="复习视频" width="160" sortable>
          <template slot-scope="scope">
            <div v-if="scope.row.reviewUrl">
              <div v-if="(videoForm.reviewUrl && !imageForm[4].reviewUrl) || !imageForm[4].status">
                <el-image style="width: 148px; height: 148px; background: #f5f7fa">
                  <div slot="error" class="course_image_slot">
                    <i class="el-icon-picture-outline"></i>
                    <span>审核中...</span>
                  </div>
                </el-image>
              </div>
              <div v-if="imageForm[4].reviewUrl && imageForm[4].status" @click="previewVideo('reviewUrl')">
                <el-image :src="imageForm[4].reviewUrl" style="width: 148px; height: 148px" fit="cover "></el-image>
              </div>
              <!-- <el-image :src="scope.row.reviewUrl" style="width: 148px; height: 148px"  fit="cover "></el-image> -->
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="id" label="操作" sortable>
          <template>
            <el-button v-if="courseType === '1' || courseLevel == 2" type="primary" size="mini" icon="el-icon-edit" @click="addVideo">编辑视频</el-button>
            <el-button type="primary" size="mini" icon="el-icon-edit" @click="getList('0')">元辅音</el-button>
            <el-button type="primary" size="mini" icon="el-icon-edit" @click="getList('1')">音节</el-button>
            <el-button v-if="courseType === '1'" type="primary" size="mini" icon="el-icon-edit" @click="getList('2')">划音节</el-button>
            <el-button type="primary" size="mini" icon="el-icon-edit" @click="getList('3')">拼读拼写</el-button>
            <el-button type="primary" size="mini" icon="el-icon-edit" @click="getList('4')">上传音频</el-button>
            <el-button type="primary" size="mini" icon="el-icon-edit" @click="getList('5')">同步词库</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div v-if="couresListShow === '0'">
      <div class="item_list">元辅音列表</div>
      <VowelList :courseCode="courseCode" :id="id"></VowelList>
    </div>
    <div v-if="couresListShow === '1'">
      <div class="item_list">音节列表</div>
      <SyllableList :courseCode="courseCode" :id="id"></SyllableList>
    </div>
    <div v-if="couresListShow === '2'">
      <div class="item_list">划音节列表</div>
      <DrawSyllableList></DrawSyllableList>
    </div>
    <div v-if="couresListShow === '3'">
      <!-- 正式课 -->
      <div v-if="courseType === '1'">
        <div class="item_list">拼读拼写列表</div>
        <SpellList></SpellList>
      </div>
      <!-- 体验课 -->
      <div v-if="courseType === '0'">
        <div class="item_list">拼读拼写列表</div>
        <ExperienceSpellList></ExperienceSpellList>
      </div>
    </div>
    <div v-if="couresListShow === '4'">
      <div class="item_list">上传音频</div>
      <UploadAudio :courseType="courseType"></UploadAudio>
    </div>
    <!-- 有划拼音-拼读拼写弹窗 -->
    <div v-if="couresListShow === '3' && spellStepDialogVisible">
      <HasSpellStepForm :spellStepDialogVisible="spellStepDialogVisible" @spellDialogClose="spellDialogClose"></HasSpellStepForm>
    </div>
    <div v-if="couresListShow === '5'">
      <div class="item_list">同步词库列表</div>
      <SynchronousVocabulary :courseCode="courseCode" />
    </div>
    <!-- 编辑视频 -->
    <el-dialog title="编辑" :visible.sync="videoDialogVisible" width="70%" :close-on-click-modal="false" @close="dialogClose">
      <el-form
        ref="videoForm"
        :model="videoForm"
        :rules="courseType === '1' ? videoRules : courseLevel == 2 ? typeRules : ''"
        label-position="left"
        label-width="120px"
        style="width: 100%"
      >
        <!-- <el-button type="button" id="upload" @click="getUpload" value="上传">上传</el-button>  -->
        <el-form-item label="学习视频" prop="studyUrl" v-if="courseType == '0'">
          <div style="position: relative">
            <input
              type="file"
              style="max-width: 200px"
              class="upload"
              accept="video/mp4,video/webm,video/ogg"
              @change="doUpload('studyUrl', 'studyUrlInputer', 5)"
              ref="studyUrlInputer"
              :disabled="courseLevel != 2 || videoForm.studyUrl ? 'disabled' : null"
              multiple
            />
            <el-button type="danger" size="small" @click="delBaseUrl('studyUrl', 'studyUrlInputer', 5)" v-if="imageForm[5].id || videoForm.studyUrl">删除</el-button>
            <div style="position: absolute; background-color: transparent; width: 130px; height: 21px; left: 70px; top: 6px"></div>
            <!-- 禁止点击↑文件名称 -->
          </div>
          <!-- <el-button type="primary" size="small" @click="startBaseUrl('baseUrl',0)" v-if="imageForm[0].id || videoForm.baseUrl">上传</el-button> -->
          <div v-if="videoForm.studyUrl && !imageForm[5].studyUrl">
            <div
              style="width: 148px; height: 148px; background: #f5f7fa; display: flex; align-items: center; justify-content: center"
              v-if="(imageForm[5].progress || imageForm[5].progress === 0) && imageForm[5].progress < 101 && !imageForm[5].failed"
            >
              <el-progress :width="100" type="circle" :percentage="imageForm[5].progress"></el-progress>
            </div>
            <div v-else style="width: 148px; height: 148px; background: #f5f7fa; display: flex; align-items: center; justify-content: center">
              <div v-if="imageForm[5].failed">上传失败，点击删除后重新上传</div>
              <el-image v-else style="width: 148px; height: 148px; background: #f5f7fa">
                <div slot="error" class="course_image_slot">
                  <i class="el-icon-picture-outline"></i>
                  <span>审核中...</span>
                </div>
              </el-image>
            </div>
          </div>
          <div v-if="imageForm[5].studyUrl">
            <el-image :src="imageForm[5].studyUrl" style="width: 148px; height: 148px" fit="cover "></el-image>
            <div>{{ imageForm[5].title }}</div>
          </div>
        </el-form-item>
        <el-form-item label="学前视频" prop="baseUrl">
          <div style="position: relative">
            <input
              type="file"
              class="upload"
              style="max-width: 200px"
              accept="video/mp4,video/webm,video/ogg"
              @change="doUpload('baseUrl', 'baseUrlInputer', 0)"
              ref="baseUrlInputer"
              :disabled="!(courseType === '1') ? 'disabled' : null"
              multiple
            />
            <el-button type="danger" size="small" @click="delBaseUrl('baseUrl', 'baseUrlInputer', 0)" v-if="imageForm[0].id || videoForm.baseUrl">删除</el-button>
            <div style="position: absolute; background-color: transparent; width: 130px; height: 21px; left: 70px; top: 6px"></div>
          </div>
          <!-- <el-button type="primary" size="small" @click="startBaseUrl('baseUrl',0)" v-if="imageForm[0].id || videoForm.baseUrl">上传</el-button> -->
          <div v-if="videoForm.baseUrl && !imageForm[0].baseUrl">
            <div
              style="width: 148px; height: 148px; background: #f5f7fa; display: flex; align-items: center; justify-content: center"
              v-if="(imageForm[0].progress || imageForm[0].progress === 0) && imageForm[0].progress < 101 && !imageForm[0].failed"
            >
              <el-progress :width="100" type="circle" :percentage="imageForm[0].progress"></el-progress>
            </div>
            <div v-else style="width: 148px; height: 148px; background: #f5f7fa; display: flex; align-items: center; justify-content: center">
              <div v-if="imageForm[0].failed">上传失败，点击删除后重新上传</div>
              <el-image v-else style="width: 148px; height: 148px; background: #f5f7fa">
                <div slot="error" class="course_image_slot">
                  <i class="el-icon-picture-outline"></i>
                  <span>审核中...</span>
                </div>
              </el-image>
            </div>
          </div>
          <div v-if="imageForm[0].baseUrl">
            <el-image :src="imageForm[0].baseUrl" style="width: 148px; height: 148px" fit="cover "></el-image>
            <div>{{ imageForm[0].title }}</div>
          </div>
        </el-form-item>
        <el-form-item label="元音视频" prop="vowelUrl">
          <div v-if="imageForm[1].status === 61 || imageForm[1].status === 60" id="vowelUrlPlayer"></div>
          <div style="position: relative">
            <input
              type="file"
              class="upload"
              style="max-width: 200px"
              accept="video/mp4,video/webm,video/ogg"
              @change="doUpload('vowelUrl', 'vowelInputer', 1)"
              :disabled="!(courseType === '1') ? 'disabled' : null"
              ref="vowelInputer"
              multiple
            />
            <el-button type="danger" size="small" @click="delBaseUrl('vowelUrl', 'vowelInputer', 1)" v-if="imageForm[1].id || videoForm.vowelUrl">删除</el-button>
            <div style="position: absolute; background-color: transparent; width: 130px; height: 21px; left: 70px; top: 6px"></div>
          </div>
          <!-- <el-button type="primary" size="small" @click="startBaseUrl('vowelUrl',1)" v-if="imageForm[1].id || videoForm.vowelUrl">上传</el-button> -->
          <div v-if="videoForm.vowelUrl && !imageForm[1].vowelUrl">
            <div
              style="width: 148px; height: 148px; background: #f5f7fa; display: flex; align-items: center; justify-content: center"
              v-if="(imageForm[1].progress || imageForm[1].progress === 0) && imageForm[1].progress < 101 && !imageForm[1].failed"
            >
              <el-progress :width="100" type="circle" :percentage="imageForm[1].progress"></el-progress>
            </div>
            <div v-else style="width: 148px; height: 148px; background: #f5f7fa; display: flex; align-items: center; justify-content: center">
              <div v-if="imageForm[1].failed">上传失败，点击删除后重新上传</div>
              <el-image v-else style="width: 148px; height: 148px; background: #f5f7fa">
                <div slot="error" class="course_image_slot">
                  <i class="el-icon-picture-outline"></i>
                  <span>审核中...</span>
                </div>
              </el-image>
            </div>
          </div>
          <div v-if="imageForm[1].vowelUrl">
            <el-image :src="imageForm[1].vowelUrl" style="width: 148px; height: 148px" fit="cover "></el-image>
            <div>{{ imageForm[1].title }}</div>
          </div>
        </el-form-item>
        <el-form-item label="辅音视频" prop="consonantUrl">
          <div v-if="imageForm[0].status === 61 || imageForm[0].status === 60" id="consonantUrlPlayer"></div>
          <div style="position: relative">
            <input
              type="file"
              class="upload"
              style="max-width: 200px"
              accept="video/mp4,video/webm,video/ogg"
              @change="doUpload('consonantUrl', 'consonantInputer', 2)"
              :disabled="!(courseType === '1') ? 'disabled' : null"
              ref="consonantInputer"
              multiple
            />
            <el-button type="danger" size="small" @click="delBaseUrl('consonantUrl', 'consonantInputer', 2)" v-if="imageForm[2].id || videoForm.consonantUrl">删除</el-button>
            <div style="position: absolute; background-color: transparent; width: 130px; height: 21px; left: 70px; top: 6px"></div>
          </div>
          <!-- <el-button type="primary" size="small" @click="startBaseUrl('consonantUrl',2)" v-if=" imageForm[2].id || videoForm.consonantUrl" >上传</el-button> -->
          <div v-if="videoForm.consonantUrl && !imageForm[2].consonantUrl">
            <div
              style="width: 148px; height: 148px; background: #f5f7fa; display: flex; align-items: center; justify-content: center"
              v-if="(imageForm[2].progress || imageForm[2].progress === 0) && imageForm[2].progress < 101 && !imageForm[2].failed"
            >
              <el-progress :width="100" type="circle" :percentage="imageForm[2].progress"></el-progress>
            </div>
            <div v-else style="width: 148px; height: 148px; background: #f5f7fa; display: flex; align-items: center; justify-content: center">
              <div v-if="imageForm[2].failed">上传失败，点击删除后重新上传</div>
              <el-image v-else style="width: 148px; height: 148px; background: #f5f7fa">
                <div slot="error" class="course_image_slot">
                  <i class="el-icon-picture-outline"></i>
                  <span>审核中...</span>
                </div>
              </el-image>
            </div>
          </div>
          <div v-if="imageForm[2].consonantUrl">
            <el-image :src="imageForm[2].consonantUrl" style="width: 148px; height: 148px" fit="cover "></el-image>
            <div>{{ imageForm[2].title }}</div>
          </div>
        </el-form-item>
        <el-form-item label="总结视频" prop="summaryUrl">
          <div v-if="imageForm[0].status === 61 || imageForm[0].status === 60" id="summaryUrlPlayer"></div>
          <div style="position: relative">
            <input
              type="file"
              class="upload"
              style="max-width: 200px"
              accept="video/mp4,video/webm,video/ogg"
              @change="doUpload('summaryUrl', 'summaryInputer', 3)"
              :disabled="!(courseType === '1') ? 'disabled' : null"
              ref="summaryInputer"
              multiple
            />
            <el-button type="danger" size="small" @click="delBaseUrl('summaryUrl', 'summaryInputer', 3)" v-if="imageForm[3].id || videoForm.summaryUrl">删除</el-button>
            <div style="position: absolute; background-color: transparent; width: 130px; height: 21px; left: 70px; top: 6px"></div>
          </div>
          <!-- <el-button type="primary" size="small" @click="startBaseUrl('summaryUrl',3)" v-if="imageForm[3].id || videoForm.summaryUrl" >上传</el-button> -->
          <div v-if="videoForm.summaryUrl && !imageForm[3].summaryUrl">
            <div
              style="width: 148px; height: 148px; background: #f5f7fa; display: flex; align-items: center; justify-content: center"
              v-if="(imageForm[3].progress || imageForm[3].progress === 0) && imageForm[3].progress < 101 && !imageForm[3].failed"
            >
              <el-progress :width="100" type="circle" :percentage="imageForm[3].progress"></el-progress>
            </div>
            <div v-else style="width: 148px; height: 148px; background: #f5f7fa; display: flex; align-items: center; justify-content: center">
              <div v-if="imageForm[3].failed">上传失败，点击删除后重新上传</div>
              <el-image v-else style="width: 148px; height: 148px; background: #f5f7fa">
                <div slot="error" class="course_image_slot">
                  <i class="el-icon-picture-outline"></i>
                  <span>审核中...</span>
                </div>
              </el-image>
            </div>
          </div>
          <div v-if="imageForm[3].summaryUrl">
            <el-image :src="imageForm[3].summaryUrl" style="width: 148px; height: 148px" fit="cover "></el-image>
            <div>{{ imageForm[3].title }}</div>
          </div>
        </el-form-item>
        <el-form-item label="复习视频" prop="reviewUrl">
          <div style="position: relative">
            <input
              type="file"
              class="upload"
              style="max-width: 200px"
              accept="video/mp4,video/webm,video/ogg"
              @change="doUpload('reviewUrl', 'reviewUrlInputer', 4)"
              :disabled="!(courseType === '1') ? 'disabled' : null"
              ref="reviewUrlInputer"
              multiple
            />
            <el-button type="danger" size="small" @click="delBaseUrl('reviewUrl', 'reviewUrlInputer', 4)" v-if="imageForm[4].id || videoForm.reviewUrl">删除</el-button>
            <div style="position: absolute; background-color: transparent; width: 130px; height: 21px; left: 70px; top: 6px"></div>
          </div>
          <!-- <el-button type="primary" size="small" @click="startBaseUrl('reviewUrl',4)" v-if="imageForm[4].id || videoForm.reviewUrl" >上传</el-button> -->
          <div v-if="videoForm.reviewUrl && !imageForm[4].reviewUrl">
            <div
              style="width: 148px; height: 148px; background: #f5f7fa; display: flex; align-items: center; justify-content: center"
              v-if="(imageForm[4].progress || imageForm[4].progress === 0) && imageForm[4].progress < 101 && !imageForm[4].failed"
            >
              <el-progress :width="100" type="circle" :percentage="imageForm[4].progress"></el-progress>
            </div>
            <div v-else style="width: 148px; height: 148px; background: #f5f7fa; display: flex; align-items: center; justify-content: center">
              <div v-if="imageForm[4].failed">上传失败，点击删除后重新上传</div>
              <el-image v-else style="width: 148px; height: 148px; background: #f5f7fa">
                <div slot="error" class="course_image_slot">
                  <i class="el-icon-picture-outline"></i>
                  <span>审核中...</span>
                </div>
              </el-image>
            </div>
          </div>
          <div v-if="imageForm[4].reviewUrl">
            <el-image :src="imageForm[4].reviewUrl" style="width: 148px; height: 148px" fit="cover "></el-image>
            <div>{{ imageForm[4].title }}</div>
          </div>
        </el-form-item>
        <el-form-item label="复习讲义" prop="reviewTextUrl" v-if="!(courseType === '1' && courseLevel == 2)">
          <el-row>
            <el-col :span="12">
              <el-upload
                :limit="1"
                :file-list="reviewUrlFileList"
                :disabled="!(courseType === '1')"
                :show-file-list="true"
                action=""
                class="upload-demo"
                v-loading="reviewTextUrlLoading"
                :http-request="reviewTextUrlHttp"
                :on-remove="removeReview"
              >
                <!-- :accept="'.doc,.docx'" :before-upload="beforeWordUpload" -->
                <el-button icon="el-icon-upload" :underline="false" class="upload_link">word文件上传</el-button>
              </el-upload>
            </el-col>
          </el-row>
        </el-form-item>
        <el-form-item label="备课资料" prop="lessonUrl" v-if="courseType === '1'">
          <el-row>
            <el-col :span="12">
              <el-upload
                :limit="10"
                :file-list="lessonMaterialsFileList"
                :show-file-list="true"
                :accept="'.pdf'"
                action=""
                class="upload-demo"
                v-loading="lessonUrlLoading"
                :http-request="lessonMaterialsUrlHttp"
                :on-remove="removeLesson"
                :on-exceed="handleExceed"
              >
                <!-- :accept="'.doc,.docx'" :before-upload="beforeWordUpload" -->
                <el-button
                  icon="el-icon-upload"
                  :underline="false"
                  class="upload_link"
                  :class="lessonMaterialsFileList.length >= 10 ? 'upload_disable' : ''"
                  @click="beforePDFUpload"
                >
                  pdf文件上传
                </el-button>
                <!-- <el-button icon="el-icon-upload" :underline="false" class="upload_link" :disabled="lessonMaterialsFileList.length >= 3" @click="beforePDFUpload">
                  pdf文件上传
                </el-button> -->
              </el-upload>
            </el-col>
          </el-row>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="dialogClose">关闭</el-button>
        <el-button :disabled="!videoStatus" size="mini" type="primary" @click="editVideoForm">确定</el-button>
      </div>
    </el-dialog>
    <div class="videoDialog" v-if="showVideo" id="player">
      <div class="closeVideo" @click="closePreviewVideo">
        <i class="el-icon-circle-close"></i>
      </div>
    </div>
  </div>
</template>
<script src="https://s1.videocc.net/library/blueimp-md5/md5-2.18.0.min.js"></script>
<!--引入上传插件js-->
<script src="https://static.polyv.net/file/plug-in-v2/polyv-upload.min.js"></script>
<script>
  import { ossPrClient } from '@/api/alibaba';
  import VowelList from '@/views/course/courseDictation/makeCourseList/vowelList.vue';
  import SyllableList from '@/views/course/courseDictation/makeCourseList/syllableList.vue';
  import DrawSyllableList from '@/views/course/courseDictation/makeCourseList/drawSyllable.vue';
  import SpellList from '@/views/course/courseDictation/makeCourseList/SpellList';
  import HasSpellStepForm from '../components/hasSpellStepForm.vue';
  import UploadAudio from '@/views/course/courseDictation/makeCourseList/uploadAudio.vue';
  import courseDictationListApi from '@/api/courseDictationList';
  import ExperienceSpellList from '@/views/course/courseDictation/makeCourseList/experienceSpellList.vue';
  import ExperienceStepForm from '../components/experienceStepForm.vue';
  import SynchronousVocabulary from '../makeCourseList/synchronousVocabulary.vue';
  import md5 from 'js-md5';
  import PlvVideoUpload from '@polyv/vod-upload-js-sdk';
  export default {
    components: {
      VowelList,
      SyllableList,
      DrawSyllableList,
      SpellList,
      UploadAudio,
      ExperienceSpellList,
      HasSpellStepForm,
      ExperienceStepForm,
      SynchronousVocabulary
    },
    data() {
      return {
        vodPlayerJs: 'https://player.polyv.net/resp/vod-player/latest/player.js',
        tableData: [],
        uploadData: [
          { word: 'red', status: 0, id: '0000000' },
          { word: 'blue', status: 0, id: '1111111' },
          { word: 'white', status: 0, id: '2222222' }
        ], //上传音频弹窗
        audioFileList: [], //已上传音频列表
        num: [],
        uploadTableLoading: false, //上传音频
        couresListShow: '',
        tableLoading: false,
        preschoolLoading: false, //上传视频
        vowelLoading: false,
        consonantLoading: false,

        videoStatus: true,
        summaryLoading: false,
        reviewTextUrlLoading: false,
        lessonUrlLoading: false,
        fileDetailList: [],
        videoForm1: {},
        videoForm: {
          courseCode: '',
          baseUrl: '',
          vowelUrl: '',
          consonantUrl: '',
          summaryUrl: '',
          reviewUrl: '',
          reviewTextUrl: '',
          studyUrl: '',
          lessonUrl: []
        },
        imageForm: [
          { name: 'baseUrl', baseUrl: '', id: '', status: '', title: '' },
          { name: 'vowelUrl', vowelUrl: '', id: '', status: '', title: '' },
          {
            name: 'consonantUrl',
            consonantUrl: '',
            id: '',
            status: '',
            title: ''
          },
          { name: 'summaryUrl', summaryUrl: '', id: '', status: '', title: '' },
          { name: 'reviewUrl', reviewUrl: '', id: '', status: '', title: '' },
          { name: 'studyUrl', studyUrl: '', id: '', status: '', title: '' }
        ],
        videoRules: {
          summaryUrl: [{ required: true, message: '请上传总结视频', trigger: 'blur' }],
          reviewUrl: [{ required: true, message: '请上传复习视频', trigger: 'blur' }],
          reviewTextUrl: [{ required: true, message: '请上传复习讲义', trigger: 'blur' }]
        },
        typeRules: {
          studyUrl: [{ required: true, message: '请上传学习视频', trigger: 'blur' }]
        },
        videoDialogVisible: false,
        // audioDialogVisible:false,//音频弹窗
        vowelListStatus: false, //元辅音列表
        syllableStatus: false, //音节列表
        drawSyllableStatus: false, //划音节列表
        spellListStatus: false, //拼读拼写列表
        courseCode: '',
        id: '',
        courseType: '',
        SpellListData: [], //拼读拼写列表
        DrawSyllableListData: [], //划音节列表
        spellStepDialogVisible: false, //有划音节 拼读拼写弹窗
        userid: '1723c88563', //从点播后台查看获取
        secretkey: 'Jkk4ml1Of8', //从点播后台查看获取
        writeToken: '94064a19-4d28-4ce9-aa05-96d476016215',
        // ts:'',
        ptime: '',
        reviewUrlFileList: [],
        lessonMaterialsFileList: [],
        isPreviewVideo: false,
        vidList: [], //视频id合集
        showVideo: false,
        courseLevel: ''
      };
    },
    created() {
      this.courseCode = this.$route.query.courseCode;
      this.courseType = this.$route.query.courseType;
      this.courseLevel = this.$route.query.courseLevel;
      this.id = this.$route.query.id;
      this.ts = new Date().getTime();
      this.getTableData();
      ossPrClient();
      this.videoUpload = new PlvVideoUpload({
        region: 'line1', // (可选)上传线路, 默认line1
        events: {
          Error: (err) => {
            // 错误事件回调
            console.log(err);
            let errMag = `${err.message}`;
            this.$alert(errMag, '标题名称', {
              confirmButtonText: '确定',
              type: 'error'
            });
          },
          UploadComplete: () => {
            // 全部上传任务完成回调
            this.$message({
              message: '上传成功',
              type: 'success'
            });
          }
        }
      });
      // this.loadPlayerScript(this.loadPlayer);
    },
    mounted() {
      this.autoUpdateUserData(null, this.videoUpload);
      let _this = this;
      this.$bus.$on('openSpell', () => {
        this.spellStepDialogVisible = false;
        this.DrawSyllableListData = [];
        console.log('openSpell');
      });
    },
    destroyed() {
      if (this.player) {
        this.player.destroy();
      }
    },
    methods: {
      previewVideo(name) {
        console.log(this.vidList);
        let videoObj = this.vidList.find((obj) => obj.name === name);
        if (!videoObj) {
          console.log('没找到');
          return;
        }
        this.showVideo = true;
        setTimeout(() => {
          this.getVideo(videoObj.vid);
        }, 0);
      },
      closePreviewVideo() {
        this.showVideo = false;
      },
      autoUpdateUserData(timer, videoUpload) {
        // 启动获取用户信息
        this.getUserData(videoUpload);
        if (timer) {
          clearTimeout(timer);
          timer = null;
        }
        timer = setTimeout(() => {
          this.autoUpdateUserData(timer, videoUpload);
        }, 3 * 50 * 1000);
      },
      getUserData() {
        // 获取用户详细信息
        this.ptime = new Date().getTime();
        let userData = {
          userid: this.userid,
          ptime: this.ptime,
          sign: this.getSignData().sign,
          hash: this.getSignData().hash
        };
        this.videoUpload.updateUserData(userData);
      },
      loadPlayerScript(callback) {
        if (!window.polyvPlayer) {
          const myScript = document.createElement('script');
          myScript.setAttribute('src', this.vodPlayerJs);
          myScript.onload = callback;
          document.body.appendChild(myScript);
        } else {
          console.log(callback);
          callback();
        }
      },
      loadPlayerVideoUrl(vid) {
        console.log(vid);
        let polyvPlayer = window.polyvPlayer;
        if (!window.polyvPlayer) {
          console.log('还没实例化');
          setTimeout(() => {
            this.loadPlayerVideoUrl(vid);
          }, 100);
        } else {
          console.log('polyvPlayer', polyvPlayer);
          this.player = polyvPlayer({
            wrap: `#player`,
            width: 960,
            height: 520,
            vid: vid
          });
        }
        // setTimeout(() => {
        //   const polyvPlayer = window.polyvPlayer;
        //   this.player = polyvPlayer({
        //     wrap:`#player`,
        //     width: 960,
        //     height: 480,
        //     vid: vid,
        //   });
        // }, 1000);
      },
      getVideo(vid) {
        this.loadPlayerScript(this.loadPlayerVideoUrl(vid));
        // if(item === 'baseUrl'){
        //   this.loadPlayerScript(this.loadPlayerBaseUrl(vid))
        // }else if(item === 'vowelUrl'){
        //   this.loadPlayerScript(this.loadPlayerVowelUrl)
        // }else if(item === 'consonantUrl'){
        //   this.loadPlayerScript(this.loadPlayerConsonantUrl)
        // }else if(item === 'summaryUrl'){
        //   this.loadPlayerScript(this.loadPlayerSummaryUrl)
        // }
      },
      //获取列表
      getTableData() {
        courseDictationListApi.getVideoUrlByCourseCode(this.courseCode).then((res) => {
          if (res.code === 20000) {
            this.tableData = [];
            this.reviewUrlFileList = [];
            this.lessonMaterialsFileList = [];
            const form = {
              baseUrl: res.data.data.baseUrl,
              vowelUrl: res.data.data.vowelUrl,
              consonantUrl: res.data.data.consonantUrl,
              summaryUrl: res.data.data.summaryUrl,
              studyUrl: res.data.data.studyUrl
            };
            const formData = res.data.data;
            this.videoForm = res.data.data;
            this.videoForm1 = JSON.parse(JSON.stringify(res.data.data));
            if (res.data.data.reviewTextUrl) {
              const match = res.data.data.reviewTextUrl.match(/.*\/(.+?)\/?$/);
              const fileName = match ? match[1] : '';
              this.reviewUrlFileList.push({
                url: res.data.data.reviewUrl,
                name: fileName
              });
            }
            if (res.data.data.lessonUrl) {
              this.lessonMaterialsFileList = res.data.data.lessonUrl.map((item) => {
                // const match = item.match(/.*\/(.+?)\/?$/);
                // const fileName = match ? match[1] : '';
                return {
                  url: item.value,
                  name: item.key
                };
              });
            }
            const data = ['baseUrl', 'vowelUrl', 'consonantUrl', 'summaryUrl', 'reviewUrl', 'studyUrl'];
            data.forEach((item, index) => {
              if (res.data.data[item]) {
                this.vidList.push({
                  name: item,
                  vid: res.data.data[item]
                });
                courseDictationListApi.getVideoInfo(res.data.data[item]).then((data) => {
                  if (res.code === 20000) {
                    this.imageForm[index][item] = data.data.data.data[0].basicInfo.coverURL;
                    // this.imageForm[Index][status] = res.data.data.data[0].basicInfo && res.data.data.data[0].basicInfo.status
                    formData[item] = data.data.data.data[0].basicInfo.coverURL;
                    this.imageForm.forEach((f) => {
                      if (f.name === item) {
                        let a = data.data.data.data[0].basicInfo.status;
                        if (a == 61 || a == 60) {
                          f.status = true;
                        } else {
                          f.status = false;
                        }
                        f.title = data.data.data.data[0].basicInfo.title;
                        // if(f.status === 60 || f.status === 61){
                        //     this.getVideo(item,res.data.data[item])
                        // }
                      }
                    });
                  }
                });
              }
            });
            this.tableData.push(formData);
            console.log(this.imageForm, 'imageFormimageForm');
          }
        });
      },
      //   getSignData() { // 加密信息参数
      //   let hash = md5(this.ts + this.writeToken)
      //   let sign = md5(this.secretkey + this.ts)
      //   return {
      //     hash: hash,
      //     sign: sign,
      //   }
      // },
      getSignData() {
        // 加密信息参数
        let hash = md5(this.ptime + this.writeToken);
        let sign = md5(this.secretkey + this.ptime);
        return {
          hash: hash,
          sign: sign
        };
      },
      //删除上传视频
      delBaseUrl(value, refName, Index) {
        if (this.videoUpload) {
          // console.log('delBaseUrl00000000000000000');
          courseDictationListApi.deleteVideo(this.videoForm[value]).then((res) => {
            if (res.code === 20000) {
              this.$message.success('删除成功');
            }
          });
          this.videoUpload.removeFile(this.imageForm[Index].id);
          // this.videoUpload.clearAll();
          (this.$refs[refName].value = ''), (this.imageForm[Index][value] = '');
          this.videoForm[value] = '';
          this.imageForm[Index].id = '';
          console.log(value, Index, 'index', this.videoForm);
        }
      },
      startBaseUrl(value, Index) {
        // 全部上传
        console.log(this.imageForm[Index], '121212');
        if (this.videoUpload) {
          // this.videoUpload.resumeFile(this.videoForm[value]);
          this.videoUpload.resumeFile(this.imageForm[Index].id);
          // this.videoUpload.startAll();
        }
      },
      isWord(file) {
        return /\.(doc|docx)$/.test(file.name);
      },
      beforeWordUpload(file) {
        if (!this.isWord(file)) {
          this.$message.error('只能上传word文件！');
          return false;
        }
      },
      //删除复习讲义
      removeReview() {
        this.videoForm.reviewTextUrl = '';
      },

      //复习讲义
      reviewTextUrlHttp({ file }) {
        // this.reviewTextUrlLoading = true
        const fileName = 'manage/' + Date.parse(new Date()) + file.name.substr(file.name.lastIndexOf('.'));
        this.$nextTick(function () {
          if (ossPrClient() && ossPrClient().put) {
            ossPrClient()
              .put(fileName, file)
              .then(({ res, url, name }) => {
                if (res && res.status === 200) {
                  console.log(`阿里云OSS上传图片成功回调1`, res, url, name);
                  this.videoForm.reviewTextUrl = url;
                  console.log(this.videoForm.reviewTextUrl, 'reviewTextUrlreviewTextUrl');
                  this.reviewTextUrlLoading = false;
                  this.$nextTick(() => {
                    this.reviewTextUrlLoading = false;
                  });
                }
              })
              .catch((err) => {
                this.reviewTextUrlLoading = false;
              });
          }
        });
      },
      // 备课资料
      lessonMaterialsUrlHttp({ file }) {
        console.log(file, 'filefilefile');
        if (this.lessonMaterialsFileList.length >= 10) {
          // this.$message.error('最多上传10个文件');
          return;
        }
        const fileName = 'manage/' + Date.parse(new Date()) + file.name.substr(file.name.lastIndexOf('.'));
        this.$nextTick(function () {
          if (ossPrClient() && ossPrClient().put) {
            ossPrClient()
              .put(fileName, file)
              .then(({ res, url, name }) => {
                if (res && res.status === 200) {
                  console.log(`阿里云OSS上传pdf成功回调1`, res, url, name);
                  this.lessonMaterialsFileList.push({
                    url: url,
                    name: file.name
                  });
                  // let temp = this.videoForm.lessonUrl ? this.videoForm.lessonUrl : [];
                  let newObj = this.lessonMaterialsFileList.map((item) => {
                    return {
                      value: item.url,
                      key: item.name || item.url.split('/').pop() || '未命名文件'
                    };
                  });
                  console.log(this.lessonMaterialsFileList, 'lessonMaterialsFileList', newObj);
                  // let temp = this.videoForm.lessonUrl ? this.videoForm.lessonUrl : [];
                  this.$set(this.videoForm, 'lessonUrl', newObj);
                  this.lessonUrlLoading = false;
                  this.$nextTick(() => {
                    this.lessonUrlLoading = false;
                  });
                }
              })
              .catch((err) => {
                this.lessonUrlLoading = false;
              });
          }
        });
      },
      //删除备课资料
      removeLesson(file, fileList) {
        console.log(`删除备课资料`, file, fileList, this.lessonMaterialsFileList);
        this.lessonMaterialsFileList = fileList;
        this.videoForm.lessonUrl = fileList.map((item) => {
          return {
            value: item.url,
            key: item.name || item.url.split('/').pop() || '未命名文件'
          };
        });
        console.log(`删除备课资料`, this.videoForm.lessonUrl);
        // this.videoForm.lessonUrl = '';
      },
      //
      beforePDFUpload() {
        console.log('beforePDFUpload123');
        if (this.lessonMaterialsFileList.length >= 10) {
          // this.$message.error('最多上传10个文件');
          return;
        }
      },
      handleExceed() {
        this.$message.warning('最多上传10个文件');
      },
      doUpload(value, refName, Index) {
        this.videoStatus = false;
        this.num.push({ id: Index, videoStatus: false });
        let inputDOM = this.$refs[refName]; // 通过DOM取文件数据
        console.log(Object.values(inputDOM.files), '1111111111111111');
        let data = Object.values(inputDOM.files);
        const videoFormat = ['video/mp4', 'video/webm', 'video/ogg'];
        if (!videoFormat.includes(data[0].type)) {
          console.log('选择了非视频格式文件', inputDOM);
          this.$refs[refName].value = null;
          this.$message.warning('请选择视频格式文件');
          return;
        } else {
          console.log('选择的是视频格式文件');
        }
        if (data.length > 0) {
          data.forEach((file, index, arr) => {
            let fileSetting = {
              // 文件上传相关信息设置
              title: file.name, // 标题
              desc: 'jssdk插件上传', // 描述
              cataid: process.env.VUE_APP_BASE_CATAID, // 上传分类目录ID
              tag: '', // 标签
              luping: 0, // 是否开启视频课件优化处理，对于上传录屏类视频清晰度有所优化：0为不开启，1为开启
              keepsource: 0, // 是否源文件播放（不对视频进行编码）：0为编码，1为不编码
              state: '' //用户自定义信息，如果提交了该字段，会在服务端上传完成回调时透传返回。
            };
            console.log('data---------->', data, this.videoUpload);
            let uploadManager = this.videoUpload.addFile(
              file, // file 为待上传的文件对象
              {
                FileStarted: (data) => this.onFileStarted(data, value, Index), // 文件开始上传回调
                FileProgress: (data) => this.onFileProgress(data, value, Index), // 文件上传中回调
                FileSucceed: (data) => this.onFileSucceed(data, value, Index), // 文件上传成功回调
                FileFailed: (data) => this.onFileFailed(data, value, Index), // 文件上传失败回调
                FileStopped: (data) => this.onFileStopped(data, value, Index) // 文件上传停止回调
              },
              fileSetting
            );
            console.log(uploadManager, 'uploadManageruploadManager');
            // this.loadPlayerScript(this.loadPlayer);
            this.addTableData(uploadManager, value, Index);
            console.log(value, refName, Index);
            this.videoUpload.resumeFile(this.imageForm[Index].id);
            // this.startBaseUrl(value,Index)
          });
        }
      },
      addTableData(data, value, Index) {
        this.imageForm[Index].id = data.id;
        this.videoForm[value] = data.id;
      },
      onFileStarted(data, value, Index) {
        console.log('文件上传开始: ', data);
        this.$set(this.imageForm[Index], 'progress', 0);
        this.$set(this.imageForm[Index], 'failed', false);
      },
      onFileSucceed(data, value, Index) {
        this.imageForm[Index].progress = 101;
        this.vid = data.fileData.vid;
        if (this.vid) {
          setTimeout(() => {
            courseDictationListApi.getVideoInfo(this.vid).then((res) => {
              if (res.code === 20000) {
                this.videoForm[value] = this.vid;
                this.imageForm[Index][value] = res.data.data.data[0].basicInfo && res.data.data.data[0].basicInfo.coverURL;
                this.num.find((e) => e.id == Index).videoStatus = true;
                if (this.num.find((o) => o.videoStatus == false)) {
                } else {
                  this.videoStatus = true;
                  this.num = [];
                }

                console.log('上传成功vid--------------------------', this.vid);
              }
            });
          }, 1000);
        }
        // this.loadPlayerScript(this.loadPlayer);
      },
      onFileFailed(data, value, Index) {
        console.log('文件上传失败: ', data);
        this.$set(this.imageForm[Index], 'failed', true);
        this.videoStatus = false;
      },
      onFileStopped(data) {
        console.log('文件上传停止: ', data);
        this.videoStatus = false;
      },
      onFileProgress(data, value, Index) {
        console.log(data, 'datafile9999');
        let p = parseInt(data.progress * 100); // 上传的进度条
        // this.$set(this.imageForm[Index], 'progress', p);
        console.log('文件上传中11111: ', data, Index, this.imageForm[Index]);
        this.imageForm[Index].progress = p;
      },
      uploadSuccess(data) {
        console.log(data, 'data');
      },
      //音频上传,移除状态显示
      audioStatus(file, status) {
        if (Array.isArray(file)) {
          if (status === 1) {
            this.uploadData.forEach((res) => {
              file.forEach((f) => {
                if (res.word === f.name.split('.')[0]) {
                  console.log(res.word, 'word');
                  res.status = status;
                }
              });
            });
          }
        } else if (file.name) {
          console.log(file, '');
          this.uploadData.forEach((f) => {
            if (f.word === file.name.split('.')[0]) {
              f.status = status;
            }
          });
          // this.uploadData.filter(res => res.word === fileList.name.split('.')[0])?.status = status
        }
      },
      //音频移除
      handleRemoveVideoWord(file, fileList) {
        this.audioStatus(file, 0);
        this.audioFileList.filter((f) => (f.name === file.name ? this.audioFileList.splice(this.audioFileList.indexOf(f), 1) : this.audioFileList));
      },

      //上传到服务器
      videoUploadFile(param) {
        console.log(param, '上传到服务器');
      },
      isAudio(file) {
        return /\.(mp3|wav|mid|wma|ra|vqf|mov|amr)$/.test(file.name);
      },
      //音频上传
      beforeWordVideoUpload(file) {
        const isFileName = this.uploadData.filter((f) => f.word === file.name.split('.')[0]).length > 0;
        const isRepeat = this.audioFileList.filter((f) => f.name === file.name).length > 0;
        if (!this.isAudio(file)) {
          this.$message.error('只能上传音频文件！');
          return false;
        } else if (!isFileName) {
          this.$message.error('只能上传检测库内单词文件！');
          return false;
        } else if (isRepeat) {
          this.$message.error('已经上传过该单词文件！');
          return;
        }
      },
      getList(index) {
        //划拼音规则,已拼读拼写不能划拼音规则
        if (index === '2') {
          courseDictationListApi.getRuleListByCourseCode(this.courseCode, 2, 2).then((res) => {
            if (res.code === 20000) {
              this.SpellListData = res.data.data;
              if (this.SpellListData.length === 0) {
                this.couresListShow = '2';
              } else {
                let isRule = this.SpellListData.some((item) => item.videoType !== '2');
                if (isRule) {
                  this.couresListShow = '2';
                } else {
                  this.$message.warning('已拼读拼写过不需要划音节');
                }
              }
            }
          });
        } else if (index === '3') {
          //拼读拼写列表------已划拼音规则不需要添加视频
          if (this.courseType === '0') {
            this.couresListShow = '3';
            console.log('体验课不要调这个接口qwerqwerqwerqwerqwerqwerqwerqwer');
            return;
          }
          courseDictationListApi.getRuleListByCourseCode(this.courseCode, 4, 1).then((res) => {
            if (res.code === 20000) {
              this.DrawSyllableListData = res.data.data;
              if (res.data.data && res.data.data.length > 0) {
                this.spellStepDialogVisible = true;
                return this.$message.warning('已有划音节规则');
              }
              // this.couresListShow = '3'
              this.DrawSyllableListData = res.data.data;
              let allEmpty = true; // 假设所有都是空的
              for (let i = 0; i < this.DrawSyllableListData.length; i++) {
                if (this.DrawSyllableListData[i].wordInfoVoList && this.DrawSyllableListData[i].wordInfoVoList.length > 0) {
                  allEmpty = false; // 如果找到一个非空的，就设置allEmpty为false
                  break; // 如果只需要知道是否至少有一个非空的，可以在这里退出循环
                }
              }
              if (allEmpty) {
                this.spellStepDialogVisible = false;
                this.DrawSyllableListData = [];
                console.log('所有子元素的wordInfoVoList都为空');
              } else {
                this.spellStepDialogVisible = true;
                console.log('至少有一个子元素的wordInfoVoList不为空');
              }
              this.couresListShow = '3';
            }
          });
        } else {
          this.couresListShow = index;
        }
      },
      //有划拼音  拼读拼写弹窗关闭
      spellDialogClose() {
        this.spellStepDialogVisible = false;
      },
      // audioCloseDialog(){
      //     this.audioDialogVisible = false
      // },
      //上传音频
      // getUploadAudio(){
      //     this.audioDialogVisible = true
      // },
      summaryUrlDel() {
        this.videoForm.summaryUrl = '';
      },
      //删除辅音视频
      consonantUrlDel() {
        this.videoForm.consonantUrl = '';
      },
      //删除元音视频
      vowelUrlDel() {
        this.videoForm.vowelUrl = '';
      },
      //删除学前视频
      preschoolDel() {
        this.videoForm.baseUrl = '';
      },
      //添加视频
      addVideo(type) {
        //type ? '编辑' : '新增'
        this.getTableData();
        this.videoDialogVisible = true;
        console.log(this.videoForm);
      },
      //关闭弹窗
      dialogClose() {
        this.videoDialogVisible = false;
        this.$refs['videoForm'].resetFields();
      },
      editVideoForm() {
        const data = ['baseUrl', 'vowelUrl', 'consonantUrl', 'summaryUrl', 'reviewUrl'];
        data.forEach((item) => {
          if (this.videoForm[item].split('.')[1]) {
            this.videoForm[item] = this.videoForm1[item];
          }
        });
        console.log(this.videoForm1);

        console.log('11111111111111111111111111', this.videoForm);
        this.$refs['videoForm'].validate((valid) => {
          if (valid) {
            courseDictationListApi.updateVideoUrlByCourseCode(this.videoForm).then((res) => {
              if (res.code === 20000) {
                this.videoDialogVisible = false;
                this.$message.success('提交成功');
                this.getTableData();
              }
            });
          }
        });
      },

      //返回课程列表
      goBack() {
        window.localStorage.setItem('isJump', 'true');
        this.$router.push({ name: 'courseDictationList' });
      },
      beforeUpload(file) {
        const isMp4 = file.type === 'video/mp4';
        // 限制文件最大不能超过 50M
        const isLt50M = file.size / 1024 / 1024 < 50;
        if (!isMp4) {
          this.$message.error('视频只能是mp4格式!');
        }
        //   if (!isLt50M) {
        //     this.$message.error("上传头像图片大小不能超过 300MB!");
        //   }
        return isMp4;
      },
      preschoolHttp({ file }) {
        this.preschoolLoading = true;
        const fileName = 'manage/' + Date.parse(new Date()) + '.mp4';
        this.$nextTick(function () {
          if (ossPrClient() && ossPrClient().put) {
            ossPrClient()
              .put(fileName, file)
              .then(({ res, url, name }) => {
                if (res && res.status === 200) {
                  console.log(`阿里云OSS上传图片成功回调1`, res, url, name);
                  this.videoForm.baseUrl = url;
                  this.preschoolLoading = false;
                  this.$nextTick(() => {
                    this.preschoolLoading = false;
                  });
                }
              })
              .catch((err) => {
                //  that.$message.error('上传图片失败请检查网络或者刷新页面')
                this.preschoolLoading = false;
                console.log(`阿里云OSS上传图片失败回调`, err);
              });
          }
        });
      },
      vowelHttp({ file }) {
        this.vowelLoading = true;
        const fileName = 'manage/' + Date.parse(new Date()) + '.mp4';
        this.$nextTick(function () {
          if (ossPrClient() && ossPrClient().put) {
            ossPrClient()
              .put(fileName, file)
              .then(({ res, url, name }) => {
                if (res && res.status === 200) {
                  console.log(`阿里云OSS上传图片成功回调1`, res, url, name);
                  this.videoForm.vowelUrl = url;
                  this.vowelLoading = false;
                  this.$nextTick(() => {
                    this.vowelLoading = false;
                  });
                }
              })
              .catch((err) => {
                //  that.$message.error('上传图片失败请检查网络或者刷新页面')
                this.vowelLoading = false;
                console.log(`阿里云OSS上传图片失败回调`, err);
              });
          }
        });
      },
      summaryHttp({ file }) {
        this.summaryLoading = true;
        const fileName = 'manage/' + Date.parse(new Date()) + '.mp4';
        this.$nextTick(function () {
          if (ossPrClient() && ossPrClient().put) {
            ossPrClient()
              .put(fileName, file)
              .then(({ res, url, name }) => {
                if (res && res.status === 200) {
                  console.log(`阿里云OSS上传图片成功回调1`, res, url, name);
                  this.videoForm.summaryUrl = url;
                  this.summaryLoading = false;
                  this.$nextTick(() => {
                    this.summaryLoading = false;
                  });
                }
              })
              .catch((err) => {
                //  that.$message.error('上传图片失败请检查网络或者刷新页面')
                this.summaryLoading = false;
                console.log(`阿里云OSS上传图片失败回调`, err);
              });
          }
        });
      },
      consonantHttp({ file }) {
        this.consonantLoading = true;
        const fileName = 'manage/' + Date.parse(new Date()) + '.mp4';
        this.$nextTick(function () {
          if (ossPrClient() && ossPrClient().put) {
            ossPrClient()
              .put(fileName, file)
              .then(({ res, url, name }) => {
                if (res && res.status === 200) {
                  console.log(`阿里云OSS上传图片成功回调1`, res, url, name);
                  this.videoForm.consonantUrl = url;
                  this.consonantLoading = false;
                  this.$nextTick(() => {
                    this.consonantLoading = false;
                  });
                }
              })
              .catch((err) => {
                //  that.$message.error('上传图片失败请检查网络或者刷新页面')
                this.consonantLoading = false;
                console.log(`阿里云OSS上传图片失败回调`, err);
              });
          }
        });
      }
    }
  };
</script>
<style lang="less" scope="scope">
  .totalBox {
    min-height: 720px;
  }
  ::v-deep .el-upload-list {
    width: auto !important;
  }
  .course_image_slot {
    height: 148px;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #c0c4cc;
  }
  .container_box {
    width: 100%;
    margin-bottom: 15px;
    background: #fff;
    border-radius: 6px;
    padding: 5px;
    padding-left: 5px;
    box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.2);
  }
  .item_list {
    font-weight: bold;
    padding: 20px 0;
    color: #909399;
  }
  .video_upload_table {
    width: 128px;
    height: 128px;
    border-radius: 8px;
    background-color: black;
  }
  .video_upload {
    position: absolute;
    z-index: 99999;
    width: 148px;
    height: 148px;
    border-radius: 8px;
    background-color: black;
  }
  .video_del {
    margin-left: 130px;
    position: absolute;
    z-index: 99999;
    color: white;
  }
  .videoDialog {
    z-index: 9999;
    position: absolute;
    top: 10vh;
    left: -105px;
    transform: translateX(50%);
    // background-color: rgba(0, 0, 0, .3);
    .closeVideo {
      position: absolute;
      top: 10px;
      left: 920px;
      transform: translateX(50%);
      z-index: 99999;
      color: #ffffff;
      font-size: 20px;
      cursor: pointer;
      &:hover {
        color: #409eff;
      }
    }
  }
  .upload_disable {
    color: #c0c4cc !important;
    border-color: #c0c4cc !important;
    background-color: transparent !important;
  }
</style>
