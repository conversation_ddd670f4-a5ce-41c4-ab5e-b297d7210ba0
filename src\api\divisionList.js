/**
 * 事业部相关接口
 */
import request from '@/utils/request';

export default {
  // 分页查询
  divisionList(pageNum, pageSize, data) {
    return request({
      url: '/znyy/division/list/' + pageNum + '/' + pageSize,
      method: 'GET',
      params: data
    });
  },
  // 导出
  agentExport(listQuery) {
    return request({
      url: '/znyy/agent/execle',
      method: 'GET',
      responseType: 'blob',
      params: listQuery
    });
  },
  //获取事业部等级
  getRankGrandName() {
    return request({
      url: '/znyy/division/rank/get/rank',
      method: 'GET'
    });
  },
  // 导出
  divisionExport(listQuery) {
    return request({
      url: '/znyy/division/execle',
      method: 'GET',
      responseType: 'blob',
      params: listQuery
    });
  },

  // 开通与暂停
  updateStatus(id, status) {
    return request({
      url: '/znyy/division/update/isEnable?id=' + id + '&isEnable=' + status,
      method: 'PUT'
    });
  },
  // 审核
  examine(id, checkReason, isCheck, rank) {
    return request({
      url: '/znyy/division/update/isCheck?id=' + id + '&checkReason=' + checkReason + '&isCheck=' + isCheck + '&rank=' + rank,
      method: 'PUT'
    });
  },

  allGone(id) {
    return request({
      url: '/znyy/division/allGone/' + id,
      method: 'PUT'
    });
  }
};
