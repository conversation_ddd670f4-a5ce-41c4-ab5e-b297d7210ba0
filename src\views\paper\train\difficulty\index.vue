<template>
  <div class="app-container">

    <el-form :model="dataQuery" ref="queryForm" :inline="true">
      <el-form-item label="所属类型：">
        <el-select v-model="dataQuery.type" placeholder="全部" clearable @change="typeChange">
          <el-option v-for="(item, index) in trainType" :key="index" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="题型：">
        <el-select v-model="dataQuery.questionType" placeholder="全部" clearable>
          <el-option v-for="(item, index) in queryQuestionList" :key="index" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="难度：">
        <el-select v-model="dataQuery.difficulty" placeholder="全部" clearable>
          <el-option v-for="(item, index) in difficultyList" :key="index" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" style="marginRight: 5px" @click="getList">查询</el-button>
      </el-form-item>
    </el-form>
    <el-col :span="24" style="margin-bottom: 30px">

      <el-button type="success" style="marginRight: 5px" @click="handleAdd">新增</el-button>
    </el-col>
    <el-table class="common-table" v-loading="tableLoading" :data="tableData" style="width: 100%;margin-bottom: 20px;"
      row-key="id" border default-expand-all>
      <el-table-column prop="id" label="ID"></el-table-column>
      <el-table-column prop="type" label="所属类型" :formatter="typeFormatter"></el-table-column>
      <el-table-column prop="withdrawnBonus" label="操作">
        <template slot-scope="scope">
          <el-button type="success" size="mini" icon="el-icon-edit-outline" @click="handleUpdate(scope.row.id)">编辑
          </el-button>
          <el-button type="danger" size="mini" icon="el-icon-delete" @click="handleDelete(scope.row.id)">删除
          </el-button>
        </template>
      </el-table-column>
      <el-table-column prop="questionType" label="题型" :formatter="questionTypeFormatter"></el-table-column>
      <el-table-column prop="difficulty" label="难度" :formatter="difficultyFormatter"></el-table-column>
      <el-table-column label="问题数量范围" align="center">
        <template slot-scope="scope">
          {{ scope.row.questionMin }} - {{ scope.row.questionMax }}
        </template>
      </el-table-column>
      <el-table-column label="答案数量范围" align="center">
        <template slot-scope="scope">
          <span v-if="scope.row.answerFlag">
            {{ scope.row.answerMin }} - {{ scope.row.answerMax }}
          </span>
          <span v-else>
            无
          </span>
        </template>
      </el-table-column>
      <el-table-column label="语速" align="center">
        <template slot-scope="scope">
          <span v-if="scope.row.isSpeed">
            {{ scope.row.speed }}
          </span>
          <span v-else>
            无
          </span>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <el-col :span="24" style="margin-bottom: 20px">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>

    <!-- 修改或者新增弹窗 -->
    <el-dialog title="编辑难度" :visible.sync="open" width="70%" @close="cancel">
      <el-form ref="form" :model="form" :rules="rules" label-width="120px" style="width: 70%;">
        <el-row :span="24">
          <el-col :span="8" :xs="24">
            <el-form-item label="所属类型:" prop="type">
              <el-select v-model="form.type" placeholder="全部" clearable @change="handleChange"
                :disabled="form.id !== null">
                <el-option v-for="(item, index) in trainType" :key="index" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" :xs="24">
            <el-form-item label="题型:" prop="questionType">
              <el-select v-model="form.questionType" placeholder="全部" clearable :disabled="form.id !== null">
                <el-option v-for="(item, index) in questionTypeFilter" :key="index" :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" :xs="24">
            <el-form-item label="难度:" prop="difficulty">
              <el-select v-model="form.difficulty" placeholder="全部" clearable>
                <el-option v-for="(item, index) in difficultyList" :key="index" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :span="24">
          <el-col :span="9" :xs="24">
            <el-form-item label="数量最小值：" prop="questionMin">
              <el-input-number v-model="form.questionMin" controls-position="right" :min="1" :step="1" />
            </el-form-item>
          </el-col>
          <el-col :span="8" :xs="24">
            <el-form-item label="数量最大值：" prop="questionMax">
              <el-input-number v-model="form.questionMax" controls-position="right" :min="form.questionMin" :step="1" />
            </el-form-item>
          </el-col>
          <el-col :span="6" :xs="24">
            <el-form-item label="语速：" v-if="form.isSpeed" prop="speed">
              <el-input-number v-model="form.speed" controls-position="right" :min="0.5" :step="0.1" :precision="2" />
            </el-form-item>
          </el-col>

        </el-row>
        <el-row :span="24">
          <el-col :span="7" :xs="24">
            <el-form-item label="答案数限制：" prop="answerFlag">
              <el-radio-group v-model="form.answerFlag">
                <el-radio :label="false">否</el-radio>
                <el-radio :label="true">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="10" :xs="24">
            <el-form-item label="答案数最小值：" v-if="form.answerFlag" prop="answerMin">
              <el-input-number v-model="form.answerMin" controls-position="right" :min="0" :step="1" />
            </el-form-item>
          </el-col>
          <el-col :span="7" :xs="24">
            <el-form-item label="答案数最大值：" v-if="form.answerFlag" prop="answerMax">
              <el-input-number v-model="form.answerMax" controls-position="right" :min="form.answerMin" :step="1" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import difficultyApi from '@/api/paper/train/difficulty'
import { pageParamNames } from '@/utils/constants'
import { mapGetters, mapState } from 'vuex'

export default {
  data() {
    return {
      queryQuestionList: null,
      questionTypeFilter: null,
      title: '',
      dataQuery: {
        type: null,
        questionType: null,
        difficulty: null,
      },
      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      open: false,
      tableData: [],
      form: {},
      // 表单校验
      rules: {
        type: [{ required: true, message: '请选择', trigger: 'blur' }],
        questionType: [{ required: true, message: '请选择', trigger: 'blur' }],
        difficulty: [{ required: true, message: '请选择', trigger: 'blur' }],
        questionMin: [{ required: true, message: '请输入', trigger: 'blur' }],
        questionMax: [{ required: true, message: '请输入', trigger: 'blur' }],
        answerFlag: [{ required: true, message: '请选择', trigger: 'blur' }],
        answerMin: [{ required: true, message: '请输入', trigger: 'blur' }],
        answerMax: [{ required: true, message: '请输入', trigger: 'blur' }],
        isSpeed: [{ required: true, message: '请选择', trigger: 'blur' }],
        speed: [{ required: true, message: '请输入', trigger: 'blur' }],
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    typeChange() {
      this.dataQuery.questionType = null
      this.queryQuestionList = this.trainQuestionType.filter(
        (data) => data.value.includes(this.dataQuery.type)
      )
    },
    handleChange() {
      this.form.questionType = null
      this.questionTypeFilter = this.trainQuestionType.filter(
        (data) => data.value.includes(this.form.type)
      )
      this.form.isSpeed = this.form.type === 'AUDITORY_'
    },
    typeFormatter(row, column, cellValue, index) {
      return this.enumFormat(this.trainType, cellValue)
    },
    questionTypeFormatter(row, column, cellValue, index) {
      return this.enumFormat(this.trainQuestionType, cellValue)
    },
    difficultyFormatter(row, column, cellValue, index) {
      return this.enumFormat(this.difficultyList, cellValue)
    },
    handleUpdate(id) {
      this.reset();
      this.questionTypeFilter = this.trainQuestionType
      difficultyApi.detail(id).then(res => {
        this.form = res.data
        this.questionTypeFilter = this.trainQuestionType.filter(
          (data) => data.value.includes(this.form.type)
        )
        this.open = true
      })
    },
    handleDelete(id) {
      this.$confirm('确定要删除吗？', '删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        difficultyApi.delete(id).then(res => {
          this.$message.success('删除成功！')
          this.getList()
        })
      })
    },
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          difficultyApi.saveOrUpdate(this.form).then(response => {
            this.$message.success('提交成功！')
            this.open = false
            this.getList()
          })
        }
      })
    },

    getList() {
      this.loading = true
      this.dataQuery.pageNum = this.tablePage.currentPage
      this.dataQuery.pageSize = this.tablePage.size
      difficultyApi.list(this.dataQuery).then(res => {
        this.tableData = res.data.data
        this.loading = false
        // 设置后台返回的分页参数
        pageParamNames.forEach(name =>
          this.$set(this.tablePage, name, parseInt(res.data[name]))
        )
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      if (this.$refs['form']) {
        this.$refs['form'].resetFields();
      }
      this.form = {
        id: null,
        type: null,
        questionType: null,
        difficulty: null,
        questionMin: undefined,
        questionMax: undefined,
        answerFlag: false,
        answerMin: undefined,
        answerMax: undefined,
        isSpeed: false,
        speed: undefined
      }
    },

    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val
      this.getList()
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val
      this.getList()
    }
  },
  computed: {
    ...mapGetters('enumItem', ['enumFormat']),
    ...mapState('enumItem', {
      trainType: state => state.train.trainType,
      difficultyList: state => state.train.difficultyList,
      trainQuestionType: state => state.train.trainQuestionType
    })
  }
}
</script>
