<template>
  <div class="app-container">
    <el-form :inline="true" class="container-card" label-width="110px" label-position="left">
      <el-form-item label="门店编号：">
        <el-input id="merchantCode" v-model="dataQuery.merchantCode" name="id" />
      </el-form-item>
      <el-form-item label="学员编号：">
        <el-input  v-model="dataQuery.name" name="id" />
      </el-form-item>
      <el-form-item label="登录账号：">
        <el-input  v-model="dataQuery.merchantName" name="id" />
      </el-form-item>
      <el-form-item label="姓名：">
        <el-input  v-model="dataQuery.merchantName" name="id" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="fetchData()">搜索</el-button>
      </el-form-item>
    </el-form>
    <el-table class="period-table" v-loading="tableLoading" :data="tableData" style="width: 100%;margin-bottom: 20px;" row-key="id" border
      default-expand-all :tree-props="{list: 'children', hasChildren: 'true'}">
      <el-table-column prop="loginName" label="门店编号" ></el-table-column>
      <el-table-column prop="nickName" label="学员编号" ></el-table-column>
      <el-table-column prop="totalBonus" label="登录账号" ></el-table-column>
      <el-table-column prop="withdrawnBonus" label="姓名"></el-table-column>
      <el-table-column prop="loginName" label="年级" ></el-table-column>
      <el-table-column prop="nickName" label="学校" ></el-table-column>
      <el-table-column prop="totalBonus" label="已购学时（节）" ></el-table-column>
      <el-table-column prop="alreadyBonus" label="剩余学时（节）" ></el-table-column>
      <el-table-column prop="withdrawnBonus" label="操作" width="180">
        <template slot-scope="scope">
            <!-- <el-button type="success" size="mini" icon="el-icon-edit-outline" @click="enterChildrenList()">查看课程</el-button> -->
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <el-col :span="24">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]" layout="total, sizes, prev, pager, next, jumper"
        :total="tablePage.totalItems" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    </el-col>
  </div>
</template>

<script>
  import {
    queryOfficialAccountLink
  } from "@/api/wechatPublicAccount";
  import Tinymce from "@/components/Tinymce";
  import {
    pageParamNames
  } from "@/utils/constants";
  export default {
    data() {
      return {
        tableLoading: false,
        // 分页
        tablePage: {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null
        },
        tableData: [{
          name:1,
          title:'1111'
        }],
        dataQuery: {},
        dialogVisible: false,
        updateMarketDate: {},
        addMarketDate: {},
        showLoginAccount: false
      };
    },
    created() {
      this.fetchData();
    },
    methods: {
      // 查询提现列表
      fetchData() {
        const that = this;
        //that.tableLoading = true
        queryOfficialAccountLink(
          that.tablePage.currentPage,
          that.tablePage.size
        ).then(res => {
          console.log(res);
          that.tableData = res.data;
          that.tableLoading = false;
          // 设置后台返回的分页参数
          pageParamNames.forEach(name =>
            that.$set(that.tablePage, name, parseInt(res.data[name]))
          );
        });
      }
    }
  };
</script>

<style>
  .container-card {
    padding: 15px 15px 15px 15px;
    margin-bottom: 15px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  }
  .period-table td,
  .period-table th{
    text-align: center;
  }
  .mt20{
    margin-top: 20px;
  }
  .red{
    color: red;
  }
  .green{
    color: green;
  }
</style>
