<template>
  <div class="container">
    <el-form :inline="true" ref="dataQuery" class="SearchForm" style="padding: 20px 30px 0">
      <el-row>
        <el-col :span="6" :xs="24">
          <el-form-item label="门店名称:">
            <el-input v-model="dataQuery.merchantName" @keyup.enter.native="fetchData()" style="width: 200px" placeholder="请输入门店名称" clearable class="filter-name"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6" :xs="24">
          <el-form-item label="合伙人名称:">
            <el-input v-model="dataQuery.realName" @keyup.enter.native="fetchData()" style="width: 200px" placeholder="请输入合伙人名称" clearable class="filter-name"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6" :xs="24">
          <el-form-item label="合伙人联系方式:">
            <el-input
              @input="handleInput(dataQuery.mobile)"
              v-model="dataQuery.mobile"
              @keyup.enter.native="fetchData()"
              style="width: 200px"
              placeholder="请输入合伙人联系方式"
              clearable
              class="partner-phone"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6" :xs="24">
          <el-form-item>
            <el-button type="primary" size="small" icon="el-icon-search" @click="searchData()">搜索</el-button>
            <el-button size="small" icon="el-icon-refresh" @click="resetData()">重置</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div class="SearchForm">
      <!-- 表格 -->
      <el-table class="common-table" :data="tableData" border :default-sort="{ prop: 'date', order: 'descending' }" v-loading="tableLoading">
        <el-table-column prop="merchantName" label="门店名称"></el-table-column>
        <el-table-column prop="realName" label="合伙人名称"></el-table-column>
        <el-table-column prop="mobile" label="合伙人联系方式"></el-table-column>
        <el-table-column label="考核1状态">
          <template slot-scope="scope">
            <span class="textStatus" :style="{ color: getStatusColor(scope.row.data[2].isPass) }">
              {{ getStatusText(scope.row.data[2].isPass) }}
            </span>
            <i v-if="scope.row.data[2].isPass === '0'" class="el-icon-edit" style="font-size: 14px; color: #1890ff; margin-right: 10px" @click="editDetail(scope.row.data[2])"></i>
            <el-button v-if="scope.row.data[2].isPass !== '9'" type="text" size="mini" @click="editStatus(scope.row.data[2], 2)">详情</el-button>
          </template>
        </el-table-column>
        <el-table-column label="考核2状态">
          <template slot-scope="scope">
            <span class="textStatus" :style="{ color: getStatusColor(scope.row.data[1].isPass) }">
              {{ getStatusText(scope.row.data[1].isPass) }}
            </span>
            <i v-if="scope.row.data[1].isPass === '0'" class="el-icon-edit" style="font-size: 14px; color: #1890ff; margin-right: 10px" @click="editDetail(scope.row.data[1])"></i>
            <el-button v-if="scope.row.data[1].isPass !== '9'" type="text" size="mini" @click="editStatus(scope.row.data[1], 1)">详情</el-button>
          </template>
        </el-table-column>
        <el-table-column label="考核3状态">
          <template slot-scope="scope">
            <span class="textStatus" :style="{ color: getStatusColor(scope.row.data[0].isPass) }">
              {{ getStatusText(scope.row.data[0].isPass) }}
            </span>
            <i class="el-icon-edit" v-if="scope.row.data[0].isPass === '0'" style="font-size: 14px; color: #1890ff; margin-right: 10px" @click="editDetail(scope.row.data[0])"></i>
            <el-button type="text" v-if="scope.row.data[0].isPass !== '9'" size="mini" @click="editStatus(scope.row.data[0], 0)">详情</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- 分页 -->
    <el-col :span="20">
      <el-pagination
        :page-size="tablePage.size"
        layout="total, sizes, prev, pager, next, jumper"
        :total="tablePage.totalItems"
        :page-sizes="[10, 20, 30, 40, 50]"
        @size-change="handleSizeChange"
        :current-page.sync="tablePage.currentPage"
        @current-change="handleCurrentChange"
      />
    </el-col>
    <!-- 详情弹窗 -->
    <el-dialog title="分数详情" :visible.sync="dialogDetail" width="60%">
      <el-table :data="tableDetail" height="200" class="detail-dialog">
        <el-table-column prop="score" label="分数"></el-table-column>
        <el-table-column prop="isPass" label="考核状态">
          <template slot-scope="scope">
            <span style="margin-right: 10px">{{ scope.row.isPass }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="examTime" label="创建时间"></el-table-column>
      </el-table>
    </el-dialog>

    <el-dialog title="状态变更" width="30%" :visible.sync="dialogStatus">
      <span>
        考核状态：
        <el-radio v-model="radio" label="1">已通过</el-radio>
        <el-radio v-model="radio" label="2">未通过</el-radio>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="cancelStatus">取 消</el-button>
        <el-button type="primary" @click="updateStatus">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
  import trainingCampAssessmentAPI from '@/api/newPartner/trainingCampAssessment';
  export default {
    data() {
      return {
        dialogDetail: false,
        dialogStatus: false,
        merchantCode: '', // 查询详情时的商户编号
        radioCourseNo: '', // 编辑状态时的课程编号
        radioCourseId: '', // 编辑状态时的课程编号
        radio: '2', // 单选按钮的选中值
        // 查询条件
        dataQuery: {
          merchantName: '',
          realName: '',
          mobile: ''
        },
        numberCheck: null, // 手机号输入框的值
        // 分页
        tablePage: {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null
        },
        tableLoading: false,
        //表格数据
        tableData: [],
        tableDetail: []
      };
    },
    created() {
      // 从 localStorage 中获取 sysUserInfo 对象
      const sysUserInfo = JSON.parse(localStorage.getItem('sysUserInfo'));
      this.merchantCode = sysUserInfo.merchantCode;
      this.fetchData();
    },
    methods: {
      fetchData() {
        const that = this;
        that.tableLoading = true;
        that.dataQuery.pageNum = that.tablePage.currentPage;
        that.dataQuery.pageSize = that.tablePage.size;
        trainingCampAssessmentAPI.trainingCampAssessmentList({ ...that.dataQuery }).then((res) => {
          if (res.success) {
            // 处理表格数据
            that.tableData = res.data.data.map((item) => ({
              ...item,
              data: item.data.map((course) => ({
                mobile: item.mobile,
                id: item.id,
                name: course.courseName,
                courseId: course.courseId,
                courseNo: course.courseNo,
                isPass: course.isPass.toString() // 将 isPass 转换为字符串
              }))
            }));
          }
          that.tableLoading = false;
          that.tablePage.totalItems = parseInt(res.data.totalItems);
        });
      },
      handleInput(item) {
        const regx = /^\d+$/; // 只能输入数字
        if ((item.length <= 11 && regx.test(item)) || item.length === 0) {
          this.numberCheck = item;
        } else if (item.length > 11 && regx.test(item)) {
          this.numberCheck = item.substr(0, 11);
        } else {
          this.$message.warning('请输入11位手机号(只能输入数字)');
        }
        this.dataQuery.mobile = this.numberCheck;
      },
      searchData() {
        if (this.numberCheck === '' || this.numberCheck === null) {
          this.tablePage = {
            currentPage: 1,
            size: 10,
            totalPage: null,
            totalItems: null
          };
          this.fetchData();
          return;
        }

        if (this.numberCheck === '0') {
          this.$message.warning('请输入大于 0 的整数');
          return;
        }

        if (!/^[1-9]\d*$/.test(this.numberCheck)) {
          this.$message.warning('请输入正整数');
          return;
        }

        if (this.numberCheck.length > 11) {
          this.$message.warning('请输入不超过 11 位的正整数');
          return;
        }

        this.tablePage = {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null
        };

        this.fetchData();
      },
      resetData() {
        this.dataQuery = {
          merchantName: '',
          realName: '',
          mobile: ''
        };
        this.fetchData();
      },
      getStatusColor(status) {
        switch (status) {
          case '1':
            return '#67C23A';
          case '0':
            return '#F56C6C';
          case '9':
            return '#909399';
          default:
            return 'black';
        }
      },
      getStatusText(status) {
        switch (status) {
          case '1':
            return '已通过';
          case '0':
            return '未通过';
          case '9':
            return '未考核';
          default:
            return '未知状态';
        }
      },
      editStatus(row, index) {
        const bvMerchantId = row.id;
        trainingCampAssessmentAPI
          .trainDetailList({ courseId: row.courseId, mobile: row.mobile, bvMerchantId: bvMerchantId })
          .then((res) => {
            if (res.success) {
              if (res.data.length > 0) {
                this.tableDetail = res.data.map((item) => ({
                  isPass: item.isPass === 1 ? '已通过' : item.isPass === 0 ? '未通过' : '未考核',
                  score: item.score,
                  examTime: item.examTime
                }));
                this.dialogDetail = true;
              } else {
                this.$message.warning('暂无详情数据');
              }
            } else {
              this.$message.error('获取详情数据失败');
            }
          })
          .catch((error) => {
            console.error(error);
            this.$message.error('请求失败，请检查网络连接');
          });
      },

      editDetail(row) {
        this.radioCourseNo = row.courseNo;
        this.radioCourseId = row.id;
        if (row.isPass == 0) {
          this.radio = '2';
        }
        this.dialogStatus = true;
        console.log(row, '选择状态');
      },
      updateStatus() {
        if (this.radio === '1') {
          // 处理“已通过”的逻辑
          trainingCampAssessmentAPI
            .updateTrainState({
              courseNo: this.radioCourseNo,
              bvMerchantId: this.radioCourseId
            })
            .then((res) => {
              if (res.success) {
                this.$message.success('修改成功');
                this.fetchData();
              }
            });
        } else if (this.radio === '2') {
          // 处理“未通过”的逻辑
          this.$message.warning('您未修改状态~');
        }
        this.dialogStatus = false; // 关闭对话框
      },
      cancelStatus() {
        this.dialogStatus = false; // 关闭对话框
      },
      // 分页
      handleSizeChange(val) {
        this.tablePage.size = val;
        this.fetchData(); // 分页数据
      },
      handleCurrentChange(val) {
        this.tablePage.currentPage = val;
        this.fetchData(); // 分页数据
      }
    }
  };
</script>

<style lang="scss" scoped>
  .textStatus {
    font-size: 12px;
    margin-right: 10px;
  }
  ::v-deep .el-input .el-input__count .el-input__count-inner {
    padding-left: 0;
    padding-right: 0;
  }
  ::v-deep .filter-name.el-input--suffix .el-input__inner {
    padding-right: 55px !important;
  }
  ::v-deep .partner-phone.el-input--suffix .el-input__inner {
    padding-right: 0 !important;
  }
  ::v-deep .partner-phone .el-icon-circle-close {
    padding-right: 10px !important;
  }
  ::v-deep .partner-phone .el-input__suffix {
    right: 12px !important;
  }
  ::v-deep .detail-dialog.el-table::before {
    background: #fff;
  }
</style>
