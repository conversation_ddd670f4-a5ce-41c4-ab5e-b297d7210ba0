<template>
  <div>
    <el-dialog title="修改推广大使" center :visible.sync="dialogVisible" width="700px" @close="handleOuterClose" :close-on-press-escape="false" :close-on-click-modal="false">
      <el-form ref="channerManagerRef" :label-position="labelPosition" :model="form" label-width="180px" :rules="rules">
        <el-form-item label="门店名称" prop="merchantName">
          <el-input disabled v-model="form.merchantName"></el-input>
        </el-form-item>
        <el-form-item label="原推广大使" prop="refereeName">
          <el-input disabled v-model="form.refereeName"></el-input>
        </el-form-item>
        <el-form-item label="新推广大使编号" prop="newReferenceCode">
          <el-input v-model.trim="form.newReferenceCode" placeholder="请输入新推广大使编号" maxlength="30" show-word-limit @blur="handleRefereeCodeBlur"></el-input>
        </el-form-item>
        <el-form-item label="新推广大使名称" prop="newRefereeName">
          <el-input disabled v-model="form.newRefereeName"></el-input>
        </el-form-item>
        <div style="color: red; text-align: center; margin-bottom: 20px">提示：新推广大使需要为所属俱乐部下已开通的门店或所属俱乐部编号</div>
        <div class="dialog-footer">
          <el-button @click="handleOuterClose">取 消</el-button>
          <el-button :loading="loading" type="primary" @click="handleConfirm">确 定</el-button>
        </div>
      </el-form>
    </el-dialog>
    <InabilityReason
      v-if="dialogReasonVisible"
      :dialogReasonVisible.sync="dialogReasonVisible"
      :showTitleStatus="2"
      :reasonType="reasonType"
      :reasonContent="reasonContent"
      :isSubmitLoading="isSubmitLoading"
      :workStep="workStep"
      @handleSubmit="handleConSubmit"
      @close="handleOuterClose"
    />
  </div>
</template>

<script>
  import InabilityReason from '@/views/merchantManagement/components/InabilityReason.vue';
  import schoolApi from '@/api/schoolList';

  export default {
    name: 'EditOperationFormDialog',
    components: {
      InabilityReason
    },
    props: {
      isShowDialog: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        form: {
          merchantName: '', //门店名称
          operationsName: '', //原所属俱乐部名称
          refereeName: '', //推广大使名称
          refereeCode: '', //推广大使编号
          newReferenceCode: '', //新推广大使编号
          newRefereeName: '', //新推广大使名称确认
          newOperationsName: '', //新俱乐部名称
          newOperationCode: '' //新俱乐部编号
        },
        merchantCode: '',
        merchantId: '',
        loading: false,
        labelPosition: 'left',
        rules: {
          newOperationCode: [{ required: true, message: '新所属俱乐部编号不为空', trigger: ['blur', 'change'] }],
          newReferenceCode: [
            { required: true, message: '新推广大使编号未输入', trigger: ['blur', 'change'] },
            {
              pattern: /^\d{1,30}$/,
              message: '新推广大使编号必须是数字',
              trigger: ['blur', 'change']
            }
          ]
        },
        dialogReasonVisible: false,
        reasonContent: [],
        isSubmitLoading: false,
        workStep: 1,
        reasonType: 0
      };
    },
    computed: {
      dialogVisible: {
        get() {
          return this.isShowDialog;
        },
        set(val) {
          this.$emit('update:isShowCityDialog', val);
        }
      }
    },

    methods: {
      handleRefereeCodeBlur() {
        if (this.form.newReferenceCode) {
          schoolApi
            .getMerchantNameApi({ merchantCode: this.form.newReferenceCode })
            .then((res) => {
              if (res.data) {
                this.$set(this.form, 'newRefereeName', res.data);
                return;
              }
              this.$set(this.form, 'newRefereeName', '');
            })
            .catch((error) => {
              console.error('获取商户名称失败:', error);
              this.$set(this.form, 'newRefereeName', '');
            });
          return;
        }
        this.$set(this.form, 'newRefereeName', '');
      },

      handleOuterClose(val) {
        if (val === 'closeDialog') {
          this.dialogReasonVisible = false;
          return;
        }
        this.reset();
        this.$emit('handleCancel');
      },

      async handleConfirm() {
        if (this.loading) return;
        try {
          await this.$refs.channerManagerRef.validate();
          this.loading = true;
          let params = {
            newReferenceCode: this.form.newReferenceCode,
            merchantCode: this.form.merchantCode
          };
          console.log(`哈哈哈哈🚀🥶💩🚀~123123  ~ params ~ L131: `, params);
          const { data } = await schoolApi.checkNewReferenceApi(params);
          if (!data.canChange) {
            this.reasonContent = data.reasons || [];
            this.workStep = data.step;
            this.dialogReasonVisible = true;
            this.reasonType = 4;
            // this.$emit('handleCancel');
            return;
          }
          this.workStep = 4;
          this.reasonType = 3;
          this.dialogReasonVisible = true;
        } catch (error) {
          console.log('报错了🚀 ~ handleConfirm ~ error:', error);
          // this.$message.error('操作失败');
          this.loading = false;
        } finally {
          this.loading = false;
          // this.$emit('handleCancel', true);
        }
      },
      /**
       * 提交
       * @returns
       */
      async handleConSubmit() {
        if (this.loading) return;
        try {
          this.isSubmitLoading = true;
          this.loading = true;
          let params = {
            merchantCode: this.form.merchantCode,
            newOperationCode: this.form.newOperationCode,
            newReferenceCode: this.form.newReferenceCode
          };
          console.log(`🚀🥶💩🚀~  ~ params ~ L149: `, params);
          this.dialogReasonVisible = true;
          schoolApi.confirmChangeClubAndReferenceApi(params);
          this.$message.success('操作成功');
          this.loading = false;
          this.isSubmitLoading = false;
          this.reset();
          this.$emit('handleCancel', true);
        } catch (error) {
          console.log('🚀 ~ handleConfirm ~ error:', error);
          this.$message.error('操作失败');
          this.loading = false;
        } finally {
          this.loading = false;
          this.isSubmitLoading = false;
        }
      },

      setData(data) {
        Object.assign(this.form, data);
      },

      reset() {
        this.form = {
          mobilePhone: '',
          merchantName: ''
        };
        this.$refs.channerManagerRef.resetFields();
      }
    }
  };
</script>

<style scoped>
  .dialog-footer {
    text-align: center;
  }
</style>
