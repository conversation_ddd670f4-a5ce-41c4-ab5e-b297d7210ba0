/**
 * 课程分类相关接口
 */
import request from '@/utils/request'

export default {
  // 课程分类分页查询
  courseList(pageNum, pageSize, data) {
    return request({
      url: '/znyy/course/category/list/' + pageNum + '/' + pageSize,
      method: 'GET',
      params: data
    })
  },
  // 课程分类新增
  addCourse(data) {
    return request({
      url: '/znyy/course/category',
      method: 'POST',
      data
    })
  },
  // 课程分类编辑
  updateCourse(data) {
    return request({
      url: '/znyy/course/category',
      method: 'PUT',
      data
    })
  },
  // 课程分类查询
  queryActive(id) {
    return request({
      url: '/znyy/course/category/check/' + id,
      method: 'GET'
    })
  },
  // 课程分类开通与暂停
  updateStatus(id,status) {
    return request({
      url: '/znyy/course/category/updateIsEnable?id=' + id + '&isEnable=' + status,
      method: 'PUT'
    })
  },
  getCurrentTime() {
    var _this = this;
    let yy = new Date().getFullYear();
    let mm = new Date().getMonth() + 1;
    let dd = new Date().getDate();
    let hh = new Date().getHours();
    let mf = new Date().getMinutes() < 10 ? '0' + new Date().getMinutes() : new Date().getMinutes();
    let ss = new Date().getSeconds() < 10 ? '0' + new Date().getSeconds() : new Date().getSeconds();
    _this.gettime = yy + '-' + mm + '-' + dd + ' ' + hh + ':' + mf + ':' + ss;
    return _this.gettime
  }
}
