<template>
  <div class="app-container">
    <el-row>
      <el-col :xs="24" :lg="18">
        <!-- 添加或修改弹窗 -->
        <el-form
          :ref="addOrUpdate ? 'addMarketDate' : 'updateMarketDate'"
          :rules="rules"
          :model="addOrUpdate ? addMarketDate : updateMarketDate"
          label-position="right"
          label-width="140px"
          style="width: 100%"
        >
          <el-form-item label="登录账号：" prop="name">
            <template>
              <el-col :xs="24" :span="18">
                <el-input v-if="addOrUpdate" v-model="addMarketDate.name" />
                <el-input
                  v-if="!addOrUpdate"
                  v-model="updateMarketDate.name"
                  disabled
                />
              </el-col>
<!--              <el-button-->
<!--                v-if="!addOrUpdate"-->
<!--                type="success"-->
<!--                style="margin-left: 20px"-->
<!--                @click="openLogin(updateMarketDate.name, updateMarketDate.id)"-->
<!--              >修改登录账号</el-button>-->
            </template>
          </el-form-item>
          <el-form-item label="高报师id：" prop="id" v-show="false">
            <el-col :xs="24" :span="18">
              <el-input v-if="addOrUpdate" v-model="addMarketDate.id" />
              <el-input v-if="!addOrUpdate" v-model="updateMarketDate.id" />
            </el-col>
          </el-form-item>

          <el-form-item label="名称：" prop="applicantsName">
            <el-col :xs="24" :span="18">
              <el-input v-if="addOrUpdate" v-model="addMarketDate.applicantsName" />
              <el-input
                v-if="!addOrUpdate"
                v-model="updateMarketDate.applicantsName"
              />
            </el-col>
          </el-form-item>
          <el-form-item label="身份证：" prop="idCard">
            <el-col :xs="24" :span="18">
              <el-input v-if="addOrUpdate" v-model="addMarketDate.idCard" />
              <el-input v-if="!addOrUpdate" v-model="updateMarketDate.idCard" />
            </el-col>
          </el-form-item>


          <el-form-item label="签约时间：" prop="signupDate">
            <el-col :xs="24" :span="18">
              <el-date-picker
                v-if="addOrUpdate"
                v-model="addMarketDate.signupDate"
                type="date"
                value-format="yyyy-MM-dd HH:mm:ss"
                placeholder="选择日期"
              >
              </el-date-picker>
              <el-date-picker
                v-if="!addOrUpdate"
                v-model="updateMarketDate.signupDate"
                type="date"
                value-format="yyyy-MM-dd HH:mm:ss"
                placeholder="选择日期"
              >
              </el-date-picker>
            </el-col>
          </el-form-item>
          <el-form-item
            v-if="updateMarketDate.isEnable !=1"
            label="开户金额："
            prop="openMoney"
          >
            <el-col :xs="24" :span="18">
              <el-input
                v-if="!addOrUpdate"
                v-model="updateMarketDate.openMoney"
                oninput="value=value.replace(/[^\d]/g,'')"
              />
              <el-input
                v-if="addOrUpdate"
                v-model="addMarketDate.openMoney"
                oninput="value=value.replace(/[^\d]/g,'')"
              />
            </el-col>
          </el-form-item>


          <el-form-item label="合同照片：" prop="contractPhoto">
            <el-col :span="20">
              <el-upload
                ref="clearupload"
                v-loading="uploadLoading"
                list-type="picture-card"
                action
                element-loading-text="图片上传中"
                :limit="10"
                :on-exceed="justPictureNum"
                :file-list="!addOrUpdate ? fileDetailList : fileDetailList.name"
                :http-request="uploadDetailHttp"
                :on-preview="handlePictureCardPreview"
                :on-remove="handleRemoveDetailContract"
              >
                <i class="el-icon-plus" />
              </el-upload>
            </el-col>
            <el-col :xs="24" :span="4">(*支持多张)</el-col>
          </el-form-item>
          <el-form-item label="证件照片:" prop="idCardPhoto">
            <el-col :span="20">
              <el-upload
                ref="clearupload"
                v-loading="uploadLoadingIdCard"
                list-type="picture-card"
                action
                element-loading-text="图片上传中"
                :limit="10"
                :on-exceed="justPictureNumIdCard"
                :file-list="!addOrUpdate ? fileIdCard : fileIdCard.name"
                :http-request="uploadIdCardDetailHttp"
                :on-preview="handlePictureCardPreview"
                :on-remove="handleRemoveDetailIdCard"
              >
                <i class="el-icon-plus" />
              </el-upload>
            </el-col>
            <el-col :xs="24" :span="4">(*支持多张)</el-col>
          </el-form-item>

          <el-form-item label="所在地区：" prop="province">
            <el-col :xs="24" :span="18">
              <el-row :gutter="10">
                <el-col :xs="24" :span="8">
                  <el-input
                    placeholder="安徽省"
                    v-if="addOrUpdate"
                    v-model="addMarketDate.province"
                  />
                  <el-input
                    placeholder="安徽省"
                    v-if="!addOrUpdate"
                    v-model="updateMarketDate.province"
                  />
                </el-col>
                <el-col :xs="24" :span="8">
                  <el-input
                    placeholder="合肥市"
                    v-if="addOrUpdate"
                    v-model="addMarketDate.city"
                  />
                  <el-input
                    placeholder="合肥市"
                    v-if="!addOrUpdate"
                    v-model="updateMarketDate.city"
                  />
                </el-col>
                <el-col :xs="24" :span="8">
                  <el-input
                    placeholder="包河区"
                    v-if="addOrUpdate"
                    v-model="addMarketDate.area"
                  />
                  <el-input
                    placeholder="包河区"
                    v-if="!addOrUpdate"
                    v-model="updateMarketDate.area"
                  />
                </el-col>
              </el-row>
            </el-col>
          </el-form-item>

          <el-form-item label="地址：" prop="address">
            <el-col :xs="24" :span="18">
              <el-input v-if="addOrUpdate" v-model="address" v-show="false" />
              <el-input v-if="!addOrUpdate" v-model="address" v-show="false" />
              <el-input v-if="addOrUpdate" v-model="addMarketDate.address" />
              <el-input
                v-if="!addOrUpdate"
                v-model="updateMarketDate.address"
              />
            </el-col>

          </el-form-item>


          <el-form-item label="简介：" prop="description">
            <el-col :xs="24" :span="18">
              <el-input
                type="textarea"
                resize="none"
                :rows="4"
                v-if="addOrUpdate"
                v-model="addMarketDate.description"
              />
              <el-input
                type="textarea"
                resize="none"
                :rows="4"
                v-if="!addOrUpdate"
                v-model="updateMarketDate.description"
              />
            </el-col>
          </el-form-item>

        </el-form>
      </el-col>
    </el-row>

    <div slot="footer" class="dialog-footer" style="margin: 30px 0 30px 140px">
      <el-button
        v-if="addOrUpdate"
        size="mini"
        type="primary"
        @click="addActiveFun('addMarketDate')"
      >新增</el-button
      >
      <el-button
        v-if="!addOrUpdate && ((updateMarketDate.isCheck == 0 || updateMarketDate.isCheck == -4 ||updateMarketDate.isCheck == 2)||(merchantCode='A0001'))"
        size="mini"
        type="primary"
        @click="updateActiveFun('updateMarketDate')"
      >修改</el-button
      >
      <el-button size="mini" @click="close">关闭</el-button>
    </div>
    <el-dialog
      :visible.sync="dialogUploadVisible"
      :close-on-click-modal="false"
    >
      <img width="100%" :src="dialogImageUrl" alt />
    </el-dialog>
    <!-- 修改登陆账号弹窗 -->
    <el-dialog
      title="修改登录账号"
      :visible.sync="showLoginName"
      width="30%"
      :close-on-click-modal="false"
      @close="close"
    >
      <el-form
        :ref="'updateLoginName'"
        :rules="rulesLoginName"
        :model="updateLoginName"
        label-position="left"
        label-width="120px"
        style="width: 80%"
      >
        <el-form-item label="原登录账号：" prop="oldName">
          <span> {{ updateLoginName.oldName }}</span>
        </el-form-item>
        <el-form-item label="id:" prop="id" v-show="false">
          <el-input v-model="updateLoginName.id" />
        </el-form-item>
        <el-form-item label="新登录账号：" prop="name">
          <el-input v-model="updateLoginName.name" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button
          size="mini"
          type="primary"
          @click="updateDealerLoginName('updateLoginName')"
        >确定</el-button
        >
        <el-button size="mini" @click="closeLoginname">关闭</el-button>
      </div>
    </el-dialog>
    <!-- 审核弹框 -->
  </div>
</template>

<script>

import schoolApi from "@/api/areasSchoolList";
import Tinymce from "@/components/Tinymce";
import enTypes from "@/api/bstatus";
import VueAMap from "vue-amap";
import marketApi from "@/api/marketList";
import { getAddOrUpdate, getDealerId } from "@/utils/auth";
import { ossPrClient } from "@/api/alibaba";
import { isvalidPhone, idCard } from "@/utils/validate";

import expertsApplicantsApi from "@/api/expertsApplicantsList";
export default {

  data() {
    //手机号验证
    var validPhone = (rule, value, callback) => {
      if (!value) {
        callback(new Error("请输入电话号码"));
      } else if (value.length<11 || value.length>11) {
        callback(new Error("请输入正确的11位手机号码"));
      } else {
        callback();
      }
    };
    //身份证表达式
    var isIdCard = (rule, value, callback) => {
      if (!value) {
        callback(new Error("请输入身份证号码"));
      } else if (!idCard(value)) {
        callback(new Error("请输入正确的18位身份证号"));
      } else {
        callback();
      }
    };
    const self = this;
    return {
      // 地图搜索分页相关参数
      pageNum: 1,
      pageSize: 5,
      result: [],
      currentResult: -1,

      disabled: true,
      tableLoading: false,
      showLoginName: false, //登录账号
      updateLoginName: {}, //修改账号
      rulesLoginName: {
        name: [
          {
            required: true,
            message: "必填",
            trigger: "change",
          },
          {
            validator: validPhone,
            trigger: "blur",
          },
        ],
      },
      id: 0,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null,
      },
      ADD: true,
      tableData: [],
      dataQuery: {},
      dialogVisible: false,
      updateMarketDate: {
        areaCoverRange: 500,
        merchantType: "3",
      }, //修改数据对象
      addMarketDate: {
        areaCoverRange: 500,
        merchantType: "3",

      }, //增加数据对象
      showLoginAccount: false,
      addOrUpdate: true,
      rules: {
        name: [
          {
            required: true,
            message: "必填",
            trigger: "blur",
          },
          {
            validator: validPhone,
            trigger: "blur",
          },
        ],
        merchantName: [
          {
            required: true,
            message: "必填",
            trigger: "blur",
          },
        ],
      province: [
          {
            required: true,
            message: "必填",
            trigger: "blur",
          },
        ],
        applicantsName: [
          {
            required: true,
            message: "必填",
            trigger: "blur",
          },
        ],

        rank: [
          {
            required: true,
            message: "必填",
            trigger: "blur",
          },
        ],
        areaCoverRange: [
          {
            required: true,
            message: "必填",
            trigger: "change",
          },
        ],
        signupDate: [
          {
            required: true,
            message: "必填",
            trigger: "change",
          },
        ],
        expireDate: [
          {
            required: true,
            message: "必填",
            trigger: "change",
          },
        ],

        contractPhoto: [
          {
            required: true,
            message: "必填",
            trigger: "change",
          },
        ],
        openMoney: [
          {
            required: true,
            message: "必填",
            trigger: "change",
          },
        ],
        idCardPhoto: [
          {
            required: true,
            message: "必填",
            trigger: "change",
          },
        ],
        province: [
          {
            required: true,
            message: "必填",
            trigger: "change",
          },
        ],
        city: [
          {
            required: true,
            message: "必填",
            trigger: "change",
          },
        ],
        area: [
          {
            required: true,
            message: "必填",
            trigger: "change",
          },
        ],
        address: [
          {
            required: true,
            message: "必填",
            trigger: "change",
          },
        ],
        idCard: [
          {
            required: true,
            message: "请填写正确的身份证号",
            pattern: /(^\d{8}(0\d|10|11|12)([0-2]\d|30|31)\d{3}$)|(^\d{6}(18|19|20)\d{2}(0\d|10|11|12)([0-2]\d|30|31)\d{3}(\d|X|x)$)/,
            trigger: "blur",
          },
          ,{ validator: isIdCard, trigger: "blur" }
        ],
        description: [
          {
            required: true,
            message: "必填",
            trigger: "change",
          },
        ],

      },
      value1: "",
      fileListPending: [], // 待处理已上传图片信息
      fileList: [], // 上传图片已有图片列表
      radio: "3",
      fileDetailListPending: [], // 待处理已上传图片信息
      fileDetailList: [], // 上传图片已有图片列表
      rankType: [], //高报师级别
      uploadLoading: false, // 上传图片加载按钮
      name: "",
      bankType: [], //银行集合
      dialogUploadVisible: false,
      dialogImageUrl: "",
      //证件照图片
      fileIdCard: [],
      uploadLoadingIdCard: false,
      //证件照结束
      uploadLoadingShop: false,
      //支付记录
      uploadLoading4: false,
      // address: "",
      filelistShop: [],
      //支付图片
      fileDetailList4: [],
      merchantCode: "",

      //地图开始
      slotWindow: {
        position: [121.5163285, 31.********],
      },
      currentWindow: {
        position: [0, 0],
        content: "",
        events: {},
        visible: false,
      },
      addDealerShow: true,
      addDealerShow01: true,
      addDealerShow02: true,
      markers: [
        // [121.59996, 31.197646],
        // [121.40018, 31.197622],
        // [121.69991, 31.207649]
      ],
      //搜索结果标注
      markers2: [],
      windows: [],
      window: "",
      searchOption: {
        city: "全国",
        citylimit: false, //是否限制城市内搜索
      },
      center: [117.26696, 31.87869],
      zoom: 12,
      lng: 0,
      lat: 0,
      address: "",
      province: "",
      city: "",
      district: "",
      loaded: false,

    };
  },
  created() {
    this.addOrUpdate = this.$route.query.addOrUpdate;


    this.getRoleTag();

    ossPrClient();
    //编辑回显
    this.updateDealer();
    if (!this.addOrUpdate) {
      this.setTitle("高报师编辑");
    } else {
      this.setTitle("高报师新增");
    }
  },

  mounted() {},
  methods: {
    // 动态设置标签页标题
    setTitle(title) {
      let i = 0;
      let visitedViews = this.$store.getters.visitedViews;
      visitedViews.forEach((route, index) => {
        if (this.$route.path == route.path) {
          i = index;
        }
      });
      this.$route.meta.title = title;
      visitedViews[i].title = title;
    },


    //编辑回显
    updateDealer() {
      const that = this;
      that.addOrUpdate = JSON.parse(
        window.localStorage.getItem("addOrUpdate")
      );
      that.id = window.localStorage.getItem("applicantsId");
      if (!that.addOrUpdate) {

        that.updateMarketDate.id = window.localStorage.getItem("applicantsId");

          expertsApplicantsApi.detail(that.id).then((res) => {
          console.log(res);
          that.center = [res.data.longitude, res.data.latitude];
          that.updateMarketDate = res.data;
          if (
            res.data.contractPhoto !== null &&
            res.data.contractPhoto.length >= 1
          ) {
            for (let i = 0; i < res.data.contractPhoto.length; i++) {
              that.fileDetailList.push({
                url: that.aliUrl + res.data.contractPhoto[i],
              });
            }
          } else {
            that.fileDetailList = [];
          }

          console.log(res.data.idCardPhoto);
          if (
            res.data.idCardPhoto !== null &&
            res.data.idCardPhoto.length >= 1
          ) {
            for (let i = 0; i < res.data.idCardPhoto.length; i++) {
              // that.fileDetailList.push({
              //     url: that.aliUrl + res.data.idCardPhoto[i]
              //   })
              that.fileIdCard.push({
                url: that.aliUrl + res.data.idCardPhoto[i],
              });
            }
          } else {
            that.fileIdCard = [];
          }


        });
      } else {
      }
    },
    onSearchResult(pois) {
      let latSum = 0;
      let lngSum = 0;
      if (pois.length > 0) {
        pois.forEach((poi) => {
          let { lng, lat } = poi;
          lngSum += lng;
          latSum += lat;
          this.markers.push([poi.lng, poi.lat]);
        });
        let center = {
          lng: lngSum / pois.length,
          lat: latSum / pois.length,
        };
        this.center = [center.lng, center.lat];
      }
    },
    addMarker: function () {
      let lng = 121.5 + Math.round(Math.random() * 1000) / 10000;
      let lat = 31.197646 + Math.round(Math.random() * 500) / 10000;
      this.markers.push([lng, lat]);
    },


    //鼠标滑过
    markerMouse(e) {
      infoWindow.setContent(e.target.content);
      infoWindow.open(map, e.target.getPosition());
    },
    //TODO
    close() {
      const that = this;
      // that.$router.push({
      //   path: "/merchantManagement/dealerList",
      // });
      // 关闭当前标签页
      that.$store.dispatch("delVisitedViews", this.$route);
      that.$router.go(-1);

      that.addMarketDate = {};
      that.updateMarketDate = {};
      that.addOrUpdate = true;
      that.$refs.clearupload.clearFiles();
    },
    //鼠标滑过
    markerOut(e) {
      map.clearInfoWindow();
    },

    //新增操作
    clickAdd() {
      this.$refs.clearupload.clearFiles();
      this.dialogVisible = true;
    },
    // 打开修改登陆账号
    openLogin(name, id) {
      this.showLoginName = true;
      this.updateLoginName.id = id;
      this.updateLoginName.oldName = name;
    },
    //改变状态
    change() {},
    changeTime(val) {
      console.log(val);
    },
    //修改账号
    updateDealerLoginName(ele) {
      const that = this;
      that.$refs[ele].validate((valid) => {
        if (valid) {
          marketApi.markertUpdateLogin(that.updateLoginName).then(() => {
            that.$nextTick(() => that.fetchData());
            that.updateMarketDate.name = this.updateLoginName.name;
            that.updateLoginName.name = "";
            that.showLoginName = false;
            that.$message.success("修改登录账号成功");
          });
        }
      });
    },
    //修改账号关闭
    closeLoginname() {
      this.showLoginName = false;
    },
    getRoleTag() {
      schoolApi.getCurrentAdmin().then((res) => {
        console.log(res.data.merchantCode + "wyy");
        this.roleTag = res.data.roleTag;
        this.merchantCode = res.data.merchantCode;
      });
    },
    //新增操作高报师
    addActiveFun(ele) {
      const that = this;

      if (that.fileDetailList.length <= 0) {
        that.$message.error("合同照片不能为空");
        return false;
      }
      if (that.fileIdCard.length <= 0) {
        that.$message.error("证件照片不能为空");
        return false;
      }
      if (that.addMarketDate.city == "" || that.addMarketDate.province == "" || that.addMarketDate.area == "") {
        that.$message.error("省市区不能为空");
        return false;
      }
      if(that.addMarketDate.address==''){
        that.$message.error("不能为空");
        return false;
      }

      that.addMarketDate.contractPhoto = that.fileDetailList;
      that.addMarketDate.idCardPhoto = that.fileIdCard;



      that.$refs[ele].validate((valid) => {
        // 表单验证
        if (valid) {
          const loading = this.$loading({
            lock: true,
            text: "新增高报师",
            spinner: "el-icon-loading",
            background: "rgba(0, 0, 0, 0.7)",
          });

          expertsApplicantsApi
            .addExpertsApplicants(that.addMarketDate)
            .then(() => {
              that.dialogVisible = false;
              loading.close();
              that.addMarketDate = {};
              that.fileIdCard = [];
              that.fileDetailList = [];
              that.filelistShop = [];
              that.fileDeatiList4 = []
              that.$router.push({
                path: "/dzy/card/expertsApplicantsList",
              });
              that.$message.success("新增成功");
            })
            .catch((err) => {
              loading.close();
            });
        } else {
          console.log("error submit!!");
          //loading.close();
          return false;
        }
      });
    },
    //修改操作
    updateActiveFun(ele) {
      console.log("======================")
      const that = this;

      if (that.fileDetailList.length <= 0) {
        that.$message.error("合同照片不能为空");
        return false;
      }
      if (that.fileIdCard.length <= 0) {
        that.$message.error("证件照片不能为空");
        return false;
      }
      if (that.updateMarketDate.province == "") {
        that.$message.error("省不能为空");
        return false;
      }
      if (that.updateMarketDate.city == "") {
        that.$message.error("市不能为空");
        return false;
      }
      if (that.updateMarketDate.area == "") {
        that.$message.error("区不能为空");
        return false;
      }

      that.updateMarketDate.contractPhoto = [];
      for (let i = 0; i < that.fileDetailList.length; i++) {
        let index = that.fileDetailList[i].url.lastIndexOf("manage");
        that.updateMarketDate.contractPhoto.push(
          that.fileDetailList[i].url.substring(
            index,
            that.fileDetailList[i].url.length
          )
        );
      }
      that.updateMarketDate.idCardPhoto = [];
      for (var i = 0; i < that.fileIdCard.length; i++) {
        let idCardIndex = that.fileIdCard[i].url.lastIndexOf("manage");
        that.updateMarketDate.idCardPhoto.push(
          that.fileIdCard[i].url.substring(
            idCardIndex,
            that.fileIdCard[i].url.length
          )
        );
      }

      that.$refs[ele].validate((valid) => {
        // 表单验证
        if (valid) {
          const loading = this.$loading({
            lock: true,
            text: "修改高报师",
            spinner: "el-icon-loading",
            background: "rgba(0, 0, 0, 0.7)",
          });
          expertsApplicantsApi
            .addExpertsApplicants(that.updateMarketDate)
            .then((res) => {
              that.dialogVisible = false;
              loading.close();
              that.fileIdCard = [];
              that.fileDetailList = [];
              that.filelistShop = [];
              that.$router.push({
                path: "/dzy/card/expertsApplicantsList",
              });
              that.$message.success("修改成功");
            }, s => {
              if (s == 'error') {
                loading.close();
              }
            })
            .catch((err) => {
              loading.close();
            });
        } else {
          console.log("error submit!!");
          //loading.close();
          return false;
        }
      });
    },


    //上传图片
//支付记录照片上传
    uploadDetailHttp4({file}) {
      this.uploadLoading4 = true
      const that = this
      const fileName = 'manage/' + Date.parse(new Date())
      that.$nextTick(function () {
        ossPrClient()
          .put(fileName, file)
          .then(({res, url, name}) => {
            if (res && res.status === 200) {
              console.log(`阿里云OSS上传图片成功回调1`, res, url, name)
              if (!that.addOrUpdate) {
                that.fileDetailList4.push({
                  uid: file.uid,
                  url: url
                })
              } else {
                // 新增上传图片
                that.fileDetailList4.push(name)
              }
              that.$nextTick(() => {
                that.uploadLoading4 = false
              })
            }
          })
          .catch((err) => {
            that.$message.error('上传图片失败请检查网络或者刷新页面')
            console.log(`阿里云OSS上传图片失败回调`, err)
          })
      })
    },
    // 上传合同照片请求
    uploadDetailHttp({file}) {
      this.uploadLoading = true;
      const that = this;
      const fileName = "manage/" + Date.parse(new Date());
      that.$nextTick(function () {
        ossPrClient()
          .put(fileName, file)
          .then(({res, url, name}) => {
            if (res && res.status === 200) {
              console.log(`阿里云OSS上传图片成功回调1`, res, url, name);
              if (!that.addOrUpdate) {
                that.fileDetailList.push({
                  uid: file.uid,
                  url: url,
                });
              } else {
                // 新增上传图片
                that.fileDetailList.push(name);
              }
              that.$nextTick(() => {
                that.uploadLoading = false;
              });
            }
          })
          .catch((err) => {
            that.$message.error("上传图片失败请检查网络或者刷新页面");
            console.log(`阿里云OSS上传图片失败回调`, err);
          });
      });
    },
    // 上传图片预览
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url;
      this.dialogUploadVisible = true;
    },
    // 上传图片数量超限
    justPictureNum(file, fileList) {
      this.$message.warning(`当前限制选择10个文件`);
    },
    //移除图片
    // 删除合同照片
    handleRemoveDetailContract(file, fileList) {
      const that = this;
      if (!that.addOrUpdate) {
        that.fileDetailList = fileList;
      } else {
        for (let a = 0; a < that.fileDetailList.length; a++) {
          if (that.fileDetailList[a].substring(7, 17) === parseInt(file.uid / 1000)) {
            that.fileDetailList.splice(a, 1)
          }
        }
      }
    },

    //证件照片开始
    //证件照上传
    uploadIdCardDetailHttp({file}) {
      this.uploadLoadingIdCard = true;
      const that = this;
      const fileName = "manage/" + Date.parse(new Date());
      that.$nextTick(function () {
        ossPrClient()
          .put(fileName, file)
          .then(({res, url, name}) => {
            if (res && res.status === 200) {
              console.log(`阿里云OSS上传图片成功回调1`, res, url, name);
              if (!that.addOrUpdate) {
                that.fileIdCard.push({
                  uid: file.uid,
                  url: url,
                });
              } else {
                // 新增上传图片
                that.fileIdCard.push(name);
              }
              that.$nextTick(() => {
                that.uploadLoadingIdCard = false;
              });
            }
          })
          .catch((err) => {
            that.$message.error("上传图片失败请检查网络或者刷新页面");
            console.log(`阿里云OSS上传图片失败回调`, err);
          });
      });
    },
    //照片限制
    // 上传图片数量超限
    justPictureNumIdCard(file, fileList) {
      this.$message.warning(`当前限制选择10个文件`);
    },
    handleRemoveDetailIdCard(file, fileList) {
      const that = this;
      if (!that.addOrUpdate) {
        that.fileIdCard = fileList;
      } else {
        for (let a = 0; a < that.fileIdCard.length; a++) {
          if (that.fileIdCard[a].substring(7, 17) == parseInt(file.uid / 1000)) {
            that.fileIdCard.splice(a, 1)
          }
        }
      }
    },
    //支付记录照片删除
    handleRemoveDetail4(file, fileList) {
      const that = this
      if (!that.addOrUpdate) {
        that.fileDetailList4 = fileList
      } else {
        for (let a = 0; a < that.fileDetailList4.length; a++) {
          if (that.fileDetailList4[a].substring(7, 17) == parseInt(file.uid / 1000)) {
            that.fileDetailList4.splice(a, 1)
          }
        }
      }
    },

    //照片限制
    // 上传图片数量超限
    justPictureNumShop(file, fileList) {
      this.$message.warning(`当前限制选择10个文件`);
    },


    req_post(val) {
      console.log(val);
    },


    changeSizeHandler(size) {
      this.pageSize = size;
    },
    handleCurrentChange(currentPage) {
      this.pageNum = currentPage;
    },
  },
};
</script>

<style>
.map-box {
  position: relative;
}

.search-box {
  position: absolute !important;
  top: 10px;
  left: 10px;
}

.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.red {
  color: red;
}

.green {
  color: green;
}

.blue {
  color: blue;
}

.prompt {
  padding: 10px;
}

.result {
  position: absolute;
  top: 0;
  left: 100%;
  width: 300px;
  /* height: 450px; */
  margin-left: 10px;
  background-color: #ffffff;
  border: 1px solid silver;
}

.result-list {
  display: flex;
  align-items: center;
  /* margin-bottom: 10px; */
  line-height: 1.6;
  overflow: hidden;
  cursor: pointer;
}

.result label {
  display: block;
  width: 19px;
  height: 33px;
  margin-right: 10px;
  line-height: 20px;
  text-align: center;
  font-size: 12px;
  color: #ffffff;
  background: url("https://webapi.amap.com/theme/v1.3/markers/n/mark_bs.png") no-repeat center/cover;
}

.result-list.active label {
  background: url("http://webapi.amap.com/theme/v1.3/markers/n/mark_r.png") no-repeat center/cover;
}

.list-right {
  flex: 1;
}

.result .name {
  font-size: 14px;
  color: #565656;
}

.result .address {
  color: #999;
}

.search-table {
  height: 380px;
  margin-bottom: 10px !important;
}

.search-table th {
  display: none;
}

.search-table td {
  padding: 5px 0 !important;
  border-bottom: none;
}

.el-vue-search-box-container {
  width: 90% !important;
}

.el-date-editor.el-input {
  width: 100% !important;
}

@media screen and (max-width: 767px) {
  .app-container {
    /* padding: 20px 10px; */
  }

  .result {
    display: none;
  }
}
</style>
