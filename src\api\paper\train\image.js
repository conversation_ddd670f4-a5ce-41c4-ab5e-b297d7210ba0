
import request from '@/utils/request'

export default {
  upload(data){
    return request({
      url: '/paper/web/trainImage/upload',
      method: 'POST',
      data
    })
  },
  // 分页查询
  list(data) {
    return request({
      url: '/paper/web/trainImage/list',
      method: 'GET',
      params: data
    })
  },
//添加
  saveOrUpdate(data) {
    return request({
      url: '/paper/web/trainImage/create',
      method: 'POST',
      data
    })
  },
  detail(id){
    return request({
      url: '/paper/web/trainImage/detail',
      method: 'GET',
      params: {
        id:id
      }
    })
  },
  delete(id){
    return request({
      url: '/paper/web/trainImage/delete',
      method: 'DELETE',
      params: {
        id:id
      }
    })
  }
}
