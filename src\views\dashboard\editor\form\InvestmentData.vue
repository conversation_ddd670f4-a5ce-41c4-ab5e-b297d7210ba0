<!--招商数据-->
<template>
  <div>
    <el-row :gutter="10" style="height: 400px;padding-top: 10px;">
      <el-col :span="12" style="height: 100%">
        <el-card shadow="always" body-style="padding:5px">
          <div>
            <el-col :span="16">
              <div>
                <h3>招商数据</h3>
              </div>
            </el-col>
            <el-col :span="8"><div>
              <el-radio-group style="float: right;margin-top: 5%" v-model="radio" @change = "initData(radio)">
                <el-radio-button label="day">日</el-radio-button>
                <el-radio-button label="week">周</el-radio-button>
                <el-radio-button label="month">月</el-radio-button>
              </el-radio-group>
            </div>
            </el-col>
            <el-table
              :data="tableData"
              v-loading="tableLoading"
              height="320"
              border
              style="width: 100%">
              <el-table-column prop="date" label="日期">
              </el-table-column>
              <el-table-column prop="division" label="事业部(个)">
                <template slot-scope="scope">
                  <span>{{scope.row.division ? scope.row.division : "0"}}</span>
                </template>
              </el-table-column>
              <el-table-column prop="company" label="分公司(个)">
                <template slot-scope="scope">
                  <span>{{scope.row.company ? scope.row.company : "0"}}</span>
                </template>
              </el-table-column>
              <el-table-column prop="agent" label="市级服务商(个)">
                <template slot-scope="scope">
                  <span>{{scope.row.agent ? scope.row.agent : "0"}}</span>
                </template>
              </el-table-column>
              <el-table-column prop="dealer" label="托管中心(个)">
                <template slot-scope="scope">
                  <span>{{scope.row.dealer ? scope.row.dealer : "0"}}</span>
                </template>
              </el-table-column>
              <el-table-column prop="school" label="门店(个)">
                <template slot-scope="scope">
                  <span>{{scope.row.school ? scope.row.school : "0"}}</span>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-card>
      </el-col>
      <el-col :span="12" style="height: 100%">
        <el-card shadow="always" body-style="padding:5px;height:85%" style="height: 100%">
          <h3>招商数据图表</h3>
          <div id="main" :style="{width: '100%', height: '100%'}">
          </div>
        </el-card>
      </el-col>
    </el-row>  </div>
</template>

<script>
import elasticSearchApi from "@/api/elasticSearch";

export default {
  name: "investmentDataForm",
  props: {
    // 表单
    form: {
      type: Object,
    },
  },
  data(){
    return {
      radio:"day",
      tableLoading: false,
      tableData: [],
      option:{},
      xAxisData:[],
      app:{},
    }
  },
  mounted() {
    this.initEcharts();
    this.initData()
  },
  methods:{
    initEcharts(){
      var chartDom = document.getElementById('main');
      var myChart = this.$echarts.init(chartDom);
      const posList = [
        'left',
        'right',
        'top',
        'bottom',
        'inside',
        'insideTop',
        'insideLeft',
        'insideRight',
        'insideBottom',
        'insideTopLeft',
        'insideTopRight',
        'insideBottomLeft',
        'insideBottomRight'
      ];
      this.app.configParameters = {
        rotate: {
          min: -90,
          max: 90
        },
        align: {
          options: {
            left: 'left',
            center: 'center',
            right: 'right'
          }
        },
        verticalAlign: {
          options: {
            top: 'top',
            middle: 'middle',
            bottom: 'bottom'
          }
        },
        position: {
          options: posList.reduce(function (map, pos) {
            map[pos] = pos;
            return map;
          }, {})
        },
        distance: {
          min: 0,
          max: 100
        }
      };
      this.app.config = {
        rotate: 90,
        align: 'left',
        verticalAlign: 'middle',
        position: 'insideBottom',
        distance: 15,
        onChange: function () {
          const labelOption = {
            rotate: this.app.config.rotate,
            align: this.app.config.align,
            verticalAlign: this.app.config.verticalAlign,
            position: this.app.config.position,
            distance: this.app.config.distance
          };
          myChart.setOption({
            series: [
              {
                label: labelOption
              },
              {
                label: labelOption
              },
              {
                label: labelOption
              },
              {
                label: labelOption
              }
            ]
          });
        }
      };
      const labelOption = {
        show: true,
        position: this.app.config.position,
        distance: this.app.config.distance,
        align: this.app.config.align,
        verticalAlign: this.app.config.verticalAlign,
        rotate: this.app.config.rotate,
        formatter: '{c}  {name|{a}}',
        fontSize: 16,
        rich: {
          name: {}
        }
      };
      this.option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {
          data: ['Forest', 'Steppe', 'Desert', 'Wetland']
        },
        toolbox: {
          feature: {
            dataView: { show: true, readOnly: false },
            magicType: { show: true, type: ['line', 'bar'] },
            restore: { show: true },
            saveAsImage: { show: true }
          }
        },
        xAxis: [
          {
            type: 'category',
            axisTick: { show: false },
            data: [],
            axisLabel:{
              showMaxLabel: true,
              interval: 0,
              rotate: 0
            }
          }
        ],
        yAxis: [
          {
            type: 'value'
          }
        ],
        series: []
      };

      this.option && myChart.setOption(this.option);
    },
    initData(type){
      if(!type){
        type = 'day';
      }
      this.tableLoading = true;
      elasticSearchApi.initData(type).then(res=>{
        this.tableLoading = false;
        this.tableData = res.data.data.data;
        let data = res.data.data;
        this.option.xAxis[0].data = data.dates;

        const labelOption = {
          show: false,
          position: this.app.config.position,
          distance: this.app.config.distance,
          align: this.app.config.align,
          verticalAlign: this.app.config.verticalAlign,
          rotate: this.app.config.rotate,
          formatter: '{c}  {name|{a}}',
          fontSize: 16,
          rich: {
            name: {}
          }
        };

        let chartDtos = data.chartDtos;
        this.option.series = [];
        this.option.legend.data = [];
        for (let i = 0; i < chartDtos.length; i++) {
          let ser = {};
          this.option.legend.data.push(chartDtos[i].name);
          ser.name = chartDtos[i].name;
          ser.data = chartDtos[i].data;
          ser.type = 'bar';
          ser.label= labelOption;
          ser.emphasis ={
            focus: 'series'
          };
          this.option.series.push(ser);
        }

        let chartDom = document.getElementById('main');
        var myChart = this.$echarts.init(chartDom);
        myChart.clear();
        myChart.setOption(this.option,true);
      }).catch(err=>{})
    },
  }
}
</script>

<style scoped>

</style>
