<template>
  <div class="app-container">
    <el-form :inline="true" class="container-card" label-width="110px" label-position="left">
      <el-row>
        <el-col :span="8" :xs="24">
          <el-form-item label="姓名：">
            <el-input v-model="dataQuery.realName" placeholder="请输入姓名" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="课程类型:">
            <el-select v-model="dataQuery.courseContentType" filterable value-key="value" placeholder="请选择">
              <el-option
                v-for="(item, index) in [
                  { value: 'Word', label: '单词' },
                  { value: 'Reading', label: '阅读理解' },
                  { value: 'PrintClose', label: '结业报告' },
                  { value: 'SuperRead', label: '新阅读理解' }
                ]"
                :key="index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="打印状态：">
            <el-select v-model="dataQuery.isEnable" placeholder="全部" style="width: 185px">
              <el-option
                v-for="(item, index) in [
                  { value: 1, label: '已打印' },
                  { value: 0, label: '未打印' }
                ]"
                :key="index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="16" :xs="24">
          <el-form-item>
            <el-form-item label="结束时间：">
              <el-date-picker
                style="width: 100%"
                value-format="yyyy-MM-dd HH:mm:ss"
                clearable
                v-model="value1"
                type="datetimerange"
                align="right"
                unlink-panels
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="['00:00:00', '23:59:59']"
              ></el-date-picker>
            </el-form-item>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item>
            <el-button type="primary" size="small" icon="el-icon-search" @click="fetchData01()">搜索</el-button>
            <el-button type="primary" size="small" icon="el-icon-refresh" @click="fetchData03()">刷新</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <el-table
      class="common-table"
      v-loading="tableLoading"
      :data="tableData"
      style="width: 100%; margin-bottom: 20px"
      row-key="id"
      border
      default-expand-all
      :tree-props="{ list: 'children', hasChildren: 'true' }"
    >
      <el-table-column prop="wordPrintCode" label="打印编号" sortable></el-table-column>
      <el-table-column prop="realName" label="姓名" sortable></el-table-column>
      <el-table-column prop="withdrawnBonus" label="操作" width="380">
        <template slot-scope="scope">
          <el-button type="success" size="mini" icon="el-icon-edit-outline" @click="jumpOpenCourse(scope.row.wordPrintCode, scope.row.realName, scope.row.courseContentType)">
            打印
          </el-button>
          <el-button
            size="mini"
            type="primary"
            icon="el-icon-edit-outline"
            v-if="scope.row.courseContentType == 'Word'"
            @click="jumpOpenDayCourse(scope.row.wordPrintCode, scope.row.realName, scope.row.courseContentType)"
          >
            打印当天所有
          </el-button>
          <el-button
            size="mini"
            type="warning"
            icon="el-icon-edit-outline"
            v-if="scope.row.courseContentType == 'Word'"
            @click="jumpOpenGraduation(scope.row.wordPrintCode, scope.row.realName, scope.row.courseContentType, scope.row.title)"
          >
            结业单词
          </el-button>
        </template>
      </el-table-column>
      <el-table-column prop="title" label="标题" sortable></el-table-column>
      <el-table-column prop="courseContentTypeStr" label="课程类型" sortable></el-table-column>
      <el-table-column prop="addTime" label="结束时间" sortable></el-table-column>
      <el-table-column prop="isEnable" label="状态" sortable>
        <template slot-scope="scope">
          <span class="blue" v-if="scope.row.isEnable === 1">已打印</span>
          <span class="red" v-else>未打印</span>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <el-col :span="24" style="overflow-x: auto" :xs="24">
      <el-pagination
        :current-page="tablePage.currentPage"
        :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="tablePage.totalItems"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-col>
  </div>
</template>

<script>
  import wordPrintApi from '@/api/areasStudentWordPrintList';
  import Tinymce from '@/components/Tinymce';
  import { pageParamNames } from '@/utils/constants';
  import ls from '@/api/sessionStorage';

  export default {
    name: 'areasStudentWordPrintList',
    data() {
      return {
        tableLoading: false,
        // 分页
        tablePage: {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null
        },
        tableData: [],
        dataQuery: {
          studentCode: '',
          merchantCode: '',
          startDate: '',
          endDate: '',
          loginName: '',
          realName: ''
        },
        studyRank: [],
        value1: '',
        exportLoading: false
      };
    },
    created() {
      this.fetchData();
      // this.getStudyRank();
    },
    methods: {
      // 获取起始时间
      dateVal(e) {
        // console.log(e[0]);
        this.dataQuery.addStartTime = e[0];
        this.dataQuery.addEndTime = e[1];
      },
      fetchData01() {
        this.tablePage = {
          currentPage: 1,
          size: this.tablePage.size,
          totalPage: null,
          totalItems: null
        };
        this.fetchData();
      },
      fetchData03() {
        this.value1 = '';
        this.tablePage = {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null
        };
        this.dataQuery = {
          studentCode: '',
          merchantCode: '',
          startDate: '',
          endDate: '',
          loginName: '',
          realName: ''
        };
        this.fetchData();
      },
      // 查询提现列表
      fetchData() {
        const that = this;
        if (that.value1 != '' && that.value1 != null && that.value1 != undefined) {
          if (that.value1.length > 0) {
            that.dataQuery.addStartTime = that.value1[0];
            that.dataQuery.addEndTime = that.value1[1];
          } else {
            that.dataQuery.addStartTime = '';
            that.dataQuery.addEndTime = '';
          }
        } else {
          that.dataQuery.addStartTime = '';
          that.dataQuery.addEndTime = '';
        }
        that.tableLoading = true;
        wordPrintApi.wordPrintList(that.tablePage.currentPage, that.tablePage.size, that.dataQuery).then((res) => {
          that.tableData = res.data.data;
          that.tableLoading = false;
          // 设置后台返回的分页参数
          pageParamNames.forEach((name) => that.$set(that.tablePage, name, parseInt(res.data[name])));
        });
      },
      // 分页
      handleSizeChange(val) {
        this.tablePage.size = val;
        this.fetchData();
      },
      handleCurrentChange(val) {
        this.tablePage.currentPage = val;
        this.fetchData();
      },
      jumpOpenDayCourse(wordPrintCode, name, courseContentType) {
        var url = '/student/studentDayPrint';
        ls.setItem('wordPrintCode', wordPrintCode);
        ls.setItem('name', name);
        ls.setItem('courseContentType', courseContentType);
        this.$router.push({
          path: url,
          query: {
            wordPrintCode: wordPrintCode,
            name: name,
            courseContentType: courseContentType
          }
        });
      },
      //结业单词
      jumpOpenGraduation(wordPrintCode, name, courseContentType, title) {
        var url = '/student/studentGraduation';
        ls.setItem('wordPrintCode', wordPrintCode);
        ls.setItem('name', name);
        ls.setItem('courseContentType', courseContentType);
        ls.setItem('title', title);
        ls.setItem('type', courseContentType);
        this.$router.push({
          path: url,
          query: {
            wordPrintCode: wordPrintCode,
            name: name,
            courseContentType: courseContentType,
            title: title
          }
        });
      },
      // 跳转到打印页面
      jumpOpenCourse(wordPrintCode, name, courseContentType) {
        const that = this;
        var url = '';
        // return console.log(courseContentType, '111111111111111')
        // 判断课程类型
        if (courseContentType == 'Word') {
          url = '/student/studentTestPrint';
        } else if (courseContentType == 'Reading' || courseContentType == 'SuperRead') {
          url = '/student/studentTestPrintReading';
        } else {
          url = '/student/studentTestPrintReport';
        }
        ls.setItem('wordPrintCode', wordPrintCode);
        ls.setItem('name', name);
        ls.setItem('courseContentType', courseContentType);
        that.$router.push({
          path: url,
          query: {
            wordPrintCode: wordPrintCode,
            name: name,
            courseContentType: courseContentType
          }
        });
      }
    }
  };
</script>

<style>
  .container-card {
    padding: 15px 15px 15px 15px;
    margin-bottom: 15px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  }

  .period-table td,
  .period-table th {
    text-align: center;
  }

  .mt20 {
    margin-top: 20px;
  }

  .red {
    color: red;
  }

  .green {
    color: green;
  }

  .blue {
    color: blue;
  }
</style>
