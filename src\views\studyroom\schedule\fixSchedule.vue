<template>
  <div class="app-container">
    <el-form :inline="true" ref="queryForm" class="container-card" label-width="110px">
      <el-form-item label="名称：">
        <el-input v-model="dataQuery.className"  placeholder="请输入名称" clearable/>
      </el-form-item>
      <el-form-item label="分类：">
        <el-select v-model="dataQuery.courseType" placeholder="全部" clearable>
          <el-option v-for="(item,index) in [{label:'系统课',value:'1'},{label:'非系统课',value:'2'}]" :key="index" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="学段：">
        <el-select v-model="dataQuery.level" placeholder="全部" clearable>
          <el-option v-for="(item,index) in [{label:'小学',value:'小学'},{label:'初中',value:'初中'},{label:'高中',value:'高中'}]" :key="index" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="getPageList()">搜索</el-button>
      </el-form-item>

    </el-form>
    <el-col :span="24" style="margin-bottom: 30px">
      <el-button type="success"  icon="el-icon-plus" size="mini" @click="handleAdd()">新增</el-button>
    </el-col>

    <el-table class="common-table" v-loading="tableLoading" :data="tableData" style="width: 100%;margin-bottom: 20px;" row-key="id" border
              default-expand-all >
      <el-table-column prop="id" label="ID"></el-table-column>
      <el-table-column prop="className" label="班级名称"></el-table-column>
      <el-table-column label="操作">
        <template slot-scope="scope">
          <el-button  type="text" size="mini" @click="handleView">查看详情</el-button>
          <el-button  type="text" size="mini" @click="">编辑</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="courseType" label="课程分类">
        <template slot-scope="scope">
          <span v-if="scope.row.courseType==1">系统课</span>
          <span v-else>非系统课</span>
        </template>
      </el-table-column>
      <el-table-column prop="roomNum" label="房间数"></el-table-column>
      <el-table-column prop="joinUserCount" label="加入人数"></el-table-column>
      <el-table-column prop="level" label="学段"></el-table-column>
      <el-table-column prop="createTime" label="创建时间" ></el-table-column>
    </el-table>
    <!-- 分页 -->
    <el-col :span="24" style="margin-bottom: 20px">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]" layout="total, sizes, prev, pager, next, jumper"
                     :total="tablePage.totalItems" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    </el-col>

    <!-- 添加弹窗 -->
    <el-dialog title="新增固定班" :visible.sync="open" width="60%" :close-on-click-modal="false" @close="close">
      <el-form ref="form" :model="form" :rules="rules" label-width="120px" style="width: 50%;">
        <el-form-item label="课程分类:" prop="courseType">
          <el-radio-group v-model="form.courseType">
            <el-radio-button label="1">系统课</el-radio-button>
            <el-radio-button label="2">非系统课</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="阶段等级:" prop="level">
          <el-radio-group v-model="form.level">
            <el-radio label="小学">小学</el-radio>
            <el-radio label="初中">初中</el-radio>
            <el-radio label="高中">高中</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="班级名称:" prop="className">
          <el-input v-model="form.className"/>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="submitForm()">确定</el-button>
        <el-button size="mini" @click="close">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import studyScheduleApi from "@/api/studyroom/studySchedule";
  import {pageParamNames} from "@/utils/constants";

  export default {
    name:'Schedule',
    data() {
      return {
        tableLoading: false,
        // 分页
        tablePage: {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null
        },
        open:false,
        tableData: [],
        dataQuery: {
          className:'',
          courseType:'',
          level:'',
        },
        form:{},
        // 表单校验
        rules: {
          courseType: [
            { required: true, message: "请选择分类", trigger: "blur" }
          ],
          level: [
            { required: true, message: "请选择阶段等级", trigger: "blur" }
          ],
          className: [
            { required: true, message: "请输入班级名称", trigger: "blur" }
          ],
        }
      };
    },
    created() {
      this.getPageList();
    },
    methods: {
      /** 详情按钮操作 */
      handleView(row) {
        this.reset();
        const id = row.id;
      },

      //提交
      submitForm() {
        this.$refs["form"].validate(valid => {
          if (valid) {
            studyScheduleApi.addSchedule(this.form).then(response => {
              this.$message.success("提交成功！")
              this.open = false;
              this.getPageList();
            });
          }
        });
      },
      getPageList(){
        this.tableLoading = true;
        studyScheduleApi.scheduleList(this.tablePage.currentPage, this.tablePage.size, this.dataQuery).then(res=>{
          console.log(res.data.data);
          this.tableData = res.data.data;
          this.tableLoading = false;
          // 设置后台返回的分页参数
          pageParamNames.forEach(name => this.$set(this.tablePage, name, parseInt(res.data[name])))
        })
      },
      /** 新增按钮操作 */
      handleAdd() {
        this.reset();
        this.open = true;
      },
      close(){
        this.open = false;
        this.reset();
      },
      // 表单重置
      reset() {
        this.form = {
          courseType:null,
          level:null,
          className:null
        };
        if (this.$refs['form']) {
          this.$refs['form'].resetFields();
        }
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.resetForm("queryForm");
        this.getPageList();
      },
      resetForm(refName) {
        if (this.$refs[refName]) {
          this.$refs[refName].resetFields();
        }
      },
      // 分页
      handleSizeChange(val) {
        this.tablePage.size = val;
        this.getPageList();
      },
      handleCurrentChange(val) {
        this.tablePage.currentPage = val;
        this.getPageList();
      },
    }
  };
</script>

<style>
  .container-card {
    padding: 15px 15px 15px 15px;
    margin-bottom: 15px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  }
  .period-table td,
  .period-table th{
    text-align: center;
  }
  @media (max-width:767px) {
    .el-message-box{
      width: 80%!important;
    }
  }
</style>
