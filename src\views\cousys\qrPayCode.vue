<template>
    <div class="app-container">
<!--      <el-card>-->
      <el-form
        class="recharge-form"
        label-width="110px"
        label-position="right">
      <el-row>
        <el-col class="paike" style="font-size: 30px" :span="8" :xs="24">
          <span>排课中心</span>
        </el-col>
      </el-row>

      <el-steps :space="200" :active="activeIndex" finish-status="success" align-center="center" class="width:600px">
        <el-step title="完善基础信息"></el-step>
        <el-step title="付款"></el-step>
        <el-step title="完善详细信息"></el-step>
        <el-step title="预排课"></el-step>
      </el-steps>

      <div class="demo-image__placeholder">
        <div class="block" align="center">
          <el-image style="width: 400px; height: 400px" :src="tableData"></el-image>
          <br><br>
          <b style="font-size: 20px">微信扫一扫付款</b>
        </div>
      </div>
    <br><br>
      <el-row>
        <el-col :span="24" style="margin-bottom: 30px; padding-left: 970px">
          <el-button type="warning" size="100px" @click="nextStep()">下一步</el-button>
        </el-col>
      </el-row>
<!--      </el-card>-->
      </el-form>
    </div>
</template>

<script>
import {
  pageParamNames
} from '@/utils/constants'
import cousysApi from '@/api/cousys'

export default {
  data() {
    return {
      activeIndex: 2,//进度
      tableLoading: false,
      tableData: '',//表格数据
      courseOrderId: ''
    }
  },
  created() {
    this.fetchData()
  },
  methods: {
    fetchData() {
      const that = this
      that.courseOrderId = window.localStorage.getItem('courseOrderId')
      that.tableLoading = true
      cousysApi.getPayCode(that.courseOrderId).then((res) => {
        that.tableData = res.data
        that.tableLoading = false
      })
    },
    nextStep() {
      const that = this;
      cousysApi.findOrderInfoById(that.courseOrderId).then(res => {
        if (res.data.orderStatus===1){
          window.localStorage.setItem("cousysMemberCode", res.data.memberCode);
          window.localStorage.setItem("cousysStudentCode", res.data.studentCode);
          that.$router.push({
            path: "/cousys/cousysDetailInfo"
          });
        }else {
          that.$message.error('请先支付订单')
        }
      })
    },
  },
  // beforeDestroy() {
  //   window.localStorage.setItem("courseOrderId", "");
  // }
}
</script>

<style scoped>
.paike {
  padding-bottom: 50px;
  padding-top: 20px;
}

.studentClass {
  padding-left: 20px;
  padding-top: 20px;
  padding-bottom: 20px;
}

.peopleClass {

  padding-top: 20px;
  padding-bottom: 20px;
}


</style>
