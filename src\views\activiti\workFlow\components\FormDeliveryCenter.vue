<template>
  <div>
    <el-form label-position="right" label-width="140px" style="width: 100%">
      <el-form-item label="登录账号：" prop="name">
        <el-col :xs="24" :span="18">
          <el-input v-model="form.name" readonly/>
        </el-col>
      </el-form-item>
      <el-form-item label="交付中心名称：" prop="merchantName">
        <el-col :xs="24" :span="18">
          <el-input v-model="form.merchantName" readonly/>
        </el-col>
      </el-form-item>
      <el-form-item label="负责人：" prop="realName">
        <el-col :xs="24" :span="18">
          <el-input v-model="form.realName" readonly/>
        </el-col>
      </el-form-item>
      <el-form-item label="身份证：" prop="idCard">
        <el-col :xs="24" :span="18">
          <el-input v-model="form.idCard" readonly/>
        </el-col>
      </el-form-item>
      <el-form-item label="推荐人：">
        <el-col :xs="24" :span="18">
          <el-input v-model="form.marketPartner" readonly/>
        </el-col>
      </el-form-item>
      <el-form-item label="开户一级分润比例：">
        <el-col :xs="24" :span="18">
          <el-input v-model="form.profitRankOne" readonly/>
          <span>（例：0.1）</span>
        </el-col>
      </el-form-item>
      <el-form-item label="开户二级分润比例：">
        <el-col :xs="24" :span="18">
          <el-input v-model="form.profitRankTwo"/>
          <span>（例：0.1）</span>
        </el-col>
      </el-form-item>
      <el-form-item label="续费一级分润比例：">
        <el-col :xs="24" :span="18">
          <el-input v-model="form.reProfitRankOne"/>
          <span>（例：0.1）</span>
        </el-col>
      </el-form-item>
      <el-form-item label="续费二级分润比例：">
        <el-col :xs="24" :span="18">
          <el-input v-model="form.reProfitRankTwo"/>
          <span>（例：0.1）</span>
        </el-col>
      </el-form-item>
      <el-form-item label="二级分润：" prop="reProfitRank">
        <template>
          <el-radio disabled v-model="form.reProfitRank" label="1">开通</el-radio>
          <el-radio disabled v-model="form.reProfitRank" label="0">暂停</el-radio>
        </template>
      </el-form-item>
      <el-form-item label="中心线上门店：">
        <el-col :xs="24">
          <el-input v-model="form.childMerchantCode" readonly/>
        </el-col>
      </el-form-item>
      <el-form-item label="提现银行：">
        <el-col :xs="24">
          <el-select
            style="width: 100%"
            filterable
            v-model="form.openBank"
          >
            <el-option
              disabled
              v-for="(item, index) in bankType"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-col>
      </el-form-item>
      <el-form-item label="提现账号：">
        <el-col :xs="24">
          <el-input v-model="form.bankNo" readonly/>
        </el-col>
      </el-form-item>
      <el-form-item label="开户金额：" prop="openMoney">
        <el-col :xs="24">
          <el-input
            readonly
            v-model="form.openMoney"
            type="number"
            maxlength="10"
            isNumber="true"
            min="1"
          />
        </el-col>
      </el-form-item>
      <el-form-item label="商户类型：" prop="merchantType">
        <template>
          <el-radio disabled v-model="form.merchantType" label="3">个人</el-radio>
          <el-radio disabled v-model="form.merchantType" label="2">企业</el-radio>
        </template>
      </el-form-item>
      <el-form-item label="是否完款：" prop="paymentIsComplete">
        <template>
          <el-radio disabled v-model="form.paymentIsComplete" label="1">完款</el-radio>
          <el-radio disabled v-model="form.paymentIsComplete" label="0">未完款</el-radio>
        </template>
      </el-form-item>
      <el-form-item label="签约时间：" prop="signupDate">
        <el-col :xs="24">
          <el-date-picker
            readonly
            v-model="form.signupDate"
            value-format="yyyy-MM-dd HH:mm:ss"
            type="datetime"
            placeholder="选择日期"
          ></el-date-picker>
        </el-col>
      </el-form-item>
      <el-form-item label="到期时间：" prop="expireDate">
        <el-col :xs="24">
          <el-date-picker
            readonly
            v-model="form.expireDate"
            value-format="yyyy-MM-dd HH:mm:ss"
            type="datetime"
            placeholder="选择日期"
          ></el-date-picker>
        </el-col>
      </el-form-item>
      <el-form-item ref="file_Rule" label="合同照片：" prop="contractPhoto">
        <el-col :xs="24" :span="20">
          <el-image
            style="width: 100px; height: 100px"
            :src="form.contractPhoto[0]"
            :preview-src-list="form.contractPhoto">
          </el-image>
        </el-col>
      </el-form-item>
      <el-form-item label="证件照片：" prop="idCardPhoto">
        <el-col :xs="24" :span="20">
          <el-image
            style="width: 100px; height: 100px"
            :src="form.idCardPhoto[0]"
            :preview-src-list="form.idCardPhoto">
          </el-image>
        </el-col>
      </el-form-item>
      <el-form-item label="门店照片：">
        <el-col :xs="24" :span="20">
          <el-image
            style="width: 100px; height: 100px"
            :src="form.shopPhoto[0]"
            :preview-src-list="form.shopPhoto">
          </el-image>
        </el-col>
      </el-form-item>
      <el-form-item label="所在地区：" prop="address">
        <el-col :xs="24">
          <el-row :gutter="10">
            <el-col :xs="24" :span="8">
              <el-input placeholder="安徽省" v-model="form.province" readonly/>
            </el-col>
            <el-col :xs="24" :span="8">
              <el-input placeholder="合肥市" v-model="form.city" readonly/>
            </el-col>
            <el-col :xs="24" :span="8">
              <el-input placeholder="包河区" v-model="form.area" readonly/>
            </el-col>
          </el-row>
        </el-col>
      </el-form-item>

      <el-form-item label="地址：" prop="address">
        <el-col :xs="24">
          <el-input v-model="form.address" readonly/>
        </el-col>
      </el-form-item>

      <el-form-item label="交付中心简介：" prop="description">
        <el-col :xs="24" :span="18">
          <el-input
            v-model="form.description"
            type="textarea"
            resize="none"
            readonly
            :rows="4"
          />
        </el-col>
      </el-form-item>

    </el-form>
  </div>
</template>

<script>
import agentApi from "@/api/agentAddList";

export default {
  name: "FormDeliveryCenter",
  props: {
    // 表单
    form: {
      type: Object
    },
  },
  data() {
    return {
      bankType: [], // 提现银行
    }
  },
  created() {
    agentApi.categoryType("BankType").then((res) => {
      this.bankType = res.data;
    });
  }
}
</script>

<style scoped>

</style>
