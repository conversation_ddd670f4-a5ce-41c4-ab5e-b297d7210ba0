<template>
  <div class="app-container">
    <el-form :inline="true" class="container-card" label-width="96px" label-position="left">
      <el-form-item>
        <el-form-item label="录入时间：" clearable>
          <el-date-picker style="width: 100%;" value-format="yyyy-MM-dd" clearable v-model="regTime" type="daterange"
            align="right" unlink-panels range-separator="至" start-placeholder="开始时间" end-placeholder="结束时间">
          </el-date-picker>
        </el-form-item>
      </el-form-item>
      <el-form-item label="地区：">
        <el-cascader :options="addressOptions" :props="props" v-model="address" filterable clearable></el-cascader>
      </el-form-item>
      <el-form-item label="负责人：">
        <el-input v-model="dataQuery.realName" placeholder="请输入姓名" clearable />
      </el-form-item>
      <el-form-item label="等级:">
        <el-select v-model="dataQuery.permission" filterable value-key="value" placeholder="请选择" clearable>
          <el-option v-for="(item,index) in [{ value :'City',label:'市级'},{value: 'Area', label: '县级'}]" :key="index"
            :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="getList()">搜索</el-button>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-re" @click="resetQuery()">重置</el-button>
      </el-form-item>
    </el-form>

    <el-table class="common-table" v-loading="tableLoading" :data="tableData" style="width: 100%;margin-bottom: 20px;"
      row-key="id" border default-expand-all :tree-props="{list: 'children', hasChildren: 'true'}">
      <el-table-column prop="merchantCode" label="服务商Code" sortable></el-table-column>
      <el-table-column prop="siteName" label="区域" sortable></el-table-column>
      <el-table-column prop="realName" label="负责人" sortable></el-table-column>
      <el-table-column prop="phoneNumber" label="联系电话" sortable></el-table-column>
      <el-table-column prop="regTime" label="录入时间" sortable></el-table-column>
      <el-table-column prop="permission" label="等级" sortable></el-table-column>

      <el-table-column prop="withdrawnBonus" label="操作">
        <template slot-scope="scope">
          <el-button v-if="scope.row.permission===''" type="primary" size="mini"
            @click="addPermission(scope.row.merchantCode,'City')">指派市级权限
          </el-button>
          <el-button v-if="scope.row.permission===''" type="success" size="mini"
            @click="addPermission(scope.row.merchantCode,'Area')">指派县级权限
          </el-button>
          <el-button v-if="scope.row.permission!==''" type="warning" size="mini"
            @click="deletePermission(scope.row.merchantCode)">撤销权限
          </el-button>

        </template>
      </el-table-column>

    </el-table>

    <!-- 分页 -->
    <el-col :span="20" style="overflow-x: auto;" :xs="24">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>

  </div>
</template>

<script>

import cousysApi from '@/api/cousys'
import { pageParamNames } from '@/utils/constants'
import coursePriceApi from '@/api/coursePrice'
import tutorApi from '@/api/tutor'

export default {
  data() {
    return {
      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      props: {
        value: 'name',
        label: 'name'
      },
      tableData: [],
      dataQuery: {
        province: '',
        city: '',
        area: '',
        realName: '',
        startTime: '',
        endTime: '',
        permission: ''
      },
      address: [],
      addressOptions: [],
      regTime: [],
      tutorInfoVo: {},
      //新增教练 
      tutorAdd: {
        tutorType: '',
        tutorNme: '',
        gender: '',
        phoneNumber: ''
      }
    }
  },
  created() {
    this.getList()
    cousysApi.getAllRegion().then(res => {
      this.addressOptions = res.data
    })
  },
  methods: {
    //获取列表
    getList() {
      const that = this
      if (that.regTime !== '' && that.regTime !== null && that.regTime !== undefined) {
        if (that.regTime.length > 0) {
          that.dataQuery.startTime = that.regTime[0]
          that.dataQuery.endTime = that.regTime[1]
        } else {
          that.dataQuery.startTime = ''
          that.dataQuery.endTime = ''
        }
      } else {
        that.dataQuery.startTime = ''
        that.dataQuery.endTime = ''
      }
      that.tableLoading = true
      var b = this.address
      if (b != null) {
        that.dataQuery.province = b[0]
        that.dataQuery.city = b[1]
        that.dataQuery.area = b[2]
      } else {
        that.dataQuery.province = null
        that.dataQuery.city = null
        that.dataQuery.area = null
      }
      that.tableLoading = true
      cousysApi.getPermissionList(that.tablePage.currentPage, that.tablePage.size, that.dataQuery).then(res => {
        that.tableData = res.data.data
        console.log(res.data.data)
        that.tableLoading = false
        // 设置后台返回的分页参数
        pageParamNames.forEach(name => this.$set(this.tablePage, name, parseInt(res.data[name])))
      })
    },

    //撤销权限
    deletePermission(merchantCode) {
      this.$confirm('确定撤销吗?', '撤销权限', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        cousysApi.deletePermission(merchantCode).then(res => {
          this.getList()
        }).catch(err => {
        })
      }).catch(err => {
      })
    },

    //重置按钮
    resetQuery() {
      this.inputTime = []
      this.dataQuery = {
        status: '',
        gender: '',
        phoneNumber: ''
      }
      this.getList()
    },

    //添加权限
    addPermission(merchantCode, permission) {
      this.$confirm('确定设置此权限?', '添加权限', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        cousysApi.addPermission(merchantCode, permission).then(res => {
          this.getList()
        }).catch(err => {
        })
      }).catch(err => {
      })
    },

    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val
      this.getList()
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val
      this.getList()
    }
  }
}
</script>

<style>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

@media screen and (max-width: 767px) {
  .recharge-dialog .el-dialog {
    width: 90% !important;
  }

  .el-message-box {
    width: 80% !important;
  }
}
</style>
