<template>
  <div class="app-container">
    <el-form :inline="true" class="container-card" label-width="170px" label-position="left">
      <el-form-item label="超级俱乐部编号：">
        <el-input id="merchantCode" v-model="dataQuery.merchantCode" name="id" clearable placeholder="请输入服务商编号" />
      </el-form-item>
      <el-form-item label="登录账号：">
        <el-input id="name" v-model="dataQuery.name" name="id" clearable placeholder="请输入登录账号" />
      </el-form-item>
      <el-form-item label="超级俱乐部名称：">
        <el-input id="merchantName" v-model="dataQuery.merchantName" name="id" clearable placeholder="请输入服务商名称" />
      </el-form-item>
      <el-form-item label="渠道合作伙伴编号：">
        <el-input id="refereeCode" v-model="dataQuery.merchantPartnerCode" name="id" placeholder="请输入渠道合作伙伴编号" clearable />
      </el-form-item>
      <!-- <el-form-item label="剩余学习管理系统数：">
        <el-input id="marketPartner" v-model="dataQuery.studyNum" name="id" placeholder="请输入剩余学习管理系统数" clearable />
      </el-form-item> -->
      <el-form-item label="负责人名称：">
        <el-input id="realName" v-model="dataQuery.realName" name="id" clearable placeholder="请输入负责人名称" />
      </el-form-item>
      <el-form-item label="业务开展地：" prop="address">
        <el-cascader style="width: 220px" :options="regionData" v-model="selectedCity" clearable :props="{ value: 'label' }" placeholder="请选择业务开展地"></el-cascader>
      </el-form-item>
      <el-form-item label="所属品牌" v-if="!isZxBrand">
        <el-input id="brandName" v-model="dataQuery.brandName" name="id" clearable placeholder="请输入所属品牌" />
      </el-form-item>
      <el-form-item label="级别：">
        <el-select v-model="dataQuery.rank" value-key="value" placeholder="请选择" clearable>
          <el-option
            v-for="(item, index) in [
              { value: 'A', label: 'A级别' },
              { value: 'B', label: 'B级别' },
              { value: 'C', label: 'C级别' },
              { value: 'D', label: 'D级别' },
              { value: 'E', label: 'E级别' },
              { value: 'F', label: 'F级别' }
            ]"
            :key="index"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="是否完款：">
        <el-select v-model="dataQuery.paymentIsComplete" value-key="value" placeholder="请选择" clearable>
          <el-option
            v-for="(item, index) in [
              { value: '1', label: '完款' },
              { value: '0', label: '未完款' }
            ]"
            :key="index"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="状态：">
        <el-select v-model="dataQuery.isEnable" filterable value-key="value" placeholder="请选择" clearable>
          <el-option
            v-for="(item, index) in [
              { value: '1', label: '开通' },
              { value: '0', label: '暂停' },
              { value: '2', label: '禁用' }
              // { value: '-3', label: '终止' },
              // { value: '-1', label: '系统关闭' },
            ]"
            :key="index"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="渠道合作伙伴协议状态：">
        <el-select v-model="dataQuery.merchantDealStatus" filterable value-key="value" placeholder="请选择" clearable>
          <el-option
            v-for="(item, index) in [
              { value: 2, label: '已签署' },
              { value: 1, label: '未签署' }
            ]"
            :key="index"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <!-- 9.15 删除城市所属地筛选项和弹框以及按钮功能 -->
      <!-- <el-form-item label="城市所属地">
        <el-cascader style="width: 300px" :options="areaData" v-model="selectedCity" clearable placeholder="请选择所属地" :props="{ value: 'label' }"></el-cascader>
      </el-form-item> -->

      <el-form-item label="添加时间：">
        <el-date-picker
          v-model="regTime"
          type="daterange"
          align="right"
          unlink-panels
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd HH-mm-ss"
          style="width: 100%"
          clearable
        ></el-date-picker>
      </el-form-item>

      <el-form-item label="开通时间：" v-if="isAdmin || isMerchantManager">
        <el-date-picker
          v-model="enableTime"
          type="daterange"
          align="right"
          unlink-panels
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd HH-mm-ss"
          style="width: 100%"
          clearable
        ></el-date-picker>
      </el-form-item>

      <!-- <el-form-item label="培训是否缴费：" v-if="isPermit">
        <el-select v-model="dataQuery.isPay" value-key="value" placeholder="请选择" clearable>
          <el-option
            v-for="(item, index) in [
              { value: 0, label: '未缴费' },
              { value: 1, label: '已缴费' },
              { value: 2, label: '无需缴费' }
            ]"
            :key="index"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item> -->
      <el-row>
        <!-- <el-col :span="8" :xs="24">
          <el-form-item label="状态：">
            <el-select v-model="dataQuery.isEnable" filterable value-key="value" placeholder="请选择" clearable>
              <el-option v-for="(item, index) in [
                { value: '1', label: '开通' },
                { value: '0', label: '暂停' },
                // { value: '-3', label: '终止' },
                // { value: '-1', label: '系统关闭' },
              ]" :key="index" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col> -->

        <el-col :span="22" :xs="24" style="text-align: right">
          <el-button type="primary" icon="el-icon-search" @click="fetchData01()">搜索</el-button>
          <el-button type="primary" icon="el-icon-search" @click="reset()">重置</el-button>
        </el-col>
      </el-row>
    </el-form>

    <el-col :span="24" style="margin-bottom: 10px">
      <!-- 隐藏品牌角色新增俱乐部的新增按钮 -->
      <el-button type="success" icon="el-icon-plus" v-if="checkPermission(['b:merchant:dealerList:add']) && !isZxBrand" @click="clickAdd" size="mini">添加</el-button>
      <!-- <el-button type="warning" v-if="checkPermission(['b:merchant:dealerList:export'])" icon="el-icon-document-copy" @click="exportFlow" v-loading="exportLoading" size="mini"> -->
      <el-button type="warning" v-if="isAdmin" icon="el-icon-document-copy" @click="exportFlow" v-loading="exportLoading" size="mini">导出</el-button>

      <span style="margin-left: 20px" v-if="isZxBrand">
        <span>
          剩余全款俱乐部学习系统数量:
          <span class="red">{{ managementSystem.fullPayment }}</span>
        </span>
        <span style="margin-left: 10px">
          剩余定金俱乐部学习系统数量：
          <span class="red">{{ managementSystem.deposit }}</span>
        </span>
      </span>
      <!-- <el-button
        type="warning"
        icon="el-icon-document-copy"
        @click="exportFlow1"
        v-loading="exportLoading1"
        v-if="checkPermission(['b:merchant:dealerList:exportList'])"
        size="mini"
      >
        导出超级俱乐部运营数据
      </el-button> -->
    </el-col>
    <el-col style="margin-bottom: 10px" v-if="isZxBrand && systemNum > 0">
      <p class="card">
        <i class="el-icon-warning"></i>
        &nbsp;您还有
        <el-tag type="danger">{{ systemNum }}</el-tag>
        位俱乐部未签署《渠道合作伙伴协议》，为保证系统正常使用，请要求尽快签署
      </p>
    </el-col>
    <el-table class="common-table" v-loading="tableLoading" :data="tableData" style="width: 100%; margin-bottom: 20px" row-key="id" border default-expand-all>
      <el-table-column prop="operationsName" label="渠道合作伙伴名称" align="center" width="120px"></el-table-column>
      <el-table-column prop="operationsCode" label="渠道合作伙伴编号" align="center" width="160px">
        <template v-slot="{ row }">
          <div style="display: flex; justify-content: center; align-items: center">
            <span v-if="row.operationsCode">{{ row.operationsCode }}</span>
            <i v-if="row.operationsCode && isAdmin" class="el-icon-edit" style="color: #409eff; margin-left: 10px" @click="editOperationsCode(row, true, 1)"></i>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="merchantCode" label="编号" align="center" width="120px"></el-table-column>
      <el-table-column prop="name" label="登录账号" align="center" width="110px"></el-table-column>
      <!--      <el-table-column label="操作" align="center" width="400" v-if="!isCityBranch">-->
      <el-table-column label="操作" align="center" min-width="500" v-if="!isCityBranch">
        <template slot-scope="scope">
          <el-button type="success" icon="el-icon-edit-outline" v-if="checkPermission(['b:merchant:dealerList:edit'])" @click="updateDealerList(scope.row.id, false)" size="mini">
            编辑
          </el-button>

          <el-button
            type="success"
            icon="el-icon-edit-outline"
            v-if="checkPermission(['b:merchant:dealerList:resubmit']) && scope.row.isOldData == 0 && scope.row.flowIsEnd == 1 && scope.row.flowEndStatus == 0"
            @click="updateDealerList(scope.row.id, true)"
            size="mini"
          >
            重新提交
          </el-button>
          <!-- <el-button type="success" icon="el-icon-edit-outline" v-if="isHaveCityPermission" @click="editCity(scope.row, scope.row.id)" size="mini">编辑所属地</el-button> -->
          <el-button
            type="danger"
            v-if="isAdmin && scope.row.isEnable == 1 && checkPermission(['b:schoolList:enableAndDisable'])"
            @click="disableAndOpenBtn(scope.row, 2)"
            size="mini"
          >
            <div style="display: flex; justify-content: center; align-items: center">
              <img style="width: 10px; height: 10px; margin-right: 4px" src="./images/close.png" alt="" />
              禁用
            </div>
          </el-button>
          <el-button
            type="success"
            v-if="isAdmin && scope.row.isEnable == 2 && checkPermission(['b:schoolList:enableAndDisable'])"
            @click="disableAndOpenBtn(scope.row, 1)"
            size="mini"
          >
            <div style="display: flex; justify-content: center; align-items: center; height: 16px">
              <img style="width: 10px; height: 10px; margin-right: 4px" src="./images/open.png" alt="" />
              启用
            </div>
          </el-button>
          <el-button type="primary" v-if="isAdmin && scope.row.isEnable == 0 && scope.row.paymentIsComplete == 0" @click="openSubmit(scope.row, scope.row.id)" size="mini">
            <div style="display: flex; justify-content: center; align-items: center">
              <img style="width: 10px; height: 10px; margin-right: 4px" src="./images/people-selected.png" alt="" />
              开通
            </div>
          </el-button>
          <!-- <el-button
            v-if="isPermit && scope.row.isCheck === 1 && scope.row.isPay === 0"
            type="success"
            size="mini"
            :disabled="scope.row.trainingBtnLoading"
            @click="trainingPay(scope.row, scope.$index)"
          >
            培训缴费
          </el-button> -->
          <!-- 接口开放前，按原有逻辑进行开通功能，接口开放后，培训缴费完成才可显示开通按钮 -->
          <!-- <el-button type="warning" size="mini" icon="el-icon-switch-button" v-if="
              (isPayOpen || !isPayOpen) &&
              ((scope.row.isEnable === 0 && scope.row.isCheck === 1) || (scope.row.isEnable === 0 && scope.row.flowEndStatus === 1) || scope.row.isEnable === -3)
            " @click="dealerStatus(scope.row.id, 1)">
            开通
          </el-button>
          <el-button type="danger" size="mini" icon="el-icon-video-pause" v-if="
              (scope.row.isEnable === 1 && scope.row.isCheck === 1 && scope.row.paymentIsComplete != '0') ||
              (scope.row.isEnable === 1 && scope.row.flowEndStatus === 1 && scope.row.paymentIsComplete != '0')
            " @click="dealerStatus(scope.row.id, 0)">
            暂停
          </el-button> -->
          <el-button
            type="danger"
            size="mini"
            icon="el-icon-circle-check"
            v-if="checkPermission(['b:risk:user:clear']) && (scope.row.isEnable === -1 || scope.row.isEnable === -2)"
            @click="dealerStatus(scope.row.id, 1)"
          >
            解封
          </el-button>
          <!-- && checkPermission(['b:merchant:dealerList:allgone']) -->
          <!-- <el-button type="success"  size="mini" icon="el-icon-video-pause" v-if="scope.row.paymentIsComplete == '0'&&!isHaveCityPermission" @click="dealerPaymentIsComplete(scope.row)">
            完款
          </el-button> -->
          <el-button
            type="success"
            size="mini"
            icon="el-icon-video-pause"
            v-if="scope.row.paymentIsComplete == '0' && isHaveFullPayment && (scope.row.isEnable == 1 || scope.row.isEnable == 0)"
            @click="dealerPaymentIsComplete(scope.row)"
          >
            <!--            v-if="scope.row.paymentIsComplete == '0' && isHaveFullPayment && scope.row.isEnable == 1"-->
            完款
          </el-button>
          <!-- TODO: 待接口开放后，开通按钮显示 -->
          <!-- <el-button type="primary" size="mini" v-if="scope.row.merchantDealStatus == 1" @click="Contract(scope.row.id, scope.row.isBrandRoleTag)">合同签署</el-button> -->
          <el-button
            type="warning"
            size="mini"
            icon="el-icon-link"
            v-if="
              false &&
              checkPermission(['b:merchant:dealerList:assignDelivery']) &&
              ((scope.row.isOldData === 1 && scope.row.isCheck === 1) || (scope.row.isOldData === 0 && scope.row.flowEndStatus === 1))
            "
            @click="openAssignDelivery(scope.row.merchantCode)"
          >
            指定交付中心
          </el-button>
          <el-button
            type="warning"
            size="mini"
            icon="el-icon-link"
            v-if="
              false &&
              checkPermission(['b:merchant:dealerList:liftDelivery']) &&
              ((scope.row.isOldData === 1 && scope.row.isCheck === 1) || (scope.row.isOldData === 0 && scope.row.flowEndStatus === 1))
            "
            @click="liftDelivery(scope.row.merchantCode)"
          >
            解除交付中心
          </el-button>
          <el-button type="primary" size="mini" @click="clubRegistrationCode(0, scope.row.merchantCode)" v-if="isZxBrand && scope.row.isEnable == 1">俱乐部注册码</el-button>
        </template>
      </el-table-column>
      <el-table-column label="定制logo" align="center" width="100px" v-if="!isCityBranch && !isHaveLogoPermission && !isMerchantManager">
        <template slot-scope="{ row }">
          <el-switch v-model="row.logoEnable" active-color="#13ce66" :active-value="1" :inactive-value="0" @change="(e) => logoChange(e, row)"></el-switch>
        </template>
      </el-table-column>
      <el-table-column prop="merchantName" label="名称" align="center" width="200px"></el-table-column>
      <el-table-column prop="studyNum" label="剩余学习管理系统" align="center" width="120px"></el-table-column>

      <el-table-column prop="realName" label="负责人姓名" align="center" width="100px"></el-table-column>
      <el-table-column label="业务开展地" align="center" width="130px">
        <template v-slot="{ row }">
          <span>{{ [row.province, row.city, row.area].filter(Boolean).join('/') || '-' }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column prop="localCity" label="城市所属地" align="center" width="130px"></el-table-column> -->
      <el-table-column prop="brandName" label="所属品牌" align="center" width="130px" v-if="isAdmin">
        <!--  所属品牌-->
        <template v-slot="{ row }">
          <div style="display: flex; justify-content: center; align-items: center">
            <span v-if="row.brandName" class="brand-class">{{ row.brandName }}</span>
            <i v-if="isAdmin && row.brandName" class="el-icon-edit" style="color: #409eff; margin-left: 10px" @click="editBrand(row)"></i>
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="subDealerCount" label="下级超级俱乐部" align="center"></el-table-column>
      <el-table-column prop="childMerchantNum" label="下级门店" align="center"></el-table-column>
      <el-table-column prop="paymentIsComplete" label="是否完款" align="center">
        <template slot-scope="scope">
          <span v-if="scope.row.paymentIsComplete == '1'" class="green">完款</span>
          <span v-else class="red">未完款</span>
        </template>
      </el-table-column>
      <!-- <el-table-column prop="isPay" label="培训是否缴费" align="center" v-if="isPermit">
        <template slot-scope="scope">{{ scope.row.isPay == 0 ? '未缴费' : scope.row.isPay == 1 ? '已缴费' : '无需缴费' }}</template>
      </el-table-column> -->
      <el-table-column prop="channelManagerName" label="渠道管理员" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.channelManagerName ? scope.row.channelManagerName : '' }}</span>
          <el-link v-if="scope.row.channelManagerName" type="primary" @click="showChannelManagerDetail(scope.row)">详情</el-link>
        </template>
      </el-table-column>
      <el-table-column prop="flowStatus" label="流程状态" align="center">
        <!--        <template slot-scope="scope">
                  <span class="green" v-if="scope.row.isCheck === 1">通过审核</span>
                  <span class="red" v-if="scope.row.isCheck === 0">等待总部审核</span>
                  <span class="blue" v-if="scope.row.isCheck === 2">未通过分公司审核</span>
                  <span v-if="scope.row.isCheck === 3" class="red">等待分公司审核</span>
                  <span v-if="scope.row.isCheck === -3" class="red">未通过市级服务商审核</span>
                  <span class="red" v-if="scope.row.isCheck === 4">等待市级服务商审核</span>
                  <span class="blue" v-if="scope.row.isCheck === -4">未通过总部审核</span>
                  <el-button
                    v-if="(scope.row.isCheck ==2||scope.row.isCheck ==-3||scope.row.isCheck ==-4) && scope.row.checkReason !=''"
                    type="text"
                    @click="open(scope.row.checkReason)"
                    showCancelButton="false"
                  >查看详情
                  </el-button>
                </template>
                -->
      </el-table-column>
      <el-table-column label="审核状态" align="center">
        <template slot-scope="scope">
          <span class="green" v-if="scope.row.isEnable === 1 && scope.row.isCheck === 0 && !scope.row.flowStatus">上级服务商通过审核</span>
          <span class="green" v-else-if="scope.row.isOldData === 1 && scope.row.isCheck === 1">通过审核</span>
          <span class="red" v-else-if="scope.row.isOldData === 1 && scope.row.isCheck === 0">等待审核</span>
          <span class="blue" v-else-if="scope.row.isOldData === 1 && scope.row.isCheck === 2">未通过分公司审核</span>
          <span v-else-if="scope.row.isOldData === 1 && scope.row.isCheck === 3" class="red">等待分公司审核</span>
          <span v-else-if="scope.row.isOldData === 1 && scope.row.isCheck === -3" class="red">未通过市级服务商审核</span>
          <span class="red" v-else-if="scope.row.isOldData === 1 && scope.row.isCheck === 4">等待市级服务商审核</span>
          <span class="blue" v-else-if="scope.row.isOldData === 1 && scope.row.isCheck === -4">未通过总部审核</span>
          <span class="green" v-else-if="scope.row.isOldData === 0 && scope.row.flowIsEnd === 1">审核流程结束</span>
          <span class="red" v-else-if="scope.row.isOldData === 0 && scope.row.flowIsEnd === 0">审核中</span>
          <el-button
            v-if="(scope.row.isCheck == 2 || scope.row.isCheck == -3 || scope.row.isCheck == -4) && scope.row.checkReason != ''"
            type="text"
            @click="open(scope.row.checkReason)"
            showCancelButton="false"
          >
            查看详情
          </el-button>
        </template>
      </el-table-column>
      <el-table-column prop="isEnable" label="账户状态" align="center">
        <template slot-scope="scope">
          <span class="red" v-if="scope.row.isEnable === 0">暂停</span>
          <span class="green" v-else-if="scope.row.isEnable == 1">开通</span>
          <span class="red" v-else-if="scope.row.isEnable == 2">禁用</span>
          <span class="red" v-else-if="scope.row.isEnable == -1">系统关闭/</span>
          <span class="red" v-else-if="scope.row.isEnable == -2">年审关闭</span>
          <span class="red" v-else-if="scope.row.isEnable == -3">终止</span>
          <span class="red" v-else-if="scope.row.isEnable == -4">注销</span>
          <span class="red" v-else-if="scope.row.isEnable == -5">到期未续费</span>
          <span class="red" v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column prop="merchantDealStatus" label="渠道合作伙伴协议状态" align="center">
        <template slot-scope="scope">
          <span class="green" v-if="scope.row.merchantDealStatus == 2">已签署</span>
          <span class="red" v-if="scope.row.merchantDealStatus == 1">未签署</span>
        </template>
      </el-table-column>
      <el-table-column prop="regTime" label="添加时间" align="center"></el-table-column>
      <el-table-column prop="enableTime" label="开通时间" align="center" v-if="isAdmin || isMerchantManager"></el-table-column>
    </el-table>

    <!--渠道管理员信息-->
    <el-dialog title="渠道管理员信息" :visible.sync="dialogVisibleForChannelManager" width="30%">
      <el-form ref="temp" label-position="left" label-width="120px">
        <el-form-item label="名称: ">
          <el-input disabled v-model="channelManager.realName" placeholder="请输入内容"></el-input>
        </el-form-item>
        <el-form-item label="编号: ">
          <el-input disabled v-model="channelManager.channelManagerCode" placeholder="请输入内容"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisibleForChannelManager = false">关 闭</el-button>
      </span>
    </el-dialog>

    <!--弹出窗口：指派交付中心-->
    <el-dialog :title="textMap[dialogStatu]" :visible.sync="dialogFormDelivery" width="40%" :close-on-click-modal="false" @close="dialogFormDelivery = false">
      <template>
        <el-input placeholder="请输入编号" style="width: 40%" v-model="searchMerchantCode" clearable></el-input>
        <el-button type="primary" mini style="margin-left: 10px" title="搜索" @click="searchChannel()">搜索</el-button>

        <el-table :data="tableDataDelivery" v-loading="tableLoading2" highlight-current-row ref="singleTable" height="550" border style="width: 100%">
          <el-table-column type="index" width="50"></el-table-column>
          <el-table-column sortable prop="merchantCode" label="编号" width="180"></el-table-column>
          <el-table-column sortable prop="merchantName" label="名称" width="180"></el-table-column>
          <el-table-column label="操作">
            <template slot-scope="scope">
              <el-button type="primary" mini title="指派至该交付中心" @click="assignDelivery(scope.row.merchantCode)">指派</el-button>
            </template>
          </el-table-column>
          <el-table-column sortable prop="regTime" label="添加时间"></el-table-column>
        </el-table>
      </template>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormDelivery = false">取消</el-button>
      </div>
    </el-dialog>

    <el-dialog
      :show-close="false"
      :close-on-press-escape="false"
      :distinguishCancelAndClose="true"
      title="审核弹框"
      :visible.sync="showLoginAccount"
      width="70%"
      :close-on-click-modal="false"
      @close="close"
    >
      <el-form :ref="'isCheckStatu'" :rules="rules" :model="isCheckStatu" label-position="left" label-width="120px" style="width: 100%">
        <el-form-item label="开户金额" prop="openMoney">
          <el-col :xs="24" :span="12">
            <el-input v-model="isCheckStatu.openMoney" :disabled="disabled" />
          </el-col>
        </el-form-item>
        <el-form-item label="超级俱乐部级别：" prop="rank">
          <el-col :xs="24" :span="12">
            <el-select v-model="isCheckStatu.rank" placeholder="请选择" style="width: 100%">
              <el-option
                v-for="item in [
                  { value: 'A', label: 'A级别', disabled: true },
                  { value: 'B', label: 'B级别', disabled: true },
                  { value: 'C', label: 'C级别', disabled: true },
                  { value: 'D', label: 'D级别', disabled: false },
                  { value: 'E', label: 'E级别', disabled: false },
                  { value: 'F', label: 'F级别', disabled: false }
                ]"
                :key="item.value"
                :label="item.label"
                :value="item.value"
                :disabled="item.disabled"
              ></el-option>
            </el-select>
          </el-col>
        </el-form-item>
        <el-form-item label="审核" prop="isCheck">
          <el-col :xs="24" :span="12">
            <el-select v-model="isCheckStatu.isCheck" placeholder="请选择" style="width: 100%">
              <el-option
                v-for="item in [
                  { value: 1, label: '通过' },
                  { value: 0, label: '不通过' }
                ]"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-col>
        </el-form-item>
        <el-form-item label="审核意见" prop="checkReason">
          <el-col :xs="24" :span="12">
            <el-input type="textarea" resize="none" :rows="4" v-model="isCheckStatu.checkReason" />
          </el-col>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="dealerIsCheck">确定</el-button>
        <el-button size="mini" @click="closeLogin">关闭</el-button>
      </div>
    </el-dialog>
    <!-- 分页 -->
    <el-col :span="20">
      <el-pagination
        :current-page="tablePage.pageNum"
        :page-size="tablePage.pageSize"
        :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="tablePage.totalItems"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-col>

    <el-dialog title="俱乐部信息补充" :visible.sync="supplementary" width="30%" @close="close">
      <span>
        <el-row>
          <el-col :span="4" :offset="0" style="display: flex; align-items: center; height: 40px">企业名称</el-col>
          <el-col :span="18" :offset="0">
            <el-autocomplete
              style="width: 300px"
              clearable
              @clear="setBlur()"
              @input="handle"
              :fetch-suggestions="querySearch"
              :trigger-on-focus="false"
              @select="handleSelect"
              class="inline-input"
              v-model="businessName"
              placeholder="请输入该俱乐部企业名称"
            ></el-autocomplete>
            <!-- <el-input v-model="businessName" placeholder="请输入该俱乐部企业名称" size="normal" clearable></el-input> -->
            <div style="color: #bd3124; margin-top: 10px">仅可选择已注册的企业名称，请如实选择</div>
          </el-col>
        </el-row>
      </span>
      <span slot="footer">
        <el-button @click="supplementary = false">取消</el-button>
        <el-button type="primary" @click="sumbit">确定</el-button>
      </span>
    </el-dialog>

    <el-dialog title="签署合同" :visible.sync="isPact" @close="close1" :width="codeList.length > 1 ? '40%' : '30%'">
      <span class="code">
        <div v-for="(item, index) in codeList" class="sucontent">
          <div style="line-height: 20px; font-size: 14px">邀请该{{ item.isFirstParty == 0 ? '渠道伙伴' : '' }}俱乐部负责人，使用手机扫码签署</div>
          <div style="line-height: 20px; font-size: 14px; margin-bottom: 10px">《渠道合作伙伴协议》</div>
          <div style="position: relative; width: 200px; height: 200px">
            <el-image style="width: 200px; height: 200px" :src="item.qrUrl" :lazy="true"></el-image>
            <!-- <el-image
              style="width: 200px; height: 200px"
              @click="changeSign()"
              src="https://document.dxznjy.com/course/bf188f4751c84e94b0678b463f7054a4.png"
              :lazy="true"
            ></el-image> -->
            <div class="codeSuccess" v-if="item.signStatus == 2">
              <i class="el-icon-circle-check" style="color: #13ce66; font-size: 25px"></i>
            </div>
          </div>
        </div>
      </span>
      <span slot="footer">
        <div style="display: flex; justify-content: center"><el-button type="primary" @click="getQrlin(2)">签署完成</el-button></div>
        <div style="text-align: center; color: #848aa1; font-size: 14px; margin-top: 5px">签署完成后，请点击该按钮</div>
      </span>
    </el-dialog>

    <!-- 培训缴费二维码弹窗 -->
    <ContractQrCodeVue v-if="isPermit" ref="payContractComponent" :dialogVisible="openDialog" @closeDialog="closeDialog" @changePayFlowStatus="changeIsInPay" />
    <!-- 编辑城市所属地 -->
    <!-- <EditCityAffiliation ref="editCityAffiliationRef" :isShowCityDialog="isOpenCityAffiliation" @closeCityDialog="closeCityDialog" /> -->
    <!-- 俱乐部确认单 -->
    <confirmClubForm ref="confirmClubFormRef" :isShowClub="isShowClubForm" @closeConfirmDialog="closeClubDialog" />
    <!--  修改所属品牌  -->
    <EditClubAndBrandDialog ref="clubAndBrandRef" :isShowDialog="clubAndBrandDialog" @handleCancel="handleCancel" @close="handleOuterClose" />
    <!-- 无法变更所属俱乐部原因弹窗 -->
    <InabilityReason
      ref="inabilityReasonRef"
      :dialogReasonVisible.sync="dialogInabilityVisible"
      :reasonContent="reasonsContent"
      :workStep="workStep"
      :showTitleStatus="showTitleStatus"
      :isSubmitLoading="isSubmitLoading"
    />
    <!--   修改渠道合作伙伴编号 -->
    <EditOperationsCodeDialog v-if="isShowOperationsCode" ref="operationsCodeRef" :dialogVisible.sync="dialogOperationsCodeVisible" @handleCancel="cancelOperationsCodeDialog" />
    <!-- 注册码 -->
    <registrationCode ref="registrationCode"></registrationCode>
  </div>
</template>

<script>
  import dealerPaylist from '@/api/operationsPayment';
  import { regionData } from 'element-china-area-data';
  import checkPermission from '@/utils/permission';
  import dynamicFunction from '@/utils/dynamicFunction'; // 动态开启培训费相关功能
  import authenticationApi from '@/api/authentication';
  import dealerListApi from '@/api/operationsList';
  import areasDealerApi from '@/api/areasDealerList';
  import schoolApi from '@/api/schoolList';
  import { mapGetters } from 'vuex';
  import { dxSource, pageParamNames } from '@/utils/constants';
  import store from '@/store';
  import deliveryCenterApi from '@/api/delivery/deliveryCenter';
  import ContractQrCodeVue from './components/contractQrCode.vue';
  import operationsList from '@/api/operationsList';
  // import areaData from '@/utils/provinceCityTree.json';

  import EditCityAffiliation from '@/views/merchantManagement/components/EditCityAffiliation.vue';
  import confirmClubForm from '@/views/merchantManagement/components/confirmClubForm.vue';
  import InabilityReason from '@/views/merchantManagement/components/InabilityReason.vue';
  import EditClubAndBrandDialog from '@/views/merchantManagement/components/EditClubAndBrandDialog.vue';
  import EditOperationsCodeDialog from '@/views/merchantManagement/components/EditOperationsCodeDialog.vue';
  import registrationCode from '@/views/layout/components/registrationCode';
  export default {
    // name: 'dealerList',
    components: {
      InabilityReason,
      EditCityAffiliation,
      ContractQrCodeVue,
      confirmClubForm,
      EditClubAndBrandDialog,
      EditOperationsCodeDialog,
      registrationCode
    },
    data() {
      return {
        regionData,
        isAdmin: false,
        isMerchantManager: false,
        isPact: false,
        systemNum: 0,
        managementSystem: {
          fullPayment: 0,
          deposit: 0
        }, // 管理系统数量
        codeList: [{}],
        token: store.getters.token,
        channelManager: {},
        dialogVisibleForChannelManager: false,
        searchMerchantCode: '',
        branchOfficeMerchantCode: '',
        dialogFormDelivery: false,
        textMap: {
          assign: '指定交付中心'
        },
        tableDataDelivery: [],
        tableLoading2: false,
        dialogStatu: 'assign',
        tableLoading: false,
        // 分页
        tablePage: {
          pageNum: 1,
          pageSize: 10,
          totalItems: null
        },
        companyCode: '',
        roleTag: '',
        roleName: '',
        exportLoading: false, //导出加载
        exportLoading1: false,
        RegTime: '',
        tableData: [],
        dataQuery: {
          isCheck: '',
          merchantCode: '',
          name: '',
          merchantName: '',
          realName: '',
          address: '',
          startTime: '',
          endTime: '',
          flowEndStatus: '',
          isPay: '',
          brandName: ''
        },
        dialogVisible: false,
        isZxBrand: false,
        updateMarketDate: {},
        addMarketDate: {},
        showLoginAccount: false,
        regTime: '',
        enableTime: [], //开通时间
        ruls: [],
        disabled: true,
        contractTime: null,
        contractTimes: 0,
        supplementary: false,
        isCheckStatu: {}, //审核状态
        rules: {
          rank: [
            {
              required: true,
              message: '必填',
              trigger: 'blur'
            }
          ],
          isCheck: [
            {
              required: true,
              message: '必填',
              trigger: 'blur'
            }
          ]
        },
        openDialog: false,
        payContractUrl: '', // 培训合同签署二维码
        isPayOpen: true,
        isInPayAction: false,
        row: null,
        businessName: '',
        signObj: null,
        isCityBranch: false,
        flowId: '',
        templateType: '',
        // isOpenCityAffiliation: false, //是否展示编辑所属地弹窗
        // areaData: areaData, //地区数据
        selectedCity: [], // 选中的城市
        isShowClubForm: false,
        clubAndBrandDialog: false, // 编辑所属俱乐部弹窗
        dialogInabilityVisible: false, // 编辑所属俱乐部弹窗
        dialogOperationsCodeVisible: false,
        reasonsContent: [],
        workStep: 1,
        showTitleStatus: 1,
        isSubmitLoading: false,
        isShowOperationsCode: false
      };
    },
    computed: {
      ...mapGetters(['roles', 'setpayUrl']),
      isPermit() {
        return this.roleTag == 'admin' && this.isPayOpen;
      },
      isHaveFullPayment() {
        const roleTagList = ['admin', 'zxBrand'];
        return roleTagList.includes(this.roleTag);
      },
      isHaveCityPermission() {
        return this.roleTag == 'DeliveryService' || this.roleTag == 'admin';
      },
      isHaveLogoPermission() {
        return this.roleTag == 'HeadquartersCityCenter';
      }
    },
    created() {
      this.clearContractTime();
      this.isZxBrand = this.roles.find((item) => item.val == 'zxBrand'); // 超级品牌
      this.isAdmin = checkPermission(['admin']); // 超级管理员
      this.isMerchantManager = checkPermission(['MerchantManager']); //新渠道管理员
      this.isCityBranch = checkPermission(['CityCompany']);

      // this.isAdmin = ls.getItem('rolesVal') === 'admin';
      // 获取当前时间，如果接口开放，则培训缴费相关功能放开
      schoolApi.getTrainingOpenTime().then((res) => {
        const openTime = res.data;
        // const isPayOpen = this.dynamicFunction(openTime);
        this.isPayOpen = openTime == 'Y' ? true : false;
      });

      var jsonData = JSON.stringify(this.roles);
      var s = JSON.stringify(this.roles).includes('admin');
      console.log(jsonData + 'qc');
      this.roleName = s;
      console.log(this.roleName);
      this.fetchData01();
    },
    beforeDestroy() {
      this.clearContractTime();
    },
    mounted() {
      authenticationApi.checkAccountBalance().then((res) => {
        this.roleTag = res.data.data.roleTag;
      });
    },
    methods: {
      async changeSign() {
        let data = {
          firstSignStatus: 2,
          secondSignStatus: 2,
          templateType: this.templateType,
          signSource: 1,
          flowId: this.flowId
        };
        const res = await dealerPaylist.compensationSignStatus(data);
        if (res.success) {
          this.$message.success('签约成功');
        }
      },
      setBlur() {
        //  在点击由 clearable 属性生成的清空按钮时，主动触发失去焦点，解决‘fetch-suggestions’输入建议不提示的bug
        document.activeElement.blur();
      },
      // 清空输入框页面重置
      handle(val) {
        if (val === '') {
          // this.getData(); // 页面重置的代码
        }
      },
      // 过滤项目和class
      async querySearch(queryString, cb) {
        if (queryString && queryString.length > 0) {
          let data1 = {
            keyword: queryString,
            pageNo: 1,
            pageSize: 10
          };

          try {
            const data = await operationsList.businessLicense(data1); // search定义在data里
            // 赋值给建议列表，渲染到页面

            var list = data.data.data.data;
            // 如果list.length等于0，则代表没有匹配到结果。手动给list添加一条提示信息
            if (!queryString) {
              list.push({
                id: '-1',
                value: '无匹配结果'
              });
              // 调用 callback 返回建议列表的数据

              cb(list);
            } else {
              list = list.map((item) => {
                return {
                  value: `${item.companyName}`,
                  id: `${item.creditNo}`
                };
              });
              list = list.filter((item) => {
                return item.value.indexOf(queryString) > -1;
              });
              // 调用 callback 返回建议列表的数据
              cb(list);
            }
          } catch (error) {
            console.log(error);
          }
        }
      },
      handleSelect(item) {
        console.log(item, '....................');
      },
      getSystemNum() {
        dealerPaylist.judgeOperationsNum().then((res) => {
          this.systemNum = res.data.channelPartnerAgreementCount; // 未签署渠道合作伙伴协议俱乐部数量
          // console.log('🚀 ~ dealerPaylist.judgeOperationsNum ~ this.systemNum:', this.systemNum);
          this.managementSystem.fullPayment = res.data.fullPaymentClubCount; // 剩余全款俱乐部学习系统数量
          this.managementSystem.deposit = res.data.depositClubCount; // 剩余定金俱乐部学习系统数量
        });
      },
      close1() {
        this.clearContractTime();
      },
      async Contract(id, flag) {
        let { data } = await dealerListApi.queryActiveV2(id);
        let obj = {
          flowId: data.signFlowId,
          templateType: data.templateType,
          signSource: data.signSource,
          isQrLink: 1
        };
        this.templateType = data.templateType;
        this.flowId = data.signFlowId;
        this.clearContractTime();
        this.signObj = obj;
        this.signObj.flag = flag;
        this.getQrlin();
        this.contractInterval(this.getQrlin);
      },
      async getQrlin(temp) {
        if (this.isPact) {
          this.signObj.isQrLink = 0;
        }
        let res = await dealerPaylist.QRlink(this.signObj);
        if (this.signObj.flag == 1) {
          if (this.isPact) {
            this.codeList[0].signStatus = res.data.find((e) => e.isFirstParty == 1).signStatus;
          } else {
            this.codeList = [];
            this.codeList.push(res.data.find((e) => e.isFirstParty == 1));
          }
        } else {
          if (this.isPact) {
            this.codeList[0].signStatus = res.data.find((e) => e.isFirstParty == 0).signStatus;
            this.codeList[1].signStatus = res.data.find((e) => e.isFirstParty == 1).signStatus;
          } else {
            this.codeList = [];
            this.codeList.push(res.data.find((e) => e.isFirstParty == 0));
            this.codeList.push(res.data.find((e) => e.isFirstParty == 1));
          }
        }
        if (this.codeList.find((e) => e.signStatus != 2)) {
          if (temp) {
            this.isPact = true;
            this.$message.warning('还未签署，请先签署合同');
          } else {
            this.isPact = true;
          }
        } else {
          this.clearContractTime();
          this.isPact = false;
          this.$message.success('合同签署成功');
          this.fetchData();
        }
      },
      contractInterval(res) {
        let that = this;
        this.contractTime = setInterval(() => {
          console.log(that.contractTimes);
          if (this.contractTimes > 60) {
            that.clearContractTime();
          } else {
            res();
            that.contractTimes = that.contractTimes + 15;
          }
        }, 15000);
      },
      clearContractTime() {
        clearInterval(this.contractTime);
        this.contractTime = null;
        this.contractTimes = 0;
      },
      // 培训缴费
      // async trainingPay(item, $index) {
      //   const that = this;
      //   if (!that.isInPayAction) {
      //     that.isInPayAction = true;
      //   } else {
      //     that.$message.warning('操作过于频繁，请稍后再试');
      //     return;
      //   }
      //   that.tableData[$index].trainingBtnLoading = true;
      //   // 获取合同签署状态(-1未创建，0未完成，1已完成)
      //   schoolApi
      //     .getTrainingContractStatus(item.id)
      //     .then((res) => {
      //       if (res.data.status == '1') {
      //         // 跳转至商户平台
      //         schoolApi
      //           .getTrainingPayInfo(item.merchantCode)
      //           .then((res) => {
      //             const split = dxSource.split('##');
      //             res.data.dxSource = res.data.registerCallIInfo.appSource + '##' + split[1] + '##' + split[2];
      //             let params = JSON.stringify(res.data);
      //             let req = 'token=' + that.token + '&params=' + params + '&back=' + window.location.href;
      //             //需要编码两遍，避免出现+号等
      //             var encode = Base64.encode(Base64.encode(req));
      //             setTimeout(() => {
      //               window.open(this.setpayUrl + 'product?' + encode, '_blank');
      //             }, 100);
      //           })
      //           .finally(() => {
      //             that.isInPayAction = false;
      //           });
      //       } else {
      //         that.openDialog = true;
      //         that.$nextTick(() => {
      //           that.$refs.payContractComponent.getContractInfo(item, res.data.status);
      //         });
      //       }
      //     })
      //     .finally(() => {
      //       that.tableData[$index].trainingBtnLoading = false;
      //     });
      // },
      closeDialog() {
        this.openDialog = false;
      },
      changeIsInPay(status) {
        setTimeout(() => {
          this.isInPayAction = status;
        }, 2000);
      },
      checkPermission,
      dynamicFunction,
      async logoChange(e, row) {
        console.log(row.isEnable);
        let obj = {
          id: row.id,
          logoEnable: e
        };
        await dealerListApi.avatarEnable(obj);
        this.$message.success('操作成功');
        this.fetchData();
      },
      reset() {
        this.tablePage.pageNum = 1;
        this.tablePage.pageSize = 10;
        // this.dataQuery.provinceCity = '';
        this.selectedCity = [];
        this.regTime = '';
        this.enableTime = [];
        this.dataQuery = {};
        this.fetchData();
      },
      fetchData01() {
        this.tablePage.pageNum = 1;
        const that = this;
        that.dataQuery.province = that.selectedCity[0];
        if (that.selectedCity && that.selectedCity[1] == '市辖区') {
          that.dataQuery.city = that.selectedCity[0];
        } else {
          that.dataQuery.city = that.selectedCity[1];
        }
        that.dataQuery.area = that.selectedCity[2];
        this.fetchData();
      },
      showChannelManagerDetail(row) {
        this.channelManager.realName = row.channelManagerRealName;
        this.channelManager.channelManagerCode = row.channelManagerCode;
        this.dialogVisibleForChannelManager = true;
      },
      // 查询+搜索课程列表
      fetchData() {
        const that = this;
        var a = that.regTime;
        if (a != null) {
          that.dataQuery.startTime = a[0];
          that.dataQuery.endTime = a[1];
        } else {
          that.dataQuery.startTime = '';
          that.dataQuery.endTime = '';
        }
        const enableRangeTime = that.enableTime;
        if (enableRangeTime != null) {
          that.dataQuery.enableTimeStrat = enableRangeTime[0];
          that.dataQuery.enableTimeEnd = enableRangeTime[1];
        } else {
          that.dataQuery.enableTimeStrat = '';
          that.dataQuery.enableTimeEnd = '';
        }

        // that.dataQuery.provinceCity = this.selectedCity?.join('-');
        let arr = { ...that.dataQuery, ...this.tablePage };
        that.tableLoading = true;
        dealerListApi.dealerListV2(arr).then((res) => {
          // that.tableData = res.data.data;
          // 为培训缴费增加loading状态
          that.tableData = res.data.data.map((item) => {
            return {
              ...item,
              trainingBtnLoading: false
            };
          });
          if (this.isZxBrand) {
            this.getSystemNum();
          }
          // console.log(res)
          that.tableLoading = false;
          this.tablePage.totalItems = res.data.totalItems - 0;
          // 设置后台返回的分页参数
          // pageParamNames.forEach((name) => that.$set(that.tablePage, name, parseInt(res.data[name])));
        });
      },
      //打开指定交付中心窗口
      openAssignDelivery(merchantCode) {
        this.dialogFormDelivery = true;
        this.branchOfficeMerchantCode = merchantCode;
        //查询所有交付中心
        deliveryCenterApi
          .allList()
          .then((res) => {
            this.tableDataDelivery = res.data;
          })
          .catch((err) => {});
      },
      //表格数据选中和跳转到指定位置
      searchChannel() {
        for (let i = 0; i < this.tableDataDelivery.length; i++) {
          if (this.tableDataDelivery[i].merchantCode === this.searchMerchantCode) {
            if (!this.$refs['singleTable']) return; //不存在这个表格则返回
            let elTable = this.$refs['singleTable'].$el;
            if (!elTable) return;
            const scrollParent = elTable.querySelector('.el-table__body-wrapper');
            const targetTop = elTable.querySelectorAll('.el-table__body tr')[i].getBoundingClientRect().top; //该行的位置
            const containerTop = elTable.querySelector('.el-table__body').getBoundingClientRect().top; //body的位置
            //跳转
            scrollParent.scrollTop = targetTop - containerTop; //跳转到存下编辑行的ScrollTop
            this.$refs.singleTable.setCurrentRow(this.tableDataDelivery[i]);
          }
        }
      },
      assignDelivery(merchantCode) {
        this.tableLoading2 = true;
        deliveryCenterApi
          .assignDelivery(this.branchOfficeMerchantCode, merchantCode)
          .then((res) => {
            this.tableLoading2 = false;
            this.$message.success('指派成功');
            this.dialogFormDelivery = false;
            this.fetchData();
          })
          .catch((err) => {});
      },
      liftDelivery(merchantCode) {
        this.$confirm('解除通过该账户绑定的交付中心?')
          .then((_) => {
            deliveryCenterApi
              .liftDelivery(merchantCode)
              .then((res) => {
                this.$message.success('解除绑定成功');
                this.fetchData();
              })
              .catch((err) => {});
          })
          .catch((_) => {});
      },
      //新增操作
      clickAdd() {
        const that = this;
        window.localStorage.setItem('addOrUpdateDealer', JSON.stringify(true));
        window.localStorage.removeItem('dealerId');
        that.$router.push({
          path: '/merchantManagement/operationsAdd',
          query: {
            addOrUpdate: true
          }
        });
      },
      // 打开修改登陆账号
      openLogin() {
        this.showLoginAccount = true;
      },
      closeLogin() {
        this.isCheckStatu.isCheck = '';
        this.showLoginAccount = false;
      },
      //审核
      isCheckDealer(id, openMoney) {
        this.showLoginAccount = true;
        this.isCheckStatu.openMoney = openMoney;
        this.isCheckStatu.id = id;
      },
      //审核页面
      dealerIsCheck() {
        dealerListApi.isCheckStatus(this.isCheckStatu).then((res) => {
          this.fetchData();
          this.isCheckStatu.isCheck = '';
          this.isCheckStatu.checkReason = '';
          this.showLoginAccount = false;
        });
      },
      // 分页
      handleSizeChange(val) {
        this.tablePage.pageSize = val;
        this.fetchData();
      },
      handleCurrentChange(val) {
        this.tablePage.pageNum = val;
        this.fetchData();
      },
      //  导出
      exportFlow1() {
        const that = this;
        var a = that.regTime;
        if (a != null) {
          that.dataQuery.startTime = a[0];
          that.dataQuery.endTime = a[1];
        } else {
          that.dataQuery.startTime = '';
          that.dataQuery.endTime = '';
        }
        if (that.dataQuery.startTime === '' || that.dataQuery.startTime === '' || that.dataQuery.startTime === undefined) {
          that.$message.info('开始时间不能为空');
          return false;
        }
        if (that.dataQuery.endTime === '' || that.dataQuery.endTime === '' || that.dataQuery.endTime === undefined) {
          that.$message.info('结束时间不能为空');
          return false;
        }

        that.exportLoading1 = true;
        const data = {
          startTime: that.dataQuery.startTime,
          endTime: that.dataQuery.endTime
        };
        that.exportLoading1 = true;
        //
        dealerListApi
          .simpleMarketExecl1(data)
          .then(
            (res) => {
              //           this.$notify.error({
              //               title: "操作失败",
              //               message: "文件下载失败"
              //             });
              const url = window.URL.createObjectURL(res);
              const link = document.createElement('a');
              link.style.display = 'none';
              link.href = url; // 获取服务器端的文件名
              link.setAttribute('download', '超级俱乐部运营数据列表.xls');
              document.body.appendChild(link);
              link.click();
              that.exportLoading1 = false;
            },
            (s) => {
              if (s === 'error') {
                that.exportLoading1 = false;
              }
            }
          )
          .catch((err) => {
            this.$message.error('');
            that.exportLoading1 = false;
          });
      },
      dateDiff(d1, d2) {
        console.log('🚀 ~ exportFlow ~ d:', d1.replace(/-/g, '/'));
        // yyyy-mm-dd hh-mm-ss 转成 yyyy/mm/dd hh:mm:ss
        let [d1a, d1b] = d1.split(' '); // 日期,时间
        d1b = ' ' + d1b;
        let [d2a, d2b] = d2.split(' ');
        d2b = ' ' + d2b;
        d1 = new Date(d1a.replace(/-/g, '/') + d1b.replace(/-/g, ':'));
        d2 = new Date(d2a.replace(/-/g, '/') + d2b.replace(/-/g, ':'));
        var obj = {};
        obj.s = Math.floor((d2 - d1) / 1000); //差几秒
        obj.m = Math.floor(obj.s / 60); //差几分钟
        obj.h = Math.floor(obj.m / 60); //差几小时
        obj.D = Math.floor(obj.h / 24); //差几天
        return obj.D;
      },
      // 判断是否跨月
      isCrossMonth(timeRange) {
        const [start, end] = timeRange;
        const startMonth = start.split('-')[1];
        const endMonth = end.split('-')[1];
        const startYear = start.split('-')[0];
        const endYear = end.split('-')[0];
        return startMonth !== endMonth || startYear !== endYear;
      },
      //导出
      exportFlow() {
        const that = this;
        this.fetchData01();
        if (this.exportLoading) return;
        // 导出数据校验

        if (this.enableTime && this.enableTime.length > 0 && (!this.regTime || this.regTime.length <= 0)) {
          console.log('🚀 导出开通时间');

          that.dataQuery.enableTimeStrat = this.enableTime[0];
          that.dataQuery.enableTimeEnd = this.enableTime[1];

          if (this.isCrossMonth(this.enableTime) && this.dateDiff(this.enableTime[0], this.enableTime[1]) > 30) {
            this.$message.error('最多可导出一个月数据，请重新选择时间');
            return;
          }
        } else if (this.regTime && this.regTime.length > 0 && (!this.enableTime || this.enableTime.length <= 0)) {
          console.log('🥶 导出添加时间');

          that.dataQuery.startTime = this.regTime[0];
          that.dataQuery.endTime = this.regTime[1];

          if (this.isCrossMonth(this.regTime) && this.dateDiff(this.regTime[0], this.regTime[1]) > 30) {
            this.$message.error('最多可导出一个月数据，请重新选择时间');
            return;
          }
        } else if ((!this.regTime || this.regTime.length <= 0) && (!this.enableTime || this.enableTime.length <= 0)) {
          console.log('💩 都没有选择');

          this.$message({
            message: '请先选择添加时间或开通时间，再进行数据导出，最多可选一个月',
            type: 'warning'
          });
          return;
        } else {
          console.log('🔥 同时选择了两个时间');

          that.dataQuery.startTime = this.regTime[0];
          that.dataQuery.endTime = this.regTime[1];
          that.dataQuery.enableTimeStrat = this.enableTime[0];
          that.dataQuery.enableTimeEnd = this.enableTime[1];

          // 校验添加时间
          if (this.isCrossMonth(this.regTime) && this.dateDiff(this.regTime[0], this.regTime[1]) > 30) {
            this.$message.error('添加时间最多可导出一个月数据，请重新选择');
            return;
          }

          // 校验开通时间
          if (this.isCrossMonth(this.enableTime) && this.dateDiff(this.enableTime[0], this.enableTime[1]) > 30) {
            this.$message.error('开通时间最多可导出一个月数据，请重新选择');
            return;
          }
        }

        console.log('🚀🥶💩~ 开始导出数据了');

        // if (!this.regTime || this.regTime.length <= 0) {
        //   this.$message({
        //     message: '请先选择添加时间，再进行数据导出，最多可选一个月',
        //     type: 'warning'
        //   });
        //   return;
        // } else {
        //   that.dataQuery.startTime = this.regTime[0];
        //   that.dataQuery.endTime = this.regTime[1];
        //   //同一月内不校验  跨月校验30天
        //   let a = this.regTime[0].split('-')[1]; // 月份
        //   let b = this.regTime[1].split('-')[1]; // 月份
        //   let c = this.regTime[0].split('-')[0]; // 年份
        //   let d = this.regTime[1].split('-')[0]; // 年份
        //   if (a != b || c != d) {
        //     if (this.dateDiff(this.regTime[0], this.regTime[1]) > 30) {
        //       this.$message.error('最多可导出一个月数据，请重新选择时间');
        //       return;
        //     }
        //   }
        // }
        // 导出数据
        that.exportLoading = true;
        dealerListApi.simpleMarketExecl(that.dataQuery).then((res) => {
          //           this.$notify.error({
          //               title: "操作失败",
          //               message: "文件下载失败"
          //             });
          const url = window.URL.createObjectURL(res);
          const link = document.createElement('a');
          link.style.display = 'none';
          link.href = url; // 获取服务器端的文件名
          link.setAttribute('download', '超级俱乐部列表.xls');
          document.body.appendChild(link);
          link.click();
          that.exportLoading = false;
        });
      },
      //审核理由
      open(chenckReason) {
        const h = this.$createElement;
        this.$msgbox({
          title: '审核理由',
          message: h('p', null, [h('i', { style: 'color: #FF0802' }, chenckReason)]),
          showCancelButton: false,
          confirmButtonText: '确定'
        });
      },
      coerceSuccess(id) {
        this.$confirm('确定强制通过审批吗？', '强制通过', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          dealerListApi.coerceSuccess(id).then(() => {
            this.$nextTick(() => this.fetchData());
            this.$message.success('操作成功');
          });
        });
      },
      //编辑
      updateDealerList(id, resubmit) {
        window.localStorage.setItem('dealerId', id);
        window.localStorage.setItem('addOrUpdateDealer', JSON.stringify(false));
        const that = this;
        that.$router.push({
          path: '/merchantManagement/operationsAdd',
          query: {
            addOrUpdate: false,
            id: id,
            isResubmit: resubmit
          }
        });
      },
      pickerOptions() {},
      close() {},
      async sumbit() {
        // TODO: 6.13注释
        // if (!this.businessName) return this.$message.warning('请输入企业名称');

        let data1 = {
          keyword: this.businessName,
          pageNo: 1,
          pageSize: 10
        };
        let obj = {
          merchantCode: this.row.merchantCode,
          businessName: this.businessName
        };

        // operationsList.businessLicense(data1).then(async (res) => {
        //   let company = res.data.data.data.some((item) => {
        //     return item.companyName == this.businessName;
        //   });
        //   if (!company) {
        //     this.$message.error('请输入正确的企业名称');
        //   } else {
        //     await dealerListApi.addBusinessName(obj);
        //     this.row = { ...this.row, businessName: this.businessName };
        //     this.$message.success('操作成功');
        //     this.dealerPaymentIsComplete(this.row);
        //     this.row = null;
        //     this.supplementary = false;
        //     this.businessName = '';
        //   }
        // });
        // TODO: 6.13隐藏  临时处理 上方注释
        await dealerListApi.addBusinessName(obj);
        this.row = { ...this.row, businessName: this.businessName };
        this.$message.success('操作成功');
        this.dealerPaymentIsComplete(this.row);
        this.row = null;
        this.supplementary = false;
        this.businessName = '';
      },
      //完款修改
      dealerPaymentIsComplete(row) {
        // TODO: 6.13隐藏  临时处理
        // if (!row.businessName) {
        //   this.row = row;
        //   return (this.supplementary = true);
        // }
        // // end
        this.$router.push({
          path: '/merchantManagement/operationsPayment',
          query: {
            id: row.id,
            merchantCode: row.merchantCode,
            isPay: row.isPay,
            merchantName: row.merchantName
          }
        });
      },
      //开通和暂停
      // 课程开通与暂停
      dealerStatus(id, status) {
        const that = this;
        this.$confirm('确定操作吗?', '修改状态', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          dealerListApi.dealerStatus(id, status).then((res) => {
            that.$nextTick(() => that.fetchData());
            that.$message.success('修改成功!');
          });
        });
      },

      //总部审核
      agree(id, isCheck) {
        const that = this;
        this.$confirm('确定操作吗?', '审核', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            areasDealerApi
              .updateIsCheck(id, isCheck)
              .then((res) => {
                that.$nextTick(() => that.fetchData());
                that.$message.success('审核成功!');
              })
              .catch((err) => {});
          })
          .catch((err) => {});
      },

      /**
       * 编辑城市
       * @param {Object} row - 当前行数据
       * @param id
       */
      // editCity(row, id) {
      //   this.isOpenCityAffiliation = true;
      //   row.selectedOptions = row.localProvince + '-' + row.localCity;
      //   this.$refs.editCityAffiliationRef.setData(row, id);
      // },

      /**
       * 禁用
       */
      disableAndOpenBtn(row, number) {
        this.$confirm(number == 2 ? '禁用后，该俱乐部将无法登陆，收益将变为无限账期，无法提现，请确认是否禁用该俱乐部?' : '是否启用该俱乐部？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          lading: true
        })
          .then(() => {
            let params = {
              id: row.id,
              isEnable: number // 禁用状态
            };
            dealerListApi
              .disableOperationsApi(params)
              .then(() => {
                this.$message.success('操作成功');
                this.fetchData();
              })
              .catch((error) => {
                console.error('🚀🥶💩~ error', error);
                this.$message.error('禁用失败，请稍后再试');
              });
          })
          .catch(() => {
            this.$message({
              type: 'info',
              message: '已取消'
            });
          });
      },

      openSubmit(row) {
        this.isShowClubForm = true;
        this.$refs.confirmClubFormRef.setData(row);
      },

      /**
       * 关闭城市编辑对话框
       */
      // closeCityDialog(val) {
      //   this.isOpenCityAffiliation = false;
      //   this.$refs.editCityAffiliationRef.resetData();
      //   if (val) {
      //     this.fetchData01();
      //   }
      // },
      /**
       * 关闭确认单
       */
      closeClubDialog(val = false) {
        this.isShowClubForm = false;
        val && this.fetchData01();
      },

      /**
       * 编辑所属品牌
       * @param row
       */
      async editBrand(row) {
        console.log(`🚀🥶💩🚀~ editBrand ~ row ~ L1509: `, row);
        try {
          const { data } = await dealerListApi.checkMerchantCanChange({ merchantCode: row.merchantCode });
          if (!data.canChange) {
            this.workStep = data.step;
            this.showTitleStatus = 3;
            this.reasonsContent = data.reasons || [];
            this.dialogInabilityVisible = true;
            return;
          }
          this.clubAndBrandDialog = true;
          this.$refs.clubAndBrandRef.setData(row);
        } catch (e) {
          console.log(`🚀🥶💩🚀~ editOperations ~ e ~ L2128: `, e);
          this.dialogInabilityVisible = false;
        }
      },

      handleOuterClose() {
        this.clubAndBrandDialog = false;
      },
      /**
       * 关闭确认单弹窗
       */
      handleCancel() {
        // console.log('打印🚀🥶💩~ val', val);
        this.clubAndBrandDialog = false;
        // if (val) this.fetchData(); // 获取列表数据
      },
      /**
       * 编辑合作伙伴编号
       */
      async editOperationsCode(row) {
        console.log(`🚀🥶💩🚀~ editOperationsCode ~ row ~ L1543: `, row);
        // 防止重复点击
        if (this.dialogOperationsCodeVisible || this.dialogInabilityVisible) {
          return;
        }

        try {
          // 调用 API 检查是否可以变更
          const { data } = await dealerListApi.checkReferenceCanChange({ merchantCode: row.merchantCode });

          if (!data.canChange) {
            // 不能变更时显示原因弹窗
            this.workStep = data.step;
            this.showTitleStatus = 4;
            this.reasonsContent = data.reasons || [];
            this.dialogInabilityVisible = true;
            return;
          }

          // 可以变更时打开编辑弹窗
          this.isShowOperationsCode = true;
          this.dialogOperationsCodeVisible = true;

          // 确保组件渲染完成后设置数据
          await this.$nextTick();
          await this.$nextTick();

          // 双重保障机制：立即尝试设置数据，如果失败则延迟重试
          if (this.$refs.operationsCodeRef) {
            this.$refs.operationsCodeRef.setData(row);
          } else {
            // 延迟重试机制，防止组件未完全渲染
            setTimeout(() => {
              if (this.$refs.operationsCodeRef) {
                this.$refs.operationsCodeRef.setData(row);
              } else {
                console.error('EditOperationsCodeDialog 组件引用未找到');
                // 如果组件引用仍然不存在，关闭弹窗并提示错误
                this.dialogOperationsCodeVisible = false;
                this.isShowOperationsCode = false;
                this.$message.error('组件加载失败，请重试');
              }
            }, 100);
          }
        } catch (error) {
          console.error(`🚀🥶💩🚀~ editOperationsCode ~ error ~ L1545: `, error);
          // API 调用失败时的错误处理
          // this.$message.error('检查权限失败，请稍后重试');
          // 确保弹窗状态被重置
          this.dialogOperationsCodeVisible = false;
          this.isShowOperationsCode = false;
          this.dialogInabilityVisible = false;
        }
      },
      /**
       * 确认单弹窗关闭
       */
      cancelOperationsCodeDialog(needRefresh = false) {
        // 重置所有相关状态
        this.dialogOperationsCodeVisible = false;
        this.isShowOperationsCode = false;

        // 确保子组件状态被重置
        if (this.$refs.operationsCodeRef) {
          this.$refs.operationsCodeRef.reset();
        }

        // 如果需要刷新列表数据
        if (needRefresh) {
          this.fetchData01();
        }
      },
      // 俱乐部注册码
      clubRegistrationCode(type, code) {
        const userInfo = {
          isZxBrand: this.isZxBrand,
          isOperations: this.isOperations
        };
        this.$refs.registrationCode.openDialogVisible(userInfo);
        this.$refs.registrationCode.getQR(userInfo, type, code);
      }
    }
  };
</script>

<style lang="scss" scoped>
  .brand-class {
    overflow: hidden; /* 超出隐藏 */
    white-space: nowrap; /* 不换行 */
    text-overflow: ellipsis; /* 超出显示 ... */
    display: block; /* 或 inline-block */
    min-width: 25px; /* 必须有固定宽度 */
  }
  .sucontent {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding-top: 30px;
    margin-right: 100px;
    &:last-child {
      margin-right: 0;
    }
  }
  .code {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .card {
    background-color: #fef0f0;
    color: #f56c6c;
    padding: 10px 15px;
    border-radius: 5px;
  }
  .container-card {
    padding: 15px 15px 15px 15px;
    margin-bottom: 15px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  }

  .red {
    color: red;
  }

  .green {
    color: green;
  }

  .blue {
    color: blue;
  }
  .codeSuccess {
    position: absolute;
    top: 0;
    left: 0;
    width: 200px;
    height: 200px;
    background: rgba(255, 255, 255, 0.85);
    display: flex;
    justify-content: center;
    align-items: center;
  }
  @media (max-width: 767px) {
    .el-message-box {
      width: 80% !important;
    }
  }
</style>
