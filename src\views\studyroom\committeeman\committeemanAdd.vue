<template>
  <el-dialog title="新增学委" :visible.sync="visible" width="60%" top="5vh" append-to-body @close="close">
    <el-form :model="form" ref="form" :rules="rules" :inline="true" label-width="78px">
      <el-form-item label="姓名" prop="name">
        <el-input
          v-model="form.name"
          placeholder="姓名"
          clearable
          size="small"/>
      </el-form-item>
      <el-form-item label="手机号" prop="phone">
        <el-input
          v-model="form.phone"
          placeholder="手机号"
          clearable
          size="small"/>
      </el-form-item>
      <el-form-item label="性别" prop="gender">
        <el-select v-model="form.gender">
          <el-option v-for="(item,index) in [{label:'男',value:'1'},{label:'女',value:'0'}]" :key="index"
                     :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="地区" prop="address" v-if="addressBtn">
        <el-cascader :options="addressOptions" v-model="form.address" filterable></el-cascader>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="submitForm" v-if="!isView">确 定</el-button>
      <el-button @click="visible = false">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>
  import committeemanApi from "@/api/studyroom/committeeman";
  export default {
    name: "committeemanAdd",
    data() {
      // 手机号验证
      const validatePhone = (rule, value, callback) => {
        if (value === "") {
          callback(new Error("请输入手机号"));
        } else {
          if (!/^1[3456789]\d{9}$/.test(value)) {
            callback(new Error("请输入正确的手机号"));
          } else {
            callback();
          }
        }
      };
      return {
        // 遮罩层
        visible: false,
        isView: false,
        addressBtn: false,
        form: {},
        addressOptions:[],
        rules: {
          name: [
            {required: true, message: "请输入姓名", trigger: "blur"}
          ],
          phone: [
            {required: true, validator: validatePhone, trigger: "blur"}
          ],
          gender: [
            {required: true, message: "请选择性别", trigger: "blur"}
          ],
          address: [
            {required: true, message: "请选择地区", trigger: "blur"}
          ],
        }
      };
    },
    created() {
      committeemanApi.getAllRegion().then(res=>{
        let temp= [];
        res.data.map( i => {
          temp.push({
            label:i.name,
            value:i.name,
            id:i.id,
            children:i.children.map((item)=>{
              return{
                label:item.name,
                value:item.name,
                id:item.id,
                leaf:true
              }
            })
          })
        });
        this.addressOptions=temp;
      });
    },
    methods: {
      btnShow() {
        this.$store.getters.roles.forEach(element => {
          if (element.val==='admin'){
            this.addressBtn=true;
          }else {
            this.addressBtn=false;
          }

        });
      },
      submitForm(){
        this.$refs["form"].validate(valid => {
          if (valid) {
            if (this.form.address) {
              this.form.province=this.form.address[0];
              this.form.city=this.form.address[1];
            }
            committeemanApi.addCommitteeman(this.form).then(response => {
              this.$message.success("提交成功！")
              this.visible = false;
              this.$emit("ok");
            });
          }
        });
      },
      show() {
        this.reset();
        this.visible = true;
        this.btnShow();
      },
      close() {
        this.reset();
      },
      reset() {
        this.form = {
          name: null,
          phone: null,
          gender: null,
          address: null,
        };
        if (this.$refs['form']) {
          this.$refs['form'].resetFields();
        }
      }
    }
  };
</script>

<style>
  .user_green {
    background: #02a490;
    color: #FFFFFF;
  }

  .user_red {
    background: #ec808d;
    color: #FFFFFF;
  }

  .border_user {
    text-align: center;

  }

  .border1_bottom {
    border-bottom: 1px solid #333333;
  }

  .border1_right {
    border-top: 1px solid #333333;
    border-left: 1px solid #333333;
    border-right: 1px solid #333333;
  }
</style>
