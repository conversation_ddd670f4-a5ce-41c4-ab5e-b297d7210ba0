/**
 * “角色管理”相关接口
 */
import request from '@/utils/request'

export default {
  /**
   * 添加角色
   * @param data
   */
  addRole(data) {
    return request({
      url: '/znyy/sys/role',
      method: 'post',
      data
    })
  },

  /**
   * 删除角色
   * @param data
   */
  deleteRole(data) {
    return request({
      url: '/znyy/sys/role/' + data,
      method: 'delete'
    })
  },

  /**
   * 查询角色
   * @param queryParam
   * @param pageParam
   */
  queryRole(query, pageParam) {
    return request({
      url: '/znyy/sys/role/query',
      method: 'GET',
      params: {
        rname:query.rname,
        rval: query.rval,
        pageNum: pageParam.currentPage==null?1:pageParam.currentPage,
        pageSize: pageParam.size==null?10:pageParam.size
      }
    })
  },

  /**
   * 更新角色
   * @param data
   */
  updateRole(data) {
    return request({
      url: '/znyy/sys/role/info',
      method: 'patch',
      data
    })
  },

  /**
   * 更新角色的权限
   * @param perm
   */
  updateRolePerms(data) {
    return request({
      url: '/znyy/sys/role/perm',
      method: 'patch',
      data
    })
  },

  /**
   * 添加角色的权限
   * @param perm
   */
  addRolePerm(data) {
    return request({
      url: '/znyy/sys/role/perm',
      method: 'post',
      data
    })
  },

  /**
   * 删除角色的权限
   * @param perm
   */
  deleteRolePerm(data) {
    return request({
      url: '/znyy/sys/role/perm',
      method: 'delete',
      data
    })
  },

  /**
   * 查选角色的所有权限值
   * @param rid
   */
  findRolePerms(rid) {
    return request({
      url: '/znyy/sys/role/perms/'+rid,
      method: 'get'
    })
  },

  /**
   * 所有角色
   * @returns {AxiosPromise}
   */
  listRole() {
    return request({
      url: '/znyy/sys/role/listRole',
      method: 'GET',
    })
  },

}
