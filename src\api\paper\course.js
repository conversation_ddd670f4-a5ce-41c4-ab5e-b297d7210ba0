import request from '@/utils/request'

export default {
  // 分页查询
  list(data) {
    return request({
      url: '/paper/web/course/list',
      method: 'GET',
      params: data
    })
  },
//添加
  createOrUpdate(data) {
    return request({
      url: '/paper/web/course',
      method: 'POST',
      data
    })
  },
  detail(id) {
    return request({
      url: '/paper/web/course',
      method: 'GET',
      params: {
        id: id
      }
    })
  },
  delete(id){
    return request({
      url: '/paper/web/course',
      method: 'DELETE',
      params: {
        id: id
      }
    })
  },

  trainAfterCourseList(data){
    return request({
      url: '/paper/web/trainQuestion/practiceList',
      method: 'GET',
      params: data
    })
  }
}
