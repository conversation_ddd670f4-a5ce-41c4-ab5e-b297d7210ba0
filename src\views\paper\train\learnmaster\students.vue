<template>
  <div class="app-container">
    <el-form :model="dataQuery" ref="queryForm" :inline="true">
      <el-form-item label="学号：">
        <el-input v-model="dataQuery.studentCode" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getList">查询</el-button>
      </el-form-item>
    </el-form>
    <el-col :span="24" style="margin-bottom: 30px">
      <el-button type="success" icon="el-icon-plus" size="mini" @click="handleAdd()">新增</el-button>
      <el-button type="warning" icon="el-icon-back" size="mini" @click="goBack()">返回</el-button>
    </el-col>
    <el-table class="common-table" v-loading="tableLoading" :data="tableData" style="width: 100%;margin-bottom: 20px;"
      row-key="id" border default-expand-all>
      <el-table-column prop="id" label="ID"></el-table-column>
      <el-table-column prop="studentName" label="学员姓名" show-overflow-tooltip></el-table-column>
      <el-table-column prop="withdrawnBonus" label="操作" width="480">
        <template slot-scope="scope">
          <el-button type="danger" size="mini" icon="el-icon-delete" @click="handleDelete(scope.row.id)">删除
          </el-button>
        </template>
      </el-table-column>
      <el-table-column prop="studentCode" label="学员编号" />
    </el-table>
    <!-- 分页 -->
    <el-col :span="24" style="margin-bottom: 20px">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>

    <!-- 修改或者新增弹窗 -->
    <el-dialog title="新增学员" :visible.sync="open" width="70%" @close="cancel">
      <el-form ref="form" :model="form" :rules="rules" label-width="120px" style="width: 70%;">
        <el-form-item label="学员编号：" prop="studentCode">
          <el-input v-model="form.studentCode" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import lmsApi from '@/api/paper/train/learnmasterstudents'
import { pageParamNames } from '@/utils/constants'

export default {
  data() {
    return {
      learnMasterCode: '',
      title: '',
      dataQuery: {
        studentCode: '',
        learnMasterCode: ''
      },
      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      open: false,
      tableData: [],
      form: {},
      // 表单校验
      rules: {
        studentCode: [{ required: true, message: '请输入学员编号', trigger: 'blur' }],
      }
    }
  },
  created() {
    this.learnMasterCode = this.$route.query.code
    if (this.learnMasterCode) {
      this.getList()
    }
  },
  methods: {
    goBack() {
      this.$store.dispatch('delVisitedViews', this.$route)
      this.$router.push({ path: '/train/learnmaster' })
    },
    handleDelete(id) {
      this.$confirm('确定要删除吗？', '删除学员', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        lmsApi.delete(id).then(res => {
          this.$message.success('删除成功！')
          this.getList()
        })
      })
    },
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          this.form.learnMasterCode = this.learnMasterCode;
          lmsApi.save(this.form).then(response => {
            this.$message.success('提交成功！')
            this.open = false
            this.getList()
          })
        }
      })
    },
    //分页查询
    getList() {
      this.loading = true
      this.dataQuery.pageNum = this.tablePage.currentPage
      this.dataQuery.pageSize = this.tablePage.size
      this.dataQuery.learnMasterCode = this.learnMasterCode
      lmsApi.list(this.dataQuery).then(res => {
        this.tableData = res.data.data
        this.loading = false
        // 设置后台返回的分页参数
        pageParamNames.forEach(name =>
          this.$set(this.tablePage, name, parseInt(res.data[name]))
        )
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: '',
        studentCode: '',
        learnMasterCode: ''
      }
    },

    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val
      this.getList()
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val
      this.getList()
    }
  }
}
</script>
