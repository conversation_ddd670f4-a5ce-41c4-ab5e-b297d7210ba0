<template>
  <div class="add-video-dialog">
    <el-dialog width="900px" :visible.sync="dialogParam.visible" @closed="closeAddDialog()" :close-on-click-modal="false" @submit.native.prevent>
      <div slot="title" class="dialog-title">
        <span>查看试卷</span>
      </div>
      <div class="exam-detail">
        <div class="inner">
          <div class="cont">
            <div class="cont-left">
              <div class="paper_title_css">{{ explainInfo.examName }}</div>
              <div class="subhead">
                试卷说明：本试卷共
                <span v-if="explainInfo.radioNum > 0">{{ explainInfo.radioNum }}道单选题</span>
                ，
                <span v-if="explainInfo.multiSelectNum > 0">{{ explainInfo.multiSelectNum }} 道多选题</span>
                ，
                <span v-if="explainInfo.judgeNum > 0">{{ explainInfo.judgeNum }} 道判断题，</span>
                满分
                <span>{{ explainInfo.examFullMarks }}</span>
                ，考试时间
                <span>{{ explainInfo.examTimeLimit }}</span>
                分钟
              </div>
              <div class="item" v-for="(i, index) in datalist" v-bind:key="index">
                <div class="subject">{{ index + 1 }}、【{{ i.questionType == 1 ? '单选' : i.questionType == 2 ? '多选' : '判断' }}】{{ i.questionName }}</div>
                <div v-if="i.questionType == 2" disabled>
                  <div v-for="(options, indx) in i.optionList" :label="options.questionOptionDescription" :key="indx">
                    <div class="paper_list">
                      <span class="title_css_style" v-if="options.questionOptionDescription">{{ options.questionOptionDescription + '、' }}</span>
                      <span class="checkbox_css_style">{{ options.questionOptionContent }}</span>
                    </div>
                  </div>
                </div>
                <div v-else class="riade_css" disabled>
                  <div :label="options.questionOptionDescription" v-for="(options, indx) in i.optionList" :key="indx">
                    <div class="paper_list">
                      <span class="title_css_style">
                        <span v-if="options.questionOptionDescription">{{ options.questionOptionDescription + '、' }}</span>
                      </span>
                      <span class="checkbox_css_style">{{ options.questionOptionContent }}</span>
                    </div>
                  </div>
                </div>
              </div>
              <!-- <div class="subhead">多选题</div>
              <div class="item" v-for="(i, index) in datalistM" v-bind:key="index" >
                <div class="subject">{{ index + 1 }}、{{ i.title }}</div>
                <el-checkbox-group v-model="i.answer" disabled="true">
                  <el-checkbox v-for="(item, index) in i.result" :key="index"
                    :label="item.outanswer" >{{ item.outanswer + "、" + item.outcome }}
                  </el-checkbox>
                </el-checkbox-group>
              </div> -->
            </div>
          </div>
        </div>
      </div>
      <!-- 底部按钮栏 -->
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="closeAddDialog()">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
  import examApi from '@/api/training/exam';
  export default {
    name: 'viewTestPaper',
    components: {},
    props: {
      dialogParam: {
        type: Object,
        default() {
          return {};
        }
      }
    },
    data() {
      return {
        choiseList: [],
        datalist: [],
        explainInfo: {
          radioNum: 0,
          multiSelectNum: 0,
          judgeNum: 0
        }
      };
    },
    mounted() {},
    methods: {
      open(id) {
        this.explainInfo = {
          radioNum: 0,
          multiSelectNum: 0,
          judgeNum: 0
        };
        examApi.trainingDetail({ id: id }).then((res) => {
          this.datalist = res.data.questionList;
          this.explainInfo = { ...this.explainInfo, ...res.data };
          res.data.questionList.forEach((item) => {
            if (item.questionType == 1) {
              this.explainInfo.radioNum += 1;
            } else if (item.questionType == 2) {
              this.explainInfo.multiSelectNum += 1;
            } else {
              this.explainInfo.judgeNum += 1;
            }
          });
        });
      },
      closeAddDialog() {
        this.$emit('closeDialog');
      }
    }
  };
</script>
<style scoped lang="scss">
  .exam-detail {
    // padding: 20px 0;
    // box-sizing: border-box;
    .inner {
      // width: 1200px;
      width: 100%;
      margin: 0 auto;
      .title {
        margin-bottom: 20px;
        a {
          color: #333;
          margin-right: 5px;
        }
      }
      .cont {
        display: flex;
        width: 100%;
        justify-content: space-between;
        .cont-left {
          // width: 742px;
          .paper_title_css {
            font-size: 25px;
            font-weight: bold;
            margin-bottom: 15px;
            text-align: center;
          }
          width: 100%;
          background: #fff;
          min-height: 600px;
          .subhead {
            width: 100%;
            height: 47px;
            background: #fafafa;
            line-height: 47px;
            color: #333;
            padding-left: 26px;
            box-sizing: border-box;
            font-size: 16px;
            font-weight: bold;
          }
          .item {
            padding: 15px 26px;
            box-sizing: border-box;
            .subject {
              margin-bottom: 20px;
              color: #606266;
            }
            .option {
              ::v-deep .el-radio {
                margin-bottom: 15px;
                display: flex;
                flex-direction: row;
                flex-wrap: wrap;
              }
              ::v-deep .el-radio__input {
                display: block;
              }
              ::v-deep.el-radio__label {
                display: block;
                white-space: normal;
                width: 600px;
                line-height: 15px;
              }
            }
          }
        }
      }
    }
  }

  ul {
    width: 270px;
    // padding: 10px;
    // box-sizing: border-box;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    flex-wrap: wrap;
    align-content: flex-start;
    align-items: flex-start;
    background: red;
    li {
      width: 35px;
      height: 35px;
      border-radius: 6px;
      margin: 5px;
      &.green-bg {
        background: green;
      }
      &.red-bg {
        background: red;
      }
      &.gray-bg {
        background: gray;
      }
    }
  }
  ::v-deep .el-checkbox {
    display: block;
    margin-bottom: 10px;
  }
  .title_css_style {
    display: inline-block;
    padding-top: 3px;
  }
  .riade_css {
    .title_css_style::before {
      border-radius: 50%;
    }
  }
  .title_css_style::before {
    content: ' ';
    border: 1px solid #dcdfe6;
    display: inline-block;
    width: 14px;
    height: 14px;
    padding-top: 4px;
    margin-right: 8px;
    margin-left: 20px;
    vertical-align: middle;
  }
  .paper_list {
    margin-top: 10px;
    color: #606266;
  }
  .checkbox_css_style {
    max-width: 720px;
    display: inline-block;
    white-space: initial;
    line-height: 25px;
    vertical-align: middle;
  }
</style>
