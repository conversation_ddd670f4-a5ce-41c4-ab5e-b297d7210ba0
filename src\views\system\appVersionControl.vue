<template>
  <div class="app-container">
    <el-form :inline="true" class="container-card" label-width="110px" label-position="left">
      <el-row>
        <el-col :span="8">
          <el-form-item label="版本号：">
            <el-input v-model="dataQuery.version" placeholder="请输入版本号：" clearable />
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item label="更新时间：">
            <el-date-picker style="width: 100%;" v-model="value1" value-format="yyyy-MM-dd" type="daterange" align="right"
              unlink-panels range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" @change="dateVal" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="App类型：">
            <el-select v-model="dataQuery.appType" placeholder="全部" style="width: 200px;">
              <el-option v-for="(item,index) in appType" :key="index" :label="item.value" :value="item.label" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="8">
          <el-form-item label="强制升级：">
            <el-select v-model="dataQuery.forcedUpGrade" placeholder="全部" style="width: 200px;">
              <el-option v-for="(item,index) in [{label:'是',value: 1 },{label:'否',value: 0 }]" :key="index" :label="item.label"
                :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="升级日志：">
            <el-input v-model="dataQuery.upGradeLog" placeholder="请输入升级日志：" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="fetchData01()">搜索</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-form :inline="true" style="margin-bottom: 20px;">
      <el-button type="primary" icon="el-icon-plus" @click="clickAdd">添加</el-button>
      <el-button type="success" icon="el-icon-switch-button" @click="batchOpen(multipleSelection)">批量开通</el-button>
      <el-button type="danger" icon="el-icon-video-pause" @click="batchPause(multipleSelection)">批量暂停</el-button>
      <el-button type="warning" icon="el-icon-delete-solid" @click="batchDelete(multipleSelection)">批量删除</el-button>
    </el-form>

    <el-table v-loading="tableLoading" class="common-table" :data="tableData" @selection-change="handleSelectionChange"
      style="width: 100%;margin-bottom: 20px;" row-key="id" border default-expand-all :tree-props="{list: 'children', hasChildren: 'true'}">
      <el-table-column type="selection" width="55" />
      <el-table-column prop="version" label="版本号" width="180" />
      <el-table-column prop="appType" label="App类型" width="180" />
      <el-table-column prop="id" label="操作" sortable width="200">
        <template slot-scope="scope">
          <el-button type="primary" size="mini" icon="el-icon-edit-outline" @click="handleUpdate(scope.row.id)">编辑</el-button>
          <el-button type="danger" size="mini" icon="el-icon-delete" @click="singleDelete(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="downloadUrl" label="下载地址" width="250" show-overflow-tooltip />
      <el-table-column prop="isEnable" label="状态" sortable>
        <template slot-scope="scope">
          <span class="green" v-if="scope.row.isEnable === 1">开通</span>
          <span class="red" v-else>暂停</span>
        </template>
      </el-table-column>
      <el-table-column prop="forcedUpGrade" label="强制升级">
        <template slot-scope="scope">
          <span class="green" v-if="scope.row.forcedUpGrade === true">是</span>
          <span class="red" v-else>否</span>
        </template>
      </el-table-column>
      <el-table-column prop="addTime" label="更新时间" width="180" />
      <el-table-column prop="upGradeLog" label="升级日志" width="180" show-overflow-tooltip />
    </el-table>
    <!-- 添加弹窗 -->
    <el-dialog :title="addOrUpdate?'添加App版本管理':'编辑App版本管理'" :visible.sync="dialogVisible" width="70%"
      :close-on-click-modal="false" @close="close">
      <el-form :ref="addOrUpdate?'addCourseData':'updateActive'" :rules="rules" :model="addOrUpdate?addCourseData:updateActive"
        label-position="left" label-width="120px" style="width: 100%;">
        <el-form-item label="版本号:" prop="version">
          <el-col :xs="24" :span="18">
            <el-input v-if="addOrUpdate" v-model="addCourseData.version" />
            <el-input v-else v-model="updateActive.version" />
          </el-col>
        </el-form-item>
        <el-form-item label="app类型:" prop="appType">
          <el-col :xs="24" :span="18">
            <el-select style="width: 100%;" v-if="addOrUpdate" v-model="addCourseData.appType">
              <el-option v-for="(item,index) in appType" :key="index" :label="item.value" :value="item.label" />
            </el-select>
            <el-select style="width: 100%;" v-else v-model="updateActive.appType">
              <el-option v-for="(item,index) in appType" :key="index" :label="item.value" :value="item.label" />
            </el-select>
          </el-col>
        </el-form-item>
        <el-form-item label="下载地址:" prop="downloadUrl">
          <el-col :xs="24" :span="18">
            <el-input v-if="addOrUpdate" v-model="addCourseData.downloadUrl" />
            <el-input v-else v-model="updateActive.downloadUrl" />
          </el-col>
        </el-form-item>
        <el-form-item label="强制升级:" prop="forcedUpGrade">
          <template>
            <el-radio v-model="radio" label='true' @change="change(radio)">是</el-radio>
            <el-radio v-model="radio" label='false' @change="change(radio)">否</el-radio>
          </template>
        </el-form-item>
        <el-form-item label="升级日志" prop="upGradeLog">
          <el-col :xs="24" :span="18">
            <el-input v-if="addOrUpdate" v-model="addCourseData.upGradeLog" type="textarea" resize="none" :rows="4" />
            <el-input v-else v-model="updateActive.upGradeLog" type="textarea" resize="none" :rows="4" />
          </el-col>
        </el-form-item>
        <el-form-item label="状态" prop="isEnable" v-if="!addOrUpdate">
          <template>
            <el-radio v-model="status" label="1" @change="changeStatus(status)">开通</el-radio>
            <el-radio v-model="status" label="0" @change="changeStatus(status)">暂停</el-radio>
          </template>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button v-if="addOrUpdate" size="mini" type="primary" @click="addActiveFun('addCourseData')">新增</el-button>
        <el-button v-if="!addOrUpdate" size="mini" type="primary" @click="updateActiveFun('updateActive')">修改</el-button>
        <el-button size="mini" @click="close">关闭</el-button>
      </div>
    </el-dialog>

    <!-- 分页 -->
    <el-col :span="24">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]" layout="total, sizes, prev, pager, next, jumper"
        :total="tablePage.totalItems" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    </el-col>
  </div>
</template>

<script>
  import appApi from '@/api/appVersionControl'
  import {
    pageParamNames
  } from '@/utils/constants'
  export default {
    data() {
      return {
        tableLoading: false,
        // 分页
        tablePage: {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null
        },
        tableData: [{
          name: 1,
          title: '1111'
        }],
        dataQuery: {
          version: '',
          startTime: '',
          endTime: '',
          upGradeLog: '',
          forcedUpGrade: '',
          appType: ''
        },
        rules: { // 表单提交规则
          version: [{
            required: true,
            message: '请填写版本号',
            trigger: 'blur'
          }],
          appType: [{
            required: true,
            message: '请选择版本类型',
            trigger: 'change'
          }],
          downloadUrl: [{
            required: true,
            message: '请填写课程描述',
            trigger: 'blur'
          }],
          forcedUpGrade: [{
            required: true,
            message: '必填',
            trigger: 'change'
          }],
          upGradeLog: [{
            required: true,
            message: '请填写升级日志',
            trigger: 'blur'
          }]
        },
        radio: '', //强制升级
        status: '0', //是否开通 值必须是字符串
        value1: '', //起始时间
        dialogVisible: false,
        addOrUpdate: true,
        updateActive: {},
        addCourseData: {},
        appType: [], //版本类型
        multipleSelection: [], //多选数据
      }
    },
    created() {
      this.getType()
      this.fetchData();
    },
    methods: {
      // app版本类型
      getType() {
        appApi.appType().then(res => {
          this.appType = res.data
          // console.log(this.appType)
        })
      },
      // 获取起始时间
      dateVal(e) {
        // console.log(e[0]);
        this.dataQuery.startTime = e[0]
        this.dataQuery.endTime = e[1]
      },
      fetchData01() {
        this.tablePage = {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null
        }
        this.fetchData();
      },
      // 查询提现列表
      fetchData() {
        const that = this
        that.tableLoading = true
        appApi.appList(that.tablePage.currentPage, that.tablePage.size, that.dataQuery).then(res => {
          that.tableData = res.data.data
          that.tableLoading = false
          // 设置后台返回的分页参数
          pageParamNames.forEach(name => that.$set(that.tablePage, name, parseInt(res.data[name])))
        })
      },
      // 新增操作
      clickAdd() {
        this.addCourseData = {
          'version': '',
          'appType': '',
          'downloadUrl': '',
          'forcedUpGrade': '',
          'upGradeLog': ''
        }
        this.dialogVisible = true
      },
      // 新增提交
      addActiveFun(ele) {
        const that = this
        that.$refs[ele].validate(valid => { // 表单验证
          if (valid) {
            const loading = this.$loading({
              lock: true,
              text: '新增App版本管理',
              spinner: 'el-icon-loading',
              background: 'rgba(0, 0, 0, 0.7)'
            })
            appApi.addApp(that.addCourseData).then(() => {
              that.dialogVisible = false
              loading.close()
              that.$nextTick(() => that.fetchData())
              that.$message.success('新增App版本管理成功')
              that.fetchData01();
            }).catch(err => {
              loading.close()
            })
          } else {
            console.log('error submit!!')
            //loading.close();
            return false
          }
        })
      },
      // 点击编辑按钮
      handleUpdate(id) {
        const that = this
        that.dialogVisible = true
        that.addOrUpdate = false
        appApi.queryActive(id).then(res => {
          that.updateActive = res.data.data
          that.radio = that.updateActive.forcedUpGrade.toString() // 状态回显
          that.status = that.updateActive.isEnable.toString()
        }).catch(err => {

        })
      },
      // 修改提交
      updateActiveFun(ele) {
        const that = this
        that.$refs[ele].validate(valid => { // 表单验证
          if (valid) {
            that.updateActive.forcedUpGrade = that.radio;
            that.updateActive.isEnable = that.status
            const loading = this.$loading({
              lock: true,
              text: '修改App版本管理提交',
              spinner: 'el-icon-loading',
              background: 'rgba(0, 0, 0, 0.7)'
            })
            appApi.updateApp(that.updateActive).then(() => {
              that.dialogVisible = false
              loading.close()
              that.$nextTick(() => that.fetchData())
              that.$message.success('修改App版本管理成功')
               that.fetchData01();
            }).catch(err => {
              // 关闭提示弹框
              loading.close()
            })
          } else {
            console.log('error submit!!')
            // loading.close();
            return false
          }
        })
      },
      // 单个删除
      singleDelete(id) {
        const that = this;
        this.$confirm('确定操作吗?', '修改状态', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          appApi.deleteApp(id).then(res => {
            that.$nextTick(() => that.fetchData())
            that.$message.success('删除成功!')
          }).catch(err => {

          })
        }).catch(err => {

        })
      },
      // 批量删除
      batchDelete(data) {
        const that = this;
        if (that.multipleSelection.length >= 1) {
          this.$confirm('确定操作吗?', '批量删除', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            const appVersionCo = {
              appVersionVos: data
            }
            appApi.batchDeleteApp(appVersionCo).then(res => {
              if (res.success) {
                that.$nextTick(() => that.fetchData())
                that.$message.success('批量删除成功!')
              } else {

              }
            }).catch(err => {

            })
          }).catch(err => {

          })
        } else {
          that.$message.info('请至少选择一项')
        }
      },
      // 批量开通
      batchOpen(data) {
        const that = this;
        if (that.multipleSelection.length >= 1) {
          this.$confirm('确定操作吗?', '批量开通', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            const appVersionCo = {
              appVersionVos: data
            }
            appApi.batchOpenApp(appVersionCo).then(res => {
              if (res.success) {
                that.$nextTick(() => that.fetchData())
                that.$message.success('批量开通成功!')
              } else {
                that.$message.error(res.message)
              }
            }).catch(err => {
              console.log(err)
            })
          }).catch(err => {

          })
        } else {
          that.$message.info('请至少选择一项')
        }
      },
      // 批量暂停
      batchPause(data) {
        const that = this;
        if (that.multipleSelection.length >= 1) {
          this.$confirm('确定操作吗?', '批量暂停', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            const appVersionCo = {
              appVersionVos: data
            }
            appApi.batchPauseApp(appVersionCo).then(res => {
              if (res.success) {
                that.$nextTick(() => that.fetchData())
                that.$message.success('批量暂停成功!')
              } else {
                that.$message.error(res.message)
              }
            }).catch(err => {
              console.log(err)
            })
          }).catch(err => {

          })
        } else {
          that.$message.info('请至少选择一项')
        }
      },
      // 分页
      handleSizeChange(val) {
        this.tablePage.size = val
        this.fetchData()
      },
      handleCurrentChange(val) {
        this.tablePage.currentPage = val
        this.fetchData()
      },
      // 关闭弹窗
      close() {
        this.dialogVisible = false
      },
      // 强制升级改变事件
      change(radio) {
        if (radio == "true") {
          this.addCourseData.forcedUpGrade = true;
        } else {
          this.addCourseData.forcedUpGrade = false;
        }
      },
      // 状态改变事件
      changeStatus(status) {
        if (status == "1") {
          this.addCourseData.isEnable = 1;
        } else {
          this.addCourseData.isEnable = 0;
        }
      },
      // 多选的值
      handleSelectionChange(val) {
        // val.forEach(item =>{
        this.multipleSelection = val;
        // })
      }
    }
  }
</script>

<style>
  .container-card {
    padding: 15px 15px 15px 15px;
    margin-bottom: 15px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  }

  .period-table td,
  .period-table th {
    text-align: center;
  }

  .mt20 {
    margin-top: 20px;
  }
  @media (max-width:767px) {
	  .el-message-box{
		width: 80%!important;
	  }
  }   
</style>
