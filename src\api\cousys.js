/**
 * 商品相关接口
 */
import request from '@/utils/request'
import da from 'element-ui/src/locale/lang/da'
import cousysApi from '@/api/grammar'

export default {
  queryInfo(loginName) {
    return request({
      url: '/cousys/web/schedule/query/one?loginName=' + loginName,
      method: 'GET'
    })
  },
  addCousysInfo(data) {
    return request({
      url: '/cousys/web/schedule/add/info',
      method: 'POST',
      data
    })
  },
  addByLoginName(data) {
    return request({
      url: '/znyy/cousys/add/parents',
      method: 'POST',
      data
    })
  },
  addStudent(data) {
    return request({
      url: '/znyy/cousys/add/student',
      method: 'POST',
      data
    })
  },
  getPayCode(courseOrderId) {
    return request({
      url: '/cousys/web/schedule/get/code?courseOrderId=' + courseOrderId,
      method: 'GET'
    })
  },
  findOrderInfoById(courseOrderId) {
    return request({
      url: '/cousys/web/schedule/find/order/info?courseOrderId=' + courseOrderId,
      method: 'GET'
    })
  },
  queryCousysInfo(courseOrderId, cousysMemberCode, cousysStudentCode) {
    return request({
      url: '/cousys/query/cousys/info?courseOrderId=' + courseOrderId + '&&cousysMemberCode=' + cousysMemberCode + '&&cousysStudentCode=' + cousysStudentCode,
      method: 'GET'
    })
  },
  updateCousysInfo(data) {
    return request({
      url: '/cousys/web/schedule/update/info',
      method: 'PUT',
      data
    })
  },
  getCheckStatus() {
    return request({
      url: '/cousys/web/schedule/check/status/list',
      method: 'GET'
    })
  },
  getCourseStatusList() {
    return request({
      url: '/cousys/web/schedule/course/status/list',
      method: 'GET'
    })
  },
  getMerchantList() {
    return request({
      url: '/cousys/web/schedule/merchant/list',
      method: 'GET'
    })
  },
  getScheduleList(pageNum, pageSize, data) {
    return request({
      url: '/cousys/web/schedule/list/' + pageNum + '/' + pageSize,
      method: 'GET',
      params: data
    })
  },
  findCousysInfo(courseOrderId) {
    return request({
      url: '/cousys/web/schedule/find/info?courseOrderId=' + courseOrderId,
      method: 'GET'
    })
  },
  findCourseTypeTimeList() {
    return request({
      url: 'cousys/web/schedule/course/time/list',
      method: 'GET'
    })
  },
  getPlanByCourseType(courseType,pageNum) {
    return request({
      url: 'cousys/web/course/type/plan?courseType='+courseType+"&pageNum="+pageNum,
      method: 'GET'
    })
  },
  matchTutor(data) {
    return request({
      url: 'cousys/web/course/match/tutor',
      method: 'GET',
      params: data
    })
  },
  getTutorPlan(data) {
    return request({
      url: 'cousys/web/course/tutor/plan',
      method: 'GET',
      params: data
    })
  },
  getPermissionList(pageNum, pageSize, data) {
    return request({
      url: '/znyy/cousys/permission/list/' + pageNum + '/' + pageSize,
      method: 'GET',
      params: data
    })
  },
  getParentCentre(pageNum, pageSize, data) {
    return request({
      url: '/cousys/web/schedule/parent/centre/' + pageNum + '/' + pageSize,
      method: 'GET',
      params: data
    })
  },
  deletePermission(merchantCode) {
    return request({
      url: '/znyy/cousys/delete/permission?merchantCode='+merchantCode,
      method: 'PUT'
    })
  },
  addPermission(merchantCode,permission) {
    return request({
      url: '/znyy/cousys/add/permission?merchantCode='+merchantCode+'&&permission='+permission,
      method: 'PUT'
    })
  },
  getAllRegion(){
    return request({
      url: '/v2/mall/queryAllRegion/',
      method: 'GET'
    })
  },
  getParentCentreDetail(memberCode){
    return request({
      url: '/cousys/web/schedule/parent/centre/detail?memberCode='+memberCode,
      method: 'GET'
    })
  },
  saveCourse(data) {
    return request({
      url: 'cousys/web/course/save/course',
      method: 'POST',
      data:data
    })
  },
}
