<template>
  <div>
    <el-form :label-position="'left'" class="Aform">
      <el-row>
        <div class="subjectCard" v-for="(subject,i) in form.subjectMap" :key="i">
          <div>{{ subject.name }}</div>
          <div class="templateCard" v-for="(template, index) in subject.dtos" :key="index">
              <el-input-number size="mini" :min="0" v-model="template.scopeMin"/>~
              <el-input-number size="mini" :min="0" v-model="template.scopeMax"/>&nbsp;（单位分）
              <el-input type="textarea" v-model="template.content" placeholder="评估反馈" style="margin-top: 20px"></el-input>
              <el-input type="textarea" v-model="template.planning" placeholder="未来规划" style="margin-top: 20px"></el-input>
              <el-button @click="deleteTemplate(subject.dtos, index)" style="margin-top: 20px;margin-left: 10px" type="danger" plain>删除</el-button>
          </div>
          <el-button icon="el-icon-plus" type="primary" style="margin: auto" @click="addTemplateOpen(subject)">添加分值</el-button>
        </div>
      </el-row>
      <el-form-item>
        <el-button type="primary" @click="onSubmit">提交</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>

import categoryApi from "@/api/abacusMentalCalc/category";
import reportApi from "@/api/abacusMentalCalc/report";
export default {
  name: 'save',
  data() {
    return {
      form:{},
    }
  },
  created() {
    let id = this.$route.query.id
    reportApi.detail(id).then(res => {
      this.form = res.data;
      for (let i = 0; i <this.form.subjectMap.length; i++) {
        if(this.form.subjectMap[i].dtos == null){
          this.form.subjectMap[i].dtos = [];
        }
      }
    });
  },

  methods: {
    getRequestData(){
      let data = {
        courseType:this.form.courseType,
        id:this.form.id,
        reportName:this.form.reportName,
        grade:this.form.grade,
        subjectVoList:[],
      }
      for (let i = 0; i <this.form.subjectMap.length; i++) {
        if(this.form.subjectMap[i].dtos){
          data.subjectVoList.push({
            id:this.form.subjectMap[i].id,
            name:this.form.subjectMap[i].name,
            paperReportTemplateList:this.form.subjectMap[i].dtos,
          })
        }
      }
      return data
    },
    onSubmit() {
      console.log(this.form)
      let param = this.getRequestData()
      console.log(param)
      if(param.subjectVoList.length === 0){
        this.$message.error("需添加分值，才能提交")
        return
      }
      reportApi.saveOrUpdate(param).then((res) => {
        if (res.success) {
          this.$message.success('提交成功！')
          this.$router.push({ path: '/abacusMentalCalc/reportIndex' })
        } else {
          this.$message.error(res.errMessage)
        }
      })
    },
    addTemplateOpen(template) {
      let item = {
        "content": "",
        "planning": "",
        "scopeMax": undefined,
        "scopeMin": undefined,
        "subjectId": template.id,
        "subjectName": template.name
      }
      template.dtos.push(item)
    },
    deleteTemplate(templateList, index) {
      this.$confirm('您确定要删除此条数据？', '提示').then(() => {
        templateList.splice(index, 1)
      })
    },
  }
}
</script>

<style scoped lang="scss">
.Aform {
  padding: 10px 50px;
}

.subjectCard {
  display: flex;
  flex-direction: column;
  box-shadow: 0 2px 4px rgba(0, 0, 0, .12), 0 0 6px rgba(0, 0, 0, .04);
  padding: 10px;
  margin-bottom: 20px;

  .templateCard {
    margin: 10px 0;
    font-size: 20px;
  }
}
</style>
