/**
 * 分账订单相关接口
 */
import request from '@/utils/request'

export default {
  // 分页查询
  memberOrder(pageNum, pageSize, data) {
    return request({
      url: '/znyy/order/split/bill/page/' + pageNum + '/' + pageSize,
      method: 'GET',
      params: data
    })
  },
  // 导出
  orderExport(listQuery) {
    return request({
      url: '/znyy/order/split/bill/to/excel/',
      method: 'GET',
      responseType: 'blob',
      params:listQuery
    })
  },
}
