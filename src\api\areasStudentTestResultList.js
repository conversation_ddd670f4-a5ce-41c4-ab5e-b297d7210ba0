/**
 * 学员管理相关接口
 */
import request from '@/utils/request'

export default {
  // 分页查询
  testResultList(pageNum, pageSize, data) {
    return request({
      url: '/znyy/areas/student/test/result/page/' + pageNum + '/' + pageSize,
      method: 'GET',
      params: data
    })
  },

  getStudyRank() {
    return request({
      url: '/znyy/areas/student/test/result/study/rank',
      method: 'GET',
    })
  },



}
