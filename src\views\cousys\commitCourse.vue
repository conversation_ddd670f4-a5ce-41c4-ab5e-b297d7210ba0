<template>
  <div class="app-container">
    <el-row>
      <el-col class="paike" style="font-size: 30px" :span="8" :xs="24">
        <span>排课中心</span>
      </el-col>
    </el-row>

    <!--步奏 -->

    <el-steps :space="200" :active="activeIndex" finish-status="success" :align-center="true" class="width:600px">
      <el-step title="完善基础信息"></el-step>
      <el-step title="付款"></el-step>
      <el-step title="完善详细信息"></el-step>
      <el-step title="预排课"></el-step>
    </el-steps>

    <!--    基础信息-->
    <el-form :ref="baseInfo" label-position="left" label-width="96px">
      <el-form-item label="id" prop="id" v-show="false">
        <el-col :xs="24" :span="12">
          <el-input v-model="baseInfo.id"></el-input>
        </el-col>
      </el-form-item>

      <el-form-item>
        <el-col :xs="24" :span="12" style="font-size: 22px">
          基本信息
        </el-col>
      </el-form-item>

      <el-form-item>
        <el-col :xs="24" :span="4">
          家长手机号：{{ baseInfo.parentPhoneNumber }}
        </el-col>
        <el-col :xs="24" :span="4">
          家长姓名：{{ baseInfo.parentName }}
        </el-col>
        <el-col :xs="24" :span="4">
          家长性别：{{ baseInfo.parentGender === '1' ? '男' : '女' }}
        </el-col>
        <el-col :xs="24" :span="4">
          教练 性别：{{ baseInfo.tutorGender === '1' ? '男' : '女' }}
        </el-col>
        <el-col :xs="24" :span="4">
          性别优先：{{ baseInfo.genderPriority === 1 ? '是' : '否' }}
        </el-col>
      </el-form-item>

      <el-form-item>
        <el-col :xs="24" :span="4">
          学员姓名：{{ baseInfo.studentName }}
        </el-col>
        <el-col :xs="24" :span="4">
          学员性别：{{ baseInfo.studentGender }}
        </el-col>
        <el-col :xs="24" :span="4">
          学员年级：{{ baseInfo.grade }}
        </el-col>
        <el-col :xs="24" :span="4">
          选择学时：{{ baseInfo.totalCourse }}
        </el-col>
        <el-col :xs="24" :span="4">
          家庭住址：{{ baseInfo.homeAddress }}
        </el-col>
      </el-form-item>
    </el-form>

    <!--    选择排课版本-->
    <el-form>
      <el-row>
        <el-col>
          <el-form-item label="选择排课版本" prop="courseType">
            <el-row>
              <el-col :span="24">
                <el-col :span="10">
                  <el-radio v-model="queryTutorInfo.courseType" label="A" @change="handleChange()">A版本</el-radio>
                </el-col>
                <el-col :span="10">
                  <el-radio v-model="queryTutorInfo.courseType" label="B" @change="handleChange()">B版本</el-radio>
                </el-col>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="10">
                <el-col v-for="item in typeAList" :key="item" class="typeClass">
                  {{ item }}
                </el-col>
              </el-col>
              <el-col :span="10">
                <el-col v-for="item in typeBList" :key="item" class="typeClass">
                  {{ item }}
                </el-col>
              </el-col>
            </el-row>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <!--    课程表-->
    <el-form label-position="left">
      <el-form-item>
        <el-col class="border1">
          <el-col :span="3" v-for="item in courseTypePlanVoList" :key="item.dateName" class="border_user border1_right">
            <el-col class="border1_bottom">{{ item.dateName }}</el-col>

            <el-col v-for="itemson in item.courseTypePlanDetailVoList" :key="itemson.time" @click.native="handleClick(itemson)"
              class="border1_bottom" :class="itemson.style1===0?'':itemson.style1===1?'user_gray':'user_green'">
              {{itemson.time}}
            </el-col>

          </el-col>
        </el-col>

        <!-- 分页 -->
        <el-col :span="20" style="overflow-x: auto;" :xs="24">
          <el-pagination style="float: right" :current-page="tablePage.currentPage" :page-size="tablePage.size"
            layout="total, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
            @current-change="handleCurrentChange" />
        </el-col>
      </el-form-item>
    </el-form>

    <div style="text-align: center">
      <el-button type="primary" @click="submitForm">完 成</el-button>
    </div>
  </div>
</template>

<script>

import cousysApi from '@/api/cousys'
import { pageParamNames } from "@/utils/constants";
export default {
  data() {
    return {
      activeIndex: 4,
      tableLoading: false,
      baseInfo: {},
      typeAList: {},
      typeBList: {},
      courseTypePlanVoList: [],//课表list
      tutorMonthRestVoList: [],//教练 没空list  [date: "2021-08-30",restList:[1,2,4]]
      courseTypePlanList: {
        courseTypePlanVoList: []
      },
      showList: [],
      tutorPlan: {},
      queryTutorInfo: {
        courseType: 'A',
        tutorGender: '',
        genderPriority: '',
        courseDate: '',
        jobTime: '',
        longitude: '',
        latitude: '',
        tutorCount: 1
      },
      queryTutorPlan: {
        courseType: '',
        tutorId: '',
        startDate: '',
        endDate: ''
      },
      submitList: [],
      submitData: {
        courseOrderId: 1,
        courseType: 1,
        tutorId: 1,
        tutorName: 1,
        studentCode: 1,
        courseDateDtoList: []
      },
      // 分页
      tablePage: {
        currentPage: 1,
        size: 1,
        totalPage: null,
        totalItems: null
      },
      flag: true,//是否第一次请求
      itemson: null,
      courseNum: null
    }
  },
  created() {
    this.getList()
    this.getPlanByCourseType()
  },
  methods: {
    //提交
    submitForm() {
      if (this.submitList.length === 0) {
        this.$message.error('请选择！');
        return;
      }
      this.submitData.courseDateDtoList = this.submitList;
      cousysApi.saveCourse(this.submitData).then(res => {
        this.$message.success("提交成功！")
      })
    },

    //获取列表
    getList() {
      const that = this;
      that.tableLoading = true;
      let courseOrderId = window.localStorage.getItem('courseOrderId');
      cousysApi.findCousysInfo(courseOrderId).then(res => {
        that.baseInfo = res.data
        this.submitData.courseOrderId = courseOrderId;
        this.submitData.studentCode = this.baseInfo.studentCode;
        this.courseNum = Math.ceil(this.baseInfo.totalCourse / 3);
        that.tableLoading = false
      });
      cousysApi.findCourseTypeTimeList().then(res => {
        that.typeAList = res.data.typeAList;
        that.typeBList = res.data.typeBList;
        that.tableLoading = false
      })
    },
    //A,B切换
    handleChange() {
      this.flag = true;
      this.submitList = []
      this.getPlanByCourseType();
    },
    //课程表颜色
    courseColor() {
      this.courseTypePlanVoList.forEach(item => {
        item.courseTypePlanDetailVoList.forEach(i => {
          this.$set(i, 'style1', 0)
        });
        if (item.status === 1) {
          item.courseTypePlanDetailVoList.forEach(i => {
            this.$set(i, 'style1', 1)
          })
        }
      });
    },
    //教练 没空颜色
    courseAndTutorColor() {
      this.courseTypePlanVoList.forEach(oldItem => {
        this.tutorMonthRestVoList.forEach(newItem => {
          for (let i = 0; i < newItem.restList.length; i++) {
            if (newItem.date === oldItem.date) {
              for (let j = 0; j < oldItem.courseTypePlanDetailVoList.length; j++) {
                for (let k = 0; k < newItem.restList.length; k++) {
                  if (oldItem.courseTypePlanDetailVoList[j].jobTime === newItem.restList[k]) {
                    oldItem.courseTypePlanDetailVoList[j].style1 = 1
                    this.$set(oldItem, 'style1', "1");
                  }
                }
              }
            }
          }
        })
      });
    },
    //已选颜色
    selectedColor() {
      this.courseTypePlanVoList.forEach(oldItem => {
        this.submitList.forEach(i => {
          if (oldItem.date === i.courseDate) {
            for (let j = 0; j < oldItem.courseTypePlanDetailVoList.length; j++) {
              for (let k = 0; k < i.jobTimeList.length; k++) {
                if (oldItem.courseTypePlanDetailVoList[j].jobTime === i.jobTimeList[k]) {
                  oldItem.courseTypePlanDetailVoList[j].style1 = 2;
                  this.$set(oldItem, 'style1', "2");
                }
              }
            }
          }
        })

      })
    },


    //根据查询条件获取课表
    getPlanByCourseType() {
      cousysApi.getPlanByCourseType(this.queryTutorInfo.courseType, this.tablePage.currentPage).then(res => {
        console.log("-----", res.data);
        this.courseTypePlanList = res.data;
        this.courseTypePlanVoList = res.data.courseTypePlanVoList;
        this.queryTutorPlan.startDate = res.data.startDate
        this.queryTutorPlan.endDate = res.data.endDate
        this.courseColor();
        this.selectedColor();
        if (!this.flag) {
          this.getTutorPlan();
        }
        // 设置后台返回的分页参数
        this.tablePage.totalItems = res.data.totalPage;
      })
    },
    //按钮点击，第一次匹配教练 
    handleClick(itemson) {
      if (itemson.style1 === 0) {
        //判断已选个数
        var num = 1;
        this.submitList.forEach(item => {
          num += item.jobTimeList.length;
        });
        if (num > this.courseNum) {
          this.$message.error("最多" + this.courseNum + "个");
          return;
        }
      }
      this.itemson = itemson;
      if (itemson.style1 !== 1) {
        if (this.flag) {
          const that = this;
          that.queryTutorInfo.tutorGender = that.baseInfo.tutorGender
          // that.queryTutorInfo.tutorGender = 1
          that.queryTutorInfo.genderPriority = that.baseInfo.genderPriority
          // that.queryTutorInfo.genderPriority = 0
          that.queryTutorInfo.courseDate = itemson.courseDate
          that.queryTutorInfo.jobTime = itemson.jobTime
          that.queryTutorInfo.longitude = that.baseInfo.longitude
          // that.queryTutorInfo.longitude = "117.116405"
          that.queryTutorInfo.latitude = that.baseInfo.latitude
          // that.queryTutorInfo.latitude = "31.835833"
          this.matchTutor();
        }
        if (!this.flag) {
          this.formatData(itemson);
        }
      }
    },
    //匹配教练 
    matchTutor() {
      cousysApi.matchTutor(this.queryTutorInfo).then(res => {
        //获取教练 行程安排
        if (res.data != null && res.data.length > 0) {
          this.submitData.tutorId = res.data[0].tutorId;
          this.submitData.tutorName = res.data[0].tutorName;
          this.submitData.courseType = this.queryTutorInfo.courseType;
          this.queryTutorPlan.tutorId = res.data[0].tutorId
          this.queryTutorPlan.courseType = this.queryTutorInfo.courseType
          this.queryTutorPlan.startDate = this.courseTypePlanList.startDate
          this.queryTutorPlan.endDate = this.courseTypePlanList.endDate
          this.getTutorPlan();
        }
        this.tableLoading = false
        this.flag = false;
        this.formatData(this.itemson);
      }).catch(err => {

      })

    },

    //获取教练 行程安排
    getTutorPlan() {
      cousysApi.getTutorPlan(this.queryTutorPlan).then(res => {
        this.tutorPlan = res.data;
        console.log("tutorPlan", this.tutorPlan);
        this.tutorMonthRestVoList = res.data.tutorMonthRestVoList;
        this.courseAndTutorColor();
      })
    },


    formatData(itemson) {
      var data = {
        courseDate: null,
        jobTimeList: []
      };
      if (itemson.style1 === 0) {
        data.courseDate = itemson.courseDate;
        let oldJobTimeList = this.submitList.filter((item, i) => {
          return item.courseDate === itemson.courseDate;
        });
        if (oldJobTimeList.length === 0) {
          data.jobTimeList.push(itemson.jobTime);
          this.submitList.push(data);
        } else {
          let jobTimeList = oldJobTimeList[0].jobTimeList;
          jobTimeList.push(itemson.jobTime);
        }
      }
      if (itemson.style1 === 2) {
        let oldJobTimeList = this.submitList.filter((item, i) => {
          return item.courseDate === itemson.courseDate;
        });
        let jobTimeList = oldJobTimeList[0].jobTimeList;
        jobTimeList.some((item, i) => {
          if (item === itemson.jobTime) {
            jobTimeList.splice(i, 1);
            return true;
          }
        });
        if (jobTimeList.length === 0) {
          this.submitList.some((item, i) => {
            if (item.courseDate === itemson.courseDate) {
              this.submitList.splice(i, 1);
              return true;
            }
          });
        }
      }
      itemson.style1 = itemson.style1 === 0 ? 2 : itemson.style1 === 2 ? 0 : 1;
    },

    //修改颜色状态
    changeStyle() {

    },

    //重置按钮
    resetQuery() {
      this.getList()
    },
    reset() {
      this.baseInfo = {}
    },
    cancel() {
      this.reset()
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val
      this.getPlanByCourseType()
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val
      this.getPlanByCourseType();
    }
  }
  // beforeDestroy() {
  //   window.localStorage.setItem('courseOrderId', '')
  //   window.localStorage.setItem('cousysMemberCode', '')
  //   window.localStorage.setItem('cousysStudentCode', '')
  // }
}
</script>

<style>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.paike {
  padding-bottom: 50px;
  padding-top: 20px;
}

.studentClass {
  padding-left: 20px;
  padding-top: 20px;
  padding-bottom: 20px;
}

.peopleClass {
  padding-top: 20px;
  padding-bottom: 20px;
}

.typeClass {
  width: 93px;
  height: 34px;
  background: #89b7b1;
  margin-right: 20px;
  color: #297c71;
  font-size: 13px;
  text-align: center;
  line-height: 34px;
}

.courseTypeClass {
  width: 200px;
  height: 100px;
  background: #89b7b1;
  color: #297c71;
  font-size: 13px;
  text-align: center;
  line-height: 34px;
}

.user_gray {
  background: gray;
  color: #ffffff;
}

.user_default {
  background: gray;
  color: #ffffff;
}

.user_green {
  background: #297c71;
  color: #ffffff;
}

.border_user {
  text-align: center;
}

.border1 {
  border-left: 1px solid #333333;
}

.border1_bottom {
  border-bottom: 1px solid #333333;
}

.border1_right {
  border: 1px solid #333333;
  border-left: none;
  border-bottom: none;
}
</style>
