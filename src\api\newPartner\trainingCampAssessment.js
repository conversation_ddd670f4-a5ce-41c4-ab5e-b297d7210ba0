import request from '@/utils/request';

export default {
  /**
   * 培训考核列表查询
   */
  trainingCampAssessmentList(data) {
    return request({
      url: '/znyy/train/camp/trainingCampPage',
      method: 'POST',
      data
    });
  },

  /**
   * 培训考核分数详情
   */
  trainDetailList(data) {
    return request({
      url: '/znyy/train/camp/trainDetail',
      method: 'GET',
      params: data
    });
  }
  /**
   * 培训考核状态修改
   */,
  updateTrainState(data) {
    return request({
      url: '/znyy/train/camp/updateTrainState',
      method: 'GET',
      params: data
    });
  }
};
