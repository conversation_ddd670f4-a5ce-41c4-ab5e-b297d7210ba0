<template>
  <div class="app-container">
    <el-form :model="dataQuery" ref="queryForm" :inline="true">
      <el-form-item label="所属类型：">
        <el-select v-model="dataQuery.type" placeholder="全部" clearable @change="typeChange">
          <el-option v-for="(item, index) in trainType" :key="index" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="相关题型：">
        <el-select v-model="dataQuery.questionType" placeholder="全部" clearable>
          <el-option v-for="(item, index) in queryQuestionList" :key="index" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" style="marginRight: 5px" @click="getList">查询</el-button>
      </el-form-item>
    </el-form>
    <el-col :span="24" style="margin-bottom: 30px">
      <el-button type="success" style="marginRight: 5px" @click="handleAdd">新增</el-button>
    </el-col>
    <el-table class="common-table" v-loading="tableLoading" :data="tableData" style="width: 100%;margin-bottom: 20px;"
      row-key="id" border default-expand-all>
      <el-table-column prop="id" label="ID"></el-table-column>
      <el-table-column prop="type" label="所属类型" :formatter="typeFormatter"></el-table-column>
      <el-table-column prop="withdrawnBonus" label="操作">
        <template slot-scope="scope">
          <el-button type="success" size="mini" icon="el-icon-edit-outline" @click="handleUpdate(scope.row.id)">编辑
          </el-button>
          <el-button type="danger" size="mini" icon="el-icon-delete" @click="handleDelete(scope.row.id)">删除
          </el-button>
        </template>
      </el-table-column>
      <el-table-column prop="questionType" label="相关题型" :formatter="questionTypeFormatter"></el-table-column>
      <el-table-column label="方法名" prop="title" show-overflow-tooltip></el-table-column>
    </el-table>
    <!-- 分页 -->
    <el-col :span="24" style="margin-bottom: 20px">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>

    <!-- 修改或者新增弹窗 -->
    <el-dialog title="编辑步骤" :visible.sync="open" width="70%" @close="cancel">
      <el-form ref="form" :model="form" :rules="rules" label-width="120px" style="width: 70%;">
        <el-row :span="24">
          <el-col :span="8" :xs="24">
            <el-form-item label="所属类型:" prop="type">
              <el-select v-model="form.type" placeholder="全部" clearable @change="handleChange">
                <el-option v-for="(item, index) in trainType" :key="index" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" :xs="24">
            <el-form-item label="相关题型:" prop="questionType">
              <el-select v-model="form.questionType" placeholder="全部" clearable>
                <el-option v-for="(item, index) in questionTypeFilter" :key="index" :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="方法名：" prop="title">
          <el-input v-model="form.title" />
        </el-form-item>
        <el-form-item label="步骤：" required>
          <el-form-item v-for="(item, index) in form.customInfo" :key="index">
            <el-input type="textarea" :rows="6" style="width: 90%;padding-bottom: 10px" placeholder="每段字数最大限制为140"
              v-model="item.label" maxlength="140" show-word-limit />
            <el-button style="marginLeft:5px" type="danger" size="mini" icon="el-icon-delete"
              @click="itemRemove(index)"></el-button>
          </el-form-item>
        </el-form-item>
        <el-form-item label="示例图：" prop="isRandom">
          <el-radio-group v-model="form.isRandom">
            <el-radio :label="false">无</el-radio>
            <el-radio :label="true">有</el-radio>
          </el-radio-group>
          <my-upload v-if="form.isRandom" @handleSuccess="handleSuccess" @handleRemove="handleRemove" :fullUrl="true"
            :file-list="fileList" :showTip="false" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="success" @click="itemAdd">增加段落</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import questionApi from '@/api/paper/train/question'
import { pageParamNames } from '@/utils/constants'
import { mapGetters, mapState } from 'vuex'
import MyUpload from '@/components/Upload/MyUpload'
export default {
  components: { MyUpload },
  data() {
    return {
      fileList: [],
      queryQuestionList: null,
      questionTypeFilter: null,
      title: '',
      dataQuery: {
        type: null,
        questionType: null,
        isStep: true
      },
      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      open: false,
      tableData: [],
      form: {
        id: null,
        title: '',
        type: '',
        questionType: '',
        customInfo: [{
          label: '', value: ''
        }],
        answer: [],
        isRandom: false,
        expandInfo: '',
        submitFalse:false,
        isStep: true
      },
      // 表单校验
      rules: {
        type: [{ required: true, message: '请选择', trigger: 'blur' }],
        questionType: [{ required: true, message: '请选择', trigger: 'blur' }],
        title: [{ required: true, message: '请输入', trigger: 'blur' }],
        isRandom: [{ required: true, message: '请选择', trigger: 'blur' }]
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    itemAdd() {
      let item = { label: '', value: '' }
      this.form.customInfo.push(item)
    },
    itemRemove(index) {
      if (this.form.customInfo.length > 1) {
        this.form.customInfo.splice(index, 1)
      }
    },
    typeChange() {
      this.dataQuery.questionType = null
      this.queryQuestionList = this.trainQuestionType.filter(
        (data) => data.value.includes(this.dataQuery.type)
      )
    },
    handleChange() {
      this.form.questionType = null
      this.questionTypeFilter = this.trainQuestionType.filter(
        (data) => data.value.includes(this.form.type)
      )
    },
    typeFormatter(row, column, cellValue, index) {
      return this.enumFormat(this.trainType, cellValue)
    },
    questionTypeFormatter(row, column, cellValue, index) {
      return this.enumFormat(this.trainQuestionType, cellValue)
    },
    difficultyFormatter(row, column, cellValue, index) {
      return this.enumFormat(this.difficultyList, cellValue)
    },
    handleUpdate(id) {
      this.reset()
      this.questionTypeFilter = this.trainQuestionType
      questionApi.detail(id).then(res => {
        this.form = res.data
        if (this.form.expandInfo) {
          this.fileList.push({ url: this.form.expandInfo })
        }
        this.questionTypeFilter = this.trainQuestionType.filter(
          (data) => data.value.includes(this.form.type)
        )
        this.open = true
        this.submitFalse = false
      })
    },
    handleDelete(id) {
      this.$confirm('确定要删除吗？', '删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        questionApi.delete(id).then(res => {
          this.$message.success('删除成功！')
          this.getList()
        })
      })
    },
    submitForm() {
      if (this.form.isRandom && this.form.expandInfo === '') {
        this.$message.error('请上传示例图！')
        return false
      }
      this.$refs['form'].validate(valid => {
        if (valid) {
          if(this.submitFalse){
            return
          }
          this.submitFalse = true
          questionApi.saveOrUpdate(this.form).then(response => {
            this.$message.success('提交成功！')
            this.open = false
            this.getList()
          })
        }
      })
    },

    getList() {
      this.loading = true
      this.dataQuery.pageNum = this.tablePage.currentPage
      this.dataQuery.pageSize = this.tablePage.size
      this.dataQuery.categoryType=1
      questionApi.list(this.dataQuery).then(res => {
        this.tableData = res.data.data
        this.loading = false
        // 设置后台返回的分页参数
        pageParamNames.forEach(name =>
          this.$set(this.tablePage, name, parseInt(res.data[name]))
        )
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      if (this.$refs['form']) {
        this.$refs['form'].resetFields()
      }
      this.form = {
        id: null,
        title: '',
        type: '',
        questionType: '',
        customInfo: [{
          label: '', value: '' //label标题，此处无，value步骤
        }],
        isRandom: false,
        expandInfo: '',//示例图地址
        isStep: true
      }
      this.fileList = []
    },

    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.submitFalse = false
    },
    handleSuccess(url) {
      this.form.expandInfo = url
    },
    handleRemove(file) {
      this.form.expandInfo = ''
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val
      this.getList()
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val
      this.getList()
    }
  },
  computed: {
    ...mapGetters('enumItem', ['enumFormat']),
    ...mapState('enumItem', {
      trainType: state => state.train.trainType,
      difficultyList: state => state.train.difficultyList,
      trainQuestionType: state => state.train.trainQuestionType
    })
  }
}
</script>
