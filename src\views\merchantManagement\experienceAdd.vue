<template>
  <div class="app-container" v-loading="jubujiazaiShow">
    <el-row>
      <el-col :xs="24" :lg="18">
        <el-form
          :ref="addOrUpdate ? 'addData' : 'updateData'"
          :rules="rules"
          :model="addOrUpdate ? addData : updateData"
          label-position="right"
          label-width="140px"
          style="width: 100%"
        >
          <el-form-item label="登录账号：" prop="name">
            <el-col :xs="24" :span="18">
              <el-input v-if="addOrUpdate" v-model="addData.name" />
              <el-input v-else v-model="updateData.name" disabled />
            </el-col>
            <el-col v-if="!addOrUpdate" :xs="24" :span="6">
              <el-button
                type="success"
                style="margin-left: 20px"
                @click="openLogin(updateData.name, updateData.id)"
              >修改登录账号</el-button
              >
            </el-col>
          </el-form-item>
          <el-form-item label="体验中心名称：" prop="merchantName">
            <el-col :xs="24" :span="18">
              <el-input v-if="addOrUpdate" v-model="addData.merchantName" />
              <el-input v-else v-model="updateData.merchantName" />
            </el-col>
          </el-form-item>
          <el-form-item label="负责人：" prop="realName">
            <el-col :xs="24" :span="18">
              <el-input v-if="addOrUpdate" v-model="addData.realName" />
              <el-input v-else v-model="updateData.realName" />
            </el-col>
          </el-form-item>
          <el-form-item label="推荐人编号：" prop="marketPartner">
            <el-col :xs="24" :span="18">
              <el-input v-if="addOrUpdate" v-model="addData.marketPartner" />
              <el-input v-if="!addOrUpdate" v-model="updateData.marketPartner" />
            </el-col>
          </el-form-item>
          <el-form-item label="身份证：" prop="idCard">
            <el-col :xs="24" :span="18">
              <el-input v-if="addOrUpdate" v-model="addData.idCard" />
              <el-input v-else v-model="updateData.idCard" />
            </el-col>
          </el-form-item>
          <el-form-item label="提现银行：">
            <el-col :xs="24" :span="18">
              <el-select
                style="width: 100%"
                v-if="addOrUpdate"
                v-model="addData.openBank"
              >
                <el-option
                  v-for="(item, index) in bankType"
                  :key="index"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
              <el-select
                style="width: 100%"
                v-if="!addOrUpdate"
                v-model="updateData.openBank"
              >
                <el-option
                  v-for="(item, index) in bankType"
                  :key="index"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-col>
          </el-form-item>
          <el-form-item label="提现账号：">
            <el-col :xs="24" :span="18">
              <el-input v-if="addOrUpdate" v-model="addData.bankNo" />
              <el-input v-else v-model="updateData.bankNo" />
            </el-col>
          </el-form-item>
          <el-form-item label="是否完款：" prop="paymentIsComplete">
            <template>
              <el-radio v-if="!addOrUpdate" v-model="updateData.paymentIsComplete"  @change="changePaymentIsComplete(updateData.paymentIsComplete)" label="1">完款</el-radio>
              <el-radio v-if="!addOrUpdate" v-model="updateData.paymentIsComplete"  @change="changePaymentIsComplete(updateData.paymentIsComplete)" label="0">未完款</el-radio>
              <el-radio v-if="addOrUpdate" v-model="addData.paymentIsComplete"  @change="changePaymentIsComplete(addData.paymentIsComplete)" label="1">完款</el-radio>
              <el-radio v-if="addOrUpdate" v-model="addData.paymentIsComplete"  @change="changePaymentIsComplete(addData.paymentIsComplete)" label="0">未完款</el-radio>
            </template>
          </el-form-item>
          <el-form-item v-if="showPrepaidMoney" label="上级编号：" prop="refereeCode">
            <el-col :xs="24" :span="18">
              <el-input v-if="addOrUpdate" v-model="addData.refereeCode" />
              <el-input v-else v-model="updateData.refereeCode" />
            </el-col>
          </el-form-item>
          <el-form-item v-if="showPrepaidMoney" label="定金：" prop="prepaidMoney">
            <el-col :xs="24" :span="18">
              <el-input
                v-if="addOrUpdate"
                v-model="addData.prepaidMoney"
                type="number"
                maxlength="10"
                isNumber="true"
                min="1"
              />
              <el-input
                v-else
                v-model="updateData.prepaidMoney"
                type="number"
                maxlength="10"
                isNumber="true"
                min="1"
              />
            </el-col>
          </el-form-item>
          <el-form-item label="开户金额：" prop="openMoney">
            <el-col :xs="24" :span="18">
              <el-input
                v-if="addOrUpdate"
                v-model="addData.openMoney"
                type="number"
                maxlength="10"
                isNumber="true"
                min="1"
              />
              <el-input
                v-else
                v-model="updateData.openMoney"
                type="number"
                maxlength="10"
                isNumber="true"
                min="1"
              />
            </el-col>
          </el-form-item>
          <el-form-item label="合同照片:" prop="contractPhoto">
            <el-col :span="20">
              <el-upload
                ref="clearupload"
                v-loading="uploadLoading"
                list-type="picture-card"
                action=""
                element-loading-text="图片上传中"
                :limit="10"
                :on-exceed="justPictureNum"
                :file-list="!addOrUpdate ? fileDetailList : fileDetailList.name"
                :http-request="uploadDetailHttp"
                :on-preview="handlePictureCardPreview"
                :on-remove="handleRemoveDetail"
              >
                <i class="el-icon-plus" />
              </el-upload>
            </el-col>
            <el-col :xs="24" :span="4">(*支持多张)</el-col>
          </el-form-item>
          <el-form-item label="证件照片:" prop="idCardPhoto">
            <el-col :span="20">
              <el-upload
                ref="clearupload"
                v-loading="uploadLoading2"
                list-type="picture-card"
                action=""
                element-loading-text="图片上传中"
                :limit="10"
                :on-exceed="justPictureNum"
                :file-list="!addOrUpdate ? fileDetailList2 : fileDetailList2.name"
                :http-request="uploadDetailHttp2"
                :on-preview="handlePictureCardPreview"
                :on-remove="handleRemoveDetail2"
              >
                <i class="el-icon-plus" />
              </el-upload>
            </el-col>
            <el-col :xs="24" :span="4">(*支持多张)</el-col>
          </el-form-item>
          <el-form-item ref="file_Rule" label="付款记录：" prop="paymentPhoto">
            <el-col :xs="24" :span="20">
              <el-upload
                ref="clearupload"
                v-loading="uploadLoading4"
                multiple
                list-type="picture-card"
                action=""
                element-loading-text="图片上传中"
                :limit="8"
                :on-exceed="justPictureNum"
                :file-list="!addOrUpdate ? fileDetailList4 : fileDetailList4.name"
                :http-request="uploadDetailHttp4"
                :on-preview="handlePictureCardPreview"
                :on-remove="handleRemoveDetail4"
              >
                <i class="el-icon-plus"/>
              </el-upload>
            </el-col>
            <el-col :xs="24" :span="4">(*支持多张)</el-col>
          </el-form-item>
          <el-form-item label="商户类型：" prop="merchantType" >
            <template>
              <el-radio v-model="radio" v-if="addOrUpdate" label="3" @change="change(radio)"
              >个人</el-radio
              >
              <el-radio v-model="radio" v-if="addOrUpdate" label="2" @change="change(radio)"
              >企业</el-radio
              >
              <el-radio v-model="radio" v-if="!addOrUpdate" label="3" @change="change(radio)"
              >个人</el-radio
              >
              <el-radio v-model="radio" v-if="!addOrUpdate" label="2" @change="change(radio)"
              >企业</el-radio
              >
            </template>

          </el-form-item>

          <el-form-item label="签约时间：" prop="signupDate">
            <el-col :xs="24" :span="18">
              <el-date-picker
                v-if="addOrUpdate"
                v-model="addData.signupDate"
                value-format="yyyy-MM-dd HH:mm:ss"
                type="datetime"
                placeholder="选择日期"
              ></el-date-picker>
              <el-date-picker
                v-else
                v-model="updateData.signupDate"
                value-format="yyyy-MM-dd HH:mm:ss"
                type="datetime"
                placeholder="选择日期"
              ></el-date-picker>
            </el-col>
          </el-form-item>
          <el-form-item label="到期时间：" prop="expireDate">
            <el-col :xs="24" :span="18">
              <el-date-picker
                v-if="addOrUpdate"
                v-model="addData.expireDate"
                value-format="yyyy-MM-dd HH:mm:ss"
                type="datetime"
                placeholder="选择日期"
              ></el-date-picker>
              <el-date-picker
                v-else
                v-model="updateData.expireDate"
                value-format="yyyy-MM-dd HH:mm:ss"
                type="datetime"
                placeholder="选择日期"
              ></el-date-picker>
            </el-col>
          </el-form-item>
          <el-form-item label="所在地区：" prop="province">
            <el-col :xs="24" :span="18">
              <el-row :gutter="10">
                <el-col :xs="24" :span="8">
                  <el-input
                    placeholder="安徽省"
                    disabled
                    v-if="addOrUpdate"
                    v-model="addData.province"
                  />
                  <el-input v-else placeholder="安徽省" disabled v-model="updateData.province"  />
                </el-col>
                <el-col :xs="24" :span="8">
                  <el-input
                    placeholder="合肥市"
                    disabled
                    v-if="addOrUpdate"
                    v-model="addData.city"
                  />
                  <el-input v-else  placeholder="合肥市" disabled v-model="updateData.city"  />
                </el-col>
                <el-col :xs="24" :span="8">
                  <el-input
                    placeholder="包河区"
                    disabled
                    v-if="addOrUpdate"
                    v-model="addData.area"
                  />
                  <el-input v-else placeholder="包河区" disabled v-model="updateData.area"  />
                </el-col>
              </el-row>
            </el-col>
          </el-form-item>
          <el-form-item label="地址：" prop="address">
            <el-col :xs="24" :span="18">
              <el-input v-if="addOrUpdate" v-model="addData.address" />
              <el-input v-else v-model="updateData.address" />
            </el-col>
          </el-form-item>

          <el-form-item label="地图标记：" prop="isEnable">
            <el-col :span="24">
              <div class="amap-page-container">
                <div
                  :style="{ width: '100%', height: '450px' }"
                  class="map-box"
                >
                  <el-amap-search-box
                    class="search-box"
                    :search-option="searchOption"
                    :on-search-result="onSearchResult"
                  ></el-amap-search-box>
                  <el-amap
                    vid="amap"
                    :plugin="plugin"
                    :center="center"
                    class="amap-demo"
                    :events="events"
                  >
                    <el-amap-circle
                      v-for="(circle, index) in circles"
                      :key="index"
                      :center="circle.center"
                      :radius="circle.radius"
                      :fill-opacity="circle.fillOpacity"
                      :events="circle.events"
                    ></el-amap-circle>
                    <!-- 定位点标注 -->
                    <el-amap-marker
                      vid="component-marker"
                      :position="center"
                    ></el-amap-marker>
                    <!-- 搜索结果标注 -->
                    <el-amap-marker
                      v-for="(marker,index) in markers2"
                      :key='index'
                      :position="marker.position"
                      :events="marker.events"
                    ></el-amap-marker>
                    <el-amap-info-window
                      v-if="window"
                      :position="window.position"
                      :visible="window.visible"
                      :content="window.content"
                    ></el-amap-info-window>
                  </el-amap>
                  <!-- 搜索结果右侧列表 -->
                  <div class="result" v-if="result != ''">
                    <el-table
                      class="search-table"
                      :data="
                        result.slice(
                          (this.pageNum - 1) * this.pageSize,
                          this.pageNum * this.pageSize
                        )
                      "
                      style="margin-bottom: 20px"
                    >
                      <el-table-column prop="name,address">
                        <template slot-scope="scope">
                          <div
                            class="result-list"
                            @click="
                              markList(
                                scope.row.lng,
                                scope.row.lat,
                                scope.$index
                              )
                            "
                            :class="[
                              currentResult == scope.$index ? 'active' : '',
                            ]"
                          >
                            <label>{{ scope.$index + 1 }}</label>
                            <div class="list-right">
                              <div class="name">{{ scope.row.name }}</div>
                              <div class="address">{{ scope.row.address }}</div>
                            </div>
                          </div>
                        </template>
                      </el-table-column>
                    </el-table>
                    <el-pagination
                      background
                      :current-page.sync="pageNum"
                      :page-size="pageSize"
                      layout="total,prev, pager, next"
                      :total="result.length"
                      @current-change="handleCurrentChange"
                      @size-change="changeSizeHandler"
                      size="small"
                      style="text-align: right"
                    >
                    </el-pagination>
                  </div>
                </div>
                <!-- <div class="toolbar">
              <span v-if="loaded">
                location: lng = {{ lng }} lat = {{ lat }}
              </span>
              <span v-else>正在定位</span>
            </div>
            <div v-on:click="req_post()">查询周边</div> -->
              </div>
            </el-col>
          </el-form-item>

          <el-form-item label="体验中心简介：" prop="description">
            <el-col :xs="24" :span="18">
              <el-input
                v-if="addOrUpdate"
                v-model="addData.description"
                type="textarea"
                resize="none"
                :rows="4"
              />
              <el-input
                v-else
                v-model="updateData.description"
                type="textarea"
                resize="none"
                :rows="4"
              />
            </el-col>
          </el-form-item>
        </el-form>
      </el-col>
    </el-row>

    <div slot="footer" class="dialog-footer" style="margin: 30px 0 30px 140px">
      <el-button
        v-if="addOrUpdate"
        type="primary"
        @click="addActiveFun('addData')"
      >新增</el-button
      >
      <!--      v-if="!addOrUpdate&&checkPermission(['b:merchant:divisionAdd:update'])"-->
      <!--审核状态和启用状态-->
      <el-button
        v-if="(updateData.isCheck == 0 ||updateData.isCheck == 2)&& updateData.isEnable == 0 && !isReSubmit"
        type="primary"
        @click="updateActiveFun('updateData')"
      >修改</el-button
      >
      <el-button
        v-if="isReSubmit"
        type="primary"
        @click="updateActiveFun('updateData')"
      >重提</el-button
      >
      <el-button @click="close()">关闭</el-button>
    </div>

    <el-dialog
      :visible.sync="dialogUploadVisible"
      :close-on-click-modal="false"
    >
      <img width="100%" :src="dialogImageUrl" alt />
    </el-dialog>

    <!-- 修改登陆账号弹窗 -->
    <el-dialog
      title="修改登录账号"
      :visible.sync="showLoginName"
      width="30%"
      :close-on-click-modal="false"
      @close="close"
    >
      <el-form
        :ref="'updateLoginName'"
        :rules="rulesLoginName"
        :model="updateLoginName"
        label-position="left"
        label-width="120px"
        style="width: 80%"
      >
        <el-form-item label="原登录账号：" prop="oldName">
          <span>{{ updateLoginName.oldName }}</span>
        </el-form-item>
        <el-form-item label="id:" prop="id" v-show="false">
          <el-input v-model="updateLoginName.id" />
        </el-form-item>
        <el-form-item label="新登录账号：" prop="name">
          <el-input v-model="updateLoginName.name" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button
          size="mini"
          type="primary"
          @click="updateDealerLoginName('updateLoginName')"
        >确定</el-button
        >
        <el-button size="mini" @click="closeLoginname">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import agentApi from "@/api/agentAddList";
import schoolApi from "@/api/areasSchoolList";
import { ossPrClient } from "@/api/alibaba";
import marketApi from "@/api/marketList";
import divisionAddApi from "@/api/divisionAdd";
import experienceApi from "@/api/experience";
import VueAMap from "vue-amap";
import { isvalidPhone, idCard } from "@/utils/validate";

import ls from "@/api/sessionStorage";
import checkPermission from '@/utils/permission'
import flowOnlineOperationApi from '@/api/activiti/flowOnlineOperation'
export default {
  data() {
    //手机号验证
    var validPhone = (rule, value, callback) => {
      if (!value) {
        callback(new Error("请输入电话号码"));
      } else if (!isvalidPhone(value)) {
        callback(new Error("请输入正确的11位手机号码"));
      } else {
        callback();
      }
    };
    //身份证表达式
    var isIdCard = (rule, value, callback) => {
      if (!value) {
        callback(new Error("请输入身份证号码"));
      } else if (!idCard(value)) {
        callback(new Error("请输入正确的18位身份证号"));
      } else {
        callback();
      }
    };
    const self = this;
    return {
      title: "我的网站",
      url: "/",
      icon: "&#xe627;",
      showPrepaidMoney: false,
      // 地图搜索分页相关参数
      pageNum: 1,
      pageSize: 5,
      result: [],
      currentResult: -1,
      showLoginName: false,
      updateLoginName: {}, //修改账号
      rulesLoginName: {
        name: [
          {
            required: true,
            message: "必填",
            trigger: "change",
          },
          {
            validator: validPhone,
            trigger: "blur",
          },
        ],
      },

      loginAccount: "", //新登陆账号
      lng: 0,
      lat: 0, // 经纬度
      addOrUpdate: false,
      newName: "", // 新登录账号
      id: "",
      dialogVisible: false,
      showLoginAccount: false,
      radio: "",
      rules: {
        // 表单提交规则
        paymentPhoto: [
          {
            required: true,
            message: '请上传证件照片',
            trigger: 'change'
          }
        ],
        name: [
          {
            required: true,
            message: "请填写登录账号",
            trigger: "blur",
          },
          {
            validator: validPhone,
            trigger: "blur",
          },
        ],
        merchantName: [
          {
            required: true,
            message: "请填写体验中心名称",
            trigger: "blur",
          },
        ],
        realName: [
          {
            required: true,
            message: "请填写负责人",
            trigger: "blur",
          },
        ],
        idCard: [
          {
            required: true,
            message: "请填写正确的身份证号",
            pattern: /(^\d{8}(0\d|10|11|12)([0-2]\d|30|31)\d{3}$)|(^\d{6}(18|19|20)\d{2}(0\d|10|11|12)([0-2]\d|30|31)\d{3}(\d|X|x)$)/,
            trigger: "blur",
          },
          ,{ validator: isIdCard, trigger: "blur" }
        ],
        description: [
          {
            required: true,
            message: "请填写简介",
            trigger: "blur",
          },
        ],
        openMoney: [
          {
            required: true,
            message: "请填写开户金额",
            trigger: "blur",
          },
        ],
        merchantType: [
          {
            required: true,
            message: "请选择商户类型",
            trigger: "change",
          },
        ],
        signupDate: [
          {
            required: true,
            message: "请选择签约时间",
            trigger: "change",
          },
        ],
        expireDate: [
          {
            required: true,
            message: "请选择到期时间",
            trigger: "change",
          },
        ],
        paymentIsComplete:[
          {
            required: true,
            message: "必填",
            trigger: "blur",
          },
        ],
        contractPhoto: [
          {
            required: true,
            message: "请上传合同照片",
            trigger: "change",
          },
        ],
        prepaidMoney: [
          {
            required: true,
            message: "请填写定金",
            trigger: "blur"
          }
        ],
        idCardPhoto: [
          {
            required: true,
            message: "请上传证件照片",
            trigger: "change",
          },
        ],
        address: [
          {
            required: true,
            message: "请填写地址",
            trigger: "blur",
          },
        ],
        province: [
          {
            required: true,
            message: "请填写省",
            trigger: "blur",
          },
        ],

      },
      jubujiazaiShow: false, //局部加载
      // 更新的数据
      updateData: {
      },
      // 新增的数据
      addData: {
        name: "",
        merchantName: "",
        realName: "",
        idCard: "",
        marketPartner: "",
        childMerchantCode: "",
        bankNoName: "",
        bankNo: "",
        openMoney: "",
        merchantType: "",
        signupDate: "",
        expireDate: "",
        contractPhoto: [],
        idCardPhoto: [],
        shopPhoto: [],
        Disabled: true,
        address: "",
        province: "",
        city: "",
        area: "",
        description: "",
        latitude: "",
        longitude: "",
        paymentIsComplete:'1',
        //保存付款记录图片
        paymentPhoto: [],
      },
      roleTag: "",
      isReSubmit: false,
      merchantCode: "",
      bankType: [], // 提现银行
      uploadLoadingIdCard: false,
      fileListPending: [], // 待处理已上传图片信息
      fileList: [], // 上传图片已有图片列表
      //保存付款记录图片
      fileDetailListPending: [], // 待处理已上传图片信息
      fileDetailList: [], // 上传图片已有图片列表，合同照片
      fileDetailList2: [], // 上传图片已有图片列表，证件照片
      fileDetailList3: [], // 上传图片已有图片列表，门店照片
      fileDetailList4: [], // 上传图片已有图片列表，付款记录照片
      uploadLoading4: false,// 付款记录图片加载按钮

      dialogUploadVisible: false,
      dialogImageUrl: "", // 上传图片预览

      uploadLoading: false, // 合同图片加载按钮
      uploadLoading2: false, // 证件图片加载按钮
      uploadLoading3: false, // 门店图片加载按钮
      fullscreenLoading: false, // 保存啥的加载
      // 地图开始
      center: [117.26696, 31.87869],
      zoom: 14,
      loaded: false,
      circles: [
        {
          center: [117.26696, 31.87869],
          radius: 200,
          fillOpacity: 0.5,
          events: {
            click: () => {
              // alert("click");
            },
          },
        },
      ],
      //搜索结果标注
      markers2: [],
      windows: [],
      window: "",
      searchOption: {
        city: "全国",
        citylimit: false, //是否限制城市内搜索
      },
      mapCenter: [121.59996, 31.197646],
      events: {
        click(e) {
          const { lng, lat } = e.lnglat;
          self.lng = lng;
          self.lat = lat;
          self.getMarkAddress(e.lnglat.lng, e.lnglat.lat);
        },
        init: (o) => {
          this.$nextTick(() => {
            // 获取当前城市的城市名
            let geocoder = new AMap.Geocoder({
              radius: 1000,
              extensions: "all",
            });
            geocoder.getAddress(
              [o.Ce.center.lng, o.Ce.center.lat],
              (status, result) => {
                if (status === "complete" && result.info === "OK") {
                  if (result && result.regeocode) {
                    if (self.addOrUpdate) {
                      self.center = [o.Ce.center.lng, o.Ce.center.lat];
                    } else {
                      // self.center = [self.updateData.longitude, self.updateData.latitude]
                    }
                  }
                }
              }
            );
          });
        },
      },

      plugin: [
        {
          enableHighAccuracy: true, // 是否使用高精度定位，默认:true
          timeout: 100, // 超过10秒后停止定位，默认：无穷大
          maximumAge: 0, // 定位结果缓存0毫秒，默认：0
          convert: true, // 自动偏移坐标，偏移后的坐标为高德坐标，默认：true
          showButton: true, // 显示定位按钮，默认：true
          buttonPosition: "RB", // 定位按钮停靠位置，默认：'LB'，左下角
          showMarker: true, // 定位成功后在定位到的位置显示点标记，默认：true
          showCircle: true, // 定位成功后用圆圈表示定位精度范围，默认：true
          panToLocation: true, // 定位成功后将定位到的位置作为地图中心点，默认：true
          zoomToAccuracy: true, // 定位成功后调整地图视野范围使定位位置及精度范围视野内可见，默认：f
          extensions: "all",
          expandZoomRange: true,
          keyboardEnable: true,
          pName: "Geolocation",
          campus: [],
          events: {
            // click(e) {
            click: (e) => {
              // console.log(e);
              alert(e.lnglat);
              const { lng, lat } = e.lnglat;
              self.lng = lng;
              self.lat = lat;
            },
            init(o) {
              // o 是高德地图定位插件实例
              o.getCityInfo((status, result) => {
                if (self.addOrUpdate) {
                  if(result!=null&&result.center!=null&&result.center.length>0){
                    self.lng = result.center[0];
                    self.lat = result.center[1];
                  }else{
                    self.lng = 117.283042;
                    self.lat = 31.86119;
                  }
                  self.center = [result.center[0], result.center[1]];
                } else {
                  self.lng = self.updateData.longitude;
                  self.lat = self.updateData.latitude;
                  if (
                    self.updateData.longitude != "" &&
                    self.updateData.latitude != ""
                  ) {
                    // self.center = [self.updateData.longitude, self.updateData.latitude]
                  }
                }
                self.loaded = true;
                self.$nextTick();
              });
            },
          },
        },
      ],
      // 地图结束
    };
  },

  created() {
    ossPrClient();
    this.getBankType();
    this.getRoleTag();
    //获取本地存储
    this.isReSubmit = this.$route.query.isReSubmit;
    let addOrUpdate = this.$route.query.addOrUpdate;
    if(addOrUpdate != undefined || addOrUpdate != ""){
      this.addOrUpdate = addOrUpdate;
    }else{
      this.addOrUpdate = ls.getItem("addOrUpdate");
    }
    this.id = ls.getItem("divisionId");
    if (!this.addOrUpdate) {
      this.getEcho(this.$route.query.id);
      this.setTitle("体验中心编辑");
    } else {
      this.setTitle("体验中心新增");
    }
    //this.loadingForm();
  },
  onLoad(option) {},
  methods: {
    // 删除上传图片
    handleRemoveDetail4(file, fileList) {
      const that = this
      if (!that.addOrUpdate) {
        that.fileDetailList4 = fileList
      } else {
        for (let a = 0; a < that.fileDetailList4.length; a++) {
          if (that.fileDetailList4[a].substring(7,17) == parseInt(file.uid/1000)){
            that.fileDetailList4.splice(a, 1)
          }
        }
      }
    },
//上传付款照片
    uploadDetailHttp4({ file }) {
      this.uploadLoading4 = true
      const that = this
      const fileName = 'manage/' + Date.parse(new Date())
      that.$nextTick(function() {
        ossPrClient()
          .put(fileName, file)
          .then(({ res, url, name }) => {
            if (res && res.status === 200) {
              console.log(`阿里云OSS上传图片成功回调1`, res, url, name)
              if (!that.addOrUpdate) {
                that.fileDetailList4.push({
                  uid: file.uid,
                  url: url
                })
              } else {
                // 新增上传图片
                that.fileDetailList4.push(name)
                that.updateData.paymentPhoto = name
                that.uploadLoading4 = false
              }
              that.$nextTick(() => {
                that.uploadLoading4 = false
              })
            }
          })
          .catch((err) => {
            that.$message.error('上传图片失败请检查网络或者刷新页面')
            console.log(`阿里云OSS上传图片失败回调`, err)
          })
      })
    },

    changePaymentIsComplete(paymentIsComplete){
      console.log("changePaymentIsComplete",paymentIsComplete)
      if(paymentIsComplete == 0){
        this.showPrepaidMoney =  true;
      }else{
        this.showPrepaidMoney =  false;
      }
    },

    checkPermission,
    // 动态设置标签页标题
    setTitle(title) {
      let i = 0;
      let visitedViews = this.$store.getters.visitedViews;
      visitedViews.forEach((route, index) => {
        if (this.$route.path == route.path) {
          i = index;
        }
      });
      this.$route.meta.title = title;
      visitedViews[i].title = title;
    },

    // 确定
    confirmAddress() {},
    getRoleTag() {
      schoolApi.getCurrentAdmin().then((res) => {
        this.roleTag = res.data.roleTag;
        this.merchantCode = res.data.merchantCode;
      });
    },
    // 清空地址
    clearAddress() {
      const that = this;
      if (that.addOrUpdate) {
        that.addData.address = "";
      } else {
        that.updateData.address = "";
      }
    },
    // 获取提现银行
    getBankType() {
      agentApi.categoryType("BankType").then((res) => {
        this.bankType = res.data;
      });
    },
    // 修改回显
    getEcho(id) {
      const that = this;
      that.fileDetailList = [];
      that.fileDetailList2 = [];
      that.fileDetailList3 = [];
      //付款记录
      that.fileDetailList4 = []

      experienceApi
        .echoDivision(id)
        .then((res) => {
          that.updateData = res.data;
          that.radio = that.updateData.merchantType; // 单选框回显
          that.lat = that.updateData.latitude;
          that.lng = that.updateData.longitude;
          that.center = [that.lng, that.lat];

          //回显付款照片
          if (that.updateData.paymentPhoto) {
            for (let i = 0; i < that.updateData.paymentPhoto.length; i++) {
              that.fileDetailList4.push({
                url: that.aliUrl + that.updateData.paymentPhoto[i]
              })
            }
          } else {
            that.fileDetailList4 = []
          }
          // 回显合同照片
          if (that.updateData.contractPhoto) {
            for (let i = 0; i < that.updateData.contractPhoto.length; i++) {
              that.fileDetailList.push({
                url: that.aliUrl + that.updateData.contractPhoto[i]
              })
            }
          } else {
            that.fileDetailList = []
          }
          // 回显证件照片
          if (that.updateData.idCardPhoto) {
            for (let i = 0; i < that.updateData.idCardPhoto.length; i++) {
              that.fileDetailList2.push({
                url: that.aliUrl + that.updateData.idCardPhoto[i]
              })
            }
          } else {
            that.fileDetailList2 = []
          }
        })
        .catch((err) => {});
    },
    // 状态改变事件
    change(radio) {
      if (radio == "3") {
        this.addData.merchantType = 3;
      } else {
        this.addData.merchantType = 2;
      }
    },
    dateFormat(date,fmt) {
      let ret;
      const opt = {
        "y+": date.getFullYear().toString(),        // 年
        "m+": (date.getMonth() + 1).toString(),     // 月
        "d+": date.getDate().toString(),            // 日
        "h+": date.getHours().toString(),           // 时
        "M+": date.getMinutes().toString(),         // 分
        "s+": date.getSeconds().toString()          // 秒
        // 有其他格式化字符需求可以继续添加，必须转化成字符串
      };
      for (let k in opt) {
        ret = new RegExp("(" + k + ")").exec(fmt);
        if (ret) {
          fmt = fmt.replace(ret[1], (ret[1].length == 1) ? (opt[k]) : (opt[k].padStart(ret[1].length, "0")))
        };
      };
      return fmt;
    },
    // 新增体验中心提交
    addActiveFun(ele) {
      const that = this;
      that.addData.paymentPhoto = that.fileDetailList4
      that.addData.contractPhoto = that.fileDetailList;
      that.addData.idCardPhoto = that.fileDetailList2;
      console.log(that.addData.paymentPhoto)
      if ( that.addData.paymentPhoto.length <= 0) {
        that.$message.error('付款照片不能为空')
        return false
      }
      if (that.fileDetailList.length <= 0) {
        that.$message.error("合同照片不能为空");
        return false;
      }
      if (that.fileDetailList2.length <= 0) {
        that.$message.error("证件照片不能为空");
        return false;
      }
      if (that.addData.city == "" || that.addData.city == null) {
        that.addData.city = that.addData.province;
      }
      that.$refs[ele].validate((valid) => {
        // 表单验证
        if (valid) {
          const loading = this.$loading({
            lock: true,
            text: "新增体验中心",
            spinner: "el-icon-loading",
            background: "rgba(0, 0, 0, 0.7)",
          });
          experienceApi
            .addExperience(that.addData)
            .then((res) => {
              if (res) {
                //需要获取业务id
                loading.close();
                that.addData = {};
                that.fileDeatiList4 = [];
                // 添加体验中心工作流
                flowOnlineOperationApi.startAndTakeUserTask("openExperience2",
                  {
                    "approvalType":"agree",
                    "masterData":{
                    "open_type":"Experience",
                    "relation_id": res.data,
                    "create_time":this.dateFormat(new Date(),'yyyy-mm-dd hh:MM:ss')
                  }}).then((res)=>{
                  that.$router.push({
                    path: "/merchantManagement/experienceList",
                  });
                  that.$message.success("体验中心审批流程开启")
                }).catch((err) => {
                  loading.close();
                });
                  //that.$refs.clearupload4.clearFiles();
                that.$message.success("新增体验中心成功");
              }
            },s=>{
              if(s==='error'){
                loading.close();
              }
            })
            .catch((err) => {
              loading.close();
            });
        } else {
          console.log("error submit!!");
          // loading.close();
          return false;
        }
      });
    },

    // 修改体验中心提交
    updateActiveFun(ele) {
      const that = this;
      that.updateData.merchantType = that.radio; //商户类型回显
      that.updateData.paymentPhoto = [];
      that.updateData.idCardPhoto = [];
      that.updateData.contractPhoto = [];
      for (var i = 0; i < that.fileDetailList4.length; i++) {
        let paymentIndex = that.fileDetailList4[i].url.lastIndexOf('manage')
        that.updateData.paymentPhoto.push(
          that.fileDetailList4[i].url.substring(
            paymentIndex,
            that.fileDetailList4[i].url.length
          )
        )
      }
      for (var i = 0; i < that.fileDetailList2.length; i++) {
        let idCardIndex = that.fileDetailList2[i].url.lastIndexOf('manage')
        that.updateData.idCardPhoto.push(
          that.fileDetailList2[i].url.substring(
            idCardIndex,
            that.fileDetailList2[i].url.length
          )
        )
      }
      for (var i = 0; i < that.fileDetailList.length; i++) {
        let contractIndex = that.fileDetailList[i].url.lastIndexOf('manage')
        that.updateData.contractPhoto.push(
          that.fileDetailList[i].url.substring(
            contractIndex,
            that.fileDetailList[i].url.length
          )
        )
      }
      if(this.isResubmit){
        that.rules.name = [];
      }
      that.$refs[ele].validate((valid) => {
        // 表单验证
        if (valid) {
          const loading = this.$loading({
            lock: true,
            text: "修改体验中心信息提交",
            spinner: "el-icon-loading",
            background: "rgba(0, 0, 0, 0.7)",
          });
          experienceApi
            .addExperience(that.updateData)
            .then((res) => {
              if (res) {
                loading.close();
                that.$message.success("编辑体验中心成功");

                if(this.isReSubmit){
                  // 添加体验中心工作流
                  flowOnlineOperationApi.startAndTakeUserTask("openExperience2",
                    {
                      "approvalType":"agree",
                      "masterData":{
                        "open_type":"Experience",
                        "relation_id": res.data,
                        "create_time":this.dateFormat(new Date(),'yyyy-mm-dd hh:MM:ss')
                      }}).then((res)=>{
                    that.$router.push({
                      path: "/merchantManagement/experienceList",
                    });
                    that.$message.success("体验中心审批流程开启")
                  }).catch((err) => {
                    loading.close();
                  });
                }

                that.close();
                that.$router.push({
                  path: "/merchantManagement/experienceList",
                });
              }
            },s=>{
              if(s==='error'){
                loading.close();
              }
            })
            .catch((err) => {
              // 关闭提示弹框
              loading.close();
            });
        } else {
          console.log("error submit!!");
          // loading.close();
          return false;
        }
      });
    },

    // 删除上传图片
    handleRemoveDetail(file, fileList) {
      const that = this;
      if (!that.addOrUpdate) {
        that.fileDetailList = fileList;
      } else {
        for (let a = 0; a < that.fileDetailListPending.length; a++) {
          that.fileDetailListPending[a].uid === file.uid
            ? that.fileDeatiList.splice(a, 1)
            : "";
        }
      }
    },
    handleRemoveDetail2(file, fileList) {
      const that = this;
      if (!that.addOrUpdate) {
        that.fileDetailList2 = fileList;
      } else {
        for (let a = 0; a < that.fileDetailListPending.length; a++) {
          that.fileDetailListPending[a].uid === file.uid
            ? that.fileDeatiList2.splice(a, 1)
            : "";
        }
      }
    },
    handleRemoveDetail3(file, fileList) {
      const that = this;
      if (!that.addOrUpdate) {
        that.fileDetailList3 = fileList;
      } else {
        for (let a = 0; a < that.fileDetailListPending.length; a++) {
          that.fileDetailListPending[a].uid === file.uid
            ? that.fileDeatiList3.splice(a, 1)
            : "";
        }
      }
    },

    // 上传合同照片
    uploadDetailHttp({ file }) {
      this.uploadLoading = true;
      const that = this;
      const fileName = "manage/" + Date.parse(new Date());
      that.$nextTick(function () {
        ossPrClient()
          .put(fileName, file)
          .then(({ res, url, name }) => {
            if (res && res.status === 200) {
              console.log(`阿里云OSS上传图片成功回调1`, res, url, name);
              if (!that.addOrUpdate) {
                that.fileDetailList.push({
                  uid: file.uid,
                  url: url,
                });
              } else {
                // 新增上传图片
                that.fileDetailList.push(name);
                that.updateData.contractPhoto = name;
                that.uploadLoading = false;
              }
              that.$nextTick(() => {
                that.uploadLoading = false;
              });
            }
          })
          .catch((err) => {
            that.$message.error("上传图片失败请检查网络或者刷新页面");
            console.log(`阿里云OSS上传图片失败回调`, err);
          });
      });
    },
    // 上传证件照片
    uploadDetailHttp2({ file }) {
      this.uploadLoading2 = true;
      const that = this;
      const fileName = "manage/" + Date.parse(new Date());
      that.$nextTick(function () {
        ossPrClient()
          .put(fileName, file)
          .then(({ res, url, name }) => {
            if (res && res.status === 200) {
              console.log(`阿里云OSS上传图片成功回调1`, res, url, name);
              if (!that.addOrUpdate) {
                that.fileDetailList2.push({
                  uid: file.uid,
                  url: url,
                });
              } else {
                // 新增上传图片
                that.fileDetailList2.push(name);
                that.updateData.idCardPhoto = name;
                that.uploadLoading2 = false;
              }
              that.$nextTick(() => {
                that.uploadLoading2 = false;
              });
            }
          })
          .catch((err) => {
            that.$message.error("上传图片失败请检查网络或者刷新页面");
            console.log(`阿里云OSS上传图片失败回调`, err);
          });
      });
    },
    // 上传门店照片
    uploadDetailHttp3({ file }) {
      this.uploadLoading3 = true;
      const that = this;
      const fileName = "manage/" + Date.parse(new Date());
      that.$nextTick(function () {
        ossPrClient()
          .put(fileName, file)
          .then(({ res, url, name }) => {
            if (res && res.status === 200) {
              console.log(`阿里云OSS上传图片成功回调1`, res, url, name);
              if (!that.addOrUpdate) {
                that.fileDetailList3.push({
                  uid: file.uid,
                  url: url,
                });
              } else {
                // 新增上传图片
                that.fileDetailList3.push(name);
                that.updateData.shopPhoto = name;
                that.uploadLoading3 = false;
              }
              that.$nextTick(() => {
                that.uploadLoading3 = false;
              });
            }
          })
          .catch((err) => {
            that.$message.error("上传图片失败请检查网络或者刷新页面");
            console.log(`阿里云OSS上传图片失败回调`, err);
          });
      });
    },
    // 上传图片预览
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url;
      this.dialogUploadVisible = true;
    },

    justPictureNumIdCard(file, fileList) {
      this.$message.warning(`当前限制选择10个文件`);
    },

    // 上传图片数量超限
    justPictureNum(file, fileList) {
      this.$message.warning(`数量不允许超过8个`);
    },
    // 关闭弹窗
    close() {
      const that = this;
      // 关闭当前标签页
      that.$store.dispatch("delVisitedViews", this.$route);
      that.$router.go(-1);

      that.addData = {};
      that.updateData = {};
      that.fileDeatiList = [];
      that.fileDeatiList2 = [];
      that.fileDeatiList3 = [];
    },
    // 打开修改登陆账号
    openLogin(name, id) {
      this.updateLoginName.id = id;
      this.updateLoginName.oldName = name;
      this.showLoginName = true;
    },
    closeLogin() {
      this.showLoginName = false;
    },
    //修改账号
    updateDealerLoginName(ele) {
      const that = this;
      that.$refs[ele].validate((valid) => {
        if (valid) {
          marketApi.markertUpdateLogin(that.updateLoginName).then(() => {
            that.$nextTick(() => that.fetchData());
            that.showLoginName = false;
            that.updateData.name = that.updateLoginName.name;
            that.$message.success("修改登录账号成功");
          });
        }
      });
    },

    // 鼠标滑过
    markerMouse(e) {
      infoWindow.setContent(e.target.content);
      infoWindow.open(map, e.target.getPosition());
    },
    // TODO
    // 鼠标滑过
    markerOut(e) {
      map.clearInfoWindow();
    },
    //修改账号关闭
    closeLoginname() {
      this.showLoginName = false;
    },
    req_post(val) {
    },
    onSearchResult(pois) {
      let latSum = 0;
      let lngSum = 0;
      let markers = [];
      let windows = [];
      let that = this;
      that.result = [];
      if (pois.length > 0) {
        // console.log(pois)
        pois.forEach((poi, index) => {
          const { lng, lat } = poi;
          lngSum += lng;
          latSum += lat;
          markers.push({
            position: [poi.lng, poi.lat],
            events: {
              click() {
                // console.log(poi)
                that.windows.forEach((window) => {
                  window.visible = false;
                });
                that.window = that.windows[index];
                that.$nextTick(() => {
                  that.window.visible = true;
                  that.getMarkAddress(poi.lng, poi.lat);
                });
              },
            },
          });
          // ${ index }<img src="" style="">
          windows.push({
            position: [poi.lng, poi.lat],
            content: `<div class="prompt"><span>${poi.name}</span></div>`,
            visible: false,
          });
          that.result.push(poi);
        });
        const center = {
          lng: lngSum / pois.length,
          lat: latSum / pois.length,
        };
        this.mapCenter = [center.lng, center.lat];
        this.center = [center.lng, center.lat];
        this.markers2 = markers;
        this.windows = windows;
      }
    },
    getMarkAddress(lng, lat) {
      // 这里通过高德 SDK 完成。
      var that = this;
      var geocoder = new AMap.Geocoder();
      geocoder.getAddress([lng, lat], function (status, result) {
        if (status === "complete" && result.info === "OK") {
          if (result && result.regeocode) {
            // console.log(result)
            // that.searchOption.city = result.regeocode.addressComponent.city
            that.center = [lng, lat];
            // 判断是添加还是编辑
            if (that.addOrUpdate) {
              that.addData.longitude = lng;
              that.addData.latitude = lat;
              that.addData.address = result.regeocode.formattedAddress;
              that.addData.province =
                result.regeocode.addressComponent.province;

              var reg = RegExp(/省/);
              if (
                that.addData.province.match(reg) &&
                result.regeocode.addressComponent.city == ""
              ) {
                that.addData.city = result.regeocode.addressComponent.district;
              } else {
                if (
                  result.regeocode.addressComponent.province == "重庆市" ||
                  result.regeocode.addressComponent.province == "天津市" ||
                  result.regeocode.addressComponent.province == "北京市" ||
                  result.regeocode.addressComponent.province == "上海市"
                ) {
                  that.addData.city =
                    result.regeocode.addressComponent.province;
                } else {
                  // 市
                  that.addData.city = result.regeocode.addressComponent.city;
                }
              }
              //that.addData.city = result.regeocode.addressComponent.city;
              that.addData.area = result.regeocode.addressComponent.district;
            } else {
              that.updateData.longitude = lng;
              that.updateData.latitude = lat;
              that.updateData.address = result.regeocode.formattedAddress;
              that.updateData.province =
                result.regeocode.addressComponent.province;

              var reg = RegExp(/省/);
              if (
                that.updateData.province.match(reg) &&
                result.regeocode.addressComponent.city == ""
              ) {
                that.updateData.city =
                  result.regeocode.addressComponent.district;
              } else {
                if (
                  result.regeocode.addressComponent.province == "重庆市" ||
                  result.regeocode.addressComponent.province == "天津市" ||
                  result.regeocode.addressComponent.province == "北京市" ||
                  result.regeocode.addressComponent.province == "上海市"
                ) {
                  that.updateData.city =
                    result.regeocode.addressComponent.province;
                } else {
                  // 市
                  that.updateData.city = result.regeocode.addressComponent.city;
                }
              }
              // that.updateData.city = result.regeocode.addressComponent.city
              that.updateData.area = result.regeocode.addressComponent.district;
            }
            that.$nextTick();
          }
        } else {
          // alert('地址获取失败')
        }
      });
    },
    // 标注列表
    markList(lng, lat, index) {
      if (this.currentResult != index) {
        this.currentResult = index;
      } else {
        this.currentResult = -1;
      }
      this.getMarkAddress(lng, lat, index);
    },
    changeSizeHandler(size) {
      this.pageSize = size;
    },
    handleCurrentChange(currentPage) {
      this.pageNum = currentPage;
    },
  },
};
</script>

<style>
.map-box {
  position: relative;
}
.search-box {
  position: absolute !important;
  top: 10px;
  left: 10px;
}
.prompt {
  padding: 10px;
}
.result {
  position: absolute;
  top: 0;
  left: 100%;
  width: 300px;
  /* height: 450px; */
  margin-left: 10px;
  background-color: #ffffff;
  border: 1px solid silver;
}
.result-list {
  display: flex;
  align-items: center;
  /* margin-bottom: 10px; */
  line-height: 1.6;
  overflow: hidden;
  cursor: pointer;
}
.result label {
  display: block;
  width: 19px;
  height: 33px;
  margin-right: 10px;
  line-height: 20px;
  text-align: center;
  font-size: 12px;
  color: #ffffff;
  background: url("https://webapi.amap.com/theme/v1.3/markers/n/mark_bs.png")
  no-repeat center/cover;
}
.result-list.active label {
  background: url("http://webapi.amap.com/theme/v1.3/markers/n/mark_r.png")
  no-repeat center/cover;
}
.list-right {
  flex: 1;
}
.result .name {
  font-size: 14px;
  color: #565656;
}
.result .address {
  color: #999;
}
.search-table {
  height: 380px;
  margin-bottom: 10px !important;
}
.search-table th {
  display: none;
}
.search-table td {
  padding: 5px 0 !important;
  border-bottom: none;
}
.el-vue-search-box-container {
  width: 90% !important;
}
.el-date-editor.el-input {
  width: 100% !important;
}
@media screen and (max-width: 767px) {
  .result {
    display: none;
  }
}
</style>
