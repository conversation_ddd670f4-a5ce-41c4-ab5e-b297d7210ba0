/**
 * 商圈码相关接口
 */
import request from '@/utils/request'

export default {
  // 分页查询
  pageMallRangList(pageNum, pageSize, data) {
    return request({
      url: '/znyy/active/code/list/' + pageNum + '/' + pageSize,
      method: 'GET',
      params: data
    })
  },
// 市级服务商二维码分页查询
pageList(pageNum, pageSize,data) {
  return request({
    url: '/znyy/active/code/page/list/' + pageNum + '/' + pageSize,
    method: 'GET',
    params: data
  })
},
merchantCodeList(pageNum,pageSize){
  return request({
    url: '/znyy/active/code/merchant/binding/code/' + pageNum + '/' + pageSize,
    method: 'GET'
  })
},
  //生成范围码
  addRangCode(count){
    return request({
      url: '/znyy/active/code/add/rang/' + count,
      method: 'POST'
    })
  },
  //绑定市级服务商
  bindCode(data){
    return request({
      url:'/znyy/active/code/bind/admin/rang',
      method:'POST',
      params :data
    })
  },
    //绑定商户
    bindmerchatnCode(data){
      return request({
        url:'/znyy/active/code/bind/merchan/rang',
        method:'POST',
        params :data
      })
    },
  // 获取订单状态
  orderType(enType){
    return request({
      url: '/znyy/bvstatus/' + enType,
      method: 'GET'
    })
  },
  // 市级服务商导出
  excel() {
    return request({
      url: '/znyy/active/code/page/list/excel',
      method: 'GET',
      responseType: 'blob'
    })
  },
  //excel/list
  excelRangCode(data){
    return request({
      url: '/znyy/active/code/page/list/excel',
      method: 'GET',
      responseType: 'blob',
      params :data
    })
  }

}
