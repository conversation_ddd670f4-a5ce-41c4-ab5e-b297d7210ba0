<template>
  <div class="app-container">
    <!-- 搜索 -->
    <el-form
      ref="dataQuery"
      :inline="true"
      :model="dataQuery"
      class="SearchForm SearchCenter"
      style="padding: 40px 0 30px 0"
    >
      <el-form-item label="阶段:" prop="phase">
        <el-select
          @change="getOptionsphase"
          size="medium"
          v-model="dataQuery.phase"
          filterable
          value-key="value"
          placeholder="请选择"
          clearable
        >
          <el-option
            v-for="(item, index) in phaseTypeList"
            :key="index"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="语法点:" style="margin-left: 40px" prop="grammarId">
        <el-select
          @change="onGrammarPoint"
          size="medium"
          v-model="dataQuery.grammarId"
          filterable
          value-key="value"
          placeholder="请选择"
          clearable
        >
          <el-option
            v-for="(item, index) in grammarTypeList"
            :key="index"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        label="知识点:"
        style="margin-left: 40px"
        prop="knowledgeName"
      >
        <el-input
          @input="onKnowledgePointInput"
          size="medium"
          v-model="dataQuery.knowledgeName"
          @keyup.enter.native="fetchData01()"
          style="width: 200px"
          placeholder="请输入知识点"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item style="margin-left: 60px">
        <el-button
          type="primary"
          icon="el-icon-search"
          size="medium"
          @click="fetchData01()"
          >搜索</el-button
        >
        <el-button
          type="info"
          icon="el-icon-refresh"
          size="medium"
          @click="resetData()"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <div class="SearchForm">
      <!-- 添加 -->
      <div class="btn-add" style="margin-bottom: 20px">
        <el-button
          size="medium"
          type="primary"
          icon="el-icon-plus"
          @click="clickAdd"
          >添加</el-button
        >
      </div>
      <!-- 表格 -->
      <el-table
        class="common-table"
        :data="tableData"
        stripe
        border
        max-height="500"
        :default-sort="{ prop: 'addTime', order: 'descending' }"
        v-loading="tableLoading"
        :empty-text="tableData.length == 0 ? '暂无数据' : ''"
      >
        <el-table-column prop="phase" label="阶段"></el-table-column>
        <el-table-column prop="grammarName" label="语法点"></el-table-column>
        <el-table-column prop="name" label="知识点"></el-table-column>
        <el-table-column prop="sortNum" label="排序"></el-table-column>
        <el-table-column prop="videoTable" label="视频封面">
          <template slot-scope="scope">
            <el-image :src="scope.row.firstImage">
              <div slot="error" class="image-slot">
                <i class="el-icon-picture-outline"></i>
                <span>审核中...</span>
              </div>
            </el-image>
          </template>
        </el-table-column>
        <el-table-column prop="hasMindMap" label="思维导图状态">
          <template slot-scope="scope">
            <i
              class="el-icon-success"
              style="color: green; font-size: 18px"
              v-if="scope.row.hasMindMap == 1"
            ></i>
          </template>
        </el-table-column>
        <el-table-column prop="hasNote" label="讲义状态">
          <template slot-scope="scope">
            <i
              class="el-icon-success"
              style="color: green; font-size: 18px"
              v-if="scope.row.hasNote == 1"
            ></i>
          </template>
        </el-table-column>
        <el-table-column prop="id" label="操作" width="500px">
          <template slot-scope="scope">
            <el-button
              type="success"
              size="medium"
              icon="el-icon-edit-outline"
              @click="openXmindName(scope.row)"
              >思维导图</el-button
            >
            <el-button
              :disabled="!scope.row.hasMindMap"
              type="success"
              size="medium"
              icon="el-icon-edit-outline"
              @click="openhandOutName(scope.row)"
              >讲义</el-button
            >
            <el-button
              type="info"
              size="medium"
              icon="el-icon-edit-outline"
              @click="openEditName(scope.row)"
              >编辑</el-button
            >
            <el-button
              type="danger"
              size="medium"
              icon="el-icon-delete"
              @click="deleteGrammar(scope.row.id)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <el-col :span="20">
      <el-pagination
        :current-page="tablePage.currentPage"
        :page-sizes="[10, 50, 100, 150, 200]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="tablePage.totalItems"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-col>

    <!-- 思维导图弹窗-->
    <el-dialog
      title="新增思维导图"
      :visible.sync="DialogFlag"
      width="900px"
      :close-on-click-modal="false"
      @close="handleClose"
    >
      <xmindVue
        @refreshList="refreshParentList"
        ref="xmindVue"
        :rowId="rowId"
        :row="xmindRow"
        :DialogFlag="DialogFlag"
        :noteJson="noteJson"
        @closeDialog="DialogFlag = !DialogFlag"
        v-if="DialogFlag"
      ></xmindVue>
    </el-dialog>

    <!-- 讲义弹窗 -->
    <el-dialog
      title="添加讲义"
      :visible.sync="dialogNote"
      width="600px"
      :close-on-click-modal="false"
      @close="dialogNote = false"
    >
      <!-- <el-form :model="noteData" :rules="noteRules" ref="noteForm"> -->
      <el-form :model="noteData" ref="noteForm">
        <div style="font-size: 18px; margin-bottom: 15px">{{ noteName }}</div>
        <el-form-item
          prop="content"
          v-for="(child, index) in getChildrenTopics()"
          :key="child.id"
          :label="index + 1 + ('.' + child.topic)"
        >
          <el-input
            type="textarea"
            placeholder="请输入内容"
            v-model="noteData.content[index].content"
            :autosize="{ minRows: 2, maxRows: 10 }"
          >
          </el-input>
        </el-form-item>
      </el-form>
      <el-row style="text-align: right; margin-top: 30px">
        <el-button
          style="margin-top: 12px"
          type="info"
          size="medium"
          @click="cancelClick"
          >取消</el-button
        >
        <el-button
          style="margin-top: 12px"
          type="primary"
          size="medium"
          @click="noteAddClick"
          >确定</el-button
        >
      </el-row>
    </el-dialog>

    <!-- 新增编辑弹窗 -->
    <el-dialog
      :title="title"
      :visible.sync="dialogVisible"
      width="60%"
      :close-on-click-modal="false"
      @close="close"
    >
      <el-form
        ref="dialogForm"
        :model="dataDialog"
        :rules="updateSingle"
        class="dialogKnowledge"
      >
        <el-form-item label="阶段:" label-width="120px" prop="phaseCode">
          <el-select
            @change="getOptions"
            size="medium"
            v-model="dataDialog.phaseCode"
            filterable
            value-key="value"
            placeholder="请选择"
            clearable
          >
            <el-option
              v-for="(item, index) in phaseTypeList"
              :key="index"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="语法点:" label-width="120px" prop="grammarId">
          <el-select
            @change="onGrammarPoint"
            size="medium"
            v-model="dataDialog.grammarId"
            filterable
            value-key="value"
            placeholder="请选择"
            clearable
          >
            <el-option
              v-for="(item, index) in grammarTypeList"
              :key="index"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="知识点:" label-width="120px" prop="knowledgeName">
          <el-input
            maxlength="50"
            show-word-limit
            size="medium"
            v-model="dataDialog.knowledgeName"
            placeholder="请输入内容"
          ></el-input>
        </el-form-item>
        <el-form-item label="排序:" label-width="120px" prop="sortNum">
          <el-input
            size="medium"
            @input="validateSortNumInput"
            v-model="dataDialog.sortNum"
            placeholder="请输入内容"
          ></el-input>
        </el-form-item>
        <el-form-item label="视频上传：" label-width="120px" prop="videoList">
          <uploadVideoFile
            @vidData="vidHandle"
            v-if="dialogVisible"
            @handleRemove="handleRemoveFile"
            @videoSucceed="videoSucceed"
            ref="uploadVideoRef"
            :videoList="dataDialog.videoList"
            @videoRemove="videoRemove"
            @saveButton="saveButtonHandle"
            @firstImage="hanleFirstImage"
            @updatetableData="handleUpdateData"
          >
            <el-input type="hidden" v-model="dataDialog.videoList"></el-input>
          </uploadVideoFile>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="medium" @click="dialogVisible = false">取消</el-button>
        <!-- <el-button size="medium" type="primary" :disabled="diaConfirm" @click="submitClick()">确定</el-button> -->
        <el-button size="medium" type="primary" @click="submitClick()"
          >确定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  queryByTypeAPI,
  optionsAPI,
  pageAPI,
  addOrUpdateKnowAPI,
  deleteAPI,
  detailXmindAPI,
  detailRowAPI,
} from "@/api/grammar/knowledgePoint";
import { pageParamNames } from "@/utils/constants";
import uploadVideoFile from "@/components/uploadVideoFile";
import { detailAPI } from "@/api/uploadVideoFile";
import xmindVue from "./component/xmindVue.vue";
import { addOrUpdateAPI } from "@/api/grammar/xmind";
export default {
  name: "syntaxPoint",
  components: {
    uploadVideoFile,
    xmindVue,
  },
  data() {
    return {
      noteName: "",
      noteData: {
        content: [],
        noteXmind: {},
      },
      labelList: [],
      noteId: "",
      // noteRules: {
      //   content: [
      //     {
      //       required: true,
      //       message: "必填",
      //       trigger: "blur",
      //     },
      //   ],
      // },
      xmindRow: {},
      xNodeRow: {},
      rowId: "",
      noteJson: {},
      CascaderValue: [],
      dialogXmind: false, // 修改弹窗是否展示
      DialogFlag: false,
      lackincidentid: null,
      firstImage: "",
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null,
      },
      tableLoading: false,
      dataQuery: {
        grammarId: "",
        phase: "",
        knowledgeName: "",
      },
      knowledgeFlag: false, // 判断是语法还是知识点搜索
      updateSingle: {
        name: [
          {
            required: true,
            message: "必填",
            trigger: "blur",
          },
        ],
        phaseCode: [
          {
            required: true,
            message: "必填",
            trigger: "blur",
          },
        ],
        sortNum: [
          {
            required: true,
            message: "必填",
            trigger: "blur",
          },
        ],
        grammarId: [
          {
            required: true,
            message: "必填",
            trigger: "blur",
          },
        ],
        knowledgeName: [
          {
            required: true,
            message: "必填",
            trigger: "blur",
          },
        ],
        videoList: [
          {
            required: true,
            message: "必填",
            trigger: "blur",
          },
        ],
      },
      tableData: [], //表格数据
      // 弹窗数据
      dialogVisible: false, // 修改弹窗是否展示
      dataDialog: {
        grammarId: "",
        knowledgeName: "",
        phaseCode: "",
        sortNum: "",
        videoList: [],
        superiorId: "", // 知识点上级 id
      },
      parentId: "", // 父级id 语法点 id
      title: "",
      addOrUpdate: false, // 是新增还是修改
      rows: [], //选中的数据id存放处
      phaseTypeList: [], //课程分类
      grammarTypeList: [], // 语法点分类
      videoVid: "", // 视频 id
      // 讲义
      dialogNote: false, // 修改弹窗是否展示
      grammarId: "",
      phaseCode: "",
      tableDatalength: "",
      dialogEdit: [], // 编辑弹层数据
      isVideoUploaded: false, // 视频是否点击保存上传
    };
  },
  created() {
    // 获取所属分类
    this.getType();
  },
  methods: {
    validateSortNumInput(event) {
      if (!/^\d*$/.test(event)) {
        this.$message.error("请输入有效的数字");
        this.dataDialog.sortNum = ""; // 清空输入框
      }
    },
    vidHandle(vid) {
      this.videoVid = vid;
    },
    getChildrenTopics() {
      const { data } = this.xmindRow;

      if (data && data.children) {
        return data.children;
      }
      return [];
    },
    handleClose() {
      this.dialogVisible = false;
    },
    openXmindName(row) {
      this.rowId = row.id;
      detailXmindAPI({ id: row.id })
        .then((res) => {
          if (res.data.mindMap) {
            this.xmindRow = JSON.parse(res.data.mindMap);
            this.noteData.content = this.xmindRow.data.children;
            let noteDataNote = this.noteData.content;
            this.noteJson = res.data.note;
            this.xNodeRow = noteDataNote;
            this.labelList = [];
            // noteDataNote.forEach(item => {
            //   this.labelList.push({
            //     content: item.topic
            //   });
            // });
            // this.noteData.content = this.labelList;
            this.labelList = this.getChildrenTopics().map((child) => ({
              node: child.topic,
              content: this.labelList.concent,
            }));
          } else {
            this.xmindRow = res.data;
          }
        })
        .then(() => {
          this.DialogFlag = true;
        });
    },
    // 讲义
    openhandOutName(row) {
      this.noteId = row.id;
      detailXmindAPI({ id: row.id })
        .then(async (res) => {
          this.noteName = res.data.name;
          this.xmindRow = JSON.parse(res.data.mindMap);
          if (res.data.note) {
            const dataNote = JSON.parse(res.data.note).noteData;
            if (dataNote.content) {
              this.labelList = [];
              this.labelList = this.getChildrenTopics().map((child) => {
                // 查找对应的 content
                const matchingItem = this.noteData.content.find(
                  (item) => item.node === child.topic
                );
                return {
                  node: child.topic,
                  topic: child.topic,
                  content: matchingItem?.content || "", // 确保内容回显正确
                };
              });
              // console.log(this.labelList, "0000");
              // 创建一个新的数组来存储独立的内容项
              const newContent = dataNote.content.map((item, index) => ({
                node: this.labelList[index].node,
                topic: this.labelList[index].topic,
                content: item.content ? item.content : "",
              }));
              this.noteData.content = newContent;
              this.dialogNote = true;
              // console.log(this.noteData, "111");
            } else {
              this.$message.error("请检查思维导图是否添加了二级节点");
            }
          }
        })
        .catch((err) => {
          console.error("Error fetching data:", err);
        });
    },
    noteAddClick() {
      this.$refs.noteForm.validate((valid) => {
        // if (!valid) {
        //   this.$message.error("请填写所有必填项");
        //   return;
        // } else {
        addOrUpdateAPI({
          knowledgeId: this.noteId,
          noteJson: JSON.stringify({
            noteData: this.noteData,
            // labelList: this.labelList,
          }),
          name: this.noteName,
        }).then((res) => {
          this.dialogNote = false;
          this.fetchData();
        });
        // }
      });
    },
    cancelClick() {
      this.dialogNote = false;
    },
    hanleFirstImage(firstImage) {
      this.firstImage = firstImage;
    },
    // 语法点与知识点的判断
    onKnowledgePointInput() {
      this.dataQuery.knowledgeFlag = true;
    },
    onGrammarPoint() {
      this.dataQuery.knowledgeFlag = false;
    },
    // 获取分类返回类型
    getType() {
      queryByTypeAPI({ dictType: "grammar_phase" }).then((response) => {
        // this.phaseTypeList = response.data;
        this.phaseTypeList = response.data.map((item) => ({
          id: item.dictValue,
          name: item.dictLabel,
        }));
      });
      this.fetchData();
    },
    // 获取所属分类 搜索框
    getOptionsphase() {
      this.dataQuery.grammarId = "";
      optionsAPI({
        knowledgeFlag: this.knowledgeFlag,
        phase: this.dataQuery.phase,
      }).then((response) => {
        this.grammarTypeList = response.data;
      });
    },
    // 获取所属分类
    getOptions() {
      this.dataDialog.grammarId = "";
      optionsAPI({
        knowledgeFlag: this.knowledgeFlag,
        phase: this.dataDialog.phaseCode,
      }).then((response) => {
        this.grammarTypeList = response.data;
      });
    },

    // 查询表格列表
    fetchData() {
      this.tableLoading = true;
      pageAPI(this.dataQuery, this.tablePage)
        .then((res) => {
          this.tableData = res.data.data;
          res.data.data.forEach((item) => {
            const foundPhase = this.phaseTypeList.find(
              (phaseItem) => phaseItem.id === item.phase
            );
            if (foundPhase) {
              item.phase = foundPhase.name;
            }
          });
          pageParamNames
            .filter((i) => i !== "currentPage")
            .forEach((name) =>
              this.$set(this.tablePage, name, parseInt(res.data[name]))
            );
          this.tableLoading = false;

          // 单个遍历每个 videoVid 并请求 detail 接口
          return Promise.all(
            this.tableData.map((item) => {
              const videoVid = item.videoVid;
              return detailAPI(videoVid)
                .then((res) => {
                  item.firstImage = res.data.firstImg;
                })
                .catch((error) => {
                  console.error("请求详情接口失败", error);
                });
            })
          );
        })
        .catch((error) => {
          console.error("数据加载失败", error);
          this.tableLoading = false;
        });
    },
    refreshParentList() {
      this.fetchData();
    },
    // 搜索
    fetchData01() {
      this.tablePage = {
        currentPage: 1,
        size: this.tablePage.size,
        totalPage: null,
        totalItems: null,
      };
      this.fetchData();
    },
    // 处理文件成功
    handleSuccessFile(url, name, info) {
      info.filePath = url;
      this.videoRemove(info, 1);
    },
    // 处理文件删除
    handleRemoveFile(file) {
      if (file.fileId) {
        this.videoRemove(file, 1);
      }
    },
    videoRemove(info, key) {
      if (this.dataDialog.id) {
        let param = {
          courseId: this.dataDialog.id,
          operatorId: this.dataDialog.operatorId,
        };
        if (!key && !info.fileId) {
          return;
        }
        if (key == 2) {
          param.videoList = [
            {
              fileName: info.fileName,
              filePath: info.fileId ? info.filePath : info.id,
            },
          ];
        } else {
          param.fileList = [
            {
              fileName: info.name,
              filePath: info.filePath,
            },
          ];
        }
        if (info.fileId) {
          param.id = info.fileId;
        }
        courseApi.courseAttachment(param).then((res) => {});

        // 更新dataDialog.videoList的值
        this.dataDialog.videoList = this.dataDialog.videoList.filter(
          (item) => item.fileName !== info.fileName
        );
      }
    },
    // 处理视频成功
    videoSucceed(data) {
      this.dataDialog.videoList = [];
      if (data) {
        data.forEach((item) => {
          this.dataDialog.videoList.push({
            fileName: item.fileName,
            filePath: item.filePath ? item.filePath : item.id,
          });
          if (!item.fileId && item.success) {
            this.videoRemove(item, 2);
          }
        });
      }
    },
    saveButtonHandle() {
      // 视频上传成功
      this.isVideoUploaded = true;
    },
    // 重置列表
    resetData() {
      this.dataQuery = {
        grammarId: "",
        phase: "",
        knowledgeName: "",
      };
      this.fetchData();
    },
    // 清空
    //添加操作
    clickAdd() {
      // this.fd = "";
      this.dialogVisible = true;
      this.dataDialog.videoList = [];
      this.grammarTypeList = [];
      this.title = "添加";
      this.addOrUpdate = true;
      this.close();
    },
    // 编辑弹窗
    openEditName(row) {
      this.dataDialog.videoList = [];

      this.addOrUpdate = false;
      detailRowAPI({ id: row.id }).then((res) => {
        // 先设置默认的数据
        this.dataDialog.phaseCode = res.data.phase;
        this.dataDialog.grammarId = res.data.grammarName;
        this.parentId = res.data.parentId;
        this.dataDialog.knowledgeName = res.data.name;
        this.dataDialog.superiorId = res.data.superior;
        this.dataDialog.sortNum = res.data.sortNum;
        this.dataDialog.videoVid = res.data.videoVid;
        this.dataDialog.id = res.data.id;
        this.phaseCode = res.data.phase;
        // 判断是否有思维导图数据
        if (res.data.mindMap) {
          this.xmindRow = JSON.stringify(res.data.mindMap);
        }
        // 思维导图数据与讲义数据进行区分
        if (res.data.noteData) {
          let noteDataNote = JSON.parse(res.data.note).noteData;
          this.labelList = [];
          // noteDataNote.content.forEach(item => {
          //   this.labelList.push({
          //     content: item.content
          //   });
          // });

          this.noteData.content = this.labelList;
          this.labelList = this.getChildrenTopics().map((child) => ({
            node: child.topic,
            content: this.labelList.concent,
          }));
        }
        const videoVid = res.data.videoVid;
        detailAPI(videoVid).then((res) => {
          let videoData = {
            fileName: res.data.title,
            id: res.data.id,
            progress: 100,
          };
          this.dataDialog.videoList.push(videoData);
          this.videoVid = res.data.vid;
          this.title = "编辑";
          this.$refs.uploadVideoRef.isProgressComplete = false;
        });
        optionsAPI({
          knowledgeFlag: this.knowledgeFlag,
          phase: this.dataDialog.phaseCode,
        }).then((response) => {
          this.grammarTypeList = response.data;
        });
        this.dialogVisible = true;
      });
    },

    //删除操作题目
    deleteGrammar(id) {
      this.$confirm(
        "语法点删除后，对应的思维导图和讲义也会全部删除",
        "删除语法点",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(function () {
          return deleteAPI({ id: id });
        })
        .then(() => {
          this.fetchData();
          this.$message.success("删除成功");
        });
    },
    handleUpdateData(length) {
      // 在这里可以获取到子组件传递过来的 tableData.length 的值
      this.tableDatalength = length;
    },
    // 弹窗确定按钮
    submitClick() {
      this.$refs.dialogForm.validate((valid) => {
        if (valid) {
          // 新增
          if (this.addOrUpdate) {
            // 判断视频是否上传成功
            if (!this.isVideoUploaded) {
              this.$message.error("请先上传视频后点击保存再点击确定");
              return false;
            }
            addOrUpdateKnowAPI({
              ...this.dataDialog,
              videoVid: this.videoVid,
            })
              .then((res) => {
                this.$nextTick(() => this.fetchData()); // 刷新数据
                this.$message.success("新增成功");
                this.dialogVisible = false;
                this.close(); // 关闭弹窗
              })
              .catch((error) => {
                console.error("新增失败", error);
              });
          } else {
            // 编辑时判断视频列表是否有变化
            if (this.tableDatalength === 0 && !this.isVideoUploaded) {
              // 如果 `tableDatalength` 为 0，检查视频列表是否为空
              this.$message.error("请先上传视频后点击保存再点击确定");
              return false;
            } else {
              const editParams = {
                grammarId: this.parentId,
                phaseCode: this.dataDialog.phaseCode,
                knowledgeName: this.dataDialog.knowledgeName,
                sortNum: this.dataDialog.sortNum,
                superiorId: this.dataDialog.superiorId,
                mindMap: this.xmindRow,
                note: JSON.stringify(this.noteData),
                id: this.dataDialog.id,
                videoList: this.dataDialog.videoList,
              };
              addOrUpdateKnowAPI({ ...editParams, videoVid: this.videoVid })
                .then((res) => {
                  this.$nextTick(() => this.fetchData()); // 刷新数据
                  this.$message.success("编辑成功");
                  this.dialogVisible = false;
                  this.close(); // 关闭弹窗
                })
                .catch((error) => {
                  console.error("编辑失败", error);
                  // this.$message.error("操作失败，请稍后重试");
                  this.dialogVisible = false;
                  this.close();
                });
            }
          }
        } else {
          this.$message.error("请完善表单信息");
          return false;
        }
      });
    },

    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val;
      this.fetchData();
    },
    handleCurrentChange(val) {
      console.log("val", val);
      this.tablePage.currentPage = val;
      this.fetchData();
    },
    // 关闭弹窗
    close() {
      this.dataDialog = {
        grammarId: "",
        knowledgeName: "",
        phaseCode: "",
        sortNum: "",
        videoList: [],
        superiorId: "",
      };
      this.$refs.dialogForm.resetFields();
    },
  },
};
</script>

<style scoped lang="scss">
.lh36 {
  line-height: 36px;
  font-size: 14px;
}

.SearchForm {
  width: 100%;
  margin-bottom: 15px;
  background: #fff;
  border-radius: 6px;
  padding: 5px 0;
  padding-left: 5px;
  box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.2);
}

.SearchCenter {
  display: flex;
  align-items: center;
  padding: 20px;
}

.el-select {
  width: 100%;
}

.el-form-item {
  margin-bottom: 0;
}

.dialogKnowledge {
  padding-bottom: 20px;
}
.dialogKnowledge .el-form-item {
  margin-bottom: 30px;
}
.btn-add {
  padding: 5px;
}

.red {
  color: red;
}

.green {
  color: green;
}

.course-table {
  text-align: center;
}

.course-table td,
.course-table th {
  padding: 5px 0;
  text-align: center;
}

.course-table button {
  padding: 2px;
}

.icon-stop {
  display: inline-block;
  width: 12px;
  height: 12px;
  background: url("../../icons/stop.png") no-repeat top center/contain;
}

.mt22 {
  margin-top: 22px;
}

.blue {
  margin-right: 50px;
  color: #409eff;
}

@media (max-width: 767px) {
  .el-message-box {
    width: 80% !important;
  }
}
</style>
