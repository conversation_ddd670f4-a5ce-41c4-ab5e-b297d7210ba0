import request from '@/utils/request'

export default {
  list(queryParam, pageParam) {
    return request({
      url: '/znyy/learn/master/list',
      method: 'get',
      params: {
        loginName: queryParam.loginName,
        realName: queryParam.realName,
        roleTag: queryParam.roleTag,
        merchantName: queryParam.merchantName,
        pageNum: pageParam.currentPage,
        pageSize: pageParam.size
      }
    })
  },
  queryUserDetail(merchantCode) {
    return request({
      url: '/znyy/learn/master',
      method: 'get',
      params: {'merchantCode': merchantCode}
    })
  },
  createLearnMaster(data) {
    return request({
      url: '/znyy/learn/master',
      method: 'POST',
      data: data
    })
  },
  updateLearnMaster(data) {
    return request({
      url: '/znyy/learn/master',
      method: 'PUT',
      data: data
    })
  }
}
