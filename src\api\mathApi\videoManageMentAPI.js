import request from '@/utils/request';

export default {
  // 课程大类筛选项
  courseCategoriesAPI() {
    return request({
      url: '/znyy/curriculum/math',
      method: 'get'
    });
  },
  // 课程大类筛选项
  courseCategoriesAPI() {
    return request({
      url: '/znyy/curriculum/math',
      method: 'get'
    });
  },

  // 获取版本id
  getVersionIdAPI(data) {
    return request({
      url: '/dyf/math/web/basisConfig/selectVersionInfo',
      method: 'get',
      params: data
    });
  },


  // 获取树的数据data
  getTreeDataAPI(data) {
    return request({
      url: '/dyf/math/web/coursePeriodConfig/selectTreeVersion',
      method: 'get',
      params: data
    });
  },
  // 获取树的数据data
  getTreeDataAPI(data) {
    return request({
      url: '/dyf/math/web/coursePeriodConfig/selectTreeVersion',
      method: 'get',
      params: data
    });
  },

  // 获取视频列表
  getVideoListAPI(data) {
    return request({
      url: '/dyf/web/math/video/page',
      method: 'get',
      params: data
    });
  },

  // 新增视频
  addVideoAPI(data) {
    return request({
      url: '/dyf/web/math/video/addOrUpdate',
      method: 'post',
      data: data
    });
  },

  // 视频保存时的校验

  addVideoCheckAPI(data) {
    return request({
      url: '/media/web/video/save',
      method: 'post',
      data: data
    });
  },

  // 根据vid 获取到图片
  getVideoImageAPI(data) {
    return request({
      url: `/media/web/video/details/${data.vid}`,
      method: 'get',
      params: data
    });
  },

  // 校验vid是否正确
  checkVideoAPI(data) {
    return request({
      url: '/dyf/web/math/video/vid',
      method: 'get',
      params: data
    });
  },
  // 删除
  deleteVideoAPI(query) {
    return request({
      url: '/dyf/web/math/video/delete',
      method: 'delete',
      params: query,
    });
  },

  // 获取视频详情
  getVideoDetailAPI(query) {
    return request({
      url: '/dyf/web/math/video/detail',
      method: 'get',
      params: query,
    });
  },
  // 导入
  importVideo(data) {
    return request({
      url: '/dyf/web/math/video/import',
      method: 'POST',
      data
    });
  },
}
