import request from "@/utils/request";

// 获取结业证书分页列表
export function pageAPI(query, pageParam) {
  const { pageNum, pageSize } = pageParam;
  return request({
    url: `/dyf/web/v2/certificate/page/${pageNum}/${pageSize}`,
    method: "get",
    params: {
      ...query,
      pageNum: pageParam.currentPage == null ? 1 : pageParam.currentPage,
      pageSize: pageParam.size == null ? 10 : pageParam.size,
    },
  });
}

// 获取课件打印分页列表
export function getPrintListAPI(query, pageParam) {
  const { pageNum, pageSize } = pageParam;
  return request({
    url: `/dyf/web/v2/note/page/${pageNum}/${pageSize}`,
    method: "get",
    params: {
      ...query,
      pageNum: pageParam.currentPage == null ? 1 : pageParam.currentPage,
      pageSize: pageParam.size == null ? 10 : pageParam.size,
    },
  });
}

// 打印状态变更
export function updatePrintStatusAPI(data) {
  return request({
    url: "/dyf/web/v2/print/update",
    method: "post",
    data,
  });
}
