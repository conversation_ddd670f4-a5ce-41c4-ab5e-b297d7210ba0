<template>
  <div>
    <el-dialog title="开通确认单" :center="true" :visible.sync="dialogVisible" width="500px" @close="handleOuterClose">
      <el-form ref="confirmFormRef" :model="form" label-width="120px">
        <el-form-item label="俱乐部编号" prop="merchantCode">
          <el-input disabled v-model="form.merchantCode"></el-input>
        </el-form-item>
        <el-form-item label="俱乐部名称" prop="merchantName">
          <el-input disabled v-model="form.merchantName"></el-input>
        </el-form-item>
        <el-form-item label="负责人" prop="realName">
          <el-input disabled type="person" v-model="form.realName"></el-input>
        </el-form-item>
        <el-form-item label="分配合伙人智能学习管理系统数">
          <el-slider v-model="form.sysNum" :max="maxNumber" show-input v-if="dialogVisible"></el-slider>
          分配最大数量不可超过自身剩余数量：
          <span style="color: red">{{ quantityNumber }}</span>
        </el-form-item>
        <el-form-item class="dialog-footer">
          <template>
            <el-button :loading="loading" type="primary" @click="handleConfirm">确认开通</el-button>
            <el-button @click="handleOuterClose">取 消</el-button>
          </template>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
  import dealerPaylist from '@/api/operationsPayment';

  export default {
    name: 'confirmForm',
    props: {
      isShowClub: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        form: {
          merchantCode: '',
          realName: '',
          merchantName: '',
          sysNum: 0
        },
        maxNumber: 999999,
        quantityNumber: 999999,
        loading: false
      };
    },
    computed: {
      dialogVisible: {
        get() {
          return this.isShowClub;
        },
        set(val) {
          this.$emit('update:isShowCityDialog', val);
        }
      }
    },
    methods: {
      handleOuterClose() {
        this.form.sysNum = 0;
        this.$emit('closeConfirmDialog');
        this.reset();
      },
      async handleConfirm() {
        if (this.loading) return;
        this.loading = true;
        try {
          const res = await dealerPaylist.openOperationApi(this.form);
          if (res) {
            this.$message.success('操作成功');
            this.loading = false;
            this.reset();
            this.$emit('closeConfirmDialog', true);
          }
        } catch (error) {
          console.log('🚀 ~ handleConfirm ~ error:', error);
          this.$message.error('操作失败');
          this.loading = false;
        }
      },

      reset() {
        this.form = {
          merchantCode: '',
          realName: '',
          merchantName: '',
          sysNum: 0
        };
      },

      /**
       *接收传值
       * @param data
       */
      setData(data) {
        console.log('🚀🥶💩~ data', data);
        this.form.merchantCode = data.merchantCode;
        this.form.merchantName = data.merchantName;
        this.form.realName = data.realName;
        this.form.sysNum = data.sysNum || 0;
      }
    }
  };
</script>

<style scoped>
  .dialog-footer {
    text-align: right;
  }
</style>
