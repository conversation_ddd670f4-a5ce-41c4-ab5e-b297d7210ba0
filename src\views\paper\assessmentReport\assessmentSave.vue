<template>
    <div>
      <el-form :label-position="'left'" class="Aform">
        <el-row>
          <el-form-item label="报告名称">
            <el-input v-model="formAbility.reportName" placeholder="报告名称"></el-input>
          </el-form-item>
          <el-form-item label="关联学段">
            <el-select :disabled="gradeIdDisabled" v-model="formAbility.gradeId" @change="getSubjectList" placeholder="关联学段" >
              <el-option v-for="item in gradeList" :label="item.name" :key="item.id" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
          <!-- <el-form-item label="版本:">
            <el-radio-group v-model="formAbility.type" @change="typeHandleChange">
              <el-radio label="REPORT">普通版</el-radio>
              <el-radio label="REPORT_DETAIL">详细版</el-radio>
            </el-radio-group>
          </el-form-item> -->
        </el-row>
        <el-row>
          <div class="subjectCard" v-for="(subject, index) in subjectList" :key="index">
            <div class="subjectTitle">
              {{ subject.name }}
            </div>
            <div class="templateCard" v-for="(template, index) in subject.assessReportContentList" :key="index">
              <div class="templateTitle">
                <el-input-number size="mini" v-model="template.scopeMin"/>
                ~
                <el-input-number size="mini" v-model="template.scopeMax"/>
                &nbsp;（单位分）
                <!--              <el-button style="float: right" type="danger" icon="el-icon-delete" circle></el-button>-->
              </div>
              <div style="display: flex">
                <div  style="width: 70%">
                  <div class="title-css">能力解析</div>
                  <el-form label-width="90px">
                    <el-form-item label="定义:" class="is-required" :class="(!template.definitionContent&&formError)?'is-error':''">
                      <el-input type="textarea" v-model="template.definitionContent"  maxlength="100"  show-word-limit
                          placeholder="请输入能力解析定义" ></el-input>
                    </el-form-item>
                    <el-form-item label="总结:"  class="is-required" :class="(!template.summarizeContent&&formError)?'is-error':''">
                      <el-input type="textarea"  class="is-required" v-model="template.summarizeContent" maxlength="100"  show-word-limit
                          placeholder="请输入能力解析总结" ></el-input>
                    </el-form-item>
                    <el-form-item label="训练建议:"  class="is-required" :class="(!template.trainContent&&formError)?'is-error':''">
                      <el-input type="textarea" v-model="template.trainContent" maxlength="500"  show-word-limit
                          placeholder="请输入能力解析训练建议" ></el-input>
                    </el-form-item>
                    <el-form-item label="效果呈现:"  class="is-required " :class="(!template.effectContent&&formError)?'is-error':''">
                      <el-input type="textarea" v-model="template.effectContent" maxlength="500"  show-word-limit
                          placeholder="请输入能力解析效果呈现" ></el-input>
                    </el-form-item>
                  </el-form>
                </div>
                <el-button @click="deleteTemplate(subject.assessReportContentList, index)" style="margin-left: 10px"  type="danger" plain >删除 </el-button>
              </div>
            </div>
            <el-button icon="el-icon-plus" type="primary"  :disabled="subject.assessReportContentList.length >= 10" style="margin: auto"  @click="addTemplateOpen(subject.assessReportContentList)" >
              添加报告内容
            </el-button>
          </div>
        </el-row>
        <el-form-item>
          <el-button type="primary" @click="onSubmit">提交</el-button>
        </el-form-item>
      </el-form>
      <!-- 修改或者新增分值弹窗 -->
      <el-dialog :title="addTemplateTitle" :visible.sync="open" width="70%" @close="addTemplateClose">
        <el-form label-width="120px" style="width: 70%;">
          <el-form-item label="最小分值:" prop="grade">
            <el-input v-model="addTemplate.scopeMin"></el-input>
          </el-form-item>
          <el-form-item label="最大分值:" prop="name">
            <el-input v-model="addTemplate.scopeMax"></el-input>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="submitAddTemplate">确 定</el-button>
          <el-button @click="open = false">取 消</el-button>
        </div>
      </el-dialog>
    </div>
  </template>
  <script>
  import gradeApi from '@/api/paper/grade'
  import abilityReportApi from '@/api/paper/abilityReport'
  import subjectApi from '@/api/paper/subject'
  export default {
    name: 'save',
    data() {
      return {
        formAbility: {
          id: undefined,
          reportName: undefined,
          gradeId: undefined,
          // type: 'REPORT'
        },
        gradeList: [],
        subjectList: [],
        // is-error
        formError:false,
        open: false,
        addTemplate: {},
        addTemplateTitle: '',
        addTemplateList: [],
        subjectMap: undefined,
        gradeIdDisabled: false
      }
    },
    created() {
      let id = this.$route.query.id
      //获取所有学段
      gradeApi.pageList().then((res) => {
        this.gradeList = res.data.data
        if (id != null) {
          // this.gradeIdDisabled = true
  
          abilityReportApi.assessReportDetail(id).then(abilityRes => {
            let abilityData = abilityRes.data
  
            this.formAbility.id = abilityData.id
            this.formAbility.reportName = abilityData.reportName
            this.formAbility.gradeId = abilityData.gradeId
  
            let subjectObj = abilityData.subjectMap
            let map = new Map()
            for (let key in subjectObj) {
              map.set(key, subjectObj[key])
            }
            // this.subjectMap = map;
  
            subjectApi.pageList({
              'pageNum': 1, 'pageSize': 100, 'gradeId': abilityData.gradeId
            }).then((subjectRes) => {
              let subjectData = subjectRes.data.data
              if (subjectData != null) {
                subjectData.forEach(i => {
                  let newVar = map.get(i.id)
                  if (newVar != null) {
                    i.assessReportContentList = newVar
                  } else {
                    i.assessReportContentList = []
                  }
                })
                this.subjectList = subjectData
              } else {
                this.subjectList = []
              }
  
              console.log(this.subjectList)
            })
            //刷新vue视图
            this.$forceUpdate()
          })
        }
      })
    },
    methods: {
      typeHandleChange(val) {
        this.getSubjectList(this.formAbility.gradeId)
      },
      getSubjectList(gradeId) {
        this.subjectList = []
            // existReportByGradeId
            abilityReportApi.existReportByGradeId(gradeId).then(res => {
              if(res.data){
                this.$message.warning('该学段已创建过报告')
              }else{
                this.formAbility.id = undefined
                // this.formAbility.reportName = undefined
                // this.formAbility.gradeId = undefined
                this.subjectList = []
                subjectApi.pageList({
                  'pageNum': 1, 'pageSize': 100, 'gradeId': gradeId
                }).then((res) => {
                  let data = res.data.data
                  if(res.data.data&&res.data.data.length>0){
                    data.forEach(i => {
                    i.assessReportContentList = []
                  })
                    this.subjectList = res.data.data
                  }else{
                    this.$message.warning('请添加该学段的维度')
                  }
                })
              }
            })
        // let type = this.formAbility.type
        // abilityReportApi.getReportByGradeId(gradeId, type).then(abilityRes => {
        //   let abilityData = abilityRes.data
        //   if (abilityData != null) {
        //     this.formAbility.id = abilityData.id
        //     this.formAbility.reportName = abilityData.reportName
        //     this.formAbility.gradeId = abilityData.gradeId
  
        //     let subjectObj = abilityData.subjectMap
        //     let map = new Map()
        //     for (let key in subjectObj) {
        //       map.set(key, subjectObj[key])
        //     }
        //     subjectApi.pageList({
        //       'pageNum': 1, 'pageSize': 100, 'gradeId': abilityData.gradeId
        //     }).then((subjectRes) => {
        //       let subjectData = subjectRes.data.data
        //       if (subjectData == null) {
        //         this.subjectList = []
        //       } else {
        //         subjectData.forEach(i => {
        //           let newVar = map.get(i.id)
        //           if (newVar != null) {
        //             i.assessReportContentList = newVar
        //           } else {
        //             i.assessReportContentList = []
        //           }
        //         })
        //         this.subjectList = subjectData
        //       }
        //     })
        //   } else {
        //     this.formAbility.id = undefined
        //     this.formAbility.reportName = undefined
        //     // this.formAbility.gradeId = undefined
        //     this.subjectList = []
        //     subjectApi.pageList({
        //       'pageNum': 1, 'pageSize': 100, 'gradeId': gradeId
        //     }).then((res) => {
        //       let data = res.data.data
        //       data.forEach(i => {
        //         i.assessReportContentList = []
        //       })
        //       this.subjectList = res.data.data
        //     })
        //   }
        //   //刷新vue视图
        //   this.$forceUpdate()
        // })
      },
      onSubmit() {
        if(!this.formAbility.gradeId){
          this.$message.error('请选择关联学段')
          return
        }
        if(!this.subjectList.length>0){
          this.$message.warning('请添加该学段的维度')
          return
        }
        this.formError=true
        // console.log(this.subjectList,'22222222222222222222222222222222222222')
        // console.log(this.subject.assessReportContentList,'11111111111111111111111111111111111')
        for(let i=0;i<this.subjectList.length;i++){
          var info = this.subjectList[i]
          console.log(info,'info')
          for(let j=0;j<info.assessReportContentList.length;j++){
            var item =info.assessReportContentList[j]
            console.log(item,'item')
            if(!item.definitionContent||!item.summarizeContent||!item.trainContent||!item.effectContent){
              this.$message.error('请输入必填项')
              return
            }
          }
        }
        let param = this.formAbility
        param.subjectVoList = this.subjectList
        abilityReportApi.assessReportSave(param).then((res) => {
          if (res.success) {
            this.$message.success('提交成功！')
            if (param.id != null) {
              this.$router.push({ path: '/paper/assessmentReport', query: { gradeId: param.gradeId } })
            } else {
              this.$router.push({ path: '/paper/assessmentReport' })
            }
          } else {
            this.$message.error(res.errMessage)
          }
        })
      },
      addTemplateOpen(templateList) {
        let type = this.formAbility.type
        let item = { scopeMin: undefined, scopeMax: undefined, content: '', type: type }
        templateList.push(item)
        // this.addTemplateTitle = "新增分值"
        // this.open = true
      },
      deleteTemplate(templateList, index) {
        this.$confirm('您确定要删除此条数据？', '提示').then(() => {
          templateList.splice(index, 1)
        })
      },
      submitAddTemplate() {
        this.addTemplateList.push(this.addTemplate)
        this.addTemplateClose()
      },
      addTemplateClose() {
        this.open = false
        this.addTemplate = {}
        this.addTemplateList = []
      }
    }
  }
  </script>
  <style scoped lang="scss">
  ::v-deep .el-textarea__inner{
    padding-right: 50px !important;
  }
  .Aform {
    padding: 10px 50px;
  }
  .subjectCard {
    display: flex;
    flex-direction: column;
    box-shadow: 0 2px 4px rgba(0, 0, 0, .12), 0 0 6px rgba(0, 0, 0, .04);
    padding: 10px;
    margin-bottom: 20px;
    //.subjectTitle {
      //font-weight: 600;
    //}
    .title-css{
      padding:20px 0px;
    }
    .templateTitle {
      width: 100%;
      font-size: 15px;
      margin-bottom: 6px;
      color: #606266;
    }
    .templateCard {
      margin: 10px 0;
    }
  }
  </style>
  