/**
 * 停服时间管理相关接口
 */
import request from '@/utils/request'
export default {
  queryList(data,tablePage) {
      return request({
        url: '/znyy/stopServiceTimeConfig/page',
        method: 'GET',
        params: {
          pageNum: tablePage.currentPage,
          pageSize: tablePage.size,
          ...data
        }
      })
    },
    addStopServiceTime(data) {
      return request({
        url: '/znyy/stopServiceTimeConfig/add',
        method: 'put',
        params: data
      })
    },
    updateStopServiceTime(data) {
      return request({
        url: '/znyy/stopServiceTimeConfig/update',
        method: 'post',
        params: data
      })
    },
    deleteStopServiceTime(id) {
      return request({
        url: '/znyy/stopServiceTimeConfig/terminate',
        method: 'post',
        params: {
          id
        }
      })
    },
}