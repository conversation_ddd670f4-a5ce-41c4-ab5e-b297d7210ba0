<template>
  <div class="app-container">
    <el-form :model="dataQuery" ref="queryForm" :inline="true">
      <el-form-item label="学段名称：">
        <el-select v-model="dataQuery.grade" clearable placeholder="请选择学段">
          <el-option v-for="item in gradeList" :key="item" :value="item" :label="item"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="维度名称：">
        <el-select v-model="dataQuery.name" clearable placeholder="请选择维度">
          <el-option v-for="item in dimensionList" :key="item.id" :value="item.name" :label="item.name"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" style="marginRight: 5px" @click="seachForm">查询</el-button>
        <el-button type="success" style="marginRight: 5px" @click="resetQuery">重置</el-button>
        <el-button type="primary" @click="addBtn">新增</el-button>
      </el-form-item>
    </el-form>
    <el-table class="common-table" v-loading="tableLoading" :data="tableData" style="width: 100%;margin-bottom: 20px;"
      row-key="id" border default-expand-all>
      <el-table-column prop="id" label="ID"></el-table-column>
      <el-table-column prop="name" label="维度名称"></el-table-column>
      <el-table-column prop="grade" label="学段"></el-table-column>
      <el-table-column prop="withdrawnBonus" label="操作" width="480">
        <template slot-scope="scope">
          <el-button type="success" size="mini" icon="el-icon-edit-outline" @click="editBtn(scope.row.id)">编辑</el-button>
          <el-button type="danger" size="mini" icon="el-icon-delete" @click="delBtn(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <el-col :span="24" style="margin-bottom: 20px">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>

    <!-- 修改或者新增弹窗 -->
    <el-dialog :title="isEdit?'编辑维度':'添加维度'" :visible.sync="open" width="50%" @close="close">
      <el-form ref="form" :model="form" :rules="rules" label-width="120px" style="width: 70%;">
        <el-form-item label="学段:" prop="grade">
          <el-input v-model="gradeName" placeholder="请输入学段"/>
        </el-form-item>
        <el-form-item label="维度:" prop="name">
          <el-input v-model="form.name" placeholder="请输入维度"/>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import categoryApi from "@/api/abacusMentalCalc/category";
import { pageParamNames } from "@/utils/constants";
export default {
  name: "subject",
  data() {
    return {
      dataQuery: {
        grade: "",
        name: ""
      },
      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      open: false,
      tableData: [],

      gradeName: "",
      form: {
        grade: "",
        name: ""
      },
      isEdit:false,

      dimensionList:[], //维度
      gradeList:[], //学段

      // 表单校验
      rules: {
        name: [{ required: true, message: "请输入维度名称", trigger: "blur" }],
      }
    };
  },
  created() {
    this.getAllDimension();
    this.getGrade()
    this.getPageList();
  },
  methods: {
    addBtn() {
      this.reset();
      this.isEdit = false;
      this.open = true;
    },
    editBtn(id) {
      this.reset();
      this.isEdit = true;
      categoryApi.detail(id).then(res => {
        this.form = res.data;
        this.gradeName = this.form.grade
        this.open = true;
      });
    },
    delBtn(id) {
      this.$confirm('确定要删除吗？', '删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        categoryApi.delete(id).then(res => {
          this.$message.success('删除成功！')
          this.getAllDimension();
          this.getGrade();
          this.getPageList()
        })
      })
    },
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if(this.gradeName){
            this.form.grade = this.gradeName
          }else{
            this.$message.error("请输入有效的学段！");
            return
          }
          categoryApi.saveOrUpdate(this.form).then(response => {
            this.$message.success("提交成功！");
            this.$refs['form'].resetFields()
            this.close()
            this.getAllDimension();
            this.getGrade();
            this.getPageList();
          });
        }
      });
    },
    seachForm(){
      this.handlePage();
    },
    getAllDimension(){
      categoryApi.getAllDimension().then(res => {
          this.dimensionList = res.data
      });
    },
    getGrade(){
      categoryApi.getGrade().then(res => {
        this.gradeList = res.data
      });
    },
    getPageList() {
      this.tableLoading = true;
      this.dataQuery.pageNum = this.tablePage.currentPage;
      this.dataQuery.pageSize = this.tablePage.size;
      categoryApi.list(this.dataQuery).then(res => {
        this.tableData = res.data.data;
        this.tableLoading = false;
        // 设置后台返回的分页参数
        pageParamNames.forEach(name =>
          this.$set(this.tablePage, name, parseInt(res.data[name]))
        );
      });
    },
    handlePage() {
      (this.tablePage = {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      }),
        this.getPageList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dataQuery = {
        grade: '',
        name: ""
      };
      this.getPageList();
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val;
      this.getPageList();
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val;
      this.getPageList();
    },
    close() {
      this.open = false;
      this.reset();
    },
    reset() {
      this.gradeName = "";
      this.form = {
        grade: "",
        name: ""
      };
    },
  },
};
</script>

<style>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.period-table td,
.period-table th {
  text-align: center;
}

@media (max-width: 767px) {
  .el-message-box {
    width: 80% !important;
  }
}

.el-tooltip__popper {
  max-width: 800px;
}

.img-lg {
  width: 100px;
  height: 100px
}
</style>
