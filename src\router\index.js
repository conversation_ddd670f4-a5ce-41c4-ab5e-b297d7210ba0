import Vue from 'vue';
import Router from 'vue-router';
/* Layout */
import Layout from '../views/layout/Layout';
const _import = require('./_import_' + process.env.NODE_ENV);
Vue.use(Router);
export const constantRouterMap = [
  /*    {
      path: '/redirect',
      component: Layout,
      hidden: true,
      children: [
        {
          path: '/redirect/:path(.*)',
          component: () => import('@/views/redirect/index')
        }
      ]
    },*/
  {
    path: '/login',
    component: _import('login/index'),
    hidden: true
  },
  {
    path: '/ui_test_auto_login',
    component: _import('login/auto_login'),
    hidden: true
  },
  {
    path: '/forgot',
    component: _import('forgot/index'),
    hidden: true
  },
  {
    path: '/redirect',
    component: _import('redirect/index'),
    hidden: true
  },

  {
    path: '/auth-redirect',
    component: _import('login/auth-redirect'),
    hidden: true
  },
  {
    path: '/404',
    component: _import('error-page/404'),
    hidden: true
  },
  {
    path: '/401',
    component: _import('error-page/401'),
    hidden: true
  },
  {
    path: '',
    component: Layout,
    name: 'dashboard',
    meta: {
      title: '我的桌面',
      icon: 'dashboard',
      noCache: true
    },
    children: [
      {
        path: 'dashboard',
        component: _import('dashboard/index'),
        name: 'dashboard',
        meta: {
          title: '我的桌面',
          icon: 'dashboard',
          noCache: true
        }
      }
    ]
  }
];

/**
 * asyncRoutes
 * the routes that need to be dynamically loaded based on user roles
 */
export const asyncRouterMap = [
  {
    hidden: true,
    path: '/news',
    component: Layout,
    // redirect: '/news/index',
    meta: {
      perm: 'm:news',
      title: '消息管理',
      icon: 'chart'
    },
    children: [
      {
        path: 'list',
        component: _import('news/list'),
        name: 'list',
        meta: {
          perm: 'm:news:list',
          title: '消息通知'
          // noCache: true,
          // icon: 'usermanage'
        }
      },

      {
        path: 'sendNews',
        // hidden: true,
        component: _import('news/sendNews'),
        name: 'sendNews',
        meta: {
          perm: 'm:news:send',
          title: '消息管理'
          // noCache: true,
          // icon: 'roleTagManage'
        }
      },

      {
        path: 'newContent',
        // hidden: true,
        component: _import('news/newContent'),
        name: 'newContent',
        meta: {
          perm: 'm:news:content',
          title: '通知内容'
          // noCache: true,
          // icon: 'roleTagManage'
        }
      },

      {
        path: 'newsAdd',
        // hidden: true,
        component: _import('news/newsAdd'),
        name: 'add',
        meta: {
          perm: 'm:news:add',
          title: '新增消息'
          // noCache: true,
          // icon: 'roleTagManage'
        }
      },

      {
        path: 'newsEdit',
        // hidden: true,
        component: _import('news/newsEdit'),
        name: 'edit',
        meta: {
          perm: 'm:news:edit',
          title: '编辑消息'
          // noCache: true,
          // icon: 'roleTagManage'
        }
      }
    ]
  },
  {
    path: '/role/system',
    component: Layout,
    meta: {
      perm: 'm:sys',
      title: '权限管理',
      icon: 'chart'
    },
    children: [
      {
        path: 'user_manage',
        name: 'user_manage',
        component: _import('_system/user/index'),
        meta: {
          perm: 'm:sys:user',
          title: '用户管理',
          noCache: true,
          icon: 'usermanage'
        }
      },
      {
        path: 'role_manage',
        name: 'role_manage',
        component: _import('_system/role/index'),
        meta: {
          perm: 'm:sys:role',
          title: '角色管理',
          noCache: true,
          icon: 'roleTagManage'
        }
      },
      {
        hidden: true,
        path: 'role_manage/:roleId/assign_perm',
        name: 'role_manage_assign_perm',
        component: _import('_system/role/assign_perm'),
        meta: {
          hiddenTag: true,
          title: '角色授权'
        }
      },
      {
        path: 'perm_manage',
        name: 'perm_manage',
        component: _import('_system/perm/index'),
        meta: {
          perm: 'm:sys:perm',
          title: '权限管理',
          noCache: true,
          icon: 'role_perm'
        }
      }
    ]
  },
  // 培训管理--改为跳转学习中心后台
  // {
  //   path: '/training',
  //   component: Layout,
  //   redirect: '/training',
  //   meta: {
  //     perm: 'm:training',
  //     title: '培训管理',
  //     icon: 'home'
  //   },
  //   children: [
  //     {
  //       path: 'home',
  //       component: () => import('@/views/training/home/<USER>'),
  //       name: 'trainingHome',
  //       meta: { perm: 'm:training:home', title: '首页', icon: 'post', noCache: true }
  //     },

  //     {
  //       path: 'courseManagement',
  //       component: () => import('@/views/training/course/course'),
  //       name: 'course',
  //       meta: {
  //         perm: 'm:training:course',
  //         title: '课程管理',
  //         icon: 'post',
  //         noCache: true
  //       }
  //     },
  //     {
  //       path: 'feedbackManagement',
  //       component: () => import('@/views/training/course/feedback'),
  //       name: 'feedback',
  //       meta: {
  //         perm: 'm:training:feedback',
  //         title: '课程反馈',
  //         icon: 'post',
  //         noCache: true
  //       }
  //     },
  //     {
  //       path: 'courseType',
  //       component: () => import('@/views/training/course/courseType'),
  //       name: 'courseType',
  //       meta: {
  //         perm: 'm:training:courseType',
  //         title: '课程分类',
  //         icon: 'post',
  //         noCache: true
  //       }
  //     },
  //     {
  //       path: 'examManagement',
  //       component: () => import('@/views/training/exam/examManagement'),
  //       name: 'examManagement',
  //       meta: {
  //         perm: 'm:training:examManagement',
  //         title: '考题管理',
  //         icon: 'post',
  //         noCache: true
  //       }
  //     },
  //     {
  //       path: 'testPaperManagement',
  //       component: () => import('@/views/training/exam/testPaperManagement'),
  //       name: 'testPaperManagement',
  //       meta: {
  //         perm: 'm:training:testPaperManagement',
  //         title: '试卷管理',
  //         icon: 'post',
  //         noCache: true
  //       }
  //     },
  //     {
  //       path: 'traineeStatistics',
  //       component: () => import('@/views/training/traineeStatistics/traineeStatistics'),
  //       name: 'traineeStatistics',
  //       meta: {
  //         perm: 'm:training:traineeStatistics',
  //         title: '学员统计',
  //         icon: 'post',
  //         noCache: true
  //       }
  //     }
  //   ]
  // },
  {
    path: '/trainingCenter',
    component: Layout,
    redirect: '/trainingCenter',
    meta: {
      perm: 'm:trainingCenter:learnCenter',
      title: '学习中心',
      icon: 'home'
    },
    children: [
      {
        path: 'learnCenter',
        component: () => import('@/views/trainingCenter/learnCenter/learnCenter'),
        name: 'learnCenter',
        meta: {
          perm: 'm:trainingCenter:learnCenter',
          title: '学习中心',
          icon: 'post'
        }
      }
      // {
      //   path: 'personalCenter',
      //   component: () => import('@/views/trainingCenter/personalCenter/personalCenter'),
      //   name: 'personalCenter',
      //   meta: {
      //     perm: 'm:trainingCenter:personalCenter',
      //     title: '个人中心',
      //     icon: 'post'
      //   }
      // },
      // {
      //   path: 'courseCenter',
      //   component: () => import('@/views/trainingCenter/courseCenter/courseCenter'),
      //   name: 'courseCenter',
      //   meta: {
      //     perm: 'm:trainingCenter:courseCenter',
      //     title: '课程中心',
      //     icon: 'post'
      //   }
      // },
      // {
      //   path: '/courseDetail',
      //   hidden: true,
      //   component: () => import('@/views/trainingCenter/courseCenter/components/courseDetail'),
      //   name: 'courseDetail',
      //   meta: {
      //     perm: 'm:trainingCenter:courseDetail',
      //     title: '课程详情',
      //     icon: 'post'
      //   }
      // },
      // {
      //   path: '/playVideo',
      //   hidden: true,
      //   component: () => import('@/views/trainingCenter/courseCenter/components/playVideo'),
      //   name: 'post',
      //   meta: {
      //     perm: 'm:trainingCenter:playVideo',
      //     title: '课程学习',
      //     icon: 'post'
      //   }
      // }
    ]
  },
  // {
  //   path: '/dzy/card',
  //   component: Layout,
  //   redirect: '/dzy/card/cardList',
  //   meta: {
  //     perm: 'm:dzy',
  //     title: '产品列表',
  //     icon: 'zhiyuan',
  //     hidden:true
  //   },
  //   children: [
  //     {
  //       path: 'cardList1',
  //       component: () => import('@/views/dzy/card/card'),
  //       name: 'cardList1',
  //       meta: {
  //         perm: 'm:dzy:card:list',
  //         title: '志愿卡列表',
  //         icon: 'card_dzy'
  //       }
  //     },
  //     {
  //       path: 'cardList2',
  //       component: () => import('@/views/dzy/card/card'),
  //       name: 'cardList2',
  //       meta: {
  //         perm: 'm:dzy:card:list',
  //         title: '升学卡列表',
  //         icon: 'card_dzy'
  //       }
  //     },
  //     {
  //       path: 'cardList3',
  //       component: () => import('@/views/dzy/card/card'),
  //       name: 'cardList3',
  //       meta: {
  //         perm: 'm:dzy:card:list',
  //         title: '体验卡列表',
  //         icon: 'card_dzy'
  //       }
  //     },
  //     {
  //       path: 'ExpertsFillMeetingList',
  //       component: () => import('@/views/dzy/card/ExpertsFillMeeting'),
  //       name: 'ExpertsFillMeeting',
  //       meta: {
  //         perm: 'm:dzy:card:list',
  //         title: '高报师课程',
  //         icon: 'peoples'
  //       }
  //     },
  //     {
  //       path: 'DzyCanLearnMeeting',
  //       component: () => import('@/views/dzy/card/CanLearnMeeting'),
  //       name: 'DzyCanLearnMeeting',
  //       meta: {
  //         perm: 'm:dzy:card:list',
  //         title: '学能师课程',
  //         icon: 'peoples'
  //       }
  //     },
  //     {
  //       path: 'purchasedProduct',
  //       component: () => import('@/views/finance/schoolFlowList'),
  //       name: 'purchasedProduct',
  //       meta: {
  //         perm: 'm:dzy:card:list',
  //         title: '已购产品订单',
  //         icon: 'school_course_flow'
  //       }
  //     },
  //     {
  //       path: 'cardTypeList',
  //       component: () => import('@/views/dzy/card/cardType'),
  //       name: 'cardTypeList',
  //       meta: {
  //         perm: 'm:dzy:card:type:list',
  //         title: '志愿卡类型列表',
  //         icon: 'card_type'
  //       }
  //     },
  //     {
  //       path: 'expertsApplicantsList',
  //       component: _import('dzy/applicants/expertsApplicantsList'),
  //       name: 'applicantsList',
  //       meta: {
  //         perm: 'm:dzy:expertsApplicants:list',
  //         title: '高报师列表',
  //         icon: 'card_dzy'
  //       }
  //     },
  //     {
  //       path: 'expertsApplicantsPerson',
  //       component: _import('dzy/applicants/expertsApplicantsPerson'),
  //       name: 'applicantsList',
  //       meta: {
  //         perm: 'm:dzy:expertsPerson:list',
  //         title: '高报师个人列表',
  //         icon: 'card_dzy'
  //       }
  //     },
  //     {
  //       path: 'expertsApplicantsAdd',
  //       hidden: true,
  //       component: _import('dzy/applicants/expertsApplicantsAdd'),
  //       name: 'expertsApplicantsAdd',
  //       meta: {
  //         perm: 'm:dzy:expertsApplicants:add',
  //         title: '高报师新增编辑页面'
  //       }
  //     },
  //     {
  //       path: 'expertsArrearsList',
  //       component: _import('dzy/applicants/expertsArrearsList'),
  //       name: 'arrearsList',
  //       meta: {
  //         perm: 'm:dzy:expertsArrears:list',
  //         title: '欠费列表',
  //         icon: 'card_dzy'
  //       }
  //     },
  //     {
  //       path: 'expertsFillList',
  //       component: _import('dzy/applicants/expertsFillList'),
  //       name: 'expertsFillList',
  //       meta: {
  //         perm: 'm:dzy:expertsFill:list',
  //         title: '专家填报',
  //         icon: 'card_type'
  //       }
  //     }
  //   ]
  // },
  {
    path: '/merchantManagement',
    component: Layout,
    redirect: '/merchantManagement/marketList',
    meta: {
      perm: 'm:merchant',
      title: '商户管理',
      icon: 'shop'
    },
    children: [
      // {
      //   path: 'marketList',
      //   component: _import('merchantManagement/marketList'),
      //   name: 'marketList',
      //   meta: {
      //     perm: 'm:merchant:marketList',
      //     title: '推荐人列表',
      //     icon: 'makertList'
      //   }
      // },
      {
        path: 'divisionList',
        component: _import('merchantManagement/divisionList'),
        name: 'divisionList',
        meta: {
          perm: 'm:merchant:divisionList',
          title: '事业部列表',
          icon: 'divisionList'
        }
      },

      {
        path: 'divisionAdd',
        hidden: true,
        component: _import('merchantManagement/divisionAdd'),
        name: 'divisionAdd',
        meta: {
          perm: 'm:merchant:divisionAdd',
          title: '事业部增加'
        }
      },
      {
        path: 'branchOfficeList',
        component: _import('merchantManagement/branchOfficeList'),
        name: 'branchOfficeList',
        meta: {
          perm: 'm:merchant:branchOfficeList',
          title: '分公司列表',
          icon: 'company'
        }
      },
      {
        path: 'branchOfficeAdd',
        hidden: true,
        component: _import('merchantManagement/branchOfficeAdd'),
        name: 'branchOfficeAdd',
        meta: {
          perm: 'm:merchant:branchOfficeAdd',
          title: '分公司增加'
        }
      },
      // {
      //   path: 'promoterList',
      //   component: _import('merchantManagement/promoterList'),
      //   name: 'promoterList',
      //   meta: {
      //     perm: 'm:merchant:promoterList',
      //     title: '推广人列表',
      //     icon:'promoter'
      //   }
      // },
      // {
      //   path: 'promoterAdd',
      //   hidden: true,
      //   component: _import('merchantManagement/promoterAdd'),
      //   name: 'promoterAdd',
      //   meta: {
      //     perm: 'm:merchant:promoterAdd',
      //     title: '推广人新增'
      //   },
      // },
      {
        path: 'agentList',
        component: _import('merchantManagement/agentList'),
        name: 'agentList',
        meta: {
          perm: 'm:merchant:agentList',
          title: '市级服务商列表',
          icon: 'agent'
        }
      },
      {
        path: 'agentAddList',
        hidden: true,
        component: _import('merchantManagement/agentAddList'),
        name: 'agentAddList',
        meta: {
          perm: 'm:merchant:agentAddList',
          title: '市级服务商新增'
        }
      },
      {
        path: 'areaAgent',
        component: _import('merchantManagement/areaAgent'),
        name: 'areaAgent',
        meta: {
          perm: 'm:merchant:areaAgent',
          title: '区县服务商列表',
          icon: 'agent'
        }
      },
      {
        path: 'areaAgentAdd',
        component: _import('merchantManagement/areaAgentAddList'),
        name: 'areaAgentAdd',
        hidden: true,
        meta: {
          perm: 'm:merchant:areaAgent:add',
          title: '区县服务商新增',
          icon: 'agent'
        }
      },
      {
        path: 'deliveryCenterList',
        component: _import('merchantManagement/deliveryCenterList'),
        name: 'deliveryCenterList',
        meta: {
          perm: 'm:merchant:deliveryCenter',
          title: '交付中心列表',
          icon: 'score_config'
        }
      },
      {
        path: 'deliveryCenterAdd',
        hidden: true,
        component: _import('merchantManagement/deliveryCenterAdd'),
        name: 'deliveryCenterAdd',
        meta: {
          perm: 'm:merchant:deliveryCenterAdd',
          title: '交付中心新增'
        }
      },
      {
        path: 'experienceList',
        component: _import('merchantManagement/experienceList'),
        hidden: true,
        name: 'experienceList',
        meta: {
          perm: 'm:merchant:experienceList',
          title: '体验中心列表',
          icon: 'dealer'
        }
      },
      {
        path: 'experienceAdd',
        hidden: true,
        component: _import('merchantManagement/experienceAdd'),
        name: 'experienceAdd',
        meta: {
          perm: 'm:merchant:experienceAdd',
          title: '添加/修改体验中心'
        }
      },
      {
        path: 'dealerList',
        component: _import('merchantManagement/dealerList'),
        name: 'dealerList',
        meta: {
          perm: 'm:merchant:dealerList',
          title: '托管中心列表',
          icon: 'dealer'
        }
      },
      {
        path: 'operationsList',
        component: _import('merchantManagement/operationsList'),
        name: 'operationsList',
        meta: {
          perm: 'm:merchant:operationsList',
          title: '超级俱乐部列表',
          icon: 'dealer'
        }
      },
      {
        path: 'operationsPayment',
        hidden: true,
        component: _import('merchantManagement/operationsPayment'),
        name: 'operationsPayment',
        meta: {
          perm: 'm:merchant:operationsPayment',
          title: '超级俱乐部完款',
          icon: 'dealer'
        }
      },
      {
        path: 'salesDetails',
        // hidden: true,
        component: _import('merchantManagement/salesDetails'),
        name: 'salesDetails',
        meta: {
          perm: 'm:merchant:salesDetails',
          title: '售课列表',
          icon: 'dealer'
        }
      },
      // {
      //   path: 'storeRevenue',
      //   // hidden: true,
      //   component: _import('merchantManagement/storeRevenue'),
      //   name: 'storeRevenue',
      //   meta: {
      //     perm: 'm:merchant:storeRevenue',
      //     title: '门店收益',
      //     icon: 'dealer'
      //   }
      // },

      // {
      //   path: 'operationsReturnStore',
      //   // hidden: true,
      //   component: _import('merchantManagement/operationsReturnStore'),
      //   name: 'operationsReturnStore',
      //   meta: {
      //     perm: 'm:merchant:operationsReturnStore',
      //     title: '退费门店列表',
      //     icon: 'dealer'
      //   }
      // },
      {
        path: 'operationsAdd',
        hidden: true,
        component: _import('merchantManagement/operationsAdd'),
        name: 'operationsAdd',
        meta: {
          perm: 'm:merchant:operationsAdd',
          title: '添加/修改超级俱乐部'
        }
      },
      {
        path: 'dealerAdd',
        hidden: true,
        component: _import('merchantManagement/dealerAdd'),
        name: 'dealerAdd',
        meta: {
          perm: 'm:merchant:dealerAdd',
          title: '添加/修改托管中心'
        }
      },
      {
        path: 'schoolList',
        component: () => import('@/views/merchantManagement/schoolList'),
        name: 'schoolList',
        meta: {
          perm: 'm:merchant:schoolList',
          title: '门店列表',
          icon: 'school'
        }
      },
      {
        path: 'schoolCheck',
        hidden: true,
        component: () => import('@/views/merchantManagement/schoolCheck'),
        name: 'schoolCheck',
        meta: {
          perm: 'm:merchant:schoolCheck',
          title: '门店审核'
        }
      },
      // {
      //   path: 'schoolEdit',
      //   hidden: true,
      //   component: () => import('@/views/merchantManagement/schoolEdit'),
      //   name: 'schoolEdit',
      //   meta: {
      //     perm: 'm:merchant:schoolEdit',
      //     title: '门店编辑'
      //   }
      // },
      {
        path: 'schoolCompletePaymentIs',
        hidden: true,
        component: () => import('@/views/merchantManagement/schoolCompletePaymentIs'),
        name: 'schoolCompletePaymentIs',
        meta: {
          // perm: 'm:merchant:schoolCompletePaymentIs',
          perm: 'm:merchant:schoolEdit',
          title: '门店完款'
        }
      },
      {
        path: 'brand',
        component: () => import('@/views/merchantManagement/brand'),
        name: 'brand',
        meta: {
          perm: 'm:merchant:brand',
          title: '超级品牌列表',
          icon: 'dealer'
        }
      },
      {
        path: 'addbrandList',
        hidden: true,
        component: () => import('@/views/merchantManagement/components/addbrandList'),
        name: 'addbrandList',
        meta: {
          perm: 'm:merchant:addbrandList',
          title: '添加/修改超级品牌'
        }
      },
      {
        path: 'refundReview',
        component: () => import('@/views/merchantManagement/refundReview'),
        name: 'refundReview',
        meta: {
          perm: 'm:merchant:refundReview',
          title: '门店退款审核',
          icon: 'dealer'
        }
      }
      // TODO: 6.13隐藏
      // {
      //   path: 'channelPartner',
      //   component: () => import('@/views/merchantManagement/channelPartner'),
      //   name: 'channelPartner',
      //   meta: {
      //     perm: 'm:merchant:channelPartner',
      //     title: '渠道伙伴',
      //     icon: 'dealer'
      //   }
      // },
      // {
      //   path: 'promotionAmbassador',
      //   component: () => import('@/views/merchantManagement/channelPartner'),
      //   name: 'promotionAmbassador',
      //   meta: {
      //     perm: 'm:merchant:promotionAmbassador',
      //     title: '推广大使',
      //     icon: 'dealer'
      //   }
      // }
    ]
  },
  // 商户审核
  {
    path: '/merchantReview',
    component: Layout,
    redirect: '/merchantReview',
    meta: {
      perm: 'm:merchantReview',
      title: '商户审核',
      icon: 'shop'
    },
    children: [
      {
        path: 'storeAuditList',
        component: () => import('@/views/merchantReview/storeAuditList'),
        name: 'storeAuditList',
        meta: {
          perm: 'm:merchantReview:storeAuditList',
          title: '门店审核',
          icon: 'agent'
        }
      },
      {
        path: 'clubReviewList',
        component: () => import('@/views/merchantReview/clubReviewList'),
        name: 'clubReviewList',
        meta: {
          perm: 'm:merchantReview:clubReviewList',
          title: '俱乐部审核',
          icon: 'agent'
        }
      }
    ]
  },
  {
    path: '/contractManagement',
    component: Layout,
    redirect: '/contractManagement/contractManagementList',
    meta: {
      perm: 'm:contractManagement:contractManagementList',
      title: '合同管理',
      icon: 'company'
    },
    children: [
      {
        path: 'contractManagementList',
        component: () => import('@/views/merchantManagement/ContractManagement'),
        name: 'ContractManagement',
        meta: {
          perm: 'm:contractManagement:contractManagementList',
          title: '合同管理',
          icon: 'company'
        }
      }
    ]
  },
  {
    path: '/learnTube',
    component: Layout,
    hidden: true,
    redirect: '/learnTube/learnTubeList',
    meta: {
      perm: 'm:learnTube:learnTubeList',
      title: '学管师管理',
      icon: 'shop'
    },
    children: [
      {
        path: 'learnTubeList',
        component: () => import('@/views/learnTube/learnTubeList'),
        name: 'learnTubeList',
        meta: {
          perm: 'm:learnTube:learnTubeList',
          title: '学管师列表',
          icon: 'agent'
        }
      }
    ]
  },
  {
    path: '/areas',
    component: Layout,
    //redirect: '/merchantManagement/marketList',
    meta: {
      perm: 'm:areas',
      title: '商户管理',
      icon: 'shop'
    },
    children: [
      {
        path: 'areasPromoterAgentList',
        component: () => import('@/views/areas/promoter/areasPromoterAgentList'),
        name: 'areasPromoterAgentList',
        meta: {
          perm: 'm:areas:areasPromoterAgentList',
          title: '市级服务商列表',
          icon: 'agent'
        }
      },
      {
        path: 'areasAgentAdd',
        hidden: true,
        component: () => import('@/views/areas/promoter/areasAgentAdd'),
        name: 'areasAgentAdd',
        meta: {
          perm: 'm:areas:areasAgentAdd',
          title: '市级服务商新增'
        }
      },
      {
        path: 'areasDealerLists',
        component: () => import('@/views/areas/dealer/areasDealerList'),
        name: 'areasDealerLists',
        meta: {
          perm: 'm:areas:areasDealerList',
          title: '托管中心列表',
          icon: 'dealer'
        }
      },
      {
        path: 'areasAgentDealerList',
        component: () => import('@/views/areas/dealer/areasAgentDealerList'),
        name: 'areasAgentDealerList',
        meta: {
          perm: 'm:areas:areasAgentDealerList',
          title: '托管中心列表',
          icon: 'dealer'
        }
      },
      {
        path: 'areasDealerAdd',
        hidden: true,
        component: () => import('@/views/areas/dealer/areasDealerAdd'),
        name: 'areasDealerAdd',
        meta: {
          perm: 'm:areas:areasDealerAdd',
          title: '托管中心新增'
        }
      },
      // {
      //   path: "areasSchoolList",
      //   component: () => import("@/views/areas/dealer/areasSchoolList"),
      //   name: "areasSchoolList",
      //   meta: {
      //     perm: "m:areas:areasSchoolList",
      //     title: "门店列表",
      //     icon: "school",
      //   },
      // },
      // {
      //   path: 'areasSchoolList',
      //   component: () => import('@/views/areas/dealer/areasSchoolList'),
      //   name: 'areasSchoolList',
      //   meta: {
      //     perm: 'b:merchant:OperationsVersion',
      //     title: '直推门店',
      //     icon: 'school'
      //   }
      // },
      {
        path: 'areasAgentSchoolList',
        component: () => import('@/views/areas/dealer/areasAgentSchoolList'),
        name: 'areasAgentSchoolList',
        meta: {
          perm: 'm:areas:areasAgentSchoolList',
          title: '门店列表',
          icon: 'school'
        }
      },
      {
        path: 'areasSchoolEdit',
        hidden: true,
        component: () => import('@/views/areas/dealer/areasSchoolEdit'),
        name: 'areasSchoolEdit',
        meta: {
          perm: 'm:areas:areasSchoolEdit',
          title: '门店编辑'
        }
      },
      {
        path: 'areasSchoolAdd',
        hidden: true,
        component: () => import('@/views/areas/dealer/areasSchoolAdd.vue'),
        name: 'areasSchoolAdd',
        meta: {
          perm: 'm:areas:areasSchoolEdit',
          title: '门店新增'
        }
      },
      {
        path: 'addMoreInfo',
        hidden: true,
        component: () => import('@/views/areas/dealer/addMoreInfo'),
        name: 'addMoreInfo',
        meta: {
          perm: 'm:merchant:addMoreInfo',
          title: '批量新增'
        }
      }
      // {
      //   path: 'areasSchoolEdit',
      //   hidden: true,
      //   component: () => import('@/views/areas/dealer/areasSchoolEdit'),
      //   name: 'areasSchoolEdit',
      //   meta: {
      //     perm: 'b:merchant:OperationsVersion',
      //     title: '门店编辑'
      //   }
      // }
      // {
      //   path: 'areasOffSite',
      //   component: () => import('@/views/areas/offsite/order'),
      //   name: 'areasOffSite',
      //   meta: {
      //     perm: 'm:areas:areasOffSite',
      //     title: '异地开店列表',
      //     icon:'yidi'
      //
      //   }
      // },
      // {
      //   path: 'referrerOrder',
      //   component: () => import('@/views/areas/offsite/referrerOrder'),
      //   name: 'referrerOrder',
      //   meta: {
      //     perm: 'm:areas:referrerOrder',
      //     title: '推荐异地开店列表',
      //     icon:'market_dealer'
      //   }
      // },

      /*   {
           path: 'areasSchoolAdd',
           hidden: true,
           component: () => import('@/views/areas/dealer/areasSchoolAdd'),
           name: 'areasSchoolAdd',
           meta: {
             perm: 'm:areas:areasSchoolAdd',
             title: 'Areas门店新增'
           }
         }*/
    ]
  },
  {
    path: '/markets',
    component: Layout,
    hidden: true,
    //redirect: '/merchantManagement/marketList',
    meta: {
      perm: 'm:markets',
      title: '推荐关系管理',
      icon: 'referee'
    },
    children: [
      {
        path: 'marketListRank',
        component: () => import('@/views/markets/marketListRank'),
        name: 'marketListRank',
        meta: {
          perm: 'm:markets:marketListRank',
          title: '推荐关系管理',
          icon: 'market_manage'
        }
      },
      {
        path: 'marketProfitRankFlows',
        component: () => import('@/views/markets/marketProfitRankFlows'),
        name: 'marketProfitRankFlows',
        meta: {
          perm: 'm:markets:marketProfitRankFlows',
          title: '分润流水',
          icon: 'profit_flow'
        }
      },
      {
        path: 'merchantAccountFlows',
        hidden: true,
        component: () => import('@/views/markets/merchantAccountFlows'),
        name: 'merchantAccountFlows',
        meta: {
          perm: 'm:markets:merchantAccountFlows',
          title: '资金流水'
        }
      },
      {
        path: 'marketProfitAccountList',
        hidden: true,
        component: () => import('@/views/markets/marketProfitAccountList'),
        name: 'marketProfitAccountList',
        meta: {
          perm: 'm:markets:marketProfitAccountList',
          title: '推荐人分润流水'
        }
      }
      /*   {
           path: 'areasSchoolAdd',
           hidden: true,
           component: () => import('@/views/areas/dealer/areasSchoolAdd'),
           name: 'areasSchoolAdd',
           meta: {
             perm: 'm:areas:areasSchoolAdd',
             title: 'Areas门店新增'
           }
         }*/
    ]
  },

  {
    path: '/profitaccount',
    component: Layout,
    hidden: true,
    redirect: '/profitaccount/profitAccountList',
    meta: {
      perm: 'm:profitaccount',
      title: '分润账户管理',
      icon: 'profit'
    },
    children: [
      {
        path: 'marketProfitAccount',
        component: () => import('@/views/profitaccount/marketProfitAccount'),
        name: 'marketProfitAccount',
        meta: {
          perm: 'm:profitaccount:marketProfitAccount',
          title: '分润账户管理',
          icon: 'profit_manage'
        }
      },
      {
        path: 'profitAccountList',
        component: () => import('@/views/profitaccount/profitAccountList'),
        name: 'profitAccountList',
        meta: {
          perm: 'm:profitaccount:profitAccountList',
          title: '分润账户',
          icon: 'profit_account'
        }
      },
      {
        hidden: true,
        path: 'profitWithdrawRecoder',
        component: () => import('@/views/profitaccount/profitWithdrawRecoder'),
        name: 'profitWithdrawRecoder',
        meta: {
          perm: 'm:profitaccount:profitWithdrawRecoder',
          title: '提现列表',
          icon: 'profit'
        }
      }
    ]
  },
  {
    path: '/market',
    component: Layout,
    hidden: true,
    redirect: '/market/marketList',
    meta: {
      perm: 'm:market:marketList',
      title: '推荐管理',
      icon: 'referee'
    },
    children: [
      {
        path: 'marketList',
        component: () => import('@/views/marketdd/marketList'),
        name: 'marketList',
        meta: {
          perm: 'm:market:marketList',
          title: '推荐人列表',
          icon: 'user'
        }
      }
    ]
  },
  {
    path: '/businessCode',
    component: Layout,
    hidden: true,
    redirect: '/area/businessCode/businessDistrictCode',
    meta: {
      perm: 'm:businessCode:businessDistrictCode',
      title: '商圈码管理',
      icon: 'busitcode'
    },
    children: [
      {
        path: 'businessDistrictCode',
        component: () => import('@/views/areas/businessCode/businessDistrictCode'),
        name: 'businessDistrictCode',
        meta: {
          perm: 'm:businessCode:businessDistrictCode',
          title: '商圈码列表',
          icon: 'busitcode'
        }
      }
    ]
  },
  {
    path: '/authorizationcode',
    component: Layout,
    redirect: '/area/authorizationcode/authorizationCode',
    meta: {
      perm: 'm:authorization:authorizationCode',
      title: '授权码列表',
      icon: 'authorization'
    },
    children: [
      {
        path: 'authorizationCode',
        component: () => import('@/views/areas/authorizationcode/authorizationCode'),
        name: 'authorizationCode',
        meta: {
          perm: 'm:authorization:authorizationCode',
          title: '授权码列表',
          icon: 'authorization'
        }
      }
    ]
  },
  {
    path: '/course',
    component: Layout,
    redirect: '/course/courseCategoryList',
    meta: {
      perm: 'm:course',
      title: '课程管理',
      icon: 'documentation'
    },
    children: [
      {
        path: 'courseTools',
        component: () => import('@/views/courseGeneralTools/courseTools'),
        name: 'courseTools',
        meta: {
          perm: 'm:courseGeneralTools:courseTools',
          title: '课程工具关联配置',
          icon: 'grammar_question'
        }
      },
      {
        path: 'generalTools',
        component: () => import('@/views/courseGeneralTools/generalTools'),
        name: 'generalTools',
        meta: {
          perm: 'm:courseGeneralTools:generalTools',
          title: '课程工具配置',
          icon: 'grammar_question'
        }
      },
      {
        path: 'courseCategoryList',
        component: () => import('@/views/course/courseCategoryList'),
        name: 'marketList',
        meta: {
          perm: 'm:course:courseCategoryList',
          title: '课程分类',
          icon: 'course'
        }
      },
      {
        path: 'coursePackageList',
        // hidden: true,
        component: () => import('@/views/course/coursePackageList'),
        name: 'coursePackageList',
        meta: {
          perm: 'm:coursePackage:list',
          title: '课程包',
          icon: 'course_category'
        }
      },
      //课时规划单
      {
        path: 'lessonPlanList',
        component: () => import('@/views/course/lessonPlanList'),
        name: 'lessonPlanList',
        meta: {
          perm: 'm:course:lessonPlanList',
          title: '课程规划单课时列表',
          icon: 'course_category'
        }
      },
      {
        path: 'regionalExamTime',
        component: () => import('@/views/course/regionalExamTime'),
        name: 'regionalExamTime',
        meta: {
          perm: 'm:course:regionalExamTime',
          title: '区域考试时间列表',
          icon: 'course_category'
        }
      },

      {
        path: 'courseChildrenList',
        hidden: true,
        component: () => import('@/views/course/courseChildrenList'),
        name: 'courseChildrenList',
        meta: {
          perm: 'm:course:courseChildrenList',
          title: '课程子类',
          icon: 'course_category'
        }
      },
      {
        path: 'courseMake',
        hidden: true,
        component: () => import('@/views/course/courseMake'),
        name: 'courseMake',
        meta: {
          perm: 'm:course:courseMake',
          title: '制作课程'
        }
      },

      ////////////////上传拼读单词/////////////////////
      {
        path: 'addDictationWord',
        hidden: true,
        component: () => import('@/views/course/courseDictation/addDictationWord'),
        name: 'addDictationWord',
        meta: {
          perm: 'm:course:addDictationWord',
          title: '制作课程'
        }
      },
      {
        path: 'makeCourseDictationList',
        hidden: true,
        component: () => import('@/views/course/courseDictation/makeCourseList/makeCourseDictationList'),
        name: 'makeCourseDictationList',
        meta: {
          // perm: "m:course:courseDictationList:make",
          title: '制作课程',
          icon: 'course_category'
        }
      },

      {
        path: 'courseReading',
        hidden: true,
        component: () => import('@/views/course/courseReading'),
        name: 'courseReading',
        meta: {
          perm: 'm:course:courseReading',
          title: '阅读理解'
        }
      },
      {
        path: 'courseListening',
        hidden: true,
        component: () => import('@/views/course/courseListening'),
        name: 'courseListening',
        meta: {
          perm: 'm:course:courseListening',
          title: '听力'
        }
      },
      // {
      //   path: 'courseReadingMake',
      //   hidden: true,
      //   component: () => import('@/views/course/courseReadingMake'),
      //   name: 'courseReadingMake',
      //   meta: {
      //     perm:'m:course:courseReadingMake',
      //     title: '阅读理解制作课程'
      //   }
      // },
      {
        path: 'courseList',
        component: () => import('@/views/course/courseList'),
        name: 'courseList',
        meta: {
          perm: 'm:course:courseList',
          title: '课程列表',
          icon: 'course_category'
        }
      },
      {
        path: 'superReadCourseList',
        component: () => import('@/views/course/superReadCourseList'),
        name: 'superReadCourseList',
        meta: {
          perm: 'm:course:superReadCourseList',
          title: '超级阅读课程列表',
          icon: 'course_category'
        }
      },
      // 文章列表
      {
        path: 'articleList',
        hidden: true,
        component: () => import('@/views/course/articleList'),
        name: 'articleList',
        meta: {
          perm: 'm:course:articleList',
          title: '文章列表'
        }
      },
      // 试题列表
      {
        path: 'testQuestionList',
        hidden: true,
        component: () => import('@/views/course/testQuestionList'),
        name: 'testQuestionList',
        meta: {
          perm: 'm:course:testQuestionList',
          title: '试题列表'
        }
      },
      // 试题编辑弹窗
      {
        path: 'questionDialog',
        hidden: true,
        component: () => import('@/views/course/questionDialog'),
        name: 'questionDialog',
        meta: {
          perm: 'm:course:questionDialog',
          title: '增加/编辑'
        }
      },
      // 新版全能听力模块
      {
        path: 'newListenCourseList',
        component: () => import('@/views/course/newListenCourseList'),
        name: 'newListenCourseList',
        meta: {
          perm: 'm:course:newListenCourseList',
          title: '新版听力课程列表',
          icon: 'course_category'
        }
      },
      {
        path: 'newListenList',
        hidden: true,
        component: () => import('@/views/course/newListenList'),
        name: 'newListenList',
        meta: {
          perm: 'm:course:newListenList',
          title: '听力列表'
        }
      },
      // 听力试题列表
      {
        path: 'listenQuestionList',
        hidden: true,
        component: () => import('@/views/course/listenQuestionList'),
        name: 'listenQuestionList',
        meta: {
          perm: 'm:course:listenQuestionList',
          title: '听力题目列表'
        }
      },
      // 试题编辑弹窗
      {
        path: 'listenQuestionDialog',
        hidden: true,
        component: () => import('@/views/course/listenQuestionDialog'),
        name: 'listenQuestionDialog',
        meta: {
          perm: 'm:course:listenQuestionDialog',
          title: '增加/编辑'
        }
      },
      {
        path: 'courseCate',
        component: () => import('@/views/course/courseCate'),
        name: 'courseCate',
        meta: {
          perm: 'm:course:courseCate',
          title: '课程类型',
          icon: 'course_category'
        }
      },
      {
        path: 'textbookVersionList',
        component: () => import('@/views/course/textbookVersionList'),
        name: 'textbookVersionList',
        meta: {
          perm: 'm:course:courseEditionList',
          title: '教材版本列表',
          icon: 'course_category'
        }
      },
      {
        path: 'courseDictationList',
        component: () => import('@/views/course/courseDictation/courseDictationList'),
        name: 'courseDictationList',
        meta: {
          perm: 'm:course:courseDictationList',
          title: '自然拼写课程列表',
          icon: 'course_category'
        }
      },
      {
        path: 'additionCourseList',
        component: () => import('@/views/course/courseDictation/additionQuestion/additionCourseList'),
        name: 'additionCourseList',
        meta: {
          perm: 'm:course:additionCourseList',
          title: '拼音法课程列表',
          icon: 'course_category'
        }
      },
      {
        path: 'makeAdditionCourseList',
        hidden: true,
        component: () => import('@/views/course/courseDictation/makeCourseList/makeAdditionCourseList'),
        name: 'makeAdditionCourseList',
        meta: {
          perm: 'm:course:makeAdditionCourseList',
          title: '制作课程',
          icon: 'course_category'
        }
      },
      {
        path: 'wordLevelList',
        component: () => import('@/views/course/wordLevelList'),
        name: 'wordLevelList',
        meta: {
          perm: 'm:course:wordLevelList',
          title: '词汇水平列表',
          icon: 'course_test'
        }
      },
      {
        path: 'courseListeningAdd',
        hidden: true,
        component: () => import('@/views/course/courseListeningAdd'),
        name: 'courseListeningAdd',
        meta: {
          perm: 'm:course:courseListeningAdd',
          title: '听力的增加'
        }
      },
      {
        path: 'courseReadingDetails',
        hidden: true,
        component: () => import('@/views/course/courseReadingDetails'),
        name: 'courseReadingDetails',
        meta: {
          perm: 'm:course:courseReadingDetails',
          title: '查看详情'
        }
      },
      {
        path: 'courseListeningUpdate',
        hidden: true,
        component: () => import('@/views/course/courseListeningUpdate'),
        name: 'courseListeningUpdate',
        meta: {
          perm: 'm:course:courseListeningUpdate',
          title: '听力的修改'
        }
      },
      // {
      //   path: 'courseReadingDetails',
      //   hidden: true,
      //   component: () => import('@/views/course/courseReadingDetails'),
      //   name: 'courseReadingDetails',
      //   meta: {
      //     perm:'m:course:courseReadingDetails',
      //     title: '查看详情'
      //   }
      // },
      {
        path: 'courseReadingList',
        hidden: true,
        component: () => import('@/views/course/courseReadingList'),
        name: 'courseReadingList',
        meta: {
          perm: 'm:course:courseReadingList',
          title: '增加/编辑'
        }
      },

      // {
      //   path: 'courseReading',
      //   hidden: true,
      //   component: () => import('@/views/course/courseReading'),
      //   name: 'courseReading',
      //   meta: {
      //     perm:'m:course:courseReading',
      //     title: '阅读理解'
      //   }
      // },
      {
        path: 'wordLevelTestdbList',
        component: () => import('@/views/course/wordLevelTestdbList'),
        name: 'wordLevelTestdbList',
        meta: {
          perm: 'm:course:wordLevelTestdbList',
          title: '单词水平测试题库',
          icon: 'course_level'
        }
      },
      {
        path: 'dictationWordLevel',
        component: () => import('@/views/course/dictationWordLevel'),
        name: 'dictationWordLevel',
        meta: {
          perm: 'm:course:dictationWordLevel',
          title: '听写单词水平列表',
          icon: 'course_category'
        }
      },
      {
        path: 'dictationWordType',
        component: () => import('@/views/course/dictationWordType'),
        name: 'dictationWordType',
        meta: {
          perm: 'm:course:dictationWordType',
          title: '听写单词类型列表',
          icon: 'course_category'
        }
      },
      {
        path: 'dictationWordTest',
        component: () => import('@/views/course/dictationWordTest'),
        name: 'dictationWordTest',
        meta: {
          perm: 'm:course:dictationWordTest',
          title: '听写单词测试题库',
          icon: 'course_category'
        }
      },
      {
        path: 'SpellStrongThesaurus',
        component: () => import('@/views/course/courseDictation/SpellStrongThesaurus'),
        name: 'SpellStrongThesaurus',
        meta: {
          perm: 'm:course:SpellStrongThesaurus',
          title: '拼读强基词库列表',
          icon: 'course_category'
        }
      }
    ]
  },
  {
    path: '/order',
    component: Layout,
    hidden: true,
    redirect: '/order/memberRechargeOrder',
    meta: {
      perm: 'm:order',
      title: '订单管理',
      icon: 'order'
    },
    children: [
      {
        path: 'memberRechargeOrder',
        component: () => import('@/views/order/memberRechargeOrder'),
        name: 'memberRechargeOrder',
        meta: {
          perm: 'm:order:memberRechargeOrder',
          title: '会员充值订单',
          icon: 'member_order'
        }
      },
      {
        path: 'ledgerOrderList',
        component: () => import('@/views/order/ledgerOrderList'),
        name: 'ledgerOrderList',
        meta: {
          perm: 'm:order:ledgerOrderList',
          title: '分账订单列表',
          icon: 'leder_order'
        }
      }
    ]
  },
  {
    path: '/member',
    component: Layout,
    redirect: '/member/memberList',
    meta: {
      perm: 'm:member',
      title: '会员管理',
      icon: 'vip'
    },
    children: [
      {
        path: 'memberList',
        component: () => import('@/views/member/memberList'),
        name: 'memberList',
        meta: {
          perm: 'm:member:memberList',
          title: '会员列表',
          icon: 'member_list'
        }
      },
      {
        path: 'allInOneMember',
        component: () => import('@/views/member/allInOneMember'),
        name: 'allInOneMember',
        meta: {
          perm: 'm:member:allInOneMember',
          title: '通联会员',
          icon: 'allInMember'
        }
      }
    ]
  },
  {
    path: '/student',
    component: Layout,
    redirect: '/student/studentList',
    meta: {
      perm: 'm:student',
      title: '学员管理',
      icon: 'student'
    },
    children: [
      {
        path: 'mathsStudentList',
        component: () => import('@/views/maths/index'),
        name: 'mathsStudentList',
        meta: {
          perm: 'm:student:mathsStudentList',
          title: '学员管理',
          icon: 'studentList'
        }
      },
      {
        path: 'studentList',
        component: () => import('@/views/student/studentList'),
        name: 'studentList',
        meta: {
          perm: 'm:student:studentList',
          title: '学员列表',
          icon: 'studentList'
        }
      },
      {
        path: 'areasStudentCourseList',
        component: () => import('@/views/areas/school/areasStudentCourseList'),
        name: 'areasStudentCourseList',
        meta: {
          perm: 'm:student:areasStudentCourseList',
          title: '学员列表',
          icon: 'studentList'
        }
      },
      {
        path: 'trialClassOrderList',
        component: () => import('@/views/student/trialClassOrderList'),
        name: 'trialClassOrderList',
        meta: {
          perm: 'm:student:trialClassOrderList',
          title: '待完善上课信息表',
          icon: 'student_course_progress'
        }
      },
      // {
      //   path: "reviewSchedule",
      //   component: () => import("@/views/student/reviewSchedule"),
      //   name: "reviewSchedule",
      //   meta: {
      //     perm: "m:student:reviewSchedule",
      //     title: "待完善复习时间表",
      //     icon: "student_course",
      //   },
      // },
      {
        path: 'jiaofufangshi',
        component: () => import('@/views/student/jiaofufangshi'),
        name: 'jiaofufangshi',
        meta: {
          noCache: true,
          // perm: 'm:student:jiaofufangshi',
          title: '集中交付学员',
          icon: 'studentList'
        }
      },
      {
        path: 'teamAuditing',
        component: () => import('@/views/student/teamAuditing'),
        name: 'teamAuditing',
        meta: {
          noCache: true,
          perm: 'm:student:teamAuditing',
          title: '更换交付小组审核',
          icon: 'studentList'
        }
      },
      {
        path: 'deliverClassFlowList',
        component: () => import('@/views/student/deliverClassFlowList'),
        name: 'deliverClassFlowList',
        meta: {
          noCache: true,
          perm: 'm:student:deliverClassFlowList',
          title: '交付学时充值流水',
          icon: 'studentList'
        }
      },
      {
        path: 'shikejilu',
        hidden: true,
        component: () => import('@/views/student/shikejilu'),
        name: '咨询记录表',
        meta: {
          title: '咨询记录表'
        }
      },
      {
        path: 'xueyuanxinxi',
        hidden: true,
        component: () => import('@/views/student/xueyuanxinxi'),
        name: '学员信息表',
        meta: {
          title: '学员信息'
        }
      },
      {
        path: 'areasOpenCourse',
        hidden: true,
        component: () => import('@/views/areas/school/areasOpenCourse'),
        name: 'areasOpenCourse',
        meta: {
          perm: 'm:student:areasOpenCourse',
          title: '开通课程'
        }
      },
      {
        path: 'areasOpenSuperReadCourse',
        hidden: true,
        component: () => import('@/views/areas/school/areasOpenSuperReadCourse'),
        name: 'areasOpenSuperReadCourse',
        meta: {
          perm: 'm:student:areasOpenSuperReadCourse',
          title: '开通超级阅读课程'
        }
      },
      {
        path: 'areasOpenListenCourse',
        hidden: true,
        component: () => import('@/views/areas/school/areasOpenListenCourse'),
        name: 'areasOpenListenCourse',
        meta: {
          perm: 'm:student:areasOpenListenCourse',
          title: '开通全能听力课程'
        }
      },
      {
        path: 'areasStudentCourseRecord',
        component: () => import('@/views/areas/school/areasStudentCourseRecord'),
        name: 'areasStudentCourseRecord',
        meta: {
          perm: 'm:student:areasStudentCourseRecord',
          title: '学员课程记录',
          icon: 'student_course'
        }
      },
      {
        path: 'areasStudentTestResultList',
        component: () => import('@/views/areas/school/areasStudentTestResultList'),
        name: 'areasStudentTestResultList',
        meta: {
          perm: 'm:student:areasStudentTestResultList',
          title: '学员词汇测试',
          icon: 'student_course_test'
        }
      },
      {
        path: 'areaStudentWordReviewPrint',
        component: () => import('@/views/areas/school/areaStudentWordReviewPrint'),
        name: 'areaStudentWordReviewPrint',
        meta: {
          perm: 'm:student:areaStudentWordReviewPrint',
          title: '21天抗遗忘复习计划',
          icon: '21_day'
        }
      },

      {
        path: 'areasStudentWordPrintList',
        component: () => import('@/views/areas/school/areasStudentWordPrintList'),
        name: 'areasStudentWordPrintList',
        meta: {
          perm: 'm:student:areasStudentWordPrintList',
          title: '学员测验打印',
          icon: 'test_print'
        }
      },
      {
        path: 'areasStudentExperience',
        component: () => import('@/views/areas/school/areasStudentExperienceList'),
        name: 'areasStudentExperience',
        meta: {
          perm: 'm:student:areasStudentExperienceList',
          title: '学员试课报告',
          icon: 'student_course'
        }
      },
      {
        path: 'studentExperience',
        hidden: true,
        component: () => import('@/views/student/studentExperience'),
        name: 'studentExperience',
        meta: {
          perm: 'znyy:student:studentExperience',
          title: '学员试课详情',
          icon: 'student_course_test'
        }
      },
      {
        path: 'studentExperienceEdit',
        hidden: true,
        component: () => import('@/views/student/studentExperienceEdit'),
        name: 'studentExperienceEdit',
        meta: {
          title: '学员试课报告编辑',
          icon: 'student_course_test'
        }
      },
      {
        path: 'areasStudentForget',
        component: () => import('@/views/areas/school/areasStudentForgetList'),
        name: 'areasStudentForget',
        meta: {
          perm: 'm:student:areasStudentForgetList',
          title: '21天抗遗忘报告',
          icon: 'student_course'
        }
      },
      {
        path: 'studentForget',
        hidden: true,
        component: () => import('@/views/student/studentForget'),
        name: 'studentForget',
        meta: {
          // perm: 'b:znyy:student:studentForget',
          title: '21天抗遗忘报告打印详情',
          icon: 'student_course_test'
        }
      },
      {
        path: 'areasStudentCourseFlow',
        component: () => import('@/views/areas/school/areasStudentCourseFlow'),
        name: 'areasStudentCourseFlow',
        meta: {
          perm: 'm:student:areasStudentCourseFlow',
          title: '学员销课记录',
          icon: 'student_course_flow'
        }
      },
      /* {
         path: 'areasSchoolList',
         component: () => import('@/views/areas/dealer/areasSchoolList'),
         name: 'areasSchoolList',
         meta: {
           perm:'m:student:areasSchoolList',
           title: 'Areas门店列表'
         }
       },*/
      /* {
         path: 'areasDealerLists',
         component: () => import('@/views/areas/dealer/areasDealerList'),
         name: 'areasDealerLists',
         meta: {
           perm:'m:student:areasDealerList',
           title: 'Areas托管中心列表'
         }
       },*/

      {
        path: 'studentCourseRecord',
        component: () => import('@/views/student/studentCourseRecord'),
        name: 'studentCourseRecord',
        meta: {
          perm: 'm:student:studentCourseRecord',
          title: '学员课程记录',
          icon: 'student_course'
        }
      },
      {
        path: 'studentCourseProgress',
        component: () => import('@/views/student/studentCourseProgress'),
        name: 'studentCourseProgress',
        meta: {
          perm: 'm:student:studentCourseProgress',
          title: '学员课程进度',
          icon: 'student_course_progress'
        }
      },

      {
        path: 'wordBookView',
        component: () => import('@/views/student/wordBookView'),
        name: 'wordBookView',
        meta: {
          perm: 'm:student:wordBookView',
          title: '单词本查看',
          icon: 'word_book'
        }
      },
      {
        path: 'studentWordsTest',
        hidden: true,
        component: () => import('@/views/student/studentWordsTest'),
        name: 'studentWordsTest',
        meta: {
          perm: 'm:student:studentWordsTest',
          title: '学员词汇测试',
          icon: 'student_course_test'
        }
      },
      {
        path: 'studentWordReviewPrint',
        hidden: true,
        component: () => import('@/views/student/studentWordReviewPrint'),
        name: 'studentWordReviewPrint',
        meta: {
          //perm:'m:student:studentWordReviewPrint',
          title: '21天抗遗忘打印'
        }
      },
      {
        path: 'studentWordReviewList',
        hidden: true,
        component: () => import('@/views/student/studentWordReviewList'),
        name: 'studentWordReviewList',
        meta: {
          title: '21天抗遗忘记录'
        }
      },
      {
        path: 'studentWordView',
        hidden: true,
        component: () => import('@/views/student/studentWordViewList'),
        name: 'studentWordViewList',
        meta: {
          title: '查看详情'
        }
      },
      {
        path: 'studentTestPrint',
        hidden: true,
        component: () => import('@/views/student/studentTestPrint'),
        name: 'studentTestPrint',
        meta: {
          perm: 'm:student:studentTestPrint',
          title: '学员测验打印',
          icon: 'student_course_test'
        }
      },
      {
        path: 'studentDayPrint',
        hidden: true,
        component: () => import('@/views/student/studentDayPrint'),
        name: 'studentDayPrint',
        meta: {
          title: '学员测验打印',
          icon: 'student_course_test'
        }
      },
      {
        path: 'studentGraduation',
        hidden: true,
        component: () => import('@/views/student/studentGraduation'),
        name: 'studentGraduation',
        meta: {
          title: '学员测验打印',
          icon: 'student_course_test'
        }
      },
      {
        path: 'studentTestPrintReading',
        hidden: true,
        component: () => import('@/views/student/studentTestPrintReading'),
        name: 'studentTestPrintReading',
        meta: {
          perm: 'm:student:studentTestPrintReading',
          title: '学员测验打印-阅读理解'
        }
      },
      {
        path: 'studentTestPrintReport',
        hidden: true,
        component: () => import('@/views/student/studentTestPrintReport'),
        name: 'studentTestPrintReport',
        meta: {
          perm: 'm:student:studentTestPrintReport',
          title: '学员测验打印-结业报告'
        }
      }
    ]
  },
  {
    path: '/finance',
    component: Layout,
    redirect: '/finance/periodValList',
    meta: {
      perm: 'm:fin',
      title: '财务管理',
      icon: 'money'
    },
    children: [
      // {
      //   path: "CoursependingPaymentList",
      //   hidden: false,
      //   component: () => import("@/views/order/CoursependingPaymentList.vue"),
      //   name: "CoursependingPaymentList",
      //   meta: {
      //     icon: "money",
      //     perm: "m:fin:CoursependingPaymentList",
      //     title: "课程等待付款列表",
      //   },
      // },
      {
        path: 'CoursependingPayment',
        hidden: false,
        component: () => import('@/views/order/CoursependingPaymentList.vue'),
        name: 'CoursependingPaymentList',
        meta: {
          icon: 'money',
          perm: 'm:fin:coursependingPayment',
          title: '课程等待付款列表'
        }
      },
      {
        path: 'rechargeCourseList',
        hidden: false,
        component: () => import('@/views/order/rechargeCourseIndex.vue'),
        name: 'rechargeCourseList',
        meta: {
          icon: 'money',
          perm: 'm:fin:merchantCourseByList',
          title: '课程购买列表'
        }
      },
      {
        path: 'refundCourseList',
        hidden: false,
        component: () => import('@/views/order/refundCourseIndex'),
        name: 'refundCourseList',
        meta: {
          icon: 'money',
          perm: 'm:fin:merchantCourseRefundList',
          title: '退课列表'
        }
      },
      {
        path: 'refundCourseLista',
        hidden: false,
        component: () => import('@/views/order/refundCourseIndexa'),
        name: 'refundCourseLista',
        meta: {
          icon: 'refund',
          perm: 'm:fin:financeInforM',
          title: '门店退课列表'
        }
      },
      // {
      //   path: 'line_collect',
      //   children: [
      //     {
      //       path: 'http://192.168.5.110:8001/jumpindex?token=${token}',
      //       meta: { title: '线上收款', icon: 'link' }
      //     }
      //   ]
      // },
      {
        path: 'memberFundStatement',
        component: () => import('@/views/finance/memberFundStatement'),
        name: 'memberFundStatement',
        meta: {
          perm: 'm:fin:memberFundStatement',
          title: '会员资金流水',
          icon: 'member_flow'
        }
      },
      {
        path: 'financeInforM',
        component: () => import('@/views/finance/financeInforM'),
        name: 'financeInforM',
        meta: {
          perm: 'm:fin:financeInforM',
          title: '门店交付订单',
          icon: 'member_flow'
        }
      },
      {
        path: 'marketProfiAccountFlows',
        component: () => import('@/views/finance/marketProfiAccountFlows'),
        name: 'marketProfiAccountFlows',
        meta: {
          perm: 'm:fin:marketProfiAccountFlows',
          title: '推荐人分润流水',
          icon: 'profit_flow'
        }
      },
      {
        path: 'onlineRecharge',
        component: () => import('@/views/finance/onlineRecharge'),
        name: 'onlineRecharge',
        meta: {
          perm: 'm:fin:onlineRecharge',
          title: '在线充值',
          icon: 'alllline_charge'
        }
      },
      {
        path: 'onlineCompanyRecharge',
        component: () => import('@/views/finance/onlineCompanyRecharge'),
        name: 'onlineCompanyRecharge',
        meta: {
          perm: 'm:fin:onlineCompanyRecharge',
          title: '分公司在线充值',
          icon: 'alllline_charge'
        }
      },

      {
        path: 'onlineCharge',
        component: () => import('@/views/finance/onlineCharge'),
        name: 'onlineCharge',
        meta: {
          perm: 'm:fin:onlineCharge',
          title: '在线扣款',
          icon: 'online_charge'
        }
      },
      {
        path: 'onlineCompanyCharge',
        component: () => import('@/views/finance/onlineCompanyCharge'),
        name: 'onlineCompanyCharge',
        meta: {
          perm: 'm:fin:onlineCompanyCharge',
          title: '分公司在线扣款',
          icon: 'online_charge'
        }
      },
      {
        path: 'merchantFlowList',
        component: () => import('@/views/finance/merchantFlowList'),
        name: 'merchantFlowList',
        meta: {
          perm: 'm:fin:merchantFlowList',
          title: '商户资金流水',
          icon: 'market_flow'
        }
      },
      {
        path: 'merchantFlowCourseList',
        component: () => import('@/views/finance/merchantFlowCourseList'),
        name: 'merchantFlowCourseList',
        meta: {
          perm: 'm:fin:merchantFlowCourseList',
          title: '商户资金学时流水',
          icon: 'market_flow'
        }
      },
      {
        path: 'investmentRecruitmentDetailsList',
        component: () => import('@/views/finance/investmentRecruitmentDetailsList'),
        name: 'merchantFlowList',
        meta: {
          perm: 'm:fin:investmentRecruitmentDetailsList',
          title: '招商招生明细',
          icon: 'market_flow'
        }
      },
      {
        path: 'merchantFlowListDealer',
        component: () => import('@/views/finance/merchantFlowListDealer'),
        name: 'merchantFlowListDealer',
        meta: {
          perm: 'm:fin:merchantFlowListDealer',
          title: '托管中心充值资金流水',
          icon: 'market_flow'
        }
      },
      {
        path: 'coursePackageProfit',
        component: () => import('@/views/finance/coursePackageProfit'),
        name: 'coursePackageProfit',
        meta: {
          // perm:'m:fin:coursePackageProfit',
          title: '收益',
          icon: 'market_flow'
        }
      },
      {
        path: 'schoolFlowList',
        component: () => import('@/views/finance/schoolFlowList'),
        name: 'schoolFlowList',
        meta: {
          perm: 'm:fin:schoolFlowList',
          title: '门店学时变动明细',
          icon: 'school_course_flow'
        }
      },
      {
        path: 'schoolFlowPackageList',
        component: () => import('@/views/finance/schoolFlowPackageList'),
        name: 'schoolFlowPackageList',
        meta: {
          perm: 'm:fin:schoolFlowPackageList',
          title: '课程包变动明细',
          icon: 'school_course_flow'
        }
      },
      {
        path: 'assistantFlowList',
        component: () => import('@/views/finance/assistantFlowList'),
        name: 'assistantFlowList',
        meta: {
          perm: 'm:fin:assistantFlowList',
          title: '学员课程变动明细',
          icon: 'student_course_flow'
        }
      },
      {
        path: 'offsiteDepositOrder',
        component: () => import('@/views/finance/offsiteDepositOrder'),
        name: 'offsiteDepositOrder',
        meta: {
          perm: 'm:fin:offsiteDepositOrder',
          title: '异地定金订单管理',
          icon: 'yi_order'
        }
      },
      {
        path: 'pendingPayList',
        component: () => import('@/views/finance/pendingPayList'),
        name: 'pendingPayList',
        meta: {
          perm: 'm:fin:transferPendingPay',
          title: '资转待支付列表',
          icon: 'member_flow'
        }
      },
      // {
      //   path: 'systemConfiguration',
      //   component: () => import('@/views/finance/systemConfiguration'),
      //   name: 'systemConfiguration',
      //   meta: {
      //     perm: 'm:fin:systemConfiguration',
      //     title: '系统配置',
      //     icon: 'member_flow'
      //   }
      // }
      {
        path: 'storeRevenue',
        // hidden: true,
        component: () => import('@/views/finance/storeRevenue'),
        name: 'storeRevenue',
        meta: {
          perm: 'm:fin:storeRevenue',
          title: '门店收益',
          icon: 'dealer'
        }
      }
    ]
  },

  {
    path: '/interest',
    component: Layout,
    redirect: '/interest/levelConfig',
    meta: {
      perm: 'm:interest',
      title: '趣味复习',
      icon: 'leve'
    },
    children: [
      {
        path: 'levelConfig',
        component: () => import('@/views/interest/levelConfig'),
        name: 'levelConfig',
        meta: {
          perm: 'm:interest:levelConfig',
          title: '关数配置',
          icon: 'level_config'
        }
      },
      {
        path: 'scoreConfig',
        component: () => import('@/views/interest/scoreConfig'),
        name: 'scoreConfig',
        meta: {
          perm: 'm:interest:scoreConfig',
          title: '等级配置',
          icon: 'score_config'
        }
      }
    ]
  },

  {
    path: '/broadcast',
    component: Layout,
    hidden: true,
    redirect: '/broadcast/broadcast',
    meta: {
      perm: 'm:broadcast',
      title: '录播系统',
      icon: 'lubo'
    },
    children: [
      {
        path: 'broadcast',
        component: () => import('@/views/broadcast/broadcast'),
        name: 'broadcast',
        meta: {
          perm: 'm:broadcast:broadcast',
          title: '录播管理',
          icon: 'broadcast'
        }
      },
      {
        path: 'banner',
        component: () => import('@/views/broadcast/banner'),
        name: 'banner',
        meta: {
          perm: 'm:broadcast:banner',
          title: '横幅配置',
          icon: 'banner'
        }
      }
    ]
  },
  {
    path: '/grammar',
    component: Layout,
    redirect: '/grammar/grammarQuestion',
    meta: {
      perm: 'm:grammar',
      title: '语法管理',
      icon: 'grammer'
    },
    children: [
      {
        path: 'grammarPoint',
        component: () => import('@/views/grammar/grammarPoint'),
        name: 'grammarPoint',
        meta: {
          perm: 'm:grammar:grammarPoint',
          title: '语法点管理',
          icon: 'grammar'
        }
      },
      {
        path: 'knowledgePoint',
        component: () => import('@/views/grammar/knowledgePoint'),
        name: 'knowledgePoint',
        meta: {
          perm: 'm:grammar:knowledgePoint',
          title: '知识点管理',
          icon: 'knowledge'
        }
      },
      {
        path: 'grammarQuestion',
        component: () => import('@/views/grammar/grammarQuestion'),
        name: 'grammarQuestion',
        meta: {
          perm: 'm:grammar:grammarQuestion',
          title: '题库管理',
          icon: 'grammar_question'
        }
      },
      {
        path: 'grammarConfig',
        component: () => import('@/views/grammar/grammarConfig'),
        name: 'grammarConfig',
        meta: {
          perm: 'm:grammar:grammarConfig',
          title: '语法点配置',
          icon: 'grammar'
        }
      },
      {
        path: 'questionConfig',
        component: () => import('@/views/grammar/questionConfig'),
        name: 'questionConfig',
        meta: {
          perm: 'm:grammar:questionConfig',
          title: '题目配置',
          icon: 'grammar_question'
        }
      },
      {
        path: 'studyRateConfig',
        component: () => import('@/views/grammar/studyRateConfig'),
        name: 'studyRateConfig',
        meta: {
          perm: 'm:grammar:studyRateConfig',
          title: '语法掌握度配置',
          icon: 'grammar'
        }
      },
      {
        path: 'grammarPwdConfig',
        component: () => import('@/views/grammar/grammarPwdConfig'),
        name: 'grammarPwdConfig',
        meta: {
          perm: 'm:grammar:grammarPwdConfig',
          title: '教练 授权码配置',
          icon: 'grammar_pwd_config'
        }
      },
      {
        path: 'grammarConsumeHours',
        component: () => import('@/views/grammar/grammarConsumeHours'),
        name: 'grammarConsumeHours',
        meta: {
          perm: 'm:grammar:grammarConsumeHours',
          title: '语法包消费学时配置',
          icon: 'grammar'
        }
      },

      {
        path: 'grammarStudent',
        component: () => import('@/views/grammar/grammarStudent'),
        name: 'grammarStudent',
        meta: {
          perm: 'm:grammar:grammarStudent',
          title: '开通语法学员列表',
          icon: 'student_grammar'
        }
      },
      {
        path: 'grammarMessage',
        component: () => import('@/views/grammar/grammarMessage'),
        name: 'grammarMessage',
        meta: {
          perm: 'm:grammar:grammarMessage',
          title: '学员寄语列表',
          icon: 'hand_out'
        }
      },
      {
        path: 'phaseCredentialList',
        component: () => import('@/views/grammar/phaseCredentialList'),
        name: 'phaseCredentialList',
        meta: {
          perm: 'm:grammar:phaseCredentialList',
          title: '学员结业证书打印列表',
          icon: 'student_course_print'
        }
      },
      {
        path: 'phaseCreadentialPrint',
        hidden: true,
        component: () => import('@/views/grammar/phaseCreadentialPrint'),
        name: 'phaseCreadentialPrint',
        meta: {
          perm: 'm:grammar:phaseCreadentialPrint',
          title: '学员结业证书打印列表-打印',
          icon: 'student_course_print'
        }
      },
      {
        path: 'handoutsPrintList',
        component: () => import('@/views/grammar/handoutsPrintList'),
        name: 'handoutsPrintList',
        meta: {
          perm: 'm:grammar:handoutsPrintList',
          title: '学员课件打印列表',
          icon: 'student_course_print'
        }
      },
      {
        path: 'studentHandoutsPrint',
        hidden: true,
        component: () => import('@/views/grammar/studentHandoutsPrint'),
        name: 'studentHandoutsPrint',
        meta: {
          perm: 'm:grammar:studentHandoutsPrint',
          title: '学员课件打印列表-打印',
          icon: 'student_course_print'
        }
      }
    ]
  },
  // 新语法项目
  {
    path: '/syntax',
    component: Layout,
    redirect: '/syntax/grammarQuestion',
    meta: {
      perm: 'm:syntax',
      title: '新版语法管理',
      icon: 'grammer'
    },
    children: [
      {
        path: 'grammarPoint',
        component: () => import('@/views/syntax/grammarPoint'),
        name: 'syntaxPoint',
        meta: {
          perm: 'm:syntax:grammarPoint',
          title: '语法点管理',
          icon: 'grammar'
        }
      },
      {
        path: 'knowledgePoint',
        component: () => import('@/views/syntax/knowledgePoint'),
        name: 'syntaxknowledgePoint',
        meta: {
          perm: 'm:syntax:knowledgePoint',
          title: '知识点管理',
          icon: 'knowledge'
        }
      },
      {
        path: 'prePostTestList',
        component: () => import('@/views/syntax/prePostTestList'),
        name: 'syntaxprePostTestList',
        meta: {
          perm: 'm:syntax:prePostTestList',
          title: '课前课后试题列表',
          icon: 'grammar_question'
        }
      },
      {
        path: 'grammarQuestion',
        component: () => import('@/views/syntax/grammarQuestion'),
        name: 'syntaxQuestion',
        meta: {
          perm: 'm:syntax:grammarQuestion',
          title: '知识点题库',
          icon: 'grammar_question'
        }
      },
      {
        path: 'grammarQuestionGra',
        component: () => import('@/views/syntax/grammarQuestionGra'),
        name: 'syntaxQuestion',
        meta: {
          perm: 'm:syntax:grammarQuestionGra',
          title: '语法点和结业检测题库',
          icon: 'grammar_question'
        }
      },
      {
        path: 'questionConfig',
        component: () => import('@/views/syntax/questionConfig'),
        name: 'syntaxConfig',
        meta: {
          perm: 'm:syntax:questionConfig',
          title: '题目配置',
          icon: 'grammar_question'
        }
      },
      {
        path: 'studyRateConfig',
        component: () => import('@/views/syntax/studyRateConfig'),
        name: 'syntaxRateConfig',
        meta: {
          perm: 'm:syntax:studyRateConfig',
          title: '语法掌握度配置',
          icon: 'grammar'
        }
      },
      {
        path: 'phaseCredentialList',
        component: () => import('@/views/syntax/phaseCredentialList'),
        name: 'syntaxphaseCredentialList',
        meta: {
          perm: 'm:grammar:phaseCredentialList',
          title: '学员结业证书打印列表',
          icon: 'student_course_print'
        }
      },
      {
        path: 'phaseCreadentialPrint',
        hidden: true,
        component: () => import('@/views/syntax/phaseCreadentialPrint'),
        name: 'syntaxphaseCreadentialPrint',
        meta: {
          perm: 'm:grammar:phaseCreadentialPrint',
          title: '学员结业证书打印列表-打印',
          icon: 'student_course_print'
        }
      },
      {
        path: 'handoutsPrintList',
        component: () => import('@/views/syntax/handoutsPrintList'),
        name: 'handoutsPrintList',
        meta: {
          perm: 'm:grammar:handoutsPrintList',
          title: '学员课件打印列表',
          icon: 'student_course_print'
        }
      },
      {
        path: 'studentHandoutsPrint',
        hidden: true,
        component: () => import('@/views/syntax/studentHandoutsPrint'),
        name: 'studentHandoutsPrint',
        meta: {
          perm: 'm:grammar:studentHandoutsPrint',
          title: '学员课件打印列表-打印',
          icon: 'student_course_print'
        }
      }
    ]
  },
  {
    path: '/recommendationChain',
    component: Layout,
    redirect: '/area/recommendationChain/recommendationChain',
    meta: {
      perm: 'm:recommendation:recommendationChain',
      title: '完整推荐链',
      icon: 'dealer_rank'
    },
    children: [
      {
        path: 'recommendationChain',
        component: () => import('@/views/areas/recommendationChain/recommendationChain'),
        name: 'recommendationChain',
        meta: {
          perm: 'm:recommendation:recommendationChain',
          title: '完整推荐链',
          icon: 'dealer_rank'
        }
      }
    ]
  },
  {
    path: '/system',
    component: Layout,
    redirect: '/system/appVersionControl',
    meta: {
      perm: 'm:system',
      title: '系统管理',
      icon: 'system'
    },
    children: [
      {
        path: 'appVersionControl',
        component: () => import('@/views/system/appVersionControl'),
        name: 'appVersionControl',
        meta: {
          perm: 'm:system:appVersionControl',
          title: 'App版本控制',
          icon: 'app_version'
        }
      },
      {
        path: 'systemConfiguration',
        component: _import('system/systemConfiguration'),
        name: 'systemConfiguration',
        meta: {
          perm: 'm:system:systemConfiguration',
          title: '系统配置',
          icon: 'system_figuration'
        }
      },
      {
        path: 'offServiceTimeManagement',
        component: _import('system/offServiceTimeManagement'),
        name: 'offServiceTimeManagement',
        meta: {
          perm: 'm:system:offServiceTimeManagement',
          title: '停服时间管理',
          icon: 'system_figuration'
        }
      },
      {
        path: 'cityCourseConfiguration',
        component: _import('system/cityCourseConfiguration'),
        name: 'cityCourseConfiguration',
        meta: {
          perm: 'm:system:cityCourseConfiguration',
          title: '学时价格配置',
          icon: 'system_figuration'
        }
      },
      {
        path: 'link',
        hidden: true,
        component: _import('system/link'),
        name: 'link',
        meta: {
          title: '通联云链接',
          perm: 'm:link:system/link'
        }
      },
      {
        path: 'deliverSetting',
        component: _import('system/deliverSetting'),
        name: 'deliverSetting',
        meta: {
          perm: 'm:system:deliverSetting',
          title: '交付中心配置',
          icon: 'system_figuration'
        }
      },
      {
        path: 'updatePssword',
        component: _import('system/updatePssword'),
        name: 'updatePssword',
        meta: {
          perm: 'm:system:updatePssword',
          title: '修改密码',
          icon: 'update_pwd'
        }
      },
      {
        path: 'authentication',
        component: _import('system/authentication'),
        name: 'authentication',
        meta: {
          perm: 'm:system:authentication',
          title: '实名认证',
          icon: 'authentication'
        }
      },
      // {
      //   path: "relieveauthentication",
      //   component: _import("system/relieveAuthentication"),
      //   name: "relieveauthentication",
      //   meta: {
      //     perm: "m:system:relieveauthentication",
      //     title: "实名认证解绑",
      //     icon: "authentication",
      //   },
      // },
      {
        path: 'experienceLevelList',
        component: _import('system/experienceLevelList'),
        name: 'experienceLevelList',
        meta: {
          perm: 'm:system:experienceLevelList',
          title: '托管中心级别列表',
          icon: 'dealer_rank'
        }
      },
      {
        path: 'authorizationCodeExport',
        component: _import('system/authorizationCodeExport'),
        name: 'authorizationCodeExport',
        meta: {
          perm: 'm:system:authorizationCodeExport',
          title: '授权码列表',
          icon: 'authorization_code'
        }
      },
      {
        path: 'businessDistrictCode',
        component: _import('system/businessDistrictCode'),
        name: 'businessDistrictCode',
        meta: {
          perm: 'm:system:businessDistrictCode',
          title: '商圈码列表',
          icon: 'busitcode'
        }
      },
      {
        path: 'divisionRankList',
        component: _import('system/divisionRankList'),
        name: 'divisionRankList',
        meta: {
          perm: 'm:system:divisionRankList',
          title: '事业部级别列表',
          icon: 'dealer_rank'
        }
      },
      {
        path: 'divisionProfitRankList',
        component: _import('system/divisionProfitRankList'),
        name: 'divisionProfitRankList',
        meta: {
          perm: 'm:system:divisionProfitRankList',
          title: '事业部推荐反润比例列表',
          icon: 'dealer_rank'
        }
      },
      {
        path: 'merProfitConfig',
        component: () => import('@/views/system/merProfitConfig'),
        name: 'merProfitConfig',
        meta: {
          perm: 'm:merProfit:config:list',
          title: '渠道分润比例配置',
          icon: 'dealer_rank'
        }
      },
      {
        path: 'riskAuthCode',
        component: () => import('@/views/system/riskAuthCodeList'),
        name: 'riskAuthCode',
        meta: {
          perm: 'm:system:riskAuthCodeList',
          title: '风控授权码列表',
          icon: 'dealer_rank'
        }
      },
      {
        path: 'userOptionLog',
        component: () => import('@/views/system/userOptionLog'),
        name: 'userOptionLog',
        meta: {
          perm: 'm:system:userOptionLog',
          title: '用户操作日志',
          icon: 'authorization_code'
        }
      },
      {
        path: 'deliverConfig',
        component: () => import('@/views/system/deliverConfig'),
        name: 'deliverConfig',
        meta: {
          perm: 'm:system:deliverConfig',
          title: '会员交付方式',
          icon: 'authorization_code'
        }
      }
    ]
  },
  {
    path: '/risk',
    component: Layout,
    meta: {
      perm: 'm:risk:list',
      redirect: '/risk/index',
      title: '监管体系',
      icon: 'risk'
    },
    children: [
      {
        path: '/riskList1',
        component: () => import('@/views/risk/index'),
        name: 'riskList1',
        meta: {
          perm: 'm:risk:list:low',
          title: '低危列表',
          icon: 'risk2'
        }
      },
      {
        path: '/riskList2',
        component: () => import('@/views/risk/index'),
        name: 'riskList2',
        meta: {
          perm: 'm:risk:list:middle',
          title: '中危列表',
          icon: 'risk2'
        }
      },
      {
        path: '/riskList3',
        component: () => import('@/views/risk/index'),
        name: 'riskList3',
        meta: {
          perm: 'm:risk:list:high',
          title: '高危列表',
          icon: 'risk2'
        }
      },
      {
        path: '/riskWhiteList',
        component: () => import('@/views/risk/riskWhiteList'),
        name: 'riskWhiteList',
        meta: {
          perm: 'm:riskWhiteList:list',
          title: '风控白名单',
          icon: 'whiteList'
        }
      }
    ]
  },
  {
    path: '/suggest',
    component: Layout,
    meta: {
      perm: 'm:suggest:suggest',
      redirect: '/suggest/suggest',
      title: '用户意见反馈',
      icon: 'course_category'
    },
    children: [
      {
        path: 'suggest',
        component: () => import('@/views/suggest/suggest'),
        name: 'suggest',
        meta: {
          perm: 'm:suggest:suggest',
          title: '用户意见反馈',
          icon: 'course_category'
        }
      }
    ]
  },
  {
    path: '/broadcast2',
    component: Layout,
    redirect: '/broadcast/broadcastCode',
    meta: {
      perm: 'm:purchaseCode:broadcastCode',
      title: '趣味复习',
      icon: 'buycode'
    },
    children: [
      {
        path: 'broadcastCode',
        component: () => import('@/views/broadcast/broadcastCode'),
        name: 'broadcastCode',
        meta: {
          perm: 'm:purchaseCode:broadcastCode',
          title: '趣味复习购买码',
          icon: 'buycode'
        }
      }
    ]
  },
  //会议管理
  {
    path: '/meeting',
    component: Layout,
    redirect: '/dxt/meeting/meeting',
    meta: {
      perm: 'm:meeting',

      title: '会议管理',
      icon: 'meeting'
    },
    children: [
      {
        path: 'meeting',
        component: () => import('@/views/dxt/meeting/meeting'),
        name: 'meeting',
        meta: {
          perm: 'm:meeting:meeting',

          title: '会议列表',
          icon: 'meetingList'
        }
      },
      {
        path: 'presenterList',
        component: () => import('@/views/dxt/presenter/presenterList'),
        name: 'presenterList',
        meta: {
          perm: 'm:meeting:presenterList',

          title: '主讲老师列表',
          icon: 'presenterList'
        }
      },
      {
        path: 'meetingInvitationList',
        component: () => import('@/views/dxt/meetinginvitation/meetingInvitationList'),
        name: 'meetingInvitationList',
        meta: {
          perm: 'm:meeting:meetingInvitationList',
          title: '邀请语列表',
          icon: 'invitationList'
        }
      },
      {
        path: 'feedBackList',
        component: () => import('@/views/dxt/feedback/feedBackList'),
        name: 'feedBackList',
        meta: {
          perm: 'm:meeting:feedBackList',
          title: '反馈意见列表',
          icon: 'feed_back'
        }
      },
      {
        path: 'meetingCode',
        component: () => import('@/views/dxt/meetingcode/meetingCode'),
        name: 'meetingCode',
        meta: {
          perm: 'm:meeting:meetingCode',
          title: '物料码列表',
          icon: 'meeting_code'
        }
      },
      {
        path: 'referreJoinMeetingList',
        component: () => import('@/views/dxt/referrer/referreJoinMeetingList'),
        name: 'referreJoinMeetingList',
        meta: {
          perm: 'm:meeting:referreJoinMeetingList',
          title: '推荐会议人员',
          icon: 'referee'
        }
      },
      {
        path: 'meetingOrder',
        component: () => import('@/views/dxt/meetingorder/meetingOrder'),
        name: 'meetingOrder',
        meta: {
          perm: 'm:meeting:meetingOrder',

          title: '会议订单',
          icon: 'meeting_order'
        }
      },
      {
        path: 'flow',
        component: () => import('@/views/dxt/flow/flow'),
        name: 'flow',
        meta: {
          perm: 'm:meeting:flow',
          title: '会议流水',
          icon: 'market_flow'
        }
      }
    ]
  },
  //自习室 start
  {
    path: '/schedule',
    component: Layout,
    redirect: '/studyroom/schedule',
    meta: {
      perm: 'm:schedule',
      title: '自习室管理',
      icon: 'busitcode'
    },
    children: [
      {
        path: 'gradeList',
        component: () => import('@/views/studyroom/schedule/gradeList'),
        name: 'gradeList',
        meta: {
          perm: 'm:schedule:gradeList',
          title: '规划列表',
          icon: 'meetingList'
        }
      },
      {
        path: 'scheduleRoomView/:scheduleId',
        hidden: true,
        component: () => import('@/views/studyroom/schedule/scheduleRoomView'),
        name: 'scheduleRoomView',
        meta: {
          perm: 'm:schedule:scheduleRoomView',
          title: '规划房间详情',
          icon: 'meetingList'
        }
      },
      {
        path: 'studentList',
        hidden: false,
        component: () => import('@/views/studyroom/schedule/studentList'),
        name: 'studentList',
        meta: {
          perm: 'm:schedule:studentList',
          title: '人员列表',
          icon: 'meetingList'
        }
      },
      {
        path: 'committeemanList',
        component: () => import('@/views/studyroom/committeeman/committeemanList'),
        name: 'committeemanList',
        meta: {
          perm: 'm:schedule:committeemanList',
          title: '学委列表',
          icon: 'meetingList'
        }
      },
      {
        path: 'unFixSchedule',
        component: () => import('@/views/studyroom/schedule/unFixSchedule'),
        name: 'unFixSchedule',
        meta: {
          perm: 'm:schedule:unFixSchedule',
          title: '非固定共享班',
          icon: 'meetingList'
        }
      },
      {
        path: 'unFixScheduleRoom/:scheduleId',
        hidden: true,
        component: () => import('@/views/studyroom/schedule/unFixScheduleRoomView'),
        name: 'unFixScheduleRoom',
        meta: {
          perm: 'm:schedule:unFixScheduleRoom',
          title: '非固定班房间详情',
          icon: 'meetingList'
        }
      },

      {
        path: 'target',
        component: () => import('@/views/studyroom/schedule/targetList'),
        name: 'targetList',
        meta: {
          perm: 'm:schedule:targetList',
          title: '自学目标',
          icon: 'meetingList'
        }
      },
      {
        path: 'planList',
        hidden: true,
        component: () => import('@/views/studyroom/scheduleplan/planList'),
        name: 'planList',
        meta: {
          perm: 'm:schedule:planList',
          title: '计划列表',
          icon: 'meetingList'
        }
      },
      {
        path: 'timerList',
        hidden: true,
        component: () => import('@/views/studyroom/timer/TimerList'),
        name: 'timerList',
        meta: {
          perm: 'm:schedule:timerList',
          title: '计时器列表',
          icon: 'meetingList'
        }
      },
      {
        path: 'sceneList',
        component: () => import('@/views/studyroom/scene/sceneInfo'),
        name: 'sceneList',
        meta: {
          perm: 'm:schedule:sceneList',
          title: '场景列表',
          icon: 'meetingList'
        }
      },
      {
        path: 'trtcRoom/:id',
        hidden: true,
        component: () => import('@/views/studyroom/trtcRoom'),
        name: 'trtcRoom',
        meta: {
          perm: 'm:studyroom:trtcRoom',
          title: '自习室管理',
          icon: 'zhibo'
        }
      },
      {
        path: 'selfstudyRoom/:id',
        hidden: true,
        component: () => import('@/views/studyroom/selfstudyRoom'),
        name: 'selfstudyRoom',
        meta: {
          perm: 'm:studyroom:selfstudyRoom',
          title: '自习室管理',
          icon: 'zhibo'
        }
      },
      {
        path: 'fixTrtcRoom/:scheduleId',
        hidden: true,
        component: () => import('@/views/studyroom/fixTrtcRoom'),
        name: 'fixTrtcRoom',
        meta: {
          perm: 'm:studyroom:fixTrtcRoom',
          title: '自习室管理',
          icon: 'zhibo'
        }
      },
      {
        path: 'roomList',
        component: () => import('@/views/studyroom/room/list'),
        name: 'roomList',
        meta: {
          perm: 'm:studyroom:roomList',
          title: '房间列表',
          icon: 'zhibo'
        }
      },
      {
        path: 'studycommitte',
        component: () => import('@/views/studyroom/studycommitteed/index'),
        name: 'studycommitte',
        meta: {
          perm: 'm:studyroom:studycommitte',
          title: '学委管理',
          icon: 'zhibo'
        }
      },
      {
        path: 'voiceAnswers',
        component: () => import('@/views/studyroom/voice-answers/index'),
        name: 'voiceAnswers',
        meta: {
          perm: 'm:studyroom:voiceAnswers',
          title: '语音问答',
          icon: 'zhibo'
        }
      },
      {
        path: 'answersContent',
        hidden: true,
        component: () => import('@/views/studyroom/voice-answers/content'),
        name: 'answersContent',
        meta: {
          perm: 'm:schedule:answersContent',
          title: '问答内容',
          icon: 'meetingList'
        }
      },
      {
        path: 'spokenText',
        component: () => import('@/views/studyroom/spoken-test/index'),
        name: 'spokenText',
        meta: {
          perm: 'm:schedule:spokenText',
          title: '口语测评',
          icon: 'zhibo'
        }
      },
      {
        path: 'spokenType',
        hidden: true,
        component: () => import('@/views/studyroom/spoken-test/type'),
        name: 'spokenType',
        meta: {
          perm: 'm:schedule:spokenType',
          title: '口语类型',
          icon: 'meetingList'
        }
      }
    ]
  },
  //自习室 end
  {
    path: '/feedback',
    component: Layout,
    redirect: '/studyroom/feedback',
    meta: {
      perm: 'm:feedback',
      title: '自习室意见反馈',
      icon: 'buycode'
    },
    children: [
      {
        path: 'index',
        component: () => import('@/views/studyroom/feedback/index'),
        name: 'feedback',
        meta: {
          perm: 'm:feedback:index',
          title: '自习室意见反馈',
          icon: 'zhibo'
        }
      }
    ]
  },
  //渠道管理
  {
    path: '/channel',
    component: Layout,
    redirect: '/channel',
    meta: {
      perm: 'm:channel',
      title: '渠道管理',
      icon: 'meeting'
    },
    children: [
      {
        path: 'index',
        component: () => import('@/views/channel/channelManager'),
        name: 'channelManager',
        meta: {
          perm: 'm:channel:channelManager',
          title: '渠道管理员列表',
          icon: 'meeting'
        }
      },
      {
        path: 'index',
        hidden: true,
        component: () => import('@/views/channel/channelManager'),
        name: 'channelManager',
        meta: {
          perm: 'm:channel:channelManager',
          title: '渠道管理员列表',
          icon: 'meeting'
        }
      }
    ]
  },
  //activiti start
  {
    path: '/activiti',
    hidden: true,
    component: Layout,
    redirect: '/activiti',
    meta: {
      perm: 'm:activiti',
      title: '工作流',
      icon: 'cascader'
    },
    children: [
      {
        path: 'definition',
        component: () => import('@/views/activiti/definition/index'),
        name: 'definition',
        meta: {
          perm: 'm:activiti:definition',
          title: '流程定义'
        }
      },
      {
        path: 'myTask',
        component: () => import('@/views/activiti/mytask'),
        name: 'myTask',
        meta: {
          perm: 'm:activiti:myTask',
          title: '我的代办'
        }
      }
    ]
  },

  {
    path: '/online',
    component: Layout,
    hidden: false,
    redirect: '/online',
    meta: {
      perm: 'm:online',
      title: '在线表单',
      icon: 'busitcode'
    },
    children: [
      {
        path: 'onlineForm',
        hidden: false,
        component: () => import('@/views/activiti/onlineForm/index'),
        name: 'onlineForm',
        meta: {
          perm: 'm:online:onlineForm',
          title: '在线表单'
        }
      },
      {
        path: 'onlineDict',
        component: () => import('@/views/activiti/onlineForm/formOnlineDict/index'),
        name: 'onlineDict',
        meta: {
          perm: 'm:online:onlineDict',
          title: '字典管理'
        }
      },
      {
        path: 'page',
        component: () => import('@/views/activiti/onlineForm/onlinePage/index'),
        name: 'onlinePage',
        meta: {
          perm: 'm:online:page',
          title: '表单管理'
        }
      }
    ]
  },
  {
    path: '/workFlow',
    component: Layout,
    redirect: '/workFlow',
    meta: {
      perm: 'm:workFlow',
      title: '流程管理',
      icon: 'busitcode'
    },
    children: [
      {
        path: 'flowCategory',
        component: () => import('@/views/activiti/workFlow/flowCategory/formFlowCategory'),
        name: 'flowCategory',
        meta: {
          perm: 'm:workFlow:flowCategory',
          title: '流程分类'
        }
      },
      {
        path: 'flowEntry',
        component: () => import('@/views/activiti/workFlow/flowEntry/formFlowEntry'),
        name: 'flowEntry',
        meta: {
          perm: 'm:workFlow:flowEntry',
          title: '流程设计'
        }
      },
      {
        path: 'flowInstance',
        component: () => import('@/views/activiti/workFlow/taskManager/formAllInstance'),
        name: 'flowInstance',
        meta: {
          perm: 'm:workFlow:flowInstance',
          title: '流程实例'
        }
      }
    ]
  },
  {
    path: '/taskManager',
    component: Layout,
    redirect: 'taskManager',
    meta: {
      perm: 'm:taskManager',
      title: '任务管理',
      icon: 'busitcode'
    },
    children: [
      {
        path: 'myTask',
        component: () => import('@/views/activiti/workFlow/taskManager/formMyTask'),
        name: 'myTask',
        meta: {
          perm: 'm:taskManager:myTask',
          title: '待办任务'
        }
      },
      {
        path: 'handlerFlowTask',
        hidden: true,
        component: () => import('@/views/activiti/workFlow/handlerFlowTask/index'),
        name: 'handlerFlowTask',
        meta: {
          perm: 'm:taskManager:handlerFlowTask',
          title: '任务办理'
        }
      },
      {
        path: 'myHistoryTask',
        component: () => import('@/views/activiti/workFlow/taskManager/formMyHistoryTask'),
        name: 'myHistoryTask',
        meta: {
          perm: 'm:taskManager:myHistoryTask',
          title: '历史任务'
        }
      },
      {
        path: 'myApprovedTask',
        component: () => import('@/views/activiti/workFlow/taskManager/formMyApprovedTask'),
        name: 'myApprovedTask',
        meta: {
          perm: 'm:taskManager:myApprovedTask',
          title: '已办任务'
        }
      }
    ]
  },

  //activiti end
  {
    path: '/mpsRefund',
    component: Layout,
    redirect: '/mps/refund',
    meta: {
      perm: 'm:refund',
      title: 'mps订单退款',
      icon: 'buycode'
    },
    children: [
      {
        path: 'index',
        component: () => import('@/views/mps/refund'),
        name: 'feedback',
        meta: {
          perm: 'm:refund:index',
          title: 'mps订单退款',
          icon: 'zhibo'
        }
      }
    ]
  },
  //自习室 end
  //试卷系统 start
  {
    path: '/paper',
    component: Layout,
    redirect: '/paper/question',
    meta: {
      perm: 'm:paper',
      title: '试卷系统',
      icon: 'buycode'
    },
    children: [
      {
        path: 'index',
        component: () => import('@/views/paper/question/index'),
        name: 'question',
        meta: { perm: 'm:paper:question', title: '评估题目', icon: 'zhibo' }
      },
      {
        path: 'singleChoice',
        component: () => import('@/views/paper/question/edit/single-choice'),
        name: 'singleChoicePage',
        meta: {
          title: '单选题编辑',
          noCache: true,
          activeMenu: '/exam/question/list'
        },
        hidden: true
      },
      {
        path: 'multipleChoice',
        component: () => import('@/views/paper/question/edit/multiple-choice'),
        name: 'multipleChoicePage',
        meta: {
          title: '多选题编辑',
          noCache: true,
          activeMenu: '/exam/question/list'
        },
        hidden: true
      },
      {
        path: 'trueFalse',
        component: () => import('@/views/paper/question/edit/true-false'),
        name: 'trueFalsePage',
        meta: {
          title: '判断题编辑',
          noCache: true,
          activeMenu: '/exam/question/list'
        },
        hidden: true
      },
      {
        path: 'gapFilling',
        component: () => import('@/views/paper/question/edit/gap-filling'),
        name: 'gapFillingPage',
        meta: {
          title: '填空题编辑',
          noCache: true,
          activeMenu: '/exam/question/list'
        },
        hidden: true
      },
      {
        path: 'shortAnswer',
        component: () => import('@/views/paper/question/edit/short-answer'),
        name: 'shortAnswerPage',
        meta: {
          title: '简答题编辑',
          noCache: true,
          activeMenu: '/exam/question/list'
        },
        hidden: true
      },
      {
        path: 'combination',
        component: () => import('@/views/paper/question/edit/combination'),
        name: 'combinationrPage',
        meta: {
          title: '组合题编辑',
          noCache: true,
          activeMenu: '/exam/question/list'
        },
        hidden: true
      },
      {
        path: 'strMemory',
        component: () => import('@/views/paper/question/edit/str-memory'),
        name: 'strMemory',
        meta: {
          title: '字符串记忆题',
          noCache: true,
          activeMenu: '/exam/question/list'
        },
        hidden: true
      },
      {
        path: 'wordMemory',
        component: () => import('@/views/paper/question/edit/word-memory'),
        name: 'wordMemory',
        meta: {
          title: '词语记忆题',
          noCache: true,
          activeMenu: '/exam/question/list'
        },
        hidden: true
      },
      {
        path: 'matchMove',
        component: () => import('@/views/paper/question/edit/match-move'),
        name: 'matchMove',
        meta: {
          title: '火柴移动题',
          noCache: true,
          activeMenu: '/exam/question/list'
        },
        hidden: true
      },
      {
        path: 'iconMove',
        component: () => import('@/views/paper/question/edit/icon-move'),
        name: 'iconMove',
        meta: {
          title: '硬币移动题',
          noCache: true,
          activeMenu: '/exam/question/list'
        },
        hidden: true
      },
      {
        path: 'clue',
        component: () => import('@/views/paper/question/edit/clue'),
        name: 'clue',
        meta: {
          title: '线索题',
          noCache: true,
          activeMenu: '/exam/question/list'
        },
        hidden: true
      },
      {
        path: 'grid',
        component: () => import('@/views/paper/question/edit/grid'),
        name: 'grid',
        meta: {
          title: '跳格子题',
          noCache: true,
          activeMenu: '/exam/question/list'
        },
        hidden: true
      },
      {
        path: 'audioFill',
        component: () => import('@/views/paper/question/edit/audio-fill'),
        name: 'audioFill',
        meta: {
          title: '音频填空题',
          noCache: true,
          activeMenu: '/exam/question/list'
        },
        hidden: true
      },
      {
        path: 'textRepeat',
        component: () => import('@/views/paper/question/edit/text-repeat'),
        name: 'textRepeat',
        meta: {
          title: '文字找重题',
          noCache: true,
          activeMenu: '/exam/question/list'
        },
        hidden: true
      },
      {
        path: 'card',
        component: () => import('@/views/paper/question/edit/card'),
        name: 'card',
        meta: {
          title: '答题卡题',
          noCache: true,
          activeMenu: '/exam/question/list'
        },
        hidden: true
      },
      {
        hidden: true,
        path: 'articleMemory',
        component: () => import('@/views/paper/question/edit/article-memory'),
        name: 'articleMemory',
        meta: { title: '物品记忆题', noCache: true }
      },
      {
        hidden: true,
        path: 'imageAnswer',
        component: () => import('@/views/paper/question/edit/image-answer'),
        name: 'imageAnswer',
        meta: { title: '图片问答题', noCache: true }
      },
      {
        hidden: true,
        path: 'listenAnswer',
        component: () => import('@/views/paper/question/edit/listen-answer'),
        name: 'listenAnswer',
        meta: { title: '听力问答题', noCache: true }
      },
      {
        path: 'schoolSection',
        component: () => import('@/views/paper/grade/index'),
        name: 'schoolSection',
        meta: {
          perm: 'm:paper:schoolSection',
          title: '学段管理',
          icon: 'zhibo'
        }
      },
      {
        path: 'subject',
        component: () => import('@/views/paper/subject/index'),
        name: 'subject',
        meta: { perm: 'm:paper:subject', title: '维度管理', icon: 'zhibo' }
      },
      {
        path: 'abilityReport',
        component: () => import('@/views/paper/abilityReport/index'),
        name: 'abilityReport',
        meta: {
          perm: 'm:paper:abilityReport',
          title: '报告管理',
          icon: 'zhibo'
        }
      },
      {
        path: 'abilityReportSave',
        component: () => import('@/views/paper/abilityReport/save'),
        name: 'abilityReportSave',
        meta: {
          perm: 'm:paper:abilityReportSave',
          title: '报告新增/修改',
          icon: 'zhibo'
        },
        hidden: true
      },
      {
        path: 'assessmentReport',
        component: () => import('@/views/paper/assessmentReport/index'),
        name: 'assessmentReport',
        meta: {
          perm: 'm:paper:assessmentReport',
          title: '评估课报告管理',
          icon: 'zhibo'
        }
      },
      {
        path: 'assessmentReportSave',
        component: () => import('@/views/paper/assessmentReport/assessmentSave'),
        name: 'assessmentReportSave',
        meta: {
          perm: 'm:paper:assessmentReportSave',
          title: '评估报告新增/修改',
          icon: 'zhibo'
        },
        hidden: true
      },
      {
        path: 'knowledge',
        component: () => import('@/views/paper/knowledge/index'),
        name: 'knowledge',
        meta: { perm: 'm:paper:knowledge', title: '知识点管理', icon: 'zhibo' }
      },
      {
        path: 'course',
        component: () => import('@/views/paper/course/index'),
        name: 'course',
        meta: { perm: 'm:paper:course', title: '课程管理', icon: 'zhibo' }
      },
      {
        path: 'courseQuestion',
        component: () => import('@/views/paper/course/question'),
        name: 'courseQuestion',
        meta: {
          perm: 'm:paper:courseQuestion',
          title: '问题列表',
          icon: 'zhibo'
        },
        hidden: true
      }
      /*{
        path: 'paperList',
        component: () => import('@/views/paper/paper/index'),
        name: 'paperList',
        meta: { perm: 'm:paper:paperList', title: '试卷列表', icon: 'zhibo' }
      },
      {
        path: 'paperEdit',
        component: () => import('@/views/paper/paper/edit'),
        name: 'paperEdit',
        meta: { perm: 'm:paper:paperEdit', title: '试卷编辑', icon: 'zhibo' },
        hidden: true
      },
      {
        path: 'paperView',
        component: () => import('@/views/paper/paper/view'),
        name: 'paperView',
        meta: { perm: 'm:paper:paperView', title: '试卷查看', icon: 'zhibo' },
        hidden: true
      },
      {
        path: 'random',
        component: () => import('@/views/paper/paper/random'),
        name: 'paperRandom',
        meta: { perm: 'm:paper:paperRandom', title: '智能组卷', icon: 'zhibo' }
      },
      {
        path: 'complex',
        component: () => import('@/views/paper/complex/img-text-index'),
        name: 'complex',
        meta: { perm: 'm:paper:complex', title: '图文复杂题', icon: 'zhibo' }
      },
      {
        hidden: true,
        path: 'imgTextDrag',
        component: () => import('@/views/paper/complex/img-text/img-text-drag'),
        name: 'imgTextDrag',
        meta: { title: '图文拖动题', noCache: true }
      },
      {
        hidden: true,
        path: 'imgTextRepeat',
        component: () => import('@/views/paper/complex/img-text/img-text-repeat'),
        name: 'imgTextRepeat',
        meta: { title: '图文找重题', noCache: true }
      },
      {
        hidden: true,
        path: 'imgTextMark',
        component: () => import('@/views/paper/complex/img-text/img-text-mark-repeat'),
        name: 'imgTextMark',
        meta: { title: '图文标记找重题', noCache: true }
      },
      {
        hidden: true,
        path: 'imgTextFilling',
        component: () => import('@/views/paper/complex/img-text/img-text-filling'),
        name: 'imgTextFilling',
        meta: { title: '图文填空题', noCache: true }
      },
      {
        hidden: true,
        path: 'imgTextSerialCalculate',
        component: () => import('@/views/paper/complex/img-text/img-text-serial-calculate'),
        name: 'imgTextSerialCalculate',
        meta: { title: '图文连续计算题', noCache: true }
      },
      {
        hidden: true,
        path: 'imgTextMarkNumber',
        component: () => import('@/views/paper/complex/img-text/img-text-mark-number'),
        name: 'imgTextMarkNumber',
        meta: { title: '图文标序题', noCache: true }
      },
      {
        hidden: true,
        path: 'imgTextSerialMarkNumber',
        component: () => import('@/views/paper/complex/img-text/img-text-serial-mark-number'),
        name: 'imgTextSerialMarkNumber',
        meta: { title: '图文连续标序题', noCache: true }
      },
      {
        path: 'audio',
        component: () => import('@/views/paper/complex/audio-index'),
        name: 'audio',
        meta: { perm: 'm:paper:audio', title: '音频复杂题', icon: 'zhibo' }
      },
      {
        hidden: true,
        path: 'audioRepeat',
        component: () => import('@/views/paper/complex/audio/audio-repeat'),
        name: 'audioRepeat',
        meta: { title: '音频找重题', noCache: true }
      },
      {
        hidden: true,
        path: 'audioListen',
        component: () => import('@/views/paper/complex/audio/audio-listen'),
        name: 'audioListen',
        meta: { title: '音频听识题', noCache: true }
      },
      {
        hidden: true,
        path: 'audioSpell',
        component: () => import('@/views/paper/complex/audio/audio-spell'),
        name: 'audioSpell',
        meta: { title: '音频拼组题', noCache: true }
      },
      {
        hidden: true,
        path: 'audioListenTrans',
        component: () => import('@/views/paper/complex/audio/audio-listen-trans'),
        name: 'audioListenTrans',
        meta: { title: '音频听译题', noCache: true }
      },
      {
        path: 'cardIndex',
        component: () => import('@/views/paper/complex/card-index'),
        name: 'cardIndex',
        meta: { perm: 'm:paper:audio', title: '答题卡复杂题', icon: 'zhibo' }
      },
      {
        hidden: true,
        path: 'card',
        component: () => import('@/views/paper/complex/card/card'),
        name: 'card',
        meta: { title: '答题卡题', noCache: true }
      },
      {
        path: 'report',
        component: () => import('@/views/paper/report/index'),
        name: 'report',
        meta: { perm: 'm:paper:report', title: '结果报告', icon: 'zhibo' }
      },
      {
        hidden: true,
        path: 'scoreReport',
        component: () => import('@/views/paper/report/score-report'),
        name: 'scoreReport',
        meta: { title: '分数型', noCache: true }
      },
      {
        hidden: true,
        path: 'knowledgeReport',
        component: () => import('@/views/paper/report/knowledge-report'),
        name: 'knowledgeReport',
        meta: { title: '知识点型', noCache: true }
      }*/
    ]
  },
  //试卷系统 end
  {
    path: '/train',
    component: Layout,
    redirect: '/train/question',
    meta: {
      perm: 'm:train',
      title: '训练管理',
      icon: 'buycode'
    },
    children: [
      {
        path: 'category',
        component: () => import('@/views/paper/train/category/index'),
        name: 'category',
        meta: { perm: 'm:train:category', title: '维度管理', icon: 'zhibo' }
      },
      {
        path: 'guide',
        component: () => import('@/views/paper/train/guide/index'),
        name: 'guide',
        meta: { perm: 'm:train:guide', title: '引导管理', icon: 'zhibo' }
      },
      {
        path: 'guideData',
        hidden: true,
        component: () => import('@/views/paper/train/guide/data'),
        name: 'guideData',
        meta: { title: '引导问答设置', icon: 'zhibo' }
      },
      {
        path: 'difficulty',
        component: () => import('@/views/paper/train/difficulty/index'),
        name: 'difficulty',
        meta: { perm: 'm:train:difficulty', title: '难度管理', icon: 'zhibo' }
      },
      {
        path: 'trainImage',
        component: () => import('@/views/paper/train/image/index'),
        name: 'trainImage',
        meta: { perm: 'm:train:trainImage', title: '题库图片', icon: 'zhibo' }
      },
      {
        path: 'auditory',
        component: () => import('@/views/paper/train/question/auditory/index'),
        name: 'auditory',
        meta: { perm: 'm:train:auditory', title: '听力题', icon: 'zhibo' }
      },
      {
        path: 'auditoryFollow',
        hidden: true,
        component: () => import('@/views/paper/train/question/auditory/follow'),
        name: 'auditoryFollow',
        meta: {
          perm: 'm:train:auditoryFollow',
          title: '跟读题',
          icon: 'zhibo'
        }
      },
      {
        path: 'auditoryHandle',
        hidden: true,
        component: () => import('@/views/paper/train/question/auditory/handle'),
        name: 'auditoryHandle',
        meta: {
          perm: 'm:train:auditoryHandle',
          title: '听力动手题',
          icon: 'zhibo'
        }
      },
      {
        path: 'auditoryFilling',
        hidden: true,
        component: () => import('@/views/paper/train/question/auditory/filling'),
        name: 'auditoryFilling',
        meta: {
          perm: 'm:train:auditoryFilling',
          title: '听力填空题',
          icon: 'zhibo'
        }
      },
      {
        path: 'auditoryWordFilling',
        hidden: true,
        component: () => import('@/views/paper/train/question/auditory/audio_filling'),
        name: 'auditoryWordFilling',
        meta: {
          perm: 'm:train:auditoryWordFilling',
          title: '音频文本填空题',
          icon: 'zhibo'
        }
      },
      {
        path: 'auditoryWordFillingNew',
        hidden: true,
        component: () => import('@/views/paper/train/question/auditory/audio_filling_new'),
        name: 'auditoryWordFillingNew',
        meta: {
          perm: 'm:train:auditoryWordFillingNew',
          title: '新音频文本填空题',
          icon: 'zhibo'
        }
      },
      {
        path: 'auditoryDistinguish',
        hidden: true,
        component: () => import('@/views/paper/train/question/auditory/distinguish'),
        name: 'auditoryDistinguish',
        meta: {
          perm: 'm:train:auditoryDistinguish',
          title: '听力辨别题',
          icon: 'zhibo'
        }
      },
      {
        path: 'auditoryLeak',
        hidden: true,
        component: () => import('@/views/paper/train/question/auditory/leak'),
        name: 'auditoryLeak',
        meta: {
          perm: 'm:train:auditoryLeak',
          title: '听力漏读题',
          icon: 'zhibo'
        }
      },
      {
        path: 'auditoryAnswer',
        hidden: true,
        component: () => import('@/views/paper/train/question/auditory/answer'),
        name: 'auditoryAnswer',
        meta: {
          perm: 'm:train:auditoryAnswer',
          title: '听力回答题',
          icon: 'zhibo'
        }
      },
      {
        path: 'auditoryAnswerNew',
        hidden: true,
        component: () => import('@/views/paper/train/question/auditory/answer_new'),
        name: 'auditoryAnswerNew',
        meta: {
          perm: 'm:train:auditoryAnswerNew',
          title: '新听力回答题',
          icon: 'zhibo'
        }
      },
      {
        path: 'auditoryRepeat',
        hidden: true,
        component: () => import('@/views/paper/train/question/auditory/repeat'),
        name: 'visualRepeat',
        meta: { perm: 'm:train:visualRepeat', title: '找重题', icon: 'zhibo' }
      },
      {
        path: 'visual',
        component: () => import('@/views/paper/train/question/visual/index'),
        name: 'visual',
        meta: { perm: 'm:train:visual', title: '视觉题', icon: 'zhibo' }
      },
      {
        path: 'visualDifferent',
        hidden: true,
        component: () => import('@/views/paper/train/question/visual/different'),
        name: 'visualDifferent',
        meta: {
          perm: 'm:train:visualDifferent',
          title: '数字找不同题',
          icon: 'zhibo'
        }
      },
      {
        path: 'visualDifferentNew',
        hidden: true,
        component: () => import('@/views/paper/train/question/visual/different_new'),
        name: 'visualDifferentNew',
        meta: {
          perm: 'm:train:visualDifferentNew',
          title: '新数字找不同题',
          icon: 'zhibo'
        }
      },
      {
        path: 'visualPattern',
        hidden: true,
        component: () => import('@/views/paper/train/question/visual/pattern'),
        name: 'visualPattern',
        meta: {
          perm: 'm:train:visualLeak',
          title: '找图形个数题',
          icon: 'zhibo'
        }
      },
      {
        path: 'visualDistinguish',
        hidden: true,
        component: () => import('@/views/paper/train/question/visual/distinguish'),
        name: 'visualDistinguish',
        meta: {
          perm: 'm:train:visualDistinguish',
          title: '视觉分辨题',
          icon: 'zhibo'
        }
      },
      {
        path: 'visualLeak',
        hidden: true,
        component: () => import('@/views/paper/train/question/visual/leak'),
        name: 'visualLeak',
        meta: {
          perm: 'm:train:visualLeak',
          title: '方格正序题',
          icon: 'zhibo'
        }
      },
      {
        path: 'visualLeakNew',
        hidden: true,
        component: () => import('@/views/paper/train/question/visual/leak_new'),
        name: 'visualLeakNew',
        meta: {
          perm: 'm:train:visualLeakNew',
          title: '新方格正序题',
          icon: 'zhibo'
        }
      },
      {
        path: 'visualChoice',
        hidden: true,
        component: () => import('@/views/paper/train/question/visual/choice'),
        name: 'visualChoice',
        meta: {
          perm: 'm:train:visualChoice',
          title: '图片找不同题',
          icon: 'zhibo'
        }
      },
      {
        path: 'visualFillLeak',
        hidden: true,
        component: () => import('@/views/paper/train/question/visual/fillLeak'),
        name: 'visualFillLeak',
        meta: {
          perm: 'm:train:visualFillLeak',
          title: '填漏题',
          icon: 'zhibo'
        }
      },
      {
        path: 'visualRead',
        hidden: true,
        component: () => import('@/views/paper/train/question/visual/read'),
        name: 'visualRead',
        meta: { perm: 'm:train:visualRead', title: '朗读题', icon: 'zhibo' }
      },
      {
        path: 'visualReadNew',
        hidden: true,
        component: () => import('@/views/paper/train/question/visual/read_new'),
        name: 'visualReadNew',
        meta: { perm: 'm:train:visualReadNew', title: '新朗读题', icon: 'zhibo' }
      },
      {
        path: 'visualPicMemory',
        hidden: true,
        component: () => import('@/views/paper/train/question/visual/picMemory'),
        name: 'visualPicMemory',
        meta: {
          perm: 'm:train:visualPicMemory',
          title: '图片记忆题',
          icon: 'zhibo'
        }
      },
      {
        path: 'visualWordMemory',
        hidden: true,
        component: () => import('@/views/paper/train/question/visual/wordMemory'),
        name: 'visualWordMemory',
        meta: {
          perm: 'm:train:visualWordMemory',
          title: '单词记忆题',
          icon: 'zhibo'
        }
      },
      {
        path: 'visualNumMemory',
        hidden: true,
        component: () => import('@/views/paper/train/question/visual/numMemory'),
        name: 'visualNumMemory',
        meta: {
          perm: 'm:train:visualNumMemory',
          title: '数字记忆题',
          icon: 'zhibo'
        }
      },
      {
        path: 'visualFindWord',
        hidden: true,
        component: () => import('@/views/paper/train/question/visual/findWord'),
        name: 'visualFindWord',
        meta: {
          perm: 'm:train:visualFindWord',
          title: '圈数字题',
          icon: 'zhibo'
        }
      },
      {
        path: 'visualFindWordNew',
        hidden: true,
        component: () => import('@/views/paper/train/question/visual/findWord_new'),
        name: 'visualFindWordNew',
        meta: {
          perm: 'm:train:visualFindWordNew',
          title: '新圈数字题',
          icon: 'zhibo'
        }
      },
      {
        path: 'visualAntiInterference',
        hidden: true,
        component: () => import('@/views/paper/train/question/visual/anti_interference'),
        name: 'visualAntiInterference',
        meta: {
          perm: 'm:train:visualAntiInterference',
          title: '视觉转移题',
          icon: 'zhibo'
        }
      },
      {
        path: 'visualTextRepeat',
        hidden: true,
        component: () => import('@/views/paper/train/question/visual/textRepeat'),
        name: 'visualTextRepeat',
        meta: {
          perm: 'm:train:visualTextRepeat',
          title: '文字找重题',
          icon: 'zhibo'
        }
      },
      {
        path: 'visualTextRepeatNew',
        hidden: true,
        component: () => import('@/views/paper/train/question/visual/textRepeat_new'),
        name: 'visualTexvisualTextRepeatNewtRepeat',
        meta: {
          perm: 'm:train:visualTextRepeatNew',
          title: '新文字找重题',
          icon: 'zhibo'
        }
      },
      {
        path: 'visualPictureGrid',
        hidden: true,
        component: () => import('@/views/paper/train/question/visual/pictureGrid'),
        name: 'visualPictureGrid',
        meta: {
          perm: 'm:train:visualPictureGrid',
          title: '图片方格题',
          icon: 'zhibo'
        }
      },
      {
        path: 'visualFeatureGraphics',
        hidden: true,
        component: () => import('@/views/paper/train/question/visual/featureGraphics'),
        name: 'visualFeatureGraphics',
        meta: {
          perm: 'm:train:visualFeatureGraphics',
          title: '特征找图形题',
          icon: 'zhibo'
        }
      },
      {
        path: 'visualFeatureGraphicsNew',
        hidden: true,
        component: () => import('@/views/paper/train/question/visual/featureGraphics_new'),
        name: 'visualFeatureGraphicsNew',
        meta: {
          perm: 'm:train:visualFeatureGraphicsNew',
          title: '新特征找图形题',
          icon: 'zhibo'
        }
      },
      {
        path: 'visualGraphicMatching',
        hidden: true,
        component: () => import('@/views/paper/train/question/visual/graphicMatching'),
        name: 'visualGraphicMatching',
        meta: {
          perm: 'm:train:visualGraphicMatching',
          title: '图形匹配题',
          icon: 'zhibo'
        }
      },
      {
        path: 'upload',
        component: () => import('@/views/paper/train/question/upload/index'),
        name: 'upload',
        meta: { perm: 'm:train:upload', title: '拍照上传题', icon: 'zhibo' }
      },
      {
        path: 'uploadCopy',
        hidden: true,
        component: () => import('@/views/paper/train/question/upload/copy'),
        name: 'uploadCopy',
        meta: {
          perm: 'm:train:uploadCopy',
          title: '内容抄写题',
          icon: 'zhibo'
        }
      },
      {
        path: 'uploadDrawing',
        hidden: true,
        component: () => import('@/views/paper/train/question/upload/drawing'),
        name: 'uploadDrawing',
        meta: {
          perm: 'm:train:uploadDrawing',
          title: '看图画画题',
          icon: 'zhibo'
        }
      },
      {
        path: 'step',
        component: () => import('@/views/paper/train/question/visual/step'),
        name: 'step',
        meta: { perm: 'm:train:step', title: '题型步骤方法', icon: 'zhibo' }
      },
      {
        path: 'lookWordsCreateSentences',
        hidden: true,
        component: () => import('@/views/paper/train/question/visual/lookWordsCreateSentences'),
        name: 'lookWordsCreateSentences',
        meta: { perm: 'm:train:lookWordsCreateSentences', title: '看字组句题', icon: 'zhibo' }
      },
      {
        path: 'findIdenticalWordExamples',
        hidden: true,
        component: () => import('@/views/paper/train/question/visual/findIdenticalWordExamples'),
        name: 'findIdenticalWordExamples',
        meta: { perm: 'm:train:findIdenticalWordExamples', title: '找相同字例题', icon: 'zhibo' }
      },
      {
        path: 'stare',
        component: () => import('@/views/paper/train/question/stare/index'),
        name: 'stare',
        meta: { perm: 'm:train:stare', title: '课外练习', icon: 'zhibo' }
      },
      {
        path: 'starePoint',
        component: () => import('@/views/paper/train/question/stare/point'),
        name: 'starePoint',
        hidden: true,
        meta: { perm: 'm:train:starePoint', title: '盯点练习', icon: 'zhibo' }
      },
      {
        path: 'stareMiniGame',
        component: () => import('@/views/paper/train/question/stare/miniGame'),
        name: 'stareMiniGame',
        hidden: true,
        meta: { perm: 'm:train:stareMiniGame', title: '课前小游戏', icon: 'zhibo' }
      },
      {
        path: 'stareVideo',
        component: () => import('@/views/paper/train/question/stare/video'),
        name: 'stareVideo',
        hidden: true,
        meta: { perm: 'm:train:stareVideo', title: '课前视频', icon: 'zhibo' }
      },
      {
        path: 'stareFingerCalc',
        component: () => import('@/views/paper/train/question/stare/fingerCalc'),
        name: 'stareFingerCalc',
        hidden: true,
        meta: { perm: 'm:train:stareFingerCalc', title: '手指计算题', icon: 'zhibo' }
      },
      {
        path: 'course',
        component: () => import('@/views/paper/train/course/index'),
        name: 'course',
        meta: { perm: 'm:train:course', title: '课程管理', icon: 'zhibo' }
      },
      {
        path: 'pass',
        hidden: true,
        component: () => import('@/views/paper/train/course/pass'),
        name: 'pass',
        meta: { perm: 'm:train:pass', title: '关卡管理', icon: 'zhibo' }
      },
      {
        path: 'question',
        hidden: true,
        component: () => import('@/views/paper/train/course/question'),
        name: 'question',
        meta: { perm: 'm:train:question', title: '关卡问题', icon: 'zhibo' }
      },
      {
        path: 'trainAfterCourse',
        component: () => import('@/views/paper/train-after-class/question-bank.vue'),
        name: 'trainAfterCourse',
        meta: {
          perm: 'm:train:trainAfterCourse',
          title: '一课一练课程',
          icon: 'zhibo'
        }
      },
      {
        path: 'learnmaster',
        component: () => import('@/views/paper/train/learnmaster/index'),
        name: 'learnmaster',
        meta: {
          perm: 'm:learnmaster:list',
          title: '学能师管理',
          icon: 'zhibo'
        }
      },
      {
        path: 'learnmasterStudents',
        component: () => import('@/views/paper/train/learnmaster/students'),
        hidden: true,
        name: 'learnmasterStudents',
        meta: {
          perm: 'm:learnmaster:students',
          title: '学员管理',
          icon: 'zhibo'
        }
      }
    ]
  },
  {
    path: '/_aaa_demo',
    component: Layout,
    redirect: '/_aaa_demo',
    meta: {
      perm: 'm:aaademo',
      title: '课程平台',
      icon: 'shop'
    },
    children: [
      // 鼎数学--课程管理
      {
        path: 'courseManagementList',
        component: () => import('@/views/courseManagement/courseManagementList.vue'),
        name: 'courseManagementList',
        meta: {
          perm: 'm:aaademo:courseManagementList',
          title: '课程管理',
          icon: 'course'
        }
      },
      {
        path: 'speech',
        hidden: true,
        component: () => import('@/views/courseManagement/speech.vue'),
        name: 'speech',
        meta: {
          perm: 'm:aaademo:speech',
          title: '话术',
          icon: 'course'
        }
      },
      {
        path: 'addCourse',
        hidden: true,
        component: () => import('@/views/courseManagement/addCourse.vue'),
        name: 'addCourse',
        meta: {
          perm: 'm:aaademo:addCourse',
          title: '新增/编辑课程',
          icon: 'course'
        }
      },
      {
        path: 'mathsVedioManage',
        component: () => import('@/views/mathsVedioManage/videoManagment.vue'),
        name: 'vedioManage',
        meta: {
          perm: 'm:aaademo:videoManagment',
          title: '视频管理',
          icon: 'course'
        }
      },
      {
        path: 'editVideoNodes',
        component: () => import('@/views/mathsVedioManage/editVideoNodes.vue'),
        name: 'editVideoNodes',
        hidden: true,
        meta: {
          perm: 'm:aaademo:editVideoNodes',
          title: '编辑视频节点',
          icon: 'course'
        }
      },
      {
        path: 'addVideo',
        hidden: true,
        component: () => import('@/views/mathsVedioManage/addVideo.vue'),
        name: 'addVideo',
        meta: {
          perm: 'm:aaademo:addVideo',
          title: '新增/编辑视频',
          icon: 'course'
        }
      },
      {
        path: 'topicManagement',
        component: () => import('@/views/maths/questionBankManagement/topicManagement'),
        name: 'topicManagement',
        meta: {
          perm: 'm:aaademo:topicManagement',
          title: '题库管理',
          icon: 'post',
          noCache: true
        }
      },
      {
        path: 'addQuestion',
        hidden: true,
        component: () => import('@/views/maths/questionBankManagement/addQuestion'),
        name: 'addQuestion',
        meta: {
          perm: 'm:aaademo:addQuestion',
          title: '新增/编辑题目'
        }
      },
      {
        path: 'knowledgeManagement',
        hidden: false,
        component: _import('_aaa_demo/knowledgeManagement/index'),
        name: 'knowledgeManagement',
        meta: {
          perm: 'm:aaademo:knowledgeManagement',
          title: '知识点管理',
          icon: 'agent'
        }
      },
      {
        path: 'testPaperManagement',
        hidden: false,
        component: _import('_aaa_demo/testPaperManagement/index'),
        name: 'testPaperManagement',
        meta: {
          perm: 'm:aaademo:testPaperManagement',
          title: '试卷管理',
          icon: 'agent'
        }
      },
      {
        path: 'addPaper',
        hidden: true,
        component: _import('_aaa_demo/testPaperManagement/addPaper'),
        name: 'addPaper',
        meta: {
          perm: 'm:aaademo:addPaper',
          title: '新增/编辑试卷',
          icon: 'agent'
        }
      },
      {
        path: 'studentManagement',
        hidden: false,
        component: _import('_aaa_demo/studentManagement/index'),
        name: 'studentManagement',
        meta: {
          perm: 'm:aaademo:studentManagement',
          title: '学生管理',
          icon: 'agent'
        }
      },
      {
        path: 'courseTypeConfig',
        hidden: false,
        component: _import('_aaa_demo/courseTypeConfig/index'),
        name: 'courseTypeConfig',
        meta: {
          perm: 'm:aaademo:courseTypeConfig',
          title: '课程分类配置',
          icon: 'agent'
        }
      },
      {
        path: 'topicOfClassConfig',
        hidden: false,
        component: _import('_aaa_demo/topicOfClassConfig/index'),
        name: 'topicOfClassConfig',
        meta: {
          perm: 'm:aaademo:topicOfClassConfig',
          title: '班型题目配置',
          icon: 'agent'
        }
      }
    ]
  },
  ///珠心算 start
  {
    path: '/abacusMentalCalc',
    component: Layout,
    redirect: '/abacusMentalCalc',
    meta: {
      perm: 'm:abacusMentalCalc',
      title: '珠心算',
      icon: 'post'
    },
    children: [
      {
        path: 'courseIndex',
        component: () => import('@/views/abacusMentalCalc/course_index'),
        name: 'courseIndex',
        meta: {
          perm: 'm:abacusMentalCalc:courseIndex',
          title: '课程管理',
          icon: 'post'
        }
      },
      {
        path: 'questionIndex',
        component: () => import('@/views/abacusMentalCalc/question_index'),
        name: 'questionIndex',
        meta: {
          perm: 'm:abacusMentalCalc:questionIndex',
          title: '题型管理',
          icon: 'post'
        }
      },
      {
        path: 'categoryIndex',
        component: () => import('@/views/abacusMentalCalc/category_index'),
        name: 'categoryIndex',
        meta: {
          perm: 'm:abacusMentalCalc:categoryIndex',
          title: '维度管理',
          icon: 'post'
        }
      },
      {
        path: 'reportIndex',
        component: () => import('@/views/abacusMentalCalc/report_index'),
        name: 'reportIndex',
        meta: {
          perm: 'm:abacusMentalCalc:reportIndex',
          title: '报告管理',
          icon: 'post'
        }
      },
      {
        path: 'report',
        component: () => import('@/views/abacusMentalCalc/report'),
        name: 'report',
        hidden: true,
        meta: {
          perm: 'm:abacusMentalCalc:report',
          title: '报告维度编辑',
          icon: 'post'
        }
      },
      {
        path: 'question',
        component: () => import('@/views/abacusMentalCalc/question'),
        name: 'question',
        hidden: true,
        meta: {
          perm: 'm:abacusMentalCalc:question',
          title: '课程题目',
          icon: 'post'
        }
      },
      {
        path: 'subQuestion',
        component: () => import('@/views/abacusMentalCalc/subQuestion'),
        name: 'subQuestion',
        hidden: true,
        meta: {
          perm: 'm:abacusMentalCalc:subQuestion',
          title: '附加题型',
          icon: 'post'
        }
      },
      {
        path: 'onlyQuestion',
        component: () => import('@/views/abacusMentalCalc/question/only_question.vue'),
        name: 'onlyQuestion',
        hidden: true,
        meta: { perm: 'm:abacusMentalCalc:onlyQuestion', title: '算式题', icon: 'post' }
      },
      {
        path: 'audioAnswer',
        component: () => import('@/views/abacusMentalCalc/question/audioAnswer.vue'),
        name: 'audioAnswer',
        hidden: true,
        meta: { perm: 'm:abacusMentalCalc:audioAnswer', title: '音频答案题', icon: 'post' }
      },
      {
        path: 'onlyScore',
        component: () => import('@/views/abacusMentalCalc/question/only_score.vue'),
        name: 'onlyScore',
        hidden: true,
        meta: { perm: 'm:abacusMentalCalc:onlyScore', title: '听写题', icon: 'post' }
      },
      {
        path: 'videoAudio',
        component: () => import('@/views/abacusMentalCalc/question/video_audio.vue'),
        name: 'videoAudio',
        hidden: true,
        meta: { perm: 'm:abacusMentalCalc:videoAudio', title: '音视频题', icon: 'post' }
      },
      {
        path: 'questionAnswer',
        component: () => import('@/views/abacusMentalCalc/question/question_answer.vue'),
        name: 'questionAnswer',
        hidden: true,
        meta: { perm: 'm:abacusMentalCalc:questionAnswer', title: '算式答案题', icon: 'post' }
      },
      {
        path: 'countdown',
        component: () => import('@/views/abacusMentalCalc/question/countdown.vue'),
        name: 'countdown',
        hidden: true,
        meta: { perm: 'm:abacusMentalCalc:countdown', title: '倒计时题', icon: 'post' }
      },
      {
        path: 'duiBuShuNum',
        component: () => import('@/views/abacusMentalCalc/question/duibushu_num.vue'),
        name: 'duiBuShuNum',
        hidden: true,
        meta: { perm: 'm:abacusMentalCalc:duiBuShuNum', title: '对补数数字', icon: 'post' }
      },
      {
        path: 'duiBuShuImg',
        component: () => import('@/views/abacusMentalCalc/question/duibushu_img.vue'),
        name: 'duiBuShuImg',
        hidden: true,
        meta: { perm: 'm:abacusMentalCalc:duiBuShuImg', title: '对补数图片', icon: 'post' }
      }
    ]
  },
  ///珠心算 end

  // 习 start
  {
    path: '/xi',
    component: Layout,
    redirect: '/xi',
    meta: {
      perm: 'm:xi',
      title: '习功能管理',
      icon: 'buycode'
    },
    children: [
      {
        path: 'task',
        component: () => import('@/views/xi/task/index'),
        name: 'task',
        meta: { perm: 'm:xi:task', title: '任务管理', icon: 'zhibo' }
      },
      {
        path: 'grading',
        component: () => import('@/views/xi/grading/index'),
        name: 'grading',
        meta: { perm: 'm:xi:grading', title: '段位管理', icon: 'zhibo' }
      },
      {
        path: 'reward',
        component: () => import('@/views/xi/reward/index'),
        name: 'reward',
        meta: { perm: 'm:xi:reward', title: '奖励配置', icon: 'zhibo' }
      },
      {
        path: 'pendant',
        component: () => import('@/views/xi/pendant/index'),
        name: 'pendant',
        meta: { perm: 'm:xi:pendant', title: '挂件配置', icon: 'zhibo' }
      },
      {
        path: 'bigGrading',
        component: () => import('@/views/xi/bigGrading/index'),
        name: 'bigGrading',
        meta: { perm: 'm:xi:bigGrading', title: '大段位管理', icon: 'zhibo' }
      },
      {
        path: 'level',
        component: () => import('@/views/xi/level/index'),
        name: 'level',
        meta: { perm: 'm:xi:level', title: '等级管理', icon: 'zhibo' }
      },
      {
        path: 'reportConfig',
        component: () => import('@/views/xi/report/index'),
        name: 'reportConfig',
        meta: {
          perm: 'm:xi:reportConfig',
          title: '举报列表配置',
          icon: 'zhibo'
        }
      },
      {
        path: 'showConfig',
        component: () => import('@/views/xi/show/index'),
        name: 'showConfig',
        meta: { perm: 'm:xi:showConfig', title: '炫耀文本配置', icon: 'zhibo' }
      },
      {
        path: 'extradate',
        component: () => import('@/views/xi/extradate/index'),
        name: 'extradate',
        meta: { perm: 'm:xi:extradate', title: '标准作息日历', icon: 'zhibo' }
      },
      {
        path: 'studentextradate',
        component: () => import('@/views/xi/extradate/student'),
        name: 'studentextradate',
        meta: {
          perm: 'm:xi:studentextradate',
          title: '学员作息日历',
          icon: 'zhibo'
        }
      },
      {
        path: 'guide',
        component: () => import('@/views/xi/guide/index'),
        name: 'guide',
        meta: { perm: 'm:xi:guide', title: '新手引导', icon: 'zhibo' }
      },
      {
        path: 'tree',
        component: () => import('@/views/xi/tree/index'),
        name: 'tree',
        meta: { perm: 'm:xi:tree', title: '树种管理', icon: 'zhibo' }
      },
      {
        path: 'dict',
        component: () => import('@/views/xi/commondict/index'),
        name: 'dict',
        meta: { perm: 'm:xi:dict', title: '提醒文本', icon: 'zhibo' }
      },
      {
        path: 'incentives',
        component: () => import('@/views/xi/incentives/index'),
        name: 'incentives',
        meta: { perm: 'm:xi:incentives', title: '激励语文本', icon: 'zhibo' }
      },
      {
        path: 'subject',
        component: () => import('@/views/xi/subject/index'),
        name: 'subject',
        meta: { perm: 'm:xi:subject', title: '学科管理', icon: 'zhibo' }
      },
      {
        path: 'classifyType',
        component: () => import('@/views/xi/classify/type'),
        name: 'classifyType',
        meta: { perm: 'm:xi:classifyType', title: '类型管理', icon: 'zhibo' }
      },
      {
        path: 'classify',
        component: () => import('@/views/xi/classify/index'),
        name: 'classify',
        meta: { perm: 'm:xi:classify', title: '分类管理', icon: 'zhibo' }
      },
      {
        path: 'goods',
        component: () => import('@/views/xi/goods/index'),
        name: 'goods',
        meta: { perm: 'm:xi:goods', title: '商品管理', icon: 'zhibo' }
      },
      {
        path: 'exchangerecord',
        component: () => import('@/views/xi/exchangerecord/index'),
        name: 'exchangerecord',
        meta: { perm: 'm:xi:exchangerecord', title: '兑换记录', icon: 'zhibo' }
      },
      // {
      //   path: 'getmethod',
      //   component: () => import('@/views/xi/getmethod/index'),
      //   name: 'getmethod',
      //   meta: { perm: 'm:xi:getmethod', title: '如何获取经验值', icon: 'zhibo' }
      // },
      {
        path: 'getmethod',
        hidden: true,
        component: () => import('@/views/xi/getmethod/index'),
        name: 'getmethod',
        meta: {
          perm: 'm:xi:getmethod',
          title: '如何获取经验值',
          icon: 'zhibo'
        }
      },
      {
        path: 'scoreContent',
        component: () => import('@/views/xi/scoreContent/index'),
        name: 'scoreContent',
        meta: { perm: 'm:xi:scoreContent', title: '点评语管理', icon: 'zhibo' }
      },
      {
        path: 'remarkContent',
        component: () => import('@/views/xi/remarkContent/index'),
        name: 'remarkContent',
        meta: {
          perm: 'm:xi:remarkContent',
          title: '备注语管理',
          icon: 'zhibo'
        }
      },
      {
        path: 'sysUser',
        component: () => import('@/views/xi/sysUser/index'),
        name: 'sysUser',
        meta: { perm: 'm:xi:sysUser', title: '用户管理', icon: 'post' }
      },
      {
        path: 'gift',
        component: () => import('@/views/xi/gift/index'),
        name: 'gift',
        meta: { perm: 'm:xi:gift', title: '礼包设置', icon: 'post' }
      },
      {
        path: 'manager',
        component: () => import('@/views/xi/manage/index'),
        name: 'manager',
        meta: { perm: 'm:xi:manager', title: '学管师学员', icon: 'post' }
      },
      {
        path: 'managerStudent',
        component: () => import('@/views/xi/manage/student'),
        name: 'managerStudent',
        hidden: true,
        meta: {
          perm: 'm:xi:managerStudent',
          title: '添加学管师学员',
          icon: 'post'
        }
      },
      {
        path: 'ratioConfig',
        component: () => import('@/views/xi/ratioconfig/index'),
        name: 'ratioConfig',
        meta: {
          perm: 'm:xi:ratioConfig',
          title: '专注时长奖励配置',
          icon: 'post'
        }
      },
      {
        path: 'punishconfig',
        component: () => import('@/views/xi/community/punishconfig'),
        name: 'punishconfig',
        meta: {
          perm: 'm:community:punishconfig',
          title: '休息超时惩罚配置',
          icon: 'post'
        }
      },
      {
        path: 'parentsSysMessage',
        component: () => import('@/views/xi/parentsSysMessage/index'),
        name: 'parentsSysMessage',
        meta: {
          perm: 'm:community:parentsSysMessage',
          title: '后台管理家长系统消息',
          icon: 'post'
        }
      }
    ]
  },
  {
    path: '/community',
    component: Layout,
    redirect: '/community',
    meta: {
      perm: 'm:community',
      title: '社区管理',
      icon: 'home'
    },
    children: [
      {
        path: 'post',
        component: () => import('@/views/xi/community/post'),
        name: 'post',
        meta: { perm: 'm:community:post', title: '帖子管理', icon: 'post' }
      },
      {
        path: 'plate',
        component: () => import('@/views/xi/community/plate'),
        name: 'plate',
        meta: { perm: 'm:community:plate', title: '板块管理', icon: 'post' }
      },
      {
        path: 'banner_run',
        component: () => import('@/views/xi/community/banner_run'),
        name: 'banner_run',
        meta: {
          perm: 'm:community:banner_run',
          title: 'banner管理',
          icon: 'post'
        }
      },
      {
        path: 'official',
        component: () => import('@/views/xi/community/official'),
        name: 'official',
        meta: { perm: 'm:community:official', title: '官方推荐', icon: 'post' }
      },
      {
        path: 'studentInfo',
        component: () => import('@/views/xi/community/studentInfo'),
        name: 'studentInfo',
        meta: {
          perm: 'm:community:studentInfo',
          title: '用户管理',
          icon: 'post'
        }
      },
      {
        path: 'posterbackground',
        component: () => import('@/views/xi/community/posterbackground'),
        name: 'posterbackground',
        meta: {
          perm: 'm:community:posterbackground',
          title: '签到背景',
          icon: 'post'
        }
      },
      {
        path: 'systemavatar',
        component: () => import('@/views/xi/community/systemavatar'),
        name: 'systemavatar',
        meta: {
          perm: 'm:community:systemavatar',
          title: '系统头像',
          icon: 'post'
        }
      },
      {
        path: 'taskawrad',
        component: () => import('@/views/xi/community/taskawrad'),
        name: 'taskawrad',
        meta: {
          perm: 'm:community:taskawrad',
          title: '任务奖励配置',
          icon: 'post'
        }
      }
    ]
  },
  {
    path: '/newpartner',
    component: Layout,
    redirect: '/newpartner',
    meta: {
      perm: 'm:newpartner',
      title: '合伙人新人陪跑',
      icon: 'home'
    },
    children: [
      {
        path: 'groupchat',
        component: () => import('@/views/newpartner/groupchat'),
        name: 'groupchat',
        meta: { perm: 'm:newpartner:groupchat', title: '群聊管理', icon: 'post' }
      },
      {
        path: 'trainingCampAssessment',
        component: () => import('@/views/newpartner/trainingCampAssessment'),
        name: 'trainingCampAssessment',
        meta: { perm: 'm:newpartner:trainingCampAssessment', title: '训练营考核列表', icon: 'post' }
      },
      {
        path: 'partnerDetail',
        component: () => import('@/views/newpartner/groupchat/partnerDetail'),
        name: 'partnerDetail',
        meta: { perm: 'm:newpartner:partnerDetail', title: '合伙人详情', icon: 'post' },
        hidden: true
      },
      {
        path: 'partnerSelfStudy',
        component: () => import('@/views/newpartner/partnerSelfStudy'),
        name: 'partnerSelfStudy',
        meta: { perm: 'm:newpartner:partnerSelfStudy', title: '合伙人自学进度列表', icon: 'post' }
      }
    ]
  },
  {
    path: '/purchase',
    component: Layout,
    meta: {
      perm: 'm:purchase',
      title: '采购管理',
      icon: 'home'
    },
    children: [
      {
        path: 'procurementAllocation',
        component: () => import('@/views/purchase/procurementAllocation'),
        name: 'procurementAllocation',
        meta: { perm: 'm:purchase:procurementAllocation', title: '采购配置', icon: 'post' }
      },
      {
        path: 'purchaseApply',
        component: () => import('@/views/purchase/purchaseApply'),
        name: 'purchaseApply',
        meta: { perm: 'm:purchase:purchaseApply', title: '采购申请', icon: 'post' }
      },
      {
        path: 'deliveryManagement',
        component: () => import('@/views/purchase/deliveryManagement'),
        name: 'deliveryManagement',
        meta: { perm: 'm:purchase:deliveryManagement', title: '发货管理', icon: 'post' }
      },
      {
        path: 'subordinateDashboard',
        component: () => import('@/views/purchase/subordinateDashboard'),
        name: 'subordinateDashboard',
        meta: { perm: 'm:purchase:subordinateDashboard', title: '下级采购看板', icon: 'post' }
      }
    ]
  },
  {
    path: '/studyExamPassed',
    component: Layout,
    redirect: '/studyExamPassed',
    meta: {
      perm: 'm:studyExamPassed',
      title: '学考通',
      icon: 'system'
    },
    children: [
      {
        path: 'course',
        component: () => import('@/views/studyExamPassed/course/index'),
        name: 'course',
        meta: { perm: 'm:studyExamPassed:course', title: '课程管理', icon: 'post' }
      },
      {
        path: 'courseVideoConfig',
        component: () => import('@/views/studyExamPassed/course/CourseVideoConfig'),
        name: 'courseVideoConfig',
        hidden: true,
        meta: { perm: 'm:studyExamPassed:courseVideoConfig', title: '课程视频管理', icon: 'post' }
      },
      {
        path: 'videoConfig',
        component: () => import('@/views/studyExamPassed/videoConfig/index'),
        name: 'videoConfig',
        meta: { perm: 'm:studyExamPassed:videoConfig', title: '视频管理', icon: 'post' }
      },
      {
        path: 'withdrawVideoConfig',
        component: () => import('@/views/studyExamPassed/widthdraCourseVideoConfig/index'),
        name: 'withdrawVideoConfig',
        meta: { perm: 'm:studyExamPassed:withdrawVideoConfig', title: '退课视频管理', icon: 'post' }
      }
    ]
  },
  // 404 page must be placed at the end !!!
  {
    path: '*',
    redirect: '/404',
    hidden: true
  }
];

export default new Router({
  // mode: 'history', // require service support
  scrollBehavior: () => ({
    y: 0
  }),
  routes: constantRouterMap
});
