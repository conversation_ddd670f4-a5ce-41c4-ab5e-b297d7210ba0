<template>
  <div class="app-container">
    <el-form :inline="true" class="container-card" label-width="110px" label-position="left">
      <el-row>

        <el-col :span="7" :xs="24">
          <el-form-item label="商户编号：">
            <el-input id="merchantCode" v-model="dataQuery.merchantCode" name="id" placeholder="请输入商户编号：" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="7" :xs="24">
          <el-form-item label="商户名称：">
            <el-input id="merchantName" v-model="dataQuery.merchantName" name="id" placeholder="请输入服务商名称:" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="7" :xs="24">
          <el-form-item label="变动方式：">
            <el-select v-model="dataQuery.type" placeholder="全部" clearable>
              <el-option v-for="item in [{ value: 1, label: '进' }, { value: 0, label: '出' }]" :key="item.value" :label="item.label"
                :value="item.label">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="3" :xs="24" style="text-align: right">
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="fetchData01()">搜索</el-button>
          </el-form-item>
        </el-col>

      </el-row>

    </el-form>
    <!--    <el-form :inline="true" style="margin-bottom: 20px;">-->
    <!--      <el-button type="warning" icon="el-icon-document-copy" @click="exportFlow()"  v-loading="exportLoading ">导出</el-button>-->
    <!--    </el-form>-->

    <el-table class="period-table" v-loading="tableLoading" :data="tableData" style="width: 100%;margin-bottom: 20px;"
      row-key="id" border default-expand-all :tree-props="{ list: 'children', hasChildren: 'true' }">
      <el-table-column prop="id" label="ID" width="130" :show-overflow-tooltip="true"></el-table-column>
      <el-table-column prop="merchantCode" label="商户编号" width="100"></el-table-column>
      <el-table-column prop="merchantName" label="商户名称" width="200"></el-table-column>
      <el-table-column prop="roleTag" label="商户类型" width="120"></el-table-column>
      <!--      <el-table-column prop="coursePrice" label="课程单价"  width="120"></el-table-column>-->
      <!--      <el-table-column prop="courseNum" label="学时数量"  width="120"></el-table-column>-->
      <el-table-column prop="rechargeMoney" label="变动金额（元）" width="150"></el-table-column>
      <el-table-column prop="type" label="变动方式"></el-table-column>
      <el-table-column prop="accountsTypeName" label="变动类型"></el-table-column>
      <!--      <el-table-column prop="befMoney" label="变动前金额（元）"  width="150"></el-table-column>-->
      <!--      <el-table-column prop="aftMoney" label="变动后金额（元）"  width="150"></el-table-column>-->
      <el-table-column prop="beforeCourse" label="变动前学时" width="150"></el-table-column>
      <el-table-column prop="afterCourse" label="变动后学时" width="150"></el-table-column>
      <el-table-column prop="coursePrice" label="课程单价（元）" width="150"></el-table-column>

      <el-table-column prop="datetime" label="变动时间" width="200" :show-overflow-tooltip="true"></el-table-column>
      <el-table-column prop="descriptionRe" label="变动描述" width="400" :show-overflow-tooltip="true"></el-table-column>
    </el-table>
    <!-- 分页 -->
    <el-col :span="24" style="overflow-x: auto;" :xs="24">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>
    <!--    <el-col :span="24" class="mt20">-->
    <!--      本次总计 变动金额入：{{changeIn-->

    <!--      }}元，变动金额出：{{-->
    <!--changeOut-->
    <!--      }}元-->
    <!--    </el-col>-->
  </div>
</template>

<script>
import
merchantAccountFlowApi
  from "@/api/merchantAccountFlow";
import Tinymce from "@/components/Tinymce";
import enTypes from '@/api/bstatus'
import {
  pageParamNames
} from "@/utils/constants";
export default {
  name: 'merchantFlowList',
  data() {
    return {
      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      tableData: [{
        name: 1,
        title: '1111'
      }],
      changeOut: 0,//变动金额出
      changeIn: 0,//变动入
      dataQuery: {},
      dialogVisible: false,
      updateMarketDate: {},
      addMarketDate: {},
      showLoginAccount: false,
      financeAccountsType: [],//变动类型
      RegTime: '',
      exportLoading: false,//商户流水导出
    };
  },
  created() {
    this.fetchData();
    this.getFinanceAccountsType();
  },
  methods: {
    fetchData01() {
      this.tablePage = {
        currentPage: 1,
        size: this.tablePage.size,
        totalPage: null,
        totalItems: null
      }
      this.fetchData();
    },
    // 查询提现列表
    fetchData() {
      const that = this;
      that.tableLoading = true;
      that.dataQuery.pageNum = that.tablePage.currentPage;
      that.dataQuery.pageSize = that.tablePage.size;
      merchantAccountFlowApi.merchantAccountCourseFlowList(that.dataQuery).then((res) => {
        that.tableData = res.data.data;
        that.tableLoading = false;
        // 设置后台返回的分页参数
        pageParamNames.forEach((name) =>
          that.$set(that.tablePage, name, parseInt(res.data[name]))
        );
      });
      merchantAccountFlowApi.merchantCapitalInquiry(that.tablePage.currentPage,
        that.tablePage.size,
        that.dataQuery).then((res) => {
          // console.log(res);
          that.changeOut = res.data.changeOut;
          that.changeIn = res.data.changeIn;
        })
    },
    //导出
    //导出
    exportFlow() {
      const that = this;
      that.exportLoading = true;
      //
      merchantAccountFlowApi.simpleMerchantExecl(that.dataQuery).then(res => {
        //           this.$notify.error({
        //               title: "操作失败",
        //               message: "文件下载失败"
        //             });
        const url = window.URL.createObjectURL(res);
        const link = document.createElement("a");
        link.style.display = "none";
        link.href = url; // 获取服务器端的文件名
        link.setAttribute("download", "资金商户流水.xls");
        document.body.appendChild(link);
        link.click();
        that.exportLoading = false;
      })
    },
    //新增操作
    clickAdd() {
      this.dialogVisible = true;
    },
    // 打开修改登陆账号
    openLogin() {
      this.showLoginAccount = true
    },
    closeLogin() {
      this.showLoginAccount = false
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val;
      this.fetchData();
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val;
      this.fetchData();
    },
    //获取变动类型
    getFinanceAccountsType() {
      var enType = "FinanceAccountsType";
      enTypes.getEnumerationAggregation(enType).then(res => {
        this.financeAccountsType = res.data;
      })
    },

  }
};
</script>

<style>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.period-table td,
.period-table th {
  text-align: center;
}

.mt20 {
  margin-top: 20px;
}</style>
