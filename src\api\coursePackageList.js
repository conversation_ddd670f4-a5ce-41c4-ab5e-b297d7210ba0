/**
 * 课程包接口
 */
import request from '@/utils/request'

export default {
  courseListeningList(pageNum, pageSize, data) {
    return request({
      url: '/znyy/course/product/list/' + pageNum + '/' + pageSize,
      method: 'GET',
      params: data
    })
  },
  //查询课程分类
  byProductCategory() {
    return request({
      url: '/znyy/course/product/by/product/category',
      method: 'POST',
    })
  },
  //查询课程类型
  byCourseCategory(categoryId) {
    return request({
      url: '/znyy/course/product/by/course/category?categoryId=' + categoryId,
      method: 'GET',

    })
  },
  saveCourse(courseProductSpecAdd) {
    return request({
      url: '/znyy/course/product/save',
      method: 'POST',
      data: courseProductSpecAdd
    })
  },
  getCourseDetail(id) {
    return request({
      url: '/znyy/course/product/' + id,
      method: 'get',
    })
  }

}
