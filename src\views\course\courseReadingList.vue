<template>
  <div class="app-container">
    <!-- 搜索 -->
    <div class="mb20 pd10" style="background-color: aliceblue;border:  darkgoldenrod;">
      <el-row type="flex" justify="center">
        <el-col :xs="24" :lg="16">
          <el-form :ref="addOrUpdate ? 'addReadingData' : 'updateReadingData'" :rules="rulseReading" :model="addOrUpdate ? addReadingData : updateReadingData"
            label-position="right" label-width="80px" style="width: 100%;margin:0 auto">
            <el-form-item label="题目类型">
              <template>
                <el-radio v-model="radio" label="0">
                  {{ courseName }}</el-radio>
              </template>
            </el-form-item>
            <el-form-item label="文章标题" prop="topicTitle">
              <el-col :lg="24">
                <el-input v-if="addOrUpdate" v-model="addReadingData.topicTitle" maxlength="800" type="text">
                </el-input>
                <el-input v-if="!addOrUpdate" v-model="updateReadingData.topicTitle" maxlength="800" type="text">
                </el-input>
              </el-col>
            </el-form-item>
            <el-form-item label="显示名称" prop="showName">
              <el-col :lg="24">
                <el-input v-if="addOrUpdate" type="text" v-model="addReadingData.showName" maxlength="10">
                </el-input>
                <el-input v-if="!addOrUpdate" type="text" v-model="updateReadingData.showName" maxlength="10">
                </el-input>
              </el-col>
            </el-form-item>
            <el-form-item label="文章内容" prop="topicContent">
              <el-input type="textarea" resize="none" :rows="4" v-if="addOrUpdate" v-model="addReadingData.topicContent" />
              <el-input type="textarea" resize="none" :rows="4" v-if="!addOrUpdate" v-model="updateReadingData.topicContent" />
            </el-form-item>
            <el-form-item label="建议时间" prop="suggestedTime">
              <el-col :span="12">
                <el-input v-if="addOrUpdate" v-model="addReadingData.suggestedTime" type="number" maxlength="4" max="99"
                  min="1">
                </el-input>
                <el-input v-if="!addOrUpdate" v-model="updateReadingData.suggestedTime" type="number" maxlength="4" max="99"
                  min="1">
                </el-input>
              </el-col>
              <el-col :span="12">分钟</el-col>
            </el-form-item>
            <el-form-item label="排序" prop="sort">
              <el-col :span="12">
                <el-input v-if="addOrUpdate" v-model="addReadingData.sort" type="number" maxlength="4" max="99" min="1">
                </el-input>
                <el-input v-if="!addOrUpdate" v-model="updateReadingData.sort" type="number" maxlength="4" max="99" min="1">
                </el-input>
              </el-col>
            </el-form-item>
            <el-form-item label="题目难度" prop="level">
              <template>
                <el-radio v-if="addOrUpdate" v-model="addReadingData.level" label="1">★
                </el-radio>
                <el-radio v-if="addOrUpdate" v-model="addReadingData.level" label="2">
                  ★★</el-radio>
                <el-radio v-if="addOrUpdate" v-model="addReadingData.level" label="3">
                  ★★★</el-radio>
                <el-radio v-if="addOrUpdate" v-model="addReadingData.level" label="4">
                  ★★★★</el-radio>
                <el-radio v-if="addOrUpdate" v-model="addReadingData.level" label="5">
                  ★★★★★</el-radio>
                <el-radio v-if="!addOrUpdate" v-model="updateReadingData.level" label="1">★
                </el-radio>
                <el-radio v-if="!addOrUpdate" v-model="updateReadingData.level" label="2">
                  ★★</el-radio>
                <el-radio v-if="!addOrUpdate" v-model="updateReadingData.level" label="3">
                  ★★★</el-radio>
                <el-radio v-if="!addOrUpdate" v-model="updateReadingData.level" label="4">
                  ★★★★</el-radio>
                <el-radio v-if="!addOrUpdate" v-model="updateReadingData.level" label="5">
                  ★★★★★</el-radio>
              </template>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
    </div>

    <div v-for="(item, index) in items" :key="index" class="mb20">
      <el-card style="background-color: aliceblue;">
        <el-row type="flex" justify="center">
          <el-col :xs="24" :lg="16">
            <el-form :ref="'rulesAnswers'+index" label-position="right" label-width="110px" :model="item" style="width: 100%;margin: 0 auto;">
              <el-form-item label="题目编号" prop="id" v-show="false">
                <el-col :span="20">
                  <el-input v-model="item.id"></el-input>
                </el-col>
              </el-form-item>
              <el-row>
                <el-col :span="20">
                  <el-form-item label="题目编号" prop="fillNumber" :rules="[{
                      required: true,
                      message: '必填',
                      trigger: 'blur',
                    }]">
                    <el-input v-model="item.fillNumber" type="number" max="99" min="1"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="2" :offset="1">
                  <el-button v-if="addOrUpdate" type="danger" icon="el-icon-close" @click="updateDeleteForm(item)">删除</el-button>
                  <el-button v-if="!addOrUpdate" type="danger" icon="el-icon-close" @click="updateDeleteForm01(item)">删除</el-button>
                </el-col>
              </el-row>

              <el-form-item label="问题标题" prop="questionText" >
                <el-col :span="24">
                  <el-input v-model="item.questionText" type="textarea"></el-input>
                </el-col>
              </el-form-item>
              <el-form-item label="答案A" prop="answerForA">
                <el-col :span="24">
                  <el-input v-model="item.answerForA"></el-input>
                </el-col>
              </el-form-item>
              <el-form-item label="答案B" prop="answerForB">
                <el-col :span="24">
                  <el-input v-model="item.answerForB"></el-input>
                </el-col>
              </el-form-item>
              <el-form-item label="答案C" prop="answerForC">
                <el-col :span="24">
                  <el-input v-model="item.answerForC"></el-input>
                </el-col>
              </el-form-item>
              <el-form-item label="答案D" prop="answerForD">
                <el-col :span="24">
                  <el-input v-model="item.answerForD"></el-input>
                </el-col>
              </el-form-item>
              <el-form-item  v-if="courseContentTypeReading=='Reading'"  label="答案E" prop="answerForE">
                <el-col :span="24">
                  <el-input v-model="item.answerForE"></el-input>
                </el-col>
              </el-form-item>
              <el-form-item  v-if="courseContentTypeReading=='Reading'"  label="答案F" prop="answerForF">
                <el-col :span="24">
                  <el-input v-model="item.answerForF"></el-input>
                </el-col>
              </el-form-item>
              <el-form-item v-if="courseContentTypeReading=='Reading'" label="答案G" prop="answerForG">
                <el-col :span="24">
                  <el-input v-model="item.answerForG"></el-input>
                </el-col>
              </el-form-item>
              <el-form-item label="题目正确答案" prop="correctAnswer" :rules="[{
            required: true,
            message: '必填',
            trigger: 'blur',
          }]">
                <el-col :span="24">
                  <el-input type="text" v-model="item.correctAnswer" maxlength="5"></el-input>
                </el-col>
              </el-form-item>
              <el-row>
                <el-col :span="20">
                  <el-form-item label="选项分析" prop="answerRemark" :rules="[{
                      required: true,
                      message: '必填',
                      trigger: 'blur',
                    }]">
                    <el-col :span="24">
                      <el-input type="textarea" class="reading-textarea" v-model="item.answerRemark"></el-input>
                    </el-col>
                  </el-form-item>
                </el-col>
                <el-col :span="3" :offset="1">
                  <el-button type="primary" icon="el-icon-plus" @click="addForm">新增</el-button>
                </el-col>
              </el-row>

            </el-form>
          </el-col>
        </el-row>
      </el-card>
    </div>



    <div slot="footer" class="dialog-footer">
      <el-row>
        <el-col :span="4"  :offset="6">
          <el-button v-if="addOrUpdate" type="primary" icon="el-icon-plus" @click="addActiveFun('addReadingData','rulesAnswers')">新增</el-button>
          <el-button v-if="!addOrUpdate" type="primary" @click="updateReadingFun('updateReadingData','rulesAnswers')">编辑</el-button>
        </el-col>
        <el-col :span="4" :offset="4">
          <el-button type="primary"  @click="getList">关闭</el-button>
        </el-col>
      </el-row>


    </div>
  </div>
</template>

<script>
  import courseReadingApi from "@/api/courseReading";
  export default {
    data() {
      return {
        // 分页
        addReadingData: {},
        updateReadingData: {},
        courseName: '',
        courseCode: '',
        courseContentTypeReading: '',
        addOrUpdate: true,
        items: [],
        //增加内容
        rulseReading: {
          topicTitle: [{
            required: true,
            message: "必填",
            trigger: "blur",
          }, ],
          showName: [{
            required: true,
            message: "必填",
            trigger: "blur",
          }, ],
          topicContent: [{
            required: true,
            message: "必填",
            trigger: "blur",
          }, ],
          suggestedTime: [{
            required: true,
            message: "必填",
            trigger: "blur",
          }, ],
          sort: [{
            required: true,
            message: "必填",
            trigger: "blur",
          }, ],
          level: [{
            required: true,
            message: "必填",
            trigger: "blur",
          }, ],
        },
        items: [{
          fillNumber: '',
          questionText: "",
          answerForA: "",
          answerForB: "",
          answerForC: "",
          answerForD: "",
          answerForE: "",
          answerForF: "",
          answerForG: "",
          correctAnswer: "",
          answerRemark: ""
        }],
        rulesAnswers: {
          fillNumber: [{
            required: true,
            message: "必填",
            trigger: "blur",
          }, ],
          questionText: [{
            required: true,
            message: "必填",
            trigger: "blur",
          }, ],
          answerForA: [{
            required: true,
            message: "必填",
            trigger: "blur",
          }, ],
          answerForB: [{
            required: true,
            message: "必填",
            trigger: "blur",
          }, ],
          answerForC: [{
            required: true,
            message: "必填",
            trigger: "blur",
          }, ],
          answerForD: [{
            required: true,
            message: "必填",
            trigger: "blur",
          }, ],
          correctAnswer: [{
            required: true,
            message: "必填",
            trigger: "blur",
          }, ],
          answerRemark: [{
            required: true,
            message: "必填",
            trigger: "blur",
          }, ],

        },
        radio: 0
      }

    },
    created() {
      const that = this;
      that.tableLoading = true;

      that.courseName = window.localStorage.getItem("ourseReadingListCourseName");
      that.courseCode = window.localStorage.getItem("courseReadingListCode");
      that.courseContentTypeReading = window.localStorage.getItem("courseContentTypeReading");
      //that.addReadingData.courseCode = that.$route.query.courseCode;
      that.addOrUpdate = JSON.parse(window.localStorage.getItem("addOrUpdateReadingList"));

      that.openEdit();

      if (!this.addOrUpdate) {
        this.setTitle('编辑阅读理解详情/完型填空')
      } else {
        this.setTitle('增加阅读理解详情/完型填空')
      }
    },
    methods: {
      // 动态设置标签页标题
      setTitle(title){
        let i = 0
        let visitedViews = this.$store.getters.visitedViews
        visitedViews.forEach((route,index) => {
           if(this.$route.path == route.path){
             i = index
           }
         })
         this.$route.meta.title = title
         visitedViews[i].title = title
      },


      // 查询+搜索课程列表
      //删除表单
      updateDeleteForm: function(ele) {
        if (this.items.length === 1) {
          this.$message({
            message: '至少要留一个',
            type: 'warning',
            duration: 1000
          })
          return
        }
        var index = this.items.indexOf(ele);
        console.log(index);
        if (index !== -1) {
          this.items.splice(index, 1)
        }
      },
      updateDeleteForm01: function(ele) {
        if (ele.id != null) {
          if (this.items.length === 1) {
            this.$message({
              message: '至少要留一个',
              type: 'warning',
              duration: 1000
            })
            return
          }
          courseReadingApi.deleteCourseReading(ele.id).then(res => {
            var index = this.items.indexOf(ele);
            console.log(index);
            if (index !== -1) {
              this.items.splice(index, 1)
            }
          })
        } else {
          if (this.items.length === 1) {
            this.$message({
              message: '至少要留一个',
              type: 'warning',
              duration: 1000
            })
            return
          }
          var index = this.items.indexOf(ele);
          console.log(index);
          if (index !== -1) {
            this.items.splice(index, 1)
          }
        }
      },
      //增加表单
      addForm: function() {
        this.items.push({
          fillNumber: '',
          questionText: "",
          answerForA: "",
          answerForB: "",
          answerForC: "",
          answerForD: "",
          answerForE: "",
          answerForF: "",
          answerForG: "",
          correctAnswer: "",
          answerRemark: ""
        });
        this.items.forEach(val => {
          console.log(val.answerForA + "wyy");
        })
      },
      //编辑
      // 打开编辑阅读理解和完型填空
      openEdit() {
        if (!this.addOrUpdate) {
          var id = window.localStorage.getItem("addOrUpdateReadingListId");
          courseReadingApi.queryReading(id).then((res) => {
            this.addOrUpdate = false;
            this.items = res.data.courseReadingAnswerList;
            this.updateReadingData = res.data.courseReadingCo;
          });
        }
      },
      // 阅读理解和完型填空提交
      addActiveFun(ele) {
        const that = this;
        let _self = this;
        let newArr = [];
        that.addReadingData.courseCode = that.$route.query.courseCode;
        that.items.forEach((item, index) => {

          checkForm('rulesAnswers' + index);
        })

        function checkForm(arrName) {

          var result = new Promise(function(resolve, feject) {
            _self.$refs[arrName][0].validate((valid) => {
              if (valid) {
                resolve();
              } else {
                reject();
              }
            })
          })
          newArr.push(result);
        };

        Promise.all(newArr).then(function() {
          setTimeout(function() {
            that.$refs[ele].validate((valid) => {
              // 表单验证
              if (valid) {
                const loading = _self.$loading({
                  lock: true,
                  text: "新增阅读理解或完型填空",
                  spinner: "el-icon-loading",
                  background: "rgba(0, 0, 0, 0.7)",
                });
                const data = {
                  courseReadingCo: that.addReadingData,
                  courseReadingAnswerList: that.items
                }
                courseReadingApi
                  .addCourseReading(data)
                  .then((res) => {
                    that.dialogVisible = false;
                    that.addReadingData = {};
                    that.items = [{
                        fillNumber: '',
                        questionText: "",
                        answerForA: "",
                        answerForB: "",
                        answerForC: "",
                        answerForD: "",
                        answerForE: "",
                        answerForF: "",
                        answerForG: "",
                        correctAnswer: "",
                        answerRemark: ""
                      }],
                      loading.close();
                      that.getList();
                    that.$router.push({
                      path: "/course/courseReading",
                      query: {
                        courseCode: this.courseCode,
                        courseName: this.courseName,
                      }
                    });
                    that.$message.success(res.data.data);
                  })
                  .catch((err) => {
                    loading.close();
                  });
              } else {
                console.log("error submit!!");
                return false;
              }
            });

          }, 1000);
        }).catch(function() {
          that.$message({
            message: "操作失败",
            type: "error"
          })
        })
      },
      //编辑完型填空和阅读理解
      updateReadingFun(ele) {
        const that = this;
        let _self = this;
        let newArr = [];
        that.items.forEach((item, index) => {

          checkForm('rulesAnswers' + index);
        })

        function checkForm(arrName) {

          var result = new Promise(function(resolve, feject) {
            _self.$refs[arrName][0].validate((valid) => {
              if (valid) {
                resolve();
              } else {
                reject();
              }
            })
          })
          newArr.push(result);
        };

        Promise.all(newArr).then(function() {
          setTimeout(function() {
            that.$refs[ele].validate((valid) => {
              // 表单验证
              if (valid) {

                const loading = _self.$loading({
                  lock: true,
                  text: "修改",
                  spinner: "el-icon-loading",
                  background: "rgba(0, 0, 0, 0.7)",
                });
                const data = {
                  courseReadingCo: that.updateReadingData,
                  courseReadingAnswerList: that.items
                }
                courseReadingApi
                  .updateWordLevel(data)
                  .then((res) => {
                    that.dialogVisible = false;
                    loading.close();
                    that.getList();
                  })
                  .catch((err) => {
                    loading.close();
                  });
              } else {
                console.log("error submit!!");
                return false;
              }
            });

          }, 1000);
        }).catch(function() {
          that.$message({
            message: "操作失败",
            type: "error"
          })
        })
      },
      // 阅读理解和完型填空提交
      // addActiveFun(ele,els) {
      //   const that = this;
      //   that.$refs[ele].validate((valid) => {
      //     // 表单验证
      //     if (valid) {
      //       const loading = this.$loading({
      //         lock: true,
      //         text: "新增阅读理解或完型填空",
      //         spinner: "el-icon-loading",
      //         background: "rgba(0, 0, 0, 0.7)",
      //       });
      //       const data ={
      // courseReadingCo:that.addReadingData,
      //  courseReadingAnswerList:that.items
      //       }
      //       courseReadingApi
      //         .addCourseReading(data)
      //         .then((res) => {
      //           that.dialogVisible = false;
      //           loading.close();
      //            that.$router.push({
      //        path: "/course/courseReading",
      //        query: {
      //         courseCode:this.courseCode,
      //         courseName:this.courseName,
      //       }
      //     });
      //           that.$message.success(res.data.data);
      //         })
      //         .catch((err) => {
      //           loading.close();
      //         });
      //     } else {
      //       console.log("error submit!!");
      //       return false;
      //     }
      //   });
      // },
      // updateReadingFun(ele,els){
      //     const that = this;
      //   that.$refs[ele].validate((valid) => {
      //     // 表单验证
      //     if (valid) {

      //       const loading = this.$loading({
      //         lock: true,
      //         text: "修改",
      //         spinner: "el-icon-loading",
      //         background: "rgba(0, 0, 0, 0.7)",
      //       });
      //       const data ={
      // courseReadingCo:that.updateReadingData,
      //  courseReadingAnswerList:that.items
      //       }
      //       courseReadingApi
      //         .updateWordLevel(data)
      //         .then((res) => {
      //           that.dialogVisible = false;
      //           loading.close();
      //          this.getList();
      //         })
      //         .catch((err) => {
      //           loading.close();
      //         });
      //     } else {
      //       console.log("error submit!!");
      //       return false;
      //     }
      //   });
      // },
      getList() {
        this.$store.dispatch('delVisitedViews', this.$route);
        this.$router.go(-1);
        this.$router.push({
          path: "/course/courseReading",
          query: {
            courseCode: this.courseCode,
            courseName: this.courseName,
            courseContentType: "Reading"
          }

        });

      },
      // 状态改变事件
      change(radio) {
        if (radio == "1") {
          this.addReadingData.isEnable = 1;
        } else {
          this.addReadingData.isEnable = 0;
        }
      },

    }
  }
</script>

<style>
  .lh36 {
    line-height: 36px;
    font-size: 14px;
  }

  .SearchForm {
    width: 100%;
    margin-bottom: 15px;
    background: #fff;
    border-radius: 6px;
    padding: 5px 0;
    padding-left: 5px;
    box-shadow: 1px 1px 3px rgba(0, 0, 0, .2);
  }

  .btn-add {
    padding: 5px;
  }

  .red {
    color: red;
  }

  .green {
    color: green;
  }

  .course-table {
    text-align: center;
  }

  .course-table td,
  .course-table th {
    padding: 5px 0;
    text-align: center;
  }

  .course-table button {
    padding: 2px;
  }

  .icon-stop {
    float: left;
    display: inline-block;
    width: 12px;
    height: 12px;
    background: url('../../icons/stop.png') no-repeat top center/contain;
  }

  .mt22 {
    margin-top: 22px;
  }

  .mb20 {
    margin-bottom: 20px;
  }

  .pd10 {
    padding: 10px;
  }
  .reading-textarea textarea{
    height:200px
  }
</style>
