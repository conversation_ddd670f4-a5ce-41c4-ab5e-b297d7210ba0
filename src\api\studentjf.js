import request from '@/utils/request'

export const buyDeliverHour = (data)=>{
    return request({
        url: '/znyy/deliver/student/buyDeliverHour',
        method: 'POST',
        params: data
    })
}

export const addStudentRegister = (data)=>{
    return request({
        url: '/deliver/web/common/addStudentRegister',
        method: 'POST',
        data: data
    })
}
// 回显详情课程表
export const getTableInfo = (data)=>{
    return request({
        url: '/deliver/web/common/getTableInfo',
        method: 'POST',
        params: data
    })
}

// 新增试课记录表
export const addTrialClassRecord = (data)=>{
    return request({
        url: '/deliver/web/common/addTrialClassRecord',
        method: 'POST',
        data: data
    })
}
