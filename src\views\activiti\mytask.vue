<template>
  <div class="app-container">
    <el-table v-loading="loading" :data="tastList">
      <el-table-column label="编号" align="center" prop="merchantCode"/>
      <el-table-column label="登录账号" align="center" prop="loginName"/>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="examineAndApprove (scope.row)"
          >审批
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="名称" align="center" prop="merchantName"/>
      <el-table-column label="负责人" align="center" prop="realName"/>
      <el-table-column label="开通金额" align="center" prop="openMoney"/>
      <el-table-column label="流程ID" align="center" prop="taskId"/>
      <el-table-column label="任务节点名称" align="center" prop="taskName"/>
      <el-table-column label="办理人" align="center" prop="assignee"/>
      <el-table-column label="创建时间" align="center" prop="createDate"/>
    </el-table>

    <!-- 审批对话框 -->
    <el-dialog :title="title" :visible.sync="open" v-if="open"  width="70%" append-to-body>
      <el-form
        :rules="rules"
        :model="form" ref="form"
        label-position="left"
        label-width="120px"
        style="width: 100%"
      >
        <el-form-item label="托管中心级别：" prop="rank">
          <el-col :xs="24" :span="12">
            <el-select v-model="form.rank" placeholder="请选择" style="width: 100%">
              <el-option
                v-for="item in [
                { value: 'A', label: 'A级别', disabled: true},
                { value: 'B', label: 'B级别', disabled: true},
                { value: 'C', label: 'C级别', disabled: true},
                { value: 'D', label: 'D级别', disabled: false},
                { value: 'E', label: 'E级别', disabled: false},
                { value: 'F', label: 'F级别', disabled: false},
              ]"
                :key="item.value"
                :label="item.label"
                :value="item.value"
                :disabled="item.disabled"
              ></el-option>
            </el-select>
          </el-col>
        </el-form-item>
        <el-form-item label="审核" prop="agree">
          <el-col :xs="24" :span="12">
            <el-select v-model="form.agree" placeholder="请选择" style="width: 100%">
              <el-option
                v-for="item in [
                { value: 1, label: '通过' },
                { value: 0, label: '不通过' },
              ]"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-col>
        </el-form-item>
        <el-form-item label="审核意见" prop="checkReason">
          <el-col :xs="24" :span="12">
            <el-input type="textarea" resize="none" :rows="4" v-model="form.checkReason"/>
          </el-col>
        </el-form-item>
      </el-form>

<!--      <el-form :model="form" ref="form" label-width="100px" class="demo-dynamic">
        <el-form-item
          v-for="(domain, index) in form.formData"
          :label="domain.controlLable"
          :key="index"
        >
          <el-radio-group v-model="domain.controlValue" v-if="'radio'==domain.controlType">
            <el-radio v-for="(defaults,indexd) in domain.controlDefault.split('&#45;&#45;__&#45;&#45;')"
                      :label=indexd
                      :key="indexd"
            >{{ defaults }}

            </el-radio>

          </el-radio-group>
          <el-input type="textarea" v-model="domain.controlValue" v-if="'textarea'==domain.controlType"
          ></el-input>
        </el-form-item>
      </el-form>-->

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>


import myTaskApi from '@/api/activiti/myTask'
import { pageParamNames } from '@/utils/constants'

export default {
  data() {
    return {
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      // 表格数据
      tastList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10
      },
      // 表单参数
      form: {
        taskId: null,
        rank: null,
        agree: null,
        checkReason: null
      },
      // 表单校验
      rules: {
        rank: [{
          required: true,
          message: '必填',
          trigger: 'blur'
        }],
        agree: [{
          required: true,
          message: '必填',
          trigger: 'blur'
        }],
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询请假列表 */
    getList() {
      this.loading = true
      this.queryParams.pageNum = this.tablePage.currentPage
      this.queryParams.pageSize = this.tablePage.size
      myTaskApi.pageList(this.queryParams).then(res => {
        this.tastList = res.data
        this.loading = false
      })
    },

    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        taskId: null,
        rank: null,
        agree: null,
        checkReason: null
      }
    },

    /** 审批按钮操作 */
    examineAndApprove(row) {
      console.log(row)
      this.reset()
      this.form.taskId = row.taskId;
      this.open = true;
    },
    /** 提交按钮 */
    submitForm() {
      myTaskApi.approve(this.form).then(response => {
        this.$message.success('审批成功')
        this.open = false
        this.getList()
      })
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val
      this.getPageList()
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val
      this.getPageList()
    }
  }
}
</script>
