
import request from '@/utils/request'

export default {

  // 分页查询
  pageList(data) {
    return request({
      url: '/paper/web/knowledge/list',
      method: 'GET',
      params: data
    })
  },
//添加
  create(data) {
    return request({
      url: '/paper/web/knowledge',
      method: 'POST',
      data
    })
  },
  //编辑
  edit(data) {
    return request({
      url: '/paper/web/knowledge',
      method: 'PUT',
      data
    })
  },
  treeList(data) {
    return request({
      url: '/paper/web/knowledge/tree',
      method: 'GET',
      params: data
    })
  },
  deleteBySubjectId(subjectId){
    return request({
      url: '/paper/web/knowledge/deleteBySubjectId',
      method: 'DELETE',
      params: {
        subjectId:subjectId
      }
    })
  },
  deleteById(id){
    return request({
      url: '/paper/web/knowledge/deleteById',
      method: 'DELETE',
      params: {
        id:id
      }
    })
  },
}
