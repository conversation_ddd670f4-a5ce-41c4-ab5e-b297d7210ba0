<template>
  <div class="app-container">
    <!-- 搜索 -->
    <el-form :inline="true" ref="form" class="SearchForm">
      <el-row style="padding: 20px 30px 0 30px">
        <el-col :span="5" :xs="24">
          <el-form-item label="所属课程分类:">
            <router-link :to="{ path: 'courseCategoryList' }" class="blue lh36">{{ categoryName }}</router-link>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="课程类型编号:">
            <el-input v-model="dataQuery.bigClassCode" @keyup.enter.native="fetchData()" placeholder="请输入课程类型编号："
              clearable></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="8" :xs="24">
          <el-form-item label="课程类型名称:">
            <el-input v-model="dataQuery.bigClassName" @keyup.enter.native="fetchData()" placeholder="请输入课程类型名称:"
              clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="3" :xs="24" style="text-align: right">
          <el-button type="warning" icon="el-icon-search" @click="fetchData01()">搜索</el-button>
        </el-col>
      </el-row>

    </el-form>

    <div class="SearchForm">
      <!-- 添加 -->
      <div class="btn-add" style="margin-bottom: 20px">
        <el-button size="small" type="primary" icon="el-icon-plus" @click="clickAdd">添加</el-button>
      </div>
      <!-- 表格 -->
      <el-table class="common-table" :data="tableData" stripe border
        :default-sort="{ prop: 'addTime', order: 'descending' }">
        <el-table-column prop="bigClassCode" label="课程类型编号" sortable></el-table-column>
        <el-table-column prop="bigClassName" label="课程类型名称" sortable></el-table-column>
        <el-table-column prop="id" label="操作" sortable width="">
          <template slot-scope="scope">
            <el-button type="success" size="mini" icon="el-icon-edit-outline"
              @click="handleUpdate(scope.row.id)">编辑</el-button>
            <el-button type="warning" size="mini" icon="el-icon-switch-button" v-if="scope.row.isEnable === 0"
              @click="courseStatus(scope.row.id,scope.row.isEnable)">开通</el-button>
            <el-button type="danger" size="mini" icon="el-icon-video-pause" v-else
              @click="courseStatus(scope.row.id,scope.row.isEnable)">暂停</el-button>
          </template>
        </el-table-column>
        <el-table-column prop="addTime" label="添加时间" sortable></el-table-column>
        <el-table-column prop="isEnable" label="状态" sortable>
          <template slot-scope="scope">
            <span class="green" v-if="scope.row.isEnable === 1">开通</span>
            <span class="red" v-else>暂停</span>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <el-col :span="20">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>

    <!-- 编辑或者添加弹窗 -->
    <el-dialog :title="addOrUpdate ? '添加课程类型' : '编辑课程类型'" :visible.sync="dialogVisible" width="70%"
      :close-on-click-modal="false" @close="close">
      <el-form :ref="addOrUpdate ? 'addCourseData' : 'updateActive'" :rules="rules"
        :model="addOrUpdate ? addCourseData : updateActive" label-position="left" label-width="120px"
        style="width: 100%;">
        <el-form-item label="课程类型名称" prop="bigClassName">
          <el-col :xs="24" :lg="18">
            <el-input v-if="addOrUpdate" v-model="addCourseData.bigClassName" />
            <el-input v-if="!addOrUpdate" v-model="updateActive.bigClassName" />
          </el-col>
        </el-form-item>
        <el-form-item label="所属分类" prop="categoryCode">
          <el-col :xs="24" :lg="18">
            <el-select style="width: 100%;" v-if="addOrUpdate" v-model="addCourseData.categoryCode" filterable
              value-key="value" placeholder="请选择">
              <el-option v-for="(item, index) in categoryType" :key="index" :label="item.label" :value="item.value" />
            </el-select>
            <el-select style="width: 100%;" v-if="!addOrUpdate" v-model="updateActive.categoryCode" filterable
              value-key="value" placeholder="请选择">
              <el-option v-for="(item, index) in categoryType" :key="index" :label="item.label" :value="item.value" />
            </el-select>
          </el-col>
        </el-form-item>
        <el-form-item label="课程类型描述" prop="classDescription">
          <el-col :xs="24" :lg="18">
            <el-input type="textarea" resize="none" :rows="4" v-if="addOrUpdate"
              v-model="addCourseData.classDescription" />
            <el-input type="textarea" resize="none" :rows="4" v-if="!addOrUpdate"
              v-model="updateActive.classDescription" />
          </el-col>
        </el-form-item>
        <el-form-item label="状态" prop="isEnable">
          <template>
            <el-radio v-model="radio" label="1" @change="change(radio)">开通</el-radio>
            <el-radio v-model="radio" label="0" @change="change(radio)">暂停</el-radio>
          </template>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button v-if="addOrUpdate" size="mini" type="primary" @click="addActiveFun('addCourseData')">新增</el-button>
        <el-button v-if="!addOrUpdate" size="mini" type="primary"
          @click="updateActiveFun('updateActive')">修改</el-button>
        <el-button size="mini" @click="close">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import courseApi from '@/api/courseChildren'
import {
  pageParamNames
} from "@/utils/constants";
export default {
  data() {
    return {
      categoryType: [], // 所属分类
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      tableLoading: false,
      dataQuery: {
        bigClassCode: '',
        bigClassName: '',
        categoryCode: '',
      },
      activeType: [], // 活动类型

      tableData: [], //表格数据
      dialogVisible: false, // 修改弹窗是否展示
      addOrUpdate: true, // 是新增还是修改
      addCourseData: {}, // 新增课程
      updateActive: {}, // 修改数据
      rules: { // 表单提交规则
        bigClassName: [{
          required: true,
          message: '请填写课程类型名称',
          trigger: 'blur'
        }],
        classDescription: [{
          required: true,
          message: '请填写课程类型描述',
          trigger: 'blur'
        }],
        isEnable: [{
          required: true,
          message: '必填',
          trigger: 'change'
        }],
        categoryCode: [{
          required: true,
          message: '必填',
          trigger: 'change'
        }]
      },
      radio: '0', //单选框状态 值必须是字符串
      memberId: undefined,
      categoryName: '',
      categoryCode: ''
    }

  },
  created() {
    this.fetchData()
    // 获取所属分类
    this.getCategoryType()
  },
  methods: {
    // 获取分类返回类型
    getCategoryType() {
      courseApi.categoryType().then(res => {
        this.categoryType = res.data
      })
    },
    fetchData01() {
      this.tablePage = {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      }
      this.fetchData();
    },
    // 查询表格列表
    fetchData() {
      const that = this;
      that.tableLoading = true;
      that.categoryName = window.localStorage.getItem("categoryName");
      that.categoryCode = window.localStorage.getItem("categoryCode");
      that.dataQuery.categoryCode = window.localStorage.getItem("categoryCode");
      courseApi.courseList(that.tablePage.currentPage, that.tablePage.size, that.dataQuery).then(res => {
        that.tableData = res.data.data;
        that.tableLoading = false;
        // 设置后台返回的分页参数
        pageParamNames.forEach(name => that.$set(that.tablePage, name, parseInt(res.data[name])))
      });
    },
    //添加操作
    clickAdd() {
      this.addCourseData = {
        'bigClassCode': '',
        'bigClassName': '',
        'categoryCode': '',
        'classDescription': '',
        'isEnable': 0,
      }
      this.radio = '0'
      this.dialogVisible = true
      this.addOrUpdate = true
      this.$nextTick(() => this.$refs['addCourseData'].clearValidate())
    },
    // 新增课程提交
    addActiveFun(ele) {
      const that = this
      that.$refs[ele].validate(valid => { // 表单验证
        if (valid) {
          const loading = this.$loading({
            lock: true,
            text: '新增课程类型',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          })
          courseApi.addCourse(that.addCourseData).then(() => {
            that.dialogVisible = false
            loading.close()
            that.$nextTick(() => that.fetchData())
            that.$message.success('新增课程成功')
          }).catch(err => {
            loading.close()
          })
        } else {
          console.log('error submit!!')
          //loading.close();
          return false
        }
      })
    },
    // 点击编辑按钮
    handleUpdate(id) {
      const that = this
      that.dialogVisible = true
      that.addOrUpdate = false
      courseApi.queryActive(id).then(res => {
        that.updateActive = res.data
        console.log(that.updateActive)
        that.radio = that.updateActive.isEnable.toString(); //状态回显
      }).catch(err => {

      })
    },
    // 状态改变事件
    change(radio) {
      if (radio == "1") {
        this.addCourseData.isEnable = 1;
      } else {
        this.addCourseData.isEnable = 0;
      }
    },
    // 修改课程提交
    updateActiveFun(ele) {
      const that = this
      that.$refs[ele].validate(valid => { // 表单验证
        if (valid) {
          that.updateActive.isEnable = that.radio;
          console.log(that.updateActive)
          const loading = this.$loading({
            lock: true,
            text: '修改课程信息提交',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          })
          courseApi.updateCourse(that.updateActive).then(() => {
            that.dialogVisible = false
            loading.close()
            that.$nextTick(() => that.fetchData())
            that.$message.success('修改课程成功')
          }).catch(err => {
            // 关闭提示弹框
            loading.close()
          })
        } else {
          console.log('error submit!!')
          // loading.close();
          return false
        }
      })
    },
    // 课程开通与暂停
    courseStatus(id, status) {
      if (status == 0) {
        status = 1;
      } else {
        status = 0
      }
      console.log(typeof status)
      const that = this;
      this.$confirm('确定操作吗?', '修改状态', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        courseApi.updateStatus(id, status).then(res => {
          that.$nextTick(() => that.fetchData())
          that.$message.success('修改成功!')
        }).catch(err => {

        })
      }).catch(err => {

      })
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val;
      this.fetchData();
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val;
      this.fetchData();
    },
    // 关闭弹窗
    close() {
      this.dialogVisible = false
    }
  }
}
</script>

<style>
.lh36 {
  line-height: 36px;
  font-size: 14px;
}

.SearchForm {
  width: 100%;
  margin-bottom: 15px;
  background: #fff;
  border-radius: 6px;
  padding: 5px 0;
  padding-left: 5px;
  box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.2);
}

.btn-add {
  padding: 5px;
}

.red {
  color: red;
}

.green {
  color: green;
}

.course-table {
  text-align: center;
}

.course-table td,
.course-table th {
  padding: 5px 0;
  text-align: center;
}

.course-table button {
  padding: 2px;
}

.icon-stop {
  float: left;
  display: inline-block;
  width: 12px;
  height: 12px;
  background: url("../../icons/stop.png") no-repeat top center/contain;
}

.mt22 {
  margin-top: 22px;
}

.blue {
  margin-right: 50px;
  color: #409eff;
}

@media (max-width: 767px) {
  .el-message-box {
    width: 80% !important;
  }
}
</style>
