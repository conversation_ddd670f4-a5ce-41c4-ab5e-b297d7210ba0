/**
 * 分润级别接口
 */
import request from '@/utils/request'

export default {
  // 获取风控行为类型
  getRiskType() {
    return request({
      url: '/znyy/riskAuthCode/getRiskType',
      method: 'GET'
    })
  },
  save(data) {
    return request({
      url: '/znyy/riskAuthCode/saveNew',
      method: 'POST',
      params: {
        authCodeType: data.authCodeType,
        authCodeNum: data.authCodeNum,
        authCodeEndDate: data.authCodeEndDate
      }
    })
  },
  pageList(data) {
    return request({
      url: '/znyy/riskAuthCode/pageList',
      method: 'GET',
      params: data
    })
  },
  delete(id) {
    return request({
      url: '/znyy/riskAuthCode/delete',
      method: 'DELETE',
      params: {
        id: id
      }
    })
  },
  bindingCode(authCode) {
    return request({
      url: '/znyy/riskAuthCode/bindingRiskCodeCode',
      method: 'PUT',
      params: {
        authCode: authCode
      }
    })
  },
}
