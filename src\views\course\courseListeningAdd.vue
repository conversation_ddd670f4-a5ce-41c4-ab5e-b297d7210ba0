<template>
  <div class="app-container">
    <!-- 搜索 -->
    <div>
      <!-- <el-form
        :ref="addOrUpdate ? 'addReadingData' : 'updateReadingData'"
        :rules="rulseReading"
        :model="addOrUpdate ? addReadingData : updateReadingData"
        label-position="left"
        label-width="120px"
        style="width: 80%"
      >
        <el-form-item label="题目类型" >
          <template>
            <el-radio v-model="radio" label="0" >
              {{ courseName }}</el-radio
            >
          </template>
        </el-form-item>
        <el-form-item label="文章标题" prop="topicTitle">
          <el-col :span="18">
            <el-input v-if="addOrUpdate" v-model="addReadingData.topicTitle">
            </el-input>
            <el-input
              v-if="!addOrUpdate"
              v-model="updateReadingData.topicTitle"
            >
            </el-input>
          </el-col>
        </el-form-item>
        <el-form-item label="显示名称" prop="showName">
          <el-col :span="18">
            <el-input v-if="addOrUpdate" v-model="addReadingData.showName">
            </el-input>
            <el-input v-if="!addOrUpdate" v-model="updateReadingData.showName">
            </el-input>
          </el-col>
        </el-form-item>
        <el-form-item label="单词词库" prop="topicContent">
          <el-input
            type="textarea"
            resize="none"
            :rows="4"
            v-if="addOrUpdate"
            v-model="addReadingData.topicContent"
          />
          <el-input
            type="textarea"
            resize="none"
            :rows="4"
            v-if="!addOrUpdate"
            v-model="updateReadingData.topicContent"
          />
        </el-form-item>
        <el-form-item label="建议时间" prop="suggestedTime">
          <el-col :span="18">
            <el-input v-if="addOrUpdate" v-model="addReadingData.suggestedTime">
            </el-input>
            <el-input
              v-if="!addOrUpdate"
              v-model="updateReadingData.suggestedTime"
            >
            </el-input>
          </el-col>
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-col :span="18">
            <el-input v-if="addOrUpdate" v-model="addReadingData.sort">
            </el-input>
            <el-input v-if="!addOrUpdate" v-model="updateReadingData.sort">
            </el-input>
          </el-col>
        </el-form-item>
        <el-form-item label="题目难度" prop="level">
          <template>
            <el-radio
              v-if="addOrUpdate"
              v-model="addReadingData.level"
              label="1"
              >★
            </el-radio>
            <el-radio
              v-if="addOrUpdate"
              v-model="addReadingData.level"
              label="2"
            >
              ★★</el-radio
            >
            <el-radio
              v-if="addOrUpdate"
              v-model="addReadingData.level"
              label="3"
            >
              ★★★</el-radio
            >
            <el-radio
              v-if="addOrUpdate"
              v-model="addReadingData.level"
              label="4"
            >
              ★★★★</el-radio
            >
            <el-radio
              v-if="addOrUpdate"
              v-model="addReadingData.level"
              label="5"
            >
              ★★★★★</el-radio
            >
            <el-radio
              v-if="!addOrUpdate"
              v-model="updateReadingData.level"
              label="1"
              >★
            </el-radio>
            <el-radio
              v-if="!addOrUpdate"
              v-model="updateReadingData.level"
              label="2"
            >
              ★★</el-radio
            >
            <el-radio
              v-if="!addOrUpdate"
              v-model="updateReadingData.level"
              label="3"
            >
              ★★★</el-radio
            >
            <el-radio
              v-if="!addOrUpdate"
              v-model="updateReadingData.level"
              label="4"
            >
              ★★★★</el-radio
            >
            <el-radio
              v-if="!addOrUpdate"
              v-model="updateReadingData.level"
              label="5"
            >
              ★★★★★</el-radio
            >
          </template>
        </el-form-item>
      </el-form> -->
    </div>

    <div class="mb20" v-for="(item, index) in items" :key="index">
      <el-card style="background-color: aliceblue;">
        <el-row type="flex" justify="center">
          <el-col :xs="24" :lg="16">
            <el-form :ref="'rulesAnswers'+index" label-position="right" label-width="120px" :model="item">
              <el-form-item label="题目id" prop="id" v-show="false">
                <el-col :span="18">
                  <el-input v-model="item.id"></el-input>
                </el-col>
              </el-form-item>

              <el-form-item label="课程编号" prop="courseCode" v-show="false">
                <el-col :span="18">
                  <el-input v-model="item.courseCode"></el-input>
                </el-col>
              </el-form-item>
              <el-form-item label="题目编号" prop="topicCode" v-show="false">
                <el-col :span="18">
                  <el-input v-model="item.topicCode"></el-input>
                </el-col>
              </el-form-item>
              <el-row>
                <el-col :span="19">
                  <el-form-item label="题目小节" prop="topicPart" :rules="[{
            required: true,
            message: '必填',
            trigger: 'blur',
          }]">

                    <el-input v-model="item.topicPart"></el-input>

                  </el-form-item>
                </el-col>
                <el-col :span="3" :offset="1">
                  <el-button v-if="addOrUpdate" type="danger" icon="el-icon-close" @click="updateDeleteForm(item)">删除</el-button>
                </el-col>
              </el-row>

              <el-form-item label="题目序号" prop="serialNumber" :rules="[{
            required: true,
            message: '必填',
            trigger: 'blur',
          }]">
                <el-col :span="18">
                  <el-input v-model="item.serialNumber"></el-input>
                </el-col>
              </el-form-item>
              <el-form-item label="题目标题" prop="questionText" :rules="[{
            required: true,
            message: '必填',
            trigger: 'blur',
          }]">
                <el-col :span="18">
                  <el-input v-model="item.questionText" type="textarea"></el-input>
                </el-col>
              </el-form-item>
              <el-form-item label="答案A" prop="answerForA" :rules="[{
            required: true,
            message: '必填',
            trigger: 'blur',
          }]">

                <el-col :span="18">
                  <el-input v-model="item.answerForA"></el-input>
                </el-col>
              </el-form-item>
              <el-form-item label="答案B" prop="answerForB" :rules="[{
            required: true,
            message: '必填',
            trigger: 'blur',
          }]">
                <el-col :span="18">
                  <el-input v-model="item.answerForB"></el-input>
                </el-col>
              </el-form-item>
              <el-form-item label="答案C" prop="answerForC" :rules="[{
            required: true,
            message: '必填',
            trigger: 'blur',
          }]">
                <el-col :span="18">
                  <el-input v-model="item.answerForC"></el-input>
                </el-col>
              </el-form-item>
              <el-form-item label="答案D" prop="answerForD">
                <!-- :rules="[{
            required: true,
            message: '必填',
            trigger: 'blur',
          }]" -->
                <el-col :span="18">
                  <el-input v-model="item.answerForD" ></el-input>
                </el-col>

              </el-form-item>
              <el-row>
                <el-col :span="19">
                  <el-form-item label="题目正确答案" prop="correctAnswer" :rules="[{
                      required: true,
                      message: '必填',
                      trigger: 'blur',
                    }]">
                      <el-input v-model="item.correctAnswer" maxlength="5" ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="3" :offset="1">
                  <el-button type="primary" icon="el-icon-plus" @click="addForm" v-if="addOrUpdate">新增</el-button>
                </el-col>
              </el-row>

            </el-form>
          </el-col>
        </el-row>
      </el-card>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-col :offset="7">
        <el-button v-if="addOrUpdate" size="mini" type="primary" @click="addActiveFun('rulesAnswers')">新增</el-button>
        <el-button v-if="!addOrUpdate" size="mini" type="primary" @click="updateReadingFun('rulesAnswers')">编辑</el-button>
      </el-col>
    </div>
  </div>
</template>

<script>
  import courseListeningApi from "@/api/courseListening";
  export default {
    data() {
      return {
        // 分页
        addReadingData: {},
        updateReadingData: {},
        courseName: '',
        courseCode: '',
        addOrUpdate: true,
        items: [],

        items: [{
          topicCode: '',
          questionText: "",
          answerForA: "",
          answerForB: "",
          answerForC: "",
          answerForD: "",
          correctAnswer: "",
          topicPart: "",
          courseCode: ""
        }],

        radio: 0,
        rulesAnswers: {}

      }

    },

    created() {
      const that = this;
      that.tableLoading = true;
      that.courseName = window.localStorage.getItem("courseListeningAddName");
      that.courseCode = window.localStorage.getItem("courseListeningAddCode");
      that.addOrUpdate = window.localStorage.getItem("courseListeningAddAddOrUpdate");
      if (this.items.length >= 1) {
        this.items.forEach(val => {
          val.courseCode = window.localStorage.getItem("courseListeningAddCode");
        })
      }
      //this.openEdit();
    },
    methods: {
      // 查询+搜索课程列表
      //删除表单
      updateDeleteForm: function(ele) {
        if (this.items.length === 1) {
          this.$message({
            message: '至少要留一个',
            type: 'warning',
            duration: 1000
          })
          return
        }
        var index = this.items.indexOf(ele);
        console.log(index);
        if (index !== -1) {
          this.items.splice(index, 1)
        }
      },

      //增加表单
      addForm: function() {
        this.items.push({
          fillNumber: '',
          questionText: "",
          answerForA: "",
          answerForB: "",
          answerForC: "",
          answerForD: "",
          correctAnswer: "",
          answerRemark: ""
        });
        this.items.forEach(val => {
          console.log(val.answerForA + "wyy");
        })
      },
      //编辑
      // 打开编辑听力
      openEdit() {
        if (this.addOrUpdate == false) {
          var id = this.$route.query.id
          courseListeningApi.queryListening(id).then((res) => {
            this.addOrUpdate = false;
            this.items = res.data.c;

          });
        }
      },

      // 听力提交
      addActiveFun(ele) {
        const that = this;
        let _self = this;
        let newArr = [];
        //验证iitems集合
        that.items.forEach((item, index) => {
          item.courseCode = that.$route.query.courseCode;
          checkForm('rulesAnswers' + index);
        })

        function checkForm(arrName) {

          var result = new Promise(function(resolve, feject) {
            _self.$refs[arrName][0].validate((valid) => {
              if (valid) {
                resolve();
              } else {
                reject();
              }
            })
          })
          newArr.push(result);
        };

        Promise.all(newArr).then(function() {
          setTimeout(function() {
            const loading = _self.$loading({
              lock: true,
              text: "增加提交",
              spinner: "el-icon-loading",
              background: "rgba(0, 0, 0, 0.7)",
            });
            const data = {
              listeningCos: that.items
            }
            courseListeningApi
              .addCourseListening(data)
              .then((res) => {
                that.dialogVisible = false;
                loading.close();
                that.getList();
                that.items = [{
                  topicCode: '',
                  questionText: "",
                  answerForA: "",
                  answerForB: "",
                  answerForC: "",
                  answerForD: "",
                  correctAnswer: "",
                  topicPart: "",
                  courseCode: ""
                }];
        that.$store.dispatch('delVisitedViews', this.$route);
        that.$router.go(-1);
                that.$message.success(res.data.data);
              })
              .catch((err) => {
                loading.close();
              });
          }, 1000);
        }).catch(function() {
          that.$message({
            message: "操作失败",
            type: "error"
          })
        })
      },
      //修改
      updateReadingFun(ele, els) {
        const that = this;
        that.$refs[ele].validate((valid) => {
          // 表单验证
          if (valid) {

            const loading = _self.$loading({
              lock: true,
              text: "修改",
              spinner: "el-icon-loading",
              background: "rgba(0, 0, 0, 0.7)",
            });
            const data = {
              courseReadingCo: that.updateReadingData,
              courseReadingAnswerList: that.items
            }
            courseListeningApi
              .updateWordLevel(data)
              .then((res) => {
                that.dialogVisible = false;
                loading.close();
                that.getList();
              })
              .catch((err) => {
                loading.close();
              });
          } else {
            console.log("error submit!!");
            return false;
          }
        });
      },
      getList() {
        this.$router.push({
          path: "/course/courseListening",
          query: {
            courseCode: this.courseCode,
            courseName: this.courseName,
            courseContentType: "Listening"
          }
        });

      },
      // 状态改变事件
      change(radio) {
        if (radio == "1") {
          this.addReadingData.isEnable = 1;
        } else {
          this.addReadingData.isEnable = 0;
        }
      },

    }
  }
</script>

<style>
  .lh36 {
    line-height: 36px;
    font-size: 14px;
  }

  .SearchForm {
    width: 100%;
    margin-bottom: 15px;
    background: #fff;
    border-radius: 6px;
    padding: 5px 0;
    padding-left: 5px;
    box-shadow: 1px 1px 3px rgba(0, 0, 0, .2);
  }

  .btn-add {
    padding: 5px;
  }

  .red {
    color: red;
  }

  .green {
    color: green;
  }

  .course-table {
    text-align: center;
  }

  .course-table td,
  .course-table th {
    padding: 5px 0;
    text-align: center;
  }

  .course-table button {
    padding: 2px;
  }

  .icon-stop {
    float: left;
    display: inline-block;
    width: 12px;
    height: 12px;
    background: url('../../icons/stop.png') no-repeat top center/contain;
  }

  .mt22 {
    margin-top: 22px;
  }
</style>
