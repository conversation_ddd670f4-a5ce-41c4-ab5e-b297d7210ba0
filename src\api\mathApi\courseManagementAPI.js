import request from '@/utils/request';

export default {
  // 课程大类筛选项
  courseCategoriesAPI() {
    return request({
      url: '/znyy/curriculum/math',
      method: 'get'
    });
  },

  // 课程管理分页列表
  listCourseAPI(data) {
    return request({
      url: '/dyf/math/web/courseManagement/list',
      method: 'get',
      params: data
    });
  },

  // 新增课程
  addCourseAPI(data) {
    return request({
      url: '/dyf/math/web/courseManagement/saveOrUpdateCourse',
      method: 'post',
      data
    });
  },
  // 保存课程草稿
  saveDraftAPI(data) {
    return request({
      url: '/dyf/math/web/courseManagement/saveDraft',
      method: 'post',
      data
    });
  },
  // 保存课程草稿
  getCourseDetail(data) {
    return request({
      url: '/dyf/math/web/courseManagement/get-course-detail',
      method: 'get',
      params: data
    });
  },
  // 删除课程
  delCourseAPI(data) {
    return request({
      url: '/dyf/math/web/courseManagement/delete-course',
      method: 'delete',
      params: data
    });
  },
  // 获取视频列表
  getVideoListAPI(data) {
    return request({
      url: '/dyf/web/math/video/page',
      method: 'get',
      params: data
    });
  }
};