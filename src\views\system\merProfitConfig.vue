<template>
  <div class="app-container">
    <el-form :inline="true" style="margin-bottom: 20px">
      <el-form-item label="产品类型:">
        <el-select v-model="tablePage.productType" filterable value-key="value" @change="fetchData" placeholder="请选择产品类型"
          clearable>
          <el-option v-for="(item, index) in productTypeList" :key="index" :label="item.productTypeName"
            :value="item.productTypeValue" />
        </el-select>
      </el-form-item>
      <el-button @click="fetchData">搜索</el-button>
      <el-button type="primary" icon="el-icon-s-tools" v-if="checkPermission(['b:merProfit:config:save'])"
        @click="clickAdd">配置
      </el-button>
    </el-form>

    <el-table class="period-table" v-loading="tableLoading" :data="tableData" style="width: 100%; margin-bottom: 20px"
      row-key="id" border default-expand-all>
      <el-table-column prop="productType" label="产品类型" sortable>
        <template slot-scope="scope">
          {{ getProductType(scope.row.productType) }}
        </template>
      </el-table-column>
      <el-table-column prop="profitParty" label="分润方" sortable>
        <template slot-scope="scope">
          {{ getProfitParty(scope.row.profitParty) }}
        </template>
      </el-table-column>
      <el-table-column prop="id" label="操作" sortable width="200">
        <template slot-scope="scope">
          <el-button type="success" size="mini" icon="el-icon-edit-outline"
            v-if="checkPermission(['b:merProfit:config:save'])" @click="handleUpdate(scope.row.id)">
            编辑
          </el-button>
          <el-button type="danger" size="mini" icon="el-icon-delete" v-if="checkPermission(['b:merProfit:config:delete'])"
            @click="deleteProfitConfig(scope.row.id)">
            删除
          </el-button>
        </template>
      </el-table-column>
      <el-table-column prop="profitRatio" label="分润比例" sortable></el-table-column>
    </el-table>
    <!-- 添加弹窗 -->
    <el-dialog title="渠道分润配置" :visible.sync="dialogVisible" width="65%" :close-on-click-modal="false" @close="close">
      <el-form :rules="rules" :model="form" ref="form" label-position="right" label-width="120px">
        <el-form-item label="产品类型:" prop="productType">
          <el-select v-model="form.productType" filterable value-key="value" @change="checkProfitParty" placeholder="请选择"
            clearable>
            <el-option v-for="(item, index) in productTypeList" :key="index" :label="item.productTypeName"
              :value="item.productTypeValue" />
          </el-select>
        </el-form-item>
        <el-form-item label="分润方:" prop="profitParty">
          <el-select v-model="form.profitParty" filterable value-key="value" @change="checkProfitParty" placeholder="请选择"
            clearable>
            <el-option v-for="(item, index) in profitPartyList" :key="index" :label="item.profitPartyName"
              :value="item.profitPartyValue" />
          </el-select>
        </el-form-item>
        <el-form-item label="分润比例:" prop="profitRatio">
          <el-input-number v-model="form.profitRatio" :precision="2" :step="0.01" :max="1" :min="0.01"></el-input-number>
        </el-form-item>
        <span class="red">例如：分润比例80%就填写0.8</span>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="addActiveFun()">保存</el-button>
        <el-button size="mini" @click="close">关闭</el-button>
      </div>
    </el-dialog>

    <!-- 分页 -->
    <el-col :span="20">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>
  </div>
</template>

<script>
import merProfitConfigApi from "@/api/merProfitConfig";
import { pageParamNames } from "@/utils/constants";
import checkPermission from '@/utils/permission'

export default {
  name: "merProfitConfig",
  data() {
    return {
      form: {
        id: undefined,
        productType: undefined,
        profitParty: undefined,
        profitRatio: undefined
      },
      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null,
        productType: ""
      },
      productTypeList: [],
      profitPartyList: [],
      tableData: [],
      rules: {
        productType: [{
          required: true,
          message: "产品类型必选",
          trigger: "blur",
        },],
        profitParty: [{
          required: true,
          message: "分润方必选",
          trigger: "blur",
        },],
        profitRatio: [{
          required: true,
          message: "分润比例必填",
          trigger: "blur",
        },],
      },
      dialogVisible: false,
      addCourseData: {},
      showLoginAccount: false,
    };
  },
  created() {
    //获取渠道分润方列表
    merProfitConfigApi.getProfitPartyList().then((res) => {
      this.profitPartyList = res.data;
    })
    //获取渠道分润产品类型列表
    merProfitConfigApi.getProductTypeList().then((res) => {
      this.productTypeList = res.data;
    })
    this.fetchData();
  },
  methods: {
    checkPermission,
    //查询渠道分润配置列表
    fetchData() {
      this.tableLoading = true;
      merProfitConfigApi.pageList({
        "pageNum": this.tablePage.currentPage,
        "pageSize": this.tablePage.size,
        "productType": this.tablePage.productType
      }).then((res) => {
        this.tableData = res.data.data;
        this.tableLoading = false;
        // 设置后台返回的分页参数
        pageParamNames.forEach((name) =>
          this.$set(this.tablePage, name, parseInt(res.data[name]))
        );
      });
    },
    //分润方字典回显
    getProfitParty(profitParty) {
      for (let index in this.profitPartyList) {
        if (this.profitPartyList[index].profitPartyValue === profitParty) {
          return this.profitPartyList[index].profitPartyName;
        }
      }
      return "未知分润方";
    },
    //产品类型字典回显
    getProductType(productType) {
      for (let index in this.productTypeList) {
        if (this.productTypeList[index].productTypeValue === productType) {
          return this.productTypeList[index].productTypeName;
        }
      }
      return "未知产品类型";
    },
    //新增操作
    clickAdd() {
      this.dialogVisible = true;
      this.form = {
        id: undefined,
        productType: undefined,
        profitParty: undefined,
        profitRatio: undefined
      }
    },
    // 点击编辑按钮
    handleUpdate(id) {
      this.dialogVisible = true;
      merProfitConfigApi.getMerProfitConfigById(id).then((res) => {
        this.form = res.data;
      })
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val;
      this.fetchData();
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val;
      this.fetchData();
    },
    //提交到后事业部级别的增加
    addActiveFun() {
      this.$refs["form"].validate((valid) => {
        // 表单验证
        if (valid) {
          const loading = this.$loading({
            lock: true,
            text: "保存渠道分润配置",
            spinner: "el-icon-loading",
            background: "rgba(0, 0, 0, 0.7)",
          });
          merProfitConfigApi.save(this.form).then(() => {
            this.dialogVisible = false;
            loading.close();
            this.$nextTick(() => this.fetchData());
            this.$message.success("保存渠道分润配置成功");
          }).catch((err) => {
            loading.close();
          });
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    //关闭弹框
    close() {
      this.dialogVisible = false;
      this.form = {
        id: undefined,
        productType: undefined,
        profitParty: undefined,
        profitRatio: undefined
      }
      this.$refs['form'].resetFields();
    },
    //删除渠道分润配置
    deleteProfitConfig(id) {
      this.$confirm("确定操作吗?", "删除操作", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        merProfitConfigApi.delete(id).then((res) => {
          this.$nextTick(() => this.fetchData());
          this.$message.success("删除成功!");
        })
      })
    },
    //动态回显分润配置
    checkProfitParty() {
      if (this.form.profitParty && this.form.productType) {
        merProfitConfigApi.getProfitPartyByProfitPartyAndProductType(this.form.profitParty, this.form.productType).then(res => {
          if (res.data) {
            this.form = res.data
          } else {
            this.form.id = undefined
            this.form.profitRatio = undefined
          }
        })
      } else {
        this.form.id = undefined
        this.form.profitRatio = undefined
      }
    }
  },
};
</script>

<style scoped>
.period-table td,
.period-table th {
  text-align: center;
}

.red {
  color: red;
  margin-left: 45px;
}
</style>
