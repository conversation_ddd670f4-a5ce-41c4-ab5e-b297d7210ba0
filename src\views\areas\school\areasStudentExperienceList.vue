<template>
  <div class="app-container">
    <el-form :inline="true" class="container-card" label-width="100px" label-position="left" style="padding: 30px 30px 10px;">
      <el-row>
        <el-col :span="6" :xs="24">
          <el-form-item label="学员编号：">
            <el-input v-model="dataQuery.studentCode" placeholder="请输入学员编号" clearable/>
          </el-form-item>
        </el-col>
        <el-col :span="6" :xs="24">
          <el-form-item label="登录账号：">
            <el-input v-model="dataQuery.loginName" placeholder="请输入登录账号" clearable/>
          </el-form-item>
        </el-col>
        <el-col :span="6" :xs="24">
          <el-form-item label="姓名：">
            <el-input v-model="dataQuery.realName" placeholder="请输入姓名" clearable/>
          </el-form-item>
        </el-col>
        <el-col :span="6" :xs="24" style="text-align: right">
          <el-form-item>
            <el-button type="primary" size="small" icon="el-icon-search" @click="fetchData01()">搜索</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <el-table class="common-table" v-loading="tableLoading" :data="tableData" style="width: 100%;margin-bottom: 20px;" row-key="id" border
              default-expand-all :tree-props="{list: 'children', hasChildren: 'true'}">
      <el-table-column prop="studentCode" label="学员编号" width="120"  sortable></el-table-column>
      <el-table-column prop="loginName" label="登录账号" width="120"  sortable></el-table-column>
      <el-table-column prop="withdrawnBonus" label="操作" width="200">
        <template slot-scope="scope">
          <el-button @click="openImage(scope.row.id,scope.row.realName)" type="success" size="mini" icon="el-icon-edit-outline">打印</el-button>
          <el-button type="success" size="mini" icon="el-icon-edit-outline" @click="editDetail(scope.row.id)">编辑</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="realName" label="姓名" sortable ></el-table-column>
      <el-table-column prop="memberPhone" label="号码" sortable ></el-table-column>
      <el-table-column prop="experienceCourse" label="体验课程" width="400" sortable></el-table-column>
      <el-table-column prop="title" label="词汇量水平" width="150" sortable></el-table-column>
      <el-table-column prop="startTime" label="开始时间" width="160"  sortable></el-table-column>
      <el-table-column prop="endTime" label="结束时间" width="160" sortable></el-table-column>
      
    </el-table>
    <!-- 分页 -->
    <el-col :span="24" style="overflow-x: auto;" :xs="24">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]" layout="total, sizes, prev, pager, next, jumper"
                     :total="tablePage.totalItems" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    </el-col>
    <div style="margin-bottom: 30px;"></div>
    <!-- 海报弹框 -->
    <el-dialog
      title="学员试课报告"
      :visible.sync="dialogVisible"
      width="40%"
      :before-close="handleClose">
      <el-image
        v-loading="loading"
        style="width: 100%; height: 800px"
        :src="url"
      ></el-image>
      <el-button type="primary" @click="download()">下载</el-button>
    </el-dialog>
  </div>
</template>

<script>
  import studentExperienceApi from "@/api/areasStudentExperienceList.js";
  import Tinymce from "@/components/Tinymce";
  import {  pageParamNames  } from "@/utils/constants";
  import ls from '@/api/sessionStorage'
  import grammarApi from "@/api/grammar";
  import studentForgetApi from "@/api/areasStudentForgetList";
  export default {
    data() {
      return {
        dialogVisible: false,
        url: '',
        loading: true,
        tableLoading: false,
        // 分页
        tablePage: {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null
        },
        tableData: [],
        dataQuery: {
          studentCode:'',
          merchantCode:'',
          startDate:'',
          endDate:'',
          loginName:'',
          realName:''
        },
        value1:'',
        exportLoading:false,
        id:"",
        realName:"",

      };
    },
    mounted() {


    },
    created() {
      this.fetchData();
    },
    methods: {
      downloadIamge(imgsrc, name) {
        //下载图片地址和图片名
        var image = new Image();

        image.setAttribute("crossOrigin", "anonymous");
        image.onload = function() {
          var canvas = document.createElement("canvas");
          canvas.width = image.width;
          canvas.height = image.height;
          var context = canvas.getContext("2d");
          context.drawImage(image, 0, 0, image.width, image.height);
          var url = canvas.toDataURL("image/png");

          var a = document.createElement("a");
          var event = new MouseEvent("click");
          a.download = name || "photo";
          a.href = url;
          a.dispatchEvent(event);
        };
        image.src = imgsrc;
      },

      download() {
        var name = new Date().getTime();
        this.downloadIamge(this.url, this.realName+'试课报告');

      },

      // 海报打印
      openImage(id,realName) {
        this.id=id;
        this.realName=realName;
        this.dialogVisible = true;
        this.loading = true;

        studentExperienceApi.download(id,realName).then(res => {
            this.url=res.data.file;
            this.loading = false;
        })

      },

      // 获取起始时间
      dateVal(e) {
        // console.log(e[0]);
        this.dataQuery.startDate = e[0]
        this.dataQuery.endDate = e[1]
      },

      fetchData01(){
         this.tablePage= {
          currentPage: 1,
          size: this.tablePage.size,
          totalPage: null,
          totalItems: null
        }
        this.fetchData();
      },
      // 查询提现列表
      fetchData() {
        const that = this;
        that.tableLoading = true
        studentExperienceApi.testResultList(that.tablePage.currentPage, that.tablePage.size, that.dataQuery).then(res => {
          that.tableData = res.data.data
          that.tableLoading = false
          // 设置后台返回的分页参数
          pageParamNames.forEach(name => that.$set(that.tablePage, name, parseInt(res.data[name])))
        })
      },
      // 分页
      handleSizeChange(val) {
        this.tablePage.size = val;
        this.fetchData();
      },
      handleCurrentChange(val) {
        this.tablePage.currentPage = val;
        this.fetchData();
      },

      //编辑
      editDetail(id){
        const that = this
        window.localStorage.setItem("experienceId",id);
        that.$router.push({
          path:'/student/studentExperienceEdit',
          query:{
            id:id
          }
        })
      },
    }
  };
</script>

<style>
  .container-card {
    padding: 15px 15px 15px 15px;
    margin-bottom: 15px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  }
  .period-table td,
  .period-table th{
    text-align: center;
  }
  .mt20{
    margin-top: 20px;
  }
  .red{
    color: red;
  }
  .green{
    color: green;
  }
</style>
