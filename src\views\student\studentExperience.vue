<template>
  <div class="app-container2" >
    <div class="bogbox">
      <div class="buttom clearfix">
<!--         <a style="background:#f0ad4e; margin-left:20px;" @click="getPdf()">下载</a> -->
        <a style="background:#3dab93" @click="printBtn()">打印</a>
        <a style="background:#f0ad4e; margin-left:20px;" @click="goback()">返回</a>
      </div>
      <section ref="print">
        <div id="print">
          <!-- <div id="pdfDom"> -->
          <div style="text-align:center">
            <img src="../../assets/imgs.png" class="top-img">
          </div>

          <div class="information clearfix" style="padding:10px;box-sizing: border-box">
            <div class="caption">体验信息</div>
            <div style="display: flex;justify-content: space-between;width: 100%;height: 90%;">
              <div class="leftBox">
                <div class="h3">学员信息</div>
                <div class="clearfix">
                  <ul class="stud-information">
                    <li>学员账号：&nbsp;&nbsp;&nbsp;{{ printData.studentCode }}</li>
                    <li>姓名：&nbsp;&nbsp;&nbsp;{{ printData.realName }}</li>
                    <li>电话：&nbsp;&nbsp;&nbsp;{{ printData.memberPhone }}</li>
                    <li>学校：&nbsp;&nbsp;&nbsp;{{ printData.school }}</li>
                    <li>年级：&nbsp;&nbsp;&nbsp;{{ printData.grade }} 年级</li>
                  </ul>
                </div>
              </div>
              <div class="rightBox">
                <div class="h3">体验课信息</div>
                <div class="clearfix">
                  <ul class="stud-information">
                    <li>词汇量水平：&nbsp;&nbsp;{{ printData.title }}&nbsp;</li>
                    <li>首测词汇量：&nbsp;&nbsp;{{ printData.wordLevel }}&nbsp;</li>
                    <li>体验次数：&nbsp;&nbsp;&nbsp;{{ printData.experienceNum }} </li>
                    <li>体验课程：&nbsp;&nbsp;&nbsp;{{ printData.experienceCourse }}</li>
                    <li>体验时间：&nbsp;&nbsp;&nbsp;{{ printData.expertTime }}</li>
                  </ul>
                </div>
              </div>
            </div>

          </div>

          <div class="information-img clearfix" style="height: 360px;padding-top:20px">
            <div class="caption">学习结果</div>
            <div class="list">
              <div class="item">
              <div><span style="font-size:18px;color:#000;">{{ printData.studyTime }}</span>分钟</div>
              <div>学习时间</div>
            </div>
              <div class="item">
                <div><span style="font-size:18px;color:#000;">{{ printData.experienceWordCount}}</span>个</div>
                <div>体验单词数量</div>
              </div>
              <div class="item">
                <div><span style="font-size:18px;color:#000;">{{printData.memory}}</span>个</div>
                <div>实记词汇</div>
              </div>
              <div class="item">
                <div><span style="font-size:18px;color:#000;">{{printData.oblivion}}</span>个</div>
                <div>遗忘词汇</div>
              </div>
              <div class="item" style="border:none">
                <div><span style="font-size:18px;color:#000;">{{printData.efficiency}}</span>个/分钟</div>
                <div>学习效率</div>
              </div>
            </div>
            <div class="charts">
              <div class="chart-wrapper" style="width: 50%;height: 200px;">
                <bar-chart :chart-data="lineChartData" width="100%" height="200px" />
              </div>
              <div class="chart-wrapper" style="width: 50%;height: 200px;">
                <bar-chart1 :chart-data="lineChartData" width="100%" height="200px" />
              </div>
            </div>
          </div>
          <div class="information clearfix" style="padding-bottom: 10px;border-radius: 0px">
            <div class="caption">学习经历</div>
            <p class="f16">{{ printData.skill }}</p>
            <div class="caption">学习情况反馈</div>
            <p class="f16">
              {{ printData.feedback }}
            </p>
            <div class="caption">学习方案</div>
            <p class="f16">{{ printData.program }}</p>
          </div>
        </div>
        <!-- </div> -->
      </section>

    </div>
  </div>
</template>

<script>

  import BarChart from '@/views/dashboard/admin/components/experienceChart'
  import BarChart1 from '@/views/dashboard/admin/components/experienceChart1'
  import ls from '@/api/sessionStorage'
  import studentExperienceApi from "@/api/areasStudentExperienceList";
  export default {
  components: {
    BarChart,
    BarChart1
  },
  data() {
    return {
      id:'',
      htmlTitle:'学员词汇量检测',
      lineChartData : {
          expectedData: [],
          actualData: [],
          xAxisData:[]
      },
      // 打印数据
      printData:[],
    }
  },
  created() {
    this.id = ls.getItem('testId');
    this.getDeatil(this.id)
  },
  onLoad(){
  },
  methods: {
    // 返回
    goback(){
      this.$store.dispatch('delVisitedViews', this.$route);
        this.$router.go(-1);//返回上一层
      // 删除本地存储
      // localStorage.removeItem('testId')
    },
    // 显示学员测试结果
    getDeatil(id) {
      const that = this
      studentExperienceApi.detail(id).then(res => {

        that.printData = res.data;
        console.log(that.printData)
      })
    },
    edit(id){
      const that = this
      window.localStorage.setItem("experienceId",id);
      that.$router.push({
        path:'/student/studentExperienceEdit',
        query:{
          id:id
        }
      })
    },
    // 打印
    printBtn() {
      // window.print();
      this.$print(this.$refs.print)
    },
  }
}
</script>

<style scoped lang="less">
.charts{
  width: 100%;
  display: flex;
  height: 200px;
}
.list{
  margin:20px 10px;
  width: 98%;
  height: 50px;
  display: flex;
  justify-content: space-between;
  border:1px solid #00a691;
  .item{
    height: 100%;
    width: 20%;
    border-right:1px solid #00a691;
    font-size:16px;
    color:#aaa;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }
}
.caption{
  width: 100%;
  height: 20px;
  margin-top:10px;
  font-weight: 900;
  font-size: 18px;
  margin-left:20px;
  padding-left:10px;
  border-left:5px solid #00a691;
  line-height: 20px;
  margin-bottom:30px;


}
.leftBox{
  height: 100%;
  width: 48%;
  border-radius: 15px;
  border:1px solid #00a691;
  position: relative;
  padding-top:10px;
.h3 {
  width: 100px;
  height: 30px;
  background: #00a691;
  position: absolute;
  top:-15px;
  left:50%;
  transform: translate(-50%,-0%);
  line-height: 30px;
  text-align: center;
  color:#fff;

}
}
.rightBox{
  height: 100%;
  width: 48%;
  border-radius: 15px;
  border:1px solid #00a691;
  position: relative;
  padding-top:10px;
.h3 {
  width: 100px;
  height: 30px;
  background: #00a691;
  position: absolute;
  color:#fff;
  top:-15px;
  left:50%;
  transform: translate(-50%,-0%);
  line-height: 30px;
  text-align: center;

}
}
  .app-container2{
    padding: 0 0 20px;
    background-color: #f3f3f4;
    color: #676a6c;
  }
  .buttom {
    width: 350px;
    color: #fff;
    cursor: pointer;
    position: absolute;
    right: 55px;
    top: 250px;
  }

  .buttom a {
    display: block;
    float: left;
    color: #fff;
    width: 90px;
    height: 35px;
    border-radius: 5px;
    text-align: center;
    line-height: 35px;
  }

  .top-img {
    width: 100%;
    height: 275px;
    /*background: url(/Content/css/images/imgs.png) no-repeat center;*/
    background-size: cover;
  }

  #page-wrapper {
    background: #fff
  }

  .titli-img {
    display: block;
    margin: 0 auto;
    margin-top: 20px;
    width: 450px;
  }

  .img {
    display: block;
    margin: 0 auto;
    margin-top: 30px;
  }

  .pic-img {
    display: block;
    width: 260px;
    /*margin-left: 50px;*/
  }

  .information-img {
    width: 800px;
    height: auto;
    margin: 0 auto;
    background: #fff;
  }

  .name {
    width: 260px;
    text-align: center;
    float: left;
    margin-left: 60px;
    font-size: 15px;
    margin-top: -60px;
  }

  .box {
    position: relative;
    border-top: none !important;
    box-shadow: 0 0px 0px rgba(0, 0, 0, 0.1) !important;
    /*width: 380px;
        height: 300px;*/
    float: right;
    /*margin-top: 30px;*/
    /*margin-left: 30px;*/
    /*padding-top: 30px;*/
    /*position:relative;*/
    padding-left: 40px !important;
  }

  .f16 {
    font-size: 16px;
  }

  .stud-information {
    padding-left: 30px !important
  }

  .information-right {
    width: 470px !important
  }
  .clearfix { zoom: 1; }
  .clearfix::before, .clearfix::after { content: ""; line-height: 0; display: table; }
  .clearfix::after { clear: both; }
  .information {
    width: 800px;
    height: auto;
    /*padding-bottom:40px;*/
    margin: 0 auto;
    background: #fff;
    border-top-left-radius: 20px;
    border-top-right-radius: 20px;

    /*-webkit-box-shadow: 0 0 10px rgba(234, 234, 234, .5);
    -moz-box-shadow: 0 0 10px rgba(234, 234, 234, .5);
    box-shadow: 0 0 10px rgba(234, 234, 234, .5);*/
  }



  .stud-information li {
    width: 300px;
    height: 45px;
    line-height: 45px;
    border-bottom: 1px solid #e5e5e5;
  }
  .stud-information li:last-child{
    border-bottom: none;
  }

  .stud-information {
    float: left;
    margin: 0;
    font-size: 16px;
    padding-left: 20px;
    list-style: none;

      /*font-weight: 550;*/
  }

  .information-right {
    float: left;
    width: 480px;
    text-align: center;
    padding-top: 30px;
  }

  .information-right p {
    font-size: 18px;
    margin-bottom: 15px;
    font-weight: 600;
  }

  .information-right span {
    font-size: 40px;
    font-weight: 600;
    color: #f42b2b !important;
  }

  .information-right span a {
    font-size: 16px !important;
  }

  .information-right div {
    font-size: 16px;
    margin-top: 10px;
  }

  .information-right a {
    color: #f42b2b !important;
  }

  .mt-20 {
    margin-top: 20px !important;
  }

  .mt-40 {
    margin-top: 40px !important;
  }

  .mt-80 {
    margin-top: 80px !important;
  }

  .information h3 a {
    color: #f42b2b !important;
    font-size: 20px;
    font-weight: 600;
  }

  .tubiao {
    display: block;
    margin: 0 auto;
    width: 700px;
  }

  .title {
    text-align: center;
  }

  .title01 {
    /*text-align: center;
    font-size: 15px;
    margin-top:-42px;
   position: absolute;
    bottom: 25px;
    left: 85px;*/
  }

  .result {
    width: 800px;
    margin: 0 auto;
    margin-top: 40px;
  }

  .result div {
    width: 115px;
    height: 40px;
    color: #fff;
    text-align: center;
    line-height: 40px !important;
    border-radius: 50px;
  }

  .result div h2 {
    font-size: 20px !important
  }

  .result p {
    margin-top: 20px;
    margin-left: 30px;
  }

  .aclass {
    font-size: 18px;
    color: #3dab93 !important;
    font-weight: bold;
    display: block;
  }
  p {
    line-height: 1.5;
    margin: 0px 40px;
  }
  @media print {
    body {
      -webkit-print-color-adjust: exact;
    }
  }
</style>
