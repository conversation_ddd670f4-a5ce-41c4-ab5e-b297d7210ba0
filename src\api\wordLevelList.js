/**
 * 单词水平相关接口
 */
import request from '@/utils/request'

export default {
  // 单词水平分页查询
  courseList(pageNum, pageSize, data) {
    return request({
      url: '/znyy/word/level/list/' + pageNum + '/' + pageSize,
      method: 'GET',
      params: data
    })
  },
  // 单词水平新增
  addWordLevel(data) {
    return request({
      url: '/znyy/word/level/save',
      method: 'POST',
      data
    })
  },
  // 单词水平编辑
  updateWordLevel(data) {
    return request({
      url: '/znyy/word/level/modify',
      method: 'PUT',
      data
    })
  },
  // 单词水平查询
  queryActive(id) {
    return request({
      url: '/znyy/word/level/seeDetails/' + id,
      method: 'GET'
    })
  },
  getCurrentTime() {
    var _this = this;
    let yy = new Date().getFullYear();
    let mm = new Date().getMonth() + 1;
    let dd = new Date().getDate();
    let hh = new Date().getHours();
    let mf = new Date().getMinutes() < 10 ? '0' + new Date().getMinutes() : new Date().getMinutes();
    let ss = new Date().getSeconds() < 10 ? '0' + new Date().getSeconds() : new Date().getSeconds();
    _this.gettime = yy + '-' + mm + '-' + dd + ' ' + hh + ':' + mf + ':' + ss;
    return _this.gettime
  }
}
