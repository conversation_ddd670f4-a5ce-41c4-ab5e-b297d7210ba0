<template>
  <div class="app-container">
    <el-form :inline="true" style="margin-bottom: 20px">
      <el-button type="primary" icon="el-icon-plus" @click="clickAdd">添加</el-button>
    </el-form>

    <el-table class="period-table" v-loading="tableLoading" :data="tableData" style="width: 100%; margin-bottom: 20px"
      row-key="id" border default-expand-all>
      <el-table-column prop="profitName" label="级别名称" sortable align="center" >
          <template slot-scope="scope">
          <span v-if="scope.row.profitName===1">一级</span>
          <span v-if="scope.row.profitName===2">二级</span>
        </template>
      </el-table-column>
      <!-- <el-table-column prop="grandMoney" label="级别金额" sortable></el-table-column> -->
      <el-table-column prop="profitRate" label="分润比例" sortable align="center" >

      </el-table-column>
      <el-table-column prop="id" label="操作" sortable align="center">
        <template slot-scope="scope">
          <el-button type="success" size="mini" icon="el-icon-edit-outline" @click="handleUpdate(scope.row.id)">编辑</el-button>
          <el-button type="danger" size="mini" icon="el-icon-delete" @click="deleteRank(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 添加弹窗 -->
    <el-dialog :title="addOrUpdate ? '添加直充分润等级' : '编辑直充分润等级'" :visible.sync="dialogVisible" width="70%"
      :close-on-click-modal="false" @close="close">
      <el-form :ref="addOrUpdate ? 'addCourseData' : 'updateActive'" :rules="rules" :model="addOrUpdate ? addCourseData : updateActive"
        label-position="right" label-width="80px" style="width: 100%">
        <el-form-item label="级别名称:">
          <el-col :xs="24" :span="18">
            <el-input v-if="addOrUpdate" v-model="addCourseData.profitName" type="number" maxlength="20" isNumber2="true" min="1"/>
            <el-input v-if="!addOrUpdate" v-model="updateActive.profitName" type="number" maxlength="20" isNumber2="true" min="1"/>
          </el-col>
        </el-form-item>
        <el-form-item label="id:" v-show="false">
          <el-col :xs="24" :span="18">
            <el-input v-if="addOrUpdate" v-model="addCourseData.id" />
            <el-input v-if="!addOrUpdate" v-model="updateActive.id" />
          </el-col>
        </el-form-item>
        <el-form-item label="返点率:">
          <el-col :xs="24" :span="18">
            <el-input v-if="addOrUpdate" v-model="addCourseData.profitRate" />
            <el-input v-if="!addOrUpdate" v-model="updateActive.profitRate" />
          </el-col>
          <el-col :xs="24" :span="12">
            <span class="red">例如：返点80%就填写0.8</span>
          </el-col>
        </el-form-item>
        <el-form-item label="状态:" prop="isEnable">
          <template>
            <el-radio v-if="addOrUpdate" v-model="addCourseData.isEnable" label="1">是</el-radio>
            <el-radio v-if="!addOrUpdate" v-model="updateActive.isEnable" label="0">否</el-radio>
            <el-radio v-if="!addOrUpdate" v-model="updateActive.isEnable" label="1">是</el-radio>
            <el-radio v-if="addOrUpdate" v-model="addCourseData.isEnable" label="0">否</el-radio>
          </template>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button v-if="addOrUpdate" size="mini" type="primary" @click="addActiveFun('addCourseData')">新增</el-button>
        <el-button v-if="!addOrUpdate" size="mini" type="primary" @click="updateActiveFun('updateActive')">修改</el-button>
        <el-button size="mini" @click="close">关闭</el-button>
      </div>
    </el-dialog>

    <!-- 分页 -->
    <el-col :span="20">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]" layout="total, sizes, prev, pager, next, jumper"
        :total="tablePage.totalItems" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    </el-col>
  </div>
</template>

<script>
  import Tinymce from "@/components/Tinymce";

  import dealerRankList from "@/api/dealerRankList";
  import profitRankApi from "@/api/profitRank"
  import {
    pageParamNames
  } from "@/utils/constants";
  // import tuiEditorEditorAll from 'tui-editor/dist/tui-editor-Editor-all';
  export default {
    data() {
      return {
        tableLoading: false,
        // 分页
        tablePage: {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null,
        },
        tableData: [],
        rules: {
          profitName: [{
            required: true,
            message: "必填",
            trigger: "blur",
          }, ],
          // grandMoney: [{
          //   required: true,
          //   message: "必填",
          //   trigger: "blur",
          // }, ],
          profitRate: [{
            required: true,
            message: "必填",
            trigger: "blur",
          }, ],
          isEnable: [{
            required: true,
            message: "必填",
            trigger: "change",
          }, ],
        }, //托管中心级别
        dialogVisible: false,
        addOrUpdate: true,
        updateActive: {},
        addCourseData: {},
        showLoginAccount: false,
      };
    },
    created() {
      this.fetchData();
    },
    methods: {
      // 查询+搜索课程列表
      fetchData() {
        //
        const that = this;
        that.tableLoading = true;
        // dealerRankList
        //   .dealerRankList(
        //    that.tablePage.currentPage, that.tablePage.size
        //   )
        //   .then((res) => {
        //     that.tableData = res.data.data;
        //     // console.log(res)
        //     that.tableLoading = false;
        //     // 设置后台返回的分页参数
        //     pageParamNames.forEach((name) =>
        //       that.$set(that.tablePage, name, parseInt(res.data[name]))
        //     );
        //   });
        profitRankApi
          .profintRankList(that.tablePage.currentPage, that.tablePage.size)
          .then((res) => {
            that.tableData = res.data.data;
            // console.log(res)
            that.tableLoading = false;
            // 设置后台返回的分页参数
            pageParamNames.forEach((name) =>
              that.$set(that.tablePage, name, parseInt(res.data[name]))
            );
          });
      },
      //新增操作
      clickAdd() {
        this.dialogVisible = true;
        this.addOrUpdate = true;
        this.addCourseData = {
          profitName: "",
          profitRate: "",
          isEnable: "0",
        };
      },
      // 点击编辑按钮
      handleUpdate(id) {
        const that = this;
        that.dialogVisible = true;
        that.addOrUpdate = false;
        profitRankApi
          .queryProfitRank(id)
          .then((res) => {
            that.updateActive = res.data;
            // console.log(that.updateActive)
          })
          .catch((err) => {});
      },

      // 分页
      handleSizeChange(val) {
        this.tablePage.size = val;
        this.fetchData();
      },
      handleCurrentChange(val) {
        this.tablePage.currentPage = val;
        this.fetchData();
      },
      //提交到后台新增直推分润级别
      addActiveFun(ele) {
        const that = this;
        that.$refs[ele].validate((valid) => {
          // 表单验证
          if (valid) {
            const loading = this.$loading({
              lock: true,
              text: "新增直充分润级别",
              spinner: "el-icon-loading",
              background: "rgba(0, 0, 0, 0.7)",
            });
            profitRankApi
              .addProfitRank(that.addCourseData)
              .then(() => {
                that.dialogVisible = false;
                loading.close();
                that.$nextTick(() => that.fetchData());
                that.$message.success("新增直充分润级别成功");
              })
              .catch((err) => {
                loading.close();
              });
          } else {
            console.log("error submit!!");
            //loading.close();
            return false;
          }
        });
      },
      //修改提交道后台直推分润级别
      updateActiveFun(ele) {
        const that = this;
        that.$refs[ele].validate((valid) => {
          // 表单验证
          if (valid) {
            const loading = this.$loading({
              lock: true,
              text: "修改直充分润级别",
              spinner: "el-icon-loading",
              background: "rgba(0, 0, 0, 0.7)",
            });
            profitRankApi
              .addProfitRank(that.updateActive)
              .then(() => {
                that.dialogVisible = false;
                loading.close();
                that.$nextTick(() => that.fetchData());
                that.$message.success("修改直充分润级别");
              })
              .catch((err) => {
                loading.close();
              });
          } else {
            console.log("error submit!!");
            //loading.close();
            return false;
          }
        });
      },
      //关闭弹框
      close() {
        this.dialogVisible = false;
      },
      //删除直冲分润等级
      deleteRank(id) {
        this.$confirm("确定操作吗?", "删除操作", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          })
          .then(() => {
            profitRankApi
              .deleteProfitRank(id)
              .then((res) => {
                this.$nextTick(() => this.fetchData());
                this.$message.success("删除成功!");
              })
              .catch((err) => {});
          })
          .catch((err) => {});
      },
    },
  };
</script>

<style>
  .container-card {
    padding: 15px 15px 15px 15px;
    margin-bottom: 15px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  }

  .period-table td,
  .period-table th {
    text-align: center;
  }

  .mt20 {
    margin-top: 20px;
  }

  .red {
    color: red;
  }
</style>
