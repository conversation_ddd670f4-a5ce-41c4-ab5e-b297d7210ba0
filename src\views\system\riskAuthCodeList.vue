<!--风控授权码列表-->
<template>
  <div class="app-container">
    <el-form :inline="true" style="margin-bottom: 20px">
      <el-form-item label="产品类型:">
        <el-select v-model="tablePage.authCodeType" filterable value-key="value" @change="fetchData" placeholder="请选择产品类型"
          clearable>
          <el-option v-for="(item, index) in authCodeTypeList" :key="index" :label="item.riskAuthTypeName"
            :value="item.riskAuthTypeValue" />
        </el-select>
      </el-form-item>
      <el-button @click="fetchData">搜索</el-button>
      <el-button type="primary" icon="el-icon-s-tools" @click="clickAdd">生成授权码
      </el-button>
    </el-form>

    <el-table class="period-table" v-loading="tableLoading" :data="tableData" style="width: 100%; margin-bottom: 20px"
      row-key="id" border default-expand-all>
      <el-table-column prop="id" label="id" sortable>
      </el-table-column>
      <el-table-column prop="authCode" label="授权码" sortable>
      </el-table-column>
      <el-table-column prop="id" label="操作" sortable width="200">
        <template slot-scope="scope">
          <el-button type="danger" size="mini" icon="el-icon-delete" @click="deleteRiskAuth(scope.row.id)">
            作废
          </el-button>
        </template>
      </el-table-column>
      <el-table-column prop="createBy" label="生成方" sortable>
      </el-table-column>
      <el-table-column prop="merchantCode" label="绑定方" sortable>
      </el-table-column>
      <el-table-column prop="relieveType" label="解除风险类型" sortable>
        <template slot-scope="scope">
          {{ getRelieveType(scope.row.relieveType) }}
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态" sortable>
        <template slot-scope="scope">
          <span v-if="scope.row.status === 0" class="green">未使用</span>
          <span v-else-if="scope.row.status === 1" class="blue">已使用</span>
          <span v-else-if="scope.row.status === 2" style="color: #F56C6C">已作废</span>
        </template>
      </el-table-column>
      <el-table-column prop="validityTime" label="截止时间" sortable>
      </el-table-column>
    </el-table>
    <!-- 添加弹窗 -->
    <el-dialog title="生成授权码" :visible.sync="dialogVisible" width="35%" :close-on-click-modal="false" @close="close">
      <el-form :rules="rules" :model="form" ref="form" label-position="right" label-width="120px">
        <el-form-item label="授权码类型:" prop="authCodeType">
          <el-select v-model="form.authCodeType" filterable value-key="value" placeholder="请选择" clearable>
            <el-option v-for="(item, index) in authCodeTypeList" :key="index" :label="item.riskAuthTypeName"
              :value="item.riskAuthTypeValue" />
          </el-select>
        </el-form-item>
        <el-form-item label="授权码有效止期:" prop="authCodeEndDate" style="width: 46%">
          <el-date-picker v-model="form.authCodeEndDate" type="date" value-format="yyyy-MM-dd" placeholder="选择日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="生成数量:" prop="authCodeNum">
          <el-input-number v-model="form.authCodeNum" :precision="0" :step="1" :max="100" :min="1"></el-input-number>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="addActiveFun()">保存</el-button>
        <el-button size="mini" @click="close">关闭</el-button>
      </div>
    </el-dialog>

    <!-- 分页 -->
    <el-col :span="20">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>
  </div>
</template>

<script>
import merProfitConfigApi from "@/api/merProfitConfig";
import { pageParamNames } from "@/utils/constants";
import checkPermission from '@/utils/permission'
import riskAuthCodeApi from "@/api/riskAuthCode";

export default {
  name: "merProfitConfig",
  data() {
    return {
      form: {
        id: undefined,
        authCodeType: undefined,
        authCodeNum: undefined,
        authCodeEndDate: undefined,
      },
      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null,
        productType: ""
      },
      authCodeTypeList: [],
      productTypeList: [],
      profitPartyList: [],
      tableData: [],
      rules: {
        authCodeType: [{
          required: true,
          message: "授权码类型必填",
          trigger: "blur",
        },],
        authCodeEndDate: [{
          required: true,
          message: "授权码止期必填",
          trigger: "blur",
        }],
        authCodeNum: [{
          required: true,
          message: "授权码数量必填",
          trigger: "blur",
        },],
      },
      dialogVisible: false,
      addCourseData: {},
      showLoginAccount: false,
    };
  },
  created() {
    //获取授权码类型
    riskAuthCodeApi.getRiskType().then((res) => {
      this.authCodeTypeList = res.data;
    })
    this.fetchData();
  },
  methods: {
    checkPermission,

    clickAdd() {
      this.dialogVisible = true;
    },
    //查询渠道分润配置列表
    fetchData() {
      this.tableLoading = true;
      riskAuthCodeApi.pageList({
        "pageNum": this.tablePage.currentPage,
        "pageSize": this.tablePage.size,
        "authCodeType": this.tablePage.authCodeType
      }).then((res) => {
        this.tableData = res.data.data;
        this.tableLoading = false;
        // 设置后台返回的分页参数
        pageParamNames.forEach((name) =>
          this.$set(this.tablePage, name, parseInt(res.data[name]))
        );
      });
    },
    getRelieveType(relieveType) {
      for (let index in this.authCodeTypeList) {
        if (this.authCodeTypeList[index].riskAuthTypeValue == relieveType) {
          return this.authCodeTypeList[index].riskAuthTypeName;
        }
      }
      return "未知类型";
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val;
      this.fetchData();
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val;
      this.fetchData();
    },
    //增加授权码
    addActiveFun() {
      this.$refs["form"].validate((valid) => {
        // 表单验证
        if (valid) {
          const loading = this.$loading({
            lock: true,
            text: "生成授权码",
            spinner: "el-icon-loading",
            background: "rgba(0, 0, 0, 0.7)",
          });
          riskAuthCodeApi.save(this.form).then(() => {
            this.dialogVisible = false;
            loading.close();
            this.$nextTick(() => this.fetchData());
            this.$message.success("保存授权码成功");
          }).catch((err) => {
            loading.close();
          });
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    //关闭弹框
    close() {
      this.dialogVisible = false;
      this.form = {
        id: undefined,
        authCodeType: undefined,
        authCodeNum: undefined,
        authCodeEndDate: undefined,
      }
      this.$refs['form'].resetFields();
    },
    //删除欧泉吗
    deleteRiskAuth(id) {
      this.$confirm("确定操作吗?", "授权码作废", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        riskAuthCodeApi.delete(id).then((res) => {
          this.$nextTick(() => this.fetchData());
          this.$message.success("作废成功!");
        })
      })
    },

  },
};
</script>

<style scoped>
.period-table td,
.period-table th {
  text-align: center;
}

.red {
  color: red;
  margin-left: 45px;
}
</style>
