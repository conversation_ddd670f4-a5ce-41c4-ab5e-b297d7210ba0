<template>
  <div class="app-container">
    <el-form :inline="true" ref="queryForm" class="container-card" label-width="110px">
      <el-form-item label="商品名称：">
        <el-input v-model="dataQuery.name" placeholder="请输入名称" clearable />
      </el-form-item>
      <el-form-item label="类型：">
        <el-select v-model="dataQuery.type" clearable>
          <el-option v-for="item in typeList" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-re" @click="resetQuery()">重置</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="search()">搜索</el-button>
      </el-form-item>
    </el-form>
    <el-col :span="24" style="margin-bottom: 30px">
      <el-button type="success" icon="el-icon-plus" size="mini" @click="addBtn()">新增</el-button>
    </el-col>
    <el-table class="common-table" v-loading="tableLoading" :data="tableData" style="width: 100%;margin-bottom: 20px;"
      row-key="id" border default-expand-all>
      <el-table-column prop="id" label="ID"></el-table-column>
      <el-table-column prop="name" label="商品名称" />
      <el-table-column prop="withdrawnBonus" label="操作" width="480">
        <template slot-scope="scope">
          <el-button type="primary" size="mini" @click="editBtn(scope.row.id)">编辑
          </el-button>
          <el-button type="danger" size="mini" @click="handleDelete(scope.row.id)">删除
          </el-button>
        </template>
      </el-table-column>
      <el-table-column prop="type" label="商品类型" :formatter="typeFormat">
      </el-table-column>
      <el-table-column prop="isShow" label="前端展示">
        <template slot-scope="scope">
          {{ scope.row.isShow ? '是' : '否' }}
        </template>
      </el-table-column>
      <el-table-column prop="price" label="金币数"></el-table-column>
      <el-table-column prop="sellNum" label="销量"></el-table-column>
      <el-table-column prop="validDays" label="有效天数">
        <template slot-scope="scope">
          <span v-if="scope.row.isAll || scope.row.type === 1">永久</span>
          <span v-else>{{ scope.row.validDays }}</span>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <el-col :span="24" style="margin-bottom: 20px">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>

    <!-- 修改或者新增弹窗 -->
    <el-dialog :title="form.id != null ? '编辑' : '添加' + '商品'" :visible.sync="open" width="70%" @close="close">
      <el-form ref="form" :model="form" :rules="rules" label-width="120px" style="width: 70%;">
        <el-form-item label="商品名称：" prop="name">
          <el-input v-model="form.name" />
        </el-form-item>
        <el-form-item label="商品类型：" prop="type">
          <el-select v-model="form.type">
            <el-option v-for="item in typeList" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="商品分类：" v-if="form.type !== 1" prop="classify">
          <el-select v-model="form.classify">
            <el-option v-for="item in classifyList" :label="item.name" :value="item.ident" />
          </el-select>
        </el-form-item>
        <el-form-item label="前端展示：" prop="isShow">
          <el-radio-group v-model="form.isShow">
            <el-radio :label="true">是</el-radio>
            <el-radio :label="false">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="商品图片：" prop="image">
          <el-input type="hidden" v-model="form.image" />
          <OneImageUpload @handleSuccess="handleSuccess" :fileList="fileList" />
        </el-form-item>
        <el-form-item label="数值：" prop="value" v-if="form.classify.includes('DZ') || form.classify.includes('BS')">
          <el-input-number v-model="form.value" :min="0" :step="1" />
        </el-form-item>
        <!--        主题，字体文件上传-->
        <el-form-item label="文件：" prop="value"
          v-if="form.classify.includes('ZT') || form.classify.includes('ZTT') || form.classify.includes('XT_TX')">
          <upload-file @handleSuccess="valueHandleSuccess" @handleRemove="valueHandleRemove" :file-list="valueFileList" />
        </el-form-item>
        <el-form-item label="永久使用：" prop="isAll" v-if="form.type !== 1 && !form.classify.includes('DZ')">
          <el-radio-group v-model="form.isAll">
            <el-radio :label="true">是</el-radio>
            <el-radio :label="false">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="有效天数：" prop="validDays" v-if="!form.isAll && form.type !== 1">
          <el-input-number v-model="form.validDays" :min="1" />
        </el-form-item>
        <el-form-item label="销量：" prop="sellNum" v-if="form.isShow">
          <el-input-number v-model="form.sellNum" :min="0" />
        </el-form-item>
        <el-form-item label="兑换金币数：" prop="price" v-if="form.isShow">
          <el-input-number v-model="form.price" :min="0" />
        </el-form-item>
        <el-form-item label="详情页：" prop="isDetail" v-if="form.isShow">
          <el-radio-group v-model="form.isDetail">
            <el-radio :label="true">有</el-radio>
            <el-radio :label="false">无</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="详情页内容" prop="detailPage" v-if="form.isDetail">
          <el-input v-model="form.detailPage" placeholder="点击编辑富文本内容">
            <i @click="inputClick(form, 'detailPage')" slot="suffix" class="el-icon-edit-outline"
              style="line-height: 36px;font-size: 20px;cursor: pointer;"></i>
          </el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="open = false">取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog :visible.sync="richEditor.dialogVisible" append-to-body :close-on-click-modal="false"
      style="width: 100%;height: 100%" :show-close="false" center>
      <Ueditor @ready="editorReady" />
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="editorConfirm">确 定</el-button>
        <el-button @click="richEditor.dialogVisible = false">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import goodsApi from '@/api/xi/goods'
import goodsclassifyApi from '@/api/xi/goodsclassify'
import { pageParamNames } from '@/utils/constants'
import OneImageUpload from '@/components/Upload/OneImageUpload'
import UploadFile from '@/components/Upload/UploadFile'
import Ueditor from '@/components/Ueditor'

export default {
  name: 'goods',
  components: { OneImageUpload, Ueditor, UploadFile },
  data() {
    return {
      valueFileList: [],
      typeList: [{ label: '实物', value: 1 }, { label: '虚拟', value: 2 }, { label: '优选好券', value: 3 }],
      classifyList: [],
      fileList: [],
      dataQuery: {
        name: ''
      },
      richEditor: {
        dialogVisible: false,
        object: null,
        parameterName: '',
        instance: null
      },
      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      open: false,
      tableData: [],
      form: {},
      // 表单校验
      rules: {
        name: [{ required: true, message: '请输入商品名称', trigger: 'blur' }],
        image: [{ required: true, message: '请上传商品图片', trigger: 'blur' }],
        type: [{ required: true, message: '请选择商品类型', trigger: 'blur' }],
        classify: [{ required: true, message: '请选择商品分类', trigger: 'blur' }],
        price: [{ required: true, message: '请输入兑换金币数', trigger: 'blur' }],
        isDetail: [{ required: true, message: '请选择', trigger: 'blur' }],
        detailPage: [{ required: true, message: '请输入', trigger: 'blur' }],
        sellNum: [{ required: true, message: '请输入销量', trigger: 'blur' }],
        isAll: [{ required: true, message: '请选择', trigger: 'blur' }],
        isShow: [{ required: true, message: '请选择', trigger: 'blur' }],
        value: [{ required: true, message: '请输入', trigger: 'blur' }],
        validDays: [{ required: true, message: '请输入有效天数', trigger: 'blur' }]
      }
    }
  },
  created() {
    this.getPageList()
    this.getClassify()
    this.reset()
    this.form.type = 1
  },
  methods: {

    getClassify() {
      goodsclassifyApi.list().then(res => {
        if (res.success) {
          this.classifyList = res.data
        }
      }).catch(err => {
      })
    },
    search() {
      this.tablePage = {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      }
      this.getPageList()
    },
    typeFormat(row, column) {
      let re = '未知'
      for (let i = 0; i < this.typeList.length; i++) {
        if (this.typeList[i].value === row.type) {
          re = this.typeList[i].label
        }
      }
      if (row.type === 2) {
        for (let a = 0; a < this.classifyList.length; a++) {
          if (this.classifyList[a].ident === row.classify) {
            re += '(' + this.classifyList[a].name + ')'
          }
        }
      }
      return re
    },
    editorReady(instance) {
      this.richEditor.instance = instance
      let currentContent = this.richEditor.object[this.richEditor.parameterName]
      this.richEditor.instance.setContent(currentContent)
      // 光标定位到Ueditor
      this.richEditor.instance.focus(true)
    },
    inputClick(object, parameterName) {
      this.richEditor.object = object
      this.richEditor.parameterName = parameterName
      this.richEditor.dialogVisible = true
    },
    editorConfirm() {
      let content = this.richEditor.instance.getContent()
      this.richEditor.object[this.richEditor.parameterName] = content
      this.richEditor.dialogVisible = false
    },
    handleSuccess(url) {
      this.fileList = [url]
      this.form.image = url
    },
    // valueHandleSuccess(url) {
    //   this.valueFileList = [url]
    //   this.form.value = url
    // },
    valueHandleSuccess(url, fileName) {
      this.form.value = JSON.stringify({ url: url, name: fileName })
    },
    valueHandleRemove() {
      this.form.value = ''
    },
    handleDelete(id) {
      this.$confirm('确定要删除吗？', '删除商品', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        goodsApi.delete(id).then(res => {
          this.$message.success('删除成功！')
          this.getPageList()
        })
      })
    },
    addBtn() {
      this.reset()
      this.open = true
    },
    editBtn(id) {
      this.reset()
      goodsApi.detail(id).then(res => {
        this.form = res.data
        this.fileList = this.form.image !== '' ? new Array(this.form.image) : []
        if (this.form.classify.startsWith('ZS')) {
          this.valueFileList = this.form.value !== '' ? new Array(JSON.parse(this.form.value)) : []
        }
        if (this.form.classify.startsWith('XT')) {
          this.valueFileList = this.form.value !== '' ? new Array(JSON.parse(this.form.value)) : []
        }
        this.open = true
      })
    },
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.form.classify.includes('DZ')) {
            this.form.isAll = true
          }
          goodsApi.saveOrUpdate(this.form).then(response => {
            this.$message.success('提交成功！')
            this.open = false
            this.getPageList()
          })
        }
      })
    },
    getPageList() {
      this.tableLoading = true
      this.dataQuery.pageNum = this.tablePage.currentPage
      this.dataQuery.pageSize = this.tablePage.size
      goodsApi.list(this.dataQuery).then(res => {
        this.tableData = res.data.data
        this.tableLoading = false
        // 设置后台返回的分页参数
        pageParamNames.forEach(name =>
          this.$set(this.tablePage, name, parseInt(res.data[name]))
        )
      })
    },
    handlePage() {
      (this.tablePage = {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      }),
        this.getPageList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dataQuery = {
        name: '',
        enable: null
      }
      this.getPageList()
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val
      this.getPageList()
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val
      this.getPageList()
    },
    close() {
      this.open = false
      this.reset()
    },
    reset() {
      this.form = {
        name: null,
        image: null,
        type: 1,
        classify: '',
        price: undefined,
        isDetail: false,
        detailPage: '',
        sellNum: undefined,
        isAll: false,
        isShow: true,
        value: undefined
      }
      if (this.$refs['form']) {
        this.$refs['form'].resetFields()
      }
      this.fileList = []
      this.valueFileList = []
    }
  }
}
</script>

<style>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.period-table td,
.period-table th {
  text-align: center;
}

@media (max-width: 767px) {
  .el-message-box {
    width: 80% !important;
  }
}

.el-tooltip__popper {
  max-width: 800px;
}
</style>
