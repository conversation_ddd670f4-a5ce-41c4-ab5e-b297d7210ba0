/**
 * 分公司相关接口
 */
import request from '@/utils/request'

export default {
  // 新增
  addCompany(data) {
    return request({
      url: '/znyy/branch/office',
      method: 'POST',
      data
    })
  },

  // 分页查询
  companyLists(pageNum, pageSize, data) {
    return request({
      url: '/znyy/branch/office/list/' + pageNum + '/' + pageSize,
      method: 'GET',
      params: data
    })
  },
  // 修改回显
  echoCompany(id) {
    return request({
      url: '/znyy/branch/office/check/detail/' + id,
      method: 'GET'
    })
  },
  // 编辑
  updateCompany(data) {
    return request({
      url: '/znyy/branch/office',
      method: 'PUT',
      data
    })
  },
  // 获取银行分类列表
  categoryType(bankType) {
    return request({
      url: '/znyy/bvstatus/' + bankType,
      method: 'GET'
    })
  },
  // 加载符近的托管中心
  loadOtherExperienceStores(lat, lon) {
    return request({
      url: '/znyy/dealer/load/dealer?lat=' + lat + '&lon=' + lon,
      method: 'GET'
    })
  },
  // 导出
  companyExport(listQuery) {
    return request({
      url: '/znyy/branch/office/execle',
      method: 'GET',
      responseType: 'blob',
      params:listQuery
    })
  },
  examine(id,checkReason,isCheck){
    return request({
      url: '/znyy/branch/office/update/isCheck?id=' + id + '&checkReason=' + checkReason + '&isCheck=' + isCheck ,
      method: 'PUT'
    })
  },
  // 开通与暂停
  updateStatus(id, status) {
    return request({
      url: '/znyy/branch/office/update/isEnable?id=' + id + '&isEnable=' + status,
      method: 'PUT'
    })
  },
  updatePaymentIsComplete(id, paymentIsComplete) {
    return request({
      url: '/znyy/branch/office/updatePaymentIsComplete?id=' + id + '&paymentIsComplete=' + paymentIsComplete,
      method: 'PUT'
    })
  },
}
