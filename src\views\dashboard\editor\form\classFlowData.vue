<!--招商数据-->
<template>
  <div>
    <el-row :gutter="10" style="height: 400px;padding-top: 10px;">
      <el-col :span="12" style="height: 100%">
        <el-card shadow="always" body-style="padding:5px">
          <div>
            <el-col :span="16">
              <div>
                <h3>学员耗课数据</h3>
              </div>
            </el-col>
            <el-col :span="8"><div>
              <el-radio-group style="float: right;margin-top: 5%" v-model="radio" @change = "initData(radio)">
                <el-radio-button label="day">日</el-radio-button>
                <el-radio-button label="week">周</el-radio-button>
                <el-radio-button label="month">月</el-radio-button>
              </el-radio-group>
            </div>
            </el-col>
            <el-table
              :data="tableData"
              v-loading="tableLoading"
              height="320"
              border
              style="width: 100%">
              <el-table-column
                prop="date"
                label="日期">
              </el-table-column>
              <el-table-column
                prop="rechargeHours"
                label="充值(学时)">
                <template slot-scope="scope">
                  <span>{{scope.row.rechargeHours ? scope.row.rechargeHours : "0.00"}}</span>
                </template>
              </el-table-column>
              <el-table-column
                prop="courseBack"
                label="退课(学时)">
              <template slot-scope="scope">
                <span>{{scope.row.courseBack ? scope.row.courseBack : "0.00"}}</span>
              </template>
              </el-table-column>
              <el-table-column
                prop="consumeHours"
                label="消耗(学时)">
                <template slot-scope="scope">
                  <span>{{scope.row.consumeHours ? scope.row.consumeHours : "0.00"}}</span>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-card>
      </el-col>
      <el-col :span="12" style="height: 100%">
        <el-card shadow="always" body-style="padding:5px;height:85%" style="height: 100%">
          <h3>学员耗课数据图表</h3>
          <div id="main4" :style="{width: '100%', height: '100%'}">
          </div>
        </el-card>
      </el-col>
    </el-row>  </div>
</template>

<script>
import elasticSearchApi from "@/api/elasticSearch";

export default {
  name: "classFlowDataForm",
  props: {
    // 表单
    form: {
      type: Object,
    },
  },
  data(){
    return {
      radio:"day",
      tableLoading: false,
      tableData: [],
      option:{},
      xAxisData:[],
      app:{},
    }
  },
  mounted() {
    this.initEcharts();
    this.initData()
  },
  methods:{
    initEcharts(){
      var chartDom = document.getElementById('main4');
      var myChart = this.$echarts.init(chartDom);
      const posList = [
        'left',
        'right',
        'top',
        'bottom',
        'inside',
        'insideTop',
        'insideLeft',
        'insideRight',
        'insideBottom',
        'insideTopLeft',
        'insideTopRight',
        'insideBottomLeft',
        'insideBottomRight'
      ];
      this.app.configParameters = {
        rotate: {
          min: -90,
          max: 90
        },
        align: {
          options: {
            left: 'left',
            center: 'center',
            right: 'right'
          }
        },
        verticalAlign: {
          options: {
            top: 'top',
            middle: 'middle',
            bottom: 'bottom'
          }
        },
        position: {
          options: posList.reduce(function (map, pos) {
            map[pos] = pos;
            return map;
          }, {})
        },
        distance: {
          min: 0,
          max: 100
        }
      };
      this.app.config = {
        rotate: 90,
        align: 'left',
        verticalAlign: 'middle',
        position: 'insideBottom',
        distance: 15,
        onChange: function () {
          const labelOption = {
            rotate: this.app.config.rotate,
            align: this.app.config.align,
            verticalAlign: this.app.config.verticalAlign,
            position: this.app.config.position,
            distance: this.app.config.distance
          };
          myChart.setOption({
            series: [
              {
                label: labelOption
              },
              {
                label: labelOption
              },
              {
                label: labelOption
              },
              {
                label: labelOption
              }
            ]
          });
        }
      };
      const labelOption = {
        show: true,
        position: this.app.config.position,
        distance: this.app.config.distance,
        align: this.app.config.align,
        verticalAlign: this.app.config.verticalAlign,
        rotate: this.app.config.rotate,
        formatter: '{c}  {name|{a}}',
        fontSize: 16,
        rich: {
          name: {}
        }
      };
      this.option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {
          data: ['Forest', 'Steppe', 'Desert', 'Wetland']
        },
        toolbox: {
          feature: {
            dataView: { show: true, readOnly: false },
            magicType: { show: true, type: ['line', 'bar'] },
            restore: { show: true },
            saveAsImage: { show: true }
          }
        },
        xAxis: [
          {
            type: 'category',
            axisTick: { show: false },
            data: [],
            axisLabel:{
              showMaxLabel: true,
              interval: 0,
              rotate: 0
            }
          }
        ],
        yAxis: [
          {
            type: 'value'
          }
        ],
        series: []
      };
      this.option && myChart.setOption(this.option);
    },
    initData(type){
      if(!type){
        type = 'day';
      }
      this.tableLoading = true;
      elasticSearchApi.getClassFlowData(type).then(res=>{
        this.tableLoading = false;
        this.tableData = res.data.data.data;
        let data = res.data.data;
        this.option.xAxis[0].data = data.dates;


        const labelOption = {
          show: false,
          position: this.app.config.position,
          distance: this.app.config.distance,
          align: this.app.config.align,
          verticalAlign: this.app.config.verticalAlign,
          rotate: this.app.config.rotate,
          formatter: '{c}  {name|{a}}',
          fontSize: 16,
          rich: {
            name: {}
          }
        };

        let chartDtos = data.chartDtos;
        this.option.series = [];
        this.option.legend.data = [];
        for (let i = 0; i < chartDtos.length; i++) {
          let ser = {};
          if(chartDtos[i].name === 'rechargeHours'){
            ser.name = '充值';
          }else if(chartDtos[i].name === 'consumeHours'){
            ser.name = '消耗';
          }else if(chartDtos[i].name === 'courseBack'){
            ser.name = '退课';
          }
          ser.data = chartDtos[i].data;
          this.option.legend.data.push(ser.name);
          ser.type = 'bar';
          ser.label= labelOption;
          ser.emphasis ={
            focus: 'series'
          };
          this.option.series.push(ser);
        }



        let chartDom = document.getElementById('main4');
        var myChart = this.$echarts.init(chartDom);
        myChart.clear();
        myChart.setOption(this.option,true);
      }).catch(err=>{})
    },
  }
}
</script>

<style scoped>

</style>
