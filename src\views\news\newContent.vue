<template>
    <div class="app-container">
        <div style="font-size: 30px;font-weight: bold;">{{ tableData.title }}</div>

        <div style="margin-top: 20px;">
            <span>发布时间：{{ tableData.addTime }}</span>
        </div>

        <div style="margin-top: 50px;line-height: 30px;">
            {{ tableData.contents }}
        </div>
    </div>
</template>
  
<script>
import newsApi from '@/api/news'
import ls from '@/api/noticeNews'


export default {
    name: 'newsContent',
    data() {
        return {
            radio: 3,
            id: '',
            type: '', // 消息类型
            tableData: [],
        }
    },
    methods: {
        index() {
            let that = this;
            newsApi.lookNotice(that.id).then((res) => {
                that.tableData = res.data;
                console.log(res.data)
            })
        },

        // readInform() {
        //     newsApi.readNotice(this.id).then((res) => {
        //         console.log('已读')
        //         // this.closePage()
        //     })
        // },

        //  B页面（组件）
        // closePage() {
        //     let status = this.$store.state.tableStatus;   //
        //     console.log(this.$store);
        //     console.log(this.$store.state);
        //     console.log(this.$store.state.device);
        //     this.$store.commit("changeStatus", !status);
            
        // },


    },
    mounted() {
        this.index();
    },

    created() {
        this.id = ls.getItem('notificationId');
    },
}
</script>
  
<style scoped>
.app-container {
    width: 60%;
    padding-top: 100px;
    padding-left: 100px;
}
</style>
  