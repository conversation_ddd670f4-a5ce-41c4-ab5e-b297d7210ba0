<template>
  <div class="app-container">
    <el-form :model="form" ref="form" label-width="100px" v-loading="formLoading" :rules="rules">
      <el-form-item label="所属类型:" prop="type">
        <el-select v-model="form.type" placeholder="全部" disabled>
          <el-option v-for="(item, index) in trainType" :key="index" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="题型:" prop="questionType">
        <el-select v-model="form.questionType" placeholder="全部" disabled>
          <el-option v-for="(item, index) in trainQuestionType" :key="index" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="选择难度：" prop="difficulty">
        <el-select v-model="form.difficulty" placeholder="全部" clearable @change="handleChange">
          <el-option v-for="(item, index) in difficultyList" :key="index" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item> -->
      <el-form-item label="年级：" prop="grade">
        <el-select v-model="form.grade" placeholder="" clearable>
          <el-option v-for="item in gradeList" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="选择维度：" prop="categoryId">
        <el-select v-model="form.categoryId" placeholder="全部" clearable>
          <el-option v-for="(item, index) in categoryList" :key="index" :label="item.name" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="选择类别：" prop="categoryType">
        <el-select v-model="form.categoryType" :disabled="questionlnCourseFalse&&form.categoryType==1" placeholder="全部" clearable>
          <el-option v-for="(item, index) in typeList" :key="index" :label="item.label" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="题干：" prop="title">
        <el-input placeholder="请输入" v-model="form.title" />
      </el-form-item>
      <!-- 内容 -->
      <el-form-item label="内容：" required>
        <div v-for="(row, rowIndex) in tableData" :key="rowIndex" class="table-row">
          <div v-for="(cell, colIndex) in row" :key="colIndex" class="table-cell">
            <el-radio v-model="radioSelections[colIndex]" :label="rowIndex" v-if="rowIndex > 0"></el-radio>
            <el-input :class="{ firstRow: rowIndex === 0 }" maxlength="15" style="width: 150px" v-model="cell.value" placeholder="请输入" />
          </div>

          <!-- 第一行的添加列按钮 -->
          <div class="add-column">
            <el-button v-if="rowIndex === 0 && showAddCol" @click="addColumn" class="add-column-button" size="small">添加列</el-button>
            <el-button v-if="rowIndex === 0 && showRemoveCol" @click="removeColumn" class="add-row-button" size="small">减少列</el-button>
          </div>
        </div>

        <!-- 第一列的添加行按钮 -->
        <div class="add-row">
          <el-button @click="addRow" class="add-row-button" size="small" v-if="showAddRow">添加行</el-button>
          <el-button v-if="showRemoveRow" @click="removeRow" class="add-column-button" size="small">减少行</el-button>
        </div>
      </el-form-item>
      <!-- 内容 -->
      <!-- <el-button type="primary" @click="processingData"></el-button> -->
      <el-form-item label="题数：" prop="score">
        <el-input-number v-model="form.score" :precision="0" :step="1" :min="0" :max="100"></el-input-number>
      </el-form-item>
      <el-form-item label="徽章：" prop="badge">
        <el-input-number v-model="form.badge" :precision="0" :step="1" :min="0" :max="100"></el-input-number>
      </el-form-item>
      <el-form-item label="解析：" prop="analysis">
        <el-input v-model="form.analysis">
          <i @click="inputClick(form, 'analysis')" slot="suffix" class="el-icon-edit-outline" style="line-height: 36px; font-size: 20px; cursor: pointer"></i>
        </el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="submitForm">提交</el-button>
        <el-button v-show="form.id === null" @click="resetForm">重置</el-button>
      </el-form-item>
    </el-form>

    <el-dialog :visible.sync="richEditor.dialogVisible" append-to-body :close-on-click-modal="false" style="width: 100%; height: 100%" :show-close="false" center>
      <Ueditor @ready="editorReady" />
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="editorConfirm">确 定</el-button>
        <el-button @click="richEditor.dialogVisible = false">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import questionApi from '@/api/paper/train/question';
// import difficultyApi from '@/api/paper/train/difficulty';
import categoryApi from '@/api/paper/train/category';
import { mapGetters, mapState } from 'vuex';
import MyUpload from '@/components/Upload/MyUpload';
import Ueditor from '@/components/Ueditor';

export default {
  components: {
    MyUpload,
    Ueditor
  },
  data() {
    return {
      richEditor: {
        dialogVisible: false,
        object: null,
        parameterName: '',
        instance: null
      },
      answerNum: 0,
      abc: ['1', '2', '3'],
      difficultyInfo: null,
      categoryList: [],
      typeList: [
        { label: '正式题', id: 1 },
        { label: '附加题', id: 2 }
      ],
      fileList: [],
      importFrom: {
        file: null
      },
      form: {
        id: null,
        title: '',
        categoryId: '',
        categoryType: '',
        // difficulty: '',
        // difficultyId: '',
        type: 'VISUAL_',
        questionType: 'VISUAL_FIND_SIMILAR_CHARACTER',
        customInfo: [],
        answer: [],
        isRandom: false,
        analysis: '',
        score: undefined,
        badge: 3,
        courseType: 0
      },
      tableData: [
        [
          {
            value: '',
            label: { x: 0, y: 0 }
          }
        ]
      ],
      radioSelections: [], // 存储每列选择的单选框值
      showAddRow: true,
      showAddCol: true,
      showRemoveRow: false,
      showRemoveCol: false,
      formLoading: false,
      rules: {
        type: [{ required: true, message: '请选择', trigger: 'blur' }],
        questionType: [{ required: true, message: '请选择', trigger: 'blur' }],
        grade: [{ required: true, message: '请选择', trigger: 'blur' }],
        title: [{ required: true, message: '请输入', trigger: 'blur' }],
        difficulty: [{ required: true, message: '请选择', trigger: 'blur' }],
        categoryId: [{ required: true, message: '请选择', trigger: 'blur' }],
        categoryType: [{ required: true, message: '请选择', trigger: 'blur' }],
        score: [{ required: true, message: '请输入题数', trigger: 'blur' }],
        badge: [{ required: true, message: '请输入徽章', trigger: 'blur' }],
        analysis: [{ required: true, message: '请输入解析', trigger: 'blur' }]
      },
      questionlnCourseFalse: false,
      currentAnswerItem: null,
      gradeList: [
        { label: '1-3年级', value: 1 },
        { label: '4-6年级', value: 2 },
        { label: '初高中', value: 3 }
      ]
    };
  },
  created() {
    this.getCategoryList();
    let id = this.$route.query.id;
    this.form.courseType = this.$route.query.courseType ? 1 : 0;
    if (id && parseInt(id) !== 0) {
      this.formLoading = true;
      questionApi.detail(id).then((re) => {
        this.form = re.data;
        this.handleData();
        // this.handleChange();
        this.formLoading = false;
      });
      questionApi.checkQuestionlnCourse(id).then((res) => {
        this.questionlnCourseFalse = res.data
      });
    }
  },
  watch: {
    tableData(val) {
      if (val.length >= 6) {
        this.showAddRow = false;
        this.showRemoveRow = true;
      } else if (val.length == 1) {
        this.showRemoveRow = false;
        this.showAddRow = true;
      } else {
        this.showAddRow = true;
        this.showRemoveRow = true;
      }
      if (val[0].length >= 3) {
        this.showAddCol = false;
        this.showRemoveCol = true;
      } else if (val[0].length == 1) {
        this.showRemoveCol = false;
        this.showAddCol = true;
      } else {
        this.showAddCol = true;
        this.showRemoveCol = true;
      }
    }
  },
  methods: {
    // 回显
    handleData() {
      let arr = JSON.parse(JSON.stringify(this.form.customInfo));
      arr.forEach((i) => (i.label = JSON.parse(i.label)));
      let res = this.reduceData(this.form.customInfo);
      this.tableData = res;
      // if (this.tableData.length >= 6) {
      //   this.showAddRow = false;
      // }
      // if (this.tableData[0].length >= 3) {
      //   this.showAddCol = false;
      // }
      this.radioSelections = this.reduceAnswer(this.form.answer);
    },
    //处理答案
    reduceAnswer(data) {
      const result = data.map((item) => {
        const label = JSON.parse(item.label); // 解析 label 字符串为对象
        return label.x; // 提取 x 的值
      });
      return result;
    },
    // 处理题目
    reduceData(data) {
      // 解析 label 字符串并按 x 值分组
      let groupedData = data.reduce((acc, item) => {
        // 解析 label 中的 JSON 字符串
        let label = JSON.parse(item.label);
        let x = label.x;

        // 如果 acc 中没有 x 值对应的数组，先创建
        if (!acc[x]) {
          acc[x] = [];
        }

        // 将 item 加入到对应的 x 组
        acc[x].push(item);

        return acc;
      }, {});
      // 将结果转换为嵌套数组的形式
      let result = Object.values(groupedData);
      return result;
    },
    // 题目转换为接口传参形式
    processingData() {
      //筛选问题STAR
      let arr = JSON.parse(JSON.stringify(this.tableData));
      this.tableData.forEach((row, x) => {
        row.forEach((cell, y) => {
          cell.label = { x, y };
        });
      });
      arr.forEach((row, x) => {
        row.forEach((cell, y) => {
          cell.label = JSON.stringify({ x, y });
        });
      });
      let questionData = arr.flat();
      //筛选问题END
      //筛选答案STAR
      console.log(this.radioSelections, 'radioSelectionsradioSelectionsradioSelections');
      let radioIndexs = this.radioSelections.map((radioItem, radioIndex) => {
        return {
          x: radioIndex,
          y: radioItem
        };
      });
      // 查找目标坐标对应的元素--答案
      let result = radioIndexs.map((coords) => {
        return this.tableData[coords.y][coords.x];
      });

      let answerData = result.flat();
      answerData.forEach((answer) => {
        answer.label = JSON.stringify(answer.label);
      });
      // console.log(answerData, 'answerDataanswerDataanswerData');
      //筛选答案END

      console.log(questionData, answerData);
      return {
        questionData,
        answerData
      };
    },
    addRow() {
      const newRow = this.tableData[0].map(() => ({
        value: ''
      }));
      this.tableData.push(newRow);
      // console.log(this.tableData, 'rowrowrow');
      // if (this.tableData.length >= 6) {
      //   this.showAddRow = false;
      // }
    },
    removeRow() {
      if (this.tableData.length > 1) {
        this.tableData.pop();
        this.radioSelections = [];
      }
    },
    addColumn() {
      this.tableData.forEach((row) => {
        row.push({ value: '' });
      });
      // console.log(this.tableData, 'colcolcol');
      // if (this.tableData[0].length >= 3) {
      //   this.showAddCol = false;
      // }
    },
    removeColumn() {
      if (this.tableData[0].length > 1) {
        this.tableData.forEach((row) => {
          row.pop();
        });
        this.radioSelections = [];
      }
    },
    editorReady(instance) {
      this.richEditor.instance = instance;
      let currentContent = this.richEditor.object[this.richEditor.parameterName];
      this.richEditor.instance.setContent(currentContent);
      // 光标定位到Ueditor
      this.richEditor.instance.focus(true);
    },
    inputClick(object, parameterName) {
      this.richEditor.object = object;
      this.richEditor.parameterName = parameterName;
      this.richEditor.dialogVisible = true;
    },
    editorConfirm() {
      let content = this.richEditor.instance.getContent();
      this.richEditor.object[this.richEditor.parameterName] = content;
      this.richEditor.dialogVisible = false;
    },

    getCategoryList() {
      categoryApi.list().then((res) => {
        this.categoryList = res.data.data;
      });
    },
    // handleChange() {
    //   if (!this.form.difficulty) {
    //     this.difficultyInfo = null;
    //     return;
    //   }
    //   let query = {};
    //   query.type = this.form.type;
    //   query.questionType = this.form.questionType;
    //   query.difficulty = this.form.difficulty;
    //   difficultyApi
    //     .isSetting(query)
    //     .then((res) => {
    //       this.difficultyInfo = res.data;
    //     })
    //     .catch((e) => {
    //       this.difficultyInfo = null;
    //       this.fileList = [];
    //     });
    // },
    submitForm() {
      let that = this;
      // if (this.difficultyInfo === null) {
      //   this.$message.error('请选择难度！');
      //   return false;
      // }
      let obj = that.processingData();
      that.form.customInfo = obj.questionData;
      that.form.answer = obj.answerData;
      this.$refs.form.validate((valid) => {
        if (valid) {
          if (that.form.answer.length == 0) {
            return that.$message.warning('请填写并选择答案');
          }
          this.formLoading = true;
          questionApi
            .saveOrUpdate(this.form)
            .then((re) => {
              if (re.success) {
                this.$message.success(re.message);
                this.formLoading = false;
                if (this.form.courseType == 1) {
                  this.$router.push('/train/trainAfterCourse');
                } else {
                  this.$router.push('/train/visual');
                }
              } else {
                this.$message.error(re.message);
                this.formLoading = false;
              }
            })
            .catch((e) => {
              this.formLoading = false;
            });
        } else {
          return false;
        }
      });
    },
    resetForm() {
      let lastId = this.form.id;
      let lastCourseType = this.form.courseType;
      this.$refs['form'].resetFields();
      this.form = {
        id: null,
        title: '',
        categoryId: '',
        // difficulty: '',
        // difficultyId: '',
        type: 'VISUAL_',
        questionType: 'VISUAL_FIND_SIMILAR_CHARACTER',
        customInfo: [],
        answer: [],
        isRandom: false,
        analysis: '',
        score: undefined,
        badge: 3,
        courseType: 0
      };
      this.form.id = lastId;
      this.form.courseType = lastCourseType;
      this.tableData = [
        [
          {
            value: '',
            label: { x: 0, y: 0 }
          }
        ]
      ];
      this.radioSelections = [];
    }
  },
  computed: {
    ...mapGetters('enumItem', ['enumFormat']),
    ...mapState('enumItem', {
      trainType: (state) => state.train.trainType,
      difficultyList: (state) => state.train.difficultyList,
      trainQuestionType: (state) => state.train.trainQuestionType
    })
  }
};
</script>

<style lang="less" scoped>
.question-item-label {
  display: flex;
  margin-bottom: 12px;
}

.drawing-item {
  overflow: hidden;
  white-space: normal;
  word-break: break-all;
}
.table-row {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  margin-left: -24px;
}

.table-cell {
  margin-right: 10px;
}

.table-input {
  width: 150px;
  margin-right: 10px;
  text-align: center !important;
}

.add-column {
  margin-left: 10px;
}

.add-row {
  margin-top: 10px;
  // margin-left: 24px;
}

.add-column-button,
.add-row-button {
  width: 70px;
}
.firstRow {
  margin-left: 24px;
}
</style>
<style lang="less">
.el-radio {
  margin-right: 10px !important;
  .el-radio__label {
    display: none !important;
  }
}
</style>
