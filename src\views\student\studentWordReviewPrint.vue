<template>
  <div class="app-container2">
    <div data-label="页面">
      <div data-label="学员打印--单词">
        <input type="hidden" id="WordPrintCode" value="P213337" />
        <div class="buttom1 clearfix">
          <div class="buttom2 clearfix">
            <!-- <a style="background: #3dab93" @click="exportList()">下载</a> -->
            <a
              style="background: #3dab93; margin-left: 20px"
              @click="printBtn()"
              >打印</a
            >
            <a style="background: #f0ad4e; margin-left: 20px" @click="goback()"
              >返回</a
            >
          </div>
        </div>
        <!-- 打印主体  -->
        <div ref="print">
          <div id="print" class="clearfix">
            <div data-label="打印操作" class="name2" style="text-align: center">
              <div class="first-top clearfix" style="padding-top: 30px">
                <span style="font-size: 20px"
                  >学员姓名：<label style="width: 100px; font-weight: normal">{{
                    realName
                  }}</label></span
                >
              </div>
              <hr />
              <div v-loading="exportLoading">

                <table class="table_default1" width="100%" border="0" cellspacing="0"  style="fontSize:16px;background-color:black;margin-bottom:20px;border-collapse:collapse;border:none;" cellpadding="0" v-for="(item,index) in data" :key="index">

                  <tr>
                    <th style="padding:20px 0;background-color:white;border:1px solid black;">复习日期</th>
                    <th  style="background-color:white;border:1px solid black;" v-for="(item, index) in item" :key="index">
                      {{ item.localDateTime }}
                    </th>
                  </tr>
                  <tr>
                    <th style="padding:20px 0;background-color:white;border:1px solid black;">遗忘词数</th>
                    <th style="background-color:white;border:1px solid black;" v-for="(item, index) in item" :key="index">
                      {{ item.num }}
                    </th>
                  </tr>
                </table>
                <!-- <span>一定要根据复习日期按时做好抗遗忘复习哦</span> -->
              </div>
            </div>
            <div
              style="
                width: 998px;
                text-align: right;
                margin: 0 auto;
                margin-top: 20px;
                font-size: 18px;
                clear: both;
              "
            >
              <p id="time">打印时间：{{ time }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import studentReviewPrintApi from "@/api/studentReviewPrint";
import printCloseApi from "@/api/studentPrintClose";
import ls from "@/api/sessionStorage";
export default {
  data() {
    return {
      // 打印数据
      printData: [],
      //         printObj: {
      //         id: "#printMe",
      //         popTitle: "览析反洗钱制裁名单系统",
      //         extraCss: "",
      //         extraHead: '<meta http-equiv="Content-Language"content="zh-cn"/>' ,
      //         endCallback(){
      //   alert("12312");
      // },     }
      //,
      exportLoading:false,
      tableLabel1: [
        {
          day: "第一天",
        },
        {
          day: "第二天",
        },
        {
          day: "第三天",
        },
        {
          day: "第五天",
        },
        {
          day: "第七天",
        },
        {
          day: "第九天",
        },
        {
          day: "第十二天",
        },
        {
          day: "第十四天",
        },
        {
          day: "第十七天",
        },
        {
          day: "第二十一天",
        },
      ],
      tableData: [
      ],
      data:[],
      dataQuary: {
        studentCode: "",
      },
      time: "",
      English: true,
      Chinese: true,
      result: [],
      realName:''
    };
  },
  created() {
    this.dataQuary.studentCode = ls.getItem("printReviewStudentCode");
    this.realName=ls.getItem("printReviewRealName");
    this.fetchData();
  },
  watch: {
    getItem() {
      if (document.execCommand("print")) {
        console.log("123");
      }
    },
  },
  methods: {
    // 返回
    goback() {
      this.$store.dispatch("delVisitedViews", this.$route);
      this.$router.go(-1); //返回上一层
      this.fetchData();
    },
    // 显示学员测试结果
    fetchData() {

      const that = this;
      that.exportLoading=true;
      studentReviewPrintApi.printPlay(this.dataQuary.studentCode).then(res => {
        that.data = res.data
        that.data.forEach((ele)=>{
          ele.localDateTime = ele.localDateTime.split(' ')[0];
        });
        if(that.data.length>=10){
          that.data=that.group(that.data,10);

        }
       that.exportLoading=false;
        // that.splitTable(that.printData.wordsMap)
      })

    },
    // 分割表格数据,每5个为一组
    splitTable(data) {
      // 格式化后台返回的集合
      var newData = [];
      for (const key in data) {
        newData.push({
          English: key,
          Chinese: data[key],
        });
      }
      var chunk = 5; //表格分组数
      for (var i = 0, j = newData.length; i < j; i += chunk) {
        this.result.push(newData.slice(i, i + chunk));
      }
    },
    group(array, subGroupLength) {
      let index = 0;
      let newArray = [];
      while(index < array.length) {
          newArray.push(array.slice(index, index += subGroupLength));
      }
      newArray.forEach((ele)=>{
        console.log(ele);
        if(ele.length<9){
          console.log(ele.length+"wyy")
        let index = ele.length;
        let i =9-ele.length;
        console.log(i+"i")
        for(var j=0 ; j<=i;j++){
          console.log(index+j+1+"i")
          ele[index+j]={localDateTime:'0000-00-00',num:'0'}
        }
        }
      });
      return newArray;
  },
    // 只打印英文
    printEnglish() {
      this.English = true;
      this.Chinese = false;
      this.getCurrentTime();
    },
    // 只打印中文
    printChinese() {
      this.English = false;
      this.Chinese = true;
      this.getCurrentTime();
    },
    // 全打印
    printAll() {
      this.English = true;
      this.Chinese = true;
      this.getCurrentTime();
    },
    // 打印
    printBtn() {
      // window.print();
      this.getCurrentTime();
      setTimeout(() => {
        this.$print(this.$refs.print);
      }, 500);
      // printCloseApi
      //   .editEnable(ls.getItem("printReviewStudentCode"))
      //   .then((res) => {});
      //
    },
    // 获取当前时间
    getCurrentTime() {
      var _this = this;
      let yy = new Date().getFullYear();
      let mm = new Date().getMonth() + 1;
      let dd = new Date().getDate();
      let hh = new Date().getHours();
      let mf =
        new Date().getMinutes() < 10
          ? "0" + new Date().getMinutes()
          : new Date().getMinutes();
      let ss =
        new Date().getSeconds() < 10
          ? "0" + new Date().getSeconds()
          : new Date().getSeconds();
      _this.gettime = yy + "-" + mm + "-" + dd + " " + hh + ":" + mf + ":" + ss;
      // return _this.gettime
      _this.time = _this.gettime;
    },

    // 下载
    exportList() {
      const that = this;
      that.exportLoading = true;
      printApi.printExport(that.dataQuary.wordPrintCode).then((response) => {
        console.log(response);
        if (!response) {
          this.$notify.error({
            title: "操作失败",
            message: "文件下载失败",
          });
        }
        const url = window.URL.createObjectURL(response);
        const link = document.createElement("a");
        link.style.display = "none";
        link.href = url; // 获取服务器端的文件名
        link.setAttribute("download", "单词表.xls");
        document.body.appendChild(link);
        link.click();
        this.exportLoading = false;
      });
    },
  },
};
</script>

<style scoped>
.app-container2 {
  padding: 20px;
  color: #676a6c;
}
.buttom2 {
  width: auto;
  margin: 0px 180px 0px auto;
  float: right;
  color: rgb(255, 255, 255);
  cursor: pointer;
}

.buttom2 a {
  display: block;
  float: left;
  color: rgb(255, 255, 255);
  width: 90px;
  height: 35px;
  border-radius: 5px;
  text-align: center;
  line-height: 35px;
}
.table_default1{
  align-self: start;
}
.buttom1 {
  margin-top: 40px;
  color: rgb(255, 255, 255);
  cursor: pointer;
  margin-left: 200px;
}

.buttom1 a {
  display: block;
  float: left;
  color: rgb(255, 255, 255);
  width: 90px;
  height: 35px;
  border-radius: 5px;
  text-align: center;
  line-height: 35px;
}

.first-top,
.first-center {
  width: 800px;
  margin: 40px auto 0px;
}

.first-top span {
  display: block;
  float: left;
  width: 25%;
  text-align: left;
}

.first-center span {
  display: block;
  float: left;
  width: 25%;
  text-align: center;
  margin-top: -10px !important;
}
.first-top input {
  color: #676a6c;
}
.clearfix {
  zoom: 1;
}

.printonly {
  display: none;
}
@media print {
  input,
  .noprint {
    display: none;
  }
  .printonly {
    display: block;
    width: 50%;
  }
}
.clearfix::before,
.clearfix::after {
  content: "";
  line-height: 0;
  display: table;
}

.clearfix::after {
  clear: both;
}

.crumbs {
  line-height: 62px;
  margin-top: 0px !important;
}
.name2 {
  width: 100%;
  margin: 0 auto;
}

@media (max-width: 767px) {
  .buttom1 {
    margin-left: 0;
  }
  .buttom2 {
    width: 100%;
    margin: 10px 0;
  }
}
</style>
