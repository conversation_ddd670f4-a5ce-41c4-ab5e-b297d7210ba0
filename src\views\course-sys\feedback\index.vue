<template>
  <div class="app-container">
    <el-form :inline="true" class="container-card" label-width="96px">
      <el-form-item label="反馈时间：">
        <el-date-picker style="width: 100%;" v-model="createTime" type="daterange" value-format="yyyy-MM-dd HH:mm:ss"
          align="right" unlink-panels range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="反馈类型：">
        <el-select v-model="dataQuery.type" filterable placeholder="请选择">
          <el-option label="校区" value="1"></el-option>
          <el-option label="教练 " value="2"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="getList()">搜索</el-button>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-re" @click="resetQuery()">重置</el-button>
      </el-form-item>
    </el-form>
    <el-table class="common-table" v-loading="tableLoading" :data="tableData" height="400px"
      style="width: 100%;margin-bottom: 20px;" row-key="id" border default-expand-all
      :tree-props="{list: 'children', hasChildren: 'true'}">
      <el-table-column prop="id" label="反馈ID"></el-table-column>
      <el-table-column prop="parentName" label="反馈人姓名"></el-table-column>
      <el-table-column prop="type" label="反馈类型">
        <template slot-scope="scope">
          <span v-if="scope.row.type===1">校区</span>
          <span v-if="scope.row.type===2">教练 </span>
        </template>
      </el-table-column>
      <el-table-column prop="remark" label="详情描述"></el-table-column>
      <el-table-column prop="createTime" label="反馈时间"></el-table-column>
      <el-table-column label="操作" align="center" width="280">
        <template slot-scope="scope">
          <el-button type="primary" @click="handleView(scope.row)" size="mini">查看</el-button>
          <el-button type="danger" @click="handleDel(scope.row)" size="mini">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog class="recharge-dialog" title="反馈详情" :visible.sync="open" width="50%">
      <el-form ref="form" :model="form" label-width="96px" :rules="rules" style="width: 100%;" @close="reset">
        <el-form-item label="反馈人姓名：" prop="parentName">
          {{form.parentName}}
        </el-form-item>
        <el-form-item label="联系方式：" prop="parentTel">
          {{form.parentTel}}
        </el-form-item>
        <el-form-item label="反馈类型：" prop="type">
          {{form.type}}
        </el-form-item>
        <el-form-item label="详细描述：" prop="remark">
          {{form.remark}}
        </el-form-item>
        <el-form-item label="反馈时间：" prop="createTime">
          {{form.createTime}}
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="cancel()">关闭</el-button>
      </div>
    </el-dialog>

    <!-- 分页 -->
    <el-col :span="20" style="overflow-x: auto;" :xs="24">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>
  </div>
</template>

<script>

import feedbackApi from '@/api/feedback'
import { pageParamNames } from "@/utils/constants";
export default {
  data() {
    return {
      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      tableData: [],
      open: false,
      dataQuery: {
        type: null,
        startTime: null,
        endTime: null
      },
      createTime: [],
      form: {},
      rules: {}
    };
  },
  created() {
    this.getList();
  },
  methods: {
    //获取列表
    getList() {
      const that = this;
      var a = that.createTime;
      if (a != null) {
        that.dataQuery.startTime = a[0];
        that.dataQuery.endTime = a[1];
      } else {
        that.dataQuery.startTime = null;
        that.dataQuery.endTime = null;
      }
      that.tableLoading = true;
      feedbackApi.feedbackList(that.tablePage.currentPage, that.tablePage.size, that.dataQuery).then(res => {
        that.tableData = res.data.data
        that.tableLoading = false
        // 设置后台返回的分页参数
        pageParamNames.forEach(name => this.$set(this.tablePage, name, parseInt(res.data[name])))
      })
    },

    //重置按钮
    resetQuery() {
      this.createTime = [];
      this.address = [];
      this.dataQuery = {
        status: null,
        courseName: null,
        startTime: null,
        endTime: null,
        province: null,
        city: null
      };
      this.getList();
    },
    //添加按钮
    handleAdd() {
      this.reset();
      this.open = true;
    },
    //查看按钮
    handleView(row) {
      this.reset();
      feedbackApi.feedbackDetail(row.id).then(response => {
        this.form = response.data;
        this.open = true;
      });
    },
    //删除按钮
    handleDel(row) {
      this.$confirm('确定要删除该反馈吗?', '删除反馈', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        feedbackApi.feedbackDel(row.id).then(res => {
          this.getList();
          this.$message.success('删除成功!')
        }).catch(err => {
        })
      }).catch(err => {
      })
    },
    reset() {
      this.form = {
        id: null,
        parentName: null,
        parentTel: null,
        type: null,
        createTime: null,
        remark: null,
      };
      if (this.$refs['form']) {
        this.$refs['form'].resetFields();
      }
    },
    cancel() {
      this.open = false;
      this.reset();
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val;
      this.getList();
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val;
      this.getList();
    },
  }
};
</script>

<style>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}
@media screen and (max-width: 767px) {
  .recharge-dialog .el-dialog {
    width: 90% !important;
  }

  .el-message-box {
    width: 80% !important;
  }
}
.illegalPic {
  width: 50px;
  height: 50px;
  margin-right: 5px;
}
</style>
