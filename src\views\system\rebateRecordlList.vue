<template>
  <div class="app-container">
    <el-form :inline="true" class="container-card" label-width="110px" label-position="left">
      <el-form-item label="增加时间">
        <el-date-picker v-model="dataQuery.RegTime" type="daterange" align="right" unlink-panels range-separator="至"
          start-placeholder="开始日期" end-placeholder="结束日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="商户编号">
        <el-input id="merchantCode" v-model="dataQuery.merchantCode" name="id" />
      </el-form-item>
      <el-form-item label="状态：">
        <el-select v-model="dataQuery.IsEnable" placeholder="全部" style="width: 185px;">
          <el-option v-for="item in [{ 1: '已指定' }, { 0: '未指定' }]" :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="fetchData()">搜索</el-button>
      </el-form-item>
    </el-form>
    <!-- <el-form :inline="true" style="margin-bottom: 20px;">
      <el-button type="success" icon="el-icon-caret-right" @click="">全部已读</el-button>
    </el-form> -->

    <el-table class="period-table" v-loading="tableLoading" :data="tableData" style="width: 100%;margin-bottom: 20px;"
      row-key="id" border default-expand-all :tree-props="{ list: 'children', hasChildren: 'true' }">
      <!-- <el-table-column
        type="selection"
        width="55">
      </el-table-column> -->
      <!-- <el-table-column prop="loginName">
        <template slot="header"  >
          <i class="el-icon-message"></i>
        </template>z
      </el-table-column> -->
      <el-table-column prop="merchantCode" label="商户编号" show-overflow-tooltip align="center"></el-table-column>
      <el-table-column prop="refereeCode" label="指定商户编号" show-overflow-tooltip align="center"></el-table-column>
      <el-table-column label="操作" align="center" width="200">
        <template slot-scope="scope">
          <!-- // <el-tooltip :content="scope.row.isEnable === 0 ? '开通' : '暂停'" placement="top"> -->
          <el-button type="warning" icon="el-icon-edit-outline" v-if="scope.row.isEnable === 0"
            @click="rebateAdd(scope.row.id)" size="mini">指定商户</el-button>
          <span v-if="scope.row.isEnable === 1">暂无操作</span>
          <!-- <el-button type="warning" size="mini" icon="el-icon-switch-button" v-if="scope.row.isEnable === 0" @click="marketStatus(scope.row.id, scope.row.isEnable)">开通</el-button>
            <el-button type="danger" size="mini" icon="el-icon-video-pause" v-else @click="marketStatus(scope.row.id, scope.row.isEnable)">暂停</el-button> -->
          <!-- </el-tooltip> -->
        </template>
      </el-table-column>
      <el-table-column prop="money" label="返利金额" show-overflow-tooltip align="center"></el-table-column>
      <el-table-column prop="isEnable" label="状态" show-overflow-tooltip align="center">
        <template slot-scope="scope">
          <span class="green" v-if="scope.row.isEnable === 1">已审核</span>
          <span class="red" v-if="scope.row.isEnable === 0">未审核</span>
        </template>
      </el-table-column>
      <el-table-column prop="addTime" label="添加时间" show-overflow-tooltip align="center"></el-table-column>
    </el-table>
    <!-- 添加弹窗 -->
    <el-dialog title="给指定商户充值" :visible.sync="dialogVisible" width="60%" :close-on-click-modal="false" @close="close">
      <el-form :ref="'addCourseData'" :model="addCourseData" label-position="left" label-width="120px"
        style="width: 50%;">
        <el-form-item label="id:" prop="id" v-show="false">
          <el-input v-model="addCourseData.id" />
        </el-form-item>
        <el-form-item label="商户编号:" prop="merchantCode">
          <el-input v-model="addCourseData.merchantCode" disabled />
        </el-form-item>
        <el-form-item label="指定商户编号:" prop="refereeCode">
          <el-input v-model="addCourseData.refereeCode" />
        </el-form-item>
        <el-form-item label="返利金额">
          <el-input v-model="addCourseData.money" disabled />
        </el-form-item>
        <!-- <el-form-item label="状态:" prop="isEnable">
          <template >
            <el-radio v-model="radio" label="1" @change="change(radio)">已指定</el-radio>
            <el-radio v-model="radio" label="0" @change="change(radio)">未指定</el-radio>
          </template>
        </el-form-item> -->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <!-- <template slot-scope="scope"> -->
        <el-button size="mini" type="primary" @click="addActiveFun('addCourseData')">指定充值</el-button>
        <el-button size="mini" @click="close">关闭</el-button>
        <!-- </template> -->
      </div>
    </el-dialog>
    <el-dialog class="remark-dialog" title="交易密码确认" :visible.sync="dialogVisibleF" width="20%"
      :close-on-click-modal="false">
      <el-input v-model="secondPassWord" type="password" show-password></el-input>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="quertrue(secondPassWord)">确认</el-button>
      </div>
    </el-dialog>
    <!-- 分页 -->
    <el-col :span="24">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>
  </div>
</template>

<script>
//import Tinymce from "@/components/Tinymce";
import {
  pageParamNames
} from "@/utils/constants";
import rebateRecordList from "@/api/rebateRecordList";
import merchantAccountFlowApi from "@/api/merchantAccountFlow";
export default {
  data() {
    return {
      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      tableData: [{
        name: 1,
        title: '1111'
      }],
      dataQuery: {},
      secondPassWord: '',
      dialogVisible: false,
      dialogVisibleF: false,
      addOrUpdate: true,
      updateActive: {},
      addCourseData: {},
      showLoginAccount: false
    };
  },
  created() {
    this.fetchData();
  },
  methods: {
    // 查询提现列表
    fetchData() {
      const that = this;
      var a = that.regTime;
      if (a != null) {
        that.dataQuery.startDate = a[0];
        that.dataQuery.endDate = a[1];
      }
      that.tableLoading = true
      rebateRecordList.pageRebateList(
        that.tablePage.currentPage,
        that.tablePage.size,
        that.dataQuery
      ).then(res => {
        console.log(res);
        that.tableData = res.data.data;
        that.radio = res.data.data.isEnable;
        that.tableLoading = false;
        // 设置后台返回的分页参数
        pageParamNames.forEach(name =>
          that.$set(that.tablePage, name, parseInt(res.data[name]))
        );
      });
    },
    //新增操作
    clickAdd() {
      this.dialogVisible = true;
    },
    //指定商户查询
    rebateAdd(id) {
      const that = this
      that.dialogVisible = true
      that.addOrUpdate = false
      rebateRecordList.query(id).then(res => {
        that.addCourseData = res.data
        that.radio = that.addCourseData.isEnable.toString(); //状态回显
      }).catch(err => {

      })
    },
    //指定充值
    addActiveFun(ele) {
      const that = this;
      if (!that.addCourseData.merchantCode) {
        that.$message.info("商户编号不能为空");
        return false;
      }
      if (!that.addCourseData.refereeCode) {
        that.$message.info("指定商户编号不能为空");
        return false;
      }
      if (!that.addCourseData.money) {
        that.$message.info("返利金额不能为空");
        return false;
      }
      that.dialogVisibleF = true;

    },
    // 打开修改登陆账号
    openLogin() {
      this.showLoginAccount = true
    },
    closeLogin() {
      this.showLoginAccount = false
    },
    close() {
      this.dialogVisible = false;
      this.addCourseData.refereeCode = '';
    },
    quertrue(secondPassWord) {
      const that = this;
      if (secondPassWord.length <= 0) {
        that.$message.info("交易密码不能为空");
      } else {
        merchantAccountFlowApi.checkSecondPwd(secondPassWord).then(res => {
          if (res.success) {
            rebateRecordList.addRebate(that.addCourseData).then(res => {
              that.dialogVisibleF = '',
                that.secondPassWord = '',
                that.addCourseData.refereeCode = '';
              that.fetchData();
            }).catch(err => {

            })
          } else {
            that.dialogVisible = false;
            that.$message.info("验证失败");
          }
        })
      }
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val;
      this.fetchData();
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val;
      this.fetchData();
    }
  }
};
</script>

<style>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.period-table td,
.period-table th {
  text-align: center;
}

.mt20 {
  margin-top: 20px;
}</style>
