import request from '@/utils/request'

export default {
  // 分页查询
  list(data) {
    return request({
      url: '/xi/web/taskawrad/pagelist',
      method: 'GET',
      params: data
    })
  },
  // 详情
  detail(id){
    return request({
      url: '/xi/web/taskawrad',
      method: 'GET',
      params:{
        id:id
      }
    })
  },
//新增或修改
  saveOrUpdate01(data) {
    return request({
      url: '/xi/web/taskawrad/saveOrUpdate',
      method: 'POST',
      data
    })
  },
  //获取可配置任务项
  saveOrUpdate() {
    return request({
      url: '/xi/web/taskawrad/gettasklist',
      method: 'GET',
    })
  },
  //删除
  delete(id){
    return request({
      url: '/xi/web/taskawrad',
      method: 'DELETE',
      params:{
        id:id
      }
    })
  }
}
