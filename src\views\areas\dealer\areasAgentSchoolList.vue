<template>
  <div class="app-container">
    <el-form :inline="true" class="container-card" label-width="96px" label-position="left">

      <el-row>
        <el-col :span="8" :xs="24">
          <el-form-item label="门店编号：">
            <el-input id="merchantCode" v-model="dataQuery.merchantCode" name="id" placeholder="门店编号：" />
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="门店名称：">
            <el-input id="merchantName" v-model="dataQuery.merchantName" name="id" placeholder="请输入门店名称" />
          </el-form-item>
        </el-col>

        <el-col :span="8" :xs="24">
          <el-form-item label="状态:">
            <el-select v-model="dataQuery.isEnable" filterable value-key="value" placeholder="请选择">
              <el-option v-for="(item, index) in [{ value: 1, label: '开通' },
              { value: 0, label: '暂停' },
              { value: -1, label: '系统关闭' },
              { value: -2, label: '年审关闭' }]" :key="index" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="14" :xs="24">
          <el-form-item label="注册时间：">
            <el-date-picker style="width: 100%;" v-model="regTime" type="daterange" value-format="yyyy-MM-dd HH:mm:ss"
              align="right" unlink-panels range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="10" :xs="24" style="text-align: right">
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="fetchData()">搜索</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-col :span="24" style="margin-bottom: 30px;">
      <!-- <el-button type="success"  v-if="roleTag!='Agent'" icon="el-icon-plus" @click="openAdd()" size="mini">新增</el-button> -->
      <el-button type="warning" icon="el-icon-document-copy" @click="exportSchool" v-loading="exportLoading"
        size="mini">导出
      </el-button>
     
    </el-col>

    <el-table class="common-table" v-loading="tableLoading" :data="tableData" height="400px"
      style="width: 100%;margin-bottom: 20px;" row-key="id" border default-expand-all
      :tree-props="{ list: 'children', hasChildren: 'true' }">
      <el-table-column prop="merchantCode" label="门店编号" width="150px"></el-table-column>
      <el-table-column prop="name" label="登录账号" width="150px"></el-table-column>
      <el-table-column label="操作" align="center" width="300">
        <template slot-scope="scope">
          <el-button v-if="scope.row.refereeCode === merchantCode" type="primary" icon="el-icon-view"
            @click="recharge(scope.row.merchantCode)" size="mini">充值
          </el-button>
          <el-button type="primary" icon="el-icon-view" @click="editSchool(scope.row.id)" size="mini">编辑
          </el-button>
          <el-button type="info" size="mini" icon="el-icon-switch-button" v-if="scope.row.isCheck === 0"
            @click="agree(scope.row.id, -3)">不通过
          </el-button>
          <el-button type="warning" size="mini" icon="el-icon-switch-button" v-if="scope.row.isCheck === 0"
            @click="agree(scope.row.id, 3)">通过
          </el-button>
          <el-button type="warning" size="mini" icon="el-icon-switch-button"
            v-if="scope.row.isEnable === 0 && scope.row.isCheck === 1" @click="schoolStatus(scope.row.id,scope.row.isEnable, 1)">开通
          </el-button>
          <el-button type="danger" size="mini" icon="el-icon-video-pause"
            v-if="scope.row.isEnable === 1 && scope.row.isCheck === 1" @click="schoolStatus(scope.row.id,scope.row.isEnable, 0)">暂停
          </el-button>
        </template>
      </el-table-column>
      <el-table-column prop="merchantName" label="门店名称" width="200px"></el-table-column>
      <el-table-column prop="refereeCode" label="上级编号" width="180"></el-table-column>
      <el-table-column prop="realName" label="负责人" show-overflow-tooltip></el-table-column>
      <el-table-column prop="address" label="所在地区" width="200px"></el-table-column>
      <el-table-column prop="totalCourseHours" label="累计充值学时（节）" width="150px"></el-table-column>
      <el-table-column prop="saleCourseHours" label="已售学时（节）" width="150px"></el-table-column>
      <el-table-column prop="haveCourseHours" label="剩余学时（节）" width="150px"></el-table-column>
      <el-table-column prop="withdrawMoney" label="账户余额（元）" width="150px"></el-table-column>
      <el-table-column prop="isCheckStatus" label="审核状态">
      </el-table-column>
      <el-table-column prop="isEnable" label="账户状态">
        <template slot-scope="scope">
          <span v-if="scope.row.isEnable === -1" class="red">系统关闭</span>
          <span v-else-if="scope.row.isEnable === 1" class="green">开通</span>
          <span v-else-if="scope.row.isEnable === -2" class="green">年审关闭</span>
          <span v-else class="red">暂停</span>
        </template>
      </el-table-column>
      <el-table-column prop="regTime" label="添加时间" show-overflow-tooltip></el-table-column>
      <!-- <el-table-column prop="canTeachStatus" label="是否教学交付能力">
       </el-table-column>-->
    </el-table>
    <el-dialog class="recharge-dialog" title="充值" :visible.sync="showSchoolRecharge" width="70%"
      :close-on-click-modal="false">
      <el-form v-model="schoolRecharge" label-position="left" label-width="130px" style="width: 100%;">
        <el-form-item label="托管中心账户余额：" prop="balance">
          <el-input v-model="schoolRecharge.balance" readonly='readonly' disabled />
        </el-form-item>
        <el-form-item label="充值账户：" prop="merchantCode">
          <el-input v-model="schoolRecharge.merchantCode" readonly="readonly" disabled />
        </el-form-item>
        <el-form-item label="充值金额 ：" prop="rechargeMoney">
          <el-input v-model="schoolRecharge.rechargeMoney" @blur="rechargeMoneyChange()" />
        </el-form-item>
        <el-form-item label="到账学时 ：" prop="rechargeCourse">
          <el-input v-model="schoolRecharge.rechargeCourse" readonly='readonly' disabled />
        </el-form-item>
        <el-form-item label="充值说明 ：" prop="rechargeRemark">
          <el-input v-model="schoolRecharge.rechargeRemark" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="openDialogVisible()">确定</el-button>
      </div>
    </el-dialog>

    <el-dialog class="remark-dialog" title="交易密码确认" :visible.sync="dialogVisible" width="20%"
      :close-on-click-modal="false">
      <el-input v-model="secondPassWord" show-password></el-input>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="submitRecharge(secondPassWord)">确认
        </el-button>
      </div>
    </el-dialog>
    <!-- 分页 -->
    <el-col :span="24" style="overflow-x: auto;" :xs="24">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>
  </div>
</template>

<script>

import schoolApi from "@/api/areasSchoolList";
import { ossPrClient } from '@/api/alibaba'
import { pageParamNames } from "@/utils/constants";
import dealerListApi from '@/api/areasDealerList'
import merchantAccountFlowApi from '@/api/merchantAccountFlow'
import ls from '@/api/sessionStorage'

export default {
  data() {
    const self = this;
    return {
      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      tableData: [],
      schoolRecharge: {
        rechargeCourse: 0
      },
      merchantCode: '',
      showSchoolRecharge: false,
      exportLoading: false,
      dataQuery: {},
      dialogVisible: false,
      updateMarketDate: {},
      addMarketDate: {},
      showLoginAccount: false,
      address: "",
      province: "",
      secondPassWord: '',
      city: "",
      district: "",
      loaded: false,
      regTime: [],
      roleTag: '',

    };
  },
  created() {
    this.fetchData();
    ossPrClient();
    this.getRoleTag();

  },
  methods: {
    
    openDialogVisible() {
      const that = this;
      if (!that.schoolRecharge.merchantCode) {
        that.$message.error('充值账户不能为空')
        return false;
      }
      if (!that.schoolRecharge.balance) {
        that.$message.error('余额不足')
        return false;
      }


      if (!that.schoolRecharge.rechargeMoney) {
        that.$message.error('充值金额不能为空')
        return false;
      }
      if (that.schoolRecharge.rechargeMoney <= 0) {
        that.$message.error('充值金额必须为0以上的整数')
        return false;
      }

      if (!that.schoolRecharge.rechargeCourse) {
        that.$message.error('到账学时不能为空')
        return false;
      }
      if (that.schoolRecharge.rechargeCourse <= 0) {
        that.$message.error('到账学时必须为20的倍数')
        return false;
      }
      this.dialogVisible = true;
      this.secondPassWord = "";
    },

    getRoleTag() {
      schoolApi.getCurrentAdmin().then(res => {
        this.roleTag = res.data.roleTag
        this.merchantCode = res.data.merchantCode;
      })
    },
    schoolStatus(id,isEnable,enable) {
      const that = this;
      this.$confirm('确定操作吗?', '修改状态', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        schoolApi.openEnable(id,isEnable,enable).then(res => {
          if (!res.success) {
            that.$message.error(res.message)
            return
          }
          that.$nextTick(() => that.fetchData())
          that.$message.success(res.message)
        })
      })
    },
    //校区审核
    agree(id, isCheck) {
      const that = this;
      this.$confirm("确定操作吗?", "审核", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          dealerListApi
            .updateIsCheck(id, isCheck)
            .then((res) => {
              that.$nextTick(() => that.fetchData());
              that.$message.success("审核成功!");
            })
            .catch((err) => {
            });
        })
        .catch((err) => {
        });
    },

    rechargeMoneyChange() {
      schoolApi.getRechargeBackCourse(this.schoolRecharge.merchantCode, this.schoolRecharge.rechargeMoney).then(res => {
        if (!res.success) {
          this.$message.error('充值金额不能为空')
          return false;
        }
        this.schoolRecharge.rechargeCourse = res.data
      })

    },
    // 查询提现列表
    fetchData() {
      const that = this;
      that.tableLoading = true;
      var a = that.regTime;
      if (a != null) {
        that.dataQuery.startRegTime = a[0];
        that.dataQuery.endRegTime = a[1];
      }
      schoolApi.agentSchoolPage(
        that.tablePage.currentPage,
        that.tablePage.size,
        that.dataQuery
      ).then(res => {
        that.tableData = res.data.data;
        that.tableLoading = false;
        // 设置后台返回的分页参数
        pageParamNames.forEach(name =>
          that.$set(that.tablePage, name, parseInt(res.data[name]))
        );
      });
    },
    exportSchool() {
      const that = this;
      that.exportLoading = true;
      schoolApi.exportAgentAreasSchool(that.dataQuery).then(response => {
        console.log(response)
        if (!response) {
          this.$notify.error({
            title: '操作失败',
            message: '文件下载失败'
          })
        }
        const url = window.URL.createObjectURL(response)
        const link = document.createElement('a')
        link.style.display = 'none'
        link.href = url // 获取服务器端的文件名
        link.setAttribute('download', '门店表.xls')
        document.body.appendChild(link)
        link.click()
        that.exportLoading = false

      })
    },
    openAdd() {
      const that = this;
      ls.setItem('addOrUpdate3', true);
      that.$router.push({
        path: "/areas/areasSchoolEdit",
        query: {
          addOrUpdate: true,
        },
      });
    },
    editSchool(id) {
      const that = this;
      ls.setItem('addOrUpdate3', false);
      ls.setItem('areasSchoolId', id);
      that.$router.push({
        path: "/areas/areasSchoolEdit",
        query: {
          id: id,
          addOrUpdate: false,
        },
      });
    },


    recharge(merchantCode) {
      const that = this;
      schoolApi.getMerchantAccount().then(res => {
        that.schoolRecharge.balance = res.data.balance
        that.schoolRecharge.merchantCode = merchantCode
        that.showSchoolRecharge = true;
        that.schoolRecharge = {
          balance: that.schoolRecharge.balance,
          merchantCode: that.schoolRecharge.merchantCode,
          rechargeMoney: 0,
          rechargeCourse: 0,
          rechargeRemark: ""
        }
      });


    },
    submitRecharge(secondPassWord) {
      const that = this;
      if (!secondPassWord) {
        this.$message.error('二级密码不能为空')
        return false;
      }
      merchantAccountFlowApi.checkSecondPwd(secondPassWord).then(res => {
        if (!res.success) {
          this.$message.error('二级密码验证失败')
          return false;
        }
        this.$confirm('确定操作吗?', '提交充值', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          schoolApi.submitRechargeCourse(that.schoolRecharge).then(res => {
            that.$nextTick(() => that.fetchData())
            that.$message.success('充值成功')
            that.dialogVisible = false;
          });
          that.showSchoolRecharge = false
        })
      })

    },


    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val;
      this.fetchData();
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val;
      this.fetchData();
    },
  }
};
</script>

<style>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

@media screen and (max-width: 767px) {
  .recharge-dialog .el-dialog {
    width: 90% !important;
  }

  .el-message-box {
    width: 80% !important;
  }
}
</style>
