<template>
  <div class="app-container">
    <el-form :model="form" ref="form" label-width="100px" v-loading="formLoading" :rules="rules">
      <el-form-item label="分类：" prop="titleType">
        <el-select v-model="form.titleType" disabled>
          <el-option v-for="item in titleType" :key="item.value" :value="item.value" :label="item.label"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="题型：" prop="questionType">
        <el-select v-model="form.questionType" disabled>
          <el-option v-for="item in questionType" :key="item.value" :value="item.value" :label="item.label"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="题目：" prop="title">
        <el-input placeholder="题目自动生成" type="textarea" :rows="5" v-model="form.title" readonly>
        </el-input>
      </el-form-item>
      <el-form-item label="x轴：" required>
        <el-input-number v-model="form.customItem.x" :min="1" :max="26" placeholder="x轴字母个数"/>
      </el-form-item>
      <el-form-item label="y轴：" required>
        <el-input-number v-model="form.customItem.y" :min="1" :max="50" placeholder="y轴数字个数"/>
      </el-form-item>
      <el-form-item label="干扰项：" required>
        <el-input-number v-model="form.customItem.disturb" :min="0" placeholder="干扰项个数"/>
      </el-form-item>
      <el-form-item label="答案个数：" required>
        <el-input-number v-model="form.customItem.answerNum" :min="1" placeholder="答案个数"/>
      </el-form-item>
      <el-form-item label="答案：" required>
          <div class="question-item-label" :key="index" v-for="(item,index) in form.items">
            <el-input v-model="item.label" style="width:70px;marginRight:5px;" readonly/>
            <el-input v-model="item.value" style="width:50px;marginRight:5px;" readonly/>
          </div>
      </el-form-item>
      <el-form-item label="说明：" prop="questionExplain" required>
        <el-input v-model="form.questionExplain">
          <i @click="inputClick(form,'questionExplain')" slot="suffix" class="el-icon-edit-outline"
             style="line-height: 36px;font-size: 20px;cursor: pointer;"></i>
        </el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="submitForm">提交</el-button>
        <el-button v-show="form.id===null" @click="resetForm">重置</el-button>
      </el-form-item>
    </el-form>
    <el-dialog :visible.sync="richEditor.dialogVisible" append-to-body :close-on-click-modal="false"
               style="width: 100%;height: 100%" :show-close="false" center
    >
      <Ueditor @ready="editorReady"/>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="editorConfirm">确 定</el-button>
        <el-button @click="richEditor.dialogVisible = false">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import Ueditor from '@/components/Ueditor'
import complexApi from '@/api/paper/complex-question'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import { mapGetters, mapState } from 'vuex'

export default {
  components: {
    Ueditor
  },
  data() {
    return {
      fileList: [],
      importFrom: {
        file: null
      },
      randomLength: undefined,
      form: {
        id: null,
        title: '',
        titleType: 'CARD',
        questionType: 'CARD',
        items: [],
        customItem: {
          x:undefined,
          y:undefined,
          disturb:undefined,
          answerNum:undefined
        },
        customInfo:'',
        questionExplain: ''
      },
      formLoading: false,
      rules: {
        questionExplain: [
          { required: true, message: '请输入题目说明', trigger: 'blur' }
        ],
        titleType: [
          { required: true, message: '请选择分类', trigger: 'blur' }
        ],
        questionType: [
          { required: true, message: '请选择题型', trigger: 'blur' }
        ],
      },
      richEditor: {
        dialogVisible: false,
        object: null,
        parameterName: '',
        instance: null
      }
    }
  },
  created() {
    let id = this.$route.query.id
    let _this = this
    if (id && parseInt(id) !== 0) {
      _this.formLoading = true
      complexApi.select(id).then(re => {
        _this.form = re.data;
        if (this.form.customInfo){
          _this.form.customItem = JSON.parse(this.form.customInfo);
        }
        _this.formLoading = false
      })
    }
  },
  methods: {
    editorReady(instance) {
      this.richEditor.instance = instance
      let currentContent = this.richEditor.object[this.richEditor.parameterName]
      this.richEditor.instance.setContent(currentContent)
      // 光标定位到Ueditor
      this.richEditor.instance.focus(true)
    },
    inputClick(object, parameterName) {
      this.richEditor.object = object
      this.richEditor.parameterName = parameterName
      this.richEditor.dialogVisible = true
    },
    editorConfirm() {
      let content = this.richEditor.instance.getContent()
      this.richEditor.object[this.richEditor.parameterName] = content
      this.richEditor.dialogVisible = false
    },
    submitForm() {
      let _this = this
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.formLoading = true
          //上传execel文件
          let file = new FormData()
          file.append('file', this.importFrom.file)
          this.form.customInfo = JSON.stringify(this.form.customItem);
          file.append('map', JSON.stringify(this.form))
          complexApi.edit(file).then(re => {
            if (re.success) {
              _this.$message.success(re.message)
              _this.$router.push({ path: '/paper/cardIndex' })
            } else {
              _this.$message.error(re.message)
              this.formLoading = false
            }
          }).catch(e => {
            this.formLoading = false
          })
        } else {
          return false
        }
      })
    },
    resetForm() {
      let lastId = this.form.id
      this.$refs['form'].resetFields()
      this.form = {
        id: null,
        title: '',
        titleType: 'CARD',
        questionType: 'CARD',
        items: [],
        customInfo: {
          x:undefined,
          y:undefined,
          disturb:undefined,
          answerNum:undefined
        },
        questionExplain: ''
      }
      this.form.id = lastId
    },
  },
  computed: {
    ...mapGetters('enumItem', ['enumFormat']),
    ...mapState('enumItem', {
      titleType: state => state.complexQuestion.titleType,
      questionType: state => state.complexQuestion.questionType
    })
  }
}
</script>

<style lang="less" scoped>
.question-item-label {
  display: flex;
  margin-bottom: 12px;
  float: left;
  width: 10%;
}
</style>
