<!--数字找不同题-->
<template>
  <div class="app-container">
    <el-form :model="form" ref="form" label-width="100px" v-loading="formLoading" :rules="rules">
      <el-form-item label="所属类型:" prop="type">
        <el-select v-model="form.type" placeholder="全部" disabled>
          <el-option v-for="(item, index) in trainType" :key="index" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="题型:" prop="questionType">
        <el-select v-model="form.questionType" placeholder="全部" disabled>
          <el-option v-for="(item, index) in trainQuestionType" :key="index" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="年级：" prop="grade" v-if="form.courseType == 1">
        <el-select v-model="form.grade" placeholder="" clearable>
          <el-option v-for="item in gradeList" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="选择难度：" prop="difficulty">
        <el-select v-model="form.difficulty" placeholder="全部" clearable @change="handleChange">
          <el-option v-for="(item, index) in difficultyList" :key="index" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="选择维度：" prop="categoryId">
        <el-select v-model="form.categoryId" placeholder="全部" clearable>
          <el-option v-for="(item, index) in categoryList" :key="index" :label="item.name" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="选择类别：" prop="categoryType">
        <el-select v-model="form.categoryType" placeholder="全部" clearable>
          <el-option v-for="(item, index) in typeList" :key="index" :label="item.label" :value="item.id"></el-option>
        </el-select>
      </el-form-item> -->
      <el-form-item label="题干：" prop="title">
        <el-input placeholder="请输入" v-model="form.title" />
      </el-form-item>
      <el-form-item label="数字串" prop="customInfo">
        <div class="numeric-string1">
          <el-input-number
            v-for="(item, index) in numbericStringList1"
            :key="index"
            v-model="numbericStringList1[index]"
            :controls="false"
            :precision="0"
            :min="0"
            :max="9"
            @input.native="(value) => handleStringChange(value, 1, index)"
          ></el-input-number>
          <div class="action-btns">
            <i class="el-icon-circle-plus" @click="addNumber(undefined)"></i>
            <i class="el-icon-remove" @click="removeNumber(undefined)"></i>
          </div>
        </div>
        <div class="numeric-string2">
          <el-input-number
            v-for="(item, index) in numbericStringList2"
            :key="index"
            v-model="numbericStringList2[index]"
            :controls="false"
            :precision="0"
            :min="0"
            :max="9"
            @input.native="(value) => handleStringChange(value, 2, index)"
          ></el-input-number>
          <div class="action-btns">
            <i class="el-icon-circle-plus" @click="addNumber(undefined)"></i>
            <i class="el-icon-remove" @click="removeNumber(undefined)"></i>
          </div>
        </div>
      </el-form-item>
      <el-form-item label="答案：" required>
        <el-tooltip content="答案" placement="top">
          <el-link type="primary" :underline="false" style="font-size: 12px; vertical-align: baseline">
            <span>输入数字串提交后自动生成，下标从1开始</span>
          </el-link>
        </el-tooltip>
        <el-form-item :key="index" v-for="(item, index) in form.answer">
          <div class="question-item-label">
            <el-input v-model="item.label" placeholder="输入数字串自动生成" readonly style="width: 180px; margin-right: 5px" />
          </div>
        </el-form-item>
      </el-form-item>
      <el-form-item label="题数：" required>
        <el-input-number v-model="form.score" :precision="0" :step="1" :min="0" :max="100" @input.native="(value) => handleInput(value)"></el-input-number>
      </el-form-item>

      <div v-for="(question, qIndex) in form.childQuestions" :key="qIndex" :class="['question-item', { 'question-item-gray': qIndex % 2 === 0 }]">
        <el-row type="flex" justify="flex-start" v-if="showChildQuestions">
          <el-col :xs="24" :lg="16">
            <el-row>
              <el-col :span="20">
                <el-form-item label="题干：" :prop="'childQuestions.' + qIndex + '.title'" :rules="rules.title">
                  <el-input placeholder="请输入题干" v-model="question.title" />
                </el-form-item>
                <el-form-item
                  label="数字串"
                  :prop="'childQuestions.' + qIndex + '.customInfo'"
                  :rules="[{ required: true, validator: createValidateString(qIndex), trigger: 'change' }]"
                >
                  <div class="numeric-string1">
                    <el-input-number
                      v-for="(item, index) in question.numbericStringList1"
                      :key="index"
                      v-model="question.numbericStringList1[index]"
                      :controls="false"
                      :precision="0"
                      :min="0"
                      :max="9"
                      @input.native="(value) => handleStringChild(qIndex, value, 1, index)"
                    ></el-input-number>
                    <div class="action-btns">
                      <i class="el-icon-circle-plus" @click="addNumber(qIndex)"></i>
                      <i class="el-icon-remove" @click="removeNumber(qIndex)"></i>
                    </div>
                  </div>
                  <div class="numeric-string2">
                    <el-input-number
                      v-for="(item, index) in question.numbericStringList2"
                      :key="index"
                      v-model="question.numbericStringList2[index]"
                      :controls="false"
                      :precision="0"
                      :min="0"
                      :max="9"
                      @input.native="(value) => handleStringChild(qIndex, value, 2, index)"
                    ></el-input-number>
                    <div class="action-btns">
                      <i class="el-icon-circle-plus" @click="addNumber(qIndex)"></i>
                      <i class="el-icon-remove" @click="removeNumber(qIndex)"></i>
                    </div>
                  </div>
                </el-form-item>
                <el-form-item label="答案：" required>
                  <el-tooltip content="答案" placement="top">
                    <el-link type="primary" :underline="false" style="font-size: 12px; vertical-align: baseline">
                      <span>输入数字串提交后自动生成，下标从1开始</span>
                    </el-link>
                  </el-tooltip>
                  <el-form-item :key="index" v-for="(item, index) in question.answer">
                    <div class="question-item-label">
                      <el-input v-model="item.label" placeholder="输入数字串自动生成" readonly style="width: 180px; margin-right: 5px" />
                    </div>
                  </el-form-item>
                </el-form-item>
                <el-form-item label="题数：" required>
                  <el-input-number
                    v-model="question.score"
                    :precision="0"
                    :step="1"
                    :min="0"
                    :max="100"
                    @input.native="(value) => handleInputChild(value, qIndex)"
                  ></el-input-number>
                </el-form-item>
              </el-col>
              <el-col :span="2" :offset="1">
                <el-button type="danger" icon="el-icon-close" @click="removeQuestion(qIndex)">删除</el-button>
              </el-col>
            </el-row>
          </el-col>
        </el-row>
      </div>
      <el-form-item>
        <el-button type="primary" @click="submitForm">提交</el-button>
        <el-button v-show="form.id === null" @click="resetForm">重置</el-button>
        <el-button type="success" v-if="!editId" @click="addQuestion" :disabled="!canAddQuestion || childQuestionsExceedLimit">添加小题</el-button>
      </el-form-item>
      <el-alert v-if="childQuestionsExceedLimit" title="题目数量不能超过10题" type="warning" show-icon></el-alert>
    </el-form>

    <el-dialog :visible.sync="richEditor.dialogVisible" append-to-body :close-on-click-modal="false" style="width: 100%; height: 100%" :show-close="false" center>
      <Ueditor @ready="editorReady" />
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="editorConfirm">确 定</el-button>
        <el-button @click="richEditor.dialogVisible = false">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
  import questionApi from '@/api/paper/train/question';
  import difficultyApi from '@/api/paper/train/difficulty';
  import categoryApi from '@/api/paper/train/category';
  import { mapGetters, mapState } from 'vuex';
  import MyUpload from '@/components/Upload/MyUpload';
  import Ueditor from '@/components/Ueditor';
  import { valid } from 'mockjs';

  export default {
    components: {
      MyUpload,
      Ueditor
    },
    data() {
      var validateString = (rule, value, callback) => {
        setTimeout(() => {
          // 检查主题目
          if (!value[0].label || !value[0].value) {
            return callback(new Error('请将数字串填写完整'));
          }
          const string1 = this.numbericStringList1.filter((el) => typeof el !== 'undefined').join('');
          const string2 = this.numbericStringList2.filter((el) => typeof el !== 'undefined').join('');
          if (string1.length !== string2.length || string1.length !== this.numbericStringList1.length || string2.length !== this.numbericStringList2.length) {
            return callback(new Error('请将数字串填写完整并保持长度一致'));
          }

          // 检查子题目
          if (!this.editId) {
            for (let i = 0; i < this.form.childQuestions.length; i++) {
              const childQuestion = this.form.childQuestions[i];
              const childString1 = childQuestion.numbericStringList1.filter((el) => typeof el !== 'undefined').join('');
              const childString2 = childQuestion.numbericStringList2.filter((el) => typeof el !== 'undefined').join('');
              if (!childQuestion.customInfo[0].label || !childQuestion.customInfo[0].value) {
                return callback(new Error(`子题目 ${i + 1}：请将数字串填写完整`));
              }
              if (
                childString1.length !== childString2.length ||
                childString1.length !== childQuestion.numbericStringList1.length ||
                childString2.length !== childQuestion.numbericStringList2.length
              ) {
                return callback(new Error(`子题目 ${i + 1}：请将数字串填写完整并保持长度一致`));
              }
            }
          }

          callback();
        }, 500);
      };
      return {
        editId: '',
        richEditor: {
          dialogVisible: false,
          object: null,
          parameterName: '',
          instance: null
        },
        difficultyInfo: null,
        categoryList: [],
        fileList: [],
        typeList:[
          {label:'正式题',id:1},
          {label:'附加题',id:2}
        ],
        importFrom: {
          file: null
        },
        randomLength: undefined,
        form: {
          id: null,
          categoryId: '',
          categoryType:'',
          difficulty: '',
          difficultyId: '',
          type: 'VISUAL_',
          questionType: 'VISUAL_DIFFERENT',
          childQuestions: [],
          title: '',
          customInfo: [{ label: '', value: '' }],
          answer: [],
          score: 0,
          isRandom: false,
          score: undefined,
          courseType: 0,
          grade: '',
          numbericStringList1: [],
          numbericStringList2: []
        },
        numbericStringList1: [undefined],
        numbericStringList2: [undefined],
        showChildQuestions: false,
        formLoading: false,
        rules: {
          type: [{ required: true, message: '请选择', trigger: 'blur' }],
          questionType: [{ required: true, message: '请选择', trigger: 'blur' }],
          title: [{ required: true, message: '请输入', trigger: 'change' }],
          difficulty: [{ required: true, message: '请选择', trigger: 'blur' }],
          categoryId: [{ required: true, message: '请选择', trigger: 'blur' }],
          categoryType: [{ required: true, message: '请选择', trigger: 'blur' }],
          customInfo: [{ required: true, validator: validateString, trigger: 'change' }]
        },
        currentAnswerItem: null,
        gradeList: [
          { label: '1-3年级', value: 1 },
          { label: '4-6年级', value: 2 },
          { label: '初高中', value: 3 }
        ]
      };
    },
    created() {
      this.getCategoryList();
      this.editId = this.$route.query.id;
      this.form.courseType = this.$route.query.courseType ? 1 : 0;
      if (this.editId && parseInt(this.editId) !== 0) {
        this.formLoading = true;
        questionApi.detail(this.editId).then((re) => {
          this.form = re.data;
          this.form.grade = this.form.grade == 0 ? '' : this.form.grade;
          const stringObj = re.data.customInfo[0];
          this.numbericStringList1 = stringObj.label.split('');
          this.numbericStringList2 = stringObj.value.split('');
          this.handleChange();
          this.formLoading = false;

          let answerLabel = this.form.answer[0].label;
          let answerLabelArr = answerLabel.split(',');
          for (let i = 0; i < answerLabelArr.length; i++) {
            answerLabelArr[i] = Number(answerLabelArr[i]) + 1;
          }
          this.form.answer[0].label = answerLabelArr.join(',');
        });
      }
    },
    computed: {
      ...mapGetters('enumItem', ['enumFormat']),
      ...mapState('enumItem', {
        trainType: (state) => state.train.trainType,
        difficultyList: (state) => state.train.difficultyList,
        trainQuestionType: (state) => state.train.trainQuestionType
      }),
      canAddQuestion() {
        if (this.editId) return false;
        this.form.numbericStringList1 = this.numbericStringList1;
        this.form.numbericStringList2 = this.numbericStringList2;
        const lastQuestion = this.form.childQuestions.length > 0 ? this.form.childQuestions[this.form.childQuestions.length - 1] : this.form;
        if (!lastQuestion) return false;
        const { title, customInfo, score, numbericStringList1, numbericStringList2 } = lastQuestion;
        return (
          title &&
          customInfo[0].label.length === numbericStringList1.length &&
          customInfo[0].value.length === numbericStringList2.length &&
          customInfo[0].label.length === customInfo[0].value.length &&
          customInfo[0].label.length !== 0 &&
          customInfo[0].value.length !== 0 &&
          score
        );
      },
      childQuestionsExceedLimit() {
        if (this.editId) return false;
        return this.form.childQuestions.length >= 9;
      }
    },
    methods: {
      createValidateString(index) {
        return (rule, value, callback) => {
          setTimeout(() => {
            // 检查子题目
            if (!this.editId) {
              const childQuestion = this.form.childQuestions[index];
              const childString1 = childQuestion.numbericStringList1.filter((el) => typeof el !== 'undefined').join('');
              const childString2 = childQuestion.numbericStringList2.filter((el) => typeof el !== 'undefined').join('');
              if (!childQuestion.customInfo[0].label || !childQuestion.customInfo[0].value) {
                return callback(new Error(`子题目 ${index + 1}：请将数字串填写完整`));
              }
              if (
                childString1.length !== childString2.length ||
                childString1.length !== childQuestion.numbericStringList1.length ||
                childString2.length !== childQuestion.numbericStringList2.length
              ) {
                return callback(new Error(`子题目 ${index + 1}：请将数字串填写完整并保持长度一致`));
              }
            }

            callback();
          }, 500);
        };
      },
      handleInput(value) {
        let inputValue = value.data?.toString().replace(/^(0+)|[^\d]+/g, '');
        this.form.score = inputValue;
        if (value.data === undefined || value.data === null || value.data === '') {
          return this.$message.error('请输入数字');
        }
      },
      handleInputChild(value, qIndex) {
        let inputValue = value.data?.toString().replace(/^(0+)|[^\d]+/g, '');
        this.form.childQuestions[qIndex].score = inputValue;
        if (value.data === undefined || value.data === null || value.data === '') {
          return this.$message.error('请输入数字');
        }
      },
      editorReady(instance) {
        this.richEditor.instance = instance;
        let currentContent = this.richEditor.object[this.richEditor.parameterName];
        this.richEditor.instance.setContent(currentContent);
        // 光标定位到Ueditor
        this.richEditor.instance.focus(true);
      },
      inputClick(object, parameterName) {
        this.richEditor.object = object;
        this.richEditor.parameterName = parameterName;
        this.richEditor.dialogVisible = true;
      },
      editorConfirm() {
        let content = this.richEditor.instance.getContent();
        this.richEditor.object[this.richEditor.parameterName] = content;
        this.richEditor.dialogVisible = false;
      },
      // 数字串输入变化
      handleStringChange(value, index, itemIndex) {
        let inputValue = value.data?.toString().replace(/[^\d]+/g, ''); // 只移除非数字字符
        if (inputValue === '') {
          this.$message.error('请输入有效的数字');
          return;
        }
        if (index === 1) {
          this.numbericStringList1[itemIndex] = inputValue;
          const string1 = this.numbericStringList1.filter((el) => typeof el != 'undefined').join('');
          this.form.customInfo[0].label = string1;
        } else if (index === 2) {
          this.numbericStringList2[itemIndex] = inputValue;
          const string2 = this.numbericStringList2.filter((el) => typeof el != 'undefined').join('');
          this.form.customInfo[0].value = string2;
        }
      },
      handleStringChild(qIndex, value, index, itemIndex) {
        let inputValue = value.data?.toString().replace(/[^\d]+/g, ''); // 只移除非数字字符
        if (inputValue === '') {
          this.$message.error('请输入有效的数字');
          return;
        }
        const formStrData = this.form.childQuestions[qIndex];
        if (index === 1) {
          formStrData.numbericStringList1[itemIndex] = inputValue;
          const string1 = formStrData.numbericStringList1.filter((el) => typeof el != 'undefined').join('');
          formStrData.customInfo[0].label = string1;
        } else if (index === 2) {
          formStrData.numbericStringList2[itemIndex] = inputValue;
          const string2 = formStrData.numbericStringList2.filter((el) => typeof el != 'undefined').join('');
          formStrData.customInfo[0].value = string2;
        }
      },
      itemRemove(qIndex, index) {
        this.form.childQuestions[qIndex].answer.splice(index, 1);
      },
      addQuestion() {
        if (this.childQuestionsExceedLimit) return;
        this.showChildQuestions = true;

        if (this.form.childQuestions.length > 0) {
          const lastQuestion = this.form.childQuestions[this.form.childQuestions.length - 1];
          if (
            !lastQuestion.title ||
            !lastQuestion.customInfo[0].label ||
            !lastQuestion.numbericStringList1.length ||
            !lastQuestion.numbericStringList2.length ||
            !lastQuestion.score
          ) {
            this.$message.error('请填写完整当前题目的内容！');
            return;
          }
        } else {
          if (!this.form.title || !this.form.customInfo[0].label || !this.numbericStringList1.length || !this.numbericStringList2.length || !this.form.score) {
            this.$message.error('请填写完整当前题目的内容！');
            return;
          }
        }

        this.form.childQuestions.push({
          title: '',
          customInfo: [{ label: '', value: '' }],
          numbericStringList1: [undefined],
          numbericStringList2: [undefined],
          answer: [],
          score: undefined
        });
        this.form.childQuestions[0].numbericStringList1 = stringObj.label.split('');
        this.form.childQuestions[0].numbericStringList2 = stringObj.value.split('');
      },
      removeQuestion(index) {
        this.$confirm('请确定是否需要删除已添加的题目，删除后不可恢复', '删除题目', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.form.childQuestions.splice(index, 1);
        });
      },
      getCategoryList() {
        categoryApi.list().then((res) => {
          this.categoryList = res.data.data;
        });
      },
      handleChange() {
        if (!this.form.difficulty) {
          this.difficultyInfo = null;
          return;
        }
        let query = {};
        query.type = this.form.type;
        query.questionType = this.form.questionType;
        query.difficulty = this.form.difficulty;
        difficultyApi
          .isSetting(query)
          .then((res) => {
            this.difficultyInfo = res.data;
          })
          .catch((e) => {
            this.difficultyInfo = null;
            this.fileList = [];
          });
      },
      submitForm() {
        if (this.difficultyInfo === null) {
          this.$message.error('请选择难度！');
          return false;
        }
        if (!this.editId && this.form.childQuestions.length > 0) {
          let cus = this.form.childQuestions[0].customInfo[0];
          if (cus.label == cus.value) {
            this.$message.error('请勿输入完全相同的数字串！');
            return false;
          }
        } else {
          if (this.form.customInfo[0].label == this.form.customInfo[0].value) {
            this.$message.error('请勿输入完全相同的数字串！');
            return false;
          }
        }
        if (this.form.score == undefined || this.form.score == null || this.form.score == '') {
          this.$message.error('请输入题数');
          return false;
        }
        if (!this.editId) {
          // 校验 childQuestions 中的 题数
          for (let i = 0; i < this.form.childQuestions.length; i++) {
            const childQuestion = this.form.childQuestions[i];
            if (childQuestion.score == undefined || childQuestion.score == null || childQuestion.score == '') {
              this.$message.error(`子题目 ${i + 1}：请输入题数`);
              return false;
            }
          }
        }
        this.$refs.form.validate((valid) => {
          if (valid) {
            this.formLoading = true;
            questionApi
              .saveOrUpdate(this.form)
              .then((re) => {
                if (re.success) {
                  this.$message.success(re.message);
                  this.formLoading = false;
                  this.$router.push('/train/trainAfterCourse');
                } else {
                  this.$message.error(re.message);
                  this.formLoading = false;
                }
              })
              .catch((e) => {
                this.formLoading = false;
              });
          } else {
            return false;
          }
        });
      },
      resetForm() {
        this.showChildQuestions = false;
        let lastId = this.form.id;
        let lastCourseType = this.form.courseType;
        this.$refs['form'].resetFields();
        this.form = {
          id: null,
          title: '',
          categoryId: '',
          difficulty: '',
          difficultyId: '',
          type: 'VISUAL_',
          questionType: 'VISUAL_DIFFERENT',
          customInfo: [{ label: '', value: '' }],
          answer: [],
          isRandom: false,
          score: undefined,
          childQuestions: [],
          grade: ''
        };
        this.numbericStringList1 = [undefined];
        this.numbericStringList2 = [undefined];
        this.form.id = lastId;
        this.form.courseType = lastCourseType;
      },
      // 数字串添加、删除
      addNumber(qIndex) {
        // 获取目标数组
        let targetList1, targetList2;

        if (qIndex !== undefined) {
          const lenData = this.form.childQuestions[qIndex];
          if (!lenData || !Array.isArray(lenData.numbericStringList1) || !Array.isArray(lenData.numbericStringList2)) {
            return;
          }
          targetList1 = lenData.numbericStringList1;
          targetList2 = lenData.numbericStringList2;
        } else {
          targetList1 = this.numbericStringList1;
          targetList2 = this.numbericStringList2;
        }
        // 检查当前长度是否超过限制
        if (targetList1.length >= 9) {
          this.$message.error('数字串最多展示9个数字！');
          return false;
        }
        // 添加新的数字串
        targetList1.push(undefined);
        targetList2.push(undefined);
        return true;
      },
      removeNumber(qIndex) {
        // 获取目标数组
        let targetList1, targetList2, customInfo;

        if (qIndex !== undefined) {
          const lenData = this.form.childQuestions[qIndex];
          if (!lenData || !Array.isArray(lenData.numbericStringList1) || !Array.isArray(lenData.numbericStringList2)) {
            return false;
          }
          targetList1 = lenData.numbericStringList1;
          targetList2 = lenData.numbericStringList2;
          customInfo = lenData.customInfo[0];
        } else {
          targetList1 = this.numbericStringList1;
          targetList2 = this.numbericStringList2;
          customInfo = this.form.customInfo[0];
        }

        // 检查当前长度是否超过最小限制
        if (targetList1.length <= 1) {
          this.$message.error('数字串至少展示一个数字！');
          return false;
        }

        // 删除最后一个数字串
        targetList1.pop();
        targetList2.pop();
        customInfo.label = customInfo.label.slice(0, -1);
        customInfo.value = customInfo.value.slice(0, -1);
        return true;
      }
    }
  };
</script>

<style lang="less" scoped>
  .question-item-label {
    display: flex;
    margin-bottom: 12px;
  }

  .drawing-item {
    overflow: hidden;
    white-space: normal;
    word-break: break-all;
  }
  .question-item {
    padding: 10px;
    margin-bottom: 10px;
    border-radius: 4px;
  }

  .question-item-gray {
    background-color: #f9f9f9;
  }
  .numeric-string1,
  .numeric-string2 {
    display: flex;
    ::v-deep .el-input-number,
    ::v-deep input.el-input__inner {
      width: 36px;
    }
    ::v-deep .el-input-number.is-without-controls .el-input__inner {
      padding-left: 5px;
      padding-right: 5px;
    }
  }
  .numeric-string1 {
    margin-bottom: 16px;
  }
  .action-btns {
    display: flex;
    flex-direction: column;
    // align-items: center;
    justify-content: space-between;
    margin-left: 5px;
  }
</style>
