<template>
  <div class="app-container">
    <el-form :inline="true" class="container-card" label-width="106px" label-position="left">
      <el-row>
        <el-col :span="8" :xs="24">
          <el-form-item label="托管中心编号：">
            <el-input id="merchantCode" v-model="dataQuery.merchantCode" name="id" clearable placeholder="请输入托管中心编号：" />
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="托管中心名称：">
            <el-input id="merchantName" v-model="dataQuery.merchantName" name="id" clearable placeholder="请输入托管中心名称" />
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="负责人：">
            <el-input id="realName" v-model="dataQuery.realName" name="id" clearable placeholder="请输入负责人" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="添加时间：">
        <el-date-picker v-model="RegTime" type="daterange" align="right" unlink-panels range-separator="至"
          start-placeholder="开始日期" end-placeholder="结束日期" value-format="yyyy-MM-dd HH:mm:ss" style="width: 100%;">
        </el-date-picker>
      </el-form-item>

      <el-row>
        <el-col :span="8" :xs="24">
          <el-form-item label="状态：">
            <el-select v-model="dataQuery.isEnable" filterable value-key="value" placeholder="请选择">
              <el-option v-for="(item, index) in [
                { value: '1', label: '开通' },
                { value: '0', label: '暂停' },
                { value: '-1', label: '系统关闭' },
              ]" :key="index" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="所在地区：">
            <el-input id="address" v-model="dataQuery.address" name="id" clearable placeholder="请输入推荐人所在地区：" />
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24" style="text-align: right">
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="fetchData()">搜索</el-button>
          </el-form-item>
        </el-col>
      </el-row>
      <!--   <el-form-item label="上级编号：">
           <el-input id="refereeCode" v-model="dataQuery.refereeCode" name="id"  clearable />
         </el-form-item>-->


    </el-form>

    <el-col :span="24" style="margin-bottom: 30px">
      <el-button type="success" icon="el-icon-plus" @click="clickAdd" size="mini">添加</el-button>
      <el-button type="warning" icon="el-icon-document-copy" v-loading="exportLoading" @click="exportExecl" size="mini">
        导出
      </el-button>
    </el-col>
    <el-table class="common-table" v-loading="tableLoading" :data="tableData" style="width: 100%; margin-bottom: 20px"
      row-key="id" border default-expand-all>
      <el-table-column prop="merchantCode" label="托管中心编号" align="center" width="180px"></el-table-column>
      <el-table-column prop="name" label="登录账号" align="center" width="180px"></el-table-column>
      <el-table-column label="操作" align="center" width="300">
        <template slot-scope="scope">
          <el-button type="success" icon="el-icon-edit-outline" size="mini" @click="updateDealerList(scope.row.id)">编辑
          </el-button>
          <el-button type="success" icon="el-icon-edit-outline" size="mini"
            @click="openRecharge(scope.row.merchantCode)">充值
          </el-button>
          <el-button type="warning" size="mini" icon="el-icon-switch-button"
            v-if="(scope.row.isEnable === 0 && scope.row.isCheck === 1) || (scope.row.isEnable === 0 && scope.row.flowEndStatus === 1)"
            @click="dealerStatus(scope.row.id, 1)">开通
          </el-button>
          <el-button type="danger" size="mini" icon="el-icon-video-pause"
            v-if="(scope.row.isEnable === 1 && scope.row.isCheck === 1) || (scope.row.isEnable === 1 && scope.row.flowEndStatus === 1)"
            @click="dealerStatus(scope.row.id, 0)">暂停
          </el-button>
          <el-button type="danger" size="mini" icon="el-icon-circle-check"
            v-if="checkPermission(['b:risk:user:clear']) && (scope.row.isEnable === -1 || scope.row.isEnable === -2)"
            @click="dealerStatus(scope.row.id, 1)">解封
          </el-button>
        </template>
      </el-table-column>
      <el-table-column prop="merchantName" label="托管中心名称" align="center" width="180px"></el-table-column>
      <el-table-column prop="realName" label="负责人" align="center" :show-overflow-tooltip="true"></el-table-column>
      <el-table-column prop="rank" label="级别" align="center" :show-overflow-tooltip="true"></el-table-column>
      <el-table-column prop="sumChargeMoney" label="累计充值金额（元）" align="center" width="150px">
        <template slot-scope="scope">
          <span v-if="scope.row.sumChargeMoney === null ||
            scope.row.sumChargeMoney === ''
            ">0.00</span>
          <span v-else>{{ scope.row.sumChargeMoney }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="sumRebateMoney" label="累计返点金额（元）" align="center" width="150px">
        <template slot-scope="scope">
          <span v-if="scope.row.sumRebateMoney === null ||
            scope.row.sumRebateMoney === ''
            ">0.00</span>
          <span v-else>{{ scope.row.sumRebateMoney }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="accountMoney" label="账户余额（元）" align="center" width="150px">
        <template slot-scope="scope">
          <span v-if="scope.row.accountMoney === null ||
            scope.row.accountMoney === ''
            ">0.00</span>
          <span v-else>{{ scope.row.accountMoney }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="withdrawMoneyOl" label="现金余额（元）" align="center" width="150px">
        <template slot-scope="scope">
          <span v-if="scope.row.withdrawMoneyOl === null ||
            scope.row.withdrawMoneyOl === ''
            ">0.00</span>
          <span v-else>{{ scope.row.withdrawMoneyOl }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="address" label="所在地区" align="center" width="180px"></el-table-column>
      <el-table-column prop="flowIsEnd" label="审核状态">
        <template slot-scope="scope">
          <span class="green"
            v-if="scope.row.isEnable === 1 && scope.row.isCheck === 0 && !scope.row.flowStatus">上级服务商通过审核</span>
          <span class="green" v-else-if="scope.row.isOldData === 1 && scope.row.isCheck === 1">通过管理员审核</span>
          <span class="red" v-else-if="scope.row.isOldData === 1 && scope.row.isCheck === 0">等待总部审核</span>
          <span class="blue" v-else-if="scope.row.isOldData === 1 && scope.row.isCheck === 2">未通过分公司审核</span>
          <span class="blue" v-else-if="scope.row.isOldData === 1 && scope.row.isCheck === 3">等待分公司审核</span>
          <span class="blue" v-else-if="scope.row.isOldData === 1 && scope.row.isCheck === -3">未通过市级服务商审核</span>
          <span class="red" v-else-if="scope.row.isOldData === 1 && scope.row.isCheck === 4">等待市级服务商审核</span>
          <span class="blue" v-else-if="scope.row.isOldData === 1 && scope.row.isCheck === -4">未通过总部审核</span>
          <span class="green" v-else-if="scope.row.isOldData === 0 && scope.row.flowIsEnd === 1">审核流程结束</span>
          <span class="red" v-else-if="scope.row.isOldData === 0 && scope.row.flowIsEnd === 0">审核中</span>
          <el-button
            v-else-if="(scope.row.isCheck ==2||scope.row.isCheck ==-3||scope.row.isCheck ==-4) && scope.row.chenckReason !=''"
            type="text" @click="open(scope.row.checkReason)" showCancelButton="false">查看详情
          </el-button>
        </template>
      </el-table-column>
      <el-table-column prop="flowStatus" label="流程状态" align="center" width="180px">
      </el-table-column>
      <el-table-column prop="isEnable" label="账户状态" align="center">
        <template slot-scope="scope">
          <span class="red" v-if="scope.row.isEnable === -1">系统关闭</span>
          <span class="green" v-else-if="scope.row.isEnable === 1">开通</span>
          <span class="red" v-if="scope.row.isEnable === 0">暂停</span>
        </template>
      </el-table-column>
      <el-table-column prop="regTime" label="添加时间" :show-overflow-tooltip="true" align="center"
        width="180px"></el-table-column>
    </el-table>

    <el-dialog class="recharge-dialog" title="充值" :visible.sync="showDealerRecharge" width="70%"
      :close-on-click-modal="false">
      <el-form v-model="dealerRecharge" label-position="left" label-width="120px" style="width: 100%;">
        <el-form-item label="账户余额(元)：" prop="balance">
          <el-input v-model="dealerRecharge.balance" readonly='readonly' disabled />
        </el-form-item>
        <el-form-item label="充值账户：" prop="merchantCode">
          <el-input v-model="dealerRecharge.merchantCode" readonly="readonly" disabled />
        </el-form-item>
        <el-form-item label="充值金额(元) ：" prop="rechargeMoney">
          <el-input v-model="dealerRecharge.rechargeMoney" type="number" maxlength="20" isNumber2="true" min="1"
            @blur="rechargeMoneyChange()" />
        </el-form-item>
        <el-form-item label="返点金额(元) ：" prop="rechargeBackMoney">
          <el-input v-model="dealerRecharge.rechargeBackMoney" readonly="readonly" disabled />
        </el-form-item>
        <el-form-item label="到账金额(元) ：" prop="rechargeSumMoney">
          <el-input v-model="dealerRecharge.rechargeSumMoney" readonly="readonly" disabled />
        </el-form-item>
        <el-form-item label="充值说明 ：" prop="rechargeRemark">
          <el-input v-model="dealerRecharge.rechargeRemark" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="openDialogVisible()">确定</el-button>
      </div>
    </el-dialog>


    <el-dialog class="remark-dialog" title="交易密码确认" :visible.sync="dialogVisible" width="20%"
      :close-on-click-modal="false" @close="close">
      <el-input v-model="secondPassWord" type="password" id="password" show-password></el-input>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="submitRecharge(secondPassWord)">确认
        </el-button>
      </div>
    </el-dialog>

    <!-- 分页 -->
    <el-col :span="20" style="overflow-x: auto;" :xs="24">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>
  </div>
</template>

<script>
import checkPermission from '@/utils/permission'
import dealerListApi from "@/api/areasDealerList";
import { pageParamNames } from "@/utils/constants";
import merchantAccountFlowApi from '@/api/merchantAccountFlow'
import ls from '@/api/sessionStorage'

export default {
  data() {
    return {
      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null,
      },
      RegTime: '',
      tableData: [],
      dataQuery: {
        merchantCode: "",
        name: "",
        merchantName: '',
        realName: '',
        address: '',
      },
      dialogVisible: false,
      exportLoading: false,
      updateMarketDate: {},
      secondPassWord: '',
      addMarketDate: {},
      showLoginAccount: false,
      ruls: [],
      showDealerRecharge: false,
      disabled: true,
      dealerRecharge: {
        rechargeSumMoney: '',
        rechargeBackMoney: '',

      },
      isCheckStatu: {}, //审核状态
      rules: {
        rank: [{
          required: true,
          message: '必填',
          trigger: 'blur'
        }],
        isCheck: [{
          required: true,
          message: '必填',
          trigger: 'blur'
        }],
      },
    };
  },
  created() {
    this.fetchData();
  },
  methods: {
    checkPermission,
    openDialogVisible() {
      if (!this.dealerRecharge.merchantCode) {
        this.$message.error('充值账户不能为空')
        return false;
      }
      if (!this.dealerRecharge.balance) {
        this.$message.error('账户余额不能为空')
        return false;
      }
      if (this.dealerRecharge.balance <= 0) {
        this.$message.error('账户余额为0')
        return false;
      }
      if (!this.dealerRecharge.rechargeMoney) {
        this.$message.error('充值金额不能为空')
        return false;
      }
      if (this.dealerRecharge.rechargeMoney <= 0) {
        this.$message.error('充值金额不能0')
        return false;
      }
      if (!this.dealerRecharge.rechargeSumMoney) {
        this.$message.error('到账金额不能为空')
        return false;
      }
      if (this.dealerRecharge.rechargeSumMoney <= 0) {
        this.$message.error('到账金额不能为0')
        return false;
      }
      dealerListApi.getDeatil(this.dealerRecharge.merchantCode).then((res) => {
        if (res.data.data.paymentIsComplete !== null) {
          if (res.data.data.paymentIsComplete == 0) {
            this.$confirm('确定操作吗?', '该账户是未完款账户', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            }).then((res) => {
              this.dialogVisible = true;
            }, s => {
              if (s === 'cancel') {
                this.dialogVisible = false;
              }
            });
          } else {
            this.dialogVisible = true;
          }
        } else {
          this.dialogVisible = true;
        }
      })
      this.secondPassWord = ""
    },

    submitRecharge(secondPassWord) {
      if (!secondPassWord) {
        this.$message.error('二级密码不能为空')
        return false;
      }
      merchantAccountFlowApi.checkSecondPwd(secondPassWord).then(res => {
        if (!res.success) {
          this.$message.error('二级密码验证失败')
          return false;
        }
        this.$confirm('确定操作吗?', '提交充值', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          dealerListApi.submitRecharge(this.dealerRecharge).then(res => {
            if (!res.success) {
              this.$message.error(res.message)
              return false
            }
            this.$nextTick(() => this.fetchData())
            this.$message.success('充值成功')

          })
          this.showDealerRecharge = false
          this.dialogVisible = false;
        })
      })
    },

    rechargeMoneyChange() {
      if (this.dealerRecharge.rechargeMoney !== null && this.dealerRecharge.rechargeMoney !== undefined && this.dealerRecharge.rechargeMoney !== "") {
        dealerListApi.getRechargeBackMoney(this.dealerRecharge.merchantCode, this.dealerRecharge.rechargeMoney).then(res => {
          if (!res.success) {
            this.$message.error(res.message);
            return false;

          }
          this.dealerRecharge.rechargeSumMoney = res.data.countMoney
          this.dealerRecharge.rechargeBackMoney = res.data.backMoney
        })
      }
    },
    openRecharge(merchantCode) {
      dealerListApi.getRecharge().then(res => {
        this.dealerRecharge.balance = res.data.balance
        this.dealerRecharge.merchantCode = merchantCode
        this.showDealerRecharge = true;

        this.dealerRecharge = {
          balance: this.dealerRecharge.balance,
          merchantCode: this.dealerRecharge.merchantCode,
          rechargeMoney: 0,
          rechargeBackMoney: 0,
          rechargeSumMoney: 0,
          rechargeRemark: ""
        }
      })
    },

    // 查询+搜索课程列表
    fetchData() {
      const that = this;
      var a = that.RegTime;
      if (a != null) {
        that.dataQuery.startDate = a[0];
        that.dataQuery.endDate = a[1];
      }
      that.tableLoading = true;
      dealerListApi
        .areasDealerList(
          that.tablePage.currentPage,
          that.tablePage.size,
          that.dataQuery
        )
        .then((res) => {
          that.tableData = res.data.data;
          // console.log(res)
          that.tableLoading = false;
          // 设置后台返回的分页参数
          pageParamNames.forEach((name) =>
            that.$set(that.tablePage, name, parseInt(res.data[name]))
          );
        });
    },
    //新增操作
    clickAdd() {
      const that = this;
      ls.setItem('addOrUpdate2', true);
      that.$router.push({
        path: "/areas/areasDealerAdd",
        query: {
          addOrUpdate: true,
        },
      });
    },
    // 打开修改登陆账号
    openLogin() {
      this.showLoginAccount = true;
    },
    closeLogin() {
      this.showLoginAccount = false;
    },
    //审核
    isCheckDealer(id, openMoney) {
      this.showLoginAccount = true;
      this.isCheckStatu.openMoney = openMoney;
      this.isCheckStatu.id = id;
    },
    //审核页面
    dealerIsCheck() {
      dealerListApi.isCheckStatus(this.isCheckStatu).then((res) => {
        this.fetchData();
        this.isCheckStatu.isCheck = '';
        this.isCheckStatu.checkReason = '';
        this.showLoginAccount = false;
      });
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val;
      this.fetchData();
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val;
      this.fetchData();
    },
    //导出
    exportExecl() {
      const that = this;
      that.exportLoading = true
      dealerListApi.exportAreasDealer(that.dataQuery).then(response => {
        console.log(response)
        if (!response) {
          this.$notify.error({
            title: '操作失败',
            message: '文件下载失败'
          })
        }
        const url = window.URL.createObjectURL(response)
        const link = document.createElement('a')
        link.style.display = 'none'
        link.href = url // 获取服务器端的文件名
        link.setAttribute('download', '托管中心表.xls')
        document.body.appendChild(link)
        link.click()
        that.exportLoading = false

      })
    },
    //审核理由
    open(chenckReason) {
      const h = this.$createElement;
      this.$msgbox({
        title: '审核理由',
        message: h('p', null, [
          h('i', { style: 'color: #FF0802' }, chenckReason)
        ]),
        showCancelButton: false,
        confirmButtonText: '确定'
      })
    },
    //编辑
    updateDealerList(id) {
      const that = this;
      ls.setItem('addOrUpdate2', false);
      ls.setItem('areasDealerId', id);
      that.$router.push({
        path: "/areas/areasDealerAdd",
        query: {
          addOrUpdate: false,
          id: id,
        },
      });
    },
    pickerOptions() {
    },
    close() {
    },
    //开通和暂停
    dealerStatus(id, status) {
      const that = this;
      this.$confirm("确定操作吗?", "开通/暂停", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        dealerListApi.dealerStatus(id, status).then((res) => {
          that.$nextTick(() => that.fetchData());
          that.$message.success("开通/暂停成功!");
        })
      })
    },
  },
};
</script>

<style>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.red {
  color: red;
}

.green {
  color: green;
}

.blue {
  color: blue;
}

@media screen and (max-width: 767px) {
  .recharge-dialog .el-dialog {
    width: 90% !important;
  }

  .el-message-box {
    width: 80% !important;
  }
}
</style>
