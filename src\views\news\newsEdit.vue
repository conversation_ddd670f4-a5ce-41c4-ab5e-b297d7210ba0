<template>
    <div class="app-container">
        <div>
            <span>通知标题</span>
            <el-input v-model="title" placeholder="请输入内容" style="width: 500px;margin-left: 30px;"></el-input>
        </div>
        <div style="width: 80%; margin: 50px 0">
            <div style="margin-bottom: 15px;">通知内容</div>
            <el-input type="textarea" :autosize="{ minRows: 20, maxRows: 40 }" placeholder="请输入内容" v-model="contents" maxlength="1000">
            </el-input>
        </div>

        <div style="margin-top: 100px;">
            <span style="margin-right: 30px;">可见范围</span>
            <el-radio-group v-model="radio">
                <el-radio :label="1">全部</el-radio>
                <el-radio :label="2">超级俱乐部</el-radio>
                <el-radio :label="3">门店</el-radio>
            </el-radio-group>
        </div>


        <div style="margin-top: 40px;">
            <el-button @click="back">返回</el-button>
            <!-- <el-button type="primary">保存</el-button> -->
            <el-button type="primary" @click="submit">提交</el-button>
        </div>
    </div>
</template>

<script>

// import { quillEditor } from 'vue-quill-editor'
import newsApi from '@/api/news'
import ls from '@/api/noticeEdit'
import { de } from 'pinyin/data/dict-zi-web'


export default {
    components: {
        // quillEditor
    },
    name: 'add',
    data() {
        return {
            radio: '',
            title: "",
            contents: "",
            userRole: "",
            id: '',
            type: '', // 1新增

        }
    },
    methods: {
        submit() {
            this.editNotice()
           
        },
        

        back() {
            this.$router.back();
        },


        checkNews() {
            let that = this;
            newsApi.lookNotice(that.id).then((res) => {
                let list = res.data;
                that.title = list.title;
                that.contents = list.contents;
                console.log(res.data);
                if (list.userRole == 'All') {
                    that.radio = 1;
                } else if (list.userRole == 'Operations') {
                    that.radio = 2;
                } else {
                    that.radio = 3;
                }
            })
        },

        // 修改
        editNotice() {
            let that = this;
            if (that.radio == 1) {
                that.userRole = 'All'
            } else if (that.radio == 2) {
                that.userRole = 'Operations'
            } else {
                that.userRole = 'School'
            }
            newsApi.updateNotice(that.id, that.title, that.contents, that.userRole).then((res) => {
                that.$message({ message: '操作成功', type: 'success' });
                that.title = '';
                that.contents = '';
                that.userRole = '';
                this.$router.back();
            })
        },


    },
    mounted() {
        this.checkNews();
    }, 

    created() {
        this.id = ls.getItem('notificationEditId');
    },


}
</script>

<style></style>
