<template>
  <div class="add-video-dialog">
    <el-dialog width="50%" :visible.sync="dialogParam.visible" @closed="closeAddDialog()" :close-on-click-modal="false" @submit.native.prevent>
      <div slot="title" class="dialog-title">
        <span>查看学员信息</span>
      </div>
      <el-table class="common-table" v-loading="dataLoading" :data="tableData" stripe border :default-sort="{ prop: 'date', order: 'descending' }">
        <el-table-column type="index" width="100" label="序号"></el-table-column>
        <el-table-column prop="courseName" label="参与课程"></el-table-column>
        <!-- <el-table-column prop="courseType" label="课程类型">
          <template slot-scope="scope">
            <span>{{ scope.row.courseType | queryByType(courseType, scope.row.courseType) }}</span>
          </template>
        </el-table-column> -->
        <el-table-column prop="isMustLearn" label="是否必修">
          <template slot-scope="scope">{{ scope.row.isMustLearn == 0 ? '选修' : '必修' }}</template>
        </el-table-column>
        <el-table-column prop="examStatus" label="考试结果">
          <template slot-scope="scope">{{ scope.row.examStatus == 1 ? '通过' : '' }}</template>
        </el-table-column>
        <!-- <el-table-column prop="categoryCode" label="考试分数"></el-table-column> -->
      </el-table>
      <div style="margin-top: 10px">
        <el-pagination
          :current-page="tablePage.currentPage"
          :page-sizes="[10, 20, 30, 40, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="tablePage.totalItems"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
      <!-- 底部按钮栏 -->
      <div slot="footer" class="dialog-footer">
        <el-button @click="closeAddDialog()">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
  import courseApi from '@/api/training/course';
  export default {
    name: 'traineeDetail',
    components: {},
    props: {
      dialogParam: {
        type: Object,
        default() {
          return {};
        }
      }
    },
    filters: {
      queryByType(type, list) {
        var index = list.findIndex((item) => {
          if (item.dictValue == type) {
            return true;
          }
        });
        return list[index].dictLabel;
      }
    },
    data() {
      return {
        tableData: [],
        tablePage: {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: 0
        },
        propItem: null,
        dataLoading: false
      };
    },
    mounted() {
      this.getQueryByType();
    },
    methods: {
      getRecordPage(info) {
        this.propItem = info;
        let roleList = [];
        console.log(info.roles);
        info.sysUserRoles.forEach((element) => {
          roleList.push(element.roleId);
        });
        let param = { pageSize: this.tablePage.size, pageNum: this.tablePage.currentPage, roleList: roleList.join(',') };
        param.userId = info.id;

        this.dataLoading = true;
        courseApi
          .studentRecordPage(param)
          .then((res) => {
            this.tableData = res.data.data;
            this.tablePage.totalItems = Number(res.data.totalItems);
          })
          .finally(() => {
            this.dataLoading = false;
          });
      },
      getQueryByType() {
        courseApi.dictQueryByType({ dictType: 'paper_course_category' }).then((res) => {
          this.courseType = res.data;
        });
      },
      closeAddDialog() {
        this.$emit('closeDialog');
      },
      handleSizeChange(val) {
        this.tablePage.size = val;
        this.getRecordPage(this.propItem);
      },
      handleCurrentChange(val) {
        this.tablePage.currentPage = val;
        this.getRecordPage(this.propItem);
      }
    }
  };
</script>
<style scoped>
  .content {
    line-height: 30px;
    width: 100%;
    height: 30px;
    background-color: #d7d7d7;
    margin-bottom: 20px;
  }
  .contentTxt {
    margin-left: 10px;
  }
  ::v-deep .el-form-item {
    margin-bottom: 18px;
  }
</style>
