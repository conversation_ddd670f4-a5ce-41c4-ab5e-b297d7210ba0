import request from '@/utils/request';
import { get } from 'jquery';

export default {
  // 获取课程大类的接口
  getCurriculum(query) {
    return request({
      url: '/znyy/curriculum/math',
      method: 'get',
      params: query
    });
  },
  // 获取版本
  getVersion(query) {
    return request({
      url: '/dyf/math/web/basisConfig/selectVersionInfo',
      method: 'get',
      params: query
    });
  },
  // 获取学科学段
  getSubject(query) {
    return request({
      url: '/dyf/math/web/coursePeriodConfig/selectTree',
      method: 'get',
      params: query
    });
  },
  // 获取试卷类型
  getPaperType(query) {
    return request({
      url: '/dyf/math/web/common/enum/list',
      method: 'get',
      params: query
    });
  },

  // 保存试卷
  savePaper(query) {
    return request({
      url: '/dyf/math/web/coursePaperConfig/save',
      method: 'post',
      data: query
    });
  },
  // 获取试卷列表
  getPaperList(query) {
    return request({
      url: '/dyf/math/web/coursePaperConfig/list/page',
      method: 'get',
      params: query
    });
  },
  // 获取试卷详情
  getPaperDetail(query) {
    return request({
      url: '/dyf/math/web/coursePaperConfig/get',
      method: 'get',
      params: query
    });
  },
  // 删除试卷管理数据
  deletePaperItem(query) {
    return request({
      url: '/dyf/math/web/coursePaperConfig/delete',
      method: 'delete',
      params: query
    });
  },
  // 启用禁用试卷管理数据
  openPaperItem(query) {
    return request({
      url: '/dyf/math/web/coursePaperConfig/open',
      method: 'POST',
      params: query
    });
  }
};
