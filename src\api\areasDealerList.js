/**
 * 单词水平相关接口
 */
import request from '@/utils/request'

export default {
  areasDealerList(pageNum, pageSize, data) {
    return request({
      url: '/znyy/areas/dealer/list/' + pageNum + '/' + pageSize,
      method: 'GET',
      params: data
    })
  },
  //市级服务商下的托管中心接口
  areasAgentDealerList(pageNum, pageSize, data) {
    return request({
      url: '/znyy/areas/dealer/agent/list/' + pageNum + '/' + pageSize,
      method: 'GET',
      params: data
    })
  },
  getRecharge() {
    return request({
      url: '/znyy/areas/dealer/recharge/detail',
      method: 'GET',
    })
  },

  //根据角色获取充值返点
  getRechargeBackMoney(merchantCode, money) {
    return request({
      url: '/znyy/areas/dealer/get/recharge/back/money?merchantCode=' + merchantCode + '&money=' + money,
      method: 'GET',
    })
  },

  //提交充值
  submitRecharge(data) {
    return request({
      url: '/znyy/areas/dealer/submit/dealer/recharge',
      method: 'POST',
      data
    })
  },
  exportAreasDealer(data) {
    return request({
      url: '/znyy/areas/dealer/to/excel',
      method: 'GET',
      params: data,
      responseType: 'blob',
    })
  },
  exportDealerAreasDealer(data) {
    return request({
      url: '/znyy/areas/dealer/to/agent/excel',
      method: 'GET',
      params: data,
      responseType: 'blob',
    })
  },
  // 托管中心开通与暂停
  dealerStatus(id, status) {
    return request({
      url: '/znyy/dealer/activationAndSuspension?id=' + id + '&isEnable=' + status,
      method: 'PUT'
    })
  },
  updatePaymentIsComplete(id, paymentIsComplete) {
    return request({
      url: '/znyy/dealer/updatePaymentIsComplete?id=' + id + '&paymentIsComplete=' + paymentIsComplete,
      method: 'PUT'
    })
  },
  //市级服务商审核
  updateIsCheck(id, isCheck) {
    return request({
      url: '/znyy/areas/dealer/update/isCheck?id= ' + id + '&isCheck=' + isCheck,
      method: 'PUT'
    })
  },
  //获取账户详情
  getDeatil(merchantCode) {
    return request({
      url: '/znyy/areas/dealer/bv/detail/' + merchantCode,
      method: 'GET'
    })
  }
}
