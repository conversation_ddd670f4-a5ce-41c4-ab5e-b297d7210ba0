
import request from '@/utils/request'

export default {
  // 分页查询
  pageList(data) {
    return request({
      url: '/paper/web/grade/pageList',
      method: 'GET',
      params: data
    })
  },
//添加学科
  saveOrUpdate(data) {
    return request({
      url: '/paper/web/grade/saveOrUpdate',
      method: 'POST',
      data
    })
  },
  detail(id) {
    return request({
      url: '/paper/web/grade/detail',
      method: 'GET',
      params:{
        id:id
      }
    })
  },
}
