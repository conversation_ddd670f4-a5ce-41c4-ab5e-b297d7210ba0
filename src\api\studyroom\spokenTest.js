import request from '@/utils/request'

export default {

  getType(data) {
    return request({
      url: '/studyroom/web/spoken/getType',
      method: 'GET',
      params: data
    })
  },
  //添加type
  createType(data) {
    return request({
      url: '/studyroom/web/spoken/createType',
      method: 'POST',
      params: data
    })
  },
  typeDetail(id) {
    return request({
      url: '/studyroom/web/spoken/typeDetail',
      method: 'GET',
      params: {
        typeId: id
      }
    })
  },
  typeDelete(id){
    return request({
      url: '/studyroom/web/spoken/deleteType',
      method: 'DELETE',
      params: {
        typeId: id
      }
    })
  },
  listType(data) {
    return request({
      url: '/studyroom/web/spoken/listType',
      method: 'GET',
      params: data
    })
  },
  updateType(data) {
    return request({
      url: '/studyroom/web/spoken/updateType',
      method: 'PUT',
      params: data
    })
  },
  //口语测评
  list(data) {
    return request({
      url: '/studyroom/web/spoken/list',
      method: 'GET',
      params: data
    })
  },
  create(data) {
    return request({
      url: '/studyroom/web/spoken/create',
      method: 'POST',
      data
    })
  },
  //编辑
  edit(data) {
    return request({
      url: '/studyroom/web/spoken/update',
      method: 'PUT',
      data
    })
  },
  //详情
  detail(id) {
    return request({
      url: '/studyroom/web/spoken/detail',
      method: 'GET',
      params: {
        id: id
      }
    })
  },
  //删除
  delete(id){
    return request({
      url: '/studyroom/web/spoken/delete',
      method: 'DELETE',
      params: {
        id: id
      }
    })
  }
}
