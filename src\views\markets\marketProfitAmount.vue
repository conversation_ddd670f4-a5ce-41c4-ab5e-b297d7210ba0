<template>
  <div class="app-container">
    <el-form :inline="true" class="container-card" label-width="96px" label-position="left" clearable>
      <el-row>
        <el-col :span="8" :xs="24">
          <el-form-item label="推荐人编号：">
            <el-input id="merchantCode" v-model="dataQuery.merchantCode" name="id" placeholder="请输入推荐人编号" clearable />
          </el-form-item>
        </el-col>

        <el-col :span="8" :xs="24">
          <el-form-item label="推荐人名称：">
            <el-input id="merchantName" v-model="dataQuery.merchantName" name="id" placeholder="请输入推荐人名称" clearable />
          </el-form-item>
        </el-col>

<!--        <el-col :span="8" :xs="24">-->
<!--          <el-form-item label="所在地区：">-->
<!--            <el-input id="name" v-model="dataQuery.address" name="id" placeholder="请输入推荐人所在地区" clearable />-->
<!--          </el-form-item>-->
<!--        </el-col>-->
      </el-row>


      <el-row>
        <el-col :span="12"  :xs="24" class="pickerSelectTime">
          <el-form-item label="添加时间：">
            <el-date-picker style="width: 80%;" v-model="regTime" type="daterange" value-format="yyyy-MM-dd HH:mm:ss" align="right" unlink-panels range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" clearable>
            </el-date-picker>
          </el-form-item>
        </el-col>

        <el-col :span="8" :xs="24">
          <el-form-item label="状态：">
            <el-select v-model="dataQuery.isEnable" placeholder="请选择">
              <el-option v-for="item in [
              { value: 1, label: '开通' },
              { value: 0, label: '暂停' },
            ]"
                         :key="item.value" :label="item.label" :value="item.value" clearable>
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="4" :xs="24" style="text-align: right">
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="fetchData()">搜索</el-button>
          </el-form-item>
        </el-col>

      </el-row>


    </el-form>
    <el-col :span="24" style="margin-bottom: 30px">
      <el-button type="success" icon="el-icon-plus" @click="clickAdd" size="mini">新增推荐人</el-button>
      <el-button type="warning" icon="el-icon-document-copy" v-loading="exportLoading" @click="exportFlow" size="mini">导出</el-button>
    </el-col>

    <el-table class="common-table" v-loading="tableLoading" :data="tableData" style="width: 100%; margin-bottom: 20px"
              row-key="id" border default-expand-all :tree-props="{ list: 'children', hasChildren: 'true' }">
      <el-table-column prop="marketPartner" label="推荐人编号"></el-table-column>
      <el-table-column prop="profitAmount" label="收益" width="160"></el-table-column>
      <el-table-column label="操作" align="center" width="180">
        <template slot-scope="scope">
          <el-tooltip content="编辑" placement="top">
            <el-button type="warning" icon="el-icon-edit-outline" @click="update(scope.row.id)" size="mini">编辑</el-button>
          </el-tooltip>
          <el-tooltip :content="scope.row.isEnable === 0 ? '开通' : '暂停'" placement="top">
            <el-button type="warning" icon="el-icon-edit-outline" @click="update(scope.row.id)" size="mini">编辑</el-button>
            <el-button type="warning" size="mini" icon="el-icon-switch-button" v-if="scope.row.isEnable === 0" @click="marketStatus(scope.row.id, scope.row.isEnable)">开通</el-button>
            <el-button type="danger" size="mini" icon="el-icon-video-pause" v-else @click="marketStatus(scope.row.id, scope.row.isEnable)">暂停</el-button>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column prop="totalAmount" label="总金额" ></el-table-column>
      <el-table-column prop="paidAmount" label="已付款金额"></el-table-column>
      <el-table-column prop="waitPaidAmount" label="待付款金额"  show-overflow-tooltip></el-table-column>
      <el-table-column prop="merchantName" label="推荐商户姓名"></el-table-column>
      <el-table-column prop="direction" label="描述" show-overflow-tooltip></el-table-column>
      <el-table-column prop="remark" label="备注" show-overflow-tooltip></el-table-column>
      <el-table-column prop="addTime" label="addTime" show-overflow-tooltip></el-table-column>
      <el-table-column prop="updateTime" label="updateTime" show-overflow-tooltip></el-table-column>
    </el-table>
    <!-- 新增或编辑弹窗 -->
    <el-dialog :title="addOrUpdate ? '新增推荐收益' : '修改推荐收益'" :visible.sync="dialogVisible" width="70%" :close-on-click-modal="false"
               @close="close">
      <el-form :ref="addOrUpdate ? 'addMarketDate' : 'updateMarketDate'" :rules="rules" :model="addOrUpdate ? addMarketDate : updateMarketDate"
               label-position="left" label-width="110px" style="width: 100%">
        <el-form-item label="总金额" prop="totalAmount">
          <el-col :xs="24" :span="18">
            <el-input v-if="addOrUpdate" v-model="addMarketDate.totalAmount" />
            <el-input v-if="!addOrUpdate" v-model="updateMarketDate.totalAmount" />
          </el-col>
        </el-form-item>
        <el-form-item label="已付款金额：" prop="paidAmount">
          <el-col :xs="24" :span="18">
            <el-input v-if="addOrUpdate" v-model="addMarketDate.paidAmount" />
            <el-input v-if="!addOrUpdate" v-model="updateMarketDate.paidAmount" />
          </el-col>
        </el-form-item>
        <el-form-item label="id" prop="id" v-show="false">
          <el-col :xs="24" :span="18">
            <el-input v-if="addOrUpdate" v-model="addMarketDate.id" />
            <el-input v-if="!addOrUpdate" v-model="updateMarketDate.id" />
          </el-col>
        </el-form-item>
        <el-form-item label="待付款金额" prop="waitPaidAmount">
          <el-col :xs="24" :span="18">
            <el-input v-if="addOrUpdate" v-model="addMarketDate.waitPaidAmount" />
            <el-input v-if="!addOrUpdate" v-model="updateMarketDate.waitPaidAmount" />
          </el-col>
        </el-form-item>
        <el-form-item label="收益" prop="profitAmount">
          <el-col :xs="24" :span="18">
            <el-input v-if="addOrUpdate" v-model="addMarketDate.profitAmount" />
            <el-input v-if="!addOrUpdate" v-model="updateMarketDate.profitAmount" />
          </el-col>
        </el-form-item>
        <el-form-item label="推荐人编号" prop="marketPartner">
          <el-col :xs="24" :span="18">
            <el-input v-if="addOrUpdate" v-model="addMarketDate.marketPartner" />
            <el-input v-if="!addOrUpdate" v-model="updateMarketDate.marketPartner" />
          </el-col>
        </el-form-item>
        <el-form-item label="分润商户名称" prop="merchantName">
          <el-col :xs="24" :span="18">
            <el-input v-if="addOrUpdate" v-model="addMarketDate.merchantName" />
            <el-input v-if="!addOrUpdate" v-model="updateMarketDate.merchantName" />
          </el-col>
        </el-form-item>
        <el-form-item label="描述" prop="direction">
          <el-col :xs="24" :span="18">
            <el-input type="textarea" :rows="4" resize="none" v-if="addOrUpdate" v-model="addMarketDate.direction" />
            <el-input type="textarea" :rows="4" resize="none" v-if="!addOrUpdate" v-model="updateMarketDate.direction" />
          </el-col>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-col :xs="24" :span="18">
            <el-input type="textarea" :rows="4" resize="none" v-if="addOrUpdate" v-model="addMarketDate.remark" />
            <el-input type="textarea" :rows="4" resize="none" v-if="!addOrUpdate" v-model="updateMarketDate.remark" />
          </el-col>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button v-if="addOrUpdate" size="mini" type="primary" @click="addActiveFun('addMarketDate')">新增</el-button>
        <el-button v-if="!addOrUpdate" size="mini" type="primary" @click="updateActiveFun('updateMarketDate')">修改</el-button>
        <el-button size="mini" @click="close">关闭</el-button>
      </div>
    </el-dialog>

    <!-- 审核弹框 -->
    <!-- 分页 -->
    <el-col :span="20">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]" layout="total, sizes, prev, pager, next, jumper"
                     :total="tablePage.totalItems" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    </el-col>
  </div>
</template>

<script>
import Tinymce from "@/components/Tinymce";
import marketApi from "@/api/marketList";
import marketProfitAmountApi from "@/api/marketProfitAmout";
import memberApi from "@/api/member";
import {
  pageParamNames
} from "@/utils/constants";
import {
  isvalidPhone,
  idCard
} from "@/utils/validate";
export default {
  data() {
    //手机号验证
    var validPhone = (rule, value, callback) => {
      if (!value) {
        callback(new Error("请输入电话号码"));
      } else if (value.length<11) {
        callback(new Error("请输入正确的11位手机号码"));
      } else {
        callback();
      }
    };
    //身份证表达式
    var isIdCard = (rule, value, callback) => {
      if (!value) {
        callback(new Error("请输入身份证号码"));
      } else if (!idCard(value)) {
        callback(new Error("请输入正确的18位身份证号"));
      } else {
        callback();
      }
    };
    return {
      tableLoading: false,
      showLoginName: false, //修改账号弹框
      updateLoginName: {}, //修改账号数据
      disabled: true,
      rulesLoginName: {
        name: [{
          required: true,
          message: '必填',
          trigger: 'blur'
        },
          {
            validator: validPhone,
            trigger: "blur"
          }
        ],
      }, //修改账号表单验证
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null,
      },
      addOrUpdate: false,
      dataQuery: {
        merchantCode: '',
        merchantName: '',
        isEnable: '',
        address: '',
        startDate: '',
        endDate: ''
      },
      dialogVisible: false,
      showLoginAccount: false, //修改登陆账号
      province: [],
      regTime: [], //增加时间
      city: [],
      areaList: [],
      tableData: [],
      updateMarketDate: {},
      addMarketDate: {},
      exportLoading: false,
      provinceStr: "", //省
      cityStr: "", //市
      areaStr: "", //区
      rulersupdate: {},
      rules: {
        totalAmount: [{
          required: true,
          message: "必填",
          trigger: "change",
        }
        ],
        paidAmount: [{
          required: true,
          message: "必填",
          trigger: "change",
        }, ],
        waitPaidAmount: [{
          required: true,
          message: "必填",
          trigger: "blur",
        }, ],
        profitAmount: [{
          required: true,
          message: "必填",
          trigger: "blur",
        }
        ],
        marketPartner: [{
          required: true,
          message: "必填",
          trigger: "blur",
        }, ],
        direction: [{
          required: true,
          message: "必填",
          trigger: "blur",

        }, ],
        remark: [{
          required: true,
          message: "必填",
          trigger: "blur",
        }, ],
      },
    };
  },
  created() {
    this.fetchData();
    //获取省
    this.getProvince();
  },
  methods: {
    // 查询提现列表
    fetchData() {
      const that = this;
      var a = that.regTime;
      if (a != null) {
        that.dataQuery.startDate = a[0];
        that.dataQuery.endDate = a[1];
      }
      that.tableLoading = true;
      marketProfitAmountApi
        .marketProfitAmountList(
          that.tablePage.currentPage,
          that.tablePage.size,
          that.dataQuery
        )
        .then((res) => {
          that.tableData = res.data.data;
          that.tableLoading = false;
          // 设置后台返回的分页参数
          pageParamNames.forEach((name) =>
            that.$set(that.tablePage, name, parseInt(res.data[name]))
          );
        });
    },
    //修改登录账号
    updateDealerLoginName(ele) {
      const that = this
      that.$refs[ele].validate((valid) => {
        if (valid) {
          marketApi.markertUpdateLogin(that.updateLoginName).then(() => {
            that.showLoginAccount = false
            that.$nextTick(() => that.fetchData());
            that.updateMarketDate.name = this.updateLoginName.name;
            that.updateLoginName.name = "";
            that.showLoginName = false;
            that.$message.success('修改登录账号成功')
          })
        }
      })

    },
    // 打开修改登陆账号
    openLogin() {
      this.showLoginName = true;
      this.updateLoginName.oldName = this.updateMarketDate.name;
      this.updateLoginName.id = this.updateMarketDate.id;
    },
    closeLogin() {
      this.showLoginName = false;
    },
    closeLoginname() {
      this.showLoginName = false;
    },
    //关闭
    close() {
      this.provinceStr = '';
      this.cityStr = '';
      this.areaStr = '';
      this.dialogVisible = false;
    },
    //编辑按钮
    update(id) {
      this.addOrUpdate = false;
      this.dialogVisible = true;
      marketProfitAmountApi.queryMarketProfitAmount(id).then((res) => {
        console.log(res);
       this.updateMarketDate=res.data;
      });
    },
    //新增操作
    clickAdd() {
      this.dialogVisible = true;
      this.addOrUpdate = true;
      this.addMarketDate = {
        name: "",
        merchantName: "",
        realName: "",
        idCard: "",
        privice: "",
        city: "",
        area: "",
        address: "",
        description: "",
      };
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val;
      this.fetchData();
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val;
      this.fetchData();
    },
    //获取省
    getProvince() {
      memberApi.getProvince(0).then((res) => {
        this.province = res.data;
      });
    },
    //获取市
    getCity(val) {
      // this.$set(this.editMem, 'city', ['请选择']);
      this.cityStr = "";
      this.areaStr="";
      this.provinceStr = this.province[val].name;
      memberApi.getProvince(this.province[val].id).then((res) => {
        this.city = res.data;
      });
      this.addMarketDate.province = this.province[val].name;
      this.updateMarketDate.province = this.province[val].name;
    },
    //开通和暂停
    marketStatus(id, status) {
      if (status == 0) {
        status = 1;
      } else {
        status = 0;
      }
      const that = this;
      this.$confirm("确定操作吗?", "修改状态", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          marketApi
            .marketStatus(id, status)
            .then((res) => {
              that.$nextTick(() => that.fetchData());
              that.$message.success("修改成功!");
            })
            .catch((err) => {});
        })
        .catch((err) => {});
    },
    //获取区集合获取市
    getArea(val) {
      memberApi.getProvince(this.city[val].id).then((res) => {
        this.areaList = res.data;
      });
      console.log(this.areaList);
      this.addMarketDate.city = this.city[val].name;
      this.updateMarketDate.city = this.city[val].name;
      this.areaStr="";
    },
    getAreaList(val) {
      this.addMarketDate.area = this.areaList[val].name;
      this.updateMarketDate.area = this.areaList[val].name;
    },

    //导出
    exportFlow() {
      const that = this;
      that.exportLoading = true;
      //
      marketApi.simpleMarketExecl(that.dataQuery).then((res) => {
        const url = window.URL.createObjectURL(res);
        const link = document.createElement("a");
        link.style.display = "none";
        link.href = url; // 获取服务器端的文件名
        link.setAttribute("download", "推荐人流水.xls");
        document.body.appendChild(link);
        link.click();
        that.exportLoading = false;
      });
    },
    //编辑提交到后台
    updateActiveFun(ele) {
      const that = this;
      if(that.updateMarketDate.id===null || that.updateMarketDate.id===''){
        that.$message.info("id能为空");
        return false;
      }
      that.$refs[ele].validate((valid) => {
        // 表单验证
        if (valid) {
          const loading = this.$loading({
            lock: true,
            text: "修改推荐人",
            spinner: "el-icon-loading",
            background: "rgba(0, 0, 0, 0.7)",
          });
          marketProfitAmountApi
            .addMarketProfitAmount(that.updateMarketDate)
            .then((res) => {
              console.log(res);
              if (res) {
                loading.close();
                that.provinceStr = '';
                that.cityStr = '';
                that.areaStr = '';
                that.fetchData();
                that.dialogVisible = false;
                that.$message.success("修改成功");
              }
            })
            .catch((err) => {
              loading.close();
            });
        } else {
          console.log("error submit!!");
          loading.close();
          return false;
        }
      });
    },
    //新增推荐人
    addActiveFun(ele) {
      const that = this;

      that.$refs[ele].validate((valid) => {
        // 表单验证
        if (valid) {
          const loading = this.$loading({
            lock: true,
            text: "新增推荐人分润",
            spinner: "el-icon-loading",
            background: "rgba(0, 0, 0, 0.7)",
          });
          marketProfitAmountApi
            .addMarketProfitAmount(that.addMarketDate)
            .then((res) => {
              console.log(res);
              if (res) {
                loading.close();
                that.provinceStr = '';
                that.cityStr = '';
                that.areaStr = '';
                that.addMarketDate = {}
                that.$nextTick(() => that.fetchData());
                that.dialogVisible = false;
                that.$message.success("新增成功");
              }
            })
            .catch((err) => {
              loading.close();
            });
        } else {
          console.log("error submit!!");
          loading.close();
          return false;
        }
      });
    },
  },
};
</script>

<style>
.pickerSelectTime {
  width: auto!important;
}
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}
@media (max-width:767px) {
  .el-message-box{
    width: 80%!important;
  }
}
</style>
