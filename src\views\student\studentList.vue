<template>
  <div class="app-container">
    <el-form :inline="true" class="container-card" label-width="110px" label-position="left" ref="dataQuery" :model="dataQuery">
      <el-row>
        <el-col :span="8" :xs="24">
          <el-form-item label="学员编号：" prop="studentCode">
            <el-input v-model="dataQuery.studentCode" placeholder="请输入学员编号" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="会员编号：" prop="memberCode">
            <el-input v-model="dataQuery.memberCode" placeholder="请输入会员编号" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="登录账号：" prop="loginName">
            <el-input v-model="dataQuery.loginName" placeholder="请输入登录账号" clearable />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="8" :xs="24">
          <el-form-item label="学员手机号：" prop="memberPhone">
            <el-input v-model="dataQuery.memberPhone" placeholder="请输入学员手机号" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="姓名：" prop="realName">
            <el-input v-model="dataQuery.realName" placeholder="请输入姓名" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="状态：">
            <el-select v-model="dataQuery.isEnable" placeholder="全部" style="width: 200px" clearable>
              <el-option v-for="(item, index) in [
                  { label: '开通', value: 1 },
                  { label: '暂停', value: 0 }
                ]" :key="index" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="8" :xs="24">
          <el-form-item label="正式学员：">
            <el-select v-model="dataQuery.isFormal" placeholder="全部" style="width: 200px" clearable>
              <el-option v-for="(item, index) in [
                  { label: '是', value: 1 },
                  { label: '否', value: 0 }
                ]" :key="index" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="区域限制：">
            <el-select v-model="dataQuery.restrictedUse" placeholder="全部" style="width: 200px" clearable>
              <el-option v-for="(item, index) in [
                  { label: '限制', value: 1 },
                  { label: '不限制', value: 0 }
                ]" :key="index" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="16" :xs="24">
          <el-form-item>
            <el-form-item label="注册时间：" prop="value1">
              <el-date-picker style="width: 100%" value-format="yyyy-MM-dd HH:mm:ss" type="daterange" clearable v-model="value1" align="right" unlink-panels range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
            </el-form-item>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="fetchData01()">搜索</el-button>
            <el-button icon="el-icon-refresh" @click="rest()">重置</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-form :inline="true" style="margin-bottom: 20px">
      <el-button type="warning" icon="el-icon-document-copy" @click="exportFlow" v-loading="exportLoading">导出</el-button>
    </el-form>

    <el-table class="common-table" v-loading="tableLoading" :data="tableData" style="width: 100%; margin-bottom: 20px" row-key="id" border default-expand-all :tree-props="{ list: 'children', hasChildren: 'true' }">
      <el-table-column prop="memberCode" label="归属会员" width="120"></el-table-column>
      <el-table-column prop="studentCode" label="学员编号" width="120"></el-table-column>
      <el-table-column prop="withdrawnBonus" label="操作" width="680">
        <template slot-scope="scope">
          <el-button type="success" size="mini" icon="el-icon-edit-outline" @click="enterChildrenList(scope.row.studentCode)">查看课程</el-button>
          <!-- 更新区域限制 -->
          <el-button type="warning" size="mini" icon="el-icon-pie-chart" v-if="scope.row.restricteduser === '不限制'" @click="areaStatus(scope.row.id, scope.row.restricteduser)">
            限制区域
          </el-button>
          <el-button type="danger" size="mini" icon="el-icon-pie-chart" v-if="scope.row.restricteduser === '限制'" @click="areaStatus(scope.row.id, scope.row.restricteduser)">
            不限制区域
          </el-button>
          <!-- 开通同步课堂 -->
          <el-button type="warning" size="mini" icon="el-icon-switch-button" v-if="scope.row.userModule === '未开通'" @click="userModuleStatus(scope.row.id, scope.row.userModule)">
            开通同步课堂
          </el-button>
          <el-button type="danger" size="mini" icon="el-icon-video-pause" v-if="scope.row.userModule === '已开通'" @click="userModuleStatus(scope.row.id, scope.row.userModule)">
            关闭同步课堂
          </el-button>
          <!-- 更新状态 -->
          <el-button type="warning" size="mini" icon="el-icon-switch-button" v-if="scope.row.idEnable === '否'" @click="studentStatus(scope.row.id, scope.row.idEnable)">
            开通
          </el-button>
          <el-button type="danger" size="mini" icon="el-icon-video-pause" v-if="scope.row.idEnable === '是'" @click="studentStatus(scope.row.id, scope.row.idEnable)">
            暂停
          </el-button>
          <el-button type="warning" size="mini" v-if="checkPermission(['admin']) && scope.row.archived == false" icon="el-icon-sold-out" @click="archive(scope.row.studentCode)">
            归档学生数据
          </el-button>
          <el-button type="warning" size="mini" v-if="checkPermission(['admin']) && scope.row.archived == true" icon="el-icon-sold-out" @click="unarchive(scope.row.studentCode)">
            恢复学生归档数据
          </el-button>
        </template>
      </el-table-column>
      <el-table-column prop="loginName" label="登录账号" width="120"></el-table-column>
      <el-table-column prop="memberPhone" label="学员手机号" width="150">
        <template #header>
          <span>学员手机号</span>
          <i :class="phoneShowStatus=='0'?'el-icon-lock':'el-icon-unlock'" style="color: #1890ff; margin-left: 5px; font-size: 15px" @click="phoneShow" v-if="currentRole == 'School'"></i>
        </template>
        <template slot-scope="scope">
          <span>
            {{ scope.row.memberPhone }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="realName" label="姓名"></el-table-column>
      <el-table-column prop="gradeName" label="年级" width="120"></el-table-column>
      <el-table-column prop="school" label="学校" width="120" :show-overflow-tooltip="true"></el-table-column>
      <el-table-column prop="totalCourseHours" label="已购学时（节）" width="120">
        <template slot-scope="scope">
          <span v-if="scope.row.totalCourseHours === '' || scope.row.totalCourseHours === null">0</span>
          <span v-else>
            {{ scope.row.totalCourseHours }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="haveCourseHourse" label="剩余学时（节）" width="120">
        <template slot-scope="scope">
          <span v-if="scope.row.haveCourseHourse === '' || scope.row.haveCourseHourse === null">0</span>
          <span v-else>
            {{ scope.row.haveCourseHourse }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="addTime" label="注册时间" width="200" :show-overflow-tooltip="true"></el-table-column>
      <el-table-column prop="restricteduser" label="区域限制">
        <template slot-scope="scope">
          <span class="red" v-if="scope.row.restricteduser === '限制'">限制</span>
          <span class="green" v-else>不限制</span>
        </template>
      </el-table-column>
      <el-table-column prop="userModule" label="同步课堂">
        <template slot-scope="scope">
          <span class="green" v-if="scope.row.userModule === '已开通'">已开通</span>
          <span class="red" v-else>未开通</span>
        </template>
      </el-table-column>
      <el-table-column prop="isFormal" label="正式学员">
        <template slot-scope="scope">
          <span class="green" v-if="scope.row.isFormal === '是'">是</span>
          <span class="red" v-else>否</span>
        </template>
      </el-table-column>
      <el-table-column prop="idEnable" label="状态">
        <template slot-scope="scope">
          <span class="green" v-if="scope.row.idEnable === '是'">开通</span>
          <span class="red" v-else>暂停</span>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <el-col :span="24" style="margin-bottom: 20px">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]" layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    </el-col>
    <ShowPhone :dialogVisible.sync="dialogVisible" :phoneShowStatus.sync="phoneShowStatus" @fetchData="fetchData"></ShowPhone>
  </div>
</template>

<script>
import studentApi from '@/api/studentList';
import { pageParamNames } from '@/utils/constants';
import ls from '@/api/sessionStorage';
import checkPermission from '@/utils/permission';
import ShowPhone from '@/components/ShowPhone/index.vue';
import auth from '@/api/auth';
import userApi from '@/api/user';
export default {
  name: 'studentList',
  components: { ShowPhone },
  data() {
    return {
      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      exportLoading: false,
      tableData: [],
      dataQuery: {
        studentCode: '',
        startDate: '',
        endDate: '',
        isEnable: '',
        isFormal: '',
        loginName: '',
        realName: '',
        memberCode: '',
        memberPhone: '',
        restrictedUse: ''
      },
      dialogVisible: false,
      updateMarketDate: {},
      addMarketDate: {},
      showLoginAccount: false,
      value1: '',
      phoneShowStatus: localStorage.getItem('phoneShowStatus'),
      // 当前权限
      currentRole: window.localStorage.getItem('roleTag'),
      currentPhone: '',
    };
  },
  async created() {
    if (this.currentRole == 'School') {
      console.log('获取手机号')
      await this.getPhoneNum()
    }
    this.fetchData(this.phoneShowStatus);
  },
  methods: {
    checkPermission,

    // 获取起始时间
    dateVal(e) {
      // console.log(e[0]);
      this.dataQuery.startDate = e[0];
      this.dataQuery.endDate = e[1];
    },
    archive(studentCode) {
      this.$message.success('归档处理中。。。。。');
      studentApi.archive(studentCode).then((res) => {
        this.$message.success('归档成功');
      });
    },
    unarchive(studentCode) {
      this.$message.success('恢复归档处理中。。。。。');
      studentApi.unarchive(studentCode).then((res) => {
        this.$message.success('恢复成功');
      });
    },
    //获取学管师列表getLearnTubes
    getLearnTubes(studentCode, id) {
      if (studentCode) {
        this.studentCode = studentCode;
      }
      if (id) {
        this.studentId = id;
      }
      //获取当前托管中心下的学管师
      const that = this;
      this.learnTUbeTableLoading = true;
      learnTubeApi
        .listByDealer(this.queryParam, that.tablePageLearnTube.currentPage, that.tablePageLearnTube.size)
        .then((res) => {
          // 设置后台返回的分页参数
          this.learnTUbeTableLoading = false;
          if (res.success) {
            this.learnTubeData = res.data.data;
            pageParamNames.forEach((name) => that.$set(that.tablePageLearnTube, name, parseInt(res.data[name])));
          }
        })
        .catch((err) => { });
    },
    //指定学管师
    assignLearnTube(idx, row) {
      const that = this;
      var param = {
        studentId: this.studentId,
        LearnTubeMerchantCode: row.merchantCode
      };
      studentApi
        .assignLearnTube(param)
        .then((res) => {
          if (res.success) {
            that.$message.success('指定学管师成功');
            this.fetchData(this.phoneShowStatus);
          }
        })
        .catch((err) => { });
    },
    //移交上级
    handOverToSuperior(studentCode, studentId) {
      const that = this;
      var param = {
        studentCode: studentCode,
        studentId: studentId
      };
      this.$confirm('确定操作吗?', '移交上级', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          studentApi
            .handOverToSuperior(param)
            .then((res) => {
              if (res.success) {
                that.$message.success('移交成功');
                this.fetchData(this.phoneShowStatus);
              }
            })
            .catch((err) => { });
        })
        .catch((err) => { });
    },
    //重置
    rest() {
      this.$refs.dataQuery.resetFields();
      this.value1 = '';
      this.fetchData(this.phoneShowStatus);
    },
    fetchData01() {
      this.tablePage = {
        currentPage: 1,
        size: this.tablePage.size,
        totalPage: null,
        totalItems: null
      };
      this.fetchData(this.phoneShowStatus);
    },
    async fetchData(phoneShowStatus) {
      const that = this;
      if (that.value1 != '' && that.value1 != null && that.value1 != undefined) {
        if (that.value1.length > 0) {
          that.dataQuery.startDate = that.value1[0];
          that.dataQuery.endDate = that.value1[1];
        } else {
          that.dataQuery.startDate = '';
          that.dataQuery.endDate = '';
        }
      } else {
        that.dataQuery.startDate = '';
        that.dataQuery.endDate = '';
      }
      that.tableLoading = true;
      studentApi.studentList(that.tablePage.currentPage, that.tablePage.size, that.dataQuery, phoneShowStatus == '0' ? '' : that.currentPhone).then((res) => {
        console.log(res);
        that.tableData = res.data.data;
        that.tableLoading = false;
        // 设置后台返回的分页参数
        pageParamNames.forEach((name) => that.$set(that.tablePage, name, parseInt(res.data[name])));
        this.tablePage.size = res.data.size;
      });
    },
    // 获取当前登录用户手机号
    async getPhoneNum() {
      const res = await userApi.getPhone();
      this.currentPhone = res.data.phone;
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val;
      this.fetchData(this.phoneShowStatus);
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val;
      this.fetchData(this.phoneShowStatus);
    },
    // 分页
    handleSizeChangeLearnTube(val) {
      this.tablePageLearnTube.size = val;
      this.getLearnTubes();
    },
    handleCurrentChangeLearnTube(val) {
      this.tablePageLearnTube.currentPage = val;
      this.getLearnTubes();
    },
    //进入查看课程
    enterChildrenList(studentCode) {
      const that = this;
      ls.setItem('studentCode', studentCode);
      that.$router.push({
        path: '/student/studentCourseRecord',
        query: {
          studentCode: studentCode
        }
      });
    },
    // 开通与暂停
    studentStatus(id, status) {
      if (status == '是') {
        status = 0;
      } else {
        status = 1;
      }
      const that = this;
      this.$confirm('确定操作吗?', '修改状态', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          studentApi.updateStatus(id, status).then((res) => {
            console.log(res);
            if (res.success) {
              that.$nextTick(() => that.fetchData(this.phoneShowStatus));
              that.$message.success('修改成功');
            }
          });
        })
        .catch((err) => { });
    },
    // 更新同步课堂
    userModuleStatus(id, status) {
      if (status == '已开通') {
        status = 0;
      } else {
        status = 1;
      }
      const that = this;
      this.$confirm('确定操作吗?', '修改状态', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          studentApi.moduleStatus(id, status).then((res) => {
            console.log(res);
            if (res.success) {
              that.$nextTick(() => that.fetchData(this.phoneShowStatus));
              that.$message.success('修改成功');
            }
          });
        })
        .catch((err) => { });
    },

    //导出
    exportFlow() {
      const that = this;
      that.exportLoading = true;
      studentApi.studentExport(that.dataQuery).then((res) => {
        //          alert(res)
        //          if(!res.success){
        //                this.$notify.error({
        //               title: "操作失败",
        //               message: res.data.message
        //             });
        //          }
        console.log(window.URL.createObjectURL(res));
        const url = window.URL.createObjectURL(res);
        const link = document.createElement('a');
        link.style.display = 'none';
        link.href = url; // 获取服务器端的文件名
        link.setAttribute('download', '学生列表导出.xls');
        document.body.appendChild(link);
        link.click();
        that.exportLoading = false;
      });
    },
    // 更新区域限制
    areaStatus(id, status) {
      if (status == '限制') {
        status = false;
      } else {
        status = true;
      }
      const that = this;
      this.$confirm('确定操作吗?', '修改状态', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          studentApi.areaStatus(id, status).then((res) => {
            console.log(res);
            if (res.success) {
              that.$nextTick(() => that.fetchData(this.phoneShowStatus));
              that.$message.success('修改成功');
            }
          });
        })
        .catch((err) => { });
    },

    //转换学时弹窗显示    status是1的时候传递数据（data有值）
    showChangeClass(status, data) {
      if (status == -1) {
        this.courseChangeData['showType' + this.changeClassType] = false;
        this.changeClassStatus = false;
      } else if (status == 1) {
        this.changeClassData.studentCode = data.studentCode;
        this.searchStudentCourse();
      } else {
        this.courseChangeData.showType2 = true;
        this.courseChangeData.showType1 = false;
      }
      this.changeClassType = status;
    },

    //查询单个学生自有学时和交付学时
    searchStudentCourse() {
      let that = this;
      studentApi.queryTopStudentCourse(that.changeClassData.merchantCode, that.changeClassData.studentCode).then((res) => {
        console.log(res);
        if (res.success) {
          that.nowCourseDate = res.data;
          that.changeClassData.merchantCode = res.data.merchantCode;
          that.nowCourseDate.haveDeliverHours = res.data.haveDeliverHours == '' ? 0 : res.data.haveDeliverHours;
          that.nowCourseDate.haveCourseHours = res.data.haveCourseHours == '' ? 0 : res.data.haveCourseHours;

          that.courseChangeData.showType1 = true;
          that.courseChangeData.showType2 = false;
        }
      });
    },

    // 确认转换学时
    confirmChangeClass() {
      let that = this;

      if (this.changeClassData.hours == undefined || this.changeClassData.hours == 0) {
        that.$message.warning('请输入转换学时');
        return;
      }

      //交付转换自有  判断交付学时是否足够交换
      if (!this.changeClassStatus && that.nowCourseDate.haveDeliverHours - this.changeClassData.hours < 0) {
        that.$message.warning('剩余交付学时不足');
        return;
      }

      //自有转交付  判断剩余自有学时是否足够交换
      if (this.changeClassStatus && that.nowCourseDate.haveCourseHours - this.changeClassData.hours < 0) {
        that.$message.warning('剩余自有学时不足');
        return;
      }

      that.changeClassData.type = that.changeClassStatus ? 1 : 2;

      studentApi.courseTransition(this.changeClassData).then((res) => {
        console.log(res);
        if (res.success) {
          that.$nextTick(() => that.fetchData(this.phoneShowStatus));
          that.showChangeClass(2);
        }
      });
    },
    // 手机号码显示隐藏
    phoneShow() {
      console.log(this.phoneShowStatus);
      if (this.tableData.length == 0) return;
      if (this.phoneShowStatus == '0') {
        console.log(this.phoneShowStatus, 11111);
        this.dialogVisible = true;
      } else {
        this.phoneShowStatus = '0';
        localStorage.setItem('phoneShowStatus', '0');
        this.fetchData(this.phoneShowStatus);
      }
    }
  }
};
</script>

<style>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.period-table td,
.period-table th {
  text-align: center;
}

.mt20 {
  margin-top: 20px;
}

.red {
  color: red;
}

.green {
  color: green;
}

@media (max-width: 767px) {
  .el-message-box {
    width: 80% !important;
  }
}
</style>
