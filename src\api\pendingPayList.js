/**
 * 资转待支付列表相关接口
 */
import request from '@/utils/request';

export default {
  conversionList(pageNum, pageSize, data) {
    return request({
      // url: '/znyy/fund/conversion/list/' + pageNum + '/' + pageSize,
      url: '/znyy/fund/conversion/list',
      method: 'GET',
      params: {
        pageNum,
        pageSize,
        ...data
      }
    });
  },
  goPay(id) {
    return request({
      // url: '/znyy/fund/conversion/list/' + pageNum + '/' + pageSize,
      url: '/znyy/fund/conversion/waitPayToPay',
      method: 'GET',
      params: {
        id: id,
        orderId: 8241111877
      }
    });
  }
};
