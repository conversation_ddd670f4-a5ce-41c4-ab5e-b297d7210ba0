import request from "@/utils/request";

export default{
  // 听力分页
  listListeningQuestion(query) {
    return request({
      url: "/dyf/listeningQuestion/list",
      method: "get",
      params: query,
    });
  },

// 听力编辑回显
findListeningQuestion(query) {
  return request({
    url: "/dyf/listeningQuestion/find",
    method: "get",
    params: query,
  });
},

// 增加听力
 addListeningQuestion(query) {
  return request({
    url: "/dyf/listeningQuestion/add",
    method: "put",
    data: query,
  });
},

// 听力编辑
 editListeningQuestion(data) {
  return request({
    url: "/dyf/listeningQuestion/edit",
    method: "post",
    data
  });
},

 // 听力删除
 deleteListeningQuestion(query) {
  return request({
    url: "/dyf/listeningQuestion/deleted",
    method: "delete",
    params: query,
  });
},
}

