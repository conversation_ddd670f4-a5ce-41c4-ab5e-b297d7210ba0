<template>
  <div class="app-container">
    <el-form :inline="true" class="container-card" label-width="110px" label-position="left">

      <el-row>
<!--        <el-col :span="8" :xs="24">
          <el-form-item label="学员编号：">
            <el-input v-model="dataQuery.studentCode" clearable/>
          </el-form-item>
        </el-col>-->
        <el-col :span="14" :xs="24">
          <el-form-item>
            <el-form-item label="开始时间：">
              <el-date-picker style="width: 100%;" value-format="yyyy-MM-dd hh:mm:ss" clearable v-model="regTime" type="daterange" align="right" unlink-panels range-separator="至"
                              start-placeholder="开始日期" end-placeholder="结束日期" >
              </el-date-picker>
            </el-form-item>
          </el-form-item>
        </el-col>
        <el-col :span="26" :xs="24" style="text-align: right">
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="fetchData01()">搜索</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <el-table class="common-table" v-loading="tableLoading" :data="tableData" style="width: 100%;margin-bottom: 20px;" row-key="id" border
              default-expand-all :tree-props="{list: 'children', hasChildren: 'true'}">
      <el-table-column prop="studentCode" label="学员编号" width="120"  sortable></el-table-column>
      <el-table-column prop="studyData" label="学习日期" width="120"  sortable></el-table-column>
      <el-table-column prop="withdrawnBonus" label="操作" width="120">
        <template slot-scope="scope">
          <el-button type="success" size="mini" icon="el-icon-edit-outline" @click="jumpOpenCourse(scope.row.id)">查看详情</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="rate" label="正确率" sortable ></el-table-column>
      <el-table-column prop="wordCount" label="单词数"  width="90" sortable></el-table-column>
      <el-table-column prop="addTime" label="开始时间" width="160"  sortable></el-table-column>
    </el-table>
    <!-- 分页 -->
    <el-col :span="24" style="overflow-x: auto;" :xs="24">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]" layout="total, sizes, prev, pager, next, jumper"
                     :total="tablePage.totalItems" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    </el-col>
  </div>
</template>

<script>
  import testResultApi from "@/api/studentWordReviewList";
  import Tinymce from "@/components/Tinymce";
  import {  pageParamNames  } from "@/utils/constants";
  import ls from '@/api/sessionStorage'

  export default {
    data() {
      return {
        tableLoading: false,
        // 分页
        tablePage: {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null
        },
        tableData: [],
        dataQuery: {
          studentCode:'',
          merchantCode:'',
          startDate:'',
          endDate:'',
          loginName:'',
          realName:''
        },
        studyRank:[],
        value1:'',
        regTime: '',
        exportLoading:false,
      };
    },
    created() {
      this.dataQuery.studentCode = ls.getItem("printReviewStudentCode");
      this.fetchData();
      this.getStudyRank();
    },
    methods: {
      // 获取起始时间
      dateVal(e) {
        // console.log(e[0]);
        this.dataQuery.startDate = e[0]
        this.dataQuery.endDate = e[1]
      },

      getStudyRank(){
        testResultApi.getStudyRank().then(res => {
          this.studyRank = res.data;
        })
      },
      fetchData01(){
         this.tablePage= {
          currentPage: 1,
          size: this.tablePage.size,
          totalPage: null,
          totalItems: null
        }
        this.fetchData();
      },
      // 查询提现列表
      fetchData() {
        const that = this;
        var a = that.regTime;
        if (a != null) {
          that.dataQuery.startTime = a[0];
          that.dataQuery.endTime = a[1];
        }else{
          that.dataQuery.startTime = '';
          that.dataQuery.endTime = '';
        }
        that.tableLoading = true
        console.log(that.dataQuery)
        testResultApi.testResultList(that.tablePage.currentPage, that.tablePage.size, that.dataQuery.studentCode,that.dataQuery).then(res => {
          that.tableData = res.data.data
          that.tableLoading = false
          // 设置后台返回的分页参数
          pageParamNames.forEach(name => that.$set(that.tablePage, name, parseInt(res.data[name])))
        })
      },
      // 分页
      handleSizeChange(val) {
        this.tablePage.size = val;
        this.fetchData();
      },
      handleCurrentChange(val) {
        this.tablePage.currentPage = val;
        this.fetchData();
      },
      // 跳转到查看详情页面
      jumpOpenCourse(id){
        const that = this
        ls.setItem('id', id);
        that.$router.push({
          path:'/student/studentWordView',
          query:{
            id:id
          }
        })
      },
    }
  };
</script>

<style>
  .container-card {
    padding: 15px 15px 15px 15px;
    margin-bottom: 15px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  }
  .period-table td,
  .period-table th{
    text-align: center;
  }
  .mt20{
    margin-top: 20px;
  }
  .red{
    color: red;
  }
  .green{
    color: green;
  }
</style>
