/**
 * 市级服务商相关接口
 */
import request from '@/utils/request'

export default {
  // 分页查询
  agentList(pageNum, pageSize, data) {
    return request({
      url: '/znyy/areaAgent/list/' + pageNum + '/' + pageSize,
      method: 'GET',
      params: data
    })
  },
  updatePaymentIsComplete(id, paymentIsComplete) {
    return request({
      url: '/znyy/areaAgent/updatePaymentIsComplete?id=' + id + '&paymentIsComplete=' + paymentIsComplete,
      method: 'PUT'
    })
  },
  // 审核
  examine(id, checkReason, isCheck) {
    return request({
      url: '/znyy/areaAgent/checkStatus?id=' + id + '&checkReason=' + checkReason + '&isCheck=' + isCheck,
      method: 'PUT'
    })
  },

  // 新增
  addAgent(data) {
    return request({
      url: '/znyy/areaAgent',
      method: 'POST',
      data
    })
  },
  // 修改回显
  echoAgent(id) {
    return request({
      url: '/znyy/areaAgent/modifyEcho/' + id,
      method: 'PUT'
    })
  },
  // 编辑
  updateAgent(data) {
    return request({
      url: '/znyy/areaAgent/updateContent',
      method: 'PUT',
      data
    })
  },
  // 获取银行分类列表
  categoryType(bankType) {
    return request({
      url: '/znyy/bvstatus/' + bankType,
      method: 'GET'
    })
  },
  updateStatus(id,status){
    return request({
      url: '/znyy/areaAgent/updateStatus',
      method: 'PUT',
      params:{
        id: id,
        isEnable: status
      }
    })
  },
  openDealer(merchantCode){
    return request({
      url: '/znyy/areaAgent/openDealer',
      method: 'POST',
      params: {
        merchantCode: merchantCode
      }
    })
  },
}
