/**
 * 事业部相关接口
 */
import request from '@/utils/request';

export default {
  // 新增
  addExperience(data) {
    return request({
      url: '/znyy/experience/save',
      method: 'POST',
      data
    });
  },
  experienceList(pageNum, pageSize, data) {
    return request({
      url: '/znyy/experience/list/' + pageNum + '/' + pageSize,
      method: 'GET',
      params: data
    });
  },
  // 审核
  examine(id, checkReason, isCheck, rank) {
    return request({
      url: '/znyy/experience/update/isCheck?id=' + id + '&checkReason=' + checkReason + '&isCheck=' + isCheck + '&rank=' + rank,
      method: 'PUT'
    });
  },
  // 修改回显
  echoDivision(id) {
    return request({
      url: '/znyy/experience/check/detail/' + id,
      method: 'GET'
    });
  },
  updateStatus(id, status) {
    return request({
      url: '/znyy/experience/update/isEnable?id=' + id + '&isEnable=' + status,
      method: 'PUT'
    });
  },

  allGone(id) {
    return request({
      url: '/znyy/experience/allGone/' + id,
      method: 'PUT'
    });
  }
};
