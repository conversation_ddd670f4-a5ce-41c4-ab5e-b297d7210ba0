/**
 * 单词水平分析
 */
import request from '@/utils/request'

export default {
  //课程单词类型分析-新增
  pdWordRankAnalysisSave(data) {
    return request({
      url: '/znyy/pd/pdWordRankAnalysis/save',
      method: 'POST',
      data,
    })
  },
  // 分页查询
  pdWordRankAnalysisPage(data) {
    return request({
      url: `/znyy/pd/pdWordRankAnalysis/page/${data.currentPage}/${data.size}?rightRateStart=${data.rightRateStart}&rightRateEnd=${data.rightRateEnd}&wordType=${data.wordType}&wordRank=${data.wordRank}`,
      method: 'GET',
    })
  },
 //详情
 getWordRankById(id) {
    return request({
      url: `/znyy/pd/pdWordRankAnalysis/getWordRankById?id=${id}`,
      method: 'GET',
    })
  },
 //编辑
 pdWordRankAnalysisUpdate(data) {
    return request({
      url: `/znyy/pd/pdWordRankAnalysis/update`,
      method: 'POST',
      data
    })
  },
     //删除
     deleteById(id) {
      return request({
        url: `/znyy/pd/pdWordRankAnalysis/deleteById?id=${id}`,
        method: 'GET',
      })
    },

}
