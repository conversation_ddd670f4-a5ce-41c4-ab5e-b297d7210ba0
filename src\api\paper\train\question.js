
import request from '@/utils/request'

export default {
  // 分页查询
  list(data) {
    return request({
      url: '/paper/web/trainQuestion/list',
      method: 'GET',
      params: data
    })
  },
  //添加
  saveOrUpdate(data) {
    return request({
      url: '/paper/web/trainQuestion/create',
      method: 'POST',
      data
    })
  },
  detail(id) {
    return request({
      url: '/paper/web/trainQuestion/detail',
      method: 'GET',
      params: {
        id: id
      }
    })
  },
  delete(id) {
    return request({
      url: '/paper/web/trainQuestion/delete',
      method: 'DELETE',
      params: {
        id: id
      }
    })
  },
  feedback(data) {
    return request({
      url: '/paper/web/trainQuestion/feedback',
      method: 'POST',
      data
    })
  },

  //保利威 上传视频
  getSign() {
    return request({
      url: '/media/web/video/getUploadSign',
      method: 'get'
    })
  },
  // /web/trainQuestion/checkQuestionlnCourse
  checkQuestionlnCourse(id) {
    return request({
      url: '/paper/web/trainQuestion/checkQuestionInCourse',
      method: 'GET',
      params: {
        id: id
      }
    })
  }
}
