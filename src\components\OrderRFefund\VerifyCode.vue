<template>
  <div class="verification-code-component">
    <el-divider></el-divider>
    <div style="font-size: 16px; font-weight: bold; margin-bottom: 10px">{{ title }}</div>
    <el-form label-width="140px" label-position="left" class="transferDropDialog-form">
      <!-- 家长手机号显示 -->
      <el-form-item :label="parentPhoneLabel" class="item">
        <span class="parent-phone">{{ parentPhone }}</span>
      </el-form-item>

      <!-- 验证码输入框 -->
      <el-form-item
        class="item"
        :label="label"
        :prop="code"
        :error="errorMessage"
        :class="customClass"
        :rules="[
          { required: true, message: '请输入验证码', trigger: 'blur' },
          { min: 6, max: 6, message: '验证码长度为6位', trigger: 'blur' }
        ]"
      >
        <el-input v-model="currentCode" :placeholder="placeholder || `请输入${label}`" :style="inputStyle" :maxlength="maxlength || 6" @input="handleInput" clearable>
          <el-tooltip v-if="isShowText" slot="append" placement="top">
            <template #content>
              <div v-html="tooltip"></div>
            </template>
            <div>获取验证码</div>
          </el-tooltip>
          <el-button v-else slot="append" :disabled="isButtonDisabled" :class="[countdown > 0 ? '' : 'btn-send-code']" @click="handleSendCode" :loading="isSending">
            {{ countdown > 0 ? `${countdown}s后重发` : buttonText || '获取验证码' }}
          </el-button>
        </el-input>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
  export default {
    name: 'VerifyCode',
    props: {
      // 家长手机号
      parentPhone: {
        type: String,
        required: true
      },
      // 家长手机号标签文本
      parentPhoneLabel: {
        type: String,
        default: ''
      },
      // 验证码输入框标签
      label: {
        type: String,
        default: '验证码'
      },
      // 表单字段名，用于表单验证
      prop: {
        type: String,
        default: ''
      },
      // 错误提示信息
      errorMessage: {
        type: String,
        default: ''
      },
      // 自定义样式类
      customClass: {
        type: String,
        default: ''
      },
      // 输入框占位符
      placeholder: {
        type: String,
        default: ''
      },
      // 输入框样式
      inputStyle: {
        type: Object,
        default: () => ({})
      },
      // 最大输入长度
      maxlength: {
        type: Number,
        default: 6
      },
      // 是否禁用
      verifyCodeDisabled: {
        type: Boolean,
        default: false
      },
      // 按钮文本
      buttonText: {
        type: String,
        default: '获取验证码'
      },

      // 倒计时秒数
      countdownSeconds: {
        type: Number,
        default: 60
      },

      title: {
        type: String,
        default: ''
      },
      tooltip: {
        type: String,
        default: '1111'
      },
      recommend: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        currentCode: '', // 当前输入的验证码
        countdown: 0, // 倒计时
        isSending: false, // 是否正在发送验证码,
        code: ''
      };
    },
    computed: {
      // 按钮是否禁用（倒计时中或禁用状态）
      isButtonDisabled() {
        return this.countdown > 0 || !this.verifyCodeDisabled;
      },
      isShowText() {
        if (this.recommend && !this.verifyCodeDisabled) {
          return true;
        }
      }
    },
    methods: {
      clearData() {
        this.currentCode = '';
      },

      // 输入事件处理
      handleInput(value) {
        this.code = value;
        // 触发输入事件，将当前值传递给父组件
        this.$emit('input', value);
      },

      // 发送验证码
      async handleSendCode() {
        try {
          this.isSending = true;
          // 触发发送验证码事件，由父组件处理实际的发送逻辑
          await this.$emit('send-code');

          // 发送成功后开始倒计时
          this.countdown = this.countdownSeconds;
          const timer = setInterval(() => {
            this.countdown--;
            if (this.countdown <= 0) {
              clearInterval(timer);
            }
          }, 1000);

          this.$message.success('验证码发送成功');
        } catch (error) {
        } finally {
          this.isSending = false;
        }
      }
    },
    watch: {
      // 监听验证码变化，实时同步给父组件
      currentCode(value) {
        this.$emit('update:currentCode', value);
      }
    }
  };
</script>

<style scoped>
  .verification-code-component {
    margin: 0 auto;
  }
  .transferDropDialog-form {
    width: 50%;
    margin: 0 auto;
  }

  .parent-phone {
    margin-left: -200px;
    font-size: 14px;
    color: #606266;
    display: inline-block;
  }
  .item .el-form-item__label {
    font-weight: 600;
    color: black;
  }
  .btn-send-code {
    background-color: #1890ff !important;
    color: #fff !important;
  }
</style>
