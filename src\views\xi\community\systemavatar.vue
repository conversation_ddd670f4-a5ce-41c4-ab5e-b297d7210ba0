<template>
  <div class="app-container" id="systemavatar">
    <el-form :inline="true" ref="queryForm" class="container-card" label-width="110px">
      <el-form-item label="头像名称：">
        <el-input v-model="dataQuery.name" placeholder="头像名称" clearable />
      </el-form-item>
      <!-- <el-form-item label="段位要求：">
          <el-select v-model="dataQuery.danGradingId" placeholder="请选择">
                <el-option
                v-for="item in pendant_options"
                :key="item.id"
                :label="item.name"
                :value="item.id">
                </el-option>
            </el-select>
        </el-form-item>
        <el-form-item label="操作人：">
          <el-input v-model="dataQuery.admin" placeholder="操作人" clearable/>
        </el-form-item>
        <el-form-item label="操作时间：">
            <el-date-picker
                v-model="operation_value1"
                type="datetimerange"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                @change="timechange"
                :default-time="['12:00:00']">
            </el-date-picker>
        </el-form-item> -->
      <el-form-item>
        <el-button icon="el-icon-re" @click="search()">查询</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-re" @click="resetQuery()">重置</el-button>
        <el-button type="primary" icon="el-icon-search" @click="updown()">上传</el-button>
      </el-form-item>
    </el-form>

    <el-table class="common-table" v-loading="tableLoading" ref="multipleTable" :data="tableData"
      style="width: 100%;margin-bottom: 20px;" row-key="id" border default-expand-all
      @selection-change="handleSelectionChange">
      <el-table-column prop="orderNum" type="index" label="序号" width="50"></el-table-column>
      <el-table-column prop="name" label="头像名称"></el-table-column>
      <el-table-column prop="withdrawnBonus" label="操作" width="220">
        <template slot-scope="scope">
          <!-- <el-button
              type="primary"
              size="mini"
              @click="editBtn(scope.row.id)"
            >移动
            </el-button> -->
          <el-button type="primary" size="mini" @click="update(scope.row.id)">编辑
          </el-button>
          <el-button type="danger" size="mini" icon="el-icon-delete" @click="singleDelete(scope.row.id)">删除
          </el-button>
        </template>
      </el-table-column>
      <el-table-column prop="address" label="头像图片">
        <template slot-scope="scope">
          <el-image style="width: 60px; height: 50px;border-radius: 50%;" :src="scope.row.headPortraitUrl"
            :preview-src-list="[scope.row.headPortraitUrl]">
          </el-image>
        </template>
      </el-table-column>
      <el-table-column prop="orderNum" label="排序" show-overflow-tooltip></el-table-column>
      <el-table-column prop="createTime" label="操作时间" show-overflow-tooltip></el-table-column>
      <el-table-column prop="isDefault" label="是否默认" show-overflow-tooltip>
        <template slot-scope="scope">
          <el-switch v-model="scope.row.isDefault" active-color="#13ce66" inactive-color="#ff4949"
            @change="switchChange(scope.row.isDefault, scope.row.id)">
          </el-switch>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <el-col :span="24" style="margin-bottom: 20px">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>


    <!-- 上传或修改头像 -->
    <el-dialog title="系统头像" :visible.sync="pendant_open" width="50%" @close="close">
      <el-form :rules="rules" ref="form" :model="imgForm" label-width="120px" style="width: 70%;">

        <el-form-item label="头像名称：" prop="name">
          <el-input v-model="imgForm.name" placeholder="挂件名称" style="width:202px" clearable />
        </el-form-item>
        <el-form-item label="是否默认：" prop="isDefault">
          <el-select v-model="imgForm.isDefault" placeholder="请选择">
            <el-option v-for="item in classifys" :key="item.value" :label="item.name" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="排序：" prop="orderNum">
          <el-input v-model="imgForm.orderNum" placeholder="排序" style="width:202px" clearable />
        </el-form-item>
        <el-form-item label="" prop="up_limit">
          <el-upload action ref="upload" :limit="1" :on-success="handleSuccess" :on-change="handleUpload"
            :file-list="fileList" :on-remove="handleRemove" :http-request="uploadHttp" list-type="picture-card"
            name="file">
            <el-button size="small" type="primary">上传挂件</el-button>
          </el-upload>
          <el-dialog :visible.sync="dialogVisible">
            <img width="100%" :src="dialogImageUrl" alt="">
          </el-dialog>
        </el-form-item>
      </el-form>
      <div class="dialog-footer">
        <el-button type="primary" @click="submitImg">确 定</el-button>
        <el-button @click="pendant_open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import systemApi from '@/api/xi/community/systemavatar'
import goodsclassifyApi from '@/api/xi/goodsclassify'
import { pageParamNames } from '@/utils/constants'
import { ossPrClient } from '@/api/alibaba'

export default {
  name: 'pendant',
  props: {
    //点击错误提示信息
    errMsg: {
      type: String,
      default: undefined,
    },

    // 图片大小尺寸
    imgSize: {
      type: Number,
      default: 5 * 1024 * 1024, // 5M=>5*1024*1024 500KB=>500*1024
    },
    // 是否显示图片的tip
    showTip: {
      type: Boolean,
      default: true,
    },
    // 展示的图片列表
    fileList: {
      type: Array,
      default() {
        return [];
      },
    },
    fullUrl: {
      type: Boolean,
      default: false,
    },
    dialogVisible: false,
  },
  data() {
    return {
      is_update: true,
      imageList: '',
      dialogImageUrl: '',
      dialogVisible: false,
      disabled: false,
      checked: false,
      operation_value1: '',
      pendant_open: false,
      pendant_options: '',
      value: '',
      dataQuery: {
        name: '',
      },
      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      open: false,
      tableData: [],
      form: {

      },
      classifys: [{ name: "是", value: true }, { name: "否", value: false }],
      imgForm: {
      },
      // 表单校验
      rules: {
        isDefault: [{ required: true, message: '请选择分类', trigger: 'blur' }],
        name: [{ required: true, message: '请输入名称', trigger: 'blur' }],
        orderNum: [{ required: true, message: '请输入排序', trigger: 'blur' }],
      },
      list_all: '',
      img_url: '',
      img_name: ''
    }
  },
  computed: {
    // 动态显示MB或者KB
    isKbOrMb() {
      return this.imgSize / 1024 / 1024 >= 1
        ? `${Number(this.imgSize / 1024 / 1024).toFixed(0)}MB`
        : `${Number(this.imgSize / 1024).toFixed(0)}KB`;
    },
  },
  created() {
    this.getPageList()
    ossPrClient();
  },
  methods: {
    switchChange(value, id) {
      console.log(value, id)
      systemApi.detail(id).then(res => {
        this.imgForm = res.data;
        this.imgForm.isDefault = value
        this.getPageList()
        systemApi.saveOrUpdate(this.imgForm).then(response => {
          this.$message.success('提交成功！')
        })
      })

    },
    updown() {
      this.imgForm = {
        name: '',
        orderNum: undefined,
        isDefault: '',
      };
      this.fileList = []
      this.pendant_open = true;
    },
    update(id) {
      console.log(this.fileList)
      systemApi.detail(id).then(res => {
        this.imgForm = res.data;
        if (this.fileList.length <= 0) {
          this.fileList.push({ url: res.data.headPortraitUrl })
        } else if (this.fileList.length > 0) {
          this.fileList[0].url = res.data.headPortraitUrl
        }
        console.log(this.fileList)
        this.is_update = false
        this.pendant_open = true;
      })
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dataQuery = {
        name: '',
      },
        this.getPageList();
    },

    submitImg() {
      if (!this.imageList && !this.fileList) {
        this.$message({
          message: '请上传一张图片',
          type: 'warning'
        });
      } else {
        this.imgForm.headPortraitUrl = this.fileList[0].url
        //   console.log(this.imgForm)
        systemApi.saveOrUpdate(this.imgForm).then(response => {
          this.$message.success('提交成功！')
          this.pendant_open = false
          this.getPageList()
          this.imgForm.danGradingId = ''
          this.$refs.upload.clearFiles()
          console.log()
        })
      }

    },
    handleUpload(file, fileList) {
      this.imageList = fileList
      console.log(file, fileList);
    },
    // handleRemove(file, fileList) {
    //   this.imageList=fileList
    //   console.log(file, fileList);
    // },
    uploadHttp({ file }) {
      this.loading = true;
      let suf = file.name.substring(file.name.lastIndexOf("."));
      const fileName = 'manage/' + Date.parse(new Date()) + suf;
      ossPrClient().put(fileName, file).then(({ res, url, name }) => {
        if (res && res.status === 200) {
          console.log(`阿里云OSS上传图片成功回调1`, res, url, name);
          file.url = url
          console.log(file)
          this.fileList.push({ uid: file.uid, url: this.aliUrl + name, name: file.name })
          console.log(this.fileList)
          if (this.fullUrl) {
            this.handleSuccess(this.aliUrl + name);
          } else {
            this.handleSuccess(name);
          }
          this.loading = false;
        }
      })
        .catch((err) => {
          this.$message.error('上传图片失败请检查网络或者刷新页面')
          console.log(`阿里云OSS上传图片失败回调`, err)
        })
    },
    moveAll() {
      if (this.list_all.length > 0) {
        this.open = true
      } else {
        this.$message({
          message: '请勾选本页全选按钮',
          type: 'warning'
        });
      }
    },
    // 图片删除
    handleRemove(file) {
      console.log("前===", this.fileList)
      var index = this.fileList.findIndex(item => {
        if (item.uid === file.uid) {
          return true;
        }
      });
      this.fileList.splice(index, 1);
      console.log("后===", this.fileList)
      this.$emit("handleRemove", file);
    },
    //   移动本页面所有数据
    idChange(val) {
      if (this.list_all.length > 0) {
        for (let index = 0; index < this.list_all.length; index++) {
          this.list_all[index].danGradingId = val;

        }
        console.log(this.list_all)
      }
      console.log(val)
    },
    deleteAll() {
      if (this.list_all.length > 0) {
        let arr = []
        for (let index = 0; index < this.list_all.length; index++) {
          arr.push(this.list_all[index].id);
        }
        console.log(arr)
        this.$confirm('确定操作吗?', '删除状态', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          systemApi.delete(arr).then(res => {
            that.$nextTick(() => that.fetchData())
            that.$message.success('删除成功!')
            this.getPageList()
          })
            .catch(err => {

            })
        }).catch(err => {

        })
      } else {
        this.$message({
          message: '请勾选本页全选按钮',
          type: 'warning'
        });
      }
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
      this.list_all = val
      console.log(val.length)
    },

    // 独立全选
    toggleSelection(selectBottom) {
      selectBottom ? this.$refs.multipleTable.toggleAllSelection() : this.$refs.multipleTable.clearSelection()
      this.select = selectBottom
    },


    // 单个删除
    singleDelete(id) {
      const that = this;
      this.$confirm('确定操作吗?', '删除状态', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        systemApi.delete(id).then(res => {
          that.$nextTick(() => that.fetchData())
          that.$message.success('删除成功!')
          this.getPageList()
        }).catch(err => {

        })
      }).catch(err => {

      })
    },
    // 图片上传成功
    handleSuccess(res) {
      this.$emit("handleSuccess", res || "");
      console.log(res)
    },
    search() {
      this.tablePage = {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      }
      this.getPageList()
    },
    handleDelete(id) {
      this.$confirm('确定要删除吗？', '删除段位', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        systemApi.delete(id).then(res => {
          this.$message.success('删除成功！')
          this.getPageList()
        })
      })
    },
    addBtn() {
      this.reset();
      this.open = true;
    },
    editBtn(id) {
      this.reset();
      systemApi.detail(id).then(res => {
        this.form = res.data;
        this.open = true;
      })
    },
    submitForm() {
      let dtoList = []
      let string = {}
      if (this.list_all.length > 1) {
        for (let index = 0; index < this.list_all.length; index++) {
          dtoList.push(this.list_all[index]);
        }
        string['dtoList'] = dtoList
      } else {
        dtoList.push(this.form);
        string['dtoList'] = dtoList
      }
      console.log(string)
      systemApi.saveOrUpdate(string).then(response => {
        this.$message.success('提交成功！')
        this.open = false
        this.getPageList()
      })
    },
    getPageList() {
      this.tableLoading = true
      this.dataQuery.pageNum = this.tablePage.currentPage
      this.dataQuery.pageSize = this.tablePage.size
      systemApi.list(this.dataQuery).then(res => {
        this.tableData = res.data.data
        this.tableLoading = false
        // 设置后台返回的分页参数
        pageParamNames.forEach(name =>
          this.$set(this.tablePage, name, parseInt(res.data[name]))
        )
      })
    },
    handlePage() {
      (this.tablePage = {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      }),
        this.getPageList()
    },

    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val
      this.getPageList()
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val
      this.getPageList()
    },
    close() {
      this.open = false
      this.pendant_open = false
      this.reset()
    },
    reset() {
      this.form = {
        name: '',
        exp: undefined,
        orderNum: undefined,
      }
      if (this.$refs['form']) {
        this.$refs['form'].resetFields();
      }
    }
  },
}
</script>

<style>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

#systemavatar .el-image-viewer__img {
  border-radius: 50%;
}

.period-table td,
.period-table th {
  text-align: center;
}

.el-form-item__content div {
  display: inline-block;
}

.el-upload-list__item-thumbnail {
  border-radius: 50%;
}

.el-upload-list--picture-card .el-upload-list__item {
  border: none;
}

.el-upload-list__item-actions {
  border-radius: 50%;
}

@media (max-width: 767px) {
  .el-message-box {
    width: 80% !important;
  }
}

.el-upload-list--picture-card {
  display: block;
}

.el-upload {
  border: none;
  width: auto;
  height: 36px;
  line-height: 36px;
}

.el-upload button {
  height: 36px;
}

.el-tooltip__popper {
  max-width: 800px;
}

.el-upload-list {
  position: relative;
}
</style>
