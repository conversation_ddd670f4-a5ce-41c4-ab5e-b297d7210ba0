<template>
  <div>
    <el-card class="frame" shadow="never">
      <el-form label-width="80px">
        <!-- 1 -->
        <el-row>
          <el-col :span="5">
            <el-form-item label="姓名:">
              <el-input clearable v-model="studentList.name" placeholder="请输入姓名"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item label="学员编号:">
              <el-input clearable v-model="studentList.studentCode" placeholder="请输入学员编号"></el-input>
            </el-form-item>
          </el-col>
          <!--          <el-col :span="5">
                      <el-form-item label="支付状态:">
                        <el-select clearable v-model="studentList.payStatus" style="width: 9vw" placeholder="请选择">
                          <el-option label="未排课" :value="-1"/>
                          <el-option label="待支付" :value="1"/>
                          <el-option label="已支付" :value="2"/>
                          <el-option label="未支付" :value="3"/>
                          <el-option label="已取消" :value="4"/>
                        </el-select>
                      </el-form-item>
                    </el-col>-->
          <el-col :span="6">
            <el-form-item label="时间筛选:" clearable>
              <el-date-picker v-model="dateArr" format="yyyy-MM-dd" value-format="yyyy-MM-dd" type="daterange"
                range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" style="width: 90%">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="5" :xs="24">
            <el-form-item label="是否资单:" prop="transfer">
              <el-select v-model="studentList.transfer" clearable placeholder="请选择">
                <el-option label="是" value="1"></el-option>
                <el-option label="否" value="0"></el-option>
              </el-select>
              </el-form-item>
          </el-col>
          <el-col :span="3" :xs="24">
            <el-button type="primary" icon="el-icon-search" @click="searchBtn" size="small">查询</el-button>
            <el-button icon="el-icon-search" @click="rest()" size="small">重置</el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
    <!--    <el-card class="frame" shadow="never">
          <el-row type="flex" justify="end">
            <span style="margin-right:8vw">
              <el-button size="medium">导出</el-button>
            </span>
          </el-row>
        </el-card>-->
    <el-table :data="tableList" style="width: 100%" id="out-table" :header-cell-style="getRowClass"
      :cell-style="{ 'text-align': 'center' }">
      <el-table-column prop="date" width="80px" type="selection" align="center"></el-table-column>
      <el-table-column prop="name" width="80px" label="姓名" header-align="center"></el-table-column>
      <el-table-column prop="studentCode" label="学员编号" header-align="center"></el-table-column>
      <el-table-column prop="address" label="操作" header-align="center" width="350">
        <template v-slot="{ row }">
          <el-button type="primary" size="mini" v-if="row.payStatus === 2 || (row.payStatus === 0 && row.planId)"
            @click="classCard(row)">查看课程表</el-button>
          <el-button type="primary" size="mini" v-if="row.payStatus === 1" @click="payNotarize(row)">
            去支付
          </el-button>
          <el-button type="primary" size="mini" v-if="row.payStatus === 1 && row.studentSource === '渠道门店'"
            @click="sendParent(row)">
            发送家长支付
          </el-button>
          <el-button type="primary" size="mini" v-if="row.payStatus === 1 && row.studentSource === '家长会员'"
            @click="contactInfo(row, 1)">
            联系家长支付
          </el-button>
          <el-button type="primary" size="mini" v-if="row.payStatus === 3 || row.payStatus === 4"
            @click="contactInfo(row, 2)">
            联系交付中心
          </el-button>
          <el-button type="danger" size="mini" v-if="currentAdmin.schoolType !== 3" @click="contactInfo(row, 2)">
            退款
          </el-button>
          <!--(value = "学生对接状态 0无  1未填写  2已填写")studentContactStatus;-->
          <el-button type="primary" size="mini" v-if="row.studentContactStatus === 2" @click="openAbutment(row)">
            上课信息对接表
          </el-button>
        </template>
      </el-table-column>
      <!--      <el-table-column prop="contentType" label="课程内容" header-align="center"></el-table-column>-->
      <el-table-column prop="deliverName" label="学员所属交付中心" header-align="center"></el-table-column>
      <el-table-column prop="firstTime" label="首次上课时间" header-align="center" min-width="110px">
        <template slot-scope="scope">
          <span>{{
            getFormatToService(scope.row.firstTime, scope.row.firstWeek)
          }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="deliverMerchant" label="交付方商户号" header-align="center"></el-table-column>
      <el-table-column prop="studentSource" label="学员来源" header-align="center"></el-table-column>
      <!-- <el-table-column prop="periods" label="课程期数" header-align="center"></el-table-column> -->
      <!-- <el-table-column prop="deliverHours" label="所需学时" header-align="center"></el-table-column> -->
      <!-- <el-table-column prop="amount" label="订单金额" header-align="center"></el-table-column> -->
      <el-table-column prop="createTime" label="上传时间" header-align="center"></el-table-column>
      <el-table-column prop="totalHours" label="已购交付学时" header-align="center"></el-table-column>
      <el-table-column prop="haveCourseHours" label="剩余交付学时" header-align="center">
        <template v-slot="{ row }">
          <span v-if="row.haveCourseHours > 4">{{ row.haveCourseHours }}</span>
          <span v-else style="color: red">{{ row.haveCourseHours }}</span>
        </template>
      </el-table-column>
      <!-- ----------------------------- -->
      <el-table-column prop="transfer" label="是否资单" header-align="center">
        <template v-slot="{ row }">
          <span v-if="row.transfer== 1">是</span>
          <span v-else >否</span>
        </template>
      </el-table-column>
      <!-- <el-table-column prop="payStatus" label="支付状态" width="90" header-align="center">
        <template v-slot="{ row }">
          <el-tag v-if="!row.planId"> 未排课</el-tag>
          <el-tag v-if="row.payStatus === 1"> 待支付</el-tag>
          <el-tag v-if="row.payStatus === 2 || (row.payStatus === 0 && row.planId)" type="success">
            已支付</el-tag>
          <el-tag v-if="row.payStatus === 3" type="danger"> 未支付</el-tag>
          <el-tag v-if="row.payStatus === 4" type="danger"> 已取消</el-tag>
        </template>
      </el-table-column>  -->
      <!-- <el-table-column min-width="100" prop="reviewType" label="复习方式" border header-align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.reviewType==1?'教练带复习':'自行复习' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="totalReviewTime" label="已购复习学时（分钟）" min-width="170" header-align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.totalReviewTime? scope.row.totalReviewTime :'-' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="haveReviewTime" label="剩余复习学时（分钟）" min-width="170" header-align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.haveReviewTime? scope.row.haveReviewTime :'-' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="" label="复习时间"  header-align="center">
        <template slot-scope="scope">
          <span v-if="scope.row.reviewWeek">{{ getReviewWeek(scope.row.reviewWeek) + scope.row.reviewTime }}</span>
          <span v-if="!scope.row.reviewWeek">-</span>
        </template>
      </el-table-column> -->
    </el-table>
    <el-row type="flex" justify="center" align="middle" style="height: 60px">
      <!-- 3个变量：每页数量、页码数、总数  -->
      <!-- 2个事件：页码切换事件、每页数量切换事件-->
      <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
        :current-page="studentList.pageNum" :page-sizes="[10, 20, 50]" :page-size="studentList.pageSize"
        layout="total, sizes, prev, pager, next, jumper" :total="total">
      </el-pagination>
    </el-row>

    <el-dialog title="学习课程表" :visible.sync="dialogVisible" :width="screenWidth > 1300 ? '70%' : '100%'">
      <ClassCard v-if="dialogVisible" :query="query" />
    </el-dialog>

    <el-dialog title="上课信息对接表" :visible.sync="dialogAbutment" :close-on-click-modal="false" :before-close="handleClose">
      <el-form ref="abutmentList" :model="abutmentList" label-width="120px" :rules="rules">
        <el-form-item label="学员姓名" prop="studentName" style="width: 50%">
          <el-input v-model="abutmentList.studentName" placeholder="" disabled />
        </el-form-item>
        <el-form-item label="学员编号" prop="studentCode" style="width: 50%">
          <el-input v-model="abutmentList.studentCode" placeholder="" disabled />
        </el-form-item>
        <el-form-item label="联系方式" prop="phone" style="width: 50%">
          <el-input v-model="abutmentList.mobile" placeholder="" disabled />
        </el-form-item>
        <el-form-item label="年级" prop="grade" style="width: 50%">
          <el-select v-model="abutmentList.grade" placeholder="" disabled>
            <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="充值学时" prop="rechargeHour" style="width: 50%">
          <el-input v-model="abutmentList.rechargeHour" placeholder="" disabled />
        </el-form-item>
        <el-form-item label="课程规划" prop :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <div class="timeClass thesaurus" style="margin-bottom: 10px; margin-left: 0"
            v-for="(item, index) in coursrList" :key="index">
            <span style="margin: 0 15px">{{ item.courseName }}</span>
            <div class="vocabulary" v-if="item.isFirstWordBase">首节课词库</div>
          </div>
        </el-form-item>
        <el-form-item label="上课时间" prop="studyTimeList" style="width: 100%">
          <el-row v-for="(item, index) in abutmentList.studyTimeList" :key="index">
            <el-col :span="5" :xs="24">
              <el-form-item :prop="`studyTimeList.${index}.usableWeek`" :rules="rules.usableWeek">
                <el-select v-model="item.usableWeek" style="width: 7vw; margin-right: 1vw; margin-bottom: 1vw"
                  placeholder="请选择星期几" @change="studyWeekChange(item.usableWeek, index)"
                  :disabled="currentAdmin.roleTag === 'admin'">
                  <el-option v-for="(week, val) in weeklist" :key="val" :label="week.label" :value="week.value">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="4" :xs="24" style="margin-right: 10px">
              <el-form-item :prop="`studyTimeList.${index}.startTime`" :rules="rules.startTime">
                <el-time-picker :disabled="currentAdmin.roleTag === 'admin'" class="time_style" format="HH:mm"
                  value-format="HH:mm" v-model="abutmentList.studyTimeList[index].startTime" style="width: 100%"
                  :clearable="false" @change="studyTimeChange(item.startTime, index)"
                  :picker-options="{ selectableRange: '00:00:00 - 23:59:00' }" placeholder="开始时间"></el-time-picker>
              </el-form-item>
            </el-col>
            <el-col :span="4" :xs="24" style="margin-right: 10px">
              <el-input-number @change="changeHour($event, item, index)"
                v-model="abutmentList.studyTimeList[index].hour" :min="1" :max="2" label="小时" size="mini"
                :disabled="currentAdmin.roleTag === 'admin'" style="margin-right: 10px"></el-input-number>
            </el-col>
            <el-col :span="4" :xs="24" style="margin-right: 10px">
              <el-time-picker disabled="true" format="HH:mm" value-format="HH:mm" style="width: 100%"
                v-model="abutmentList.studyTimeList[index].endTime" placeholder="结束时间"></el-time-picker>
            </el-col>
            <el-col :span="5" v-if="currentAdmin.roleTag != 'admin'">
              <div v-if="index === abutmentList.studyTimeList.length - 1">
                <el-button v-if="index != 0" @click="deleteTime(index)" type="danger" icon="el-icon-minus" size="small"
                  circle></el-button>
                <el-button size="small" @click="addTime" type="primary" icon="el-icon-plus" circle></el-button>
              </div>
              <el-button v-else @click="deleteTime(index)" type="danger" icon="el-icon-minus" size="small"
                circle></el-button>
            </el-col>
          </el-row>
        </el-form-item>
        <!-- <el-form-item label="复习时间" prop style="width: 90%;margin-top: 10px;">
          <el-form-item v-if="currentAdmin.roleTag != 'admin'" prop="reviewWeek">
            <el-checkbox-group v-model="abutmentList.reviewWeek" size="medium" @change="reviewWeekChange">
              <el-checkbox-button v-for="item in weeklist" :label="item.value" :key="item.value" :vaule="item.value">
                {{ item.label }}
              </el-checkbox-button>
            </el-checkbox-group>
          </el-form-item>
          <el-form-item label="" v-else>
            <el-col :span="3" :xs=12 v-for="(item, index) in abutmentList.reviewWeek" :key="index">
              <span class="week">{{ getWeekName(item) }}</span>
            </el-col>
          </el-form-item>
          <el-form-item style="width: 40%;margin-top: 1vw;" prop :rules="rules.reviewTime">
            <el-time-picker :disabled="currentAdmin.roleTag === 'admin'" v-model="abutmentList.reviewTime"
              placeholder="开始时间" format="HH:mm"></el-time-picker>
          </el-form-item>
        </el-form-item> -->

        <div style="
            position: relative;
            display: flex;
            margin-bottom: 22px;
            align-items: center;
          " v-if="abutmentList.firstTime != ''">
          <div style="width: 120px; text-align: end; padding-right: 12px">
            首次上课时间
            <div style="color: #999999; font-size: 11px">限制24小时之后</div>
          </div>
          <div class="timeClass" style="margin-left: 0; line-height: 36px; width: 37%">
            <span style="margin: 0 15px; color: #000000">
              {{
                getFormatToService(
                  abutmentList.firstTime,
                  abutmentList.firstWeek
                )
              }}
            </span>
          </div>
        </div>
        <el-form-item label="是否试课" prop="isExp" style="width: 50%">
          <el-radio-group v-model="abutmentList.isExp" :disabled="currentAdmin.roleTag === 'admin'">
            <el-radio :label="1">是</el-radio>
            <el-radio :label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="词汇量检测" prop="wordBase" style="width: 50%" v-if="abutmentList.isExp === 1">
          <el-input v-model="abutmentList.wordBase" placeholder="" disabled></el-input>
        </el-form-item>

        <el-form-item label="是否新生" prop="isNewStudent" style="width: 50%">
          <el-radio-group v-model="abutmentList.isNewStudent" :disabled="currentAdmin.roleTag === 'admin'">
            <el-radio :label="1">是</el-radio>
            <el-radio :label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark" style="width: 70%">
          <el-input v-model="abutmentList.remark" type="textarea" placeholder="请输入" :rows="5" maxlength="200"
            :disabled="currentAdmin.roleTag === 'admin'" />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  getContactInfo,
  getPlanCoursePayInfo,
  getStudentList,
  sendPlanCourse,
} from "@/api/studyroom/studentList";
import { dxSource } from "@/utils/constants";
import { Base64 } from "@/utils/base64";
import ClassCard from "@/views/student/classCard";
import store from "@/store";
import schoolList from "@/api/schoolList";
import systemApi from "@/api/systemConfiguration";
import studentApi from "@/api/studentList";
import dayjs from "dayjs";
import courseApi from "@/api/courseList";

export default {
  name: "deliverStudentList",
  components: {
    ClassCard,
  },
  directives: {
    "el-select-loadmore": {
      bind(el, binding) {
        const SELECTWRAP_DOM = el.querySelector(
          ".el-select-dropdown .el-select-dropdown__wrap"
        );
        SELECTWRAP_DOM.addEventListener("scroll", function () {
          //临界值的判断滑动到底部就触发
          const condition =
            this.scrollHeight - this.scrollTop <= this.clientHeight;
          if (condition) {
            binding.value();
          }
        });
      },
    },
  },
  data() {
    return {
      screenWidth: window.screen.width,
      currentAdmin: "",
      loading: false,
      //级联菜单选择内容
      token: store.getters.token,
      setpayUrl: store.getters.setpayUrl,
      studentList: {
        name: "",
        pageNum: 1,
        pageSize: 10,
        phone: "",
        startTime: "",
        studentCode: "",
        transfer:"",
      },
      tableList: [],
      total: null,
      query: {},
      dialogVisible: false,
      dateArr: [],

      dialogAbutment: false, // 上课信息对接表
      abutmentList: {},
      //年纪
      options: [
        { grade: "18", value: "幼儿园" },
        {
          value: 1,
          label: "一年级",
        },
        {
          value: 2,
          label: "二年级",
        },
        {
          value: 3,
          label: "三年级",
        },
        {
          value: 4,
          label: "四年级",
        },
        {
          value: 5,
          label: "五年级",
        },
        {
          value: 6,
          label: "六年级",
        },
        {
          value: 7,
          label: "初一",
        },
        {
          value: 8,
          label: "初二",
        },
        {
          value: 9,
          label: "初三",
        },
        {
          value: 10,
          label: "高一",
        },
        {
          value: 11,
          label: "高二",
        },
        {
          value: 12,
          label: "高三",
        },
        {
          value: 13,
          label: "大一",
        },
        {
          value: 14,
          label: "大二",
        },
        {
          value: 15,
          label: "大三",
        },
        {
          value: 16,
          label: "大四",
        },
        {
          value: 17,
          label: "其他",
        },
      ],
      weeklist: [
        {
          value: 0,
          label: "星期一",
        },
        {
          value: 1,
          label: "星期二",
        },
        {
          value: 2,
          label: "星期三",
        },
        {
          value: 3,
          label: "星期四",
        },
        {
          value: 4,
          label: "星期五",
        },
        {
          value: 5,
          label: "星期六",
        },
        {
          value: 6,
          label: "星期天",
        },
      ],
      coursrList: [],
      rules: {
        usableWeek: {
          required: true,
          message: "请选择上课星期",
          trigger: "change",
        },
        startTime: {
          required: true,
          message: "请选择上课时间",
          trigger: "change",
        },
        reviewWeek: {
          type: "array",
          required: true,
          message: "请选择复习星期",
          trigger: "change",
        },
        reviewTime: {
          required: true,
          message: "请选择复习时间",
          trigger: "change",
        },
        isExp: [
          { required: true, message: "请选择是否试课", trigger: "change" },
        ],
        isNewStudent: [
          { required: true, message: "请选择是否新生", trigger: "change" },
        ],
      },
      changeFirstTime: {
        changeExp: false,
        changeReviewTime: false,
        changeReviewWeek: false,
        changeStudy: false,
        changeNew: false,
        changeRemark: false,
      },
      normalFirstWeek: "",
      normalFirstTime: "",
      normalFirstStudyTime: "",
    };
  },
  watch: {
    "abutmentList.studyTimeList": {
      handler(n, o) {
        if (o === undefined) {
          return;
        }
        this.changeFirstTime.changeStudy = true;
        this.setFirstTimeChange();
      },
      deep: true,
    },
    "abutmentList.reviewTime": {
      handler(n, o) {
        if (o === undefined) {
          return;
        }
        this.changeFirstTime.changeReviewTime = true;
        this.setFirstTimeChange();
      },
      deep: true,
    },
    "abutmentList.reviewWeek": {
      handler(n, o) {
        if (o === undefined) {
          return;
        }
        this.changeFirstTime.changeReviewWeek = true;
        this.setFirstTimeChange();
      },
      deep: true,
    },
    "abutmentList.isExp": {
      handler(n, o) {
        if (o === undefined) {
          return;
        }
        this.changeFirstTime.changeExp = true;
        this.setFirstTimeChange();
      },
      deep: true,
    },
    "abutmentList.isNewStudent": {
      handler(n, o) {
        if (o === undefined) {
          return;
        }
        this.changeFirstTime.changeNew = true;
        this.setFirstTimeChange();
      },
      deep: true,
    },
    "abutmentList.remark": {
      handler(n, o) {
        if (o === undefined) {
          return;
        }
        this.changeFirstTime.changeRemark = true;
        this.setFirstTimeChange();
      },
      deep: true,
    },
  },
  created() {
    this.initData();
    this.getCurrentAdmin();
    this.screenWidth = window.screen.width;
  },
  methods: {
    // 复习时间
    getReviewWeek(week) {
      // console.log(JSON.parse(week),888);
      let that = this;
      let list = [];
      JSON.parse(week).forEach(function (item) {
        list.push(that.weeklist[item].label);
      });
      let str = list.join("、");
      return str;
    },
    setFirstTimeChange() {
      let isNeed = false;
      for (let key in this.changeFirstTime) {
        if (key) {
          isNeed = key;
          break;
        }
      }
      if (isNeed) {
        this.getFirstStudyTime();
      } else {
        this.abutmentList.firstWeek = this.normalFirstWeek;
        this.abutmentList.firstTime = this.normalFirstTime;
        this.abutmentList.firstStudyTime = this.normalFirstStudyTime;
      }
    },
    getCurrentAdmin() {
      schoolList.getCurrentAdmin().then((res) => {
        this.currentAdmin = res.data;
        if (res.data.schoolType == 3) {
          systemApi.coursePriceList(1, 900000).then((res) => {
            this.cityPrice = res.data.data;
          });
        }
      });
    },
    fillTableNormalData(item) {
      const that = this;
      let data = {
        id: item.studentContactInfoId,
      };
      studentApi.getStudentContactInfoDetail(data).then((res) => {
        that.abutmentList = res.data;
        that.coursrList = JSON.parse(that.abutmentList.courseProject);
        that.abutmentList.reviewWeek = JSON.parse(that.abutmentList.reviewWeek);
        that.abutmentList.reviewTime = that.setReviewTime(
          that.abutmentList.reviewTime
        );
        that.setStudyTime();
        that.normalFirstWeek = that.abutmentList.firstWeek;
        that.normalFirstTime = that.abutmentList.firstTime;
        that.normalFirstStudyTime = that.getFormatToService(
          that.normalFirstTime,
          that.normalFirstWeek
        );
      });
    },
    setStudyTime() {
      for (let i = 0; i < this.abutmentList.studyTimeList.length; i++) {
        this.abutmentList.studyTimeList[i].hour = this.getHourForService(
          this.abutmentList.studyTimeList[i]
        );
      }
    },
    setReviewTime(time) {
      let date = dayjs().format("YYYY-MM-DD");
      return dayjs(date + " " + time);
    },
    // 分页
    handleSizeChange(val) {
      this.studentList.pageSize = val;
      this.initData();
    },
    handleCurrentChange(val) {
      this.studentList.pageNum = val;
      this.initData();
    },
    async initData() {
      this.loading = true
      if (this.dateArr && this.dateArr.length > 0) {
        this.studentList.startTime = this.dateArr[0];
        this.studentList.endTime = this.dateArr[1];
      } else {
        this.studentList.startTime = "";
        this.studentList.endTime = "";
      }
      let { data } = await getStudentList(this.studentList);
      this.tableList = data.data;
      this.loading = false
      this.total = Number(data.totalItems);
    },
    getRowClass({ rowIndex, columnIndex }) {
      if (rowIndex == 0) {
        return "background:#f5f7fa";
      }
    },

    //重置
    rest() {
      this.studentList.pageNum = 1
      this.studentList.name = "";
      this.studentList.studentCode = "";
      this.dateArr = "";
      this.initData();
    },
    async searchBtn() {
      this.studentList.pageNum = 1
      await this.initData();
    },
    //排课支付
    payNotarize(row) {
      getPlanCoursePayInfo(row.planId).then((res) => {
        if (res.success) {
          const split = dxSource.split("##");
          res.data.dxSource =
            res.data.registerCallIInfo.appSource +
            "##" +
            split[1] +
            "##" +
            split[2];
          let params = JSON.stringify(res.data);
          let req =
            "token=" +
            this.token +
            "&params=" +
            params +
            "&back=" +
            window.location.href;
          //需要编码两遍，避免出现+号等
          var encode = Base64.encode(Base64.encode(req));
          this.disabledF = false;
          // window.open("http://192.168.5.35:8000/product?" + encode, "_blank");
          window.open(this.setpayUrl + "product?" + encode, "_blank");
        }
        this.btndialog = false;
      });
    },
    //发送家长支付
    sendParent(row) {
      sendPlanCourse(row.planId, 2).then((res) => {
        if (res.success) {
          this.$message.success("发送成功！");
        }
      });
    },
    //获取联系信息
    contactInfo(row, type) {
      getContactInfo(row.planId, type).then((res) => {
        let title;
        let content;
        if (type === 1) {
          title = "联系家长支付";
          content =
            "家长姓名： " +
            res.data.name +
            " &nbsp;&nbsp;&nbsp; 手机号：" +
            res.data.mobile;
        } else {
          title = "联系交付中心";
          content =
            "交付中心负责人： " +
            res.data.name +
            " &nbsp;&nbsp;&nbsp; 手机号：" +
            res.data.mobile;
        }
        this.$alert(content, title, {
          dangerouslyUseHTMLString: true,
          confirmButtonText: "确定",
        });
      });
    },
    //查看课程表
    classCard(row) {
      this.query = row;
      this.dialogVisible = true;
    },

    openAbutment(row) {
      this.fillTableNormalData(row);
      this.dialogAbutment = true;
    },

    ///////首次上课//////////////////
    getFormatToService(date, week) {
      if (date) {
        let str = dayjs(date).format("MM月DD日& HH:mm");
        let allStr = str;
        if (week) {
          allStr = str.replace("&", this.getWeekName(week));
        } else {
          allStr = str.replace("&", "");
        }
        return allStr;
      }
      return "-";
    },
    getFirstStudyTime() {
      if (this.abutmentList.studyTimeList.length === 0) {
        this.abutmentList.firstWeek = "";
        this.abutmentList.firstTime = "";
        this.abutmentList.firstStudyTime = "";
        return;
      }
      let dateArr = [];
      let dateMinArr = [];
      let nowDate = dayjs().format("YYYY-MM-DD HH:mm");
      for (let i = 0; i < this.abutmentList.studyTimeList.length; i++) {
        if (this.abutmentList.studyTimeList[i].startTime == "") {
          continue;
        }
        let date = this.getChoseDataToOption(
          this.abutmentList.studyTimeList[i]
        );
        dateArr.push(date);
        let dayjs1 = dayjs(date);
        dateMinArr.push(dayjs1.diff(nowDate, "minute"));
      }
      if (dateArr.length === 0) {
        this.abutmentList.firstWeek = "";
        this.abutmentList.firstTime = "";
        this.abutmentList.firstStudyTime = "";
        return;
      }
      console.log(dateArr);
      console.log(dateMinArr);
      let needIndex = -1;
      let minMinVal = Infinity;
      for (let i = 0; i < dateMinArr.length; i++) {
        if (dateMinArr[i] > 24 * 60 && dateMinArr[i] < minMinVal) {
          needIndex = i;
          minMinVal = dateMinArr[i];
        }
      }
      console.log(dateArr);
      console.log(needIndex);
      if (needIndex != -1) {
        this.abutmentList.firstStudyTime = this.getFormatToShow(
          dateArr[needIndex]
        );
      } else {
        let minIndex = dateMinArr.indexOf(Math.min(...dateMinArr));
        let lastDate = dayjs(dateArr[minIndex]).add(7, "day");
        this.abutmentList.firstStudyTime = this.getFormatToShow(lastDate);
      }
    },

    getFormatToShow(date) {
      let str = dayjs(date).format("MM月DD日& HH:mm");
      let weekIndex = this.getLocalTypeWeek(dayjs(date).day());
      this.abutmentList.firstTime = dayjs(date).format("YYYY-MM-DD HH:mm");
      this.abutmentList.firstWeek = weekIndex;
      let allStr = str.replace("&", this.getWeekName(weekIndex));
      return allStr;
    },

    getWeekName(week) {
      for (let i = 0; i < this.weeklist.length; i++) {
        if (this.weeklist[i].value == week) {
          return this.weeklist[i].label;
        }
      }
      return "";
    },

    getChoseDataToOption(data) {
      let date = this.getDateForWeek(data.usableWeek);
      return date + " " + data.startTime;
    },

    getDateForWeek(week) {
      let nowWeek = this.getLocalTypeWeek(dayjs().day());
      let diff = week - nowWeek;
      let date = "";
      if (diff >= 0) {
        date = dayjs().add(Math.abs(diff), "day").format("YYYY-MM-DD");
      } else {
        date = dayjs().subtract(Math.abs(diff), "day").format("YYYY-MM-DD");
      }
      return date;
    },

    getLocalTypeWeek(nowWeek) {
      return (nowWeek + 6) % 7;
    },
    ////////修改上课信息表////
    addTime() {
      this.abutmentList.studyTimeList.push({
        usableWeek: "",
        endTime: "",
        startTime: "",
        hour: 1,
      });
    },
    deleteTime(index) {
      this.abutmentList.studyTimeList.splice(index, 1);
    },
    submitForm() {
      if (this.currentAdmin.roleTag === "admin") {
        this.handleClose();
        return;
      }
      const that = this;
      that.$refs["abutmentList"].validate((valid) => {
        console.log(valid);
        if (valid) {
          let data = {
            id: that.abutmentList.id,
            isExp: that.abutmentList.isExp,
            isNewStudent: that.abutmentList.isNewStudent,
            remark: that.abutmentList.remark,
            reviewWeek: JSON.stringify(that.abutmentList.reviewWeek),
            reviewTime: that.getReviewTime(that.abutmentList.reviewTime),
            studyTimeList: that.getServiceStudyTimeList(
              that.abutmentList.studyTimeList
            ),
            wordBase: that.abutmentList.wordBase,
            firstTime: that.abutmentList.firstTime,
            firstWeek: that.abutmentList.firstWeek,
          };
          console.log(data);
          studentApi.updateStudentContactInfo(data).then((res) => {
            // this.$refs['abutmentList'].resetFields();
            this.dialogAbutment = false;
            this.initData();
            this.abutmentList = {};
            this.coursrList = [];
            this.$message.success("修改成功");
          });
        } else {
          return false;
        }
      });
    },

    getReviewTime(time) {
      let date = new Date(time);
      let hour = date.getHours() > 9 ? date.getHours() : "0" + date.getHours();
      let minutes =
        date.getMinutes() > 9 ? date.getMinutes() : "0" + date.getMinutes();
      return `${hour}:${minutes}`;
    },

    getServiceStudyTimeList(list) {
      for (let i = 0; i < list.length; i++) {
        let data = list[i];
        let [startHours, startMinutes] = data.startTime.split(":").map(Number);
        let [endHours, endMinutes] = data.endTime.split(":").map(Number);
        data.usableHourEnd = endHours;
        data.usableMinuteEnd = endMinutes;
        data.usableHourStart = startHours;
        data.usableMinuteStart = startMinutes;
      }
      return list;
    },

    reviewWeekChange(val) {
      console.log(val);
    },

    studyWeekChange(e, index) {
      if (e != null) {
        if (
          this.abutmentList.studyTimeList[index].startTime &&
          this.abutmentList.studyTimeList[index].endTime
        ) {
          if (
            this.judgeAllBetween(
              this.abutmentList.studyTimeList[index].startTime,
              this.abutmentList.studyTimeList[index].endTime,
              index
            )
          ) {
            this.$message.error("选择时间段与其他时间段重叠");
            this.abutmentList.studyTimeList[index].usableWeek = "";
            return;
          }
        }
      }
    },

    changeHour(e, item, index) {
      if (item.startTime == "") {
        return;
      }
      item.endTime = this.getAutoEndTime(item.startTime, item.hour);
      if (item.startTime != "" && item.endTime != "") {
        if (this.judgeAllBetween(item.startTime, item.endTime, index)) {
          item.startTime = "";
          item.endTime = "";
          this.$message.error("选择时间段与其他时间段重叠");
        }
      }
    },

    //自动获取结束时间
    getAutoEndTime(startTime, addHour) {
      let timeParts = startTime.split(":");
      let hours = parseInt(timeParts[0], 10);
      let minutes = parseInt(timeParts[1], 10);

      hours += addHour;

      if (hours >= 24) {
        hours -= 24;
      }
      let endTime = `${hours < 10 ? "0" + hours : hours}:${minutes < 10 ? "0" + minutes : minutes
        }`;
      return endTime;
    },
    studyTimeChange(e, index) {
      console.log(e);
      if (e) {
        this.abutmentList.studyTimeList[index].endTime = this.getAutoEndTime(
          e,
          this.abutmentList.studyTimeList[index].hour
        );
        if (
          this.judgeAllBetween(
            this.abutmentList.studyTimeList[index].startTime,
            this.abutmentList.studyTimeList[index].endTime,
            index
          )
        ) {
          this.$message.error("选择时间段与其他时间段重叠");
          this.abutmentList.studyTimeList[index].startTime = "";
          this.abutmentList.studyTimeList[index].endTime = "";
          return;
        }
      }
    },

    judgeAllBetween(start, end, index) {
      console.log(index);
      let week = this.abutmentList.studyTimeList[index].usableWeek;
      if (week.toString().length == 0) {
        return false;
      }
      let s1 = this.getMinutesForTime(start);
      let e1 = this.getMinutesForTime(end);
      console.log(this.abutmentList.studyTimeList);
      for (let i = 0; i < this.abutmentList.studyTimeList.length; i++) {
        if (
          index == i ||
          this.abutmentList.studyTimeList[i].usableWeek != week
        ) {
          continue;
        }
        let s2 = this.getMinutesForTime(
          this.abutmentList.studyTimeList[i].startTime
        );
        let e2 = this.getMinutesForTime(
          this.abutmentList.studyTimeList[i].endTime
        );
        if (s1 <= s2 && e1 >= e2) {
          return true;
        }
        if (s1 >= s2 && e1 <= e2) {
          return true;
        }
        if (s1 <= s2 && e1 <= e2 && s2 <= e1) {
          return true;
        }
        if (s1 >= s2 && e1 >= e2 && s1 <= e2) {
          return true;
        }
      }
      return false;
    },

    getMinutesForTime(time) {
      let [hours, minutes] = time.split(":").map(Number);
      return hours * 60 + minutes;
    },
    handleClose() {
      this.abutmentList = {};
      this.coursrList = [];
      this.dialogAbutment = false;
    },
    getHourForService(timeData) {
      let startHours = timeData.usableHourStart;
      let endHours = timeData.usableHourEnd + 24;
      let diff = endHours - startHours;
      if (diff > 24) {
        return diff - 24;
      } else {
        return diff;
      }
    },
  },
};
</script>

<style lang="scss">
.frame {
  padding-top: 13px;

  //margin: 15px;
}

.timeClass {
  border: 1px solid #dfe4ed;
  border-radius: 5px;
  background-color: #fff;
  box-sizing: border-box;
  margin-left: 20px;
}

.week {
  border: 1px solid #dfe4ed;
  border-radius: 5px;
  box-sizing: border-box;
  padding: 7px 20px;
}

.vocabulary {
  position: absolute;
  top: 6px;
  right: -1px;
  height: 24px;
  color: #fff;
  font-size: 12px;
  line-height: 24px;
  border-radius: 3px;
  padding: 0 4px;
  background-color: #46a6ff;
}

.thesaurus {
  position: relative;
}
</style>
