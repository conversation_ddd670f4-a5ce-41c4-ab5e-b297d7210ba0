<!--流程定义-->
<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="88px">
      <el-form-item label="流程KEY" prop="key">
        <el-input v-model="queryParams.key" placeholder="请输入流程KEY" clearable size="small"
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="名称" prop="name">
        <el-input v-model="queryParams.name" placeholder="请输入名称" clearable size="small"
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="来源">
        <el-select v-model="queryParams.appSource" placeholder="请选择">
          <el-option v-for="item in appSourceList" :key="item.value" :label="item.name" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="success" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" style="margin-bottom: 30px;">
      <!--      <el-col :span="1.5">
              <el-button
                type="primary"
                icon="el-icon-plus"
                size="mini"
                @click="OnlineDrawingProcess"
              >在线绘制流程
              </el-button>
            </el-col>-->
      <el-col :span="1.5">
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleImport">部署流程
        </el-button>
      </el-col>
    </el-row>


    <el-table v-loading="loading" :data="tableData" border stripe>
      <el-table-column label="流程ID" align="center" prop="id" />
      <el-table-column label="来源" align="center" prop="appSource" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)">删除
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="流程KEY" align="center" prop="key" />
      <el-table-column label="流程名称" align="center" prop="name" />
      <el-table-column label="版本" align="center" prop="version" />
      <el-table-column label="部署时间" align="center" prop="deploymentTime" />
      <el-table-column label="部署ID" align="center" prop="deploymentId" />
      <el-table-column label="状态" align="center" prop="suspendState">
        <template slot-scope="scope">
          <span>{{ scope.row.suspendState != 1 ? '挂起' : '激活' }}</span>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <el-col :span="20">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>


    <!-- 用户导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-form :model="form" ref="form" :inline="true" label-width="78px">
        <el-form-item label="来源：" prop="appSource" :rules="[{
          required: true,
          message: '必填',
          trigger: 'blur',
        }]">
          <el-select v-model="form.appSource" placeholder="请选择">
            <el-option v-for="item in appSourceList" :key="item.value" :label="item.name" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="文件：" required>
          <el-upload ref="upload" :limit="1" accept=".bpmn, .bar, .zip" :headers="upload.headers" :action="upload.url"
            :data="uploadData" :disabled="upload.isUploading" :on-progress="handleFileUploadProgress"
            :on-success="handleFileSuccess" :auto-upload="false" drag>
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">
              将文件拖到此处，或
              <em>点击上传</em>
            </div>
            <div class="el-upload__tip" style="color:red" slot="tip">提示：仅允许导入“bpmn”、“bar”或“zip”格式文件！</div>
          </el-upload>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import definitionApi from '@/api/activiti/definition'
import { baseUrl } from '@/utils/constants'
import { getToken } from '@/utils/auth'
import { pageParamNames } from '@/utils/constants'

export default {
  data() {
    return {
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      modelerUrl: '',
      modelVisible: false,
      // 遮罩层
      loading: false,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // VIEW表格数据
      tableData: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      src: '',
      // 查询参数
      queryParams: {
        key: null,
        name: null,
        appSource: null
      },
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: '',
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: { 'x-www-iap-assertion': getToken() },
        // 上传的地址
        url: baseUrl + 'activiti/processDefinition/uploadStreamAndDeployment'
      },
      form: {
        appSource: null
      },
      appSourceList: []
    }
  },
  computed: {
    uploadData() {
      return {
        appSource: this.form.appSource
      }
    }
  },
  created() {
    this.getAppSourceList()
    this.getList()
  },
  methods: {
    getAppSourceList() {
      definitionApi.appSourceList().then(res => {
        this.appSourceList = res.data
      })
    },
    /** 查询Definition列表 */
    getList() {
      this.loading = true
      this.queryParams.pageNum = this.tablePage.currentPage
      this.queryParams.pageSize = this.tablePage.size
      definitionApi.pageList(this.queryParams).then(res => {
        this.tableData = res.data.data
        this.loading = false
        // 设置后台返回的分页参数
        pageParamNames.forEach(name => this.$set(this.tablePage, name, parseInt(res.data[name])))
      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = {
        key: null,
        name: null,
        appSource: null
      },
        this.handleQuery()
    },
    handleImport() {
      this.upload.title = '上传模型图'
      this.upload.open = true
    },

    // 提交上传文件
    submitFileForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          this.$refs.upload.submit()
        }
      })
    },

    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false
      this.upload.isUploading = false
      this.$refs.upload.clearFiles()
      this.$message.success('导入成功')
      this.getList()
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$confirm('是否确认删除编号为"' + row.id + '"的数据项?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        definitionApi.deleteRow(row.deploymentId).then(res => {
          this.$nextTick(() => this.getList())
          this.$message.success('删除成功!')
        }).catch(err => {
        })
      }).catch(function () {
      })
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val
      this.getPageList()
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val
      this.getPageList()
    }
  }
}
</script>
