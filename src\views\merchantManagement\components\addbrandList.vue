<template>
  <div class="app-container">
    <el-row>
      <el-col :xs="24" :lg="18">
        <!-- 添加或修改弹窗 -->
        <el-form :ref="addOrUpdate ? 'addMarketDate' : 'updateMarketDate'" :rules="rules" :model="addOrUpdate ? addMarketDate : updateMarketDate" label-position="right" label-width="140px" style="width: 100%">
          <el-form-item label="登录账号：" prop="phone">
            <template>
              <el-col :xs="24" :span="18">
                <el-input v-if="addOrUpdate" v-model="addMarketDate.phone" />
                <el-input v-if="!addOrUpdate" v-model="updateMarketDate.phone" disabled />
              </el-col>
              <!-- <el-button
                v-if="!addOrUpdate"
                type="success"
                style="margin-left: 20px"
                @click="openLogin(updateMarketDate.phone, updateMarketDate.id)"
                >修改登录账号
              </el-button> -->
            </template>
          </el-form-item>
          <el-form-item label="超级俱乐部id：" prop="id" v-show="false">
            <el-col :xs="24" :span="18">
              <el-input v-if="addOrUpdate" v-model="addMarketDate.id" />
              <el-input v-if="!addOrUpdate" v-model="updateMarketDate.id" />
            </el-col>
          </el-form-item>
          <el-form-item label="登录超级品牌名称：" prop="merchantName">
            <el-col :xs="24" :span="18">
              <el-input v-if="addOrUpdate" v-model="addMarketDate.merchantName" placeholder="鼎校甄选超级品牌" />
              <el-input v-if="!addOrUpdate" v-model="updateMarketDate.merchantName" placeholder="鼎校甄选超级品牌" />
              <span>（例：鼎校甄选超级品牌 ）</span>
            </el-col>
          </el-form-item>
          <el-form-item label="总负责人：" prop="realName">
            <el-col :xs="24" :span="18">
              <el-input v-if="addOrUpdate" v-model="addMarketDate.realName" />
              <el-input v-if="!addOrUpdate" v-model="updateMarketDate.realName" />
            </el-col>
          </el-form-item>

          <el-form-item label="负责人身份证：" prop="idCard">
            <el-col :xs="24" :span="18">
              <el-input v-if="addOrUpdate" v-model="addMarketDate.idCard" />
              <el-input v-if="!addOrUpdate" v-model="updateMarketDate.idCard" />
            </el-col>
          </el-form-item>

          <el-form-item label="签约时间：" prop="signupDate">
            <el-col :xs="24" :span="18">
              <el-date-picker v-if="addOrUpdate" v-model="addMarketDate.signupDate" type="date" value-format="yyyy-MM-dd HH:mm:ss" placeholder="选择日期"></el-date-picker>
              <el-date-picker v-if="!addOrUpdate" v-model="updateMarketDate.signupDate" type="date" value-format="yyyy-MM-dd HH:mm:ss" placeholder="选择日期"></el-date-picker>
            </el-col>
          </el-form-item>
          <el-form-item label="推荐人编号：" prop="shareCode">
            <el-col :xs="24" :span="18">
              <el-input v-if="addOrUpdate" v-model="addMarketDate.shareCode" />
              <el-input v-else v-model="updateMarketDate.shareCode" />
            </el-col>
          </el-form-item>
          <!-- <el-form-item label="到期时间：" prop="expireDate">
            <el-col :xs="24" :span="18">
              <el-date-picker
                v-if="addOrUpdate"
                v-model="addMarketDate.expireDate"
                type="date"
                value-format="yyyy-MM-dd HH:mm:ss"
                @change="changeTime(addMarketDate.expireDate)"
                placeholder="选择日期">
              </el-date-picker>
              <el-date-picker
                v-if="!addOrUpdate"
                v-model="updateMarketDate.expireDate"
                type="date"
                value-format="yyyy-MM-dd HH:mm:ss"
                placeholder="选择日期">
              </el-date-picker>
            </el-col>
          </el-form-item> -->
          <el-form-item label="首充开店名额" prop="firstStoreOpeningNum">
            <el-col :span="16">
              <el-input v-if="addOrUpdate" v-model="addMarketDate.firstStoreOpeningNum" oninput="value=value.replace(/[^\d]/g,'')" disabled />
              <el-input v-if="!addOrUpdate" v-model="updateMarketDate.firstStoreOpeningNum" oninput="value=value.replace(/[^\d]/g,'')" disabled />
            </el-col>
            <el-col :span="5" :offset="1">个</el-col>
          </el-form-item>
          <el-form-item label="首充付款金额" prop="initialPaymentAmount">
            <el-col :span="16">
              <el-input v-if="addOrUpdate" v-model="addMarketDate.initialPaymentAmount" oninput="value=value.replace(/[^\d]/g,'')" disabled />
              <el-input v-if="!addOrUpdate" v-model="updateMarketDate.initialPaymentAmount" oninput="value=value.replace(/[^\d]/g,'')" disabled />
            </el-col>
            <el-col :span="5" :offset="1">元</el-col>
          </el-form-item>

          <el-form-item label="合同照片:" prop="contractPhotoPathList">
            <el-col :span="20">
              <el-upload ref="clearupload" v-loading="uploadLoading" list-type="picture-card" action="" element-loading-text="图片上传中" :limit="10" :on-exceed="justPictureNum" :file-list="fileDetailList" :http-request="uploadDetailHttp" :on-preview="handlePictureCardPreview" :on-remove="handleRemoveDetailContract">
                <i class="el-icon-plus" />
              </el-upload>
            </el-col>
            <el-col :xs="24" :span="4">(*支持多张)</el-col>
          </el-form-item>
          <!-- 11111111111111111111111111111111111111111111111111 -->
          <el-form-item label="证件照片:" prop="idPhotoPathList">
            <el-col :span="20">
              <el-upload ref="clearupload" v-loading="uploadLoadingIdCard" list-type="picture-card" action="" element-loading-text="图片上传中" :limit="10" :on-exceed="justPictureNumIdCard" :file-list="fileIdCard" :http-request="uploadIdCardDetailHttp" :on-preview="handlePictureCardPreview" :on-remove="handleRemoveDetailIdCard">
                <i class="el-icon-plus" />
              </el-upload>
            </el-col>
            <el-col :xs="24" :span="4">(*支持多张)</el-col>
          </el-form-item>
          <el-form-item ref="file_Rule" label="付款记录：" prop="paymentPhotoPathList">
            <el-col :xs="24" :span="20">
              <el-upload ref="clearupload" v-loading="uploadLoading4" multiple list-type="picture-card" action="" element-loading-text="图片上传中" :limit="8" :on-exceed="justPictureNumPayment" :file-list="fileDetailList4" :http-request="uploadDetailHttp4" :on-preview="handlePictureCardPreview" :on-remove="handleRemoveDetail4">
                <i class="el-icon-plus" />
              </el-upload>
            </el-col>
            <el-col :xs="24" :span="4">(*支持多张)</el-col>
          </el-form-item>
          <el-form-item label="门店照片:" prop="storePhotoPathList">
            <el-col :span="20">
              <el-upload ref="clearupload" v-loading="uploadLoadingShop" list-type="picture-card" action="" element-loading-text="图片上传中" :limit="10" :on-exceed="justPictureNumShop" :file-list="!addOrUpdate ? filelistShop : filelistShop" :http-request="uploadDetailHttpShop" :on-preview="handlePictureCardPreview" :on-remove="handleRemoveDetailShop">
                <i class="el-icon-plus" />
              </el-upload>
            </el-col>
            <el-col :xs="24" :span="4">(*支持多张)</el-col>
          </el-form-item>
          <el-form-item label="所在地区：" required>
            <el-col :xs="24" :span="18">
              <el-cascader style="width: 300px" :options="regionData" v-model="selectedOptions" :props="{ value: 'label' }"></el-cascader>
            </el-col>
          </el-form-item>
          <el-form-item label="地址：" prop="address">
            <el-col :xs="24" :span="18">
              <el-input v-if="addOrUpdate" v-model="address" v-show="false" />
              <el-input v-if="!addOrUpdate" v-model="address" v-show="false" />
              <el-input v-if="addOrUpdate" v-model="addMarketDate.address" />
              <el-input v-if="!addOrUpdate" v-model="updateMarketDate.address" />
            </el-col>
          </el-form-item>
          <el-form-item label="品牌简介：" prop="description">
            <el-col :xs="24" :span="18">
              <el-input type="textarea" resize="none" :rows="4" v-if="addOrUpdate" v-model="addMarketDate.description" />
              <el-input type="textarea" resize="none" :rows="4" v-if="!addOrUpdate" v-model="updateMarketDate.description" />
            </el-col>
          </el-form-item>
        </el-form>
      </el-col>
    </el-row>

    <div slot="footer" class="dialog-footer" style="margin: 30px 0 30px 140px">
      <el-button v-if="addOrUpdate" size="mini" type="primary" @click="addActiveFun('addMarketDate')">新增</el-button>
      <el-button v-if="!addOrUpdate && (updateMarketDate.isCheck == 0 || updateMarketDate.isCheck == -4 || updateMarketDate.isCheck == 2 || (merchantCode = 'A0001')) && !isResubmit" size="mini" type="primary" @click="updateActiveFun('updateMarketDate', false)">
        修改
      </el-button>
      <el-button v-if="isResubmit" size="mini" type="primary" @click="updateActiveFun('updateMarketDate', true)">重提</el-button>
      <el-button size="mini" @click="close">关闭</el-button>
    </div>
    <el-dialog :visible.sync="dialogUploadVisible" :close-on-click-modal="false">
      <img width="100%" :src="dialogImageUrl" alt />
    </el-dialog>
    <!-- 修改登陆账号弹窗 -->
    <el-dialog title="修改登录账号" :visible.sync="showLoginName" width="30%" :close-on-click-modal="false" @close="closeLoginname">
      <el-form :ref="'updateLoginName'" :rules="rulesLoginName" :model="updateLoginName" label-position="left" label-width="120px" style="width: 80%">
        <el-form-item label="原登录账号：" prop="oldName">
          <span>{{ updateLoginName.oldName }}</span>
        </el-form-item>
        <el-form-item label="id:" prop="id" v-show="false">
          <el-input v-model="updateLoginName.id" />
        </el-form-item>
        <el-form-item label="新登录账号：" prop="name">
          <el-input v-model="updateLoginName.phone" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="updateDealerLoginName('updateLoginName')">确定</el-button>
        <el-button size="mini" @click="closeLoginname">关闭</el-button>
      </div>
    </el-dialog>
    <!-- 审核弹框 -->
  </div>
</template>

<script>
import { regionData } from 'element-china-area-data';
import brandApi from '@/api/brands';
// import { fetchMerchantInfo, updateMerchantManage, fetchMealInfoByType } from '@/api/admin/commissionManagement';
// import axios from 'axios';
import { ossPrClient } from '@/api/alibaba';
import { isvalidPhone, idCard } from '@/utils/validate';
import checkPermission from '@/utils/permission';

export default {
  name: 'dealerAdd',
  data() {
    //手机号验证
    var validPhone = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入电话号码'));
      } else if (!isvalidPhone(value)) {
        callback(new Error('请输入正确的11位手机号码'));
      } else {
        callback();
      }
    };
    //身份证表达式
    var isIdCard = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入身份证号码'));
      } else if (!idCard(value)) {
        callback(new Error('请输入正确的18位身份证号'));
      } else {
        callback();
      }
    };
    const self = this;
    return {
      roleTag: '',
      showPrepaidMoney: false,
      forbidRankType: ['A', 'B', 'C'],
      // 地图搜索分页相关参数
      pageNum: 1,
      pageSize: 5,
      regionData,
      selectedOptions: [],
      result: [],
      currentResult: -1,
      isResubmit: false,
      disabled: true,
      tableLoading: false,
      showLoginName: false, //登录账号
      updateLoginName: {}, //修改账号
      rulesLoginName: {
        name: [
          {
            required: true,
            message: '必填',
            trigger: 'change'
          },
          {
            validator: validPhone,
            trigger: 'blur'
          }
        ]
      },
      id: 0,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      ADD: true,
      tableData: [],
      dataQuery: {},
      dialogVisible: false,
      updateMarketDate: {
        areaCoverRange: 500,
        merchantType: '3',
        initialPaymentAmount: 0,
        firstStoreOpeningNum: 0
      }, //修改数据对象
      addMarketDate: {
        areaCoverRange: 500,
        merchantType: '3',
        initialPaymentAmount: 0,
        firstStoreOpeningNum: 0
      }, //增加数据对象
      showLoginAccount: false,
      addOrUpdate: true,
      rules: {
        phone: [
          {
            required: true,
            message: '必填',
            trigger: 'blur'
          },
          {
            validator: validPhone,
            trigger: 'blur'
          }
        ],
        editPhone: [
          {
            required: true,
            message: '必填',
            trigger: 'blur'
          }
        ],
        merchantName: [
          {
            required: true,
            message: '必填',
            trigger: 'blur'
          }
        ],
        realName: [
          {
            required: true,
            message: '必填',
            trigger: 'blur'
          }
        ],
        idCard: [
          {
            required: true,
            message: '必填',
            trigger: 'blur'
          }
        ],
        signupDate: [
          {
            required: true,
            message: '必填',
            trigger: 'blur'
          }
        ],
        // expireDate: [
        //   {
        //     required: true,
        //     message: "必填",
        //     trigger: "blur",
        //   },
        // ],
        // merchantType: [
        //   {
        //     required: true,
        //     message: "必填",
        //     trigger: "change",
        //   },
        // ],
        contractPhotoPath: [
          {
            required: true,
            message: '必填',
            trigger: 'change'
          }
        ],
        idPhotoPath: [
          {
            required: true,
            message: '必填',
            trigger: 'change'
          }
        ],
        paymentPhotoPath: [
          {
            required: true,
            message: '必填',
            trigger: 'change'
          }
        ],

        province: [
          {
            required: true,
            message: '必填',
            trigger: 'change'
          }
        ],
        city: [
          {
            required: true,
            message: '必填',
            trigger: 'change'
          }
        ],
        area: [
          {
            required: true,
            message: '必填',
            trigger: 'change'
          }
        ],
        address: [
          {
            required: true,
            message: '必填',
            trigger: 'change'
          }
        ],
        isEnable: [
          {
            required: true,
            message: '必填',
            trigger: 'blur'
          }
        ],
        description: [
          {
            required: true,
            message: '必填',
            trigger: 'change'
          }
        ],
        contractPhotoPathList: [
          {
            required: true,
            message: '必填',
            trigger: 'change'
          }
        ],
        idPhotoPathList: [
          {
            required: true,
            message: '必填',
            trigger: 'change'
          }
        ]
      },
      value1: '',
      fileListPending: [], // 待处理已上传图片信息
      fileList: [], // 上传图片已有图片列表
      radio: '3',
      fileDetailListPending: [], // 待处理已上传图片信息
      fileDetailList: [], // 上传图片已有图片列表
      rankType: [], //超级俱乐部级别
      uploadLoading: false, // 上传图片加载按钮
      name: '',
      bankType: [], //银行集合
      dialogUploadVisible: false,
      dialogImageUrl: '',
      //证件照图片
      fileIdCard: [],
      uploadLoadingIdCard: false,
      //证件照结束
      uploadLoadingShop: false,
      //支付记录
      uploadLoading4: false,
      // address: "",
      filelistShop: [],
      //支付图片
      fileDetailList4: [],
      merchantCode: '',
      //门店照开始
      //门店照结束
      //地图开始
      slotWindow: {
        position: [121.5163285, 31.********]
      },
      currentWindow: {
        position: [0, 0],
        content: '',
        events: {},
        visible: false
      },
      addDealerShow: true,
      addDealerShow01: true,
      addDealerShow02: true,
      markers: [
        // [121.59996, 31.197646],
        // [121.40018, 31.197622],
        // [121.69991, 31.207649]
      ],
      //搜索结果标注
      markers2: [],
      windows: [],
      window: '',
      searchOption: {
        city: '全国',
        citylimit: false //是否限制城市内搜索
      },
      center: [117.26696, 31.87869],
      zoom: 12,
      lng: 0,
      lat: 0,
      address: '',
      province: '',
      city: '',
      district: '',
      loaded: false
      //地图结束
    };
  },
  created() {
    this.addOrUpdate = this.$route.query.addOrUpdate || JSON.parse(localStorage.getItem('addBrand'));
    console.log(this.addOrUpdate, 'bianjibianji', typeof this.addOrUpdate, this.$route.query.addOrUpdate);
    //编辑回显
    if (!this.addOrUpdate || this.addOrUpdate == 'false') {
      this.updateDealer();
      this.setTitle('超级品牌编辑');
    } else {
      this.setTitle('超级品牌新增');
    }
  },

  mounted() {
    ossPrClient();
    brandApi
      .fetchMealInfoByType({
        mealType: '4'
      })
      .then((res) => {
        console.log(res, 9090);
        if (this.addOrUpdate) {
          this.addMarketDate.firstStoreOpeningNum = res.data.data.invitationCodeNum;
          this.addMarketDate.initialPaymentAmount = res.data.data.mealPrice;
        } else {
          this.updateMarketDate.firstStoreOpeningNum = res.data.data.invitationCodeNum;
          this.updateMarketDate.initialPaymentAmount = res.data.data.mealPrice;
        }
      })
      .catch((err) => {
        console.log(err, 999);
      });
  },
  methods: {
    checkPermission,
    changePaymentIsComplete(paymentIsComplete) {
      console.log('changePaymentIsComplete', paymentIsComplete);
      if (paymentIsComplete == 0) {
        this.showPrepaidMoney = true;
      } else {
        this.showPrepaidMoney = false;
      }
    }, // 动态设置标签页标题
    setTitle(title) {
      let i = 0;
      let visitedViews = this.$store.getters.visitedViews;
      visitedViews.forEach((route, index) => {
        if (this.$route.path == route.path) {
          i = index;
        }
      });
      console.log(visitedViews, '9999title', title);
      this.$route.meta.title = title;

      visitedViews[i].title = title;
    },
    // 状态改变事件
    changeRadio(radio) {
      if (radio == '3') {
        this.addMarketDate.merchantType = '3';
      } else {
        this.addMarketDate.merchantType = '2';
      }
    },
    changeupDate() {
      if (radio == '3') {
        this.updateMarketDate.merchantType = '3';
      } else {
        this.updateMarketDate.merchantType = '2';
      }
    },
    //编辑回显
    updateDealer() {
      const that = this;
      console.log(2222);
      that.addOrUpdate = JSON.parse(window.localStorage.getItem('addBrand'));
      that.id = window.localStorage.getItem('brandId');
      brandApi.fetchMerchantInfo({ id: this.id }).then((res) => {
        console.log(res, 123);
        that.updateMarketDate = JSON.parse(JSON.stringify(res.data));
        console.log(res, 123, that.updateMarketDate);
        that.updateMarketDate.contractPhotoPathList = res.data.contractPhotoPath;
        that.updateMarketDate.contractPhotoPath = null;
        that.updateMarketDate.idPhotoPathList = res.data.idPhotoPath;
        that.updateMarketDate.idPhotoPath = null;
        that.updateMarketDate.storePhotoPathList = res.data.storePhotoPath;
        that.updateMarketDate.storePhotoPath = null;
        that.updateMarketDate.paymentPhotoPathList = res.data.paymentPhotoPath;
        that.updateMarketDate.paymentPhotoPath = null;
        that.selectedOptions = [that.updateMarketDate.province, that.updateMarketDate.city, that.updateMarketDate.area];
        if (that.selectedOptions[0] == that.selectedOptions[1]) {
          that.selectedOptions[1] = '市辖区';
        }
        if (that.updateMarketDate.contractPhotoPathList !== null && that.updateMarketDate.contractPhotoPathList.length >= 1) {
          // this.fileDetailList = that.updateMarketDate.contractPhotoPathList;
          for (let i = 0; i < that.updateMarketDate.contractPhotoPathList.length; i++) {
            that.fileDetailList.push({
              url: that.updateMarketDate.contractPhotoPathList[i]
            });
          }
        } else {
          that.fileDetailList = [];
        }
        if (that.updateMarketDate.idPhotoPathList !== null && that.updateMarketDate.idPhotoPathList.length >= 1) {
          for (let i = 0; i < that.updateMarketDate.idPhotoPathList.length; i++) {
            // that.fileDetailList.push({
            //     url: that.aliUrl + res.data.idCardPhoto[i]
            //   })
            that.fileIdCard.push({
              url: that.updateMarketDate.idPhotoPathList[i]
            });
          }
        } else {
          that.fileIdCard = [];
        }
        /******/
        if (that.updateMarketDate.paymentPhotoPathList !== null && that.updateMarketDate.paymentPhotoPathList.length >= 1) {
          for (let i = 0; i < that.updateMarketDate.paymentPhotoPathList.length; i++) {
            that.fileDetailList4.push({
              url: that.updateMarketDate.paymentPhotoPathList[i]
            });
          }
        } else {
          that.fileDetailList4 = [];
        }

        if (that.updateMarketDate.storePhotoPathList !== null && that.updateMarketDate.storePhotoPathList.length >= 1) {
          for (let i = 0; i < that.updateMarketDate.storePhotoPathList.length; i++) {
            that.filelistShop.push({
              url: that.updateMarketDate.storePhotoPathList[i]
            });
          }
        }
      });
    },
    //TODO
    close() {
      const that = this;
      // that.$router.push({
      //   path: "/merchantManagement/dealerList",
      // });
      // 关闭当前标签页
      that.$store.dispatch('delVisitedViews', this.$route);
      that.$router.go(-1);

      that.addMarketDate = {};
      that.updateMarketDate = {};
      that.addOrUpdate = true;
      that.$refs.clearupload.clearFiles();
    },

    //新增操作
    clickAdd() {
      this.$refs.clearupload.clearFiles();
      this.dialogVisible = true;
    },
    // 打开修改登陆账号
    openLogin(name, id) {
      this.showLoginName = true;
      this.updateLoginName.id = id;
      this.updateLoginName.oldName = name;
    },
    //改变状态
    change() { },
    changeTime(val) {
      console.log(val);
    },
    //修改账号
    updateDealerLoginName(ele) {
      const that = this;
      that.$refs[ele].validate((valid) => {
        if (valid) {
          marketApi.markertUpdateLogin(that.updateLoginName).then(() => {
            that.$nextTick(() => that.fetchData());
            that.updateMarketDate.phone = this.updateLoginName.phone;
            that.updateLoginName.phone = '';
            that.showLoginName = false;
            that.$message.success('修改登录账号成功');
          });
        }
      });
    },
    //修改账号关闭
    closeLoginname() {
      this.showLoginName = false;
    },
    getRoleTag() {
      // schoolApi.getCurrentAdmin().then((res) => {
      //   console.log(res.data.merchantCode + 'wyy')
      //   this.roleTag = res.data.roleTag
      //   this.merchantCode = res.data.merchantCode
      // })
    },
    //新增操作超级俱乐部
    addActiveFun(ele) {
      const that = this;
      if (!that.addMarketDate.phone) {
        that.$message.error('登录账号不能为空');
        return false;
      }
      if (!that.addMarketDate.merchantName) {
        that.$message.error('超级品牌名称不能为空');
        return false;
      }
      if (!that.addMarketDate.idCard) {
        that.$message.error('负责人身份证不能为空');
        return false;
      }
      // if (that.fileDetailList4.length <= 0) {
      //   that.$message.error("付款照片不能为空");
      //   return false;
      // }
      if (that.fileDetailList.length <= 0) {
        that.$message.error('合同照片不能为空');
        return false;
      }
      if (that.fileIdCard.length <= 0) {
        that.$message.error('证件照片不能为空');
        return false;
      }
      that.addMarketDate.province = this.selectedOptions[0];
      if (this.selectedOptions[1] == '市辖区') {
        that.addMarketDate.city = this.selectedOptions[0];
      } else {
        that.addMarketDate.city = this.selectedOptions[1];
      }
      this.addMarketDate.area = this.selectedOptions[2];
      if (!that.addMarketDate.city || !that.addMarketDate.province) {
        that.$message.error('省市区不能为空,请选择');
        return false;
      }
      if (that.addMarketDate.detailedAddress == '') {
        that.$message.error('详细地址不能为空');
        return false;
      }

      // that.addMarketDate.contractPhotoPathList = that.fileDetailList;

      that.addMarketDate.contractPhotoPathList = that.fileDetailList.map((e) => {
        if (e) {
          return (e = that.aliUrl + e);
        }
      });
      that.addMarketDate.idPhotoPathList = that.fileIdCard.map((e) => {
        if (e) {
          return (e = that.aliUrl + e);
        }
      });
      that.addMarketDate.storePhotoPathList = that.filelistShop.map((e) => {
        if (e) {
          return (e = that.aliUrl + e);
        }
      });
      that.addMarketDate.paymentPhotoPathList = that.fileDetailList4.map((e) => {
        if (e) {
          return (e = that.aliUrl + e);
        }
      });

      that.$refs[ele].validate((valid) => {
        // 表单验证
        if (valid) {
          const loading = this.$loading({
            lock: true,
            text: '新增超级品牌',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          });
          brandApi
            .updateMerchantManage(that.addMarketDate)
            .then((res) => {
              if (res.success) {
                that.dialogVisible = false;
                loading.close();
                that.$router.push({
                  name: 'brand'
                });
              } else {
                loading.close();
              }
              // that.dialogVisible = false;

              // this.$route.back();
            })
            .catch(() => {
              loading.close();
            });
          // dealerListApi.addDealerList(that.addMarketDate).then(async(res) => {
          //   // if (res.data && res.data < 2) {
          //   //   if (res.data > 0) {
          //   //     await this.$confirm('该俱乐部有合伙人账户，您填写的上级编号与所属俱乐部编号都不会生效，是否继续？')
          //   //   } else {
          //   //     await this.$confirm('该俱乐部没有合伙人账户，是否继续？',{type: 'warning'})
          //   //   }
          //   // }
          //   const bvId = await dealerListApi.addDealerList(that.addMarketDate,true)
          //   that.dialogVisible = false
          //   loading.close()
          //   that.addMarketDate = {}
          //   that.fileIdCard = []
          //   that.fileDetailList = []
          //   that.filelistShop = []
          //   that.fileDeatiList4 = []
          //   const data = await dealerListApi.startAndTakeUserTaskByAddDealer({
          //     approvalType: 'agree',
          //     masterData: {
          //       open_type: 'addOperations',
          //       relation_id: bvId.data,
          //       create_time: this.formatDate(new Date())
          //     }
          //   })
          //   that.$message.success('新增俱乐部成功')
          //   that.$router.push({
          //     path: '/CommissionManagement/brand'
          //   })
          //   that.$message.success('俱乐部审批流程开启~')

          // })
          //   .catch((err) => {
          //     loading.close()
          //   })
        } else {
          console.log('error submit!!');
          //loading.close();
          return false;
        }
      });
    },
    formatDate(current_datetime) {
      return (
        current_datetime.getFullYear() +
        '-' +
        (current_datetime.getMonth() + 1) +
        '-' +
        current_datetime.getDate() +
        ' ' +
        current_datetime.getHours() +
        ':' +
        current_datetime.getMinutes() +
        ':' +
        current_datetime.getSeconds()
      );
    },
    //修改操作
    updateActiveFun(ele, isResubmit) {
      const that = this;
      if (that.fileDetailList.length <= 0) {
        that.$message.error('合同照片不能为空');
        return false;
      }
      if (that.fileIdCard.length <= 0) {
        that.$message.error('证件照片不能为空');
        return false;
      }
      this.updateMarketDate.merchantType = '3';
      that.updateMarketDate.province = this.selectedOptions[0];
      if (this.selectedOptions[1] == '市辖区') {
        that.updateMarketDate.city = this.selectedOptions[0];
      } else {
        that.updateMarketDate.city = this.selectedOptions[1];
      }
      this.updateMarketDate.area = this.selectedOptions[2];
      if (that.updateMarketDate.city == '') {
        that.$message.error('市不能为空');
        return false;
      }
      //付款记录照片
      that.updateMarketDate.paymentPhotoPathList = that.fileDetailList4.map((e) => {
        if (e.url) {
          return (e = e.url);
        } else {
          return e;
        }
      });
      that.updateMarketDate.contractPhotoPathList = that.fileDetailList.map((e) => {
        if (e.url) {
          return (e = e.url);
        } else {
          return e;
        }
      });
      that.updateMarketDate.idPhotoPathList = that.fileIdCard.map((e) => {
        if (e.url) {
          return (e = e.url);
        } else {
          return e;
        }
      });
      that.updateMarketDate.storePhotoPathList = that.filelistShop.map((e) => {
        if (e.url) {
          return (e = e.url);
        } else {
          return e;
        }
      });
      if (this.isResubmit) {
        that.rules.name = [];
      }
      that.$refs[ele].validate((valid) => {
        // 表单验证
        if (valid) {
          const loading = this.$loading({
            lock: true,
            text: '修改超级品牌',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          });
          brandApi
            .updateMerchantManage(that.updateMarketDate)
            .then(
              (res) => {
                console.log(res);
                if (res.success) {
                  that.dialogVisible = false;
                  loading.close();
                  that.$router.push({
                    name: 'brand'
                  });
                } else {
                  loading.close();
                }
              },
              (s) => {
                loading.close();
              }
            )
            .catch((err) => {
              loading.close();
            });
        } else {
          console.log('error submit!!');
          loading.close();
          return false;
        }
      });
    },

    //上传图片
    //支付记录照片上传
    uploadDetailHttp4({ file }) {
      this.uploadLoading4 = true;
      const that = this;
      const fileName = 'manage/' + Date.parse(new Date());
      that.$nextTick(function () {
        ossPrClient()
          .put(fileName, file)
          .then(({ res, url, name }) => {
            if (res && res.status === 200) {
              console.log(file, `阿里云OSS上传图片成功回调1`, res, url, name);
              if (!that.addOrUpdate) {
                that.fileDetailList4.push({
                  uid: file.uid,
                  url: url
                });
              } else {
                // 新增上传图片
                that.fileDetailList4.push(name);
              }
              // that.fileDetailList4.push(url);
              that.$nextTick(() => {
                that.uploadLoading4 = false;
              });
            }
          })
          .catch((err) => {
            that.$message.error('上传图片失败请检查网络或者刷新页面');
            console.log(`阿里云OSS上传图片失败回调`, err);
          });
      });
    },

    justPictureNumPayment(file, fileList) {
      this.$message.warning(`当前限制选择8个文件`);
      console.log('上传照片', file, fileList);
    },
    // 上传合同照片请求
    uploadDetailHttp({ file }) {
      console.log('上传合同照片', file);
      this.uploadLoading = true;
      const that = this;
      const fileName = 'manage/' + Date.parse(new Date());
      that.$nextTick(function () {
        ossPrClient()
          .put(fileName, file)
          .then(({ res, url, name }) => {
            if (res && res.status === 200) {
              console.log(`阿里云OSS上传图片成功回调1`, res, url, name, that.addOrUpdate);
              if (!that.addOrUpdate) {
                that.fileDetailList.push({
                  uid: file.uid,
                  url: url
                });
              } else {
                // 新增上传图片
                that.fileDetailList.push(name);
              }
              // that.fileDetailList.push(url);
              that.$nextTick(() => {
                that.uploadLoading = false;
              });
              console.log(`阿里云OSS上传图片成功回调12`, res, url, name, that.fileDetailList);
            }
          })
          .catch((err) => {
            that.$message.error('上传图片失败请检查网络或者刷新页面');
            console.log(`阿里云OSS上传图片失败回调`, err);
          });
      });
    },
    // 上传图片预览
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url;
      this.dialogUploadVisible = true;
    },
    // 上传图片数量超限
    justPictureNum(file, fileList) {
      this.$message.warning(`当前限制选择10个文件`);
    },
    //移除图片
    // 删除合同照片
    handleRemoveDetailContract(file, fileList) {
      console.log('删除合同照片', file, fileList);
      const that = this;
      if (!that.addOrUpdate) {
        that.fileDetailList = fileList;
      } else {
        for (let a = 0; a < that.fileDetailList.length; a++) {
          if (that.fileDetailList[a].substring(7, 17) == parseInt(file.uid / 1000)) {
            that.fileDetailList.splice(a, 1);
          }
        }
      }
    },

    //证件照片开始
    //证件照上传
    uploadIdCardDetailHttp({ file }) {
      this.uploadLoadingIdCard = true;
      const that = this;
      const fileName = 'manage/' + Date.parse(new Date());
      that.$nextTick(function () {
        ossPrClient()
          .put(fileName, file)
          .then(({ res, url, name }) => {
            if (res && res.status === 200) {
              console.log(`阿里云OSS上传图片成功回调1`, res, url, name);
              if (!that.addOrUpdate) {
                that.fileIdCard.push({
                  uid: file.uid,
                  url: url
                });
              } else {
                // 新增上传图片
                that.fileIdCard.push(name);
              }
              // that.fileIdCard.push(url);
              that.$nextTick(() => {
                that.uploadLoadingIdCard = false;
              });
            }
          })
          .catch((err) => {
            that.$message.error('上传图片失败请检查网络或者刷新页面');
            console.log(`阿里云OSS上传图片失败回调`, err);
          });
      });
    },
    //照片限制
    // 上传图片数量超限
    justPictureNumIdCard(file, fileList) {
      this.$message.warning(`当前限制选择10个文件`);
    },
    handleRemoveDetailIdCard(file, fileList) {
      const that = this;
      if (!that.addOrUpdate) {
        that.fileIdCard = fileList;
      } else {
        for (let a = 0; a < that.fileIdCard.length; a++) {
          if (that.fileIdCard[a].substring(7, 17) == parseInt(file.uid / 1000)) {
            that.fileIdCard.splice(a, 1);
          }
        }
      }
    },
    //支付记录照片删除
    handleRemoveDetail4(file, fileList) {
      const that = this;
      if (!that.addOrUpdate) {
        that.fileDetailList4 = fileList;
      } else {
        for (let a = 0; a < that.fileDetailList4.length; a++) {
          if (that.fileDetailList4[a].substring(7, 17) == parseInt(file.uid / 1000)) {
            that.fileDetailList4.splice(a, 1);
          }
        }
      }
    },
    //门店照开始
    uploadDetailHttpShop({ file }) {
      this.uploadLoadingShop = true;
      const that = this;
      const fileName = 'manage/' + Date.parse(new Date());
      that.$nextTick(function () {
        ossPrClient()
          .put(fileName, file)
          .then(({ res, url, name }) => {
            if (res && res.status === 200) {
              console.log(`阿里云OSS上传图片成功回调1`, res, url, name);
              if (!that.addOrUpdate) {
                that.filelistShop.push({
                  uid: file.uid,
                  url: url
                });
              } else {
                // 新增上传图片
                that.filelistShop.push(name);
              }
              // that.filelistShop.push(url);
              that.$nextTick(() => {
                that.uploadLoadingShop = false;
              });
            }
          })
          .catch((err) => {
            that.$message.error('上传图片失败请检查网络或者刷新页面');
            console.log(`阿里云OSS上传图片失败回调`, err);
          });
      });
    },
    //照片限制
    // 上传图片数量超限
    justPictureNumShop(file, fileList) {
      console.log('门店照片数量超限', file, fileList);
      this.$message.warning(`当前限制选择10个文件`);
    },
    handleRemoveDetailShop(file, fileList) {
      const that = this;
      if (!that.addOrUpdate) {
        that.filelistShop = fileList;
      } else {
        for (let a = 0; a < that.filelistShop.length; a++) {
          if (that.filelistShop[a].substring(7, 17) == parseInt(file.uid / 1000)) {
            that.filelistShop.splice(a, 1);
          }
        }
      }
    }
    //门店照结束
  }
};
</script>

<style>
.map-box {
  position: relative;
}

.search-box {
  position: absolute !important;
  top: 10px;
  left: 10px;
}

.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.red {
  color: red;
}

.green {
  color: green;
}

.blue {
  color: blue;
}

.prompt {
  padding: 10px;
}

.result {
  position: absolute;
  top: 0;
  left: 100%;
  width: 300px;
  /* height: 450px; */
  margin-left: 10px;
  background-color: #ffffff;
  border: 1px solid silver;
}

.result-list {
  display: flex;
  align-items: center;
  /* margin-bottom: 10px; */
  line-height: 1.6;
  overflow: hidden;
  cursor: pointer;
}

.result label {
  display: block;
  width: 19px;
  height: 33px;
  margin-right: 10px;
  line-height: 20px;
  text-align: center;
  font-size: 12px;
  color: #ffffff;
  background: url('https://webapi.amap.com/theme/v1.3/markers/n/mark_bs.png') no-repeat center/cover;
}

.result-list.active label {
  background: url('http://webapi.amap.com/theme/v1.3/markers/n/mark_r.png') no-repeat center/cover;
}

.list-right {
  flex: 1;
}

.result .name {
  font-size: 14px;
  color: #565656;
}

.result .address {
  color: #999;
}

.search-table {
  height: 380px;
  margin-bottom: 10px !important;
}

.search-table th {
  display: none;
}

.search-table td {
  padding: 5px 0 !important;
  border-bottom: none;
}

.el-vue-search-box-container {
  width: 90% !important;
}

.el-date-editor.el-input {
  width: 100% !important;
}

@media screen and (max-width: 767px) {
  .app-container {
    /* padding: 20px 10px; */
  }

  .result {
    display: none;
  }
}
</style>
