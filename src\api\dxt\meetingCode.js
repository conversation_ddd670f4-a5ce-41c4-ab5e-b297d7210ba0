/**
 * 物料码相关接口
 */
import request from '@/utils/request'

export default {
  // 新增
  meetingCodeAdd() {
    return request({
      url: '/dxt/web/meeting/code/add',
      method: 'POST'
    })
  },
  // 修改回显
  echoMeeting(id) {
    return request({
      url: '/dxt/meeting/get/detail/' + id,
      method: 'GET'
    })
  },
  // 编辑
  updateMeetingData(data) {
    return request({
      url: '/dxt/meeting/update',
      method: 'PUT',
      data
    })
  },
  deleteMeeting(id){
    return request({
      url:'/dxt/meeting/delete/'+id,
      method: 'DELETE'
    })
  },
  // 绑定退款码
  bingCode(itemCode,refundCode) {
    return request({
      url: '/dxt/web/meeting/code/bind/refund/'+itemCode+'/'+refundCode,
      method: 'GET'
    })
  },
  getMeetingList(){
    return request({
      url: '/dxt/web/meeting/code/get/list',
      method: 'GET'
    })
  },
  // 分页查询
  // meetingCodeList(pageNum, pageSize, data) {
  //   return request({
  //     url: '/dxt/meeting/list/' + pageNum + '/' + pageSize,
  //     method: 'GET',
  //     params: data
  //   })
  // },

  meetingCodeList(pageNum, pageSize) {
    return request({
      url: '/dxt/web/meeting/code/list/' + pageNum + '/' + pageSize,
      method: 'GET'
    })
  },
}
