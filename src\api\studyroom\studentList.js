import request from '@/utils/request'

export const getSuggestList = (pageNum,pageSize,data) => {
  return request({
    url: `/znyy/suggest/query/page/${pageNum}/${pageSize}`,
    method: 'get',
    params: data
  })
}

// 学员列表
export const getStudentList = (data) => {
  return request({
    url: '/deliver/web/common/getZnyyStudentDeliverVo',
    method: 'get',
    params: data
  })
}

//获取排课支付数据，用来跳转支付模块付款
export const getPlanCoursePayInfo = (planId) => {
  return request({
    url: '/deliver/web/common/getPlanCoursePayInfo',
    method: 'get',
    params: {planId: planId}
  })
}

//排课计划发送
export const sendPlanCourse = (planId, sendType) => {
  return request({
    url: '/deliver/web/learnManager/sendPlanCourse',
    method: 'get',
    params: {planId: planId, sendType: sendType}
  })
}

//根据排课信息获取联系方式
export const getContactInfo = (planId, type) => {
  return request({
    url: '/deliver/web/common/getContactInfo',
    method: 'get',
    params: {planId: planId, type: type}
  })
}

// 课程表列表
export const getTimetable = (data) => {
  return request({
    url: '/deliver/web/learnManager/getTimetableByPlanId',
    method: 'GET',
    params: data
  })
}


// 交付课时充值流水
export const getDeliverHours = (pageNum, pageSize,data) => {
  data.pageNum = pageNum;
  data.pageSize = pageSize;
  return request({
    url: '/deliver/web/common/getStuDeliverHours',
    method: 'GET',
    params: data
  })
}

