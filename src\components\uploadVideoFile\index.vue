<template>
  <div class="hello">
    <div>
      <input class="upload" :disabled="disabled" @change="doUpload" ref="inputer" type="file" multiple="false" />
    </div>
    <div>
      <el-table :data="tableData" border style="width: 100%">
        <el-table-column prop="id" label="ID" align="center"></el-table-column>
        <el-table-column prop="fileName" label="文件名" align="center"></el-table-column>
        <el-table-column label="进度" align="center" prop="progress">
          <template slot-scope="scope">
            <div>
              <el-progress :text-inside="true" :stroke-width="15" :percentage="scope.row.progress"></el-progress>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="deleteVid" label="操作" align="center">
          <template slot-scope="scope">
            <el-button type="text" size="small" :disabled="!isProgressComplete" @click="videoClick">保存</el-button>
            <el-button type="text" size="small" :disabled="disabled" @click="remove(scope.row.id, scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>
<script>
  import md5 from 'js-md5';
  import PlvVideoUpload from '@polyv/vod-upload-js-sdk';
  import { getUploadTokenAPI, detailAPI, saveAPI } from '@/api/uploadVideoFile';
  export default {
    name: 'demo',
    props: ['videoList', 'disabled', 'progress'],
    data() {
      return {
        videoUpload: null, // 视频上传实例
        // userid: "1723c88563", //从点播后台查看获取
        // secretkey: "Jkk4ml1Of8", //从点播后台查看获取
        // writeToken: "94064a19-4d28-4ce9-aa05-96d476016215", //从点播后台查看获取
        userid: '', // 接口动态获取
        secretkey: '', // 接口动态获取
        writeToken: '', // 接口动态获取
        ptime: '', // 当前时间戳
        tableData: [], //表格数据
        vidId: '', // 拼接保利威返回的视频链接
        first_image: '', // 视频图片
        vid: '',
        saveButton: false,
        isProgressComplete: false
      };
    },

    created() {
      this.tableData = this.videoList;
      this.videoUpload = new PlvVideoUpload({
        region: 'line1', // (可选)上传线路, 默认line1
        events: {
          Error: (err) => {
            // 错误事件回调
            console.log(err);
            let errMag = `(错误代码：${err.code})${err.message}`;
            this.$alert(errMag, '标题名称', {
              confirmButtonText: '确定',
              type: 'error'
            });
            this.$refs.inputer.value = '';
          },
          UploadComplete: () => {
            // 全部上传任务完成回调
            // console.info("上传结束：", this.videoUpload);
            this.$emit('videoSucceed', this.tableData);
            this.$message({
              message: '视频上传完成',
              type: 'success'
            });
          }
        }
      });
      // 获取 token
      // this.getToken()
    },
    mounted() {
      this.autoUpdateUserData('null', this.videoUpload);
    },

    methods: {
      // 删除视频
      remove(uploaderid, info) {
        // 单个删除
        this.videoUpload.removeFile();
        this.videoUpload.clearAll();
        this.$refs.inputer.value = '';
        this.tableData = [];
        // this.tableData = this.tableData.filter((item) => item.id !== uploaderid);
        // this.$emit('videoRemove', info)
        this.$emit('updatetableData', this.tableData.length);
      },
      // 上传视频保存
      videoClick() {
        saveAPI({ vid: this.vidId })
          .then((res) => {
            this.$message.success('保存视频信息成功');
            this.saveButton = true;
            this.$emit('saveButton', this.saveButton);
          })
          .then(() => {
            detailAPI(this.vidId).then((res) => {
              this.first_image = res.data.firstImg;
              this.vid = res.data.vid;
              this.$emit('firstImage', this.first_image);
              this.$emit('vidData', this.vid);
            });
          });
      },
      // 获取 token
      getToken() {
        return new Promise((resolve, reject) => {
          getUploadTokenAPI()
            .then((res) => {
              this.ptime = res.data.ptime;
              this.userid = res.data.userId;
              this.secretkey = res.data.secretKey;
              this.writeToken = res.data.writeToken;
              resolve();
            })
            .catch((error) => {
              reject(error);
            });
        });
      },
      start(uploaderid) {
        // 单个上传
        this.videoUpload.resumeFile(uploaderid);
      },
      doUpload() {
        // 检查是否已有文件在上传
        if (this.tableData.length >= 1) {
          this.$message.warning('只能上传一个视频文件');
          this.$refs.inputer.value = '';
          return;
        }
        // 选择文件
        let inputDOM = this.$refs.inputer; // 通过 DOM 取文件数据
        let data = Object.values(inputDOM.files);

        data.forEach((file, index, arr) => {
          let fileSetting = {
            // 文件上传相关信息设置
            title: file.name, // 标题
            desc: 'jssdk 插件上传', // 描述
            cataid: process.env.VUE_APP_BASE_CATAID, // 上传分类目录 ID
            tag: '', // 标签
            luping: 0, // 是否开启视频课件优化处理，对于上传录屏类视频清晰度有所优化：0 为不开启，1 为开启
            keepsource: 0, // 是否源文件播放（不对视频进行编码）：0 为编码，1 为不编码
            state: '' // 用户自定义信息，如果提交了该字段，会在服务端上传完成回调时透传返回。
          };
          let uploadManager = this.videoUpload.addFile(
            file, // file 为待上传的文件对象
            {
              FileStarted: this.onFileStarted, // 文件开始上传回调
              FileProgress: this.onFileProgress, // 文件上传中回调
              FileSucceed: this.onFileSucceed, // 文件上传成功回调
              FileFailed: this.onFileFailed, // 文件上传失败回调
              FileStopped: this.onFileStopped // 文件上传停止回调
            },
            fileSetting
          );

          this.addTableData(uploadManager);
        });
      },
      onFileStarted(data) {
        this.tableData.filter((item) => item.id === data.uploaderid)[0].progress = 0;
      },
      onFileProgress(data) {
        let p = parseInt(data.progress * 100); // 上传的进度条
        // console.log("文件上传中: ", data);
        this.tableData.filter((item) => item.id === data.uploaderid)[0].progress = p;
      },
      onFileSucceed(data) {
        this.tableData.forEach((item, index) => {
          if (item.fileName == data.fileData.title) {
            item.id = data.fileData.vid;
            item.progress = 100;
            this.$set(this.tableData[index], 'id', data.fileData.vid);
            item.success = true;
          }
        });
        this.isProgressComplete = true;
        this.vidId = data.fileData.vid;
      },
      onFileFailed(data) {
        // console.log("文件上传失败: ", data);
      },
      onFileStopped(data) {
        // console.log("文件上传停止: ", data);
      },
      addTableData(data) {
        // 增加表格数据
        let obj = {
          id: data.id,
          fileName: data.fileData.title,
          size: data.fileData.size,
          progress: 0,
          success: false
        };
        this.tableData.push(obj);
        this.start(data.id);
      },
      autoUpdateUserData(timer, videoUpload) {
        // 启动获取用户信息
        this.getUserData(videoUpload);
        if (timer) {
          clearTimeout(timer);
          timer = null;
        }
        timer = setTimeout(() => {
          this.autoUpdateUserData(timer, videoUpload);
        }, 3 * 50 * 1000);
      },
      async getUserData() {
        await this.getToken();
        // 获取用户详细信息
        let userData = {
          sign: this.getSignData().sign,
          hash: this.getSignData().hash,
          userid: this.userid,
          ptime: this.ptime
        };
        this.videoUpload.updateUserData(userData);
      },
      getSignData() {
        // 加密信息参数
        let hash = md5(this.ptime + this.writeToken);
        let sign = md5(this.secretkey + this.ptime);
        return {
          hash: hash,
          sign: sign
        };
      },
      transformSize(bytes) {
        // 文件大小转换
        const bt = parseInt(bytes);
        let result;
        if (bt === 0) {
          result = '0B';
        } else {
          const k = 1024;
          const sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
          const i = Math.floor(Math.log(bt) / Math.log(k));
          if (typeof i !== 'number') {
            result = '-';
          } else {
            result = (bt / Math.pow(k, i)).toFixed(2) + sizes[i];
          }
        }
        return result;
      }
    }
  };
</script>

<style scoped></style>
