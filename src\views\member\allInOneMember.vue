<template>
  <div class="app-container">
    <el-form :inline="true" class="container-card" label-width="110px" label-position="left">
      <el-row>
        <el-col :span="8" :xs="24">
          <el-form-item label="鼎校编号：">
            <el-input id="userCode" v-model="dataQuery.userCode" name="id" placeholder="鼎校编号" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="通联编号：">
            <el-input id="ystCode" v-model="dataQuery.ystCode" name="id" placeholder="通联编号" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="用户姓名：">
            <el-input id="realName" v-model="dataQuery.realName" name="id" placeholder="请输入用户姓名" clearable />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8" :xs="24">
          <el-form-item label="手机号：">
            <el-input id="mobile" v-model="dataQuery.mobile" name="id" placeholder="请输入手机号" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="实名认证：">
            <el-select v-model="dataQuery.certStatus" placeholder="全部" style="width: 185px;">
              <el-option
                v-for="(item, index) in [{ value: 0, label: '未认证' }, { value: 1, label: '已认证' }, { value: 2, label: '认证中' }]"
                :key="index" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="是否绑手机：">
            <el-select v-model="dataQuery.bindPhoneStatus" placeholder="全部" style="width: 185px;">
              <el-option v-for="(item, index) in [{ value: 1, label: '是' }, { value: 0, label: '否' }]" :key="index"
                :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8" :xs="24">
          <el-form-item label="是否绑卡：">
            <el-select v-model="dataQuery.bindCardStatus" placeholder="全部" style="width: 185px;">
              <el-option v-for="(item, index) in [{ value: 1, label: '是' }, { value: 0, label: '否' }]" :key="index"
                :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="鼎校用户类型：">
            <el-select v-model="dataQuery.userRole" placeholder="全部" style="width: 185px;">
              <el-option v-for="(item, index) in [{ value: 'Merchant', label: '商户' }, { value: 'Member', label: '会员' }]"
                :key="index" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="认证类型：">
            <el-select v-model="dataQuery.certType" placeholder="全部" style="width: 185px;">
              <el-option v-for="(item, index) in [{ value: 0, label: '个人' }, { value: 1, label: '企业' }]" :key="index"
                :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="16" :xs="24">
          <el-form-item label="状态：">
            <el-select v-model="dataQuery.isEnable" placeholder="请选择" style="width: 185px;">
              <el-option v-for="(item, index) in [{ value: 1, label: '开通' }, { value: 0, label: '暂停' }]" :key="index"
                :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="fetchData()">搜索</el-button>
          </el-form-item>
        </el-col>
      </el-row>

    </el-form>
    <el-table class="common-table" v-loading="tableLoading" :data="tableData" style="width: 100%;margin-bottom: 20px;"
      row-key="id" border default-expand-all :tree-props="{ list: 'children', hasChildren: 'true' }">
      <el-table-column prop="userCode" label="鼎校编号"></el-table-column>
      <el-table-column prop="ysTCode" label="通联编号" width="190"></el-table-column>
      <el-table-column label="操作" align="center" width="150">
        <template slot-scope="scope">
          <el-button type="primary" icon="el-icon-view" @click="clickDetail(scope.row.id)" size="mini">查看</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="realName" label="用户姓名"></el-table-column>
      <el-table-column prop="mobile" label="手机号码"></el-table-column>
      <el-table-column prop="userRoleName" label="鼎校用户类型"></el-table-column>
      <el-table-column prop="certTypeName" label="通联用户类型"></el-table-column>
      <el-table-column prop="idCard" label="身份证" width="200"></el-table-column>
      <el-table-column prop="bankName" label="银行卡" width="200"></el-table-column>
      <el-table-column prop="bankCard" label="银行账号" width="200"></el-table-column>
      <el-table-column prop="certStatus" label="实名认证" width="120">
        <template slot-scope="scope">
          <span class="green" v-if="scope.row.certStatus === '1'">已认证</span>
          <span class="green" v-else-if="scope.row.certStatus === '2'">认证中</span>
          <span class="red" v-else>未认证</span>
        </template>
      </el-table-column>
      <el-table-column prop="bindCardStatus" label="是否绑卡">
        <template slot-scope="scope">
          <span class="green" v-if="scope.row.bindCardStatus === 1">是</span>
          <span class="red" v-else>否</span>
        </template>
      </el-table-column>
      <el-table-column prop="bindPhoneStatus" label="是否绑手机">
        <template slot-scope="scope">
          <span class="green" v-if="scope.row.bindPhoneStatus === 1">是</span>
          <span class="red" v-else>否</span>
        </template>
      </el-table-column>
      <el-table-column prop="signContractStatus" label="是否电子签约">
        <template slot-scope="scope">
          <span class="green" v-if="scope.row.signContractStatus === 1">是</span>
          <span class="red" v-else>否</span>
        </template>
      </el-table-column>
      <el-table-column prop="isEnable" label="账户状态">
        <template slot-scope="scope">
          <span class="green" v-if="scope.row.isEnable === 1">开通</span>
          <span class="red" v-else>关闭</span>
        </template>
      </el-table-column>
    </el-table>
    <!-- 添加或修改弹窗 -->
    <el-dialog title="查看通联会员" :visible.sync="dialogVisible" width="70%" :close-on-click-modal="false" @close="close">
      <el-form :ref="addOrUpdate ? 'addMarketDate' : 'updateMarketDate'" :model="updateMarketDate" label-position="left"
        label-width="120px">
        <el-form-item label="鼎校编号：" prop="userCode">
          <el-col :xs="24" :span="18">
            <el-input v-model="updateMarketDate.userCode" readonly=readonly autocomplete="off" />
          </el-col>
        </el-form-item>
        <el-form-item label="通联用户编号：" prop="ysTCode">
          <el-col :xs="24" :span="18">
            <el-input v-model="updateMarketDate.ysTCode" readonly=readonly autocomplete="off" />
          </el-col>
        </el-form-item>
        <el-form-item label="用户姓名：" prop="realName">
          <el-col :xs="24" :span="18">
            <el-input v-model="updateMarketDate.realName" readonly=readonly autocomplete="off" />
          </el-col>
        </el-form-item>
        <el-form-item label="法人姓名：" prop="legalName">
          <el-col :xs="24" :span="18">
            <el-input v-model="updateMarketDate.legalName" readonly=readonly autocomplete="off" />
          </el-col>
        </el-form-item>
        <el-form-item label="手机号码：" prop="mobile">
          <el-col :xs="24" :span="18">
            <el-input v-model="updateMarketDate.mobile" readonly=readonly autocomplete="off" />
          </el-col>
        </el-form-item>

        <el-form-item label="鼎校用户类型 ：" prop="userRoleName">
          <el-col :xs="24" :span="18">
            <el-input v-model="updateMarketDate.userRoleName" readonly=readonly autocomplete="off" />
          </el-col>
        </el-form-item>
        <el-form-item label="通联用户类型：" prop="certTypeName">
          <el-col :xs="24" :span="18">
            <el-input v-model="updateMarketDate.certTypeName" readonly=readonly autocomplete="off" />
          </el-col>
        </el-form-item>
        <el-form-item label="身份证号：" prop="idCard">
          <el-col :xs="24" :span="18">
            <el-input v-model="updateMarketDate.idCard" readonly=readonly autocomplete="off" />
          </el-col>
        </el-form-item>
        <el-form-item label="银行账号：" prop="bankCard">
          <el-col :xs="24" :span="18">
            <el-input v-model="updateMarketDate.bankCard" readonly=readonly autocomplete="off" />
          </el-col>
        </el-form-item>
        <el-form-item label="企业名称：" prop="companyName">
          <el-col :xs="24" :span="18">
            <el-input v-model="updateMarketDate.companyName" readonly=readonly autocomplete="off" />
          </el-col>
        </el-form-item>
        <el-form-item label="开户银行：" prop="bankName">
          <el-col :xs="24" :span="18">
            <el-input v-model="updateMarketDate.bankName" readonly=readonly autocomplete="off" />
          </el-col>
        </el-form-item>
        <el-form-item label="统一社会信用证书：" prop="uniCreditImg">
          <el-col :xs="24" :span="18">
            <el-input v-model="updateMarketDate.uniCredit" readonly=readonly autocomplete="off" />
          </el-col>
        </el-form-item>
        <el-form-item label="企业地址：" prop="companyAddress">
          <el-col :xs="24" :span="18">
            <el-input v-model="updateMarketDate.companyAddress" readonly=readonly autocomplete="off" />
          </el-col>
        </el-form-item>
        <el-form-item label="营业执照：" prop="uniCreditImg">
          <el-col :xs="24" :span="18">
            <el-image style="width: 100px; height: 100px" :src="updateMarketDate.uniCreditImg"></el-image>
          </el-col>
        </el-form-item>
        <el-form-item label="身份证正面：" prop="idCardFrontimg">
          <el-col :xs="24" :span="18">
            <el-image style="width: 100px; height: 100px" :src="updateMarketDate.idCardFrontImg"></el-image>
          </el-col>
        </el-form-item>
        <el-form-item label="身份证反面：" prop="idCardBackimg">
          <el-col :xs="24" :span="18">
            <el-image style="width: 100px; height: 100px" :src="updateMarketDate.idCardBackImg"></el-image>
          </el-col>
        </el-form-item>

        <el-form-item label="认证状态：" prop="certStatus">
          <el-col :xs="24" :span="18">
            <template v-if="updateMarketDate.certStatus === '1'">
              <el-input value="已认证" readonly=readonly autocomplete="off" />
            </template>
            <template v-else-if="updateMarketDate.certStatus === '0'">
              <el-input value="未认证" readonly=readonly autocomplete="off" />
            </template>
            <template v-else>
              <el-input value="认证中" readonly=readonly autocomplete="off" />
            </template>
          </el-col>
        </el-form-item>
        <el-form-item label="是否绑卡：" prop="bindCardStatus">
          <el-col :xs="24" :span="18">
            <template v-if="updateMarketDate.bindCardStatus === '1'">
              <el-input value="是" readonly=readonly autocomplete="off" /></template>
            <template v-else>
              <el-input value="否" readonly=readonly autocomplete="off" /></template>
          </el-col>
        </el-form-item>
        <el-form-item label="是否绑手机：" prop="bindPhoneStatus">
          <el-col :xs="24" :span="18">
            <template v-if="updateMarketDate.bindPhoneStatus === '1'">
              <el-input value="是" readonly=readonly autocomplete="off" /></template>
            <template v-else>
              <el-input value="否" readonly=readonly autocomplete="off" /></template>
          </el-col>
        </el-form-item>
        <el-form-item label="是否电子签约：" prop="signContractStatus">
          <el-col :xs="24" :span="18">
            <template v-if="updateMarketDate.signContractStatus === '1'">
              <el-input value="是" readonly=readonly autocomplete="off" /></template>
            <template v-else>
              <el-input value="否" readonly=readonly autocomplete="off" /></template>
          </el-col>
        </el-form-item>
        <el-form-item label="状态 ：" prop="isEnable">
          <el-col :xs="24" :span="18">
            <template v-if="updateMarketDate.isEnable === 1">
              <el-input value="开通" readonly=readonly autocomplete="off" /></template>
            <template v-else>
              <el-input value="关闭" readonly=readonly autocomplete="off" /></template>
          </el-col>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <!-- <el-button type="primary" size="mini" @click="close">确定</el-button> -->
        <el-button size="mini" @click="close">关闭</el-button>
      </div>
    </el-dialog>
    <!-- 分页 -->
    <el-col :span="20">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>
  </div>
</template>

<script>
import
ystMember
  from "@/api/allinOneMember";
import {
  pageParamNames
} from "@/utils/constants";
import {
  ossPrClient
} from '@/api/alibaba'

export default {
  name: 'allInOneMember',
  data() {
    return {
      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      tableData: [],
      dataQuery: {},
      dialogVisible: false,
      addOrUpdate: false,
      updateMarketDate: {
        contractPhotoPath: [],
      },
      addMarketDate: {},
      showLoginAccount: false
    };
  },
  created() {
    this.fetchData();
  },
  methods: {
    // 查询提现列表
    fetchData() {
      const that = this;
      that.tableLoading = true
      ystMember.ystMemberPage(
        that.tablePage.currentPage,
        that.tablePage.size,
        that.dataQuery
      ).then(res => {
        console.log(res);
        that.tableData = res.data.data;
        that.tableLoading = false;
        // 设置后台返回的分页参数
        pageParamNames.forEach(name =>
          that.$set(that.tablePage, name, parseInt(res.data[name]))
        );
      });
    },
    //查看分支列表
    checkWithdrawalBranchList(memberId) {
      const that = this;
      that.$router.push({
        path: "/wechatPublicAccount/experienceBonusWithdrawRecoder",
        query: {
          memberId: memberId
        }
      });
    },
    //新增操作
    clickDetail(id) {
      const that = this
      ystMember.ystDetail(id).then(res => {

        that.updateMarketDate = res.data
        if (res.data.idCardFrontImgPath != null) {
          that.updateMarketDate.idCardFrontImg = that.aliUrl + res.data.idCardFrontImgPath[0]
        }
        if (res.data.idCardBackImgPath != null) {
          that.updateMarketDate.idCardBackImg = that.aliUrl + res.data.idCardBackImgPath[0]
        }
        if (res.data.uniCreditImgPath != null) {
          that.updateMarketDate.uniCreditImg = that.aliUrl + res.data.uniCreditImgPath[0]
        }

        this.dialogVisible = true;


      })

    },
    // 打开修改登陆账号
    openLogin() {
      this.showLoginAccount = true
    },
    closeLogin() {
      this.showLoginAccount = false
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val;
      this.fetchData();
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val;
      this.fetchData();
    },
    close() {
      this.dialogVisible = false
    }
  }
};
</script>

<style>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}
</style>
