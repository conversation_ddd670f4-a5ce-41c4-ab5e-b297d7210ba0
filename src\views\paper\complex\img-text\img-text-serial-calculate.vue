<template>
  <div class="app-container">
    <el-form :model="form" ref="form" label-width="150px" v-loading="formLoading" :rules="rules">
      <el-form-item label="分类：" prop="titleType">
        <el-select v-model="form.titleType" disabled>
          <el-option v-for="item in titleType" :key="item.value" :value="item.value" :label="item.label"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="题型：" prop="questionType">
        <el-select v-model="form.questionType" disabled>
          <el-option v-for="item in questionType" :key="item.value" :value="item.value" :label="item.label"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="题目：" prop="title">
        <el-input placeholder="请输入题目" v-model="form.title" @input="form.title = isNaN(form.title)
        ? form.title.replace(/[^\d]/g, ''): form.title"
        />
      </el-form-item>
      <el-form-item label="连续次数：" prop="questionType">
        <el-input-number v-model="form.customInfo.num" :min="1"/>
      </el-form-item>
      <el-form-item label="运算符号：" prop="questionType">
        <el-select v-model="form.customInfo.sign">
          <el-option v-for="item in [{label:'加',value:'+'},{label:'减',value:'-'},{label:'乘',value:'*'},{label:'除',value:'/'}]" :key="item.label" :value="item.value" :label="item.label"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="运算结果：" prop="questionType">
        <el-input-number v-model="form.customInfo.result" :min="1"/>
      </el-form-item>
      <el-form-item label="运算结果出现次数：" prop="questionType">
        <el-input-number v-model="form.customInfo.resultNum" :min="1"/>
      </el-form-item>
      <el-form-item label="字符是否多次使用：" prop="questionType">
        <el-radio-group v-model="form.customInfo.use">
          <el-radio label="1">是</el-radio>
          <el-radio label="0">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="说明：" prop="questionExplain" required>
        <el-input v-model="form.questionExplain">
          <i @click="inputClick(form,'questionExplain')" slot="suffix" class="el-icon-edit-outline"
             style="line-height: 36px;font-size: 20px;cursor: pointer;"></i>
        </el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="submitForm">提交</el-button>
        <el-button v-show="form.id===null" @click="resetForm">重置</el-button>
      </el-form-item>
    </el-form>
    <el-dialog :visible.sync="richEditor.dialogVisible" append-to-body :close-on-click-modal="false"
               style="width: 100%;height: 100%" :show-close="false" center
    >
      <Ueditor @ready="editorReady"/>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="editorConfirm">确 定</el-button>
        <el-button @click="richEditor.dialogVisible = false">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import Ueditor from '@/components/Ueditor'
import complexApi from '@/api/paper/complex-question'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import { mapGetters, mapState } from 'vuex'

export default {
  components: {
    Ueditor
  },
  data() {
    return {
      fileList: [],
      importFrom: {
        file: null
      },
      randomLength: undefined,
      form: {
        id: null,
        title: '',
        titleType: 'IMG_TEXT',
        questionType: 'SERIAL_CALCULATE',
        items: [],
        customInfo: {
          num:undefined,
          sign:undefined,
          use:undefined,
          result:undefined,
          resultNum:undefined,
        },
        questionExplain: ''
      },
      formLoading: false,
      rules: {
        title: [
          { required: true, message: '请输入题干', trigger: 'blur' }
        ],
        questionExplain: [
          { required: true, message: '请输入题目说明', trigger: 'blur' }
        ],
        titleType: [
          { required: true, message: '请选择分类', trigger: 'blur' }
        ],
        questionType: [
          { required: true, message: '请选择题型', trigger: 'blur' }
        ]
      },
      richEditor: {
        dialogVisible: false,
        object: null,
        parameterName: '',
        instance: null
      }
    }
  },
  created() {
    let id = this.$route.query.id
    let _this = this
    if (id && parseInt(id) !== 0) {
      _this.formLoading = true
      complexApi.select(id).then(re => {
        _this.form = re.data
        this.form.customInfo = JSON.parse(this.form.customInfo);
        _this.formLoading = false
      })
    }
  },
  methods: {
    editorReady(instance) {
      this.richEditor.instance = instance
      let currentContent = this.richEditor.object[this.richEditor.parameterName]
      this.richEditor.instance.setContent(currentContent)
      // 光标定位到Ueditor
      this.richEditor.instance.focus(true)
    },
    inputClick(object, parameterName) {
      this.richEditor.object = object
      this.richEditor.parameterName = parameterName
      this.richEditor.dialogVisible = true
    },
    editorConfirm() {
      let content = this.richEditor.instance.getContent()
      this.richEditor.object[this.richEditor.parameterName] = content
      this.richEditor.dialogVisible = false
    },
    submitForm() {
      let _this = this
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.formLoading = true
          //上传execel文件
          let file = new FormData()
          file.append('file', this.importFrom.file)
          this.form.customInfo = JSON.stringify(this.form.customInfo);
          file.append('map', JSON.stringify(this.form))
          complexApi.edit(file).then(re => {
            if (re.success) {
              _this.$message.success(re.message)
              _this.$router.push({ path: '/paper/complex' })
            } else {
              _this.$message.error(re.message)
              this.formLoading = false
            }
          }).catch(e => {
            this.formLoading = false
          })
        } else {
          return false
        }
      })
    },
    resetForm() {
      let lastId = this.form.id
      this.$refs['form'].resetFields()
      this.form = {
        id: null,
        title: '',
        titleType: 'IMG_TEXT',
        questionType: 'SERIAL_CALCULATE',
        items: [],
        customInfo: {
          num:undefined,
          sign:undefined,
          use:undefined,
          result:undefined,
          resultNum:undefined,
        },
        questionExplain: ''
      }
      this.form.id = lastId
    },
  },
  computed: {
    ...mapGetters('enumItem', ['enumFormat']),
    ...mapState('enumItem', {
      titleType: state => state.complexQuestion.titleType,
      questionType: state => state.complexQuestion.questionType
    })
  }
}
</script>

<style lang="less" scoped>
.question-item-label {
  display: flex;
  margin-bottom: 12px;
}
</style>
