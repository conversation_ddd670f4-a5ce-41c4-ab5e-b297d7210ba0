<template>
  <div class="app-container">
    <el-form :model="dataQuery" ref="queryForm" :inline="true">
      <el-form-item label="题目ID：">
        <el-input v-model="dataQuery.id" clearable></el-input>
      </el-form-item>
      <el-form-item label="学段：">
        <el-select v-model="dataQuery.gradeId" placeholder="学段" @change="levelChange" clearable>
          <el-option v-for="item in gradeList" :key="item.id" :value="item.id" :label="item.name"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="维度：">
        <el-select v-model="dataQuery.subjectId" clearable placeholder="请先选择学段">
          <el-option v-for="item in subjectFilter" :key="item.id" :value="item.id" :label="item.name"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="题型：">
        <el-select v-model="dataQuery.questionType" clearable>
          <el-option v-for="item in questionType" :key="item.value" :value="item.value" :label="item.label"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" style="marginRight: 5px" @click="submitForm">查询</el-button>
        <el-popover placement="bottom" trigger="click">
          <el-button type="warning" size="mini" v-for="item in editUrlEnum" :key="item.label"
            @click="$router.push({ path: item.value })">{{ item.name }}</el-button>
          <el-button slot="reference" type="primary" class="link-left">添加</el-button>
        </el-popover>
        <!--        <el-button type="warning" style="margin-left:5px" @click="importBtn">导入</el-button>-->
      </el-form-item>
    </el-form>
    <el-table class="common-table" v-loading="listLoading" :data="tableData" style="width: 100%; margin-bottom: 20px"
      row-key="id" border default-expand-all>
      <el-table-column prop="id" label="Id" />
      <el-table-column prop="gradeId" label="学段" :formatter="gradeListFormatter" width="120px" />
      <el-table-column label="操作" align="center" width="220px">
        <template slot-scope="{ row }">
          <!--          <el-button size="mini" @click="showQuestion(row)">预览</el-button>-->
          <el-button size="mini" @click="editQuestion(row)">编辑</el-button>
          <el-button size="mini" type="danger" @click="deleteQuestion(row)" class="link-left">删除</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="subjectId" label="维度" :formatter="subjectFormatter" width="120px" />
      <el-table-column prop="questionType" label="题型" :formatter="questionTypeFormatter" width="120px" />
      <el-table-column prop="title" label="题目" show-overflow-tooltip>
        <template slot-scope="scope">
          <div v-html="scope.row.title"></div>
        </template>
      </el-table-column>
      <el-table-column prop="score" label="分数" width="120px" />
      <!--      <el-table-column prop="difficulty" label="难度" width="120px" />-->
      <el-table-column prop="createTime" label="创建时间" />
    </el-table>
    <!-- 分页 -->
    <el-col :span="24" style="margin-bottom: 20px">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>
    <div class="q-dailog">
      <el-dialog :visible.sync="questionShow.dialog" style="width: 100%; height: 100%" title="题目预览">

        <QuestionShow :qType="questionShow.qType" :question="questionShow.question" :qLoading="questionShow.loading" />
      </el-dialog>
    </div>
    <!-- excel导入 -->
    <el-dialog title="题目导入" :visible.sync="importOpen" width="70%" :close-on-click-modal="false" @close="close">
      <el-form ref="importFrom" :model="importFrom" label-position="left" label-width="120px" style="width: 100%">
        <el-form-item>
          <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;"
            href="https://document.dxznjy.com/manage/1649407675343.xlsx">下载模板</el-link>
        </el-form-item>
        <el-form-item label="题目导入">
          <el-upload :file-list="fileList" ref="upload" class="upload-demo"
            action="https://jsonplaceholder.typicode.com/posts/" :on-change="beforeUpload" multiple :limit="1"
            :on-exceed="handleExceed" :on-remove="handleRemoveDetail" :auto-upload="false" name="flie">
            <div class="el-upload__text">
              <el-button size="small" type="primary" style>点击上传</el-button>
            </div>
            <div slot="tip" class="el-upload__tip">只能上传execel文件</div>
          </el-upload>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="importSubmit()">确定</el-button>
        <el-button size="mini" @click="close">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import QuestionShow from './components/Show'
import questionApi from '@/api/paper/question'
import subjectApi from "@/api/paper/subject";
import { mapGetters, mapState } from 'vuex';
import { pageParamNames } from "@/utils/constants";
import Ueditor from '@/components/Ueditor'
import '/public/components/ueditor/themes/iframe.css'
import courseWordApi from '@/api/courseMake'

export default {
  components: { QuestionShow },
  data() {
    return {
      fileList: [],
      importOpen: false,
      importFrom: {
        file: null,
      },
      dataQuery: {
        id: null,
        questionType: null,
        level: null,
        subjectId: null,
        gradeId: null,
      },
      subjectFilter: null,
      listLoading: true,
      tableData: [],
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null,
      },
      total: 0,
      questionShow: {
        qType: '',
        dialog: false,
        question: null,
        loading: false,
      },
      subjectList: [],
      gradeList: [],
    };
  },
  created() {
    this.initGrade();
    this.initSubject();
    this.getPageList()
  },
  methods: {
    initGrade() {
      subjectApi.gradeListAll().then(res => {
        this.gradeList = res.data;
      })
    },
    importSubmit() {
      //上传execel文件
      let file = new FormData();
      if (this.importFrom.file === null || this.importFrom.file === "") {
        this.$message.error("请上传文件");
        return;
      }
      file.append("file", this.importFrom.file);
      questionApi.importData(file).then(res => {
        this.importOpen = false;
        this.$nextTick(() => this.getPageList());
        this.$message.success("导入成功！");
      }).catch((err) => {
        this.close();
      });
    },
    importBtn() {
      this.close();
      this.importOpen = true;
    },
    close() {
      this.importOpen = false;
      this.importFrom = {
        file: null
      }
      this.fileList = [];
    },
    //上传模板
    beforeUpload(file) {
      this.importFrom.file = file.raw;
    },
    handleRemoveDetail() {
      this.importFrom.file = "";
    },
    //限制文件
    handleExceed(files, fileList) {
      this.$message.warning(
        `当前限制选择 1个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length
        } 个文件`
      );
    },
    initSubject() {
      subjectApi.pageList().then(res => {
        this.subjectList = res.data.data;
      })
    },
    gradeListFormatter(row, column, cellValue, index) {
      return this.subjectFormat(this.gradeList, cellValue)
    },
    questionTypeFormatter(row, column, cellValue, index) {
      return this.enumFormat(this.questionType, cellValue)
    },
    subjectFormatter(row, column, cellValue, index) {
      return this.subjectFormat(this.subjectList, cellValue)
    },
    submitForm() {
      this.getPageList()
    },
    getPageList() {
      this.listLoading = true
      this.dataQuery.pageNum = this.tablePage.currentPage;
      this.dataQuery.pageSize = this.tablePage.size;
      questionApi.pageList(this.dataQuery).then((res) => {
        this.tableData = res.data.data;
        this.listLoading = false;
        // 设置后台返回的分页参数
        pageParamNames.forEach((name) =>
          this.$set(this.tablePage, name, parseInt(res.data[name]))
        );
      });
    },
    levelChange() {
      this.dataQuery.subjectId = null;
      this.subjectFilter = this.subjectList.filter(
        (data) => data.gradeId === this.dataQuery.gradeId
      );
    },
    showQuestion(row) {
      this.questionShow.loading = true;
      questionApi.select(row.id).then((re) => {
        this.questionShow.qType = re.data.questionType;
        this.questionShow.question = re.data;
        this.questionShow.loading = false;
        this.questionShow.dialog = true;
      });
    },
    editQuestion(row) {
      let url = this.pageFormat(this.editUrlEnum, row.questionType);
      this.$router.push({ path: url, query: { id: row.id } });
    },
    deleteQuestion(row) {
      let _this = this
      this.$confirm('是否删除该题目？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        questionApi.deleteQuestion(row.id).then(re => {
          if (re.success) {
            _this.$message.success(re.message)
            _this.getPageList()
          } else {
            _this.$message.error(re.message)
          }
        })
      })
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val;
      this.getPageList();
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val;
      this.getPageList();
    },
  },
  computed: {
    ...mapGetters('enumItem', ['enumFormat', 'subjectFormat', 'pageFormat']),
    ...mapState('enumItem', {
      // gradeList: state => state.question.gradeList,
      questionType: state => state.question.typeList,
      editUrlEnum: state => state.question.editUrlEnum,
    }),
  }
}
</script>

<style lang="less" scoped>
/deep/.el-dialog__header {
  border-bottom: 1px solid #dfdfdf;
}
</style>
