/**
 * 推荐人分润列表
 */
import request from '@/utils/request'

export default {
  //课程包获取账号信息
  coursePackageProfitAccountFlow() {
    return request({
      // url: '/znyy/areas/merchant/profit/account/flow/' + pageNum + '/' + pageSize,
      url: '/znyy/account',
      method: 'GET'
    })
  },
  //课程包收益流水
  coursePackageFlow(pageNum,pageSize,merchantCode) {
    return request({
      url: '/znyy/account/getFlowList?pageNum=' + pageNum + '&pageSize=' + pageSize+'&merchantCode='+merchantCode,
      method: 'GET'
    })
  },
  //课程包提现
  coursePackageWithdraw(data) {
    return request({
        url: '/znyy/account/withdraw?money=' + data,
        method: 'POST'
      }
    )
  },
  realName(){
    return request({
      url: '/znyy/account/realNameBack',
      method: 'POST',
    })
  }



}
