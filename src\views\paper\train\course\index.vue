<template>
  <div class="app-container" >
    <el-form :model="dataQuery" ref="queryForm" :inline="true">
      <el-form-item label="年级：" prop="difficulty">
        <el-select v-model="dataQuery.grade" clearable>
          <el-option v-for="(item, index) in gradeList" :key="index" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getList">查询</el-button>
      </el-form-item>
    </el-form>
    <el-col :span="24" style="margin-bottom: 30px">
      <el-button type="success" icon="el-icon-plus" size="mini" @click="handleAdd()">新增</el-button>
    </el-col>
    <el-table class="common-table" v-loading="tableLoading" :data="tableData" style="width: 100%;margin-bottom: 20px;"
      row-key="id" border default-expand-all>
      <el-table-column prop="id" label="ID"></el-table-column>
      <el-table-column prop="name" label="课程名称" show-overflow-tooltip></el-table-column>
      <el-table-column prop="withdrawnBonus" label="操作" width="480">
        <template slot-scope="scope">
          <el-button type="success" size="mini" icon="el-icon-edit-outline" @click="handleUpdate(scope.row.id)">编辑
          </el-button>
          <el-button type="danger" size="mini" icon="el-icon-delete" @click="handleDelete(scope.row.id)">删除
          </el-button>
        </template>
      </el-table-column>
      <el-table-column prop="grade" label="年级" :formatter="gradeFormatter" />
      <el-table-column label="关卡" align="center" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <router-link :to="{ path: '/train/pass', query: { id: scope.row.id } }" class="link-type">
            <span>设置</span>
          </router-link>
        </template>
      </el-table-column>
      <el-table-column label="排序" prop="orderNum" />
    </el-table>
    <!-- 分页 -->
    <el-col :span="24" style="margin-bottom: 20px">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>

    <!-- 修改或者新增弹窗 -->
    <el-dialog title="编辑课程" :visible.sync="open" width="70%" @close="cancel"  v-loading="formLoading">
      <el-form ref="form" :model="form" :rules="rules" label-width="120px" style="width: 70%;">
        <el-form-item label="课程名称：" prop="name">
          <el-input v-model="form.name" />
        </el-form-item>
        <el-form-item label="年级：" prop="grade">
          <el-select v-model="form.grade">
            <el-option v-for="(item, index) in gradeList" :key="index" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="排序" prop="orderNum">
          <el-input-number v-model="form.orderNum" controls-position="right" :min="1" :step="1" />
        </el-form-item>
        <el-form-item label="课程类型" prop="formal">
          <el-radio-group v-model="form.formal">
            <el-radio :label="0">体验课</el-radio>
            <el-radio :label="1">正式课</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import courseApi from '@/api/paper/train/course'
import { pageParamNames } from '@/utils/constants'
import { mapGetters, mapState } from 'vuex'

export default {
  data() {
    return {
      gradeList: [{ label: '1-3年级', value: 1 }, { label: '4-6年级', value: 2 }, { label: '初高中', value: 3 }],
      title: '',
      dataQuery: {
        parentId: 0,
        type: 'COURSE'
      },
      formLoading:false,
      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      open: false,
      tableData: [],
      form: {},
      // 表单校验
      rules: {
        name: [{ required: true, message: '请输入标题', trigger: 'blur' }],
        orderNum: [{ required: true, message: '请输入', trigger: 'blur' }],
        grade: [{ required: true, message: '请选择', trigger: 'blur' }],
        formal: [{ required: true, message: '请选择', trigger: 'blur' }],
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    gradeFormatter(row, column, cellValue, index) {
      return this.enumFormat(this.gradeList, cellValue)
    },
    handleUpdate(id) {
      this.reset()
      courseApi.detail(id).then(res => {
        this.form = res.data
        this.open = true
      })
    },
    handleDelete(id) {
      this.$confirm('确定要删除吗？', '删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        courseApi.delete(id).then(res => {
          this.$message.success('删除成功！')
          this.getList()
        })
      })
    },
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          this.formLoading=true;
          courseApi.createOrUpdate(this.form).then(response => {
            this.$message.success('提交成功！')
            this.formLoading=false;
            this.open = false
            this.getList()
          })
        }
      })
    },
    //分页查询
    getList() {
      this.loading = true
      this.dataQuery.pageNum = this.tablePage.currentPage
      this.dataQuery.pageSize = this.tablePage.size
      courseApi.list(this.dataQuery).then(res => {
        this.tableData = res.data.data
        this.loading = false
        // 设置后台返回的分页参数
        pageParamNames.forEach(name =>
          this.$set(this.tablePage, name, parseInt(res.data[name]))
        )
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: '',
        type: 'COURSE',
        parentId: 0,
        name: '',
        grade: '',
        orderNum: 1,
        formal:undefined,
      }
    },

    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val
      this.getList()
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val
      this.getList()
    }
  },
  computed: {
    ...mapGetters('enumItem', ['enumFormat', 'trainUrlFormat']),
  }
}
</script>
