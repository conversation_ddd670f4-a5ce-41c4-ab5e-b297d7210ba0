/**
 * 事业部相关接口
 */
import request from '@/utils/request'

export default {
  // 新增
  addDivision(data) {
    return request({
      url: '/znyy/division/save',
      method: 'POST',
      data
    })
  },
  // 修改回显
  echoDivision(id) {
    return request({
      url: '/znyy/division/check/detail/' + id,
      method: 'GET'
    })
  },
  // 编辑
  updateDivision(data) {
    return request({
      url: '/znyy/division/save',
      method: 'POST',
      data
    })
  },
  // 获取银行分类列表
  categoryType(bankType) {
    return request({
      url: '/znyy/bvstatus/' + bankType,
      method: 'GET'
    })
  },
  // 加载符近的托管中心
  loadOtherExperienceStores(lat, lon) {
    return request({
      url: '/znyy/dealer/load/dealer?lat=' + lat + '&lon=' + lon,
      method: 'GET'
    })
  },


}
