<template>
  <div class="app-container">
    <el-form :inline="true" ref="queryForm" class="container-card" label-width="110px">
      <el-form-item label="计划名称：" prop="planName">
        <el-input v-model="dataQuery.planName" placeholder="请输入计划名称" clearable />
      </el-form-item>
      <el-form-item label="分类：" prop="courseType">
        <el-select v-model="dataQuery.courseType" placeholder="全部" clearable>
          <el-option v-for="(item, index) in [{ label: '系统课', value: '1' }, { label: '非系统课', value: '2' }]" :key="index"
            :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="状态：" prop="status">
        <el-select v-model="dataQuery.status" placeholder="全部" clearable>
          <el-option v-for="(item, index) in [{ label: '未开始', value: '2' }, { label: '进行中', value: '1' }, { label: '已结束', value: '3' }]"
            :key="index" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handlePage()">搜索</el-button>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-re" @click="resetQuery()">重置</el-button>
      </el-form-item>

    </el-form>
    <el-col :span="24" style="margin-bottom: 30px">
      <el-button type="success" icon="el-icon-plus" size="mini" @click="handleAdd()" v-if="btn || btn1">新增计划</el-button>
      <el-button type="warning" icon="el-icon-back" size="mini" @click="goBack()">返回</el-button>
    </el-col>

    <el-table class="common-table" v-loading="tableLoading" :data="tableData" style="width: 100%;margin-bottom: 20px;"
      row-key="id" border default-expand-all>
      <el-table-column prop="id" label="计划ID"></el-table-column>
      <el-table-column prop="planName" label="计划名称"></el-table-column>
      <el-table-column label="操作" v-if="btn">
        <template slot-scope="scope">
          <el-button type="text" size="mini" @click="handleEdit(scope.row)">编辑</el-button>
          <el-button type="text" size="mini" @click="changeStatus(scope.row)" v-if="scope.row.isEnable" style="color: red">停用</el-button>
          <el-button type="text" size="mini" @click="changeStatus(scope.row)" v-if="!scope.row.isEnable">启用</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="courseType" label="课程分类">
        <template slot-scope="scope">
          <span v-if="scope.row.courseType == 1">系统课</span>
          <span v-else>非系统课</span>
        </template>
      </el-table-column>
      <el-table-column prop="planDate" label="计划日期"></el-table-column>
      <el-table-column prop="planTime" label="计划时间"></el-table-column>
      <el-table-column prop="status" label="状态">
        <template slot-scope="scope">
          <span v-if="scope.row.status === 1">进行中</span>
          <span v-if="scope.row.status === 2">未开始</span>
          <span v-if="scope.row.status === 3">已结束</span>
        </template>
      </el-table-column>
      <el-table-column prop="starLevel" label="星级">
        <template slot-scope="scope">
          <i :style="item.color" v-for="(item, index) in setStar(scope.row.starLevel)" :key="index"
            :class="item.value"></i>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <el-col :span="24" style="margin-bottom: 20px">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>

    <!-- 添加弹窗 -->
    <el-dialog :title="`${isAdd ? '新建' : '编辑'}计划`" :visible.sync="open" width="63%" :close-on-click-modal="false"
      @close="close">
      <el-form ref="form" :model="form" :rules="rules" label-width="120px" style="width: 70%;">
        <el-form-item label="计划名称:" prop="planName">
          <el-input v-model="form.planName" />
        </el-form-item>
        <el-form-item label="选择时间" required>
          <el-date-picker v-model="planDates" :type="isAdd ? 'dates' : 'date'" value-format="yyyy-MM-dd"
            :picker-options="pickerOptions" placeholder="选择一个或多个日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="学科:" prop="subject">
          <el-radio-group v-model="form.subject">
            <el-radio-button v-for="item in subjects" :label="item" :key="item">{{ item }}</el-radio-button>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="submitForm()">确定</el-button>
        <el-button size="mini" @click="close">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import studySchedulePlanApi from "@/api/studyroom/scheduleplan";
import { pageParamNames } from "@/utils/constants";
import ls from '@/api/sessionStorage'
import studyScheduleApi from "@/api/studyroom/studySchedule";

export default {
  name: 'planList',
  data() {
    var that = this;
    return {
      subjects: [
        '语文',
        '数学',
        '英语',
        '物理',
        '化学',
        '生物',
        '政治',
        '历史',
        '地理',
        '其他'
      ],
      planDates: [],
      btn: false,
      btn1: false,
      open: false,
      scheduleId: null,
      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      isAdd: true,
      tableData: [],
      dataQuery: {
        planName: '',
        courseType: '',
        status: '',
      },
      form: {},
      // 表单校验
      rules: {
        planName: [
          { required: true, message: "请输入计划名称", trigger: "blur" }
        ],
      },
      //禁用的天
      weeks: [0, 1, 2, 3, 4, 5, 6],
      pickerOptions: {
        disabledDate(date) {
          let zero = new Date().setHours(0, 0, 0, 1);
          if (date.getTime() + 1 < zero) {
            return true;
          }
          for (let i = 0; i < that.weeks.length; i++) {
            let a = parseInt(that.weeks[i]);
            if (a === 7) { a = 0 };
            if (date.getDay() === a) {
              return true;
            }
          }
          return false;
        }
      },
      setStar(data) {
        var arr = [];
        if (data == 0) {
          for (var i = 1; i < 4; i++) {
            arr.push({
              index: i,
              value: 'el-icon-star-off',
            });
            if (i + 1 == data) return arr;
          }
        } else {
          for (var i = 1; i < 4; i++) {
            if (i > data) {
              arr.push({
                index: i,
                value: 'el-icon-star-on',
                color: 'color:rgb(196,196,196)',
              });
            } else {
              arr.push({
                index: i,
                value: 'el-icon-star-on',
                color: 'color:rgb(0,186,174)',
              });
            }
            if (i + 1 == 4) return arr;
          }
        }
      },
    };
  },
  created() {
    this.scheduleId = ls.getItem('scheduleId');
    this.getPageList();
    this.getTarget();
    this.btnShow();
  },
  methods: {
    btnShow() {
      this.$store.getters.roles.forEach(element => {
        if (element.val === "admin" || element.val === 'Dealer' || element.val === 'School') {
          this.btn = true;
          this.btn1 = true;
        } else {
          this.btn = false;
          this.btn1 = false;
        }
      });
    },

    getTarget() {
      studyScheduleApi.getTarget(this.scheduleId).then(res => {
        this.getSchedule(res.data.studyScheduleId);
      })
    },
    getSchedule(id) {
      studyScheduleApi.getSchedule(id).then(res => {
        if (res.data.weeks) {
          var arr = res.data.weeks.split(",");
          arr = arr.map(Number);
          arr.forEach(x => {
            if (x === 7) {
              x = 0;
            }
            this.weeks = this.weeks.filter(y => {
              return x !== y;
            })
          });
        }
      })
    },
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.planDates.length === 0 || this.planDates == null || this.planDates == '') {
            this.$message.error("请选择时间！");
            return false
          }
          this.form.planDates = this.isAdd ? this.planDates : [this.planDates];
          if (this.form.id) {
            studySchedulePlanApi.editPlan(this.form).then(response => {
              this.$message.success("提交成功！")
              this.open = false;
              this.getPageList();
            });
          } else {
            studySchedulePlanApi.addPlan(this.form).then(response => {
              this.$message.success("提交成功！")
              this.open = false;
              this.getPageList();
            });
          }
        }
      });
    },
    goBack() {
      this.$store.dispatch('delVisitedViews', this.$route);
      this.$router.go(-1);
    },
    changeStatus(row) {
      let msg = row.isEnable ? '停用' : '启用';
      let enable = row.isEnable ? 0 : 1;
      this.$confirm('确定要' + msg + '吗?', '修改状态', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        studySchedulePlanApi.updateEnable(row.id, enable).then(res => {
          this.$nextTick(() => this.getPageList())
          this.$message.success('修改成功!')
        }).catch(err => {

        })
      }).catch(err => {

      })
    },
    /** 编辑按钮*/
    handleEdit(row) {
      this.isAdd = false
      this.reset();
      studySchedulePlanApi.planDetail(row.id).then(res => {
        this.form = res.data;
        this.planDates = this.form.planStartTime.split(" ")[0] + '';
        this.open = true;
      })
    },
    getPageList() {
      this.tableLoading = true;
      // this.dataQuery.scheduleId="898253576756269056";
      this.dataQuery.scheduleId = this.scheduleId;
      studySchedulePlanApi.schedulePlanList(this.tablePage.currentPage, this.tablePage.size, this.dataQuery).then(res => {
        this.tableData = res.data.data;
        this.tableLoading = false;
        // 设置后台返回的分页参数
        pageParamNames.forEach(name => this.$set(this.tablePage, name, parseInt(res.data[name])))
      })
    },

    /** 新增按钮操作 */
    handleAdd() {
      this.isAdd = true;
      this.reset();
      this.open = true;
    },
    handlePage() {
      this.tablePage = {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
        this.getPageList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dataQuery = {
        planName: '',
        courseType: '',
        status: '',
      };
      this.getPageList();
    },
    close() {
      this.open = false;
      this.reset();
    },
    reset() {
      this.form = {
        planDates: '',
        planName: '',
        studyScheduleId: this.scheduleId,
        subject: ''
      };
      this.planDates = '';
      if (this.$refs['form']) {
        this.$refs['form'].resetFields();
      }
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val;
      this.getPageList();
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val;
      this.getPageList();
    },
  }
};
</script>

<style>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.period-table td,
.period-table th {
  text-align: center;
}

@media (max-width: 767px) {
  .el-message-box {
    width: 80% !important;
  }
}
</style>
