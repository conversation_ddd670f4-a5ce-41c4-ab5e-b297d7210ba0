<!--用户操作日志-->
<template>
  <div class="app-container">
    <el-form :inline="true" style="margin-bottom: 20px">
      <el-form-item label="用户名:">
        <el-input v-model="query.userName" placeholder="用户名"></el-input>
      </el-form-item>
      <el-button type="primary" @click="fetchData" style="margin-left: 20px;">搜索</el-button>
      <el-button type="warning" @click="exportData()" v-loading="exportLoading" title="导出完整数据">导出</el-button>
    </el-form>

    <el-table class="period-table" v-loading="tableLoading" :data="tableData" style="width: 100%; margin-bottom: 20px"
      row-key="id" border default-expand-all>
      <el-table-column prop="userId" label="用户id" sortable>
      </el-table-column>
      <el-table-column prop="userName" label="用户名" sortable>
      </el-table-column>
      <el-table-column prop="desc" label="操作" sortable>
      </el-table-column>
      <!--      <el-table-column prop="requParam" label="请求参数" sortable>
      </el-table-column>-->
      <!--      <el-table-column prop="requBody" label="请求体" sortable>
      </el-table-column>-->
      <el-table-column prop="methodName" label="方法名" sortable>
      </el-table-column>
      <el-table-column prop="ip" label="ip地址" sortable>
      </el-table-column>
      <el-table-column prop="crateTime" label="时间" sortable>
      </el-table-column>
      <el-table-column prop="success" label="是否成功" sortable width="200">
        <template slot-scope="scope">
          <span v-if="scope.row.success === true" class="green">成功</span>
          <span v-else class="red">失败</span>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <el-col :span="20">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>
  </div>
</template>

<script>
import { pageParamNames } from "@/utils/constants";
import checkPermission from '@/utils/permission'
import riskAuthCodeApi from "@/api/riskAuthCode";
import userOptionLogsApi from "@/api/userOptionLogsApi";
import { baseUrl } from '@/utils/constants'
import divisionListApi from "@/api/divisionList";

export default {
  name: "merProfitConfig",
  data() {
    return {
      query: {
        userName: undefined,
      },
      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null,
        productType: ""
      },
      tableData: [],
      dialogVisible: false,
      exportLoading: false,
      addCourseData: {},
    };
  },
  created() {
    //获取授权码类型
    riskAuthCodeApi.getRiskType().then((res) => {
      this.authCodeTypeList = res.data;
    })
    this.fetchData();
  },
  methods: {
    checkPermission,
    //查询渠道分润配置列表
    fetchData() {
      this.tableLoading = true;
      userOptionLogsApi.pageList({
        "pageNum": this.tablePage.currentPage,
        "pageSize": this.tablePage.size,
        "userName": this.query.userName
      }).then((res) => {
        this.tableData = res.data.data.data;
        this.tableLoading = false;
        // 设置后台返回的分页参数
        pageParamNames.forEach((name) =>
          this.$set(this.tablePage, name, parseInt(res.data.data[name]))
        );
      });
    },
    //数据导出
    exportData() {
      this.exportLoading = true;
      window.location.href = baseUrl + "znyy/userOptionLog/exportData";
      this.$nextTick(() => {
        this.exportLoading = false;
      })
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val;
      this.fetchData();
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val;
      this.fetchData();
    },
    //关闭弹框
    close() {
      this.dialogVisible = false;
      this.form = {
        id: undefined,
        authCodeType: undefined,
        authCodeNum: undefined,
        authCodeEndDate: undefined,
      }
      this.$refs['form'].resetFields();
    },

  },
};
</script>

<style scoped>
.period-table td,
.period-table th {
  text-align: center;
}
</style>
