<template>
  <div class="app-container">
    <!-- 新增按钮 -->
    <el-row class="container-card" style="margin-bottom: 30px;">
      <el-col :span="6">
        <el-col :span="8" class="marginbottom" style="line-height: 36px;">订单类型</el-col>
        <el-col :span="15">

          <el-select
            v-model="dataQuery.orderType"
            filterable
            value-key="value"
            placeholder="请选择"
            clearable
          >
            <el-option
              v-for="(item,index) in orderType"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </el-select>

        </el-col>
      </el-col>
      <el-col :span="1" style="height: 1px" />
      <el-col :span="6">
        <el-col :span="8" class="marginbottom" style="line-height: 36px;">订单状态</el-col>
        <el-col :span="15">

          <el-select
            v-model="dataQuery.orderStatus"
            filterable
            value-key="value"
            placeholder="请选择"
            clearable
          >
            <el-option
              v-for="(item,index) in orderStatus"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </el-select>

        </el-col>
      </el-col>

      <el-col :span="11" style="text-align: right;">
        <el-button type="primary" icon="el-icon-search" size="mini" @click="fetchData()">搜索</el-button>
      </el-col>
    </el-row>
    <el-table v-loading="tableLoading" :data="tableData" stripe border style="width: 100%;margin-bottom: 30px;">
      <el-table-column prop="id" label="订单id" align="center" width="180"/>
      <el-table-column prop="orderTypeName" label="订单类型" align="center" :show-overflow-tooltip="true"/>
      <el-table-column label="操作" align="center" width="260">
        <template slot-scope="scope">
          <el-tooltip content="发货" placement="top" >
            <el-button type="success" size="mini" @click="deliverGoods(scope.row)" :disabled="scope.row.logisticsStatus=='待发货'? (scope.row.orderStatus=='已支付'||scope.row.orderStatus=='已支付'?false:true):false">
             {{scope.row.logisticsStatus=='已发货' ? '发货详情' : '发货'}}
            </el-button>
          </el-tooltip>
          <el-tooltip content="赠送码详情" placement="top">
            <el-button size="mini" type="warning" :disabled="!scope.row.isHasCode"  @click="giftDetail(scope.row.id,scope.row.memberId)">赠送码</el-button>
          </el-tooltip>
          <el-tooltip content="详情" placement="top">
            <el-button type="warning" size="mini" @click="detail(scope.row.id)">详情</el-button>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column prop="memberId" label="用户code" align="center" :show-overflow-tooltip="true"/>
      <el-table-column prop="priceDeal" label="订单成交价(扣除手续费)" align="center" :show-overflow-tooltip="true"/>
      <el-table-column prop="priceConfirm" label="用户支付价格" align="center" :show-overflow-tooltip="true"/>
      <el-table-column prop="logisticsStatus" label="物流状态"  align="center">
        <template slot-scope="scope">
          <span :style="scope.row.logisticsStatus === '未知'? 'color:#f66c81' : (scope.row.logisticsStatus === '已收货' ? 'color:#67c23a' : (scope.row.logisticsStatus === '待发货' ? 'color:#e6a23c':'color:#409eff'))" >{{scope.row.logisticsStatus}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="orderStatus" label="订单状态"  align="center">
        <template slot-scope="scope">
          <span :style="scope.row.orderStatus === '超时取消'||scope.row.orderStatus === '已取消' ||scope.row.orderStatus === '已退款'||scope.row.orderStatus === '未知'? 'color:#f66c81' : (scope.row.orderStatus === '已完成' ? 'color:#67c23a' : (scope.row.orderStatus === '待支付' ? 'color:#e6a23c':'color:#409eff'))" >{{scope.row.orderStatus}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="purchaseCount" label="购买数量" align="center" :show-overflow-tooltip="true"/>
      <el-table-column prop="paidTime" label="支付时间" align="center" :show-overflow-tooltip="true"/>
    </el-table>

    <!-- 分页 -->
    <el-col :span="20">
      <el-pagination
        :current-page="tablePage.currentPage"
        :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="tablePage.totalItems"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-col>

    <el-dialog title="发货" :visible.sync="dialogFormVisible">
      <el-form :model="deliver">
        <el-form-item label="订单id" :label-width="formLabelWidth" prop="id">
          <el-input v-model="deliver.id" readonly="readonly" autocomplete="off" />
        </el-form-item>
        <el-form-item label="物流单号" :label-width="formLabelWidth" prop="logisticsNumber">
          <el-input v-model="deliver.logisticsNumber" autocomplete="off" />
        </el-form-item>
        <el-form-item label="物流公司" :label-width="formLabelWidth" prop="logistcsCompany">
          <el-input v-model="deliver.logistcsCompany" autocomplete="off" />
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button size="mini"  type="warning" @click="dialogFormVisible = false">取 消</el-button>
        <el-button type="success" size="mini" @click="deliverPost(deliver)">确 定</el-button>
      </div>
    </el-dialog>

    <el-dialog title="订单详情" :visible.sync="dialogDetailVisible">
      <el-form :model="orderDetail">
        <el-form-item label="赠品" :label-width="formLabelWidth" prop="freebievos">
          <el-input v-model="orderDetail.freebievos" readonly="readonly" autocomplete="off" />
        </el-form-item>
        <el-form-item label="活动" :label-width="formLabelWidth" prop="active">
          <el-input v-model="orderDetail.active" autocomplete="off" />
        </el-form-item>
        <el-form-item label="收货地址" :label-width="formLabelWidth" prop="receivingAddress">
          <el-input v-model="orderDetail.receivingAddress" autocomplete="off" />
        </el-form-item>
        <el-form-item label="收货人号码" :label-width="formLabelWidth" prop="receivingPhone">
          <el-input v-model="orderDetail.receivingPhone" autocomplete="off" />
        </el-form-item>
        <el-form-item label="收货人" :label-width="formLabelWidth" prop="receivingName">
          <el-input v-model="orderDetail.receivingName" autocomplete="off" />
        </el-form-item>
      </el-form>
    </el-dialog>

    <!-- 二维码列表 -->
    <el-dialog v-loading="tableLoading" title="咨询师码" :visible.sync="dialogCodeVisible" width="70%" :close-on-click-modal="false" @close="dialogCodeVisible=false">
      <el-table v-loading.body="tableLoading" :data="schoolCodeList" border fit highlight-current-row style="width: 100%">
        <el-table-column label="是否使用" prop="isUsed" align="center">
          <template slot-scope="scope">
            <el-button :type="scope.row.isUsed==0? 'primary':'default'" size="mini" plain disabled>{{ scope.row.isUsed==0? '未使用':'使用' }}</el-button>
          </template>
        </el-table-column>
        <el-table-column label="二维码" prop="qrcodePath" align="center">
          <template slot-scope="scope"><img width="40px" :src="scope.row.qrcodePath"></template>
        </el-table-column>
        <el-table-column prop="id" label="二维码编号" align="center" width="180" />
        <el-table-column prop="memberName" label="使用人" align="center" :show-overflow-tooltip="true"/>
        <el-table-column prop="memberMobile" label="联系方式" align="center" :show-overflow-tooltip="true"/>
        <el-table-column prop="useTime" label="使用时间" align="center" :show-overflow-tooltip="true"/>
      </el-table>
    </el-dialog>


  </div>

</template>

<script>
import orderApi from '@/api/order'

import {
  pageParamNames
} from '@/utils/constants'

export default {
  name:'order',
  components: {},
  data() {
    return {

      dialogFormVisible: false,
      dialogDetailVisible: false,
      deliver: {
        id: '',
        logisticsNumber: '',
        logistcsCompany: '',
        logisticsStatus: ''
      },
      formLabelWidth: '120px',
      orderDetail: {
        id: '',
        freebievos: '',
        active: '',
        receivingAddress: '',
        receivingPhone: '',
        receivingName: ''

      },

      orderType: [], // 活动类型
      orderStatus: [],
      tableLoading: false,
      dataQuery: {
        orderType: ''
      },
      options: [{
        value: 'MACHINE',
        label: '学习机'
      }, {
        value: 'COLLAGE',
        label: '拼团课'
      }, {
        value: 'EXPERIENCE',
        label: '体验课'
      }, {
        value: '',
        label: '全部'
      }],
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      tableData: [],

      logisticsType: [], // 物流状态
      dialogCodeVisible:false,    //二维码弹窗展示
      schoolCodeList:[],        //二维码列表
    }
  },
  created() {
    this.fetchData()
    this.queryOrderType()
    this.queryOrderStatus()
  },
  methods: {
    // 获取活动返回类型
    queryOrderType() {
      orderApi.queryOrderType().then(res => {
        this.orderType = res.data
      })
    },

    queryOrderStatus() {
      orderApi.queryOrderStatus().then(res => {
        this.orderStatus = res.data
      })
    },

    deliverGoods(ele) {
      this.dialogFormVisible = true
      this.deliver.id = ele.id
      this.deliver.logisticsNumber = ele.logisticsNumber
      this.deliver.logistcsCompany = ele.logistcsCompany
      this.deliver.logisticsStatus = ele.logisticsStatus
    },

    detail(id) {
      this.dialogDetailVisible = true
      orderApi.orderDetail(id).then(res => {
        const ex = JSON.parse(res.data.orderDetail.orderExtend);
        let freebieVo = '';
        for (var i = 0; i < ex.freebieVos.length; i++) {
          freebieVo += '名称:' + ex.freebieVos[i].name + '  数量:' + ex.freebieVos[i].num + '  '
        }
        this.orderDetail.freebievos = freebieVo
        this.orderDetail.active = ex.activeName
        this.orderDetail.receivingAddress = res.data.receivingAddress
        this.orderDetail.receivingName = res.data.receivingName
        this.orderDetail.receivingPhone = res.data.receivingPhone
      })
    },

    deliverPost(ele) {
      const that = this
      if (!ele.logisticsNumber) {
        that.$message.error('物流单号不能为空')
        return false
      }
      if (!ele.logistcsCompany) {
        that.$message.error('物流公司不能为空')
        return false
      }
      if (ele.logisticsStatus !== '待发货') {
        that.$message.error('不能重复发货')
        return false
      }

      orderApi.deliverGoods(ele).then(res => {
        const loading = this.$loading({
          lock: true,
          text: '发货提交',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        })
        that.dialogFormVisible = false
        loading.close()
        that.$nextTick(() => that.fetchData())
        that.$message.success('发货成功')
      })
    },

    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val
      this.fetchData()
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val
      this.fetchData()
    },

    // 查询
    fetchData() {
      const that = this
      that.tableLoading = true
      orderApi.queryOrderPage(that.tablePage.currentPage, that.tablePage.size, that.dataQuery).then(res => {
        that.tableData = res.data.data
        that.tableLoading = false
        that.tableData.forEach(item =>{                     //判断是否有赠送二维码
              that.$set(item,'isHasCode',false)
              if(item.orderDetail!==null){
                item.orderDetail.orderExtend = JSON.parse(item.orderDetail.orderExtend)
                if(item.orderStatus==='已支付'||item.orderStatus==='已完成'){
                  item.orderDetail.orderExtend.freebieVos.forEach(itemson=>{
                    if(itemson.relevanceType==='ZXSM'){
                      item.isHasCode=true
                    }
                  })
                }
              }

        })


        // 设置后台返回的分页参数
        pageParamNames.forEach(name => that.$set(that.tablePage, name, parseInt(res.data[name])))
      })
    },

//  赠送码详情点击
giftDetail(id, memberId) {
  const that = this
  that.tableLoading = true
  orderApi.queryQrcode(id, memberId).then(res => {
    that.dialogCodeVisible = true
    that.tableLoading = false
    that.schoolCodeList = res.data
  })
}

  }
}
</script>

<style>
  .container-card {
    padding: 15px 15px 15px 15px;
    margin-bottom: 15px ;
    box-shadow: 0 2px 4px rgba(0, 0, 0, .12), 0 0 6px rgba(0, 0, 0, .04)
  }
</style>
