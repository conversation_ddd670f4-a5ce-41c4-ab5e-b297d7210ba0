<!-- 新增试卷 -->
<template>
  <div class="app-container" v-loading="btnLoading" element-loading-text="保存中">
    <el-radio-group v-model="tabPosition" size="medium" class="groupItem" :disabled="true">
      <el-radio-button :class="{ 'active-highlight': tabPosition === 0 }" label="0">1、考试信息</el-radio-button>
      <el-radio-button :class="{ 'active-highlight': tabPosition === 1 }" label="1">2、题目配置</el-radio-button>
    </el-radio-group>
    <el-form ref="importFrom" :model="importFrom" label-position="top" label-width="120px" style="width: 100%" v-if="tabPosition == 0">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item label="课程大类" label-width="80px" required>
            <el-select v-model="importFrom.curriculumId" placeholder="请选择课程大类" @change="changeCurriculumId">
              <el-option v-for="item in curriculumList" :key="item.enCode" :value="item.id" :label="item.enName"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="16">
          <el-form-item label="试卷类型" label-width="80px" required>
            <el-select v-model="importFrom.examinePaperType" placeholder="请选择" @change="changePaper" @clear="clearPaper" clearable>
              <el-option v-for="item in testpaperList" :key="item.value" :value="item.value" :label="item.desc"></el-option>
            </el-select>
            &nbsp;&nbsp;
            <el-select v-model="importFrom.courseVersionId" placeholder="请选择版本" clearable @change="changeVersion" @clear="clearVersionId">
              <el-option v-for="item in versionList" :key="item.id" :value="item.id" :label="item.versionName"></el-option>
            </el-select>
            &nbsp;&nbsp;
            <el-select v-model="importFrom.subjectId" placeholder="请选择学科" clearable @change="updateChildOptions">
              <el-option v-for="item in subjectList" :key="item.id" :value="item.id" :label="item.nodeName"></el-option>
            </el-select>
            &nbsp;&nbsp;
            <el-select v-model="importFrom.gradeId" placeholder="请选择学段" clearable>
              <el-option v-for="item in gradeList" :key="item.id" :value="item.id" :label="item.nodeName"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="试卷名称" label-width="80px" required>
            <el-input v-model.trim="importFrom.examinePaperName" style="width: 600px" maxlength="20" show-word-limit></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="答题时间" label-width="80px" required>
            <span>
              整卷限时
              <el-input v-model.trim="importFrom.answerTime" style="width: 60px"></el-input>
              分钟
            </span>
          </el-form-item>
        </el-col>
        <el-col :span="16">
          <!-- TODO 时间 -->
          <el-form-item label="考试周期" label-width="80px" required>
            <el-date-picker
              value-format="yyyy-MM-dd"
              clearable
              v-model="examTime"
              type="daterange"
              align="right"
              unlink-panels
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            ></el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item style="display: flex">
            <div style="margin-bottom: 10px; font-weight: bolder; color: #606266">
              限时强制交卷&nbsp;&nbsp;
              <el-switch v-model="importFrom.isForce" active-color="#13ce66" inactive-color="#ff4949"></el-switch>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-form ref="titleFrom" :model="titleFrom" label-position="top" label-width="120px" style="width: 100%" v-else>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="组卷方式" label-width="80px">
            <el-radio-group v-model="titleFrom.isAiGroup">
              <el-radio :label="1" disabled>AI组卷</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="关联知识点" label-width="80px" required>
            <el-button style="width: 600px" @click="handleChangeKnowledgeId" clearable>
              {{ !this.titleFrom.knowledgeIds ? '请选择关联知识点' : `已选择${this.titleFrom.knowledgeIds.length}个知识点` }}
            </el-button>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item>
            <div style="margin-bottom: 18px; font-weight: bolder; color: #606266">
              <span class="required-star">*</span>
              试卷题型&nbsp;&nbsp;
              <el-button size="mini" @click="addTitle">添加题型</el-button>
              <span style="margin-left: 10px">试卷共{{ titleFrom.questionConfigCoList ? titleFrom.questionConfigCoList.length : 0 }}种题型</span>
            </div>
            <div v-for="(config, index) in titleFrom.questionConfigCoList" :key="config.id" style="margin-bottom: 18px; display: flex; align-items: center">
              <span v-if="getQuestionTypeName(config.questionsType)">
                &nbsp;&nbsp;{{ getQuestionTypeName(config.questionsType) }}共
                <el-input v-model.trim="config.size" placeholder="请输入题目数量" style="width: 200px; margin: 0 10px"></el-input>
                题，每题
                <el-input v-model.trim="config.num" placeholder="请输入分数" style="width: 200px; margin: 0 10px"></el-input>
                分
              </span>
              <i v-if="getQuestionTypeName(config.questionsType)" class="el-icon-delete" style="font-size: 26px; cursor: pointer" @click="deleteQuestion(index)"></i>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="pageBack" v-if="tabPosition == 1">上一步</el-button>
      <el-button @click="backList" v-if="tabPosition == 0">取 消</el-button>
      <el-button type="info" @click="saveDemo" v-if="isAdd || btnIsEnable != 1">存为草稿</el-button>
      <el-button type="primary" @click="nextStep" v-if="tabPosition == 0">下一步</el-button>
      <el-button type="primary" @click="saveQuestion" v-if="tabPosition == 1">保 存</el-button>
    </div>
    <el-dialog title="新添加题型" :visible.sync="titleDiaOpen" width="30%" :close-on-click-modal="false" center>
      <el-form ref="titleDiaFrom" :model="titleDiaFrom" label-position="right">
        <el-row :gutter="20">
          <el-col :span="18">
            <el-form-item label="课程大类" label-width="100px">
              <span>{{ titleDiaFrom.curriculumName }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="18">
            <el-form-item label="题型" label-width="100px">
              <el-select v-model="valueType" multiple placeholder="请选择" clearable>
                <el-option v-for="item in questionTypeList" :key="item.value" :value="item.value" :label="item.desc"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="titleDiaOpen = false">取 消</el-button>
        <el-button type="primary" @click="confirmAddType">保 存</el-button>
      </span>
    </el-dialog>
    <el-dialog title="添加知识点" :visible.sync="isKnow" width="70%" @close="isKnow = false" :before-close="dialogBeforeClose">
      <span>
        <knowledge-point
          ref="knowledgePointRef"
          v-if="isKnow"
          :disciplineId="this.importFrom.subjectId"
          :curriculumId="this.importFrom.curriculumId"
          :knowledgePointIds="this.titleFrom.knowledgeIds"
        ></knowledge-point>
      </span>
      <span slot="footer">
        <el-button @click="isKnow = false">取消</el-button>
        <el-button type="primary" @click="confirmDialog">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
  import { mapGetters, mapState } from 'vuex';
  import testpaper from '@/api/mathApi/testPaperManagementAPI';
  import videoManageMentAPI from '@/api/mathApi/videoManageMentAPI';
  import '/public/components/ueditor/themes/iframe.css';

  export default {
    name: 'addPaper',
    data() {
      return {
        btnLoading: false,
        isKnow: false,
        isAdd: true, // 是否新增
        parentSendData: {},
        titleDiaOpen: false,
        nodeLevel: 2, // 学科节点级别
        curriculumList: [], // 课程大类
        versionList: [], // 版本
        subjectList: [], // 学科
        gradeList: [], // 学段
        questionTypeList: [], // 题目类型
        testpaperList: [], // 试卷类型
        sizeOptions: [
          { label: 1, value: 1 },
          { label: 2, value: 2 },
          { label: 3, value: 3 },
          { label: 4, value: 4 }
        ],
        numOptions: [
          { label: 1, value: 1 },
          { label: 2, value: 2 },
          { label: 3, value: 3 },
          { label: 4, value: 4 }
        ],
        tabPosition: 0, // 状态：0-考试信息 1-题目配置
        titleFrom: {
          isAiGroup: 1,
          knowledgeIds: [],
          questionConfigCoList: []
        },
        examTime: '',
        valueType: [],
        isEnable: 1,
        btnIsEnable: 1, // 存为草稿按钮是否可用
        importFrom: {
          id: '', // 试卷id
          curriculumId: '', // 课程大类id
          curriculumName: '', // 课程大类名称
          courseVersionId: '', // 版本id
          courseVersionName: '', // 版本名称
          examinePaperType: '', // 试卷类型
          subjectId: '', // 学科id
          subjectName: '', // 学科名称
          gradeId: '', // 学段id
          examinePaperName: '', // 试卷名称
          answerTime: '', // 答题时间
          examinePaperEndDate: '', // 试卷截止日期
          examinePaperStartDate: '', // 试卷开始日期
          isForce: false
        }
      };
    },
    watch: {
      'importFrom.curriculumId': function (newVal) {
        // 清空相关数据
        // this.importFrom.courseVersionId = '';
        // this.importFrom.subjectId = '';
        // this.importFrom.gradeId = '';
        if (!newVal) {
          // 如果课程大类的 id 为空，则不请求数据
          return;
        }
        // 请求版本和学科数据
        this.getVersion({ curriculumId: newVal });
      },

      'importFrom.courseVersionId': function (newVal) {
        console.log('版本变化了', newVal);
        // 清空相关数据
        // this.importFrom.subjectId = '';
        // this.importFrom.gradeId = '';
        if (!newVal) {
          // 如果版本的 id 为空，则不请求数据
          return;
        }
        // 请求版本和学科数据
        this.getTreeData({ courseVersionId: newVal });
      }
    },

    created() {
      console.log();

      this.getCurriculum();
      this.getPaperType({ type: 'testPaperType' });
      this.getType();
    },
    mounted() {
      this.reCoverData();
    },
    methods: {
      changeCurriculumId(e) {
        if (e) {
          this.$set(this.importFrom, 'examinePaperType', '');
          this.$set(this.importFrom, 'courseVersionId', '');
          this.$set(this.importFrom, 'subjectId', '');
          this.$set(this.importFrom, 'gradeId', '');
        }
      },
      clearVersionId() {
        this.$set(this.importFrom, 'courseVersionId', '');
        this.$set(this.importFrom, 'subjectId', '');
        this.$set(this.importFrom, 'gradeId', '');
      },
      changeVersion(e) {
        console.log(e);
        if (e) {
          this.$set(this.importFrom, 'subjectId', '');
          this.$set(this.importFrom, 'gradeId', '');
        }
      },
      clearPaper() {
        this.$set(this.importFrom, 'examinePaperType', '');
        this.$set(this.importFrom, 'courseVersionId', '');
        this.$set(this.importFrom, 'subjectId', '');
        this.$set(this.importFrom, 'gradeId', '');
      },
      changePaper(e) {
        if (e) {
          this.$set(this.importFrom, 'courseVersionId', '');
          this.$set(this.importFrom, 'subjectId', '');
          this.$set(this.importFrom, 'gradeId', '');
        }
      },
      confirmDialog() {
        const selectedKnowledge = this.$refs.knowledgePointRef.multipleSelection;
        console.log('🚀 ~ confirmDialog ~ selectedKnowledge:', selectedKnowledge);
        console.log('1111111:', this.importFrom.subjectId);
        // 处理你的逻辑，比如赋值到表单
        this.titleFrom.knowledgeIds = selectedKnowledge;
        this.isKnow = false;
      },
      dialogBeforeClose() {
        this.isKnow = false;
      },
      handleChangeKnowledgeId() {
        if (this.importFrom.curriculumId == '' || this.importFrom.curriculumId == null) {
          this.$message.error('请先选择课程大类');
          return false;
        }
        if (this.importFrom.subjectId == '' || this.importFrom.subjectId == null) {
          this.$message.error('请先选择学科');
          return;
        }
        this.isKnow = true;
      },
      reCoverData() {
        // 获取路由传递的 query 参数
        const importFromData = this.$route.query.importFrom;
        if (importFromData) {
          this.isAdd = false;
          // 反序列化
          this.parentSendData = JSON.parse(decodeURIComponent(importFromData));
          console.log('🚀 ~ reCoverData ~ this.parentSendData:', this.parentSendData);
        } else {
          this.isAdd = true;
          console.log('🚀 ~ reCoverData ~  this.importFrom:', this.importFrom);
          return;
        }
        this.btnIsEnable = this.parentSendData.isEnable;
        this.titleFrom.knowledgeIds = this.parentSendData.courseKnowledgeDtoList;
        this.importFrom.id = this.parentSendData.id || null;
        this.importFrom.curriculumId = this.parentSendData.curriculumId || '';
        this.importFrom.examinePaperType = this.parentSendData.examinePaperType || '';
        this.importFrom.examinePaperName = this.parentSendData.examinePaperName || '';
        this.importFrom.answerTime = this.parentSendData.answerTime || '';
        this.importFrom.isForce = this.parentSendData.isForce == 1 ? true : false || false;
        this.titleFrom.isAiGroup = this.parentSendData.isAiGroup;
        this.$set(this.importFrom, 'courseVersionId', this.parentSendData.courseVersionId);
        this.$set(this.importFrom, 'subjectId', this.parentSendData.courseSubjectId);
        this.$set(this.importFrom, 'gradeId', this.parentSendData.coursePeriodId);
        console.log(this.importFrom, '=------------');
        const typeDtoList = Array.isArray(this.parentSendData.typeDtoList)
          ? this.parentSendData.typeDtoList.map((item) => ({
              // 从数组元素中获取 questionsNum 赋值给 size
              size: item.questionsNum,
              // 从数组元素中获取 score 赋值给 num
              num: item.score,
              // 从数组元素中获取 questionsType 赋值给 questionsType
              questionsType: item.questionsType,
              id: item.id
            }))
          : [];
        this.titleFrom.questionConfigCoList = typeDtoList;
        if (this.parentSendData.examinePaperStartDate && this.parentSendData.examinePaperEndDate) {
          this.examTime = [this.parentSendData.examinePaperStartDate.split(' ')[0], this.parentSendData.examinePaperEndDate.split(' ')[0]];
        } else {
          this.examTime = [];
        }
      },
      addTitle() {
        this.titleDiaOpen = true;
      },
      // 获取课程大类id
      getCurriculum() {
        testpaper.getCurriculum().then((res) => {
          if (res.success) {
            this.curriculumList = res.data;
            if (Object.keys(this.parentSendData).length !== 0) {
              this.importFrom.curriculumId = this.parentSendData.curriculumId;
            } else {
              this.importFrom.curriculumId = this.curriculumList[0].id;
            }
            if (this.$route.query.curriculumId) {
              this.importFrom.curriculumId = this.$route.query.curriculumId;
            }
          }
        });
      },
      // 获取试卷类型
      getPaperType(query) {
        testpaper.getPaperType(query).then((res) => {
          if (res.success) {
            this.testpaperList = res.data;
            if (Object.keys(this.parentSendData).length !== 0) {
              this.importFrom.examinePaperType = this.parentSendData.examinePaperType;
            } else {
              this.importFrom.examinePaperType = this.testpaperList[0].value;
            }
            // this.testpaperList = Object.entries(res.data).map(([key, value]) => ({
            //   value: key,
            //   label: value
            // }));
          }
        });
      },
      // 获取题目类型
      async getType() {
        const res = await testpaper.getPaperType({ type: 'testPaperQuestionType' });
        if (res.success) {
          this.questionTypeList = res.data;
        }
      },
      // 获取版本
      getVersion(query) {
        return new Promise((resolve) => {
          videoManageMentAPI.getVersionIdAPI(query).then((res) => {
            if (res.success) {
              this.versionList = res.data;
              if (Object.keys(this.parentSendData).length !== 0) {
                this.importFrom.courseVersionId = this.parentSendData.courseVersionId;
              } else {
                // this.importFrom.courseVersionId = this.versionList[0].id;
              }
              this.getTreeData(query);
            } else {
              this.versionList = [];
              this.importFrom.courseVersionId = '';
            }
            resolve();
          });
        });
      },
      getTreeData(query) {
        console.log('🚀 ~ getTreeData ~ query:', query);
        console.log('🚀 ~ this.importFromData.curriculumId ~ query:', this.titleDiaFrom.curriculumId);
        if (!query.courseVersionId) return;
        videoManageMentAPI
          .getTreeDataAPI({
            curriculumId: this.importFrom.curriculumId,
            versionId: query.courseVersionId,
            nodeLevel: 2
          })
          .then((res) => {
            if (res.success) {
              // // 转换数据结构
              this.subjectList = res.data;
              let arr = this.subjectList.filter((item) => item.id == this.importFrom.subjectId);
              console.log(arr, '==========');
              if (arr && arr.length > 0) {
                this.gradeList = arr[0].childList;
              }
              // if (!this.isAdd) {
              //   // this.importFrom.subjectId = this.subjectList[0].id;
              //   this.importFrom.subjectId = this.parentSendData.courseSubjectId;
              //   const currentSubject = this.subjectList.find((item) => item.id === this.importFrom.subjectId);
              //   this.gradeList = currentSubject ? currentSubject.childList : [];
              //   this.importFrom.gradeId = this.parentSendData.coursePeriodId;
              // }
            }
          });
      },
      updateChildOptions() {
        // 根据选中的父选项 ID 找到对应的父选项对象
        const selectedParentObj = this.subjectList.find((item) => item.id === this.importFrom.subjectId);
        if (selectedParentObj) {
          // 更新子选项列表
          this.gradeList = selectedParentObj.childList;
        } else {
          this.gradeList = [];
        }
        // 清空子选项的选择
        this.importFrom.gradeId = '';
      },
      saveDemo() {
        this.isEnable = 2; // 草稿状态
        this.justDoSave();
        this.titleDiaOpen = false;
        this.tabPosition = 0;
      },
      validateForm() {
        if (!this.importFrom.curriculumId) {
          this.$message.error('请选择课程大类');
          return false;
        }
        if (!this.importFrom.courseVersionId) {
          this.$message.error('请选择版本');
          return false;
        }
        if (!this.importFrom.subjectId) {
          this.$message.error('请选择学科');
          return false;
        }
        if (!this.importFrom.gradeId) {
          this.$message.error('请选择学段');
          return false;
        }
        if (!this.importFrom.examinePaperName) {
          this.$message.error('请输入试卷名称');
          return false;
        }

        const answerTime = parseInt(this.importFrom.answerTime, 10);
        if (!answerTime || answerTime <= 0 || isNaN(answerTime)) {
          this.$message.error('答题时间必须为大于0的正整数');
          return false;
        }
        if (!this.examTime || this.examTime.length !== 2) {
          this.$message.error('请选择考试周期');
          return false;
        }
        return true;
      },
      nextStep() {
        if (!this.validateForm()) {
          return false;
        }
        this.tabPosition = 1;
      },
      validateSize(config) {
        const size = parseInt(config.size, 10);
        if (!size || size <= 0 || !Number.isInteger(size)) {
          this.$message.error('题目数量必须为正整数');
          config.size = null;
        }
      },
      validateNum(config) {
        const num = parseFloat(config.num);
        if (!num || num <= 0 || num % 1 !== 0) {
          this.$message.error('分数必须为正整数');
          config.num = null;
        }
      },
      saveQuestion() {
        // this.isEnable = 1; // 启用状态
        // isEnable 0禁用 1 启用 2 草稿
        // 如果是草稿，则改为启用；否则保持原来的状态
        this.isEnable = this.btnIsEnable == 2 ? 1 : this.btnIsEnable;
        // 校验知识点
        if (!this.titleFrom.knowledgeIds || this.titleFrom.knowledgeIds.length === 0) {
          this.$message.error('请选择关联知识点');
          return;
        }
        // 校验题型必填
        if (!this.titleFrom.questionConfigCoList || this.titleFrom.questionConfigCoList.length === 0) {
          this.$message.error('请添加题型');
          return;
        }
        // 校验题型配置
        for (let i = 0; i < this.titleFrom.questionConfigCoList.length; i++) {
          const config = this.titleFrom.questionConfigCoList[i];
          if (!config.size || !config.num) {
            this.$message.error('请填写完整的题型配置');
            return;
          }

          const size = parseInt(config.size, 10);
          const num = parseFloat(config.num);

          if (size <= 0 || num <= 0) {
            this.$message.error('题目数量和分数必须大于0');
            return;
          }

          if (!Number.isInteger(size) || num % 1 !== 0) {
            this.$message.error('题目数量必须为正整数');
            return;
          }

          if (num % 1 !== 0) {
            this.$message.error('分数必须为整数');
            return;
          }
        }
        this.justDoSave();
        this.titleDiaOpen = false;
      },
      justDoSave() {
        const params = {
          id: this.importFrom.id,
          examinePaperType: this.importFrom.examinePaperType,
          curriculumId: this.importFrom.curriculumId,
          curriculumName: this.curriculumList.find((item) => item.id === this.importFrom.curriculumId)?.enName || '',
          courseVersionId: this.importFrom.courseVersionId,
          courseVersionName: this.versionList.find((item) => item.id === this.importFrom.courseVersionId)?.versionName || '',
          courseSubjectId: this.importFrom.subjectId,
          courseSubjectName: this.subjectList.find((item) => item.id === this.importFrom.subjectId)?.nodeName || '',
          coursePeriodId: this.importFrom.gradeId,
          coursePeriodName: this.gradeList.find((item) => item.id === this.importFrom.gradeId)?.nodeName || '',
          examinePaperName: this.importFrom.examinePaperName,
          answerTime: this.importFrom.answerTime,
          examinePaperStartDate: this.examTime[0],
          examinePaperEndDate: this.examTime[1],
          isForce: this.importFrom.isForce ? 1 : 0,
          isAiGroup: this.titleFrom.isAiGroup,
          courseKnowledgeDtoList: this.titleFrom.knowledgeIds, // 知识点 ID 列表
          questionConfigCoList:
            Array.isArray(this.titleFrom.questionConfigCoList) && this.titleFrom.questionConfigCoList.length > 0
              ? this.titleFrom.questionConfigCoList.map((config) => ({
                  id: config.id || '', // 如果是新增，id 为 0
                  parperConfigId: config.parperConfigId || 0, // 如果是新增，parperConfigId 为 0
                  questionsType: config.questionsType,
                  questionsNum: config.size, // 题目数量
                  score: config.num, // 每题分数
                  deleted: 0 // 默认未删除
                }))
              : [],
          knowledgeName: this.titleFrom.knowledgeName
        };
        params.isEnable = this.isEnable;
        this.btnLoading = true;
        testpaper
          .savePaper(params)
          .then((res) => {
            if (res.success) {
              this.$message({
                message: '操作成功',
                type: 'success'
              });
              this.importFrom = {};
              this.$router.push({ path: '/_aaa_demo/testPaperManagement' });
            }
          })
          .finally(() => {
            this.btnLoading = false; // 不管接口调用成功与否，都关闭加载状态
          });
      },
      pageBack() {
        this.tabPosition = 0;
      },
      backList() {
        this.$router.go(-1);
      },
      confirmAddType() {
        if (this.valueType.length === 0) {
          this.$message.warning('请选择至少一个题型');
          return;
        }
        // 遍历选中的题型，生成对应的配置
        this.valueType.forEach((type) => {
          if (this.isTypeExists(type)) {
            const questionType = this.getQuestionTypeName(type);
            this.$message.warning(`题型 ${questionType} 已存在，请勿重复添加`);
          } else {
            this.titleFrom.questionConfigCoList.push({
              questionsType: type, // 题型
              size: null, // 题目数量
              num: null // 每题分数
            });
          }
        });

        // 清空选择并关闭对话框
        this.valueType = [];
        this.titleDiaOpen = false;
      },
      // 检查题型是否已存在
      isTypeExists(type) {
        return this.titleFrom.questionConfigCoList.some((item) => item.questionsType === type);
      },
      getQuestionTypeName(type) {
        const questionType = this.questionTypeList.find((item) => item.value === type);
        return questionType ? questionType.desc : '';
      },
      deleteQuestion(index) {
        this.titleFrom.questionConfigCoList.splice(index, 1);
      }
    },
    computed: {
      // 计算 titleDiaFrom 为 curriculumList 中当前选中的一项
      titleDiaFrom() {
        const selectedItem = this.curriculumList.find((item) => item.id === this.importFrom.curriculumId);
        return selectedItem
          ? {
              curriculumName: selectedItem.enName,
              curriculumId: selectedItem.id
            }
          : {
              curriculumName: '',
              curriculumId: ''
            };
      },
      ...mapGetters('enumItem', ['enumFormat', 'subjectFormat', 'pageFormat']),
      ...mapState('enumItem', {
        editUrlEnum: (state) => state.question.editUrlEnum
      })
    }
  };
</script>

<style lang="less" scoped>
  /deep/.el-dialog__header {
    border-bottom: 1px solid #dfdfdf;
  }
  .groupItem {
    margin-bottom: 20px;
  }
  /deep/.el-radio-button__inner {
    width: 500px;
    font-weight: bolder;
  }
  /deep/.el-form-item__label {
    font-weight: bolder;
  }
  /* 新增高亮样式 */
  /deep/.active-highlight .el-radio-button__inner {
    background-color: #409eff !important; /* 高亮背景色 */
    border-color: #409eff !important; /* 高亮边框色 */
    color: #fff !important; /* 高亮文字颜色 */
  }
  .required-star {
    color: #f55353;
  }
</style>
