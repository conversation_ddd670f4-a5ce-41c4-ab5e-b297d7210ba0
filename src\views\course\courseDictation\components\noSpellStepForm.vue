<template>
  <!-- 正式课拼读拼写 -->
  <div>
    <el-steps :active="activeCode" finish-status="success" class="activeStep">
      <el-step title="规则视频" :active="0"></el-step>
      <el-step title="拼读词" :active="1"></el-step>
      <el-step title="拼写词" :active="2"></el-step>
      <el-step title="学后读检测词" :active="3"></el-step>
      <el-step title="学后写检测词" :active="4"></el-step>
    </el-steps>
    <div>
      <!-- 视频 -->
      <el-form ref="ruleVideoForm" :model="ruleVideoForm" :rules="ruleVideoRules" label-width="160px" v-if="activeCode === 0">
        <el-form-item label="规则视频" prop="videoUrl">
          <div>
            <input type="file" class="upload" accept="video/mp4,video/webm,video/ogg" @change="doUpload('videoUrl', 'videoUrlInputer', 0)" ref="videoUrlInputer" multiple />
            <!-- <el-button type="primary" size="small" @click="startBaseUrl('videoUrl',0)" v-if="imageForm[0].id || ruleVideoForm.videoUrl">上传</el-button> -->
            <el-button type="danger" size="small" @click="delBaseUrl('videoUrl', 'videoUrlInputer', 0, 'click')" v-if="imageForm[0].id || ruleVideoForm.videoUrl">删除</el-button>
            <div v-if="ruleVideoForm.videoUrl && !imageForm[0].videoUrl && !videoUpStatus">
              <el-image style="width: 148px; height: 148px; background: #f5f7fa">
                <div slot="error" class="course_image_slot">
                  <i class="el-icon-picture-outline"></i>
                  <span>审核中...</span>
                </div>
              </el-image>
            </div>
            <div v-if="imageForm[0].videoUrl">
              <el-image :src="imageForm[0].videoUrl" style="width: 148px; height: 148px" fit="cover "></el-image>
            </div>
          </div>
          <el-button size="small " type="success" :disabled="videoUpStatus" @click="submitRuleVideoValidate()">下一环节</el-button>
        </el-form-item>
      </el-form>
      <!-- 拼读词 -->
      <el-form :model="stepReadForm" v-if="activeCode === 1 && !isShowStress" ref="stepReadForm" :rules="stepReadRules" label-width="160px">
        <el-form-item label="单词" prop="word" required>
          <el-row>
            <el-col :span="12">
              <el-input v-model="stepReadForm.word" type="textarea" :spellcheck="false" @change="inputChange(stepReadForm.word, '0')" placeholder="请输入单词" :rows="3"></el-input>
            </el-col>
          </el-row>
        </el-form-item>
        <el-form-item label="划音节" prop="syllable" required>
          <el-row>
            <el-col :span="12">
              <el-input
                v-model="stepReadForm.syllable"
                type="textarea"
                :spellcheck="false"
                @change="inputChange(stepReadForm.syllable, '1')"
                :rows="3"
                placeholder="请输入音节"
              ></el-input>
            </el-col>
          </el-row>
        </el-form-item>
        <el-form-item label="单词拆分" prop="splitInfo" required>
          <el-row>
            <el-col :span="12">
              <el-input
                v-model="stepReadForm.splitInfo"
                type="textarea"
                :spellcheck="false"
                @change="inputChange(stepReadForm.splitInfo, '2')"
                :rows="3"
                placeholder="请输入单词拆分"
              ></el-input>
            </el-col>
          </el-row>
        </el-form-item>
        <el-form-item label="" v-if="spellDialogTitile == '新增'">
          <div class="upload_box">
            <el-upload
              class="upload-demo"
              action="#"
              :show-file-list="true"
              ref="uploadReadWord"
              :file-list="fileListReadWord"
              :on-remove="handlSetRemoveReadWord"
              :http-request="uploadDetailHttp"
              :before-upload="beforeWordUpload"
            >
              <div class="el-upload__text">
                <el-link icon="el-icon-upload2" :underline="false" class="upload_link">excel文件上传</el-link>
              </div>
            </el-upload>
            <el-link class="download_link" :underline="false" icon="el-icon-download" href="https://document.dxznjy.com/applet/zhimi/config/pd_word.xls">模板下载</el-link>
          </div>
        </el-form-item>
        <el-form-item>
          <el-button size="small " type="success" @click="submitWordValidte('stepReadForm', '0')">下一环节</el-button>
        </el-form-item>
      </el-form>
      <el-form :model="stressReadForm" v-if="activeCode === 1 && isShowStress" ref="stressReadForm" :rules="stressReadRules" label-width="160px">
        <div v-for="(item, topIndex) in stepForm.pdWordRuleDto" :key="topIndex">
          <el-divider content-position="left">{{ item.word }}</el-divider>
          <el-form-item label="重音节" prop="stressWord">
            <el-row>
              <el-col :span="16">
                <el-checkbox-group v-model="stressReadForm[topIndex].stressWord" @change="readStressChange(item, '1', $event, topIndex)">
                  <el-checkbox v-for="(f, fIndex) in item.ruleWordTypeDtoList" :label="computeLabel(f.syllable, item.ruleWordTypeDtoList, fIndex)" :key="fIndex"></el-checkbox>
                </el-checkbox-group>
              </el-col>
            </el-row>
          </el-form-item>
          <el-form-item label="次重音节" prop="stressWord">
            <el-row>
              <el-col :span="16">
                <el-checkbox-group v-model="stressReadForm[topIndex].secondaryStressedWords" @change="readStressChange(item, '2', $event, topIndex)">
                  <el-checkbox v-for="(f, fIndex) in item.ruleWordTypeDtoList" :label="computeLabel(f.syllable, item.ruleWordTypeDtoList, fIndex)" :key="fIndex"></el-checkbox>
                </el-checkbox-group>
              </el-col>
            </el-row>
          </el-form-item>
        </div>
        <el-form-item>
          <el-button size="small " type="success" @click="submitValidte2('stressReadForm', '0')">下一环节</el-button>
        </el-form-item>
      </el-form>
      <!-- 拼写词 -->
      <el-form :model="stepWriteForm" v-if="activeCode === 2" ref="stepWriteForm" label-width="160px">
        <el-form-item label="拼写词:">
          <el-checkbox-group v-model="witerList">
            <el-checkbox v-for="(city, index) in cities" :label="city" :key="index">{{ city }}</el-checkbox>
          </el-checkbox-group>
          <el-input readonly type="textarea" disabled :value="witerList.join(',')" aria-label="拼写词"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button size="small" type="success" @click="submitWordValidte1('stepWriteForm', '1')">下一环节</el-button>
        </el-form-item>
      </el-form>
      <!-- 拼读检测词 -->
      <el-form ref="readTestForm" :model="readTestForm" v-if="activeCode === 3" :rules="readTestRules">
        <el-form-item label="练习拼读和练习拼写词:" :prop="id ? '' : 'word'" :required="id ? false : true">
          <el-checkbox-group v-model="checkList">
            <el-checkbox v-for="(city, index) in cities" :label="city" :key="index">{{ city }}</el-checkbox>
          </el-checkbox-group>
          <el-input readonly type="textarea" disabled :value="checkList.join(',')" aria-label="学后读检测词"></el-input>
        </el-form-item>
        <el-form-item label="">
          <el-button size="small" type="success" @click="submitValidte5(2, 'readTestForm', readTestWords)">下一环节</el-button>
        </el-form-item>
      </el-form>
      <!-- 拼写检测词 -->
      <el-form ref="writeTestForm" :model="writeTestForm" v-if="activeCode === 4" :rules="writeTestRules">
        <el-form-item label="练习拼读和练习拼写词:">
          <el-checkbox-group v-model="checkWriteList">
            <el-checkbox v-for="(city, index) in cities" :label="city" :key="index">{{ city }}</el-checkbox>
          </el-checkbox-group>
          <el-input readonly type="textarea" disabled :value="checkWriteList.join(',')" aria-label="学后写检测词"></el-input>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>
<script>
  import courseDictationListApi from '@/api/courseDictationList';
  import { ossPrClient } from '@/api/alibaba';
  import md5 from 'js-md5';
  import PlvVideoUpload from '@polyv/vod-upload-js-sdk';
  export default {
    props: {
      spellDialogVisible: {
        type: Boolean,
        default: false
      },
      spellId: {
        type: String,
        default: ''
      },
      editWordCode: {
        type: String,
        default: ''
      },
      spellType: {
        type: Number,
        default: 0
      },
      id: {
        type: String,
        default: ''
      },
      isSelect: {
        type: Number,
        default: 0
      },
      isWriteSelect: {
        type: Number,
        default: 0
      }
    },
    data() {
      return {
        fileListReadWord: [],
        allwords: [],
        witerList: [],
        fileListRemoveReadWord: [],
        activeCode: 0,
        readWordUrl: '',
        readSyllableUrl: '',
        readSplitInfoUrl: '',
        //单词拼读
        stepReadForm: {
          word: '',
          syllable: '',
          splitInfo: ''
        },
        stepWriteForm: {
          word: '',
          syllable: '',
          splitInfo: ''
        },
        readTestForm: {
          word: ''
        },
        writeTestForm: {
          word: ''
        },
        readTestRules: {
          word: [{ required: true, message: '请选择拼读单词', trigger: 'change' }]
        },
        writeTestRules: {
          word: [{ required: true, message: '请选择拼写单词', trigger: 'change' }]
        },
        courseId: '',
        stepForm: {
          id: this.$route.query.id,
          wordType: '2', // 2拼读单词 3拼写单词
          pdWordRuleDto: []
        },
        stepForm1: {
          id: this.$route.query.id,
          wordType: '3', // 2拼读单词 3拼写单词
          pdWordRuleDto: []
        },
        //拼读检测词
        readFilterData: [],
        readRightDefaultChecked: [], //拼读右侧默认选中
        //拼写检测词
        writeFilterData: [],
        writeRightDefaultChecked: [], //拼写右侧默认选中
        readFilterWords: [],
        writeFilterWords: [],
        stepWriteRules: {},
        stepReadRules: {
          word: [
            {
              validator: (rule, value, callback) => {
                this.stepWordValidate(rule, value, callback, this.stepReadForm);
              },
              trigger: 'blur'
            }
          ],
          syllable: [
            {
              validator: (rule, value, callback) => {
                this.stepSyllableValidate(rule, value, callback, this.stepReadForm);
              },
              trigger: 'blur'
            }
          ],
          splitInfo: [
            {
              validator: (rule, value, callback) => {
                this.stepSplitInfoValidate(rule, value, callback, this.stepReadForm);
              },
              trigger: 'blur'
            }
          ]
        },
        //重音
        stressReadRules: {
          stressReadForm: [{ required: true, message: '请选择重音节', trigger: 'blur' }]
        },
        stressWriteRules: {
          stressWriteForm: [{ required: true, message: '请选择重音节', trigger: 'blur' }]
        },
        readSyllableData: [],
        wordData: [],
        stressWord: [],
        stressWriteForm: [], //拼写词重音节
        stressReadForm: [], //拼读词重音节
        isShowStress: false,
        showReadSelectWord: false,
        showWriteSelectWord: false,
        readTestWords: [],
        writeTestWords: [],
        spellDialogTitile: '新增',
        id: '',
        //规则视频
        ruleVideoForm: {
          videoUrl: ''
        },
        videoUpload: null, //
        videoUpStatus: true,
        userid: '1723c88563', //从点播后台查看获取
        secretkey: 'Jkk4ml1Of8', //从点播后台查看获取
        writeToken: '94064a19-4d28-4ce9-aa05-96d476016215',
        imageForm: [{ name: 'videoUrl', videoUrl: '', id: '', status: '' }],
        vid: '',
        ruleVideoRules: {
          // videoUrl:[{ required: true, message: '请上传规则视频', trigger: 'blur' }]
        },
        uploadLoading: false,
        wordType: '', //编辑时获取详情拼读拼写类型 2拼读单词 3拼写单词
        spellViewForm: {},
        cities: [],
        citieList: [],
        checkList: [],
        checkWriteList: [],
        notEditItem: [],
        readList: [],
        writeList: [],
        readWord: [],
        writeWord: [],
        show: true,
        checkread: [],
        checkwrite: [],
        readStatus: 0,
        writeStatus: 0,
        concatWriteWord: []
      };
    },
    watch: {
      spellId: {
        handler(val, old) {
          if (val) {
            console.log(val);
            //   this.id = val;
            this.getView(val);
          } else {
            console.log('新增----------------------');
          }
        },
        immediate: true
      }
    },
    created() {
      this.courseId = this.$route.query.id;

      this.$bus.$on('clearData', () => {
        this.stressReadForm = [];
        this.stressWriteForm = [];
        this.readTestForm.word = '';
        this.writeTestForm.word = '';
        this.activeCode = 0;
      });
      this.videoUpload = new PlvVideoUpload({
        region: 'line1', // (可选)上传线路, 默认line1
        events: {
          Error: (err) => {
            // 错误事件回调
            console.log(err);
            let errMag = `${err.message}`;
            this.$alert(errMag, '标题名称', {
              confirmButtonText: '确定',
              type: 'error'
            });
          },
          UploadComplete: () => {
            // 全部上传任务完成回调
            this.$message({
              message: '上传成功',
              type: 'success'
            });
          }
        }
      });
    },
    mounted() {
      this.autoUpdateUserData(null, this.videoUpload);
      this.$bus.$on('clearFile', () => {
        if (this.imageForm[0].id) {
          this.delBaseUrl('videoUrl', 'videoUrlInputer', 0);
        }
      });
    },
    methods: {
      autoUpdateUserData(timer, videoUpload) {
        // 启动获取用户信息
        this.getUserData(videoUpload);
        if (timer) {
          clearTimeout(timer);
          timer = null;
        }
        timer = setTimeout(() => {
          this.autoUpdateUserData(timer, videoUpload);
        }, 3 * 50 * 1000);
      },
      getUserData() {
        // 获取用户详细信息
        this.ptime = new Date().getTime();
        let userData = {
          userid: this.userid,
          ptime: this.ptime,
          sign: this.getSignData().sign,
          hash: this.getSignData().hash
        };
        this.videoUpload.updateUserData(userData);
      },
      getSignData() {
        // 加密信息参数
        let hash = md5(this.ptime + this.writeToken);
        let sign = md5(this.secretkey + this.ptime);
        return {
          hash: hash,
          sign: sign
        };
      },
      transformSize(bytes) {
        // 文件大小转换
        const bt = parseInt(bytes);
        let result;
        if (bt === 0) {
          result = '0B';
        } else {
          const k = 1024;
          const sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
          const i = Math.floor(Math.log(bt) / Math.log(k));
          if (typeof i !== 'number') {
            result = '-';
          } else {
            result = (bt / Math.pow(k, i)).toFixed(2) + sizes[i];
          }
        }
        return result;
      },
      loadPlayerScript(callback) {
        console.log(callback, 'callback00');
        if (!window.polyvPlayer) {
          const myScript = document.createElement('script');
          myScript.setAttribute('src', this.vodPlayerJs);
          myScript.onload = callback;
          document.body.appendChild(myScript);
        } else {
          callback();
        }
      },
      loadPlayerSummaryUrl() {
        const polyvPlayer = window.polyvPlayer;
        this.player = polyvPlayer({
          wrap: `#summaryUrlPlayer`,
          width: 148,
          height: 148,
          vid: this.videoForm.summaryUrl
        });
      },
      delBaseUrl(value, refName, Index, msg) {
        if (this.videoUpload) {
          // console.log('delBaseUrl222222222222');
          courseDictationListApi.deleteVideo(this.ruleVideoForm[value]).then((res) => {
            if (res.code === 20000) {
              if (msg == 'click') {
                this.videoUpStatus = true;
                this.$message.success('删除成功');
              }
            }
          });
          this.videoUpload.removeFile(this.imageForm[Index].id);
          // this.videoUpload.clearAll();
          (this.$refs[refName].value = ''), (this.imageForm[Index][value] = '');
          this.imageForm[Index].id = '';
          this.ruleVideoForm[value] = '';
        }
      },
      startBaseUrl(value, Index) {
        // 全部上传
        if (this.videoUpload) {
          // this.videoUpload.resumeFile(this.videoForm[value]);
          this.videoUpload.resumeFile(this.imageForm[Index].id);
          // this.videoUpload.startAll();
        }
      },
      doUpload(value, refName, Index) {
        let inputDOM = this.$refs[refName]; // 通过DOM取文件数据
        let data = Object.values(inputDOM.files);
        const videoFormat = ['video/mp4', 'video/webm', 'video/ogg'];
        if (!videoFormat.includes(data[0].type)) {
          console.log('选择了非视频格式文件', inputDOM);
          this.$refs[refName].value = null;
          this.$message.warning('请选择视频格式文件');
          return;
        } else {
          console.log('选择的是视频格式文件');
        }
        if (data.length > 0) {
          data.forEach((file, index, arr) => {
            let fileSetting = {
              // 文件上传相关信息设置
              title: file.name, // 标题
              desc: '正式课拼读拼写', // 描述
              cataid: process.env.VUE_APP_BASE_CATAID, // 上传分类目录ID
              tag: '', // 标签
              luping: 0, // 是否开启视频课件优化处理，对于上传录屏类视频清晰度有所优化：0为不开启，1为开启
              keepsource: 0, // 是否源文件播放（不对视频进行编码）：0为编码，1为不编码
              state: '' //用户自定义信息，如果提交了该字段，会在服务端上传完成回调时透传返回。
            };
            let uploadManager = this.videoUpload.addFile(
              file, // file 为待上传的文件对象
              {
                FileStarted: this.onFileStarted, // 文件开始上传回调
                FileProgress: this.onFileProgress, // 文件上传中回调
                FileSucceed: (data) => this.onFileSucceed(data, value, Index), // 文件上传成功回调
                FileFailed: this.onFileFailed, // 文件上传失败回调
                FileStopped: this.onFileStopped // 文件上传停止回调
              },
              fileSetting
            );
            console.log(uploadManager, 'uploadManageruploadManager');
            // this.loadPlayerScript(this.loadPlayer);

            this.addTableData(uploadManager, value, Index);
            this.videoUpload.resumeFile(this.imageForm[Index].id);
          });
        }
      },
      onFileStarted(data) {
        console.log('文件上传开始: ', data);
        // this.tableData.filter((item) => item.id === data.uploaderid)[0].progress = 0
      },
      onFileProgress(data) {
        let p = parseInt(data.progress * 100); // 上传的进度条
        // this.tableData.filter((item) => item.id === data.uploaderid)[0].progress = p
      },
      addTableData(data, value, Index) {
        console.log(data, value, Index, 'imageFormimageForm');
        this.imageForm[Index].id = data.id;
        this.ruleVideoForm[value] = data.id;
      },
      onFileSucceed(data, value, Index) {
        this.vid = data.fileData.vid;
        if (this.vid) {
          setTimeout(() => {
            courseDictationListApi.getVideoInfo(this.vid).then((res) => {
              if (res.code === 20000) {
                this.ruleVideoForm.videoUrl = this.vid;
                this.imageForm[Index][value] = res.data.data.data[0].basicInfo && res.data.data.data[0].basicInfo.coverURL;
                this.videoUpStatus = false;
                console.log(this.ruleVideoForm.videoUrl, 'videoUrlvideoUrlvideoUrl');
                // console.log(this.imageForm.filter(res => res.name === value)?.status,'121212');
                // this.imageForm.filter(item => item.name === value)?.status = res.data.data.data[0].basicInfo.status
              }
            });
          }, 1000);
        }
      },
      onFileFailed(data) {
        console.log('文件上传失败: ', data);
      },
      onFileStopped(data) {
        console.log('文件上传停止: ', data);
      },
      submitRuleVideoValidate() {
        console.log(this.ruleVideoForm.videoUrl, 'videoUrl-----------------------');
        this.$refs.ruleVideoForm.validate((valid) => {
          if (valid) {
            this.activeCode = 1;
          }
        });
      },
      computeLabel(syllable, ruleWordTypeDtoList, fIndex) {
        // 处理重音节重复字母
        let label = '';
        let repeatCount = 0;
        ruleWordTypeDtoList.forEach((item, Index) => {
          if (item.syllable == syllable) {
            repeatCount++;
            if (Index == fIndex) {
              label = repeatCount > 1 ? `${syllable}[${repeatCount}]` : `${syllable}`;
            }
          }
        });
        return label;
      },
      //禁止输入单词时输入中文和中文符号
      inputChange(val, type) {
        let key = type == '0' ? 'word' : type == '1' ? 'syllable' : 'splitInfo';
        let newValue = val.replace(/[^a-zA-Z,-]/g, '');
        // if (key == "splitInfo") {
        //   console.log(newValue);
        //   if (newValue[newValue.length - 1] == ",") {
        //     console.log(222222222);
        //   }
        // }
        newValue = newValue.replace(/,*$/, '');
        newValue = newValue.replace(/,+/g, ',');
        // 使用replace方法替换掉所有匹配到的中文和中文标点符号为空字符串
        if (this.activeCode === 1) {
          this.stepReadForm[key] = newValue;
        } else {
          this.stepWriteForm[key] = newValue;
        }
      },
      getView(id) {
        console.log(id, this.id);
        courseDictationListApi.getRuleReadAndWrite(this.$route.query.id, this.id, this.spellType).then((res) => {
          if (res.code === 20000) {
            // 2拼读单词 3拼写单词
            this.spellDialogTitile = '编辑';
            this.spellViewForm = res.data.data;
            this.notEditItem = this.spellViewForm.pdWordRuleDto.filter((item) => item.id !== id);
            this.spellViewForm.pdWordRuleDto = this.spellViewForm.pdWordRuleDto.filter((item) => item.id === id);
            this.wordType = res.data.data.wordType;
            this.ruleVideoForm.videoUrl = this.spellViewForm.videoUrl;
            if (this.ruleVideoForm.videoUrl) {
              courseDictationListApi.getVideoInfo(this.ruleVideoForm.videoUrl).then((res) => {
                if (res.code === 20000) {
                  this.videoUpStatus = false;
                  this.imageForm[0].videoUrl = res.data.data.data[0].basicInfo.coverURL;
                  this.imageForm[0].status = res.data.data.data[0].basicInfo.status;
                }
              });
            }
            // 复选框回显
            if (this.isSelect == 1) {
              this.checkList.push(this.spellViewForm.pdWordRuleDto[0].word);
            } else if (this.isSelect == 2) {
              this.checkWriteList.push(this.spellViewForm.pdWordRuleDto[0].word);
            } else if (this.isSelect == 3) {
              this.checkWriteList.push(this.spellViewForm.pdWordRuleDto[0].word);
              this.checkList.push(this.spellViewForm.pdWordRuleDto[0].word);
            }
            if (this.isWriteSelect == 1) {
              this.witerList.push(this.spellViewForm.pdWordRuleDto[0].word);
            }
            //单词回显
            const wordData = this.stepReadForm;
            // 重音回显
            // this.activeCode = this.wordType === 2 ? 0 : 1;
            wordData.word = this.spellViewForm.pdWordRuleDto[0].word;
            this.readTestWords.push({
              id: this.id,
              word: res.data.data.pdWordRuleDto[0].word,
              isSelect: 0,
              isReadSelect: 0,
              isWriteSelect: 0
            });
            this.writeTestWords.push({
              id: this.id,
              word: res.data.data.pdWordRuleDto[0].word,
              isSelect: 0,
              isReadSelect: 0,
              isWriteSelect: 0
            });
            // const syllableData = [...new Set(this.spellViewForm.pdWordRuleDto[0].ruleWordTypeDtoList.map(res=> { return res.syllable}))]
            let syllableData = [];
            this.spellViewForm.pdWordRuleDto[0].ruleWordTypeDtoList.forEach((item) => {
              syllableData.push(item.syllable.replace(/\[.*\]$/, ''));
            });
            wordData.syllable = syllableData.join();
            wordData.splitInfo = this.spellViewForm.pdWordRuleDto[0].splitInfo;
            this.stressReadForm = [];
            this.stressWriteForm = [];
          }
        });
      },
      //下一环节存储拼读拼写
      async submitValidte2(type, num) {
        if (type === 'stressReadForm') {
          this.activeCode = 2;
          this.isShowStress = false;
        } else {
          this.activeCode = 4;
        }
        if (this.stepReadForm.word.indexOf(',') !== -1) {
          //多个单词
          //分割单词
          this.readWord = this.stepReadForm.word.split(',');
          //分割拼读
          this.readWord.forEach((item) => {
            this.readList.push(item);
          });
        } else {
          this.readList.push(this.stepReadForm.word);
        }

        if (this.id) {
          this.readTestForm.word = '';
          this.cities.push(this.stepReadForm.word);
          // 复现选中
          this;
        } else {
          this.cities = [];
          this.cities = this.stepForm.pdWordRuleDto.map((item) => item.word);
        }
      },
      submitValidte5(type, num, value) {
        if (!this.id && this.checkList.length === 0) {
          return this.$message.error('学后读检测词必须选择');
        }
        this.$emit('submitChangeStatus');
        if (this.isSelect) {
        } else {
          this.checkWriteList = [];
        }

        this.writeTestForm.word = '';
        this.activeCode = 4;
      },
      //拼读拼写提交
      async updateSubmitValidte() {
        const refName = this.wordType === 2 ? 'readTestForm' : 'writeTestForm';
        const wordsList = this.wordType === 2 ? this.readTestWords : this.writeTestWords;
        await this.updateWordDetail();
        // await this.testReadSubmitValidte1(3, refName, wordsList)
      },
      async updateWordDetail() {
        if (this.wordType === 2) {
          await this.submitStressValidte();
          await this.testReadSubmitValidte0(2, 'readTestForm', this.readTestWords);
        } else {
          await this.submitStressValidte1('stressWriteForm', '1');
          await this.testReadSubmitValidte0(2, 'readTestForm', this.readTestWords);
        }
      },
      //拼读检测词提交
      async testReadSubmitValidte() {
        await this.submitStressValidte();
        await this.testReadSubmitValidte0(2, 'readTestForm', this.readTestWords);
        // await this.testReadSubmitValidte1(3, 'writeTestForm', this.writeTestWords)
      },
      extractAndRemoveCommonValues(arr1, arr2) {
        // 使用 filter 和 includes 找到并移除arr1中的相同值，同时构造新数组存放相同值
        const commonValues = arr1.filter((value) => arr2.includes(value));
        const arr1WithoutCommon = arr1.filter((value) => !commonValues.includes(value));

        // 移除arr2中的相同值（假设我们想同时处理arr2，使其也不包含相同值）
        const arr2WithoutCommon = arr2.filter((value) => !commonValues.includes(value));

        return { commonValues, arr1WithoutCommon, arr2WithoutCommon };
      },
      async testReadSubmitValidte0(val, refName, wordsList) {
        //   console.log(wordsList, "读");
        let obj = this.extractAndRemoveCommonValues(this.checkList, this.checkWriteList);
        this.allwords.forEach((o) => {
          if (this.witerList.includes(o.word)) {
            o.isWriteSelect = 1;
          }
          if (obj.commonValues.includes(o.word)) {
            o.isSelect = 3;
          }
          if (obj.arr1WithoutCommon.includes(o.word)) {
            o.isSelect = 1;
          }
          if (obj.arr2WithoutCommon.includes(o.word)) {
            o.isSelect = 2;
          }
        });
        try {
          await courseDictationListApi.updateAllRuleWord(this.allwords);
          this.$message.success('操作成功');
          this.$emit('spellDialogClose');
        } catch (e) {
          this.$emit('spellDialogClose');
        }
      },
      filterChange(value, direction, movedKeys) {
        console.log(value, direction, movedKeys, 'filterChangevalue');
      },
      //拼读选择单词
      onReadSelectWord() {
        this.showReadSelectWord = true;
      },
      onWriteSelectWord() {
        this.showWriteSelectWord = true;
      },
      handleCheckedCitiesChange(value) {
        this.readTestForm.word = value.join(',');
        for (let i in value) {
          this.checkread.push({
            word: value[i]
          });
        }
        this.checkread.forEach((res) => {
          res.IsReadSelect = value.find((item) => item === res.word) ? 1 : 0;
        });
      },
      handleCheckedCitiesWrite(value) {
        this.writeTestForm.word = value.join(',');
        for (let i in value) {
          this.checkwrite.push({
            word: value[i]
          });
        }
        this.checkwrite.forEach((res) => {
          res.IsWriteSelect = value.find((item) => item === res.word) ? 1 : 0;
        });
      },
      //左侧选中/取消选中
      leftCheckChange(item, newItem) {
        console.log(item, newItem, 'leftCheckChange');
      },
      //拼读拼写校验
      stepWordValidate(rule, value, callback, data) {
        if (!value) {
          callback(new Error('请输入单词'));
        } else {
          callback();
        }
      },
      //特殊字符检测
      checkSpecialKey(str) {
        let specialKey = "[`~!#$^&*()=|{}':;'\\[\\].<>/?~！#￥……&*（）——|{}【】‘；：”“'。，、？]‘'";
        for (let i = 0; i < str.length; i++) {
          if (specialKey.indexOf(str.substr(i, 1)) != -1) {
            return false;
          }
        }
        return true;
      },
      stepSyllableValidate(rule, value, callback, data) {
        if (!value) {
          callback(new Error('请输入音节'));
        } else {
          console.log(data, 'asdasdas');
          const wordArr = data.word.includes(',') ? data.word.split(',') : [data.word];
          const syllableArr = data.syllable.includes('-') ? data.syllable.split('-') : [data.syllable];
          const words = this.checkSpecialKey(data.syllable);
          if (syllableArr.length !== wordArr.length || !words) {
            callback(new Error('请正确输入音节'));
          } else {
            callback();
          }
        }
      },
      stepSplitInfoValidate(rule, value, callback, data) {
        if (!value) {
          callback(new Error('请输入单词拼读'));
        } else {
          const wordArr = data.word.includes(',') ? data.word.split(',') : [data.word];
          const splitInfoArr = data.splitInfo.includes('-') ? data.splitInfo.split('-') : [data.splitInfo];
          if (splitInfoArr.length !== wordArr.length) {
            callback(new Error('请正确输入单词拼读'));
          } else {
            callback();
          }
        }
      },
      addSuffixToRepeatedSyllables(objects) {
        // 创建一个对象来跟踪每个syllable出现的次数
        const syllableCounts = {};
        return objects.map((obj, index) => {
          const syllable = obj.syllable;

          // 如果syllable已经出现过，则增加计数并添加后缀
          if (syllableCounts[syllable]) {
            syllableCounts[syllable]++;
            return {
              ...obj,
              syllable: `${syllable}[${syllableCounts[syllable]}]`
            };
          } else {
            // 如果是第一次出现，则记录该syllable并设置计数为1
            syllableCounts[syllable] = 1;
            return obj;
          }
        });
      },
      //拼读选择重音
      readStressChange(val, type, event, topIndex) {
        // let ruleWordTypeDtoList = this.addSuffixToRepeatedSyllables(value.ruleWordTypeDtoList);
        // ruleWordTypeDtoList.map((res) => {
        //   (res.type = '0'), (res.status = 0);
        //   if (event.includes(res.syllable)) {
        //     res.type = '1';
        //     res.status = 1;
        //   }
        // });
        // value.ruleWordTypeDtoList = ruleWordTypeDtoList.map((item) => {
        //   if (item.syllable.includes('[')) {
        //     item.too = '[' + item.syllable.split('[')[1];
        //   }
        //   // 使用正则表达式匹配括号及其内的内容，并替换为空字符串
        //   let syllable = item.syllable.replace(/\[.*\]$/, '');
        //   // 返回处理后的对象
        //   return { ...item, syllable }; // 使用扩展运算符来复制其他属性
        // });
        console.log(val, type, event, topIndex, '选择重音');
        let ruleWordTypeDtoList = this.addSuffixToRepeatedSyllables(val.ruleWordTypeDtoList);
        // this.stressForm.stressWord = this.stressReadForm[topIndex].stressWord;
        ruleWordTypeDtoList.map((res) => {
          if (res.type == type) {
            res.type = '0';
          }

          // this.stressForm.stressWord.map(item => {
          //     if(res.syllable === item){
          //         res.type = '1'
          //         res.status = '1'
          //     }
          // })

          if (event.includes(res.syllable)) {
            res.type = type;
            res.status = '1';
          }
        });
        if (type == 1) {
          if (this.stressReadForm[topIndex].secondaryStressedWords.includes(event[event.length - 1])) {
            this.stressReadForm[topIndex].secondaryStressedWords = this.stressReadForm[topIndex].secondaryStressedWords.filter((data) => data != event[event.length - 1]);
          }
        } else {
          if (this.stressReadForm[topIndex].stressWord.includes(event[event.length - 1])) {
            this.stressReadForm[topIndex].stressWord = this.stressReadForm[topIndex].stressWord.filter((data) => data != event[event.length - 1]);
          }
        }
        val.ruleWordTypeDtoList = ruleWordTypeDtoList.map((item) => {
          // 先存入重复的音节
          if (item.syllable.includes('[')) {
            item.too = '[' + item.syllable.split('[')[1];
          }
          // 使用正则表达式匹配括号及其内的内容，并替换为空字符串
          let syllable = item.syllable.replace(/\[.*\]$/, '');
          // 返回处理后的对象
          return { ...item, syllable }; // 使用扩展运算符来复制其他属性
        });
        // console.log(val, '选择重音', this.stressForm.stressWord);
      },
      stepReadValidate(rule, value, callback) {
        if (!value) {
          return callback(new Error('请输入单词'));
        }
      },
      async submitStressValidte() {
        this.stepForm.videoUrl = this.ruleVideoForm.videoUrl;
        // 遍历数组，前面删除了重复音节，提交时在加上传给后台[]
        this.stepForm.pdWordRuleDto.forEach((item) => {
          item.ruleWordTypeDtoList.forEach((e) => {
            if (e.too) {
              e.syllable = e.syllable + e.too;
            }
          });
        });
        console.log(this.stepForm.pdWordRuleDto, 'aaaaaaaaaaaaaaaaa.stepForm.pdWordRuleDto');
        let res;
        //修改逻辑
        try {
          if (this.id) {
            this.stepForm.pdWordRuleDto = this.stepForm.pdWordRuleDto.concat(this.notEditItem);
            this.notEditItem = [];
            if (this.editWordCode) {
              this.$set(this.stepForm.pdWordRuleDto[0], 'wordCode', this.editWordCode);
              this.$emit('restCode', '');
            }

            res = await courseDictationListApi.updateRuleReadAndWrite({
              ...this.stepForm,
              listId: this.id
            });
            this.allwords = [
              res.data.data.map((e) => {
                return {
                  id: e.id,
                  word: e.word,
                  isSelect: 0,
                  isReadSelect: 0,
                  isWriteSelect: 0
                };
              })[0]
            ];
          } else {
            res = await courseDictationListApi.saveRuleReadAndWrite(this.stepForm);
            this.allwords = res.data.data.map((e) => {
              return {
                id: e.id,
                word: e.word,
                isSelect: 0,
                isReadSelect: 0,
                isWriteSelect: 0
              };
            });
          }
        } catch (error) {
          this.$emit('spellDialogClose');
        }
      },
      async submitStressValidte1(refName, type) {
        this.stepForm1.videoUrl = this.ruleVideoForm.videoUrl;
        this.stepForm1.pdWordRuleDto.forEach((item) => {
          item.ruleWordTypeDtoList.forEach((e) => {
            if (e.too) {
              e.syllable = e.syllable + e.too;
            }
          });
        });
        let res;
        if (this.id) {
          this.stepForm1.pdWordRuleDto = this.stepForm1.pdWordRuleDto.concat(this.notEditItem);
          this.notEditItem = [];
          if (this.editWordCode) {
            this.$set(this.stepForm1.pdWordRuleDto[0], 'wordCode', this.editWordCode);
          }
          res = await courseDictationListApi.updateRuleReadAndWrite({
            ...this.stepForm1,
            listId: this.id
          });
          this.allwords = [
            res.data.data.map((e) => {
              return {
                id: e.id,
                word: e.word,
                isSelect: 0,
                isReadSelect: 0,
                isWriteSelect: 0
              };
            })[0]
          ];
          console.log(this.allwords);
        } else {
          res = await courseDictationListApi.saveRuleReadAndWrite(this.stepForm1);
          let arr = res.data.data.map((e) => {
            return {
              id: e.id,
              word: e.word,
              isSelect: 0,
              isReadSelect: 0,
              isWriteSelect: 0
            };
          });
          this.allwords = [...this.allwords, ...arr];
        }
      },
      submitWordValidte(refName, val) {
        this.stressReadForm = [];
        this.$refs[refName].validate(async (valid) => {
          if (valid) {
            await courseDictationListApi.checkSyllableSplit({
              wordSyllable: this.stepReadForm.syllable,
              wordSplit: this.stepReadForm.splitInfo
            });
            this.stepForm = {
              id: this.$route.query.id,
              wordType: '',
              pdWordRuleDto: []
            };
            // 2拼读单词 3拼写单词
            this.stepForm.wordType = '2';
            const data = this.stepReadForm;
            if (data.word.indexOf(',') !== -1) {
              //多个单词
              //分割音节
              this.readSyllableData = data.syllable.split('-');
              this.readSyllableData = this.readSyllableData.map((e) => (e = e.replace(/,*$/, '')));
              //分割单词

              this.wordData = data.word.split(',');
              //分割拼读
              const splitInfoArr = data.splitInfo.split('-');
              //根据后端数据格式处理
              this.stepForm.pdWordRuleDto = [];
              this.wordData.forEach((item, index) => {
                this.stepForm.pdWordRuleDto.push({
                  word: item,
                  splitInfo: splitInfoArr[index],
                  ruleWordTypeDtoList: []
                });
              });

              //相应单词对象中添加音节
              this.readSyllableData.forEach((res, index) => {
                //单词有多个音节
                if (res.indexOf(',') !== -1) {
                  res.split(',').forEach((item) => {
                    this.stepForm.pdWordRuleDto[index].ruleWordTypeDtoList.push({
                      syllable: item,
                      type: '0',
                      status: 0
                    });
                  });
                } else {
                  //单词有单个音节
                  this.stepForm.pdWordRuleDto[index].ruleWordTypeDtoList.push({
                    syllable: res,
                    type: '0',
                    status: 0
                  });
                }
              });
              this.isShowStress = true;
            } else {
              //单个单词/编辑
              this.stepForm.wordType = '2';
              const data = this.stepReadForm;
              this.stepForm.pdWordRuleDto.push({
                word: data.word,
                splitInfo: data.splitInfo,
                ruleWordTypeDtoList: []
              });

              //单个单词中多个音节
              if (data.syllable.indexOf(',') !== -1) {
                data.syllable.split(',').forEach((item) => {
                  this.stepForm.pdWordRuleDto[0].ruleWordTypeDtoList.push({
                    syllable: item,
                    type: '0',
                    status: 0
                  });
                });
              } else {
                //单个单词单个音节
                this.stepForm.pdWordRuleDto[0].ruleWordTypeDtoList.push({
                  syllable: data.syllable,
                  type: '0',
                  status: 0
                });
              }
              this.isShowStress = true;
            }
            //拼读词的重音节的数据绑定
            if (val == '0') {
              this.stepForm.pdWordRuleDto.forEach((item, index) => {
                let stressObj = {
                  index: index,
                  word: item.word,
                  stressWord: [],
                  secondaryStressedWords: []
                };
                this.stressReadForm.push(stressObj);
              });
            }
            // 修改逻辑
            if (this.id) {
              //判断是否修改了音节
              if (this.spellViewForm.pdWordRuleDto[0].ruleWordTypeDtoList.map((e) => e.syllable.replace(/\[.*\]$/, '')).join(',') == this.stepReadForm.syllable) {
                // 回显重音节
                this.spellViewForm.pdWordRuleDto[0].ruleWordTypeDtoList.forEach((e, i) => {
                  console.log(e, i, '回显重音节');
                  if (e.type == 1) {
                    this.stressReadForm[0].stressWord.push(e.syllable);
                    this.readStressChange(this.stepForm.pdWordRuleDto[0], '1', e.syllable, i);
                  } else if (e.type == 2) {
                    this.stressReadForm[0].secondaryStressedWords.push(e.syllable);
                    this.readStressChange(this.stepForm.pdWordRuleDto[0], '2', e.syllable, i);
                  }
                });
              }
              // 判断是否修改单词
              if (this.spellViewForm.pdWordRuleDto[0].word != this.stepReadForm.word) {
                // 清空拼写 学后读 学后写
                this.checkList = [];
                this.checkWriteList = [];
                this.witerList = [];
              }
            }
          }
        });
      },
      submitWordValidte1(refName, val) {
        if (!this.stepWriteForm.word) {
          return (this.activeCode = 3);
        }
        val == 0 ? (this.stressReadForm = []) : (this.stressWriteForm = []);
        this.$refs[refName].validate((valid) => {
          if (valid) {
            this.stepForm1 = {
              id: this.$route.query.id,
              wordType: '',
              pdWordRuleDto: []
            };
            // 2拼读单词 3拼写单词
            this.stepForm1.wordType = '3';
            const data1 = val === '0' ? this.stepReadForm : this.stepWriteForm;

            if (data1.word.indexOf(',') !== -1) {
              //多个单词
              //分割音节
              this.readSyllableData = data1.syllable.split('-');
              //分割单词
              this.wordData = data1.word.split(',');
              //分割拼读
              const splitInfoArr = data1.splitInfo.split('-');
              //根据后端数据格式处理
              this.stepForm1.pdWordRuleDto = [];
              this.wordData.forEach((item, index) => {
                this.stepForm1.pdWordRuleDto.push({
                  word: item,
                  splitInfo: splitInfoArr[index],
                  ruleWordTypeDtoList: []
                });
              });
              //相应单词对象中添加音节
              this.readSyllableData.forEach((res, index) => {
                //单词有多个音节
                if (res.indexOf(',') !== -1) {
                  res.split(',').forEach((item) => {
                    this.stepForm1.pdWordRuleDto[index].ruleWordTypeDtoList.push({
                      syllable: item,
                      type: '0',
                      status: 0
                    });
                  });
                } else {
                  //单词有单个音节
                  this.stepForm1.pdWordRuleDto[index].ruleWordTypeDtoList.push({
                    syllable: res,
                    type: '0',
                    status: 0
                  });
                }
              });
              this.isShowStress = true;
            } else {
              //单个单词/编辑

              this.stepForm1.wordType = '3';
              const data1 = val === '0' ? this.stepReadForm : this.stepWriteForm;
              this.stepForm1.pdWordRuleDto.push({
                word: data1.word,
                splitInfo: data1.splitInfo,
                ruleWordTypeDtoList: []
              });
              //单个单词中多个音节
              if (data1.syllable.indexOf(',') !== -1) {
                data1.syllable.split(',').forEach((item) => {
                  this.stepForm1.pdWordRuleDto[0].ruleWordTypeDtoList.push({
                    syllable: item,
                    type: '0',
                    status: 0
                  });
                });
              } else {
                //单个单词单个音节
                this.stepForm1.pdWordRuleDto[0].ruleWordTypeDtoList.push({
                  syllable: data1.syllable,
                  type: '0',
                  status: 0
                });
              }
              this.isShowStress = true;
            }
            //拼读词的重音节的数据绑定
            if (val == '0') {
              this.stepForm1.pdWordRuleDto.forEach((item, index) => {
                let stressObj = {
                  index: index,
                  word: item.word,
                  stressWord: [],
                  secondaryStressedWords: []
                };
                this.stressReadForm.push(stressObj);
              });
            } else if (val == '1') {
              //拼写词的重音节的数据绑定
              this.stepForm1.pdWordRuleDto.forEach((item, index) => {
                let stressObj = {
                  index: index,
                  word: item.word,
                  stressWord: [],
                  secondaryStressedWords: []
                };
                this.stressWriteForm.push(stressObj);
              });
            }
            // 修改
          }
        });
      },
      //必须是excel文件
      isExcel(file) {
        return /\.(xlsx|xls)$/.test(file.name);
      },
      beforeWordUpload(file) {
        if (!this.isExcel(file)) {
          this.$message.error('只能上传excel文件！');
          return false;
        }
      },
      //单词拼读上传成功
      uploadDetailHttp({ file, fileList }) {
        const that = this;
        this.wordFile = `https://document.dxznjy.com/applet/zhimi/usercode_${file.uid}.xls`;
        const fileType = file.type.substring(file.type.lastIndexOf('/') + 1);
        const fileName = `manage/${Date.parse(new Date())}.${fileType}`;
        that.$nextTick(function () {
          ossPrClient()
            .put(fileName, file)
            .then(({ res, url, name }) => {
              if (res && res.status === 200) {
                console.log(`阿里云OSS上传图片成功回调1`, file, res, url, name);
                const formData = new FormData();
                formData.append('file', file);
                //   formData.append('analysisType','1');
                this.$refs.uploadReadWord.clearFiles();

                courseDictationListApi
                  .analysisFile2(formData)
                  .then((res) => {
                    console.log('返回了？？？？？', res);
                    if (res.code === 20000) {
                      const { word, syllable, split } = { ...res.data.data };
                      if (this.stepReadForm.word) {
                        this.stepReadForm.word = this.stepReadForm.word + ',' + word;
                      } else {
                        this.stepReadForm.word = word;
                      }
                      if (this.stepReadForm.syllable) {
                        this.stepReadForm.syllable = this.stepReadForm.syllable + '-' + syllable;
                      } else {
                        this.stepReadForm.syllable = syllable;
                      }
                      if (this.stepReadForm.splitInfo) {
                        this.stepReadForm.splitInfo = this.stepReadForm.splitInfo + '-' + split;
                      } else {
                        this.stepReadForm.splitInfo = split;
                      }
                      console.log('this.stepReadForm---------', this.stepReadForm);
                    }
                  })
                  .catch((err) => {
                    this.fileListReadWord = [];
                    console.log(`解析失败`, err);
                  });
                that.$nextTick(() => {});
              }
            })
            .catch((err) => {
              that.$message.error('上传图片失败请检查网络或者刷新页面');
              console.log(`阿里云OSS上传图片失败回调`, err);
            });
        });
      },

      //音节拼读上传成功
      handleSetReadSyllableSuccess(response, file, fileList) {
        if (file && file.raw && file.uid) {
          this.readSyllableUrl = `https://document.dxznjy.com/applet/zhimi/usercode_${file.raw.uid}.xls`;
        }
      },
      //音节拼读上传成功
      handleSetReadSplitInfoSuccess(response, file, fileList) {
        if (file && file.raw && file.uid) {
          this.readSplitInfoUrl = `https://document.dxznjy.com/applet/zhimi/usercode_${file.raw.uid}.xls`;
        }
      },
      //单词拼读删除
      handlSetRemoveReadWord() {
        this.readWordUrl = '';
      },
      //音节拼读删除
      handlSetRemoveReadSyllable() {
        this.readSyllableUrl = '';
      },
      //单词拼读删除
      handlSetRemoveReadSplitInfo() {
        this.readSplitInfoUrl = '';
      },
      restForm(form) {
        for (let i in form) {
          form[i] = '';
        }
      },
      //字段制空

      //关闭弹窗
      dialogClose() {
        this.restForm(this.stepReadForm);
        this.restForm(this.stepWriteForm);
        this.restForm(this.stressReadForm);
        this.restForm(this.stressWriteForm);
        if (this.$refs['stepReadForm'] && this.$refs['stepReadForm'].resetFields) {
          this.$refs['stepReadForm'].resetFields();
        }
        if (this.$refs['stepWriteForm'] && this.$refs['stepWriteForm'].resetFields) {
          this.$refs['stepWriteForm'].resetFields();
        }
        if (this.$refs['stressReadForm'] && this.$refs['stressReadForm'].resetFields) {
          this.$refs['stressReadForm'].resetFields();
        }
        if (this.$refs['readTestForm'] && this.$refs['readTestForm'].resetFields) {
          this.$refs['readTestForm'].resetFields();
        }
        if (this.$refs['writeTestForm'] && this.$refs['writeTestForm'].resetFields) {
          this.$refs['writeTestForm'].resetFields();
        }
      }
    }
  };
</script>
<style lang="less" scoped>
  .activeStep {
    padding: 0 80px 16px;
  }

  .download_link {
    // height: 32px;
    // margin: 0;
    // padding: 0 15px;
    color: #13ce66;
  }

  .upload_link {
    color: #1890ff;
  }

  .upload_box {
    // display: flex;
    // justify-content: space-around;
  }
</style>
