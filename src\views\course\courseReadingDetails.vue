<template>
  <div class="app-container">
    <!-- 搜索 -->
    <div class="mb20 pd10" style="background-color: aliceblue;border:  darkgoldenrod;">
      <el-row type="flex" justify="center">
        <el-col :xs="24" :lg="16">
          <el-form :ref="addOrUpdate ? 'addReadingData' : 'updateReadingData'" :rules="rulseReading"
            :model="addOrUpdate ? addReadingData : updateReadingData" label-position="right" label-width="120px"
            style="width: 100%;margin: 0 auto;" :disabled="true">
            <el-form-item label="题目类型">
              <template>
                <el-radio v-model="radio" label="0">
                  {{ courseName }}</el-radio>
              </template>
            </el-form-item>
            <el-form-item label="文章标题" prop="topicTitle">
              <el-col :span="18">
                <el-input v-if="addOrUpdate" v-model="addReadingData.topicTitle">
                </el-input>
                <el-input v-if="!addOrUpdate" v-model="updateReadingData.topicTitle">
                </el-input>
              </el-col>
            </el-form-item>
            <el-form-item label="显示名称" prop="showName">
              <el-col :span="18">
                <el-input v-if="addOrUpdate" v-model="addReadingData.showName">
                </el-input>
                <el-input v-if="!addOrUpdate" v-model="updateReadingData.showName">
                </el-input>
              </el-col>
            </el-form-item>
            <el-form-item label="文章内容" prop="topicContent">
              <el-col :span="18">
                <el-input type="textarea" resize="none" :rows="4" v-if="addOrUpdate"
                  v-model="addReadingData.topicContent" />
                <el-input type="textarea" resize="none" :rows="4" v-if="!addOrUpdate"
                  v-model="updateReadingData.topicContent" />
              </el-col>
            </el-form-item>
            <el-form-item label="建议时间" prop="suggestedTime">
              <el-col :span="18">
                <el-input v-if="addOrUpdate" v-model="addReadingData.suggestedTime">
                </el-input>
                <el-input v-if="!addOrUpdate" v-model="updateReadingData.suggestedTime">
                </el-input>
              </el-col>
            </el-form-item>
            <el-form-item label="排序" prop="sort">
              <el-col :span="18">
                <el-input v-if="addOrUpdate" v-model="addReadingData.sort">
                </el-input>
                <el-input v-if="!addOrUpdate" v-model="updateReadingData.sort">
                </el-input>
              </el-col>
            </el-form-item>
            <el-form-item label="题目难度" prop="level">
              <template>
                <el-radio v-if="addOrUpdate" v-model="addReadingData.level" label="1">★
                </el-radio>
                <el-radio v-if="addOrUpdate" v-model="addReadingData.level" label="2">
                  ★★</el-radio>
                <el-radio v-if="addOrUpdate" v-model="addReadingData.level" label="3">
                  ★★★</el-radio>
                <el-radio v-if="addOrUpdate" v-model="addReadingData.level" label="4">
                  ★★★★</el-radio>
                <el-radio v-if="addOrUpdate" v-model="addReadingData.level" label="5">
                  ★★★★★</el-radio>
                <el-radio v-if="!addOrUpdate" v-model="updateReadingData.level" label="1">★
                </el-radio>
                <el-radio v-if="!addOrUpdate" v-model="updateReadingData.level" label="2">
                  ★★</el-radio>
                <el-radio v-if="!addOrUpdate" v-model="updateReadingData.level" label="3">
                  ★★★</el-radio>
                <el-radio v-if="!addOrUpdate" v-model="updateReadingData.level" label="4">
                  ★★★★</el-radio>
                <el-radio v-if="!addOrUpdate" v-model="updateReadingData.level" label="5">
                  ★★★★★</el-radio>
              </template>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
    </div>

    <div class="mb20" v-for="(item, index) in items" :key="index">
      <el-card style="background-color: aliceblue;">
        <el-row type="flex" justify="center">
          <el-col :xs="24" :lg="16">
            <el-form :rules="rulesAnswers" label-position="right" label-width="120px" :model="item" :disabled="true">
              <el-form-item label="题目编号" prop="id" v-show="false">
                <el-col :span="18">
                  <el-input v-model="item.id"></el-input>
                </el-col>
              </el-form-item>
              <el-form-item label="题目编号" prop="fillNumber">
                <el-col :span="18">
                  <el-input v-model="item.fillNumber"></el-input>
                </el-col>
              </el-form-item>
              <el-form-item label="问题标题" prop="questionText">
                <el-col :span="18">
                  <el-input v-model="item.questionText" type="textarea"></el-input>
                </el-col>
              </el-form-item>
              <el-form-item label="答案A" prop="answerForA">
                <el-col :span="18">
                  <el-input v-model="item.answerForA"></el-input>
                </el-col>
              </el-form-item>
              <el-form-item label="答案B" prop="answerForB">
                <el-col :span="18">
                  <el-input v-model="item.answerForB"></el-input>
                </el-col>
              </el-form-item>
              <el-form-item label="答案C" prop="answerForC">
                <el-col :span="18">
                  <el-input v-model="item.answerForC"></el-input>
                </el-col>
              </el-form-item>
              <el-form-item label="答案D" prop="answerForD">
                <el-col :span="18">
                  <el-input v-model="item.answerForD"></el-input>
                </el-col>
              </el-form-item>
              <el-form-item v-if="courseContentTypeReading == 'Reading'" label="答案E" prop="answerForE">
                <el-col :span="18">
                  <el-input v-model="item.answerForE"></el-input>
                </el-col>
              </el-form-item>
              <el-form-item v-if="courseContentTypeReading == 'Reading'" label="答案F" prop="answerForF">
                <el-col :span="18">
                  <el-input v-model="item.answerForF"></el-input>
                </el-col>
              </el-form-item>
              <el-form-item v-if="courseContentTypeReading == 'Reading'" label="答案G" prop="answerForG">
                <el-col :span="18">
                  <el-input v-model="item.answerForG"></el-input>
                </el-col>
              </el-form-item>
              <el-form-item label="题目正确答案" prop="correctAnswer">
                <el-col :span="18">
                  <el-input v-model="item.correctAnswer"></el-input>
                </el-col>
              </el-form-item>
              <el-form-item label="选项分析" prop="answerRemark">
                <el-col :span="18">
                  <el-input v-model="item.answerRemark" type="textarea"></el-input>
                </el-col>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
      </el-card>
    </div>
  </div>
</template>

<script>
import courseReadingApi from "@/api/courseReading";
export default {
  data() {
    return {
      // 分页
      addReadingData: {},
      updateReadingData: {},
      courseName: '',
      courseCode: '',
      courseContentTypeReading: '',
      addOrUpdate: false,
      items: [],
      //增加内容
      rulseReading: {
        topicTitle: [{
          required: true,
          message: "必填",
          trigger: "blur",
        },],
        showName: [{
          required: true,
          message: "必填",
          trigger: "blur",
        },],
        topicContent: [{
          required: true,
          message: "必填",
          trigger: "blur",
        },],
        suggestedTime: [{
          required: true,
          message: "必填",
          trigger: "blur",
        },],
        sort: [{
          required: true,
          message: "必填",
          trigger: "blur",
        },],
        level: [{
          required: true,
          message: "必填",
          trigger: "blur",
        },],
      },
      items: [{
        fillNumber: '',
        questionText: "",
        answerForA: "",
        answerForB: "",
        answerForC: "",
        answerForD: "",
        answerForE: "",
        answerForF: "",
        answerForG: "",
        correctAnswer: "",
        answerRemark: ""
      }],
      rulesAnswers: {
        fillNumber: [{
          required: true,
          message: "必填",
          trigger: "blur",
        },],
        questionText: [{
          required: true,
          message: "必填",
          trigger: "blur",
        },],
        answerForA: [{
          required: true,
          message: "必填",
          trigger: "blur",
        },],
        answerForB: [{
          required: true,
          message: "必填",
          trigger: "blur",
        },],
        answerForC: [{
          required: true,
          message: "必填",
          trigger: "blur",
        },],
        answerForD: [{
          required: true,
          message: "必填",
          trigger: "blur",
        },],
        correctAnswer: [{
          required: true,
          message: "必填",
          trigger: "blur",
        },],
        answerRemark: [{
          required: true,
          message: "必填",
          trigger: "blur",
        },],

      },
      radio: 0,
      id: '',
      forbidden: false
    }

  },
  created() {
    const that = this;
    that.tableLoading = true;
    that.courseName = window.localStorage.getItem("ourseReadingDetailsCourseName");
    that.courseCode = window.localStorage.getItem("courseReadingDetailsCode");
    that.addReadingData.courseCode = window.localStorage.getItem("courseReadingDetailsCode");
    that.addOrUpdate = JSON.parse(window.localStorage.getItem("addOrUpdateReading"));
    that.id = window.localStorage.getItem("addOrUpdateReadingId");
    that.courseContentTypeReading = window.localStorage.getItem("courseContentTypeReading");
    this.openEdit();
  },
  methods: {
    // 查询+搜索课程列表
    //删除表单
    updateDeleteForm: function (ele) {
      if (this.items.length === 1) {
        this.$message({
          message: '至少要留一个',
          type: 'warning',
          duration: 1000
        })
        return
      }
      var index = this.items.indexOf(ele);
      console.log(index);
      if (index !== -1) {
        this.items.splice(index, 1)
      }
    },
    updateDeleteForm01: function (ele) {
      if (ele.id != null) {
        if (this.items.length === 1) {
          this.$message({
            message: '至少要留一个',
            type: 'warning',
            duration: 1000
          })
          return
        }
        courseReadingApi.deleteCourseReading(ele.id).then(res => {
          var index = this.items.indexOf(ele);
          console.log(index);
          if (index !== -1) {
            this.items.splice(index, 1)
          }
        })
      } else {
        if (this.items.length === 1) {
          this.$message({
            message: '至少要留一个',
            type: 'warning',
            duration: 1000
          })
          return
        }
        var index = this.items.indexOf(ele);
        console.log(index);
        if (index !== -1) {
          this.items.splice(index, 1)
        }
      }
    },
    //增加表单
    addForm: function () {
      this.items.push({
        fillNumber: '',
        questionText: "",
        answerForA: "",
        answerForB: "",
        answerForC: "",
        answerForD: "",
        answerForE: "",
        answerForF: "",
        answerForG: "",
        correctAnswer: "",
        answerRemark: ""
      });
      this.items.forEach(val => {
        console.log(val.answerForA + "wyy");
      })
    },
    //编辑
    // 打开编辑阅读理解和完型填空
    openEdit() {
      courseReadingApi.queryReading(this.id).then((res) => {
        this.addOrUpdate = false;
        this.items = res.data.courseReadingAnswerList;
        this.updateReadingData = res.data.courseReadingCo;
      });
    },
    // 阅读理解和完型填空提交
    addActiveFun(ele, els) {
      const that = this;
      that.$refs[ele].validate((valid) => {
        // 表单验证
        if (valid) {
          const loading = this.$loading({
            lock: true,
            text: "新增阅读理解或完型填空",
            spinner: "el-icon-loading",
            background: "rgba(0, 0, 0, 0.7)",
          });
          const data = {
            courseReadingCo: that.addReadingData,
            courseReadingAnswerList: that.items
          }
          courseReadingApi
            .addCourseReading(data)
            .then((res) => {
              that.dialogVisible = false;
              loading.close();
              that.$router.push({
                path: "/course/courseReading",
                query: {
                  courseCode: this.courseCode,
                  courseName: this.courseName,
                }
              });
              that.$message.success(res.data.data);
            })
            .catch((err) => {
              loading.close();
            });
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    updateReadingFun(ele, els) {
      const that = this;
      that.$refs[ele].validate((valid) => {
        // 表单验证
        if (valid) {

          const loading = this.$loading({
            lock: true,
            text: "修改",
            spinner: "el-icon-loading",
            background: "rgba(0, 0, 0, 0.7)",
          });
          const data = {
            courseReadingCo: that.updateReadingData,
            courseReadingAnswerList: that.items
          }
          courseReadingApi
            .updateWordLevel(data)
            .then((res) => {
              that.dialogVisible = false;
              loading.close();
              this.getList();
            })
            .catch((err) => {
              loading.close();
            });
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    getList() {
      this.$router.push({
        path: "/course/courseReading",
        query: {
          courseCode: this.courseCode,
          courseName: this.courseName,
          courseContentType: "Reading"
        }
      });
    },
    // 状态改变事件
    change(radio) {
      if (radio == "1") {
        this.addReadingData.isEnable = 1;
      } else {
        this.addReadingData.isEnable = 0;
      }
    },

  }
}
</script>

<style>
.lh36 {
  line-height: 36px;
  font-size: 14px;
}

.SearchForm {
  width: 100%;
  margin-bottom: 15px;
  background: #fff;
  border-radius: 6px;
  padding: 5px 0;
  padding-left: 5px;
  box-shadow: 1px 1px 3px rgba(0, 0, 0, .2);
}

.btn-add {
  padding: 5px;
}

.red {
  color: red;
}

.green {
  color: green;
}

.course-table {
  text-align: center;
}

.course-table td,
.course-table th {
  padding: 5px 0;
  text-align: center;
}

.course-table button {
  padding: 2px;
}

.icon-stop {
  float: left;
  display: inline-block;
  width: 12px;
  height: 12px;
  background: url('../../icons/stop.png') no-repeat top center/contain;
}

.mt22 {
  margin-top: 22px;
}
</style>
