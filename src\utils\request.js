import axios from 'axios';
import { Message, MessageBox } from 'element-ui';
import store from '@/store';
import { getToken } from '@/utils/auth';
import Code from '@/utils/code';
import { baseUrl, dxSource } from './constants';
// create an axios instance
const service = axios.create({
  baseURL: baseUrl, // api的base_url
  // baseURL: 'https://api.ngrok.dxznjy.com/',
  //baseURL: 'https://jzc.ngrok.dxznjy.com/',
  // baseURL: 'https://ceshi.ngrok.dxznjy.com/',
  // baseURL: 'https://*************:10005/',
  // baseURL: 'https://*************:10005/',
  //郑读峰 本地
  // baseURL: 'http://*************:8081/',
  // baseURL: 'http://*************:8081/',
  //baseURL: 'https://wangying.ngrok.dxznjy.com/',
  timeout: 60000, // request timeout
  withCredentials: true // 使前台能够保存cookie
});

// request interceptor
service.interceptors.request.use(
  (config) => {
    const host = window.location.host;
    const subdomain = host.split('.')[0];
    const isLocalhost = /^([0-9.]+|localhost):[0-9]+$/.test(host);
    const isNgrokDomain = host.includes('ngrok');
    const isTestDomain = host.includes('test');
    const isManageDomain = subdomain.includes('manage');
    // debugger;
    if (!isLocalhost && !isNgrokDomain && !isTestDomain) {
      // 非本地环境、非 ngrok 环境且非 deliver 相关域名，使用自定义域名
      if (!isManageDomain) {
        const prefix = subdomain.slice(0, -1);
        config.baseURL = `https://${prefix}i.dxznjy.com/`;
      }
    }
    // if (/^[0-9.]+:[0-9]+$/.test(window.location.host)) {
    // } else {
    //   if (
    //     window.location.host.split('.')[0].includes('manage') ||
    //     window.location.host.split('.')[0] == 'test-manage' ||
    //     window.location.host.split('.')[0] == 'uat-manage' ||
    //     window.location.host.split('.')[0] == 'test-znyy'
    //   ) {
    //   } else {
    //     let a = window.location.host.split('.')[0].slice(0, -1);
    //     config.baseURL = `https://${a}i.dxznjy.com/`;
    //   }
    // }

    // Do something before request is sent
    if (store.getters.token) {
      config.headers['x-www-iap-assertion'] = getToken(); // 让每个请求携带token-- ['X-Token']为自定义key 请根据实际情况自行修改
    }
    config.headers['temp-dx-source'] = dxSource;
    config.headers['dx-source'] = dxSource;
    config.headers['www-cid'] = 'dx_znyy_resource';

    return config;
  },
  (error) => {
    // Do something with request error
    console.log(error); // for debug
    Promise.reject(error);
  }
);
// respone interceptor
service.interceptors.response.use(
  /**
   * 下面的注释为通过response自定义code来标示请求状态，当code返回如下情况为权限有问题，登出并返回到登录页
   * 如通过xmlhttprequest 状态码标识 逻辑可写在下面error中
   */
  /**
   *
   >>>>>>> 54abb52da5593455459f6fc272e3cd93a44dbb6a
   * if (res.code !== 20000) {
      Message({
        message: res.message || 'Error',
        type: 'error',
        duration: 5 * 1000
      })

      // 50008: Illegal token; 50012: Other clients logged in; 50014: Token expired;
      if (res.code === 50008 || res.code === 50012 || res.code === 50014) {
        // to re-login
        MessageBox.confirm('You have been logged out, you can cancel to stay on this page, or log in again', 'Confirm logout', {
          confirmButtonText: 'Re-Login',
          cancelButtonText: 'Cancel',
          type: 'warning'
        }).then(() => {
          store.dispatch('user/resetToken').then(() => {
            location.reload()
          })
        })
      }
      return Promise.reject(new Error(res.message || 'Error'))
   */
  (res) => {
    // console.log(res.data.success);
    // res.data.success = true;
    // return this.form.childQuestions.length >= 9;;

    // TYPE_LIST 用于检查响应是否为文件下载
    const TYPE_LIST = ['application/octet-stream', 'application/vnd.ms-excel'];
    if (TYPE_LIST.includes(res.data.type)) {
      return res.data;
    }
    if (res.data.succ || res.data.success || res.data.status === 1) {
      //console.log(JSON.stringify(res.data)+"wyy");

      // 如果后台返回的json显示成功，pass
      return res.data;
    } else {
      if (res.data.code == Code.UNAUTHEN || res.data.code == Code.SESSION_TIMOUT) {
        // 处理登录相关的错误
        MessageBox.confirm('你已被登出，可以取消继续留在该页面，或者重新登录', '确定登出', {
          confirmButtonText: '重新登录',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          store.dispatch('FedLogOut').then(() => {
            location.reload(); // 为了重新实例化vue-router对象 避免bug
          });
        });
      } else {
        console.log(res.data.message, 'resresres');
        // 其它错误弹出错误信息
        // return Promise.reject(new Error(res.message || 'Error'))
        // console.log(res);

        // console.log('12313')
        if (res.data.code == 80002) {
          Message({ message: res.data.message, type: 'error', duration: 2000 });
          return Promise.reject(res.data);
        } else if (res.data.code == 82001 || res.data.code == 50010) {
          return Promise.reject(res.data);
        } else if (res.data.code == 50005) {
          console.log('🚀🥶💩~ res.data.code', res.data.code);
          Message.error(res.data.message);
        }
        else {
          if (res.data.message) Message({ message: res.data.message, type: 'error', duration: 2000 });
        }
      }
      return Promise.reject('error');
    }
  },

  /**
   * 请求发生错误，一般都是服务器抛异常了
   */
  (err) => {
    if (err && err.response) {
      console.log(err, 'err');
      console.log(err.response.data, 'err.response.data');
      if (err.response.data.code == 50005) {
        Message.error(err.response.data.message);
        setTimeout(() => {
          store.dispatch('FedLogOut').then(() => {
            location.reload(); // 为了重新实例化vue-router对象 避免bug
          });
        }, 100);
        return;
      }
      if (err.response.data.message) {
        switch (err.response.status) {
          case 400:
            err.message = err.response.data.message;
            break;
          case 401:
            err.message = err.response.data.message;
            break;
          case 403:
            err.message = err.response.data.message;
            break;
          case 404:
            err.message = err.response.data.message;
            break;
          case 408:
            err.message = err.response.data.message;
            break;
          case 500:
            err.message = err.response.data.message;
            break;
          case 501:
            err.message = err.response.data.message;
            break;
          case 502:
            err.message = err.response.data.message;
            break;
          case 503:
            err.message = err.response.data.message;
            break;
          case 504:
            err.message = err.response.data.message;
            break;
          case 505:
            err.message = err.response.data.message;
            break;
          default:
            err.message = `连接出错(${err.response.status})!`;
        }
      } else {
        switch (err.response.status) {
          case 400:
            err.message = '请求错误(400)';
            break;
          case 401:
            err.message = '未授权，请重新登录(401)';
            break;
          case 403:
            err.message = '账户没有权限';
            break;
          case 404:
            err.message = '请求出错(404)';
            break;
          case 408:
            err.message = '请求出错(408)';
            break;
          case 500:
            err.message = '服务器错误';
            break;
          case 501:
            err.message = '服务未实现(501)';
            break;
          case 502:
            err.message = '网络错误(502)';
            break;
          case 503:
            err.message = '服务不可用(503)';
            break;
          case 504:
            err.message = '网络超时(504)';
            break;
          case 505:
            err.message = 'HTTP版本不受支持(505)';
            break;
          case 80000:
            err.message = '147258369';
            break;
          default:
            err.message = `连接出错(${err.response.status})!`;
        }
      }
    } else {
      err.message = '连接服务器失败!';
    }
    if (!err.response.data.message) {
      try {
        const reader = new FileReader();
        reader.onload = function (event) {
          const mymessage = JSON.parse(reader.result).message;
          if (mymessage) {
            Message({ message: mymessage, type: 'error', duration: 3000 });
          } else {
            Message({ message: err.message, type: 'error', duration: 3000 });
          }
        };
        reader.readAsText(err.response.data);
      } catch (e) {
        Message({ message: err.message, type: 'error', duration: 3000 });
      }
    } else {
      Message({ message: err.message, type: 'error', duration: 3000 });
    }
    return Promise.reject('error');
  }
);

export default service;
