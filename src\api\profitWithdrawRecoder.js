
/**
 *
 */
import request from '@/utils/request'

export default {
  // 分润账户新增
  addMarketProfitAmount(data) {
    return request({
      url: '/znyy/market/profit/amount/add/profit',
      method: 'POST',
      data
    })
  },
  // 分入账户修改编辑
  updateMarketProfitAmount(data) {
    return request({
      url: '/znyy/market/profit/amount/add/profit',
      method: 'POST',
      data
    })
  },

  // 推荐人查询
  queryActive(id) {
    return request({
      url: '/znyy/market/profit/amount/check/detail/' + id,
      method: 'GET'
    })
  },

  // 提现完成
  update(id,status) {
    return request({
      url: '/znyy/profit/account/update/' + id +'/'+status,
      method: 'PUT'
    })
  },
  profitAccountList(pageNum, pageSize, data) {
    return request({
      url: '/znyy/profit/account/list/' + pageNum + '/' + pageSize,
      method: 'GET',
      params: data
    })
  },
  marketWithdrawRecoderList(pageNum, pageSize, data) {
    return request({
      url: '/znyy/profit/account/page/list/' + pageNum + '/' + pageSize,
      method: 'GET',
      params: data
    })
  },
  getCurrentTime() {
    var _this = this;
    let yy = new Date().getFullYear();
    let mm = new Date().getMonth() + 1;
    let dd = new Date().getDate();
    let hh = new Date().getHours();
    let mf = new Date().getMinutes() < 10 ? '0' + new Date().getMinutes() : new Date().getMinutes();
    let ss = new Date().getSeconds() < 10 ? '0' + new Date().getSeconds() : new Date().getSeconds();
    _this.gettime = yy + '-' + mm + '-' + dd + ' ' + hh + ':' + mf + ':' + ss;
    return _this.gettime
  }
}
