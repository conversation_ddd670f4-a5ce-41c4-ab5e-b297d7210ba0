<template>
  <div class="app-container">
    <!-- 搜索 -->
    <el-form :inline="true" ref="form" class="SearchForm">
      <el-row style="padding: 20px 20px 0 20px">
        <el-col :span="8" :xs="24">
          <el-form-item label="课程分类编号:">
            <el-input v-model="dataQuery.categoryCode" @keyup.enter.native="fetchData()" placeholder="请输入课程分类编号：" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="课程分类名称: ">
            <el-input v-model="dataQuery.categoryName" @keyup.enter.native="fetchData()" placeholder="课程分类名称: " clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24" style="text-align: right">
          <el-button type="warning" icon="el-icon-search" @click="fetchData01()">搜索</el-button>
        </el-col>
      </el-row>
    </el-form>

    <div class="SearchForm">
      <!-- 添加 -->
      <div class="btn-add" style="margin-bottom: 10px">
        <el-button size="small" type="primary" icon="el-icon-plus" @click="clickAdd">添加</el-button>
      </div>
      <!-- 表格 -->
      <el-table class="common-table" :data="tableData" stripe border :default-sort="{prop: 'date', order: 'descending'}">
        <el-table-column prop="categoryCode" label="分类编号" sortable width="150px"></el-table-column>
        <el-table-column prop="categoryName" label="分类名称" sortable></el-table-column>
        <el-table-column prop="id" label="操作" sortable width="300">
          <template slot-scope="scope">
            <el-button type="success" size="mini" icon="el-icon-edit-outline" @click="handleUpdate(scope.row.id)">编辑</el-button>
            <el-button type="warning" size="mini" icon="el-icon-switch-button" v-if="scope.row.isEnable === 0" @click="courseStatus(scope.row.id,scope.row.isEnable)">开通</el-button>
            <el-button type="danger" size="mini" icon="el-icon-video-pause" v-else @click="courseStatus(scope.row.id,scope.row.isEnable)">暂停</el-button>
            <el-button @click="enterChildrenList(scope.row.categoryName,scope.row.categoryCode)" type="primary" size="mini" icon="el-icon-view">进入子类</el-button>
          </template>
        </el-table-column>
        <el-table-column prop="addTime" label="添加时间" sortable></el-table-column>
        <el-table-column prop="isEnable" label="状态" sortable>
          <template slot-scope="scope">
            <span class="green" v-if="scope.row.isEnable === 1">开通</span>
            <span class="red" v-else>暂停</span>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <el-col :span="20">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]" layout="total, sizes, prev, pager, next, jumper"
                     :total="tablePage.totalItems" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    </el-col>

    <!-- 编辑或者添加弹窗 -->
    <el-dialog :title="addOrUpdate?'添加课程分类':'编辑课程分类'" :visible.sync="dialogVisible" width="70%" :close-on-click-modal="false"
               @close="close">
      <el-form :ref="addOrUpdate?'addCourseData':'updateActive'" :rules="rules" :model="addOrUpdate?addCourseData:updateActive"
               label-position="left" label-width="120px" style="width: 100%;">
        <el-form-item label="分类名称" prop="categoryName">
          <el-col :xs="24" :lg="18">
            <el-input v-if="addOrUpdate" v-model="addCourseData.categoryName" />
            <el-input v-if="!addOrUpdate" v-model="updateActive.categoryName" />
          </el-col>
        </el-form-item>
        <el-form-item label="课程描述" prop="categoryDescription">
          <el-col :xs="24" :lg="18">
            <el-input type="textarea" resize="none" :rows="4" v-if="addOrUpdate" v-model="addCourseData.categoryDescription" />
            <el-input type="textarea" resize="none" :rows="4" v-if="!addOrUpdate" v-model="updateActive.categoryDescription" />
          </el-col>
        </el-form-item>
        <el-form-item label="图标" prop="icon">
          <el-col :xs="24" :lg="18">
            <el-upload ref="clearupload" v-loading="uploadLoading" list-type="picture-card" action=""
                       element-loading-text="图片上传中" :limit="1" :on-exceed="justPictureNum" :file-list="!addOrUpdate? fileDetailList: fileDetailList.name"
                       :http-request="uploadDetailHttp" :on-preview="handlePictureCardPreview" :on-remove="handleRemoveDetail">
              <i class="el-icon-plus" />
            </el-upload>
          </el-col>
        </el-form-item>
        <el-form-item label="状态" prop="isEnable">
          <template>
            <el-radio v-model="radio" label="1" @change="change(radio)">开通</el-radio>
            <el-radio v-model="radio" label="0" @change="change(radio)">暂停</el-radio>
          </template>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button v-if="addOrUpdate" size="mini" type="primary" @click="addActiveFun('addCourseData')">新增</el-button>
        <el-button v-if="!addOrUpdate" size="mini" type="primary" @click="updateActiveFun('updateActive')">修改</el-button>
        <el-button size="mini" @click="close">关闭</el-button>
      </div>
    </el-dialog>

    <el-dialog :visible.sync="dialogUploadVisible" :close-on-click-modal="false">
      <img width="100%" :src="dialogImageUrl" alt>
    </el-dialog>
  </div>
</template>

<script>
import courseApi from '@/api/courseCategory'
import {
  pageParamNames
} from "@/utils/constants";
import {
  ossPrClient
} from '@/api/alibaba'

export default {
  data() {
    return {
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      tableLoading: false,
      dataQuery: {
        categoryCode: '',
        categoryName: ''
      },
      activeType: [], // 活动类型

      tableData: [], //表格数据
      dialogVisible: false, // 修改弹窗是否展示
      addOrUpdate: true, // 是新增还是修改
      addCourseData: {}, // 新增课程
      updateActive: {}, // 修改数据
      rules: { // 表单提交规则
        categoryName: [{
          required: true,
          message: '请填写分类名称',
          trigger: 'blur'
        }],
        categoryDescription: [{
          required: true,
          message: '请填写课程描述',
          trigger: 'blur'
        }],
        isEnable: [{
          required: true,
          message: '必填',
          trigger: 'change'
        }],
        icon: [{
          required: true,
          message: '请上传图标',
          trigger: 'change'
        }]
      },

      fileListPending: [], // 待处理已上传图片信息
      fileList: [], // 上传图片已有图片列表

      fileDetailListPending: [], // 待处理已上传图片信息
      fileDetailList: [], // 上传图片已有图片列表

      dialogUploadVisible: false,
      dialogImageUrl: '', // 上传图片预览

      uploadLoading: false, // 上传图片加载按钮
      fullscreenLoading: false, // 保存啥的加载

      content: '',
      isUploadSuccess: true, // 是否上传成功
      isShowRelevance: true, // 新增或修改是否展示关联产品

      radio: '0', //单选框状态 值必须是字符串
      gettime: "" //获取当前时间
    }

  },
  created() {
    this.fetchData01();
    ossPrClient();
  },
  methods: {
    fetchData01() {
      this.tablePage = {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      }
      this.fetchData();
    },
    // 查询+搜索课程列表
    fetchData() {
      const that = this
      that.tableLoading = true
      courseApi.courseList(that.tablePage.currentPage, that.tablePage.size, that.dataQuery).then(res => {
        that.tableData = res.data.data
        // console.log(res)
        that.tableLoading = false
        // 设置后台返回的分页参数
        pageParamNames.forEach(name => that.$set(that.tablePage, name, parseInt(res.data[name])))
      })
    },
    //添加操作
    clickAdd() {
      this.addCourseData = {
        'imageListFilePath': '',
        'imageInfoFilePath': '',
        // 'addTime' :'',
        'categoryCode': '',
        'categoryDescription': '',
        'categoryName': '',
        // 'dbStatus' : '',
        'icon': '',
        'isEnable': 0,
      }
      this.radio = '0'
      this.dialogVisible = true
      this.addOrUpdate = true
      if (this.fileList.length !== 0) {
        this.$refs.clearupload.clearFiles()
      }
      this.$nextTick(() => this.$refs['addCourseData'].clearValidate())
    },
    // 新增课程提交
    addActiveFun(ele) {
      const that = this
      that.$refs[ele].validate(valid => { // 表单验证
        if (valid) {
          const loading = this.$loading({
            lock: true,
            text: '新增课程',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          })
          courseApi.addCourse(that.addCourseData).then(() => {
            that.dialogVisible = false
            loading.close()
            that.$nextTick(() => that.fetchData())
            that.$message.success('新增课程成功')
          }).catch(err => {
            loading.close()
          })
        } else {
          console.log('error submit!!')
          //loading.close();
          return false
        }
      })
    },
    // 点击编辑按钮
    handleUpdate(id) {
      const that = this
      that.dialogVisible = true
      that.addOrUpdate = false
      courseApi.queryActive(id).then(res => {
        that.updateActive = res.data
        that.radio = that.updateActive.isEnable.toString(); //状态回显
        // console.log(that.updateActive)
        if (that.updateActive.icon !== null && that.updateActive.icon.length > 1) {
          that.fileDetailList = [{
            url: that.aliUrl + that.updateActive.icon
          }]
        } else {
          that.fileDetailList = []
        }
      }).catch(err => {

      })
    },
    // 状态改变事件
    change(radio) {
      if (radio == "1") {
        this.addCourseData.isEnable = 1;
      } else {
        this.addCourseData.isEnable = 0;
      }
    },
    // 修改课程提交
    updateActiveFun(ele) {
      const that = this
      if (that.fileDetailList.length > 0) {
        const b = that.fileDetailList[0].url.split('manage/')
        that.updateActive.icon = 'manage/' + b[b.length - 1]
        that.updateActive.isEnable = that.radio;
      }

      that.$refs[ele].validate(valid => { // 表单验证
        if (valid) {
          const loading = this.$loading({
            lock: true,
            text: '修改课程信息提交',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          })
          courseApi.updateCourse(that.updateActive).then(() => {
            that.dialogVisible = false
            loading.close()
            that.$nextTick(() => that.fetchData())
            that.$message.success('修改课程成功')
          }).catch(err => {
            // 关闭提示弹框
            loading.close()
          })
        } else {
          console.log('error submit!!')
          // loading.close();
          return false
        }
      })
    },
    // 课程开通与暂停
    courseStatus(id, status) {
      if (status == 0) {
        status = 1;
      } else {
        status = 0
      }
      const that = this;
      this.$confirm('确定操作吗?', '修改状态', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        courseApi.updateStatus(id, status).then(res => {
          that.$nextTick(() => that.fetchData())
          that.$message.success('修改成功!')
        }).catch(err => {

        })
      }).catch(err => {

      })
    },

    // 删除上传图片
    handleRemove(file, fileList) {
      const that = this
      if (!that.addOrUpdate) {
        that.fileList = fileList
      } else {
        for (let a = 0; a < that.fileDetailList4.length; a++) {
          if (that.fileDetailList4[a].substring(7,17) == parseInt(file.uid/1000)){
            that.fileDetailList4.splice(a, 1)
          }
        }
      }
    },

    // 删除上传图片
    handleRemoveDetail(file, fileList) {
      const that = this
      if (!that.addOrUpdate) {
        that.fileDetailList = fileList
      } else {
        for (let a = 0; a < that.fileDetailList4.length; a++) {
          if (that.fileDetailList4[a].substring(7,17) == parseInt(file.uid/1000)){
            that.fileDetailList4.splice(a, 1)
          }
        }
      }
    },
    // 上传图片预览
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogUploadVisible = true
    },
    // 上传图片数量超限
    justPictureNum(file, fileList) {
      this.$message.warning(`当前限制选择1个文件`)
    },
    // 上传图片请求
    uploadPrHttp({
      file
    }) {
      this.uploadLoading = true
      const that = this
      const fileName = 'manage/' + Date.parse(new Date())
      that.$nextTick(function() {
        ossPrClient().put(fileName, file).then(({
          res,
          url,
          name
        }) => {
          if (res && res.status === 200) {
            console.log(`阿里云OSS上传图片成功回调1`, res, url, name)
            if (!that.addOrUpdate) {
              that.fileList.push({
                'uid': file.uid,
                'url': url
              })
            } else { // 新增上传图片
              that.fileList.push({
                name
              })
              that.addCourseData.imageListFilePath = name
            }
            that.$nextTick(() => {
              that.uploadLoading = false
            })
          }
        }).catch(err => {
          that.$message.error('上传图片失败请检查网络或者刷新页面')
          console.log(`阿里云OSS上传图片失败回调`, err)
        })
      })
    },

    uploadDetailHttp({
      file
    }) {
      this.uploadLoading = true
      const that = this
      const fileName = 'manage/' + Date.parse(new Date())
      that.$nextTick(function() {
        ossPrClient().put(fileName, file).then(({
          res,
          url,
          name
        }) => {
          if (res && res.status === 200) {
            console.log(`阿里云OSS上传图片成功回调1`, res, url, name)
            if (!that.addOrUpdate) {
              that.fileDetailList.push({
                'uid': file.uid,
                'url': url
              })
            } else { // 新增上传图片
              that.fileDetailList.push({
                name
              })
              that.addCourseData.icon = name
            }
            that.$nextTick(() => {
              that.uploadLoading = false
            })
          }
        }).catch(err => {
          that.$message.error('上传图片失败请检查网络或者刷新页面')
          console.log(`阿里云OSS上传图片失败回调`, err)
        })
      })
    },
    //进入子类
    enterChildrenList(categoryName, categoryCode) {
      const that = this;
      that.$router.push({
        path: "/course/courseChildrenList",
        query: {
          categoryName: categoryName,
          categoryCode: categoryCode
        }
      });
      window.localStorage.setItem("categoryName", categoryName);
      window.localStorage.setItem("categoryCode", categoryCode);
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val;
      this.fetchData();
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val;
      this.fetchData();
    },
    // 关闭弹窗
    close() {
      this.dialogVisible = false
    }
  }
}
</script>

<style >
.lh36 {
  line-height: 36px;
  font-size: 14px;
}

.SearchForm {
  width: 100%;
  margin-bottom: 15px;
  background: #fff;
  border-radius: 6px;
  padding: 5px 0;
  padding-left: 5px;
  box-shadow: 1px 1px 3px rgba(0, 0, 0, .2);
}

.btn-add {
  padding: 5px;
}

.red {
  color: red;
}

.green {
  color: green;
}

.course-table {
  text-align: center;
}

.course-table td,
.course-table th {
  padding: 5px 0;
  text-align: center;
}

.course-table button {
  padding: 2px;
}

.icon-stop {
  float: left;
  display: inline-block;
  width: 12px;
  height: 12px;
  background: url('../../icons/stop.png') no-repeat top center/contain;
}

.mt22 {
  margin-top: 22px;
}

@media screen and (max-width: 767px) {
  .el-upload-list--picture-card .el-upload-list__item,
  .el-upload--picture-card{
    width: 100px;
    height: 100px;
    line-height: 100px;
  }
  .el-message-box{
    width: 80%!important;
  }
}

</style>
