import request from "@/utils/request";

export default {
  list(data){
    return request({
      url: '/xi/web/subject/list',
      method: 'GET',
      params: data
    })
  },
  saveOrUpdate(data){
    return request({
      url: '/xi/web/subject/saveOrUpdate',
      method: 'POST',
      params: data
    })
  },
  detail(id){
    return request({
      url: '/xi/web/subject/detail',
      method: 'GET',
      params: {
        id: id
      }
    })
  },
  delete(id){
    return request({
      url: '/xi/web/subject',
      method: 'DELETE',
      params: {
        id: id
      }
    })
  }
}
