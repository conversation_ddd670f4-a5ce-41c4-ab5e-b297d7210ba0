<template>
  <div class="app-container">
    <el-form :inline="true" class="container-card" label-width="110px" label-position="left">
      <el-form-item label="体验中心编号：">
        <el-input v-model="dataQuery.merchantCode" placeholder="请输入体验中心编号：" clearable />
      </el-form-item>
      <el-form-item label="登录账号：">
        <el-input v-model="dataQuery.name" placeholder="请输入登录账号：" clearable />
      </el-form-item>
      <el-form-item label="体验中心名称：">
        <el-input v-model="dataQuery.merchantName" placeholder="请输入体验中心名称：" clearable />
      </el-form-item>
      <el-form-item label="负责人：">
        <el-input v-model="dataQuery.realName" placeholder="请输入负责人：" clearable />
      </el-form-item>
      <el-form-item label="上级编号：" v-if="roleTag === 'admin'">
        <el-input v-model="dataQuery.refereeCode" placeholder="请输入上级编号：" clearable />
      </el-form-item>
      <el-form-item label="添加时间：">
        <el-date-picker style="width: 100%;" v-model="value1" value-format="yyyy-MM-dd HH:mm:ss" type="daterange"
          align="right" unlink-panels range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" />
      </el-form-item>
      <el-form-item label="推荐人编号：">
        <el-input v-model="dataQuery.marketPartner" placeholder="请输入推荐人编号：" clearable />
      </el-form-item>
      <el-form-item label="状态：">
        <el-select v-model="dataQuery.isEnable" value-key="value" placeholder="请选择" style="width: 200px" clearable>
          <el-option v-for="(item, index) in [
            { label: '开通', value: 1 },
            { label: '暂停', value: 0 },
            { label: '系统关闭', value: -1 },
          ]" :key="index" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="fetchData01()">搜索</el-button>
      </el-form-item>
    </el-form>

    <el-col :span="24" style="margin-bottom: 30px">
      <el-button type="success" icon="el-icon-plus" size="mini" v-if="checkPermission(['b:merchant:experienceList:add'])"
        @click="clickAdd">添加</el-button>
      <el-button type="warning" icon="el-icon-document-copy" size="mini" v-if="false" @click="exportList()">导出
      </el-button>

    </el-col>
    <!-- 渲染表格 -->
    <el-table v-loading="tableLoading" class="common-table" :data="tableData" hstyle="width: 100%; margin-bottom: 20px"
      row-key="id" border default-expand-all :tree-props="{ list: 'children', hasChildren: 'true' }">
      <el-table-column prop="merchantCode" label="编号" align="center" width="100px" />
      <el-table-column prop="name" label="登陆账号" align="center" width="120px" />
      <el-table-column label="操作" align="center" width="300">
        <template slot-scope="scope">
          <el-button type="success" icon="el-icon-edit-outline" size="mini"
            v-if="checkPermission(['b:merchant:experienceList:edit'])" @click="update(scope.row.id, false)">编辑</el-button>
          <el-button type="success" icon="el-icon-edit-outline" size="mini"
            v-if="checkPermission(['b:merchant:experienceList:edit']) && scope.row.flowIsEnd == 1 && scope.row.flowEndStatus == 0"
            @click="update(scope.row.id, true)">重新提交</el-button>
          <!--          <el-button type="primary" icon="el-icon-edit-outline" size="mini" v-if="scope.row.isCheck == 0 && roleTag==='admin'" @click="openExamine(scope.row.id)">审核</el-button>
                    <el-button type="primary" icon="el-icon-edit-outline" size="mini" v-if="scope.row.isCheck == 4 && roleTag==='Company'"  @click="openExamine(scope.row.id)">审核</el-button>-->
          <el-button type="warning" size="mini" icon="el-icon-switch-button"
            v-if="(scope.row.isEnable === 0 && scope.row.flowEndStatus === 1) || scope.row.isEnable === -3"
            @click="agentStatus(scope.row.id, 1)">开通
          </el-button>
          <el-button type="danger" size="mini" icon="el-icon-video-pause"
            v-if="scope.row.isEnable === 1 && scope.row.isEnable === 1" @click="agentStatus(scope.row.id, 0)">暂停
          </el-button>
          <el-button type="danger" size="mini" icon="el-icon-video-pause"
            v-if="checkPermission(['b:merchant:experienceList:termination']) && scope.row.isEnable === 1 && scope.row.isEnable === 1"
            @click="agentStatus(scope.row.id, -3)">终止
          </el-button>
          <el-button type="warning" size="mini" icon="el-icon-switch-button" v-if="scope.row.paymentIsComplete === 0"
            @click="allGone(scope.row.id)">完款
          </el-button>
          <el-button type="danger" size="mini" icon="el-icon-circle-check"
            v-if="checkPermission(['b:risk:user:clear']) && (scope.row.isEnable === -1 || scope.row.isEnable === -2)"
            @click="agentStatus(scope.row.id, 1)">解封
          </el-button>
        </template>
      </el-table-column>
      <el-table-column prop="merchantName" label="名称" align="center" min-width="120px" />
      <el-table-column prop="realName" label="负责人" align="center" width="100px" />
      <el-table-column prop="marketPartner" label="推荐人" align="center" width="100px" />
      <el-table-column v-if="sort" prop="serialNumber" label="排名" align="center" width="100px" />
      <el-table-column prop="refereeCode" label="上级编号" align="center" width="100px" />
      <el-table-column prop="sumChargeMoney" label="累计充值金额（元）" align="center" width="110px">
        <template slot-scope="scope">
          <span v-if="scope.row.sumChargeMoney === null ||
            scope.row.sumChargeMoney === ''
            ">0.00</span>
          <span v-else>{{ scope.row.sumChargeMoney }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="sumRebateMoney" label="累计返点金额（元）" align="center" width="110px">
        <template slot-scope="scope">
          <span v-if="scope.row.sumRebateMoney === null ||
            scope.row.sumRebateMoney === ''
            ">0.00</span>
          <span v-else>{{ scope.row.sumRebateMoney }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="sumGiveMoney" label="累计赠送金额（元）" align="center" width="110px">
        <template slot-scope="scope">
          <span v-if="scope.row.sumGiveMoney === null || scope.row.sumGiveMoney === ''
            ">0.00</span>
          <span v-else>{{ scope.row.sumGiveMoney }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="accountMoney" label="账户余额（元）" align="center" width="110px">
        <template slot-scope="scope">
          <span v-if="scope.row.accountMoney === null || scope.row.accountMoney === ''
            ">0.00</span>
          <span v-else>{{ scope.row.accountMoney }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="paymentIsComplete" label="是否完款" align="center">
        <template slot-scope="scope">
          <span v-if="scope.row.paymentIsComplete === 1" class="green">完款</span>
          <span v-else class="red">未完款</span>
        </template>
      </el-table-column> <el-table-column prop="flowIsEnd" label="审核状态">
        <template slot-scope="scope">
          <span class="green" v-if="scope.row.flowIsEnd === 1">审核流程结束</span>
          <span class="red" v-if="scope.row.flowIsEnd === 0">审核中</span>
        </template>
      </el-table-column>
      <el-table-column prop="flowStatus" label="流程状态" align="center">
      </el-table-column>
      <el-table-column prop="isEnable" label="账户状态" align="center">
        <template slot-scope="scope">
          <span v-if="scope.row.isEnable === -1" class="red">系统关闭</span>
          <span v-else-if="scope.row.isEnable === -3" class="red">终止</span>
          <span v-else-if="scope.row.isEnable === 1" class="green">开通</span>
          <span v-else class="red">暂停</span>
        </template>
      </el-table-column>
      <el-table-column prop="regTime" label="添加时间" align="center" />
    </el-table>

    <!-- 审核页面 -->
    <el-dialog title="审核" :visible.sync="showExamine" width="70%" :close-on-click-modal="false" @close="closeExamine">
      <el-form :ref="examine" :rules="rules" :model="examine" label-position="left" label-width="100px"
        style="width: 100%">
        <el-form-item label="是否通过：" prop="isCheck">
          <template>
            <el-radio v-model="isCheck" label="1" @change="change(isCheck)">通过</el-radio>
            <el-radio v-model="isCheck" label="0" @change="change(isCheck)">不通过</el-radio>
          </template>
        </el-form-item>
        <el-form-item label="体验中心级别：" prop="rank" v-if="roleTag === 'admin'">
          <el-col :xs="24" :span="12">
            <el-select v-model="examine.rank" placeholder="请选择" style="width: 100%">
              <el-option v-for="item in grandNameList" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-col>
        </el-form-item>
        <el-form-item label="审核意见">
          <el-col :xs="24" :span="18">
            <el-input type="textarea" resize="none" :rows="4" v-model="examine.checkReason" />
          </el-col>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="
          toExamine(examine.id, examine.checkReason, examine.isCheck, examine, examine.rank)
          ">确定
        </el-button>
        <el-button size="mini" @click="closeExamine">关闭</el-button>
      </div>
    </el-dialog>

    <!-- 分页 -->
    <el-col :span="20">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>
  </div>
</template>

<script>
import authenticationApi from '@/api/authentication'
import { pageParamNames } from "@/utils/constants";
import { ossPrClient } from "@/api/alibaba";
import divisionListApi from "@/api/divisionList";
import experienceApi from "@/api/experience";
import ls from '@/api/sessionStorage'
import checkPermission from '@/utils/permission'

export default {
  data() {

    return {
      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null,
      },
      roleTag: "",
      uploadLoading: true,
      addOrUpdate: true, // true为添加，false为修改
      dataQuery: {
        refereeCode: "",
        roleTag: "",
        merchantCode: "",
        name: "",
        realName: "",
        txtStartTime: "",
        txtEndRegTime: "",
        marketPartner: "",
        address: "",
        isEnable: "",
      },
      value1: "",
      dialogVisible: false,
      tableData: [],
      updateData: {}, // 更新的数据
      addData: {}, // 新增的数据
      rules: {
        isCheck: [{
          required: true,
          message: "请选择是否通过",
          trigger: "change",
        },],
      },
      content: "",
      isUploadSuccess: true, // 是否上传成功
      showLoginAccount: false,
      showExamine: false, //审核页面
      //审核字段
      examine: {
        isCheck: "",
        checkReason: "",
        id: "",
      },
      grandNameList: [],//体验中心等级
      isCheck: "",
      exportLoading: false, //导出加载
      exportLoading1: false,
      sort: true
    };
  },
  created() {
    ossPrClient();
    this.fetchData01();
    this.getRankGrandName();
  },
  mounted() {
    authenticationApi.checkAccountBalance().then(res => {
      this.roleTag = res.data.data.roleTag;
    })
  },
  methods: {
    checkPermission,
    fetchData01() {
      this.tablePage = {
        currentPage: 1,
        size: this.tablePage.size,
        totalPage: null,
        totalItems: null
      }
      this.fetchData();
    },
    // 查询+搜索课程列表
    fetchData() {
      const that = this;
      that.tableLoading = true;

      if (that.value1 != null) {
        if (that.value1.length > 0) {
          this.dataQuery.startDate = that.value1[0];
          this.dataQuery.endDate = that.value1[1];
        } else {
          this.dataQuery.startDate = '';
          this.dataQuery.endDate = '';
        }
      } else {
        this.dataQuery.startDate = '';
        this.dataQuery.endDate = '';
      }
      experienceApi.experienceList(
        that.tablePage.currentPage,
        that.tablePage.size,
        that.dataQuery
      )
        .then((res) => {
          if (that.dataQuery.roleTag == "Agent") {
            that.sort = false;
          }
          that.tableData = res.data.data;
          console.log(that.tableData)
          for (let i = 0; i < that.tableData.length; i++) {
            if (that.tableData[i].name.substring(0, 1) == "A") {
              that.tableData[i].name = that.tableData[i].name.substring(1, 12)
            }
          }
          // console.log(that.dataQuery.isEnable);
          that.tableLoading = false;
          // 设置后台返回的分页参数
          pageParamNames.forEach((name) =>
            that.$set(that.tablePage, name, parseInt(res.data[name]))
          );
        });
    },
    // 获取起始时间
    dateVal(e) {
      // console.log(e[0]);
      if (e != null) {
        if (e.length > 0) {
          this.dataQuery.txtStartTime = e[0];
          this.dataQuery.txtEndRegTime = e[1];
        } else {
          this.dataQuery.txtStartTime = '';
          this.dataQuery.txtEndRegTime = '';
        }
      }


    },
    // 进入添加页面
    clickAdd() {
      const that = this;
      that.addOrUpdate = true;

      ls.setItem('addOrUpdate', that.addOrUpdate);
      ls.removeItem('divisionId')

      that.$router.push({
        path: "/merchantManagement/experienceAdd",
        query: {
          addOrUpdate: that.addOrUpdate,
        },
      });
    },
    //审核理由
    open(chenckReason) {
      const h = this.$createElement;
      this.$msgbox({
        title: '审核理由',
        message: h('p', null, [
          h('i', { style: 'color: #FF0802' }, chenckReason)
        ]),
        showCancelButton: false,
        confirmButtonText: '确定'
      })
    },
    // 进入编辑页面
    update(id, isReSubmit) {
      const that = this;
      that.addOrUpdate = false;

      that.$router.push({
        path: "/merchantManagement/experienceAdd",
        query: {
          addOrUpdate: that.addOrUpdate,
          id: id,
          isReSubmit: isReSubmit
        },
      });
    },
    //获取体验中心等级
    getRankGrandName() {
      divisionListApi.getRankGrandName().then(res => {
        this.grandNameList = res.data;
      })
    },
    // 分页
    handleSizeChange(val) {
      console.log(val);
      this.tablePage.size = val;
      this.fetchData();
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val;
      this.fetchData();
    },
    //完款
    allGone(id) {
      experienceApi.allGone(id).then(res => {
        this.$message.success("完款成功");
        this.fetchData();
      }).catch(err => { })
    },
    // 导出
    exportList() {
      const that = this;
      that.exportLoading = true;
      divisionListApi.divisionExport(that.dataQuery).then((response) => {
        console.log(response);
        if (!response) {
          this.$notify.error({
            title: "操作失败",
            message: "文件下载失败",
          });
        }
        const url = window.URL.createObjectURL(response);
        const link = document.createElement("a");
        link.style.display = "none";
        link.href = url; // 获取服务器端的文件名
        link.setAttribute("download", "体验中心表.xls");
        document.body.appendChild(link);
        link.click();
        this.exportLoading = false;
      });
    },
    // 开通与暂停
    agentStatus(id, status) {
      const that = this;
      this.$confirm("确定操作吗?", "修改状态", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        experienceApi.updateStatus(id, status).then((res) => {
          if (res.success) {
            that.fetchData01();
            that.$message.success("操作成功");
          }
        });
      })
    },

    // 打开审核弹框
    openExamine(id) {
      this.showExamine = true;
      this.examine.id = id;
      this.isCheck = "";
      this.examine = {
        rank: "",
        checkReason: "",
        isCheck: "",
        id: id
      }
    },
    // 关闭审核弹框
    closeExamine() {
      this.showExamine = false;
      this.examine.id = "";
    },
    // 状态改变事件
    change(radio) {
      if (radio == "1") {
        this.examine.isCheck = 1;
      } else {
        this.examine.isCheck = 0;
      }
    },
    // 审核
    toExamine(id, checkReason, isCheck, ele, rank) {
      const that = this;
      if ((rank === "" || rank === '') && this.roleTag === 'admin') {
        that.$message.info("等级不能为空");
        return false;
      }


      that.$refs[ele].validate((valid) => {
        // 表单验证
        if (valid) {
          const loading = this.$loading({
            lock: true,
            text: "审核中",
            spinner: "el-icon-loading",
            background: "rgba(0, 0, 0, 0.7)",
          });
          experienceApi
            .examine(id, checkReason, isCheck, rank)
            .then((res) => {
              if (res.success) {
                loading.close();
                that.showExamine = false;
                that.$nextTick(() => that.fetchData());
                that.$message.success("审核成功！");
              } else {
                that.$message.warning(res.message);
              }
            }, s => {
              if (s == "error") {
                loading.close();
              }
            })
            .catch((err) => {
              loading.close();
            });
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
  },
};
</script>

<style>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

@media (max-width: 767px) {
  .el-message-box {
    width: 80% !important;
  }
}
</style>
