<template>
  <div class="app-container">
    <el-form :inline="true" ref="queryForm" class="container-card" label-width="110px">
      <el-form-item label="反馈时间：">
        <el-date-picker
          clearable
          v-model="value"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="反馈人姓名：" >
        <el-input v-model="dataQuery.studentName" placeholder="请输入反馈人姓名" clearable/>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handlePage()">搜索</el-button>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-re" @click="resetQuery()">重置</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="warning" icon="el-icon-download" v-loading="exportLoading" @click="exportList()">导出</el-button>
      </el-form-item>
    </el-form>

    <el-table class="common-table" v-loading="tableLoading" :data="tableData" style="width: 100%;margin-bottom: 20px;" row-key="id" border
              default-expand-all >
      <el-table-column prop="id" label="反馈ID" width="200"></el-table-column>
      <el-table-column prop="studentCode" label="学员code" width="200"></el-table-column>
      <el-table-column prop="studentName" label="反馈人" width="200"></el-table-column>
      <el-table-column prop="createTime" label="反馈时间" width="200"></el-table-column>
      <el-table-column prop="content" label="反馈内容" show-overflow-tooltip></el-table-column>
    </el-table>
    <!-- 分页 -->
    <el-col :span="24" style="margin-bottom: 20px">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]" layout="total, sizes, prev, pager, next, jumper"
                     :total="tablePage.totalItems" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    </el-col>
  </div>
</template>

<script>
import feedbackApi from "@/api/studyroom/feedback";
import {pageParamNames} from "@/utils/constants";
import referrerJoinMeetingApi from "@/api/dxt/referrerJoinMeeting";

export default {
  name:'feedback',
  data() {
    return {
      exportLoading:false,
      dataQuery:{
        studentName:'',
        startTime:'',
        entTime:'',
      },
      value:[],
      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      open:false,
      tableData: [],
    };
  },
  created() {
    this.getPageList();
  },
  methods: {
    // 导出
    exportList() {
      const that = this;
      that.exportLoading = true;
      if (that.value != '' && that.value != null && that.value != undefined) {
        if (this.value.length > 0) {
          this.dataQuery.startTime = this.value[0]
          this.dataQuery.endTime = this.value[1]
        } else {
          this.dataQuery.startTime = ''
          this.dataQuery.endTime = ''
        }
      }else {
        this.dataQuery.startTime = ''
        this.dataQuery.endTime = ''
      }
      feedbackApi.dataExport(that.dataQuery).then((response) => {
        if (!response) {
          this.$notify.error({
            title: "操作失败",
            message: "文件下载失败",
          });
        }
        const url = window.URL.createObjectURL(response);
        const link = document.createElement("a");
        link.style.display = "none";
        link.href = url; //获取服务器端的文件名
        link.setAttribute("download", "意见反馈.xls");
        document.body.appendChild(link);
        link.click();
        this.exportLoading = false;
      }).catch(err => {
        this.exportLoading = false;
      });
    },
    getPageList(){
      this.tableLoading = true;
      this.dataQuery.pageNum = this.tablePage.currentPage;
      this.dataQuery.pageSize = this.tablePage.size;
      if (this.value != '' && this.value != null && this.value != undefined) {
        if (this.value.length > 0) {
          this.dataQuery.startTime = this.value[0]
          this.dataQuery.endTime = this.value[1]
        } else {
          this.dataQuery.startTime = ''
          this.dataQuery.endTime = ''
        }
      }
      feedbackApi.pageList(this.dataQuery).then(res=>{
        console.log(res.data.data);
        this.tableData = res.data.data;
        this.tableLoading = false;
        // 设置后台返回的分页参数
        pageParamNames.forEach(name => this.$set(this.tablePage, name, parseInt(res.data[name])))
      })
    },
    handlePage() {
      this.tablePage = {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
       this.getPageList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dataQuery = {
        className: '',
        courseType: '',
        level: '',
      };
      this.value =[]
      this.getPageList();
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val;
      this.getPageList();
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val;
      this.getPageList();
    },
  }
};
</script>

<style>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}
.period-table td,
.period-table th{
  text-align: center;
}
@media (max-width:767px) {
  .el-message-box{
    width: 80%!important;
  }
}
.el-tooltip__popper {
max-width: 800px;
}
</style>
