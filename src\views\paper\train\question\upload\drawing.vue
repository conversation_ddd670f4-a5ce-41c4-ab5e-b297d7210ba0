<template>
  <div class="app-container">
    <el-form :model="form" ref="form" label-width="100px" v-loading="formLoading" :rules="rules">
      <el-form-item label="所属类型:" prop="type">
        <el-select v-model="form.type" placeholder="全部" disabled>
          <el-option v-for="(item, index) in trainType" :key="index" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="题型:" prop="questionType">
        <el-select v-model="form.questionType" placeholder="全部" disabled>
          <el-option v-for="(item, index) in trainQuestionType" :key="index" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="年级：" prop="grade" v-if="form.courseType == 1">
        <el-select v-model="form.grade" placeholder="" clearable>
          <el-option v-for="item in gradeList" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="选择难度：" prop="difficulty">
        <el-select v-model="form.difficulty" placeholder="全部" clearable>
          <el-option v-for="(item, index) in difficultyList" :key="index" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="选择维度：" prop="categoryId">
        <el-select v-model="form.categoryId" placeholder="全部" clearable>
          <el-option v-for="(item, index) in categoryList" :key="index" :label="item.name" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item v-if="form.courseType!=1" label="选择类别：" prop="categoryType">
        <el-select v-model="form.categoryType" :disabled="questionlnCourseFalse&&form.categoryType==1" placeholder="全部" clearable>
          <el-option v-for="(item, index) in typeList" :key="index" :label="item.label" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="题干：" prop="title">
        <el-input placeholder="请输入" v-model="form.title" />
      </el-form-item>
      <!--      <el-form-item label="自动生成" required>-->
      <!--        <el-radio-group v-model="form.isRandom">-->
      <!--          <el-radio :label="true">是</el-radio>-->
      <!--          <el-radio :label="false">否</el-radio>-->
      <!--        </el-radio-group>-->
      <!--      </el-form-item>-->
      <el-form-item label="题目：" required>
        <div v-if="form.customInfo.length > 0" style="display: flex">
          <el-form-item v-for="(cus, cusIndex) in form.customInfo" :key="cusIndex" style="margin-right: 20px">
            <el-input v-model="cus.label" style="width: 45px; marginright: 5px" readonly />
            <el-image :src="cus.value" style="width: 200px; height: 200px" />
          </el-form-item>
        </div>
        <el-button type="success" size="mini" @click="choiceImage">选择图片</el-button>
      </el-form-item>
      <el-form-item label="学时控制：">
        <el-input-number :min="1" :step="1" :precision="0" v-model="form.expandInfo" controls-position="right" />
        分钟
      </el-form-item>
      <el-form-item label="题数：" prop="score">
        <el-input-number v-model="form.score" :precision="0" :step="1" :min="0" :max="100"></el-input-number>
      </el-form-item>
      <el-form-item label="徽章：" prop="badge">
        <el-input-number v-model="form.badge" :precision="0" :step="1" :min="0" :max="100"></el-input-number>
      </el-form-item>
      <el-form-item label="解析：" required>
        <el-input v-model="form.analysis">
          <i @click="inputClick(form, 'analysis')" slot="suffix" class="el-icon-edit-outline" style="line-height: 36px; font-size: 20px; cursor: pointer"></i>
        </el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="submitForm">提交</el-button>
        <el-button v-show="form.id === null" @click="resetForm">重置</el-button>
      </el-form-item>
    </el-form>

    <el-dialog :visible.sync="richEditor.dialogVisible" append-to-body :close-on-click-modal="false" style="width: 100%; height: 100%" :show-close="false" center>
      <Ueditor @ready="editorReady" />
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="editorConfirm">确 定</el-button>
        <el-button @click="richEditor.dialogVisible = false">取 消</el-button>
      </span>
    </el-dialog>

    <el-dialog :visible.sync="imagePage.showDialog" width="70%">
      <el-form :model="imagePage.dataQuery" ref="queryForm" :inline="true">
        <el-form-item label="名称：">
          <el-input v-model="imagePage.dataQuery.name" clearable></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="search">查询</el-button>
        </el-form-item>
      </el-form>
      <el-table v-loading="imagePage.listLoading" :data="imagePage.tableData" ref="multipleTable" @current-change="handleSelectionChange" :row-class-name="tableRowClassName" border fit highlight-current-row style="width: 100%">
        <el-table-column prop="questionType" label="题型" :formatter="questionTypeFormatter"></el-table-column>
        <el-table-column prop="difficulty" label="难度" :formatter="difficultyFormatter"></el-table-column>
        <el-table-column prop="name" label="名称"></el-table-column>
        <el-table-column prop="image" label="图片" align="center">
          <template slot-scope="scope">
            <el-image v-for="(item, imgIndex) in scope.row.fileList" :src="item" :preview-src-list="[item]" :key="imgIndex" class="img-lg" />
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <el-col :span="24" style="margin-bottom: 20px">
        <el-pagination :current-page="imagePage.tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]" layout="total, sizes, prev, pager, next, jumper" :total="imagePage.tablePage.totalItems" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
      </el-col>
      <span slot="footer" class="dialog-footer">
        <el-button @click="imagePage.showDialog = false">取 消</el-button>
        <el-button type="primary" @click="confirmImageSelect">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import questionApi from '@/api/paper/train/question';
import categoryApi from '@/api/paper/train/category';
import { mapGetters, mapState } from 'vuex';
import MyUpload from '@/components/Upload/MyUpload';
import { pageParamNames } from '@/utils/constants';
import imageApi from '@/api/paper/train/image';
import Ueditor from '@/components/Ueditor';

export default {
  components: {
    MyUpload,
    Ueditor
  },
  data() {
    return {
      richEditor: {
        dialogVisible: false,
        object: null,
        parameterName: '',
        instance: null
      },
      imagePage: {
        showDialog: false,
        listLoading: false,
        tableData: [],
        selectData: null,
        dataQuery: {
          questionType: null,
          difficulty: null
        },
        // 分页
        tablePage: {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null
        }
      },
      categoryList: [],
      fileList: [],
      typeList: [
        { label: '正式题', id: 1 },
        { label: '附加题', id: 2 }
      ],
      importFrom: {
        file: null
      },
      randomLength: undefined,
      form: {
        id: null,
        title: '',
        categoryId: '',
        categoryType: '',
        difficulty: '',
        difficultyId: '',
        type: 'UPLOAD_',
        questionType: 'UPLOAD_DRAWING',
        customInfo: [],
        answer: [],
        isRandom: false,
        expandInfo: undefined,
        analysis: '',
        score: undefined,
        badge: 3,
        courseType: 0,
        grade: ''
      },
      formLoading: false,
      rules: {
        type: [{ required: true, message: '请选择', trigger: 'blur' }],
        questionType: [{ required: true, message: '请选择', trigger: 'blur' }],
        grade: [{ required: true, message: '请选择', trigger: 'blur' }],
        title: [{ required: true, message: '请输入', trigger: 'blur' }],
        difficulty: [{ required: true, message: '请选择', trigger: 'blur' }],
        categoryId: [{ required: true, message: '请选择', trigger: 'blur' }],
        categoryType: [{ required: true, message: '请选择', trigger: 'blur' }],
        score: [{ required: true, message: '请输入题数', trigger: 'blur' }],
        badge: [{ required: true, message: '请输入徽章', trigger: 'blur' }]
      },
      questionlnCourseFalse: false,
      currentAnswerItem: null,
      gradeList: [
        { label: '1-3年级', value: 1 },
        { label: '4-6年级', value: 2 },
        { label: '初高中', value: 3 }
      ]
    };
  },
  created() {
    this.getCategoryList();
    let id = this.$route.query.id;
    this.form.courseType = this.$route.query.courseType ? 1 : 0;
    if (id && parseInt(id) !== 0) {
      this.formLoading = true;
      questionApi.detail(id).then((re) => {
        this.form = re.data;
        this.form.grade = this.form.grade == 0 ? '' : this.form.grade;

        this.formLoading = false;
      });
      questionApi.checkQuestionlnCourse(id).then((res) => {
        this.questionlnCourseFalse = res.data
      });
    }
  },
  methods: {
    editorReady(instance) {
      this.richEditor.instance = instance;
      let currentContent = this.richEditor.object[this.richEditor.parameterName];
      this.richEditor.instance.setContent(currentContent);
      // 光标定位到Ueditor
      this.richEditor.instance.focus(true);
    },
    inputClick(object, parameterName) {
      this.richEditor.object = object;
      this.richEditor.parameterName = parameterName;
      this.richEditor.dialogVisible = true;
    },
    editorConfirm() {
      let content = this.richEditor.instance.getContent();
      this.richEditor.object[this.richEditor.parameterName] = content;
      this.richEditor.dialogVisible = false;
    },

    choiceImage() {
      if (!this.form.difficulty) {
        this.$message.error('请选择难度！');
        return false;
      }
      this.imagePage.showDialog = true;
      this.search();
    },
    search() {
      this.imagePage.dataQuery.questionType = this.form.questionType;
      this.imagePage.dataQuery.difficulty = this.form.difficulty;
      this.imagePage.listLoading = true;
      this.imagePage.dataQuery.pageNum = this.imagePage.tablePage.currentPage;
      this.imagePage.dataQuery.pageSize = this.imagePage.tablePage.size;
      imageApi.list(this.imagePage.dataQuery).then((res) => {
        this.imagePage.tableData = res.data.data;
        this.imagePage.listLoading = false;
        // 设置后台返回的分页参数
        pageParamNames.forEach((name) => this.$set(this.imagePage.tablePage, name, parseInt(res.data[name])));
      });
    },
    handleSelectionChange(val) {
      if (val) {
        this.imagePage.selectData = val;
      } else {
        this.imagePage.selectData = null;
      }
    },
    tableRowClassName({ row, rowIndex }) {
      if (row.id === this.form.paperId) {
        return 'current-row';
      }
      return '';
    },
    confirmImageSelect() {
      if (this.imagePage.selectData) {
        this.form.customInfo = [];
        let items = this.imagePage.selectData.fileList;
        let newLastPrefix = 'A';
        items.forEach((a) => {
          this.form.customInfo.push({ label: newLastPrefix, value: a });
          newLastPrefix = String.fromCharCode(newLastPrefix.charCodeAt() + 1);
        });
      }
      this.imagePage.showDialog = false;
    },
    questionTypeFormatter(row, column, cellValue, index) {
      return this.enumFormat(this.trainQuestionType, cellValue);
    },
    difficultyFormatter(row, column, cellValue, index) {
      return this.enumFormat(this.difficultyList, cellValue);
    },

    getCategoryList() {
      categoryApi.list().then((res) => {
        this.categoryList = res.data.data;
      });
    },
    submitForm() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.formLoading = true;
          questionApi
            .saveOrUpdate(this.form)
            .then((re) => {
              if (re.success) {
                this.$message.success(re.message);
                this.formLoading = false;
                if (this.form.courseType == 1) {
                  this.$router.push('/train/trainAfterCourse');
                } else {
                  this.$router.push('/train/upload');
                }
              } else {
                this.$message.error(re.message);
                this.formLoading = false;
              }
            })
            .catch((e) => {
              this.formLoading = false;
            });
        } else {
          return false;
        }
      });
    },
    resetForm() {
      let lastId = this.form.id;
      let lastCourseType = this.form.courseType;
      this.$refs['form'].resetFields();
      this.form = {
        id: null,
        title: '',
        categoryId: '',
        difficulty: '',
        difficultyId: '',
        type: 'UPLOAD_',
        questionType: 'UPLOAD_DRAWING',
        customInfo: [],
        answer: [],
        isRandom: false,
        expandInfo: undefined,
        analysis: '',
        grade: ''
      };
      this.form.id = lastId;
      this.form.courseType = lastCourseType;
    },
    // 分页
    handleSizeChange(val) {
      this.imagePage.tablePage.size = val;
      this.search();
    },
    handleCurrentChange(val) {
      this.imagePage.tablePage.currentPage = val;
      this.search();
    }
  },
  computed: {
    ...mapGetters('enumItem', ['enumFormat']),
    ...mapState('enumItem', {
      trainType: (state) => state.train.trainType,
      difficultyList: (state) => state.train.difficultyList,
      trainQuestionType: (state) => state.train.trainQuestionType
    })
  }
};
</script>

<style lang="less" scoped>
.question-item-label {
  display: flex;
  margin-bottom: 12px;
}

.drawing-item {
  overflow: hidden;
  white-space: normal;
  word-break: break-all;
}

.img-lg {
  width: 60px;
  height: 60px;
}
</style>
