<template>
  <div class="app-container">
    <!-- <el-form :inline="true" class="container-card" label-width="110px" label-position="left"
             v-if="checkPermission(['admin'])||!checkPermission(['b:merchant:OperationsVersion'])&&currentAdmin.schoolType!=3">
      <el-row>
       <el-col :span="8">
         <el-form-item label="按城市查询：">
           <el-input v-model="dataQuery.name"></el-input>
         </el-form-item>
       </el-col>
       <el-col :span="8" style="text-align: right">
         <el-form-item>
           <el-button type="primary" icon="el-icon-search" @click="fetchData()">搜索</el-button>
         </el-form-item>
       </el-col>
      </el-row>

    </el-form> -->
    <div>
      <div style="display: flex;justify-content: space-between;margin-bottom: 0.5vw;font-weight: bold;">
        <div>学时单价设置</div>
        <el-button type="primary" size="small" v-if="checkPermission(['b:system:cityCourseConfiguration:edit'])"
          icon="el-icon-plus" @click="clickAdd">新增学时单价</el-button>
      </div>
      <el-table class="common-table" v-loading="tableLoading" :data="tableData" style="width: 100%;margin-bottom: 20px;"
        row-key="id" border default-expand-all :tree-props="{ list: 'children', hasChildren: 'true' }"
        :header-cell-style="getRowClass">
        <!--      <el-table-column prop="name" label="城市"></el-table-column>-->
        <el-table-column prop="value" label="学时数区间">
          <template slot-scope="scope">
            <span>{{ scope.row.value + "-" + scope.row.cnNameText }}</span>
          </template>
        </el-table-column>

      
        <el-table-column prop="configState" label="小学">
          <template slot-scope="scope">
            <span>{{ scope.row.levelCourseConfigList[0].value/ 100}}</span>
          </template>
        </el-table-column>
        <el-table-column prop="configState" label="初中">
          <template slot-scope="scope">
            <span>{{ scope.row.levelCourseConfigList[1].value/ 100}}</span>
          </template>
        </el-table-column>
        <el-table-column prop="configState" label="高中">
          <template slot-scope="scope">
            <span>{{ scope.row.levelCourseConfigList[2].value/ 100}}</span>
          </template>
        </el-table-column>
        <el-table-column prop="assConfigVo.configState" label="交付单价(元)">
          <template slot-scope="scope">
            <span>{{ scope.row.assConfigVo.configState / 100 }}</span>
          </template>
        </el-table-column>
        <el-table-column label="小学总价">
          <template v-slot="{ row }">
            {{  (parseFloat(row.levelCourseConfigList[0].value/ 100) + parseFloat((row.assConfigVo.configState / 100))).toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column label="初中总价">
          <template v-slot="{ row }">
            {{  (parseFloat(row.levelCourseConfigList[1].value/ 100) + parseFloat((row.assConfigVo.configState / 100))).toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column label="高中总价">
          <template v-slot="{ row }">
            {{  (parseFloat(row.levelCourseConfigList[2].value/ 100) + parseFloat((row.assConfigVo.configState / 100))).toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template slot-scope="scope">
            <el-button type="primary" size="mini" icon="el-icon-edit-outline" @click="handleUpdate(scope.row)">编辑
            </el-button>
            <el-button type="danger" size="mini" icon="el-icon-delete"
              v-if="tableData && scope.row.id !== tableData[0].id&&scope.row.cnNameText!='以上'" @click="deleteDurationUnitPrice(scope.row)">删除
            </el-button>
          </template>
        </el-table-column>
      
        <!-- <el-table-column prop="isEnable" label="状态">
            <template slot-scope="scope">
              <span class="green" v-if="scope.row.isEnable === 1">开通</span>
              <span class="red" v-else>暂停</span>
            </template>
          </el-table-column> -->
      </el-table>
    </div>

    <!-- <div>
      <div style="display: flex;justify-content: space-between;margin: 3vw 0 0.5vw;font-weight: bold;">
        <div>复习包设置</div>
        <el-button type="primary" size="small" v-if="checkPermission(['b:system:cityCourseConfiguration:edit'])"
          icon="el-icon-plus" @click="reviewKitEdit('add', '')">新增复习包</el-button>
      </div>
      <el-table class="common-table" v-loading="tableLoading" :data="reviewkitList" :header-cell-style="getRowClass"
        style="width: 100%;margin-bottom: 20px;" row-key="id" border default-expand-all
        :tree-props="{ list: 'children', hasChildren: 'true' }">
        <el-table-column prop="name" label="学时"></el-table-column>
        <el-table-column label="价格">
          <template v-slot="{ row }">
            {{ Number(row.value).toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template slot-scope="scope">
            <el-button type="primary" size="mini" icon="el-icon-edit-outline" @click="reviewKitEdit('edit', scope.row)">编辑
            </el-button>
            <el-button type="danger" size="mini" icon="el-icon-delete" @click="deleteReviewMaterial(scope.row)">删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div> -->

    <!-- 添加弹窗 -->
    <el-dialog :title="addOrUpdate ? '添加学时单价配置' : '编辑学时单价配置'" :visible.sync="dialogVisible" width="40%"
      :close-on-click-modal="false" @close="close">
      <el-form :ref="addOrUpdate ? 'addCourseData' : 'updateActive'"
        :model="addOrUpdate ? addCourseData : updateActive" label-position="left" label-width="120px"
        style="width: 100%;">
        <!-- <el-form-item label="城市:" prop="name">
          <el-col :xs="24" :span="20">
            <el-input :disabled="!checkPermission(['admin'])" v-if="addOrUpdate" v-model="addCourseData.name" />
            <el-input :disabled="true" v-else v-model="updateActive.name" />
          </el-col>
        </el-form-item> -->
        <el-form-item label="学时数区间:">
          <el-col :xs="24" :span="20">
            <el-row :gutter="20">
              <el-col :span="3">
                <span>起始值</span>
              </el-col>
              <el-col :span="9">
                <el-form-item label="" prop="value">
                  <el-input :min="0" v-model="value" type="Number" clearable></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="3">
                <span>结束值</span>
              </el-col>
              <el-col :span="9">
                <el-form-item label="" prop="cnName">
                  <el-input :min="0" v-model="cnName" type="Number" clearable
                    :disabled="updateActive.cnName == 'infinity'" />
                </el-form-item>
              </el-col>
            </el-row>
          </el-col>
        </el-form-item>
        <el-form-item label="小学单价" prop="configState">
          <el-col :xs="24" :span="12">
            <el-input :min="0" type="Number" v-if="addOrUpdate" clearable v-model="addCourseData.configState" />
            <el-input :min="0" type="Number" clearable v-else v-model="updateActive.levelCourseConfigList[0].value" />
          </el-col>
          &nbsp;元
        </el-form-item>
        <el-form-item label="初中单价" prop="configState">
          <el-col :xs="24" :span="12">
            <el-input :min="0" type="Number" v-if="addOrUpdate" clearable v-model="addCourseData.configState1" />
            <el-input :min="0" type="Number" clearable v-else v-model="updateActive.levelCourseConfigList[1].value" />
          </el-col>
          &nbsp;元
        </el-form-item>
        <el-form-item label="高中单价" prop="configState">
          <el-col :xs="24" :span="12">
            <el-input :min="0" type="Number" v-if="addOrUpdate" clearable v-model="addCourseData.configState2" />
            <el-input :min="0" type="Number" clearable v-else v-model="updateActive.levelCourseConfigList[2].value" />
          </el-col>
          &nbsp;元
        </el-form-item>
        <el-form-item label="交付单价" prop="configState" v-if="!addOrUpdate">
          <el-col :xs="24" :span="12">
            <el-input :min="0" type="Number" v-if="addOrUpdate" disabled clearable   :value="90" />
            <el-input :min="0" type="Number" clearable v-else v-model="updateActive.assConfigVo.configState/100" disabled />
          </el-col>
          &nbsp;元
        </el-form-item>
        <!-- <el-form-item label="备注" prop="remark">
          <el-col :xs="24" :span="20">
            <el-input v-if="addOrUpdate" v-model="addCourseData.remark" />
            <el-input v-else v-model="updateActive.remark" />
          </el-col>
        </el-form-item> -->
        <!-- <el-form-item label="状态:" prop="isEnable">
          <template>
            <el-radio v-model="radio" label="1" @change="change(radio)">开通</el-radio>
            <el-radio v-model="radio" label="0" @change="change(radio)">暂停</el-radio>
          </template>
        </el-form-item> -->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button v-if="addOrUpdate" size="small" type="primary" @click="addActiveFun('addCourseData')">新增</el-button>
        <el-button v-if="!addOrUpdate" size="small" type="primary" @click="updateActiveFun('updateActive')">修改</el-button>
        <el-button size="small" @click="close">关闭</el-button>
      </div>
    </el-dialog>

    <!-- 新增、编辑复习包 -->
    <el-dialog :title="reviewMaterial.id ? '编辑复习包' : '新增复习包'" :visible.sync="reviewKitVisible" width="380px"
      :close-on-click-modal="false" :before-close="handleClose">
      <el-form ref="reviewkitForm" :model="reviewMaterial" label-width="80px" :rules="reviewkitRules">
        <el-form-item label="学时：" prop="name">
          <el-input :min="0" type="number" v-model="reviewMaterial.name" style="width: 200px;" @input="change"
            placeholder="请先输入"></el-input><span style="margin-left: 20px;">分钟</span>
        </el-form-item>
        <el-form-item label="价格：" prop="value">
          <el-input :min="0" type="number" v-model="reviewMaterial.value" style="width: 200px;" @input="change"
            placeholder="请先输入"></el-input><span style="margin-left: 20px;">元</span>
        </el-form-item>
        <el-form-item>
          <el-button size="small" @click="reviewKitVisible = false">取消</el-button>
          <el-button size="small" type="primary" @click="reviewPackage('reviewkitForm')">确定</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>

    <!-- 删除学时单价 -->
    <el-dialog :title="deleteStatus ? '删除复习包' : '删除学时单价'" :visible.sync="deleteVisible" width="380px"
      :close-on-click-modal="false" class="delete_kit">
      <i class="el-icon-delete delete_icon"></i>
      <div v-if="deleteStatus">
        <span>复习包学时：{{ deleteTime }}分钟</span>
        <span style="margin-left: 20px;">价格：{{ deletePrice }}</span>
      </div>
      <div v-if="!deleteStatus">
        <div style="display: flex;flex-direction: column;">
          <span>区间：{{ interval }}</span>
          <span style="margin-left: 50px;" v-if="deleteUnitPrice.levelCourseConfigList">小学学时单价：{{ deleteUnitPrice.levelCourseConfigList[0].value/100 }}</span>
          <span style="margin-left: 50px;"  v-if="deleteUnitPrice.levelCourseConfigList">初中学时单价：{{ deleteUnitPrice.levelCourseConfigList[1].value/100 }}</span>
          <span style="margin-left: 50px;"  v-if="deleteUnitPrice.levelCourseConfigList">高中学时单价：{{ deleteUnitPrice.levelCourseConfigList[2].value /100}}</span>
        </div>
        <div class="mt-20">
          <i class="el-icon-warning delete-icon c-f89"></i><span style="color: #999;">删除后，该区间学时单价为默认</span>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="deleteVisible = false">取 消</el-button>
        <el-button size="small" type="primary" @click="deleteReviewPackage">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 分页 -->
    <!-- <el-col :span="24">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col> -->
  </div>
</template>

<script>
import systemApi from "@/api/systemConfiguration";
import {
  pageParamNames
} from "@/utils/constants";
import schoolList from "@/api/schoolList";
import checkPermission from "@/utils/permission";

export default {
  name: "cityCourseConfiguration",
  data() {
    return {
      value: 0,
      cnName: 0,
      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      tableData: [],
      dataQuery: {
        name: '',
        value: ''
      },
      rules: { // 表单提交规则
        value: [
          { required: true, message: '请填写起始值', trigger: 'blur' }
        ],
        // name: [{
        //   required: true,
        //   message: '请填写键',
        //   trigger: 'blur'
        // }],
        cnName: [
          { required: true, message: '请填写结束值', trigger: 'blur' }
        ],
        // isEnable: [
        //   { required: true, message: '请选择状态', trigger: 'change' }
        // ],
        configState: [
          { required: true, message: '请填写学时单价', trigger: 'blur' }
        ]
      },
      radio: '', //状态
      dialogVisible: false,
      addOrUpdate: true,
      updateActive: {},
      addCourseData: {},
      currentAdmin: {},
      totalScore: '', //学时单价和交付单价的总数

      reviewKitVisible: false,
      reviewMaterial: {
        name: '',
        value: ''
      },
      reviewkitRules: {
        name: [{
          required: true,
          message: '请输入学时',
          trigger: 'blur'
        }],
        value: [{
          required: true,
          message: '请输入价格',
          trigger: 'blur'
        }]
      },
      reviewkitList: {},


      deleteId: '',
      deleteTime: '',
      deletePrice: '',
      deleteVisible: false,

      deleteStatus: false, // 删除类型，默认学时单价
      interval: '', // 删除区间
      deleteUnitPrice: {},


      items: [
        { id: 1, name: 'Apple', price: 10 },
        { id: 2, name: 'Banana', price: 20 },
        { id: 3, name: 'Cherry', price: 30 },
        { id: 4, name: 'Date', price: 40 },
        { id: 5, name: 'Fig', price: 50 },
      ],

    };
  },
  created() {
    this.fetchData();
    this.getRoleTag();
    this.reviewPackageList();
  },
  methods: {
    checkPermission,
    getRoleTag() {
      schoolList.getCurrentAdmin().then(res => {
        this.currentAdmin = res.data
      });
    },
    // 查询列表
    fetchData() {
      const that = this
      that.tableLoading = true
      systemApi.coursePriceList(that.tablePage.currentPage, that.tablePage.size, that.dataQuery).then(res => {
        that.tableData = res.data.data;
        that.tableData.sort((a, b) => Number(a.value) - Number(b.value));
        that.tableData.forEach(e=>{
          e.cnNameText = e.cnName == "infinity" ? '以上' : e.cnName;
        })
        // that.tableData = that.tableData.filter((item) =>
        //   item.cnName != "infinity"
        // )
        console.log(that.tableData, 'that.tableData');
        that.tableLoading = false
        // 设置后台返回的分页参数
        pageParamNames.forEach(name => that.$set(that.tablePage, name, parseInt(res.data[name])))
      })
    },
    //新增操作
    clickAdd() {
      this.addOrUpdate = true
      this.addCourseData = {
        'name': "default",
        'isEnable': 1
      }

      systemApi.queryCourseMax("default").then((res) => {
        this.value = res.data;
        this.updateActive = {}
        this.cnName = 0;
        this.dialogVisible = true;
      })

    },
    // 新增提交
    addActiveFun(ele) {
      const that = this
      if (!that.value) {
        that.$message.error('开始值不能为空')
        return;
      }
      if (!that.cnName) {
        that.$message.error('结束值不能为空')
        return;
      }
      if (!that.addCourseData.configState) {
        that.$message.error('学时单价不能为空')
        return;
      }
      if (!that.addCourseData.configState) {
        that.$message.error('小学学时单价不能为空')
        return;
      }
      if (!that.addCourseData.configState1) {
        that.$message.error('初中学时单价不能为空')
        return;
      }
      if (!that.addCourseData.configState2) {
        that.$message.error('高中学时单价不能为空')
        return;
      }
      if (that.cnName != 'infinity' && Number(that.cnName) <= Number(that.value)) {
        that.$message.error('结束学时数区间结束值要大于开始值')
        return;
      }
      const loading = that.$loading({
        lock: true,
        text: '新增学时单价',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      that.addCourseData.value = that.value
      that.addCourseData.cnName = that.cnName
      let obj ={
        value:that.value,
        cnName:that.cnName,
        isEnable:0,
        name:'default',
        configGroup:'CITY_COURSE_PRICE',
        levelCourseConfigList:[
          {
          cnName:'小学',
          configGroup: "LEVEL_COURSE_PRICE",
          value:this.addCourseData.configState*100,
          name:'xiaoxue',
        },
        {
          cnName:'初中',
          configGroup: "LEVEL_COURSE_PRICE",
          value:this.addCourseData.configState1*100,
          name:'chuzhong',
        },
        {
          cnName:'高中',
          configGroup: "LEVEL_COURSE_PRICE",
          value:this.addCourseData.configState2*100,
          name:'gaozhong',
        },
      ]
      }
      systemApi.addsystemCoursePrice(obj).then(() => {
        that.dialogVisible = false
        that.fetchData();
        that.radio = '';
        loading.close()
        //that.$nextTick(() => that.fetchData())
        that.$message.success('新增学时单价成功')
      }).catch(err => {
        loading.close()
      })
    },
    // 点击编辑按钮
    handleUpdate(row) {
      const that = this
      that.dialogVisible = true
      that.addOrUpdate = false
      that.updateActive=JSON.parse(JSON.stringify(row)) 
      that.updateActive.levelCourseConfigList.forEach(e=>{e.value=e.value/100})
      this.value = row.value
      this.cnName = row.cnName
    },
    // 修改提交
    updateActiveFun(ele) {
      const that = this;
      if (!that.value) {
        that.$message.error('开始值不能为空')
        return;
      }
      if (!that.cnName) {
        that.$message.error('结束值不能为空')
        return;
      }
      if (!that.updateActive.levelCourseConfigList[0].value) {
        that.$message.error('小学学时单价不能为空')
        return;
      }
      if (!that.updateActive.levelCourseConfigList[1].value) {
        that.$message.error('初中学时单价不能为空')
        return;
      }
      if (!that.updateActive.levelCourseConfigList[2].value) {
        that.$message.error('高中学时单价不能为空')
        return;
      }
      if (that.cnName != 'infinity' && Number(that.cnName) <= Number(that.value)) {
        that.$message.error('结束学时数区间结束值要大于开始值')
        return;
      }
      that.updateActive.isEnable = that.radio
      const loading = this.$loading({
        lock: true,
        text: '编辑学时单价',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      that.updateActive.value = that.value
      that.updateActive.cnName = that.cnName
      that.updateActive.levelCourseConfigList.forEach(e=>{e.value=e.value*100})
      systemApi.addsystemCoursePrice(that.updateActive).then(() => {
        that.dialogVisible = false
        loading.close()
        that.$nextTick(() => that.fetchData())
        that.$message.success('编辑学时单价成功')
      }).catch(err => {
        // 关闭提示弹框
        loading.close()
      })
    },
    deleteDurationUnitPrice(row) {
      this.deleteId = row.id;
      this.interval = row.value + '~' + row.cnName;
      this.deleteUnitPrice = row
      this.deleteStatus = false;
      this.deleteVisible = true;
    },
    // 单个删除
    singleDelete(id) {
      const that = this;
      this.$confirm('确定操作吗?', '删除学时单价', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        systemApi.deleteCourse(id).then(res => {
          that.$nextTick(() => that.fetchData())
          that.$message.success('删除成功!')
        }).catch(err => {
        })
      }).catch(err => {
      })
    },
    // 关闭
    close() {
      this.dialogVisible = false;
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val;
      this.fetchData();
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val;
      this.fetchData();
    },
    // 状态改变事件
    change(radio) {
      if (radio == "1") {
        this.addCourseData.isEnable = 1;
      } else {
        this.addCourseData.isEnable = 0;
      }
    },

    addreview() {

    },
    // 获取复习包
    async reviewPackageList() {
      await systemApi.reviewKitList().then((res) => {
        this.reviewkitList = res.data;
        this.reviewkitList.sort((a, b) => Number(a.name) - Number(b.name));
      }).catch(err => {
        console.log(err)
      })
    },

    // 新增、编辑复习包
    reviewPackage(fromName) {
      let that = this;
      that.$refs[fromName].validate(valid => { // 表单验证
        if (valid) {
          const loading = this.$loading({
            lock: true,
            text: '请求中...',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          })
          if (that.reviewMaterial.id == '') {
            systemApi.addReviewKit(that.reviewMaterial).then(() => {
              that.reviewKitVisible = false;
              that.$refs[fromName].resetFields();
              that.reviewPackageList();
              loading.close();
              that.$message.success('操作成功');
            }).catch(err => {
              loading.close()
            })
          } else {
            systemApi.addReviewKit(that.reviewMaterial).then(() => {
              that.reviewKitVisible = false;
              that.$refs[fromName].resetFields();
              that.reviewPackageList();
              loading.close();
              that.$message.success('操作成功');
            }).catch(err => {
              loading.close()
            })
          }
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },

    // 新增、编辑复习包
    reviewKitEdit(val, row) {
      // console.log(val);
      if (val == 'edit') {
        this.reviewMaterial.name = row.name;
        this.reviewMaterial.value = row.value;
        this.reviewMaterial.id = row.id;
      } else {
        this.reviewMaterial.name = '';
        this.reviewMaterial.value = '';
        this.reviewMaterial.id = '';
      }
      this.reviewKitVisible = true;
    },

    deleteReviewMaterial(row) {
      this.deleteId = row.id;
      this.deleteTime = row.name;
      this.deletePrice = row.value;
      this.deleteStatus = true;
      this.deleteVisible = true;
    },

    async deleteReviewPackage() {
      let that = this;
      let loading = this.$loading({
        lock: true,
        text: '正在删除中，请稍候...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      let data = {
        id: that.deleteId
      }
      if (that.deleteStatus) {
        await systemApi.deleteReviewKit(data).then(res => {
          that.deleteVisible = false;
          // that.reviewPackageList();
          loading.close();
          that.$message.success('删除成功!');
        }).catch(err => {
          loading.close();
        })
      } else {
        systemApi.deleteCourse(that.deleteId).then(res => {
          that.deleteVisible = false;
          loading.close();
          that.$nextTick(() => that.fetchData())
          that.$message.success('删除成功!')
        }).catch(err => {
          loading.close();
        })
      }
    },

    handleClose() {
      this.reviewKitVisible = false;
      this.$refs['reviewkitForm'].resetFields();
    },


    // 动态class
    getRowClass({ rowIndex, columnIndex }) {
      if (rowIndex == 0) {
        return 'background:#e2eaf6'
      }
    },
  }
};
</script>

<style scoped>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.period-table td,
.period-table th {
  text-align: center;
}

.mt20 {
  margin-top: 20px;
}

/* .delete_kit {
  ::v-deep .el-dialog__header
} */

::v-deep.delete_kit .el-dialog__title {
  margin-left: 1vw !important;
}

.delete_icon {
  position: absolute;
  left: 0.7vw;
  top: 22px;
  color: #bf0502;
  font-size: 18px;
}

.mt-20 {
  margin-top: 20px;
}

.c-f89 {
  color: #f89728;
}

.delete-icon {
  margin: 0 10px 0 !important;
}
</style>
