<template>
  <div class="app-container">
    <el-form :inline="true" ref="queryForm" class="container-card" label-width="110px">
      <el-form-item label="名称：" prop="className">
        <el-input v-model="dataQuery.className" placeholder="请输入名称" clearable />
      </el-form-item>
      <el-form-item label="分类：" prop="courseType">
        <el-select v-model="dataQuery.courseType" placeholder="全部" clearable>
          <el-option v-for="(item, index) in [{ label: '系统课', value: '1' }, { label: '非系统课', value: '2' }]" :key="index"
            :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="学段：" prop="level">
        <el-select v-model="dataQuery.level" placeholder="全部" clearable>
          <el-option v-for="(item, index) in [{ label: '小学', value: '1' }, { label: '初中', value: '2' }, { label: '高中', value: '3' }]"
            :key="index" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handlePage()">搜索</el-button>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-re" @click="resetQuery()">重置</el-button>
      </el-form-item>

    </el-form>
    <el-col :span="24" style="margin-bottom: 30px">
      <el-button type="success" icon="el-icon-plus" size="mini" @click="handleAdd()">新增</el-button>
    </el-col>

    <el-table class="common-table" v-loading="tableLoading" :data="tableData" style="width: 100%;margin-bottom: 20px;"
      row-key="id" border default-expand-all>
      <el-table-column prop="id" label="ID"></el-table-column>
      <el-table-column prop="className" label="规划名称"></el-table-column>
      <el-table-column label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="mini">
            <router-link :to="'/schedule/unFixScheduleRoom/' + scope.row.id">查看详情</router-link>
          </el-button>
          <el-button type="text" size="mini" @click="handleEdit(scope.row)">编辑</el-button>
          <el-button type="text" size="mini" @click="changeStatus(scope.row)" v-if="scope.row.status"
            style="color: red">停用</el-button>
          <el-button type="text" size="mini" @click="changeStatus(scope.row)" v-if="!scope.row.status">启用</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="courseType" label="课程分类">
        <template slot-scope="scope">
          <span v-if="scope.row.courseType == 1">系统课</span>
          <span v-else>非系统课</span>
        </template>
      </el-table-column>
      <el-table-column prop="tranWeeks" label="日期"></el-table-column>
      <el-table-column prop="times" label="时间"></el-table-column>
      <el-table-column prop="roomNum" label="自学室数量"></el-table-column>
      <el-table-column prop="level" label="学段">
        <template slot-scope="scope">
          <span v-if="scope.row.level === '1'">小学</span>
          <span v-if="scope.row.level === '2'">初中</span>
          <span v-if="scope.row.level === '3'">高中</span>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间"></el-table-column>
    </el-table>
    <!-- 分页 -->
    <el-col :span="24" style="margin-bottom: 20px">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>

    <el-dialog :title="title" :visible.sync="visible" width="60%" top="5vh" append-to-body @close="close">
      <el-form :model="form" ref="form" :rules="rules" :inline="true" label-width="78px">
        <el-form-item label="规划名称" prop="className">
          <el-input v-model="form.className" placeholder="规划名称" clearable size="small" />
        </el-form-item>
        <el-form-item label="学段" prop="level">
          <el-select v-model="form.level">
            <el-option v-for="(item, index) in [{ label: '小学', value: '1' }, { label: '初中', value: '2' }, { label: '高中', value: '3' }]"
              :key="index" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="类别" prop="courseType">
          <el-select v-model="form.courseType">
            <el-option v-for="(item, index) in [{ label: '系统课', value: '1' }, { label: '非系统课', value: '2' }]" :key="index"
              :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="日期" prop="weeks">
          <el-select v-model="form.weeks" multiple collapse-tags placeholder="请选择">
            <el-option v-for="item in dateOptions" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="时间" required>
          <el-row>
            <el-col :span="6">
              <el-time-select size="small" v-model="time1" value-format="HH:mm" :picker-options="{
                start: '00:00',
                step: '00:01',
                end: '23:58'
              }"></el-time-select>
            </el-col>
            <el-col :span="1">--</el-col>
            <el-col :span="6">
              <el-time-select size="small" v-model="time2" value-format="HH:mm" :picker-options="{
                start: '00:00',
                step: '00:01',
                end: '23:59',
                minTime: time1,
              }"></el-time-select>
            </el-col>
          </el-row>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="visible = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import studyScheduleApi from "@/api/studyroom/studySchedule";
import { pageParamNames } from "@/utils/constants";
import gradeAdd from "./gradeAdd";
import ls from '@/api/sessionStorage'

export default {
  name: 'gradeList',
  components: { gradeAdd },
  data() {
    return {
      time1: '',
      time2: '',
      title: '',
      // 遮罩层
      visible: false,
      form: {},
      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      tableData: [],
      dataQuery: {
        className: '',
        courseType: '',
        level: '',
      },
      dateOptions: [{ value: '1', label: '周一' }, { value: '2', label: '周二' }, { value: '3', label: '周三' },
      { value: '4', label: '周四' }, { value: '5', label: '周五' }, { value: '6', label: '周六' }, { value: '7', label: '周日' },
      ],
      rules: {
        courseType: [
          { required: true, message: "请选择分类", trigger: "blur" }
        ],
        level: [
          { required: true, message: "请选择阶段等级", trigger: "blur" }
        ],
        className: [
          { required: true, message: "请输入规划名称", trigger: "blur" }
        ],
        userCount: [
          { required: true, message: "请输入人数", trigger: "blur" }
        ],
        weeks: [
          { required: true, message: "请选择日期", trigger: "blur" }
        ],
        startTime: [
          { required: true, message: "请选择时间", trigger: "blur" }
        ],
        endTime: [
          { required: true, message: "请选择时间", trigger: "blur" }
        ],
      }
    };
  },
  created() {
    this.getPageList();
  },
  methods: {
    getDetail(id) {
      studyScheduleApi.getSchedule(id).then(res => {
        this.form = res.data;
        if (this.form.weeks) {
          var arr = this.form.weeks.split(",");
          this.form.weeks = [];
          this.form.weeks = arr;
        }
        this.form.courseType += "";
        this.time1 = this.form.times.split("-")[0];
        this.time2 = this.form.times.split("-")[1];
      })
    },

    submitForm() {
      if (this.time1 === '' || this.time1 == null || this.time2 === '' || this.time2 == null) {
        this.$message.error("请输入必填项！");
        return false;
      }
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.form.startTime = this.time1;
          this.form.endTime = this.time2;
          studyScheduleApi.createUnFix(this.form).then(response => {
            this.$message.success("提交成功！")
            this.visible = false;
            this.getPageList();
          });
        }
      });
    },
    changeStatus(row) {
      let msg = row.status ? '停用' : '启用';
      let status = row.status ? 0 : 1;
      this.$confirm('确定要' + msg + '吗?', '修改状态', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        studyScheduleApi.updateStatus(row.id, status).then(res => {
          this.$nextTick(() => this.getPageList())
          this.$message.success('修改成功!')
        }).catch(err => {

        })
      }).catch(err => {

      })
    },
    /** 编辑按钮*/
    handleEdit(row) {
      this.reset();
      this.visible = true;
      this.title = '编辑规划';
      if (row.id) {
        this.getDetail(row.id);
      }
    },
    /** 详情按钮操作 */
    handleView(row) {
      const id = row.id;
      // ls.setItem('scheduleId',id);
      // this.$router.push({path:'/schedule/studentList'})
    },

    getPageList() {
      this.tableLoading = true;
      this.dataQuery.pageNum = this.tablePage.currentPage;
      this.dataQuery.pageSize = this.tablePage.size;
      studyScheduleApi.getUnFixList(this.dataQuery).then(res => {
        this.tableData = res.data.data;
        this.tableLoading = false;
        // 设置后台返回的分页参数
        pageParamNames.forEach(name => this.$set(this.tablePage, name, parseInt(res.data[name])))
      })
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.visible = true;
      this.title = '新增规划';
    },
    handlePage() {
      this.tablePage = {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
        this.getPageList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dataQuery = {
        className: '',
        courseType: '',
        level: '',
      };
      this.getPageList();
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val;
      this.getPageList();
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val;
      this.getPageList();
    },
    close() {
      this.reset();
    },
    reset() {
      this.form = {
        className: null,
        courseType: null,
        level: null,
        weeks: null,
        startTime: '',
        endTime: ''
      };
      this.time1 = '';
      this.time2 = '';
      if (this.$refs['form']) {
        this.$refs['form'].resetFields();
      }
    }
  }
};
</script>

<style>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.period-table td,
.period-table th {
  text-align: center;
}

@media (max-width: 767px) {
  .el-message-box {
    width: 80% !important;
  }
}
</style>
