<template>
  <div class="app-container">
    <el-form :inline="true" class="container-card" label-width="110px" label-position="left">
      <el-row>
        <el-col :span="8" :xs="24">
          <el-form-item label="门店编号：">
            <el-input v-model="dataQuery.merchantCode" placeholder="请输入门店编号" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="学员编号：">
            <el-input v-model="dataQuery.studentCode" placeholder="请输入学员编号" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="登录账号：">
            <el-input v-model="dataQuery.loginName" placeholder="请输入登录账号" clearable />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>

        <el-col :span="8" :xs="24">
          <el-form-item label="姓名：">
            <el-input v-model="dataQuery.studentName" placeholder="请输入姓名" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="状态:">
            <el-select v-model="dataQuery.isEnable" filterable value-key="value" placeholder="请选择"
              @change="check(dataQuery.isEnable)" clearable>
              <el-option v-for="(item,index) in statusList" :key="index" :label="item.value" :value="item.type" />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="8" :xs="24">
          <el-form-item>
            <el-button type="primary" size="small" icon="el-icon-search" @click="fetchData01()">搜索</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-table class="common-table" v-loading="tableLoading" :data="tableData" style="width: 100%;margin-bottom: 20px;"
      row-key="id" border default-expand-all :tree-props="{list: 'children', hasChildren: 'true'}">
      <el-table-column prop="scheduleCode" label="课程进度编号" width="120"></el-table-column>
      <el-table-column prop="merchantCode" label="门店编号" width="100"></el-table-column>
      <el-table-column prop="withdrawnBonus" label="操作" width="120">
        <template slot-scope="scope">
          <el-button type="warning" size="mini" icon="el-icon-switch-button" v-if="scope.row.isEnable === '开通'"
            @click="updateStatus(scope.row.id,0)">暂停</el-button>
          <el-button type="danger" size="mini" icon="el-icon-video-pause" v-if="scope.row.isEnable === '暂停'"
            @click="updateStatus(scope.row.id,1)">开通</el-button>
          <el-button type="danger" size="mini" icon="el-icon-video-pause" v-if="scope.row.isEnable === '归档'"
            @click="regainArchive(scope.row.id,1)">恢复存档</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="studentCode" label="学员编号" width="100"></el-table-column>
      <el-table-column prop="courseCode" label="课程编号"></el-table-column>
      <el-table-column prop="studentName" label="学员名称"></el-table-column>
      <el-table-column prop="categoryName" label="课程分类"></el-table-column>
      <el-table-column prop="bigClassName" label="课程类型"></el-table-column>
      <el-table-column prop="courseLevelName" label="课程小类"></el-table-column>
      <el-table-column prop="courseName" label="课程名称" width="100"></el-table-column>
      <el-table-column prop="courseStageName" label="课程学段"></el-table-column>
      <el-table-column prop="courseEditionName" label="教材版本" width="100"></el-table-column>
      <el-table-column prop="lastTime" label="开通时间" width="160"></el-table-column>
      <el-table-column prop="studyIngWordCount" label="学习中单词" width="160"></el-table-column>
      <el-table-column prop="scheduleName" label="学习进度" width="120"></el-table-column>
      <el-table-column prop="learnProgress" label="学习轮次" width="120"></el-table-column>
      <el-table-column prop="isEnable" label="状态" width="80"></el-table-column>
    </el-table>
    <!-- 分页 -->
    <el-col :span="24" style="overflow-x: auto;" :xs="24">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>
  </div>
</template>

<script>
import studentRecordApi from "@/api/areasStudentCourseList";
import Tinymce from "@/components/Tinymce";
import { pageParamNames } from "@/utils/constants";
import schoolApi from '@/api/areasSchoolList'
import ls from '@/api/sessionStorage'
import grammarApi from '@/api/grammar'

export default {
  data() {
    return {
      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      tableData: [],
      dataQuery: {
        studentCode: '',
        loginName: '',
        merchantCode: '',
        studentName: '',
        isEnable: ''
      },
      statusList: [
        {
          type: 1,
          value: '开通'
        },
        {
          type: 0,
          value: '暂停'
        },
        {
          type: 2,
          value: '归档'
        }
      ], //状态
    }
  },
  created() {
    this.fetchData01();
    // 获取上个页面的学员编号
    this.dataQuery.studentCode = this.$route.query.studentCode;
  },
  methods: {
    fetchData01() {
      this.tablePage = {
        currentPage: 1,
        size: this.tablePage.size,
        totalPage: null,
        totalItems: null
      }
      this.fetchData();
    },
    // 查询列表
    fetchData() {
      const that = this;
      that.tableLoading = true
      studentRecordApi.courseRecordList(that.tablePage.currentPage, that.tablePage.size, that.dataQuery).then(res => {
        that.tableData = res.data.data
        that.tableLoading = false
        // 设置后台返回的分页参数
        pageParamNames.forEach(name => that.$set(that.tablePage, name, parseInt(res.data[name])))
      })
    },
    exportStudentRecord() {
      const that = this;
      studentRecordApi.exportAreasStudentRecord(that.dataQuery).then(response => {
        console.log(response)
        if (!response) {
          this.$notify.error({
            title: '操作失败',
            message: '文件下载失败'
          })
        }
        const url = window.URL.createObjectURL(response)
        const link = document.createElement('a')
        link.style.display = 'none'
        link.href = url // 获取服务器端的文件名
        link.setAttribute('download', '课程记录表.xls')
        document.body.appendChild(link)
        link.click()
        this.exportLoading = false

      })

    },
    updateStatus(id, status) {
      const that = this;
      this.$confirm('确定操作吗?', '修改状态', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        studentRecordApi.updateStatus(id, status).then(res => {
          that.$nextTick(() => that.fetchData())
          that.$message.success('修改成功')
        })

      })

    },
    regainArchive(id, status) {
      const that = this;
      this.$confirm('确定操作吗?', '修改状态', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        studentRecordApi.regainArchive(id, status).then(res => {
          that.$nextTick(() => that.fetchData())
          that.$message.success('修改成功')
        })

      })

    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val;
      this.fetchData();
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val;
      this.fetchData();
    },
    //查看进度
    // enterChildrenList(studentCode) {
    //   const that = this;
    //   ls.setItem('areaStudentCode', studentCode);
    //   that.$router.push({
    //     path: "/student/studentCourseProgress",
    //     query: {
    //       studentCode:studentCode
    //     }
    //   });
    // },
  }
};
</script>

<style>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}
.period-table td,
.period-table th {
  text-align: center;
}
.mt20 {
  margin-top: 20px;
}
.red {
  color: red;
}
.green {
  color: green;
}
@media (max-width: 767px) {
  .el-message-box {
    width: 80% !important;
  }
}
</style>
