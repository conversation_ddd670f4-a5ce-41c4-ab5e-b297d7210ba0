
import request from '@/utils/request'

export default {

  // 分页查询
  committeemanList(pageNum, pageSize, data) {
    return request({
      url: '/studyroom/webCommitteeman/list/' + pageNum + '/' + pageSize,
      method: 'GET',
      params: data
    })
  },
  //新增
  addCommitteeman(data){
    return request({
      url: '/studyroom/webCommitteeman/addCommitteeman',
      method: 'POST',
      data
    })
  },
  //详情
  committeemanView(id){
    return request({
      url: '/studyroom/webCommitteeman/detail',
      method: 'GET',
      params:{
        id:id
      }
    })
  },
  //修改状态
  committeemanSuspend(id,suspend){
    return request({
      url: '/studyroom/webCommitteeman/suspend',
      method: 'PUT',
      params:{
        id:id,
        suspend:suspend
      }
    })
  },

  //查询省市区
  getProvince(pid){
    return request({
      url: '/v2/mall/queryProvinceRegion/two/level',
      method: 'GET',
      params: {
        pid:pid
      }
    })
  },
  getAllRegion(){
    return request({
      url: '/v2/mall/queryAllRegion/two/level',
      method: 'GET'
    })
  },
  getAllArea(){
    return request({
      url: '/v2/mall/queryAllRegion',
      method: 'GET'
    })
  }
}

