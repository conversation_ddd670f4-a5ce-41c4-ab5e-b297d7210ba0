<template>
  <div style="position:relative">
    <el-upload
      action
      v-loading="loading"
      :http-request="uploadHttp"
      :file-list="fileList"
      :limit="1"
      :on-error="handleError"
      :on-exceed="onExceed"
      :on-remove="handleRemove"
      :on-success="handleSuccess"
      name="file"
    >
      <div class="el-upload__text">
        <el-button size="small" type="primary" style>点击上传</el-button>
      </div>
    </el-upload>
    <el-link type="primary" :underline="false" v-for="item in fileList" target="_blank"
             :href="item.url"
             style="font-size:12px;vertical-align: baseline;margin-left: 20px;position: absolute;left: 100px;top: 0px"
    >预览
    </el-link>
  </div>
</template>

<script>
import questionApi from '@/api/paper/question'

export default {
  name: 'JsonUpload',
  props: {
    // 展示的图片列表
    fileList: {
      type: Array,
      default() {
        return []
      }
    },
    dialogVisible: false
  },
  data() {
    return {
      loading: false
    }
  },
  created() {

  },
  methods: {
    uploadHttp({ file }) {
      this.loading = true
      const formData = new FormData()
      formData.append('file', file)
      questionApi.uploadJson(formData).then(res => {
        if (res.success) {
          this.fileList.push({ name: file.name, uid: file.uid, url: res.data })
          this.handleSuccess(res.data, file.name)
        }
        this.loading = false
      }).catch((e) => {
        this.loading = false
        this.fileList = []
        this.handleSuccess()
      })
    },
    // 图片上传失败
    handleError(err) {
      this.$message.error('上传失败')
      console.log(err)
    },
    // 文件超过上传个数
    onExceed() {
      this.$message.error(`最多只能上传1个文件`)
      return false
    },
    // 图片删除
    handleRemove(file) {
      console.log('前===', this.fileList)
      var index = this.fileList.findIndex(item => {
        if (item.uid === file.uid) {
          return true
        }
      })
      this.fileList.splice(index, 1)
      console.log('后===', this.fileList)
      this.$emit('handleRemove', file)
    },
    // 图片上传成功
    handleSuccess(res, fileName) {
      this.$emit('handleSuccess', res || '', fileName || '')
    }
    /* 上传图片结束 */
  }
}
</script>
