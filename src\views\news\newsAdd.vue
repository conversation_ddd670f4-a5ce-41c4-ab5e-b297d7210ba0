<template>
    <div class="app-container">
        <div>
            <span>通知标题</span>
            <el-input v-model="title" placeholder="请输入内容" style="width: 500px;margin-left: 30px;" maxlength="30"></el-input>
        </div>
        <div style="width: 80%; margin: 50px 0">
            <div style="margin-bottom: 15px;">通知内容</div>
            <el-input type="textarea" :autosize="{ minRows: 20, maxRows: 40 }" placeholder="请输入内容" v-model="contents" maxlength="1000">
            </el-input>
        </div>

        <div style="margin-top: 100px;">
            <span style="margin-right: 30px;">可见范围</span>
            <el-radio-group v-model="radio">
                <el-radio :label="1">全部</el-radio>
                <el-radio :label="2">超级俱乐部</el-radio>
                <el-radio :label="3">门店</el-radio>
            </el-radio-group>
        </div>


        <div style="margin-top: 40px;">
            <el-button @click="back">返回</el-button>
            <!-- <el-button type="primary">保存</el-button> -->
            <el-button type="primary" @click="submit">提交</el-button>
        </div>
    </div>
</template>

<script>

// import { quillEditor } from 'vue-quill-editor'
import newsApi from '@/api/news'
import ls from '@/api/noticeEdit'
import { de } from 'pinyin/data/dict-zi-web'


export default {
    components: {
        // quillEditor
    },
    name: 'add',
    data() {
        return {
            radio: '',
            title: "",
            contents: "",
            userRole: "",
        }
    },
    methods: {
        submit() {
            this.index()
        },
        index() {
            let that = this;
            if (that.radio == 1) {
                that.userRole = 'All'
            } else if (that.radio == 2) {
                that.userRole = 'Operations'
            } else {
                that.userRole = 'School'
            }
            newsApi.addNotice(that.title, that.contents, that.userRole).then((res) => {
                that.$message({ message: '操作成功', type: 'success' });
                this.title = '';
                this.contents = '';
                this.userRole = '';
                this.radio='';
                this.$router.go(-1);
            })
        },

        back() {
            this.$router.back();
        },

    },
    mounted() {}, 

    created() {},


}
</script>

<style></style>
