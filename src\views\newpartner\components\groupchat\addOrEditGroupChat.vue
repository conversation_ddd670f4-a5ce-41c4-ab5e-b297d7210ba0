<template>
  <el-dialog :title="openType == 'add' ? '新增群聊' : '编辑群聊'" :visible.sync="dialogVisible" width="560px" @close="handleClose">
    <el-form ref="form" :model="groupForm" :rules="groupRules" label-width="80px" label-position="left">
      <el-form-item label="群聊名称" prop="groupChatName">
        <el-input v-model="groupForm.groupChatName" placeholder="请输入群聊名称" maxlength="42"></el-input>
      </el-form-item>
      <el-form-item label="督导" prop="supervisionList">
        <el-select v-model="groupForm.supervisionList" multiple placeholder="请选择" class="fitWidth">
          <el-option v-for="item in showSupervisionList" :key="item.id" :label="item.realName" :value="item.merchantCode"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="赋能专员" prop="empoweringSpecialist">
        <el-select v-model="groupForm.empoweringSpecialist" multiple placeholder="请选择" class="fitWidth">
          <el-option v-for="item in showEmpoweringSpecialist" :key="item.id" :label="item.realName" :value="item.merchantCode"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="建群时间" prop="chatCreateTime">
        <el-date-picker v-model="groupForm.chatCreateTime" value-format="yyyy-MM-dd HH:mm:ss" type="datetime" placeholder="选择时间"></el-date-picker>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">取 消</el-button>
      <el-button type="primary" :loading="formLoading" @click="handleSubmit">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  import groupChatApi from '@/api/newPartner/groupChat';
  export default {
    props: {
      dialogVisible: {
        type: Boolean,
        default: false
      },
      openType: {
        type: String,
        default: ''
      }
    },
    data() {
      return {
        formLoading: false,
        groupForm: {
          groupChatName: '',
          supervisionList: [],
          empoweringSpecialist: [],
          chatCreateTime: ''
        },
        groupRules: {
          groupChatName: [{ required: true, message: '请输入群聊名称', trigger: 'blur' }],
          supervisionList: [{ required: true, message: '请选择督导', trigger: 'change' }],
          empoweringSpecialist: [{ required: true, message: '请选择赋能专员', trigger: 'change' }],
          chatCreateTime: [{ required: true, message: '请选择建群时间', trigger: 'change' }]
        },
        showSupervisionList: [],
        showEmpoweringSpecialist: [],
        chatId: '' // 群聊id
      };
    },
    created() {
      this.getUserData();
    },
    methods: {
      open(item) {
        this.groupForm.groupChatName = item.groupChatName;
        this.groupForm.supervisionList = item.supervisionList.map((el) => el.merchantCode);
        this.groupForm.empoweringSpecialist = item.empoweringSpecialist.map((el) => el.merchantCode);
        this.groupForm.chatCreateTime = item.chatCreateTime;
        this.chatId = item.id;
      },
      getUserData() {
        // 督导数据
        groupChatApi.getUserListByTag('Supervision').then((res) => {
          this.showSupervisionList = res.data;
        });
        // 赋能专员数据
        groupChatApi.getUserListByTag('Empowering').then((res) => {
          this.showEmpoweringSpecialist = res.data;
        });
      },
      handleCancel() {
        this.handleClose();
      },
      handleSubmit() {
        const that = this;
        that.$refs['form'].validate((valid) => {
          if (valid) {
            let params = {
              groupChatName: that.groupForm.groupChatName,
              chatCreateTime: that.groupForm.chatCreateTime,
              supervisionList: that.groupForm.supervisionList,
              empoweringSpecialist: that.groupForm.empoweringSpecialist
            };
            that.formLoading = true;
            if (that.openType == 'add') {
              groupChatApi
                .addGroupChat(params)
                .then((res) => {
                  if (res.success && res.code == 20000) {
                    that.$message.success('新增群聊成功!');
                    that.handleClose(true);
                  }
                })
                .finally(() => {
                  that.formLoading = false;
                });
            } else {
              params = Object.assign(params, { id: that.chatId });
              groupChatApi
                .updateGruopChat(params)
                .then((res) => {
                  if (res.success && res.code == 20000) {
                    that.$message.success('修改群聊成功!');
                    that.handleClose(true);
                  }
                })
                .finally(() => {
                  that.formLoading = false;
                });
            }
          }
        });
      },
      handleClose(isTrue) {
        this.$refs['form'].clearValidate();
        this.$refs['form'].resetFields();
        this.$emit('closeDialog', isTrue);
      }
    }
  };
</script>

<style lang="scss" scoped>
  .fitWidth {
    width: 100%;
  }
</style>
