/**
 * 高报师相关接口
 */
import request from '@/utils/request'

export default {
  // 新增
  addExpertsApplicants(data) {
    return request({
      url: '/dzy/applicants',
      method: 'POST',
      data
    })
  },
  getRecharge() {
    return request({
      url: '/dzy/applicants/recharge/detail',
      method: 'GET',
    })
  },

  // 分页查询
  expertsApplicantsList(pageNum, pageSize, data) {
    return request({
      url: '/dzy/applicants/page/' + pageNum + '/' + pageSize,
      method: 'GET',
      params: data
    })
  },

  // 分页查询
  expertsArrearstList(pageNum, pageSize, data) {
    return request({
      url: '/dzy/applicants/arrears/' + pageNum + '/' + pageSize,
      method: 'GET',
      params: data
    })
  },

  // 分页查询
  expertsPersonList(pageNum, pageSize, data) {
    return request({
      url: '/dzy/applicants/person/' + pageNum + '/' + pageSize,
      method: 'GET',
      params: data
    })
  },
  // 修改回显
  detail(id) {
    return request({
      url: '/dzy/applicants/detail/' + id,
      method: 'GET'
    })
  },
  // 编辑
  updateApplicants(data) {
    return request({
      url: '/dzy/applicants',
      method: 'PUT',
      data
    })
  },

  // 高报师开通与暂停
  updateIsEnable(id, status,merchantCode) {
    return request({
      url: '/dzy/applicants/enable?id=' + id + '&isEnable=' + status+ '&merchantCode=' + merchantCode,
      method: 'PUT'
    })
  },
  // 优志愿账户开通与暂停
  updateStatus(id, status) {
    return request({
      url: '/dzy/applicants/status?id=' + id + '&status=' + status,
      method: 'PUT'
    })
  },
// 微信扫码充值接口
  recharge(data){
    return request({
      url: '/dzy/applicants/recharge',
      method: 'POST',
      data
    })
  },
  //上级充值
  expertsRecharge(data){
    return request({
      url: '/dzy/applicants/superior',
      method: 'POST',
      data
    })
  },
}
