/**
 * 授权码相关接口
 */
import request from '@/utils/request'

export default {
  // 分页查询
  authorizationCodeList(pageNum, pageSize, data) {
    return request({
      url: '/znyy/author/code/page/list/' + pageNum + '/' + pageSize,
      method: 'GET',
      params: data
    })
  },
  //生成授权码
  importAuthorizationCode(count){
    return request({
      url: '/znyy/author/code/add/' +count,
      method : 'POST'
    })
  },
  //批量导入
  addAuthorizationCode(file) {
    return request({
      url: '/znyy/author/code/execel/import',
      method: 'POST',
      params: file,
      responseType:'blob'
    })
  },
  // 导出
  exportAuthorizationCode(count) {
    return request({
      url: '/znyy/author/code/export/authorization/code/'+count,
      method: 'GET',
      responseType: 'blob'
    })
  },
  // 导出
  agentExport1(listQuery) {
    return request({
      url: '/znyy/operate/agent/query',
      method: 'GET',
      responseType: 'blob',
      params:listQuery
    })
  },
  // 开通与暂停
  updateStatus(id, status) {
    return request({
      url: '/znyy/agent/updateStatus?id=' + id + '&isEnable=' + status,
      method: 'PUT'
    })
  },

  updatePaymentIsComplete(id,paymentIsComplete){
    return request({
      url:'/znyy/agent/updatePaymentIsComplete?id='+id+'&paymentIsComplete='+paymentIsComplete,
      method : 'PUT'
    })
  },

  // 开通与暂停推荐二级分润
  updateReProfitRank(id, reProfitRank) {
    return request({
      url: '/znyy/agent/update/profit?id=' + id + '&reProfitRank=' + reProfitRank,
      method: 'PUT'
    })
  },
  // 审核
  examine(id,checkReason,isCheck){
    return request({
      url: '/znyy/agent/checkStatus?id=' + id + '&checkReason=' + checkReason + '&isCheck=' + isCheck ,
      method: 'PUT'
    })
  },
  // 分页查询
  authorizationByCode(pageNum, pageSize, data) {
    return request({
      url: '/znyy/author/code/list/check/' + pageNum + '/' + pageSize,
      method: 'GET',
      params: data
    })
  },
  // 授权码绑定校区
  bindingAuthorizationCode(data) {
    return request({
      url: '/znyy/author/code/school/binding',
      method: 'PUT',
      params: data
    })
  },
}
