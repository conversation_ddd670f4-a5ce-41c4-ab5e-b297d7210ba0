<template>
  <div class="app-container">
    <!-- 搜索 -->
    <el-form ref="form" :inline="true" class="SearchForm">
      <el-row>
        <el-col :span="8" :xs="24">
          <el-form-item label="阶段:">
            <el-select v-model="dataQuery.phase" filterable value-key="value" placeholder="请选择"
              @change="check(dataQuery.phase)" clearable>
              <el-option v-for="(item, index) in phaseTypeList" :key="index" :label="item.value" :value="item.type" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="知识点:">
            <el-input v-model="dataQuery.grammarName" @keyup.enter.native="fetchData02()" style="width: 200px;"
              placeholder="请输入知识点：" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="2" :xs="8" style="text-align: right;">
          <el-button type="primary" icon="el-icon-search" size="mini" @click="fetchData02()">搜索</el-button>
        </el-col>
      </el-row>
      <el-col :span="15" style="height: 1px;" />
    </el-form>

    <div class="SearchForm">
      <!-- 表格 -->
      <el-table class="common-table" :data="tableData" stripe border
        :default-sort="{ prop: 'addTime', order: 'descending' }" v-loading="tableLoading">
        <el-table-column prop="phaseName" label="阶段" sortable></el-table-column>
        <el-table-column prop="parentName" label="语法点" sortable></el-table-column>
        <el-table-column prop="id" label="操作" sortable>
          <template slot-scope="scope">
            <el-button type="success" size="mini" icon="el-icon-edit-outline"
              @click="openEditName(scope.row.id)">编辑</el-button>
            <el-button type="success" size="mini" icon="el-icon-edit-outline"
              @click="openEdit(scope.row.id)">课件编辑</el-button>
            <el-button type="success" size="mini" icon="el-icon-edit-outline"
              @click="editQuestion(scope.row.id)">题库编辑</el-button>
            <el-button type="danger" size="mini" icon="el-icon-delete" @click="deleteGrammar(scope.row.id)">删除</el-button>
          </template>
        </el-table-column>
        <el-table-column prop="grammarName" label="知识点" sortable></el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <el-col :span="20">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 50, 100, 150, 200]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>

    <!--  知识点名编辑  -->
    <el-dialog title="编辑知识点" :visible.sync="showNameEdit" width="60%" :close-on-click-modal="false" @close="close">
      <el-form :ref="'updateGrammar'" :rules="updateSingle" :model="updateGrammar" label-position="left"
        label-width="80px" style="width: 100%">
        <el-form-item label="id" prop="id" v-show="false">
          <el-col :xs="24" :span="12">
            <el-input v-model="updateGrammar.id"></el-input>
          </el-col>
        </el-form-item>
        <el-form-item label="语法名" prop="grammarName">
          <el-col :xs="24" :span="12">
            <el-input v-model="updateGrammar.grammarName"></el-input>
          </el-col>
        </el-form-item>
        <!--        <el-form-item label="视频url" prop="videoUrl">-->
        <!--          <el-col :xs="24" :span="12">-->
        <!--            <el-input v-model="updateGrammar.videoUrl"></el-input>-->
        <!--          </el-col>-->
        <!--        </el-form-item>-->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="editName('updateGrammar')">确定</el-button>
        <el-button size="mini" @click="closeEdit">关闭</el-button>
      </div>
    </el-dialog>

    <!-- 编辑弹窗 -->
    <el-dialog title="编辑课件" :visible.sync="showEdit" width="60%" :close-on-click-modal="false" @close="close">
      <el-form :ref="'updateKnowledge'" :rules="updateSingle" :model="updateKnowledge" label-position="left"
        label-width="80px" style="width: 100%">
        <el-form-item label="id" prop="id" v-show="false">
          <el-col :xs="24" :span="12">
            <el-input v-model="updateKnowledge.id"></el-input>
          </el-col>
        </el-form-item>
        <el-tabs v-model="activeName" type="border-card" @tab-click="switchActiveName(activeName)">

          <el-tab-pane label="课件详情" name="first">
            <el-row>
              <el-col :span="4">
                <span style="font-weight: bold;">课件详情</span>
              </el-col>
              <el-col :span="20">
                <Tinymce ref="editor" v-model="contenthtml" :height="400" />
              </el-col>
            </el-row>
          </el-tab-pane>

          <el-tab-pane label="需背诵课件详情" name="second">
            <el-row>
              <el-col :span="4">
                <span style="font-weight: bold;">需背诵课件详情</span>
              </el-col>
              <el-col :span="20">
                <Tinymce ref="editor01" v-model="reciteContenthtml" :height="400" />
              </el-col>
            </el-row>
          </el-tab-pane>

        </el-tabs>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="editKnowledge('updateKnowledge')">确定</el-button>
        <el-button size="mini" @click="closeEdit">关闭</el-button>
      </div>

    </el-dialog>
  </div>
</template>

<script>
import grammarApi from "@/api/grammar";
import Tinymce from '@/components/Tinymce'
import {
  pageParamNames
} from "@/utils/constants";
export default {
  components: {
    Tinymce
  },
  data() {
    return {
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null,
      },
      tableLoading: false,
      dataQuery: {
        grammarName: "",
        phase: "",
        knowledgeName: "",
        parent: ""
      },
      updateSingle: {
        handouts: [{
          required: true,
          message: '必填',
          trigger: 'blur'
        }]
      },
      updateKnowledge: {}, //更新题目集合
      isRouterAlive: true, //局部刷新
      tableData: [], //表格数据
      dialogVisible: false, // 修改弹窗是否展示
      showEdit: false, //编辑弹窗
      showNameEdit: false,
      rows: [], //选中的数据id存放处
      updateCourseWord: {}, // 修改数据
      updateGrammar: {}, // 修改数据
      phase: "",
      phaseTypeList: [], //课程分类
      contenthtml: '',
      reciteContenthtml: '',
      // contenthtml01:'',
      activeName: 'first', // tab默认第一个
    };
  },
  created() {
    this.fetchData01();
    // 获取所属分类
    this.getPhaseType();
  },
  methods: {
    fetchData01() {
      this.tablePage = {
        currentPage: 1,
        size: this.tablePage.size,
        totalPage: null,
        totalItems: null
      }
      this.fetchData()
    },
    fetchData02() {
      this.tablePage = {
        currentPage: 1,
        size: this.tablePage.size,
        totalPage: null,
        totalItems: null
      }
      window.localStorage.setItem("grammarParent", "");
      this.fetchData()
    },
    // 获取分类返回类型
    getPhaseType() {
      grammarApi.getPhaseType().then((res) => {
        this.phaseTypeList = res.data;
      });
    },
    switchActiveName(ele) {
      const that = this;
      // if(ele==="second"){
      //   that.contenthtml=that.reciteContenthtml;
      that.$refs.editor01.setContent(that.reciteContenthtml)
      // }else{
      //   that.contenthtml=that.contenthtml01;
      that.$refs.editor.setContent(that.contenthtml)
      // }
    },
    // 查询表格列表
    fetchData() {
      const that = this;
      that.dataQuery.parent = window.localStorage.getItem("grammarParent");
      grammarApi.getKnowledgeList(
        that.tablePage.currentPage,
        that.tablePage.size,
        that.dataQuery
      ).then((res) => {
        that.tableData = res.data.data;
        that.tableLoading = false;
        // 设置后台返回的分页参数
        pageParamNames.forEach((name) =>
          that.$set(that.tablePage, name, parseInt(res.data[name]))
        );
      });
    },
    //局部刷新
    reload() {
      this.isRouterAlive = false;
      this.$nextTick(function () {
        this.isRouterAlive = true;
      });
    },
    openEdit(id) {
      const that = this
      that.activeName = 'first'
      grammarApi.getKnowledge(id).then(res => {
        that.updateKnowledge = res.data;
        // that.contenthtml01 = that.updateKnowledge.handouts
        that.contenthtml = that.updateKnowledge.handouts// 副文本赋值
        that.reciteContenthtml = that.updateKnowledge.reciteHandouts // 副文本赋值
        that.$refs.editor.setContent(that.updateKnowledge.handouts)
        that.$refs.editor01.setContent(that.updateKnowledge.reciteHandouts)
      })
      that.showEdit = true;
    },
    closeEdit() {
      this.showEdit = false;
      this.showNameEdit = false;
    },
    editKnowledge(ele) {
      const that = this;
      that.updateKnowledge.handouts = that.$refs.editor.value
      that.updateKnowledge.reciteHandouts = that.$refs.editor01.value

      that.$refs[ele].validate((valid) => {
        // 表单验证
        if (valid) {
          const loading = this.$loading({
            lock: true,
            text: "编辑课件提交",
            spinner: "el-icon-loading",
            background: "rgba(0, 0, 0, 0.7)",
          });
          grammarApi.editHandouts(that.updateKnowledge).then(() => {
            that.showEdit = false;
            loading.close();
            that.$nextTick(() => that.fetchData());
            that.$message.success("修改课件成功");
          })
            .catch((err) => {
              // 关闭提示弹框
              loading.close();
            });
        } else {
          console.log("error submit!!");
          // loading.close();
          return false;
        }
      });
    },
    editQuestion(grammarId) {
      const that = this;
      window.localStorage.setItem("grammarId", grammarId);
      that.$router.push({
        path: "/grammar/grammarQuestion"
      });
    },
    // 关闭弹窗
    close() {
      this.dialogVisible = false;
    },
    openEditName(id) {
      grammarApi.getKnowledge(id).then(res => {
        this.updateGrammar = res.data;
      })
      this.showNameEdit = true;
    },
    editName(ele) {
      const that = this;
      that.$refs[ele].validate((valid) => {
        // 表单验证
        if (valid) {
          const loading = this.$loading({
            lock: true,
            text: "编辑知识点提交",
            spinner: "el-icon-loading",
            background: "rgba(0, 0, 0, 0.7)",
          });
          grammarApi.editGrammarName(that.updateGrammar).then(() => {
            that.showNameEdit = false;
            loading.close();
            that.$nextTick(() => that.fetchData());
            that.$message.success("修改知识名成功");
          })
            .catch((err) => {
              // 关闭提示弹框
              loading.close();
            });
        } else {
          console.log("error submit!!");
          // loading.close();
          return false;
        }
      });
    },
    //删除操作题目
    deleteGrammar(id) {
      this.$confirm("知识点删除后，对应的题目也会全部删除", "删除知识点", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          grammarApi.deleteGrammar(id)
            .then((res) => {
              this.$nextTick(() => this.fetchData());
              this.$message.success("删除知识点成功!");
            })
            .catch((err) => { });
        })
        .catch((err) => { });
    },
    // 点击编辑按钮
    handleUpdate(id) {
      const that = this;
      that.dialogVisible = true;
      grammarApi.queryActive(id).then((res) => {
        that.updateCourseWord = res.data;
        console.log(that.updateCourseWord);
      }).catch((err) => { });
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val;
      this.fetchData();
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val;
      this.fetchData();
    },
  },
  beforeDestroy() {
    window.localStorage.setItem("grammarParent", "");
  }
};
</script>

<style>
.lh36 {
  line-height: 36px;
  font-size: 14px;
}

.SearchForm {
  width: 100%;
  margin-bottom: 15px;
  background: #fff;
  border-radius: 6px;
  padding: 5px 0;
  padding-left: 5px;
  box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.2);
}

.btn-add {
  padding: 5px;
}

.red {
  color: red;
}

.green {
  color: green;
}

.course-table {
  text-align: center;
}

.course-table td,
.course-table th {
  padding: 5px 0;
  text-align: center;
}

.course-table button {
  padding: 2px;
}

.icon-stop {
  float: left;
  display: inline-block;
  width: 12px;
  height: 12px;
  background: url("../../icons/stop.png") no-repeat top center/contain;
}

.mt22 {
  margin-top: 22px;
}

.blue {
  margin-right: 50px;
  color: #409eff;
}

@media (max-width:767px) {
  .el-message-box {
    width: 80% !important;
  }
}</style>
