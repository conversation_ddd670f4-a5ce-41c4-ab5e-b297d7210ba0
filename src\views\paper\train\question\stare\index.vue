<template>
  <div class="app-container">
    <el-form :model="dataQuery" ref="queryForm" :inline="true">
      <el-form-item label="题型：">
        <el-select v-model="dataQuery.questionType" placeholder="全部" clearable>
          <el-option v-for="(item, index) in questionTypeFilter" :key="index" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" style="marginRight: 5px" @click="submitForm">查询</el-button>
        <el-popover placement="bottom" trigger="click">
          <el-button type="warning" size="mini" v-for="item in stareEditUrlEnum" :key="item.questionType"
            @click="$router.push({ path: item.value })">{{ item.name }}</el-button>
          <el-button slot="reference" type="primary" class="link-left">添加</el-button>
        </el-popover>
      </el-form-item>
    </el-form>
    <el-table class="common-table" v-loading="listLoading" :data="tableData" style="width: 100%; margin-bottom: 20px"
      row-key="id" border default-expand-all>
      <el-table-column prop="id" label="Id" />
      <el-table-column prop="type" label="所属类型" :formatter="typeFormatter"></el-table-column>
      <el-table-column label="操作" align="center" width="220px">
        <template slot-scope="{ row }">
          <el-button size="mini" @click="editQuestion(row)">编辑</el-button>
          <el-button size="mini" type="danger" @click="deleteQuestion(row.id)" class="link-left">删除</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="questionType" label="题型" :formatter="questionTypeFormatter"></el-table-column>
      <el-table-column prop="title" label="题目" show-overflow-tooltip />
      <el-table-column prop="createTime" label="创建时间" />

    </el-table>
    <!-- 分页 -->
    <el-col :span="24" style="margin-bottom: 20px">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>
  </div>
</template>

<script>
import questionApi from '@/api/paper/train/question'
import { mapGetters, mapState } from 'vuex';
import { pageParamNames } from "@/utils/constants";
import '/public/components/ueditor/themes/iframe.css'

export default {
  data() {
    return {
      questionTypeFilter: [],
      dataQuery: {
        id: null,
        type: 'STARE_',
        questionType: null,
        difficulty: null,
      },
      listLoading: true,
      tableData: [],
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null,
      },
      total: 0,
    };
  },
  created() {
    this.getList();
    this.questionTypeFilter = this.trainQuestionType.filter(
      (data) => data.value.includes(this.dataQuery.type)
    )
  },
  methods: {
    typeFormatter(row, column, cellValue, index) {
      return this.enumFormat(this.trainType, cellValue)
    },
    questionTypeFormatter(row, column, cellValue, index) {
      return this.enumFormat(this.trainQuestionType, cellValue)
    },
    submitForm() {
      this.getList()
    },
    getList() {
      this.listLoading = true
      this.dataQuery.pageNum = this.tablePage.currentPage
      this.dataQuery.pageSize = this.tablePage.size
      this.dataQuery.categoryType=1
      questionApi.list(this.dataQuery).then(res => {
        this.tableData = res.data.data
        this.listLoading = false
        // 设置后台返回的分页参数
        pageParamNames.forEach(name =>
          this.$set(this.tablePage, name, parseInt(res.data[name]))
        )
      })
    },
    editQuestion(row) {
      let url = this.trainUrlFormat(this.stareEditUrlEnum, row.questionType);
      this.$router.push({ path: url, query: { id: row.id } });
    },
    deleteQuestion(id) {
      this.$confirm('确定要删除吗？', '删除题型', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        questionApi.delete(id).then(res => {
          this.$message.success('删除成功！')
          this.getList()
        })
      })
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val;
      this.getList();
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val;
      this.getList();
    },
  },
  computed: {
    ...mapGetters('enumItem', ['enumFormat', 'trainUrlFormat']),
    ...mapState('enumItem', {
      trainType: state => state.train.trainType,
      trainQuestionType: state => state.train.trainQuestionType,
      stareEditUrlEnum: state => state.train.stareEditUrlEnum
    }),
  }
}
</script>

<style lang="less" scoped>
/deep/.el-dialog__header {
  border-bottom: 1px solid #dfdfdf;
}
</style>
<style>
.el-tooltip__popper {
  max-width: 800px;
}
</style>
