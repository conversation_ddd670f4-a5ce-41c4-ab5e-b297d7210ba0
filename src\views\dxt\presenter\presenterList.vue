<template>
  <div class="app-container">
    <el-form :inline="true" class="container-card" label-width="140px" label-position="left">
      <el-form-item label="主讲老师姓名：">
        <el-input v-model="dataQuery.presenterName" placeholder="请输入主讲老师姓名：" clearable />
      </el-form-item>
      <el-form-item label="主讲老师手机号：">
        <el-input v-model="dataQuery.presenterPhone" placeholder="请输入主讲老师手机号：" clearable />
      </el-form-item>
      <el-form-item label="主讲老师编号：">
        <el-input v-model="dataQuery.id" placeholder="请输入主讲老师编号：" clearable />
      </el-form-item>
      <el-form-item style="margin-left: 20px;">
        <el-button type="primary" icon="el-icon-search" size="mini" @click="fetchData()">搜索</el-button>
      </el-form-item>
    </el-form>
    <!-- 新增按钮 -->
    <el-row>
      <el-col :span="24" style="text-align: right;margin-bottom: 30px;">
        <el-button type="success" icon="el-icon-plus" size="mini" @click="clickAdd">新增主讲老师</el-button>
      </el-col>
    </el-row>
    <el-table v-loading="tableLoading" stripe :data="tableData" border style="width: 100%;margin-bottom: 30px;">
      <el-table-column prop="id" label="主讲老师编号" align="center" />
      <el-table-column prop="presenterPhone" label="主讲老师手机号" align="center" :show-overflow-tooltip="true" />
      <el-table-column label="操作" align="center" width="260" fixed="right">
        <template slot-scope="scope">
          <el-tooltip content="编辑" placement="top">
            <el-button size="mini" icon="el-icon-edit" type="warning" @click="handleUpdate(scope.row.id)">编辑</el-button>
          </el-tooltip>
          <el-tooltip content="删除" placement="top">
            <el-button size="mini" type="danger" icon="el-icon-delete" @click="handleDelete(scope.row.id)">删除</el-button>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column prop="presenterName" label="主讲老师姓名" align="center" :show-overflow-tooltip="true" />
      <el-table-column prop="avatorPath" label="主讲老师头像" align="center" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <img width="60px" :src="aliUrl + scope.row.avatorPath" alt />
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间" align="center" :show-overflow-tooltip="true" />
    </el-table>
    <!-- 分页 -->
    <el-col :span="20">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>

    <!-- 修改或者新增弹窗 -->
    <el-dialog :title="addOrUpdate ? '新增主讲老师' : '修改主讲老师'" :visible.sync="dialogVisible" width="70%"
      :close-on-click-modal="false" @close="close">
      <el-form :ref="addOrUpdate ? 'addPresenterData' : 'updatePresenterData'" :rules="rules"
        :model="addOrUpdate ? addPresenterData : updatePresenterData" label-position="left" label-width="120px">
        <el-tabs v-model="activeName" type="border-card">
          <el-tab-pane label="主讲老师信息" name="first">
            <el-row>
              <el-form-item v-if="!addOrUpdate" label="主讲老师编号" prop="id">
                <el-input v-model="updatePresenterData.id" disabled />
              </el-form-item>
              <el-col :span="12">
                <el-form-item label="主讲老师名称" prop="presenterName">
                  <el-input v-if="addOrUpdate" v-model="addPresenterData.presenterName" style="width: 200px" />
                  <el-input v-if="!addOrUpdate" v-model="updatePresenterData.presenterName" style="width: 200px" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="主讲老师手机号" prop="presenterPhone">
                  <el-input v-if="addOrUpdate" v-model="addPresenterData.presenterPhone" style="width: 200px" />
                  <el-input v-if="!addOrUpdate" v-model="updatePresenterData.presenterPhone" style="width: 200px" />
                </el-form-item>
              </el-col>
              <div>

                <el-col :span="4">
                  <span style="font-weight: bold;">讲师图片</span>
                </el-col>
                <el-col :span="20">
                  <el-upload ref="clearupload" v-loading="uploadLoading" list-type="picture-card" action
                    element-loading-text="图片上传中" :limit="1" :on-exceed="justPictureNum"
                    :file-list="!addOrUpdate ? fileList : fileList.name" :http-request="uploadPrHttp"
                    :on-preview="handlePictureCardPreview" :on-remove="handleRemoveDetail">
                    <i class="el-icon-plus" />
                  </el-upload>
                </el-col>
              </div>
            </el-row>
          </el-tab-pane>
        </el-tabs>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="warning" @click="close">取消</el-button>
        <el-button v-if="addOrUpdate" size="mini" type="success"
          @click="addPresenterFun('addPresenterData')">新增</el-button>
        <el-button v-if="!addOrUpdate" size="mini" type="primary"
          @click="updatePresenterDataFun('updatePresenterData')">修改</el-button>
      </div>
    </el-dialog>

    <el-dialog :visible.sync="dialogUploadVisible" :close-on-click-modal="false">
      <img width="100%" :src="dialogImageUrl" alt>
    </el-dialog>
  </div>
</template>

<script>

import Tinymce from '@/components/Tinymce'
import { ossPrClient } from '@/api/alibaba'

import presenterListApi from '@/api/dxt/presenterList'


import { pageParamNames } from '@/utils/constants'
import { isvalidPhone, idCard } from "@/utils/validate";
export default {
  components: {
    Tinymce
  },
  data() {
    //手机号验证
    var validPhone = (rule, value, callback) => {
      if (!value) {
        callback(new Error("请输入电话号码"));
      } else if (value.length < 11) {
        callback(new Error("请输入正确的11位手机号码"));
      } else {
        callback();
      }
    };
    //身份证表达式
    var isIdCard = (rule, value, callback) => {
      if (!value) {
        callback(new Error("请输入身份证号码"));
      } else if (!idCard(value)) {
        callback(new Error("请输入正确的18位身份证号"));
      } else {
        callback();
      }
    };
    return {
      pickerOptions: {
        shortcuts: [{
          text: '今天',
          onClick(picker) {
            picker.$emit('pick', new Date());
          }
        }, {
          text: '昨天',
          onClick(picker) {
            const date = new Date();
            date.setTime(date.getTime() - 3600 * 1000 * 24);
            picker.$emit('pick', date);
          }
        }, {
          text: '一周前',
          onClick(picker) {
            const date = new Date();
            date.setTime(date.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', date);
          }
        }]
      },
      time: '',
      fileDetailList01: [],
      tableLoading: false,
      isActiveTrueShow: true,//已上架
      isActiveFalseShow: false,//未上架
      dataQuery: {
        presenterName: '',
        presenterPhone: '',
        id: ''
      },
      activeType: [], // 主讲老师类型
      presenterType: [],
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },

      tableData: [],
      productList: [], // 商品下拉列表数据
      dialogVisible: false, // 修改弹窗是否展示
      addOrUpdate: true, // 是新增还是修改
      activeName: 'first', // tab默认第一个
      addPresenterData: {}, // 新增主讲老师
      updatePresenterData: {}, // 修改数据
      rules: {
        // 表单提交规则
        presenterName: [
          {
            required: true,
            message: '必填',
            trigger: 'blur'
          }
        ],
        presenterPhone: [
          {
            required: true,
            message: '必填',
            trigger: 'change'
          },
          {
            validator: validPhone,
            trigger: "blur",
          },
        ]
      },
      merchantCodeType: [],//市级服务商
      fileListPending: [], // 待处理已上传图片信息
      fileList: [], // 上传图片已有图片列表

      fileDetailListPending: [], // 待处理已上传图片信息
      fileDetailList: [], // 上传图片已有图片列表
      meetingType: [
        { value: 'Admin', label: '总部' },
        { value: 'Agent', label: '市级服务商' }
      ],
      dialogUploadVisible: false,
      dialogImageUrl: '', // 上传图片预览
      uploadLoading01: false,
      uploadLoading: false, // 上传图片加载按钮
      fullscreenLoading: false, // 保存啥的加载

      content: '',
      isUploadSuccess: true, // 是否上传成功
      isShowRelevance: true // 新增或修改是否展示关联产品
    }
  },
  created() {
    ossPrClient()
    this.fetchData()
  },
  methods: {
    // 获取UUid
    getUUid: function () {
      const s = []
      const hexDigits = '0123456789abcdef'
      for (let i = 0; i < 36; i++) {
        s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1)
      }
      s[14] = '4'
      s[19] = hexDigits.substr((s[19] & 0x3) | 0x8, 1)
      s[8] = s[13] = s[18] = s[23] = '-'
      const UUid = s.join('')
      return UUid
    },
    //判断主办方是哪个
    changeAddOrUpdate() {
      if (this.addPresenterData.sponsorType === "Admin") {
        this.merchantCodeType = [
          { value: '100082', label: '总部' }
        ]
      } else {
        this.getAgentList()
      }
    },

    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val
      this.fetchData()
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val
      this.fetchData()
    },

    // 查询
    fetchData() {
      const that = this
      that.tableLoading = true
      presenterListApi
        .presenterList(
          that.tablePage.currentPage,
          that.tablePage.size,
          that.dataQuery
        )
        .then((res) => {
          that.tableData = res.data.data
          that.tableLoading = false
          // 设置后台返回的分页参数
          pageParamNames.forEach((name) =>
            that.$set(that.tablePage, name, parseInt(res.data[name]))
          )
        })
    },

    // 删除主讲老师
    handleDelete(id) {
      this.$confirm('您确定要删除此条主讲老师？', '提示', confirm)
        .then(() => {
          presenterListApi.deletePresenter(id).then(() => {
            this.$message.success('删除主讲老师')
            this.fetchData()
          })
        })
        .catch(() => {
          this.$message.info('已取消操作')
        })
    },
    // 点击新增按钮
    clickAdd() {
      this.addPresenterData = {
        presenterPhone: '',
        presenterName: ''
      }
      this.dialogVisible = true
      this.addOrUpdate = true
      if (this.fileList.length !== 0) {
        this.$refs.clearupload.clearFiles()
      }
      this.activeName = 'first',   //tab默认第一个
        this.$nextTick(() => this.$refs['addPresenterData'].clearValidate())
    },
    // 删除上传图片
    handleRemoveDetail(file, fileList) {
      const that = this
      if (!that.addOrUpdate) {
        that.fileList = fileList
      } else {
        for (let a = 0; a < that.fileDetailList4.length; a++) {
          if (that.fileDetailList4[a].substring(7, 17) == parseInt(file.uid / 1000)) {
            that.fileDetailList4.splice(a, 1)
          }
        }
      }
    },
    // 上传图片预览
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogUploadVisible = true
    },
    // 上传图片数量超限
    justPictureNum(file, fileList) {
      this.$message.warning(`当前限制选择1个文件`)
    },
    // 上传图片请求
    uploadPrHttp({ file }) {
      this.uploadLoading = true
      const that = this
      const fileName = 'manage/' + Date.parse(new Date())
      that.$nextTick(function () {
        ossPrClient()
          .put(fileName, file)
          .then(({ res, url, name }) => {
            if (res && res.status === 200) {
              console.log(`阿里云OSS上传图片成功回调1`, res, url, name)
              if (!that.addOrUpdate) {
                that.fileList.push({ uid: file.uid, url: url })
              } else {
                // 新增上传图片
                that.fileList.push({ name })
                that.addPresenterData.avatorPath = name
              }
              that.$nextTick(() => {
                that.uploadLoading = false
              })
            }
          })
          .catch((err) => {
            that.$message.error('上传图片失败请检查网络或者刷新页面')
            console.log(`阿里云OSS上传图片失败回调`, err)
          })
      })
    },

    // 新增主讲老师提交后台
    addPresenterFun(ele) {
      const that = this
      if (!that.addPresenterData.avatorPath) {
        return that.$message.info("主讲人头像不能为空");
      }
      that.$refs[ele].validate((valid) => {
        // 表单验证
        if (valid) {
          const loading = this.$loading({
            lock: true,
            text: '新增主讲老师',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          })
          presenterListApi
            .addPresenterData(that.addPresenterData)
            .then(() => {
              that.dialogVisible = false
              loading.close()
              that.fetchData()
              that.$message.success('新增主讲老师成功')
            })
            .catch((err) => {
              if (err === 'error') {
                that.$message.error('新增主讲老师失败')
                loading.close()
              }
            })
        } else {
          that.$message.error('请填写必填项')
          console.log('error submit!!')
          loading.close();
          return false
        }
      })
    },
    p(s) {
      return s < 10 ? '0' + s : s
    },
    //
    // 点击编辑按钮
    handleUpdate(id) {
      const that = this
      that.dialogVisible = true
      that.addOrUpdate = false
      presenterListApi.updatePresenterData(id).then((res) => {
        that.updatePresenterData = res.data
        if (
          that.updatePresenterData.avatorPath !== null &&
          that.updatePresenterData.avatorPath.length > 1
        ) {
          that.fileList = [
            {
              url: that.aliUrl + that.updatePresenterData.avatorPath
            }
          ]
        } else {
          that.fileList = []
        }
      })
    },
    // 修改主讲老师提交
    updatePresenterDataFun(ele) {
      const that = this
      that.$refs[ele].validate((valid) => {
        // 表单验证
        if (valid) {
          console.log(that.fileList[0])
          const b = that.fileList[0].url.split('manage/')
          that.updatePresenterData.avatorPath = 'manage/' + b[b.length - 1]
          const loading = this.$loading({
            lock: true,
            text: '修改主讲老师',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          })
          presenterListApi
            .updatePresenterDataList(that.updatePresenterData)
            .then(() => {
              that.dialogVisible = false
              loading.close()
              that.$nextTick(() => that.fetchData())
              that.$message.success('修改主讲老师')
            })
            .catch((err) => {
              if (err === 'error') {
                that.$message.error('修改主讲老师失败')
                loading.close()
              }
            })
        } else {
          console.log('error submit!!')
          loading.close();
          return false
        }
      })
    },

    // base64转blob
    toBlob(urlData, fileType) {
      const bytes = window.atob(urlData)
      let n = bytes.length
      const u8arr = new Uint8Array(n)
      while (n--) {
        u8arr[n] = bytes.charCodeAt(n)
      }
      return new Blob([u8arr], {
        type: fileType
      })
    },

    // 关闭弹窗
    close() {
      this.dialogVisible = false
    }
  }
}
</script>
