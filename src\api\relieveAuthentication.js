import request from "@/utils/request";

export default {
  //   // 课程分页查询
  //   ystUnbindList(params) {
  //     return request({
  //       url: "/znyy/unbind/query/ystUnbindList",
  //       method: "GET",
  //       params,
  //     });
  //   },
  ystUnbindList(params) {
    return request({
      url: "/znyy/unbind/query/ystUnbindList",
      method: "get",
      params,
    });
  },
  approveYstUnbind(params) {
    return request({
      url: "/znyy/unbind/approveYstUnbind",
      method: "post",
      params,
    });
  },
};
