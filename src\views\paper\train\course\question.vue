<template>
  <div class="app-container">

    <el-col :span="24" style="margin-bottom: 30px">
      <el-button type="success" icon="el-icon-plus" size="mini" @click="handleAdd()">新增</el-button>
      <el-button type="warning" icon="el-icon-back" size="mini" @click="goBack()">返回</el-button>
    </el-col>
    <el-table class="common-table" v-loading="tableLoading" :data="tableData" style="width: 100%;margin-bottom: 20px;" row-key="id" border default-expand-all>
      <el-table-column prop="questionDto.id" label="问题ID" />
      <el-table-column label="课程名称">
        {{ courseName }}
      </el-table-column>
      <el-table-column prop="withdrawnBonus" label="操作">
        <template slot-scope="scope">
          <el-button type="primary" size="mini" icon="el-icon-sort" @click="handleUpdate(scope.row.id)">编辑
          </el-button>
          <el-button type="danger" size="mini" icon="el-icon-delete" @click="handleDelete(scope.row.id)">删除
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="关卡名称">
        {{ passName }}
      </el-table-column>
      <el-table-column prop="questionDto.title" label="题干" show-overflow-tooltip />
      <el-table-column prop="questionDto.type" label="所属类型" :formatter="typeFormatter" />
      <el-table-column label="题型" prop="questionDto.questionType" :formatter="questionTypeFormatter" />
      <el-table-column label="是否为三选一题型" prop="isChoose">
        <template slot-scope="scope">
          {{ scope.row.isChoose == 1 ? "是" : "否" }}
        </template>
      </el-table-column>
      <el-table-column label="排序" prop="orderNum" />
    </el-table>
    <!-- 分页 -->
    <el-col :span="24" style="margin-bottom: 20px">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]" layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    </el-col>

    <!--    选择问题弹窗-->
    <el-dialog :visible.sync="questionPage.showDialog" width="70%" @close="close">
      <el-form :model="questionPage.dataQuery" ref="queryForm" :inline="true">
        <el-form-item label="所属类型：">
          <el-select v-model="questionPage.dataQuery.type" placeholder="全部" clearable @change="typeChange">
            <el-option v-for="(item, index) in trainType" :key="index" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="题型：">
          <el-select v-model="questionPage.dataQuery.questionType" placeholder="全部" clearable>
            <el-option v-for="(item, index) in queryQuestionList" :key="index" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-select v-model="questionPage.dataQuery.isStep">
            <el-option label="题目" :value="false" />
            <el-option label="步骤方法" :value="true" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="search2">查询</el-button>
          <el-button @click="searchReset">重置</el-button>
        </el-form-item>
      </el-form>
      <el-table v-loading="questionPage.listLoading" :data="questionPage.tableData" ref="multipleTable" @selection-change="handleSelectionChange" :row-key="getRowKeys" border fit highlight-current-row style="width: 100%">
        <el-table-column type="selection" width="40" :reserve-selection="true"></el-table-column>
        <el-table-column prop="id" label="问题Id" width="200" />
        <el-table-column prop="type" label="所属类型" :formatter="typeFormatter" width="200" />
        <el-table-column prop="questionType" label="题型" :formatter="questionTypeFormatter" width="200" />
        <el-table-column prop="title" label="题干" show-overflow-tooltip>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <el-col :span="24" style="margin-bottom: 20px">
        <el-pagination :current-page="questionPage.tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]" layout="total, sizes, prev, pager, next, jumper" :total="questionPage.tablePage.totalItems" @size-change="questionHandleSizeChange" @current-change="questionHandleCurrentChange" />
      </el-col>
      <span slot="footer" class="dialog-footer">
        <el-button @click="close">取 消</el-button>
        <el-button type="primary" @click="confirmQuestionSelect">确定</el-button>
      </span>
    </el-dialog>

    <!-- 修改或者新增弹窗 -->
    <el-dialog title="编辑排序" :visible.sync="open" width="40%" @close="reset">
      <el-form ref="form" :model="form" label-width="140px" style="width: 100%">
        <el-form-item label="课程名称" required>
          <el-input v-model="courseName" readonly />
        </el-form-item>
        <el-form-item label="关卡名称" required>
          <el-input v-model="passName" readonly />
        </el-form-item>
        <el-form-item label="排序" required>
          <el-input-number v-model="form.orderNum" controls-position="right" :min="1" :step="1" />
        </el-form-item>
        <el-form-item label="是否为三选一题型" required>
          <el-radio-group v-model="form.isChoose">
            <el-radio :label="0">否</el-radio>
            <el-radio :label="1">是</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="open = false">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import courseApi from '@/api/paper/train/course'
import { pageParamNames } from '@/utils/constants'
import { mapGetters, mapState } from 'vuex'
import questionApi from '@/api/paper/train/question'

export default {
  data() {
    return {
      courseName: '',
      passName: '',
      currentQuestions: [],
      queryQuestionList: null,
      parentId: '',
      courseId: '',
      title: '',
      confirmFalse: false,
      dataQuery: {
        parentId: '',
        type: 'QUESTION'
      },
      open: false,
      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      tableData: [],
      form: {},
      questionPage: {
        multipleSelection: [],
        showDialog: false,
        dataQuery: {
          isStep: false,
          type: null,
          questionType: null
        },
        // 分页
        tablePage: {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null
        },
        listLoading: true,
        tableData: [],
        total: 0
      }
    }
  },
  updated() {
    // 在这里调用toggleSelection选中方法
    this.toggleSelection(this.questionPage.tableData)
  },
  created() {
    this.parentId = this.$route.query.id
    this.courseId = this.$route.query.courseId
    if (this.parentId && this.courseId) {
      courseApi.detail(this.parentId).then(res => {
        this.passName = res.data.name
        courseApi.detail(this.courseId).then(re => {
          this.courseName = re.data.name
          this.getList()
        })
      })
    } else {
      this.getList()
    }
  },
  methods: {
    close() {
      this.questionPage.showDialog = false
      this.searchReset()
    },
    searchReset() {
      this.questionPage.dataQuery = {
        isStep: false,
        type: null,
        questionType: null
      }
    },
    toggleSelection(rows) {
      if (rows) {
        rows.forEach(row => {
          this.currentQuestions.forEach(a => {
            if (row.id === a.id) {
              this.$refs.multipleTable.toggleRowSelection(row, true)
            }
          })
        })
      }
    },
    confirmQuestionSelect() {
      if (this.confirmFalse) {
        return
      }
      this.confirmFalse = true
      this.questionPage.multipleSelection.forEach(q => {
        if (this.currentQuestions.findIndex(item => item.id === q.id) === -1) {
          this.reset()
          this.form.questionId = q.id
          courseApi.createOrUpdate(this.form).then(res => {
            this.currentQuestions.push(q)
            this.getList()
          })
        }
      })
      this.questionPage.showDialog = false

    },
    handleSelectionChange(val) {
      this.questionPage.multipleSelection = val
    },
    getRowKeys(row) {
      return row.id;
    },
    search2() {
      this.questionPage.tablePage = {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      }
      this.search()
    },
    search() {
      this.questionPage.listLoading = true
      this.questionPage.dataQuery.pageNum = this.questionPage.tablePage.currentPage
      this.questionPage.dataQuery.pageSize = this.questionPage.tablePage.size
      this.questionPage.dataQuery.categoryType = 1
      questionApi.list(this.questionPage.dataQuery).then(res => {
        this.questionPage.tableData = res.data.data
        this.questionPage.listLoading = false
        // 设置后台返回的分页参数
        pageParamNames.forEach(name => this.$set(this.questionPage.tablePage, name, parseInt(res.data[name])))
      })
      console.log('----', this.currentQuestions)
    },
    typeChange() {
      this.questionPage.dataQuery.questionType = null
      this.queryQuestionList = this.trainQuestionType.filter(
        (data) => data.value.includes(this.questionPage.dataQuery.type)
      )
    },
    typeFormatter(row, column, cellValue, index) {
      return this.enumFormat(this.trainType, cellValue)
    },
    questionTypeFormatter(row, column, cellValue, index) {
      return this.enumFormat(this.trainQuestionType, cellValue)
    },
    goBack() {
      this.$store.dispatch('delVisitedViews', this.$route)
      this.$router.push({ path: '/train/pass', query: { id: this.courseId } })
    },
    handleUpdate(id) {
      this.reset()
      courseApi.detail(id).then(res => {
        this.form = res.data
        this.open = true
      })
    },
    handleDelete(id) {
      this.$confirm('确定要删除吗？', '删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        courseApi.delete(id).then(res => {
          this.$message.success('删除成功！')
          this.tablePage = {
            currentPage: 1,
            size: 10,
            totalPage: null,
            totalItems: null
          }
          this.getList()
        })
      })
    },
    getList() {
      this.currentQuestions = []
      this.loading = true
      this.dataQuery.parentId = this.parentId
      this.dataQuery.pageNum = this.tablePage.currentPage
      this.dataQuery.pageSize = this.tablePage.size
      courseApi.list(this.dataQuery).then(res => {
        this.tableData = res.data.data
        this.tableData.forEach(i => {
          this.currentQuestions.push(i.questionDto)
        })
        this.loading = false
        // 设置后台返回的分页参数
        pageParamNames.forEach(name =>
          this.$set(this.tablePage, name, parseInt(res.data[name]))
        )
      })
    },
    // 表单重置
    reset() {
      this.form = {
        id: '',
        type: 'QUESTION',
        parentId: this.parentId,
        name: '',
        grade: '',
        questionId: '',
        orderNum: 1
      }
    },
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          courseApi.createOrUpdate(this.form).then(response => {
            this.$message.success('提交成功！')
            this.open = false
            this.getList()
          })
        }
      })
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.questionPage.showDialog = true
      this.search()
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val
      this.getList()
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val
      this.getList()
    },
    // 分页
    questionHandleSizeChange(val) {
      this.questionPage.tablePage.size = val
      this.search()
    },
    questionHandleCurrentChange(val) {
      this.questionPage.tablePage.currentPage = val
      this.search()
    }
  },
  computed: {
    ...mapGetters('enumItem', ['enumFormat', 'trainUrlFormat']),
    ...mapState('enumItem', {
      trainType: state => state.train.trainType,
      difficultyList: state => state.train.difficultyList,
      trainQuestionType: state => state.train.trainQuestionType
    })
  }
}
</script>
