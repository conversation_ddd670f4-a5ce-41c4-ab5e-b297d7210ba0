<template>
  <div>
    <el-form ref="form" :model="formData" label-width="150px">
      <el-card shadow="always" :body-style="{ padding: '20px' }">
        <div slot="header">
          <el-form-item label="主交付中心名称">
            <el-input v-model="formData.schoolBingDevlierName" style="width:300px;" disabled></el-input>
          </el-form-item>
          <el-form-item label="小组名称">
            <el-input v-model="formData.schoolBindDevlierGroupName" style="width:300px;" disabled></el-input>
          </el-form-item>
        </div>
        <!-- card body -->
        <el-form-item label="备用交付中心名称">
          <el-input v-model="formData.spareSchoolBindDevlierName" style="width:300px;" disabled></el-input>
        </el-form-item>
        <el-form-item label="小组名称">
          <el-input v-model="formData.spareSchoolBindDevlierGroupName" style="width:300px;" disabled></el-input>
        </el-form-item>
      </el-card>
    </el-form>
  </div>
</template>

<script>
import { getConig } from '@/api/deliverSetting'
export default {
  name: 'deliverSetting',

  data() {
    return {
      formData: {
        schoolBingDevlierName: '-',
        schoolBindDevlierGroupName: '-',
        spareSchoolBindDevlierName: '-',
        spareSchoolBindDevlierGroupName: '-',
      },
    };
  },

  created() {
    this.initData()
  },

  methods: {
    async initData() {
      const res = await getConig()
      console.log(res)
      if (res != 'null') {
        this.formData = res.data
      }
    }
  },
};
</script>