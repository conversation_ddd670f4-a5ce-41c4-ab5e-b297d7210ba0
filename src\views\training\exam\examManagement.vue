<template>
  <div class="app-container">
    <el-form :inline="true" ref="queryForm" class="container-card" label-width="110px">
      <el-row>
        <el-col :span="8">
          <el-form-item label="课程名称：">
            <el-input v-model="form.courseName" placeholder="请输入课程名称" clearable />
          </el-form-item>
        </el-col>
        <el-form-item>
          <el-button icon="el-icon-refresh" size="small" @click="resetQuery()">重置</el-button>
          <el-button icon="el-icon-search" size="small" type="primary" @click="search(1)">搜索</el-button>
        </el-form-item>
      </el-row>
    </el-form>
    <div class="SearchForm">
      <!-- 添加 -->
      <div style="margin-bottom: 10px">
        <el-button size="small" type="primary" icon="el-icon-plus" @click="clickAdd">添加</el-button>
        <el-button size="small" type="primary" icon="el-icon-plus" @click="clickBatchAdd">批量添加</el-button>
      </div>
      <!-- 表格 -->
      <el-table class="common-table" :data="tableData" stripe border :default-sort="{ prop: 'date', order: 'descending' }">
        <el-table-column type="index" width="100" label="序号"></el-table-column>
        <el-table-column prop="id" label="考题编号"></el-table-column>
        <el-table-column prop="questionDescription" label="简介"></el-table-column>
        <el-table-column prop="courseName" label="关联课程"></el-table-column>
        <el-table-column prop="id" label="操作" width="200">
          <template slot-scope="scope">
            <!-- <el-button  type="success"  size="mini"  icon="el-icon-edit-outline"  @click="handleDetail(scope.row.id)"  >查看</el-button > -->
            <el-button type="success" size="mini" icon="el-icon-edit-outline" @click="handleUpdate(scope.row)">编辑</el-button>
            <el-button @click="handleDel(scope.row.id)" type="danger" size="mini" icon="el-icon-view">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 添加弹窗 -->
      <addExamManagement
        ref="addExamManagementRefs"
        :courseList="courseList"
        v-if="dialogFrom.visible"
        :questions="courseType"
        :dialog-param="dialogFrom"
        @closeDialog="closeDialog"
      ></addExamManagement>
      <!-- 批量添加弹窗 -->
      <batchAddManagement :dialogOpen="batchDialogVisible" @closeBatchDialog="closeBatchDialog" @updateTable="updateTable"></batchAddManagement>
    </div>
    <!-- 分页 -->
    <el-col :span="24" style="margin-bottom: 20px">
      <el-pagination
        :current-page="page_data.page"
        :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="page_data.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-col>
  </div>
</template>

<script>
  import addExamManagement from './components/addExamManagement.vue';
  import batchAddManagement from './components/batchAddManagement.vue';
  import examApi from '@/api/training/exam';
  import courseApi from '@/api/training/course';
  export default {
    name: 'examManagement',
    components: {
      addExamManagement,
      batchAddManagement
    },
    data() {
      return {
        tableLoading: false,
        dialogFrom: {
          visible: false,
          type: 'add'
        },
        courseType: [],
        form: {},
        tableData: [],
        courseList: [],
        page_data: {
          pageSize: 10,
          page: 1,
          total: 0
        },
        batchDialogVisible: false
      };
    },
    created() {
      this.getCourse();
      this.search();
      this.getQueryByType();
    },
    methods: {
      // 分页
      handleSizeChange(val) {
        this.page_data.pageSize = val;
        this.search();
      },

      handleCurrentChange(val) {
        this.page_data.page = val;
        this.search();
      },
      getQueryByType() {
        courseApi.dictQueryByType({ dictType: 'paper_question_type' }).then((res) => {
          this.courseType = res.data;
        });
      },
      // 重置
      resetQuery() {
        this.form = {};
        this.page_data.page = 1;
        this.search();
      },
      getCourse() {
        // let param = {pageSize:20,pageNum:1}
        this.courseList = [];
        courseApi.queryCoursesDict().then((res) => {
          this.courseList = res.data;
        });
      },
      // 搜索
      search(page) {
        if (typeof page == 'number' && page > 0) {
          this.page_data.page = page;
        }
        let param = { pageSize: this.page_data.pageSize, pageNum: page || this.page_data.page };
        // questionList
        param = { ...this.form, ...param };
        examApi.questionList(param).then((res) => {
          this.tableData = res.data.data;
          this.page_data.total = Number(res.data.totalItems);
        });
      },

      // 新增
      clickAdd() {
        this.dialogFrom.visible = true;
        this.dialogFrom.type = 'add';
        this.getCourse();
      },
      // // 查看
      // handleDetail(id) {},
      // 编辑
      handleUpdate(info) {
        console.log(info);
        this.dialogFrom.visible = true;
        this.dialogFrom.type = 'edit';
        this.getCourse();
        this.$nextTick(() => {
          this.$refs.addExamManagementRefs.open(info);
        });
      },
      //删除
      handleDel(id) {
        this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            examApi.questionDelete({ id: id }).then((res) => {
              // this.resetQuery()
              if (this.page_data.page > 1 && this.tableData.length == 1) {
                this.page_data.page = this.page_data.page - 1;
              }
              this.search();
            });
          })
          .catch(() => {
            this.$message({
              type: 'info',
              message: '已取消删除'
            });
          });
      },
      closeDialog(type) {
        this.dialogFrom.visible = false;
        console.log(type);

        if (type && type == 'edit') {
          this.search();
        } else {
          this.resetQuery();
        }
      },

      // 批量添加
      clickBatchAdd() {
        this.batchDialogVisible = true;
      },
      // 关闭批量添加弹窗
      closeBatchDialog() {
        this.batchDialogVisible = false;
      },
      updateTable() {
        this.closeBatchDialog();
        this.search(1);
      }
    }
  };
</script>

<style>
  .SearchForm {
    width: 100%;
    margin-bottom: 15px;
    background: #fff;
    border-radius: 6px;
    padding: 5px 0;
    padding-left: 5px;
    box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.2);
  }
  .container-card {
    padding: 15px 15px 15px 15px;
    margin-bottom: 15px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  }

  .period-table td,
  .period-table th {
    text-align: center;
  }

  @media (max-width: 767px) {
    .el-message-box {
      width: 80% !important;
    }
  }

  .el-tooltip__popper {
    max-width: 800px;
  }
</style>
