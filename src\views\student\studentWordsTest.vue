<template>
  <div class="app-container2" >
    <div class="bogbox">
      <div class="buttom clearfix">
        <!-- <a style="background:#f0ad4e; margin-left:20px;" @click="getPdf()">下载</a> -->
        <a style="background:#3dab93" @click="printBtn()">打印</a>
        <a style="background:#f0ad4e; margin-left:20px;" @click="goback()">返回</a>
      </div>
      <section ref="print">
        <div id="print">
          <!-- <div id="pdfDom"> -->
          <div style="text-align:center">
            <img src="../../assets/imgs.png" class="top-img">
          </div>
          <div style="text-align: center; font-size: 15px; margin: 5px 0;color: #676a6c;">测评日期：{{ printData.addTime }}</div>
          <div class="information clearfix">
            <h3>学员信息</h3>
            <div class="clearfix">
              <ul class="stud-information">
                <li>姓名：&nbsp;&nbsp;&nbsp;{{ printData.realName }}</li>
                <li>学校：&nbsp;&nbsp;&nbsp;{{ printData.school }}</li>
                <li>年级：&nbsp;&nbsp;&nbsp;{{ printData.grade }} 年级</li>
                <li>号码：&nbsp;&nbsp;&nbsp;{{ printData.phone }}</li>
                <li>登录账号：&nbsp;&nbsp;&nbsp;{{ printData.loginName }}</li>
              </ul>
              <div class="information-right">
                <p><a style="color: #333 !important;">{{ printData.realName }}</a>同学 你的词汇量约为</p>
                <span>{{ printData.wordLevel }}<a style="color:#f42b2b !important">词</a></span>
                <div>相当于<a>{{ printData.title }}</a></div>
              </div>
            </div>
          </div>
          <div class="information-img clearfix" style="text-align:center;">
            <table style="width:800px;height:360px">
              <tbody>
                <tr>
                  <td style="text-align:center;">
                    <span class="aclass">{{ printData.title }} 总词汇量：{{ printData.wordUpperLimit }} 词</span>
                  </td>
                  <td style="text-align:center;">
                    <span class="aclass">现实掌握词汇：{{ printData.wordLevel }} 词</span>
                  </td>
                </tr>
                <tr>
                  <td style="text-align:center">
                    <div class="name">
                      <img src="../../assets/pic.png" class="pic-img">
                    </div>
                  </td>
                  <td style="text-align:center;">
                      <!-- 柱状图 开始 -->
                      <div class="chart-wrapper" style="width: 100%;height: 200px;">
                        <bar-chart :chart-data="lineChartData" width="100%" height="200px" />
                      </div>
                      <!-- 柱状图 结束 -->
                  </td>
                </tr>
                <tr>
                  <td>
                    <div style="text-align:center; margin-top:-42px; font-size:16px;color: #676a6c;">各年级英语水平划分表</div>
                  </td>
                  <td>
                    <p class="title01" style="text-align:center; margin-top:-42px; font-size:16px;color: #676a6c;">测评结果柱状对比图</p>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
          <div class="information clearfix" style="margin-top: 15px !important;padding-bottom: 10px;">
            <h3>测评结果</h3>
            <p class="f16">{{ printData.levelDescription }}</p>
            <h3>测评分析</h3>
            <p class="f16">
              {{ printData.evaluationAnalysis }}
            </p>
            <h3>学习建议</h3>
            <p class="f16">{{ printData.learningAdvice }}</p>
          </div>
        </div>
        <!-- </div> -->
      </section>

    </div>
  </div>
</template>

<script>
  import printApi from '@/api/studentWordsTest'
  import BarChart from '@/views/dashboard/admin/components/BarChart'
  import ls from '@/api/sessionStorage'
  export default {
  components: {
    BarChart,
  },
  data() {
    return {
      id:'',
      htmlTitle:'学员词汇量检测',
      lineChartData : {
          expectedData: [],
          actualData: [],
          xAxisData:[]
      },
      // 打印数据
      printData:[],
    }
  },
  created() {
    this.id = ls.getItem('testId');
    this.fetchData(this.id)
  },
  onLoad(){
  },
  methods: {
    // 返回
    goback(){
      this.$store.dispatch('delVisitedViews', this.$route);
        this.$router.go(-1);//返回上一层
      // 删除本地存储
      // localStorage.removeItem('testId')
    },
    // 显示学员测试结果
    fetchData(id) {
      const that = this
      printApi.studentList(id).then(res => {
        that.printData = res.data
        that.lineChartData.xAxisData = [res.data.title,'现实掌握词汇量']
        that.lineChartData.expectedData = [res.data.wordUpperLimit,res.data.wordLevel]
        // that.lineChartData.actualData = [res.data.wordLevel]
      })
    },
    // 打印
    printBtn() {
      // window.print();
      this.$print(this.$refs.print)
    },
  }
}
</script>

<style scoped>
  .app-container2{
    padding: 0 0 20px;
    background-color: #f3f3f4;
    color: #676a6c;
  }
  .buttom {
    width: 240px;
    color: #fff;
    cursor: pointer;
    position: absolute;
    right: 55px;
    top: 250px;
  }

  .buttom a {
    display: block;
    float: left;
    color: #fff;
    width: 90px;
    height: 35px;
    border-radius: 5px;
    text-align: center;
    line-height: 35px;
  }

  .top-img {
    width: 100%;
    height: 275px;
    /*background: url(/Content/css/images/imgs.png) no-repeat center;*/
    background-size: cover;
  }

  #page-wrapper {
    background: #fff
  }

  .titli-img {
    display: block;
    margin: 0 auto;
    margin-top: 20px;
    width: 450px;
  }

  .img {
    display: block;
    margin: 0 auto;
    margin-top: 30px;
  }

  .pic-img {
    display: block;
    width: 260px;
    /*margin-left: 50px;*/
  }

  .information-img {
    width: 800px;
    height: auto;
    margin: 0 auto;
    margin-top: 15px;
    background: #fff;
    border-radius: 20px;
  }

  .name {
    width: 260px;
    text-align: center;
    float: left;
    margin-left: 60px;
    font-size: 15px;
    margin-top: -60px;
  }

  .box {
    position: relative;
    border-top: none !important;
    box-shadow: 0 0px 0px rgba(0, 0, 0, 0.1) !important;
    /*width: 380px;
        height: 300px;*/
    float: right;
    /*margin-top: 30px;*/
    /*margin-left: 30px;*/
    /*padding-top: 30px;*/
    /*position:relative;*/
    padding-left: 40px !important;
  }

  .f16 {
    font-size: 16px;
  }

  .stud-information {
    padding-left: 30px !important
  }

  .information-right {
    width: 470px !important
  }
  .clearfix { zoom: 1; }
  .clearfix::before, .clearfix::after { content: ""; line-height: 0; display: table; }
  .clearfix::after { clear: both; }
  .information {
    width: 800px;
    height: auto;
    /*padding-bottom:40px;*/
    margin: 0 auto;
    background: #fff;
    border-radius: 20px;
    /*-webkit-box-shadow: 0 0 10px rgba(234, 234, 234, .5);
    -moz-box-shadow: 0 0 10px rgba(234, 234, 234, .5);
    box-shadow: 0 0 10px rgba(234, 234, 234, .5);*/
  }

  .information h3 {
    margin-left: 20px;
    margin-bottom: 0;
    font-size: 18px;
    font-weight: 550 !important;
    border-left: 4px solid #19bc99;
    padding-left: 5px;
  }

  .stud-information li {
    width: 300px;
    height: 45px;
    line-height: 45px;
    border-bottom: 1px solid #e5e5e5;
  }
  .stud-information li:last-child{
    border-bottom: none;
  }

  .stud-information {
    float: left;
    margin: 0;
    font-size: 16px;
    padding-left: 20px;
    list-style: none;

      /*font-weight: 550;*/
  }

  .information-right {
    float: left;
    width: 480px;
    text-align: center;
    padding-top: 30px;
  }

  .information-right p {
    font-size: 18px;
    margin-bottom: 15px;
    font-weight: 600;
  }

  .information-right span {
    font-size: 40px;
    font-weight: 600;
    color: #f42b2b !important;
  }

  .information-right span a {
    font-size: 16px !important;
  }

  .information-right div {
    font-size: 16px;
    margin-top: 10px;
  }

  .information-right a {
    color: #f42b2b !important;
  }

  .mt-20 {
    margin-top: 20px !important;
  }

  .mt-40 {
    margin-top: 40px !important;
  }

  .mt-80 {
    margin-top: 80px !important;
  }

  .information h3 a {
    color: #f42b2b !important;
    font-size: 20px;
    font-weight: 600;
  }

  .tubiao {
    display: block;
    margin: 0 auto;
    width: 700px;
  }

  .title {
    text-align: center;
  }

  .title01 {
    /*text-align: center;
    font-size: 15px;
    margin-top:-42px;
   position: absolute;
    bottom: 25px;
    left: 85px;*/
  }

  .result {
    width: 800px;
    margin: 0 auto;
    margin-top: 40px;
  }

  .result div {
    width: 115px;
    height: 40px;
    color: #fff;
    text-align: center;
    line-height: 40px !important;
    border-radius: 50px;
  }

  .result div h2 {
    font-size: 20px !important
  }

  .result p {
    margin-top: 20px;
    margin-left: 30px;
  }

  .aclass {
    font-size: 18px;
    color: #3dab93 !important;
    font-weight: bold;
    display: block;
  }
  p {
    line-height: 1.5;
    margin: 0px 40px;
  }
  @media print {
    body {
      -webkit-print-color-adjust: exact;
    }
  }
</style>
