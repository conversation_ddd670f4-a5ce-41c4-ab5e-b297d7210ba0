import request from '@/utils/request'

export default {
  // 分页查询
  list(data) {
    return request({
      url: '/xi/web/banner/list',
      method: 'GET',
      params: data
    })
  },
  detail(id){
    return request({
      url: '/xi/web/banner/details',
      method: 'GET',
      params:{
        id:id
      }
    })
  },
// 段位要求
  rankList(data) {
    return request({
      url: '/xi/web/grading/list',
      method: 'GET',
      params: data
    })
  },
//新增
  saveOrUpdate(data) {
    return request({
      url: '/xi/web/banner/saveOrUpdate',
      method: 'POST',
      data
    })
  },
  //删除
  delete(id){
    return request({
      url: '/xi/web/banner',
      method: 'DELETE',
      params:{
        id:id
      }
    })
  }
}
