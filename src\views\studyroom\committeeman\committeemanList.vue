<template>
  <div class="app-container">
    <el-form :inline="true" ref="queryForm" class="container-card" label-width="110px">
      <el-form-item label="学委名称：" prop="name">
        <el-input v-model="dataQuery.name" placeholder="请输入学委名称" clearable />
      </el-form-item>
      <el-form-item label="性别：" prop="gender">
        <el-select v-model="dataQuery.gender" placeholder="全部" clearable>
          <el-option v-for="(item, index) in [{ label: '男', value: '1' }, { label: '女', value: '0' }]" :key="index"
            :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="地区：" prop="address" v-if="addressBtn">
        <el-cascader :options="addressOptions" v-model="address" filterable></el-cascader>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handlePage()">搜索</el-button>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-re" @click="resetQuery()">重置</el-button>
      </el-form-item>
    </el-form>
    <el-col :span="24" style="margin-bottom: 30px">
      <el-button type="success" icon="el-icon-plus" size="mini" @click="handleAdd()">新增</el-button>
    </el-col>
    <el-table class="common-table" v-loading="tableLoading" :data="tableData" style="width: 100%;margin-bottom: 20px;"
      row-key="id" border default-expand-all>
      <el-table-column prop="id" label="学委ID"></el-table-column>
      <el-table-column prop="name" label="姓名"></el-table-column>
      <el-table-column label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="mini" @click="handleView(scope.row)">查看详情</el-button>
          <el-button type="text" size="mini" @click="handleAppoint(scope.row)">指派自学室</el-button>
          <!-- <el-button type="text" size="mini" @click="handleSuspend(scope.row)" v-if="scope.row.suspend">恢复</el-button>
          <el-button type="text" size="mini" @click="handleSuspend(scope.row)" v-if="!scope.row.suspend"
                     style="color: red">停课
          </el-button> -->
        </template>
      </el-table-column>
      <el-table-column prop="phone" label="手机号"></el-table-column>
      <el-table-column prop="gender" label="性别">
        <template slot-scope="scope">
          <span v-if="scope.row.gender === '1'">男</span>
          <span v-else>女</span>
        </template>
      </el-table-column>
      <el-table-column prop="address" label="地区" v-if="addressBtn">
        <template slot-scope="scope">
          {{ scope.row.province }}-{{ scope.row.city }}
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间"></el-table-column>
      <el-table-column prop="classNum" label="班级数量"></el-table-column>
      <el-table-column prop="status" label="状态">
        <template slot-scope="scope">
          <span v-if="scope.row.status === 1">督学中</span>
          <span v-else>空闲中</span>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <el-col :span="24" style="margin-bottom: 20px">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>
    <committeeman-add ref="add" @ok="getPageList"></committeeman-add>
    <committeeman-view ref="view"></committeeman-view>
    <room-list ref="room" :committeemanId="committeemanId" @ok="getPageList"></room-list>
  </div>
</template>
<script>
import { pageParamNames } from "@/utils/constants";
import committeemanApi from "@/api/studyroom/committeeman";
import CommitteemanAdd from "./committeemanAdd";
import CommitteemanView from "./committeemanView";
import RoomList from "./roomList";

export default {
  name: 'studentList',
  components: { RoomList, CommitteemanView, CommitteemanAdd },
  data() {
    return {
      addressOptions: [],
      address: false,
      addressBtn: false,
      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      tableData: [],
      dataQuery: {
        name: '',
        gender: ''
      },
      committeemanId: null,
    };
  },
  created() {
    this.getPageList();
    this.btnShow();
    committeemanApi.getAllRegion().then(res => {
      let temp = [];
      res.data.map(i => {
        temp.push({
          label: i.name,
          value: i.name,
          id: i.id,
          children: i.children.map((item) => {
            return {
              label: item.name,
              value: item.name,
              id: item.id,
              leaf: true
            }
          })
        })
      });
      this.addressOptions = temp;
    });
  },
  methods: {
    btnShow() {
      this.$store.getters.roles.forEach(element => {
        if (element.val === 'admin') {
          this.addressBtn = true;
        } else {
          this.addressBtn = false;
        }

      });
    },
    handleSuspend(row) {
      console.log(row)
      let msg = row.suspend ? '恢复' : '停课';
      let suspend = row.suspend ? 0 : 1;
      this.$confirm('确定要' + msg + '吗?', '修改状态', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        committeemanApi.committeemanSuspend(row.id, suspend).then(res => {
          this.$nextTick(() => this.getPageList())
          this.$message.success('修改成功!')
        }).catch(err => {

        })
      }).catch(err => {

      })
    },
    /** 指派按钮*/
    handleAppoint(row) {
      this.committeemanId = row.id;
      this.$refs.room.show();
    },
    /** 详情按钮操作 */
    handleView(row) {
      this.$refs.view.getDetail(row.id);
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.$refs.add.show();
    },
    getPageList() {
      this.tableLoading = true;
      if (this.address) {
        this.dataQuery.province = this.address[0];
        this.dataQuery.city = this.address[1];
      }
      committeemanApi.committeemanList(this.tablePage.currentPage, this.tablePage.size, this.dataQuery).then(res => {
        this.tableData = res.data.data;
        this.tableLoading = false;
        // 设置后台返回的分页参数
        pageParamNames.forEach(name => this.$set(this.tablePage, name, parseInt(res.data[name])))
      })
    },
    handlePage() {
      this.tablePage = {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
        this.getPageList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dataQuery = {
        name: '',
        gender: '',
        province: '',
        city: ''
      };
      this.address = [];
      this.getPageList();
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val;
      this.getPageList();
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val;
      this.getPageList();
    },
  }
};
</script>

<style>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.period-table td,
.period-table th {
  text-align: center;
}

@media (max-width: 767px) {
  .el-message-box {
    width: 80% !important;
  }
}
</style>
