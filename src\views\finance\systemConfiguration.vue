<template>
  <div class="main" element-loading-spinner="el-icon-loading" element-loading-text="加载中..." v-loading="loading">
    <div class="border-card">
      <span class="p-d" @click="allocation(2)" :style="{ color: allocationIndex == 2 ? '#4095e5' : '' }">自有课时</span>
      <span class="p-d" @click="allocation(1)" :style="{ color: allocationIndex == 1 ? '#4095e5' : '' }">交付课时</span>
    </div>
    <div class="m-t s-font">
      <div v-if="allocationIndex == 1">
        <div class="f-s">
          <img src="../../assets/gant.png" alt="" class="gant-img" />
          以下为渠道后台和鼎校甄选通用分润配置，按照课程大类配置后，即可按照各角色的配比进行分润
        </div>
      </div>
      <div v-if="allocationIndex == 2">
        <div class="f-s">
          <img src="../../assets/gant.png" alt="" class="gant-img" />
          以下配置分润金额为:课程自有课时价格-成本价=可分润金额
        </div>
        <div class="f-s" style="margin-top: 5px">
          <img src="../../assets/gant.png" alt="" class="gant-img" />
          以下为渠道后台和鼎校甄选通用分润配置，按照课程大类配置后，即可按照各角色的配比进行分润
        </div>
        <div class="f-s" style="margin-top: 5px">
          <img src="../../assets/gant.png" alt="" class="gant-img" />
          请注意:鼎英语仅10.5元按照以下配比分配，剩余自有课时金额自动全部分配给合伙人;其他课程大类按全额自有课时费用按照以下配比分配
        </div>
      </div>
    </div>
    <div class="m-t">
      <el-table :data="tableData" border style="width: 100%" :header-cell-style="{ 'text-align': 'center' }" v-if="allocationIndex == 2">
        <el-table-column align="center" prop="curriculumName" label="课程大类" width="180"></el-table-column>
        <el-table-column align="center" prop="clubCooperateRatio" label="合作伙伴俱乐部-渠道合作伙伴费" width="200">
          <template slot-scope="scope">
            <div>
              <el-input v-model="scope.row.clubCooperateRatio" style="width: 70%" :disabled="!isEditing(scope.row.id)">
                <template slot="append">%</template>
              </el-input>
            </div>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="clubChannelRatio" label="俱乐部-渠道管理费" width="180">
          <template slot-scope="scope">
            <div>
              <el-input v-model="scope.row.clubChannelRatio" style="width: 70%" :disabled="!isEditing(scope.row.id)">
                <template slot="append">%</template>
              </el-input>
            </div>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="clubCourseRatio" label="俱乐部课时差价" width="180">
          <template slot-scope="scope">
            <div>
              <el-input v-model="scope.row.clubCourseRatio" style="width: 70%" :disabled="!isEditing(scope.row.id)">
                <template slot="append">%</template>
              </el-input>
            </div>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="partnerStudentRatio" label="合伙人-学生管理费" width="180">
          <template slot-scope="scope">
            <div>
              <el-input v-model="scope.row.partnerStudentRatio" style="width: 70%" :disabled="!isEditing(scope.row.id)">
                <template slot="append">%</template>
              </el-input>
            </div>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="partnerRecommendRatio" label="合伙人-推荐费" width="180">
          <template slot-scope="scope">
            <div>
              <el-input v-model="scope.row.partnerRecommendRatio" style="width: 70%" :disabled="!isEditing(scope.row.id)">
                <template slot="append">%</template>
              </el-input>
            </div>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="partnerCourseRatio" label="课程推广大使-课时费" width="180">
          <template slot-scope="scope">
            <div>
              <el-input v-model="scope.row.partnerCourseRatio" style="width: 70%" :disabled="!isEditing(scope.row.id)">
                <template slot="append">%</template>
              </el-input>
            </div>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="headquartersRatio" label="总部-课时服务费" width="180">
          <template slot-scope="scope">
            <div>
              <el-input v-model="scope.row.headquartersRatio" style="width: 70%" :disabled="!isEditing(scope.row.id)">
                <template slot="append">%</template>
              </el-input>
            </div>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="salesIncentivesRatio" label="营销费用" width="180">
          <template slot-scope="scope">
            <div>
              <el-input v-model="scope.row.salesIncentivesRatio" style="width: 70%" :disabled="!isEditing(scope.row.id)">
                <template slot="append">%</template>
              </el-input>
            </div>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="memberCommissionRatio" label="家长会员-鼎币" width="200">
          <template slot-scope="scope">
            <div>
              <el-input v-model="scope.row.memberCommissionRatio" style="width: 70%" :disabled="!isEditing(scope.row.id)">
                <template slot="append">%</template>
              </el-input>
            </div>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="costRatio" label="产品+平台" width="180">
          <template slot-scope="scope">
            <div>
              <el-input v-model="scope.row.costRatio" style="width: 70%" :disabled="!isEditing(scope.row.id)">
                <template slot="append">%</template>
              </el-input>
            </div>
          </template>
        </el-table-column>
        <el-table-column align="center" label="操作" width="220" fixed="right">
          <template slot-scope="scope">
            <div class="f-c">
              <el-button size="mini" type="primary" @click="updateCourse(scope.row.id)" v-if="!isEditing(scope.row.id)">编辑</el-button>
              <el-button size="mini" type="primary" @click="submitCourse(scope.row)" v-if="isEditing(scope.row.id)">完成</el-button>
              <el-button size="mini" type="primary" @click="endCourse" v-if="isEditing(scope.row.id)">取消</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <el-table :data="tableData" border style="width: 100%" :header-cell-style="{ 'text-align': 'center' }" v-else>
        <el-table-column align="center" prop="curriculumName" label="课程大类" width="300"></el-table-column>
        <el-table-column align="center" prop="coursePrice" label="课时价" width="200">
          <template slot-scope="scope">
            <div>
              <el-input v-model="scope.row.coursePrice" style="width: 70%" :disabled="!isEditing(scope.row.id)">
                <template slot="append">元</template>
              </el-input>
            </div>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="servicePrice" label="甄选-技术服务费价" width="300">
          <template slot-scope="scope">
            <div>
              <el-input v-model="scope.row.servicePrice" style="width: 70%" :disabled="!isEditing(scope.row.id)">
                <template slot="append">元</template>
              </el-input>
            </div>
          </template>
        </el-table-column>

        <el-table-column align="center" prop="deliverStudentRatio" label="交付中心-学员服务费" width="300">
          <template slot-scope="scope">
            <div>
              <el-input v-model="scope.row.deliverStudentRatio" style="width: 70%" :disabled="!isEditing(scope.row.id)">
                <template slot="append">%</template>
              </el-input>
            </div>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="deliverCoachRatio" label="交付中心-教练管理费" width="300">
          <template slot-scope="scope">
            <div>
              <el-input v-model="scope.row.deliverCoachRatio" style="width: 70%" :disabled="!isEditing(scope.row.id)">
                <template slot="append">%</template>
              </el-input>
            </div>
          </template>
        </el-table-column>
        <el-table-column align="center" label="操作" fixed="right">
          <template slot-scope="scope">
            <div class="f-c">
              <el-button size="mini" type="primary" @click="updateCourse(scope.row.id)" v-if="!isEditing(scope.row.id)">编辑</el-button>
              <el-button size="mini" type="primary" @click="submitCourse(scope.row)" v-if="isEditing(scope.row.id)">完成</el-button>
              <el-button size="mini" type="primary" @click="endCourse" v-if="isEditing(scope.row.id)">取消</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <Page :total="page_data.total" :page.sync="page_data.pageNum" :limit.sync="page_data.pageSize" @pagination="index" />
    </div>
  </div>
</template>
<script>
  import { getProfitConfigPage, profitConfigEdit, profitConfigDelete, goodsList, goodsCategoryList, addCourseConfig } from '@/api/FinanceApi/systemApi';
  import Page from '@/components/Pages/pages.vue';
  export default {
    components: {
      Page
    },
    data() {
      return {
        loading: false,
        allocationIndex: 2, //1 交付 2 自由
        tableData: [],
        page_data: {
          total: 0,
          pageNum: 1,
          pageSize: 10
        },
        editingRowId: null
      };
    },
    mounted() {
      this.index();
    },
    methods: {
      index() {
        this.loading = true;

        let data = {
          type: this.allocationIndex,
          pageSize: this.page_data.pageSize,
          pageNum: this.page_data.pageNum
          // coursePrice: (this.allocationIndex == 1 && this.courseId == 1) ? this.coursePrice : null
        };

        getProfitConfigPage(data).then((res) => {
          if (res.success) {
            this.tableData = res.data.data;
            console.log(res.data.totalItems, '0000000000000');

            this.page_data.total = res.data.totalItems * 1;
            this.loading = false;
          } else {
            this.$message.error(res.message);
            this.loading = false;
          }
        });
      },

      allocation(id) {
        let that = this;
        that.allocationIndex = id;
        that.page_data = {
          total: 0,
          pageNum: 1,
          pageSize: 10
        };
        that.index();
      },

      edit() {
        let that = this;
        that.isPeriod = true;
      },
      edit1(id) {
        this.editingRowId = id;
      },
      submitPeriod(value) {
        let that = this;
        let data = value.isTrusted ? that.list[0] : value;
        profitConfigEdit(data).then((res) => {
          console.log(res);
          if (res.code == 20000) {
            that.isPeriod = false;
            that.$message.success(res.message);
            that.editingRowId = null;
            that.index();
            if (value.id) {
              console.log(value.id);
            }
          } else {
            that.$message.error(res.message);
          }
        });
      },
      endPeriod() {
        let that = this;
        that.editingRowId = null;
      },
      isEditing(id) {
        return this.editingRowId === id;
      },
      updateCourse(id) {
        this.editingRowId = id;
      },
      deleteCourse(id) {
        let that = this;
        let data = {
          id: id
        };
        profitConfigDelete(data).then((res) => {
          console.log(res);
          if (res.code == 20000) {
            that.$message.success(res.message);
            this.index();
          } else {
            that.$message.error(res.message);
          }
        });
      },
      submitCourse(list) {
        this.submitPeriod(list);
      },
      endCourse(id) {
        let that = this;
        that.editingRowId = null;
        this.index();
      },
      HandleValue(value) {
        this.courseId = value;
        this.isPeriod = false;
        if (value == 1) {
          this.courseShowPrice = '90元';
          this.coursePrice = 90;
        } else {
          this.courseShowPrice = null;
          this.coursePrice = null;
        }
        this.index();
      },
      HandleValue2(value) {
        this.isPeriod = false;
        switch (value) {
          case 0:
            this.coursePrice = 90;
            this.courseShowPrice = '90元';
            break;
          case 1:
            this.coursePrice = 80;
            this.courseShowPrice = '80元';
            break;
          case 2:
            this.coursePrice = 70;
            this.courseShowPrice = '70元';
            break;
          case 3:
            this.coursePrice = 65;
            this.courseShowPrice = '65元';
            break;
        }
        this.index();
      },

      showinfo() {
        this.dialogVisible = true;
        this.loading = false;
        let param = { pageSize: this.coursepage_data.pageSize, pageNum: this.coursepage_data.page };
        Object.assign(param, this.searchCourseForm);
        // Object.assign(param, param2Obj(this.$route.fullPath));
        this.loading = true;
        goodsList(param).then((res) => {
          if (res.code === 20000) {
            this.courseList = res.data.data ? res.data.data : [];
            this.coursepage_data.total = res.data.data ? res.data.totalItems * 1 : 0;
            this.loading = false;
          } else {
            this.$message.error(res.message);
            this.loading = false;
          }
        });
        goodsCategoryList({ pageNum: 1, pageSize: 100 }).then((res) => {
          this.courseOptions = res.data.data;
        });
      },
      courseSubmit() {
        let that = this;
        that.loading = true;
        let param = { pageSize: this.page_data.pageSize, pageNum: this.page_data.page };
        Object.assign(param, this.searchCourseForm);
        // Object.assign(param, param2Obj(this.$route.fullPath));
        // this.loading = true;
        goodsList(param).then((res) => {
          if (res.code === 20000) {
            that.courseList = res.data.data ? res.data.data : [];
            that.coursepage_data.total = res.data.data ? res.data.totalItems : 0;
            that.loading = false;
          } else {
            this.$message.error(res.message);
            this.loading = fals;
          }
        });
      },
      courseReset() {
        this.searchCourseForm = {
          goodsName: '',
          courseCateName: ''
        };
        this.courseSubmit();
      },
      addCourseAll() {
        let addcourseParma = [];

        this.addcourse.forEach((item) => {
          addcourseParma.push({
            brandCommissionRatio: '',
            clubCommissionRatio: '',
            costPrice: item.goodsOriginalPrice,
            courseCode: item.id,
            courseName: item.goodsName,
            memberCommissionRatio: '',
            partnerCommissionRatio: ''
          });
        });

        addCourseConfig(addcourseParma).then((res) => {
          if (res.code === 20000) {
            this.$message({
              type: 'success',
              message: '添加成功!'
            });
            this.index();
          } else {
            this.$message({
              type: 'error',
              message: '添加失败: ' + res.message
            });
          }
        });
      },
      handleSelectionChange(selection) {
        this.addcourse = selection;
      }
    }
  };
</script>
<style lang="scss">
  .main {
    padding: 20px;
  }
  .f-c {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .border-card {
    width: 100%;
    height: 40px;
    line-height: 40px;
    background-color: #cecece;
  }
  .s-font {
    font-size: 12px;
  }
  .m-t {
    margin-top: 20px;
  }

  .m-r {
    margin-right: 10px;
  }

  .p-d {
    padding-left: 20px;
    cursor: pointer;
  }

  .font-color {
    color: #4095e5;
  }
  .f-s {
    font-size: 16px;
    display: flex;
  }
  .gant-img {
    margin-right: 10px;
    width: 20px;
    height: 20px;
  }
</style>
