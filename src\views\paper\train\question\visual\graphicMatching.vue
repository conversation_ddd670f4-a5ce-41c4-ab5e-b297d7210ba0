<!--图形匹配题-->
<template>
  <div class="app-container">
    <el-form :model="form" ref="form" label-width="100px" v-loading="formLoading" :rules="rules">
      <el-form-item label="所属类型:" prop="type">
        <el-select v-model="form.type" placeholder="全部" disabled>
          <el-option v-for="(item,index) in trainType" :key="index" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="题型:" prop="questionType">
        <el-select v-model="form.questionType" placeholder="全部" disabled>
          <el-option v-for="(item,index) in trainQuestionType" :key="index" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="选择难度：" prop="difficulty">
        <el-select v-model="form.difficulty" placeholder="全部" clearable>
          <el-option v-for="(item,index) in difficultyList" :key="index" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="选择维度：" prop="categoryId">
        <el-select v-model="form.categoryId" placeholder="全部" clearable>
          <el-option v-for="(item,index) in categoryList" :key="index" :label="item.name" :value="item.id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item v-if="form.courseType!=1" label="选择类别：" prop="categoryType">
        <el-select v-model="form.categoryType" :disabled="questionlnCourseFalse&&form.categoryType==1" placeholder="全部" clearable>
          <el-option v-for="(item, index) in typeList" :key="index" :label="item.label" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="题干：" prop="title">
        <el-input placeholder="请输入" v-model="form.title" />
      </el-form-item>

      <el-form-item label="主图" required prop="customInfo">
        <upload-with-number @handleSuccess="handleSuccess" @handleRemove="handleRemove" :fullUrl="true" :file-list="mainFileList" :limit="8" @handleInputNumber="handleInputNumber" />
      </el-form-item>
      <el-form-item label="展示图" required prop="answer">
        <upload-with-number @handleSuccess="handleShowPicSuccess" @handleRemove="handleShowPicRemove" :fullUrl="true" :file-list="showFileList" :limit="999" @handleInputNumber="handleShowPicInputNumber" />
      </el-form-item>
      <el-form-item label="题数：" prop="score">
        <el-input-number v-model="form.score" :precision="0" :step="1" :min="1" :max="999"></el-input-number>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="submitForm">提交</el-button>
        <el-button v-show="form.id===null" @click="resetForm">重置</el-button>
      </el-form-item>
    </el-form>
    <el-dialog :visible.sync="imagePage.showDialog" width="70%">
      <el-form :model="imagePage.dataQuery" ref="queryForm" :inline="true">
        <el-form-item label="名称：">
          <el-input v-model="imagePage.dataQuery.name" clearable></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="search">查询</el-button>
        </el-form-item>
      </el-form>
      <el-table v-loading="imagePage.listLoading" :data="imagePage.tableData" ref="multipleTable" @current-change="handleSelectionChange" :row-class-name="tableRowClassName" border fit highlight-current-row style="width: 100%">
        <el-table-column prop="questionType" label="题型" :formatter="questionTypeFormatter"></el-table-column>
        <el-table-column prop="difficulty" label="难度" :formatter="difficultyFormatter"></el-table-column>
        <el-table-column prop="name" label="名称"></el-table-column>
        <el-table-column prop="image" label="图片" align="center">
          <template slot-scope="scope">
            <el-image v-for="(item, index) in scope.row.fileList" :src="item" :key="index" :preview-src-list="[item]" class="img-lg" />
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <el-col :span="24" style="margin-bottom: 20px">
        <el-pagination :current-page="imagePage.tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]" layout="total, sizes, prev, pager, next, jumper" :total="imagePage.tablePage.totalItems" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
      </el-col>
      <span slot="footer" class="dialog-footer">
        <el-button @click="imagePage.showDialog = false">取 消</el-button>
        <el-button type="primary" @click="confirmImageSelect">确定</el-button>
      </span>
    </el-dialog>
    <el-dialog :visible.sync="richEditor.dialogVisible" append-to-body :close-on-click-modal="false" style="width: 100%;height: 100%" :show-close="false" center>
      <Ueditor @ready="editorReady" />
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="editorConfirm">确 定</el-button>
        <el-button @click="richEditor.dialogVisible = false">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import questionApi from '@/api/paper/train/question'
import categoryApi from '@/api/paper/train/category'
import { mapGetters, mapState } from 'vuex'
import FeatureGraphicsUpload from '@/components/Upload/FeatureGraphicsUpload'
import { pageParamNames } from '@/utils/constants'
import imageApi from '@/api/paper/train/image'
import Ueditor from '@/components/Ueditor'
import UploadWithNumber from '@/components/Upload/UploadWithNumber.vue'
import fa from 'element-ui/src/locale/lang/fa'

export default {
  components: {
    FeatureGraphicsUpload,
    UploadWithNumber,
    Ueditor,
  },
  data() {
    var validateCustomInfo = (rule, value, callback) => {
      if (value.length == 0) {
        return callback(new Error('请上传主图！'))
      }
      callback()
    }
    var validateAnswer = (rule, value, callback) => {
      if (value.length == 0) {
        return callback(new Error('请上传展示图！'))
      }
      callback()
    }
    return {
      richEditor: {
        dialogVisible: false,
        object: null,
        parameterName: '',
        instance: null
      },
      imagePage: {
        showDialog: false,
        listLoading: false,
        tableData: [],
        selectData: null,
        dataQuery: {
          questionType: null,
          difficulty: null
        },
        // 分页
        tablePage: {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null
        }
      },
      categoryList: [],
      typeList: [
        { label: '正式题', id: 1 },
        { label: '附加题', id: 2 }
      ],
      mainFileList: [],
      showFileList: [],
      fileList: [],
      answerRadio: [],
      importFrom: {
        file: null
      },
      randomLength: undefined,
      form: {
        id: null,
        title: '',
        categoryId: '',
        categoryType: '',
        difficulty: '',
        difficultyId: '',
        type: 'VISUAL_',
        questionType: 'VISUAL_GRAPHIC_MATCHING',
        customInfo: [],
        answer: [],
        isRandom: false,
        analysis: '',
        score: undefined,
        badge: 3,
        courseType: 0,
      },
      formLoading: false,
      rules: {
        type: [
          { required: true, message: '请选择', trigger: 'blur' }
        ],
        questionType: [
          { required: true, message: '请选择', trigger: 'blur' }
        ],
        title: [
          { required: true, message: '请输入', trigger: 'blur' }
        ],
        difficulty: [
          { required: true, message: '请选择', trigger: 'blur' }
        ],
        categoryId: [
          { required: true, message: '请选择', trigger: 'blur' }
        ],
        categoryType: [
          { required: true, message: '请选择', trigger: 'blur' }
        ],
        score: [
          { required: true, message: '请输入题数', trigger: 'blur' }
        ],
        badge: [
          { required: true, message: '请输入徽章', trigger: 'blur' }
        ],
        customInfo: [
          { required: true, validator: validateCustomInfo, trigger: 'change' }
        ],
        answer: [
          { required: true, validator: validateAnswer, trigger: 'change' }
        ]
      },
      questionlnCourseFalse: false,
      currentAnswerItem: null,
      selectFileUrl: '',
    }
  },
  created() {
    this.getCategoryList()
    let id = this.$route.query.id
    this.form.courseType = this.$route.query.courseType ? 1 : 0
    if (id && parseInt(id) !== 0) {
      this.formLoading = true
      questionApi.detail(id).then(re => {
        this.form = re.data
        this.form.customInfo.map((item) => {
          this.mainFileList.push({ url: item.value, label: item.label })
        });
        this.form.answer.map((item) => {
          this.showFileList.push({ url: item.value, label: item.label })
        });
        this.formLoading = false
      })
      questionApi.checkQuestionlnCourse(id).then((res) => {
        this.questionlnCourseFalse = res.data
      });
    }
  },
  methods: {
    editorReady(instance) {
      this.richEditor.instance = instance
      let currentContent = this.richEditor.object[this.richEditor.parameterName]
      this.richEditor.instance.setContent(currentContent)
      // 光标定位到Ueditor
      this.richEditor.instance.focus(true)
    },
    editorConfirm() {
      let content = this.richEditor.instance.getContent()
      this.richEditor.object[this.richEditor.parameterName] = content
      this.richEditor.dialogVisible = false
    },
    search() {
      this.imagePage.dataQuery.questionType = this.form.questionType
      this.imagePage.dataQuery.difficulty = this.form.difficulty
      this.imagePage.listLoading = true
      this.imagePage.dataQuery.pageNum = this.imagePage.tablePage.currentPage
      this.imagePage.dataQuery.pageSize = this.imagePage.tablePage.size
      imageApi.list(this.imagePage.dataQuery).then(res => {
        this.imagePage.tableData = res.data.data
        this.imagePage.listLoading = false
        // 设置后台返回的分页参数
        pageParamNames.forEach(name => this.$set(this.imagePage.tablePage, name, parseInt(res.data[name])))
      })
    },
    handleSelectionChange(val) {
      if (val) {
        this.imagePage.selectData = val
      } else {
        this.imagePage.selectData = null
      }
    },
    tableRowClassName({ row, rowIndex }) {
      if (row.id === this.form.paperId) {
        return 'current-row'
      }
      return ''
    },
    confirmImageSelect() {
      if (this.imagePage.selectData) {
        this.form.customInfo = []
        let items = this.imagePage.selectData.fileList
        let newLastPrefix = 'A'
        items.forEach(a => {
          this.form.customInfo.push({ label: newLastPrefix, value: a })
          newLastPrefix = String.fromCharCode(newLastPrefix.charCodeAt() + 1)
        })
      }
      this.imagePage.showDialog = false
    },
    questionTypeFormatter(row, column, cellValue, index) {
      return this.enumFormat(this.trainQuestionType, cellValue)
    },
    difficultyFormatter(row, column, cellValue, index) {
      return this.enumFormat(this.difficultyList, cellValue)
    },

    getCategoryList() {
      categoryApi.list().then(res => {
        this.categoryList = res.data.data
      })
    },
    //展示图中有没有填写所有主图中的序列号
    isExistShowPicNum() {
      for (let i = 0; i < this.form.customInfo.length; i++) {
        let exist = false;
        for (let j = 0; j < this.form.answer.length; j++) {
          let cLabel = this.form.customInfo[i].label;
          let aLabel = this.form.answer[j].label;
          if (cLabel == aLabel) {
            exist = true;
            break;
          }
        }
        if (exist) {
          exist = false;
        } else {
          this.$message.error("您还没有正确填写所有序号");
          return false;
        }
      }
      return true;
    },
    //主图是否配置有序号
    isExistMainPicNum() {
      for (let i = 0; i < this.form.customInfo.length; i++) {
        if (this.form.customInfo[i].label != "") continue;
        this.$message.error("主图不可以没有序号！");
        return false;
      }
      return true;
    },
    //展示图中配置的序列号，在主图中是否存在
    isExistMainNumInShowNum() {
      for (let i = 0; i < this.form.answer.length; i++) {
        if (this.form.answer[i].label == '') continue; //为空跳过

        let isExist = false;
        for (let j = 0; j < this.form.customInfo.length; j++) {
          let aLabel = this.form.answer[i].label;
          let cLabel = this.form.customInfo[j].label;
          if (aLabel == cLabel) {
            isExist = true;
            break;
          }
        }
        if (isExist) {
          isExist = false;
        } else {
          this.$message.error("展示图中配置的序号在主图中找不到！序号：" + this.form.answer[i].label);
          return false;
        }
      }
      return true;
    },
    //主图中是否有序号重复
    isRepeatNum(arr, msg) {
      for (let i = 0; i < arr.length; i++) {
        let item = arr.find((item, index) => {
          return index != i && item.label != '' && item.label == arr[i].label;
        });
        if (item) {
          this.$message.error(msg + item.label);
          return true;
        }
      }
      return false;
    },
    //主图中的序号是否连续
    isContinuousMainPicNum(arr, msg) {
      let labelArr = [];
      arr.map((item) => { labelArr.push(Number(item.label)) });
      labelArr.sort((a, b) => a - b);

      for (let i = 1; i < labelArr.length; i++) {
        if (labelArr[i] - labelArr[i - 1] !== 1) {
          this.$message.error(msg + labelArr[i - 1].toString() + ', ' + labelArr[i].toString());
          return false;
        }
      }
      return true;
    },
    submitForm() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          if (!this.isExistMainPicNum()) return false; //主图一定要有序号
          if (!this.isExistShowPicNum()) return false; //主图中的序号，附图中一定要有
          if (!this.isExistMainNumInShowNum()) return false; //展示图中配置的序列号一定要在主图中存在
          if (this.isRepeatNum(this.form.customInfo, "主图中存在重复的序号！序号：")) return false; //主图中的序号不能重复
          if (this.isRepeatNum(this.form.answer, "展示图中存在重复的序号！序号：")) return false; //展示图中的序号不能重复
          if (!this.isContinuousMainPicNum(this.form.customInfo, "主图中的序号不连续！序号：")) return false; //主图中的序号一定要连续
          this.formLoading = true
          questionApi.saveOrUpdate(this.form).then(re => {
            if (re.success) {
              this.$message.success(re.message)
              this.formLoading = false
              if (this.form.courseType == 1) {
                this.$router.push("/train/trainAfterCourse")
              } else {
                this.$router.push("/train/visual")
              }
            } else {
              this.$message.error(re.message)
              this.formLoading = false
            }
          }).catch(e => {
            this.formLoading = false
          })
        } else {
          return false
        }
      })
    },
    resetForm() {
      let lastId = this.form.id
      let lastCourseType = this.form.id
      this.$refs['form'].resetFields()
      this.form = {
        id: null,
        title: '',
        categoryId: '',
        difficulty: '',
        difficultyId: '',
        type: 'VISUAL_',
        questionType: 'VISUAL_GRAPHIC_MATCHING',
        customInfo: [],
        answer: [],
        isRandom: false
      }
      this.form.id = lastId
      this.form.courseType = lastCourseType
      this.mainFileList = [];
      this.showFileList = [];
    },
    // 分页
    handleSizeChange(val) {
      this.imagePage.tablePage.size = val
      this.search()
    },
    handleCurrentChange(val) {
      this.imagePage.tablePage.currentPage = val
      this.search()
    },
    // 主图上传成功回调
    handleSuccess(url) {
      let value = { label: "", value: url };
      this.form.customInfo.push(value);
    },
    // 删除主图回调
    handleRemove(file) {
      let index = this.form.customInfo.findIndex(item => {
        if (item.value === file.url) {
          return true;
        }
      });
      this.form.customInfo.splice(index, 1);
    },
    // 展示图上传成功回调
    handleShowPicSuccess(url) {
      let value = { label: "", value: url };
      this.form.answer.push(value);
    },
    // 删除展示图回调
    handleShowPicRemove(file) {
      var index = this.form.answer.findIndex(item => {
        if (item.value === file.url) {
          return true;
        }
      });
      this.form.answer.splice(index, 1);
    },
    //主图序号
    handleInputNumber(e, file) {
      this.form.customInfo.forEach((item) => {
        if (item.value === file.url) {
          item.label = e.target.value;
        }
      });
    },
    //展示图序号
    handleShowPicInputNumber(e, file) {
      this.form.answer.forEach((item) => {
        if (item.value === file.url) {
          item.label = e.target.value;
        }
      });
    }
  },
  computed: {
    ...mapGetters('enumItem', ['enumFormat']),
    ...mapState('enumItem', {
      trainType: state => state.train.trainType,
      difficultyList: state => state.train.difficultyList,
      trainQuestionType: state => state.train.trainQuestionType
    })
  }
}
</script>

<style lang="less" scoped>
.img-lg {
  width: 60px;
  height: 60px;
}
</style>
