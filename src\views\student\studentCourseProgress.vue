<template>
  <div class="app-container">
    <el-form :inline="true" class="container-card" label-width="110px" label-position="left">
      <el-row>
        <el-col :span="6" :xs="24">
          <el-form-item label="门店编号：">
            <el-input v-model="dataQuery.merchantCode" placeholder="请输入门店编号" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="6" :xs="24">
          <el-form-item label="学员编号：">
            <el-input v-model="dataQuery.studentCode" placeholder="请输入学员编号" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="6" :xs="24">
          <el-form-item label="学员姓名：">
            <el-input v-model="dataQuery.studentName" placeholder="请输入学员姓名" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="6" :xs="24">
          <el-form-item label="课程名称：">
            <el-input v-model="dataQuery.courseName" placeholder="请输入课程名称" clearable />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="6" :xs="24">
          <el-form-item label="课程编号：">
            <el-input v-model="dataQuery.courseCode" placeholder="请输入课程编号" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="12" :xs="24">
          <el-form-item label="开通时间：">
            <el-date-picker style="width: 100%;" value-format="yyyy-MM-dd HH:mm:ss" type="daterange" v-model="value1"
              clearable align="right" unlink-panels range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="6" :xs="24">
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="fetchData01()">搜索</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-table class="common-table" v-loading="tableLoading" :data="tableData" style="width: 100%;margin-bottom: 20px;"
      row-key="id" border default-expand-all :tree-props="{ list: 'children', hasChildren: 'true' }">
      <el-table-column prop="scheduleCode" label="进度编号" width="120" sortable
        :show-overflow-tooltip="true"></el-table-column>
      <el-table-column prop="merchantCode" label="门店编号" sortable width="180"></el-table-column>
      <el-table-column prop="studentCode" label="学员编号" width="120" sortable></el-table-column>
      <el-table-column prop="courseCode" label="课程编号" width="120" sortable></el-table-column>
      <el-table-column prop="studentName" label="学员名称" width="120" sortable></el-table-column>
      <el-table-column prop="categoryName" label="课程分类" width="120" sortable></el-table-column>
      <el-table-column prop="bigClassName" label="课程类型" width="120" sortable></el-table-column>
      <el-table-column prop="courseLevelName" label="课程小类" width="120" sortable></el-table-column>
      <el-table-column prop="courseName" label="课程名称" width="150" sortable></el-table-column>
      <el-table-column prop="courseStageName" label="课程学段" width="120" sortable></el-table-column>
      <el-table-column prop="courseEditionName" label="教材版本" width="120" sortable></el-table-column>
      <el-table-column prop="lastTime" label="开通时间" width="200" sortable
        :show-overflow-tooltip="true"></el-table-column>
      <el-table-column prop="scheduleName" label="学习进度" width="180" sortable
        :show-overflow-tooltip="true"></el-table-column>
    </el-table>
    <!-- 分页 -->
    <el-col :span="24" style="overflow-x: auto;" :xs="24">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>
  </div>
</template>

<script>
import studentProgressApi from "@/api/studentCourseProgress";
import Tinymce from "@/components/Tinymce";
import { pageParamNames } from "@/utils/constants";
import ls from '@/api/sessionStorage'
export default {
  data() {
    return {
      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      tableData: [],
      dataQuery: {
        studentCode: '',
        studentName: '',
        startDate: '',
        endDate: '',
        courseName: '',
        courseCode: '',
        merchantCode: ''
      },
      value1: ''
    };
  },
  created() {
    this.fetchData();
    // 获取上个页面的学员编号
    this.dataQuery.studentCode = ls.getItem('courseStudentCode');

  },
  methods: {
    // 获取起始时间
    dateVal(e) {
      // console.log(e[0]);
      this.dataQuery.startDate = e[0]
      this.dataQuery.endDate = e[1]
    },
    fetchData01() {
      this.tablePage = {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      }
      this.fetchData();
    },
    // 查询提现列表
    fetchData() {

      const that = this;
      if (that.value1 != '' && that.value1 != null && that.value1 != undefined) {
        if (that.value1.length > 0) {
          that.dataQuery.startDate = that.value1[0]
          that.dataQuery.endDate = that.value1[1]
        } else {
          that.dataQuery.startDate = ''
          that.dataQuery.endDate = ''
        }
      } else {
        that.dataQuery.startDate = ''
        that.dataQuery.endDate = ''
      }
      that.tableLoading = true
      studentProgressApi.studentProgress(that.tablePage.currentPage, that.tablePage.size, that.dataQuery).then(res => {
        that.tableData = res.data.data
        that.tableLoading = false
        // 设置后台返回的分页参数
        pageParamNames.forEach(name => that.$set(that.tablePage, name, parseInt(res.data[name])))
      })
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val;
      this.fetchData();
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val;
      this.fetchData();
    },
  }
};
</script>

<style>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.period-table td,
.period-table th {
  text-align: center;
}

.mt20 {
  margin-top: 20px;
}

.red {
  color: red;
}

.green {
  color: green;
}
</style>
