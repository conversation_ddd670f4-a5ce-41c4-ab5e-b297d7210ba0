<template>
  <div class="app-container">
    <el-form
      class="recharge-form"
      label-width="110px"
      label-position="right"
      :ref="'dataQuery'"
      :rules="rules"
      :model="dataQuery"
    >
      <el-form-item label="新登录密码" prop="newPassWord">
        <el-input
          maxlength="20"
          isNumber2="true"
          min="1"
          id="newPassword"
          v-model="dataQuery.newPassWord"
          name="id"
        />
      </el-form-item>
      <el-form-item label="重复新登录密码" prop="rNewPassWord">
        <el-input
          maxlength="20"
          isNumber2="true"
          min="1"
          id="rNewPassWord"
          v-model="dataQuery.rNewPassWord"
          name="id"
        />
      </el-form-item>
      <el-form-item label="新交易密码：" prop="newSecondPwd">
        <el-input
          maxlength="20"
          isNumber2="true"
          min="1"
          id="newSecondPwd"
          v-model="dataQuery.newSecondPwd"
          name="id"
        />
      </el-form-item>
      <el-form-item
        label="重复新交易密码：
"
        prop="rNewSecondPwd"
      >
        <el-input
          maxlength="20"
          isNumber2="true"
          min="1"
          id="rNewSecondPwd"
          v-model="dataQuery.rNewSecondPwd"
          name="id"
        />
      </el-form-item>
      <el-form-item
        label="输入当前登录密码 * ：
"
        prop="password"
      >
        <el-input
          maxlength="20"
          isNumber2="true"
          min="1"
          id="password"
          v-model="dataQuery.password"
          name="id"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="success" @click="save('dataQuery')">保存</el-button>
        <el-button type="waring" @click="deletePassPwd('dataQuery')"
          >清除</el-button
        >
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
  import systemApi from "@/api/systemConfiguration";
  export default {
    data() {
      var validatePass = (rule, value, callback) => {
        if (value === '') {
          callback(new Error('请输入密码'));
        } else {
          if (this.dataQuery.rNewPassWord !== '') {
            this.$refs.dataQuery.validateField('rNewPassWord');
          }
          callback();
        }
      };
      var validatePass2 = (rule, value, callback) => {
        if (value === '') {
          callback(new Error('请再次输入密码'));
        } else if (value !== this.dataQuery.newPassWord) {
          callback(new Error('两次输入密码不一致!'));
        } else {
          callback();
        }
      };
      var validatePass3 = (rule, value, callback) => {
        if (value === '') {
          callback(new Error('请输入密码'));
        } else {
          if (this.dataQuery.rNewSecondPwd !== '') {
            this.$refs.dataQuery.validateField('rNewSecondPwd');
          }
          callback();
        }
      };
      var validatePass4 = (rule, value, callback) => {
        if (value === '') {
          callback(new Error('请再次输入密码'));
        } else if (value !== this.dataQuery.newSecondPwd) {
          callback(new Error('两次输入密码不一致!'));
        } else {
          callback();
        }
      };
      return {
        dataQuery: {},
        chargeCourseShow: false, //课程显示
        disabled: false,
        backeMoneyShow: false, //金额显示
        accountType: [], //扣除方式
        merchantCodeType: [], //账户方式
        queryTrueShow: true,
        dialogVisible: false, //密码弹框
        checkSecondPwdShow: false, //密码成功
        secondPassWord: "",
        backCourse: "", //剩余课时
        symoney: "", //账户余额
        // false: true,
        rules: {
          newPassWord: [{
              required: true,
              message: '请输入密码',
              trigger: 'blur'
            },
            {
              min: 6,
              max: 16,
              message: '长度在 6 到 16 个字符',
              trigger: 'blur'
            },
            {
              validator: validatePass,
              trigger: 'blur'
            }
          ],
          rNewPassWord: [{
              required: true,
              message: '请确认密码',
              trigger: 'blur'
            },
            {
              min: 6,
              max: 16,
              message: '长度在 6 到 16 个字符',
              trigger: 'blur'
            },
            {
              validator: validatePass2,
              trigger: 'blur',
              required: true
            }
          ],
          newSecondPwd: [{
              required: true,
              message: '请输入密码',
              trigger: 'blur'
            },
            {
              min: 6,
              max: 16,
              message: '长度在 6 到 16 个字符',
              trigger: 'blur'
            },
            {
              validator: validatePass3,
              trigger: 'blur'
            }
          ],
          rNewSecondPwd: [{
              required: true,
              message: '请确认密码',
              trigger: 'blur'
            },
            {
              min: 6,
              max: 16,
              message: '长度在 6 到 16 个字符',
              trigger: 'blur'
            },
            {
              validator: validatePass4,
              trigger: 'blur',
              required: true
            }
          ],

          password: [{
            required: true,
            message: '请输入旧密码',
            trigger: 'blur'
          }],
        },
      };
    },
    created() {},
    methods: {

      //修改密码
      save(ele) {
        this.$refs[ele].validate((valid) => {
          // 表单验证
          if (valid) {
            systemApi.updatePassWord(this.dataQuery).then(res => {
              this.dataQuery = {}
              this.$message.success("修改成功");
              this.$store.dispatch('LogOut').then(() => {
                location.reload() // In order to re-instantiate the vue-router object to avoid bugs
              })
            })
          } else {
            console.log("error submit!!");
            //loading.close();
            return false;
          }
        });
      },
      //删除
      deletePassPwd(ele) {
        this.dataQuery = {}
      }


    },
  };
</script>

<style scoped>
.recharge-form input {
  width: 100% !important;
}

.recharge-form textarea {
  width: 400px;
}

.recharge-form button {
  padding: 10px 40px;
}
</style>
