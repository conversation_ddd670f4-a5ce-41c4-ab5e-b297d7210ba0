<!--方格正序题-->
<template>
  <div class="app-container">
    <el-form :model="form" ref="form" label-width="100px" v-loading="formLoading" :rules="rules">
      <el-form-item label="所属类型:" prop="type">
        <el-select v-model="form.type" placeholder="全部" disabled>
          <el-option v-for="(item, index) in trainType" :key="index" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="题型:" prop="questionType">
        <el-select v-model="form.questionType" placeholder="全部" disabled>
          <el-option v-for="(item, index) in trainQuestionType" :key="index" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="年级：" prop="grade" v-if="form.courseType == 1">
        <el-select v-model="form.grade" placeholder="" clearable>
          <el-option v-for="item in gradeList" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="选择难度：" prop="difficulty">
        <el-select v-model="form.difficulty" placeholder="全部" clearable @change="handleChange">
          <el-option v-for="(item, index) in difficultyList" :key="index" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="选择维度：" prop="categoryId">
        <el-select v-model="form.categoryId" placeholder="全部" clearable>
          <el-option v-for="(item, index) in categoryList" :key="index" :label="item.name" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item v-if="form.courseType!=1" label="选择类别：" prop="categoryType">
        <el-select v-model="form.categoryType" :disabled="questionlnCourseFalse&&form.categoryType==1" placeholder="全部" clearable>
          <el-option v-for="(item, index) in pTypeList" :key="index" :label="item.label" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="题干：" prop="title">
        <el-input placeholder="请输入" v-model="form.title" />
      </el-form-item>
      <el-form-item label="选择类型：" required>
        <el-select v-model="selectType" placeholder="全部" :disabled="isEdit">
          <el-option v-for="(item, index) in typeList" :key="index" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <!-- 选择数字类型需填字段 -->
      <template v-if="this.selectType == '1'">
        <el-form-item label="题目：" required prop="customInfo">
          <div class="range" v-for="(item, index) in form.customInfo" :key="index">
            <el-input-number v-model="item.value.min" :precision="0" :step="1" :min="0" :max="100"></el-input-number>
            <div class="divider"></div>
            <el-input-number v-model="item.value.max" :precision="0" :step="1" :min="0" :max="100"></el-input-number>
          </div>
        </el-form-item>
      </template>
      <!-- 选择文字类型需填字段 -->
      <template v-else>
        <el-form-item label="题目：" required prop="customInfo">
          <div v-for="(item, index) in form.customInfo" :key="index">
            <el-input type="textarea" v-model="item.value" placeholder="请输入" maxlength="20" show-word-limit></el-input>
          </div>
        </el-form-item>
      </template>
      <el-form-item label="答案个数：" prop="answer">
        <div v-for="(item, index) in form.answer" :key="index">
          <el-input-number v-model="item.label" :precision="0" :step="1" :min="0" :max="999" @change="validateAnswerNum"></el-input-number>
        </div>
      </el-form-item>
      <el-form-item label="题数：" prop="score">
        <el-input-number v-model="form.score" :precision="0" :step="1" :min="1" :max="999"></el-input-number>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="submitForm">提交</el-button>
        <el-button v-show="form.id === null" @click="resetForm">重置</el-button>
      </el-form-item>
    </el-form>
    <el-dialog :visible.sync="richEditor.dialogVisible" append-to-body :close-on-click-modal="false" style="width: 100%; height: 100%" :show-close="false" center>
      <Ueditor @ready="editorReady" />
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="editorConfirm">确 定</el-button>
        <el-button @click="richEditor.dialogVisible = false">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import questionApi from '@/api/paper/train/question';
import difficultyApi from '@/api/paper/train/difficulty';
import categoryApi from '@/api/paper/train/category';
import { mapGetters, mapState } from 'vuex';
import MyUpload from '@/components/Upload/MyUpload';
import Ueditor from '@/components/Ueditor';

export default {
  components: {
    MyUpload,
    Ueditor
  },
  data() {
    return {
      richEditor: {
        dialogVisible: false,
        object: null,
        parameterName: '',
        instance: null
      },
      difficultyInfo: null,
      categoryList: [],
      pTypeList: [
        { label: '正式题', id: 1 },
        { label: '附加题', id: 2 }
      ],
      typeList: [
        {
          value: '1',
          label: '数字'
        },
        {
          value: '2',
          label: '文字'
        }
      ],
      fileList: [],
      importFrom: {
        file: null
      },
      randomLength: undefined,
      form: {
        id: null,
        title: '',
        categoryId: '',
        categoryType: '',
        difficulty: '',
        difficultyId: '',
        type: 'VISUAL_',
        questionType: 'VISUAL_LEAK',
        customInfo: [{ label: undefined, value: { min: undefined, max: undefined } }],
        answer: [{ label: undefined, value: '' }],
        analysis: '',
        score: undefined,
        badge: 3,
        courseType: 0
      },
      selectType: '', // 选择类型
      formLoading: false,
      rules: {
        type: [{ required: true, message: '请选择', trigger: 'blur' }],
        questionType: [{ required: true, message: '请选择', trigger: 'blur' }],
        grade: [{ required: true, message: '请选择', trigger: 'blur' }],
        title: [{ required: true, message: '请输入', trigger: 'blur' }],
        difficulty: [{ required: true, message: '请选择', trigger: 'blur' }],
        categoryId: [{ required: true, message: '请选择', trigger: 'blur' }],
        categoryType: [{ required: true, message: '请选择', trigger: 'blur' }],
        score: [{ required: true, message: '请输入题数', trigger: 'blur' }],
        badge: [{ required: true, message: '请输入徽章', trigger: 'blur' }]
      },
      currentAnswerItem: null,
      frontQAInfo: {
        // 修改时暂存题目及答案信息
        customInfo: [],
        answer: []
      },
      isEdit: false, // 是否为编辑状态
      gradeList: [
        { label: '1-3年级', value: 1 },
        { label: '4-6年级', value: 2 },
        { label: '初高中', value: 3 }
      ],
      questionlnCourseFalse: false,
    };
  },
  created() {
    this.getCategoryList();
    let id = this.$route.query.id;
    this.form.courseType = this.$route.query.courseType ? 1 : 0;
    this.selectType = '1'; // 默认选择类型为数字
    if (id && parseInt(id) !== 0) {
      this.formLoading = true;
      questionApi.detail(id).then((re) => {
        this.form = re.data;
        this.form.grade = this.form.grade == 0 ? '' : this.form.grade;
        this.selectType = this.form.customInfo[0].label;
        this.isEdit = true;
        // this.frontQAInfo.customInfo = this.form.customInfo[1];
        // this.frontQAInfo.answer = this.form.answer.slice(1, this.form.answer.length)

        this.form.customInfo.splice(1); // 后端返回题目信息中第一项为后台所需字段
        this.form.answer.splice(1);
        this.form.customInfo.forEach((a) => {
          if (a.label == '1') {
            a.value = JSON.parse(a.value);
          }
        });

        this.handleChange();
        this.formLoading = false;
      });
      questionApi.checkQuestionlnCourse(id).then((res) => {
        this.questionlnCourseFalse = res.data
      });
    }
  },
  watch: {
    // 选择类型变化，匹配相对应的题目与答案校验规则
    selectType(newVal, oldVal) {
      var validateQuestion = (rule, value, callback) => {
        const questionMin = this.difficultyInfo ? this.difficultyInfo.questionMin : undefined;
        const questionMax = this.difficultyInfo ? this.difficultyInfo.questionMax : undefined;
        if (!value[0].value.min || !value[0].value.max) {
          return callback(new Error('请将题目填写完整'));
        } else if (this.difficultyInfo && questionMin > value[0].value.min) {
          return callback(new Error('左区间数值应大于等于' + questionMin));
        } else if (this.difficultyInfo && questionMax < value[0].value.max) {
          return callback(new Error('右区间数值应小于等于' + questionMax));
        } else if (value[0].value.max <= value[0].value.min) {
          return callback(new Error('右区间数值必须大于左区间数值！'));
        }
        callback();
      };
      var validateQuestion1 = (rule, value, callback) => {
        if (value[0].value.min == 0 || value[0].value.max == 0) {
          return callback(new Error('请填入正整数'));
        } else if (!value[0].value) {
          return callback(new Error('请填写题目'));
        }
        callback();
      };
      let id = this.$route.query.id;
      if (newVal == '1') {
        const numberRules = {
          customInfo: [{ required: true, validator: validateQuestion, trigger: 'blur' }]
        };
        this.rules = Object.assign(this.rules, numberRules);
        // 若为编辑状态，不清除基础数据信息
        if (!id || parseInt(id) == 0) {
          this.form.customInfo = [{ label: undefined, value: { min: undefined, max: undefined } }];
          this.form.answer = [{ label: undefined, value: '' }];
          this.form.score = undefined;
        }
        console.log('校验规则变化了');
      } else {
        const textRules = {
          customInfo: [{ required: true, validator: validateQuestion1, trigger: 'blur' }]
        };
        this.rules = Object.assign(this.rules, textRules);
        // 若为编辑状态，不清除基础数据信息
        if (!id || parseInt(id) == 0) {
          this.form.customInfo = [{ label: undefined, value: '' }];
          this.form.answer = [{ label: undefined, value: '' }];
          this.form.score = undefined;
        }
      }

      this.$refs.form.clearValidate();
    }
  },
  methods: {
    validateAnswerNum(value) {
      if (this.selectType == '1') {
        //数字
        if (this.form.customInfo[0].value.max && value >= this.form.customInfo[0].value.max) {
          this.$message.error('答案个数不能超过题目最大数！');
          this.form.answer[0].label = null;
          return false;
        }
      } else {
        if (this.form.customInfo[0].value && value > this.form.customInfo[0].value.length) {
          this.$message.error('答案个数不能超过题目内容！');
          this.form.answer[0].label = null;
          return false;
        }
      }
      return true;
    },

    editorReady(instance) {
      this.richEditor.instance = instance;
      let currentContent = this.richEditor.object[this.richEditor.parameterName];
      this.richEditor.instance.setContent(currentContent);
      // 光标定位到Ueditor
      this.richEditor.instance.focus(true);
    },
    inputClick(object, parameterName) {
      this.richEditor.object = object;
      this.richEditor.parameterName = parameterName;
      this.richEditor.dialogVisible = true;
    },
    editorConfirm() {
      let content = this.richEditor.instance.getContent();
      this.richEditor.object[this.richEditor.parameterName] = content;
      this.richEditor.dialogVisible = false;
    },

    getCategoryList() {
      categoryApi.list().then((res) => {
        this.categoryList = res.data.data;
      });
    },
    handleChange() {
      if (!this.form.difficulty) {
        this.difficultyInfo = null;
        return;
      }
      let query = {};
      query.type = this.form.type;
      query.questionType = this.form.questionType;
      query.difficulty = this.form.difficulty;
      difficultyApi
        .isSetting(query)
        .then((res) => {
          this.difficultyInfo = res.data;
        })
        .catch((e) => {
          this.difficultyInfo = null;
          this.fileList = [];
        });
    },
    submitForm() {
      if (this.difficultyInfo === null) {
        this.$message.error('请选择难度！');
        return false;
      }
      if (this.difficultyInfo.answerMin > this.form.answer[0].label || this.difficultyInfo.answerMax < this.form.answer[0].label) {
        this.$message.error('答案个数需在' + this.difficultyInfo.answerMin + '~' + this.difficultyInfo.answerMax + '范围内');
        return false;
      }
      if (!this.validateAnswerNum(this.form.answer[0].label)) {
        return false;
      }
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.form.customInfo.forEach((a) => {
            a.label = this.selectType;
            if (this.selectType == '1') {
              a.value = JSON.stringify(a.value);
            }
          });
          const item = this.form.answer.find((el) => typeof el.label != 'undefined');
          if (!item) {
            this.form.answer = [{ label: 0, value: '' }];
          }

          this.formLoading = true;
          questionApi
            .saveOrUpdate(this.form)
            .then((re) => {
              if (re.success) {
                this.$message.success(re.message);
                this.formLoading = false;
                if (this.form.courseType == 1) {
                  this.$router.push('/train/trainAfterCourse');
                } else {
                  this.$router.push('/train/visual');
                }
              } else {
                this.$message.error(re.message);
                this.formLoading = false;
              }
            })
            .catch((e) => {
              this.formLoading = false;
            });
        } else {
          return false;
        }
      });
    },
    resetForm() {
      let lastId = this.form.id;
      let lastCourseType = this.form.courseType;
      this.selectType = '1';
      this.$refs['form'].resetFields();
      this.form = {
        id: null,
        title: '',
        categoryId: '',
        difficulty: '',
        difficultyId: '',
        type: 'VISUAL_',
        questionType: 'VISUAL_LEAK',
        customInfo: [{ label: undefined, value: { min: undefined, max: undefined } }],
        answer: [{ label: undefined, value: '' }],
        analysis: '',
        score: undefined,
        badge: 3,
        grade: ''
      };
      this.form.id = lastId;
      this.form.courseType = lastCourseType;
    }
  },
  computed: {
    ...mapGetters('enumItem', ['enumFormat']),
    ...mapState('enumItem', {
      trainType: (state) => state.train.trainType,
      difficultyList: (state) => state.train.difficultyList,
      trainQuestionType: (state) => state.train.trainQuestionType
    })
  }
};
</script>

<style lang="less" scoped>
.question-item-label {
  display: flex;
  margin-bottom: 12px;
}

.drawing-item {
  overflow: hidden;
  white-space: normal;
  word-break: break-all;
}
.divider {
  display: inline-block;
  width: 80px;
  height: 4px;
  vertical-align: middle;
  background-color: #ccc;
  margin: 0 5px;
  border-radius: 2px;
}
</style>
