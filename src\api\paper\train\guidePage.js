import request from '@/utils/request'

export default {
  // 分页查询
  list(data) {
    return request({
      url: '/paper/web/guide/list',
      method: 'GET',
      params: data
    })
  },
//添加
  createOrUpdate(data) {
    return request({
      url: '/paper/web/guide/createOrUpdate',
      method: 'POST',
      data
    })
  },
  detail(id) {
    return request({
      url: '/paper/web/guide/detail',
      method: 'GET',
      params: {
        id: id
      }
    })
  },
  delete(id){
    return request({
      url: '/paper/web/guide/delete',
      method: 'DELETE',
      params: {
        id: id
      }
    })
  }
}
