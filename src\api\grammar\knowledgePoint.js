import request from "@/utils/request";

// 根据字典查询阶段相应的值 {dictType:'grammar_phase'}
export function queryByTypeAPI(query) {
  return request({
    url: "/dyf/web/v2/dict/queryByType",
    method: "get",
    params: query,
  });
}

// 知识点分页数据
export function pageAPI(query, pageParam) {
  return request({
    url: "/dyf/web/v2/knowledge/page",
    method: "get",
    params: {
      ...query,
      pageNum: pageParam.currentPage == null ? 1 : pageParam.currentPage,
      pageSize: pageParam.size == null ? 10 : pageParam.size,
    },
  });
}

// 下拉选项 语法点 知识点
export function optionsAPI(query) {
  return request({
    url: "/dyf/web/v2/knowledge/options",
    method: "get",
    params: query,
  });
}

// 新增 编辑 知识点
export function addOrUpdateKnowAPI(data) {
  return request({
    url: "/dyf/web/v2/knowledge/addOrUpdate",
    method: "post",
    data: data,
  });
}

// 删除知识点
export function deleteAPI(query) {
  return request({
    url: "/dyf/web/v2/knowledge/delete",
    method: "delete",
    params: query,
  });
}

// 知识点详情
export function detailXmindAPI(query) {
  return request({
    url: "/dyf/web/v2/knowledge/detail",
    method: "get",
    params: query,
  });
}

// 编辑行数据 /dyf/web/v2/knowledge/detail
export function detailRowAPI(query) {
  return request({
    url: "/dyf/web/v2/knowledge/detail",
    method: "get",
    params: query,
  });
}
