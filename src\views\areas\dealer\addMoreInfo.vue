<template>
<div class="container">
  <!-- 顶部标签 -->
   <div class="back" @click="gobanck">
    <i class="el-icon-arrow-left" ></i> 批量新增
   </div>
  <!-- 中间 表单 -->
  <div class="elTable">
    <el-form
      :model="addJsonForm"
      ref="addJsonForm"
      :rules="addJsonForm.addJsonRules"
      :inline="true"
      label-width="108px"
      
    >
      <el-table :data="addJsonForm.params" style="width: 100%" border  
      :row-class-name="tableRowClassName" @row-click = "onRowClick"
      :header-cell-style="{textAlign: 'center',background: '#f4f6f8', color: '#000000'}">
        <!-- 登录账号 -->
        <el-table-column align="center" label="登录账号">
          
          <template slot-scope="scope">
            <el-form-item
              :prop="'params.' + scope.$index + '.name'"
              :rules="addJsonForm.addJsonRules.name"
            >
              <el-input
                type="text"
                v-model="scope.row.name"
                placeholder="请输入登录账号"
                autocomplete="off"
                maxlength="11"
              ></el-input>
            </el-form-item>
          </template>
        </el-table-column>
        <!-- 门店类型 -->
        <el-table-column align="center" label="门店类型">
          
          <template slot-scope="scope">
            <el-form-item
              :prop="'params.' + scope.$index + '.stores'"
              :rules="addJsonForm.addJsonRules.stores"
            >
              <el-input
                type="text"
                v-model="scope.row.stores"
                autocomplete="off"
               
              ></el-input>
            </el-form-item>
          </template>
        </el-table-column>
        <!-- 负责人 -->
        <el-table-column align="center" label="负责人">
          
          <template slot-scope="scope">
            <el-form-item
              :prop="'params.' + scope.$index + '.realName'"
              :rules="addJsonForm.addJsonRules.realName"
            >
              <el-input
                type="text"
                v-model="scope.row.realName"
                placeholder="请填负责人名称"
                autocomplete="off"
              ></el-input>
            </el-form-item>
          </template>
        </el-table-column>
        <!-- 身份证 -->
        <el-table-column align="center" label="身份证">
          
          <template slot-scope="scope">
            <el-form-item
              :prop="'params.' + scope.$index + '.idCard'"
              :rules="addJsonForm.addJsonRules.idCard"
            >
              <el-input
                type="text"
                v-model="scope.row.idCard"
                placeholder="请输入身份证号码"
                autocomplete="off"
                maxlength="18"
              ></el-input>
            </el-form-item>
          </template>
        </el-table-column>
        <!-- 合同照片 -->
        <el-table-column align="center" label="合同照片">
          
          <template slot-scope="scope">
            <el-form-item
              :prop="'params.' + scope.$index + '.contractList'"
              :rules="addJsonForm.addJsonRules.contractList"
            >
              <el-upload ref="clearuploadC" aria-setsize="" v-loading="uploadLoading"
               list-type="picture-card"  action="" 
               element-loading-text="图片上传中"
              :http-request="uploadDetailHttpShop" 
              :on-remove="handleRemoveDetailHT"
              :on-preview="handlePreviewHT"
               >
              <i class="el-icon-plus" />
              </el-upload>
            </el-form-item>
          </template>
        </el-table-column>
        <!-- 证件照片 -->
        <el-table-column align="center" label="证件照片">
          
          <template slot-scope="scope">
            <el-form-item
              :prop="'params.' + scope.$index + '.idList'"
              :rules="addJsonForm.addJsonRules.idList"
            >
            <el-upload  ref="clearupload"  v-loading="uploadLoading" list-type="picture-card"  action="" 
             element-loading-text="图片上传中"
              :http-request="uploadDetailHttpIdCard" 
              :on-remove="handleRemoveDetailId"
              :on-preview="handlePreviewId"
               >
              <i class="el-icon-plus" />
              </el-upload>
            </el-form-item>
          </template>
        </el-table-column>
        <!-- 所在地区 -->
        <el-table-column align="center" label="所在地区">
          
          <template slot-scope="scope">
            <el-form-item
              :prop="'params.' + scope.$index + '.address'"
              :rules="addJsonForm.addJsonRules.location"
            >
              <el-input
               v-if="scope.row.location"
                type="text"
                v-model="scope.row.location"
                autocomplete="off"
              ></el-input>
              <div v-else @click="choseAddress">
                请选择
              </div>
            </el-form-item>
          </template>
        </el-table-column>
        <!-- 介绍 -->
        <el-table-column align="center" label="门店简介">
          
          <template slot-scope="scope">
            <el-form-item
              :prop="'params.' + scope.$index + '.description'"
              :rules="addJsonForm.addJsonRules.description"
            >
              <el-input
                type="text"
                v-model="scope.row.description"
                placeholder="请填写门店简介"
                autocomplete="off"
              ></el-input>
            </el-form-item>
          </template>
        </el-table-column>
      
      </el-table>
      <div class="">

      </div>
    </el-form>
    <div class="fromAdd" @click="addRow">
           点击新增
    </div>
  </div>
 


 
  <!-- 按钮：确定，取消 -->
  <div class="bottom">
  <el-button type="primary" @click="submit"  :disable="sub" >确定</el-button>
  <el-button @click="cancle">取消</el-button>
  </div>
  <!-- 预览 -->
  <el-dialog :visible.sync="dialogVisible">
  <img width="100%" :src="dialogImageUrl" alt="">
</el-dialog>
<!-- 地图弹窗 -->
<el-dialog :visible.sync="dialogVisibleMap">
  <div class="amap-page-container">
              <div :style="{ width: '100%', height: '450px' }">
                <el-amap-search-box class="search-box" :search-option="searchOption"
                  :on-search-result="onSearchResult"></el-amap-search-box>
                <el-amap vid="amap" :plugin="plugin" :center="center" class="amap-demo" :events="events">
                  <el-amap-info-window :position="currentWindow.position" :content="currentWindow.content"
                    :visible="currentWindow.visible" :events="currentWindow.events">
                  </el-amap-info-window>
                  <!-- 定位点标注 -->
                  <!--                  <el-amap-marker
                                      vid="component-marker"
                                      :position="center"
                                    ></el-amap-marker>-->
                  <el-amap-marker v-for="(marker, index) in markers" :position="marker"
                    :key="index + '-only'"></el-amap-marker>
                  <!-- 搜索结果标注 -->
                  <el-amap-marker v-for="(marker, index) in markers2" :key="index" :position="marker.position"
                    :events="marker.events"></el-amap-marker>
                  <el-amap-info-window v-if="window" :position="window.position" :visible="window.visible"
                    :content="window.content"></el-amap-info-window>
                </el-amap>
              </div>
            </div>
</el-dialog>
</div>

</template>

<script >
import { ossPrClient } from '@/api/alibaba'
import schoolApi from '@/api/schoolList'
import { isvalidPhone, idCard } from '@/utils/validate'
export default {
  name: 'addMoreInfo',
  // data() {
  //     return {
  //       // 登录账号,门店类型,负责人,身份证,合同照片,证件照片,所在地区,门店简介
  //       tableData: [{
  //         accoutn: 'dxadmin',
  //         stores: '超级俱乐部门店',
  //         name: '王二麻子',
  //         idCard:'234567890090',
  //         contractPhoto:null,
  //         idCardPhoto:null,
  //         address:'xx省xx市xx县',
  //         introduce:'无'
  //       }]
  //     }
  //   },
  data() {


   //手机号验证
   var validPhone = (rule, value, callback) => {
      if (this.updateMarketDate.schoolType === 3) {
        callback()
      } else if (!value) {
        callback(new Error('请输入电话号码'))
      } else if (!isvalidPhone(value)) {
        callback(new Error('请输入正确的11位手机号码'))
      } else {
        callback()
      }
    }
    //身份证表达式
    var isIdCard = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入身份证号码'))
      } else if (!idCard(value)) {
        callback(new Error('请输入正确的18位身份证号'))
      } else {
        callback()
      }
    }

    const self = this
    return {
      addJsonForm: {
        params: [
          {
            name: '', //登录账号，
            schoolType:'3',//门店类型
            stores: '超级俱乐部门店',
            realName: '', //姓名
            idCard:'', //身份证号码
            contractPhoto:[], //合同照片
            idCardPhoto:[], //身份证照片
            address:'', //详细地址
            description:'', //店面介绍
            location:'', //省市区
            merchantType:'3',
            paymentIsComplete: "1", // 是否完款，这里应该是写死1
            merchantName: "-", // 门店名称
            centralizedDeliver: false, // 是否集中交付 true：是，false：否
           
            isCheck: 0, // 审核状态，默认写死0
            isEnable: 0, // 是否启用，默认写死0
            idList:[],//身份证图片信息列表
            contractList:[] //合同照片信息列表
          }
        ],
        addJsonRules: {
          name: [
            { required: true, message: "登录账号不能为空", trigger: "blur" },
            {
            validator: validPhone,
            trigger: 'blur'
          }
          ],
          stores: [
            { required: true, message: "门店类型不能为空", trigger: "blur" }
          ],
          realName: [
            { required: true, message: "负责人名称不能为空", trigger: "blur" }
          ],
          idCard: [
            { required: true, message: "身份证号不能为空", trigger: "blur" },
            {
            validator: isIdCard,
            trigger: 'blur'
          }
          ],
          contractPhoto: [
            { required: true, message: "合同照片不能为空", trigger: "blur" }
          ],
          idCardPhoto: [
            { required: true, message: "证件照片不能为空", trigger: "blur" }
          ],
          location: [
            { required: true, message: "所在地区不能为空", trigger: "blur" }
          ],
          introduce: [
            { required: true, message: "门店简介不能为空", trigger: "blur" }
          ],
         
        }
      },
       sub:false,
        tableId:0,
        dialogImageUrl: '',
        dialogVisible: false, //图片预览弹窗
        dialogVisibleMap:false,//地图弹窗
        disabled: false,
        fileShopDetailList:[],
        uploadLoadingShop: false,
        dialogUploadVisible: false,
        fileDetailListPending: [], // 待处理已上传图片信息
        uploadLoading: false, // 上传图片加载按钮,
        fileContractDetailList:[],
        // addOrUpdate: true,
        updateMarketDate: {
        paymentIsComplete: '1'
        },
        addOrUpdate: true,
        addMarketDate: {
        address: '',
        merchantType: '3',
        paymentIsComplete: '1'
        },
        uploadLoadingIdCard: false, //上传身份证图片加载
        showAddress:false, //默认展示点击按钮
        //地图开始
      currentWindow: {
        position: [0, 0],
        content: '',
        events: {},
        visible: false
      },
      markers: [],
      roleTag: '',
      //搜索结果标注
      markers2: [],
      windows: [],
      window: '',
      searchOption: {
        city: '全国',
        citylimit: false
      },
      center: [117.26696, 31.87869],
      zoom: 12,
      lng: 0,
      lat: 0,
      address: '',
      province: '',
      city: '',
      district: '',
      loaded: false,
      circles: [],
      events: {
        click(e) {
          let { lng, lat } = e.lnglat
          self.lng = lng
          self.lat = lat
          // 这里通过高德 SDK 完成。
          var geocoder = new AMap.Geocoder({
            radius: 1000,
            extensions: 'all'
          })
          self.showWindow = false
          geocoder.getAddress(
            [e.lnglat.lng, e.lnglat.lat],
            function (status, result) {
              if (status === 'complete' && result.info === 'OK') {
                if (result && result.regeocode) {
                  let city = null
                  if (result.regeocode.addressComponent.city == '') {
                    city = result.regeocode.addressComponent.district
                  } else {
                    if (
                      result.regeocode.addressComponent.province ==
                      '重庆市' ||
                      result.regeocode.addressComponent.province ==
                      '天津市' ||
                      result.regeocode.addressComponent.province ==
                      '北京市' ||
                      result.regeocode.addressComponent.province ==
                      '上海市'
                    ) {
                      city = result.regeocode.addressComponent.province
                    } else {
                      city = result.regeocode.addressComponent.city
                    }
                  }
                  self.markers = [[lng, lat]]
                  self.addJsonForm.params[self.tableId].latitude = lat
                  self.addJsonForm.params[self.tableId].longitude = lng
                  self.addJsonForm.params[self.tableId].province = result.regeocode.addressComponent.province
                  self.addJsonForm.params[self.tableId].city = city
                  self.addJsonForm.params[self.tableId].area = result.regeocode.addressComponent.district
                  self.addJsonForm.params[self.tableId].address = result.regeocode.formattedAddress
                  self.addJsonForm.params[self.tableId].location = result.regeocode.addressComponent.province + city + result.regeocode.addressComponent.district
                  self.$nextTick()
                }
              } else {
                alert('地址获取失败')
              }
            }
          )
        }
      },
      plugin: [
        {
          enableHighAccuracy: true, //是否使用高精度定位，默认:true
          timeout: 100, //超过10秒后停止定位，默认：无穷大
          maximumAge: 0, //定位结果缓存0毫秒，默认：0
          convert: true, //自动偏移坐标，偏移后的坐标为高德坐标，默认：true
          showButton: true, //显示定位按钮，默认：true
          buttonPosition: 'RB', //定位按钮停靠位置，默认：'LB'，左下角
          showMarker: true, //定位成功后在定位到的位置显示点标记，默认：true
          showCircle: true, //定位成功后用圆圈表示定位精度范围，默认：true
          panToLocation: true, //定位成功后将定位到的位置作为地图中心点，默认：true
          zoomToAccuracy: true, //定位成功后调整地图视野范围使定位位置及精度范围视野内可见，默认：f
          extensions: 'all',
          expandZoomRange: true,
          keyboardEnable: true,
          pName: 'Geolocation',
          campus: [],
          events: {
            init(o) {
              // o 是高德地图定位插件实例
              o.getCurrentPosition((status, result) => {
                if (result && result.position) {
                  self.lng = result.position.lng
                  self.lat = result.position.lat
                  self.markers = [[result.position.lng, result.position.lat]]

                  self.loaded = true
                  self.$nextTick()
                }
              })
            }
          }
        }
      ]
    };
  },
  created(){
    ossPrClient()
  },
mounted(){

},
  methods:{
    //删除合同预览图片 
    handleRemoveDetailHT(file, fileList){
    
     let phList =  this.addJsonForm.params[this.tableId].contractList
     for(let a =0;a<phList.length;a++){
      phList[a].uid === file.uid ? phList.splice(a,1):''
     }

     this.addJsonForm.params[this.tableId].contractPhoto = []
    for(var i =0; i<phList.length;i++){
      this.addJsonForm.params[this.tableId].contractPhoto.push(phList[i].name)
    }
    },
      // 上传合同图片预览
    handlePreviewHT(file){
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
   
   
    // 合同图片上传oss 获取图片网络地址
    uploadDetailHttpShop({ file }) {
     this.uploadLoadingShop = true
      const that = this
      const fileName = 'manage/' + Date.parse(new Date())
      that.$nextTick(function () {
        ossPrClient()
          .put(fileName, file)
          .then(({ res, url, name }) => {
            if (res && res.status === 200) {
              console.log(`阿里云OSS上传图片成功回调1`, res, url, name)

                  that.addJsonForm.params[ this.tableId].contractList.push({
                    uid:file.uid,
                    url:url,
                    name
                  })
                  that.addJsonForm.params[ this.tableId].contractPhoto.push(name)
                    console.log(that.addJsonForm);
              
              // if (!that.addOrUpdate) {
              //   that.fileShopDetailList.push({
              //     uid: file.uid,
              //     url: url
              //   })
              // } else {
              //   // 新增上传图片
              //   that.fileShopDetailList.push({
              //     name
              //   })
              //   that.updateMarketDate.shopPhoto = name
              //   that.uploadLoadingShop = false
              // }
              that.$nextTick(() => {
                that.uploadLoadingShop = false
              })
            }
          })
          .catch((err) => {
            that.$message.error('上传图片失败请检查网络或者刷新页面')
            console.log(`阿里云OSS上传图片失败回调`, err)
          })
      })
    },
    // 上传证件照片
    uploadDetailHttpIdCard({ file }) {
     this.uploadLoadingIdCard = true
      const that = this
      const fileName = 'manage/' + Date.parse(new Date())
      that.$nextTick(function () {
        ossPrClient()
          .put(fileName, file)
          .then(({ res, url, name }) => {
            if (res && res.status === 200) {
              console.log(`阿里云OSS上传图片成功回调1`, res, url, name)

                  that.addJsonForm.params[ this.tableId].idList.push({
                    uid:file.uid,
                    url:url,
                    name
                  })
                  that.addJsonForm.params[ this.tableId].idCardPhoto.push(name)
                    console.log(that.addJsonForm);
              
              // if (!that.addOrUpdate) {
              //   that.fileShopDetailList.push({
              //     uid: file.uid,
              //     url: url
              //   })
              // } else {
              //   // 新增上传图片
              //   that.fileShopDetailList.push({
              //     name
              //   })
              //   that.updateMarketDate.shopPhoto = name
              //   that.uploadLoadingShop = false
              // }
              that.$nextTick(() => {
                that.uploadLoadingIdCard = false
              })
            }
          })
          .catch((err) => {
            that.$message.error('上传图片失败请检查网络或者刷新页面')
            console.log(`阿里云OSS上传图片失败回调`, err)
          })
      })
    },
    // 预览身份照片
      handlePreviewId(file){
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    //删除身份照片
    handleRemoveDetailId(file, fileList){
    
    let phList =  this.addJsonForm.params[this.tableId].idList
    for(let a =0;a<phList.length;a++){
     idList[a].uid === file.uid ? phList.splice(a,1):''
    }
    this.addJsonForm.params[this.tableId].idCardPhoto = []
    for(var i =0; i<phList.length;i++){
      this.addJsonForm.params[this.tableId].idCardPhoto.push(phList[i].name)
    }

   },
  //  选择地址
   choseAddress(){
     console.log('选择地址');
     this.dialogVisibleMap = true
   },

  //  搜索地址展示
  onSearchResult(pois) {
      let latSum = 0
      let lngSum = 0
      let markers = []
      let windows = []
      let that = this
      that.result = []
      if (pois.length > 0) {
        // console.log(pois)
        pois.forEach((poi, index) => {
          const { lng, lat } = poi
          lngSum += lng
          latSum += lat
          markers.push({
            position: [poi.lng, poi.lat],
            events: {
              click() {
                // console.log(poi)
                that.windows.forEach((window) => {
                  window.visible = false
                })
                that.window = that.windows[index]
                that.$nextTick(() => {
                  that.window.visible = true
                  that.getMarkAddress(poi.lng, poi.lat)
                })
              }
            }
          })
          // ${ index }<img src="" style="">
          windows.push({
            position: [poi.lng, poi.lat],
            content: `<div class="prompt"><span>${poi.name}</span></div>`,
            visible: false
          })
          that.result.push(poi)
        })
        const center = {
          lng: lngSum / pois.length,
          lat: latSum / pois.length
        }
        this.mapCenter = [center.lng, center.lat]
        this.center = [center.lng, center.lat]
        this.markers2 = markers
        this.windows = windows
      }
    },
    getMarkAddress(lng, lat) {
      // 这里通过高德 SDK 完成。
      var that = this
      var geocoder = new AMap.Geocoder()
      geocoder.getAddress([lng, lat], function (status, result) {
        if (status === 'complete' && result.info === 'OK') {
          if (result && result.regeocode) {
            // console.log(result)
            // that.searchOption.city = result.regeocode.addressComponent.city
               that.center = [lng, lat]
              that.addJsonForm.params[that.tableId].longitude = lng
              that.addJsonForm.params[that.tableId].latitude = lat
              that.addJsonForm.params[that.tableId].address = result.regeocode.formattedAddress
              that.addJsonForm.params[that.tableId].province =
                result.regeocode.addressComponent.province
              var reg = RegExp(/省/)
              if (
                that.addJsonForm.params[that.tableId].province.match(reg) &&
                result.regeocode.addressComponent.city == ''
              ) {
                that.addJsonForm.params[that.tableId].city =
                  result.regeocode.addressComponent.district
              } else {
                if (
                  result.regeocode.addressComponent.province == '重庆市' ||
                  result.regeocode.addressComponent.province == '天津市' ||
                  result.regeocode.addressComponent.province == '北京市' ||
                  result.regeocode.addressComponent.province == '上海市'
                ) {
                  that.addJsonForm.params[that.tableId].city =
                    result.regeocode.addressComponent.province
                } else {
                  // 市
                  that.addJsonForm.params[that.tableId].city =
                    result.regeocode.addressComponent.city
                }
              }
              that.addJsonForm.params[that.tableId].area =
                result.regeocode.addressComponent.district
            that.$nextTick()
          }
        } else {
          // alert('地址获取失败')
        }
      })
    },
  gobanck(){
    this.$router.go(-1)
    
  },
// 新增部分
tableRowClassName ({row, rowIndex}) {
   //把每一行的索引放进row
   row.index = rowIndex;
},
onRowClick (row, event, column) {
   //点击获取索引
   const index = row.index;
   this.tableId = index
   console.log( this.tableId);
},

 

  addRow(){
  if( this.addJsonForm.params.length >=10 ) {
    return this.$message.error('单次最多添加10条')
  }
  
 for(let i = 0; i < this.addJsonForm.params.length; i++) {
        const item = this.addJsonForm.params[i];
        const filterarr = ["area"];
        for(let key in item) {
          filterarr.find((el) => {
           if (key === el) {
           delete item[el];
           }
           });
        }
        for(let key in item) {
            // 检查值是否为null, undefined, 空字符串或空数组
            if(item[key] === null || item[key] === undefined || item[key] === '' || (Array.isArray(item[key]) && item[key].length === 0)) {
            this.$message.error('请完善表格信息')
                return true;
            }
        }
    }
   
    const newTableData = {
            name: '', //登录账号，
            schoolType:'3',//门店类型
            stores: '超级俱乐部门店',
            realName: '', //姓名
            idCard:'', //身份证号码
            contractPhoto:[], //合同照片
            idCardPhoto:[], //身份证照片
            address:'', //详细地址
            description:'', //店面介绍
            location:'', //省市区
            merchantType:'3',
            paymentIsComplete: "1", // 是否完款，这里应该是写死1
            merchantName: "-", // 门店名称
            centralizedDeliver: false, // 是否集中交付 true：是，false：否
            isCheck: 0, // 审核状态，默认写死0
            isEnable: 0 ,// 是否启用，默认写死0
            idList:[],
            contractList:[]
        }

       this.addJsonForm.params= [...this.addJsonForm.params,newTableData]
    
  },

  checkIfAnyItemIsEmpty(params){
   

    return false;
},

// 提交数据给后端
submit(){
  // refereeCode: "",// 推荐人
   this.sub = true
  for(var i =0; i<this.addJsonForm.params.length;i++){
    this.addJsonForm.params[i].refereeCode = ''
    }
  schoolApi.moreAddSchool(this.addJsonForm.params).then((res)=>{
      
    if(res.success === true){
      this.$refs.addJsonForm.resetFields()
      this.$refs.clearupload.clearFiles()
      this.$refs.clearuploadC.clearFiles()
      this.addJsonForm.params =[]
      this.sub = false

      console.log(res.data.suc.toString());
      // this.$message.success(res.data.suc)
      if(res.data.fail){
        // this.$message.error(res.data.fail.toString(),res.data.suc.toString())
        // this.$message.error('创建成功，异常门店数据：' + res.data.fail.toString())
        this.$alert('操作成功，异常门店数据：' + res.data.fail.toString(), { confirmButtonText: '确定' 
        })
      }else {
        this.$message.success('操作成功')
      }
    }
  })
 
// console.log( this.addJsonForm.params);

  // this.$refs.addJsonForm.resetFields()
  
},
cancle(){
  this.$refs.addJsonForm.resetFields()
  this.$refs.clearupload.clearFiles()
  this.$refs.clearuploadC.clearFiles()
  // this.$refs.clearupload.clearQueue()
  // this.$refs.clearuploadC.clearQueue()
  this.addJsonForm.params =[
        
          ]
}
}
}


</script>

<style lang="less" scoped>

 .container{
  
  .back{
      margin-top: 30px;
      margin-left: 50px;
      vertical-align: middle;
      .el-icon-arrow-left{
        margin-right: 10px;
      }
      
  }
 .elTable{
  margin-top: 10px;
 
 }
 .fromAdd{
  width: 100%;
  height: 40pX;
  display: flex;
  justify-content: center;
  align-items:center ;
  border: 1px solid #efefef;
 }
 .bottom{
  width: 100%;
  display: flex;
  justify-content: center;
  margin-top: 30px;
 }
 }

</style>