<template>
  <div class="app-container">
	<el-row type="flex" justify="center">
		<el-col :xs="24" :span="10">
			<el-form label-width="96px" label-position="left" style="width: 100%;margin: 0 auto;" :model="checkStatus">
			  <el-form-item label="是否通过：" prop="isCheck">
				<el-radio v-model="radio" label="1" @change="change(radio)">通过</el-radio>
				<el-radio v-model="radio" label="0" @change="change(radio)">不通过</el-radio>
			  </el-form-item>
			  <el-form-item label="审核意见：">
				<el-col :xs="24" :span="24">
					<el-input type="textarea" resize="none" rows="4"  v-model="checkStatus.checkReason"></el-input>			
				</el-col>
			  </el-form-item>
			  <el-form-item>
				<el-button type="primary"  @click="check()">审核</el-button>
			  </el-form-item>
			</el-form>			
		</el-col>
	</el-row>
  </div>
</template>

<script>
  import schoolApi from "@/api/schoolList";
  import ls from '@/api/sessionStorage'
  export default {
    data() {
      return {
        checkStatus:{
          isCheck:1
        },
        radio:'1',


      }
    },
    created() {

    },
    onLoad(option) {

    },
    methods:{
      change(radio) {
        const that = this;

        if (radio == "1") {
          that.checkStatus.isCheck = 1;
        } else {
          that.checkStatus.isCheck = 2;
        }
      },
      check(){
        const that = this;
       let id = ls.getItem('schoolId');
       if(!that.checkStatus.checkReason){
         that.checkStatus.checkReason=''
       }
        that.checkStatus.id = window.localStorage.getItem("schoolId");
        this.$confirm('确定操作吗?', '修改状态', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          schoolApi.schoolCheck(that.checkStatus).then(res => {
            that.$message.success(res.message)
            this.$router.go(-1);//返回上一层
          })
        })
      }
    }
  }
</script>

<style>
  @media (max-width:767px) {
	  .el-message-box{
		width: 80%!important;
	  }
  } 	
</style>
