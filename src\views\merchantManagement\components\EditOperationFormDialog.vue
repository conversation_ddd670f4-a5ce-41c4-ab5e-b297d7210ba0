<template>
  <div>
    <el-dialog title="修改所属俱乐部" center :visible.sync="dialogVisible" width="700px" @close="handleOuterClose" :close-on-press-escape="false" :close-on-click-modal="false">
      <el-form ref="channerManagerRef" :label-position="labelPosition" :model="form" label-width="180px" :rules="rules">
        <el-form-item label="门店名称" prop="merchantName">
          <el-input disabled v-model="form.merchantName"></el-input>
        </el-form-item>
        <el-form-item label="原所属俱乐部" prop="operationsNam">
          <el-input disabled v-model="form.operationsName"></el-input>
        </el-form-item>
        <el-form-item label="原推广大使" prop="refereeName">
          <el-input disabled v-model="form.refereeName"></el-input>
        </el-form-item>
        <el-form-item label="新所属俱乐部编号" prop="newOperationCode">
          <el-input v-model.trim="form.newOperationCode" placeholder="请输入新所属俱乐部编号" maxlength="30" show-word-limit @blur="handeBlur"></el-input>
        </el-form-item>
        <el-form-item label="新所属俱乐部名称" prop="newOperationsName">
          <el-input disabled v-model="form.newOperationsName"></el-input>
        </el-form-item>
        <el-form-item label="新推广大使编号" prop="newReferenceCode">
          <el-input v-model.trim="form.newReferenceCode" placeholder="请输入新推广大使编号" maxlength="30" show-word-limit @blur="handleRefereeCodeBlur"></el-input>
        </el-form-item>
        <el-form-item label="新推广大使名称" prop="newRefereeName">
          <el-input disabled v-model="form.newRefereeName"></el-input>
        </el-form-item>
        <div style="color: red; text-align: center; margin-bottom: 20px">提示：新推广大使需要为新所属俱乐部下已开通的门店或新所属俱乐部编号</div>
        <div class="dialog-footer">
          <el-button @click="handleOuterClose">取 消</el-button>
          <el-button :loading="loading" type="primary" @click="handleConfirm">确 定</el-button>
        </div>
      </el-form>
    </el-dialog>

    <!--    无法修改原因弹窗-->
    <InabilityReason
      v-if="dialogReasonVisible"
      :dialogReasonVisible.sync="dialogReasonVisible"
      :showTitleStatus="1"
      :reasonContent="reasonContent"
      :workStep="workStep"
      :reasonType="reasonType"
      :isSubmitLoading="isSubmitLoading"
      @handleSubmit="handleConSubmit"
      @handleCloseDialog="handleOuterClose"
    />
  </div>
</template>

<script>
  import InabilityReason from '@/views/merchantManagement/components/InabilityReason.vue';
  import schoolApi from '@/api/schoolList';

  export default {
    name: 'EditOperationFormDialog',
    components: {
      InabilityReason
    },
    props: {
      isShowDialog: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        form: {
          merchantName: '', //门店名称
          operationsName: '', //原所属俱乐部名称
          refereeName: '', //推广大使名称
          refereeCode: '', //推广大使编号
          newReferenceCode: '', //新推广大使编号
          newRefereeName: '', //新推广大使名称
          newOperationsName: '', //新俱乐部名称
          newOperationCode: '' //新俱乐部编号
        },
        merchantCode: '',
        merchantId: '',
        loading: false,
        labelPosition: 'left',
        rules: {
          channelManagerName: [{ required: true, message: '渠道经理姓名不为空', trigger: ['blur', 'change'] }],
          mobilePhone: [{ required: true, message: '渠道经理手机号不为空', trigger: ['blur', 'change'] }],
          newOperationCode: [{ required: true, message: '新所属俱乐部编号不为空', trigger: ['blur', 'change'] }],
          newReferenceCode: [{ required: true, message: '新推广大使编号不为空', trigger: ['blur', 'change'] }],
          newRefereeName: [{ required: true, message: '新推广大使名称不为空', trigger: ['blur', 'change'] }],
          newOperationsName: [{ required: true, message: '新所属俱乐部名称不为空', trigger: ['blur', 'change'] }]
        },
        dialogReasonVisible: false,
        reasonContent: [],
        changeUnableDialogVisible: true,
        workStep: 1,
        isSubmitLoading: false,
        reasonType: 0
      };
    },
    computed: {
      dialogVisible: {
        get() {
          return this.isShowDialog;
        },
        set(val) {
          this.$emit('update:isShowCityDialog', val);
        }
      }
    },

    methods: {
      async handeBlur() {
        if (this.form.newOperationCode) {
          try {
            const res = await schoolApi.getMerchantNameApi({ merchantCode: this.form.newOperationCode });
            this.$set(this.form, 'newOperationsName', res.data);
            console.log(`🚀🥶💩🚀~ handeBlur ~ this.form ~ L112: `, this.form);
            console.log(`🚀🥶💩🚀~ handeBlur ~ res.data ~ L112: `, res.data);
          } catch (error) {
            console.log(`🚀🥶💩🚀~ handeBlur ~ e ~ L115: `, error);
            this.$set(this.form, 'newOperationsName', '');
          }
        }
      },
      async handleRefereeCodeBlur() {
        if (this.form.newReferenceCode) {
          try {
            const res = await schoolApi.getMerchantNameApi({ merchantCode: this.form.newReferenceCode });
            this.$set(this.form, 'newRefereeName', res.data);
          } catch (error) {
            console.log(`🚀🥶💩🚀~ handeBlur ~ e ~ L115: `, error);
            this.$set(this.form, 'newRefereeName', '');
          }
        }
      },

      handleOuterClose(val) {
        if (val === 'closeDialog') {
          this.dialogReasonVisible = false;
          return;
        }
        this.reset();
        this.$emit('handleCancel');
      },

      async handleConfirm() {
        if (this.loading) return;
        try {
          await this.$refs.channerManagerRef.validate();
          this.loading = true;
          let params = {
            merchantCode: this.form.merchantCode,
            newOperationCode: this.form.newOperationCode, //新俱乐部编号
            newReferenceCode: this.form.newReferenceCode //新推广大使编号
          };
          // this.loading = false;
          const res = await schoolApi.checkNewClubAndReferenceApi(params);
          if (!res.data.canChange) {
            console.log('123123🚀🥶💩~ this.loading', this.loading);
            this.workStep = 2;
            this.dialogReasonVisible = true;
            this.reasonContent = res.data.reasons || [];
            return;
          }
          this.dialogReasonVisible = true;
          this.workStep = 4;
          this.reasonType = 1;
        } catch (error) {
          console.log('🚀 ~ handleConfirm ~ error:', error);
          // this.$message.error('操作失败');
        } finally {
          this.loading = false;
        }
      },
      /**
       * 提交
       * @returns
       */
      async handleConSubmit() {
        if (this.loading) return;
        this.loading = true;
        try {
          // await this.$refs.channerManagerRef.validate();
          this.isSubmitLoading = true;
          let params = {
            merchantCode: this.form.merchantCode,
            newOperationCode: this.form.newOperationCode, //新俱乐部编号
            newReferenceCode: this.form.newReferenceCode //新推广大使编号
          };
          console.log(`🚀🥶💩🚀~  ~ params ~ L149: `, params);
          this.dialogReasonVisible = true;
          const res = await schoolApi.confirmChangeClubApi(params);
          if (res.code === 20000) {
            this.$message.success('操作成功');
            this.loading = false;
            this.isSubmitLoading = false;
            this.dialogReasonVisible = false;
            this.reset();
            this.$emit('handleCancel', true);
          }
        } catch (error) {
          console.log('🚀 ~ handleConfirm ~ error:', error);
          this.loading = false;
          this.isSubmitLoading = false;
        } finally {
          this.loading = false;
          this.isSubmitLoading = false;
        }
      },

      setData(data) {
        console.log(`🚀🥶💩🚀~ setData ~ data ~ L197: `, data);
        Object.assign(this.form, data);
      },

      reset() {
        this.form = {
          mobilePhone: '',
          merchantName: ''
        };
        this.$refs.channerManagerRef.resetFields();
      }
    }
  };
</script>

<style scoped>
  .dialog-footer {
    text-align: center;
  }
</style>
