import request from "@/utils/request";

export default{
  // 听力课程分页
  listListeningCourse(query) {
    return request({
      url: "/dyf/listeningCourse/list",
      method: "get",
      params: query,
    });
  },

// 听力课程编辑回显
findListeningCourse(query) {
  return request({
    url: "/dyf/listeningCourse/find",
    method: "get",
    params: query,
  });
},

// 新增课程
 addListeningCourse(query) {
  return request({
    url: "/dyf/listeningCourse/add",
    method: "put",
    data: query,
  });
},

// 听力课程编辑
 editListeningCourse(data) {
  return request({
    url: "/dyf/listeningCourse/edit",
    method: "post",
    data
  });
},

// 听力课程 开通/暂停 状态修改
 statusListeningCourse(id,status) {
  return request({
    url: '/dyf/listeningCourse/edit/status?id=' + id + '&status=' + status,
    method: 'POST'
  })
},
/**************************************  门店开通课程接口  ***********************************/
// 查询可开通的听力课程
getListeningCourseOpen(query) {
  return request({
    url: "/dyf/listeningCourseOpen/list",
    method: "get",
    params: query,
  });
},
// 开通听力课程
openCourse(data) {
  return request({
    url: '/dyf/listeningCourseOpen/open',
    method: 'POST',
    data
  })
},
}

