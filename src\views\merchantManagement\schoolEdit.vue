<template>
  <div class="app-container container-card">
    <el-row>
      <el-col :xs="24" :lg="18">
        <el-form
          :ref="addOrUpdate ? 'addMarketDate' : 'updateMarketDate'"
          :rules="rules"
          :model="addOrUpdate ? addMarketDate : updateMarketDate"
          label-position="right"
          label-width="140px"
          style="width: 100%"
        >
          <el-form-item label="登录账号：" prop="name">
            <el-col :xs="24" :span="18">
              <el-input
                v-if="updateMarketDate"
                readonly="readonly"
                v-model="updateMarketDate.name"
                disabled
              />
            </el-col>
            <el-col :span="6">
              <el-button
                type="success"
                style="margin-left: 20px"
                @click="openLogin"
                >修改登录账号
              </el-button>
            </el-col>
          </el-form-item>
          <el-form-item label="门店名称：" prop="merchantName">
            <el-col :xs="24" :span="18">
              <el-input
                v-if="updateMarketDate"
                v-model="updateMarketDate.merchantName"
                placeholder="请输入：市+商圈/路名/学校名称+门店"
              />
              <span>（例：合肥市祥源广场门店）</span>
            </el-col>
            <!-- <el-col :span="5" :offset="1">合肥市祥源广场门店</el-col>  -->
          </el-form-item>
          <el-form-item label="负责人：" prop="realName">
            <el-col :xs="24" :span="18">
              <el-input
                v-if="updateMarketDate"
                v-model="updateMarketDate.realName"
              />
            </el-col>
          </el-form-item>
          <el-form-item label="身份证：" prop="idCard">
            <el-col :xs="24" :span="18">
              <el-input
                v-if="updateMarketDate"
                v-model="updateMarketDate.idCard"
              />
            </el-col>
          </el-form-item>
          <el-form-item
            v-if="checkPermission(['admin'])"
            label="上级编号："
            prop="refereeCode"
          >
            <el-col :xs="24" :span="18">
              <el-input
                v-if="updateMarketDate"
                v-model="updateMarketDate.refereeCode"
              />
            </el-col>
          </el-form-item>
          <el-form-item
            v-if="checkPermission(['admin'])"
            label="所属俱乐部编号："
            prop="operationsCode"
          >
            <el-col :xs="24" :span="18">
              <el-input
                v-if="updateMarketDate"
                v-model="updateMarketDate.operationsCode"
              />
            </el-col>
          </el-form-item>
          <el-form-item label="是否教学交付能力：" prop="canTeachNum">
            <template>
              <el-radio v-model="radio" label="1" @change="change(radio)"
                >开通
              </el-radio>
              <el-radio v-model="radio" label="0" @change="change(radio)"
                >暂停
              </el-radio>
            </template>
          </el-form-item>
          <el-form-item label="签约时间：" prop="">
            <el-col :xs="24" :span="18">
              <el-date-picker
                v-model="updateMarketDate.signupDate"
                value-format="yyyy-MM-dd HH:mm:ss"
                type="datetime"
                placeholder="选择日期"
              >
              </el-date-picker>
            </el-col>
          </el-form-item>
          <el-form-item label="到期时间：" prop="">
            <el-col :xs="24" :span="18">
              <el-date-picker
                v-model="updateMarketDate.expireDate"
                value-format="yyyy-MM-dd HH:mm:ss"
                type="datetime"
                placeholder="选择日期"
              >
              </el-date-picker>
            </el-col>
          </el-form-item>
          <el-form-item label="合同照片:">
            <el-col :span="20">
              <el-upload
                ref="clearupload"
                v-loading="uploadLoading"
                list-type="picture-card"
                action=""
                element-loading-text="图片上传中"
                :limit="10"
                :on-exceed="justPictureNum"
                :file-list="
                  !addOrUpdate
                    ? fileContractDetailList
                    : fileContractDetailList.name
                "
                :http-request="uploadDetailHttpContract"
                :on-preview="handlePictureCardPreviewContract"
                :on-remove="handleRemoveDetailContract"
              >
                <i class="el-icon-plus" />
              </el-upload>
            </el-col>
            <el-col :xs="24" :span="4">(*支持多张)</el-col>
          </el-form-item>
          <el-form-item label="证件照片:">
            <el-col :span="20">
              <el-upload
                ref="clearupload"
                v-loading="uploadLoadingIdCard"
                list-type="picture-card"
                action=""
                element-loading-text="图片上传中"
                :limit="10"
                :on-exceed="justPictureNum"
                :file-list="
                  !addOrUpdate
                    ? fileIdCardDetailList
                    : fileIdCardDetailList.name
                "
                :http-request="uploadDetailHttpIdCard"
                :on-preview="handlePictureCardPreviewIdCard"
                :on-remove="handleRemoveDetailIdCard"
              >
                <i class="el-icon-plus" />
              </el-upload>
            </el-col>
            <el-col :xs="24" :span="4">(*支持多张)</el-col>
          </el-form-item>
          <el-form-item label="门店环境照片:">
            <el-col :span="20">
              <el-upload
                ref="clearupload"
                v-loading="uploadLoadingShop"
                list-type="picture-card"
                action=""
                element-loading-text="图片上传中"
                :limit="10"
                :on-exceed="justPictureNum"
                :file-list="
                  !addOrUpdate ? fileShopDetailList : fileShopDetailList.name
                "
                :http-request="uploadDetailHttpShop"
                :on-preview="handlePictureCardPreviewShop"
                :on-remove="handleRemoveDetailShop"
              >
                <i class="el-icon-plus" />
              </el-upload>
            </el-col>
            <el-col :xs="24" :span="4">(*支持多张)</el-col>
          </el-form-item>
          <el-form-item label="所在地区：">
            <el-col :xs="24" :span="18">
              <el-cascader
                style="width: 300px"
                :options="regionData"
                v-model="selectedOptions"
                :props="{ value: 'label' }"
              >
              </el-cascader>
              <!-- <el-row :gutter="10">
                <el-col :xs="24" :span="8">
                  <el-input
                    v-if="updateMarketDate"
                    v-model="updateMarketDate.province"
                  />
                </el-col>
                <el-col :xs="24" :span="8">
                  <el-input
                    v-if="updateMarketDate"
                    v-model="updateMarketDate.city"
                  />
                </el-col>
                <el-col :xs="24" :span="8">
                  <el-input
                    v-if="updateMarketDate"
                    v-model="updateMarketDate.area"
                  />
                </el-col>
              </el-row> -->
            </el-col>
          </el-form-item>
          <el-form-item label="地址：" prop="address">
            <el-col :xs="24" :span="12">
              <el-input
                v-if="updateMarketDate"
                v-model="updateMarketDate.address"
              />
            </el-col>
            <el-col :xs="24" :span="11" :offset="1">
              <!-- <el-button type="success" size="mini">确定</el-button>
          <el-button type="info" size="mini" @click="clearAddress()">清空</el-button> -->
              <span class="green">(请在此输入详细地址！)</span>
            </el-col>
          </el-form-item>
          <!-- <el-form-item label="地图标记" prop="isEnable">
            <div class="amap-page-container">
              <div :style="{ width: '100%', height: '450px' }" class="map-box">
                <el-amap-search-box
                  class="search-box"
                  :search-option="searchOption"
                  :on-search-result="onSearchResult"
                ></el-amap-search-box>
                <el-amap
                  vid="amap"
                  :plugin="plugin"
                  :center="center"
                  class="amap-demo"
                  :events="events"
                >
                  <el-amap-marker v-for="(marker, index) in markers" :position="marker"
                                  :key="index + '-only'"></el-amap-marker>
               
                  <el-amap-marker
                    vid="component-marker"
                    :position="center"
                  ></el-amap-marker>
                 
                  <el-amap-marker
                    v-for="(marker,index) in markers2"
                    :key="index"
                    :position="marker.position"
                    :events="marker.events"
                  ></el-amap-marker>
                  <el-amap-info-window
                    v-if="window"
                    :position="window.position"
                    :visible="window.visible"
                    :content="window.content"
                  ></el-amap-info-window>
                </el-amap>
              
                <div class="result" v-if="result != ''">
                  <el-table
                    class="search-table"
                    :data="
                      result.slice(
                        (this.pageNum - 1) * this.pageSize,
                        this.pageNum * this.pageSize
                      )
                    "
                    style="margin-bottom: 20px"
                  >
                    <el-table-column prop="name,address">
                      <template slot-scope="scope">
                        <div
                          class="result-list"
                          @click="
                            markList(scope.row.lng, scope.row.lat, scope.$index)
                          "
                          :class="[
                            currentResult == scope.$index ? 'active' : '',
                          ]"
                        >
                          <label>{{ scope.$index + 1 }}</label>
                          <div class="list-right">
                            <div class="name">{{ scope.row.name }}</div>
                            <div class="address">{{ scope.row.address }}</div>
                          </div>
                        </div>
                      </template>
                    </el-table-column>
                  </el-table>
                  <el-pagination
                    background
                    :current-page.sync="pageNum"
                    :page-size="pageSize"
                    layout="total,prev, pager, next"
                    :total="result.length"
                    @current-change="handleCurrentChange"
                    @size-change="changeSizeHandler"
                    size="small"
                    style="text-align: right"
                  >
                  </el-pagination>
                </div>
              </div>
            </div>
          </el-form-item>
          <el-form-item label="所在经度" prop="longitude">
            <el-col :span="7">
              <el-input disabled v-if="addOrUpdate" v-model="addMarketDate.longitude"/>
              <el-input disabled
                v-if="!addOrUpdate"
                v-model="updateMarketDate.longitude"
              />
            </el-col>
          </el-form-item>
          <el-form-item label="所在维度" prop="latitude" disabled>
            <el-col :span="7">
              <el-input disabled v-if="addOrUpdate" v-model="addMarketDate.latitude"/>
              <el-input disabled
                v-if="!addOrUpdate"
                v-model="updateMarketDate.latitude"
              />
            </el-col>
          </el-form-item> -->
          <el-form-item label="开户金额" prop="openMoney">
            <el-col :xs="24" :span="18">
              <el-input v-if="addOrUpdate" v-model="addMarketDate.openMoney" />
              <el-input
                v-if="!addOrUpdate"
                v-model="updateMarketDate.openMoney"
              />
            </el-col>
          </el-form-item>
          <el-form-item label="门店简介：" prop="name">
            <el-input
              type="textarea"
              resize="none"
              :rows="4"
              v-if="!addOrUpdate"
              v-model="updateMarketDate.description"
            />
          </el-form-item>
        </el-form>
      </el-col>
    </el-row>

    <div slot="footer" class="dialog-footer" style="margin: 30px 0 30px 140px">
      <el-button
        v-if="updateMarketDate && merchantCode == 'A0001'"
        size="mini"
        type="primary"
        @click="updateActiveFun('updateMarketDate')"
        >修改
      </el-button>
      <el-button size="mini" @click="close">关闭</el-button>
    </div>
    <!-- 修改登陆账号弹窗 -->
    <el-dialog
      title="修改登录账号"
      :visible.sync="showLoginAccount"
      width="30%"
      :close-on-click-modal="false"
      @close="closeLogin"
    >
      <el-form
        :ref="addOrUpdate ? 'addMarketDate' : 'updateLoginName'"
        :rules="ruless"
        :model="addOrUpdate ? addMarketDate : updateLoginName"
        label-position="left"
        label-width="120px"
        style="width: 80%"
      >
        <el-form-item label="原登录账号：" prop="name">
          <!-- <el-input v-if="updateLoginName" v-model="updateLoginName.name" /> -->
          <span>{{ updateLoginName.name }} </span>
        </el-form-item>
        <el-form-item label="新登录账号：" prop="newName">
          <el-input v-model="updateLoginName.newName" />
        </el-form-item>
        <el-form-item v-show="false">
          <el-input v-model="updateLoginName.id"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button
          size="mini"
          type="primary"
          @click="updateSchoolLogin('updateLoginName')"
          >确定
        </el-button>
        <el-button size="mini" @click="closeLogin">关闭</el-button>
      </div>
    </el-dialog>

    <el-dialog
      :visible.sync="dialogUploadVisible"
      :close-on-click-modal="false"
    >
      <img width="100%" :src="dialogImageUrl" alt />
    </el-dialog>
  </div>
</template>

<script>
import schoolApi from "@/api/schoolList";
import areaSchoolApi from "@/api/areasSchoolList";
import { ossPrClient } from "@/api/alibaba";
import ls from "@/api/sessionStorage";
import { idCard, isvalidPhone } from "@/utils/validate";
import checkPermission from "@/utils/permission";
import { regionData } from "element-china-area-data";
export default {
  data() {
    //手机号验证
    var validPhone = (rule, value, callback) => {
      if (!value) {
        callback(new Error("请输入电话号码"));
      } else if (!isvalidPhone(value)) {
        callback(new Error("请输入正确的11位手机号码"));
      } else {
        callback();
      }
    };
    //身份证表达式
    var isIdCard = (rule, value, callback) => {
      if (!value) {
        callback(new Error("请输入身份证号码"));
      } else if (!idCard(value)) {
        callback(new Error("请输入正确的18位身份证号"));
      } else {
        callback();
      }
    };
    const self = this;
    return {
      // 地图搜索分页相关参数
      pageNum: 1,
      pageSize: 5,
      result: [],
      currentResult: -1,
      regionData,
      selectedOptions: [],
      addOrUpdate: false,
      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null,
      },
      merchantCode: "",
      ruless: {
        newName: [
          {
            required: true,
            message: "必填",
            trigger: "blur",
          },
          {
            validator: validPhone,
            trigger: "blur",
          },
        ],
      },
      rules: {
        name: [
          {
            required: true,
            message: "必填",
            trigger: "blur",
          },
        ],
        merchantName: [
          {
            required: true,
            message: "必填",
            trigger: "blur",
          },
        ],
        signupDate: [
          {
            required: true,
            message: "必填",
            trigger: "blur",
          },
        ],
        realName: [
          {
            required: true,
            message: "必填",
            trigger: "blur",
          },
        ],
        idCard: [
          {
            required: true,
            message: "必填",
            trigger: "change",
          },
        ],
        refereeCode: [
          {
            required: true,
            message: "必填",
            trigger: "change",
          },
        ],
        // marketPartner: [{
        //   required: true,
        //   message: '必填',
        //   trigger: 'change'
        // }],
        openBank: [
          {
            required: true,
            message: "必填",
            trigger: "change",
          },
        ],
        bankName: [
          {
            required: true,
            message: "必填",
            trigger: "change",
          },
        ],
        openMoney: [
          {
            required: true,
            message: "必填",
            trigger: "change",
          },
        ],
        Rank: [
          {
            required: true,
            message: "必填",
            trigger: "change",
          },
        ],
        merchantType: [
          {
            required: true,
            message: "必填",
            trigger: "change",
          },
        ],
        areaCoverRange: [
          {
            required: true,
            message: "必填",
            trigger: "change",
          },
        ],

        expireDate: [
          {
            required: true,
            message: "必填",
            trigger: "change",
          },
        ],

        contractPhoto: [
          {
            required: true,
            message: "必填",
            trigger: "change",
          },
        ],
        idCardPhoto: [
          {
            required: true,
            message: "必填",
            trigger: "change",
          },
        ],
        shopPhoto: [
          {
            required: true,
            message: "必填",
            trigger: "change",
          },
        ],
        province: [
          {
            required: true,
            message: "必填",
            trigger: "change",
          },
        ],
        city: [
          {
            required: true,
            message: "必填",
            trigger: "change",
          },
        ],
        area: [
          {
            required: true,
            message: "必填",
            trigger: "change",
          },
        ],
        address: [
          {
            required: true,
            message: "必填",
            trigger: "change",
          },
        ],
        description: [
          {
            required: true,
            message: "必填",
            trigger: "change",
          },
        ],
      },
      tableData: [],
      merhcantCode: "",
      dataQuery: {},
      dialogVisible: false,
      updateMarketDate: {},
      addMarketDate: {},
      showLoginAccount: false,
      radio: "0", //单选框状态 值必须是字符串
      fileListPending: [], // 待处理已上传图片信息

      fileDetailListPending: [], // 待处理已上传图片信息
      fileContractDetailList: [], // 上传图片已有图片列表
      fileIdCardDetailList: [],
      fileShopDetailList: [],

      dialogUploadVisible: false,
      uploadLoading: false, // 上传图片加载按钮
      dialogImageUrl: "", // 上传图片预览
      fileList: [], // 上传图片已有图片列表
      uploadLoadingIdCard: false,
      uploadLoadingShop: false,
      updateLoginName: {},
      refereeCodeList: [],

      //地图开始
      markers: [],
      //搜索结果标注
      markers2: [],
      windows: [],
      window: "",
      searchOption: {
        city: "全国",
        citylimit: false,
      },
      center: [0, 0],
      zoom: 12,
      lng: 0,
      lat: 0,
      address: "",
      province: "",
      city: "",
      district: "",
      loaded: false,
      circles: [],
      events: {
        click(e) {
          let { lng, lat } = e.lnglat;
          self.lng = lng;
          self.lat = lat;
          // 这里通过高德 SDK 完成。
          var geocoder = new AMap.Geocoder({
            radius: 1000,
            extensions: "all",
          });
          self.showWindow = false;
          geocoder.getAddress(
            [e.lnglat.lng, e.lnglat.lat],
            function (status, result) {
              if (status === "complete" && result.info === "OK") {
                if (result && result.regeocode) {
                  let city = null;
                  if (result.regeocode.addressComponent.city == "") {
                    city = result.regeocode.addressComponent.district;
                  } else {
                    if (
                      result.regeocode.addressComponent.province == "重庆市" ||
                      result.regeocode.addressComponent.province == "天津市" ||
                      result.regeocode.addressComponent.province == "北京市" ||
                      result.regeocode.addressComponent.province == "上海市"
                    ) {
                      city = result.regeocode.addressComponent.province;
                    } else {
                      city = result.regeocode.addressComponent.city;
                    }
                  }
                  self.markers = [[lng, lat]];
                  self.updateMarketDate.latitude = lat;
                  self.updateMarketDate.longitude = lng;
                  self.updateMarketDate.address =
                    result.regeocode.formattedAddress;
                  self.updateMarketDate.province =
                    result.regeocode.addressComponent.province;
                  self.updateMarketDate.city = city;
                  self.updateMarketDate.area =
                    result.regeocode.addressComponent.district;
                  self.$nextTick();
                }
              } else {
                alert("地址获取失败");
              }
            }
          );
        },
      },
      plugin: [
        {
          enableHighAccuracy: true, //是否使用高精度定位，默认:true
          timeout: 100, //超过10秒后停止定位，默认：无穷大
          maximumAge: 0, //定位结果缓存0毫秒，默认：0
          convert: true, //自动偏移坐标，偏移后的坐标为高德坐标，默认：true
          showButton: true, //显示定位按钮，默认：true
          buttonPosition: "RB", //定位按钮停靠位置，默认：'LB'，左下角
          showMarker: true, //定位成功后在定位到的位置显示点标记，默认：true
          showCircle: true, //定位成功后用圆圈表示定位精度范围，默认：true
          panToLocation: true, //定位成功后将定位到的位置作为地图中心点，默认：true
          zoomToAccuracy: true, //定位成功后调整地图视野范围使定位位置及精度范围视野内可见，默认：f
          extensions: "all",
          expandZoomRange: true,
          keyboardEnable: true,
          pName: "Geolocation",
          campus: [],
          events: {
            init(o) {
              // o 是高德地图定位插件实例
              o.getCurrentPosition((status, result) => {
                if (result && result.position) {
                  self.lng = result.position.lng;
                  self.lat = result.position.lat;
                  self.loaded = true;
                  self.$nextTick();
                }
              });
            },
          },
        },
      ],
    };
  },
  created() {
    //this.fetchData();
    this.clickAdd(ls.getItem("schoolId"));
    ossPrClient();
    this.getMerchantCode();
  },
  methods: {
    checkPermission,
    getMerchantCode() {
      areaSchoolApi.getCurrentAdmin().then((res) => {
        console.log(res.data.merchantCode + "wyy");
        this.roleTag = res.data.roleTag;
        this.merchantCode = res.data.merchantCode;
      });
    },
    // 关闭弹窗
    close() {
      const that = this;
      // that.$router.push({
      //   path: '/merchantManagement/schoolList'
      // })
      // 关闭当前标签页
      that.$store.dispatch("delVisitedViews", this.$route);
      that.$router.go(-1);
    },
    clearAddress() {
      const that = this;
      that.updateMarketDate.address = "";
    },
    schoolStatus(id, enable) {
      const that = this;
      this.$confirm("确定操作吗?", "修改状态", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        schoolApi.openEnable(id, enable).then((res) => {
          that.$nextTick(() => that.fetchData());
          that.$message.success("修改成功!");
        });
      });
    },
    // 状态改变事件
    change(radio) {
      if (radio == "1") {
        this.updateMarketDate.canTeach = true;
      } else {
        this.updateMarketDate.canTeach = false;
      }
    },
    //修改操作
    updateActiveFun(ele) {
      const that = this;
      if (that.fileContractDetailList.length <= 0) {
        that.$message.error("合同照片不能为空");
        return false;
      }
      if (that.fileIdCardDetailList.length <= 0) {
        that.$message.error("证件照片不能为空");
        return false;
      }
      if (!that.updateMarketDate.signupDate) {
        that.$message.error("签约时间不能为空");
        return false;
      }
      that.updateMarketDate.province = that.selectedOptions[0];
      if (that.selectedOptions[1] == "市辖区") {
        that.updateMarketDate.city = that.selectedOptions[0];
      } else {
        that.updateMarketDate.city = that.selectedOptions[1];
      }
      that.updateMarketDate.area = that.selectedOptions[2];
      if (
        that.updateMarketDate.city == "" ||
        that.updateMarketDate.province == "" ||
        that.updateMarketDate.area == ""
      ) {
        that.$message.error("省市区不能为空");
        return false;
      }
      that.updateMarketDate.contractPhoto = [];
      for (let i = 0; i < that.fileContractDetailList.length; i++) {
        let index = that.fileContractDetailList[i].url.lastIndexOf("manage");
        that.updateMarketDate.contractPhoto.push(
          that.fileContractDetailList[i].url.substring(
            index,
            that.fileContractDetailList[i].url.length
          )
        );
      }
      that.updateMarketDate.idCardPhoto = [];

      for (var i = 0; i < that.fileIdCardDetailList.length; i++) {
        let idCardIndex =
          that.fileIdCardDetailList[i].url.lastIndexOf("manage");
        that.updateMarketDate.idCardPhoto.push(
          that.fileIdCardDetailList[i].url.substring(
            idCardIndex,
            that.fileIdCardDetailList[i].url.length
          )
        );
      }
      that.updateMarketDate.shopPhoto = [];
      for (var i = 0; i < that.fileShopDetailList.length; i++) {
        let shopindex = that.fileShopDetailList[i].url.lastIndexOf("manage");
        that.updateMarketDate.shopPhoto.push(
          that.fileShopDetailList[i].url.substring(
            shopindex,
            that.fileShopDetailList[i].url.length
          )
        );
      }
      this.$confirm("确定操作吗?", "修改校区", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        that.$refs[ele].validate((valid) => {
          // 表单验证
          if (valid) {
            const loading = this.$loading({
              lock: true,
              text: "修改校区",
              spinner: "el-icon-loading",
              background: "rgba(0, 0, 0, 0.7)",
            });
            schoolApi
              .addSchool(that.updateMarketDate)
              .then((res) => {
                if (!res.success) {
                  that.$message.error(res.message);
                  loading.close();
                  return;
                }
                loading.close();
                that.$nextTick(() => that.fetchData());
                that.$message.success(res.message);
                this.$router.go(-1); //返回上一层
              })
              .catch((err) => {
                loading.close();
              });
          } else {
            console.log("error submit!!");
            //loading.close();
            return false;
          }
        });
      });
    },
    // 修改回显
    clickAdd(id) {
      this.dialogVisible = true;
      const that = this;
      that.fileContractDetailList = [];
      that.fileIdCardDetailList = [];
      that.fileShopDetailList = [];
      schoolApi.schoolDetail(id).then((res) => {
        that.updateMarketDate = res.data;
        that.radio = res.data.canteachNum;
        that.updateLoginName.name = res.data.name;
        that.updateLoginName.id = res.data.id;
        this.selectedOptions = [
          res.data.province,
          res.data.city,
          res.data.area,
        ];
        if (this.selectedOptions[0] == this.selectedOptions[1]) {
          this.selectedOptions[1] = "市辖区";
        }
        if (that.updateMarketDate.latitude && that.updateMarketDate.longitude) {
          that.lat = that.updateMarketDate.latitude;
          that.lng = that.updateMarketDate.longitude;
          that.center = [that.lng, that.lat];
          that.markers = [[that.lng, that.lat]];
        }
        that.dataQuery.merchantCode = res.data.refereeCode;
        if (that.updateMarketDate.contractPhoto) {
          for (let i = 0; i < that.updateMarketDate.contractPhoto.length; i++) {
            that.fileContractDetailList.push({
              url: that.aliUrl + that.updateMarketDate.contractPhoto[i],
            });
          }
        } else {
          that.fileContractDetailList = [];
        }
        if (that.updateMarketDate.idCardPhoto) {
          for (let i = 0; i < that.updateMarketDate.idCardPhoto.length; i++) {
            that.fileIdCardDetailList.push({
              url: that.aliUrl + that.updateMarketDate.idCardPhoto[i],
            });
          }
        } else {
          that.fileIdCardDetailList = [];
        }
        if (that.updateMarketDate.shopPhoto) {
          for (let i = 0; i < that.updateMarketDate.shopPhoto.length; i++) {
            that.fileShopDetailList.push({
              url: that.aliUrl + that.updateMarketDate.shopPhoto[i],
            });
          }
        } else {
          that.fileShopDetailList = [];
        }
      });
    },

    onSearchResult(pois) {
      let latSum = 0;
      let lngSum = 0;
      if (pois.length > 0) {
        pois.forEach((poi) => {
          let { lng, lat } = poi;
          lngSum += lng;
          latSum += lat;
          this.markers.push([poi.lng, poi.lat]);
        });
        let center = {
          lng: lngSum / pois.length,
          lat: latSum / pois.length,
        };
        this.center = [center.lng, center.lat];
      }
    },
    addMarker: function () {
      let lng = 121.5 + Math.round(Math.random() * 1000) / 10000;
      let lat = 31.197646 + Math.round(Math.random() * 500) / 10000;
      this.markers.push([lng, lat]);
    },
    // 打开修改登陆账号
    openLogin() {
      this.showLoginAccount = true;
      this.updateLoginName.id = this.updateMarketDate.id;
    },
    closeLogin() {
      this.showLoginAccount = false;
    },
    updateSchoolLogin(ele) {
      const that = this;
      that.$refs[ele].validate((valid) => {
        if (valid) {
          that.updateLoginName.name = that.updateLoginName.newName;
          schoolApi.schoolUpdateLogin(that.updateLoginName).then(() => {
            that.showLoginAccount = false;
            that.$nextTick(() => that.fetchData());
            that.$message.success("修改登录账号成功");
          });
        }
      });
    },

    //--合同照片开始
    // 删除上传图片
    handleRemoveDetailContract(file, fileList) {
      const that = this;
      if (!that.addOrUpdate) {
        that.fileContractDetailList = fileList;
      } else {
        for (let a = 0; a < that.fileDetailListPending.length; a++) {
          that.fileDetailListPending[a].uid === file.uid
            ? that.fileContractDetailList.splice(a, 1)
            : "";
        }
      }
    },
    // 上传图片预览
    handlePictureCardPreviewContract(file) {
      this.dialogImageUrl = file.url;
      this.dialogUploadVisible = true;
    },
    // 上传图片数量超限
    justPictureNum(file, fileList) {
      this.$message.warning(`当前限制选择1个文件`);
    },

    uploadDetailHttpContract({ file }) {
      this.uploadLoading = true;
      const that = this;
      const fileName = "manage/" + Date.parse(new Date());
      that.$nextTick(function () {
        ossPrClient()
          .put(fileName, file)
          .then(({ res, url, name }) => {
            if (res && res.status === 200) {
              console.log(`阿里云OSS上传图片成功回调1`, res, url, name);
              if (!that.addOrUpdate) {
                that.fileContractDetailList.push({
                  uid: file.uid,
                  url: url,
                });
              } else {
                // 新增上传图片
                that.fileContractDetailList.push({
                  name,
                });
                that.updateMarketDate.contractPhoto = name;
                that.uploadLoading = false;
              }
              that.$nextTick(() => {
                that.uploadLoading = false;
              });
            }
          })
          .catch((err) => {
            that.$message.error("上传图片失败请检查网络或者刷新页面");
            console.log(`阿里云OSS上传图片失败回调`, err);
            that.uploadLoading = false;
          });
      });
    },
    //---合同上传图片结束

    //--身份证照片开始
    // 删除上传图片
    handleRemoveDetailIdCard(file, fileList) {
      const that = this;
      if (!that.addOrUpdate) {
        that.fileIdCardDetailList = fileList;
      } else {
        for (let a = 0; a < that.fileDetailListPending.length; a++) {
          that.fileDetailListPending[a].uid === file.uid
            ? that.fileIdCardDetailList.splice(a, 1)
            : "";
        }
      }
    },
    // 上传图片预览
    handlePictureCardPreviewIdCard(file) {
      this.dialogImageUrl = file.url;
      this.dialogUploadVisible = true;
    },

    uploadDetailHttpIdCard({ file }) {
      this.uploadLoadingIdCard = true;
      const that = this;
      const fileName = "manage/" + Date.parse(new Date());
      that.$nextTick(function () {
        ossPrClient()
          .put(fileName, file)
          .then(({ res, url, name }) => {
            if (res && res.status === 200) {
              console.log(`阿里云OSS上传图片成功回调1`, res, url, name);
              if (!that.addOrUpdate) {
                that.fileIdCardDetailList.push({
                  uid: file.uid,
                  url: url,
                });
              } else {
                // 新增上传图片
                that.fileIdCardDetailList.push({
                  name,
                });
                that.updateMarketDate.idCardPhoto = name;
                that.uploadLoadingIdCard = false;
              }
              that.$nextTick(() => {
                that.uploadLoadingIdCard = false;
              });
            }
          })
          .catch((err) => {
            that.$message.error("上传图片失败请检查网络或者刷新页面");
            console.log(`阿里云OSS上传图片失败回调`, err);
          });
      });
    },
    //---身份证上传图片结束

    //--校区照片开始
    // 删除上传图片
    handleRemoveDetailShop(file, fileList) {
      const that = this;
      if (!that.addOrUpdate) {
        that.fileIdCardDetailList = fileList;
      } else {
        for (let a = 0; a < that.fileDetailListPending.length; a++) {
          that.fileDetailListPending[a].uid === file.uid
            ? that.fileShopDetailList.splice(a, 1)
            : "";
        }
      }
    },
    // 上传图片预览
    handlePictureCardPreviewShop(file) {
      this.dialogImageUrl = file.url;
      this.dialogUploadVisible = true;
    },

    uploadDetailHttpShop({ file }) {
      this.uploadLoadingShop = true;
      const that = this;
      const fileName = "manage/" + Date.parse(new Date());
      that.$nextTick(function () {
        ossPrClient()
          .put(fileName, file)
          .then(({ res, url, name }) => {
            if (res && res.status === 200) {
              console.log(`阿里云OSS上传图片成功回调1`, res, url, name);
              if (!that.addOrUpdate) {
                that.fileShopDetailList.push({
                  uid: file.uid,
                  url: url,
                });
              } else {
                // 新增上传图片
                that.fileShopDetailList.push({
                  name,
                });
                that.updateMarketDate.shopPhoto = name;
                that.uploadLoadingShop = false;
              }
              that.$nextTick(() => {
                that.uploadLoadingShop = false;
              });
            }
          })
          .catch((err) => {
            that.$message.error("上传图片失败请检查网络或者刷新页面");
            console.log(`阿里云OSS上传图片失败回调`, err);
          });
      });
    },
    // 标注列表
    markList(lng, lat, index) {
      if (this.currentResult != index) {
        this.currentResult = index;
      } else {
        this.currentResult = -1;
      }
      this.getMarkAddress(lng, lat, index);
    },
    changeSizeHandler(size) {
      this.pageSize = size;
    },
    handleCurrentChange(currentPage) {
      this.pageNum = currentPage;
    },
  },
};
</script>

<style>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

@media (max-width: 767px) {
  .amap-geolocation-con {
    z-index: 999 !important;
  }

  .el-message-box {
    width: 80% !important;
  }
}
</style>
