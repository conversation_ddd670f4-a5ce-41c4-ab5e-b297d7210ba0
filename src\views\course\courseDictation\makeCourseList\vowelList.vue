<!-- 元辅音列表 -->
<template>
  <div class="container_box">
    <el-button
      type="primary"
      @click="onAdd"
      icon="el-icon-plus"
      size="mini"
      style="margin-bottom: 10px"
      >添加</el-button
    >
    <el-table
      :data="wordData"
      class="common-table"
      stripe
      border
      v-loading="tableLoading"
    >
      <el-table-column label="元辅音ID" prop="id" width="240" sortable>
      </el-table-column>
      <el-table-column
        label="元辅音"
        prop="word"
        width="360"
        sortable
      ></el-table-column>
      <!-- <el-table-column label="音频" prop="wordSyllableAudioUrl" width="360"  sortable>
            <template slot-scope="scope">
                <div>
                    <audio @play="playFunc(scope.row.wordSyllableAudioUrl)" class="wordAudio" :src="scope.row.wordSyllableAudioUrl" :autoplay="false" 
                    ref="audio"
                    controls></audio>

                </div>
            </template>
         </el-table-column> -->
      <el-table-column label="类型" prop="wordType" width="360" sortable>
        <template slot-scope="scope">
          <span>{{ scope.row.wordType === 0 ? "元音" : "辅音" }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" prop="id" sortable>
        <template slot-scope="{ row }">
          <el-button
            type="primary"
            size="mini"
            icon="el-icon-edit"
            @click="editwordItem(row.id, row.wordType)"
            >编辑</el-button
          >
          <el-button
            type="danger"
            size="mini"
            icon="el-icon-delete"
            @click="delwordItem(row.id)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <el-dialog
      :title="`${dialogTitle}元辅音`"
      :visible.sync="wordDialogVisible"
      width="70%"
      :close-on-click-modal="false"
      @close="wordDialogClose"
    >
      <!-- 新增添加多个 -->
      <el-form
        :model="wordForm"
        ref="wordForm"
        :rules="wordRules"
        label-position="left"
        label-width="60px"
        style="width: 60%"
        v-if="dialogTitle === '新增'"
      >
        <el-form-item label=""
          ><span style="font-weight: bold">页面批量上传</span></el-form-item
        >
        <el-form-item label="辅音" prop="consonantInfo">
          <el-input
            type="textarea"
            :rows="2"
            placeholder="请输入辅音用*分隔"
            :spellcheck="false"
            @change="wordChange"
            v-model="wordForm.consonantInfo"
          ></el-input>
        </el-form-item>
        <el-form-item label="元音" prop="vowelInfo">
          <el-input
            type="textarea"
            :rows="2"
            @change="wordChange1"
            :spellcheck="false"
            placeholder="请输入元音用*分隔"
            v-model="wordForm.vowelInfo"
          ></el-input>
        </el-form-item>
        <el-form-item label="">
          <div class="upload_box">
            <el-upload
              class="upload-demo"
              action="#"
              :show-file-list="true"
              :limit="1"
              :on-remove="handlSetRemoveReadWord"
              :http-request="uploadDetailHttp"
              :before-upload="beforeWordUpload"
              ref="myUpload"
            >
              <div class="el-upload__text">
                <el-link
                  icon="el-icon-upload2"
                  :underline="false"
                  class="upload_link"
                  >excel文件上传</el-link
                >
              </div>
            </el-upload>
            <el-link
              class="download_link"
              :underline="false"
              icon="el-icon-download"
              href="https://document.dxznjy.com/applet/zhimi/config/pd_consonant_vowel.xls"
              >模板下载</el-link
            >
          </div>
        </el-form-item>
      </el-form>
      <!-- 编辑单个显示 -->
      <el-form
        :model="editWordForm"
        ref="editWordForm"
        :rules="editWordRules"
        label-position="left"
        label-width="60px"
        style="width: 60%"
        v-else
      >
        <el-form-item label="辅音" prop="wordInfo" v-if="type === -1">
          <el-input
            type="textarea"
            :rows="4"
            placeholder="请输入辅音用*分隔"
            v-model="editWordForm.wordInfo"
            @change="wordChange2"
          ></el-input>
        </el-form-item>
        <el-form-item label="元音" prop="wordInfo" v-else>
          <el-input
            type="textarea"
            :rows="4"
            placeholder="请输入元音用*分隔"
            v-model="editWordForm.wordInfo"
            @change="wordChange2"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="wordDialogClose">关闭</el-button>
        <el-button size="mini" type="primary" @click="onSubmit">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { Base64 } from "@/utils/base64";
import courseDictationListApi from "@/api/courseDictationList";
import { ossPrClient } from "@/api/alibaba";
export default {
  props: {
    courseCode: {
      type: String,
      default: "",
    },
    id: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      wordData: [],
      wordForm: {
        id: "",
        consonantInfo: "",
        vowelInfo: "",
      },
      editWordForm: {
        id: "",
        wordInfo: "",
      },
      dialogTitle: "新增",
      wordRules: {
        consonantInfo: [
          { required: true, message: "请输入辅音", trigger: "blur" },
        ],
        vowelInfo: [{ required: true, message: "请输入元音", trigger: "blur" }],
      },
      editWordRules: {
        wordInfo: [{ required: true, message: "请输入", trigger: "blur" }],
      },
      tableLoading: false,
      wordDialogVisible: false,
      type: "",
      courseId: "",
      wordFile: "",
    };
  },
  created() {
    this.getwordList();
    this.courseId = this.$route.query && this.$route.query.id;
    ossPrClient();
  },
  methods: {
    wordChange(e) {
      this.wordForm.consonantInfo = e.replaceAll(/[\s\n\r]+/g, "");
      //     .replace(/\\/, "")
      //     .replace(/\[/g, "")
      //     .replace(/\]/g, "");
    },
    wordChange1(e) {
      this.wordForm.vowelInfo = e.replaceAll(/[\s\n\r]+/g, "");
      //     .replace(/\\/, "")
      //     .replace(/\[/g, "")
      //     .replace(/\]/g, "");
    },
    wordChange2(e) {
      this.editWordForm.wordInfo = e.replaceAll(/[\s\n\r]+/g, "");
    },
    playFunc(audioUrl) {
      console.log("点击");
      fetch(audioUrl)
        .then((response) => response.arrayBuffer())
        .then((arrayBuffer) => {
          // 创建Blob对象
          const blob = new Blob([arrayBuffer]);
          // 创建本地URL
          this.audioUrl = URL.createObjectURL(blob);
          // 创建audio元素
          const audio = new Audio();
          audio.src = this.audioUrl;
          audio.play();
          // 下面的代码是为了实现点击下载
          const downloadLink = document.createElement("a");
          downloadLink.href = this.audioUrl;
          downloadLink.download = audioUrl.substring(
            audioUrl.lastIndexOf("\/") + 1,
            audioUrl.length
          );
          // downloadLink.download = 'audio.mp3';
          downloadLink.click();
        });
    },
    // showLong() { //音频加载成功后获取时长
    // this.duration = parseInt(this.$refs.audio.duration)
    // },
    //元辅音上传
    beforeWordUpload(file) {
      if (!this.isExcel(file)) {
        this.$message.error("只能上传excel文件！");
        return false;
      }
    },
    //必须是excel文件
    isExcel(file) {
      return /\.(xlsx|xls)$/.test(file.name);
    },
    //上传元辅音文件解析
    uploadDetailHttp({ file }) {
      const that = this;
      this.wordFile = `https://document.dxznjy.com/applet/zhimi/usercode_${file.uid}.xls`;
      const fileType = file.type.substring(file.type.lastIndexOf("/") + 1);
      const fileName = `manage/${Date.parse(new Date())}.${fileType}`;
      console.log(fileName);

      that.$nextTick(function () {
        ossPrClient()
          .put(fileName, file)
          .then(({ res, url, name }) => {
            if (res && res.status === 200) {
              console.log(`阿里云OSS上传图片成功回调1`, file, res, url, name);
              const formData = new FormData();
              formData.append("file", file);
              formData.append("analysisType", "0");

              courseDictationListApi
                .baseAnalysisFile(formData)
                .then((res) => {
                  console.log("sssssssssssssssss", res);
                  if (res.code === 20000) {
                    this.wordForm.consonantInfo = "";
                    this.wordForm.vowelInfo = "";
                    if (res.data.data.consonantInfo.length > 0) {
                      this.wordForm.consonantInfo = this.wordForm.consonantInfo
                        ? `${this.wordForm.consonantInfo},${res.data.data.consonantInfo}`
                        : res.data.data.consonantInfo;
                    }
                    if (res.data.data.vowelInfo.length > 0) {
                      this.wordForm.vowelInfo = this.wordForm.vowelInfo
                        ? `${this.wordForm.vowelInfo},${res.data.data.vowelInfo}`
                        : res.data.data.vowelInfo;
                    }
                    this.$refs.myUpload.clearFiles();
                  }
                })
                .catch((err) => {
                  console.log(`err--------------------`, err);
                  this.$refs.myUpload.clearFiles();
                });
            }
          })
          .catch((err) => {
            that.$message.error("上传图片失败请检查网络或者刷新页面");
            console.log(`阿里云OSS上传图片失败回调`, err);
          });
      });
    },
    //音节拼读上传成功
    //   handleSetReadWordSuccess(response, file, fileList){
    //     if(file && file.raw && file.uid){
    //       this.wordFile =`https://document.dxznjy.com/applet/zhimi/usercode_${file.raw.uid}.xls`;
    //       const formData = new FormData();
    //       formData.append('file',file.raw);
    //       formData.append('analysisType','0');
    //       courseDictationListApi.baseAnalysisFile(formData).then(res => {
    //          if(res.code === 20000){
    //           console.log(res.data.data,'data');
    //         //   "consonantInfo": "t,r",
    //         // "vowelInfo": "s,v"
    //          }
    //       })
    //     }
    //   },
    //音节拼读删除
    handlSetRemoveReadWord() {
      this.wordFile = "";
    },
    getwordList() {
      this.tableLoading = true;
      courseDictationListApi
        .getVowelAndConsonantList(this.courseCode)
        .then((res) => {
          if (res.code === 20000) {
            console.log(res.data.data, "res");
            this.wordData = res.data.data;
            this.tableLoading = false;
          } else {
            this.tableLoading = false;
          }
          console.log(this.wordData, "res000");
        });
    },
    //新增
    onAdd() {
      this.wordDialogVisible = true;
      this.dialogTitle = "新增";
    },
    //新增编辑元辅音
    onSubmit() {
      if (this.dialogTitle === "新增") {
        this.$refs["wordForm"].validate((valid) => {
          if (valid) {
            let { id, consonantInfo, vowelInfo } = { ...this.wordForm };
            consonantInfo = Base64.encode(consonantInfo)
              .replace(/\+/g, "-")
              .replace(/\//g, "_");
            vowelInfo = Base64.encode(vowelInfo)
              .replace(/\+/g, "-")
              .replace(/\//g, "_");
            console.log(consonantInfo, vowelInfo);
            courseDictationListApi
              .saveVowelAndConsonant(this.id, consonantInfo, vowelInfo)
              .then((res) => {
                if (res.code === 20000) {
                  this.wordDialogVisible = false;
                  this.getwordList();
                  this.$message.success("操作成功");
                  this.$refs.myUpload.clearFiles();
                }
              });
          }
        });
      } else {
        this.$refs["editWordForm"].validate((valid) => {
          if (valid) {
            let { id, wordInfo } = { ...this.editWordForm };
            wordInfo = Base64.encode(wordInfo)
              .replace(/\+/g, "-")
              .replace(/\//g, "_");
            courseDictationListApi
              .updateVowelAndConsonant(id, wordInfo)
              .then((res) => {
                if (res.code === 20000) {
                  this.wordDialogVisible = false;
                  this.getwordList();
                  this.$message.success("操作成功");
                }
              });
          }
        });
      }
    },
    //编辑
    editwordItem(id, type) {
      courseDictationListApi.getVowelAndConsonant(id).then((res) => {
        if (res.code === 20000) {
          this.editWordForm.id = res.data.data.id;
          this.editWordForm.wordInfo = res.data.data.word;
        }
      });
      this.wordDialogVisible = true;
      this.dialogTitle = "编辑";
      this.wordForm.id = id;
      this.type = type;
    },
    //删除
    delwordItem(id) {
      courseDictationListApi.deleteWordById(id, 1).then((res) => {
        if (res.code === 20000) {
          this.$message.success("删除成功");
          this.getwordList();
        }
      });
    },
    wordDialogClose() {
      this.wordDialogVisible = false;
      this.wordForm = {
        id: "",
        consonantInfo: "",
        vowelInfo: "",
      };
      this.editWordForm = {
        id: "",
        wordInfo: "",
      };
      this.dialogTitle === "新增"
        ? this.$refs["wordForm"].clearValidate()
        : this.$refs["editWordForm"].clearValidate();
    },
  },
};
</script>
<style lang="less" scoped>
.upload_link {
  color: #1890ff;
}

.download_link {
  // height: 32px;
  // margin: 0;
  // padding: 0 15px;
  color: #13ce66;
}
.common-table {
  // overflow-y: auto;
}
/* 尝试隐藏音量滑块（但可能不起作用，取决于浏览器） */
.wordAudio::-webkit-media-controls-volume-slider {
  display: none !important;
}
</style>
