<template>
  <div>
    <el-card class="frame" shadow="never">
      <el-form label-width="120px">
        <!-- 1 -->
        <!-- <el-row>
            <el-col :span="8">
              <el-form-item label="姓名:">
                <el-input v-model="searchNum.name" clearable placeholder="请选择" size="small" style="width: 13vw"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="学员编号:">
                <el-input v-model="searchNum.studentCode" :disabled="!!stuudentCode" clearable placeholder="请输入"
                  size="small" style="width: 13vw"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="审核状态:">
                <el-select v-model="searchNum.checkStatus" clearable placeholder="请选择" style="width: 13vw">
                  <el-option label="未审核" value="0"></el-option>
                  <el-option label="已审核" value="1"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row> -->
        <!-- 2 -->
        <el-row>
          <el-col :span="5">
            <el-form-item label="门店编号:">
              <el-input v-model="searchNum.merchantCode" :disabled="!!stuudentCode" clearable placeholder="请输入"
                size="small" style="width: 10vw"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item label="门店名称:">
              <el-input v-model="searchNum.merchantName" :disabled="!!stuudentCode" clearable placeholder="请输入"
                size="small" style="width: 10vw"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item label="门店手机号:">
              <el-input v-model="searchNum.merchantPhone" :disabled="!!stuudentCode" clearable placeholder="请输入"
                size="small" style="width: 10vw"></el-input>
            </el-form-item>
          </el-col>

          <!-- <el-col :span="8">
              <el-form-item label="审核状态:">
                <el-select v-model="searchNum.checkStatus" clearable placeholder="请选择" style="width: 10vw">
                  <el-option label="未审核" value="0"></el-option>
                  <el-option label="已审核" value="1"></el-option>
                </el-select>
              </el-form-item>
            </el-col> -->
          <el-col :span="5">
            <el-form-item label="时间筛选:">
              <el-date-picker v-model="searchNum.refundDate" type="month" placeholder="选择月份" size="small"
                value-format="yyyy-MM">
              </el-date-picker>
              <!-- <el-date-picker v-model="timeAll" style="width: 18vw" size="small" format="yyyy-MM-dd HH:mm"
                  value-format="yyyy-MM-dd HH:mm" :picker-options="pickerOptions" align="right" type="datetimerange"
                  range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker> -->
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-button type="primary" icon="el-icon-search" size="small" @click="initData01">查询</el-button>
            <el-button icon="el-icon-refresh" size="small" @click="rest()">重置</el-button>
            <!-- <el-button type="warning" icon="el-icon-download" size="mini" @click="exportExcel" :loading="exportLoading">
                导出
              </el-button> -->
          </el-col>
        </el-row>
        <!-- 3 -->
      </el-form>
    </el-card>
    <!-- 表格 -->
    <el-table v-loading="tableLoading" :data="luyouclassCard" style="width: 100%" id="out-table"
      :header-cell-style="getRowClass" :cell-style="{ 'text-align': 'center' }">
      <el-table-column prop="merchantName" min-width="200" label="门店名称" header-align="center" />
      <el-table-column prop="merchantCode" min-width="200" label="门店编号" header-align="center" />
      <el-table-column prop="address" label="操作" header-align="center" width="200">
        <template v-slot="{ row }">
          <el-button type="success" size="mini" @click="LeaveBtn(row)">查看详情</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="refundDate" label="时间" min-width="200" header-align="center"></el-table-column>
      <el-table-column prop="monthRefundTimes" label="退课次数" min-width="150" header-align="center"></el-table-column>
      <el-table-column prop="monthRefundCourseHours" label="已退学时" min-width="150"
        header-align="center"></el-table-column>
      <el-table-column prop="monthRefundDeliverHours" label="已退交付学时" min-width="150"
        header-align="center"></el-table-column>
      <el-table-column prop="monthRefundSelfDeliverHours" label="自行交付学时" min-width="150"
        header-align="center"></el-table-column>
      <el-table-column prop="merchantRealName" label="门店负责人" min-width="200" header-align="center"></el-table-column>
      <el-table-column prop="merchantPhone" label="门店手机号" min-width="200" header-align="center"></el-table-column>
    </el-table>
    <!-- 分页器 -->
    <el-row type="flex" justify="center" align="middle" style="height: 60px">
      <!-- 3个变量：每页数量、页码数、总数  -->
      <!-- 2个事件：页码切换事件、每页数量切换事件-->
      <el-pagination v-if="tableIshow" @size-change="handleSizeChange" @current-change="handleCurrentChange"
        :current-page="searchNum.pageNum" :page-sizes="[10, 20, 30, 40, 50]" :page-size="searchNum.pageSize"
        layout="total, sizes, prev, pager, next, jumper" :total="total"></el-pagination>
    </el-row>
    <el-dialog :visible.sync="refundDetailShow" width="50%" @close="close"  append-to-body class="fansdialog">
      <span slot="title">
        <div style="margin: 10px;"> {{ rowData.merchantName }}</div>
        <div style="margin-left: 10px; color: #999;"> {{ rowData.refundDate }}</div>
      </span>
      <div v-for="i in detailArry" :key="i.detailKey">
        <div style="margin-left: 10px;font-weight: 550;">{{ i.detailKey }}</div>

        <ul style="display: flex;justify-content: space-between;flex-wrap: wrap;width: 97%;">
          <li v-for="it in (i.detailValue)" :key="it.id"
            style="width: 47%;">
            <span  v-if="it.studentName && it.studentCode && it.remark">{{ it.studentName }} ( {{ it.studentCode }} ) {{ it.remark }}</span></li>
          <!-- <li v-for="(it,in) in (i.detailValue)" :key="it.id">{{ rowData.studentName }}({{ rowData.merchantCode }})
            退课学时{{ rowData.createTime }}节,退交付学时{{ rowData.createTime }}节;</li> -->
        </ul>
      </div>
      <span slot="footer" class="dialog-footer">
        <!-- <el-button @click="refundDetailShow = false">取 消</el-button> -->
        <el-button type="primary" @click="refundDetailShow = false">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getAdjustInfo, getTimetable, cancelRefund, studentStudyExport, getRefundInfo } from '@/api/checkOrder'
import ls from '@/api/sessionStorage'
import schoolList from '@/api/schoolList'
import systemApi from '@/api/systemConfiguration'
import { getMonthRefundCourseList } from '@/api/menu'
export default {
  name: 'classCard',
  data() {
    return {
      detailKey: [],
      detailValue: [],
      tableData: [],
      refundDetailShow: false,
      stuudentCode: null,
      dataLookerStyle: false,
      // 日期组件
      pickerOptions: {
        shortcuts: [
          {
            text: '今天',
            onClick(picker) {
              // const end = new Date();
              // const start = new Date();
              // picker.$emit('pick', [start, end]);
              const temp = new Date()
              picker.$emit('pick', [
                new Date(temp.setHours(0, 0, 0, 0)),
                new Date(temp.setHours(23, 59, 59, 0))
              ])
            }
          },
          {
            text: '昨天',
            onClick(picker) {
              const temp = new Date()
              temp.setTime(temp.getTime() - 3600 * 1000 * 24)
              picker.$emit('pick', [
                new Date(temp.setHours(0, 0, 0, 0)),
                new Date(temp.setHours(23, 59, 59, 0))
              ])
            }
          },
          {
            text: '最近七天',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
              picker.$emit('pick', [start, end])
            }
          }
        ]
      },
      qjstartTime: '',
      qjendTime: '',
      currentAdmin: '',
      tableIshow: true,
      redLeave: [],
      lookstyle: false,
      LeaveStyle: false,
      classCardstyle: false, //抽屉状态
      LeaveId: '',
      searchNum: {
        merchantName: '',
        merchantCode: '',
        merchantPhone: '',
        refundDate: '',
        pageNum: 1,
        pageSize: 10,
      }, //搜索参数
      tableLoading: false,
      direction: 'rtl',//超哪边打开
      teacher: '',
      timeAll: [],
      contentType: '',
      total: null,
      rowData: {},
      luyouclassCard: [],
      leaveApplication: {
        id: '',
        type: 1
      },
      isAdmin: false,
      exportLoading: false,
      detailArry: []
    }
  },
  created() {
    this.isAdmin = ls.getItem('rolesVal') === 'admin' || ls.getItem('rolesVal') === 'JiaofuManager'
    this.initData()
    this.getRoleTag()
    this.stuudentCode = this.$route.query.classCard
    this.searchNum.planId = this.$route.query.planId
    this.searchNum.studentCode = this.stuudentCode ? this.stuudentCode : ''
  },
  methods: {
    tableRowClassName({ row, rowIndex }) {
      if (row.orderStatus === 4) {
        return 'warning-row'
      } else if (row.orderStatus === 3) {
        return 'success-row'
      }
      return ''
    },
    headClass() { //表头居中显示
      return "text-align:center"
    },
    rowClass() { //表格数据居中显示
      return "text-align:center"
    },
    getRoleTag() {
      schoolList.getCurrentAdmin().then((res) => {
        this.currentAdmin = res.data

      })
    },
    //重置
    rest() {
      this.searchNum.merchantCode = "";
      this.searchNum.merchantName = "";
      this.searchNum.merchantPhone = "";
      this.searchNum.refundDate = "";
      this.initData01();
    },
    // 搜索
    initData01() {
      this.searchNum.pageNum = 1,
        this.searchNum.pageSize = 10,
        this.initData()
    },
    dataLooker(v) {
      this.dataLookerStyle = v
    },
    // statusClass(status) {
    //   switch (status) {
    //     case 11:
    //       return 'error'
    //     case 8:
    //       return ''
    //     case 9:
    //       return ''
    //     default:
    //       return 'normal'
    //   }
    // },
    // orderStatusClass(status) {
    //   switch (status) {
    //     case 11:
    //       return 'error'
    //     case 9:
    //       return 'error'
    //     case 8:
    //       return 'error'
    //     case 4:
    //       return 'error'
    //     case 3:
    //       return 'normal'
    //     case 2:
    //       return 'warning'
    //     default:
    //       return ''
    //   }
    // },
    close(v) {
      this.refundDetailShow = false
    },
    // qjstartTime1(v) {
    //   this.qjstartTime = v
    // },
    // qjendTime1(v) {
    //   this.qjendTime = v
    // },
    async cancel(row) {
      let { data } = await cancelRefund(row.id)
      this.$message.success('取消成功')
      this.initData()
    },
    //处理方法
    objectOrder(obj) {//排序的函数
      var newkey = Object.keys(obj).sort(); //先用Object内置类的keys方法获取要排序对象的属性名，再利用Array原型上的sort方法对获取的属性名进行排序，newkey是一个数组
      var newObj = {};//创建一个新的对象，用于存放排好序的键值对
      for (var i = 0; i < newkey.length; i++) {//遍历newkey数组
        newObj[newkey[i]] = obj[newkey[i]];//向新创建的对象中按照排好的顺序依次增加键值对
      }
      return newObj;//返回排好序的新对象
    },
    // 查看详情
    async LeaveBtn(row) {
      // console.log(row.refundCourseDetail);
      this.rowData = row
      // let { data } = await getRefundInfo(row.id)
      let myArray = []
      this.refundDetailShow = true
      for (let i in this.objectOrder(row.refundCourseDetail)) {
        let newObj = {
          detailKey: i,
          detailValue: row.refundCourseDetail[i]
          // 添加属性和值...
        };
        // 将新对象添加到数组
        myArray.push(newObj);
      }
      console.log(myArray);
      myArray.forEach((i, t) => {
        // console.log(i);
        i.detailValue.forEach((x, v) => {
          var str2 = x.remark.indexOf(",")
          var result2 = x.remark.substring(str2 + 1, x.remark.length)
          x.remark = result2
        })
      });
      this.detailArry = myArray
    },
    async initData() {
      // // 判断为null的时候赋空
      // if (!this.timeAll) {
      //   this.timeAll = []
      // }
      this.tableLoading = true
      // this.searchNum.status = null
      // this.searchNum.startTime = this.timeAll[0]
      // this.searchNum.endTime = this.timeAll[1]
      let { data } = await getMonthRefundCourseList(this.searchNum)
      console.log(data);
      this.tableLoading = false
      this.total = Number(data.totalItems)

      this.luyouclassCard = data.data
    },
    LeaveDialog(v) {
      this.LeaveStyle = v
    },
    changeDrawer(v) {
      this.classCardstyle = v
    },
    lookDrawer(v) {
      this.lookstyle = v
    },
    // 转文字
    teachingType(val) {
      if (val.teachingType == 1) {
        return '远程'
      } else if (val.teachingType == 2) {
        return '线下'
      } else if (val.teachingType == 3) {
        return '远程和线下'
      } else {
        return '暂无'
      }
    },
    courseType(val) {
      if (val.courseType == 1) {
        return '鼎英语'
      }
    },
    status(val) {
      switch (val.status) {
        case 8:
          return '已取消'
        case 9:
          return '已拒绝'
        case 11:
          return '待审核'
        default:
          return '已审核或未该审核'
      }
    },
    orderStatus(status) {
      switch (status) {
        case 8:
          return '已取消'
        case 4:
          return '退款失败'
        case 9:
          return '已拒绝'
        case 10:
          return '部分支付失败'
        case 11:
          return '等待审核'
        case 3:
          return '退款成功'
        case 15:
          return '等待收款订单完成'
        default:
          return '退款中'
      }
    },
    studyStatus(val) {
      if (val.studyStatus == 0) {
        return '未上课'
      } else if (val.studyStatus == 2) {
        return '已上课'
      }
    },
    // 动态class
    getRowClass({ rowIndex, columnIndex }) {
      if (rowIndex == 0) {
        return 'background:#f5f7fa'
      }
    },
    // 分页
    handleSizeChange(val) {
      this.searchNum.pageSize = val
      this.initData()
    },
    handleCurrentChange(val) {
      this.searchNum.pageNum = val
      this.initData()
    },
    // 编辑按钮
    async paikeBtn(row) {
      if (row) {
        let reslist = await getAdjustInfo(row.id)
        this.$refs.rowshuju.classCardnum = reslist.data
        this.$refs.rowshuju.studentList = row
        this.$refs.rowshuju.teacherId = reslist.data.teacherId
        this.classCardstyle = true
        this.$refs.rowshuju.planStudy.studentCode = row.studentCode
        this.$refs.rowshuju.getTeachlist()
      } else {
        let reslist = await getAdjustInfo(this.LeaveId)
        this.$refs.rowshuju.classCardnum = reslist.data
        this.$refs.rowshuju.studentList = this.redLeave
        this.$refs.rowshuju.teacherId = reslist.data.teacherId
        this.classCardstyle = true
        this.$refs.rowshuju.getTeachlist()
        if (this.qjstartTime !== '' && this.qjstartTime != undefined && this.qjstartTime != null) {
          this.$refs.rowshuju.classCardnum.startStudyTime = this.qjstartTime
        }
        if (this.qjendTime !== '' && this.qjendTime != undefined && this.qjendTime != null) {
          this.$refs.rowshuju.classCardnum.endStudyTime = this.qjendTime
        }
      }
      // let reslist = await getAdjustInfo(row.id);
      // this.$refs.rowshuju.classCardnum = reslist.data;
      // this.$refs.rowshuju.studentList = this.redLeave;
      // this.$refs.rowshuju.teacherId = reslist.data.teacherId;
      // this.classCardstyle = true;
      // this.$refs.rowshuju.getTeachlist();
    },
    // 数据查看按钮
    //定义导出Excel表格事件
    exportExcel() {
      this.exportLoading = true
      studentStudyExport(this.searchNum).then(res => {
        console.log(window.URL.createObjectURL(res))
        const url = window.URL.createObjectURL(res)
        const link = document.createElement('a')
        link.style.display = 'none'
        link.href = url//获取服务器端的文件名
        link.setAttribute('download', '学习课程表.xls')
        document.body.appendChild(link)
        link.click()
        this.exportLoading = false
      })
    },
    //删除学习课程
    async deleteStudy(row) {
      await this.$confirm('您确定要删除学习课程吗?')
      await deletePlanStudy(row.id)
      this.$message.success('删除成功')
      await this.initData()
    }
  }
}
</script>

<style scoped>
body {
  background-color: #f5f7fa;
}

.normal {
  color: rgb(28, 179, 28);
}

.error {
  color: rgba(234, 36, 36, 1);
}

.warning {
  color: rgb(234, 155, 36);
}

body {
  background-color: #f5f7fa;
}
</style>

<style lang="scss" scoped>
.frame {
  margin-top: 0.5vh;
  background-color: rgba(255, 255, 255);
}

.el-date-editor.el-input {
  width: 10vw !important;
}

ul {
  list-style: none;
  padding-left: 0 !important;

  li {
    margin: 10px;
  }
}

// .el-button--success {
//   color: #ffffff;
//   background-color: #6ed7c4;
//   border-color: #6ed7c4;
// }</style>
<style>
.el-dialog__footer {
  text-align: center !important;
}

</style>
<style >
.el-dialog__body {
    height: 60vh;
    overflow: auto;
}
</style>