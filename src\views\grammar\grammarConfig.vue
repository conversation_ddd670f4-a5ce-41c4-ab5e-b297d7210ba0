<template>
  <div class="Echarts">
    <div id="main" style="width: 600px; height: 400px"></div>
  </div>
</template>

<script>
export default {
  name: "Echarts",
  methods: {
    myEcharts() {
      // 基于准备好的dom，初始化echarts实例
      var myChart = this.$echarts.init(document.getElementById("main"));

      // 指定图表的配置项和数据
      var option = {
        title: {
          text: "实施产值数量",
          textStyle: {
            fontSize: 20,
            color: "#4ed3d3",
          },
        },
        tooltip: {},
        legend: {
          data: ["销量1", "销量2", "销量3"],
          top: "bottom",
        },
        xAxis: {
          splitLine: {
            show: false,
          },
          data: ["", "", "", "", "", ""],
        },
        yAxis: {
          splitLine: {
            show: false,
          },
        },
        series: [
          {
            name: "销量1",
            type: "bar",
            data: [
              {
                value: 20,
                itemStyle: {
                  color: "#a90000",
                },
              },

              {
                value: 20,
                itemStyle: {
                  color: "#940",
                },
              },
              {
                value: 20,
                itemStyle: {
                  color: "#500",
                },
              },
              20,
              50,
            ],
          },
          {
            name: "销量2",
            type: "bar",
          },
          {
            name: "销量3",
            type: "bar",
          },
        ],
      };

      // 使用刚指定的配置项和数据显示图表。
      myChart.setOption(option);
    },
  },
  mounted() {
    this.myEcharts();
  },
};
</script>

<style></style>
