/**
 * 学员管理相关接口
 */
import request from '@/utils/request'

export default {

  selectOption() {
    return request({
      url: '/znyy/bvadmin/select',
      method: 'GET',
    })
  },
  queryUserDetail(merchantCode) {
    return request({
      url: '/znyy/bvadmin/study/committee',
      method: 'get',
      params: {'merchantCode': merchantCode}
    })
  },
  createStudyCommitteeMan(data) {
    return request({
      url: '/znyy/bvadmin/study/committee',
      method: 'POST',
      data: data
    })
  },
  updateStudyCommitteeMan(data) {
    return request({
      url: '/znyy/bvadmin/study/committee',
      method: 'PUT',
      data: data
    })
  }
}
