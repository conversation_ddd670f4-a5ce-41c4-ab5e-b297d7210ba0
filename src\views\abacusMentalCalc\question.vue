<template>
  <div class="app-container">
    <el-form :model="dataQuery" ref="queryForm" :inline="true">
      <el-form-item label="ID查询：" prop="questionId">
        <el-input type="number" v-model="dataQuery.questionId" clearable placeholder="请输入ID"></el-input>
      </el-form-item>
      <el-form-item label="维度：" prop="dimensionId">
        <el-select v-model="dataQuery.dimensionId" clearable>
          <el-option v-for="(item, index) in dimensionList" :key="index" :label="item.name" :value="item.id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="类型：" prop="type">
        <el-select v-model="dataQuery.type" clearable>
          <el-option v-for="(item, index) in calculationType" :key="index" :label="item.msg" :value="item.code">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="题型：" prop="type">
        <el-select v-model="dataQuery.questionType" clearable>
          <el-option v-for="(item, index) in questionTypeList" :key="index" :label="item.msg" :value="item.code">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="时间查询：">
        <el-date-picker
          v-model="timeDate"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="queryList">查询</el-button>
        <el-button type="primary" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-col :span="24" style="margin-bottom: 30px">
      <el-button type="success" icon="el-icon-plus" size="mini" @click="handleAdd()">新增</el-button>
    </el-col>

    <el-table class="common-table" v-loading="tableLoading" :data="tableData" style="width: 100%;margin-bottom: 20px;"
      row-key="id" border default-expand-all>
      <el-table-column prop="orderNum" label="排序" />
      <el-table-column prop="questionId" label="题目ID" />
      <el-table-column prop="courseName" label="课程名称"></el-table-column>
      <el-table-column prop="withdrawnBonus" label="操作" width="260px">
        <template slot-scope="scope">
          <el-button type="primary" size="mini" icon="el-icon-sort" @click="handleUpdate(scope.row)">编辑
          </el-button>
          <el-button type="primary" size="mini" icon="el-icon-sort" @click="clickUpdateOrderNum(scope.row)">排序
          </el-button>
          <el-button type="danger" size="mini" icon="el-icon-delete" @click="handleDelete(scope.row.id)">删除
          </el-button>
        </template>
      </el-table-column>
      <el-table-column prop="grade" label="学段" show-overflow-tooltip />
      <el-table-column prop="dimension" label="所属维度" show-overflow-tooltip />
      <el-table-column prop="questionName" label="题干" show-overflow-tooltip />
      <el-table-column label="类型" prop="type"/>
      <el-table-column label="添加时间" prop="createTime"/>
    </el-table>
    <!-- 分页 -->
    <el-col :span="24" style="margin-bottom: 20px">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>

    <!-- 排序弹窗-->
    <el-dialog title="切换顺序" :visible.sync="questionPage.showDialogOrderNum" width="30%" :before-close="closeOrderNum" center>
      <div class="order-num-input">
        <span class="order-num-input-title">排序：</span>
        <el-input-number v-model="orderNum" controls-position="right" :min="1" :max="999"></el-input-number>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="questionPage.showDialogOrderNum = false">取 消</el-button>
        <el-button type="primary" @click="handleUpdateOrderNum">确 定</el-button>
      </span>
    </el-dialog>

    <!--    选择问题弹窗-->
    <el-dialog :visible.sync="questionPage.showDialog" width="70%" @close="close">
      <el-form :model="questionPage.dataQuery" ref="queryForm" :inline="true">
        <el-form-item label="ID查询：">
          <el-input type="number" v-model="questionPage.dataQuery.id" clearable placeholder="请输入ID"></el-input>
        </el-form-item>
        <el-form-item label="题型：">
          <el-select v-model="questionPage.dataQuery.questionType" placeholder="全部" clearable>
            <el-option v-for="(item, index) in questionTypeList" :key="index" :label="item.msg" :value="item.code">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="维度：">
          <el-select v-model="questionPage.dataQuery.dimensionId" placeholder="全部" clearable>
            <el-option v-for="(item, index) in dimensionList" :key="index" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="类型：">
          <el-select v-model="questionPage.dataQuery.type" placeholder="全部" clearable>
            <el-option v-for="(item, index) in calculationType" :key="index" :label="item.msg" :value="item.code">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="addQuestionSeach">查询</el-button>
          <el-button @click="searchResetQuery">重置</el-button>
        </el-form-item>
      </el-form>
      <el-table v-loading="questionPage.listLoading" :data="questionPage.tableData" ref="multipleTable"
        @selection-change="handleSelectionChange" :row-key="getRowKeys" border fit highlight-current-row style="width: 100%">
        <el-table-column type="selection" width="40" :reserve-selection="true"></el-table-column>
        <el-table-column prop="id" label="问题Id" width="200" />
        <el-table-column prop="grade" label="学段" width="200" />
        <el-table-column prop="type" label="类型" :formatter="questionTypeFormatter" width="200" />
        <el-table-column prop="stem" label="题干" show-overflow-tooltip/>
        <el-table-column prop="createTime" label="添加时间" />
        <el-table-column prop="dimension" label="维度" />
        <el-table-column prop="questionTypeName" label="题型" />
      </el-table>
      <!-- 分页 -->
      <!-- 分页 -->
      <el-col :span="24" style="margin-bottom: 20px">
        <el-pagination :current-page="questionPage.tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
          layout="total, sizes, prev, pager, next, jumper" :total="questionPage.tablePage.totalItems"
          @size-change="questionHandleSizeChange" @current-change="questionHandleCurrentChange" />
      </el-col>
      <span slot="footer" class="dialog-footer">
        <el-button @click="close">取 消</el-button>
        <el-button type="primary" @click="confirmQuestionSelect">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<style scoped>
.order-num-input {
  display: flex;
  justify-content: center;
}
.order-num-input-title {
  display: flex;
  align-items: center;
}
</style>

<script>

import { pageParamNames } from '@/utils/constants'
import { mapGetters, mapState } from 'vuex'
import categoryApi from "@/api/abacusMentalCalc/category";
import courseQuestionApi from "@/api/abacusMentalCalc/courseQuestion";

export default {
  data() {
    return {
      orderNum:0,
      selectedOrderNumData:null,

      timeDate:null,
      dimensionList: [],
      questionTypeList: [],
      calculationType:[],

      parentId: '',
      dataQuery: {
        courseId : '',
        dimensionId : '',
        type : '',
        questionId:"",
        endTime : '',
        startTime : '',
        questionType:"",
        isPromote: 0,
      },

      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      tableData: [],

      courseName:"",
      courseFormal:null,
      courseGrade:null,

      currentQuestionIds: [],//已有的题目
      form:{},
      questionPage: {
        multipleSelection: [],
        showDialog: false,
        showDialogOrderNum: false,
        dataQuery: {
          type: null,
          grade: null,
          questionType: null,
          id: "",
          dimensionId: null,
        },
        // 分页
        tablePage: {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null
        },
        listLoading: true,
        tableData: [],
        total: 0
      }
    }
  },
  updated() {
    // 在这里调用toggleSelection选中方法
    if (this.questionPage.showDialog) {
      this.toggleSelection(this.questionPage.tableData)
    }
  },
  created() {
    this.parentId = this.$route.query.id
    if (this.parentId) {
      courseQuestionApi.courseDetails(this.parentId).then(res => {
        this.courseName = res.data.courseName
        this.courseFormal = res.data.formal
        this.courseGrade = res.data.grade
        this.getList()
      })
    } else {
      this.getList()
    }
    this.getAllDimension()
    this.getQuestionType()
    this.getCalculationType()
  },
  methods: {
    getAllDimension(){
      categoryApi.getAllDimensionHaveRepeat().then(res => {
        this.dimensionList = res.data
      });
    },
    getQuestionType(){
      courseQuestionApi.getQuestionType().then(res => {
        this.questionTypeList = res.data
      });
    },
    getCalculationType(){
      courseQuestionApi.getCalculationType().then(res => {
        this.calculationType = res.data
      });
    },
    handleUpdate(row) {
      let url = this.trainUrlFormat(this.questionUrlEnum, row.questionType);
      this.$router.push({ path: url, query: { formData: {id:row.questionId} } });
    },
    clickUpdateOrderNum(row) {
      this.orderNum = row.orderNum;
      this.selectedOrderNumData = row;
      this.questionPage.showDialogOrderNum = true;
    },
    handleUpdateOrderNum() {
      let params = "id="+this.selectedOrderNumData.id+"&";
      params += "questionId="+this.selectedOrderNumData.questionId+"&";
      params += "orderNum="+this.orderNum;
      courseQuestionApi.updateOrderNum(params).then(res=>{
        this.questionPage.showDialogOrderNum = false;
        this.resetQuery();
      });
    },
    handleDelete(id) {
      this.$confirm('确定要删除吗？', '删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        courseQuestionApi.courseQuesDelete(id).then(res => {
          this.$message.success('删除成功！')
          this.tablePage = {
            currentPage: 1,
            size: 10,
            totalPage: null,
            totalItems: null
          }
          this.getList()
        })
      })
    },
    queryList(){
      this.tablePage.currentPage = 1
      this.getList()
    },
    resetQuery(){
      this.tablePage.currentPage = 1
      this.timeDate = null
      this.dataQuery = {
        dimensionId : '',
        type : '',
        questionId:"",
        endTime : '',
        startTime : '',
        questionType:"",
      }
      this.getList()
    },
    getList() {
      this.currentQuestionIds = []
      this.tableLoading = true

      this.dataQuery.courseId = this.parentId || 0
      this.dataQuery.pageNum = this.tablePage.currentPage
      this.dataQuery.pageSize = this.tablePage.size
      if(this.timeDate && this.timeDate.length > 0){
        this.dataQuery.startTime = this.getFormatTime(this.timeDate[0]);
        this.dataQuery.endTime = this.getFormatTime(this.timeDate[1]);
      }
      courseQuestionApi.courseQuesList(this.dataQuery).then(res => {
        this.tableData = res.data.data
        this.tableData.forEach(i => {
          this.currentQuestionIds.push(i.questionId)
        })
        this.tableLoading = false
        // 设置后台返回的分页参数
        pageParamNames.forEach(name =>
          this.$set(this.tablePage, name, parseInt(res.data[name]))
        )
      })
    },
    getFormatTime(time){
      let date = new Date(time);
      let year = date.getFullYear();
      let month = date.getMonth() + 1;  // getMonth()返回值是0~11
      let day = date.getDate();
      let hour = date.getHours();
      let minute = date.getMinutes();
      let second = date.getSeconds();

      let result = year + "-" + (month < 10 ? "0" + month : month) + "-" + (day < 10 ? "0" + day : day) + " " + (hour < 10 ? "0" + hour : hour) + ":" + (minute < 10 ? "0" + minute : minute) + ":" + (second < 10 ? "0" + second : second);
      return result
    },

    toggleSelection(rows) {
      if (rows.length > 0) {
        rows.forEach(row => {
          this.currentQuestionIds.forEach(a => {
            if (row.id === a) {
              this.$refs.multipleTable.toggleRowSelection(row, true)
            }
          })
        })
      }
    },
    getRowKeys(row) {
      return row.id;
    },
    handleSelectionChange(val) {
      this.questionPage.multipleSelection = val
    },
    confirmQuestionSelect() {
      let needAdd = [];
      this.questionPage.multipleSelection.forEach(q => {
        let value = this.currentQuestionIds.find(el => el === q.id);
        !value && needAdd.push(q.id)
      })
      needAdd = this.currentQuestionIds.concat(needAdd)
      this.form.courseName = this.courseName
      this.form.formal = this.courseFormal
      this.form.grade = this.courseGrade
      this.form.id = this.parentId
      this.form.type = "QUESTION"
      this.form.questionId = needAdd
      this.questionPage.listLoading = true
      courseQuestionApi.courseQuesAdd(this.form).then(res => {
          this.questionPage.listLoading = false
          this.currentQuestionIds = (needAdd)
          this.getList()
          this.questionPage.showDialog = false
      })
    },
    addQuestionSeach() {
      this.questionPage.tablePage.currentPage = 1
      this.questionPage.tablePage.size = 10
      this.search()
    },
    search() {
      this.questionPage.listLoading = true
      this.questionPage.dataQuery.grade = this.courseGrade
      this.questionPage.dataQuery.pageNum = this.questionPage.tablePage.currentPage
      this.questionPage.dataQuery.pageSize = this.questionPage.tablePage.size
      courseQuestionApi.questionList(this.questionPage.dataQuery).then(res => {
        this.questionPage.tableData = res.data.data
        this.questionPage.listLoading = false
        // 设置后台返回的分页参数
        pageParamNames.forEach(name => this.$set(this.questionPage.tablePage, name, parseInt(res.data[name])))
      })
    },

    questionTypeFormatter(row, column, cellValue, index) {
      for (let item of this.calculationType) {
        if (item.code === cellValue) {
          return item.msg
        }
      }
      return '无'
    },

    close() {
      this.questionPage.showDialog = false
      this.searchReset()
    },
    closeOrderNum() {
      this.questionPage.showDialogOrderNum = false;
    },
    searchReset() {
      this.questionPage.dataQuery = {
        type: null,
        questionType: null,
        grade: null,
        id: "",
        dimensionId: null,
      }
    },
    searchResetQuery() {
      this.searchReset()
      this.search()
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.questionPage.showDialog = true
      this.search()
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val
      this.getList()
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val
      this.getList()
    },
    // 分页
    questionHandleSizeChange(val) {
      this.questionPage.tablePage.size = val
      this.search()
    },
    questionHandleCurrentChange(val) {
      this.questionPage.tablePage.currentPage = val
      this.search()
    }
  },
  computed: {
    ...mapGetters('enumItem', ['trainUrlFormat']),
    ...mapState('enumItem', {
      questionUrlEnum: state => state.zhuXinSuan.questionUrlEnum
    }),
  }
}
</script>
