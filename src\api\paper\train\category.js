
import request from '@/utils/request'

export default {
  // 分页查询
  list(data) {
    return request({
      url: '/paper/web/trainCategory/list',
      method: 'GET',
      params: data
    })
  },
//添加
  saveOrUpdate(data) {
    return request({
      url: '/paper/web/trainCategory/saveOrUpdate',
      method: 'POST',
      data
    })
  },
  detail(id){
    return request({
      url: '/paper/web/trainCategory/detail',
      method: 'GET',
      params: {
        id:id
      }
    })
  },
  delete(id){
    return request({
      url: '/paper/web/trainCategory/delete',
      method: 'DELETE',
      params: {
        id:id
      }
    })
  }
}
