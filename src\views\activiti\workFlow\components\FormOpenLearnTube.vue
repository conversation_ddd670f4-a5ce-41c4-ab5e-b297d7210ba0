<template>
  <div>
    <el-form label-position="right" label-width="140px" style="width: 100%">
        <el-form-item label="手机号" prop="mobile">
          <el-input v-model="form.name" :disabled="true"></el-input>
        </el-form-item>
        <el-form-item label="真实姓名" prop="realName">
          <el-input  :disabled="true" v-model="form.realName"></el-input>
        </el-form-item>
        <el-form-item label="密码" prop="password" v-if="false">
          <el-input type="password" v-model="form.password" auto-complete="new-password"></el-input>
        </el-form-item>

        <el-form-item label="确认密码" prop="password2" v-if="false">
          <el-input type="password" v-model="form.password2"></el-input>
        </el-form-item>
        <el-form-item label="学管师身份证号：" prop="idCard">
          <el-col :xs="24" :span="18" >
            <el-input  :disabled="true" v-model="form.idCard"/>
          </el-col>
        </el-form-item>
        <el-form-item label="学管师身份证照片:" prop="idCardPhoto" :disabled="true">
          <el-col :span="20">
            <el-upload
              :disabled="true"
              ref="clearupload"
              list-type="picture-card"
              action=""
              element-loading-text="图片上传中"
              :limit="10"
              :file-list="form.fileList"
            >
              <i class="el-icon-plus"/>
            </el-upload>
          </el-col>
          <el-col :xs="24" :span="4">(*支持多张)</el-col>
        </el-form-item>
        <el-form-item label="所在地区：" prop="name" >
          <el-col :xs="24" :span="18">
            <el-row :gutter="10">
              <el-col :xs="24" :span="8">
                <el-input :disabled="true"
                  placeholder="安徽省"
                  v-model="form.province"
                />
              </el-col>
              <el-col :xs="24" :span="8">
                <el-input :disabled="true"
                  placeholder="合肥市"
                  v-model="form.city"
                />
              </el-col>
              <el-col :xs="24" :span="8">
                <el-input :disabled="true"
                  placeholder="包河区"
                  v-model="form.area"
                />
              </el-col>
            </el-row>
          </el-col>
        </el-form-item>
        <el-form-item label="详细地址" prop="address">
          <el-col :xs="24" :span="18">
            <el-input :disabled="true"
              v-model="form.address"
            />
          </el-col>
        </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: "FormOpenLearnTube",
  props: {
    // 表单
    form: {
      type: Object,
    },
  }
}
</script>

<style scoped>

</style>
