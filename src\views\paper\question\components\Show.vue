<template>
  <div class="paper-question-contain">
    <div class="q-row">
      <div class="q-title q-head">[{{ getQuestType(qType) }}]</div>
      <div class="q-content" v-html="question.topic"></div>
      <span v-if="qType === 'COMBINATION'">（{{ question.score }}分）</span>
    </div>
<!--    <div
      v-loading="qLoading"
      v-if="qType !== 'COMBINATION'"
    >
      <div class="q-row" v-if="qType !== 'EXPLAIN'">
        <div class="q-title">{{ qType == 'FILLING' ? '答案' : '选项' }}：</div>
        <div class="q-content" :key="item.id" v-for="item in question.options">
          <div class="q-answer">
            <span class="q-item-prefix">{{ item.label }}.&nbsp;</span>
            <span v-html="item.value" class="q-item-content"></span>
          </div>
        </div>
      </div>
      <div
        class="q-row"
        v-if="qType == 'SINGLE_CHOICE' || qType == 'MULTIPLE_CHOICE' || qType == 'JUDGE'"
      >
        <div class="q-title">答案：</div>
        <div class="q-content">{{ question.answer }}</div>
      </div>
      <div class="q-row">
        <div class="q-title">分数：</div>
        <div class="q-content">{{ question.score }}分</div>
      </div>
      <div class="q-row">
        <div class="q-title">难度：</div>
        <div class="q-content">
          <el-rate v-model="question.difficulty" disabled></el-rate>
        </div>
      </div>
      <div class="q-row">
        <div class="q-title">解析：</div>
        <div class="q-content"><span>{{ question.analysis }}</span></div>
      </div>
    </div>

    <div
      v-else-if="qType == 'COMBINATION'"
      v-loading="qLoading"
      class="paper-question-contain-item"
    >
      <div
        class="q-content paper-question-contain-item"
        v-for="(child, index) in question.childQuestionVo"
        :key="index"
      >
        <div class="q-content q-content-child">
          子题{{ index + 1 }}：
          <span v-html="child.topic"></span>
          （{{ child.score }}分）
        </div>
        <div class="q-row" v-if="child.questionType !== 'EXPLAIN'">
          <div class="q-title">
            {{ child.questionType === 'FILLING' ? '答案' : '选项' }}：
          </div>
          <div
            class="q-content"
            :key="item.id"
            v-for="item in child.options"
          >
            <div class="q-answer">
              <span class="q-item-prefix">{{ item.label }}.&nbsp;</span>
              <span v-html="item.value" class="q-item-content"></span>
            </div>
          </div>
        </div>
        <div
          class="q-row"
          v-if="child.questionType === ('SINGLE_CHOICE' || 'MULTIPLE_CHOICE' || 'JUDGE')"
        >
          <div class="q-title">答案：</div>
          <div class="q-content">{{ child.answer }}</div>
        </div>
        <div class="q-row">
          <div class="q-title">分数：</div>
          <div class="q-content">{{ child.score }}分</div>
        </div>
        <div class="q-row">
          <div class="q-title">难度：</div>
          <div class="q-content">
            <el-rate v-model="child.difficulty" disabled></el-rate>
          </div>
        </div>
        <div class="q-row">
          <div class="q-title">解析：</div>
          <div class="q-content">{{ child.analysis }}</div>
        </div>
      </div>
    </div>-->
  </div>

</template>
<script>
import '/public/components/ueditor/themes/iframe.css'

export default {
  name: 'QuestionShow',
  props: {
    question: {
      type: Object,
      default: function() {
        return {}
      }
    },
    qLoading: {
      type: Boolean,
      default: false
    },
    qType: {
      type: String,
      default: ''
    }
  },
  mounted() {
  },
  methods: {
    getQuestType(key) {
      let type = ''
      switch (key) {
        case 'SINGLE_CHOICE':
          type = '单选题'
          break
        case 'MULTIPLE_CHOICE':
          type = '多选题'
          break
        case 'JUDGE':
          type = '判断题'
          break
        case 'FILLING':
          type = '填空题'
          break
        case 'EXPLAIN':
          type = '简答题'
          break
        case 'COMBINATION':
          type = '组合题'
          break
        case 'STR_MEMORY':
          type = '字符串记忆题'
          break
        case 'WORD_MEMORY':
          type = '词语记忆题'
          break
        case 'MATCH_MOVE':
          type = '火柴移动题'
          break
        case 'ICON_MOVE':
          type = '硬币移动题'
          break
        case 'CLUE':
          type = '线索题'
          break
        case 'GRID':
          type = '跳格子题'
          break
        default:
          type = '未知题型'
          break
      }
      return type
    }
  }
}
</script>

<style lang="less">
.q-row {
  display: flex;
  margin-bottom: 5px;

  .q-title {
    margin-right: 15px;
  }

  .q-answer {
    margin-right: 20px;
  }

  .q-content {
    flex: 1;
  }
}

.q-head {
  color: #409eff;
}

.paper-question-contain-item {
  margin-bottom: 10px;
  background-color: hsla(0, 0%, 98%, 0.4);
  padding: 10px;
}

// .el-form-item--medium .el-form-item__content,
// .el-form-item--medium .el-form-item__label {
//   line-height: 36px;
// }
// .q-content-child {
//   margin-left: 10px;
//   background-color: hsla(0, 0%, 97%, 0.4);
// }
</style>
