<template>
  <div class="contain">
    <!-- v-if="JlbInfo.logoEnable && JlbInfo.roleTag == 'Operations'" -->
    <div class="isLogo">
      <div class="logoTitle">我的配置</div>
      <el-form :model="form" ref="form" :rules="rules" label-position="left" label-width="120px" :inline="false"
        size="normal">
        <el-form-item label="名称：" prop="customName">
          <el-input style="width: 200px" v-model="form.customName"></el-input>
        </el-form-item>
        <el-form-item label="域名：" prop="secondaryDomainName">
          <el-input style="width: 200px; margin-right: 5px" v-model="form.secondaryDomainName"></el-input>.dxznjy.com
          <div v-if="JlbInfo.secondaryDomainName" style="font-size: 12px">
            <div>
              渠道系统后台网址：{{
                `https://${JlbInfo.secondaryDomainName}m.dxznjy.com`
              }}
            </div>
            <div>
              交付系统后台网址：{{
                `https://${JlbInfo.secondaryDomainName}d.dxznjy.com`
              }}
            </div>
            <div>
              支付系统后台网址：{{
                `https://${JlbInfo.secondaryDomainName}p.dxznjy.com`
              }}
            </div>
            <div>APP联系总部获取</div>
          </div>
          <div style="font-size: 12px">
            新的域名将十分钟后生效，当前域名即将失效，请您记住新的域名
          </div>
        </el-form-item>
        <!-- :disabled="fisrtSet" -->
        <el-form-item label="自定义logo：">
          <my-upload @handleSuccess="handleSuccess" @handleRemove="handleRemove" :fullUrl="true" :file-list="fileList"
            :limit="1" :showTip="true" tipText="建议尺寸：150*150，支持格式：JPG、PNG格式 支持尺寸：<300KB"
            :imgSize="0.292969 * 1024 * 1024" />
        </el-form-item>
        <el-form-item label="上传压缩包：">
          <!-- :versions="versions" -->
          <zip-upload @handleSuccess="handleSuccess2" @handleRemove="handleRemove2" :zipName="form.secondaryDomainName"
            :fullUrl="true" :file-list="fileList2" :limit="1" :showTip="true" tipText="仅支持上传zip"
            :imgSize="0.292969 * 1024 * 1024" />
        </el-form-item>
        <el-form-item label="主题色值：" prop="rgba">
          <!-- <el-color-picker v-model="form.rgba"></el-color-picker> -->
          #<el-input size="small" style="width: 200px; margin-left: 5px" v-model="form.rgba"></el-input>
          <div>
            不知道怎么取色值，点击跳转
            <el-button type="text" @click="go">取色图片编辑器</el-button>
          </div>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" size="small " @click="onSubmit">确定</el-button>
          <el-button size="small " @click="findJlbId">取消</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>
<script>
import { mapGetters } from "vuex";
import authApi from "@/api/auth";
import dealerListApi from "@/api/operationsList";
import MyUpload from "@/components/Upload/MyUpload";
import ZipUpload from "@/components/Upload/ZipUpload";
export default {
  data() {
    return {
      form: {
        customName: "",
        secondaryDomainName: "",
        rgba: "",
      },
      secondaryDomainName: "",
      fileList: [],
      // versions: 1,
      fileList2: [],
      rules: {
        customName: [
          { required: true, message: "请输入会员名称", trigger: "blur" },
          { min: 2, max: 10, message: "长度在 2 到 10个字符", trigger: "blur" },
        ],
        secondaryDomainName: [
          { required: true, message: "请输入域名", trigger: "blur" },
          {
            pattern: /^[A-Za-z]{3,10}$/,
            message: "请输入长度在 3 到 10个字母",
            trigger: "blur",
          },
        ],
        rgba: [
          {
            pattern: /^[0-9A-Fa-f]{6}$/,
            message: "请输入16进制色值0-9A-Fa-f",
            trigger: "blur",
          },
        ],
      },
    };
  },
  components: {
    ZipUpload,
    MyUpload,
  },
  computed: {
    ...mapGetters(["JlbInfo", "token"]),
  },
  created() {
    this.findJlbId();
  },
  mounted() {
    if (this.JlbInfo.logoEnable) {
      document.title = this.JlbInfo.customName + "鼎校甄选";
    }
  },

  methods: {
    // 获取自己的logo
    async findJlbId() {
      this.fileList = [];
      this.fileList2 = [];
      let { data } = await authApi.getJlbInfo();
      this.form.resourceUrl = data.resourceUrl;
      this.secondaryDomainName = data.secondaryDomainName;
      if (data.avatar) {
        this.fileList = [{ name: "默认", url: data.avatar }];
      }

      if (data.resourceUrl) {
        this.fileList2 = [
          {
            url: data.resourceUrl,
            name: data.resourceUrl.split("/")[
              data.resourceUrl.split("/").length - 1
            ],
          },
        ];
      }
      this.form.avatar = data.avatar;
      this.form.customName = data.customName;
      this.form.rgba = data.rgba.slice(4);
      this.form.secondaryDomainName = data.secondaryDomainName;
    },
    // 俱乐部定制logo 提交
    async onSubmit() {
      if (
        this.secondaryDomainName != this.form.secondaryDomainName &&
        this.secondaryDomainName != ""
      ) {
        await this.$confirm(
          "更换域名后，新的域名十分钟才会生效,当前域名即将失效；确定后请勿退出登录，否则将无法登录系统，是否继续?",
          "提示",
          {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          }
        );
      }
      await this.$refs.form.validate();
      if (!this.form.avatar) return this.$message.warning("请上传自定logo");
      if (!this.form.resourceUrl) return this.$message.warning("请上传压缩包");
      let obj = JSON.parse(JSON.stringify(this.form));
      obj.id = this.JlbInfo.id;
      if (obj.rgba) {
        obj.rgba = "0xFF" + obj.rgba;
      } else {
        obj.rgba = "";
      }
      await dealerListApi.customMerchantInfo(obj);
      this.$message.success("操作成功");
      if (
        this.secondaryDomainName == this.form.secondaryDomainName ||
        this.secondaryDomainName == ""
      ) {
        setTimeout(() => {
          location.reload();
        }, 500);
      } else {
        setTimeout(() => {
          window.location.href =
            "https://manage.dxznjy.com/#/dashboard?token=" + this.token;
        }, 500);
      }
    },
    go() {
      window.open("https://www.jyshare.com/front-end/6214/#12746b");
    },
    // logo图赋值
    handleSuccess(res) {
      this.form.avatar = res;
    },
    // logo图删除
    handleRemove() {
      this.fileList = [];
      this.form.avatar = "";
    },
    // zip文件赋值
    handleSuccess2(res) {
      this.form.resourceUrl = res;
    },
    // zip文件删除
    handleRemove2() {
      this.fileList2 = [];
      this.form.resourceUrl = "";
    },
  },
};
</script>

<style lang="scss" scoped>
.isLogo {
  // height: 90vh;
  .logoTitle {
    font-size: 20px;
    margin-bottom: 20px;
  }
}
</style>
