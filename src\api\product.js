/**
 * 商品相关接口
 */
import request from '@/utils/request'
// import request from '@/utils/request'

export default {

  // 商品管理
  // 商品新增
  addProduct(data) {
    return request({
      url: '/mall/product',
      method: 'POST',
      data
    })
  },
  // 商品修改
  updateProduct(data) {
    return request({
      url: '/mall/product',
      method: 'PUT',
      data
    })
  },
  // 商品查询
  queryProduct(id) {
    return request({
      url: '/mall/product/' + id,
      method: 'GET'
    })
  },
  // 商品删除
  deleteProduct(id) {
    return request({
      url: '/mall/product/' + id,
      method: 'DELETE'
    })
  },
  // 商品分页查询
  productList(pageNum, pageSize, data) {
    return request({
      url: '/mall/product/page?pageNum=' + pageNum + '&pageSize=' + pageSize,
      method: 'GET',
      params: data
    })
  },
  // 商品类型
  productType() {
    return request({
      url: '/mall/product/type',
      method: 'GET'
    })
  },
  // 商品单位类型
  productUnit() {
    return request({
      url: '/mall/product/unit',
      method: 'GET'
    })
  },

  // 赠品接口
  // 新增接口
  addFreebie(data) {
    return request({
      url: '/mall/freebie',
      method: 'POST',
      data
    })
  },

  // 修改赠品
  updateFreebie(data) {
    return request({
      url: '/mall/freebie',
      method: 'PUT',
      data
    })
  },

  // 删除赠品
  deleteFreebie(id) {
    return request({
      url: '/mall/freebie/' + id,
      method: 'DELETE'
    })
  },

  // 赠品查询列表
  freebieList(pageNum, pageSize, data) {
    return request({
      url: '/mall/freebie/page/' + pageNum + '/' + pageSize,
      method: 'GET',
      params: data
    })
  },
  // 返回赠品类型
  freebieType() {
    return request({
      url: '/mall/freebie/type',
      method: 'GET'
    })
  }

}
