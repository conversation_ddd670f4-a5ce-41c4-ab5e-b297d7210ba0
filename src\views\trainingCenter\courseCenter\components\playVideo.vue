<template>
  <div class="container">
    <div class="video_main">
      <div style="padding: 10px 1px">
        <!-- <el-button type="primary" size="mini">下载视频</el-button> -->
        <el-button @click="goBack">返回</el-button>
        <span v-if="showStudy" style="display: inline-block; margin-left: 20px">
          <span>学习时间</span>
          <span>{{ countdownMinutes }}:{{ countdownSeconds < 10 ? '0' + countdownSeconds : countdownSeconds }}</span>
        </span>
      </div>
      <div v-if="this.playInfo.lessonType == 1" id="polyvplayer_video"></div>
      <div v-else style="height: 85vh" v-loading="videoLoading">
        <iframe id="iframeId" :src="'https://ksdocp.dxznjy.com/onlinePreview?url=' + playInfo.lessonFile.filePath" width="100%" height="100%"></iframe>
        <!-- <iframe  id="iframeId"  :src="'https://view.officeapps.live.com/op/view.aspx?src='+ playInfo.filePath" width='100%' height='100%' frameborder='1'> -->
        <!-- </iframe> -->
      </div>
    </div>
  </div>
</template>
<script>
  import { videoPlayer } from 'vue-video-player';
  import { Base64 } from '@/utils/base64';
  // trainingProgress
  import courseApi from '@/api/training/course';
  export default {
    name: '',
    components: { videoPlayer },
    props: {},
    data() {
      return {
        videoLoading: false,
        vodPlayerJs: 'https://player.polyv.net/resp/vod-player/latest/player.js',
        vid: '1723c88563ce5d7153988e8b74bd6b3b_1',
        playInfo: {},
        player: '',
        countdownMinutes: 3,
        // countdownMinutes: 1,
        countdownSeconds: 0,
        showStudy: false,
        attachmentStatusMap: {}
        // playerOptions: {
        //   playbackRates: [0.5, 1.0, 1.5, 2.0],
        //   autoplay: false,
        //   muted: false,
        //   loop: false,
        //   preload: "auto",
        //   language: "zh-CN",
        //   aspectRatio: "16:9",
        //   fluid: true,
        //   sources: [
        //     {
        //       type: "video/mp4",
        //       src: "1723c88563ce5d7153988e8b74bd6b3b_1",
        //     },
        //   ],
        //   poster: "", // 封面地址
        //   width: document.documentElement.clientWidth,
        //   notSupportedMessage: "加载中......", //允许覆盖Video.js无法播放媒体源时显示的默认信息。
        //   controlBar: {
        //     timeDivider: true, // 当前时间和持续时间的分隔符
        //     durationDisplay: true, // 显示持续时间
        //     remainingTimeDisplay: true, // 是否显示剩余时间功能
        //     fullscreenToggle: true, // 是否显示全屏按钮
        //   },
        // },
      };
    },
    computed: {},
    watch: {
      src: {
        handler(newval) {
          this.playerOptions.sources[0].src = newval;
          this.srcvideo = newval;
        }
      }
    },
    created() {
      this.playInfo = JSON.parse(window.localStorage.getItem('playVideoOrText'));
      this.changeState(2); //初始传2 学习中
      //  this.playInfo.filePath = encodeURIComponent(Base64.encode(this.playInfo.filePath))
      //lessonType 1视频 2文档
      if (this.playInfo.lessonType !== 1) {
        this.playInfo.lessonFile.filePath = encodeURIComponent(Base64.encode(this.playInfo.lessonFile.filePath));
        console.log('this.playInfo.lessonFile.filePath', this.playInfo.lessonFile.filePath);

        this.videoLoading = true;
      }
    },
    beforeDestroy() {
      clearInterval(this.timer);
    },
    mounted() {
      if (this.playInfo.lessonType == 1) {
        // console.log(this.playInfo.filePath,2222222);
        this.loadPlayerScript(this.loadPlayer);
      } else {
        let that = this;
        const iframe = document.querySelector('#iframeId');
        // 处理兼容行问题 兼容IE
        if (iframe.attachEvent) {
          iframe.attachEvent('onload', function () {
            // iframe加载完毕以后执行操作
            that.startTime();
          });
        } else {
          iframe.onload = function () {
            // iframe加载完毕以后执行操作
            that.startTime();
            that.videoLoading = false;
          };
        }
      }
    },
    methods: {
      loadPlayerScript(callback) {
        if (!window.polyvPlayer) {
          const myScript = document.createElement('script');
          myScript.setAttribute('src', this.vodPlayerJs);
          myScript.onload = callback;
          document.body.appendChild(myScript);
        } else {
          callback();
        }
      },
      startTime() {
        this.showStudy = true;
        this.timer = setInterval(() => {
          if (this.countdownSeconds === 0) {
            if (this.countdownMinutes === 0) {
              clearInterval(this.timer);
              // alert("倒计时结束");
              this.changeState(3);
              this.showStudy = false;
            } else {
              this.countdownMinutes--;
              this.countdownSeconds = 59;
            }
          } else {
            this.countdownSeconds--;
          }
        }, 1000);
      },
      changeState(caseXX) {
        let sysUserRoles = JSON.parse(window.localStorage.getItem('sysUserRoles'));
        courseApi
          .lessonProgress({ userId: sysUserRoles[0].userId, roleTag: window.localStorage.getItem('roleTag'), lessonId: this.playInfo.id, learningStatus: caseXX })
          .then((res) => {});
      },
      loadPlayer() {
        const polyvPlayer = window.polyvPlayer;
        this.player = polyvPlayer({
          wrap: '#polyvplayer_video',
          width: '100%',
          height: '85vh',
          vid: this.playInfo.lessonFile.filePath,
          // ban_seek:'on',
          ban_seek_by_limit_time: 'on'
        });
        let that = this;
        this.player.on('s2j_onPlayOver', function () {
          that.changeState(3);
        });
      },
      goBack() {
        this.$router.go(-1);
        window.localStorage.setItem('showStudy', 1);
      }
    },
    destroyed() {
      if (this.player) {
        this.player.destroy();
      }
    }
  };
</script>

<style scoped lang="scss">
  .container {
    width: 100%;
  }
  .video_main {
    width: 100%;
  }

  ::v-deep .video-js .vjs-big-play-button {
    width: 88px !important;
    height: 88px !important;
    border-radius: 50% !important;
    border: 0;
    line-height: 88px;
    font-size: 56px;
    // margin-left: -50px;
    // margin-top: -60px;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    object-fit: cover;
  }
  ::v-deep .video-js .vjs-control-bar {
    height: 58px;
    line-height: 58px;
    background: rgba(0, 0, 0, 0.24) !important;
    // background: #ac8282;
  }
  ::v-deep .vjs-button > .vjs-icon-placeholder:before {
    font-size: 40px;
  }
  ::v-deep .video-js .vjs-control {
    font-size: 22px;
    line-height: 58px;
  }
  ::v-deep .vjs-playback-rate .vjs-playback-rate-value {
    line-height: 58px;
  }
  ::v-deep .vjs-button > .vjs-icon-placeholder:before {
    line-height: 60px;
  }
  ::v-deep .vjs-remaining-time-display {
    font-size: 24px;
  }
  ::v-deep .video-js .vjs-big-play-button {
    background-color: rgba(0, 0, 0, 0.5);
  }
  ::v-deep .video-js .vjs-slider {
    background-color: rgba(255, 255, 255, 0.5);
  }
  // 视频封面
  ::v-deep .video-js .vjs-poster {
    background-size: cover !important;
  }
</style>
