<template>
  <div class="forgot-container">
    <!-- 忘记密码第一步 -->
    <div class="unpay-member clearfix">
      <div class="member-right">
        <div class="login_box center">
          <div class="login-inner">
            <div class="logo_box clearfix">
              <p class="right" style="color: #00b878">忘记密码</p>
            </div>
            <div class="login_content">
              <div class="el-form-item el-form-item--medium">
                <div class="el-form-item__content">
                  <div class="el-input el-input--medium el-input--prefix">
                    <span class="svg-container svg-container_login">
                      <svg-icon icon-class="user" />
                    </span>
                    <input id="UserName" type="text" autocomplete="off" maxlength="11" class="el-input__inner" placeholder="请输入您的手机号码" v-model="mobile" />
                  </div>
                </div>
              </div>
              <div class="el-form-item el-form-item--medium">
                <div class="el-form-item__content">
                  <div class="el-input el-input--medium el-input--prefix">
                    <span class="svg-container">
                      <svg-icon icon-class="key" />
                    </span>
                    <input id="UserName" autocomplete="off" class="el-input__inner" placeholder="请输入您的新密码" v-model="newPwd" :type="passwordNewType" />
                    <span class="svg-container-end" @click="showNewPwd">
                      <svg-icon :icon-class="passwordNewType=='password' ? 'eye' : 'eye-open'" />
                    </span>
                  </div>
                </div>
              </div>

              <div class="el-form-item el-form-item--medium">
                <div class="el-form-item__content">
                  <div class="el-input el-input--medium el-input--prefix">
                    <span class="svg-container">
                      <svg-icon icon-class="key" />
                    </span>
                    <input id="UserName" :type="passwordType" autocomplete="off" class="el-input__inner" placeholder="请再次输入您的新密码" v-model="confirmPwd" />
                    <span class="svg-container-end" @click="showPwd">
                      <svg-icon :icon-class="passwordType=='password' ? 'eye' : 'eye-open'" />
                    </span>
                  </div>
                </div>
              </div>

              <div class="el-form-item el-form-item--medium">
                <div class="el-form-item__content">
                  <div class="el-input el-input--medium el-input--prefix" style="width: 220px">
                    <span class="svg-container">
                      <svg-icon icon-class="password" />
                    </span>
                    <input id="VerCode" type="text" autocomplete="off" maxlength="6" class="el-input__inner" placeholder="请输入验证码" v-model="smsCode" />
                  </div>
                  <button id="get_code" type="button" class="code_duanxin_box" @click="getSmsClick(mobile)" :disabled="disabledSmsClick">{{ count }}</button>
                </div>
              </div>

              <div class="el-form-item el-form-item--medium">
                <div class="el-form-item__content">
                  <button id="btnNext" type="button" class="el-button login_btn el-button--primary el-button--medium" @click="confirm()"><span>修改密码</span></button>
                </div>
              </div>

              <ul class="login_router">
                <li><a @click="goBack()">返回登录</a></li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
    <Verify ref="verify" :captcha-type="'blockPuzzle'" :img-size="{ width: '375px', height: '200px' }" @success="sendSmg" />
  </div>
</template>

<script>
import forgotApi from '@/api/forgot';
import Verify from '@/components/verifition/Verify';
export default {
  data() {
    return {
      mobile: '',
      smsCode: '',
      newPwd: '',
      confirmPwd: '',
      disabledSmsClick: false, //点击事件
      count: '获取验证码',
      timer: null,
      passwordType: 'password',
      passwordNewType: 'password',
    };
  },
  components: {
    Verify
  },
  methods: {
    //发送验证码
    getSmsClick(mobile) {
      var patrn = /^[1]{1}\d{10}$/;
      this.disabledSmsClick = true;
      if (!this.mobile) {
        this.$message.error('请输入手机号码');
        this.disabledSmsClick = false;
        return false;
      }
      if (!patrn.test(this.mobile)) {
        this.$message.error('请输入11位有效的手机号码');
        this.disabledSmsClick = false;
        return false;
      }
      this.sendSmg();
    },
    sendSmg() {
      forgotApi.sendSmg(this.mobile).then((res) => {
        console.log(res);
        this.$message.success('短信验证码发送成功，请注意查收');
        var num = 60;
        if (!this.timer) {
          this.timer = setInterval(() => {
            if (num > 0) {
              num--;
              this.count = num + 's';
            } else {
              clearInterval(this.timer);
              this.timer = null;
              this.count = '重新获取验证码';
              this.disabledSmsClick = false;
            }
          }, 1000);
        }
      });
    },
    // 修改密码
    confirm() {
      var that = this;
      var patrn = /^\d{6}$/;
      if (!that.mobile) {
        that.$message.error('请输入手机号码');
        return false;
      }
      if (!that.smsCode) {
        that.$message.error('请输入验证码');
        return false;
      }
      if (!patrn.test(that.smsCode)) {
        that.$message.error('验证码为6位数字');
        return false;
      }
      if (!that.newPwd) {
        that.$message.error('新密码不能为空');
        return false;
      }
      if (!that.confirmPwd) {
        that.$message.error('确认密码不能为空');
        return false;
      }
      if (that.newPwd.length < 8) {
        that.$message.error('密码不能少于8个字符');
        return false;
      }
      if (that.newPwd.length > 50) {
        that.$message.error('密码不能多于50个字符');
        return false;
      }
      const regexForbidden = /[\u4e00-\u9fa5\s\W_]/; // 匹配汉字、空格和符号
      if (regexForbidden.test(that.newPwd)) {
        that.$message.error('密码不能包含汉字、空格和符号');
        return;
      }
      const regex = /^(?=.*[A-Za-z])(?=.*\d).+$/; // 确保密码包含字母和数字
      if (!regex.test(that.newPwd)) {
        that.$message.error('密码需同时包含字母和数字');
        return false;
      }
      if (that.newPwd != that.confirmPwd) {
        that.$message.error('两次输入密码不一致');
        return false;
      }
      var submitData = {
        mobile: that.mobile,
        newPwd: that.newPwd,
        smsCode: that.smsCode,
        source: 'admin',
        sysType: 1,
      };
      forgotApi.pswReset(submitData).then((res) => {
        if (res) {
          that.$message.success('密码修改成功');
          that.$router.push({
            path: '/login'
          });
        }
      });
    },
    // 返回登录
    goBack() {
      this.$router.push({
        path: '/login'
      });
    },
    showNewPwd() {
      if (this.newPwd == '') return;
      if (this.passwordNewType === 'password') {
        this.passwordNewType = '';
      } else {
        this.passwordNewType = 'password';
      }
    },
    showPwd() {
      if (this.confirmPwd == '') return;
      if (this.passwordType === 'password') {
        this.passwordType = '';
      } else {
        this.passwordType = 'password';
      }
    }
  }
};
</script>
<style scoped rel="assets/disk/slidercaptcha.min.css" />
<style rel="stylesheet/scss" lang="scss" scoped>
$bg: #2d3a4b;
$dark_gray: #d7dee3;
$light_gray: #01c176;

.forgot-container {
  width: 100%;
  height: 100%;
  background: url('../../assets/bg.jpg') no-repeat top center/cover;
}

.svg-container {
  position: absolute;
  top: -2px;
  left: 0;
  padding: 6px 5px 6px 15px;
  color: $dark_gray;
  vertical-align: middle;
  width: 50px;
  display: inline-block;

  &_login {
    font-size: 20px;
  }
}
.svg-container-end {
  position: absolute;
  color: $dark_gray;
  vertical-align: middle;
  display: inline-block;
  margin: 0 -25px;
  margin-top: 5px;
  font-size: 20px;
}
.bg-login {
  width: 100%;
  height: 100%;
  overflow-x: hidden;
}

.country_bg {
  /* background: url(../images/zhong.png) no-repeat; */
  vertical-align: -3px;
  display: inline-block;
  width: 22px;
  height: 16px;
}

#input {
  padding: 0 15px;
  height: 42px;
  line-height: 42px;
}

.buttom {
  width: 120px;
  position: absolute;
  top: 0;
  right: 0;
  height: 42px;
  border-radius: 6px;
  border: 1px solid #29b1e7;
  color: #29b1e7;
  display: inline-block;
  line-height: 1;
  white-space: nowrap;
  cursor: pointer;
  background: #fff;
  -webkit-appearance: none;
  text-align: center;
  box-sizing: border-box;
  outline: 0;
  margin: 0;
  transition: 0.1s;
  font-weight: 500;
  padding: 12px 20px;
  font-size: 14px;
}

.unpay-member {
  height: 100%;
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
}

.unpay-member .member-left {
  height: 100%;
  right: 510px;
  left: 0;
  position: absolute;
}

.unpay-member .member-left .title {
  width: 100%;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  color: #fff;
  font-size: 45px;
  text-align: center;
}

.member-right {
  width: 600px;
  height: 480px;
  position: absolute;
  top: 50%;
  margin-top: -240px;
  left: 50%;
  margin-left: -300px;
  background-color: #fff;
}

.login_box {
  min-height: 100%;
  box-sizing: border-box;
}

.login-inner {
  width: 100%;
  /*background: #fff;*/
  /*box-shadow: 0 2px 12px 0 rgba(0,0,0,.5);*/
}

.logo_box {
  width: 100%;
  text-align: center;
}

.logo_box .left img {
  width: 120px;
}

.right {
  margin: 0;
  padding-top: 40px;
  font-size: 24px;
  color: #3f3f3f;
}

.login_content {
  margin-top: 40px;
  padding: 0 70px;
  position: relative;
  z-index: 2;
}

.el-form-item {
  margin-bottom: 22px;
}

.login_box .el-form .el-form-item label {
  color: #666;
  padding-bottom: 0;
}

.el-form-item--medium .el-form-item__content,
.el-form-item--medium .el-form-item__label {
  line-height: 36px;
}

#label {
  line-height: 1;
  margin-bottom: 10px;
}

.el-form-item__label {
  display: inline-block;
  text-align: right;
  font-size: 14px;
  color: #606266;
  line-height: 40px;
  padding: 0 12px 0 0;
  box-sizing: border-box;
}

.el-form-item__content .el-input-group,
.el-form-item__label,
.el-tag .el-icon-close {
  vertical-align: middle;
}

.el-form-item__content {
  line-height: 40px;
  position: relative;
  font-size: 14px;
}

.el-input--medium {
  font-size: 14px;
}

.el-input {
  position: relative;
  font-size: 14px;
  display: inline-block;
  width: 100%;
  background-color: rgba(255, 255, 255, 0.9);
}

.el-input--prefix .el-input__inner {
  padding-left: 40px;
}

.el-input--medium .el-input__inner {
  height: 36px;
  line-height: 36px;
}

.el-input--prefix .el-input__inner {
  padding-left: 30px;
  height: 42px;
  line-height: 42px;
}

.login_box .el-input__inner {
  border-radius: 3px;
}

.el-input--prefix .el-input__inner {
  padding-left: 45px;
}

.el-input__inner {
  border-radius: 0;
  border-color: #b9b9b9;
}

.el-input__inner {
  -webkit-appearance: none;
  background-color: rgba(255, 255, 255, 0.9);
  background-image: none;
  border-radius: 4px;
  border: 1px solid #b9b9b9;
  box-sizing: border-box;
  color: #606266;
  display: inline-block;
  font-size: inherit;
  height: 40px;
  line-height: 40px;
  outline: 0;
  padding: 0 15px;
  transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
  width: 100%;
}

.el-input.is-active .el-input__inner,
.el-input__inner:focus {
  border-color: #00b878;
  outline: 0;
}

.el-input__inner:hover {
  border-color: #c0c4cc;
}

.el-input__prefix {
  left: 5px;
  transition: all 0.3s;
}

.el-input__prefix,
.el-input__suffix {
  position: absolute;
  top: 0;
  -webkit-transition: all 0.3s;
  height: 100%;
  color: #c0c4cc;
}

.login_icon_user {
  background-position: 6px 11px;
}

.login_icon_paw {
  background-position: -27px 11px;
}

.login_icon_code {
  background-position: -60px 11px;
}

.login_icon {
  display: block;
  height: 100%;
  width: 30px;
  text-align: center;
  line-height: 45px;
  /* background-image: url(../images/icon.png); */
  background-repeat: no-repeat;
}

.el-form-item__error {
  color: #f56c6c;
  font-size: 12px;
  line-height: 1;
  padding-top: 4px;
  position: absolute;
  top: 100%;
  left: 0;
}

.login_box .code_img_box {
  border: 1px solid #b9b9b9;
  position: absolute;
  right: 0;
  top: 0;
  height: 42px;
  line-height: 42px;
  box-sizing: border-box;
  width: 142px;
  text-align: center;
  border-radius: 3px;
  cursor: pointer;
}

.code_duanxin_box {
  border: 1px solid #45afa4;
  color: #45afa4;
  background: #fff;
  line-height: 42px;
  position: absolute;
  right: 0;
  top: 0;
  height: 42px;
  box-sizing: border-box;
  width: 130px;
  text-align: center;
  border-radius: 3px;
  cursor: pointer;
  font-size: 14px;
  outline: none;
}

.code_duanxin_box:disabled {
  border: 1px solid #ccc;
  color: #ccc;
}

.login_box .code_img_box img {
  height: 40px;
  width: 140px;
}

.el-form-item--medium .el-form-item__content,
.el-form-item--medium .el-form-item__label {
  line-height: 36px;
}

.el-form-item__content {
  line-height: 40px;
  position: relative;
  font-size: 14px;
}

.el-button--medium {
  padding: 10px 20px;
  font-size: 14px;
  border-radius: 4px;
}

.el-button--primary {
  color: #fff;
  background-color: #00b878 !important;
  border-color: #00b878 !important;
}

.el-button {
  display: inline-block;
  line-height: 1;
  white-space: nowrap;
  cursor: pointer;
  background: #fff;
  border: 1px solid #dcdfe6;
  color: #606266;
  -webkit-appearance: none;
  text-align: center;
  box-sizing: border-box;
  outline: 0;
  margin: 0;
  transition: 0.1s;
  font-weight: 500;
  padding: 12px 20px;
  font-size: 14px;
  border-radius: 4px;
}

.el-button,
.el-checkbox,
.el-step__icon-inner {
  -moz-user-select: none;
  -ms-user-select: none;
}

.el-button,
.el-checkbox,
.el-slider__button,
.el-step__icon-inner {
  -webkit-user-select: none;
}

.login_btn {
  width: 100%;
  background: #00b878;
  /*    border: 1px solid #e5e5e5;
*/
  color: #fff;
  height: 45px;
  border-radius: 3px;
  font-size: 17px;
  cursor: pointer;
  outline: none;
}

.login_router {
  text-align: center;
  cursor: pointer;
  color: #45afa4;
  padding-bottom: 35px;
  padding-left: 0;
}

.login_router a {
  color: #00b878;
}

.login_router li {
  display: inline-block;
  font-size: 14px;
  margin: 0 5px;
}

ol li,
ul li {
  list-style: none;
}
input[type='password']::-ms-reveal,
input[type='password']::-ms-clear {
  display: none;
}
// .member-right {
//     height: 362px;
//     position: absolute;
//     top: 50%;
//     margin-top: -181px;
// }
</style>
