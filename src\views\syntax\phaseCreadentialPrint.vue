<template>
  <div class="app-container">
    <div ref="print">
      <div id="print" class="clearfix">
        <img
          v-if="phase === '小学语法'"
          src="../../assets/xxpc.png"
          style="
            width: 1124px !important;
            height: 714px !important;
            display: block !important;
            margin: 0 auto !important;
          "
        />
        <img
          v-if="phase === '初中语法'"
          src="../../assets/czpc.png"
          style="
            width: 1124px !important;
            height: 714px !important;
            display: block !important;
            margin: 0 auto !important;
          "
        />
        <img
          v-if="phase === '高中语法'"
          src="../../assets/gzpc.png"
          style="
            width: 1124px !important;
            height: 714px !important;
            display: block !important;
            margin: 0 auto !important;
          "
        />

        <div
          style="
            width: 68% !important;
            position: absolute !important;
            left: 45% !important;
            margin-left: -29% !important;
            top: 300px !important;
            font-size: 30px;
            font-family: arial !important;
            color: #474747 !important;
          "
        >
          <div class="name">
            <a
              style="
                color: black !important;
                font-size: 35px !important;
                font-weight: 600 !important;
                margin-right: 5px !important;
              "
              >{{ studentName }}</a
            >同学
          </div>
          <div
            style="
              margin-top: 30px !important;
              line-height: 50px !important;
              text-indent: 3em;
            "
          >
            经过一段时间的努力学习，您已学会《{{ phase }}》共<a
              style="color: red !important"
              >{{ grammarCount }} </a
            >个语法点， <a style="color: red !important">{{ knowledgeCount }}</a
            >个知识点，顺利完成语法学习任务。
          </div>
          <div
            style="
              text-align: center !important;
              float: right !important;
              margin-top: 80px !important;
              margin-right: -40px !important;
              font-size: 24px !important;
            "
          >
            <span> 鼎校智能英语</span><br />
            <span>{{ addTime }}</span>
          </div>
        </div>
      </div>
    </div>
    <div class="buttom clearfix">
      <!-- <a style="background:#3dab93; margin-right: 20px;" @click="download()">下载</a> -->
      <a style="background: #3dab93" @click="print()">打印</a>
      <a style="background: #f0ad4e; margin-left: 20px" @click="goBack()"
        >返回</a
      >
    </div>
  </div>
</template>

<script>
// import html2canvas from 'html2canvas';
import { updatePrintStatusAPI } from "@/api/grammar/phaseCredentialList";

export default {
  data() {
    return {
      studentName: "",
      phase: "",
      grammarCount: "",
      knowledgeCount: "",
      phaseCreadentialPrintId: "",
      addTime: "",
    };
  },
  created() {
    this.getPhaseCredential();
    // console.log(this.$route.query);
  },
  methods: {
    getPhaseCredential() {
      this.studentName = this.$route.query.studentName;
      this.grammarCount = this.$route.query.grammarCount;
      this.phase = this.$route.query.phase;
      this.knowledgeCount = this.$route.query.knowledgeCount;
      this.addTime = this.$route.query.createTime;
      this.phaseCreadentialPrintId = this.$route.query.phaseCreadentialPrintId;
    },
    print() {
      this.getPhaseCredential();
      // this.$print(this.$refs.print)
      updatePrintStatusAPI({ id: this.phaseCreadentialPrintId, type: 1 }).then(
        () => {
          this.$print(this.$refs.print);
        }
      );
    },
    //返回按钮
    goBack() {
      this.$store.dispatch("delVisitedViews", this.$route);
      this.$router.go(-1);
      this.$router.push({
        path: "/grammar/phaseCreadentialList",
      });
    },
  },
};
</script>

<style scoped lang="scss">
.app-container {
  background-color: #f3f3f4;
}

.clearfix {
  zoom: 1;
}

.clearfix:before,
.clearfix:after {
  content: "";
  line-height: 0;
  display: table;
}

.clearfix:after {
  clear: both;
}

#print {
  width: 1124px;
  height: 714px;
  margin: 0 auto;
  position: relative;
  overflow: hidden;
}

.buttom {
  margin: 10px 50px 10px 0;
  color: #fff;
  cursor: pointer;
  text-align: right;
}

.buttom a {
  display: inline-block;
  color: #fff;
  width: 90px;
  height: 35px;
  border-radius: 5px;
  text-align: center;
  line-height: 35px;
}
</style>
