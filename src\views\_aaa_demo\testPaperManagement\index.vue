<!-- 试卷管理 -->
<template>
  <div class="app-container">
    <el-form :model="dataQuery" ref="queryForm" :inline="true">
      <el-form-item label="课程大类：">
        <el-select v-model="dataQuery.curriculumId" placeholder="请选择">
          <el-option v-for="item in curriculumList" :key="item.enCode" :value="item.id" :label="item.enName"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="试卷信息：">
        <el-input v-model.trim="dataQuery.examinePaperName" placeholder="请输入试卷名称或ID搜索" clearable></el-input>
      </el-form-item>
      <el-form-item label="学科：">
        <el-select v-model="dataQuery.courseSubjectId" filterable placeholder="请选择" clearable @change="updateChildOptions">
          <el-option v-for="item in subjectList" :key="item.id" :value="item.id" :label="item.nodeName"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="学段：">
        <el-select v-model="dataQuery.coursePeriodId" filterable placeholder="请选择" clearable>
          <el-option v-for="item in gradeList" :key="item.id" :value="item.id" :label="item.nodeName"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" style="margin-left: 5px" @click="reset">重置</el-button>
        <el-button type="primary" style="margin-right: 5px" @click="submitForm">查询</el-button>
      </el-form-item>
    </el-form>
    <div class="btnBox">
      <el-radio-group v-model="tabPosition" size="medium" @input="tabPositionClick">
        <el-radio-button label="3">全部</el-radio-button>
        <el-radio-button label="1">启用</el-radio-button>
        <el-radio-button label="0">禁用</el-radio-button>
        <el-radio-button label="2">草稿</el-radio-button>
      </el-radio-group>
      <el-button type="primary" style="margin-left: 5px" @click="addPaper">新增试卷</el-button>
    </div>
    <div class="container">
      <el-table class="common-table" v-loading="listLoading" :data="tableData" row-key="id" :default-sort="{ prop: 'sort', order: 'descending' }" max-height="62vh">
        <el-table-column prop="gradeId" label="序号" type="index" />
        <el-table-column prop="examinePaperCode" label="试卷ID" />
        <el-table-column prop="examinePaperName" label="试卷名称" />
        <el-table-column prop="isEnable" label="状态">
          <template slot-scope="{ row }">
            <span>{{ row.isEnable === 0 ? '禁用' : row.isEnable === 1 ? '启用' : row.isEnable === 2 ? '草稿' : '未知状态' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="curriculumName" label="课程大类" />
        <el-table-column prop="courseSubjectName" label="学科" />
        <el-table-column prop="coursePeriodName" label="学段" />
        <el-table-column prop="createTime" label="创建时间" />
        <el-table-column label="操作" align="center" width="220px">
          <template slot-scope="{ row }">
            <el-button size="mini" type="text" @click="viewQuestion(row)">查看</el-button>
            <el-button size="mini" type="text" @click="rewriteQuestion(row)">编辑</el-button>
            <el-button size="mini" type="text" v-if="row.isEnable !== 2" @click="editQuestion(row)">{{ row.isEnable === 1 ? '禁用' : '启用' }}</el-button>
            <el-button size="mini" type="text" @click="deleteQuestion(row)" class="link-left">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <div class="pagination">
        <el-col :span="24" style="margin-bottom: 20px; margin-top: 20px; padding-right: 20px">
          <el-pagination
            :current-page="tablePage.currentPage"
            :page-sizes="[10, 20, 30, 40, 50]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="tablePage.totalItems"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </el-col>
      </div>
    </div>
    <!-- 查看 -->
    <el-dialog title="查看" :visible.sync="viewDiaOpen" width="50%" top="50px" :close-on-click-modal="false">
      <el-form ref="importFrom" :model="importFrom" label-position="top" label-width="120px" style="width: 100%" v-loading="detailLoading">
        <h3>考试信息</h3>
        <el-row :gutter="20">
          <el-col :span="9">
            <el-form-item label="课程大类" label-width="80px">
              <el-tag>{{ importFrom.curriculumName }}</el-tag>
            </el-form-item>
          </el-col>
          <el-col :span="15">
            <el-form-item label="试卷名称" label-width="80px">
              <el-tag>{{ importFrom.examinePaperName }}</el-tag>
            </el-form-item>
          </el-col>
          <el-col :span="9">
            <el-form-item label="答题时间" label-width="80px">
              <span>
                整卷限时
                <el-tag>{{ importFrom.answerTime }}</el-tag>
                分钟
              </span>
            </el-form-item>
          </el-col>
          <el-col :span="15">
            <el-form-item label="考试周期" label-width="80px">
              <el-tag>{{ importFrom.examinePaperStartDate }}</el-tag>
              ~
              <el-tag>{{ importFrom.examinePaperEndDate }}</el-tag>
            </el-form-item>
          </el-col>
        </el-row>
        <h3>题目配置</h3>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="组卷方式" label-width="80px">
              <el-radio-group v-model="importFrom.isAiGroup">
                <el-radio :label="0" disabled>AI组卷</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="关联知识点" label-width="80px">
              <span>
                已选择
                <el-tag>{{ queryData.lenNum }}</el-tag>
                个知识点
              </span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item>
              <div style="margin-bottom: 10px">
                试卷题型
                <span style="margin-left: 20%">
                  试卷共
                  <el-tag>{{ queryData.titleNum }}</el-tag>
                  题,总分
                  <el-tag>{{ queryData.countNum }}</el-tag>
                  分
                </span>
              </div>
              <div v-for="(item, index) in importFrom.typeDtoList" :key="index">
                <span>
                  {{ getQuestionTypeName(item.questionsType) }}
                  共
                  {{ item.questionsNum }}
                  题，每题
                  {{ item.score }}
                  分
                </span>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="viewDiaOpen = false">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import { mapActions } from 'vuex';
  import testPaperAPI from '@/api/mathApi/testPaperManagementAPI';
  import testpaper from '@/api/mathApi/testPaperManagementAPI';
  import { mapGetters, mapState } from 'vuex';
  import { pageParamNames } from '@/utils/constants';
  import '/public/components/ueditor/themes/iframe.css';

  export default {
    name: 'testPaperManagement',
    data() {
      return {
        questionTypeList: [], // 题目类型
        tabPosition: 3, // 状态：3-全部 0-启用 1-禁用 2-草稿
        viewDiaOpen: false,
        importFrom: {
          isAiGroup: 0
        },
        queryData: {
          lenNum: 0,
          titleNum: 0,
          countNum: 0
        },
        dataQuery: {
          examinePaperName: null,
          curriculumId: null,
          courseSubjectId: null,
          coursePeriodId: null
        },
        subjectFilter: null,
        listLoading: true,
        detailLoading: false,
        tableData: [],
        // 分页
        tablePage: {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null
        },
        total: 0,
        questionShow: {
          qType: '',
          dialog: false,
          question: null,
          loading: false
        },
        curriculumList: [], // 课程大类
        subjectList: [], // 学科
        gradeList: [] // 学段
      };
    },
    created() {
      this.getCurriculum();
      this.getType();
      this.submitForm();
    },
    watch: {
      // 监听课程大类的变化
      'dataQuery.curriculumId': function (newVal) {
        if (newVal) {
          this.getSubjectList({ curriculumId: newVal, nodeLevel: 2 });
        } else {
          this.subjectList = [];
          this.gradeList = [];
        }
      },
      $route: function (newVal, oldVal) {
        console.log('路由变化了', newVal, oldVal);
        if (oldVal.name == 'addPaper' && oldVal.path == '/_aaa_demo/addPaper') {
          this.deleteView(oldVal.path, oldVal.name);
        }
      }
    },
    methods: {
      ...mapActions(['delVisitedViews']),
      deleteView(path, name) {
        // 定义要删除的视图对象，需包含 path 和 name 属性
        const viewToDelete = {
          path: path,
          name: name
        };
        this.delVisitedViews(viewToDelete).then((visitedViews) => {
          console.log('删除后剩余的 visitedViews:', visitedViews);
          this.submitForm();
        });
      },
      addPaper() {
        this.$router.push({ path: '/_aaa_demo/addPaper' });
      },
      // 获取课程大类
      getCurriculum() {
        testpaper.getCurriculum().then((res) => {
          if (res.success) {
            this.curriculumList = res.data;
            this.dataQuery.curriculumId = this.curriculumList[0].id;
            this.getPageList();
          }
        });
      },
      getSubjectList(query) {
        testpaper.getSubject(query).then((res) => {
          this.subjectList = res.data;
        });
      },
      updateChildOptions() {
        // 根据选中的父选项 ID 找到对应的父选项对象
        const selectedParentObj = this.subjectList.find((item) => item.id === this.dataQuery.courseSubjectId);
        if (selectedParentObj) {
          // 更新子选项列表
          this.gradeList = selectedParentObj.childList;
        } else {
          this.gradeList = [];
        }
        // 清空子选项的选择
        this.dataQuery.coursePeriodId = '';
      },
      reset() {
        this.dataQuery.examinePaperName = null;
        this.dataQuery.courseSubjectId = null;
        this.dataQuery.coursePeriodId = null;
        this.tablePage.currentPage = 1;
        this.tablePage.size = 10;
        this.getPageList();
      },
      submitForm() {
        this.tablePage.currentPage = 1;
        this.tablePage.size = 10;
        this.getPageList();
      },
      tabPositionClick() {
        this.getPageList();
      },
      getPageList() {
        this.listLoading = true;
        let data = {};
        data.curriculumId = this.dataQuery.curriculumId || null;
        data.examinePaperName = this.dataQuery.examinePaperName || null;
        data.courseSubjectId = this.dataQuery.courseSubjectId || null;
        data.coursePeriodId = this.dataQuery.coursePeriodId || null;
        data.pageNum = this.tablePage.currentPage;
        data.pageSize = this.tablePage.size;
        if (this.tabPosition == 3) {
          data.isEnable = null;
        } else {
          data.isEnable = this.tabPosition;
        }
        testPaperAPI.getPaperList(data).then((res) => {
          this.tableData = res.data.data;
          this.listLoading = false;
          // 设置后台返回的分页参数
          pageParamNames.forEach((name) => this.$set(this.tablePage, name, parseInt(res.data[name] || 0)));
        });
      },
      getDetalial(row) {
        this.queryData.lenNum = 0;
        this.queryData.titleNum = 0;
        this.queryData.countNum = 0;
        return new Promise((resolve, reject) => {
          testPaperAPI
            .getPaperDetail({ id: row.id })
            .then((res) => {
              if (res.success) {
                this.importFrom = res.data;
                this.queryData.lenNum = res.data.courseKnowledgeDtoList.length;
                this.importFrom.typeDtoList.forEach((item) => {
                  this.queryData.titleNum += item.questionsNum;
                  this.queryData.countNum += item.questionsNum * item.score;
                });
                resolve();
              } else {
                reject(new Error('获取试卷详情失败'));
              }
              this.detailLoading = false;
            })
            .catch((error) => {
              reject(error);
              this.detailLoading = false;
            });
        });
      },
      viewQuestion(row) {
        this.getDetalial(row);
        this.viewDiaOpen = true;
        this.detailLoading = true;
      },
      async rewriteQuestion(row) {
        await this.getDetalial(row);
        // 对 importFrom 对象进行序列化处理
        const importFromData = encodeURIComponent(JSON.stringify(this.importFrom));
        this.$router.push({
          path: '/_aaa_demo/addPaper',
          query: { importFrom: importFromData }
        });
      },
      editQuestion(row) {
        let data = {
          id: row.id,
          status: row.isEnable === 0 || row.isEnable === 2 ? 1 : 0
        };
        testPaperAPI.openPaperItem(data).then((res) => {
          if (res.success) {
            this.$message.success(res.message);
            this.getPageList();
          }
        });
      },
      deleteQuestion(row) {
        let _this = this;
        this.$confirm('是否删除该题目？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          testPaperAPI.deletePaperItem({ id: row.id }).then((re) => {
            if (re.success) {
              _this.$message.success(re.message);
              _this.getPageList();
            }
          });
        });
      },
      // 获取题目类型
      async getType() {
        const res = await testpaper.getPaperType({ type: 'testPaperQuestionType' });
        if (res.success) {
          this.questionTypeList = res.data;
        }
      },
      // 分页
      handleSizeChange(val) {
        this.tablePage.size = val;
        this.getPageList();
      },
      handleCurrentChange(val) {
        this.tablePage.currentPage = val;
        this.getPageList();
      },
      getQuestionTypeName(type) {
        const questionType = this.questionTypeList.find((item) => item.value === type);
        return questionType ? questionType.desc : '';
      }
    },
    computed: {
      ...mapGetters('enumItem', ['enumFormat', 'subjectFormat', 'pageFormat']),
      ...mapState('enumItem', {
        // gradeList: state => state.question.gradeList,
        questionType: (state) => state.question.typeList,
        editUrlEnum: (state) => state.question.editUrlEnum
      })
    }
  };
</script>

<style lang="less" scoped>
  /deep/.el-dialog__header {
    border-bottom: 1px solid #dfdfdf;
  }
  .btnBox {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 50px;
    margin-bottom: 20px;
  }
  .container {
    width: 100%;
    // height: 100%;
    height: 72vh;
    box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.2);
    padding: 18px;
    box-sizing: border-box;
    border-radius: 6px;
    position: relative;
    .pagination {
      position: absolute;
      bottom: 20px;
      right: 20px;
    }
  }
  /* 表格区域样式 */
  .add-course-btn {
    margin-bottom: 20px;
    display: flex;
    justify-content: flex-end;
  }
  ::v-deep .common-table .el-table__header th {
    background-color: #f0f0f0;
  }
  ::v-deep .el-pagination {
    text-align: right;
  }
  .disabled {
    color: #aaa;
  }
</style>
