<template>
  <div class="app-container">
    <el-form :model="form" ref="form" label-width="100px" v-loading="formLoading" :rules="rules">
      <el-form-item label="分类：" prop="titleType">
        <el-select v-model="form.titleType" disabled>
          <el-option v-for="item in titleType" :key="item.value" :value="item.value" :label="item.label"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="题型：" prop="questionType">
        <el-select v-model="form.questionType" disabled>
          <el-option v-for="item in questionType" :key="item.value" :value="item.value" :label="item.label"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="题目：" prop="title">
        <el-input placeholder="请输入题目" v-model="form.title" >
          <i @click="inputClick(form,'title')" slot="suffix" class="el-icon-edit-outline"
             style="line-height: 36px;font-size: 20px;cursor: pointer;"></i>
        </el-input>
      </el-form-item>
      <el-form-item label="单字：" required>
        <el-form-item :key="index" v-for="(item,index) in form.customItems">
          <div class="question-item-label">
            <el-input v-model="item.label" type="textarea" :rows="4" @blur="handInput"/>
            <el-input v-model="item.value" type="hidden"/>
          </div>
        </el-form-item>
      </el-form-item>
      <el-form-item label="答案：" required>
        <el-form-item :key="index" v-for="(item,index) in form.items">
          <div class="question-item-label">
            <el-input v-model="item.label=index+1" style="width:50px;marginRight:5px;" readonly/>
            <el-input v-model="item.value"/>
            <el-button style="marginLeft:5px" type="danger" size="mini" icon="el-icon-delete"
                       @click="answerItemRemove(index)"
            ></el-button>
          </div>
        </el-form-item>
      </el-form-item>
      <el-form-item label="说明：" prop="questionExplain" required>
        <el-input v-model="form.questionExplain" @focus="inputClick(form,'questionExplain')"/>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="submitForm">提交</el-button>
        <el-button v-show="form.id===null" @click="resetForm">重置</el-button>
        <el-button type="success" @click="answerItemAdd">添加答案</el-button>
      </el-form-item>
    </el-form>
    <el-dialog :visible.sync="richEditor.dialogVisible" append-to-body :close-on-click-modal="false"
               style="width: 100%;height: 100%" :show-close="false" center
    >
      <Ueditor @ready="editorReady"/>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="editorConfirm">确 定</el-button>
        <el-button @click="richEditor.dialogVisible = false">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import Ueditor from '@/components/Ueditor'
import complexApi from '@/api/paper/complex-question'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import { mapGetters, mapState } from 'vuex'

export default {
  components: {
    Ueditor
  },
  data() {
    return {
      fileList: [],
      importFrom: {
        file: null
      },
      randomLength: undefined,
      form: {
        id: null,
        title: '',
        titleType: 'AUDIO',
        questionType: 'SPELL',
        customItems: [{ label:'',value:''} ],
        items: [  {
          label: '', value: ''
        }],
        customInfo:'',
        questionExplain: ''
      },
      formLoading: false,
      rules: {
        title: [
          { required: true, message: '请输入题目', trigger: 'blur' }
        ],
        questionExplain: [
          { required: true, message: '请输入题目说明', trigger: 'blur' }
        ],
        titleType: [
          { required: true, message: '请选择分类', trigger: 'blur' }
        ],
        questionType: [
          { required: true, message: '请选择题型', trigger: 'blur' }
        ]
      },
      richEditor: {
        dialogVisible: false,
        object: null,
        parameterName: '',
        instance: null
      }
    }
  },
  created() {
    let id = this.$route.query.id
    let _this = this
    if (id && parseInt(id) !== 0) {
      _this.formLoading = true
      complexApi.select(id).then(re => {
        _this.form = re.data;
        if (this.form.customInfo){
          _this.form.customItems = JSON.parse(this.form.customInfo);
        }
        _this.formLoading = false
      })
    }
  },
  methods: {
    handInput(event){
      let val = event.target.value;
      if (!val){
        return;
      }
      if (this.form.customItems.filter((i) => {
        return i.label===val;
      }).length>1){
        this.$message.error("重复的字符串，请重新输入！")
        return;
      }
      let arr = val.match(/[a-z]+[\-\']?[a-z]*/ig);
      let index = this.form.customItems.findIndex((arr) => arr.label === val);//数组下标
      if (arr!=null&&arr.length>0){
        this.form.items[index].value = this.form.items[index].value+" "+ arr.join(" ");
      }
    },
    test(str) {
      // arr是需要解析的数组
      let arr = str.split('');
      //objGroup是输出的对象
      let objGroup = arr.reduce(function(obj, name) {
        obj[name] = obj[name] ? ++obj[name] : 1
        return obj
      }, {})
      return objGroup;
    },
    editorReady(instance) {
      this.richEditor.instance = instance
      let currentContent = this.richEditor.object[this.richEditor.parameterName]
      this.richEditor.instance.setContent(currentContent)
      // 光标定位到Ueditor
      this.richEditor.instance.focus(true)
    },
    inputClick(object, parameterName) {
      this.richEditor.object = object
      this.richEditor.parameterName = parameterName
      this.richEditor.dialogVisible = true
    },
    editorConfirm() {
      let content = this.richEditor.instance.getContent()
      this.richEditor.object[this.richEditor.parameterName] = content
      this.richEditor.dialogVisible = false
    },
    answerItemRemove(index) {
      if (this.form.items.length===1){
        return false;
      }
      this.form.items.splice(index, 1)
    },
    answerItemAdd() {
      let items = this.form.items
      items.push({ label: '',value: undefined })
    },
    submitForm() {
      let _this = this
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.formLoading = true
          //上传execel文件
          let file = new FormData()
          file.append('file', this.importFrom.file)
          this.form.customInfo = JSON.stringify(this.form.customItems);
          file.append('map', JSON.stringify(this.form))
          complexApi.edit(file).then(re => {
            if (re.success) {
              _this.$message.success(re.message)
              _this.$router.push({ path: '/paper/audio' })
            } else {
              _this.$message.error(re.message)
              this.formLoading = false
            }
          }).catch(e => {
            this.formLoading = false
          })
        } else {
          return false
        }
      })
    },
    resetForm() {
      let lastId = this.form.id
      this.$refs['form'].resetFields()
      this.form = {
        id: null,
        title: '',
        titleType: 'AUDIO',
        questionType: 'SPELL',
        customItems:  [{ label:'',value: ''} ],
        items: [  {
          label: '', value: ''
        }],
        customInfo:'',
        questionExplain: ''
      }
      this.form.id = lastId
    },
  },
  computed: {
    ...mapGetters('enumItem', ['enumFormat']),
    ...mapState('enumItem', {
      titleType: state => state.complexQuestion.titleType,
      questionType: state => state.complexQuestion.questionType
    })
  }
}
</script>

<style lang="less" scoped>
.question-item-label {
  display: flex;
  margin-bottom: 12px;
}
</style>
