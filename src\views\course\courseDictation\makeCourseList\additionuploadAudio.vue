<template>
  <!-- 上传音频 -->
  <div class="container_box">
    <div class="uploadAudio_table">
      <el-table
        :data="readList"
        style="width: 80%; margin-right: 8px"
        class="common-table"
        stripe
        border
        :default-sort="{ prop: 'date', order: 'descending' }"
        v-loading="uploadTableLoading"
        :span-method="videoSpanMethod"
      >
        <el-table-column v-for="item in tableColumn" :key="item.prop" :prop="item.prop" :label="item.label">
          <template slot-scope="scope">
            <div v-if="item.prop === 'wordSyllable'">
              {{ scope.row.wordSyllable }}
            </div>
            <div v-else-if="item.prop === 'status'">
              <i v-if="scope.row.wordSyllableAudioUrl" class="el-icon-circle-check" style="color: #13ce66; font-size: 16px"></i>
            </div>
            <div v-else-if="item.prop === 'wordId'">
              <el-upload
                multiple
                ref="uploadVideo"
                class="upload-demo"
                v-loading="readUploadLoading"
                :http-request="(params) => readUploadHttp(params, scope.row)"
                :show-file-list="true"
                :file-list="readFileList.find((e) => e.id == scope.row.wordId).list"
                action=""
                :on-success="readHandleSuccess"
                :on-remove="(file, fileList) => readHandleRemove(file, fileList, scope.row)"
                :on-change="(file, fileList) => videoHandleUpload(file, fileList, scope.row)"
                :before-upload="(file, fileList) => readBeforeUpload(file, fileList, scope.row)"
              >
                <div class="el-upload__text">
                  <el-button size="small" type="primary">
                    <i class="el-icon-plus"></i>
                    上传
                  </el-button>
                </div>
              </el-upload>
            </div>
            <div v-else-if="item.prop === 'submitId'">
              <el-button type="success" @click="onPySave(scope.row)" size="small" :disabled="scope.row.readDisabled">保存</el-button>
            </div>
            <div v-else>
              {{ scope.row[item.prop] }}
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>
<script>
  import { getMergeCells } from '../components/table.js';
  import courseAdditionList from '@/api/courseAdditionList';
  import { ossPrClient } from '@/api/alibaba';
  export default {
    name: 'uploadAudio',
    props: {
      dialogVisible: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        tableColumn: [
          { prop: 'word', label: '单词' },
          { prop: 'wordSyllable', label: '拆分词' },
          { prop: 'status', label: '上传状态' },
          { prop: 'wordId', label: '上传音频' },
          { prop: 'submitId', label: '操作' }
        ],
        readList: [], //单词划拼读,划音节
        uploadTableLoading: false,
        readUploadLoading: false, //拼读上传
        readWordList: [],
        readFileList: [],
        readtampFileList: [], //拼读临时存放上传文件
        courseData: JSON.parse(this.$route.query.courseData),
        oldReadList: []
      };
    },
    computed: {
      spanArr() {
        if (!this.tableColumn.length) return [];
        const mergeCols = ['word', 'wordId', 'submitId']; // 需要合并的列（字段）
        return getMergeCells(this.readList, this.tableColumn, mergeCols);
      }
    },
    watch: {},
    created() {
      ossPrClient();
      this.getTableData();
    },
    methods: {
      //获取音频列表信息
      getTableData() {
        this.uploadTableLoading = true;
        courseAdditionList.searchAudio({ courseCode: this.courseData.courseCode }).then((res) => {
          if (res.code === 20000) {
            this.oldReadList = res.data.pdAttachCourseWordAudioCoList;
            this.readList = [];
            res.data.pdAttachCourseWordAudioCoList.forEach((e) => {
              e.pdWordSplitInfoDtoList.forEach((o) => {
                //拆分单词音频
                this.readList.push({
                  wordId: e.id,
                  word: e.word,
                  submitId: e.id,
                  wordSyllable: o.split,
                  status: o.audioUrl ? 1 : 0,
                  wordSyllableAudioUrl: o.audioUrl,
                  remoteAudioUrl: o.audioUrl,
                  readDisabled: true
                });
              });
              //完整单词音频
              this.readList.push({
                wordId: e.id,
                word: e.word,
                submitId: e.id,
                wordSyllable: e.word,
                status: e.wordAudioUrl ? 1 : 0,
                wordSyllableAudioUrl: e.wordAudioUrl,
                remoteAudioUrl: e.wordAudioUrl,
                readDisabled: true
              });
            });
            this.readList.forEach((e) => {
              this.readFileList.push({ id: e.wordId, list: [] });
            });
          }
        });

        this.uploadTableLoading = false;
      },
      //上传音频列表批量上传操作合并
      videoSpanMethod({ row, column, rowIndex, columnIndex }) {
        return this.spanArr[rowIndex][columnIndex];
      },
      //随机字符串
      generateRandomString(length) {
        const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        let result = '';
        const charactersLength = characters.length;
        for (let i = 0; i < length; i++) {
          result += characters.charAt(Math.floor(Math.random() * charactersLength));
        }
        return result;
      },
      //拼读单词音频上传↓↓↓↓↓
      readUploadHttp({ file }, row) {
        const randomString = this.generateRandomString(6);
        const fileName = `manage/${Date.parse(new Date()) + '_' + randomString}${file.name}`;
        const isReadRepeat = this.readFileList.find((e) => e.id == row.wordId).list.filter((f) => f.name === file.name).length > 0;
        if (isReadRepeat) {
          this.readFileList.find((e) => e.id == row.wordId).list = Array.from(new Set(this.readFileList.find((e) => e.id == row.wordId).list));
          return;
        }
        this.$nextTick(function () {
          if (ossPrClient() && ossPrClient().put) {
            ossPrClient()
              .put(fileName, file)
              .then(({ res, url, name }) => {
                if (res && res.status === 200) {
                  this.readFileList.find((e) => e.id == row.wordId).list = this.readtampFileList;
                  console.log(`上传音频3`, res, url, name);
                  this.readAudioStatus(file, url, row);
                  this.readUploadLoading = false;
                  this.$nextTick(() => {
                    this.readUploadLoading = false;
                  });
                }
              })
              .catch((err) => {
                this.readUploadLoading = false;
              });
          }
        });
      },
      readAudioStatus(file, url, row) {
        this.readUploadLoading = true;
        let isWord = false;
        if (file && file.name) {
          this.readList.forEach((f) => {
            if (f.wordSyllable === file.name.split('.')[0] && f.word == row.word) {
              f.wordSyllableAudioUrl = url;
              if (f.wordSyllable == f.word) {
                isWord = true;
              }
            }
          });
          this.readUploadLoading = false;
          let readWordList = this.readList.filter((res) => res.word === row.word);
          this.replaceList(readWordList);
          row['readDisabled'] = this.getReadDisabled(row);
        }
      },

      getReadDisabled(row) {
        let filterList = this.readWordList.filter((res) => res.word === row.word);
        return !filterList.every((item) => item['wordSyllableAudioUrl'] !== null && item['wordSyllableAudioUrl'] !== undefined && item['wordSyllableAudioUrl'] !== '');
      },
      replaceList(readWordList) {
        console.log(readWordList);
        if (this.readWordList.length == 0) {
          this.readWordList.push(...readWordList);
          return;
        }
        readWordList.forEach((newItem) => {
          let index = this.readWordList.findIndex((oldItem) => oldItem.wordId === newItem.wordId && oldItem.word === newItem.word && newItem.wordSyllable === oldItem.wordSyllable);
          if (index !== -1) {
            this.readWordList[index] = newItem;
          } else {
            this.readWordList.push(newItem);
          }
        });
      },
      // 拼读保存
      onPySave(row) {
        let readList = [];
        readList = this.oldReadList.filter((item) => item.id === row.wordId);
        this.readWordList.forEach((res) => {
          readList[0].pdWordSplitInfoDtoList.forEach((item) => {
            if (res.wordId === readList[0].id && res.wordSyllable === item.split && res.wordSyllableAudioUrl != '') {
              item.audioUrl = res.wordSyllableAudioUrl;
            }
          });
        });
        //整个单词音频
        let allWordUrl = this.readWordList.find((f) => f.wordSyllable === readList[0].word)?.wordSyllableAudioUrl;
        if (allWordUrl) {
          readList[0].wordAudioUrl = allWordUrl;
        }
        let audioUrl = {
          courseCode: this.courseData.courseCode,
          pdAttachCourseWordAudioCoList: readList
        };
        courseAdditionList.uploadAudio(audioUrl).then((res) => {
          if (res.code === 20000) {
            this.$message.success('保存成功');
            this.refreshReadList();
            row.readDisabled = true;
          }
        });
      },
      refreshReadList() {
        for (let i = 0; i < this.readList.length; i++) {
          if (this.readList[i].wordSyllableAudioUrl !== '' && this.readList[i].remoteAudioUrl === '') {
            this.readList[i].remoteAudioUrl = this.readList[i].wordSyllableAudioUrl;
          }
        }
      },
      readHandleSuccess(file, fileList, row) {
        // 处理上传成功后的逻辑
        row.uploadedFileName = file.name; // 更新已上传文件名
      },
      //拼读上传移除
      readHandleRemove(file, fileList, row) {
        this.readUploadLoading = true;
        if (file && file.name) {
          console.log(row);
          this.readList.forEach((f) => {
            if (f.wordSyllable === file.name.split('.')[0] && row.word === f.word) {
              f.status = '';
              let remoteAudioUrl = f.remoteAudioUrl;
              if (remoteAudioUrl) {
                f.wordSyllableAudioUrl = remoteAudioUrl;
              } else {
                f.wordSyllableAudioUrl = '';
              }
            }
          });
          row.readDisabled = true;
        }
        this.readUploadLoading = false;
        this.readFileList.find((e) => e.id == row.wordId).list = this.readFileList.find((e) => e.id == row.wordId).list.filter((e) => e.name != file.name);
      },
      uniqueObjectsByPropertyMap(arr, property) {
        const map = new Map();
        arr.forEach((item) => {
          const key = JSON.stringify(item[property]);
          map.set(key, item);
        });
        return Array.from(map.values());
      },
      isAudio(file) {
        return /\.(mp3|wav|mid|wma|ra|vqf|mov|amr)$/.test(file.name);
      },
      readBeforeUpload(file, fileList, row) {
        const arr = this.readList.filter((res) => res.word === row.word);
        const isFileName = arr.filter((f) => f.wordSyllable === file.name.split('.')[0])?.length > 0;
        const isRepeat = this.readFileList.find((e) => e.id == row.wordId).list.filter((f) => f.name === file.name).length > 0;

        if (!this.isAudio(file)) {
          this.$message.warning('只能上传音频文件！');
          return false;
        } else if (!isFileName) {
          this.$message.warning('只能上传检测库内此单词文件！');
          return false;
        } else if (isRepeat) {
          this.$message.warning('已经上传过该单词文件！');
          return;
        }
      },
      videoHandleUpload(file, fileList, id) {
        this.readtampFileList = fileList;
        this.readtampFileList = this.uniqueObjectsByPropertyMap(this.readtampFileList, 'name');
      }
    }
  };
</script>
<style lang="less" scoped>
  .uploadAudio_table {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
  }
  ::v-deep .el-upload-list {
    width: auto !important;
  }
</style>
