<template>
  <div class="app-container">
    <el-form
      :inline="true"
      ref="queryForm"
      class="container-card"
      label-width="110px"
    >
      <el-form-item label="语言：">
        <el-select
          v-model="dataQuery.languageType"
          placeholder="全部"
          clearable
        >
          <el-option
            v-for="item in [
              { label: '中文', value: 'cn' },
              { label: '英文', value: 'en' },
            ]"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="内容：">
        <el-input
          v-model="dataQuery.content"
          placeholder="请输入内容"
          clearable
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handlePage()"
          >搜索</el-button
        >
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-re" @click="resetQuery()">重置</el-button>
      </el-form-item>
    </el-form>
    <el-col :span="24" style="margin-bottom: 30px">
      <el-button
        type="success"
        icon="el-icon-plus"
        size="mini"
        @click="handleAdd()"
        >新增</el-button
      >
      <el-button
        type="primary"
        icon="el-icon-edit-outline"
        size="mini"
        @click="$router.push({ path: '/schedule/spokenType' })"
        >编辑类型
      </el-button>
    </el-col>

    <el-table
      class="common-table"
      v-loading="tableLoading"
      :data="tableData"
      style="width: 100%; margin-bottom: 20px"
      row-key="id"
      border
      default-expand-all
    >
      <el-table-column prop="id" label="ID"></el-table-column>
      <el-table-column prop="languageType" label="语言">
        <template slot-scope="scope">
          {{ scope.row.languageType === "cn" ? "中文" : "英文" }}
        </template>
      </el-table-column>
      <el-table-column label="操作">
        <template slot-scope="scope">
          <el-button type="primary" size="mini" @click="handleEdit(scope.row)"
            >编辑</el-button
          >
          <el-button type="danger" size="mini" @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
      <el-table-column prop="spokenTypeName" label="类型"></el-table-column>
      <el-table-column prop="grade" label="年级" :formatter="gradeFormatter" />
      <el-table-column
        prop="content"
        label="内容"
        show-overflow-tooltip
      ></el-table-column>
    </el-table>
    <!-- 分页 -->
    <el-col :span="24" style="margin-bottom: 20px">
      <el-pagination
        :current-page="tablePage.currentPage"
        :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="tablePage.totalItems"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-col>

    <!-- 添加弹窗 -->
    <el-dialog
      :title="`${form.id ? '编辑' : '新建'}口语测评`"
      :visible.sync="open"
      width="60%"
      :close-on-click-modal="false"
      @close="close"
    >
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        label-width="120px"
        style="width: 70%"
      >
        <el-form-item label="语言：" prop="languageType">
          <el-select
            v-model="form.languageType"
            clearable
            filterable
            placeholder="请选择"
            @change="typeChange"
          >
            <el-option
              v-for="item in [
                { label: '中文', value: 'cn' },
                { label: '英文', value: 'en' },
              ]"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="类型：" prop="spokenTypeId">
          <el-select
            v-model="form.spokenTypeId"
            clearable
            filterable
            placeholder="请选择语言"
          >
            <el-option
              v-for="item in typeOptions"
              :key="item.typeId"
              :label="item.name"
              :value="item.typeId"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="年级：" prop="grade">
          <el-select
            v-model="form.grade"
            clearable
            filterable
            placeholder="请选择年级"
          >
            <el-option
              v-for="item in gradeList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="内容：" prop="content">
          <el-input
            type="textarea"
            v-model="form.content"
            :rows="6"
            maxlength="150"
            show-word-limit
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="submitForm()"
          >确定</el-button
        >
        <el-button size="mini" @click="close">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import spokenTestApi from "@/api/studyroom/spokenTest";
import { pageParamNames } from "@/utils/constants";

export default {
  data() {
    return {
      gradeList: [
        { grade: "18", value: "幼儿园" },
        { label: "一年级", value: 1 },
        { label: "二年级", value: 2 },
        { label: "三年级", value: 3 },
        { label: "四年级", value: 4 },
        { label: "五年级", value: 5 },
        { label: "六年级", value: 6 },
        { label: "七年级", value: 7 },
        { label: "八年级", value: 8 },
        { label: "九年级", value: 9 },
        { label: "高一", value: 10 },
        { label: "高二", value: 11 },
        { label: "高三", value: 12 },
      ],
      typeOptions: [],
      open: false,
      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null,
      },
      tableData: [],
      dataQuery: {
        languageType: "",
        content: "",
      },
      form: {},
      // 表单校验
      rules: {
        languageType: [
          { required: true, message: "请选择语言", trigger: "blur" },
        ],
        spokenTypeId: [
          { required: true, message: "请选择类型", trigger: "blur" },
        ],
        grade: [{ required: true, message: "请选择年级", trigger: "blur" }],
        content: [{ required: true, message: "请输入内容", trigger: "blur" }],
      },
    };
  },
  created() {
    this.getPageList();
  },
  methods: {
    gradeFormatter(row, column, cellValue, index) {
      for (let item of this.gradeList) {
        if (item.value === cellValue) {
          return item.label;
        }
      }
    },
    typeChange(val) {
      this.form.spokenTypeId = null;
      this.typeOptions = [];
      if (val) {
        this.getType();
      }
    },
    getType() {
      let data = {
        languageType: this.form.languageType,
      };
      spokenTestApi.getType(data).then((res) => {
        this.typeOptions = res.data;
      });
    },
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (!this.form.id) {
            spokenTestApi
              .create(this.form)
              .then((response) => {
                this.$message.success("提交成功！");
                this.open = false;
                this.getPageList();
              })
              .catch((err) => {});
          } else {
            spokenTestApi
              .edit(this.form)
              .then((response) => {
                this.$message.success("提交成功！");
                this.open = false;
                this.getPageList();
              })
              .catch((err) => {});
          }
        }
      });
    },
    /** 编辑按钮*/
    handleEdit(row) {
      spokenTestApi.detail(row.id).then((res) => {
        this.form = res.data;
        this.getType();
        this.open = true;
      });
    },
    //删除
    handleDelete(row) {
      this.$confirm("确定要删除吗？", "删除口语测评", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        spokenTestApi.delete(row.id).then((res) => {
          this.$message.success("删除成功！");
          this.getPageList();
        });
      });
    },
    getPageList() {
      this.tableLoading = true;
      this.dataQuery.pageNum = this.tablePage.currentPage;
      this.dataQuery.pageSize = this.tablePage.size;
      spokenTestApi.list(this.dataQuery).then((res) => {
        this.tableData = res.data.data;
        this.tableLoading = false;
        // 设置后台返回的分页参数
        pageParamNames.forEach((name) =>
          this.$set(this.tablePage, name, parseInt(res.data[name]))
        );
      });
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
    },
    handlePage() {
      (this.tablePage = {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null,
      }),
        this.getPageList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dataQuery = {
        name: "",
        enabled: "",
        parentId: "0",
      };
      this.getPageList();
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val;
      this.getPageList();
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val;
      this.getPageList();
    },
    close() {
      this.open = false;
      this.reset();
    },
    reset() {
      this.form = {
        id: null,
        languageType: null,
        grade: null,
        spokenTypeId: null,
        content: null,
      };
      this.typeOptions = [];
      if (this.$refs["form"]) {
        this.$refs["form"].resetFields();
      }
    },
  },
};
</script>

<style lang="less" scoped>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.period-table td,
.period-table th {
  text-align: center;
}

@media (max-width: 767px) {
  .el-message-box {
    width: 80% !important;
  }
}

.el-table .success-row {
  background: #e8f4ff;
}

/deep/ .el-dialog__body {
  padding: 0 20px !important;
}
</style>
