<template>
  <div class="app-container">
    <el-form :inline="true" ref="queryForm" class="container-card" label-width="110px">
      <el-form-item label="课程名称：">
        <el-input v-model="form.courseName" placeholder="请输入课程名称" clearable />
      </el-form-item>
      <el-form-item label="试卷名称：">
        <el-input v-model="form.paperName" placeholder="请输入试卷名称" clearable />
      </el-form-item>
      <el-form-item>
        <el-button size="small" icon="el-icon-refresh" @click="resetQuery()">重置</el-button>
        <el-button size="small" icon="el-icon-search" type="primary" @click="search(1)">搜索</el-button>
      </el-form-item>
    </el-form>
    <div class="SearchForm">
      <!-- 添加 -->
      <div style="margin-bottom: 10px">
        <el-button size="small" type="primary" icon="el-icon-plus" @click="clickAdd">添加</el-button>
      </div>
      <!-- 表格 -->
      <el-table class="common-table" :data="tableData" stripe border :default-sort="{ prop: 'date', order: 'descending' }">
        <el-table-column type="index" width="100" label="序号"></el-table-column>
        <el-table-column prop="id" label="试卷编号"></el-table-column>
        <el-table-column prop="examName" label="试卷名称"></el-table-column>
        <el-table-column prop="courseName" label="对应课程"></el-table-column>
        <el-table-column prop="id" label="操作" width="260">
          <template slot-scope="scope">
            <el-button size="mini" @click="handleDetail(scope.row.id)">查看</el-button>
            <!-- <el-button type="primary" size="mini" icon="el-icon-edit-outline"  @click="handleUpdate(scope.row.id)">编辑</el-button> -->
            <el-button @click="handleDel(scope.row.id)" type="danger" size="mini" icon="el-icon-view">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 添加试卷 -->
      <addTestPaperManagement
        :courseType="courseType"
        ref="addTestPaperManagementRefs"
        :linkCourseList="courseList"
        v-if="dialogFrom.visible"
        :dialog-param="dialogFrom"
        @closeDialog="closeDialog"
      ></addTestPaperManagement>
      <!-- 查看试卷详情 -->
      <viewTestPaper ref="viewTestPaper" v-if="dialogDetail.visible" :dialog-param="dialogDetail" @closeDialog="closeDialog"></viewTestPaper>
    </div>
    <!-- 分页 -->
    <el-col :span="24" style="margin-bottom: 20px">
      <el-pagination
        :current-page="tablePage.currentPage"
        :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="tablePage.totalItems"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-col>
  </div>
</template>

<script>
  import viewTestPaper from './components/viewTestPaper.vue';
  import addTestPaperManagement from './components/addTestPaperManagement.vue';
  import courseApi from '@/api/training/course';
  import examApi from '@/api/training/exam';
  export default {
    name: 'testPaperManagement',
    components: {
      addTestPaperManagement,
      viewTestPaper
    },
    data() {
      return {
        tableLoading: false,
        dialogFrom: {
          visible: false,
          type: 'add'
        },
        dialogDetail: {
          visible: false
        },
        // 分页
        tablePage: {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: 0
        },
        form: {
          courseName: '',
          paperName: ''
        },
        tableData: [],
        courseList: [],
        courseType: []
      };
    },
    created() {
      this.getCourse();
      // this.getQueryByType()
      this.search(1);
    },
    methods: {
      // 分页
      handleSizeChange(val) {
        this.tablePage.currentPage = 1;
        this.tablePage.size = val;
        this.search();
      },
      handleCurrentChange(val) {
        this.tablePage.currentPage = val;
        this.search();
      },

      // 重置
      resetQuery() {
        // this.tablePage.currentPage=1
        this.form = {};
        this.search(1);
      },

      // 搜索
      search(page) {
        if (typeof page == 'number' && page > 0) {
          this.tablePage.currentPage = page;
        }
        let param = { pageSize: this.tablePage.size, pageNum: page || this.tablePage.currentPage };
        // questionList
        param = { ...this.form, ...param };

        examApi.paperPage(param).then((res) => {
          this.tableData = res.data.data;
          this.tablePage.totalItems = Number(res.data.totalItems);
        });
      },
      // 查看
      handleDetail(id) {
        // this.dialogDetail.visible = true;
        // this.getCourse()
        // this.$nextTick(() => {
        //   this.$refs.viewTestPaper.open(id)
        //   // this.getQueryByType()
        // })
        this.dialogFrom.visible = true;
        this.dialogFrom.type = 'detail';
        this.getCourse();
        this.courseType = [];
        this.getQueryByType();
        this.$nextTick(() => {
          this.$refs.addTestPaperManagementRefs.open(id);
        });
      },
      // 新增
      clickAdd() {
        this.dialogFrom.visible = true;
        this.dialogFrom.type = 'add';
        this.getCourse();
        this.courseType = [];
        this.getQueryByType();
      },
      //删除
      handleDel(id) {
        this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            examApi.paperDelete({ id: id }).then((res) => {
              this.resetQuery();
            });
          })
          .catch(() => {
            this.$message({
              type: 'info',
              message: '已取消删除'
            });
          });
      },
      getCourse() {
        console.log('=================================================');
        let param = { pageSize: 20, pageNum: 1 };
        this.courseList = [];
        courseApi.queryCoursesDict(param).then((res) => {
          this.courseList = res.data;
        });
      },
      //选择题目类型
      getQueryByType() {
        courseApi.dictQueryByType({ dictType: 'paper_question_type' }).then((res) => {
          this.courseType = res.data;
          console.log(res);
          console.log('--------------=============----------------------');
        });
      },
      // 编辑
      handleUpdate(id) {
        this.dialogFrom.visible = true;
        this.dialogFrom.type = 'edit';
        this.$nextTick(() => {
          this.$refs.addTestPaperManagementRefs.open(id);
        });
        // this.$refs.addTestPaperManagementRefs.open(info)
      },

      closeDialog(type) {
        this.dialogFrom.visible = false;
        this.dialogDetail.visible = false;
        if (!type || type !== 'detail') {
          this.tablePage.currentPage = 1;
        }
        this.search();
      }
    }
  };
</script>

<style>
  .SearchForm {
    width: 100%;
    margin-bottom: 15px;
    background: #fff;
    border-radius: 6px;
    padding: 5px 0;
    padding-left: 5px;
    box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.2);
  }
  .container-card {
    padding: 15px 15px 15px 15px;
    margin-bottom: 15px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  }

  .period-table td,
  .period-table th {
    text-align: center;
  }

  @media (max-width: 767px) {
    .el-message-box {
      width: 80% !important;
    }
  }

  .el-tooltip__popper {
    max-width: 800px;
  }
</style>
