<template>
  <div class="app-container">
    <el-form ref="formTaskCommit" :model="formData" class="full-width-input" :rules="rules"
      label-width="100px" size="mini" label-position="right" @submit.native.prevent>
      <el-row :gutter="20">
        <el-col :span="24"
                v-if="operation.type === SysFlowTaskOperationType.LEVEL_AGREE&&operation.processDefinitionKey!='addOperations'">
          <el-form-item label="开户等级：" prop="message">
            <el-select v-model="formData.rank" placeholder="请选择等级" style="width: 100%">
              <el-option
                v-for="item in [
                { value: 'A', label: 'A级别', disabled: false},
                { value: 'B', label: 'B级别', disabled: true},
                { value: 'C', label: 'C级别', disabled: true},
                { value: 'D', label: 'D级别', disabled: false},
                { value: 'E', label: 'E级别', disabled: false},
                { value: 'F', label: 'F级别', disabled: false},
              ]"
                :key="item.value"
                :label="item.label"
                :value="item.value"
                :disabled="item.disabled"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24"
          v-if="operation.type !== SysFlowTaskOperationType.CO_SIGN && operation.type !== SysFlowTaskOperationType.MULTI_SIGN">
          <el-form-item label="审批意见：" prop="message">
            <el-input v-model="formData.message" clearable placeholder="请输入审批意见" />
          </el-form-item>
        </el-col>
        <el-col :span="24" v-if="showAssignSelect">
          <el-form-item label="指派用户：" prop="assignee">
            <TagSelect v-model="formData.assignee">
              <el-button slot="append" class="append-add" type="default" icon="el-icon-plus" @click="onSelectAssignee" />
            </TagSelect>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-row class="no-scroll flex-box" type="flex" justify="end">
            <el-button type="primary" size="mini" :plain="true"
              @click="onCancel(false)">
              取消
            </el-button>
            <el-button type="primary" size="mini" @click="onSubmitClick()">
              提交
            </el-button>
          </el-row>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
import TagSelect from '@/views/activiti/workFlow/components/TagSelect.vue';
import TaskUserSelect from '@/views/activiti/workFlow/components/TaskUserSelect.vue';

export default {
  name: 'formTaskCommit',
  props: {
    operation: {
      type: Object,
      required: true
    }
  },
  components: {
    TagSelect
  },
  data () {
    return {
      formData: {
        rank: undefined,
        message: undefined,
        assignee: undefined
      },
      rules: {
        message: [{required: true, message: '审批意见不能为空', trigger: 'blur'}],
        assignee: [{required: true, message: '指派用户不能为空', trigger: 'blur'}]
      }
    }
  },
  methods: {
    onCancel (isSuccess, data) {
      if (this.observer != null) {
        this.observer.cancel(isSuccess, data);
      }
    },
    onSubmitClick () {
      this.$refs.formTaskCommit.validate(valid => {
        if (!valid) return;
        this.onCancel(true, this.formData);
      });
    },
    onSelectAssignee () {
      this.$dialog.show('选择用户', TaskUserSelect, {
        area: ['1000px', '600px']
      }, {
        showAssignee: false,
        multiple: this.multiSelect
      }).then(res => {
        let assignee = null;
        if (Array.isArray(res)) {
          assignee = res.map(item => item.loginName).join(',');
        } else {
          assignee = (res || {}).loginName;
        }
        this.formData.assignee = assignee;
      }).catch(e => {});
    }
  },

  created() {
    },
  computed: {
    showAssignSelect () {
      let showAssignSelect = false;
      // 如果是会签操作，判断是否在流程内设置了会签人
      if (this.operation.type === this.SysFlowTaskOperationType.MULTI_SIGN) {
        showAssignSelect = !this.operation.multiSignAssignee ||
          !Array.isArray(this.operation.multiSignAssignee.assigneeList) ||
          this.operation.multiSignAssignee.assigneeList.length <= 0;
      }
      return [
        this.SysFlowTaskOperationType.TRANSFER,
        this.SysFlowTaskOperationType.CO_SIGN,
        this.SysFlowTaskOperationType.SET_ASSIGNEE
      ].indexOf(this.operation.type) !== -1 || showAssignSelect;
    },
    multiSelect () {
      return this.operation.type === this.SysFlowTaskOperationType.CO_SIGN || this.operation.type === this.SysFlowTaskOperationType.MULTI_SIGN;
    }
  }
}
</script>

<style scoped>
  .append-add {
    border: none;
    border-left: 1px solid #DCDFE6;
    border-radius: 0px;
    background: #F5F7FA;
  }
</style>
