<template>
  <div class="app-container">
    <el-form :inline="true" ref="queryForm" class="container-card" label-width="110px">
      <!-- <el-form-item label="名字：">
        <el-form-item label>
          <el-input v-model="dataQuery.name" placeholder="请输入搜索关键词">
            <i class="el-icon-search el-input__icon" slot="prefix"></i>
          </el-input>
        </el-form-item>
      </el-form-item> -->
      <!-- <el-form-item>
        <el-button icon="el-icon-re" @click="resetQuery()">重置</el-button>
      </el-form-item> -->
      <el-form-item>
        <el-button type="primary" @click="search()">刷新</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="success" icon="el-icon-plus" size="mini" @click="addBtn()">新增</el-button>
      </el-form-item>
    </el-form>

    <el-table class="common-table" v-loading="tableLoading" :data="tableData" style="width: 100%;margin-bottom: 20px;"
      row-key="id" border default-expand-all>
      <el-table-column prop="id" label="ID" />
      <el-table-column prop="taskName" label="任务名" />
      <el-table-column prop="withdrawnBonus" label="操作" width="210">
        <template slot-scope="scope">
          <el-dropdown>
            <span class="el-dropdown-link" style="color:#108EE9">
              <el-button type="text">
                更多
                <i class="el-icon-arrow-down" />
              </el-button>
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item @click.native="editBtn(scope.row.id)">编辑</el-dropdown-item>
              <el-dropdown-item @click.native="singleDelete(scope.row.id)">删除</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
      <el-table-column prop="minTime" label="超时时间下限(分钟)" />
      <el-table-column prop="maxTime" label="超时时间上限(分钟)" />
      <el-table-column prop="creditsRate" label="学分比率" />
      <el-table-column prop label="排序" show-overflow-tooltip>
        <i class="el-icon-rank"></i>
      </el-table-column>
      <el-table-column prop="createTime" label="时间" />
    </el-table>
    <!-- 分页 -->
    <el-col :span="24" style="margin-bottom: 20px">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>

    <!-- 查看弹窗 -->
    <el-dialog :title="banner_titile" :visible.sync="open" width="50%" @close="close">
      <el-form ref="form" :model="form" :rules="rules" label-width="160px">
        <el-form-item label="任务：" prop="taskId">
          <el-select v-model="form.taskId" placeholder="请选择任务">
            <el-option v-for="item in taskList" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label='超时时间下限(分钟)：' style="width:325px" prop="minTime">
          <el-input v-model="form.minTime" placeholder="请输入搜索关键词"></el-input>
        </el-form-item>

        <el-form-item label="超时时间上限(分钟)：" style="width:325px" prop="maxTime">
          <el-input v-model="form.maxTime" placeholder="请输入搜索关键词"></el-input>
        </el-form-item>

        <el-form-item label="学分倍率：" style="width:325px" prop="creditsRate">
          <el-input v-model="form.creditsRate" placeholder="请填写序号" type="number" />
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import punishconfigApi from "@/api/xi/community/punishconfig";
import { pageParamNames } from "@/utils/constants";
import xiTaskApi from "@/api/xi/xiTask";
import ratioconfigApi from "@/api/xi/ratioconfig";

export default {
  name: "punishconfig",
  data() {
    return {
      tabPosition: "1",
      dataQuery: {
      },
      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      taskList: [],
      open: false,
      tableData: [],
      form: {
        orderNum: ""
      },
      up_options: [],
      banner_titile: "",
      // 表单校验
      rules: {
        minTime: [{ required: true, message: '请输入分钟最小值', trigger: 'blur' }],
        maxTime: [{ required: true, message: '请输入分钟最大值', trigger: 'blur' }],
        creditsRate: [{ required: true, message: '请输入学分比例值', trigger: 'blur' }],
        taskId: [{ required: true, message: '请选择任务', trigger: 'blur' }],
      }
    };
  },
  created() {
    this.getPageList();
    xiTaskApi.allList().then(resp => {
      this.taskList = resp.data;
    }).catch(err => { });
  },
  methods: {
    // 详情接口
    detailBtn() {
      this.tagFn()
      this.open = true;
      this.banner_titile = "详情";
      punishconfigApi.detail(id).then(res => {
        this.form = res.data;
      });
    },
    // 提交
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          punishconfigApi.saveOrUpdate(this.form).then(response => {
            this.$message.success("提交成功！");
            this.open = false;
            this.getPageList();
            this.$refs.upload.clearFiles();
          });
        }
      })
    },
    search() {
      this.tablePage = {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      };
      this.getPageList();
    },
    // 单个删除
    singleDelete(id) {
      const that = this;
      this.$confirm("确定操作吗?", "删除状态", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          punishconfigApi
            .delete(id)
            .then(res => {
              that.$nextTick(() => that.fetchData());
              that.$message.success("删除成功!");
              this.getPageList();
            })
            .catch(err => { });
        })
        .catch(err => { });
    },
    addBtn() {
      this.reset();
      this.open = true;
      this.banner_titile = "新增";
      this.tagFn()
    },
    // 修改
    editBtn(id) {
      this.open = true;
      this.banner_titile = "修改";
      punishconfigApi.detail(id).then(res => {
        this.form = res.data;
      });
    },
    // 列表数据
    getPageList() {
      this.tableLoading = true;
      this.dataQuery.pageNum = this.tablePage.currentPage;
      this.dataQuery.pageSize = this.tablePage.size;
      punishconfigApi.list(this.dataQuery).then(res => {
        this.tableData = res.data.data;
        this.tableLoading = false;
        // 设置后台返回的分页参数
        pageParamNames.forEach(name =>
          this.$set(this.tablePage, name, parseInt(res.data[name]))
        );
      });
    },
    handlePage() {
      (this.tablePage = {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      }),
        this.getPageList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dataQuery = {
        type: 1
      };
      this.getPageList();
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val;
      this.getPageList();
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val;
      this.getPageList();
    },
    close() {
      this.open = false;
      this.reset();
    },
    reset() {
      this.form = {
        id: null,
        title: null,
        subTitle: null,
        num: undefined,
        routePath: null,
        sortNum: undefined,
        enable: true
      };
      if (this.$refs["form"]) {
        this.$refs["form"].resetFields();
      }
    }
  }
};
</script>

<style scoped>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.period-table td,
.period-table th {
  text-align: center;
}

@media (max-width: 767px) {
  .el-message-box {
    width: 80% !important;
  }
}

.el-tooltip__popper {
  max-width: 800px;
}

.el-upload-list--picture-card {
  display: block;
}

.el-upload {
  border: none;
  width: auto;
  height: 36px;
  line-height: 36px;
}

.el-upload button {
  height: 36px;
}

.el-tooltip__popper {
  max-width: 800px;
}

.el-upload-list {
  position: relative;
}
</style>
