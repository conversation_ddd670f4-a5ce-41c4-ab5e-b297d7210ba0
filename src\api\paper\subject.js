
import request from '@/utils/request'

export default {
  // 分页查询
  pageList(data) {
    return request({
      url: '/paper/web/subject/list',
      method: 'GET',
      params: data
    })
  },
//添加学科
  create(data) {
    return request({
      url: '/paper/web/subject',
      method: 'POST',
      data
    })
  },
  //编辑学科
  edit(data) {
    return request({
      url: '/paper/web/subject',
      method: 'PUT',
      data
    })
  },
  detail(id) {
    return request({
      url: '/paper/web/subject',
      method: 'GET',
      params:{
        id:id
      }
    })
  },
  //年级
  gradeListAll(){
    return request({
      url: '/paper/web/grade/all',
      method: 'GET'
    })
  },
  delete(id){
    return request({
      url: '/paper/web/subject',
      method: 'DELETE',
      params:{
        id:id
      }
    })
  }
}
