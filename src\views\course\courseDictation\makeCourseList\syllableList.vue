<!-- 添加音节 -->
<template>
  <div class="container_box">
    <el-button
      type="primary"
      @click="onAddSyllable"
      icon="el-icon-plus"
      size="mini"
      style="margin-bottom: 10px"
      >添加</el-button
    >
    <el-table
      :data="syllableData"
      class="common-table"
      stripe
      border
      v-loading="syllableLoading"
    >
      <el-table-column label="音节ID" prop="id" width="240" sortable>
        <!-- <template slot-scope="{ row, column, $index }">
          <span>{{ courseId }}</span>
        </template> -->
      </el-table-column>
      <el-table-column
        label="音节"
        prop="word"
        width="360"
        sortable
      ></el-table-column>
      <el-table-column
        label="音节拆分"
        prop="split"
        width="360"
        sortable
      ></el-table-column>
      <el-table-column label="操作" prop="id" sortable>
        <template slot-scope="{ row }">
          <el-button
            type="primary"
            size="mini"
            icon="el-icon-edit"
            @click="editSyllableItem(row.id)"
            >编辑</el-button
          >
          <el-button
            type="danger"
            size="mini"
            icon="el-icon-delete"
            @click="delSyllableItem(row.id)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <el-dialog
      :title="`${syllableTitle}音节`"
      :visible.sync="syllableDialogVisible"
      width="70%"
      :close-on-click-modal="false"
      @close="syllableDialogClose"
    >
      <el-form
        :model="syllableForm"
        :rules="syllableRules"
        ref="syllableForm"
        label-position="left"
        label-width="60px"
        style="width: 60%"
      >
        <el-form-item label="音节" prop="vowelInfo">
          <el-input
            type="textarea"
            :rows="4"
            :spellcheck="false"
            placeholder="请输入音节用,分隔"
            v-model="syllableForm.vowelInfo"
          ></el-input>
        </el-form-item>
        <el-form-item label="">
          <!-- :on-success="handleSetReadWordSuccess" -->
          <div class="upload_box" v-if="syllableTitle === '新增'">
            <el-upload
              class="upload-demo"
              action="#"
              :show-file-list="true"
              :on-remove="handlSetRemoveReadWord"
              :http-request="uploadDetailHttp"
              :limit="1"
              :before-upload="beforeWordUpload"
              ref="myUpload"
            >
              <div class="el-upload__text">
                <el-link
                  icon="el-icon-upload2"
                  :underline="false"
                  class="upload_link"
                  >excel文件上传</el-link
                >
              </div>
            </el-upload>
            <el-link
              class="download_link"
              :underline="false"
              icon="el-icon-download"
              href="https://document.dxznjy.com/applet/zhimi/config/pd_syllable.xls"
              >模板下载</el-link
            >
          </div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="syllableDialogClose">关闭</el-button>
        <el-button size="mini" type="primary" @click="onSubmit">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import courseDictationListApi from "@/api/courseDictationList";
import { ossPrClient } from "@/api/alibaba";
import { Base64 } from "@/utils/base64";
export default {
  props: {
    courseCode: {
      type: String,
      default: "",
    },
    // id:{
    //     type:String,
    //     default:''
    // }
  },
  data() {
    return {
      syllableData: [],
      syllableForm: {
        vowelInfo: "",
      },
      oldSyllableInfo: "",
      syllableRules: {
        vowelInfo: [{ required: true, message: "请输入音节", trigger: "blur" }],
      },
      syllableTitle: "新增",
      syllableLoading: false,
      syllableDialogVisible: false,
      courseId: "",
      id: "",
      wordFile: "",
    };
  },
  created() {
    this.courseCode = this.$route.query && this.$route.query.courseCode;
    this.courseId = this.$route.query && this.$route.query.id;
    this.getTableData();
    ossPrClient();
  },
  methods: {
    //必须是excel文件
    isExcel(file) {
      return /\.(xlsx|xls)$/.test(file.name);
    },
    //音节上传
    beforeWordUpload(file) {
      if (!this.isExcel(file)) {
        this.$message.error("只能上传excel文件！");
        return false;
      }
    },
    //上传音节文件解析
    uploadDetailHttp({ file }) {
      const that = this;
      this.wordFile = `https://document.dxznjy.com/applet/zhimi/usercode_${file.uid}.xls`;
      const fileType = file.type.substring(file.type.lastIndexOf("/") + 1);
      const fileName = `manage/${Date.parse(new Date())}.${fileType}`;
      that.$nextTick(function () {
        ossPrClient()
          .put(fileName, file)
          .then(({ res, url, name }) => {
            if (res && res.status === 200) {
              // console.log(`阿里云OSS上传图片成功回调1`,file, res, url, name)
              const formData = new FormData();
              formData.append("file", file);
              formData.append("analysisType", "1");
              courseDictationListApi
                .baseAnalysisFile(formData)
                .then((res) => {
                  if (res.code === 20000) {
                    this.syllableForm.vowelInfo = "";
                    const syllableList = res.data.data.syllableList;
                    if (syllableList.length > 0) {
                      this.syllableForm.vowelInfo = this.syllableForm.vowelInfo
                        ? `${this.syllableForm.vowelInfo}\n${syllableList
                            .map((item) => item + "\n")
                            .join("")}`
                        : syllableList.map((item) => item + "\n").join("");
                      this.$refs.myUpload.clearFiles();
                    }
                  }
                })
                .catch((err) => {
                  //  console.log(`err--------------------`, err)
                  this.$refs.myUpload.clearFiles();
                });
            }
          })
          .catch((err) => {
            that.$message.error("上传图片失败请检查网络或者刷新页面");
            console.log(`阿里云OSS上传图片失败回调`, err);
          });
      });
    },
    //音节文件上传成功
    handleSetReadWordSuccess(response, file, fileList) {
      if (file && file.raw && file.uid) {
        this.wordFile = `https://document.dxznjy.com/applet/zhimi/usercode_${file.raw.uid}.xls`;
        const formData = new FormData();
        formData.append("file", file.raw);
        formData.append("analysisType", "1");
        courseDictationListApi.baseAnalysisFile(formData).then((res) => {
          if (res.code === 20000) {
            console.log(res.data.data, "data");
            //   "consonantInfo": "t,r",
            // "vowelInfo": "s,v"
          }
        });
      }
    },
    //音节拼读删除
    handlSetRemoveReadWord() {
      this.wordFile = "";
    },
    getTableData() {
      this.syllableLoading = true;
      courseDictationListApi.getSyllableList(this.courseCode).then((res) => {
        if (res.code === 20000) {
          this.syllableData = res.data.data;
          this.syllableLoading = false;
        }
      });
    },
    //编辑
    editSyllableItem(id) {
      this.syllableTitle = "编辑";
      this.syllableDialogVisible = true;
      courseDictationListApi.getSyllable(id).then((res) => {
        if (res.code === 20000) {
          this.syllableForm["id"] = res.data.data.id;
          this.syllableForm.vowelInfo = res.data.data.wordRemark;
          this.oldSyllableInfo = res.data.data.wordRemark;
        }
      });
    },
    //删除
    delSyllableItem(id) {
      courseDictationListApi.deleteWordById(id, 3).then((res) => {
        if (res.code === 20000) {
          this.$message.success("删除成功");
          this.getTableData();
        }
      });
    },
    //添加
    onAddSyllable() {
      this.syllableTitle = "新增";
      this.syllableDialogVisible = true;
    },
    syllableDialogClose() {
      this.syllableDialogVisible = false;
      this.$refs["syllableForm"].resetFields();
    },
    //提交
    onSubmit() {
      let word = /\n|\r\n|\r/.test(this.syllableForm.vowelInfo)
        ? this.syllableForm.vowelInfo.replace(/\n|\r\n/g, "\n")
        : this.syllableForm.vowelInfo;
      word = Base64.encode(word).replace(/\+/g, "-").replace(/\//g, "_");
      this.$refs["syllableForm"].validate((valid) => {
        if (valid) {
          const data = { id: this.courseId, vowelInfo: word };
          if (this.syllableTitle === "新增") {
            //添加
            courseDictationListApi.saveSyllable(data).then((res) => {
              if (res.code === 20000) {
                this.syllableDialogVisible = false;
                this.$message.success("操作成功");
                this.syllableForm.vowelInfo = "";
                this.getTableData();
                //  this.$refs.myUpload.clearFiles()
              }
            });
          } else {
            //编辑
            const old = Base64.encode(this.oldSyllableInfo)
              .replace(/\+/g, "-")
              .replace(/\//g, "_");
            // const data = {oldSyllableInfo:this.oldSyllableInfo,newSyllableInfo:word,id:id}
            courseDictationListApi
              .updateSyllable(this.courseId, old, word)
              .then((res) => {
                if (res.code === 20000) {
                  this.syllableDialogVisible = false;
                  this.$message.success("操作成功");
                  this.syllableForm.vowelInfo = "";
                  this.getTableData();
                }
              });
          }
        }
      });
    },
  },
};
</script>
<style lang="less" scoped>
.upload_link {
  color: #1890ff;
}

.download_link {
  // height: 32px;
  // margin: 0;
  // padding: 0 15px;
  color: #13ce66;
}
</style>
