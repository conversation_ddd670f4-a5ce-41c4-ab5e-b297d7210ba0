<template>
  <div class="app-container2">
    <div data-label="页面">
      <div data-label="学员打印--单词">
        <input type="hidden" id="WordPrintCode" value="P213337" />
        <div class="buttom1 clearfix">
          <div class="fl clearfix">
            <a style="background: #3dab93" @click="printEnglish()"
              >只打印英文</a
            >
            <a
              style="background: #3dab93; margin-left: 20px"
              @click="printChinese()"
              >只打印中文</a
            >
            <a
              style="background: #3dab93; margin-left: 20px"
              @click="printAll()"
              >全打印</a
            >
          </div>
          <div class="buttom2 clearfix">
            <a style="background: #3dab93" @click="exportList()">下载</a>
            <a
              style="background: #3dab93; margin-left: 20px"
              @click="printBtn()"
              >打印</a
            >
            <a style="background: #f0ad4e; margin-left: 20px" @click="goback()"
              >返回</a
            >
          </div>
        </div>
        <!-- 打印主体  -->
        <div>
          <div>
            <div data-label="打印操作" class="name2" style="text-align: center">
              <hr />
            </div>
            <div style="width: 70%; text-align: center">
              <div v-for="item in this.printData.previewImages">
                <el-image :src="'data:image/png;base64,' + item"></el-image>
                <hr />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import printApi from "@/api/studentTestPrint";
import printCloseApi from "@/api/studentPrintClose";
import ls from "@/api/sessionStorage";
import printJs from "print-js";

export default {
  data() {
    return {
      // 打印数据
      printData: [],
      dataQuary: {
        wordPrintCode: "",
        name: "",
        courseContentType: "",
      },
      time: "",
      English: true,
      Chinese: true,
      exportLoading: false,
      result: [],
      num: "",
      a: "",
      b: "",
      c: "",
      dayin: true,
      imgarr: [],
    };
  },
  created() {
    this.dataQuary.wordPrintCode = ls.getItem("wordPrintCode");
    this.dataQuary.name = ls.getItem("name");
    this.dataQuary.courseContentType = ls.getItem("courseContentType");
    this.dataQuary.type = ls.getItem("type");
    this.dataQuary.title = ls.getItem("title");
    this.fetchData(this.dataQuary);
  },
  watch: {
    getItem() {
      if (document.execCommand("print")) {
        console.log("123");
      }
    },
  },
  methods: {
    getStyle(item) {
      if (item.newline) {
        return "clear: left;float:left";
      } else {
        return "float:left";
      }
    },
    async exportList() {
      let requestData = {
        contentType: this.dataQuary.contentType || "ALL",
        fileType: "ZIP",
        printCode: this.dataQuary.printCode,
        studentName: this.dataQuary.name,
        type: this.dataQuary.type,
        title: this.dataQuary.title,
        day: true,
      };
      let ae = await printApi.studentZip(requestData);
      window.open(ae.data.files[0]);
      // let a = document.createElement('a')
      //         a.href = ae.data.files[0]
      //         a.setAttribute('download', this.printData.printCode + '_' + i)
      //         a.click()
      // const style = '@page {margin:0 10mm};'
      // let i = 0
      // for (let previewImagesKey in this.printData.previewImages) {
      //     const imgUrl = ('data:image/png;base64,' + this.printData.previewImages[previewImagesKey])
      //     // 如果浏览器支持msSaveOrOpenBlob方法（也就是使用IE浏览器的时候），那么调用该方法去下载图片
      //     if (window.navigator.msSaveOrOpenBlob) {
      //         // let bstr = atob(imgUrl.split(',')[1])
      //         // let n = bstr.length
      //         // let u8arr = new Uint8Array(n)
      //         // while (n--) {
      //         //   u8arr[n] = bstr.charCodeAt(n)
      //         // }
      //         // let blob = new Blob([u8arr])
      //         // window.navigator.msSaveOrOpenBlob(blob, this.printData.printCode + '_' + i + '.' + 'png')
      //         this.$message.warning('请使用Chrome浏览器或Edge浏览器')
      //     } else {
      //         // 这里就按照chrome等新版浏览器来处理
      //         let a = document.createElement('a')
      //         a.href = imgUrl
      //         a.setAttribute('download', this.printData.printCode + '_' + i)
      //         a.click()
      //     }
      //     i++
      // }
    },
    printBtn() {
      const style = "@page {margin:0 10mm};";
      let previewImages = [];
      for (let previewImagesKey in this.imgarr) {
        previewImages.push(
          "data:image/png;base64," + this.imgarr[previewImagesKey]
        );
      }
      printJs({
        printable: previewImages,
        type: "image",
        targetStyles: ["*"],
        style,
      });
    },
    // 只打印英文
    async printEnglish() {
      this.dataQuary.contentType = "EN";
      this.fetchData(this.dataQuary);
      // let res = await printApi.studentGraduation(this.dataQuary)
      // console.log(res);
      // if (res.data != {}) {
      //     this.printData = res.data
      //     if(this.printData.previewImages.length>0){
      //         this.printData.previewImages=this.printData.previewImages.slice(0,1)
      //     }

      // }
    },
    // 只打印中文
    async printChinese() {
      this.dataQuary.contentType = "CN";
      this.fetchData(this.dataQuary);
      // let res = await printApi.studentGraduation(this.dataQuary)
      // console.log(res);
      // if (res.data != {}) {
      //     this.printData = res.data

      //     if(this.printData.previewImages.length>0){
      //         this.printData.previewImages=this.printData.previewImages.slice(0,1)
      //     }

      // }
    },
    // 全打印
    async printAll() {
      this.dataQuary.contentType = "ALL";
      this.fetchData(this.dataQuary);
    },

    // 返回
    goback() {
      this.$store.dispatch("delVisitedViews", this.$route);
      this.$router.go(-1); //返回上一层
    },
    // 显示学员测试结果
    async fetchData(dataQuary) {
      const that = this;
      that.imgarr = [];
      dataQuary.preview = true;
      dataQuary.printCode = dataQuary.wordPrintCode;
      dataQuary.wordPrintCode = dataQuary.wordPrintCode;
      dataQuary.studentName = dataQuary.name;
      dataQuary.name = dataQuary.name;
      dataQuary.title = dataQuary.title;
      dataQuary.courseContentType = dataQuary.type;
      dataQuary.type = dataQuary.type;
      let res = await printApi.studentGraduation(dataQuary);
      console.log(res);
      if (res.data != {}) {
        that.printData = res.data;
        that.imgarr = that.printData.previewImages;
        if (that.printData.previewImages.length > 0) {
          that.printData.previewImages = that.printData.previewImages.slice(
            0,
            1
          );
        }
      }
      // console.log(that.printData.previewImages)
      that.dataQuary.wordPrintCode = res.data.printCode;
      that.dataQuary.printCode = res.data.printCode;
    },
  },
};
</script>
<style scoped>
.app-container2 {
  padding: 20px;
  color: #676a6c;
}

.wordTable tr:last-child {
  border-bottom: 1px;
}

.buttom2 {
  width: auto;
  margin: 0px 180px 0px auto;
  float: right;
  color: rgb(255, 255, 255);
  cursor: pointer;
}

.buttom2 a {
  display: block;
  float: left;
  color: rgb(255, 255, 255);
  width: 90px;
  height: 35px;
  border-radius: 5px;
  text-align: center;
  line-height: 35px;
}

.buttom1 {
  margin-top: 40px;
  color: rgb(255, 255, 255);
  cursor: pointer;
  margin-left: 200px;
}

.buttom1 a {
  display: block;
  float: left;
  color: rgb(255, 255, 255);
  width: 90px;
  height: 35px;
  border-radius: 5px;
  text-align: center;
  line-height: 35px;
}

.first-top,
.first-center {
  width: 1000px;
  margin: 40px auto 0px;
}

.first-top span {
  display: block;
  float: left;
  width: 25%;
  text-align: left;
}

.first-center span {
  display: block;
  float: left;
  width: 25%;
  text-align: center;
  margin-top: -10px !important;
}

.first-top input {
  color: #676a6c;
}

.clearfix {
  zoom: 1;
}

.printonly {
  display: none;
}

@media print {
  input,
  .noprint {
    display: none;
  }

  .printonly {
    display: block;
    width: 50%;
  }
}

.clearfix::before,
.clearfix::after {
  content: "";
  line-height: 0;
  display: table;
}

.clearfix::after {
  clear: both;
}

.crumbs {
  line-height: 62px;
  margin-top: 0px !important;
}

.name2 {
  width: 100%;
  margin: 0 auto;
}

@media (max-width: 767px) {
  .buttom1 {
    margin-left: 0;
  }

  .buttom2 {
    width: 100%;
    margin: 10px 0;
  }
}
</style>
