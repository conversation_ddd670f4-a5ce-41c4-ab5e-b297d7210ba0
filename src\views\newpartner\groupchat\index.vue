<template>
  <div class="app-container">
    <el-card class="filter-options">
      <el-form :inline="true" :model="filterForm" ref="filterForm" class="demo-form-inline" label-width="100px">
        <el-form-item label="群聊状态" prop="status">
          <el-select v-model="filterForm.status" placeholder="请选择">
            <el-option label="未建群" :value="0"></el-option>
            <el-option label="已建群" :value="1"></el-option>
            <el-option label="建群失败" :value="2"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="建群时间" prop="dateRange">
          <el-date-picker
            v-model="filterForm.dateRange"
            type="daterange"
            align="right"
            range-separator="-"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            value-format="yyyy-MM-dd"
          ></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button icon="el-icon-search" type="primary" @click="searchData">查询</el-button>
          <el-button icon="el-icon-refresh-left" @click="resetData">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <el-button type="primary" icon="el-icon-plus" @click="addGroup" style="margin-bottom: 20px">新增群聊</el-button>

    <el-table v-loading="tableLoading" class="common-table" :data="tableData" style="width: 100%; margin-bottom: 20px" border>
      <el-table-column prop="groupChatName" label="群聊名称" width="300"></el-table-column>
      <el-table-column prop="supervisionList" label="督导名称">
        <template slot-scope="scope">
          <span>{{ merchantNames(scope.row.supervisionList) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="empoweringSpecialist" label="赋能专员名称">
        <template slot-scope="scope">
          <span>{{ merchantNames(scope.row.empoweringSpecialist) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="partnerNum" label="合伙人数量">
        <template slot-scope="scope">
          <span class="partner-num">{{ scope.row.partnerNum }}</span>
          <el-button type="text" @click="showDetail(scope.row)">详情</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="status" label="群聊状态">
        <template slot-scope="scope">
          <span v-if="scope.row.status === 0">未建群</span>
          <span v-else-if="scope.row.status === 1">已建群</span>
          <span v-else-if="scope.row.status === 2">建群失败</span>
        </template>
      </el-table-column>
      <el-table-column prop="chatCreateTime" label="建群时间"></el-table-column>
      <el-table-column prop="id" label="操作">
        <template slot-scope="scope">
          <el-button type="text" :disabled="scope.row.status === 1" @click="editGroupChat(scope.row)">编辑</el-button>
          <el-button type="text" v-if="scope.row.status !== 1" @click="bindPartner(scope.row)">绑定合伙人</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <el-col :span="24" style="overflow-x: auto" :xs="24">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="tablePage.currentPage"
        :page-sizes="[10, 20, 30, 40, 50]"
        :page-size="tablePage.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="tablePage.totalItems"
      ></el-pagination>
    </el-col>

    <!-- 新增、编辑群聊 -->
    <addOrEditGroupChat ref="editRef" :openType="openType" :dialogVisible="openDialog" @closeDialog="handleClose" />
    <!-- 绑定合伙人 -->
    <bindPartner :targetGroupChatId="bindChatId" :dialogVisible="openBindDialog" @closeBindDialog="handleBindClose" />
  </div>
</template>

<script>
  import addOrEditGroupChat from '../components/groupchat/addOrEditGroupChat.vue';
  import bindPartner from '../components/groupchat/bindPartner.vue';
  import groupChatApi from '@/api/newPartner/groupChat';
  export default {
    components: {
      addOrEditGroupChat,
      bindPartner
    },
    data() {
      return {
        tableLoading: false,
        filterForm: {
          status: '',
          dateRange: []
        },
        tableData: [],
        tablePage: {
          currentPage: 1,
          pageSize: 10,
          totalItems: 0
        },
        openDialog: false,
        openType: 'add', // 弹窗打开状态 新增/编辑
        openBindDialog: false,
        bindChatId: '' // 绑定合伙人目标群聊id
      };
    },
    created() {
      this.getGroupChatList();
    },
    methods: {
      merchantNames(merchantArray) {
        if (merchantArray && merchantArray instanceof Array && merchantArray.length > 0) {
          return merchantArray.map((el) => el.merchantName).join('、');
        } else {
          return '';
        }
      },
      getGroupChatList() {
        const that = this;
        this.tableLoading = true;
        const params = {
          pageNum: this.tablePage.currentPage,
          pageSize: this.tablePage.pageSize,
          status: this.filterForm.status,
          startTime: this.filterForm.dateRange[0] ? this.filterForm.dateRange[0] + ' 00:00:00' : '',
          endTime: this.filterForm.dateRange[1] ? this.filterForm.dateRange[1] + ' 23:59:59' : ''
        };
        groupChatApi
          .groupChatList(params)
          .then((res) => {
            that.tableData = res.data.data;
            that.tablePage.totalItems = Number(res.data.totalItems);
          })
          .finally(() => {
            that.tableLoading = false;
          });
      },
      //   搜索
      searchData() {
        this.tablePage.currentPage = 1;
        this.getGroupChatList();
      },
      //   重置
      resetData() {
        this.$refs['filterForm'].resetFields();
        this.searchData();
      },
      //   编辑群聊
      editGroupChat(item) {
        this.openDialog = true;
        this.openType = 'edit';
        this.$nextTick(() => {
          this.$refs['editRef'].open(item);
        });
      },
      //   添加群聊
      addGroup() {
        this.openDialog = true;
        this.openType = 'add';
      },
      handleClose(isTrue) {
        this.openDialog = false;
        if (isTrue) {
          this.getGroupChatList();
        }
      },
      //   绑定合伙人
      bindPartner(item) {
        this.bindChatId = item.id;
        this.openBindDialog = true;
      },
      //   关闭绑定合伙人弹窗
      handleBindClose(action) {
        this.openBindDialog = false;
        if (action === 'update') {
          this.getGroupChatList();
        }
      },
      showDetail(item) {
        this.$router.push({
          name: 'partnerDetail',
          query: {
            groupChatId: item.id,
            iscreated: item.status
          }
        });
      },
      handleSizeChange(e) {
        this.tablePage.pageSize = e;
        this.getGroupChatList();
      },
      handleCurrentChange(e) {
        this.tablePage.currentPage = e;
        this.getGroupChatList();
      }
    }
  };
</script>

<style lang="scss" scoped>
  .filter-options {
    margin-bottom: 20px;
  }
  .partner-num {
    display: inline-block;
    margin-right: 10px;
  }
</style>
