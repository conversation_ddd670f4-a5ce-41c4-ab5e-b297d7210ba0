<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta name="renderer" content="webkit" />
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" />
    <link rel="icon" href="<%= BASE_URL %>favicon.ico" />
    <script src="./components/ueditor/ueditor.config.js?v=12"></script>
    <script src="./components/ueditor/ueditor.all.js?v=12"></script>
    <script src="./components/ueditor/lang/zh-cn/zh-cn.js"></script>
    <script src="./components/ueditor/kityformula-plugin/addKityFormulaDialog.js"></script>
    <script src="./components/ueditor/kityformula-plugin/getKfContent.js"></script>
    <script src="./components/ueditor/kityformula-plugin/defaultFilterFix.js"></script>
    <script src="https://s1.videocc.net/library/blueimp-md5/md5-2.18.0.min.js"></script>
    <!--引入上传插件js-->
    <script src="https://player.polyv.net/resp/vod-player/latest/player.js"></script>
    <script>
      window.MathJax = {
        tex: {
          inlineMath: [
            ['$', '$'],
            ['\\(', '\\)']
          ],
          displayMath: [
            ['$$', '$$'],
            ['\\[', '\\]']
          ]
        },
        svg: { fontCache: 'global' }
      };
    </script>
    <script src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js"></script>

    <title><%= webpackConfig.name %></title>
  </head>
  <body>
    <div id="app"></div>
    <!-- built files will be auto injected -->
  </body>
</html>
