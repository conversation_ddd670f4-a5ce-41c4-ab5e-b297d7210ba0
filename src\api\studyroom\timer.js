
import request from '@/utils/request'

export default {

  // 分页查询
  timerList(pageNum, pageSize, data) {
    return request({
      url: '/studyroom//web/lamp/timer/list/' + pageNum + '/' + pageSize,
      method: 'GET',
      params: data
    })
  },
  //添加
  addTimer(data){
    return request({
      url: '/studyroom/web/lamp/timer',
      method: 'POST',
      data
    })
  },
  //详情
  timerDetail(id){
    return request({
      url: '/studyroom/web/lamp/timer',
      method: 'GET',
      params:{
        id:id
      }
    })
  },
  editTimer(data){
    return request({
      url: '/studyroom/web/lamp/timer',
      method: 'PUT',
      data
    })
  },
}
