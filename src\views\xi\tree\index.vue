<template>
  <div class="app-container">
    <el-form :inline="true" ref="queryForm" class="container-card" label-width="110px">
      <el-form-item label="类型：">
        <el-select v-model="dataQuery.commonWithered">
          <el-option label="树苗" :value="false" />
          <el-option label="通用枯树" :value="true" />
        </el-select>
      </el-form-item>
      <el-form-item label="默认树种：" v-if="!dataQuery.commonWithered">
        <el-select v-model="dataQuery.isDefault" size="small">
          <el-option label="否" :value="false" />
          <el-option label="是" :value="true" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-re" @click="resetQuery()">重置</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="search()">搜索</el-button>
      </el-form-item>
    </el-form>
    <el-col :span="24" style="margin-bottom: 30px">
      <el-button type="success" icon="el-icon-plus" size="mini" @click="addBtn()">新增</el-button>
    </el-col>
    <el-table class="common-table" v-loading="tableLoading" :data="tableData" style="width: 100%;margin-bottom: 20px;"
      row-key="id" border default-expand-all>
      <el-table-column prop="id" label="ID"></el-table-column>
      <el-table-column prop="name" label="树种名称" />
      <el-table-column prop="withdrawnBonus" label="操作" width="480">
        <template slot-scope="scope">
          <el-button type="primary" size="mini" @click="editBtn(scope.row.id)">编辑
          </el-button>
          <el-button type="danger" size="mini" @click="handleDelete(scope.row.id)">删除
          </el-button>
        </template>
      </el-table-column>
      <el-table-column prop="commonWithered" label="类型">
        <template slot-scope="scope">
          <span v-if="scope.row.commonWithered">通用枯树</span>
          <span v-else>树苗</span>
        </template>
      </el-table-column>
      <el-table-column prop="isDefault" label="默认树种">
        <template slot-scope="scope">
          <span v-if="!scope.row.commonWithered && scope.row.isDefault">是</span>
          <span v-else-if="!scope.row.commonWithered && !scope.row.isDefault">否</span>
          <span v-else>无</span>
        </template>
      </el-table-column>
      <el-table-column prop="unlockGold" label="解锁金币数">
        <template slot-scope="scope">
          <span v-if="!scope.row.commonWithered">{{ scope.row.unlockGold }}</span>
          <span v-else>无</span>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <el-col :span="24" style="margin-bottom: 20px">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>

    <!-- 修改或者新增弹窗 -->
    <el-dialog title="编辑树种" :visible.sync="open" width="80%" @close="close">
      <el-form ref="form" :model="form" :rules="rules" label-width="120px" style="width: 100%;">
        <el-form-item label="树种名称：" prop="name">
          <el-input v-model="form.name" />
        </el-form-item>
        <el-form-item label="类型：" prop="commonWithered">
          <el-radio-group v-model="form.commonWithered" size="small" @change="handleChange">
            <el-radio :label="false" border>树苗</el-radio>
            <el-radio :label="true" border>通用枯树</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="默认树种：" prop="isDefault" v-if="!form.commonWithered">
          <el-radio-group v-model="form.isDefault" size="small">
            <el-radio :label="false" border>否</el-radio>
            <el-radio :label="true" border>是</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="解锁金币数：" prop="unlockGold" v-if="!form.commonWithered">
          <el-input-number v-model="form.unlockGold" :min="0" />
        </el-form-item>
        <el-form-item label="生长周期阶段：" required v-if="!form.commonWithered">
          <div style="display: flex;flex-wrap:wrap">
            <el-form-item v-for="(item, index) in form.stages" :key="index" style="margin-right: 20px;margin-bottom: 10px">
              <OneImageUpload @handleSuccess="handleSuccess(item, $event)" :fileList="item.fileList" />
              <el-tooltip class="item" effect="dark" :content="index === 0 ? '阶段1默认为0' : '请输入百分比'" placement="top">
                <el-input v-if="!form.commonWithered" v-model="item.value" placeholder="百分比"
                  oninput="value=value.replace(/[^\d]/g,'')" style="margin-top: 10px;width: 150px"
                  controls-position="right" :readonly="index === 0" />
              </el-tooltip>
            </el-form-item>
          </div>
        </el-form-item>
        <el-form-item label="生长周期阶段（小）：" required v-if="!form.commonWithered">
          <div style="display: flex;flex-wrap:wrap">
            <el-form-item v-for="(item, index) in form.stages" :key="index" style="margin-right: 20px;margin-bottom: 10px">
              <OneImageUpload @handleSuccess="smallHandleSuccess(item, $event)" :fileList="item.smallFileList" />
            </el-form-item>
          </div>
        </el-form-item>
        <el-form-item v-if="!form.commonWithered">
          <el-button type="success" @click="addStage">添加生长周期阶段</el-button>
          <el-button type="danger" @click="removeStage">删除生长周期阶段</el-button>
        </el-form-item>
        <el-form-item label="时间周期阶段：" required v-if="!form.commonWithered">
          <div style="display: flex;flex-wrap:wrap">
            <el-form-item v-for="(item, index) in form.timeStages" :key="index" style="margin-right: 20px;margin-bottom: 10px">
              <OneImageUpload @handleSuccess="handleSuccess(item, $event)" :fileList="item.fileList" />
              <el-tooltip class="item" effect="dark" :content="index === 0 ? '阶段1默认为0' : '请输入分钟'" placement="top">
                <el-input v-if="!form.commonWithered" v-model="item.value" placeholder="分钟"
                  oninput="value=value.replace(/[^\d]/g,'')" style="margin-top: 10px;width: 150px"
                  controls-position="right" :readonly="index === 0" />
              </el-tooltip>
            </el-form-item>
          </div>
        </el-form-item>
        <el-form-item label="时间周期阶段（小）：" required v-if="!form.commonWithered">
          <div style="display: flex;flex-wrap:wrap">
            <el-form-item v-for="(item, index) in form.timeStages" :key="index" style="margin-right: 20px;margin-bottom: 10px">
              <OneImageUpload @handleSuccess="smallHandleSuccess(item, $event)" :fileList="item.smallFileList" />
            </el-form-item>
          </div>
        </el-form-item>
        <el-form-item v-if="!form.commonWithered">
          <el-button type="success" @click="addTimeStage">添加时间周期阶段</el-button>
          <el-button type="danger" @click="removeTimeStage">删除时间周期阶段</el-button>
        </el-form-item>
        <el-form-item label="枯树图片：">
          <OneImageUpload @handleSuccess="witheredHandleSuccess" :fileList="witheredFile" />
        </el-form-item>
        <el-form-item label="土壤图片：">
          <OneImageUpload @handleSuccess="soilUrlHandleSuccess" :fileList="soilFile" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import treeApi from '@/api/xi/tree'
import { pageParamNames } from '@/utils/constants'
import OneImageUpload from '@/components/Upload/OneImageUpload'
import MyUpload from '@/components/Upload/MyUpload'

export default {
  name: 'grading',
  components: { OneImageUpload, MyUpload },
  data() {
    return {
      witheredFile: [],
      soilFile: [],
      dataQuery: {
        commonWithered: null,
        isDefault: null,
        type: null
      },
      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      open: false,
      tableData: [],
      form: {},
      // 表单校验
      rules: {
        name: [{ required: true, message: '请输入树种名称', trigger: 'blur' }],
        isDefault: [{ required: true, message: '请选择', trigger: 'blur' }],
        type: [{ required: true, message: '请选择', trigger: 'blur' }],
        unlockGold: [{ required: true, message: '请输入解锁金币数', trigger: 'blur' }],
        commonWithered: [{ required: true, message: '请选择', trigger: 'blur' }]
      }
    }
  },
  created() {
    this.getPageList()
  },
  methods: {
    search() {
      this.tablePage = {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      }
      this.getPageList()
    },
    handleChange(val) {
      if (val) {
        this.form.stages = [
          {
            id: null,
            iconType: true,//true树苗 false枯树
            icon: null,//图片地址
            value: undefined
          }
        ]
      }
    },
    addStage() {
      let stage = {
        id: null,
        type: 1,//1生长周期 2时间周期
        icon: null,//图片地址
        value: undefined
      }
      this.form.stages.push(stage)
    },
    removeStage() {
      if (this.form.stages.length > 1) {
        this.form.stages.splice(this.form.stages.length - 1, 1)
      }
    },
    addTimeStage() {
      let stage = {
        id: null,
        type: 2,//1生长周期 2时间周期
        icon: null,//图片地址
        value: undefined
      }
      this.form.timeStages.push(stage)
    },
    removeTimeStage() {
      if (this.form.timeStages.length > 1) {
        this.form.timeStages.splice(this.form.timeStages.length - 1, 1)
      }
    },
    handleDelete(id) {
      this.$confirm('确定要删除吗？', '删除树种', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        treeApi.delete(id).then(res => {
          this.$message.success('删除成功！')
          this.getPageList()
        })
      })
    },
    addBtn() {
      this.reset()
      this.open = true
    },
    editBtn(id) {
      this.reset()
      treeApi.detail(id).then(res => {
        this.form = res.data
        this.form.stages.forEach(i => {
          this.$set(i, 'fileList', new Array(i.icon))
          this.$set(i, 'smallFileList', new Array(i.smallIcon))
        })
        this.form.timeStages.forEach(i => {
          this.$set(i, 'fileList', new Array(i.icon))
          this.$set(i, 'smallFileList', new Array(i.smallIcon))
        })
        if (this.form.witheredUrl) {
          this.witheredFile.push(this.form.witheredUrl)
        }
        if (this.form.soilUrl) {
          this.soilFile.push(this.form.soilUrl)
        }
        if (this.form.stages.length === 0) {
          let stage = {
            id: null,
            type: 1,//1生长周期 2时间周期
            icon: null,//图片地址
            value: 0
          }
          this.form.stages.push(stage)
        }
        if (this.form.timeStages.length === 0) {
          let stage = {
            id: null,
            type: 2,//1生长周期 2时间周期
            icon: null,//图片地址
            value: 0
          }
          this.form.timeStages.push(stage)
        }
        this.open = true
      })
    },
    submitForm() {
      let showPicError = false
      let showValueError = false
      if (!this.form.commonWithered) {
        this.form.stages.forEach(i => {
          if (i.value === undefined) {
            showValueError = true
          }
          if (i.icon === null) {
            showPicError = true
          }
        })
        this.form.timeStages.forEach(i => {
          if (i.value === undefined) {
            showValueError = true
          }
          if (i.icon === null) {
            showPicError = true
          }
        })
      }
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (showPicError) {
            this.$message.error('请上传图片')
            return false
          }
          if (!this.form.commonWithered) {
            if (showValueError) {
              this.$message.error('请输入')
              return false
            }
          }
          treeApi.saveOrUpdate(this.form).then(response => {
            this.$message.success('提交成功！')
            this.open = false
            this.getPageList()
          })
        }
      })
    },
    getPageList() {
      this.tableLoading = true
      this.dataQuery.pageNum = this.tablePage.currentPage
      this.dataQuery.pageSize = this.tablePage.size
      treeApi.list(this.dataQuery).then(res => {
        this.tableData = res.data.data
        this.tableLoading = false
        // 设置后台返回的分页参数
        pageParamNames.forEach(name =>
          this.$set(this.tablePage, name, parseInt(res.data[name]))
        )
      })
    },
    handlePage() {
      (this.tablePage = {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      }),
        this.getPageList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dataQuery = {
        commonWithered: null,
        isDefault: null,
        type: null
      }
      this.getPageList()
    },
    smallHandleSuccess(item, url) {
      item.smallFileList = [url]
      item.smallIcon = url
    },
    witheredHandleSuccess(url) {
      this.form.witheredUrl = url
      this.witheredFile = [url]
    },
    soilUrlHandleSuccess(url) {
      this.form.soilUrl = url
      this.soilFile = [url]
    },
    handleSuccess(item, url) {
      item.fileList = [url]
      item.icon = url
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val
      this.getPageList()
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val
      this.getPageList()
    },
    close() {
      this.open = false
      this.reset()
    },
    reset() {
      this.form = {
        name: null,
        isDefault: false,
        type: null,
        unlockGold: undefined,
        commonWithered: false,
        witheredUrl: null,
        soilUrl: null,
        stages: [//生长周期
          {
            id: null,
            type: 1,//1生长周期 2时间周期
            icon: null,//图片地址
            value: 0,
            fileList: []
          }
        ],
        timeStages: [//时间周期
          {
            id: null,
            type: 2,//1生长周期 2时间周期
            icon: null,//图片地址
            value: 0,
            fileList: []
          }
        ]
      }
      this.witheredFile = []
      this.soilFile = []
      if (this.$refs['form']) {
        this.$refs['form'].resetFields()
      }
    }
  }
}
</script>

<style>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.period-table td,
.period-table th {
  text-align: center;
}

@media (max-width: 767px) {
  .el-message-box {
    width: 80% !important;
  }
}

.el-tooltip__popper {
  max-width: 800px;
}
</style>
