/**
 * 教练 投诉相关接口
 */
import request from '@/utils/request'

export default {
  // 绩效分页查询
  performanceList(pageNum, pageSize, data) {
    return request({
      url: '/cousys/web/performance/list/' + pageNum + '/' + pageSize,
      method: 'GET',
      params: data
    })
  },
  // 详情
  performanceDetail(id) {
    return request({
      url: '/cousys/web/performance/detail/' + id,
      method: 'GET'
    })
  },
  //删除
  performanceDel(id) {
    return request({
      url: '/cousys/web/performance/delete/' + id,
      method: 'GET'
    })
  }
}

