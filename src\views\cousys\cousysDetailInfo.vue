<template>
  <div class="app-container">
    <!-- 面包屑导航区 -->
    <!--    <el-breadcrumb separator="/">-->
    <!--      <el-breadcrumb-item :to="{ path: '/welcome' }">首页</el-breadcrumb-item>-->
    <!--      <el-breadcrumb-item>排课管理</el-breadcrumb-item>-->
    <!--      <el-breadcrumb-item>排课中心</el-breadcrumb-item>-->
    <!--    </el-breadcrumb>-->
    <!-- 卡片视图 -->
    <!--     <el-card>-->
    <el-row>
      <el-col class="paike" style="font-size: 30px" :span="8" :xs="24">
        <span>排课中心</span>
      </el-col>
    </el-row>

    <!--步奏 -->

    <el-steps :space="200" :active="activeIndex" finish-status="success" align-center="center" class="width:600px">
      <el-step title="完善基础信息"></el-step>
      <el-step title="付款"></el-step>
      <el-step title="完善详细信息"></el-step>
      <el-step title="预排课"></el-step>
    </el-steps>

    <el-row>
      <el-col class="studentClass" :span="8" :xs="24">
        <span>学员信息</span>
      </el-col>
    </el-row>
    <el-form :inline="true" :rules="addSingle" class="container-card" label-width="96px" :model="cousysDetailAdd"
      label-position="left">
      <el-row>
        <el-col :span="8">
          <el-form-item label="学员学校：" prop="school">
            <el-input id="school" v-model="cousysDetailAdd.school" name="school" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="学校类别：" prop="schoolType">
            <el-radio v-model="cousysDetailAdd.schoolType" label="1">普通学校</el-radio>
            <el-radio v-model="cousysDetailAdd.schoolType" label="2">区重点</el-radio>
            <el-radio v-model="cousysDetailAdd.schoolType" label="3">市重点</el-radio>
            <el-radio v-model="cousysDetailAdd.schoolType" label="4">省重点</el-radio>
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item label="英语成绩：" prop="englishScore">
            <el-input oninput="value=value.replace(/[^\d]/g,'')" id="gradName" v-model="cousysDetailAdd.englishScore"
              name="englishScore" clearable />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="6">
          <el-form-item label="班级排名：" prop="classRank">
            <el-input oninput="value=value.replace(/[^\d]/g,'')" maxlength="20" isNumber2="true" min="1" id="classRank"
              v-model="cousysDetailAdd.classRank" name="id" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="学校排名：" prop="schoolRank">
            <el-input oninput="value=value.replace(/[^\d]/g,'')" maxlength="20" isNumber2="true" min="1" id="schoolRank"
              v-model="cousysDetailAdd.schoolRank" name="id" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="教练 性别：" prop="tutorGender">
            <el-select v-model="cousysDetailAdd.tutorGender" value-key="value" placeholder="请选择" clearable>
              <el-option v-for="(item, index) in [
                 { value: '1', label: '男' },
                 { value: '0', label: '女' }]" :key="index" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="性别优先：" prop="genderPriority">
            <el-radio v-model="cousysDetailAdd.genderPriority" label="1">是</el-radio>
            <el-radio v-model="cousysDetailAdd.genderPriority" label="0">否</el-radio>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col class="peopleClass" :span="24" :xs="24">
          <span>家长信息</span>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="工作单位：" prop="companyAddress">
            <el-input id="companyAddress" v-model="cousysDetailAdd.companyAddress" name="id" placeholder="" clearable />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24" style="margin-bottom: 30px; padding-left: 970px">
          <el-button type="warning" size="100px" @click="saveInfo()">下一步</el-button>
        </el-col>
      </el-row>
    </el-form>

    <!--     </el-card>-->
  </div>
</template>
<script>

import cousysApi from '@/api/cousys'

export default {
  data() {
    return {
      activeIndex: 3,
      addDate: {},
      rules: {},
      cousysDetailAdd: {
        memberCode: '',
        studentCode: '',
        courseOrderId: '',
        school: '',
        schoolType: '',
        englishScore: '',
        classRank: '',
        schoolRank: '',
        companyAddress: '',
        tutorGender: '',
        genderPriority: ''
      },
      radio: '0',
      //新增学员验证
      addSingle: {
        school: [{
          required: true,
          message: '必填',
          trigger: 'blur'
        }],
        schoolType: [{
          required: true,
          message: '必填',
          trigger: 'blur'
        }],
        englishScore: [{
          required: true,
          message: '必填',
          trigger: 'blur'
        }],
        classRank: [{
          required: true,
          message: '必填',
          trigger: 'blur'
        }],
        schoolRank: [{
          required: true,
          message: '必填',
          trigger: 'blur'
        }],
        companyAddress: [{
          required: true,
          message: '必填',
          trigger: 'blur'
        }],
        tutorGender: [{
          required: true,
          message: '必填',
          trigger: 'change'
        }],
        genderPriority: [{
          required: true,
          message: '必填',
          trigger: 'change'
        }]
      }
    }
  },

  created() {
    this.fetchData()
  },
  methods: {
    fetchData() {
      const that = this
      that.cousysDetailAdd.courseOrderId = window.localStorage.getItem('courseOrderId')
      that.cousysDetailAdd.studentCode = window.localStorage.getItem('cousysStudentCode')
      that.cousysDetailAdd.memberCode = window.localStorage.getItem('cousysMemberCode')
      if (that.cousysDetailAdd.memberCode && that.cousysDetailAdd.studentCode != null
        && that.cousysDetailAdd.memberCode > 0 && that.cousysDetailAdd.studentCode.length > 0) {
        cousysApi.queryCousysInfo(that.cousysDetailAdd.courseOrderId, that.cousysDetailAdd.memberCode, that.cousysDetailAdd.studentCode).then((res) => {
          that.cousysDetailAdd = res.data
          that.cousysDetailAdd.courseOrderId = window.localStorage.getItem('courseOrderId')
          that.cousysDetailAdd.studentCode = window.localStorage.getItem('cousysStudentCode')
          that.cousysDetailAdd.memberCode = window.localStorage.getItem('cousysMemberCode')
          that.tableLoading = false
        })
      }
    },
    saveInfo() {
      const that = this
      cousysApi.updateCousysInfo(that.cousysDetailAdd).then(res => {
        that.dataQuery = {}
        window.localStorage.setItem('courseOrderId', res.data)
        that.$router.push({
          path: '/cousys/commitCourse'
        })
      })
    }
  },
  // beforeDestroy() {
  //   window.localStorage.setItem('cousysMemberCode', '')
  //   window.localStorage.setItem('cousysStudentCode', '')
  // }
}

</script>
<style scoped>
.paike {
  padding-bottom: 50px;
  padding-top: 20px;
}

.studentClass {
  padding-left: 20px;
  padding-top: 20px;
  padding-bottom: 20px;
}

.peopleClass {
  padding-top: 20px;
  padding-bottom: 20px;
}
</style>
