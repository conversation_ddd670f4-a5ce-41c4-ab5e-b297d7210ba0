/**
 * “课程类型”相关接口
 */
import request from '@/utils/request';

/**
 * 课程类型列表查询
 * @param data
 */
export const getCourseCateList = (data) => {
  return request({
    url: '/znyy/curriculum/list/page',
    method: 'GET',
    params: data
  });
};
/**
 * 课程类型列表查询-根据课程名模糊查询课程类型
 * @param data
 */
export const getCourseCateByName = (data) => {
  return request({
    url: `/znyy/curriculum/findListByName/page`,
    method: 'GET',
    params: data
  });
};

/**
 * 课程类型列表查询-根据课程名模糊查询课程类型
 * @param data
 */
export const getCourseCateDetail = (id) => {
  return request({
    url: `/znyy/curriculum/findById?id=${id}`,
    method: 'GET'
  });
};

/**
 * 课程类型列表查询-根据课程名模糊查询课程类型
 * @param data
 */
export const addCourseCate = (data) => {
  return request({
    url: '/znyy/curriculum/save',
    method: 'POST',
    data
  });
};

/**
 * 课程类型列表查询-根据课程名模糊查询课程类型
 * @param data
 */
export const editCourseCate = (data) => {
  return request({
    url: '/znyy/curriculum/update',
    method: 'POST',
    data
  });
};

/**
 * 更换小组审核
 * @param data
 */
export const checkChange = (data) => {
  return request({
    url: '/deliver/web/team/checkChange',
    method: 'PUT',
    params: data
  });
};

/**
 * 更换小组审核列表
 * @param data
 */
export const changeTeamList = (data) => {
  return request({
    url: '/deliver/web/team/findChangeList',
    method: 'GET',
    params: data
  });
};

/**
 * 修改课程启用状态
 */
export const changeCourseStatus = (data) => {
  return request({
    url: '/znyy/curriculum/update/is-enable',
    method: 'POST',
    data
  });
};
