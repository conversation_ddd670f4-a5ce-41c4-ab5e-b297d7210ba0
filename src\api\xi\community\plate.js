import request from '@/utils/request'

export default {
  // 分页查询
  list(data) {
    return request({
      url: '/xi/web/plate/list',
      method: 'GET',
      params: data
    })
  },
  detail(id){
    return request({
      url: '/xi/web/plate',
      method: 'GET',
      params:{
        id:id
      }
    })
  },
//修改
  saveOrUpdate(data) {
    return request({
      url: '/xi/web/plate',
      method: 'POST',
      data
    })
  },
  //删除
  delete(id){
    return request({
      url: '/xi/web/plate',
      method: 'DELETE',
      params:{
        id:id
      }
    })
  }
 
}
