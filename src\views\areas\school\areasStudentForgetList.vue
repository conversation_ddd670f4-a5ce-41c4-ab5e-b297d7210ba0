<template>
  <div class="app-container">
    <el-form :inline="true" class="container-card" label-width="110px" label-position="left">

      <el-row>
        <el-col :span="6" :xs="24">
          <el-form-item label="学员编号：">
            <el-input v-model="dataQuery.studentCode" placeholder="请输入学员编号" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="6" :xs="24">
          <el-form-item label="登录账号：">
            <el-input v-model="dataQuery.loginName" placeholder="请输入登录账号" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="6" :xs="24">
          <el-form-item label="姓名：">
            <el-input v-model="dataQuery.realName" placeholder="请输入姓名" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="6" :xs="24">
          <el-form-item>
            <el-button type="primary" size="small" icon="el-icon-search" @click="fetchData01()">搜索</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <el-table class="common-table" v-loading="tableLoading" :data="tableData" style="width: 100%;margin-bottom: 20px;" row-key="id" border default-expand-all :tree-props="{list: 'children', hasChildren: 'true'}">
      <el-table-column prop="studentCode" label="学员编号"></el-table-column>
      <el-table-column prop="loginName" label="登录账号"></el-table-column>
      <el-table-column prop="withdrawnBonus" label="操作" width="120">
        <template slot-scope="scope">
          <el-button @click="openImage(scope.row.id,scope.row.realName)" type="success" size="mini" icon="el-icon-edit-outline">打印</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="realName" label="姓名"></el-table-column>
      <el-table-column prop="memberPhone" label="学员手机号">
        <template #header>
          <span>学员手机号</span>
          <i :class="phoneShowStatus=='0'?'el-icon-lock':'el-icon-unlock'" style="color: #1890ff; margin-left: 5px; font-size: 15px" @click="phoneShow" v-if="currentRole == 'School'"></i>
        </template>
        <template slot-scope="scope">
          <span>
            {{ scope.row.memberPhone }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="gradeName" label="年级"></el-table-column>
      <el-table-column prop="school" label="学校" width="300" show-overflow-tooltip></el-table-column>
      <el-table-column prop="createTime" label="添加时间"></el-table-column>
    </el-table>
    <!-- 分页 -->
    <el-col :span="24" style="overflow-x: auto;" :xs="24">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]" layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    </el-col>
    <!-- 海报弹框 -->
    <el-dialog title="21天抗遗忘报告" :visible.sync="dialogVisible" width="40%">
      <el-image v-loading="loading" style="width: 100%; height: 800px" :src="url"></el-image>
      <el-button type="primary" @click="download()">下载</el-button>
    </el-dialog>
    <ShowPhone :dialogVisible.sync="dialogVisiblePhone" :phoneShowStatus.sync="phoneShowStatus" @fetchData="fetchData"></ShowPhone>
  </div>
</template>

<script>
import studentForgetApi from "@/api/areasStudentForgetList.js";
import Tinymce from "@/components/Tinymce";
import { pageParamNames } from "@/utils/constants";
import ls from '@/api/sessionStorage'
import studentExperienceApi from "@/api/areasStudentExperienceList";
import ShowPhone from '@/components/ShowPhone/index.vue';
import userApi from '@/api/user';


export default {
  components: { ShowPhone },
  data() {
    return {
      dialogVisible: false,
      url: '',
      loading: true,
      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      tableData: [],
      dataQuery: {
        studentCode: '',
        merchantCode: '',
        startDate: '',
        endDate: '',
        loginName: '',
        realName: '',
      },
      value1: '',
      exportLoading: false,
      id: "",
      realName: "",
      dialogVisiblePhone: false,
      phoneShowStatus: localStorage.getItem('phoneShowStatus'),
      currentPhone: '',
      // 当前权限
      currentRole: window.localStorage.getItem('roleTag'),
    };
  },
  async created() {
    if (this.currentRole == 'School') {
      await this.getPhoneNum()
    }
    this.fetchData(this.phoneShowStatus);
  },
  methods: {
    downloadIamge(imgsrc, name) {

      var image = new Image();
      image.setAttribute("crossOrigin", "anonymous");
      image.onload = function () {
        var canvas = document.createElement("canvas");
        canvas.width = image.width;
        canvas.height = image.height;
        var context = canvas.getContext("2d");
        context.drawImage(image, 0, 0, image.width, image.height);
        var url = canvas.toDataURL("image/png");

        var a = document.createElement("a");
        var event = new MouseEvent("click");
        a.download = name || "photo";
        a.href = url;
        a.dispatchEvent(event);
      };
      image.src = imgsrc;
    },

    download() {
      var name = new Date().getTime();
      this.downloadIamge(this.url, this.realName + '21天抗遗忘报告');
    },

    // 海报打印
    openImage(id, realName) {
      this.id = id;
      this.realName = realName;
      this.dialogVisible = true;
      this.loading = true;
      // 海报打印注释loading
      studentForgetApi.download(id, realName).then(res => {
        this.url = res.data.file;
        this.loading = false;
      })

    },


    // 获取起始时间
    dateVal(e) {
      this.dataQuery.startDate = e[0]
      this.dataQuery.endDate = e[1]
    },

    fetchData01() {
      this.tablePage = {
        currentPage: 1,
        size: this.tablePage.size,
        totalPage: null,
        totalItems: null
      }
      this.fetchData(this.phoneShowStatus);
    },
    // 查询提现列表
    async fetchData(phoneShowStatus) {
      const that = this;
      that.tableLoading = true
      studentForgetApi.studentList(that.tablePage.currentPage, that.tablePage.size, that.dataQuery, phoneShowStatus == '0' ? '' : that.currentPhone).then(res => {
        console.log(res.data.data)
        that.tableData = res.data.data
        that.tableLoading = false
        // 设置后台返回的分页参数
        pageParamNames.forEach(name => that.$set(that.tablePage, name, parseInt(res.data[name])))
      })
    },
    async getPhoneNum() {
      const res = await userApi.getPhone();
      this.currentPhone = res.data.phone;
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val;
      this.fetchData(this.phoneShowStatus);
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val;
      this.fetchData(this.phoneShowStatus);
    },
    // 跳转到测试结果
    jumpOpenCourse(id) {
      const that = this
      ls.setItem('testId', id);
      that.$router.push({
        path: '/student/studentForget',
        query: {
          id: id
        }
      })
    },
    // 手机号码显示隐藏
    phoneShow() {
      console.log(this.phoneShowStatus);
      if (this.tableData.length == 0) return;
      if (this.phoneShowStatus == '0') {
        console.log(this.phoneShowStatus, 11111);
        this.dialogVisiblePhone = true;
      } else {
        this.phoneShowStatus = '0';
        localStorage.setItem('phoneShowStatus', '0');
        this.fetchData(this.phoneShowStatus);
      }
    }
  }
};
</script>

<style>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}
.period-table td,
.period-table th {
  text-align: center;
}
.mt20 {
  margin-top: 20px;
}
.red {
  color: red;
}
.green {
  color: green;
}
</style>
