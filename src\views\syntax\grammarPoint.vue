<template>
  <div class="app-container">
    <!-- 搜索 -->
    <el-form
      ref="dataQuery"
      :inline="true"
      :model="dataQuery"
      class="SearchForm SearchCenter"
      style="padding: 40px 0 20px 20px"
    >
      <el-form-item label="阶段:" prop="phase">
        <el-select
          size="medium"
          v-model="dataQuery.phase"
          filterable
          value-key="value"
          placeholder="请选择"
          clearable
        >
          <el-option
            v-for="(item, index) in phaseTypeList"
            :key="index"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        label="语法点:"
        style="margin-left: 40px"
        prop="grammarName"
      >
        <el-input
          size="medium"
          v-model="dataQuery.grammarName"
          @keyup.enter.native="fetchData01()"
          style="width: 200px"
          placeholder="请输入语法点"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item style="margin-left: 60px">
        <el-button
          type="primary"
          icon="el-icon-search"
          size="medium"
          @click="fetchData01()"
          >搜索</el-button
        >
        <el-button
          type="info"
          icon="el-icon-refresh"
          size="medium"
          @click="resetData()"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <div class="SearchForm">
      <!-- 添加 -->
      <div class="btn-add" style="margin-bottom: 20px">
        <el-button
          size="medium"
          type="primary"
          icon="el-icon-plus"
          @click="clickAdd"
          >添加</el-button
        >
      </div>
      <!-- 表格 -->
      <el-table
        class="common-table"
        :data="tableData"
        stripe
        border
        max-height="500"
        :default-sort="{ prop: 'addTime', order: 'descending' }"
        v-loading="tableLoading"
        :empty-text="tableData.length == 0 ? '暂无数据' : ''"
      >
        <!-- <el-table-column prop="phase" :formatter="mappedPhase" label="阶段"></el-table-column> -->
        <el-table-column prop="phase" label="阶段"></el-table-column>
        <el-table-column prop="name" label="语法点"></el-table-column>
        <el-table-column prop="sortNum" label="排序"></el-table-column>
        <el-table-column prop="id" label="操作">
          <template slot-scope="scope">
            <el-button
              type="success"
              size="medium"
              icon="el-icon-edit-outline"
              @click="openEditName(scope.row)"
              >编辑</el-button
            >
            <el-button
              type="danger"
              size="medium"
              icon="el-icon-delete"
              @click="deleteGrammar(scope.row.id)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <el-col :span="20">
      <el-pagination
        :current-page="tablePage.currentPage"
        :page-sizes="[10, 50, 100, 150, 200]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="tablePage.totalItems"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-col>

    <!-- 弹窗 -->
    <el-dialog
      :title="title"
      :visible.sync="dialogVisible"
      width="40%"
      :close-on-click-modal="false"
      @close="close"
      class="common-form"
    >
      <el-form
        ref="dialogForm"
        :model="dataQueryDialog"
        :rules="updateSingle"
        style="width: 100%"
      >
        <el-form-item
          class="elformItem"
          label="阶段:"
          label-width="120px"
          prop="phase"
        >
          <el-select
            size="medium"
            style="width: 70%"
            v-model="dataQueryDialog.phase"
            filterable
            value-key="value"
            placeholder="请选择"
            clearable
          >
            <el-option
              v-for="(item, index) in phaseTypeList"
              :key="index"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="语法点:" label-width="120px" prop="name">
          <el-input
            size="medium"
            maxlength="50"
            show-word-limit
            v-model="dataQueryDialog.name"
            style="width: 70%"
            placeholder="请输入内容"
          ></el-input>
        </el-form-item>
        <el-form-item label="排序:" label-width="120px" prop="sortNum">
          <el-input
            size="medium"
            v-model="dataQueryDialog.sortNum"
            style="width: 70%"
            @input="validateSortNumInput"
            placeholder="请输入排序"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="medium" @click="close">取消</el-button>
        <el-button size="medium" type="primary" @click="submitClick()"
          >确定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  queryByTypeAPI,
  detailAPI,
  pageAPI,
  deleteAPI,
  addOrUpdateAPI,
} from "@/api/grammar/grammarPoint";
import { pageParamNames } from "@/utils/constants";
export default {
  data() {
    return {
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null,
      },
      tableLoading: false,
      dataQuery: {
        phase: "",
        grammarName: "",
      },
      dataQueryDialog: {
        phase: "",
        grammarName: "",
        sortNum: "",
      },
      updateSingle: {
        name: [
          {
            required: true,
            message: "必填",
            trigger: "blur",
          },
        ],
        phase: [
          {
            required: true,
            message: "必填",
            trigger: "blur",
          },
        ],
        sortNum: [
          {
            required: true,
            message: "必填",
            trigger: "blur",
          },
        ],
      },
      isRouterAlive: true, //局部刷新
      tableData: [], //表格数据
      phase: "",
      // 弹窗数据
      dialogVisible: false, // 修改弹窗是否展示
      title: "",
      rows: [], //选中的数据id存放处
      phaseTypeList: [], //课程分类
    };
  },

  created() {
    // 获取所属分类
    this.getPhaseType();
  },
  methods: {
    validateSortNumInput(event) {
      if (!/^\d*$/.test(event)) {
        this.$message.error("请输入有效的数字");
        this.dataQueryDialog.sortNum = ""; // 清空输入框
      }
    },
    // 重置表单
    reset() {
      this.dataQueryDialog = {
        phase: "",
        name: "",
        sortNum: "",
      };
    },

    fetchData01() {
      this.tablePage.currentPage = 1; // 重置为第一页
      this.fetchData();
    },
    // 获取分类返回类型
    getPhaseType() {
      queryByTypeAPI({ dictType: "grammar_phase" }).then((response) => {
        this.phaseTypeList = response.data.map((item) => ({
          id: item.dictValue,
          name: item.dictLabel,
        }));
        this.fetchData();
      });
    },
    // 查询表格列表
    fetchData() {
      this.tableLoading = true;
      pageAPI(this.dataQuery, this.tablePage)
        .then((res) => {
          this.tableData = res.data.data; // 确认接口返回的数据结构
          res.data.data.forEach((item) => {
            const foundPhase = this.phaseTypeList.find(
              (phaseItem) => phaseItem.id === item.phase
            );
            if (foundPhase) {
              item.phase = foundPhase.name;
            }
          });
          this.tableLoading = false;
          // 设置后台返回的分页参数
          pageParamNames
            .filter((i) => i !== "currentPage")
            .forEach((name) =>
              this.$set(this.tablePage, name, parseInt(res.data[name]))
            );
        })
        .catch((error) => {
          console.error("数据加载失败", error);
          this.tableLoading = false;
        });
    },
    // 重置列表
    resetData() {
      this.$refs.dataQuery.resetFields();
      this.dataQuery = {
        phase: "",
        name: "",
      };
      this.fetchData();
    },

    //添加操作
    clickAdd() {
      this.reset();
      this.reload();
      this.dialogVisible = true;
      this.title = "添加";
    },
    // 弹窗确定按钮
    submitClick() {
      this.$refs.dialogForm.validate((valid) => {
        if (valid) {
          let id = this.dataQueryDialog.id;
          if (!id) {
            // 新增操作
            addOrUpdateAPI(this.dataQueryDialog).then((res) => {
              this.$nextTick(() => this.fetchData()); // 刷新数据
              this.$message.success("新增成功");
              this.close(); // 关闭弹窗
            });
          } else {
            // 编辑操作
            addOrUpdateAPI(this.dataQueryDialog).then((res) => {
              this.$nextTick(() => this.fetchData()); // 刷新数据
              this.$message.success("编辑成功");
              this.close(); // 关闭弹窗
            });
          }
        } else {
          this.$message.error("请完善表单信息");
          return false;
        }
      });
    },
    //局部刷新
    reload() {
      this.isRouterAlive = false;
      this.$nextTick(function () {
        this.isRouterAlive = true;
      });
    },
    // 编辑弹窗
    openEditName(row) {
      detailAPI({ id: row.id })
        .then((res) => {
          this.dataQueryDialog = res.data;
        })
        .then(() => {
          this.dialogVisible = true;
          this.title = "编辑";
        });
    },

    //删除操作题目
    deleteGrammar(id) {
      this.$confirm(
        "语法点删除后，对应的知识点和题目也会全部删除",
        "删除语法点",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(function () {
          return deleteAPI({ id: id });
        })
        .then(() => {
          this.fetchData();
          this.$message.success("删除成功");
        });
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val;
      this.fetchData();
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val;
      this.fetchData();
    },
    // 关闭弹窗
    close() {
      this.dialogVisible = false;
      this.dataQuery = {
        phase: "",
        name: "",
      };
      this.reset();
      this.$refs.dialogForm.resetFields();
    },
  },
};
</script>
<style scoped lang="scss">
.common-form .el-form-item {
  margin-bottom: 50px;
}
</style>
