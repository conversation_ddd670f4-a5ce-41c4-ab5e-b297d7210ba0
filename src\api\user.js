/**
 * “用户管理”相关接口
 */
import request from '@/utils/request';

export default {
  queryUser(queryParam, pageParam) {
    return request({
      url: '/znyy/sys/user/list',
      method: 'get',
      params: {
        loginName: queryParam.loginName,
        realName: queryParam.realName,
        roleTag: queryParam.roleTag,
        merchantName: queryParam.merchantName,
        pageNum: pageParam.currentPage,
        pageSize: pageParam.size
      }
    });
  },
  queryStudyCommitteeUser(queryParam, pageParam) {
    return request({
      url: '/znyy/sys/user/studycommittee/list',
      method: 'get',
      params: {
        loginName: queryParam.loginName,
        realName: queryParam.realName,
        roleTag: queryParam.roleTag,
        merchantName: queryParam.merchantName,
        pageNum: pageParam.currentPage,
        pageSize: pageParam.size
      }
    });
  },
  updateUser(data) {
    return request({
      url: '/znyy/sys/user/info',
      method: 'patch',
      data
    });
  },

  // 获取手机号
  getPhone() {
    return request({
      url: '/znyy/sys/user/getLoginPhone',
      method: 'get'
    });
  },

  updatePwd(data) {
    return request({
      url: '/znyy/sys/user/pwd',
      method: 'patch',
      data
    });
  },

  addUser(data) {
    return request({
      url: '/znyy/sys/user',
      method: 'post',
      data
    });
  },

  deleteUser(data) {
    return request({
      url: '/znyy/sys/user',
      method: 'delete',
      data
    });
  },

  /**
   * 更新用户的角色
   * @param perm
   */
  updateUserRoles(data) {
    return request({
      url: '/znyy/sys/user/role',
      method: 'patch',
      data
    });
  },

  /**
   * 新增用户
   */
  addNewUser(data) {
    return request({
      url: '/znyy/sys/user/addUser',
      method: 'post',
      data
    });
  },
  /**
   * 新 编辑用户
   */
  editNewUser(data) {
    return request({
      url: '/znyy/sys/user/editUser',
      method: 'PUT',
      data
    });
  },
  /**
   * 通过商户编号获取用户开通状态isEnable
   * @param {*} merchantCode
   * @returns
   */
  getUserIsEnable(merchantCode) {
    return request({
      url: '/znyy/V2/merchant/getIsEnable',
      method: 'GET',
      params: { merchantCode }
    });
  },

  sendSms(data) {
    return request({
      url: `/znyy/alibaba/sms/refund/send?orderId=${data.orderId}&amount=${data.amount}`,
      method: 'PUT',
    });
  },
  saveSms(data) {
    return request({
      url:'/znyy/areas/student/tui/course/save',
      method: 'POST',
      data
    })
  }
};
