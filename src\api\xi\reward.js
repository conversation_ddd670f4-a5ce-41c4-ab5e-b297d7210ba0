import request from '@/utils/request'

export default {
  // 分页查询
  list(data) {
    return request({
      url: '/xi/web/awardRules/list',
      method: 'GET',
      params: data
    })
  },
  detail(id){
    return request({
      url: '/xi/web/awardRules/detail',
      method: 'GET',
      params:{
        id:id
      }
    })
  },
//修改
  saveOrUpdate(data) {
    return request({
      url: '/xi/web/awardRules/saveOrUpdate',
      method: 'POST',
      data
    })
  },
  //删除
  delete(id){
    return request({
      url: '/xi/web/awardRules',
      method: 'DELETE',
      params:{
        id:id
      }
    })
  }
 
}
