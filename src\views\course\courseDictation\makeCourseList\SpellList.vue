<!--正式课拼读拼写 划拼音规则 -->
<template>
  <div class="container_box">
    <el-button type="primary" style="margin-bottom: 10px" @click="onAddspell" icon="el-icon-plus" size="mini" v-if="showColumn">添加</el-button>
    <el-table :data="spellData" class="common-table" stripe border v-loading="spellLoading" default-expand-all>
      <el-table-column label="单词ID" prop="id"></el-table-column>
      <el-table-column label="单词" prop="word"></el-table-column>
      <el-table-column label="音节" v-if="showColumn" prop="syllable" width="160"></el-table-column>

      <el-table-column label="单词拆分" v-if="showColumn" prop="split">
        <template slot-scope="{ row }">
          {{ formatSplit(row.split) }}
        </template>
      </el-table-column>
      <el-table-column label="重音" v-if="showColumn" prop="accent" width="160"></el-table-column>
      <el-table-column label="次重音" v-if="showColumn" prop="secondaryAccent" width="160"></el-table-column>
      <el-table-column label="单词类型" v-if="true">
        <template slot-scope="{ row }">
          {{ row.type == '2' ? '拼读' : '' }}
          {{ row.isWriteSelect == '1' ? '拼写' : '' }}
          {{ row.isSelect == '2' ? '学后写' : row.isSelect == '1' ? '学后读' : row.isSelect == '3' ? '学后读写' : '' }}
        </template>
      </el-table-column>
      <el-table-column label="操作" prop="id" v-if="showColumn" width="350" align="left">
        <template slot-scope="{ row }">
          <div style="text-align: left">
            <el-button type="primary" size="mini" icon="el-icon-edit" @click="editspellItem(row)">编辑</el-button>
            <el-button type="danger" size="mini" icon="el-icon-delete" @click="delSyllableItem(row)">删除</el-button>
            <el-button class="botton_focus" size="mini" icon="el-icon-top" v-if="row.previousId" @click="moveUpItem(row)">上移</el-button>
            <el-button class="botton_focus" size="mini" icon="el-icon-bottom" v-if="row.nextId" @click="moveDownItem(row)">下移</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog
      v-loading="submiteStatus2"
      :title="`${spellTitle}拼读拼写规则`"
      :visible.sync="spellDialogVisible"
      width="70%"
      :close-on-click-modal="false"
      @close="spellDialogClose"
    >
      <NoSpellStepForm
        ref="NoSpellStepForm"
        v-if="spellDialogVisible"
        :spellId="editWordId"
        :editWordCode="editWordCode"
        :id="id"
        :isSelect="isSelect"
        :isWriteSelect="isWriteSelect"
        :spellType="spellType"
        @submitChangeStatus="submiteStatus = false"
        @spellDialogClose="spellDialogClose"
        @resetCode="resetCode"
      ></NoSpellStepForm>

      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="spellDialogClose">关闭</el-button>
        <el-button size="mini" type="primary" :disabled="submiteStatus" @click="debouncedSubmit">提交</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
  import { debounce } from '@/utils/index.js';
  import AddDictationWord from '@/views/course/courseDictation/addDictationWord.vue';
  import NoSpellStepForm from '../components/noSpellStepForm.vue';
  import courseDictationListApi from '@/api/courseDictationList';
  export default {
    components: { AddDictationWord, NoSpellStepForm },
    props: {},
    data() {
      return {
        spellData: [],
        spellTitle: '添加',
        spellLoading: false,
        spellDialogVisible: false,
        courseCode: this.$route.query.courseCode,
        courseId: this.$route.query.id,
        id: '',
        isSelect: 0,
        isWriteSelect: 0,
        submiteStatus2: false,
        editWordId: '',
        editWordCode: '',
        submiteStatus: true,
        showColumn: true //显示编辑
      };
    },
    created() {
      this.isHideEdit();
      this.debouncedSubmit = debounce(this.submiteValidate, 1000);
    },
    mounted() {
      this.getTableData();

      // this.$bus.$on('hideSpellEdit',()=>{
      //     this.showColumn = false
      //     console.log('hideSpellEditXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX',this.showColumn);
      // })
    },
    watch: {},
    methods: {
      isHideEdit() {
        courseDictationListApi.getRuleListByCourseCode(this.courseCode, 4, 1).then((res) => {
          if (res.code === 20000) {
            let DrawSyllableListData = res.data.data;
            let allEmpty = true; // 假设所有都是空的
            for (let i = 0; i < DrawSyllableListData.length; i++) {
              if (DrawSyllableListData[i].wordInfoVoList && DrawSyllableListData[i].wordInfoVoList.length > 0) {
                allEmpty = false; // 如果找到一个非空的，就设置allEmpty为false
                break; // 如果只需要知道是否至少有一个非空的，可以在这里退出循环
              }
            }
            this.showColumn = allEmpty;
            console.log('hideSpellEditXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX', this.showColumn);
          }
        });
      },
      //删除
      delSyllableItem(row) {
        courseDictationListApi.deleteWordById(row.id, '2').then((res) => {
          if (res.code === 20000) {
            this.$message.success('删除成功');
            this.getTableData();
          }
        });
      },
      formatSplit(str) {
        let index = str.indexOf('/');
        let splitText = index > 0 ? str.substring(0, index) : str;
        let index2 = splitText.indexOf('&');
        splitText = index2 > 0 ? str.substring(0, index2) : splitText;
        // console.log('splitText',splitText,);
        return splitText;
      },
      async getTableData() {
        this.spellLoading = true;

        let res = await courseDictationListApi.getRuleListByCourseCode(this.courseCode, '2', '2');

        // .then((res) => {
        this.spellData = [];
        if (res.code === 20000) {
          let rootData = res.data.data;
          rootData.forEach((item, index) => {
            item.wordInfoVoList.forEach((word) => {
              word.courseId = item.id;
              this.spellData.push(word);
            });
          });
        }

        this.spellLoading = false;
        console.log('this.spellData', this.spellData, this.showColumn);
        // });
      },

      submiteValidate() {
        this.submiteStatus2 = true;
        if (this.editWordId) {
          this.$refs.NoSpellStepForm.updateSubmitValidte();
        } else {
          this.$refs.NoSpellStepForm.testReadSubmitValidte();
        }
        this.submiteStatus2 = false;
      },
      //添加划拼音规则
      onAddspell() {
        this.spellType = 0;
        this.$bus.$emit('clearFile');
        this.id = '';
        this.editWordId = '';
        this.editWordCode = '';
        this.spellDialogVisible = true;
        this.isSelect = 0;
        this.isWriteSelect = 0;
      },
      //编辑划音节
      editspellItem(row) {
        this.spellDialogVisible = true;
        this.spellTitle = '编辑';
        this.id = row.courseId;
        this.spellType = row.type;
        this.editWordId = row.id;
        this.isSelect = row.isSelect;
        this.isWriteSelect = row.isWriteSelect;
        this.editWordCode = row.wordCode;
      },
      //新增/编辑
      onSubmit() {},
      //关闭弹窗
      spellDialogClose() {
        this.spellDialogVisible = false;
        this.id = '';
        this.submiteStatus = true;
        this.getTableData();
      },
      resetCode(val) {
        this.editWordCode = val;
      },
      // 上移
      moveUpItem(row) {
        console.log('row-------------shangyi', row);
        courseDictationListApi
          .wordUp({
            currentId: row.id,
            nextId: row.previousId
          })
          .then((res) => {
            if (res.code === 20000) {
              this.$message.success(`${row.word}` + '上移成功');
              this.getTableData();
            } else {
              this.$message.error(`${res.message}`);
            }
          });
      },
      // 下移
      moveDownItem(row) {
        courseDictationListApi
          .wordDown({
            currentId: row.id,
            nextId: row.nextId
          })
          .then((res) => {
            if (res.code === 20000) {
              this.$message.success(`${row.word}` + '下移成功');
              this.getTableData();
            } else {
              this.$message.error(`${res.message}`);
            }
          });
      }
    }
  };
</script>
<style lang="less" scoped>
  .botton_focus:focus {
    color: #606266 !important;
    background-color: transparent !important;
    border-color: #dcdfe6 !important;
  }
  .botton_focus:hover {
    color: #1890ff !important;
    background-color: #e8f4ff !important;
    border-color: #badeff !important;
  }
</style>
