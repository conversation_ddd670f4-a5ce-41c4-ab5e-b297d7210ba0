import request from "@/utils/request";

// 根据字典查询阶段相应的值 {dictType:'grammar_phase'}
export function queryByTypeAPI(query) {
  return request({
    url: "/dyf/web/v2/dict/queryByType",
    method: "get",
    params: query,
  });
}

// 下拉选项 语法点 知识点
export function optionsAPI(query) {
  return request({
    url: "/dyf/web/v2/knowledge/options",
    method: "get",
    params: query,
  });
}

// 试题分页查询 /dyf/web/v2/question/page
export function pageAPI(query, pageParam) {
  return request({
    url: "/dyf/web/v2/question/page",
    method: "get",
    params: {
      ...query,
      pageNum: pageParam.currentPage == null ? 1 : pageParam.currentPage,
      pageSize: pageParam.size == null ? 10 : pageParam.size,
    },
  });
}

//
export function superPageAPI(query, pageParam) {
  return request({
    url: "/dyf/web/v2/question/superPage",
    method: "get",
    params: {
      ...query,
      pageNum: pageParam.currentPage == null ? 1 : pageParam.currentPage,
      pageSize: pageParam.size == null ? 10 : pageParam.size,
    },
  });
}

// 试题新增 编辑 /dyf/web/v2/question/addOrUpdate
export function addOrUpdateAPI(data) {
  return request({
    url: "/dyf/web/v2/question/addOrUpdate",
    method: "post",
    data,
  });
}

// 根据知识点 id 查找思维导图的所有三级节点 /dyf/web/v2/mindMap/thirdNode
export function thirdNodeAPI(query) {
  return request({
    url: "/dyf/web/v2/mindMap/thirdNode",
    method: "get",
    params: query,
  });
}

// 题目删除 /dyf/web/v2/question/delete
export function deleteAPI(query) {
  return request({
    url: "/dyf/web/v2/question/delete",
    method: "delete",
    params: query,
  });
}

// 试题详情 /dyf/web/v2/question/detail
export function detailAPI(query) {
  return request({
    url: "/dyf/web/v2/question/detail",
    method: "get",
    params: query,
  });
}
