<template>
  <div class="app-container">
    <el-form :inline="true" class="container-card" label-width="110px" label-position="left">
      <el-form-item label="商户编号：">
        <el-input v-model="dataQuery.merchantCode" placeholder="请输入商户编号：" clearable />
      </el-form-item>
      <el-form-item label="添加时间：">
        <el-date-picker style="width: 100%;" v-model="value" value-format="yyyy-MM-dd HH:mm:ss" type="daterange"
          align="right" unlink-panels range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" clearable />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="fetchData()">搜索</el-button>
      </el-form-item>
    </el-form>
    <el-table v-loading="tableLoading" :data="tableData" style="width: 100%;margin-bottom: 20px;" row-key="id" border
      default-expand-all :tree-props="{ list: 'children', hasChildren: 'true' }">
      <el-table-column prop="id" align="center" label="编号" />
      <el-table-column prop="merchantCode" align="center" label="商户编号" />
      <el-table-column label="操作" align="center" width="200" fixed="right">
        <template slot-scope="scope">
          <el-tooltip content="完成提现" placement="top">
            <el-button type="success" size="mini"
              v-if="checkPermission(['b:profitaccount:profitWithdrawRecoder:over']) && scope.row.bonusStatus == 0 && roleTag === 'admin' && scope.row.companyCode === ''"
              @click="successwithdraw(scope.row.id, 1)">已支付</el-button>
          </el-tooltip>
          <el-tooltip content="完成提现" placement="top">
            <el-button type="success" size="mini"
              v-if="checkPermission(['b:profitaccount:profitWithdrawRecoder:over']) && scope.row.bonusStatus == 0 && roleTag === 'Company'"
              @click="successwithdraw(scope.row.id, 1)">已支付</el-button>
          </el-tooltip>
          <el-tooltip content="不通过" placement="top">
            <el-button type="danger" size="mini"
              v-if="checkPermission(['b:profitaccount:profitWithdrawRecoder:over']) && scope.row.bonusStatus == 0 && roleTag === 'admin' && scope.row.companyCode === ''"
              @click="successwithdraw(scope.row.id, -1)">不通过</el-button>
          </el-tooltip>
          <el-tooltip content="不通过" placement="top">
            <el-button type="danger" size="mini"
              v-if="checkPermission(['b:profitaccount:profitWithdrawRecoder:over']) && scope.row.bonusStatus == 0"
              @click="successwithdraw(scope.row.id, -1)">不通过</el-button>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column prop="countBonus" align="center" label="提现金额" />
      <el-table-column prop="bonusStatus" align="center" label="提现状态">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.bonusStatus === 0"> 提现中</el-tag>
          <el-tag v-if="scope.row.bonusStatus === 1"> 已支付</el-tag>
          <el-tag v-if="scope.row.bonusStatus === -1"> 不通过</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="addTime" align="center" label="增加时间" />
    </el-table>

    <!-- 分页 -->
    <el-col :span="20">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>
  </div>
</template>

<script>
import authenticationApi from '@/api/authentication'
import checkPermission from '@/utils/permission'
import profitWithdrawRecoderApi from '@/api/profitWithdrawRecoder'
import ls from '@/api/sessionStorage'
import { pageParamNames } from '@/utils/constants'
export default {
  data() {
    return {
      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      value: '',
      id: '',
      roleTag: '',
      tableData: [],
      dataQuery: {
        merchantCode: ''
      }
    }
  },
  created() {
    this.fetchData()
    this.dataQuery.merchantCode = ls.getItem("withdrawRecodeMerchantCode");
  },
  mounted() {
    authenticationApi.checkAccountBalance().then(res => {
      this.roleTag = res.data.data.roleTag;
    })
  },
  methods: {
    checkPermission,
    // 查询提现列表
    fetchData() {
      const that = this
      that.tablePage = {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      }
      if (that.value != null) {
        that.dataQuery.startDate = that.value[0];
        that.dataQuery.endDate = that.value[1];
      } else {
        that.dataQuery.startDate = '';
        that.dataQuery.endDate = '';
      }
      that.tableLoading = true
      profitWithdrawRecoderApi.marketWithdrawRecoderList(
        that.tablePage.currentPage,
        that.tablePage.size,
        that.dataQuery
      ).then(res => {
        console.log(res)
        that.tableData = res.data.data
        that.tableLoading = false
        // 设置后台返回的分页参数
        pageParamNames.forEach(name =>
          that.$set(that.tablePage, name, parseInt(res.data[name]))
        )
      })
    },
    // 查看分支列表
    checkWithdrawalBranchList(memberId) {
      const that = this
      that.$router.push({
        path: '/wechatPublicAccount/experienceBonusWithdrawRecoder',
        query: {
          memberId: memberId
        }
      })
    },
    // 查看分支列表
    successwithdraw(id, status) {
      const that = this
      profitWithdrawRecoderApi.update(id, status).then(res => {
        that.fetchData();
        that.$message.success("操作完成");
      })
    },
    // 查看流水
    showFlow(id) {
      const that = this
      that.$router.push({
        path: '/wechatPublicAccount/experienceFlow',
        query: {
          id: id
        }
      })
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val
      this.fetchData()
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val
      this.fetchData()
    }
  }
}
</script>

<style>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}
</style>
