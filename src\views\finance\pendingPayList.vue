<template>
  <div class="app-container">
    <el-form :inline="true" class="container-card" label-width="96px" label-position="left" ref="dataQuery" :model="dataQuery" :rules="rules">
      <el-row>
        <el-col :span="8" :xs="24">
          <el-form-item label="姓名：" prop="studentName">
            <el-input id="studentName" v-model="dataQuery.studentName" name="id" placeholder="请输入姓名" />
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="学员编号：" prop="studentCode">
            <el-input id="studentCode" v-model="dataQuery.studentCode" name="id" placeholder="请输入学员编号" oninput="value=value.replace(/[^\d]/g,'')" />
          </el-form-item>
        </el-col>

        <el-col :span="8" :xs="24">
          <el-form-item label="订单状态:" prop="orderStatus">
            <el-select v-model="dataQuery.orderStatus" filterable value-key="value" placeholder="请选择" clearable>
              <el-option
                v-for="(item, index) in [
                  { value: 0, label: '待支付' },
                  { value: 1, label: '支付成功' },
                  { value: 2, label: '已失效' },
                  { value: 3, label: '支付失败' }
                ]"
                :key="index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <!-- ----------------------------- -->
      <el-row>
        <el-col :span="14" :xs="24">
          <el-form-item label="创建时间：" prop="regTime">
            <el-date-picker
              style="width: 100%"
              v-model="regTime"
              type="daterange"
              value-format="yyyy-MM-dd HH:mm:ss"
              align="right"
              unlink-panels
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            ></el-date-picker>
          </el-form-item>
        </el-col>
        <!-- ----------------------------- -->
        <el-col :span="10" :xs="24" style="text-align: right">
          <el-form-item>
            <el-button
              type="primary"
              icon="el-icon-search"
              @click="
                () => {
                  this.tablePage.currentPage = 1;
                  fetchData();
                }
              "
            >
              搜索
            </el-button>
            <el-button icon="el-icon-refresh" @click="rest()">重置</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-table
      class="center-table"
      v-loading="tableLoading"
      :data="tableData"
      height="400px"
      style="width: 100%; margin-bottom: 20px"
      row-key="id"
      default-expand-all
      :tree-props="{ list: 'children', hasChildren: 'true' }"
      :header-cell-style="{ color: '#666', height: '50px', textAlign: 'center' }"
      :row-style="{ height: '40px' }"
    >
      <el-table-column prop="studentName" label="姓名" width="150px"></el-table-column>
      <el-table-column prop="studentCode" label="学员编号" width="150px"></el-table-column>
      <el-table-column prop="grade" label="学段" width="100px">
        <template v-slot="{ row }">
          <el-tag type="info" v-if="row.grade == 'xiaoxue'">小学</el-tag>
          <el-tag type="warning" v-else-if="row.grade == 'chuzhong'">初中</el-tag>
          <el-tag type="danger" v-else-if="row.grade == 'gaozhong'">高中</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="rechargeHours" label="充值课时" width="100px">
        <template v-slot="{ row }">
          <el-tag>{{ row.rechargeHours }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="hoursPrice" label="课时单价" width="100px">
        <template v-slot="{ row }">
          <span>{{ (row.hoursPrice * 0.01).toFixed(2) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="rechargeDeliveryHours" label="充值交付课时" width="130px">
        <template v-slot="{ row }">
          <el-tag>{{ row.rechargeDeliveryHours }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="deliveryHoursPrice" label="交付课时单价" width="130px">
        <template v-slot="{ row }">
          <span>{{ (row.deliveryHoursPrice * 0.01).toFixed(2) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="orderPrice" label="订单金额" width="150px">
        <template v-slot="{ row }">
          <span>{{ (row.orderPrice * 0.01).toFixed(2) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="orderStatus" label="订单状态" width="150px">
        <template v-slot="{ row }">
          <el-tag type="info" v-if="row.orderStatus == '0'">待支付</el-tag>
          <el-tag type="success" v-else-if="row.orderStatus == '1'" class="green">支付成功</el-tag>
          <el-tag type="danger" v-else-if="row.orderStatus == '2'">已失效</el-tag>
          <el-tag type="danger" v-else-if="row.orderStatus == '3'" class="red">支付失败</el-tag>
          <el-tag type="warning" v-else-if="row.orderStatus == '4'">支付中</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间" width="170px"></el-table-column>
      <el-table-column prop="orderLapseTime" label="订单失效时间" width="170px"></el-table-column>
      <el-table-column label="操作" width="150px" fixed="right">
        <template v-slot="{ row }">
          <el-button v-if="row.orderStatus == '0'" type="primary" plain @click="goPay(row)">去支付</el-button>
          <span v-else-if="row.orderStatus == '2'" class="red">已失效</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <el-col :span="24" style="overflow-x: auto" :xs="24">
      <el-pagination
        :current-page="tablePage.currentPage"
        :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="tablePage.totalItems"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-col>
  </div>
</template>

<script>
  import { mapGetters } from 'vuex';
  import { ossPrClient } from '@/api/alibaba';
  import orderPage from '@/api/pendingPayList';
  import { dxSource, pageParamNames } from '@/utils/constants';
  import store from '@/store';
  import { Base64 } from '@/utils/base64';
  export default {
    data() {
      return {
        token: store.getters.token,
        // 分页
        tablePage: {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null
        },
        tableData: [],
        // 查询条件
        dataQuery: {
          studentName: '',
          studentCode: ''
        },
        regTime: '',
        tableLoading: false,
        rules: {
          studentName: [{ min: 0, max: 10, message: '姓名最多输入10个字符', trigger: 'blur' }],
          studentCode: [
            { min: 0, max: 10, message: '编号最多输入10个数字', trigger: 'blur' },
            { pattern: /^\d*$/, message: '请输入数字编号', trigger: 'blur' }
          ]
        }
      };
    },
    computed: {
      ...mapGetters(['setpayUrl'])
    },
    created() {
      ossPrClient();
    },
    mounted() {
      this.fetchData();
    },
    methods: {
      // 分页
      handleSizeChange(val) {
        this.tablePage.size = val;
        this.fetchData();
      },
      handleCurrentChange(val) {
        this.tablePage.currentPage = val;
        this.fetchData();
      },
      //重置
      rest() {
        this.$refs.dataQuery.resetFields();
        this.regTime = '';
        this.tablePage.currentPage = 1;
        this.fetchData();
      },
      // 查询提现列表
      fetchData() {
        const that = this;
        this.$refs['dataQuery'].validate((valid) => {
          if (valid) {
            that.tableLoading = true; 
            var a = that.regTime;
            if (a != null) {
              that.dataQuery.startTime = a[0];
              that.dataQuery.endTime = a[1];
            } else {
              delete that.dataQuery.startTime;
              delete that.dataQuery.endTime;
            }
            console.log(that.roleTag);
            orderPage.conversionList(that.tablePage.currentPage, that.tablePage.size, that.dataQuery).then((res) => {
              that.tableData = res.data.data;
              that.tableLoading = false;
              // 设置后台返回的分页参数
              pageParamNames.forEach((name) => {
                that.$set(that.tablePage, name, parseInt(res.data[name]));
              });
            });
          }
        });
      },
      goPay(row) {
        console.log(row);
        console.log(this.setpayUrl);
        orderPage.goPay(row.id).then((res) => {
          console.log(res.data);

          const split = dxSource.split('##'); //["ZNYY", "BROWSER", "WEB"]
          res.data.dxSource = res.data.registerCallIInfo.appSource + '##' + split[1] + '##' + split[2];
          let params = JSON.stringify(res.data);
          let req = 'token=' + this.token + '&params=' + params + '&back=' + window.location.href;
          //需要编码两遍，避免出现+号等
          var encode = Base64.encode(Base64.encode(req));
          window.open(this.setpayUrl + 'product?' + encode, '_blank');
        });
      }
    }
  };
</script>

<style lang="scss" scoped>
  .container-card {
    padding: 15px 15px 15px 15px;
    margin-bottom: 15px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  }
  .info {
    color: #dcdfe6 !important;
  }
  .image {
    width: 80% !important;
    margin: 0 auto;
    display: block;
  }

  :deep(.center-table td),
  :deep(.center-table th) {
    text-align: center !important;
  }
</style>
