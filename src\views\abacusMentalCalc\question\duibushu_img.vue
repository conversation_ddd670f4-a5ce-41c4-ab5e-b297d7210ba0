<!--对补数数字-->
<template>
  <div class="app-container">
    <el-form :model="form" ref="form" label-width="120px" v-loading="formLoading" :rules="rules">
      <el-form-item label="学段：" prop="grade" required>
        <el-select v-model="form.grade" @change="choseGradeChange()">
          <el-option v-for="(item, index) in gradeList" :key="index" :label="item" :value="item"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="维度：" prop="dimensionId" required>
        <el-select v-model="form.dimensionId" @change="dimensionIdChange()">
          <el-option v-for="(item, index) in gradeDimensionList" :key="index" :label="item.name" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="题型：" prop="questionTypeName" required>
        <el-input v-model="form.questionTypeName" style="width: 200px" disabled />
      </el-form-item>
      <el-form-item label="类型：" prop="type" required>
        <el-select v-model="form.type" placeholder="请选择" @change="typeChange">
          <el-option v-for="item in calculationType" :key="item.code" :value="item.code" :label="item.msg"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="有标题：" required>
        <el-radio-group v-model="form.expandInfo.titleFlag">
          <el-radio :label="false">否</el-radio>
          <el-radio :label="true">是</el-radio>
        </el-radio-group>
        <el-form-item v-show="form.expandInfo.titleFlag">
          <el-input v-model="form.expandInfo.titleInfo" placeholder="输入标题" />
        </el-form-item>
      </el-form-item>
      <el-form-item label="有图片：" required>
        <el-radio-group v-model="form.expandInfo.imgFlag">
          <el-radio :label="false">否</el-radio>
          <el-radio :label="true">是</el-radio>
        </el-radio-group>
        <el-form-item v-show="form.expandInfo.imgFlag">
          <my-upload @handleSuccess="handleSuccess" @handleRemove="handleRemove" :fullUrl="true" :file-list="fileList" :showTip="false" />
          <el-input type="hidden" v-model="form.expandInfo.imgInfo" />
        </el-form-item>
      </el-form-item>
      <el-form-item label="有音频：" required>
        <el-radio-group v-model="form.expandInfo.audioFlag">
          <el-radio :label="false">否</el-radio>
          <el-radio :label="true">是</el-radio>
        </el-radio-group>
        <el-form-item v-show="form.expandInfo.audioFlag">
          <upload-file @handleSuccess="handleAudioSuccess" @handleRemove="handleAudioRemove" :file-list="audioFileList" />
          <el-input type="hidden" v-model="form.expandInfo.audioInfo" />
        </el-form-item>
      </el-form-item>
      <el-form-item label="有视频：" required>
        <el-radio-group v-model="form.expandInfo.videoFlag">
          <el-radio :label="false">否</el-radio>
          <el-radio :label="true">是</el-radio>
        </el-radio-group>
        <el-form-item v-show="form.expandInfo.videoFlag">
          <input ref="inputer" type="file" class="upload" @change="doUpload" />
          <el-input type="hidden" v-model="form.expandInfo.videoInfo" />
        </el-form-item>
      </el-form-item>
      <el-form-item v-if="form.type != 'CALCULATION_ABACUS'" label="题干：" prop="stem" required>
        <el-input v-model="form.stem"></el-input>
      </el-form-item>
      <el-form-item label="题目：" required>
        第一排
        <multiple-upload
          style="margin-bottom: 20px"
          @handleSuccess="handleTopSuccess"
          @handleRemove="handleTopRemove"
          :fullUrl="true"
          :file-list="topPicImg"
          :showTip="false"
          :limit="6"
        />
        <div style="display: flex">
          <div :key="index" v-for="(item, index) in topPicNum" style="margin-bottom: 10px; margin-right: 10px">
            <el-input v-model="item.num" style="width: 145px" />
          </div>
        </div>

        第二排
        <multiple-upload @handleSuccess="handleBottomSuccess" @handleRemove="handleBottomRemove" :fullUrl="true" :file-list="bottomPicImg" :showTip="false" :limit="6" />
        <div style="display: flex">
          <div :key="index" v-for="(item, index) in bottomPicNum" style="margin-bottom: 10px; margin-right: 10px">
            <el-input v-model="item.num" style="width: 145px" />
          </div>
        </div>
      </el-form-item>
      <el-form-item label="分数：" prop="score">
        <el-input-number v-model="form.score" :precision="0" :step="1" :min="0" :max="100"></el-input-number>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="inspectHaveVideo">提交</el-button>
        <el-button @click="resetForm">重置</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
  import Ueditor from '@/components/Ueditor/index.vue';
  import MyUpload from '@/components/Upload/MyUpload.vue';
  import UploadFile from '@/components/Upload/UploadFile.vue';
  import courseQuestionApi from '@/api/abacusMentalCalc/courseQuestion';
  import PlvVideoUpload from '@polyv/vod-upload-js-sdk';
  import MultipleUpload from '@/components/Upload/MultipleUpload.vue';
  import categoryApi from '@/api/abacusMentalCalc/category';

  export default {
    components: {
      MultipleUpload,
      UploadFile,
      Ueditor,
      MyUpload
    },
    data() {
      return {
        videoUpload: null, // 视频上传实例
        userid: '1723c88563', //从点播后台查看获取
        secretkey: 'Jkk4ml1Of8', //从点播后台查看获取
        writeToken: '94064a19-4d28-4ce9-aa05-96d476016215', //从点播后台查看获取

        fileList: [],
        audioFileList: [],

        calculationType: [], //课程类型
        customInfoArr: [[]],

        topPicImg: [],
        bottomPicImg: [],
        topPicNum: [],
        bottomPicNum: [],

        gradeDimensionList: [],
        gradeList: [],

        form: {
          id: null,
          answer: '',
          courseType: null,
          customInfo: '',
          dimension: null,
          dimensionId: null,
          grade: '',
          questionType: '',
          score: null,
          stem: '',
          questionTypeName: '',
          step: '',
          type: null,
          expandInfo: {
            titleFlag: false,
            titleInfo: '',
            imgFlag: false,
            imgInfo: '',
            videoFlag: false,
            videoInfo: '',
            videoName: '',
            audioFlag: false,
            audioInfo: '',
            audioName: '',
            secondFlag: false,
            secondInfo: ''
          }
        },

        formLoading: false,
        rules: {
          stem: [{ required: true, message: '请输入题干', trigger: 'blur' }],
          score: [{ required: true, message: '请输入分数', trigger: 'blur' }]
        }
      };
    },
    mounted() {
      this.autoUpdateUserData(null, this.videoUpload);
    },
    created() {
      this.getCalculationType();
      this.getAllGrade();

      let formData = this.$route.query.formData;
      let _this = this;
      if (formData) {
        for (let key in formData) {
          this.form[key] = formData[key];
        }
        if (formData.id && parseInt(formData.id) !== 0) {
          _this.formLoading = true;
          courseQuestionApi.questionDetail(formData.id).then((re) => {
            _this.form = re.data;
            if (_this.form.expandInfo) {
              if (this.form.expandInfo.imgFlag) {
                this.fileList.push({ url: this.form.expandInfo.imgInfo });
              }
              if (this.form.expandInfo.audioFlag) {
                this.audioFileList.push({
                  name: this.form.expandInfo.audioName,
                  url: this.form.expandInfo.audioInfo
                });
              }
              if (this.form.customInfo) {
                let imgArr = JSON.parse(this.form.customInfo);
                this.topPicNum = imgArr;
                for (let i = 0; i < imgArr.length; i++) {
                  this.topPicImg.push({ url: imgArr[i].url });
                }
              }
              if (this.form.answer) {
                let imgArr = JSON.parse(this.form.answer);
                this.bottomPicNum = imgArr;
                for (let i = 0; i < imgArr.length; i++) {
                  this.bottomPicImg.push({ url: imgArr[i].url });
                }
              }
            }
            this.getDimensionForGrade();
            _this.formLoading = false;
          });
        } else {
          this.getDimensionForGrade();
        }
      }

      this.videoUpload = new PlvVideoUpload({
        region: 'line1', // (可选)上传线路, 默认line1
        events: {
          Error: (err) => {
            // 错误事件回调
            console.log(err);
            let errMag = `（错误代码：${err.code}）${err.message}`;
            this.$alert(errMag, '标题名称', {
              confirmButtonText: '确定',
              type: 'error'
            });
          },
          UploadComplete: () => {
            // 全部上传任务完成回调
            console.info('上传结束：', this.videoUpload);
            // console.log(this.tableData);
            this.$message({
              message: '全部上传任务完成',
              type: 'success'
            });
          }
        }
      });
    },
    methods: {
      typeChange() {
        this.form.stem = '';
      },
      getAllGrade() {
        categoryApi.getGrade().then((res) => {
          this.gradeList = res.data;
        });
      },
      getDimensionForGrade() {
        categoryApi.getDimensionForGrade(this.form.grade, null).then((res) => {
          this.gradeDimensionList = res.data;
        });
      },

      choseGradeChange() {
        if (this.form.dimension) {
          this.form.dimension = null;
          this.form.dimensionId = null;
        }
        this.getDimensionForGrade();
      },
      dimensionIdChange() {
        let result = this.gradeDimensionList.find((item) => {
          return item.id === this.form.dimensionId;
        });
        this.form.dimension = result.name;
      },

      getCalculationType() {
        courseQuestionApi.getCalculationType().then((res) => {
          this.calculationType = res.data;
        });
      },

      judgeHaveNum(list) {
        let isHave = true;
        for (let i = 0; i < list.length; i++) {
          if (list[i].num == '') {
            isHave = false;
          }
        }
        return isHave;
      },
      submitForm() {
        let _this = this;
        this.$refs.form.validate((valid) => {
          if (valid) {
            if (this.topPicImg.length == 0) {
              _this.$message.error('请在第一排传入图片');
              return;
            }
            if (!this.judgeHaveNum(this.topPicNum)) {
              _this.$message.error('请在第一排传入数字');
              return;
            }
            this.form.customInfo = JSON.stringify(this.topPicNum);
            if (this.bottomPicImg.length == 0) {
              _this.$message.error('请在第二排传入图片');
              return;
            }
            if (!this.judgeHaveNum(this.bottomPicNum)) {
              _this.$message.error('请在第二排传入数字');
              return;
            }
            this.form.answer = JSON.stringify(this.bottomPicNum);
            this.formLoading = true;
            if (this.form.id) {
              courseQuestionApi
                .questionEdit(this.form)
                .then((re) => {
                  if (re.success) {
                    _this.$message.success(re.message);
                    _this.$router.push({
                      path: '/abacusMentalCalc/questionIndex'
                    });
                  } else {
                    _this.$message.error(re.message);
                    this.formLoading = false;
                  }
                })
                .catch((e) => {
                  this.formLoading = false;
                });
            } else {
              courseQuestionApi
                .questionAdd(this.form)
                .then((re) => {
                  if (re.success) {
                    _this.$message.success(re.message);
                    _this.$router.push({
                      path: '/abacusMentalCalc/questionIndex'
                    });
                  } else {
                    _this.$message.error(re.message);
                    this.formLoading = false;
                  }
                })
                .catch((e) => {
                  this.formLoading = false;
                });
            }
          } else {
            this.formLoading = false;
            return false;
          }
        });
      },
      resetForm() {
        this.topPicImg = [];
        this.topPicNum = [];
        this.bottomPicImg = [];
        this.bottomPicNum = [];
        this.fileList = [];
        this.audioFileList = [];
        let lastForm = this.form;
        this.$refs['form'].resetFields();
        this.form = {
          id: lastForm.id,
          answer: '',
          courseType: null,
          customInfo: '',
          dimension: lastForm.dimension,
          dimensionId: lastForm.dimensionId,
          grade: lastForm.grade,
          questionType: lastForm.questionType,
          score: null,
          stem: '',
          questionTypeName: lastForm.questionTypeName,
          step: '',
          type: null,
          expandInfo: {
            titleFlag: false,
            titleInfo: '',
            imgFlag: false,
            imgInfo: '',
            videoFlag: false,
            videoInfo: '',
            videoName: '',
            audioFlag: false,
            audioInfo: '',
            audioName: '',
            secondFlag: false,
            secondInfo: ''
          }
        };
      },

      handleAudioSuccess(url, fileName) {
        this.form.expandInfo.audioInfo = url;
        this.form.expandInfo.audioName = fileName;
      },
      handleAudioRemove() {
        this.form.expandInfo.audioInfo = '';
        this.form.expandInfo.audioName = '';
      },
      handleSuccess(url) {
        this.form.expandInfo.imgInfo = url;
      },
      handleRemove() {
        this.form.expandInfo.imgInfo = '';
      },

      handleTopSuccess(url) {
        this.topPicNum.push({ url: url, num: '' });
      },
      handleTopRemove(data) {
        this.removePicNum(data.url, this.topPicNum);
      },
      handleBottomSuccess(url) {
        this.bottomPicNum.push({ url: url, num: '' });
      },
      handleBottomRemove(data) {
        this.removePicNum(data.url, this.bottomPicNum);
      },

      removePicNum(url, arr) {
        for (let i = 0; i < arr.length; i++) {
          if (arr[i].url === url) {
            arr.splice(i, 1);
            break;
          }
        }
      },

      inspectHaveVideo() {
        let inputDOM = this.$refs.inputer; // 通过DOM取文件数据
        let data = Object.values(inputDOM.files);
        if (data.length <= 0) {
          this.submitForm();
        } else {
          if (this.videoUpload) {
            this.videoUpload.startAll();
          }
        }
      },
      autoUpdateUserData(timer, videoUpload) {
        // 启动获取用户信息
        this.getSign();
        if (timer) {
          clearTimeout(timer);
          timer = null;
        }
        timer = setTimeout(() => {
          this.autoUpdateUserData(timer, videoUpload);
        }, 3 * 50 * 1000);
      },
      async getSign() {
        const { data } = await courseQuestionApi.getSign();
        let userData = {
          userid: '1723c88563', // Polyv云点播账号的ID
          ptime: data.ptime, // 时间戳
          sign: data.sign, // 是根据将secretkey和ts按照顺序拼凑起来的字符串进行MD5计算得到的值
          hash: data.hash //
        };
        this.videoUpload.updateUserData(userData);
      },
      doUpload() {
        // 选择文件
        let inputDOM = this.$refs.inputer; // 通过DOM取文件数据
        let data = Object.values(inputDOM.files);
        if (data.length > 0) {
          data.forEach((file, index, arr) => {
            let fileSetting = {
              // 文件上传相关信息设置
              title: file.name, // 标题
              desc: 'jssdk插件上传', // 描述
              cataid: process.env.VUE_APP_BASE_CATAID, // 上传分类目录ID
              tag: '', // 标签
              luping: 0, // 是否开启视频课件优化处理，对于上传录屏类视频清晰度有所优化：0为不开启，1为开启
              keepsource: 0, // 是否源文件播放（不对视频进行编码）：0为编码，1为不编码
              state: '' //用户自定义信息，如果提交了该字段，会在服务端上传完成回调时透传返回。
            };
            let uploadManager = this.videoUpload.addFile(
              file, // file 为待上传的文件对象
              {
                FileStarted: this.onFileStarted, // 文件开始上传回调
                FileProgress: this.onFileProgress, // 文件上传中回调
                FileSucceed: this.onFileSucceed, // 文件上传成功回调
                FileFailed: this.onFileFailed, // 文件上传失败回调
                FileStopped: this.onFileStopped // 文件上传停止回调
              },
              fileSetting
            );
            console.log(uploadManager);
          });
        }
      },
      onFileStarted(data) {
        console.log('文件上传开始: ', data);
        this.loading = this.$loading({
          lock: true,
          text: '视频上传中...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
      },
      onFileProgress(data) {
        console.log('文件上传中: ', data);
      },
      onFileSucceed(data) {
        if (this.loading) {
          this.loading.close();
        }
        console.log('文件上传成功: ', data);
        this.form.expandInfo.videoInfo = data.fileData.vid;
        this.form.expandInfo.videoName = data.fileData.filename;
        this.submitForm();
      },
      onFileFailed(data) {
        if (this.loading) {
          this.loading.close();
        }
        console.log('文件上传失败: ', data);
      },
      onFileStopped(data) {
        if (this.loading) {
          this.loading.close();
        }
        console.log('文件上传停止: ', data);
      }
    }
  };
</script>

<style lang="less" scoped>
  .question-item-label {
    display: flex;
    margin-bottom: 12px;
  }
</style>
