import request from '@/utils/request'
//枚举类型
export default {
 // 获取枚举类型
 getEnumerationAggregation(enType) {
    return request({
      url: '/znyy/bvstatus/'+enType,
      method: 'GET'
    })
  },

  getEnumerationcheckList() {
    return request({
      url: '/znyy/course/edition/check/list',
      method: 'GET'
    })
  },
  getPurchasedProduct(enType){
    return request({
      url: '/znyy/bvstatus/purchasedProduct/'+enType,
      method: 'GET'
    })
  }
}
