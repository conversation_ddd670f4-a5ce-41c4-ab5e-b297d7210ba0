import request from '@/utils/request';
export default {
  // 培训中心2.0接口
  // 课程详情-考试详情
  examDetail(data) {
    return request({
      url: '/train/web/exam/training/exam_record_detail',
      method: 'GET',
      params: data
    });
  },

  //之前的
  // 分页查询
  questionList(data) {
    return request({
      url: '/train/web/exam/question/page',
      method: 'GET',
      params: data
    });
  },
  // 新增/编辑考题
  questionCreateOrUpdate(data) {
    return request({
      url: '/train/web/exam/question/createOrUpdate',
      method: 'POST',
      data
    });
  },
  // 考题详情
  questionDetail(data) {
    return request({
      url: '/train/web/exam/question/detail',
      method: 'GET',
      params: data
    });
  },
  // /train/web/exam/question/delete
  //删除考题
  questionDelete(data) {
    return request({
      url: '/train/web/exam/question/delete',
      method: 'POST',
      params: data
    });
  },
  // /train/web/exam/paper/create
  // 新增/编辑试卷
  paperCreate(data) {
    return request({
      url: '/train/web/exam/paper/create',
      method: 'POST',
      data
    });
  },
  // 试卷详情
  paperDetail(data) {
    return request({
      url: '/train​/web​/exam​/paper​/detail',
      method: 'GET',
      params: data
    });
  },
  // /train/web/exam/training/handPaper
  // 试卷提交
  trainingHandPaper(data) {
    data.roleTag = window.localStorage.getItem('roleTag');
    return request({
      url: '/train/web/exam/training/handPaper',
      method: 'POST',
      data
    });
  },
  // /train/web/exam/training/paper
  // 获取考试试卷
  trainingPaper(data) {
    data.roleTag = window.localStorage.getItem('roleTag');
    return request({
      url: '/train/web/exam/training/paper',
      method: 'GET',
      params: data
    });
  },
  //试卷查询分页 /train/web/exam/paper/page
  paperPage(data) {
    return request({
      url: '/train/web/exam/paper/page',
      method: 'GET',
      params: data
    });
  },
  //课程详情 paper/web/exam/paper/detail
  trainingDetail(data) {
    return request({
      url: '/train/web/exam/paper/detail',
      method: 'GET',
      params: data
    });
  },
  // 删除试卷/
  paperDelete(data) {
    return request({
      url: '/train/web/exam/paper/delete',
      method: 'POST',
      params: data
    });
  },

  // 导出考题模板
  importTemplate(data) {
    return request({
      url: '/train/web/exam/question/importTemplate',
      method: 'GET',
      params: data,
      responseType: 'blob'
    });
  },
  // 批量导入考题
  importQuestion(data) {
    return request({
      url: '/train/web/exam/question/importQuestion',
      method: 'POST',
      data
    });
  },
  // 考试记录分页
  courseExamRecordPage(data) {
    return request({
      url: '/train/web/exam/training/course_exam_record_page',
      method: 'POST',
      data
    });
  },
  // 考试-试卷详情
  examRecordDetail(data) {
    return request({
      url: '/train/web/exam/training/exam_record_detail',
      method: 'GET',
      params: data
    });
  },
  // 上传课件并获取文件链接
  uploadCourseFile(data) {
    return request({
      url: '/train/web/exam/course/uploadCourseFile',
      method: 'POST',
      data
    });
  }
};
