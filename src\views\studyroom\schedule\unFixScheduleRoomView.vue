<template>
  <div class="app-container">
    <el-col :span="24" style="margin-bottom: 30px">
      <el-button type="warning" icon="el-icon-back" size="mini" @click="goBack()">返回</el-button>
    </el-col>

    <el-table class="common-table" v-loading="tableLoading" :data="tableData" style="width: 100%;margin-bottom: 20px;" row-key="id" border
              default-expand-all >
      <el-table-column prop="roomId" label="房间ID"></el-table-column>
      <el-table-column prop="roomName" label="房间名称" ></el-table-column>
      <el-table-column label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="mini">
            <router-link :to="'/schedule/trtcRoom/'+scope.row.roomId">进入房间</router-link>
          </el-button>
        </template>
      </el-table-column>
      <el-table-column prop="courseType" label="课程分类">
        <template slot-scope="scope">
          <span v-if="scope.row.courseType==1">系统课</span>
          <span v-else>非系统课</span>
        </template>
      </el-table-column>
      <el-table-column prop="weeks" label="日期"></el-table-column>
      <el-table-column label="房间人数">
        <template slot-scope="scope">
          {{scope.row.currentCount}}/{{scope.row.maxUserCount}}
        </template>
      </el-table-column>
      <el-table-column prop="times" label="规划时间"></el-table-column>
      <el-table-column label="剩余时间" >
        <template slot-scope="scope">
          <span v-if="scope.row.minute ==='-1'">
             --
          </span>
          <span v-else>{{scope.row.minute}}分钟</span>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import studyScheduleApi from "@/api/studyroom/studySchedule";
import {pageParamNames} from "@/utils/constants";
import committeemanApi from "@/api/studyroom/committeeman";

export default {
  name:'Schedule',
  data() {
    return {
      tableLoading: false,
      loading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      open:false,
      tableData: [],
      scheduleId:null,
    };
  },
  created() {
    if (this.$route.params && this.$route.params.scheduleId) {
      this.scheduleId = this.$route.params.scheduleId;
      this.getUnFixScheduleRooms();
    }

  },
  methods: {
    goBack(){
      this.$store.dispatch('delVisitedViews', this.$route);
      this.$router.push("/schedule/unFixSchedule");
    },

    getUnFixScheduleRooms(){
      this.tableLoading = true;
      studyScheduleApi.getUnFixScheduleViewRooms(this.scheduleId).then(res=>{
        this.tableData = res.data;
        this.tableLoading = false;
      })
    }
  }
};
</script>

<style>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}
.period-table td,
.period-table th{
  text-align: center;
}
@media (max-width:767px) {
  .el-message-box{
    width: 80%!important;
  }
}
.el-table .success-row {
  background: #e8f4ff;
}
</style>
