<!-- 商户管理-合同管理 -->
<template>
  <div class="app-container">
    <el-form :inline="true" class="container-card">
      <el-form-item label="合同状态：">
        <el-select v-model="searchNum.signState" placeholder="请选择合同签署状态" clearable>
          <el-option v-for="item in signstateOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="签署方名称：">
        <el-input v-model="searchNum.signPartyName" placeholder="请输入签署方名称" clearable />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="search()">搜索</el-button>
        <el-button type="primary" @click="reset()">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 渲染表格 -->
    <el-table
      v-loading="tableLoading"
      class="common-table"
      :data="tableData"
      :header-cell-style="{ background: '#eef1f6', color: '#606266' }"
      :row-style="{ height: '43px' }"
      :span-method="spanMethod"
      border
    >
      <el-table-column prop="index" label="序号" width="50"></el-table-column>
      <el-table-column prop="contractName" label="合同名称" align="center" />
      <el-table-column prop="merchantName" label="签署方名称" align="center" />
      <el-table-column prop="roleTagName" label="签署方角色" align="center" />
      <el-table-column prop="signStatus" label="签署状态" align="center">
        <template slot-scope="{ row }">
          <div :style="{ color: row.signStatus == '2' ? '#339933' : '#f56c6c' }">
            {{ commonFormat(signStatusOptions, row.signStatus, '未签署') }}
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="approveTime" label="签署时间" align="center" width="180px" />
      <el-table-column prop="signState" label="合同状态" align="center" width="180px">
        <template slot-scope="{ row }">
          <div :style="{ color: [3, 4, 5, 6].includes(row.signState) ? '#f56c6c' : '#339933' }">
            {{ commonFormat(signstateOptions, row.signState, '签署中') }}
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="approveDeadline" label="签订截止时间" align="center" width="180px" />
      <el-table-column prop="createTime" label="创建时间" align="center" width="180px" />
      <el-table-column label="操作" align="center" width="320px">
        <template slot-scope="{ row }">
          <el-button v-if="row.signStatus == '1'" type="primary" @click="handleSignClick(row)">合同签署</el-button>
        </template>
      </el-table-column>
    </el-table>

    <div style="text-align: center; margin-top: 45px">
      <el-pagination
        :current-page="pagination.pageNum"
        :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <el-dialog :visible.sync="contractSigningDialogVisible" width="20%" center>
      <div slot="title" class="contract-dialog-title">
        {{ contractSigningData.qrMessage }}
      </div>
      <div class="contract-dialog-content">
        <div class="contract-dialog-content-qrcode" v-loading="contractQRLoading" @click="handleContractQRClick()">
          <el-image style="width: 100%; height: 100%" fit="fill" :src="contractSigningData.qrCodeUrl" />
        </div>
        <el-button class="contract-dialog-content-btn" type="primary" @click="handleSignSuccessClick">已签署</el-button>
        <div>签署完成后，请点击该按钮</div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import { getContractList, getContractDetail, getContractSigingStatus } from '@/api/contractManagement';
  export default {
    name: 'ContractManagement',
    data() {
      return {
        //搜索参数
        searchNum: {},
        realSearchNum: {},
        searchTimestamp: 0,

        // 签署状态
        signStatusOptions: [
          { value: '1', label: '未签署' },
          { value: '2', label: '已签署' },
          { value: '3', label: '签署失败' },
          { value: '4', label: '已过期' },
          { value: '5', label: '已撤销' },
          { value: '6', label: '已作废' }
        ],

        // 合同状态
        signstateOptions: [
          { value: '1', label: '签署中' },
          { value: '2', label: '已完成' },
          { value: '3', label: '签署失败' },
          { value: '4', label: '已过期' },
          { value: '5', label: '已撤销' },
          { value: '6', label: '已作废' }
        ],

        // 表格参数
        tableLoading: false,
        tableData: [],

        // 分页参数
        pagination: {
          pageNum: 1,
          pageSize: 10,
          total: 0
        },

        // 合同签署弹窗参数
        contractSigningDialogVisible: false,
        contractQRLoading: false,
        contractSigningData: {
          qrMessage: '',
          qrCodeUrl: ''
        }
      };
    },
    mounted() {
      this.getTableList();
    },
    methods: {
      spanMethod({ row, columnIndex }) {
        if (row.spanInfo.needColSpan.includes(columnIndex)) {
          return row.spanInfo.colSpan;
        }
      },
      search() {
        this.getTableList();
      },
      reset() {
        this.pagination.pageNum = 1;
        this.searchNum = {};
        this.getTableList();
      },
      // 更改每页条数
      handleSizeChange(val) {
        this.pagination.pageSize = val;
        this.getTableList(false);
      },
      //更改当前页
      handleCurrentChange(val) {
        this.pagination.pageNum = val;
        this.getTableList(false);
      },
      getTableList(updateSearch = true) {
        this.tableLoading = true;
        if (updateSearch) {
          this.realSearchNum = JSON.parse(JSON.stringify(this.searchNum));
        } else {
          this.searchNum = JSON.parse(JSON.stringify(this.realSearchNum));
        }
        const searchTimestamp = Date.now();
        this.searchTimestamp = searchTimestamp;
        getContractList({ pageNum: this.pagination.pageNum, pageSize: this.pagination.pageSize, ...this.realSearchNum })
          .then((res) => {
            if (this.searchTimestamp !== searchTimestamp) return;
            const listResult = [];
            if (Array.isArray(res.data.data)) {
              const needColSpan = [0, 1, 6, 7, 8, 9];
              res.data.data.forEach((item, i) => {
                const index = ++i;
                const firstItem = {
                  ...item,
                  index,
                  spanInfo: { needColSpan, colSpan: [2, 1] }
                };
                const secondItem = {
                  signFlowId: item.signFlowId,
                  merchantCode: item.secondMerchantCode,
                  merchantName: item.secondMerchantName,
                  roleTag: item.secondRoleTag,
                  roleTagName: item.secondRoleTagName,
                  signStatus: item.secondSignStatus,
                  approveTime: item.secondApproveTime,
                  createTime: item.createTime,
                  spanInfo: { needColSpan, colSpan: [0, 0] }
                };
                listResult.push(firstItem, secondItem);
              });
            }
            this.tableData = listResult;
            this.pagination.total = Number(res.data.totalItems);
            this.tableLoading = false;
          })
          .catch(() => {
            if (this.searchTimestamp !== searchTimestamp) return;
            this.tableData = [];
            this.tableLoading = false;
          });
      },
      handleSignClick(row) {
        const params = {
          flowId: row.signFlowId,
          templateType: row.templateType,
          signSource: row.signSource,
          participantFlag: row.participantFlag,
          isQrLink: 1
        };
        this.contractQRLoading = true;
        getContractDetail(params)
          .then((res) => {
            this.contractSigningData = {
              flowId: row.signFlowId,
              templateType: row.templateType,
              signSource: row.signSource,
              participantFlag: row.participantFlag,
              qrMessage: res.data.qrMessage,
              qrCodeUrl: row.contractName == '俱乐部权益转让合同' && row.participantFlag == '乙方' ? res.data.orgQrUrl : res.data.qrUrl
            };
            this.contractSigningDialogVisible = true;
            this.contractQRLoading = false;
          })
          .catch(() => (this.contractQRLoading = false));
      },
      handleSignSuccessClick() {
        const params = {
          flowId: this.contractSigningData.flowId,
          signSource: this.contractSigningData.signSource,
          participantFlag: this.contractSigningData.participantFlag
        };
        getContractSigingStatus(params).then((res) => {
          if (res.data?.signStatus == 2) {
            this.$message.success('签署成功！');
            this.getTableList(false);
            this.contractSigningDialogVisible = false;
          } else {
            this.$message.error('合同暂未签署');
          }
        });
      },
      /**
       * 处理合同二维码点击刷新
       */
      handleContractQRClick() {
        if (!this.contractSigningData.qrCodeUrl) {
          const params = {
            flowId: this.contractSigningData.flowId,
            templateType: this.contractSigningData.templateType,
            signSource: this.contractSigningData.signSource,
            participantFlag: this.contractSigningData.participantFlag,
            isQrLink: 1
          };
          this.contractQRLoading = true;
          getContractDetail(params)
            .then((res) => {
              this.contractSigningData = {
                flowId: row.signFlowId,
                templateType: row.templateType,
                signSource: row.signSource,
                participantFlag: row.participantFlag,
                qrMessage: res.data.qrMessage,
                qrCodeUrl: res.data.qrUrl
              };
              this.contractQRLoading = false;
            })
            .catch(() => (this.contractQRLoading = false));
        }
      },
      commonFormat(array, key, placeholder = '无') {
        for (let item of array) {
          if (item.value == key) {
            return item.label;
          }
        }
        return placeholder;
      }
    }
  };
</script>

<style lang="scss" scoped>
  .container-card {
    padding: 15px 15px 15px 15px;
    margin-bottom: 15px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  }
  .contract-dialog-title {
    line-height: 30px;
    white-space: break-spaces;
  }
  .contract-dialog-content {
    text-align: center;
    .contract-dialog-content-qrcode {
      width: 270px;
      height: 270px;
      background-color: #ccc;
      margin: 0 auto;
    }
    .contract-dialog-content-btn {
      margin: 24px auto 16px;
    }
  }
</style>
