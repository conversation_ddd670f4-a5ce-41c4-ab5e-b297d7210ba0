<template>
  <div class="app-container">
    <!-- 搜索 -->
    <el-form ref="form" :inline="true" class="SearchForm">
      <el-row style="padding: 20px 20px 0 20px">
        <el-col :span="5">
          <el-form-item label="所属课程:">
            <router-link :to="{ path: 'courseList' }" class="blue lh36">{{ courseName }}</router-link>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="英文:">
            <el-input placeholder="请输入英文:" clearable v-model="dataQuery.word" @keyup.enter.native="fetchData()"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="中文:">
            <el-input placeholder="请输入中文:" clearable v-model="dataQuery.translation" @keyup.enter.native="fetchData()"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="3" style="text-align: right">
          <el-button type="warning" icon="el-icon-search" @click="fetchData01()">搜索</el-button>
        </el-col>
      </el-row>
    </el-form>
    <div class="SearchForm">
      <!-- 添加 -->
      <div class="btn-add" style="margin-bottom: 20px">
        <el-button size="small" type="success" @click="goBack()">返回课程列表</el-button>
        <el-button size="small" type="primary" icon="el-icon-plus" @click="clickAdd">添加</el-button>
        <el-button size="small" type="danger" icon="el-icon-close" @click="batchDeletion">批量删除</el-button>
        <el-button size="small" type="success" icon="el-icon-edit-outline" @click="openWord">更新单词本</el-button>
      </div>
      <!-- 表格 -->
      <el-table
        class="common-table"
        :data="tableData"
        stripe
        border
        :default-sort="{ prop: 'addTime', order: 'descending' }"
        @selection-change="handleSelectionChange"
        v-loading="tableLoading"
      >
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column prop="courseCode" label="课程编号" sortable width="150px"></el-table-column>
        <el-table-column prop="word" label="英语" sortable></el-table-column>
        <el-table-column prop="id" label="操作" sortable width="300">
          <template slot-scope="scope">
            <el-button type="success" size="mini" icon="el-icon-edit-outline" @click="openEdit(scope.row.id)">编辑</el-button>
            <el-button type="danger" size="mini" icon="el-icon-delete" @click="deleteWord(scope.row.id)">删除</el-button>
          </template>
        </el-table-column>
        <el-table-column prop="translation" label="中文" sortable></el-table-column>
        <el-table-column prop="isEnable" label="状态" sortable>
          <template slot-scope="scope">
            <span class="green" v-if="scope.row.isEnable === 1">开通</span>
            <span class="red" v-else>暂停</span>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <el-col :span="20">
      <el-pagination
        :current-page="tablePage.currentPage"
        :page-sizes="[10, 50, 100, 150, 200]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="tablePage.totalItems"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-col>

    <!-- 添加弹窗 -->
    <el-dialog title="添加单词" :visible.sync="dialogVisible" width="70%" :close-on-click-modal="false" @close="close">
      <el-form ref="addCourseWord" :rules="rules" :model="addCourseWord" label-position="left" label-width="120px" style="width: 100%">
        <el-form-item label="题目类型" prop="isEnable">
          <template>
            <el-radio v-model="radio" label="0" @change="change(radio)">单词</el-radio>
          </template>
        </el-form-item>
        <el-form-item label="课程编号" prop="courseCode" v-show="false">
          <template>
            <el-input type="textarea" resize="none" :rows="4" v-model="addCourseWord.courseCode" />
          </template>
        </el-form-item>
        <el-form-item label="方式一">
          <span>页面批量上传</span>
        </el-form-item>
        <el-form-item label="单词词库">
          <el-col :xs="24" :span="12">
            <el-input type="textarea" resize="none" :rows="4" v-model="addCourseWord.word" />
          </el-col>
        </el-form-item>
        <el-form-item label="方式二">
          <el-row>
            <el-col :xs="24" :span="6">
              <span>模板批量上传</span>
            </el-col>
            <el-col :xs="24" :span="6" :offset="12">
              <el-link type="success" href="http://dx-applet-mall.oss-cn-shanghai.aliyuncs.com/manage/17cef6426c5c43e58961fce56eb24e98.xlsx">模板下载</el-link>
            </el-col>
          </el-row>
        </el-form-item>
        <el-form-item label="模板上传" v-if="isRouterAlive">
          <el-upload
            class="upload-demo"
            action="https://jsonplaceholder.typicode.com/posts/"
            :on-change="beforeUplpad01"
            multiple
            :limit="1"
            :on-exceed="handleExceed"
            :on-remove="handleRemoveDetail"
            :auto-upload="false"
            name="flie"
            v-if="showUpload"
            ref="uploadRef"
          >
            <div class="el-upload__text">
              <el-button size="small" type="primary" style v-if="showUpload">点击上传</el-button>
            </div>
            <!-- <div slot="tip" class="el-upload__tip">只能上传execel文件</div> -->
          </el-upload>
          <div class="el-upload__text" v-if="!showUpload">
            <el-button size="small" type="primary" style @click="openDialog">点击上传</el-button>
          </div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button v-if="addOrUpdate" size="mini" type="primary" @click="addActiveFun('addCourseWord')">新增</el-button>
        <el-button size="mini" @click="close">关闭</el-button>
      </div>
    </el-dialog>

    <!-- 更新单词本弹窗 -->
    <el-dialog title="更新单词本" :visible.sync="showWordBook" width="60%" :close-on-click-modal="false" @close="close">
      <el-form
        :ref="addOrUpdate ? 'updateCourseWordBook' : 'updateCourseWord'"
        :rules="rulesWordBook"
        :model="addOrUpdate ? updateCourseWordBook : updateCourseWord"
        label-position="left"
        label-width="80px"
        style="width: 100%"
      >
        <el-form-item label="课程编号" prop="courseCode">
          <el-col :xs="24" :span="12">
            <el-input :disabled="true" v-model="updateCourseWordBook.courseCode"></el-input>
          </el-col>
        </el-form-item>
        <el-form-item label="原单词" prop="oldWord">
          <el-col :xs="24" :span="12">
            <el-input v-model="updateCourseWordBook.oldWord"></el-input>
          </el-col>
        </el-form-item>
        <el-form-item label="原翻译" prop="oldTranslation">
          <el-col :xs="24" :span="12">
            <el-input v-model="updateCourseWordBook.oldTranslation"></el-input>
          </el-col>
        </el-form-item>
        <el-form-item label="新单词" prop="newWord">
          <el-col :xs="24" :span="12">
            <el-input v-model="updateCourseWordBook.newWord"></el-input>
          </el-col>
        </el-form-item>
        <el-form-item label="新翻译" prop="newTranslation">
          <el-col :xs="24" :span="12">
            <el-input v-model="updateCourseWordBook.newTranslation"></el-input>
          </el-col>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="updateWordBook('updateCourseWordBook')">确定</el-button>
        <el-button size="mini" @click="closeWordBook">关闭</el-button>
      </div>
    </el-dialog>

    <!-- 编辑弹窗 -->
    <el-dialog title="编辑单词" :visible.sync="showEdit" width="60%" :close-on-click-modal="false" @close="close">
      <el-form :ref="'updateSingleCourseWord'" :rules="updateSingle" :model="updateSingleCourseWord" label-position="left" label-width="80px" style="width: 100%">
        <el-form-item label="id" prop="id" v-show="false">
          <el-col :xs="24" :span="12">
            <el-input v-model="updateSingleCourseWord.id"></el-input>
          </el-col>
        </el-form-item>
        <el-form-item label="课程编号" prop="courseCode" v-show="false">
          <el-col :xs="24" :span="12">
            <el-input v-model="updateSingleCourseWord.courseCode"></el-input>
          </el-col>
        </el-form-item>
        <el-form-item label="英文" prop="word">
          <el-col :xs="24" :span="12">
            <el-input v-model="updateSingleCourseWord.word"></el-input>
          </el-col>
        </el-form-item>
        <el-form-item label="中文" prop="translation">
          <el-col :xs="24" :span="12">
            <el-input v-model="updateSingleCourseWord.translation"></el-input>
          </el-col>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="editWord('updateSingleCourseWord')">确定</el-button>
        <el-button size="mini" @click="closeEdit">关闭</el-button>
      </div>
    </el-dialog>

    <!-- 上传规则弹窗 -->
    <el-dialog title="上传规则说明" :visible.sync="dialogVisibleRule" width="40%" center :close-on-click-modal="false">
      <div style="line-height: 25px">
        1、第一列里面不能有英文格式的下引号’，需要中文格式的下引号 ’。
        <br />
        2、第一列不允许有中文，不允许出现特殊符号，特殊符号有/ （） ， . ? ＄ + n. cn. un. v. “” adj. adv. 等。
        <br />
        3、第一列不允许有重复项，比如第一行有dog这个单词，第十行也出现dog这个词，会导致上传不成功。
        <br />
        4、第一列不要缩写，比如something不要写成sth, somebody不要写成sb。
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisibleRule = false">取消</el-button>
        <el-button type="primary" @click="confirmUpload">确认</el-button>
      </span>
    </el-dialog>

    <!-- 上传失败弹窗 -->
    <el-dialog title="文件上传格式失败" :visible.sync="dialogVisibleError" width="40%" center :close-on-click-modal="false">
      <h4 style="line-height: 25px">
        共发现
        <span style="color: red">{{ addErrorCourseWord.length }}</span>
        处错误
      </h4>
      <div style="height: 400px; width: 100%; overflow-y: auto; color: red">
        <p v-for="(item, index) in addErrorCourseWord" :key="index">
          {{ item.errorMsg }}
        </p>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="confirmUploadError">重新上传</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
  import courseWordApi from '@/api/courseMake';
  import { pageParamNames } from '@/utils/constants';
  import fa from 'element-ui/src/locale/lang/fa';
  export default {
    data() {
      return {
        categoryType: [], // 所属分类
        // 分页
        tablePage: {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null
        },
        tableLoading: false,
        dataQuery: {
          translation: '',
          word: '',
          courseCode: ''
        },
        isRouterAlive: true, //局部刷新
        activeType: [], // 活动类型
        tableData: [], //表格数据
        dialogVisible: false, // 修改弹窗是否展示
        showWordBook: false, //展示更新单词本弹窗
        updateCourseWordBook: {}, //更新单词本集合
        updateSingleCourseWord: {}, //更新单词集合
        showEdit: false, //编辑弹窗
        rows: [], //选中的数据id存放处
        addOrUpdate: true, // 是新增还是修改
        addCourseWord: {}, // 新增单词
        addErrorCourseWord: [], // 新增有问题的单词
        updateCourseWord: {}, // 修改数据
        rules: {},
        radio: '0', //单选框状态 值必须是字符串
        memberId: undefined,
        courseName: '',
        courseCode: '',
        fd: '',
        dialogVisibleRule: false, // 控制上传规则弹窗
        dialogVisibleError: false,
        showUpload: false, // 控制上传组件的显示
        updateSingle: {
          word: [
            {
              required: true,
              message: '必填',
              trigger: 'blur'
            }
          ],
          translation: [
            {
              required: true,
              message: '必填',
              trigger: 'blur'
            }
          ]
        },

        rulesWordBook: {
          oldWord: [
            {
              required: true,
              message: '必填',
              trigger: 'blur'
            }
          ],
          oldTranslation: [
            {
              required: true,
              message: '必填',
              trigger: 'blur'
            }
          ],
          newWord: [
            {
              required: true,
              message: '必填',
              trigger: 'blur'
            }
          ],
          newTranslation: [
            {
              required: true,
              message: '必填',
              trigger: 'blur'
            }
          ]
        }
      };
    },
    created() {
      this.fetchData();
      // 获取所属分类
      this.getCategoryType();
    },
    methods: {
      // 获取分类返回类型
      getCategoryType() {
        courseWordApi.categoryType().then((res) => {
          this.categoryType = res.data;
        });
      },
      fetchData01() {
        this.tablePage = {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null
        };
        this.fetchData();
      },
      // 查询表格列表
      fetchData() {
        const that = this;
        that.tableLoading = true;
        window.localStorage.getItem('courseContentTypeWord');
        that.courseName = window.localStorage.getItem('courseNameWord');
        that.courseCode = window.localStorage.getItem('courseCodeWord');
        that.addCourseWord.courseCode = window.localStorage.getItem('courseCodeWord');
        that.dataQuery.courseCode = window.localStorage.getItem('courseCodeWord');
        console.log(that.updateCourseWordBook.courseCode);
        courseWordApi.courseWordList(that.tablePage.currentPage, that.tablePage.size, that.dataQuery).then((res) => {
          that.tableData = res.data.data;
          that.tableLoading = false;
          // 设置后台返回的分页参数
          pageParamNames.forEach((name) => that.$set(that.tablePage, name, parseInt(res.data[name])));
        });
      },
      //局部刷新
      reload() {
        this.isRouterAlive = false;
        this.$nextTick(function () {
          this.isRouterAlive = true;
        });
      },
      //添加操作
      clickAdd() {
        this.addCourseWord = {
          word: '',
          courseCode: this.$route.query.courseCode
        };
        this.reload();
        this.fd = '';
        this.dialogVisible = true;
        this.addOrUpdate = true;
        this.$nextTick(() => this.$refs['addCourseWord'].clearValidate());
      },
      // 打开更新单词本
      openWord() {
        this.updateCourseWordBook = {};
        this.updateCourseWordBook.courseCode = this.$route.query.courseCode;
        this.showWordBook = true;
      },
      //删除操作单词
      deleteWord(id) {
        this.$confirm('确定操作吗?', '删除单词', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            courseWordApi
              .deleteCourseWord(id)
              .then((res) => {
                this.$nextTick(() => this.fetchData());
                this.$message.success('删除单词成功!');
              })
              .catch((err) => {});
          })
          .catch((err) => {});
      },

      //关闭单词本
      closeWordBook() {
        this.showWordBook = false;
      },
      //更新单词本
      updateWordBook(ele) {
        const that = this;
        that.$refs[ele].validate((valid) => {
          // 表单验证
          if (valid) {
            const loading = this.$loading({
              lock: true,
              text: '更新单词本',
              spinner: 'el-icon-loading',
              background: 'rgba(0, 0, 0, 0.7)'
            });
            courseWordApi
              .updateWordBook(that.updateCourseWordBook)
              .then((res) => {
                that.showWordBook = false;
                loading.close();
                that.$nextTick(() => that.fetchData());
                that.$message.success('更新单词本成功');
              })
              .catch((err) => {
                loading.close();
              });
          } else {
            console.log('error submit!!');
            return false;
          }
        });
      },
      // 打开编辑单词
      openEdit(id) {
        courseWordApi.queryActive(id).then((res) => {
          this.updateSingleCourseWord = res.data;
        });
        this.showEdit = true;
      },
      closeEdit() {
        this.showEdit = false;
      },
      //编辑单个单词
      editWord(ele) {
        const that = this;
        that.$refs[ele].validate((valid) => {
          // 表单验证
          if (valid) {
            const loading = this.$loading({
              lock: true,
              text: '编辑单词提交',
              spinner: 'el-icon-loading',
              background: 'rgba(0, 0, 0, 0.7)'
            });
            courseWordApi
              .singleWordEditing(that.updateSingleCourseWord)
              .then(() => {
                that.showEdit = false;
                loading.close();
                that.$nextTick(() => that.fetchData());
                that.$message.success('修改单词成功');
              })
              .catch((err) => {
                // 关闭提示弹框
                loading.close();
              });
          } else {
            console.log('error submit!!');
            // loading.close();
            return false;
          }
        });
      },
      // 单词提交
      addActiveFun(ele) {
        if(this.fd == '' && this.addCourseWord.word == ''){
          this.$message.warning('请先上传单词');
          return;
        }
        const that = this;
        that.$refs[ele].validate((valid) => {
          // 表单验证
          if (valid) {
            const loading = this.$loading({
              lock: true,
              text: '新增单词',
              spinner: 'el-icon-loading',
              background: 'rgba(0, 0, 0, 0.7)'
            });
            //上传execel文件
            let file = new FormData();
            if (that.fd != '') {
              file.append('file', that.fd);
            }
            courseWordApi
              .addCourseWordList(file, that.addCourseWord)
              .then((res) => {
                if (res.data.success) {
                  that.dialogVisible = false;
                  loading.close();
                  that.$nextTick(() => that.fetchData());
                  that.$message.success(res.data.data);
                  that.dialogVisible = false;
                } else {
                  loading.close();
                  this.$message.warning('上传失败');
                  that.addErrorCourseWord = res.data.data;
                  that.dialogVisibleError = true;
                }
              })
              .catch((err) => {
                loading.close();
                this.$message.warning('上传失败');
              });
          } else {
            this.showUpload = false;
            console.log('error submit!!');
            return false;
          }
        });
      },
      // 点击编辑按钮
      handleUpdate(id) {
        const that = this;
        that.dialogVisible = true;
        that.addOrUpdate = false;
        courseWordApi
          .queryActive(id)
          .then((res) => {
            that.updateCourseWord = res.data;
            console.log(that.updateCourseWord);
            that.radio = that.updateCourseWord.isEnable.toString(); //状态回显
          })
          .catch((err) => {});
      },
      // 状态改变事件
      change(radio) {
        if (radio == '1') {
          this.addCourseWord.isEnable = 1;
        } else {
          this.addCourseWord.isEnable = 0;
        }
      },
      // 修改课程提交
      updateCourseWordFun(ele) {
        const that = this;
        that.$refs[ele].validate((valid) => {
          // 表单验证
          if (valid) {
            that.updateCourseWord.isEnable = that.radio;
            console.log(that.updateCourseWord);
            const loading = this.$loading({
              lock: true,
              text: '修改课程信息提交',
              spinner: 'el-icon-loading',
              background: 'rgba(0, 0, 0, 0.7)'
            });
            courseWordApi
              .updateCourse(that.updateCourseWord)
              .then(() => {
                that.dialogVisible = false;
                loading.close();
                that.$nextTick(() => that.fetchData());
                that.$message.success('修改课程成功');
              })
              .catch((err) => {
                // 关闭提示弹框
                loading.close();
              });
          } else {
            console.log('error submit!!');
            // loading.close();
            return false;
          }
        });
      },
      handleRemoveDetail() {
        this.fd = '';
      },
      // 课程开通与暂停
      courseStatus(id, status) {
        if (status == 0) {
          status = 1;
        } else {
          status = 0;
        }
        console.log(typeof status);
        const that = this;
        this.$confirm('确定操作吗?', '修改状态', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            courseWordApi
              .updateStatus(id, status)
              .then((res) => {
                that.$nextTick(() => that.fetchData());
                that.$message.success('修改成功!');
              })
              .catch((err) => {});
          })
          .catch((err) => {});
      },
      // 分页
      handleSizeChange(val) {
        this.tablePage.size = val;
        this.fetchData();
      },
      handleCurrentChange(val) {
        this.tablePage.currentPage = val;
        this.fetchData();
      },
      // 关闭弹窗
      close() {
        this.dialogVisible = false;
        this.showUpload = false;
      },
      //表选中的多个数据获取id
      handleSelectionChange(val) {
        this.rows = val;
      },
      //批量删除
      batchDeletion() {
        if (this.rows.length <= 0) {
          this.$message.warning('请选中至少一条记录');
        } else {
          const courseWordDelete = {
            courseWordCo: this.rows
          };
          this.$confirm('确定操作吗?', '删除单词', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          })
            .then(() => {
              courseWordApi.batchDeletion(courseWordDelete).then((res) => {
                this.$message.success('删除成功');
                this.fetchData();
              });
            })
            .catch((err) => {
              // this.$message.error("删除成功");
            });
        }
      },
      // 返回课程列表
      goBack() {
        this.$router.push({
          path: '/course/courseList'
        });
      },

      //上传模板
      beforeUplpad01(file) {
        this.fd = file.raw;
      },
      //限制文件
      handleExceed(files, fileList) {
        this.$message.warning(`当前限制选择 1个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`);
      },
      // 上传规则弹窗
      openDialog() {
        this.dialogVisibleRule = true;
      },
      confirmUpload() {
        this.dialogVisibleRule = false; // 关闭弹窗
        this.showUpload = true; // 显示上传界面
        this.$nextTick(() => {
          if (this.$refs.uploadRef) {
            // 直接查找 input 元素并触发点击
            const fileInput = this.$refs.uploadRef.$el.querySelector("input[type='file']");
            if (fileInput) {
              fileInput.click();
            } else {
              console.error("未找到 input[type='file']");
            }
          }
        });
      },
      confirmUploadError() {
        this.dialogVisibleError = false;
        this.fd = '';
        this.$nextTick(() => {
          if (this.$refs.uploadRef) {
            this.$refs.uploadRef.clearFiles(); // 清空已上传的文件
            // 直接查找 input 元素并触发点击
            const fileInput = this.$refs.uploadRef.$el.querySelector("input[type='file']");
            if (fileInput) {
              fileInput.click();
            } else {
              console.error("未找到 input[type='file']");
            }
          }
        });
      }
    }
  };
</script>

<style>
  .lh36 {
    line-height: 36px;
    font-size: 14px;
  }

  .SearchForm {
    width: 100%;
    margin-bottom: 15px;
    background: #fff;
    border-radius: 6px;
    padding: 5px 0;
    padding-left: 5px;
    box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.2);
  }

  .btn-add {
    padding: 5px;
  }

  .red {
    color: red;
  }

  .green {
    color: green;
  }

  .course-table {
    text-align: center;
  }

  .course-table td,
  .course-table th {
    padding: 5px 0;
    text-align: center;
  }

  .course-table button {
    padding: 2px;
  }

  .icon-stop {
    float: left;
    display: inline-block;
    width: 12px;
    height: 12px;
    background: url('../../icons/stop.png') no-repeat top center/contain;
  }

  .mt22 {
    margin-top: 22px;
  }

  .blue {
    margin-right: 50px;
    color: #409eff;
  }

  @media (max-width: 767px) {
    .el-message-box {
      width: 80% !important;
    }
  }
</style>
