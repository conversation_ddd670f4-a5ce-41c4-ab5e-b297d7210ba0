<template>
  <div class="app-container">
    <div class="SearchForm">
      <!-- 表格 -->
      <el-table
        class="common-table"
        :data="tableData"
        stripe
        border
        :default-sort="{ prop: 'addTime', order: 'descending' }"
        v-loading="tableLoading"
      >
        <el-table-column prop="name" label="题目类型"></el-table-column>
        <el-table-column
          prop="secondValue"
          label="做题时间（秒）"
        ></el-table-column>
        <el-table-column prop="id" label="操作">
          <template slot-scope="scope">
            <el-button
              type="primary"
              size="medium"
              icon="el-icon-edit-outline"
              @click="openEdit(scope.row)"
              >编辑</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="SearchForm">
      <!-- 表格 -->
      <el-table
        class="common-table"
        :data="tableTestingData"
        stripe
        border
        :default-sort="{ prop: 'addTime', order: 'descending' }"
        v-loading="tableLoading"
      >
        <el-table-column prop="name" label="题目类型"></el-table-column>
        <el-table-column
          prop="secondValue"
          label="通过线（%）"
        ></el-table-column>
        <el-table-column prop="id" label="操作">
          <template slot-scope="scope">
            <el-button
              type="success"
              size="medium"
              icon="el-icon-edit-outline"
              @click="openTestingEdit(scope.row)"
              >编辑</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </div>

    <el-dialog
      title="题目配置"
      :visible.sync="showEdit"
      width="45%"
      :close-on-click-modal="false"
      @close="showEdit = false"
    >
      <el-form
        :ref="'updateGrammar'"
        :rules="updateSingle"
        :model="updateGrammar"
        label-position="left"
        label-width="80px"
        style="width: 100%"
      >
        <el-form-item label="id" prop="id" v-show="false">
          <el-col :xs="24" :span="12">
            <el-input v-model="updateGrammar.id"></el-input>
          </el-col>
        </el-form-item>
        <el-form-item label="题目类型" prop="name" label-width="120px">
          <el-col :xs="24" :span="12">
            <el-input v-model="updateGrammar.name"></el-input>
          </el-col>
        </el-form-item>
        <el-form-item
          label="做题时间（秒）"
          prop="secondValue"
          label-width="120px"
        >
          <el-col :xs="24" :span="12">
            <el-input v-model="updateGrammar.secondValue"></el-input>
          </el-col>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button
          size="medium"
          type="primary"
          @click="editConfig('updateGrammar')"
          >确定</el-button
        >
        <el-button size="medium" @click="closeEdit">关闭</el-button>
      </div>
    </el-dialog>

    <el-dialog
      title="题目配置"
      :visible.sync="showTestingEdit"
      width="45%"
      :close-on-click-modal="false"
      @close="showTestingEdit = false"
    >
      <el-form
        :ref="'updateGrammar'"
        :rules="updateSingle"
        :model="updateGrammar"
        label-position="left"
        label-width="80px"
        style="width: 100%"
      >
        <el-form-item label="id" prop="id" v-show="false">
          <el-col :xs="24" :span="12">
            <el-input v-model="updateGrammar.id"></el-input>
          </el-col>
        </el-form-item>
        <el-form-item label="题目类型" prop="name" label-width="120px">
          <el-col :xs="24" :span="12">
            <el-input v-model="updateGrammar.name"></el-input>
          </el-col>
        </el-form-item>
        <el-form-item
          label="通过线（%）"
          prop="secondValue"
          label-width="120px"
        >
          <el-col :xs="24" :span="12">
            <el-input v-model="updateGrammar.secondValue"></el-input>
          </el-col>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button
          size="medium"
          type="primary"
          @click="editConfig('updateGrammar')"
          >确定</el-button
        >
        <el-button size="medium" @click="closeEdit">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listAPI, updateAPI } from "@/api/grammar/configUration";
export default {
  data() {
    return {
      tableLoading: false,
      updateSingle: {
        name: [
          {
            required: true,
            message: "必填",
            trigger: "blur",
          },
        ],
        secondValue: [
          {
            required: true,
            message: "必填",
            trigger: "blur",
          },
        ],
      },
      tableData: [], //表格数据
      tableTestingData: [], //表格数据
      showEdit: false, //编辑弹窗
      showTestingEdit: false, //编辑弹窗
      updateGrammar: {}, // 修改数据
      editId: "", // 修改id
    };
  },
  created() {
    this.fetchData();
  },
  methods: {
    // 查询表格列表
    fetchData() {
      listAPI({ configType: 1 }).then((res) => {
        this.tableData = res.data;
      });
      listAPI({ configType: 2 }).then((res) => {
        this.tableTestingData = res.data;
      });
    },
    openEdit(row) {
      this.showEdit = true;
      // 创建临时数据对象
      this.updateGrammar = JSON.parse(JSON.stringify(row));
      this.editId = row.id;
    },
    openTestingEdit(row) {
      console.log(row, "222");
      this.showTestingEdit = true;
      // 创建临时数据对象
      this.updateGrammar = JSON.parse(JSON.stringify(row));
      this.editId = row.id;
    },
    editConfig(ele) {
      this.$refs[ele].validate((valid) => {
        // 表单验证
        if (valid) {
          updateAPI({
            id: Number(this.editId),
            name: this.updateGrammar.name,
            secondValue: this.updateGrammar.secondValue,
          })
            .then((res) => {
              this.showEdit = false;
              this.showTestingEdit = false;
              // 更新列表数据
              const index = this.tableData.findIndex(
                (item) => item.id === this.editId
              );
              if (index !== -1) {
                this.tableData[index] = {
                  ...this.tableData[index],
                  ...this.updateGrammar,
                };
              }
              const testingIndex = this.tableTestingData.findIndex(
                (item) => item.id === this.editId
              );
              if (testingIndex !== -1) {
                this.tableTestingData[testingIndex] = {
                  ...this.tableTestingData[testingIndex],
                  ...this.updateGrammar,
                };
              }
              this.fetchData();
              this.$message.success("语法配置成功");
            })
            .catch(() => {
              this.$message.error("语法配置失败");
              return false;
            });
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    closeEdit() {
      this.showEdit = false;
      this.showTestingEdit = false;
    },
  },
};
</script>

<style scoped lang="scss">
.lh36 {
  line-height: 36px;
  font-size: 14px;
}

.SearchForm {
  width: 100%;
  margin-bottom: 15px;
  background: #fff;
  border-radius: 6px;
  padding: 5px 0;
  padding-left: 5px;
  box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.2);
}

.btn-add {
  padding: 5px;
}

.red {
  color: red;
}

.green {
  color: green;
}

.course-table {
  text-align: center;
}

.course-table td,
.course-table th {
  padding: 5px 0;
  text-align: center;
}

.course-table button {
  padding: 2px;
}

.icon-stop {
  float: left;
  display: inline-block;
  width: 12px;
  height: 12px;
  background: url("../../icons/stop.png") no-repeat top center/contain;
}

.mt22 {
  margin-top: 22px;
}

.blue {
  margin-right: 50px;
  color: #409eff;
}
@media (max-width: 767px) {
  .el-message-box {
    width: 80% !important;
  }
}
</style>
