<template>
  <div class="app-container">
    <el-form :inline="true" class="container-card" label-width="110px" label-position="left">
      <el-row>
        <el-col :span="8" :xs="24">
          <el-form-item label="会员编号：">
            <el-input id="merchantCode" v-model="dataQuery.memberCode" name="memberCode" placeholder="请输入会员编号" clearable/>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="登录账号：">
            <el-input v-model="dataQuery.loginName" name="loginName" placeholder="请输入登录账号：" clearable/>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="会员姓名：">
            <el-input v-model="dataQuery.bankAccName" name="bankAccName" placeholder="请输入会员姓名" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8" :xs="24">
          <el-form-item label="会员等级：">
            <el-select v-model="dataQuery.rank" placeholder="全部" style="width: 185px;">
              <el-option v-for="(item,index) in memberRank" :key="index" :label="item.label" :value="item.value" >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="推荐人名称：">
            <el-input v-model="dataQuery.referrerName" name="referrerName" />
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="推荐人手机号：">
            <el-input v-model="dataQuery.referrerBankAccMobile" name="referrerBankAccMobile" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8" :xs="24">
          <el-form-item>
            <el-form-item label="注册时间：">
              <el-date-picker style="width: 100%;" v-model="regTime" type="daterange" value-format="yyyy-MM-dd HH:mm:ss"
                              align="right" unlink-panels range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
              </el-date-picker>
            </el-form-item>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="状态:">
            <el-select v-model="dataQuery.isEnable" filterable value-key="value" placeholder="请选择">
              <el-option v-for="(item,index) in [{value: 1, label: '开通'},{ value :0,label:'暂停'}]" :key="index" :label="item.label"
                         :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="fetchData01()">搜索</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <el-col :span="24" style="margin-bottom: 30px;">
      <el-button type="warning" icon="el-icon-document-copy" @click="exportMember()" v-loading="exportLoading" size="mini">导出</el-button>
    </el-col>

    <el-table class="common-table" v-loading="tableLoading" :data="tableData" style="width: 100%;margin-bottom: 20px;"
      row-key="id" border default-expand-all :tree-props="{list: 'children', hasChildren: 'true'}">
      <el-table-column prop="memberCode" label="会员编号"></el-table-column>
      <el-table-column prop="loginName" label="登录账号"></el-table-column>
      <el-table-column label="操作" align="center" width="380">
        <template slot-scope="scope">
          <el-button type="primary" icon="el-icon-view" @click="clickAdd(scope.row.id)" size="mini">查看</el-button>
          <el-button type="warning" size="mini" icon="el-icon-switch-button" v-if="scope.row.isEnable === 0" @click="memberStatus(scope.row.id)">开通</el-button>
          <el-button type="danger" size="mini" icon="el-icon-video-pause" v-else @click="memberStatus(scope.row.id)">暂停</el-button>
          <el-button type="success" size="mini" icon="el-icon-edit-outline" v-if="scope.row.memberRank !== '会员'" @click="openEdit(scope.row.id)">编辑</el-button>
          <el-button type="success" size="mini" icon="el-icon-edit-outline" v-if="scope.row.memberRank !== '会员'" @click="openRecommender(scope.row.id)">设置推介人</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="bankAccName" label="会员姓名"></el-table-column>
      <el-table-column prop="memberRank" label="会员等级"></el-table-column>
      <el-table-column prop="address" label="所在地区" show-overflow-tooltip></el-table-column>
      <el-table-column prop="referrerName" label="推荐人名称"></el-table-column>
      <el-table-column prop="referrerPhone" label="推荐人手机号"></el-table-column>
      <el-table-column prop="subMembers" label="下级会员"></el-table-column>
      <el-table-column prop="studentCount" label="挂靠学员" ></el-table-column>
      <el-table-column prop="balance" label="累计收益（元）" ></el-table-column>
      <el-table-column prop="settleBalance" label="现金余额（元）" ></el-table-column>
      <el-table-column prop="drawCashBalance" label="可提现金额（元）" ></el-table-column>
      <el-table-column prop="regTime" label="注册时间" width="160"></el-table-column>
      <el-table-column prop="vipOpenTime" label="VIP开通时间" width="160"></el-table-column>
      <el-table-column prop="vipOverTime" label="VIP结束时间" width="160"></el-table-column>
      <el-table-column prop="isEnable" label="状态" sortable>
        <template slot-scope="scope">
          <span class="green" v-if="scope.row.isEnable === 1">开通</span>
          <span class="red" v-else>暂停</span>
        </template>
      </el-table-column>
    </el-table>
    <!-- 查看信息弹窗 -->
    <el-dialog title="会员信息" :visible.sync="dialogVisible" width="70%" :close-on-click-modal="false" @close="close">
      <el-form :ref="addOrUpdate?'addMarketDate':'updateMarketDate'" :rules="rules" :model="updateMarketDate"
        label-position="left" label-width="100px" style="width: 100%;">
        <el-form-item label="会员编号：" prop="memberCode">
          <el-col :xs="24" :span="18">
            <el-input v-model="updateMarketDate.memberCode" readonly=readonly autocomplete="off" />
          </el-col>
        </el-form-item>
        <el-form-item label="登录账号：" prop="loginName">
          <el-col :xs="24" :span="18">
            <el-input v-model="updateMarketDate.loginName" readonly=readonly autocomplete="off" />
          </el-col>
        </el-form-item>
        <el-form-item label="推荐人编号：" prop="referrerCode">
          <el-col :xs="24" :span="18">
            <el-input v-model="updateMarketDate.referrerCode" readonly=readonly autocomplete="off" />
          </el-col>
        </el-form-item>
        <el-form-item label="会员等级：" prop="memberRank">
          <el-col :xs="24" :span="18">
            <el-input v-model="updateMarketDate.rankStr" readonly=readonly autocomplete="off" />
          </el-col>
        </el-form-item>
        <el-form-item label="身份证号：" prop="idCard">
          <el-col :xs="24" :span="18">
            <el-input v-model="updateMarketDate.idCard" readonly=readonly autocomplete="off" />
          </el-col>
        </el-form-item>
        <el-form-item label="开户银行：" prop="bankName">
          <el-col :xs="24" :span="18">
            <el-input v-model="updateMarketDate.bankName" readonly=readonly autocomplete="off" />
          </el-col>
        </el-form-item>
        <el-form-item label="账户姓名：" prop="bankAccName">
          <el-col :xs="24" :span="18">
            <el-input v-model="updateMarketDate.bankAccName" readonly=readonly autocomplete="off" />
          </el-col>
        </el-form-item>
        <el-form-item label="银行账号：" prop="bankCardCode">
          <el-col :xs="24" :span="18">
            <el-input v-model="updateMarketDate.bankCardCode" readonly=readonly autocomplete="off" />
          </el-col>
        </el-form-item>
        <el-form-item label="所在地区：" prop="area">
          <el-col :xs="24" :span="18">
            <el-input v-model="updateMarketDate.area" readonly=readonly autocomplete="off" />
          </el-col>
        </el-form-item>
        <el-form-item label="地址：" prop="address">
          <el-col :xs="24" :span="18">
            <el-input v-model="updateMarketDate.address" readonly=readonly autocomplete="off" />
          </el-col>
        </el-form-item>
        <el-form-item label="注册时间：" prop="regTime">
          <el-col :xs="24" :span="18">
            <el-input v-model="updateMarketDate.regTime" readonly=readonly autocomplete="off" />
          </el-col>
        </el-form-item>
        <el-form-item label="状态：" prop="isEnable">
          <el-col :xs="24" :span="18">
            <el-input v-model="updateMarketDate.isEnableStr" readonly=readonly autocomplete="off" />
          </el-col>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" size="mini" @click="close">关闭</el-button>
      </div>
    </el-dialog>
    <!-- 编辑弹窗 -->
    <el-dialog title="编辑" :visible.sync="dialogEditVisible" width="70%" :close-on-click-modal="false" @close="closeEdit">
      <el-form :ref="addOrUpdate?'addMarketDate':'editMem'" :rules="rules" :model="editMem" label-position="left"
        label-width="100px" style="width: 100%;">
        <el-form-item label="会员名称：" prop="memberName">
          <el-col :xs="24" :span="18">
            <el-input v-model="editMem.bankAccName" autocomplete="off"  />
          </el-col>
        </el-form-item>
        <el-form-item label="会员等级：" prop="memberRank">
          <el-col :xs="24" :span="18">
            <el-select v-model="editMem.memberRank" placeholder="请选择" >
              <el-option v-for="(item,index) in memberRank" :key="index" :label="item.label" :value="item.value" />
            </el-select>
          </el-col>
        </el-form-item>
        <el-form-item label="所在地区：" prop="address">
          <el-col :xs="24" :span="18">
            <el-select v-model="editMem.provice" placeholder="请选择" style="margin-right: 20px;" @change="getCity">
              <el-option v-for="(item,index) in province" :key="index" :label="item.name" :value="index"></el-option>
            </el-select>
            <el-select v-model="editMem.city" placeholder="请选择"  @change="getCityStr">
              <el-option v-for="(item,index) in city" :key="index" :label="item.name" :value="index"></el-option>
            </el-select>
          </el-col>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" size="mini" @click="editMember(editMem)">确定</el-button>
        <el-button size="mini" @click="closeEdit">关闭</el-button>
      </div>
    </el-dialog>

    <!-- 设置推荐人弹窗 -->
    <el-dialog title="设置推荐人" :visible.sync="dialogRecommenderVisible" width="70%" :close-on-click-modal="false" @close="closeRecommender">
      <el-form :ref="addOrUpdate?'addMarketDate':'editRef'" :rules="rules" :model="editRef" label-position="left"
        label-width="120px">
        <el-col :xs="24" :span="12">
          <el-input v-show="false" v-model="editRef.id" readonly=readonly autocomplete="off" />
        </el-col>
        <el-form-item label="会员编号：" prop="memberCode">
          <el-col :xs="24" :span="12">
            <el-input v-model="editRef.memberCode" readonly=readonly autocomplete="off"  />
          </el-col>
        </el-form-item>
        <el-form-item label="会员名称：" prop="bankAccName">
          <el-col :xs="24" :span="12">
            <el-input v-model="editRef.bankAccName" readonly=readonly autocomplete="off"  />
          </el-col>
        </el-form-item>
        <el-form-item label="会员手机号：" prop="loginName">
          <el-col :xs="24" :span="12">
            <el-input v-model="editRef.loginName" readonly=readonly autocomplete="off"  />
          </el-col>
        </el-form-item>
        <el-form-item label="推荐人编号：" prop="referrerCode">
          <el-col :xs="24" :span="12">
            <el-input v-model="editRef.referrerCode" autocomplete="off"  />
          </el-col>
        </el-form-item>
        <el-form-item label="推荐人名称：" prop="referrerName">
          <el-col :xs="24" :span="12">
            <el-input v-model="editRef.referrerName" readonly=readonly autocomplete="off"  />
          </el-col>
        </el-form-item>
        <el-form-item label="推荐人手机号：" prop="referrerPhone">
          <el-col :xs="24" :span="12">
              <el-input v-model="editRef.referrerPhone" readonly=readonly autocomplete="off"  />
          </el-col>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" size="mini" @click="editMemRef(editRef)">确定</el-button>
        <el-button size="mini" @click="closeRecommender">关闭</el-button>
      </div>
    </el-dialog>
    <!-- 分页 -->
    <el-col :span="20">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]" layout="total, sizes, prev, pager, next, jumper"
        :total="tablePage.totalItems" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    </el-col>
  </div>
</template>

<script>
  import memberApi from '@/api/member'
  import {
    pageParamNames
  } from "@/utils/constants";
  import courseApi from '@/api/courseList'
  export default {
    name:'memberList',
    data() {
      return {
        rules: { // 表单提交规则
          city: [{
            required: true,
            message: '请选择城市',
            trigger: 'blur'
          }],
          province: [{
            required: true,
            message: '请选择省份',
            trigger: 'blur'
          }],
          memberName: [{
            required: true,
            message: '请填写会员名称',
            trigger: 'blur'
          }],
          memberRank: [{
            required: true,
            message: '请选择会员等级',
            trigger: 'change'
          }]
        },
        exportLoading:false,
        tableLoading: false,
        // 分页
        tablePage: {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null
        },
        tableData: [],
        regTime: [],
        dataQuery: {},
        addOrUpdate: false,
        dialogVisible: false,
        dialogEditVisible: false, //咨询师编辑弹框
        dialogRecommenderVisible: false, //设置推荐人弹窗
        updateMarketDate: {},
        editMem: {},
        addMarketDate: {},
        memberRank: [],
        province: [],
        city: [],
        IsEnable: [],
        showLoginAccount: false,
        provinceStr: '',
        cityStr: '',
        editRef: {}

      };
    },
    created() {
      this.fetchData();
      this.getRank();

    },
    methods: {
        fetchData01(){
         this.tablePage= {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null
        }
        this.fetchData();
      },
      // 查询提现列表
      fetchData() {
        const that = this;
        that.tableLoading = true
        var a = that.regTime;
        if (a != null) {
          that.dataQuery.regStartTime = a[0];
          that.dataQuery.regEndTime = a[1];
        }
        memberApi.memberPage(
          that.tablePage.currentPage,
          that.tablePage.size,
          that.dataQuery
        ).then(res => {
          that.tableData = res.data.data;
          that.tableLoading = false;
          // 设置后台返回的分页参数
          pageParamNames.forEach(name =>
            that.$set(that.tablePage, name, parseInt(res.data[name]))
          );
        });
      },
      exportMember() {
        const that = this;
        that.exportLoading=true;
        memberApi.exportMember(that.dataQuery).then(response => {
          console.log(response)
          // if (!response) {
          //   this.$notify.error({
          //     title: '操作失败',
          //     message: '文件下载失败'
          //   })
          // }
          const url = window.URL.createObjectURL(response)
          const link = document.createElement('a')
          link.style.display = 'none'
          link.href = url // 获取服务器端的文件名
          link.setAttribute('download', '会员表.xls')
          document.body.appendChild(link)
          link.click()
          this.exportLoading = false

        })
      },

      getRank() {
        memberApi.memberRank().then(res => {
          this.memberRank = res.data

        });
      },

      getCity(val) {
        this.$set(this.editMem, 'city', ['请选择']);
        this.cityStr = '';
        this.provinceStr = this.province[val].name;
        memberApi.getProvince(this.province[val].id).then(res => {
          this.city = res.data
        })


      },

      getCityStr(val) {
        this.cityStr = this.city[val].name;
      },

      //新增操作
      clickAdd(id) {
        // alert(id)
        const that = this
        memberApi.memberDetail(id).then(res => {
          that.updateMarketDate = res.data
        })
        this.dialogVisible = true;
      },
      memberStatus(id, status) {
        const that = this;
        this.$confirm('确定操作吗?', '修改状态', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          memberApi.updateStatus(id).then(res => {
            that.fetchData01();
            that.$message.success('修改成功!')
          }).catch(err => {

          })
        }).catch(err => {

        })
      },
      editMember(ele) {
        const that = this;
        that.editMem.province = that.provinceStr;
        that.editMem.city = that.cityStr;
        if (!that.editMem.bankAccName) {
          that.$message.error('请填写名字');
          return false;
        }
        if (!that.editMem.memberRank) {
          that.$message.error('请选择等级');
          return false;
        }

        if (!that.editMem.province) {
          that.$message.error('请选择省份');
          return false;
        }
        if (!that.editMem.city || that.editMem.city === ' ') {
          that.$message.error('请选择市');
          return false;
        }


        const loading = this.$loading({
          lock: true,
          text: '修改课程信息提交',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        })
        memberApi.updateMember(that.editMem).then(() => {
          that.dialogVisible = false
          loading.close()
          that.$nextTick(() => that.fetchData())
          that.$message.success('修改会员成功')
          this.dialogEditVisible = false
        }).catch(err => {
          loading.close()
        })
      },

      editMemRef(ele) {
        const that = this;

        if(that.editRef.id===null || that.editRef.id==='' || that.editRef.id===""){
          that.$message.info("id不能为空");
          return false;
        }
        if(that.editRef.referrerCode===null || that.editRef.referrerCode==='' || that.editRef.referrerCode==="" ){
          that.$message.info("推荐人编号不能为空");
          return false;
        }
         const loading = this.$loading({
          lock: true,
          text: '修改推介人信息提交',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        })
        memberApi.updateMemberRef(that.editRef.id, that.editRef.referrerCode).then(res => {
          if (!res.success) {
            loading.close()
            that.$message.error(res.message)
            return
          }
          that.dialogVisible = false
          loading.close()
          that.fetchData01();
          that.$message.success('修改会员成功')
          that.dialogRecommenderVisible = false
        }).catch(err => {
          loading.close();
        })


      },
      // 关闭
      close() {
        this.dialogVisible = false
      },
      // 打开编辑弹窗
      openEdit(id) {
        this.dialogEditVisible = true
        const that = this
        memberApi.getProvince(0).then(res => {
          this.province = res.data
        })
        memberApi.memberDetail(id).then(res => {
          that.editMem = res.data
          that.provinceStr = res.data.provice;
          that.cityStr = res.data.city;
          that.city=res.data.cityList;

        })

      },
      //关闭编辑弹窗
      closeEdit() {

        this.dialogEditVisible = false
      },
      // 打开推荐人弹窗
      openRecommender(id) {
        const that = this
        memberApi.memberDetail(id).then(res => {
          console.log(res);
          that.editRef = res.data;
        })
        this.dialogRecommenderVisible = true
      },
      //关闭推荐人弹窗
      closeRecommender() {
        this.dialogRecommenderVisible = false
      },
      // 分页
      handleSizeChange(val) {
        this.tablePage.size = val;
        this.fetchData();
      },
      handleCurrentChange(val) {
        this.tablePage.currentPage = val;
        this.fetchData();
      }
    }
  };
</script>

<style>
  .container-card {
    padding: 15px 15px 15px 15px;
    margin-bottom: 15px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  }
  @media (max-width:767px) {
	  .el-message-box{
		width: 80%!important;
	  }
  }
</style>
