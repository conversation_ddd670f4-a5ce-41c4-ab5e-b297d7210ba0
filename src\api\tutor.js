/**
 * 教练 相关接口
 */
import request from '@/utils/request'

export default {
  getTutorTypeList() {
    return request({
      url: '/cousys/web/tutor/type/list',
      method: 'GET'
    })
  },
  addTutorCommit(data) {
    return request({
      url: '/cousys/web/tutor/add',
      method: 'POST',
      data
    })
  },
  getTutorList(pageNum, pageSize, data) {
    return request({
      url: '/cousys/web/tutor/list/' + pageNum + '/' + pageSize,
      method: 'GET',
      params: data
    })
  },
  getStudentTutorInfo(tutorId) {
    return request({
      url: '/cousys/web/tutor/one/student?tutorId=' + tutorId,
      method: 'GET'
    })
  },
  getUnemployedInfo(tutorId) {
    return request({
      url: '/cousys/web/tutor/one/unemployed?tutorId=' + tutorId,
      method: 'GET'
    })
  },
  getJobInfo(tutorId) {
    return request({
      url: '/cousys/web/tutor/one/job?tutorId=' + tutorId,
      method: 'GET'
    })
  },
  deleteTutorInfo(tutorId) {
    return request({
      url: '/cousys/web/tutor/delete/' + tutorId,
      method: 'PUT'
    })
  },
  editStatus(data) {
    return request({
      url: '/cousys/web/tutor/update',
      method: 'PUT',
      data
    })
  },
}
