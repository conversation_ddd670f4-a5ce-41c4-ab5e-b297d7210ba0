<template>
  <div class="add-video-dialog">
    <el-dialog width="800px" :visible.sync="dialogParam.visible" :show-close="false" :close-on-click-modal="false" @submit.native.prevent>
      <div class="exam-detail">
        <div class="inner">
          <div class="title">
            <span>{{ explainInfo.examName }}</span>
          </div>
          <div class="testTime">
            考试时间：
            <el-button type="text" icon="el-icon-timer"></el-button>
            {{ countdownMinutes }}:{{ countdownSeconds < 10 ? '0' + countdownSeconds : countdownSeconds }}
          </div>
          <div class="cont">
            <div class="cont-left">
              <div class="subhead">
                试卷说明：本试卷共
                <span v-if="explainInfo.radioNum > 0">{{ explainInfo.radioNum }}道单选题</span>
                <span v-if="explainInfo.multiSelectNum > 0">，{{ explainInfo.multiSelectNum }} 道多选题</span>
                <span v-if="explainInfo.judgeNum > 0">，{{ explainInfo.judgeNum }} 道判断题</span>
                ，满分
                <span>{{ explainInfo.examFullMarks }}</span>
                ，考试时间
                <span>{{ explainInfo.totalTime }}</span>
                分钟
              </div>
              <div class="item" v-for="(i, index) in datalist" :key="index">
                <div class="subject">{{ index + 1 }}、【{{ i.questionType == 1 ? '单选' : i.questionType == 2 ? '多选' : '判断' }}】{{ i.questionName }}</div>
                <el-checkbox-group v-if="i.questionType == 2" v-model="i.checkList">
                  <el-checkbox v-for="(options, indx) in i.optionList" :label="options.optionSort" :key="indx">
                    <span v-if="options.optionSort >= 0">{{ options.questionOptionDescription + '、' }}</span>
                    <span class="content_css">{{ options.questionOptionContent }}</span>
                  </el-checkbox>
                </el-checkbox-group>
                <el-radio-group v-else v-model="i.checkText" class="option" @change="chooseAnswer">
                  <el-radio :label="options.optionSort" v-for="(options, indx) in i.optionList" :key="indx">
                    <span v-if="options.optionSort >= 0">{{ options.questionOptionDescription + '、' }}</span>
                    <span class="content_css">{{ options.questionOptionContent }}</span>
                  </el-radio>
                </el-radio-group>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 底部按钮栏 -->
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" size="small" @click="submitForm()">提交</el-button>
      </div>
      <el-dialog title="温馨提示" :visible.sync="isShow" append-to-body width="300px" @close="cancelSubmit">
        <span v-if="showFalse">你还有几道题没有写，请继续答题</span>
        <span v-else>确定提交</span>
        <span slot="footer" class="dialog-footer">
          <el-button @click="cancelSubmit">取 消</el-button>
          <el-button type="primary" @click="queSubmitForm" :loading="loadingTrue">确 定</el-button>
        </span>
      </el-dialog>
    </el-dialog>
  </div>
</template>
<script>
  import store from '@/store';
  // trainingHandPaper
  import examApi from '@/api/training/exam';
  export default {
    name: 'addExamManagement',
    components: {},
    props: {
      dialogParam: {
        type: Object,
        default() {
          return {};
        }
      }
    },
    data() {
      return {
        loadingTrue: false,
        datalist: [],
        choiseList: [],
        countdownMinutes: 70,
        countdownSeconds: 0,
        timer: null,
        showFalse: false,
        isShow: false,
        explainInfo: {
          radioNum: 0,
          multiSelectNum: 0,
          judgeNum: 0
        },
        formInfo: {
          courseId: 0,
          examId: 0,
          questionList: [],
          userId: 0
        }
      };
    },
    mounted() {
      // this.startTime();
    },
    methods: {
      open(res, id) {
        this.explainInfo = {
          radioNum: 0,
          multiSelectNum: 0,
          judgeNum: 0
        };
        console.log(res);
        let sysUserRoles = JSON.parse(window.localStorage.getItem('sysUserRoles'));
        this.datalist = res.questionList;
        this.formInfo.examId = res.examId;
        this.countdownMinutes = res.examTimeLimit;
        this.explainInfo.totalTime = res.examTimeLimit;
        this.explainInfo.examName = res.examName;
        this.explainInfo.examFullMarks = res.examFullMarks;
        this.formInfo.courseId = id;
        this.formInfo.userId = sysUserRoles[0].userId;
        this.datalist.forEach((item) => {
          if (item.questionType == 2) {
            item.checkList = [];
            this.explainInfo.multiSelectNum += 1;
          } else if (item.questionType == 1) {
            item.checkText = '';
            this.explainInfo.radioNum += 1;
          } else {
            item.checkText = '';
            this.explainInfo.judgeNum += 1;
          }
          item.optionList.forEach((info) => {
            info.optionSortText = String.fromCharCode(65 + info.optionSort);
          });
        });
        console.log('this.datalist', this.datalist);
        console.log('this.explainInfo', this.explainInfo);
        this.startTime();
      },
      chooseAnswer(option) {
        console.log(option, 'option');
      },
      handleClose(done) {
        this.$confirm('确认关闭？')
          .then((_) => {
            done();
          })
          .catch((_) => {});
      },
      startTime() {
        this.timer = setInterval(() => {
          if (this.countdownSeconds === 0) {
            if (this.countdownMinutes === 0) {
              clearInterval(this.timer);
              // alert("倒计时结束");
              this.queSubmitForm(1);
            } else {
              this.countdownMinutes--;
              this.countdownSeconds = 59;
            }
          } else {
            this.countdownSeconds--;
          }
        }, 1000);
      },

      // 提交
      submitForm() {
        this.showFalse = false;
        // 提交之后展示成绩
        console.log(this.datalist);
        console.log('kkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkk');
        this.datalist.forEach((item) => {
          let optionList = [];
          item.optionList.forEach((info) => {
            if (item.questionType == 2) {
              if (!item.checkList.length > 0) {
                this.showFalse = true;
                optionList.push({ id: info.id, isAnswer: 0 });
              } else {
                optionList.push({ id: info.id, isAnswer: item.checkList.indexOf(info.optionSort) != -1 ? 1 : 0 });
              }
            } else {
              if (!item.checkText && item.checkText !== 0) {
                this.showFalse = true;
                optionList.push({ id: info.id, isAnswer: 0 });
              } else {
                optionList.push({ id: info.id, isAnswer: item.checkText == info.optionSort ? 1 : 0 });
              }
            }
          });
          this.formInfo.questionList.push({ optionList: optionList, id: item.id });
        });
        console.log('this.datalist提交之后展示成绩', this.datalist);
        console.log('this.explainInfo提交之后展示成绩', this.explainInfo);
        this.isShow = true;
        this.loadingTrue = false;
      },
      cancelSubmit() {
        this.formInfo.questionList = [];
        this.isShow = false;
      },
      queSubmitForm(show) {
        this.loadingTrue = true;
        //倒计时结束
        if (show == 1) {
          this.formInfo.questionList = [];
          this.datalist.forEach((item) => {
            let optionList = [];
            item.optionList.forEach((info) => {
              if (item.questionType == 2) {
                if (!item.checkList.length > 0) {
                  optionList.push({ id: info.id, isAnswer: 0 });
                } else {
                  optionList.push({ id: info.id, isAnswer: item.checkList.indexOf(info.optionSort) != -1 ? 1 : 0 });
                }
              } else {
                if (!item.checkText && item.checkText !== 0) {
                  optionList.push({ id: info.id, isAnswer: 0 });
                } else {
                  optionList.push({ id: info.id, isAnswer: item.checkText == info.optionSort ? 1 : 0 });
                }
              }
            });
            this.formInfo.questionList.push({ optionList: optionList, id: item.id });
          });
        } else {
          if (this.showFalse && this.countdownMinutes >= 0 && this.countdownSeconds > 0) {
            this.isShow = false;
            this.formInfo.questionList = [];
            return;
          }
        }
        examApi.trainingHandPaper(this.formInfo).then((res) => {
          this.$emit('closeDialog', res.data);
          // if (res.data && res.data.passFlag == 1) {
          //   store.dispatch("checkExamStatus"); // 考试通过，获取考试状态调整菜单权限
          // }
          this.formInfo.questionList = [];
          this.isShow = false;
          this.loadingTrue = false;
        });
      }
    },
    beforeDestroy() {
      clearInterval(this.timer);
    }
  };
</script>
<style scoped lang="scss">
  .el-dialog__header {
    display: none;
  }
  .testTime {
    font-size: 16px;
    margin-right: 20px;
    float: right;
  }
  .exam-detail {
    background: #fafafa;
    padding: 20px 0;
    box-sizing: border-box;
    .inner {
      // width: 1200px;
      width: 100%;
      margin: 0 auto;
      .title {
        font-size: 26px;
        font-weight: bold;
        margin-bottom: 20px;
        text-align: center;
        a {
          color: #333;
          margin-right: 5px;
        }
      }
      .cont {
        display: flex;
        width: 100%;
        justify-content: space-between;
        .cont-left {
          // width: 742px;
          width: 100%;
          background: #fff;
          min-height: 600px;
          .subhead {
            width: 100%;
            height: 47px;
            background: #effaff;
            line-height: 47px;
            color: #0076ff;
            padding-left: 26px;
            box-sizing: border-box;
            font-size: 16px;
            font-weight: bold;
          }
          .item {
            padding: 15px 26px;
            box-sizing: border-box;
            .subject {
              margin-bottom: 20px;
            }
            .option {
              ::v-deep .el-radio {
                margin-bottom: 15px;
                display: flex;
                flex-direction: row;
                flex-wrap: wrap;
              }
              ::v-deep .el-radio__input {
                display: block;
              }
              ::v-deep.el-radio__label {
                display: block;
                white-space: normal;
                width: 664px;
                line-height: 15px;
              }
            }
          }
        }
      }
    }
  }

  ul {
    width: 270px;
    // padding: 10px;
    // box-sizing: border-box;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    flex-wrap: wrap;
    align-content: flex-start;
    align-items: flex-start;
    background: red;
    li {
      width: 35px;
      height: 35px;
      border-radius: 6px;
      margin: 5px;
      &.green-bg {
        background: green;
      }
      &.red-bg {
        background: red;
      }
      &.gray-bg {
        background: gray;
      }
    }
  }
  ::v-deep .el-checkbox {
    display: block;
    margin-bottom: 10px;
  }
  .content_css {
    width: 620px;
    display: inline-block;
    white-space: normal;
    vertical-align: middle;
  }
</style>
