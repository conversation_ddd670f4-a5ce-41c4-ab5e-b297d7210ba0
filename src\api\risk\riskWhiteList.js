import request from '@/utils/request'

export default {

  // 分页查询
  whiteListPage(params) {
    return request({
      url: '/znyy/riskWhiteList/pageList',
      method: 'GET',
      params: params
    })
  },

  // 获取被授权方类型
  getAcceptType() {
    return request({
      url: '/znyy/riskWhiteList/getAcceptType',
      method: 'GET'
    })
  },

  // 获取风险行为类型
  getBehaviorList() {
    return request({
      url: '/znyy/riskWhiteList/getBehaviorList',
      method: 'GET'
    })
  },

  //新增风控白名单
  addRiskWhiteList(data) {
    return request({
      url: '/znyy/riskWhiteList/addWhiteList',
      method: 'POST',
      params: data
    })
  },

  //删除风控白名单
  deleteWhiteList(id) {
    return request({
      url: '/znyy/riskWhiteList/delete/' + id,
      method: 'POST'
    })
  }

}
