<template>
  <div class="app-container">
    <!-- 搜索 -->
    <el-form
      ref="dataQuery"
      :model="dataQuery"
      :inline="true"
      class="SearchForm"
      label-width="100px"
      style="padding: 40px 0 30px 20px"
    >
      <el-row style="padding-bottom: 15px">
        <el-col :span="8" :xs="24">
          <el-form-item label="阶段:" prop="phase">
            <el-select
              size="medium"
              v-model="dataQuery.phase"
              filterable
              value-key="value"
              placeholder="请选择阶段"
              @change="getKnowIdList"
              clearable
            >
              <el-option
                v-for="(item, index) in mapPhaseType"
                :key="index"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="8" :xs="24">
          <el-form-item label="知识点:" prop="knowledgeId">
            <el-select
              size="medium"
              style="width: 200px"
              v-model="dataQuery.knowledgeId"
              filterable
              value-key="index"
              placeholder="请选择知识点"
              clearable
            >
              <el-option
                v-for="(item, index) in grammarTypeListG"
                :key="index"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8" :xs="24">
          <el-form-item label="题目类型:" prop="questionType">
            <el-select
              size="medium"
              v-model="dataQuery.questionType"
              filterable
              value-key="value"
              placeholder="请选择题型"
              clearable
            >
              <el-option
                v-for="(item, index) in mapQuestionTypeType"
                :key="index"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="排序:" prop="sortNum">
            <el-input
              size="medium"
              @input="validateSortNumS"
              v-model="dataQuery.sortNum"
              @keyup.enter.native="fetchData02()"
              style="width: 200px"
              placeholder="请输入"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col
          :span="8"
          :xs="8"
          style="text-align: right; padding-top: 6px; padding-right: 100px"
        >
          <el-button
            type="primary"
            icon="el-icon-search"
            size="medium"
            @click="fetchData02()"
            style="margin-right: 20px"
            >搜索</el-button
          >
          <el-button
            type="info"
            icon="el-icon-refresh"
            size="medium"
            @click="resetData()"
            >重置</el-button
          >
        </el-col>
      </el-row>
    </el-form>

    <div class="SearchForm">
      <!-- 添加 -->
      <el-button
        style="margin-bottom: 20px; margin: 5px 5px 5px 0"
        size="medium"
        type="success"
        icon="el-icon-plus"
        @click="clickAdd"
        >添加</el-button
      >
      <!-- 表格 -->
      <el-table
        class="common-table"
        :data="tableData"
        stripe
        border
        max-height="500"
        :default-sort="{ prop: 'addTime', order: 'descending' }"
        v-loading="tableLoading"
        :empty-text="tableData.length === 0 ? '暂无数据' : ''"
      >
        <el-table-column prop="id" label="题目编号"></el-table-column>
        <el-table-column prop="sortNum" label="排序"></el-table-column>
        <el-table-column prop="phase" label="阶段"></el-table-column>
        <el-table-column prop="questionCategory" label="题目所属">
        </el-table-column>
        <el-table-column prop="questionType" label="题目类型">
        </el-table-column>
        <el-table-column label="题目" show-overflow-tooltip prop="questionName">
        </el-table-column>
        <el-table-column
          prop="questionExamPoints"
          show-overflow-tooltip
          label="考点"
        >
        </el-table-column>
        <el-table-column prop="knowledgeName" label="知识点"></el-table-column>
        <el-table-column prop="createTime" label="上传时间"></el-table-column>
        <el-table-column prop="id" label="操作" fixed="right" width="300px">
          <template slot-scope="scope">
            <el-button
              type="success"
              size="medium"
              icon="el-icon-edit-outline"
              @click="openEdit(scope.row)"
              >编辑</el-button
            >
            <el-button
              type="danger"
              size="medium"
              icon="el-icon-delete"
              @click="deleteQuestion(scope.row.id)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <el-col :span="20">
      <el-pagination
        :current-page="tablePage.currentPage"
        :page-sizes="[10, 50, 100, 150, 200]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="tablePage.totalItems"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-col>

    <!-- 添加弹窗   -->
    <el-dialog
      :title="title"
      :visible.sync="dialogVisible"
      width="1200px"
      :close-on-click-modal="false"
      @close="close"
    >
      <el-form
        ref="dataDialog"
        :rules="updateSingle"
        :model="dataDialog"
        :inline="true"
        label-width="100px"
        class="common-form"
      >
        <el-row>
          <el-col :span="8" :xs="24">
            <el-form-item label="阶段：" prop="phase">
              <el-select
                size="medium"
                @change="getGrammarType"
                v-model="dataDialog.phase"
                filterable
                value-key="value"
                placeholder="请选择阶段"
                clearable
                :disabled="editDisabled"
              >
                <el-option
                  v-for="(item, index) in mapPhaseType"
                  :key="index"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" :xs="24">
            <el-form-item label="题目所属：" prop="questionCategory">
              <el-select
                size="medium"
                v-model="dataDialog.questionCategory"
                filterable
                value-key="value"
                placeholder="请选择题目所属"
                clearable
                :disabled="editDisabled"
                @change="quesTypeChange"
              >
                <el-option label="知识点" value="1" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" :xs="24">
            <el-form-item
              label="知识点:"
              :key="dataDialog.questionCategory"
              prop="knowledgeId"
            >
              <el-select
                size="medium"
                @change="handleGrammarChange"
                v-model="dataDialog.knowledgeId"
                filterable
                value-key="index"
                placeholder="请先选择阶段"
                clearable
              >
                <el-option
                  v-for="(item, index) in grammarTypeList"
                  :key="index"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8" :xs="24">
            <el-form-item label="题目类型：" prop="questionType">
              <el-select
                size="medium"
                v-model="dataDialog.questionType"
                filterable
                value-key="value"
                placeholder="请选择题型"
                clearable
                :disabled="editDisabled"
                @change="questionTypeData"
              >
                <el-option
                  v-for="(item, index) in mapQuestionTypeType"
                  :key="index"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" :xs="24">
            <el-form-item
              label="排序："
              prop="sortNum"
              style="margin-bottom: 20px"
            >
              <el-input
                size="medium"
                @input="validateSortNumInput"
                v-model="dataDialog.sortNum"
                style="width: 200px"
                placeholder="请输入"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item
          label="题目："
          prop="questionName"
          style="margin-bottom: 30px"
        >
          <el-row>
            <el-input
              type="textarea"
              size="medium"
              v-model="dataDialog.questionName"
              style="width: 790px"
              placeholder="请输入题目"
              clearable
            ></el-input
          ></el-row>
        </el-form-item>
        <div v-if="dataDialog.questionType !== ''">
          <!-- 选项  改为原题选项-->
          <el-form-item
            :label="dataDialog.questionType == '1' ? '原题选项：' : '原题答案'"
            prop="tableFrom"
            label-width="100px"
          >
            <el-row>
              <el-button
                style="color: #4c8eff; border-color: #4c8eff"
                size="medium"
                icon="el-icon-plus"
                @click="confirmOrigin('check')"
              >
                添加选项
              </el-button>
            </el-row>
            <el-radio-group v-model="status" @input="groupChanged">
              <el-table
                class="common-table"
                style="margin-bottom: 30px; width: 790px"
                :data="
                  dataDialog.questionType === '1' ? tableCheck : tableCheckData
                "
                stripe
                border
                max-height="500"
                :default-sort="{ prop: 'addTime', order: 'descending' }"
                v-loading="tableLoading"
              >
                <el-table-column
                  prop="num"
                  v-if="dataDialog.questionType === '1' && editAns"
                  label="勾选正确答案"
                >
                  <template slot-scope="scope">
                    <el-radio :key="scope.row.id" :label="scope.row.index">{{
                      ""
                    }}</el-radio>
                    <span v-if="!scope.row.tableEditing">{{
                      scope.row.num
                    }}</span>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="optionContent"
                  v-if="dataDialog.questionType === '1'"
                  label="选项内容"
                >
                  <template slot-scope="scope">
                    <el-input
                      v-if="scope.row.tableEditing"
                      v-model="scope.row.optionContent"
                      placeholder="请输入内容"
                    ></el-input>
                    <span v-else>{{ scope.row.optionContent }}</span>
                  </template>
                </el-table-column>
                <el-table-column
                  v-if="dataDialog.questionType === '2'"
                  prop="optionContent"
                  label="空题答案"
                >
                  <template slot-scope="scope">
                    <el-input
                      v-if="scope.row.tableEditing"
                      v-model="scope.row.optionContent"
                      placeholder="请输入内容"
                    ></el-input>
                    <span v-else>{{ scope.row.optionContent }}</span>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="id"
                  label="操作"
                  fixed="right"
                  width="200px"
                >
                  <template slot-scope="scope">
                    <el-button
                      v-if="scope.row.tableEditing"
                      type="text"
                      size="medium"
                      @click="confirmOriginEdit('check', scope.$index)"
                      >确定</el-button
                    >
                    <el-button
                      v-else
                      type="text"
                      size="medium"
                      style="color: red"
                      @click="deleteOption('check', scope.$index)"
                      >删除</el-button
                    >
                  </template>
                </el-table-column>
              </el-table>
            </el-radio-group>
          </el-form-item>
        </div>
        <el-form-item
          label="解析："
          prop="questionDescription"
          style="margin-bottom: 30px"
        >
          <el-row>
            <el-input
              type="textarea"
              size="medium"
              v-model="dataDialog.questionDescription"
              style="width: 790px"
              placeholder="请输入解析"
              clearable
            ></el-input>
          </el-row>
        </el-form-item>
        <!-- 题目所属是知识点时是获取思维导图中的三级节点 -->
        <el-form-item label="考点：" prop="examPoints">
          <el-select
            size="medium"
            value-key="id"
            v-model="dataDialog.examPoints"
            multiple
            placeholder="请选择"
            @focus="handleSelectChange()"
            style="width: 590px"
          >
            <el-option
              style="width: 590px"
              v-for="item in thirdNodeList"
              :key="item.id"
              :label="item.topic"
              :value="item"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <el-row style="text-align: right; margin-top: 30px">
        <el-button
          type="info"
          icon="el-icon-close"
          size="medium"
          @click="resetData()"
          >取消</el-button
        >
        <el-button
          type="primary"
          icon="el-icon-check"
          size="medium"
          @click="submitClick()"
          >确定</el-button
        >
      </el-row>
    </el-dialog>
  </div>
</template>

<script>
import {
  optionsAPI,
  queryByTypeAPI,
  pageAPI,
  addOrUpdateAPI,
  thirdNodeAPI,
  deleteAPI,
  detailAPI,
} from "@/api/grammar/grammarQuestion";
import { pageParamNames } from "@/utils/constants";
import { valid } from "mockjs";
export default {
  data() {
    return {
      alphabetList: [
        "A",
        "B",
        "C",
        "D",
        "E",
        "F",
        "G",
        "H",
        "I",
        "J",
        "K",
        "L",
        "M",
        "N",
        "O",
        "P",
        "Q",
        "R",
        "S",
        "T",
        "U",
        "V",
        "W",
        "X",
        "Y",
        "Z",
      ],
      knowId: "",
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null,
      },
      tableLoading: false,
      // 搜索
      dataQuery: {
        questionName: "",
        questionType: "", // 题型
        phase: "", // 阶段
        questionCategory: "", // 题目所属
        questionName: "", // 题目
        sortNum: "",
        knowledgeId: "",
      },
      isRouterAlive: true, //局部刷新
      //表格数据
      tableData: [],
      tableOrigin: [],
      tableOriginData: [],
      tableCheck: [],
      tableCheckData: [],
      // 弹层数据
      dialogVisible: false, // 修改弹窗是否展示
      title: "",
      dataDialog: {
        sortNum: "", //排序
        knowledgeIdList: [], // 知识点列表
        knowledgeId: "", // 知识点
        questionType: "", // 题型
        phase: "", // 阶段
        questionCategory: "", // 题目所属
        questionName: "", // 题目
        examPoints: [], // 考点
        questionDescription: "", // 解析
        optionList: [], // 空列表接收并赋值
        optionCheck: [], // 选项列表
      },
      // 考点 思维导图三级节点
      thirdNodeList: [],
      tableEditing: false, // 是否正在编辑
      rows: [], //选中的数据id存放处
      addOrUpdate: true, // 是新增还是修改
      updateCourseWord: {}, // 修改数据
      rules: {},
      phase: "",
      fd: "",
      status: null,
      statusOrigin: null,
      statusPoint: null,
      updateSingle: {
        knowledgeId: [
          {
            required: true,
            message: "必填",
            trigger: "blur",
          },
        ],
        questionType: [
          {
            required: true,
            message: "必填",
            trigger: "blur",
          },
        ],
        phase: [
          {
            required: true,
            message: "必填",
            trigger: "blur",
          },
        ],
        grammarPoint: [
          {
            required: true,
            message: "必填",
            trigger: "blur",
          },
        ],
        questionCategory: [
          {
            required: true,
            message: "必填",
            trigger: "blur",
          },
        ],
        questionName: [
          {
            required: true,
            message: "必填",
            trigger: "blur",
          },
        ],
        examPoints: [
          {
            required: true,
            message: "必填",
            trigger: "blur",
          },
        ],
        sortNum: [
          {
            required: true,
            message: "必填",
            trigger: "blur",
          },
        ],
        tableFrom: [
          {
            validator: this.validateTableForm,
            required: true,
            trigger: "blur",
          },
        ],
        questionDescription: [
          {
            required: true,
            message: "必填",
            trigger: "blur",
          },
        ],
      },
      //题型分类
      // 语法分类
      grammarTypeList: [],
      grammarTypeListG: [],
      // 试题所属
      mapQuestionBelongsType: [],
      // 题型选择
      questionType: [],
      // 阶段数据
      mapPhaseType: [],
      mapQuestionTypeType: [],
      editAns: false,
      editDisabled: false,
    };

  },
  created() {
    this.getType();
    // this.getKnowIdList();
  },
  watch:{
  },
  methods: {

    questionTypeData(){
      if(this.dataDialog.questionType == 1){
        this.status = null
        this.tableCheckData = [];
      } else {
        this.status = null
        this.tableCheck = []
      }
    },
       // 知识点下拉框
    getKnowIdList() {
      this.dataQuery.knowledgeId = "";
      optionsAPI({ knowledgeFlag: true, phase: this.dataQuery.phase }).then(
        (response) => {
          this.grammarTypeListG = response.data;
        }
      );
    },

    validateSortNumInput(event) {
      if (!/^\d*$/.test(event)) {
        this.$message.error("请输入有效的数字");
        this.dataDialog.sortNum = ""; // 清空输入框
      }
    },
    validateSortNumS(event) {
      if (!/^\d*$/.test(event)) {
        this.$message.error("请输入有效的数字");
        this.dataQuery.sortNum = "";
      }
    },
    groupChanged(e) {
      this.dataDialog.optionCheck.forEach((item, index) => {
        if (e !== index) {
          item.optionIsAnswer = 0;
        } else {
          item.optionIsAnswer = 1;
        }
      });
    },
    // 阶段 语法点 试题所属
    getType() {
      Promise.all([
        queryByTypeAPI({ dictType: "grammar_phase" }),
        queryByTypeAPI({ dictType: "question_category" }),
        queryByTypeAPI({ dictType: "grammar_question_type" }),
      ])
        .then((responses) => {
          this.mapPhaseType = responses[0].data.map((item, index) => ({
            id: item.dictValue,
            name: item.dictLabel,
          }));
          this.mapQuestionBelongsType = responses[1].data.map((item) => ({
            id: item.dictValue,
            name: item.dictLabel,
          }));
          this.mapQuestionTypeType = responses[2].data.map((item) => ({
            id: item.dictValue,
            name: item.dictLabel,
          }));
          // 获取完字典数据后再调用 fetchData 方法
          this.fetchData();
        })
        .catch((error) => {
          console.error("获取字典数据失败", error);
        });
    },

    // 切换阶段或者题目所属后清空对应数据
    resetOption() {
      this.tableOrigin = [];
      this.tableOriginData = [];
      this.tableCheck = [];
      this.tableCheckData = [];
      this.dataDialog.examPoints = [];
      this.dataDialog.questionName = "";
      this.dataDialog.questionDescription = "";
      this.dataDialog.sortNum = "";
    },
    // 获取题目所属内容
    quesType() {
      this.dataDialog.knowledgeId = "";
      this.resetOption();
    },
    quesTypeChange() {
      this.tablePointData = [];
      this.tableOrigin = [];
      this.tableCheck = [];
      this.tableCheckData = [];
    },
    // 阶段触发知识点 下拉框
    async getGrammarType() {
      this.resetOption();
      this.dataDialog.examPoints = [];
      this.dataDialog.knowledgeId = "";
      optionsAPI({ knowledgeFlag: true, phase: this.dataDialog.phase }).then(
        (response) => {
          this.grammarTypeList = response.data;
        }
      );
    },
    // 编辑下拉框显示所有数据
    handleSelectChange() {
      if (this.dataDialog.knowledgeId === "") {
        this.$message.error("请选择知识点");
        return;
      }
      thirdNodeAPI({ knowledgeIdList: this.dataDialog.knowledgeId }).then(
        (response) => {
          this.thirdNodeList = response.data;
        }
      );
    },
    // 下拉框选中值后请求考点数据
    handleGrammarChange(value) {
      this.dataDialog.examPoints = [];
      const formattedValue = Array.isArray(value) ? value.join(",") : value;
      if (formattedValue === "") {
        this.thirdNodeList = [];
        return;
      }
      thirdNodeAPI({ knowledgeIdList: formattedValue }).then((response) => {
        this.thirdNodeList = response.data;
      });
    },
    // 查询表格列表
    fetchData() {
      pageAPI(this.dataQuery, this.tablePage)
        .then((res) => {
          this.tableData = res.data.data;
          res.data.data.forEach((item) => {
            const foundPhase = this.mapPhaseType.find(
              (phaseItem) => phaseItem.id === item.phase
            );
            if (foundPhase) {
              item.phase = foundPhase.name;
            }
          });
          res.data.data.forEach((item) => {
            const foundQues = this.mapQuestionBelongsType.find(
              (quesItem) => quesItem.id === item.questionCategory
            );
            if (foundQues) {
              item.questionCategory = foundQues.name;
            }
          });
          res.data.data.forEach((item) => {
            const questionFound = this.mapQuestionTypeType.find(
              (quesTypeItem) => quesTypeItem.id === item.questionType
            );
            if (questionFound) {
              item.questionType = questionFound.name;
            }
          });
          // 设置后台返回的分页参数
          pageParamNames
            .filter((i) => i !== "currentPage")
            .forEach((name) =>
              this.$set(this.tablePage, name, parseInt(res.data[name]))
            );
        })
        .catch((error) => {
          console.error("数据加载失败", error);
        });
    },
    // 点击添加表格选项 原题
    confirmOrigin(type) {
      this.tableEditing = true;
      const newItem = {
        optionSort: 0,
        optionContent: "",
        optionIsAnswer: 0,
        optionIsExamPoints: 0,
        tableEditing: true,
      };

      const getTargetArrayAndSortCounter = () => {
        let targetArray, sortCounter;
        if (this.dataDialog.questionType === "1") {
          targetArray = this.tableCheck;
          sortCounter = this.tableCheck.length;
        } else if (this.dataDialog.questionType === "2") {
          newItem.optionIsAnswer = 1;
          targetArray = this.tableCheckData;
          sortCounter = this.tableCheckData.length;
        }
        return { targetArray, sortCounter };
      };
      const { targetArray, sortCounter } = getTargetArrayAndSortCounter();
      newItem.optionSort = sortCounter;
      targetArray.push(newItem);
      targetArray.forEach((item, i) => (item.index = i));
      // 重新排序 optionSort
      targetArray.sort((a, b) => a.optionSort - b.optionSort);
      for (let i = 0; i < targetArray.length; i++) {
        targetArray[i].optionSort = i;
      }
    },
    // 表格点击确定 原题 考点 选项
    async confirmOriginEdit(type, index) {
      let dataToLoad;
      dataToLoad =
        this.dataDialog.questionType === "1"
          ? this.tableCheck
          : this.tableCheckData;
      dataToLoad[index].num = `${this.alphabetList[index]}`;
      if (
        this.dataDialog.questionType === "1" &&
        !dataToLoad[index].optionContent
      ) {
        this.$message.error("单选时，内容不能为空");
        return;
      }
      if (
        this.dataDialog.questionType === "2" &&
        !dataToLoad[index].optionContent
      ) {
        this.$message.error("填空时，内容不能为空");
        return;
      }

      this.dataDialog.optionCheck = dataToLoad;
      this.tableEditing = false;
      this.dataDialog.optionCheck[index].tableEditing = false;
      this.editAns = true;
      await this.$nextTick();
    },
    // 表格删除选项
    deleteOption(type, index) {
      let targetArray;
      targetArray =
        this.dataDialog.questionType === "1"
          ? this.tableCheck
          : this.tableCheckData;
      this.status = null;

      if (targetArray && targetArray.length > index) {
        targetArray.splice(index, 1);
        // 重新设置字母标识和选中状态
        targetArray.forEach((item, i) => {
          item.num = this.alphabetList[i];
          item.index = i;
        });
        // 重新排序 optionSort
        targetArray.sort((a, b) => a.optionSort - b.optionSort);
        for (let i = 0; i < targetArray.length; i++) {
          targetArray[i].optionSort = i;
        }
        this.$message.success("删除成功");
      } else {
        this.$message.error("删除操作失败");
      }
    },
    // 添加选项校验
    validateTableForm(rule, value, callback) {
      if (this.dataDialog.questionCategory === "1") {
        if (this.dataDialog.questionType === "1") {
          // 单选
          const originData =
            this.dataDialog.questionType === "1"
              ? this.tableCheck
              : this.tableCheckData;
          for (let item of originData) {
            if (!item.num || !item.optionContent) {
              // callback(new Error("选项内容不能为空"));
              return;
            }
          }
        } else if (this.dataDialog.questionType === "2") {
          // 填空
          const originData =
            this.dataDialog.questionType === "1"
              ? this.tableCheck
              : this.tableCheckData;
          for (let item of originData) {
            if (!item.optionContent) {
              // callback(new Error("填空内容不能为空"));
              return;
            }
          }
        }
      }

      callback();
    },
    // 弹层 确定按钮
    submitClick() {
      console.log("submit click", "111", this.dataDialog);
      if (
        this.tableCheck.some((i) => i.tableEditing) ||
        this.tableCheckData.some((i) => i.tableEditing)
      ) {
        return this.$message.error("请确定选项内容");
      }
      if (valid) {
        console.log("valid", "222");
        this.$refs.dataDialog.validate((valid) => {
          if (valid) {
            let id = this.dataDialog.id;
            let knowledgeIdList =
              this.dataDialog.questionCategory === "1"
                ? [this.dataDialog.knowledgeId]
                : this.dataDialog.knowledgeIdList;

            if (!Array.isArray(knowledgeIdList)) {
              knowledgeIdList = [knowledgeIdList];
            }

            // 判断每个表格中是否有一项 optionIsAnswer 等于 1（针对 questionType 为 1 的情况）
            if (this.dataDialog.questionType === "1") {
              let hasAnswerInTables = false;
              const checkData = this.tableCheck;
              hasAnswerInTables = checkData.some(
                (item) => item.optionIsAnswer === 1
              );
              if (!hasAnswerInTables) {
                this.$message.error("请在每个表格中勾选正确答案");
                return false;
              }
            } else {
              const questionText = this.dataDialog.questionName;
              const blankCount = (questionText.match(/##/g) || []).length;
              let answerData;
              if (this.tableCheckData.length) {
                answerData = this.tableCheckData;
              } else {
                answerData = this.tableOriginData;
              }
              const answerCount = answerData.length;
              if (blankCount === 0 || answerCount !== blankCount) {
                const message =
                  blankCount === 0
                    ? "题目有误，请检查"
                    : `答案输入个数有误，应输入 ${blankCount} 个答案`;
                this.$message.error(message);
                return false;
              }
            }

            if (!id) {
              this.dataDialog.optionList = this.dataDialog.optionCheck;
              addOrUpdateAPI({ ...this.dataDialog, knowledgeIdList })
                .then((response) => {
                  console.log("11111111111111");
                  this.$message.success("新增成功");
                  this.dialogVisible = false;
                  this.fetchData();
                  this.closeData();
                  //  清空单选框的数据
                  this.status = null;
                  this.editAns = false;
                })
                .catch((error) => {
                  // this.$message.error("操作失败，请稍后重试");
                  // this.dialogVisible = false;
                });
            } else {
              this.dataDialog.optionList = this.dataDialog.optionCheck.concat(
                this.tablePoint
              );
              addOrUpdateAPI({ ...this.dataDialog, knowledgeIdList })
                .then((response) => {
                  this.$message.success("编辑成功");
                  this.dialogVisible = false;
                  this.fetchData();
                  this.closeData();
                  //  清空单选框的数据
                  this.status = null;
                  this.editAns = false;
                })
                .catch((error) => {
                  // this.$message.error("操作失败，请稍后重试");
                  // this.dialogVisible = false;
                });
            }
          } else {
            this.$message.error("请完善表单信息");
            return false;
          }
          // this.closeData();
          // this.dialogVisible = false;
        });
      }
    },
    // 打开编辑题目
    openEdit(row) {
      this.editAns = true;
      this.title = "编辑试题";
      this.editDisabled = true;
      // this.dataDialog.knowledgeId = "";
      // 请求详细信息
      detailAPI({ id: row.id }).then((res) => {
        const data = res.data;
        this.dataDialog = { ...data, optionCheck: data.optionList };
        this.grammarTypeList = data.knowledgeIdList;

        // 处理 knowledgeId
        this.dataDialog.knowledgeId =
          data.knowledgeIdList.length > 0 &&
          this.dataDialog.questionCategory === "1"
            ? data.knowledgeIdList[0].id
            : "";

        // 处理 examPoints
        this.thirdNodeList =
          this.dataDialog.questionCategory === "1"
            ? data.examPoints
            : data.knowledgeIdList.map((item) => ({
                id: item.id,
                topic: item.name,
              }));

        this.dataDialog.examPoints = this.thirdNodeList;

        // 处理问题类型
        if (this.dataDialog.questionType === "1") {
          this.processOptionData(data.optionList, data.examPointsOptionList);
        } else if (this.dataDialog.questionType === "2") {
          this.processOptionData(
            data.optionList,
            data.examPointsOptionList,
            true
          );
        }
        optionsAPI({ knowledgeFlag: true, phase: this.dataDialog.phase }).then(
          (response) => {
            this.grammarTypeList = response.data;
          }
        );
        // 显示弹窗
        this.dialogVisible = true;
      });
    },

    processOptionData(
      optionList = [],
      examPointsOptionList = [],
      isSingleChoice = false
    ) {
      const setIndexAndAnswerStatus = (list, statusKey) => {
        list.forEach((item, index) => {
          item.index = index;
          // 根据不同的列表设置 optionIsExamPoints 的值
          item.optionIsExamPoints = list === examPointsOptionList ? 1 : 0;
          if (item.optionIsAnswer == 1) {
            this[statusKey] = index;
          }
        });
      };

      const setAlphabetSuffix = (list) => {
        list.forEach((option, index) => {
          if (option && this.alphabetList[index]) {
            option.num = `${this.alphabetList[index]}`;
          }
        });
      };

      if (isSingleChoice) {
        this.tableOriginData = optionList;
        this.tablePointData = examPointsOptionList;
        this.tableCheckData = optionList;

        setIndexAndAnswerStatus(this.tablePointData, "statusPoint");
        setAlphabetSuffix(this.tablePointData);
      } else {
        this.tableOrigin = optionList;
        this.tablePoint = examPointsOptionList;
        this.tableCheck = optionList;

        setIndexAndAnswerStatus(this.tableOrigin, "statusOrigin");
        setIndexAndAnswerStatus(this.tableCheck, "status");
        setIndexAndAnswerStatus(this.tablePoint, "statusPoint");

        setAlphabetSuffix(this.tableOrigin);
        setAlphabetSuffix(this.tablePoint);
      }
    },

    fetchData01() {
      this.tablePage = {
        currentPage: 1,
        size: this.tablePage.size,
        totalPage: null,
        totalItems: null,
      };
      this.fetchData();
    },
    fetchData02() {
      this.tablePage = {
        currentPage: 1,
        size: this.tablePage.size,
        totalPage: null,
        totalItems: null,
      };
      window.localStorage.setItem("grammarId", "");
      this.fetchData();
    },
    // 重置列表
    resetData() {
      this.$refs.dataQuery.resetFields();
      this.dataQuery = {
        grammarName: "",
        question: "",
        questionType: "",
        phase: "",
        difficulty: "",
        grammarId: "",
        grammarPoint: "",
      };
      this.dialogVisible = false;
      this.fetchData();
    },
    //局部刷新
    reload() {
      this.isRouterAlive = false;
      this.$nextTick(function () {
        this.isRouterAlive = true;
      });
    },
    closeData() {
      this.dataDialog = {
        sortNum: "", //排序
        knowledgeIdList: [], // 知识点
        knowledgeId: "",
        questionType: "", // 题型
        phase: "", // 阶段
        questionCategory: "", // 题目所属
        questionName: "", // 题目
        examPoints: [], // 考点
        questionDescription: "", // 解析
        optionList: [], // 选项列表
      };
    },
    //添加操作
    clickAdd() {
      this.tableOrigin = [];
      this.tableOriginData = [];
      this.tablePoint = [];
      this.tableCheck = [];
      this.tableOriginData = [];
      this.tableCheckData = [];
      this.grammarTypeList = [];
      this.closeData();
      this.title = "新增试题";
      this.reload();
      this.fd = "";
      this.dialogVisible = true;
      this.addOrUpdate = true;
      this.editDisabled = false;
    },
    //删除操作题目
    deleteQuestion(id) {
      this.$confirm("确定进行删除操作吗?", "删除题目", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return deleteAPI({ id: id });
        })
        .then(() => {
          this.fetchData();
          this.$message.success("删除成功");
        });
    },

    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val;
      this.fetchData();
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val;
      this.fetchData();
    },
    // 关闭弹窗
    close() {
      this.dialogVisible = false;
      this.$refs.dataDialog.resetFields();
      this.status = null;
      this.editAns = false;
    },
  },
  //生命周期结束 离开页面时销毁本地缓存
  beforeDestroy() {
    window.localStorage.setItem("grammarId", "");
  },
};
</script>

<style scoped lang="scss">
.lh36 {
  line-height: 36px;
  font-size: 14px;
}

.SearchForm {
  width: 100%;
  margin-bottom: 15px;
  background: #fff;
  border-radius: 6px;
  padding: 5px 0;
  padding-left: 5px;
  box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.2);
}

.common-form .el-form-item {
  margin-bottom: 30px;
}

textarea {
  min-height: 100px;
}

.el-select {
  width: 100%;
}

.el-radio {
  margin-right: 0;
}

.el-input {
  width: 200px;
}

.btn-add {
  padding: 5px;
}

.red {
  color: red;
}

.green {
  color: green;
}

.course-table {
  text-align: center;
}

.course-table td,
.course-table th {
  padding: 5px 0;
  text-align: center;
}

.course-table button {
  padding: 2px;
}

.icon-stop {
  float: left;
  display: inline-block;
  width: 12px;
  height: 12px;
  background: url("../../icons/stop.png") no-repeat top center/contain;
}

.mt22 {
  margin-top: 22px;
}

.blue {
  margin-right: 50px;
  color: #409eff;
}

@media (max-width: 767px) {
  .el-message-box {
    width: 80% !important;
  }
}
</style>
