import { asyncRouterMap, constantRouterMap } from '@/router'
import authenticationApi from "@/api/authentication";
import channelAuthorityApi from "@/api/channel/channelAuthority";

/**
 * 通过meta.perm判断是否与当前用户权限匹配
 * @param perms 登录用户的权限
 * @param route 路由对象
 */
function hasPermission(perms, route) {
  // 如果没有声明meta或者meta.perm，都视为可以公共访问的路由
  if (!route.meta || !route.meta.perm) {
    return true
  }
  return perms.some(p => p.val == route.meta.perm)
}

/**
 * 递归过滤异步路由表，返回符合用户角色权限的路由表
 * @param asyncRouterMap
 * @param roles
 */
function filterAsyncRouter(asyncRouterMap, perms) {
  const accessedRouters = asyncRouterMap.filter(route => {
    if (hasPermission(perms, route)) {
      if (route.children && route.children.length) {
        route.children = filterAsyncRouter(route.children, perms)
      }
      return true
    }
    return false
  })
  return accessedRouters
}

const permission = {
  state: {
    routers: constantRouterMap,
    addRouters: []
  },
  mutations: {
    SET_ROUTERS: (state, routers) => {
      console.log(routers, "routers");

      state.addRouters = routers
      state.routers = constantRouterMap.concat(routers)
    }
  },
  actions: {
    GenerateRoutes({ commit }, data) {
      return new Promise(resolve => {
        //初始存入路由数据到缓存-防止路由数据被更新
        localStorage.setItem("asyncRouterMap", JSON.stringify(asyncRouterMap))
        let accessedRouters
        let perms = data.perms
        // console.log(perms,"perms");


        //获取当前登录角色，如果是渠道管理员，则查询对应的角色权限
        authenticationApi.getLoginRoleTag().then(res => {
          let roleTag = res.data.roleTag;
          localStorage.setItem("roleTag", roleTag);
          if (roleTag == "channelManager") {
            //if(false){
            let merchantCode = res.data.merchantCode;
            channelAuthorityApi.queryByMerchantCode(merchantCode).then(res => {
              let temp = res.data;
              let roles = temp.roles;
              let permsCache = [];
              for (let i = 0; i < perms.length; i++) {
                for (let j = 0; j < roles.length; j++) {
                  if (perms[i].val.substr(0, 1) == 'b') {
                    permsCache.push(perms[i]);
                  } else if (perms[i].val.indexOf("m:merchant") == -1 && perms[i].val.indexOf("m:areas") == -1 && perms[i].val.indexOf("m:learnTube") == -1) {
                    permsCache.push(perms[i]);
                  } else if (perms[i].val === roles[j][0]) {
                    permsCache.push(perms[i]);
                  }
                }
              }
              perms = permsCache;
              if (perms.some(p => p.val == '*')) {
                accessedRouters = asyncRouterMap
              } else {
                accessedRouters = filterAsyncRouter(asyncRouterMap, perms)
              }
              commit('SET_ROUTERS', accessedRouters)
              resolve()
            }).catch(err => { })
          } else {
            if (perms.some(p => p.val == '*')) {
              accessedRouters = asyncRouterMap
            } else {
              accessedRouters = filterAsyncRouter(asyncRouterMap, perms)
            }
            commit('SET_ROUTERS', accessedRouters)
            resolve()
          }
        }).catch(err => { })

      })
    }
  }
}

export default permission
