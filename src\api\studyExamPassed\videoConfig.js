import request from '@/utils/request';

export default {
  // 分页查询
  videoList(data) {
    return request({
      url: '/dyf/web/xktVideo/page',
      method: 'GET',
      params: data
    });
  },
  //新增
  addVideo(data) {
    return request({
      url: '/dyf/web/xktVideo/save',
      method: 'POST',
      data
    });
  },
  updateVideo(data) {
    return request({
      url: '/dyf/web/xktVideo/update',
      method: 'POST',
      data
    });
  },
  //删除
  deleteVideo(id) {
    return request({
      url: '/dyf/web/xktVideo/delete',
      method: 'DELETE',
      params: {
        id: id
      }
    });
  }
};
