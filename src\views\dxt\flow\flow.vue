<template>
  <div class="app-container">
    <el-form :inline="true" class="container-card" label-width="110px" label-position="left">

      <el-row>
        <el-col :span="8" :xs="24">
          <el-form-item label="流水流出账户：">
            <el-input id="outAccount" v-model="dataQuery.outAccount" name="id" placeholder="请输入流水流出账户：" clearable />
          </el-form-item>
        </el-col>

        <el-col :span="8" :xs="24">
          <el-form-item label="订单号：">
            <el-input id="orderId" v-model="dataQuery.orderId" name="id" placeholder="请输入订单号：：" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="变动方式：">
            <el-select v-model="dataQuery.type" placeholder="全部" style="width: 185px;" clearable>
              <el-option v-for="item in [{ value: 'BUY', label: '购买' }, { value: 'REFUND', label: '退款' }]" :key="item.value"
                :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="14" :xs="24">
          <el-form-item style="text-align: right">
            <el-button type="primary" icon="el-icon-search" @click="fetchData01()">搜索</el-button>
            <el-button type="warning" icon="el-icon-download" v-loading="exportLoading"
              @click="exportList()">导出</el-button>
          </el-form-item>
        </el-col>
      </el-row>

    </el-form>
    <el-table class="period-table" v-loading="tableLoading" :data="tableData" style="width: 100%;margin-bottom: 20px;"
      row-key="id" border default-expand-all :tree-props="{ list: 'children', hasChildren: 'true' }">
      <el-table-column prop="id" label="交易流水号" :show-overflow-tooltip="true"></el-table-column>
      <el-table-column prop="orderId" label="订单编号"></el-table-column>
      <el-table-column prop="outAccount" label="流出账户"></el-table-column>
      <el-table-column prop="outType" label="流出账户类型" align="center">
        <template slot-scope="scope">
          <span v-if="scope.row.outType == 'XCX'">小程序</span>
          <span v-if="scope.row.outType == 'TSY'">通商云</span>
        </template>
      </el-table-column>
      <el-table-column prop="inflowAccount" label="流入账户"></el-table-column>
      <el-table-column prop="inflowType" label="流入账户类型" align="center">
        <template slot-scope="scope">
          <span v-if="scope.row.inflowType == 'XCX'">小程序</span>
          <span v-if="scope.row.inflowType == 'TSY'">通商云</span>
        </template>
      </el-table-column>
      <el-table-column prop="flowType" label="变动方式">
      </el-table-column>
      <el-table-column prop="flowAmount" label="金额"></el-table-column>
      <el-table-column prop="createTime" label="变动时间" :show-overflow-tooltip="true"></el-table-column>
    </el-table>
    <!-- 分页 -->
    <el-col :span="24" style="overflow-x: auto;" :xs="24">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>
  </div>
</template>

<script>
import
flowApi
  from "@/api/dxt/flow";
import Tinymce from "@/components/Tinymce";
import enTypes from '@/api/bstatus'
import {
  pageParamNames
} from "@/utils/constants";
export default {
  name: 'assistantFlowList',
  data() {
    return {
      tableLoading: false,
      exportLoading: false,
      classHoursBeforeTheChange: 0,
      changeOfClassHours: 0,
      classHoursAfterTheChange: 0,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      tableData: [{
        name: 1,
        title: '1111'
      }],
      RegTime: [],
      dataQuery: { outAccount: '', orderId: '', type: '' },
      dialogVisible: false,
      updateMarketDate: {},
      addMarketDate: {},
      showLoginAccount: false,
      financeAccountsType: [],
      exportLoading: false, //导出加载
    };
  },
  created() {
    this.fetchData();
    //获取变动类型
    this.getFinanceAccountsType();
  },
  methods: {
    // 导出
    exportList() {
      const that = this;
      that.exportLoading = true;
      flowApi.flowExport(that.dataQuery).then((response) => {
        if (!response) {
          this.$notify.error({
            title: "操作失败",
            message: "文件下载失败",
          });
        }
        const url = window.URL.createObjectURL(response);
        const link = document.createElement("a");
        link.style.display = "none";
        link.href = url; // 获取服务器端的文件名
        link.setAttribute("download", "会议流水.xls");
        document.body.appendChild(link);
        link.click();
        this.exportLoading = false;
      }).catch(err => {
        this.exportLoading = false;
      });
    },

    fetchData01() {
      this.tablePage = {
        currentPage: 1,
        size: this.tablePage.size,
        totalPage: null,
        totalItems: null
      }
      this.fetchData();
    },
    // 查询提现列表
    fetchData() {
      const that = this;
      that.tableLoading = true
      flowApi
        .flowList(
          that.tablePage.currentPage,
          that.tablePage.size,
          that.dataQuery
        )
        .then((res) => {
          that.tableData = res.data.data;
          that.tableLoading = false;
          // 设置后台返回的分页参数
          pageParamNames.forEach((name) =>
            that.$set(that.tablePage, name, parseInt(res.data[name]))
          );
        });
    },
    //新增操作
    clickAdd() {
      this.dialogVisible = true;
    },
    // 打开修改登陆账号
    openLogin() {
      this.showLoginAccount = true
    },
    closeLogin() {
      this.showLoginAccount = false
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val;
      this.fetchData();
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val;
      this.fetchData();
    },

  }
};
</script>

<style>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.period-table td,
.period-table th {
  text-align: center;
}

.mt20 {
  margin-top: 20px;
}</style>
