<template>
  <div class="app-container">
    <el-form :inline="true" class="container-card" label-width="110px" label-position="left">
      <el-row>
        <el-col :span="8">
          <el-form-item label="商户编号：">
            <el-input id="merchantCode" v-model="dataQuery.memberCode" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="分账商户：">
            <el-input v-model="dataQuery.loginName" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="姓名：">
            <el-input v-model="dataQuery.bankAccName" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="商户类型：">
            <el-select v-model="dataQuery.memberRole" placeholder="全部" style="width: 185px;">
              <el-option v-for="(item,index) in [{label:'会员'},{label:'商户'},{label:'教练 '}]" :key="index"
                :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="原订单号：">
            <el-input v-model="dataQuery.orderCode" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="分账订单号：">
            <el-input v-model="dataQuery.splitCode" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="分账批次号：">
            <el-input v-model="dataQuery.batchSplitCode" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="分账类型：">
            <el-select v-model="dataQuery.splitType" placeholder="全部" style="width: 185px;">
              <el-option v-for="(item,index) in splitType" :key="index" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="分账状态：">
            <el-select v-model="dataQuery.splitStatus" placeholder="全部" style="width: 185px;">
              <el-option
                v-for="(item,index) in [{label:'分账完结',value:'Finish'},{label:'分账中',value:'SplitIng'},{label:'分账失败',value:'SplitSubmitFail'}]"
                :key="index" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="12">
          <el-form-item label="生成时间：">
            <el-date-picker v-model="value1" value-format="yyyy-MM-dd" @change="dateVal" type="daterange" align="right"
              unlink-panels range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" style="width: 100%;" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="完成时间：">
            <el-date-picker v-model="value2" value-format="yyyy-MM-dd" @change="datePayVal" type="daterange"
              align="right" unlink-panels range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
              style="width: 100%;" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="24" style="text-align: right">
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="fetchData()">搜索</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <el-col :span="24" style="margin-bottom: 30px;">
      <el-button type="warning" icon="el-icon-document-copy" size="mini" v-loading="exportLoading"
        @click="exportList()">导出</el-button>
    </el-col>
    <!-- 渲染表格 -->
    <el-table v-loading="tableLoading" class="common-table" :data="tableData" style="width: 100%;margin-bottom: 20px;"
      row-key="id" border default-expand-all :tree-props="{list: 'children', hasChildren: 'true'}">
      <el-table-column prop="memberCode" label="商户编号" />
      <el-table-column prop="bankAccName" label="姓名" />
      <el-table-column prop="addTime" label="操作">
        <template slot-scope="scope">
          <el-tooltip content="编辑" placement="top">
            <!-- <el-button type="warning" icon="el-icon-edit-outline" size="mini" @click="update(scope.row.id)">编辑</el-button> -->
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column prop="memberRole" label="商户类型" />
      <el-table-column prop="orderCode" width="150" label="原订单号" />
      <el-table-column prop="splitCode" label="分账订单号" width="200" />
      <el-table-column prop="splitType" label="分账类型" />
      <el-table-column prop="splitMoney" label="分账金额（元）" />
      <el-table-column prop="addTime" label="生成时间" width="160"></el-table-column>
      <el-table-column prop="submitTime" label="提交时间" width="160" />
      <el-table-column prop="finishTime" label="完成时间" width="160" />
      <el-table-column prop="splitChannel" label="分账通道" />
      <el-table-column prop="splitStatus" label="分账状态">
        <template slot-scope="scope">
          <span class="green" v-if="scope.row.splitStatus === '分账完结'">分账完结</span>
          <span class="green" v-if="scope.row.splitStatus === '分账中'">分账中</span>
          <span class="red" v-if="scope.row.splitStatus === '分账失败'">分账失败</span>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <el-col :span="20">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>
  </div>
</template>

<script>
import ledgerApi from '@/api/ledgerOrderList'
import { pageParamNames } from '@/utils/constants'
import enTypes from '@/api/bstatus'
export default {
  name: 'ledgerOrderList',
  data() {
    return {
      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      uploadLoading: true,
      addOrUpdate: false,
      dataQuery: {
        memberCode: '',
        loginName: '',
        bankAccName: '',
        memberRole: '',
        orderCode: '',
        splitCode: '',
        batchSplitCode: '',
        splitType: '',
        splitStatus: '',
        startAddTime: '',
        endAddTime: '',
        startFinishTime: '',
        endFinishTime: ''
      },
      dialogVisible: false,
      tableData: [],
      value1: '',
      value2: '',
      splitType: [],//分账类型
      exportLoading: false,//导出加载
    }
  },
  created() {
    this.getSplitType()
    this.fetchData()
  },
  methods: {
    //获取词汇等级下拉框
    getSplitType() {
      var enType = "SplitType";
      enTypes.getEnumerationAggregation(enType).then(res => {
        this.splitType = res.data;
      })
    },
    // 获取生成时间
    dateVal(e) {
      this.dataQuery.startAddTime = e[0]
      this.dataQuery.endAddTime = e[1]
    },
    // 获取完成时间
    datePayVal(e) {
      this.dataQuery.startFinishTime = e[0]
      this.dataQuery.endFinishTime = e[1]
    },
    // 查询提现列表
    fetchData() {
      const that = this;
      that.tableLoading = true
      ledgerApi.memberOrder(that.tablePage.currentPage, that.tablePage.size, that.dataQuery).then(res => {
        that.tableData = res.data.data
        that.tableLoading = false
        // 设置后台返回的分页参数
        pageParamNames.forEach(name => that.$set(that.tablePage, name, parseInt(res.data[name])))
      })
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val
      this.fetchData()
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val
      this.fetchData()
    },
    // 导出
    exportList() {
      const that = this
      that.exportLoading = true;
      ledgerApi.orderExport(that.dataQuery).then(response => {
        console.log(response)
        // if (!response) {
        //   this.$notify.error({
        //     title: '操作失败',
        //     message: '文件下载失败'
        //   })
        // }
        const url = window.URL.createObjectURL(response)
        const link = document.createElement('a')
        link.style.display = 'none'
        link.href = url // 获取服务器端的文件名
        link.setAttribute('download', '分账订单表.xls')
        document.body.appendChild(link)
        link.click()
        this.exportLoading = false
      })
    },
  }
}
</script>

<style>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}
.order-table {
  text-align: center;
}

.order-table td,
.order-table th {
  padding: 5px 0;
  text-align: center;
}

.order-table button {
  padding: 2px;
}
</style>
