/**
 * 单词类型分析
 */
import request from '@/utils/request'

export default {
  //课程单词类型分析-新增
  pdWordTypeAnalysisSave(data) {
    return request({
      url: '/znyy/pd/pdWordTypeAnalysis/save',
      method: 'POST',
      data,
    })
  },
  // 分页查询
  pdCourseConfigGetInfo(data) {
    return request({
      url: `/znyy/pd/pdWordTypeAnalysis/page/${data.currentPage}/${data.size}?rightRateStart=${data.rightRateStart}&rightRateEnd=${data.rightRateEnd}&studyType=${data.studyType}&wordType=${data.wordType}`,
      method: 'GET',
    })
  },
 //详情
 getWordTypeById(id) {
    return request({
      url: `/znyy/pd/pdWordTypeAnalysis/getWordTypeById?id=${id}`,
      method: 'GET',
    })
  },
 //编辑
 pdWordTypeAnalysisUpdate(data) {
    return request({
      url: `/znyy/pd/pdWordTypeAnalysis/update`,
      method: 'POST',
      data
    })
  },
   //删除
   deleteById(id) {
    return request({
      url: `/znyy/pd/pdWordTypeAnalysis/deleteById?id=${id}`,
      method: 'GET',
    })
  },

}
