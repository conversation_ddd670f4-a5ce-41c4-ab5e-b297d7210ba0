import request from '@/utils/request'

export default {

  getUserSig(userId) {
    return request({
      url: '/studyroom/user/sign?userCode=' + userId,
      method: 'GET'
    })
  },

  // 导入群成员
  importMember(data) {
    return request({
      url: '/studyroom/tencent/group/import/member',
      method: 'POST',
      data,
    })
  },

  // 删除群成员
  deleteMember(data) {
    return request({
      url: '/studyroom/tencent/group/delete/member',
      method: 'POST',
      data,
    })
  },
  joinRoom(params) {
    return request({
      url: '/studyroom/room/join',
      method: 'POST',
      data: params
    })
  },

  // 吊
  getuserInfo(studentCodes) {
    return request({
      url: '/studyroom/studentInfo/getInfo/batch',
      method: 'GET',
      params: {'studentCodes': studentCodes}
    })

  },
  confirmCall(callId, confirm) {
    return request({
      url: '/studyroom/room/call/confirm',
      method: 'PUT',
      params: {'callId': callId, 'confirm': confirm}
    })
  },
  endCall(studyRoomId){
    return request({
      url: '/studyroom/web/studyroom/call/end',
      method: 'PUT',
      params: {'studyRoomId': studyRoomId }
    })
  },
  endCallId(callId){
    return request({
      url: '/studyroom/room/call/end',
      method: 'PUT',
      params: {'callId': callId }
    })
  },
  // 学员的
  getUserStatus(studyRoomId, studentCodes,pageNum,pageSize) {
    return request({
      url: '/studyroom/room/user/status/batch',
      method: 'GET',
      params: {'studyRoomId': studyRoomId, 'studentCodes': studentCodes,'pageNum':pageNum,'pageSize':pageSize}
    })
  },

  // 禁麦
  micAble(studyRoomId, targetUserCode, status) {
    return request({
      url: '/studyroom/web/studyroom/mic',
      method: 'PUT',
      params: {'studyRoomId': studyRoomId, 'targetUserCode':targetUserCode, 'status':status }
    })
  },

  // 禁言
  MessageAble(studyRoomId, targetUserCode, status) {
    return request({
      url: '/studyroom/web/studyroom/message',
      method: 'PUT',
      params: {'studyRoomId': studyRoomId, 'targetUserCode':targetUserCode, 'status':status }
    })
  },
  // 禁摄像头
  videoAble(studyRoomId, targetUserCode, status) {
    return request({
      url: '/studyroom/web/studyroom/video',
      method: 'PUT',
      params: {'studyRoomId': studyRoomId, 'targetUserCode':targetUserCode, 'status':status }
    })
  },

  // 踢出房间
  kickOut(studyRoomId,userId,targetUserCode) {
    return request({
      url: '/studyroom/web/studyroom/kick/out',
      method: 'PUT',
      params: {'studyRoomId': studyRoomId, 'targetUserCode':targetUserCode, 'userId':userId }
    })
  },

  // 连麦
  roomCall(callId,targetCode,studyRoomId) {
    return request({
      url: '/studyroom/room/call',
      method: 'PUT',
      params: {'callId': callId, 'targetCode':targetCode, 'studyRoomId':studyRoomId }
    })
  },

  // 获取房间信息
  getRoomInfo(roomId) {
    return request({
      url: '/studyroom/web/studyroom/roomInfo',
      method: 'GET',
      params: {'roomId':roomId}
    })
  },

  // 全体禁麦
  allMicAble(studyRoomId,open) {
    return request({
      url: '/studyroom/web/studyroom/silence',
      method: 'PUT',
      params: {'studyRoomId':studyRoomId,'open': open}
    })
  },

  // 关闭PK
  allPKAble(studyRoomId) {
    return request({
      url: '/studyroom/webSolo/edit/solo',
      method: 'PUT',
      params: {'studyRoomId':studyRoomId }
    })
  },

  // 关闭房间
  dissolveRoom(targetRoomID) {
    return request({
      url: '/studyroom/web/studyroom/dissolve',
      method: 'PUT',
      params: {'targetRoomID':targetRoomID}
    })
  },

  // 修改公告/名称
  editName(announcement,name,studyRoomId ) {
    return request({
      url: '/studyroom/web/studyroom/updateInfo',
      method: 'PUT',
      params: {'announcement':announcement,'name':name,'studyRoomId':studyRoomId}
    })
  },

  // 学委点赞
  giveLike(committeeCode,studyRoomId,targetStudentCode) {
    return request({
      url: '/studyroom/web/studyroom/give/like',
      method: 'POST',
      params: {'committeeCode':committeeCode,'studyRoomId':studyRoomId,'targetStudentCode':targetStudentCode}
    })
  },

  // 获取固定班房间信息
  getScheduleRoomInfo(scheduleId) {
    return request({
      url: '/studyroom/webSchedule/scheduleViewRoomInfo',
      method: 'GET',
      params: {'scheduleId':scheduleId}
    })
  },

  leaveRoom(params) {
    return request({
      url: '/studyroom/room/exit',
      method: 'PUT',
      data: params
    })
  },
  // 学管师自习室调用接口
  selfstudyRoomApi(roomId,username,pageNum) {
    return request({
      url: '/xi/web/room/list',
      method: 'GET',
      params: {'roomId': roomId,'username':username,'pageNum':pageNum}
    })
  },
}
