/**
 * APP版本控制相关接口
 */
import request from '@/utils/request'

export default {
  // 获取app版本类型
  appType(){
    return request({
      url: '/znyy/appversion/get/appVersion',
      method: 'GET'
    })
  },
  // 分页查询
  appList(pageNum, pageSize, data) {
    return request({
      url: '/znyy/appversion/list/' + pageNum + '/' + pageSize,
      method: 'GET',
      params: data
    })
  },
  // 新增
  addApp(data) {
    return request({
      url: '/znyy/appversion',
      method: 'POST',
      data
    })
  },
  // 编辑
  updateApp(data) {
    return request({
      url: '/znyy/appversion',
      method: 'PUT',
      data
    })
  },
  // 修改回显
  queryActive(id) {
    return request({
      url: '/znyy/appversion/echo/' + id,
      method: 'GET'
    })
  },
  // 删除
  deleteApp(id) {
    return request({
      url: '/znyy/appversion/' + id,
      method: 'DELETE'
    })
  },
  // 批量删除
  batchDeleteApp(data) {
    return request({
      url: '/znyy/appversion/list',
      method: 'DELETE',
      data
    })
  },
  // 批量开通
  batchOpenApp(data) {
    return request({
      url: '/znyy/appversion/list/isEnable',
      method: 'PUT',
      data
    })
  },
  // 批量暂停
  batchPauseApp(data) {
    return request({
      url: '/znyy/appversion/list/pause',
      method: 'PUT',
      data
    })
  },
  // 开通与暂停
  updateStatus(id, status) {
    return request({
      url: '/znyy/agent/updateStatus?id=' + id + '&isEnable=' + status,
      method: 'PUT'
    })
  },
}
