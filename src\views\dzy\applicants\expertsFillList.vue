<template>
  <div class="app-container">
    <el-form :inline="true" class="container-card" label-width="110px" label-position="left">
      <el-form-item label="手机号：">
        <el-input v-model="dataQuery.phone" placeholder="请输入手机号：" clearable />
      </el-form-item>
      <el-form-item label="省份：">
        <el-input v-model="dataQuery.province" placeholder="请输入省份：" clearable />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="fetchData01()">搜索</el-button>
      </el-form-item>
    </el-form>


    <!-- 渲染表格 -->
    <el-table v-loading="tableLoading" class="common-table" :data="tableData" hstyle="width: 100%; margin-bottom: 20px"
      row-key="id" border default-expand-all :tree-props="{ list: 'children', hasChildren: 'true' }">
      <el-table-column prop="province" label="省份"  align="center"  width="100px" />
      <el-table-column prop="name" label="姓名"  align="center"  width="120px"/>
      <el-table-column prop="gender" label="性别"  align="center"  min-width="60px"/>
      <el-table-column prop="phone" label="手机号"  align="center"  width="120px"/>
      <el-table-column prop="examYear" label="高考年份" align="center" />
      <el-table-column prop="score" label="成绩" align="center"  />
      <el-table-column prop="report" label="报告" align="center" />
      <el-table-column prop="volunteerForm" label="志愿表" align="center" />
      <el-table-column prop="statusName" label="状态" align="center" />
      <el-table-column prop="teacher" label="老师" align="center" />
      <el-table-column prop="createTime" label="创建时间" align="center" />
    </el-table>

    <!-- 分页 -->
    <el-col :span="20">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]" layout="total, sizes, prev, pager, next, jumper"
        :total="tablePage.totalItems" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    </el-col>
  </div>
</template>

<script>
  import agentApi from "@/api/agentList";
  import {  pageParamNames } from "@/utils/constants";
  import {  ossPrClient  } from "@/api/alibaba";
  import promoterApi from "@/api/promoterList";
  import expertsFillApi from "@/api/expertsFillList"
  import ls from '@/api/sessionStorage'
  import { idCard } from '@/utils/validate'
  export default {
    data() {
      return {
        tableLoading: false,
        // 分页
        tablePage: {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null,
        },

        dataQuery: {
          phone: "",
          province: "",
        },
        tableData: [],

      };
    },
    created() {
      ossPrClient();
      this.fetchData01();
    },
    methods: {
        fetchData01(){
         this.tablePage= {
          currentPage: 1,
          size: this.tablePage.size,
          totalPage: null,
          totalItems: null
        }
        this.fetchData();
      },
      // 查询+搜索
      fetchData() {
        const that = this;
        that.tableLoading = true;

        expertsFillApi.expertsFillList(
            that.tablePage.currentPage,
            that.tablePage.size,
            that.dataQuery
          )
          .then((res) => {
            that.tableData = res.data.data;
            that.tableLoading = false;
            // 设置后台返回的分页参数
            pageParamNames.forEach((name) =>
              that.$set(that.tablePage, name, parseInt(res.data[name]))
            );
          });
      },

      // 进入添加页面
      clickAdd() {
        const that = this;
        that.addOrUpdate = true;
        ls.setItem('addOrUpdate', that.addOrUpdate);
        ls.removeItem('companyId')
        that.$router.push({
          path: "/merchantManagement/branchOfficeAdd",
          query: {
            addOrUpdate: that.addOrUpdate,
          },
        });
      },

      //审核理由
      open(chenckReason) {
        const h = this.$createElement;
        this.$msgbox({
          title: '审核理由',
          message: h('p', null, [
            h('i', { style: 'color: #FF0802' }, chenckReason)
          ]),
          showCancelButton: false,
          confirmButtonText: '确定'
        })
      },
      // 进入编辑页面
      update(id) {
        const that = this;
        that.addOrUpdate = false;
        ls.setItem('companyId', id);
        ls.setItem('addOrUpdate', that.addOrUpdate);

        that.$router.push({
          path: "/merchantManagement/branchOfficeAdd",
          query: {
            addOrUpdate: that.addOrUpdate,
            id: id,
          },
        });
      },

      // 分页
      handleSizeChange(val) {
        console.log(val);
        this.tablePage.size = val;
        this.fetchData();
      },
      handleCurrentChange(val) {
        this.tablePage.currentPage = val;
        this.fetchData();
      },



    },
  };
</script>

<style>
  .container-card {
    padding: 15px 15px 15px 15px;
    margin-bottom: 15px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  }
  @media (max-width:767px) {
	  .el-message-box{
		width: 80%!important;
	  }
  }
</style>
