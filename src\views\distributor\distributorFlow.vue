<template>

  <div class="app-container">

    <el-table v-loading="tableLoading" :data="tableData" stripe border style="width: 100%;margin-bottom: 30px;">

      <el-table-column prop="flowType" label="流水类型" align="center" />
      <el-table-column prop="commissionState" label="分润流水类型" align="center" />
      <el-table-column prop="flowAmount" label="分润金额" align="center" />
      <el-table-column prop="orderId" label="关联订单id" align="center" width="180"/>
      <el-table-column prop="outAccount" label="流出账户" align="center"  :show-overflow-tooltip="true"/>
      <el-table-column prop="outType" label="流出账户类型" align="center" />
      <el-table-column prop="inflowAccount" label="流入账户" align="center" :show-overflow-tooltip="true"/>
      <el-table-column prop="inflowType" label="流入账户类型" align="center" />
      <el-table-column prop="createTime" label="创建时间" align="center" :show-overflow-tooltip="true"/>

    </el-table>

    <!-- 分页 -->
    <el-col :span="20">
      <el-pagination
        :current-page="tablePage.currentPage"
        :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="tablePage.totalItems"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-col>

  </div>
</template>

<script>
import flowApi from '@/api/flow'
import {
  pageParamNames
} from '@/utils/constants'

export default {
  components: {},
  data() {
    return {
      tableLoading: false,

      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      tableData: [],

      dialogVisible: false, // 修改弹窗是否展示


    }
  },
  created() {
    this.fetchData()
  },
  methods: {
    // 获取UUid
    getUUid: function() {
      const s = []
      const hexDigits = '0123456789abcdef'
      for (let i = 0; i < 36; i++) {
        s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1)
      }
      s[14] = '4'
      s[19] = hexDigits.substr((s[19] & 0x3) | 0x8, 1)
      s[8] = s[13] = s[18] = s[23] = '-'
      const UUid = s.join('')
      return UUid
    },

    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val
      this.fetchData()
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val
      this.fetchData()
    },

    // 查询
    fetchData() {
      const that = this
      that.tableLoading = true
      flowApi.queryFlowPage(that.tablePage.currentPage, that.tablePage.size, this.$route.query.distributorCommissionId).then(res => {
        for (var i = 0; i < res.data.data.length; i++) {
          if (res.data.data[i].flowType === 'REFUND') {
            res.data.data[i].flowType = '退款'
          }
          if (res.data.data[i].flowType === 'COMMISSION') {
            res.data.data[i].flowType = '分润'
          }

          if (res.data.data[i].commissionState === 'DFR') {
            res.data.data[i].commissionState = '待分润'
          }
          if (res.data.data[i].commissionState === 'KTX') {
            res.data.data[i].commissionState = '可提现'
          }
          if (res.data.data[i].commissionState === 'YTX') {
            res.data.data[i].commissionState = '已提现'
          }
          if (res.data.data[i].outType === 'XCX') {
            res.data.data[i].outType = '小程序'
          }
          if (res.data.data[i].outType === 'TSY') {
            res.data.data[i].outType = '通商云'
          }
          if (res.data.data[i].outType === 'JXS') {
            res.data.data[i].outType = '经销商'
          }

          if (res.data.data[i].inflowType === 'XCX') {
            res.data.data[i].inflowType = '小程序'
          }
          if (res.data.data[i].inflowType === 'TSY') {
            res.data.data[i].inflowType = '通商云'
          }
          if (res.data.data[i].inflowType === 'JXS') {
            res.data.data[i].inflowType = '经销商'
          }
        }

        that.tableData = res.data.data
        that.tableLoading = false
        // 设置后台返回的分页参数
        pageParamNames.forEach(name => that.$set(that.tablePage, name, parseInt(res.data[name])))
      })
    }

  }
}

</script>

<style>
</style>
