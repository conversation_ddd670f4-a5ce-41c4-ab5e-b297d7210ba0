import request from '@/utils/request'

export default {
  // 分页查询
  list(data) {
    return request({
      url: '/xi/web/task/list',
      method: 'GET',
      params: data
    })
  },
  detail(id){
    return request({
      url: '/xi/web/task',
      method: 'GET',
      params:{
        id:id
      }
    })
  },
//添加
  saveOrUpdate(data) {
    return request({
      url: '/xi/web/task',
      method: 'POST',
      data
    })
  },
  changeEnable(id, enable) {
    return request({
      url: '/xi/web/task/changeEnable',
      method: 'PUT',
      params: {
        id: id,
        enable: enable
      }
    })
  },
  //全部任务
  allList() {
    return request({
      url: '/xi/web/task/allList',
      method: 'GET',
    })
  },
}
