/**
 * 分润级别接口
 */
import request from '@/utils/request'

export default {
  // 获取授权码类型
  pageList(data) {
    return request({
      url: '/znyy/userOptionLog/queryUserOptionLog',
      method: 'GET',
      params: data
    })
  },
  exportData(userName){
    return request({
      url: '/znyy/userOptionLog/exportData',
      method: 'GET',
      params: {
        userName: userName
      }
    })
  }
}
