<template>
  <div class="app-container">
    <el-row>
      <el-col :xs="24" :lg="18">
        <!-- 添加或修改弹窗 -->
        <el-form :ref="addOrUpdate ? 'addMarketDate' : 'updateMarketDate'" :rules="rules" :model="addOrUpdate ? addMarketDate : updateMarketDate" label-position="right" label-width="140px" style="width: 100%">
          <el-form-item label="登录账号：" prop="name">
            <template>
              <el-col :xs="24" :span="18">
                <el-input v-if="addOrUpdate" v-model="addMarketDate.name" />
                <el-input v-if="!addOrUpdate" v-model="updateMarketDate.name" disabled />
              </el-col>
              <el-button v-if="!addOrUpdate" type="success" style="margin-left: 20px" @click="openLogin(updateMarketDate.name, updateMarketDate.id)">修改登录账号
              </el-button>
            </template>
          </el-form-item>
          <el-form-item label="托管中心id：" prop="id" v-show="false">
            <el-col :xs="24" :span="18">
              <el-input v-if="addOrUpdate" v-model="addMarketDate.id" />
              <el-input v-if="!addOrUpdate" v-model="updateMarketDate.id" />
            </el-col>
          </el-form-item>
          <el-form-item label="托管中心名称：" prop="merchantName">
            <el-col :xs="24" :span="18">
              <el-input v-if="addOrUpdate" v-model="addMarketDate.merchantName" placeholder="请输入省+市+商圈/路名/学校名称+托管中心" />
              <el-input v-if="!addOrUpdate" v-model="updateMarketDate.merchantName" placeholder="请输入省+市+商圈/路名/学校名称+托管中心" />
              <span>（例：安徽省合肥市包河区平安国际金融托管中心）</span>
            </el-col>
          </el-form-item>
          <el-form-item label="总负责人：" prop="realName">
            <el-col :xs="24" :span="18">
              <el-input v-if="addOrUpdate" v-model="addMarketDate.realName" />
              <el-input v-if="!addOrUpdate" v-model="updateMarketDate.realName" />
            </el-col>
          </el-form-item>
          <el-row :span="24">
            <el-col :span="12">
              <el-form-item label="市场负责人" prop="marketChargePerson">
                <el-col :xs="24" :span="18">
                  <el-input v-if="addOrUpdate" v-model="addMarketDate.marketChargePerson" />
                  <el-input v-if="!addOrUpdate" v-model="updateMarketDate.marketChargePerson" />
                </el-col>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="市场负责人手机号" prop="marketChargePersonPhone">
                <el-col :xs="24" :span="18">
                  <el-input v-if="addOrUpdate" v-model="addMarketDate.marketChargePersonPhone" />
                  <el-input v-if="!addOrUpdate" v-model="updateMarketDate.marketChargePersonPhone" />
                </el-col>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :span="24">
            <el-col :span="12">
              <el-form-item label="咨询负责人" prop="consultingChargePerson">
                <el-col :xs="24" :span="18">
                  <el-input v-if="addOrUpdate" v-model="addMarketDate.consultingChargePerson" />
                  <el-input v-if="!addOrUpdate" v-model="updateMarketDate.consultingChargePerson" />
                </el-col>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="咨询负责人手机号" prop="consultingChargePersonPhone">
                <el-col :xs="24" :span="18">
                  <el-input v-if="addOrUpdate" v-model="addMarketDate.consultingChargePersonPhone" />
                  <el-input v-if="!addOrUpdate" v-model="updateMarketDate.consultingChargePersonPhone" />
                </el-col>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :span="24">
            <el-col :span="12">
              <el-form-item label="教学负责人" prop="teachingChargePerson">
                <el-col :xs="24" :span="18">
                  <el-input v-if="addOrUpdate" v-model="addMarketDate.teachingChargePerson" />
                  <el-input v-if="!addOrUpdate" v-model="updateMarketDate.teachingChargePerson" />
                </el-col>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="教学负责人手机号" prop="teachingChargePersonPhone">
                <el-col :xs="24" :span="18">
                  <el-input v-if="addOrUpdate" v-model="addMarketDate.teachingChargePersonPhone" />
                  <el-input v-if="!addOrUpdate" v-model="updateMarketDate.teachingChargePersonPhone" />
                </el-col>
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item label="负责人身份证：" prop="idCard">
            <el-col :xs="24" :span="18">
              <el-input v-if="addOrUpdate" v-model="addMarketDate.idCard" />
              <el-input v-if="!addOrUpdate" v-model="updateMarketDate.idCard" />
            </el-col>
          </el-form-item>
          <!--          <el-form-item label="上级编号：" prop="refereeCode">
            <el-col :xs="24" :span="18">
              <el-input
                v-if="addOrUpdate"
                v-model="addMarketDate.refereeCode"
              />
              <el-input
                v-if="!addOrUpdate"
                v-model="updateMarketDate.refereeCode"
              />
            </el-col>
          </el-form-item>-->
          <el-form-item label="推荐人编号：" prop="marketPartner">
            <el-col :xs="24" :span="18">
              <el-input v-if="addOrUpdate" v-model="addMarketDate.marketPartner" />
              <el-input v-if="!addOrUpdate" v-model="updateMarketDate.marketPartner" />
            </el-col>
          </el-form-item>
          <el-form-item label="开户一级分润比例：">
            <el-col :xs="24" :span="18">
              <el-input v-if="addOrUpdate" v-model="addMarketDate.profitRankOne" />
              <el-input v-else v-model="updateMarketDate.profitRankOne" />
            </el-col>
          </el-form-item>
          <el-form-item label="开户二级分润比例：">
            <el-col :xs="24" :span="18">
              <el-input v-if="addOrUpdate" v-model="addMarketDate.profitRankTwo" />
              <el-input v-else v-model="updateMarketDate.profitRankTwo" />
            </el-col>
          </el-form-item>
          <el-form-item label="续费一级分润比例：">
            <el-col :xs="24" :span="18">
              <el-input v-if="addOrUpdate" v-model="addMarketDate.reProfitRankOne" />
              <el-input v-else v-model="updateMarketDate.reProfitRankOne" />
            </el-col>
          </el-form-item>
          <el-form-item label="续费二级分润比例：">
            <el-col :xs="24" :span="18">
              <el-input v-if="addOrUpdate" v-model="addMarketDate.reProfitRankTwo" />
              <el-input v-else v-model="updateMarketDate.reProfitRankTwo" />
            </el-col>
          </el-form-item>
          <el-form-item label="二级分润：" prop="reProfitRank">
            <template>
              <el-radio v-if="addOrUpdate" v-model="addMarketDate.reProfitRank" label="1">开通
              </el-radio>
              <el-radio v-if="addOrUpdate" v-model="addMarketDate.reProfitRank" label="0">暂停
              </el-radio>
              <el-radio v-if="!addOrUpdate" v-model="updateMarketDate.reProfitRank" label="1">开通
              </el-radio>
              <el-radio v-if="!addOrUpdate" v-model="updateMarketDate.reProfitRank" label="0">暂停
              </el-radio>
            </template>
          </el-form-item>

          <el-form-item label="提现银行：" prop="openBank">
            <el-col :xs="24" :span="18">
              <el-select style="width: 100%" v-if="addOrUpdate" v-model="addMarketDate.openBank" filterable value-key="value" placeholder="请选择">
                <el-option v-for="(item, index) in bankType" :key="index + '-only'" :label="item.label" :value="item.value" />
              </el-select>
              <el-select style="width: 100%" v-if="!addOrUpdate" v-model="updateMarketDate.openBank" filterable value-key="value" placeholder="请选择">
                <el-option v-for="(item, index) in bankType" :key="index + '-only'" :label="item.label" :value="item.value" />
              </el-select>
            </el-col>
          </el-form-item>
          <el-form-item label="教练 人数：" prop="assistantPersons">
            <el-col :xs="24" :span="18">
              <el-input v-if="addOrUpdate" v-model="addMarketDate.assistantPersons" oninput="value=value.replace(/[^\d]/g,'')" />
              <el-input v-if="!addOrUpdate" v-model="updateMarketDate.assistantPersons" oninput="value=value.replace(/[^\d]/g,'')" />
            </el-col>
          </el-form-item>

          <el-form-item label="提现账户：" prop="bankNoName">
            <el-col :xs="24" :span="18">
              <el-input v-if="addOrUpdate" v-model="addMarketDate.bankNoName" />
              <el-input v-if="!addOrUpdate" v-model="updateMarketDate.bankNoName" />
            </el-col>
          </el-form-item>
          <el-form-item label="提现账号：" prop="bankNo">
            <el-col :xs="24" :span="18">
              <el-input v-if="addOrUpdate" v-model="addMarketDate.bankNo" />
              <el-input v-if="!addOrUpdate" v-model="updateMarketDate.bankNo" />
            </el-col>
          </el-form-item>
          <el-form-item label="托管中心级别：" prop="rank">
            <el-col :xs="24" :span="18">
              <el-select style="width: 100%" v-if="addOrUpdate" v-model="addMarketDate.rank" filterable value-key="value" placeholder="请选择">
                <el-option v-for="(item, index) in rankType" :key="index + '-o1nly'" :disabled="
                    forbidRankType.findIndex((t) => t === item.value) !== -1
                  " :label="item.label" :value="item.value" />
              </el-select>
              <el-select style="width: 100%" v-if="!addOrUpdate" v-model="updateMarketDate.rank" filterable value-key="value" placeholder="请选择">
                <el-option v-for="(item, index) in rankType" :key="index + '-o2nly'" :disabled="
                    forbidRankType.findIndex((t) => t === item.value) !== -1
                  " :label="item.label" :value="item.value" />
              </el-select>
            </el-col>
          </el-form-item>
          <el-form-item label="商户类型：" prop="merchantType">
            <template>
              <el-radio v-if="addOrUpdate" v-model="radio" @change="changeRadio(radio)" label="3">个人
              </el-radio>
              <el-radio v-if="!addOrUpdate" v-model="radio" @change="changeupDate(radio)" label="3">个人
              </el-radio>
              <el-radio v-if="addOrUpdate" v-model="radio" @change="changeRadio(radio)" label="2">企业
              </el-radio>
              <el-radio v-if="!addOrUpdate" v-model="radio" @change="changeupDate(radio)" label="2">企业
              </el-radio>
            </template>
          </el-form-item>
          <el-form-item label="是否完款：" prop="paymentIsComplete" v-if="roleTag === 'admin'">
            <template>
              <el-radio v-if="!addOrUpdate" @change="
                  changePaymentIsComplete(updateMarketDate.paymentIsComplete)
                " v-model="updateMarketDate.paymentIsComplete" label="1">完款
              </el-radio>
              <el-radio v-if="!addOrUpdate" @change="
                  changePaymentIsComplete(updateMarketDate.paymentIsComplete)
                " v-model="updateMarketDate.paymentIsComplete" label="0">未完款
              </el-radio>
              <el-radio v-if="addOrUpdate" @change="
                  changePaymentIsComplete(addMarketDate.paymentIsComplete)
                " v-model="addMarketDate.paymentIsComplete" label="1">完款
              </el-radio>
              <el-radio v-if="addOrUpdate" @change="
                  changePaymentIsComplete(addMarketDate.paymentIsComplete)
                " v-model="addMarketDate.paymentIsComplete" label="0">未完款
              </el-radio>
            </template>
          </el-form-item>
          <el-form-item v-if="showPrepaidMoney" label="上级编号：" prop="refereeCode">
            <el-col :xs="24" :span="18">
              <el-input v-if="addOrUpdate" v-model="addMarketDate.refereeCode" />
              <el-input v-else v-model="updateMarketDate.refereeCode" />
            </el-col>
          </el-form-item>
          <el-form-item v-if="showPrepaidMoney" label="定金：" prop="prepaidMoney">
            <el-col :xs="24" :span="18">
              <el-input v-if="addOrUpdate" v-model="addMarketDate.prepaidMoney" type="number" maxlength="10" isNumber="true" min="1" />
              <el-input v-else v-model="updateMarketDate.prepaidMoney" type="number" maxlength="10" isNumber="true" min="1" />
            </el-col>
          </el-form-item>
          <el-form-item label="开户金额：" prop="openMoney">
            <el-col :xs="24" :span="18">
              <el-input v-if="addOrUpdate" v-model="addMarketDate.openMoney" type="number" maxlength="10" isNumber="true" min="30000" />
              <el-input v-if="!addOrUpdate" v-model="updateMarketDate.openMoney" type="number" maxlength="10" isNumber="true" min="30000" />
            </el-col>
          </el-form-item>
          <el-form-item label="场地类型" prop="siteType">
            <el-col :span="18">
              <el-input v-if="addOrUpdate" v-model="addMarketDate.siteType" placeholder="请输入商铺/住宅/写字楼/其他" />
              <el-input v-if="!addOrUpdate" v-model="updateMarketDate.siteType" placeholder="请输入商铺/住宅/写字楼/其他" />
            </el-col>
          </el-form-item>
          <el-form-item label="场地面积" prop="siteArea">
            <el-col :span="18">
              <el-input v-if="addOrUpdate" v-model="addMarketDate.siteArea" oninput="value=value.replace(/[^\d]/g,'')" />
              <el-input v-if="!addOrUpdate" v-model="updateMarketDate.siteArea" oninput="value=value.replace(/[^\d]/g,'')" />
            </el-col>
            <el-col :span="5" :offset="1">米</el-col>
          </el-form-item>
          <el-form-item label="楼层数" prop="floorNum">
            <el-col :span="18">
              <el-input v-if="addOrUpdate" v-model="addMarketDate.floorNum" oninput="value=value.replace(/[^\d]/g,'')" />
              <el-input v-if="!addOrUpdate" v-model="updateMarketDate.floorNum" oninput="value=value.replace(/[^\d]/g,'')" />
            </el-col>
            <el-col :span="5" :offset="1">层</el-col>
          </el-form-item>
          <el-form-item label="范围半径：" prop="areaCoverRange">
            <el-col :span="18">
              <el-input v-if="addOrUpdate" v-model="addMarketDate.areaCoverRange" oninput="value=value.replace(/[^\d]/g,'')" />
              <el-input v-if="!addOrUpdate" v-model="updateMarketDate.areaCoverRange" oninput="value=value.replace(/[^\d]/g,'')" />
            </el-col>
            <el-col :span="5" :offset="1">米</el-col>
          </el-form-item>
          <el-form-item label="签约时间：" prop="signupDate">
            <el-col :xs="24" :span="18">
              <el-date-picker v-if="addOrUpdate" v-model="addMarketDate.signupDate" type="date" value-format="yyyy-MM-dd HH:mm:ss" placeholder="选择日期">
              </el-date-picker>
              <el-date-picker v-if="!addOrUpdate" v-model="updateMarketDate.signupDate" type="date" value-format="yyyy-MM-dd HH:mm:ss" placeholder="选择日期">
              </el-date-picker>
            </el-col>
          </el-form-item>
          <el-form-item label="到期时间：" prop="expireDate">
            <el-col :xs="24" :span="18">
              <el-date-picker v-if="addOrUpdate" v-model="addMarketDate.expireDate" type="date" value-format="yyyy-MM-dd HH:mm:ss" @change="changeTime(addMarketDate.expireDate)" placeholder="选择日期">
              </el-date-picker>
              <el-date-picker v-if="!addOrUpdate" v-model="updateMarketDate.expireDate" type="date" value-format="yyyy-MM-dd HH:mm:ss" placeholder="选择日期">
              </el-date-picker>
            </el-col>
          </el-form-item>
          <!-- 1 11111111111111111111111111111111111111111-->
          <el-form-item label="合同照片:" prop="contractPhoto">
            <el-col :span="20">
              <el-upload ref="clearupload" v-loading="uploadLoading" list-type="picture-card" action="" element-loading-text="图片上传中" :limit="10" :on-exceed="justPictureNum" :file-list="!addOrUpdate ? fileDetailList : fileDetailList" :http-request="uploadDetailHttp" :on-preview="handlePictureCardPreview" :on-remove="handleRemoveDetailContract">
                <i class="el-icon-plus" />
              </el-upload>
            </el-col>
            <el-col :xs="24" :span="4">(*支持多张)</el-col>
          </el-form-item>
          <!-- 11111111111111111111111111111111111111111111111111 -->
          <el-form-item label="证件照片:" prop="idCardPhoto">
            <el-col :span="20">
              <el-upload ref="clearupload" v-loading="uploadLoadingIdCard" list-type="picture-card" action="" element-loading-text="图片上传中" :limit="10" :on-exceed="justPictureNumIdCard" :file-list="!addOrUpdate ? fileIdCard : fileIdCard" :http-request="uploadIdCardDetailHttp" :on-preview="handlePictureCardPreview" :on-remove="handleRemoveDetailIdCard">
                <i class="el-icon-plus" />
              </el-upload>
            </el-col>
            <el-col :xs="24" :span="4">(*支持多张)</el-col>
          </el-form-item>
          <el-form-item ref="file_Rule" label="付款记录：" prop="paymentPhoto">
            <el-col :xs="24" :span="20">
              <el-upload ref="clearupload" v-loading="uploadLoading4" multiple list-type="picture-card" action="" element-loading-text="图片上传中" :limit="8" :on-exceed="justPictureNum" :file-list="
                  !addOrUpdate ? fileDetailList4 : fileDetailList4.name
                " :http-request="uploadDetailHttp4" :on-preview="handlePictureCardPreview" :on-remove="handleRemoveDetail4">
                <i class="el-icon-plus" />
              </el-upload>
            </el-col>
            <el-col :xs="24" :span="4">(*支持多张)</el-col>
          </el-form-item>
          <el-form-item label="门店照片:" prop="">
            <el-col :span="20">
              <el-upload ref="clearupload" v-loading="uploadLoadingShop" list-type="picture-card" action="" element-loading-text="图片上传中" :limit="10" :on-exceed="justPictureNumShop" :file-list="!addOrUpdate ? filelistShop : filelistShop" :http-request="uploadDetailHttpShop" :on-preview="handlePictureCardPreview" :on-remove="handleRemoveDetailShop">
                <i class="el-icon-plus" />
              </el-upload>
            </el-col>
            <el-col :xs="24" :span="4">(*支持多张)</el-col>
          </el-form-item>
          <el-form-item label="所在地区：" prop="province">
            <el-col :xs="24" :span="18">
              <el-cascader style="width: 300px" :options="regionData" v-model="selectedOptions" :props="{ value: 'label' }">
              </el-cascader>
              <!-- <el-row :gutter="10">
                <el-col :xs="24" :span="8">
                  <el-input placeholder="安徽省" v-if="addOrUpdate" disabled v-model="addMarketDate.province" />
                  <el-input placeholder="安徽省" v-if="!addOrUpdate" disabled v-model="updateMarketDate.province" />
                </el-col>
                <el-col :xs="24" :span="8">
                  <el-input placeholder="合肥市" v-if="addOrUpdate" disabled v-model="addMarketDate.city" />
                  <el-input placeholder="合肥市" v-if="!addOrUpdate" disabled v-model="updateMarketDate.city" />
                </el-col>
                <el-col :xs="24" :span="8">
                  <el-input placeholder="包河区" v-if="addOrUpdate" disabled v-model="addMarketDate.area" />
                  <el-input placeholder="包河区" v-if="!addOrUpdate" disabled v-model="updateMarketDate.area" />
                </el-col>
              </el-row> -->
            </el-col>
          </el-form-item>
          <el-row :gutter="10">
            <!-- <el-form-item label="所在省份" prop="province">
          <el-col :span="7">
            <el-input v-if="addOrUpdate" v-model="addMarketDate.province" />
            <el-input v-if="!addOrUpdate" v-model="updateMarketDate.province" />
          </el-col>
           </el-form-item>
          <el-form-item label="所在市" prop="city">
          <el-col :span="7">
            <el-input v-if="addOrUpdate" v-model="addMarketDate.city" />
            <el-input v-if="!addOrUpdate" v-model="updateMarketDate.city" />
          </el-col>
           </el-form-item>
          <el-form-item label="所在区" prop="area">
          <el-col :span="7">
            <el-input v-if="addOrUpdate" v-model="addMarketDate.area" />
            <el-input v-if="!addOrUpdate" v-model="updateMarketDate.area" />
          </el-col>
           </el-form-item> -->
            <!-- <el-form-item label="所在经度：" prop="longitude" v-show="false">
              <el-col :span="7">
                <el-input v-if="addOrUpdate" v-model="addMarketDate.longitude" />
                <el-input v-if="!addOrUpdate" v-model="updateMarketDate.longitude" />
              </el-col>
            </el-form-item>
            <el-form-item label="所在维度：" prop="latitude" v-show="false">
              <el-col :span="7">
                <el-input v-if="addOrUpdate" v-model="addMarketDate.latitude" />
                <el-input v-if="!addOrUpdate" v-model="updateMarketDate.latitude" />
              </el-col>
            </el-form-item> -->
          </el-row>
          <el-form-item label="地址：" prop="address">
            <el-col :xs="24" :span="18">
              <el-input v-if="addOrUpdate" v-model="address" v-show="false" />
              <el-input v-if="!addOrUpdate" v-model="address" v-show="false" />
              <el-input v-if="addOrUpdate" v-model="addMarketDate.address" />
              <el-input v-if="!addOrUpdate" v-model="updateMarketDate.address" />
            </el-col>
            <!-- <el-col :span="11" :offset="1">
          <el-button type="success" size="mini">确定</el-button>
          <el-button type="info" size="mini">取消</el-button>
          <span>(请在此输入详细地址！)</span>
        </el-col> -->
          </el-form-item>
          <!--          <el-form-item label="详细地址" prop="detailedAddress">
            <el-col :xs="24" :span="18">
              <el-input
                v-if="addOrUpdate"
                v-model="addMarketDate.detailedAddress"
              />
              <el-input
                v-if="!addOrUpdate"
                v-model="updateMarketDate.detailedAddress"
              />
            </el-col>
          </el-form-item>-->
          <!-- <el-form-item label="地图标记：" prop="isEnable">
            <div class="amap-page-container">
              <div :style="{ width: '100%', height: '450px' }" class="map-box">
                <el-amap-search-box class="search-box" :search-option="searchOption"
                  :on-search-result="onSearchResult"></el-amap-search-box>
                <el-amap vid="amap" :plugin="plugin" :center="center" class="amap-demo" :events="events">
                  <el-amap-circle v-for="(circle, index) in circles" :key="index" :center="circle.center"
                    :radius="circle.radius" :fill-opacity="circle.fillOpacity" :events="circle.events"></el-amap-circle>
                  <el-amap-info-window :position="currentWindow.position" :content="currentWindow.content"
                    :visible="currentWindow.visible" :events="currentWindow.events">
                  </el-amap-info-window>
                 
                  <el-amap-marker vid="component-marker" :position="center"></el-amap-marker>
                 
                  <el-amap-marker v-for="(marker, index) in markers" :position="marker.position" :key="index + 'flag'"
                    :icon="marker.icon">
                  </el-amap-marker>
                 
                  <el-amap-marker v-for="(marker, index) in markers2" :key="index" :position="marker.position"
                    :events="marker.events"></el-amap-marker>
                  <el-amap-info-window v-if="window" :position="window.position" :visible="window.visible"
                    :content="window.content"></el-amap-info-window>
                </el-amap>
               
                <div class="result" v-if="result != ''">
                  <el-table class="search-table" :data="
                                result.slice(
                                  (this.pageNum - 1) * this.pageSize,
                                  this.pageNum * this.pageSize
                                )
                              " style="margin-bottom: 20px">
                    <el-table-column prop="name,address">
                      <template slot-scope="scope">
                        <div class="result-list" @click="
                                      markList(scope.row.lng, scope.row.lat, scope.$index)
                                    " :class="[
                                      currentResult == scope.$index ? 'active' : '',
                                    ]">
                          <label>{{ scope.$index + 1 }}</label>
                          <div class="list-right">
                            <div class="name">{{ scope.row.name }}</div>
                            <div class="address">{{ scope.row.address }}</div>
                          </div>
                        </div>
                      </template>
                    </el-table-column>
                  </el-table>
                  <el-pagination background :current-page.sync="pageNum" :page-size="pageSize"
                    layout="total,prev, pager, next" :total="result.length" @current-change="handleCurrentChange"
                    @size-change="changeSizeHandler" size="small" style="text-align: right">
                  </el-pagination>
                </div>
              </div>
            </div>
          </el-form-item> -->

          <el-form-item label="代理简介：" prop="description">
            <el-col :xs="24" :span="18">
              <el-input type="textarea" resize="none" :rows="4" v-if="addOrUpdate" v-model="addMarketDate.description" />
              <el-input type="textarea" resize="none" :rows="4" v-if="!addOrUpdate" v-model="updateMarketDate.description" />
            </el-col>
          </el-form-item>
          <el-form-item label="周边资源：" prop="periphery">
            <el-col :xs="24" :span="18">
              <el-input type="textarea" resize="none" :rows="4" v-if="addOrUpdate" v-model="addMarketDate.periphery" />
              <el-input type="textarea" resize="none" :rows="4" v-if="!addOrUpdate" v-model="updateMarketDate.periphery" />
            </el-col>
          </el-form-item>
        </el-form>
      </el-col>
    </el-row>

    <div slot="footer" class="dialog-footer" style="margin: 30px 0 30px 140px">
      <el-button v-if="addOrUpdate" size="mini" type="primary" @click="addActiveFun('addMarketDate')">新增
      </el-button>
      <el-button v-if="
          !addOrUpdate &&
          (updateMarketDate.isCheck == 0 ||
            updateMarketDate.isCheck == -4 ||
            updateMarketDate.isCheck == 2 ||
            (merchantCode = 'A0001')) &&
          !isResubmit
        " size="mini" type="primary" @click="updateActiveFun('updateMarketDate', false)">修改
      </el-button>
      <el-button v-if="isResubmit" size="mini" type="primary" @click="updateActiveFun('updateMarketDate', true)">重提
      </el-button>
      <el-button size="mini" @click="close">关闭</el-button>
    </div>
    <el-dialog :visible.sync="dialogUploadVisible" :close-on-click-modal="false">
      <img width="100%" :src="dialogImageUrl" alt />
    </el-dialog>
    <!-- 修改登陆账号弹窗 -->
    <el-dialog title="修改登录账号" :visible.sync="showLoginName" width="30%" :close-on-click-modal="false" @close="closeLoginname">
      <el-form :ref="'updateLoginName'" :rules="rulesLoginName" :model="updateLoginName" label-position="left" label-width="120px" style="width: 80%">
        <el-form-item label="原登录账号：" prop="oldName">
          <span> {{ updateLoginName.oldName }}</span>
        </el-form-item>
        <el-form-item label="id:" prop="id" v-show="false">
          <el-input v-model="updateLoginName.id" />
        </el-form-item>
        <el-form-item label="新登录账号：" prop="name">
          <el-input v-model="updateLoginName.name" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="updateDealerLoginName('updateLoginName')">确定
        </el-button>
        <el-button size="mini" @click="closeLoginname">关闭</el-button>
      </div>
    </el-dialog>
    <!-- 审核弹框 -->
  </div>
</template>

<script>
import { regionData } from "element-china-area-data";
import dealerListApi from "@/api/dealerList";
import schoolApi from "@/api/areasSchoolList";
import enTypes from "@/api/bstatus";
import marketApi from "@/api/marketList";
import { ossPrClient } from "@/api/alibaba";
import { isvalidPhone, idCard } from "@/utils/validate";

export default {
  //name: 'dealerAdd',

  data() {
    //手机号验证
    var validPhone = (rule, value, callback) => {
      if (!value) {
        callback(new Error("请输入电话号码"));
      } else if (!isvalidPhone(value)) {
        callback(new Error("请输入正确的11位手机号码"));
      } else {
        callback();
      }
    };
    //身份证表达式
    var isIdCard = (rule, value, callback) => {
      if (!value) {
        callback(new Error("请输入身份证号码"));
      } else if (!idCard(value)) {
        callback(new Error("请输入正确的18位身份证号"));
      } else {
        callback();
      }
    };
    const self = this;
    return {
      roleTag: "",
      showPrepaidMoney: false,
      forbidRankType: ["A", "B", "C"],
      // 地图搜索分页相关参数
      pageNum: 1,
      pageSize: 5,
      result: [],
      currentResult: -1,
      isResubmit: false,
      regionData,
      selectedOptions: [],
      disabled: true,
      tableLoading: false,
      showLoginName: false, //登录账号
      updateLoginName: {}, //修改账号
      rulesLoginName: {
        name: [
          {
            required: true,
            message: "必填",
            trigger: "change",
          },
          {
            validator: validPhone,
            trigger: "blur",
          },
        ],
      },
      id: 0,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null,
      },
      ADD: true,
      tableData: [],
      dataQuery: {},
      dialogVisible: false,
      updateMarketDate: {
        areaCoverRange: 500,
        merchantType: "3",
      }, //修改数据对象
      addMarketDate: {
        areaCoverRange: 500,
        merchantType: "3",
        paymentIsComplete: "1",
      }, //增加数据对象
      showLoginAccount: false,
      addOrUpdate: true,
      rules: {
        name: [
          {
            required: true,
            message: "必填",
            trigger: "blur",
          },
          {
            validator: validPhone,
            trigger: "blur",
          },
        ],
        merchantName: [
          {
            required: true,
            message: "必填",
            trigger: "blur",
          },
        ],
        realName: [
          {
            required: true,
            message: "必填",
            trigger: "blur",
          },
        ],
        idCard: [
          {
            required: true,
            message: "必填",
            trigger: "blur",
          },
        ],
        paymentIsComplete: [
          {
            required: true,
            message: "必填",
            trigger: "blur",
          },
        ],
        prepaidMoney: [
          {
            required: true,
            message: "请填写定金",
            trigger: "blur",
          },
        ],
        openMoney: [
          {
            required: true,
            message: "必填",
            trigger: "blur",
          },
        ],
        rank: [
          {
            required: true,
            message: "必填",
            trigger: "blur",
          },
        ],
        // merchantType: [
        //   {
        //     required: true,
        //     message: "必填",
        //     trigger: "change",
        //   },
        // ],
        areaCoverRange: [
          {
            required: true,
            message: "必填",
            trigger: "change",
          },
        ],
        signupDate: [
          {
            required: true,
            message: "必填",
            trigger: "change",
          },
        ],
        expireDate: [
          {
            required: true,
            message: "必填",
            trigger: "change",
          },
        ],

        contractPhoto: [
          {
            required: true,
            message: "必填",
            trigger: "change",
          },
        ],
        idCardPhoto: [
          {
            required: true,
            message: "必填",
            trigger: "change",
          },
        ],
        shopPhoto: [
          {
            required: true,
            message: "必填",
            trigger: "change",
          },
        ],
        paymentPhoto: [
          {
            required: true,
            message: "必填",
            trigger: "change",
          },
        ],
        province: [
          {
            required: true,
            message: "请选择从地图选择省市县",
            trigger: "blur",
          },
        ],
        city: [
          {
            required: true,
            message: "必填",
            trigger: "change",
          },
        ],
        area: [
          {
            required: true,
            message: "必填",
            trigger: "change",
          },
        ],
        address: [
          {
            required: true,
            message: "必填",
            trigger: "change",
          },
        ],
        description: [
          {
            required: true,
            message: "必填",
            trigger: "change",
          },
        ],
      },
      value1: "",
      fileListPending: [], // 待处理已上传图片信息
      fileList: [], // 上传图片已有图片列表
      radio: "3",
      fileDetailListPending: [], // 待处理已上传图片信息
      fileDetailList: [], // 上传图片已有图片列表
      rankType: [], //托管中心级别
      uploadLoading: false, // 上传图片加载按钮
      name: "",
      bankType: [], //银行集合
      dialogUploadVisible: false,
      dialogImageUrl: "",
      //证件照图片
      fileIdCard: [],
      uploadLoadingIdCard: false,
      //证件照结束
      uploadLoadingShop: false,
      //支付记录
      uploadLoading4: false,
      // address: "",
      filelistShop: [],
      //支付图片
      fileDetailList4: [],
      merchantCode: "",
      //门店照开始
      //门店照结束
      //地图开始
      slotWindow: {
        position: [121.5163285, 31.********],
      },
      currentWindow: {
        position: [0, 0],
        content: "",
        events: {},
        visible: false,
      },
      addDealerShow: true,
      addDealerShow01: true,
      addDealerShow02: true,
      markers: [
        // [121.59996, 31.197646],
        // [121.40018, 31.197622],
        // [121.69991, 31.207649]
      ],
      //搜索结果标注
      markers2: [],
      windows: [],
      window: "",
      searchOption: {
        city: "全国",
        citylimit: false, //是否限制城市内搜索
      },
      center: [117.26696, 31.87869],
      zoom: 12,
      lng: 0,
      lat: 0,
      address: "",
      province: "",
      city: "",
      district: "",
      loaded: false,
      circles: [
        {
          center: [117.26696, 31.87869],
          radius: 100,
          fillOpacity: 0.5,
          events: {
            click(e) {
              let { lng, lat } = e.lnglat;
              self.lng = lng;
              self.lat = lat;
              console.log(e);
              // 这里通过高德 SDK 完成。
            },
          },
        },
      ],
      events: {
        click(e) {
          let { lng, lat } = e.lnglat;
          self.lng = lng;
          self.lat = lat;
          console.log(e);
          // 这里通过高德 SDK 完成。
          var geocoder = new AMap.Geocoder({
            radius: 1000,
            extensions: "all",
          });
          dealerListApi
            .loadOtherExperienceStores(self.lat, self.lng)
            .then((res) => {
              if (res.data.length > 0) {
                res.data.forEach((val) => {
                  if (val.distance < 1000) {
                    self.currentWindow = {
                      position: [self.lng, self.lat],
                      content: "此区域与其他托管中心范围重合，请重新设置",
                      visible: true,
                    };
                    self.showWindow = true;
                  }
                });
                if (!self.showWindow) {
                  geocoder.getAddress(
                    [e.lnglat.lng, e.lnglat.lat],
                    function (status, result) {
                      if (status === "complete" && result.info === "OK") {
                        if (result && result.regeocode) {
                          if (self.addOrUpdate) {
                            // 具体地址
                            self.addMarketDate.longitude = lng;
                            self.addMarketDate.latitude = lat;
                            self.addMarketDate.address =
                              result.regeocode.formattedAddress;
                            console.log(self.addMarketDate.address);
                            self.address = result.regeocode.formattedAddress;
                            // 省
                            self.addMarketDate.province =
                              result.regeocode.addressComponent.province;
                            // 市
                            self.addMarketDate.city =
                              result.regeocode.addressComponent.city;
                            // 区
                            self.addMarketDate.area =
                              result.regeocode.addressComponent.district;
                          } else {
                            self.updateMarketDate.latitude = lat;
                            self.updateMarketDate.longitude = lng;
                            self.updateMarketDate.address =
                              result.regeocode.formattedAddress;
                            self.address = result.regeocode.formattedAddress;
                            // 省
                            self.updateMarketDate.province =
                              result.regeocode.addressComponent.province;
                            // 市
                            self.updateMarketDate.city =
                              result.regeocode.addressComponent.city;
                            // 区
                            self.updateMarketDate.area =
                              result.regeocode.addressComponent.district;
                          }
                          self.$nextTick();
                        }
                      } else {
                        alert("地址获取失败");
                      }
                    }
                  );
                }
              } else {
                self.showWindow = false;
                geocoder.getAddress(
                  [e.lnglat.lng, e.lnglat.lat],
                  function (status, result) {
                    if (status === "complete" && result.info === "OK") {
                      if (result && result.regeocode) {
                        if (self.addOrUpdate) {
                          // 具体地址
                          self.addMarketDate.longitude = lng;
                          self.addMarketDate.latitude = lat;
                          self.addMarketDate.address =
                            result.regeocode.formattedAddress;
                          self.address = result.regeocode.formattedAddress;
                          // 省
                          self.addMarketDate.province =
                            result.regeocode.addressComponent.province;
                          //       if(result.regeocode.addressComponent.province=='重庆市' || result.regeocode.addressComponent.province=='天津市' || result.regeocode.addressComponent.province=='北京市' || result.regeocode.addressComponent.province=='上海市'){
                          //    self.addMarketDate.city =
                          //   result.regeocode.addressComponent.province;
                          //  }else{
                          // // 市
                          //  self.addMarketDate.city =
                          //   result.regeocode.addressComponent.city;
                          //  }
                          var reg = RegExp(/省/);
                          if (
                            self.addMarketDate.province.match(reg) &&
                            result.regeocode.addressComponent.city == ""
                          ) {
                            self.addMarketDate.city =
                              result.regeocode.addressComponent.district;
                          } else {
                            if (
                              result.regeocode.addressComponent.province ==
                              "重庆市" ||
                              result.regeocode.addressComponent.province ==
                              "天津市" ||
                              result.regeocode.addressComponent.province ==
                              "北京市" ||
                              result.regeocode.addressComponent.province ==
                              "上海市"
                            ) {
                              self.addMarketDate.city =
                                result.regeocode.addressComponent.province;
                            } else {
                              // 市
                              self.addMarketDate.city =
                                result.regeocode.addressComponent.city;
                            }
                          }
                          // if (result.regeocode.addressComponent.city == "") {
                          //   self.addMarketDate.city =
                          //     result.regeocode.addressComponent.province;
                          // }
                          // // 市
                          // self.addMarketDate.city =
                          //   result.regeocode.addressComponent.city;
                          // 区
                          self.addMarketDate.area =
                            result.regeocode.addressComponent.district;
                        } else {
                          self.updateMarketDate.latitude = lat;
                          self.updateMarketDate.longitude = lng;
                          self.updateMarketDate.address =
                            result.regeocode.formattedAddress;
                          self.address = result.regeocode.formattedAddress;
                          // 省
                          self.updateMarketDate.province =
                            result.regeocode.addressComponent.province;

                          var reg = RegExp(/省/);
                          if (
                            self.updateMarketDate.province.match(reg) &&
                            result.regeocode.addressComponent.city == ""
                          ) {
                            self.updateMarketDate.city =
                              result.regeocode.addressComponent.district;
                          } else {
                            if (
                              result.regeocode.addressComponent.province ==
                              "重庆市" ||
                              result.regeocode.addressComponent.province ==
                              "天津市" ||
                              result.regeocode.addressComponent.province ==
                              "北京市" ||
                              result.regeocode.addressComponent.province ==
                              "上海市"
                            ) {
                              self.updateMarketDate.city =
                                result.regeocode.addressComponent.province;
                            } else {
                              // 市
                              self.updateMarketDate.city =
                                result.regeocode.addressComponent.city;
                            }
                          }
                          // if (result.regeocode.addressComponent.city == "") {
                          //   self.updateMarketDate.city =
                          //     result.regeocode.addressComponent.province;
                          // }
                          // // 市
                          // self.updateMarketDate.city =
                          //   result.regeocode.addressComponent.city;
                          // 区
                          self.updateMarketDate.area =
                            result.regeocode.addressComponent.district;
                        }
                        self.$nextTick();
                      }
                    } else {
                      alert("地址获取失败");
                    }
                  }
                );
              }
            });
        },
      },
      init: (o) => {
        this.$nextTick(() => {
          // 获取当前城市的城市名
          let geocoder = new AMap.Geocoder({
            radius: 1000,
            extensions: "all",
          });
          geocoder.getAddress(
            [o.Ce.center.lng, o.Ce.center.lat],
            (status, result) => {
              if (status === "complete" && result.info === "OK") {
                if (result && result.regeocode) {
                  console.log(result);
                  if (self.addOrUpdate) {
                    self.center = [o.Ce.center.lng, o.Ce.center.lat];
                    // self.searchOption.city = result.regeocode.addressComponent.city
                  }
                }
              }
            }
          );
        });
      },
      plugin: [
        {
          enableHighAccuracy: true, //是否使用高精度定位，默认:true
          timeout: 100, //超过10秒后停止定位，默认：无穷大
          maximumAge: 0, //定位结果缓存0毫秒，默认：0
          convert: true, //自动偏移坐标，偏移后的坐标为高德坐标，默认：true
          showButton: true, //显示定位按钮，默认：true
          buttonPosition: "RB", //定位按钮停靠位置，默认：'LB'，左下角
          showMarker: true, //定位成功后在定位到的位置显示点标记，默认：true
          showCircle: true, //定位成功后用圆圈表示定位精度范围，默认：true
          panToLocation: true, //定位成功后将定位到的位置作为地图中心点，默认：true
          zoomToAccuracy: true, //定位成功后调整地图视野范围使定位位置及精度范围视野内可见，默认：f
          extensions: "all",
          expandZoomRange: true,
          keyboardEnable: true,
          pName: "Geolocation",
          campus: [],
          events: {
            init(o) {
              // o 是高德地图定位插件实例
              o.getCityInfo((status, result) => {
                if (
                  result != null &&
                  result.center != null &&
                  result.center.length > 0
                ) {
                  self.lng = result.center[0];
                  self.lat = result.center[1];
                } else {
                  self.lng = 117.283042;
                  self.lat = 31.86119;
                }
                //alert(self.lat);
                dealerListApi.loadOtherDelaer().then((res) => {
                  for (var i = 0; i < res.data.length; i++) {
                    let merchantName = res.data[i].merchantName;
                    let code = res.data[i].merchantCode;
                    let address = res.data[i].address;
                    let lat = res.data[i].latitude;
                    let lon = res.data[i].longitude;
                    let areaDisance = 500;
                    if (
                      code === "8210113393" ||
                      code === "8210416662" ||
                      code === "821046000"
                    ) {
                      areaDisance = 250;
                    } else {
                      areaDisance = res.data[i].areaCoverRange;
                    }
                    self.circles.push({
                      center: [lon, lat],
                      radius: areaDisance,
                      fillOpacity: 0.5,
                      events: {
                        mouseover: (e) => {
                          self.currentWindow = {
                            position: [lon, lat],
                            content:
                              '<span style="font-size:14px;color:#000000;font-weight: bold">名称：' +
                              merchantName +
                              '</span style="font-size:14px;color:#000;font"><span style="font-size:12px;color:#ff0000;">编码:' +
                              code +
                              '</span><hr style="margin: 10px 0" />地址：' +
                              address,
                            visible: true,
                          };
                        },
                        mouseout: () => {
                          self.currentWindow.visible = false;
                        },
                      },
                    });
                    self.markers.push({
                      position: [res.data[i].longitude, res.data[i].latitude],
                      icon: "https://document.dxznjy.com/manage/flag.png",
                    });
                  }
                });
                // dealerListApi
                //   .loadOtherExperienceStores(self.lat, self.lng)
                //   .then((res) => {
                //     console.log(res);
                //     for (var i = 0; i < res.data.length; i++) {
                //       // var marker = new AMap.Marker({
                //       //     position: [res.data[i].longitude, res.data[i].latitude],
                //       //     title: res.data[i].merchantCode,
                //       //     icon: new AMap.Icon({
                //       //         size: new AMap.Size(32, 32),  //图标大小
                //       //         image: "http://dx-applet-mall.oss-cn-shanghai.aliyuncs.com/manage/1606807647000",
                //       //         imageOffset: new AMap.Pixel(2, 2)
                //       //     })
                //       // });
                //      //   map.add([marker, circle]);
                //       let merchantName = res.data[i].merchantName;
                //       let code = res.data[i].merchantCode;
                //       let address = res.data[i].address;
                //       self.circles.push({
                //         center: [res.data[i].longitude, res.data[i].latitude],
                //         radius: res.data.areaCoverRange==null?1500:res.data.areaCoverRange,
                //         fillOpacity: 0.5,
                //         events: {
                //           mouseover: (e) => {
                //             // self.windows.push({
                //             //   position:[res.data[i].longitude, res.data[i].latitude],
                // content:'<span style="font-size:14px;color:#000;font-weight: bold">名称：' +
                //   merchantName +
                //   '</span style="font-size:14px;color:#000;font"><span style="font-size:12px;color:#F00;">编码:' +
                //   code +
                //   '</span><hr style="margin: 10px 0" />地址：' +
                //   address,
                //             //   open:true
                //             // })
                //           },
                //         },
                //       });

                //       // self.markers.push({
                //       //   position:[res.data[i].longitude, res.data[i].latitude],
                //       //   icon:'http://dx-applet-mall.oss-cn-shanghai.aliyuncs.com/manage/1606807647000'
                //       // })
                //     }
                //   });

                self.center = [self.lng, self.lat];
                self.loaded = true;
                self.$nextTick();
              });
            },
          },
        },
      ],
      //地图结束
    };
  },
  created() {
    this.addOrUpdate = this.$route.query.addOrUpdate;
    this.getSelectResultList();
    this.isResubmit = this.$route.query.isResubmit;
    //获取银行的下拉框
    this.getStady();
    this.getRoleTag();
    //加载其他托管中心
    // this.loadOtherExperienceStores();
    ossPrClient();
    //编辑回显
    this.updateDealer();
    if (!this.addOrUpdate) {
      this.setTitle("托管中心编辑");
    } else {
      this.setTitle("托管中心新增");
    }
  },

  mounted() { },
  methods: {
    changePaymentIsComplete(paymentIsComplete) {
      console.log("changePaymentIsComplete", paymentIsComplete);
      if (paymentIsComplete == 0) {
        this.showPrepaidMoney = true;
      } else {
        this.showPrepaidMoney = false;
      }
    }, // 动态设置标签页标题
    setTitle(title) {
      let i = 0;
      let visitedViews = this.$store.getters.visitedViews;
      visitedViews.forEach((route, index) => {
        if (this.$route.path == route.path) {
          i = index;
        }
      });
      this.$route.meta.title = title;
      visitedViews[i].title = title;
    },
    // 状态改变事件
    changeRadio(radio) {
      if (radio == "3") {
        this.addMarketDate.merchantType = "3";
      } else {
        this.addMarketDate.merchantType = "2";
      }
    },
    changeupDate() {
      if (radio == "3") {
        this.updateMarketDate.merchantType = "3";
      } else {
        this.updateMarketDate.merchantType = "2";
      }
    },
    //编辑回显
    updateDealer() {
      const that = this;
      that.addOrUpdate = JSON.parse(
        window.localStorage.getItem("addOrUpdateDealer")
      );
      that.id = window.localStorage.getItem("dealerId");

      if (!that.addOrUpdate) {
        that.updateMarketDate.id = window.localStorage.getItem("dealerId");
        dealerListApi.queryActive(that.id).then((res) => {
          console.log(res);
          that.center = [res.data.longitude, res.data.latitude];
          that.updateMarketDate = res.data;
          that.selectedOptions = [
            res.data.province,
            res.data.city,
            res.data.area,
          ];
          if (that.selectedOptions[0] == that.selectedOptions[1]) {
            that.selectedOptions[1] = "市辖区";
          }
          if (
            res.data.contractPhoto !== null &&
            res.data.contractPhoto.length >= 1
          ) {
            for (let i = 0; i < res.data.contractPhoto.length; i++) {
              that.fileDetailList.push({
                url: that.aliUrl + res.data.contractPhoto[i],
              });
            }
          } else {
            that.fileDetailList = [];
          }

          console.log(res.data.idCardPhoto);
          if (
            res.data.idCardPhoto !== null &&
            res.data.idCardPhoto.length >= 1
          ) {
            for (let i = 0; i < res.data.idCardPhoto.length; i++) {
              // that.fileDetailList.push({
              //     url: that.aliUrl + res.data.idCardPhoto[i]
              //   })
              that.fileIdCard.push({
                url: that.aliUrl + res.data.idCardPhoto[i],
              });
            }
          } else {
            that.fileIdCard = [];
          }
          /******/
          console.log(res.data.paymentPhoto);
          if (
            res.data.paymentPhoto !== null &&
            res.data.paymentPhoto.length >= 1
          ) {
            for (let i = 0; i < res.data.paymentPhoto.length; i++) {
              that.fileDetailList4.push({
                url: that.aliUrl + res.data.paymentPhoto[i],
              });
            }
          } else {
            that.fileDetailList4 = [];
          }

          console.log(res.data.shopPhoto);
          if (res.data.shopPhoto !== null && res.data.shopPhoto.length >= 1) {
            for (let i = 0; i < res.data.shopPhoto.length; i++) {
              that.filelistShop.push({
                url: that.aliUrl + res.data.shopPhoto[i],
              });
            }
          }
        });
      } else {
      }
    },
    addMarker: function () {
      let lng = 121.5 + Math.round(Math.random() * 1000) / 10000;
      let lat = 31.197646 + Math.round(Math.random() * 500) / 10000;
      this.markers.push([lng, lat]);
    },
    //获取托管中心级别列表
    getSelectResultList() {
      dealerListApi
        .getSelectResult()
        .then((res) => {
          this.rankType = res.data;
        })
        .catch((err) => { });
    },

    //鼠标滑过
    markerMouse(e) {
      infoWindow.setContent(e.target.content);
      infoWindow.open(map, e.target.getPosition());
    },
    //TODO
    close() {
      const that = this;
      // that.$router.push({
      //   path: "/merchantManagement/dealerList",
      // });
      // 关闭当前标签页
      that.$store.dispatch("delVisitedViews", this.$route);
      that.$router.go(-1);

      that.addMarketDate = {};
      that.updateMarketDate = {};
      that.addOrUpdate = true;
      that.$refs.clearupload.clearFiles();
    },
    //鼠标滑过
    markerOut(e) {
      map.clearInfoWindow();
    },

    //新增操作
    clickAdd() {
      this.$refs.clearupload.clearFiles();
      this.dialogVisible = true;
    },
    // 打开修改登陆账号
    openLogin(name, id) {
      this.showLoginName = true;
      this.updateLoginName.id = id;
      this.updateLoginName.oldName = name;
    },
    //改变状态
    change() { },
    changeTime(val) {
      console.log(val);
    },
    //修改账号
    updateDealerLoginName(ele) {
      const that = this;
      that.$refs[ele].validate((valid) => {
        if (valid) {
          marketApi.markertUpdateLogin(that.updateLoginName).then(() => {
            that.$nextTick(() => that.fetchData());
            that.updateMarketDate.name = this.updateLoginName.name;
            that.updateLoginName.name = "";
            that.showLoginName = false;
            that.$message.success("修改登录账号成功");
          });
        }
      });
    },
    //修改账号关闭
    closeLoginname() {
      this.showLoginName = false;
    },
    getRoleTag() {
      schoolApi.getCurrentAdmin().then((res) => {
        console.log(res.data.merchantCode + "wyy");
        this.roleTag = res.data.roleTag;
        this.merchantCode = res.data.merchantCode;
      });
    },
    //新增操作托管中心
    addActiveFun(ele) {
      const that = this;
      if (that.fileDetailList4.length <= 0) {
        that.$message.error("付款照片不能为空");
        return false;
      }
      if (that.addMarketDate.openMoney < 30000) {
        that.$message.error("开户金额不能小于30000");
        return false;
      }
      if (that.fileDetailList.length <= 0) {
        that.$message.error("合同照片不能为空");
        return false;
      }
      if (that.fileIdCard.length <= 0) {
        that.$message.error("证件照片不能为空");
        return false;
      }
      that.addMarketDate.province = that.selectedOptions[0];
      if (that.selectedOptions[1] == "市辖区") {
        that.addMarketDate.city = that.selectedOptions[0];
      } else {
        that.addMarketDate.city = that.selectedOptions[1];
      }
      that.addMarketDate.area = that.selectedOptions[2];
      if (
        !that.addMarketDate.city ||
        !that.addMarketDate.province ||
        !that.addMarketDate.area
      ) {
        that.$message.error("省市区不能为空,请从选择您所在的省市区");
        return false;
      }
      if (that.addMarketDate.marketChragePerson == "") {
        that.$message.error("市场负责人不能为空");
        return false;
      }
      if (that.addMarketDate.consultingChargePerson == "") {
        that.$message.error("咨询负责人不能为空");
        return false;
      }
      if (that.addMarketDate.teachingChargePerson == "") {
        that.$message.error("教学负责人不能为空");
        return false;
      }
      if (that.addMarketDate.marketChargePersonPhone == "") {
        that.$message.error("市场负责人手机号不能为空");
        return false;
      }
      if (that.addMarketDate.consultingChargePersonPhone == "") {
        that.$message.error("咨询负责人手机号不能为空");
        return false;
      }
      if (that.addMarketDate.teachingChargePersonPhone == "") {
        that.$message.error("教学负责人手机号不能为空");
        return false;
      }
      if (that.addMarketDate.detailedAddress == "") {
        that.$message.error("详细地址不能为空");
        return false;
      }
      if (that.addMarketDate.siteType == "") {
        that.$message.error("场地类型不能为空");
        return false;
      }
      if (that.addMarketDate.siteArea == "") {
        that.$message.error("场地面积不能为空");
        return false;
      }
      if (that.addMarketDate.floorNum == "") {
        that.$message.error("楼层不能为空");
        return false;
      }
      that.addMarketDate.contractPhoto = that.fileDetailList;
      that.addMarketDate.idCardPhoto = that.fileIdCard;
      that.addMarketDate.shopPhoto = that.filelistShop;
      that.addMarketDate.paymentPhoto = that.fileDetailList4;

      that.$refs[ele].validate((valid) => {
        // 表单验证
        if (valid) {
          const loading = this.$loading({
            lock: true,
            text: "新增托管中心",
            spinner: "el-icon-loading",
            background: "rgba(0, 0, 0, 0.7)",
          });
          dealerListApi
            .addDealerList(that.addMarketDate)
            .then((res) => {
              that.dialogVisible = false;
              loading.close();
              that.addMarketDate = {};
              that.fileIdCard = [];
              that.fileDetailList = [];
              that.filelistShop = [];
              that.fileDeatiList4 = [];
              dealerListApi
                .startAndTakeUserTaskByAddDealer({
                  approvalType: "agree",
                  masterData: {
                    open_type: "addDealer",
                    relation_id: res.data,
                    create_time: this.formatDate(new Date()),
                  },
                })
                .then(() => {
                  that.$message.success("托管中心审批流程开启~");
                });
              that.$message.success("新增托管中心成功");
              that.$router.push({
                path: "/merchantManagement/dealerList",
              });
            })
            .catch((err) => {
              loading.close();
            });
        } else {
          console.log("error submit!!");
          //loading.close();
          return false;
        }
      });
    },
    formatDate(current_datetime) {
      return (
        current_datetime.getFullYear() +
        "-" +
        (current_datetime.getMonth() + 1) +
        "-" +
        current_datetime.getDate() +
        " " +
        current_datetime.getHours() +
        ":" +
        current_datetime.getMinutes() +
        ":" +
        current_datetime.getSeconds()
      );
    },
    //修改操作
    updateActiveFun(ele, isResubmit) {
      console.log("======================");
      const that = this;
      if (that.updateMarketDate.openMoney < 30000) {
        that.$message.error("开户金额不能小于30000");
        return false;
      }
      if (that.fileDetailList.length <= 0) {
        that.$message.error("合同照片不能为空");
        return false;
      }
      if (that.fileIdCard.length <= 0) {
        that.$message.error("证件照片不能为空");
        return false;
      }
      if (that.updateMarketDate.city == "") {
        that.$message.error("市不能为空");
        return false;
      }
      //付款记录照片
      that.updateMarketDate.paymentPhoto = [];
      for (let i = 0; i < that.fileDetailList4.length; i++) {
        let paymentIndex = that.fileDetailList4[i].url.lastIndexOf("manage");
        that.updateMarketDate.paymentPhoto.push(
          that.fileDetailList4[i].url.substring(
            paymentIndex,
            that.fileDetailList4[i].url.length
          )
        );
      }
      that.updateMarketDate.contractPhoto = [];
      for (let i = 0; i < that.fileDetailList.length; i++) {
        let index = that.fileDetailList[i].url.lastIndexOf("manage");
        that.updateMarketDate.contractPhoto.push(
          that.fileDetailList[i].url.substring(
            index,
            that.fileDetailList[i].url.length
          )
        );
      }
      that.updateMarketDate.idCardPhoto = [];
      for (var i = 0; i < that.fileIdCard.length; i++) {
        let idCardIndex = that.fileIdCard[i].url.lastIndexOf("manage");
        that.updateMarketDate.idCardPhoto.push(
          that.fileIdCard[i].url.substring(
            idCardIndex,
            that.fileIdCard[i].url.length
          )
        );
      }
      that.updateMarketDate.shopPhoto = [];
      for (var i = 0; i < that.filelistShop.length; i++) {
        let shopindex = that.filelistShop[i].url.lastIndexOf("manage");
        that.updateMarketDate.shopPhoto.push(
          that.filelistShop[i].url.substring(
            shopindex,
            that.filelistShop[i].url.length
          )
        );
      }
      if (this.isResubmit) {
        that.rules.name = [];
      }
      that.$refs[ele].validate((valid) => {
        // 表单验证
        if (valid) {
          const loading = this.$loading({
            lock: true,
            text: "修改托管中心",
            spinner: "el-icon-loading",
            background: "rgba(0, 0, 0, 0.7)",
          });
          dealerListApi
            .addDealerList(that.updateMarketDate)
            .then(
              (res) => {
                that.dialogVisible = false;
                loading.close();
                that.fileIdCard = [];
                that.fileDetailList = [];
                that.filelistShop = [];
                if (isResubmit) {
                  dealerListApi
                    .startAndTakeUserTaskByAddDealer({
                      approvalType: "agree",
                      masterData: {
                        open_type: "addDealer",
                        relation_id: res.data,
                        create_time: this.formatDate(new Date()),
                      },
                    })
                    .then(() => {
                      that.$message.success("托管中心审批流程开启~");
                    });
                }
                that.$router.push({
                  path: "/merchantManagement/dealerList",
                });
                that.$message.success("修改托管中心成功");
              },
              (s) => {
                if (s == "error") {
                  loading.close();
                }
              }
            )
            .catch((err) => {
              loading.close();
            });
        } else {
          console.log("error submit!!");
          //loading.close();
          return false;
        }
      });
    },

    //上传图片
    //支付记录照片上传
    uploadDetailHttp4({ file }) {
      this.uploadLoading4 = true;
      const that = this;
      const fileName = "manage/" + Date.parse(new Date());
      that.$nextTick(function () {
        ossPrClient()
          .put(fileName, file)
          .then(({ res, url, name }) => {
            if (res && res.status === 200) {
              console.log(`阿里云OSS上传图片成功回调1`, res, url, name);
              if (!that.addOrUpdate) {
                that.fileDetailList4.push({
                  uid: file.uid,
                  url: url,
                });
              } else {
                // 新增上传图片
                that.fileDetailList4.push(name);
              }
              that.$nextTick(() => {
                that.uploadLoading4 = false;
              });
            }
          })
          .catch((err) => {
            that.$message.error("上传图片失败请检查网络或者刷新页面");
            console.log(`阿里云OSS上传图片失败回调`, err);
          });
      });
    },
    // 上传合同照片请求
    uploadDetailHttp({ file }) {
      this.uploadLoading = true;
      const that = this;
      const fileName = "manage/" + Date.parse(new Date());
      that.$nextTick(function () {
        ossPrClient()
          .put(fileName, file)
          .then(({ res, url, name }) => {
            if (res && res.status === 200) {
              console.log(`阿里云OSS上传图片成功回调1`, res, url, name);
              if (!that.addOrUpdate) {
                that.fileDetailList.push({
                  uid: file.uid,
                  url: url,
                });
              } else {
                // 新增上传图片
                that.fileDetailList.push(name);
              }
              that.$nextTick(() => {
                that.uploadLoading = false;
              });
            }
          })
          .catch((err) => {
            that.$message.error("上传图片失败请检查网络或者刷新页面");
            console.log(`阿里云OSS上传图片失败回调`, err);
          });
      });
    },
    // 上传图片预览
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url;
      this.dialogUploadVisible = true;
    },
    // 上传图片数量超限
    justPictureNum(file, fileList) {
      this.$message.warning(`当前限制选择10个文件`);
    },
    //移除图片
    // 删除合同照片
    handleRemoveDetailContract(file, fileList) {
      const that = this;
      if (!that.addOrUpdate) {
        that.fileDetailList = fileList;
      } else {
        for (let a = 0; a < that.fileDetailList.length; a++) {
          if (
            that.fileDetailList[a].substring(7, 17) == parseInt(file.uid / 1000)
          ) {
            that.fileDetailList.splice(a, 1);
          }
        }
      }
    },

    //证件照片开始
    //证件照上传
    uploadIdCardDetailHttp({ file }) {
      this.uploadLoadingIdCard = true;
      const that = this;
      const fileName = "manage/" + Date.parse(new Date());
      that.$nextTick(function () {
        ossPrClient()
          .put(fileName, file)
          .then(({ res, url, name }) => {
            if (res && res.status === 200) {
              console.log(`阿里云OSS上传图片成功回调1`, res, url, name);
              if (!that.addOrUpdate) {
                that.fileIdCard.push({
                  uid: file.uid,
                  url: url,
                });
              } else {
                // 新增上传图片
                that.fileIdCard.push(name);
              }
              that.$nextTick(() => {
                that.uploadLoadingIdCard = false;
              });
            }
          })
          .catch((err) => {
            that.$message.error("上传图片失败请检查网络或者刷新页面");
            console.log(`阿里云OSS上传图片失败回调`, err);
          });
      });
    },
    //照片限制
    // 上传图片数量超限
    justPictureNumIdCard(file, fileList) {
      this.$message.warning(`当前限制选择10个文件`);
    },
    handleRemoveDetailIdCard(file, fileList) {
      const that = this;
      if (!that.addOrUpdate) {
        that.fileIdCard = fileList;
      } else {
        for (let a = 0; a < that.fileIdCard.length; a++) {
          if (
            that.fileIdCard[a].substring(7, 17) == parseInt(file.uid / 1000)
          ) {
            that.fileIdCard.splice(a, 1);
          }
        }
      }
    },
    //支付记录照片删除
    handleRemoveDetail4(file, fileList) {
      const that = this;
      if (!that.addOrUpdate) {
        that.fileDetailList4 = fileList;
      } else {
        for (let a = 0; a < that.fileDetailList4.length; a++) {
          if (
            that.fileDetailList4[a].substring(7, 17) ==
            parseInt(file.uid / 1000)
          ) {
            that.fileDetailList4.splice(a, 1);
          }
        }
      }
    },
    //门店照开始
    uploadDetailHttpShop({ file }) {
      this.uploadLoadingShop = true;
      const that = this;
      const fileName = "manage/" + Date.parse(new Date());
      that.$nextTick(function () {
        ossPrClient()
          .put(fileName, file)
          .then(({ res, url, name }) => {
            if (res && res.status === 200) {
              console.log(`阿里云OSS上传图片成功回调1`, res, url, name);
              if (!that.addOrUpdate) {
                that.filelistShop.push({
                  uid: file.uid,
                  url: url,
                });
              } else {
                // 新增上传图片
                that.filelistShop.push(name);
              }
              that.$nextTick(() => {
                that.uploadLoadingShop = false;
              });
            }
          })
          .catch((err) => {
            that.$message.error("上传图片失败请检查网络或者刷新页面");
            console.log(`阿里云OSS上传图片失败回调`, err);
          });
      });
    },
    //照片限制
    // 上传图片数量超限
    justPictureNumShop(file, fileList) {
      this.$message.warning(`当前限制选择10个文件`);
    },
    handleRemoveDetailShop(file, fileList) {
      const that = this;
      if (!that.addOrUpdate) {
        that.filelistShop = fileList;
      } else {
        for (let a = 0; a < that.filelistShop.length; a++) {
          if (
            that.filelistShop[a].substring(7, 17) == parseInt(file.uid / 1000)
          ) {
            that.filelistShop.splice(a, 1);
          }
        }
      }
    },
    //门店照结束

    //查看状态
    //获取学段下拉框
    getStady() {
      var enType = "BankType";
      enTypes.getEnumerationAggregation(enType).then((res) => {
        this.bankType = res.data;
      });
    },
    req_post(val) {
      console.log(val);
    },

    onSearchResult(pois) {
      let latSum = 0;
      let lngSum = 0;
      let markers = [];
      let windows = [];
      let that = this;
      that.result = [];
      if (pois.length > 0) {
        // console.log(pois)
        pois.forEach((poi, index) => {
          const { lng, lat } = poi;
          lngSum += lng;
          latSum += lat;
          markers.push({
            position: [poi.lng, poi.lat],
            events: {
              click() {
                // console.log(poi)
                that.windows.forEach((window) => {
                  window.visible = false;
                });
                that.window = that.windows[index];
                that.$nextTick(() => {
                  that.window.visible = true;
                  that.getMarkAddress(poi.lng, poi.lat);
                });
              },
            },
          });
          // ${ index }<img src="" style="">
          windows.push({
            position: [poi.lng, poi.lat],
            content: `<div class="prompt"><span>${poi.name}</span></div>`,
            visible: false,
          });
          that.result.push(poi);
        });
        const center = {
          lng: lngSum / pois.length,
          lat: latSum / pois.length,
        };
        this.mapCenter = [center.lng, center.lat];
        this.center = [center.lng, center.lat];
        this.markers2 = markers;
        this.windows = windows;
      }
    },
    getMarkAddress(lng, lat) {
      // 这里通过高德 SDK 完成。
      var that = this;
      var geocoder = new AMap.Geocoder();
      geocoder.getAddress([lng, lat], function (status, result) {
        if (status === "complete" && result.info === "OK") {
          if (result && result.regeocode) {
            // console.log(result)
            // that.searchOption.city = result.regeocode.addressComponent.city
            that.center = [lng, lat];
            // 判断是添加还是编辑
            if (that.addOrUpdate) {
              that.addMarketDate.longitude = lng;
              that.addMarketDate.latitude = lat;
              that.addMarketDate.address = result.regeocode.formattedAddress;
              that.addMarketDate.province =
                result.regeocode.addressComponent.province;
              var reg = RegExp(/省/);
              if (
                that.addMarketDate.province.match(reg) &&
                result.regeocode.addressComponent.city == ""
              ) {
                that.addMarketDate.city =
                  result.regeocode.addressComponent.district;
              } else {
                if (
                  result.regeocode.addressComponent.province == "重庆市" ||
                  result.regeocode.addressComponent.province == "天津市" ||
                  result.regeocode.addressComponent.province == "北京市" ||
                  result.regeocode.addressComponent.province == "上海市"
                ) {
                  that.addMarketDate.city =
                    result.regeocode.addressComponent.province;
                } else {
                  // 市
                  that.addMarketDate.city =
                    result.regeocode.addressComponent.city;
                }
              }
              that.addMarketDate.area =
                result.regeocode.addressComponent.district;
            } else {
              that.updateMarketDate.longitude = lng;
              that.updateMarketDate.latitude = lat;
              that.updateMarketDate.address = result.regeocode.formattedAddress;
              that.updateMarketDate.province =
                result.regeocode.addressComponent.province;

              var reg = RegExp(/省/);
              if (
                that.updateMarketDate.province.match(reg) &&
                result.regeocode.addressComponent.city == ""
              ) {
                that.updateMarketDate.city =
                  result.regeocode.addressComponent.district;
              } else {
                if (
                  result.regeocode.addressComponent.province == "重庆市" ||
                  result.regeocode.addressComponent.province == "天津市" ||
                  result.regeocode.addressComponent.province == "北京市" ||
                  result.regeocode.addressComponent.province == "上海市"
                ) {
                  that.updateMarketDate.city =
                    result.regeocode.addressComponent.province;
                } else {
                  // 市
                  that.updateMarketDate.city =
                    result.regeocode.addressComponent.city;
                }
              }
              that.updateMarketDate.area =
                result.regeocode.addressComponent.district;
            }
            that.$nextTick();
          }
        } else {
          // alert('地址获取失败')
        }
      });
    },
    // 标注列表
    markList(lng, lat, index) {
      // if (this.currentResult != index) {
      //   this.currentResult = index
      // } else {
      //   this.currentResult = -1
      // }
      this.getMarkAddress(lng, lat, index);
    },
    changeSizeHandler(size) {
      this.pageSize = size;
    },
    handleCurrentChange(currentPage) {
      this.pageNum = currentPage;
    },
  },
};
</script>

<style>
.map-box {
  position: relative;
}

.search-box {
  position: absolute !important;
  top: 10px;
  left: 10px;
}

.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.red {
  color: red;
}

.green {
  color: green;
}

.blue {
  color: blue;
}

.prompt {
  padding: 10px;
}

.result {
  position: absolute;
  top: 0;
  left: 100%;
  width: 300px;
  /* height: 450px; */
  margin-left: 10px;
  background-color: #ffffff;
  border: 1px solid silver;
}

.result-list {
  display: flex;
  align-items: center;
  /* margin-bottom: 10px; */
  line-height: 1.6;
  overflow: hidden;
  cursor: pointer;
}

.result label {
  display: block;
  width: 19px;
  height: 33px;
  margin-right: 10px;
  line-height: 20px;
  text-align: center;
  font-size: 12px;
  color: #ffffff;
  background: url('https://webapi.amap.com/theme/v1.3/markers/n/mark_bs.png') no-repeat center/cover;
}

.result-list.active label {
  background: url('http://webapi.amap.com/theme/v1.3/markers/n/mark_r.png') no-repeat center/cover;
}

.list-right {
  flex: 1;
}

.result .name {
  font-size: 14px;
  color: #565656;
}

.result .address {
  color: #999;
}

.search-table {
  height: 380px;
  margin-bottom: 10px !important;
}

.search-table th {
  display: none;
}

.search-table td {
  padding: 5px 0 !important;
  border-bottom: none;
}

.el-vue-search-box-container {
  width: 90% !important;
}

.el-date-editor.el-input {
  width: 100% !important;
}

@media screen and (max-width: 767px) {
  .app-container {
    /* padding: 20px 10px; */
  }

  .result {
    display: none;
  }
}
</style>
