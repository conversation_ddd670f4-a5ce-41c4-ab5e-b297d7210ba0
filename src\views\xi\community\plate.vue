<template>
  <div class="app-container">
    <el-form :inline="true" ref="queryForm" class="container-card" label-width="110px">
      <el-form-item>
        <el-button type="primary" icon="el-icon-re" @click="addBtn()">添加板块</el-button>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-re" @click="topic()">话题管理</el-button>
      </el-form-item>
    </el-form>

    <el-table class="common-table" v-loading="tableLoading" :data="tableData" style="width: 100%;margin-bottom: 20px;"
      row-key="id" border default-expand-all>
      <el-table-column prop="orderNum" type="index" label="序号" width="50"></el-table-column>
      <el-table-column prop="name" label="板块名称"></el-table-column>
      <el-table-column prop="withdrawnBonus" label="操作" width="220">
        <template slot-scope="scope">
          <el-button type="primary" size="mini" @click="update(scope.row.id)">编辑
          </el-button>
          <el-button type="danger" size="mini" icon="el-icon-delete" @click="handleDelete(scope.row.id)">删除
          </el-button>
        </template>
      </el-table-column>
      <el-table-column prop="enable" label="显示状态">
        <template slot-scope="scope">
          <el-switch v-model="scope.row.enable" active-color="#13ce66" inactive-color="#ff4949"
            @change="switchChange(scope.row.enable, scope.row.id)">
          </el-switch>
        </template>
      </el-table-column>
      <el-table-column prop="platePostNum" label="帖子数量" show-overflow-tooltip></el-table-column>
      <el-table-column prop="orderNum" label="排序" show-overflow-tooltip></el-table-column>
    </el-table>
    <!-- 分页 -->
    <el-col :span="24" style="margin-bottom: 20px">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>

    <!-- 新增弹窗 -->
    <el-dialog :title="topic_title" :visible.sync="open" width="70%" @close="close">
      <el-form ref="form" :model="form" :rules="rules" label-width="120px" style="width: 70%;">

        <el-form-item label="名称：" prop="name">
          <el-input v-model="form.name" placeholder="请填写名称" />
        </el-form-item>
        <el-form-item label="序号：" prop="orderNum">
          <el-input v-model="form.orderNum" placeholder="请填写序号" type="number" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">添 加</el-button>
        <el-button @click="open = false">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 修改弹窗 -->
    <el-dialog :title="topic_title" :visible.sync="change_open" width="70%" @close="close">
      <el-form ref="form" :model="form" :rules="rules" label-width="120px" style="width: 70%;">

        <el-form-item label="名称：" prop="name">
          <el-input v-model="form.name" placeholder="请填写名称" maxlength="6" />
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="change_open = false">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 话题管理 -->
    <el-dialog title="话题管理" :visible.sync="open_topic" width="70%" @close="close">
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="topic_add">添 加</el-button>
      </div>
      <el-table class="common_table" v-loading="tableLoading" :data="tableData_topic"
        style="width: 100%;margin-bottom: 20px;" row-key="programid" border default-expand-all>
        <el-table-column prop="orderNum" type="index" label="序号" width="50"></el-table-column>
        <el-table-column prop="name" label="话题名称"></el-table-column>
        <el-table-column prop="withdrawnBonus" label="操作" width="220">
          <template slot-scope="scope">
            <el-button type="primary" size="mini" @click="update_topic(scope.row.id)">编辑
            </el-button>
            <el-button type="danger" size="mini" icon="el-icon-delete" @click="handleDeletes(scope.row.id)">删除
            </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="enable" label="显示状态">
          <template slot-scope="scope">
            <el-switch v-model="scope.row.enable" active-color="#13ce66"
              @change="switchChanges(scope.row.enable, scope.row.id)" inactive-color="#ff4949">
            </el-switch>
          </template>
        </el-table-column>
        <el-table-column prop="topicPostNum" label="帖子数量" show-overflow-tooltip></el-table-column>
        <el-table-column prop="orderNum" label="排序" show-overflow-tooltip>
        </el-table-column>
      </el-table>

    </el-dialog>

  </div>
</template>
<script>
import platetApi from '@/api/xi/community/plate'
import { pageParamNames } from '@/utils/constants'
import Sortable from "sortablejs";

export default {
  name: 'plate',
  data() {
    return {
      dataQuery: {
        type: '',
        name: ''
      },
      tableLoading: false,
      tableData_topic: [],
      change_open: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      open: false,
      open_topic: false,
      topic_title: '',
      tableData: [],
      form: {
        type: 1,
      },
      // 表单校验
      rules: {
        content: [{ required: true, message: '请输入敏感词', trigger: 'blur' }],
      }
    }
  },
  created() {
    this.getPageList()
  },
  mounted() {
    // 表格拖动 行拖动
    const table = document.querySelector('.el-table__body-wrapper tbody')
    const self = this
    Sortable.create(table, {
      onEnd({ newIndex, oldIndex }) {
        const targetRow = self.tableData_topic.splice(oldIndex, 1)[0]
        self.tableData.splice(newIndex, 0, targetRow)
      },

    })

  },
  methods: {

    switchChanges(value, id) {
      console.log(value, id)
      platetApi.detail(id).then(res => {
        this.form = res.data;
        this.form.enable = value
        platetApi.saveOrUpdate(this.form).then(response => {
          this.$message.success('提交成功！')
        })
        console.log(this.form)
      })

    },
    switchChange(value, id) {
      console.log(value, id)
      platetApi.detail(id).then(res => {
        this.form = res.data;
        this.form.enable = value
        platetApi.saveOrUpdate(this.form).then(response => {
          this.$message.success('提交成功！')
        })
        console.log(this.form)
      })

    },
    topic() {
      this.open_topic = true

      this.getTopicList()
    },
    handleDelete(id) {
      this.$confirm('确定要删除吗？', '删除板块', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        platetApi.delete(id).then(res => {
          this.$message.success('删除成功！')
          this.getPageList()
        })
      })
    },
    handleDeletes(id) {
      this.$confirm('确定要删除吗？', '删除板块', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        platetApi.delete(id).then(res => {
          this.$message.success('删除成功！')
          this.getTopicList()
        })
      })
    },
    update(id) {
      platetApi.detail(id).then(res => {
        this.form = res.data;
        this.change_open = true;
        this.topic_title = '板块分类'
      })
    },
    update_topic(id) {
      platetApi.detail(id).then(res => {
        this.form = res.data;
        this.change_open = true;
        this.topic_title = '话题管理'
      })
    },
    addBtn() {
      this.reset();
      this.open = true;
      this.topic_title = '板块分类'
    },
    topic_add() {
      this.reset();
      this.open = true;
      this.topic_title = '话题管理'
    },
    editBtn(id) {
      this.reset();
      platetApi.detail(id).then(res => {
        this.form = res.data;
        this.open = true;
      })
    },

    submitForm() {
      const that = this;
      if (this.form.name) {
        this.$refs['form'].validate(valid => {
          if (valid) {
            if (this.topic_title == '板块分类') {
              this.form.type = 1
            } else {
              this.form.type = 2
            }
            platetApi.saveOrUpdate(that.form).then(response => {
              that.$message.success('提交成功！')
              that.open = false
              that.change_open = false
              if (this.topic_title == '板块分类') {
                that.getPageList()
              } else {
                that.getTopicList()
              }
            })
          }
        })
      } else {
        this.$message({
          message: '请输入名称',
          type: 'warning'
        });
      }

    },
    getTopicList() {
      this.tableLoading = true
      this.dataQuery.type = 2
      this.dataQuery.pageNum = this.tablePage.currentPage
      this.dataQuery.pageSize = this.tablePage.size
      platetApi.list(this.dataQuery).then(res => {
        console.log(res)
        this.tableData_topic = res.data.data
        this.tableLoading = false
        const tbody = document.querySelector('.common_table tbody')
        const _this = this
        Sortable.create(tbody, {
          onEnd({ newIndex, oldIndex }) {
            const currRows = _this.tableData_topic.splice(oldIndex, 1)[0]
            _this.tableData_topic.splice(newIndex, 0, currRows)
          }
        })
        // 设置后台返回的分页参数
        pageParamNames.forEach(name =>
          this.$set(this.tablePage, name, parseInt(res.data[name]))
        )
      })
    },
    getPageList() {
      this.tableLoading = true
      this.dataQuery.type = 1
      this.dataQuery.pageNum = this.tablePage.currentPage
      this.dataQuery.pageSize = this.tablePage.size
      platetApi.list(this.dataQuery).then(res => {
        console.log(res)
        this.tableData = res.data.data
        this.tableLoading = false
        // 设置后台返回的分页参数
        pageParamNames.forEach(name =>
          this.$set(this.tablePage, name, parseInt(res.data[name]))
        )
      })
    },
    handlePage() {
      (this.tablePage = {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      }),
        this.getPageList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dataQuery = {
        name: '',
        enable: null
      }
      this.getPageList()
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val
      this.getPageList()
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val
      this.getPageList()
    },
    close() {
      this.open = false
      this.reset()
    },
    reset() {
      this.form = {
        name: '',
        type: 1,
      }
      if (this.$refs['form']) {
        this.$refs['form'].resetFields();
      }
    }
  },
}
</script>

<style>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.period-table td,
.period-table th {
  text-align: center;
}

@media (max-width: 767px) {
  .el-message-box {
    width: 80% !important;
  }
}

.el-tooltip__popper {
  max-width: 800px;
}
</style>
