/**
 * 学员管理相关接口
 */
import request from '@/utils/request'

export default {
  //购买课程包
  buyCoursePackage(data) {
    return request({
      url: '/znyy/course/product/buy',
      method: 'POST',
      data
    })
  },

  //获取课程信息
  getProductCateGory(id) {
    return request({
      url: '/znyy/course/product/category?id=' + id,
      method: 'GET',
    })
  },
  //根据目录获取课程产品
  getProductVo(categoryId) {
    return request({
      url: '/znyy/course/product/by/categoryId?categoryId=' + categoryId,
      method: 'GET',
    })
  },
  //根据课程获取明细
  getCourseProductSpecVo(productId) {
    return request({
      url: '/znyy/course/product/sku?productId=' + productId,
      method: 'GET',
    })
  },

  // 分页查询
  studentList(pageNum, pageSize, data, phone) {
    return request({
      url: '/znyy/areas/student/forget/list/' + pageNum + '/' + pageSize,
      method: 'GET',
      params: data,
      headers: {
        'User-Phone': phone
      }
    })
  },
  detail(id, realName) {
    return request({
      url: '/znyy/areas/student/forget/detail?printCode=' + id + "&studentName=zhangsan" + "&realName=" + realName,
      method: 'GET'
    })
  },
  download(id, realName) {
    return request({
      url: '/znyy/areas/student/forget/download/file?printCode=' + id + "&fileType=png" + "&contentType=forget" + "&studentName=" + realName,
      method: 'GET'
    })
  },
  courseList(pageNum, pageSize, data) {
    return request({
      url: '/znyy/areas/course/list/' + pageNum + '/' + pageSize,
      method: 'GET',
      params: data
    })
  },
  courseRecordList(pageNum, pageSize, data) {
    return request({
      url: '/znyy/areas/student/course/progress/list/' + pageNum + '/' + pageSize,
      method: 'GET',
      params: data
    })
  },
  updateStatus(id, status) {
    return request({
      url: '/znyy/areas/student/update/course/schedule/enable/' + id + '/' + status,
      method: 'PUT',
    })

  },
  regainArchive(id, status) {
    return request({
      url: '/znyy/archive/regain/student/word/' + id + '/' + status,
      method: 'PUT',
    })

  },
  getSchoolAccount() {
    return request({
      url: '/znyy/areas/student/get/account',
      method: 'GET',
    })
  },

  submitRecharge(data) {
    return request({
      url: '/znyy/areas/student/charge/course/save',
      method: 'POST',
      data
    })
  },
  //根据课程分类查子类
  checkNeedLineCollect(studentCode) {
    return request({
      url: '/znyy/areas/student/line/collect?studentCode=' + studentCode,
      method: 'GET'
    })
  },
  getBackRecharge(studentCode) {
    return request({
      url: '/znyy/areas/student/tui/course/' + studentCode,
      method: 'GET',
    })
  },

  submitBackRecharge(data) {
    return request({
      url: '/znyy/areas/student/tui/course/save',
      method: 'POST',
      data
    }).catch(err => {
      debugger
      console.log(err)
    })
  },
  //根据课程分类查子类
  checkClassification(categoryCode) {
    return request({
      url: '/znyy/course/big/class/type/' + categoryCode,
      method: 'GET'
    })
  },

  submitOpenCourse(data) {
    return request({
      url: '/znyy/areas/course/batch/add/student/course',
      method: 'POST',
      data
    })
  },

  // 分页查询
  salesRecord(pageNum, pageSize, data) {
    return request({
      url: '/znyy/areas/student/sales/record/' + pageNum + '/' + pageSize,
      method: 'GET',
      params: data
    })
  },
  exportAreasStudentRecord(data) {
    return request({
      url: '/znyy/areas/student/studentCourseProgressList/to/excel',
      method: 'GET',
      params: data,
      responseType: 'blob',
    })

  },
  findStudent(phoneNum) {
    return request({
      url: 'znyy/grammmar/consume/hours/select/student?loginName=' + phoneNum,
      method: 'GET',
    })
  }
}
