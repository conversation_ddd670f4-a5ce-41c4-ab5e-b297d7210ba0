<template>
  <div class="app-container">
    <el-col :span="24" style="margin-bottom: 30px">
      <el-button type="success" icon="el-icon-plus" size="mini" @click="addBtn()">新增</el-button>
    </el-col>
    <el-table class="common-table" v-loading="tableLoading" :data="tableData" style="width: 100%;margin-bottom: 20px;"
      row-key="id" border default-expand-all>
      <el-table-column prop="id" label="ID"></el-table-column>
      <el-table-column prop="name" label="名称" />
      <el-table-column prop="withdrawnBonus" label="操作" width="480">
        <template slot-scope="scope">
          <el-button type="primary" size="mini" @click="editBtn(scope.row.id)">编辑
          </el-button>
          <el-button type="danger" size="mini" @click="handleDelete(scope.row.id)">删除
          </el-button>
        </template>
      </el-table-column>
      <el-table-column prop="ident" label="标识" />
      <el-table-column prop="type" label="类型" :formatter="typeFormat" />
      <el-table-column prop="explainInfo" label="说明" />
      <el-table-column prop="orderNum" label="序号" />
    </el-table>
    <!-- 分页 -->
    <el-col :span="24" style="margin-bottom: 20px">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>

    <!-- 修改或者新增弹窗 -->
    <el-dialog title="编辑分类" :visible.sync="open" width="70%" @close="close">
      <el-form ref="form" :model="form" :rules="rules" label-width="130px" style="width: 70%;">
        <el-form-item label="分类名称：" prop="name">
          <el-input v-model="form.name" placeholder="请输入分类名称" />
        </el-form-item>
        <el-form-item label="商品分类：" prop="goodsType">
          <el-radio v-model="form.goodsType" :label="true">是</el-radio>
          <el-radio v-model="form.goodsType" :label="false">否</el-radio>
        </el-form-item>
        <el-form-item label="分类类型：" prop="typeIdent" v-if="form.goodsType">
          <el-select v-model="form.typeIdent" :disabled="form.id !== null" @change="handleChange">
            <el-option v-for="item in classifyTypeList" :label="item.name" :value="item.ident" />
          </el-select>
        </el-form-item>
        <el-form-item label="分类标识：" prop="ident">
          <el-input v-model="form.ident" placeholder="请输入分类标识" :disabled="form.id !== null" />
        </el-form-item>
        <el-form-item label="说明：" prop="explainInfo">
          <el-input v-model="form.explainInfo" placeholder="请输入说明" />
        </el-form-item>
        <el-form-item label="序号：" prop="orderNum">
          <el-input-number v-model="form.orderNum" controls-position="right" :min="0" :step="1" placeholder="请输入序号" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import classifyApi from '@/api/xi/classify'
import classifyTypeApi from '@/api/xi/classifyType'
import { pageParamNames } from '@/utils/constants'

export default {
  name: 'classify',
  data() {
    return {
      classifyTypeList: [],
      dataQuery: {},
      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      open: false,
      tableData: [],
      form: {},
      // 表单校验
      rules: {
        name: [{ required: true, message: '请输入分类名称', trigger: 'blur' }],
        ident: [{ required: true, message: '请输入分类标识', trigger: 'blur' }],
        typeIdent: [{ required: true, message: '请选择分类', trigger: 'blur' }],
        explainInfo: [{ required: true, message: '请输入说明', trigger: 'blur' }],
        goodsType: [{ required: true, message: '请选择', trigger: 'blur' }],
        orderNum: [{ required: true, message: '请输入序号', trigger: 'blur' }],
      }
    }
  },
  created() {
    this.getPageList()
    this.getClassifyType()
  },
  methods: {
    typeFormat(row, column) {
      for (let i = 0; i < this.classifyTypeList.length; i++) {
        if (this.classifyTypeList[i].ident === row.typeIdent) {
          return this.classifyTypeList[i].name
        }
      }
      return '无'
    },
    handleChange(val) {
      this.form.ident = val + '_'
    },
    getClassifyType() {
      classifyTypeApi.all().then(res => {
        this.classifyTypeList = res.data
      })
    },
    handleDelete(id) {
      this.$confirm('确定要删除吗？', '删除分类', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        classifyApi.delete(id).then(res => {
          this.$message.success('删除成功！')
          this.getPageList()
        })
      })
    },
    addBtn() {
      this.reset()
      this.open = true
    },
    editBtn(id) {
      this.reset()
      classifyApi.detail(id).then(res => {
        this.form = res.data
        this.open = true
      })
    },
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          classifyApi.saveOrUpdate(this.form).then(response => {
            this.$message.success('提交成功！')
            this.open = false
            this.getPageList()
          })
        }
      })
    },
    getPageList() {
      this.tableLoading = true
      this.dataQuery.pageNum = this.tablePage.currentPage
      this.dataQuery.pageSize = this.tablePage.size
      classifyApi.list(this.dataQuery).then(res => {
        this.tableData = res.data.data
        this.tableLoading = false
        // 设置后台返回的分页参数
        pageParamNames.forEach(name =>
          this.$set(this.tablePage, name, parseInt(res.data[name]))
        )
      })
    },
    handlePage() {
      (this.tablePage = {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      }),
        this.getPageList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dataQuery = {
        name: '',
        enable: null
      }
      this.getPageList()
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val
      this.getPageList()
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val
      this.getPageList()
    },
    close() {
      this.open = false
      this.reset()
    },
    reset() {
      this.audioFileList = []
      this.form = {
        id: null,
        name: undefined,
        ident: undefined,
        typeIdent: undefined,
        explainInfo: undefined,
        goodsType: true,
        orderNum: undefined
      }
      if (this.$refs['form']) {
        this.$refs['form'].resetFields()
      }
    }
  }
}
</script>

<style>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.period-table td,
.period-table th {
  text-align: center;
}

@media (max-width: 767px) {
  .el-message-box {
    width: 80% !important;
  }
}

.el-tooltip__popper {
  max-width: 800px;
}
</style>
