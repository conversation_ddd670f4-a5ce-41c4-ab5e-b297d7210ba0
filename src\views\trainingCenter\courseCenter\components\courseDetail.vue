<template>
  <div class="course-container">
    <div class="up-container">
      <div class="class-container">
        <div class="picContent">
          <img :src="courseDetail.courseCover" style="height: 300px" alt="" srcset="" />
        </div>
        <div class="classContent">
          <div class="titleContent">{{ courseDetail.courseName }}</div>
          <div class="descContent">
            <!-- <div class="mainTeac">
              参与角色：{{
                courseDetail.examinee ? courseDetail.examinee.join(",") : ""
              }}
            </div>-->
            <div>
              是否考试：{{ courseDetail.needExam == 1 ? '是' : '否' }}
              <el-button size="small" class="ml-20" @click="goExam" v-if="courseDetail.needExam == 1 && courseDetail.isUserPassExam == false && courseDetail.isCreatedExam == true">
                开始考试
              </el-button>
              <el-button size="small" v-if="courseDetail.isUserPassExam == true" class="ml-20" type="success">考试已通过</el-button>
              <el-button size="small" class="ml-20" @click="goRecord" v-if="courseDetail.needExam == 1">考试记录</el-button>
            </div>
            <div class="ellipsis-container" style="margin-top: 20px" @mouseover="showFullText = true" @mouseout="showFullText = false">
              <div v-if="!showFullText" class="ellipsis-text">课程介绍： {{ courseDetail.courseOverview }}</div>
              <div v-if="showFullText" class="full-text">课程介绍： {{ courseDetail.courseOverview }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="down-container">
      <div class="" style="font-size: 20px; font-weight: 700">目录</div>
      <div v-loading="detailLoading">
        <div class="newCont" style="" v-for="(item, inde) in courseDetail.lessonList" :key="inde">
          <div>
            <span>
              {{ `课时${inde + 1}` }}
            </span>
            <span v-if="item.lessonType == 1" size="small" class="btnStyle mr-15">视频</span>
            <span v-if="item.lessonType == 2" size="small" class="btnStyle mr-15">文本</span>
            <span class="mr-15">
              {{ item.lessonName }}
            </span>
            <span v-if="item.isAllowDownloadDataFile == 1" @click="handleDownload(item)" class="mr-15" style="color: #1890ff; margin-right: 10px; cursor: pointer">
              下载学习资料
            </span>
            <span v-if="item.isAllowDownloadDataFile == 0" class="mr-15" style="color: #808080; margin-right: 10px">下载学习资料</span>
            <span class="mr-15" v-if="!item.isFeedback" @click="submitFeedback(item)" style="color: #1890ff; cursor: pointer">填写反馈</span>
            <span class="mr-15" v-if="item.isFeedback" style="color: gray">已填写反馈</span>
          </div>
          <div style="display: flex; justify-content: space-between; align-items: center">
            <div class="mr-15">
              <span v-if="item.learningStatus === 1">未学习</span>
              <span v-else-if="item.learningStatus === 2">学习中</span>
              <span v-else-if="item.learningStatus === 3">已学习</span>
            </div>
            <div class="mr-15">
              <el-button @click="playVideo(item)" style="font-size: 20px" type="text" :icon="item.lessonType == 2 ? 'el-icon-document' : 'el-icon-video-play'"></el-button>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 填写反馈弹窗 -->
    <el-dialog title="课程反馈" :visible.sync="isShowFeek" :close-on-click-modal="false">
      <el-form :model="formFeed" :rules="rulesFeed" ref="formFeed">
        <el-form-item label="您觉得这节课程对您是否有帮助，是否达到了学习预期？" prop="learningExpectation">
          <el-radio-group v-model="formFeed.learningExpectation">
            <el-radio label="1">非常好</el-radio>
            <el-radio label="2">较好</el-radio>
            <el-radio label="3">一般</el-radio>
            <el-radio label="4">不太好</el-radio>
            <el-radio label="5">较差</el-radio>
          </el-radio-group>
          <!-- <el-radio-group v-else  v-model="i.checkText" class="option"  @change="chooseAnswer" >
            <el-radio  :label="options.optionSort"  v-for="(options, indx) in i.optionList" :key="indx" ><span v-if="options.optionSort>=0">{{String.fromCharCode(65 + options.optionSort) + "、" }}</span>
              <span  class="content_css">{{ options.questionOptionContent }}</span>
            </el-radio >
          </el-radio-group> -->
        </el-form-item>
        <el-form-item label="学习建议" prop="learningOpinion">
          <el-input type="textarea" v-model="formFeed.learningOpinion" maxlength="200" show-word-limit></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="isShowFeek = false">取 消</el-button>
        <el-button type="primary" @click="submitFeed">确 定</el-button>
      </div>
    </el-dialog>
    <!-- 考试结果 -->
    <el-dialog title="考试结果" :visible.sync="isShow" width="30%" @close="isShow = false">
      <span>
        考试分数为：{{ handPaperInfo.examScore }},考试结果：{{ handPaperInfo.passFlag == 1 ? '通过' : '未通过' }},
        <!-- <el-link type="danger" v-if="
          handPaperInfo.passFlag == 0 &&
          (!recordList || recordList.length <= 1)
        " @click="retakeExam">重新考试</el-link> -->
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="isShow = false">取 消</el-button>
        <el-button type="primary" @click="submitResult">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 是否考试确定框 -->
    <el-dialog title="提示" :visible.sync="isShowTested" width="30%" :before-close="handleCloseExam">
      <div style="margin-bottom: 20px">
        1.本次考试共有{{ trainingPaperInfo.questionList ? trainingPaperInfo.questionList.length : 0 }}题，时间{{ trainingPaperInfo.examTimeLimit }}分钟，满分{{
          trainingPaperInfo.examFullMarks
        }}，及格{{ trainingPaperInfo.examPassScore }}
      </div>
      <!-- <div>2.一共有2次考试机会</div> -->
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="confirmExam">确 定</el-button>
      </span>
    </el-dialog>
    <testPaper v-if="dialogFrom.visible" ref="tesPaperRefs" :dialog-param="dialogFrom" @closeDialog="closeDialog"></testPaper>
  </div>
</template>

<script>
  import testPaper from './testPaper';
  import courseApi from '@/api/training/course';
  import examApi from '@/api/training/exam';
  export default {
    name: 'courseDetail',
    components: { testPaper },
    data() {
      return {
        detailLoading: false,
        isShowFeek: false,
        userId: '',
        userName: '',
        userRoleTag: '',
        formFeed: {
          userId: '',
          userRoleTag: '',
          learningExpectation: '',
          learningOpinion: '' //学习建议
        },
        dialogTableVisible: false,
        dialogFormVisible: false,
        showFullText: false,
        totalTestNum: 60,
        totalScore: 100,
        isShowTested: false,
        isTested: '2', // 是否考过试
        // isLearned: "1", // 学习状态
        score: 59,
        isPassed: '未通过',
        isShow: false,
        isShowLearned: false,
        handPaperInfo: {},
        courseDetail: {},

        currentTab: 0,
        className: {
          titleContent: '交付流程及细节详解',
          teacher: '李四',
          limitTime: 60,
          isSignUp: true
        },
        recordList: [],
        trainingPaperInfo: {},
        imageUrl: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg',
        tableLoading: false,
        dialogFrom: {
          visible: false,
          type: 'add'
        },
        rulesFeed: {
          learningExpectation: [{ required: true, message: '请选择一下评价哦', trigger: 'change' }]
        }
      };
    },
    watch: {
      $route(route) {
        console.log(route);
        console.log('000000000000000000000000000000000');
        console.log(route.name);
        if (route.name == 'courseDetail') {
          this.getCourseDetail();
        }
      }
    },
    created() {
      //初始加载用户相关信息
      this.initUserInfo();
      this.getCourseDetail();
    },
    methods: {
      initUserInfo() {
        let sysUserRoles = JSON.parse(window.localStorage.getItem('sysUserRoles'));
        this.userId = sysUserRoles[0].userId; //userId
        this.userName = JSON.parse(window.localStorage.getItem('sysUserInfo')).realname; //userName
        this.userRoleTag = window.localStorage.getItem('roleTag'); //userRoleTag
        console.log('userId', this.userId, this.userName, this.userRoleTag);
      },
      // 获取课程的考试记录，根据记录数量更新考试状态
      // getRecordPage() {
      //   courseApi
      //     .trainingRecord({ courseId: window.localStorage.getItem("courseId") })
      //     .then((res) => {
      //       this.recordList = res.data.recordList;
      //       if (res.data.recordList && res.data.recordList.length > 0) {
      //         this.isTested = "2";
      //       } else {
      //         this.isTested = "1";
      //       }
      //     });
      // },

      submitFeedback(val) {
        //  console.log('val.learningStatus',val.learningStatus);

        if (val.learningStatus === 3) {
          this.isShowFeek = true;
          this.formFeed.courseId = val.courseId;
          this.formFeed.courseLessonId = val.id;
          this.formFeed.userId = this.userId;
          this.formFeed.userName = this.userName;
          this.formFeed.userRoleTag = this.userRoleTag;
        } else {
          this.$message('请先完成课时学习！');
        }
      },
      submitFeed() {
        this.$refs.formFeed.validate((valid) => {
          if (valid) {
            // 表单验证通过，可以提交表单
            // this.isShowFeek = false;
            courseApi
              .addFeed(this.formFeed)
              .then((res) => {
                console.log('res提交成功', res);
                this.$message({
                  message: '提交成功',
                  type: 'success'
                });
                this.isShowFeek = false;
              })
              .catch((err) => {
                console.log('err', err);
                this.$message({
                  message: '提交失败',
                  type: 'error'
                });
              });

            // 这里可以添加提交表单的逻辑
          } else {
            // 表单验证失败，提示用户
            console.log('表单验证失败');
            return false;
          }
        });
      },
      // 考试结果
      submitResult() {
        this.isShow = false;
        this.getCourseDetail(); //刷新页面
      },
      getCourseDetail() {
        this.currentTab = 0;
        if (window.localStorage.getItem('showStudy') == 1) {
          this.isShowLearned = true;
          window.localStorage.removeItem('showStudy');
        } else {
          this.isShowLearned = false;
        }
        // 从这开始
        let sysUserRoles = JSON.parse(window.localStorage.getItem('sysUserRoles'));
        let roleTag = window.localStorage.getItem('roleTag');
        this.detailLoading = true;
        courseApi.Detail({ userId: sysUserRoles[0].userId, roleTag: roleTag, courseId: window.localStorage.getItem('courseId') }).then((res) => {
          this.detailLoading = false;
          this.courseDetail = res.data;
        });
      },
      getTabIndex(index) {
        console.log(index);
        this.currentTab = index;
      },
      //重新考试
      retakeExam() {
        this.isShowTested = true;
        this.getPaper();
      },
      // 开始学习
      // startLearn() {
      //   this.isShowLearned = true;
      // },
      // 跳转视频/文件
      playVideo(item) {
        this.$router.push('/playVideo');
        window.localStorage.setItem('playVideoOrText', JSON.stringify(item));
      },
      handleDownload(item) {
        //文件下载功能
        if (item && item.attachFile && item.attachFile.filePath) {
          window.localStorage.setItem('playVideoOrText', JSON.stringify(item.attachFile.filePath));
          let playInfo = JSON.parse(window.localStorage.getItem('playVideoOrText'));
          this.Download(playInfo);
          console.log;
        } else {
          console.error('文件路径不存在');
        }
      },
      Download(filePath) {
        // window.open 方法中，第二个参数用于指定新窗口或标签页的行为 _blank 是一个特殊的值，表示在新的浏览器标签页中打开链接。
        window.open(filePath, '_blank');
      },
      confirmExam() {
        this.dialogFrom.visible = true;
        this.isShowTested = false;
        this.$nextTick(() => {
          this.$refs.tesPaperRefs.open(this.trainingPaperInfo, window.localStorage.getItem('courseId'));
        });
      },
      // 开始考试
      openTest() {
        this.isShowTested = true;
        this.getPaper();
      },
      // 前往考试
      goExam() {
        this.isShowTested = true;
        this.getPaper();
      },
      //考试记录
      goRecord() {
        let getCourseId = window.localStorage.getItem('courseId');
        this.$router.push({
          path: '/trainingCenter/examRecords',
          query: { courseId: getCourseId }
        });
      },
      getPaper() {
        this.trainingPaperInfo = '';
        let getCourseId = window.localStorage.getItem('courseId');
        // JavaScript 的 Number 类型在处理大数字时会丢失精度使用 BigInt 或第三方库来处理大数字，以保持精度。

        examApi.trainingPaper({ courseId: BigInt(getCourseId), userId: this.userId, roleTag: this.userRoleTag }).then((res) => {
          this.trainingPaperInfo = res.data;
        });
      },
      handleCloseExam(done) {
        this.$confirm('确认关闭？')
          .then((_) => {
            done();
          })
          .catch((_) => {});
      },
      handleClose() {
        this.score = 0;
      },
      closeDialog(info) {
        this.dialogFrom.visible = false;
        this.isShow = true;
        this.handPaperInfo = info;
        // this.getRecordPage();
      }
    }
  };
</script>

<style>
  ::v-deep .el-radio {
    display: block;
    margin: 10px 0;
  }

  .course-container {
    width: 100%;
    /* text-align: center; */
    margin-bottom: 15px;
    background: #fff;
    border-radius: 6px;
    padding: 5px 0;
    padding-left: 5px;
    box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.2);
  }

  .up-container {
    width: 1200px;
    margin: 0 auto 0;
  }

  .down-container {
    width: 1200px;
    margin: 0 auto 0;
  }

  .class-container {
    margin-top: 40px;
    display: flex;
    justify-content: space-between;
    margin-bottom: 50px;
  }

  .classContent {
    margin-top: 20px;
    padding: 0 20px;
    box-sizing: border-box;
    width: 100%;
  }

  .titleContent {
    font-size: 24px;
    margin-bottom: 20px;
  }

  .descContent {
    margin-bottom: 20px;
  }

  .mainTeac {
    margin-bottom: 20px;
  }

  /* .classBtn {
  display: flex;
  justify-content: space-around;
} */
  .signUpBtn {
    cursor: pointer;
    display: inline-block;
    margin-right: 50px;
    color: #fff;
    text-align: center;
    line-height: 40px;
    width: 120px;
    height: 40px;
    background-color: #d9001b;
    border-radius: 5px;
  }

  .class-detail {
    width: 1000px;
    padding: 20px 50px 50px 0px;
    /* border: 1px solid #d9001b; */
    margin-left: 0%;
    margin-top: 100px;
  }

  .tab {
    display: flex;
  }

  .tab div {
    margin-right: 200px;
    padding-bottom: 10px;
    /* padding: 10px; */
    cursor: pointer;
    font-size: 20px;
    font-weight: 700;
  }

  .tab div.active {
    border-bottom: 5px solid #797979;
    /* background-color: lightgray; */
  }

  .tab-content {
    margin-top: 30px;
  }

  .playBtn {
    margin-left: 20px;
    margin-top: -13px;
    cursor: pointer;
  }

  .ml-20 {
    margin-left: 20px;
  }

  .learned {
    margin-left: 20px;
    color: #d9001b;
  }

  .ellipsis-container {
    position: relative;
    width: 100%;
    min-height: 150px;
    overflow: hidden;
  }

  .ellipsis-text {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .full-text {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    width: 100%;
    /* height: auto; */
    /* white-space: pre-wrap; */
    /* background: white; 背景色与页面背景色一致 */
    z-index: 10;
  }

  .newCont {
    display: flex;
    margin-bottom: 20px;
    margin-top: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .btnStyle {
    margin-left: 20px;
    border: 1px solid gray;
    padding: 3px 10px;
    border-radius: 6px;
    color: red;
  }

  .mr-15 {
    margin-right: 20px;
  }
</style>
