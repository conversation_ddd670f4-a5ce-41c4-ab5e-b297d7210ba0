<template>
  <div>
    <el-form label-position="right" label-width="140px" style="width: 100%">
      <el-form-item label="登录账号：" prop="name">
        <template>
          <el-col :xs="24">
            <el-input v-model="form.name" readonly />
          </el-col>
        </template>
      </el-form-item>
      <el-form-item label="托管中心id：" prop="id" v-show="false">
        <el-col :xs="24">
          <el-input v-model="form.id" readonly />
        </el-col>
      </el-form-item>
      <el-form-item label="托管中心名称：" prop="merchantName">
        <el-col :xs="24">
          <el-input v-model="form.merchantName" readonly />
        </el-col>
      </el-form-item>
      <el-form-item label="总负责人：" prop="realName">
        <el-col :xs="24">
          <el-input v-model="form.realName" readonly />
        </el-col>
      </el-form-item>
      <el-row :span="24">
        <el-col :span="12">
          <el-form-item label="市场负责人" prop="marketChargePerson">
            <el-col :xs="24">
              <el-input v-model="form.marketChargePerson" readonly />
            </el-col>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="市场负责人手机号" prop="marketChargePersonPhone">
            <el-col :xs="24">
              <el-input v-model="form.marketChargePersonPhone" readonly />
            </el-col>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :span="24">
        <el-col :span="12">
          <el-form-item label="咨询负责人" prop="consultingChargePerson">
            <el-col :xs="24">
              <el-input v-model="form.consultingChargePerson" readonly />
            </el-col>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="咨询负责人手机号" prop="consultingChargePersonPhone">
            <el-col :xs="24">
              <el-input v-model="form.consultingChargePersonPhone" readonly />
            </el-col>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :span="24">
        <el-col :span="12">
          <el-form-item label="教学负责人" prop="teachingChargePerson">
            <el-col :xs="24">
              <el-input v-model="form.teachingChargePerson" readonly />
            </el-col>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="教学负责人手机号" prop="teachingChargePersonPhone">
            <el-col :xs="24">
              <el-input v-model="form.teachingChargePersonPhone" readonly />
            </el-col>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="负责人身份证：" prop="idCard">
        <el-col :xs="24">
          <el-input v-model="form.idCard" readonly />
        </el-col>
      </el-form-item>
      <el-form-item label="上级编号：" prop="refereeCode">
        <el-col :xs="24">
          <el-input v-model="form.refereeCode" readonly />
        </el-col>
      </el-form-item>
      <el-form-item label="推荐人编号：" prop="marketPartner">
        <el-col :xs="24">
          <el-input v-model="form.marketPartner" readonly />
        </el-col>
      </el-form-item>
      <el-form-item label="开户一级分润比例：">
        <el-col :xs="24">
          <el-input v-model="form.profitRankOne" readonly />
        </el-col>
      </el-form-item>
      <el-form-item label="开户二级分润比例：">
        <el-col :xs="24">
          <el-input v-model="form.profitRankTwo" readonly />
        </el-col>
      </el-form-item>
      <el-form-item label="续费一级分润比例：">
        <el-col :xs="24">
          <el-input v-model="form.reProfitRankOne" readonly />
        </el-col>
      </el-form-item>
      <el-form-item label="续费二级分润比例：">
        <el-col :xs="24">
          <el-input v-model="form.reProfitRankTwo" readonly />
        </el-col>
      </el-form-item>
      <el-form-item label="二级分润：" prop="reProfitRank">
        <template>
          <el-radio disabled v-model="form.reProfitRank" label="1">开通</el-radio>
          <el-radio disabled v-model="form.reProfitRank" label="0">暂停</el-radio>
        </template>
      </el-form-item>
      <el-form-item label="提现银行：" prop="openBank">
        <el-col :xs="24">
          <el-select style="width: 100%" v-model="form.openBank" filterable value-key="value" placeholder="请选择">
            <el-option disabled v-for="(item, index) in bankType" :key="index + '-only'" :label="item.label"
              :value="item.value" />
          </el-select>
        </el-col>
      </el-form-item>
      <el-form-item label="教练 人数：" prop="assistantPersons">
        <el-col :xs="24">
          <el-input v-model="form.assistantPersons" readonly />
        </el-col>
      </el-form-item>
      <el-form-item label="提现账户：" prop="bankNoName">
        <el-col :xs="24">
          <el-input v-model="form.bankNoName" readonly />
        </el-col>
      </el-form-item>
      <el-form-item label="提现账号：" prop="bankNo">
        <el-col :xs="24">
          <el-input v-model="form.bankNo" readonly />
        </el-col>
      </el-form-item>
      <el-form-item label="开户金额：" prop="openMoney">
        <el-col :xs="24">
          <el-input readonly v-model="form.openMoney" type="number" maxlength="10" isNumber="true" min="30000" />
        </el-col>
      </el-form-item>
      <el-form-item label="托管中心级别：" prop="rank">
        <el-col :xs="24">
          <el-select style="width: 100%" v-model="form.rank" filterable value-key="value" placeholder="请选择">
            <el-option disabled v-for="(item, index) in rankType" :key="index + '-o2nly'" :label="item.label"
              :value="item.value" />
          </el-select>
        </el-col>
      </el-form-item>
      <el-form-item label="商户类型：" prop="merchantType">
        <template>
          <el-radio disabled v-model="form.merchantType" label="3">
            个人
          </el-radio>
          <el-radio disabled v-model="form.merchantType" label="2">
            企业
          </el-radio>
        </template>
      </el-form-item>
      <el-form-item label="是否完款：" prop="paymentIsComplete">
        <template>
          <el-radio disabled v-model="form.paymentIsComplete" label="1">
            完款
          </el-radio>
          <el-radio disabled v-model="form.paymentIsComplete" label="0">
            未完款
          </el-radio>
        </template>
      </el-form-item>
      <el-form-item label="场地类型" prop="siteType">
        <el-col>
          <el-input v-model="form.siteType" placeholder="请输入商铺/住宅/写字楼/其他" readonly />
        </el-col>
      </el-form-item>
      <el-form-item label="场地面积" prop="siteArea">
        <el-col>
          <el-input v-model="form.siteArea" readonly>
            <template slot="append">米</template>
          </el-input>
        </el-col>
      </el-form-item>
      <el-form-item label="楼层数" prop="floorNum">
        <el-col>
          <el-input v-model="form.floorNum" readonly>
            <template slot="append">层</template>
          </el-input>
        </el-col>
      </el-form-item>
      <el-form-item label="范围半径：" prop="areaCoverRange">
        <el-col>
          <el-input v-model="form.areaCoverRange" readonly>
            <template slot="append">米</template>
          </el-input>
        </el-col>
      </el-form-item>
      <el-form-item label="签约时间：" prop="signupDate">
        <el-col :xs="24">
          <el-date-picker readonly v-model="form.signupDate" type="date" value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="选择日期">
          </el-date-picker>
        </el-col>
      </el-form-item>
      <el-form-item label="到期时间：" prop="expireDate">
        <el-col :xs="24">
          <el-date-picker readonly v-model="form.expireDate" type="date" value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="选择日期">
          </el-date-picker>
        </el-col>
      </el-form-item>
      <el-form-item label="合同照片:" prop="contractPhoto">
        <el-col :span="20">
          <el-image style="width: 100px; height: 100px" :src="form.contractPhoto[0]"
            :preview-src-list="form.contractPhoto">
          </el-image>
        </el-col>
      </el-form-item>
      <el-form-item label="证件照片:" prop="idCardPhoto">
        <el-col :span="20">
          <el-image style="width: 100px; height: 100px" :src="form.idCardPhoto[0]" :preview-src-list="form.idCardPhoto">
          </el-image>
        </el-col>
      </el-form-item>
      <el-form-item ref="file_Rule" label="付款记录：" prop="paymentPhoto">
        <el-col :xs="24" :span="20">
          <el-image style="width: 100px; height: 100px" :src="form.paymentPhoto[0]"
            :preview-src-list="form.paymentPhoto">
          </el-image>
        </el-col>
      </el-form-item>
      <el-form-item label="所在地区：" prop="name">
        <el-col :xs="24">
          <el-row :gutter="10">
            <el-col :xs="24" :span="8">
              <el-input readonly placeholder="安徽省" v-model="form.province" />
            </el-col>
            <el-col :xs="24" :span="8">
              <el-input readonly placeholder="合肥市" v-model="form.city" />
            </el-col>
            <el-col :xs="24" :span="8">
              <el-input readonly placeholder="包河区" v-model="form.area" />
            </el-col>
          </el-row>
        </el-col>
      </el-form-item>
      <el-form-item label="地址：" prop="address">
        <el-col :xs="24">
          <el-input readonly v-model="form.address" />
        </el-col>
      </el-form-item>
      <el-form-item label="详细地址" prop="detailedAddress">
        <el-col :xs="24">
          <el-input v-model="form.detailedAddress" readonly />
        </el-col>
      </el-form-item>
      <el-form-item label="代理简介：" prop="description">
        <el-col :xs="24">
          <el-input readonly type="textarea" resize="none" :rows="4" v-model="form.description" />
        </el-col>
      </el-form-item>
      <el-form-item label="周边资源：" prop="periphery">
        <el-col :xs="24">
          <el-input readonly type="textarea" resize="none" :rows="4" v-model="form.periphery" />
        </el-col>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import dealerListApi from "@/api/dealerList";
import agentApi from "@/api/agentAddList";

export default {
  name: "FormAddDealer",
  props: {
    // 表单
    form: {
      type: Object
    },
  },
  data() {
    return {
      rankType: [], // 托管中心列表
      bankType: [], //体现银行
    }
  },
  created() {
    dealerListApi.getSelectResult().then((res) => {
      this.rankType = res.data;
    })
    agentApi.categoryType("BankType").then((res) => {
      this.bankType = res.data;
    });
  }
}
</script>

<style scoped>
</style>
