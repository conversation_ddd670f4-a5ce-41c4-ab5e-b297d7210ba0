<!--鼎校教育管理后台-2商户管理-1直推门店-->
<template>
  <div class="app-container">
    <el-form :inline="true" class="container-card" label-width="110px" label-position="left" ref="dataQuery" :model="dataQuery">
      <el-row>
        <el-col :span="8" :xs="24">
          <el-form-item label="门店编号：" prop="merchantCode">
            <el-input id="merchantCode" v-model="dataQuery.merchantCode" name="id" placeholder="门店编号：" />
          </el-form-item>
        </el-col>
        <el-col :span="7" :xs="24">
          <el-form-item label="门店名称：" prop="merchantName" label-width="120px">
            <el-input id="merchantName" v-model="dataQuery.merchantName" name="id" placeholder="请输入门店名称" />
          </el-form-item>
        </el-col>

        <el-col :span="5" :xs="24">
          <el-form-item label="状态:" prop="isEnable">
            <el-select v-model="dataQuery.isEnable" filterable value-key="value" placeholder="请选择" clearable>
              <el-option
                v-for="(item, index) in [
                  { value: 1, label: '开通' },
                  { value: 0, label: '暂停' },
                  { value: -1, label: '系统关闭' },
                  { value: -2, label: '年审关闭' },
                  { value: -3, label: '终止' },
                  { value: -5, label: '到期未续费' }
                ]"
                :key="index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <!-- ----------------------------- -->
      <el-row>
        <el-col :span="8" :xs="24">
          <el-form-item label="注册时间：">
            <el-date-picker
              style="width: 100%"
              v-model="regTime"
              type="daterange"
              value-format="yyyy-MM-dd HH:mm:ss"
              align="right"
              unlink-panels
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            ></el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="7" :xs="24">
          <el-form-item label="回本计划类型:" :label-width="'120px'" prop="paybackPlanType">
            <el-select v-model="dataQuery.paybackPlanType" filterable value-key="value" placeholder="请选择回本计划类型" clearable>
              <el-option
                v-for="(item, index) in [
                  { value: 0, label: '委托回本' },
                  { value: 1, label: '自行回本' }
                ]"
                :key="index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="签署状态:" prop="signingStatus">
            <el-select v-model="dataQuery.signingStatus" filterable value-key="value" placeholder="请选择门店状态" clearable>
              <el-option
                v-for="(item, index) in [
                  { value: 0, label: '已签署' },
                  { value: 1, label: '未签署' },
                  { value: 2, label: '无需签署' }
                ]"
                :key="index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row style="display: flex">
        <el-form-item label="培训是否缴费：" prop="isPay" v-if="isNeedPay">
          <el-select v-model="dataQuery.isPay" value-key="value" placeholder="请选择" clearable>
            <el-option
              v-for="(item, index) in [
                { value: 0, label: '未缴费' },
                { value: 1, label: '已缴费' },
                { value: 2, label: '无需缴费' }
              ]"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label=" " style="flex: 1; text-align: right">
          <el-button type="primary" icon="el-icon-search" @click="fetchData()">搜索</el-button>
          <el-button icon="el-icon-refresh" @click="rest()">重置</el-button>
        </el-form-item>
      </el-row>
    </el-form>
    <el-col :span="24" style="margin-bottom: 30px">
      <el-button
        type="success"
        v-if="roleTag != 'Agent' && checkPermission(['b:areas:areasSchoolList:add', 'b:merchant:OperationsVersion'])"
        icon="el-icon-plus"
        @click="openAdd()"
        size="mini"
      >
        新增
      </el-button>
      <el-button type="warning" icon="el-icon-document-copy" v-if="checkPermission(['b:areas:areasSchoolList:export'])" @click="exportSchool" v-loading="exportLoading" size="mini">
        导出
      </el-button>
      <el-button type="primary" v-if="checkPermission(['b:areas:addmoreMoreInfo'])" icon="el-icon-plus" size="mini" @click="addMore">批量新增</el-button>
    </el-col>

    <el-table
      class="common-table"
      v-loading="tableLoading"
      :data="tableData"
      height="400px"
      style="width: 100%; margin-bottom: 20px"
      row-key="id"
      border
      default-expand-all
      :tree-props="{ list: 'children', hasChildren: 'true' }"
    >
      <el-table-column prop="merchantCode" label="门店编号" width="150px"></el-table-column>
      <el-table-column prop="name" label="登录账号" width="150px"></el-table-column>
      <el-table-column label="操作" width="620">
        <template slot-scope="scope">
          <el-row>
            <el-col :span="6">
              <el-button-group>
                <el-button
                  type="primary"
                  icon="el-icon-view"
                  @click="recharge(scope.row.merchantCode)"
                  size="mini"
                  v-if="scope.row.schoolType !== 3 && checkPermission(['b:areas:areasSchoolList:recharge'])"
                >
                  充值
                </el-button>
                <el-tooltip content="使用剩余的免费门店名额抵用" placement="top">
                  <el-button
                    type="success"
                    size="mini"
                    icon="el-icon-switch-button"
                    v-if="scope.row.paymentIsComplete === '0' && operationsFreeSchoolNum > 0 && checkPermission(['b:merchant:OperationsVersion'])"
                    @click="allGone(scope.row, 3, false)"
                  >
                    <!-- allGone(scope.row.id, 3, false) -->
                    账号开通抵扣
                  </el-button>
                </el-tooltip>
                <el-tooltip content="使用剩余的免费门店名额抵用" placement="top">
                  <el-button
                    type="success"
                    size="mini"
                    icon="el-icon-switch-button"
                    v-if="scope.row.schoolType === 3 && scope.row.paymentIsComplete !== '0' && operationsFreeSchoolNum > 0 && checkPermission(['b:merchant:OperationsVersion'])"
                    @click="allGone(scope.row, 3, true)"
                  >
                    账号开通抵扣续费
                  </el-button>
                </el-tooltip>
              </el-button-group>
            </el-col>
            <el-col :span="8">
              <el-button-group>
                <!-- <el-button
                  icon="el-icon-bank-card"
                  v-if="isNeedPay && scope.row.isPay == 0"
                  type="success"
                  size="mini"
                  :disabled="scope.row.trainingBtnLoading"
                  @click="trainingPay(scope.row, scope.$index)"
                >
                  培训缴费
                </el-button> -->
                <el-tooltip content="签署委托回本计划合同" placement="top">
                  <el-button icon="el-icon-edit" type="primary" v-if="scope.row.signingStatus === 1" @click="getEsignCode(scope.row.id)" size="mini">签署合同</el-button>
                </el-tooltip>
              </el-button-group>
            </el-col>
            <el-col :span="10">
              <el-button-group>
                <!-- <el-tooltip content="签署委托回本计划合同" placement="top">
                  <el-button icon="el-icon-edit" type="primary" v-if="scope.row.signingStatus === 1" @click="getEsignCode(scope.row.id)" size="mini">签署合同</el-button>
                </el-tooltip> -->
                <el-button
                  type="primary"
                  icon="el-icon-view"
                  v-if="roleTag != 'Agent' && checkPermission(['b:areas:areasSchoolList:edit', 'b:merchant:OperationsVersion'])"
                  @click="editSchool(scope.row.id)"
                  size="mini"
                >
                  编辑
                </el-button>
                <el-button
                  type="warning"
                  size="mini"
                  icon="el-icon-switch-button"
                  v-if="
                    (scope.row.isEnable === 0 && scope.row.isCheck === 1) ||
                    (scope.row.isEnable === 0 && scope.row.flowEndStatus === 1) ||
                    scope.row.isEnable === -3 ||
                    scope.row.isEnable === -4
                  "
                  @click="schoolStatus(scope.row.id, scope.row.isEnable, 1)"
                >
                  开通
                </el-button>
                <el-button
                  type="danger"
                  size="mini"
                  icon="el-icon-video-pause"
                  v-if="(scope.row.isEnable === 1 && scope.row.isCheck === 1) || (scope.row.isEnable === 1 && scope.row.flowEndStatus === 1)"
                  @click="schoolStatus(scope.row.id, scope.row.isEnable, 0)"
                >
                  暂停
                </el-button>
                <el-button
                  type="danger"
                  size="mini"
                  icon="el-icon-video-pause"
                  v-if="
                    checkPermission(['b:areas:areasSchoolList:termination']) &&
                    ((scope.row.isEnable === 1 && scope.row.isCheck === 1) || (scope.row.isEnable === 1 && scope.row.flowEndStatus === 1))
                  "
                  @click="schoolStatus(scope.row.id, scope.row.isEnable, -3)"
                >
                  终止
                </el-button>
                <el-button
                  type="danger"
                  size="mini"
                  icon="el-icon-circle-check"
                  v-if="checkPermission(['b:risk:user:clear']) && (scope.row.isEnable === -1 || scope.row.isEnable === -2)"
                  @click="schoolStatus(scope.row.id, scope.row.isEnable, 1)"
                >
                  解封
                </el-button>
                <el-button
                  type="warning"
                  size="mini"
                  icon="el-icon-link"
                  v-if="
                    false &&
                    checkPermission(['b:merchant:areasSchoolList:assignDelivery']) &&
                    ((scope.row.isOldData === 1 && scope.row.isCheck === 1) || (scope.row.isOldData === 0 && scope.row.flowEndStatus === 1))
                  "
                  @click="openAssignDelivery(scope.row.merchantCode)"
                >
                  指定交付中心
                </el-button>
                <el-button
                  type="warning"
                  size="mini"
                  icon="el-icon-link"
                  v-if="
                    false &&
                    checkPermission(['b:merchant:areasSchoolList:liftDelivery']) &&
                    ((scope.row.isOldData === 1 && scope.row.isCheck === 1) || (scope.row.isOldData === 0 && scope.row.flowEndStatus === 1))
                  "
                  @click="liftDelivery(scope.row.merchantCode)"
                >
                  解除交付中心
                </el-button>
              </el-button-group>
            </el-col>
          </el-row>
        </template>
      </el-table-column>
      <el-table-column prop="merchantName" label="门店名称" width="200px"></el-table-column>
      <!-- <el-table-column prop="refereeCode" label="上级编号" width="180"></el-table-column>-->
      <el-table-column prop="realName" label="负责人" show-overflow-tooltip></el-table-column>
      <!-- ----------------------------- -->
      <el-table-column prop="paybackPlanType" label="回本计划类型">
        <template slot-scope="scope">
          <span v-if="scope.row.paybackPlanType === 0">委托回本</span>
          <span v-else>自行回本</span>
        </template>
      </el-table-column>
      <el-table-column prop="address" v-if="!checkPermission(['b:merchant:OperationsVersion'])" label="所在地区" width="200px"></el-table-column>
      <el-table-column label="累计充值学时（节）" v-if="!checkPermission(['b:merchant:OperationsVersion'])" width="80px">
        <template slot-scope="scope">
          <span style="margin-left: 10px">{{ scope.row.schoolType === 3 ? '/' : scope.row.totalCourseHours }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="saleCourseHours" label="已售学时（节）" width="150px"></el-table-column>
      <el-table-column prop="haveCourseHours" label="剩余学时（节）" v-if="!checkPermission(['b:merchant:OperationsVersion'])" width="150px">
        <template slot-scope="scope">
          <span style="margin-left: 10px">{{ scope.row.schoolType == 3 ? '/' : scope.row.haveCourseHours }}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="!checkPermission(['b:merchant:OperationsVersion'])" prop="withdrawMoney" label="账户余额（元）" width="150px"></el-table-column>
      <!--  <el-table-column prop="flowStatus" label="流程状态" align="center"></el-table-column>
            <el-table-column prop="isCheckStatus" label="审核状态">
                    <template slot-scope="scope">
                      <span class="green" v-if="scope.row.isOldData === 0 && scope.row.flowIsEnd === 1">审核流程结束</span>
                      <span class="red" v-else-if="scope.row.isOldData === 0 && scope.row.flowIsEnd === 0">审核中</span>
                      <span class="green" v-else>{{ scope.row.isCheckStatus }}</span>
                    </template>
                  </el-table-column>-->
      <el-table-column prop="deliveryCenterCode" label="所属交付中心"></el-table-column>
      <el-table-column prop="isEnable" label="账户状态" width="150px">
        <template slot-scope="scope">
          <span v-if="scope.row.isEnable === -1 && scope.row.paymentIsComplete === '1'" class="red">系统关闭</span>
          <span v-else-if="scope.row.expireStatus === 1 && isExpireDate(scope.row.expireDate)" class="red">到期未续费</span>
          <span v-else-if="scope.row.isEnable === 1" class="green">开通</span>
          <span v-else-if="scope.row.isEnable === -2" class="red">年审关闭</span>
          <span v-else-if="scope.row.isEnable === -3 || scope.row.isEnable === -4" class="red">终止</span>
          <span v-else-if="scope.row.isEnable === -1 && scope.row.paymentIsComplete === '0'" class="red">等待完款</span>
          <span v-else class="red">暂停</span>
        </template>
      </el-table-column>
      <!-- ----------------------------- -->
      <el-table-column prop="signingStatus" label="签署状态">
        <template slot-scope="scope">
          <span v-if="scope.row.signingStatus === 0">已签署</span>
          <span v-else-if="scope.row.signingStatus === 1">未签署</span>
          <span v-else-if="scope.row.signingStatus === 2">无需签署</span>
        </template>
      </el-table-column>
      <el-table-column v-if="checkPermission(['b:risk:user:areaChannelManagerName'])" prop="channelManagerName" label="渠道管理员" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.channelManagerName ? scope.row.channelManagerName : '' }}</span>
          <el-link v-if="scope.row.channelManagerName" type="primary" @click="showChannelManagerDetail(scope.row)">详情</el-link>
        </template>
      </el-table-column>
      <el-table-column prop="we" label="培训是否缴费" v-if="isNeedPay">
        <template slot-scope="scope">{{ scope.row.isPay == 0 ? '未缴费' : scope.row.isPay == 1 ? '已缴费' : '无需缴费' }}</template>
      </el-table-column>
      <el-table-column prop="regTime" label="添加时间" width="150px" show-overflow-tooltip></el-table-column>
      <el-table-column prop="expireDate" v-if="checkPermission(['b:merchant:OperationsVersion'])" width="150px" label="到期时间" show-overflow-tooltip></el-table-column>
      <!-- <el-table-column prop="canTeachStatus" label="是否教学交付能力">
      </el-table-column>-->
    </el-table>
    <!-- 弹出窗口：签署合同 -->
    <el-dialog title="签署合同" :visible.sync="conDialogVisible" width="20%" center @close="closeDialog">
      <!-- <div style="border-bottom: 1px solid #e0e0e0;"></div> -->
      <el-image class="image" :src="esignCode" v-loading="esignLoading">
        <div slot="placeholder" class="image-slot">
          加载中
          <span class="dot">...</span>
        </div>
      </el-image>
      <p style="text-align: center">请使用手机扫码，签署《委托回本计划合同》</p>
    </el-dialog>
    <!--弹出窗口：指派交付中心-->
    <el-dialog :title="textMap[dialogStatu]" :visible.sync="dialogFormDelivery" width="40%" :close-on-click-modal="false" @close="dialogFormDelivery = false">
      <template>
        <el-input placeholder="请输入编号" style="width: 40%" v-model="searchMerchantCode" clearable></el-input>
        <el-button type="primary" mini style="margin-left: 10px" title="搜索" @click="searchChannel()">搜索</el-button>

        <el-table :data="tableDataDelivery" v-loading="tableLoading2" highlight-current-row ref="singleTable" height="550" border style="width: 100%">
          <el-table-column type="index" width="50"></el-table-column>
          <el-table-column sortable prop="merchantCode" label="编号" width="180"></el-table-column>
          <el-table-column sortable prop="merchantName" label="名称" width="180"></el-table-column>
          <el-table-column label="操作">
            <template slot-scope="scope">
              <el-button type="primary" mini title="指派至该交付中心" @click="assignDelivery(scope.row.merchantCode)">指派</el-button>
            </template>
          </el-table-column>
          <el-table-column sortable prop="regTime" label="添加时间"></el-table-column>
        </el-table>
      </template>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormDelivery = false">取消</el-button>
      </div>
    </el-dialog>

    <el-dialog class="recharge-dialog" title="充值" :visible.sync="showSchoolRecharge" width="70%" :close-on-click-modal="false">
      <el-form v-model="schoolRecharge" label-position="left" label-width="130px" style="width: 100%">
        <el-form-item label="托管中心账户余额：" prop="balance">
          <el-input v-model="schoolRecharge.balance" readonly="readonly" disabled />
        </el-form-item>
        <el-form-item label="充值账户：" prop="merchantCode">
          <el-input v-model="schoolRecharge.merchantCode" readonly="readonly" disabled />
        </el-form-item>
        <el-form-item label="充值金额 ：" prop="rechargeMoney">
          <el-input v-model="schoolRecharge.rechargeMoney" @blur="rechargeMoneyChange()" />
        </el-form-item>
        <el-form-item label="到账学时 ：" prop="rechargeCourse">
          <el-input v-model="schoolRecharge.rechargeCourse" readonly="readonly" disabled />
        </el-form-item>
        <el-form-item label="充值说明 ：" prop="rechargeRemark">
          <el-input v-model="schoolRecharge.rechargeRemark" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="openDialogVisible()">确定</el-button>
      </div>
    </el-dialog>

    <!--渠道管理员信息-->
    <el-dialog title="渠道管理员信息" :visible.sync="dialogVisibleForChannelManager" width="30%">
      <el-form ref="temp" label-position="left" label-width="120px">
        <el-form-item label="名称: ">
          <el-input disabled v-model="channelManager.realName" placeholder="请输入内容"></el-input>
        </el-form-item>
        <el-form-item label="编号: ">
          <el-input disabled v-model="channelManager.channelManagerCode" placeholder="请输入内容"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisibleForChannelManager = false">关 闭</el-button>
      </span>
    </el-dialog>

    <el-dialog class="remark-dialog" title="交易密码确认" :visible.sync="dialogVisible" width="20%" :close-on-click-modal="false">
      <el-input v-model="secondPassWord" show-password></el-input>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="submitRecharge(secondPassWord)">确认</el-button>
      </div>
    </el-dialog>
    <!-- 培训缴费签署合同二维码 -->
    <el-dialog center :close-on-click-modal="false" :close-on-press-escape="false" :visible.sync="expenseVisible" width="440px" @close="closePayDialog">
      <div class="contract-title" slot="title">
        <h4>请将以下二维码发送给创建门店的负责人</h4>
        <h4>签署《培训费合同》</h4>
      </div>
      <div class="img-area" v-loading="contractEsignLoading">
        <img v-if="contractEsignCode" :src="contractEsignCode" alt="" class="qrcode" />
      </div>
      <span slot="footer" class="dialog-footer" v-if="!contractEsignLoading">
        <el-button type="primary" @click="getContractState">他/她已签署</el-button>
      </span>
    </el-dialog>
    <!-- 分页 -->
    <el-col :span="20" style="overflow-x: auto" :xs="24">
      <el-pagination
        :current-page="tablePage.currentPage"
        :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="tablePage.totalItems"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-col>
  </div>
</template>

<script>
  import { mapGetters } from 'vuex';
  import schoolApi from '@/api/areasSchoolList';
  import { ossPrClient } from '@/api/alibaba';
  import { dxSource, pageParamNames } from '@/utils/constants';
  import merchantAccountFlowApi from '@/api/merchantAccountFlow';
  import ls from '@/api/sessionStorage';
  import checkPermission from '@/utils/permission';
  import dynamicFunction from '@/utils/dynamicFunction'; // 动态开启培训费相关功能
  import deliveryCenterApi from '@/api/delivery/deliveryCenter';
  import { Base64 } from '@/utils/base64';
  import store from '@/store';
  import schoolList from '@/api/schoolList';

  export default {
    data() {
      const self = this;
      return {
        operationsFreeSchoolNum: window.localStorage.getItem('operationsFreeSchoolNum'),
        token: store.getters.token,
        channelManager: {},
        conDialogVisible: false,
        esignCode: '', //签署合同二维码
        esignLoading: false, //图片加载
        intervalId: null, //轮询查询状态定时器
        dialogVisibleForChannelManager: false,
        searchMerchantCode: '',
        branchOfficeMerchantCode: '',
        dialogFormDelivery: false,
        textMap: {
          assign: '指定交付中心'
        },
        tableDataDelivery: [],
        tableLoading2: false,
        dialogStatu: 'assign',
        tableLoading: false,
        // 分页
        tablePage: {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null
        },
        tableData: [],
        schoolRecharge: {
          rechargeCourse: 0
        },
        showSchoolRecharge: false,
        exportLoading: false,
        dataQuery: {
          merchantCode: '',
          merchantName: '',
          isEnable: '',
          isPay: '',
          paybackPlanType: '',
          signingStatus: ''
        },
        dialogVisible: false,
        updateMarketDate: {},
        addMarketDate: {},
        showLoginAccount: false,
        address: '',
        province: '',
        secondPassWord: '',
        city: '',
        district: '',
        loaded: false,
        regTime: [],
        roleTag: store.getters.roles[0]['val'],
        expenseVisible: false,
        isPayOpen: true,
        contractProcessId: '', // 创建合同流程id
        contractEsignCode: '',
        contractEsignLoading: false,
        merchantCode: '', //门店编号（培训缴费
        payIntervalId: null,
        timeoutId: null,
        storeId: '',
        isInPayAction: false // 是否在培训缴费操作进行中
      };
    },
    created() {
      // 获取当前时间，如果接口开放，则培训缴费相关功能放开
      schoolList.getTrainingOpenTime().then((res) => {
        const openTime = res.data;
        // const isPayOpen = this.dynamicFunction(openTime);
        this.isPayOpen = openTime == 'Y' ? true : false;
      });
      this.fetchData();
      ossPrClient();
    },
    computed: {
      ...mapGetters(['setpayUrl']),
      isNeedPay() {
        return checkPermission(['admin', 'Operations', 'School']) && this.isPayOpen;
      }
    },
    methods: {
      // 培训缴费
      dynamicFunction,
      trainingPay(item, $index) {
        const that = this;
        // 培训缴费操作是否在进行中， true在，false不在
        if (!that.isInPayAction) {
          that.isInPayAction = true;
          that.clearEsignInterval();
        } else {
          return;
        }

        that.tableData[$index].trainingBtnLoading = true;

        that.storeId = item.id;
        that.merchantCode = item.merchantCode;
        if (that.isInPayAction) {
          schoolList
            .getTrainingContractStatus(item.id)
            .then((res) => {
              if (res.data.status === '-1' && that.isInPayAction) {
                that.expenseVisible = true;
                that.contractEsignLoading = true;
                // 未创建-获取培训费合同
                schoolList.createTraingFeeCode(item.id).then((res) => {
                  if (that.isInPayAction) {
                    that.contractProcessId = res.data;
                    setTimeout(() => {
                      const params = {
                        id: item.id,
                        signFlowId: that.contractProcessId,
                        type: 0 // 是否为第一次创建，0是1否
                      };
                      schoolList.getContractCode(params).then((res) => {
                        if (that.isInPayAction) {
                          that.contractEsignCode = res.data;
                          that.contractEsignLoading = false;
                          that.getPollingTime();
                        }
                      });
                    }, 2000);
                  }
                });
              } else if (res.data.status === '0' && that.isInPayAction) {
                that.expenseVisible = true;
                that.contractEsignLoading = true;
                // 未完成-重新获取培训费合同
                schoolList.reCreateTrainingFeeCode(item.id).then((res) => {
                  if (that.isInPayAction) {
                    let contractData = res.data;
                    if (contractData instanceof Object && contractData.value) {
                      if (contractData.type == '0') {
                        that.contractProcessId = contractData.value;
                        setTimeout(() => {
                          const params = {
                            id: item.id,
                            signFlowId: that.contractProcessId,
                            type: 1 // 是否为第一次创建，0是1否
                          };
                          schoolList.getContractCode(params).then((res) => {
                            if (that.isInPayAction) {
                              that.contractEsignCode = res.data;
                              that.contractEsignLoading = false;
                              that.getPollingTime();
                            }
                          });
                        }, 2000);
                      } else if (contractData.type == '1') {
                        that.contractEsignCode = contractData.value;
                        that.getPollingTime();
                      }
                    }
                  }
                });
              } else if (res.data.status === '1' && that.isInPayAction) {
                const loading = that.$loading({
                  lock: true,
                  text: '即将前往支付···',
                  spinner: 'el-icon-loading',
                  background: 'rgba(0, 0, 0, 0.7)'
                });
                // 跳转至商户平台
                schoolList
                  .getTrainingPayInfo(item.merchantCode)
                  .then((res) => {
                    if (that.isInPayAction) {
                      const split = dxSource.split('##');
                      res.data.dxSource = res.data.registerCallIInfo.appSource + '##' + split[1] + '##' + split[2];
                      let params = JSON.stringify(res.data);
                      let req = 'token=' + this.token + '&params=' + params + '&back=' + window.location.href;
                      //需要编码两遍，避免出现+号等
                      var encode = Base64.encode(Base64.encode(req));
                      setTimeout(() => {
                        window.open(this.setpayUrl + 'product?' + encode, '_blank');
                        loading.close();
                      }, 100);
                    }
                  })
                  .catch((err) => {
                    loading.close();
                  })
                  .finally(() => {
                    that.isInPayAction = false;
                  });
              }
            })
            .finally(() => {
              that.tableData[$index].trainingBtnLoading = false;
            });
        }
      },
      // 获取轮询时间
      getPollingTime() {
        const that = this;
        schoolList.getContractPolling().then((res) => {
          that.clearEsignInterval();
          // 定时器轮询
          that.payIntervalId = setInterval(that.getSigntStatus, 5000);
          // 超时清除定时器
          let s = res.data.time * 60 * 1000;
          that.timeoutId = setTimeout(() => {
            that.clearEsignInterval();
          }, s);
        });
      },
      // 清除定时器
      clearEsignInterval() {
        if (this.payIntervalId) {
          clearInterval(this.payIntervalId);
          this.payIntervalId = null;
        }
      },
      getSigntStatus() {
        // 获取合同签署状态(-1未创建，0未完成，1已完成)
        const that = this;
        if (that.isInPayAction) {
          schoolList.getTrainingContractStatus(this.storeId).then((res) => {
            if (res.data.status === '1' && that.isInPayAction) {
              that.clearEsignInterval();
              that
                .$confirm('合同已签署完成，是否继续前往支付培训费?', '提示', {
                  confirmButtonText: '确定',
                  cancelButtonText: '取消',
                  type: 'success'
                })
                .then((res) => {
                  // 跳转至商户平台
                  schoolList.getTrainingPayInfo(that.merchantCode).then((res) => {
                    const split = dxSource.split('##');
                    res.data.dxSource = res.data.registerCallIInfo.appSource + '##' + split[1] + '##' + split[2];
                    let params = JSON.stringify(res.data);
                    let req = 'token=' + that.token + '&params=' + params + '&back=' + window.location.href;
                    //需要编码两遍，避免出现+号等
                    var encode = Base64.encode(Base64.encode(req));
                    setTimeout(() => {
                      window.open(that.setpayUrl + 'product?' + encode, '_blank');
                    }, 100);
                    setTimeout(() => {
                      that.closePayDialog();
                    }, 2000);
                  });
                })
                .catch((err) => {
                  that.closePayDialog();
                })
                .finally(() => {
                  that.isInPayAction = false;
                });
            }
          });
        }
      },
      // 获取培训合同签署状态(-1未创建，0未完成，1已完成)
      getContractState() {
        let that = this;
        schoolList.getTrainingContractStatus(this.storeId).then((res) => {
          if (res.data.status === '0') {
            this.$message.error('合同还未签署完成！');
          } else {
            that.clearEsignInterval();
            schoolList.getTrainingPayInfo(that.merchantCode).then((res) => {
              const split = dxSource.split('##');
              res.data.dxSource = res.data.registerCallIInfo.appSource + '##' + split[1] + '##' + split[2];
              let params = JSON.stringify(res.data);
              let req = 'token=' + this.token + '&params=' + params + '&back=' + window.location.href;
              //需要编码两遍，避免出现+号等
              var encode = Base64.encode(Base64.encode(req));
              setTimeout(() => {
                window.open(this.setpayUrl + 'product?' + encode, '_blank');
              }, 100);
            });
          }
        });
      },
      closePayDialog() {
        if (this.payIntervalId) {
          this.clearEsignInterval();
          // this.payIntervalId = null;
        }
        this.expenseVisible = false;
        this.contractEsignCode = '';
        this.isInPayAction = false;
      },
      //判断时间是否到期
      isExpireDate(date) {
        if (!date) {
          return false;
        }
        let data = (new Date(date) * 1000) / 1000;
        let now = Date.now();
        console.log(data, now);

        if (data - now > 0) {
          return false;
        } else {
          return true;
        }
      },
      //关闭签署合同弹窗
      closeDialog() {
        this.esignLoading = false;
        this.esignCode = '';
        this.fetchData();
      },
      // 获取e签宝二维码
      async getEsignCode(id) {
        const that = this;
        // console.log(this.addStoreId,"code");
        this.esignLoading = true;
        this.conDialogVisible = true;
        setTimeout(() => window.scrollTo(0, 0), 0);
        // 获取二维码
        let res = await schoolApi.getEsignCode(id);
        this.esignCode = res.data;
        this.esignLoading = false;
        // 轮询查询
        this.handleSetInterval(id);
        //查询轮询时间
        let time = await schoolList.getContractPolling();
        // 超时清除定时器
        let s = time.data.time * 60 * 1000;
        console.log(s);

        // const timeoutId = setTimeout(() => {
        //   console.log(8888);
        //   that.clearEsignInterval();
        // }, s);
      },
      //
      handleSetInterval(id) {
        this.intervalId = setInterval(() => {
          this.getEsignStatus(id);
          console.log('轮询中');
        }, 5000);
      },
      // 判断e签宝二维码是否签署
      getEsignStatus(id) {
        const that = this;
        schoolList.getEsignStatus(id).then((res) => {
          that.esignStatus = res.data.status;
          if (res.data.status == 0) {
            // that.$message.error("未签署");
          } else {
            that.$message.success('签署成功!');
            // 成功清除定时器
            that.clearEsignInterval();
          }
        });
      },
      // 清除轮询定时器，关闭二维码弹窗
      clearEsignInterval() {
        // 清除轮询定时器
        clearInterval(this.intervalId);
        this.intervalId = null;
        // 关闭二维码弹窗
        this.conDialogVisible = false;
        this.closeDialog();
      },
      // 点击跳转到批量新增新页面
      async addMore() {
        let isShow = await this.isNeedRenew();
        if (!isShow) {
          return;
        }
        console.log('跳转到批量新增页面');
        const that = this;
        ls.setItem('addOrUpdate3', true);
        that.$router.push({
          path: '/areas/addMoreInfo',
          query: {
            addOrUpdate: true
          }
        });
      },
      checkPermission,
      openDialogVisible() {
        const that = this;
        if (!that.schoolRecharge.merchantCode) {
          that.$message.error('充值账户不能为空');
          return false;
        }

        if (!that.schoolRecharge.balance) {
          that.$message.error('余额不足');
          return false;
        }

        if (!that.schoolRecharge.rechargeMoney) {
          that.$message.error('充值金额不能为空');
          return false;
        }
        if (that.schoolRecharge.rechargeMoney <= 0) {
          that.$message.error('充值金额必须为0以上的整数');
          return false;
        }

        if (!that.schoolRecharge.rechargeCourse) {
          that.$message.error('到账学时不能为空');
          return false;
        }
        if (that.schoolRecharge.rechargeCourse <= 0) {
          that.$message.error('到账学时必须为20的倍数');
          return false;
        }
        this.dialogVisible = true;
        this.secondPassWord = '';
      },

      showChannelManagerDetail(row) {
        this.channelManager.realName = row.channelManagerRealName;
        this.channelManager.channelManagerCode = row.channelManagerCode;
        this.dialogVisibleForChannelManager = true;
      },
      //打开指定交付中心窗口
      openAssignDelivery(merchantCode) {
        this.dialogFormDelivery = true;
        this.branchOfficeMerchantCode = merchantCode;
        //查询所有交付中心
        deliveryCenterApi
          .allList()
          .then((res) => {
            this.tableDataDelivery = res.data;
          })
          .catch((err) => {});
      },
      //表格数据选中和跳转到指定位置
      searchChannel() {
        for (let i = 0; i < this.tableDataDelivery.length; i++) {
          if (this.tableDataDelivery[i].merchantCode === this.searchMerchantCode) {
            if (!this.$refs['singleTable']) return; //不存在这个表格则返回
            let elTable = this.$refs['singleTable'].$el;
            if (!elTable) return;
            const scrollParent = elTable.querySelector('.el-table__body-wrapper');
            const targetTop = elTable.querySelectorAll('.el-table__body tr')[i].getBoundingClientRect().top; //该行的位置
            const containerTop = elTable.querySelector('.el-table__body').getBoundingClientRect().top; //body的位置
            //跳转
            scrollParent.scrollTop = targetTop - containerTop; //跳转到存下编辑行的ScrollTop
            this.$refs.singleTable.setCurrentRow(this.tableDataDelivery[i]);
          }
        }
      },
      assignDelivery(merchantCode) {
        this.tableLoading2 = true;
        deliveryCenterApi
          .assignDelivery(this.branchOfficeMerchantCode, merchantCode)
          .then((res) => {
            this.tableLoading2 = false;
            this.$message.success('指派成功');
            this.dialogFormDelivery = false;
            this.fetchData();
          })
          .catch((err) => {});
      },
      liftDelivery(merchantCode) {
        this.$confirm('解除通过该账户绑定的交付中心?')
          .then((_) => {
            deliveryCenterApi
              .liftDelivery(merchantCode)
              .then((res) => {
                this.$message.success('解除绑定成功');
                this.fetchData();
              })
              .catch((err) => {});
          })
          .catch((_) => {});
      },

      schoolStatus(id, isEnable, enable) {
        const that = this;
        this.$confirm('确定操作吗?', '修改状态', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          // console.log(id,isEnable,enable);
          schoolApi.openEnable(id, isEnable, enable).then((res) => {
            if (!res.success) {
              that.$message.error(res.message);
              return;
            }
            that.$nextTick(() => that.fetchData());
            that.$message.success(res.message);
          });
        });
      },
      rechargeMoneyChange() {
        schoolApi.getRechargeBackCourse(this.schoolRecharge.merchantCode, this.schoolRecharge.rechargeMoney).then((res) => {
          if (!res.success) {
            this.$message.error('充值金额不能为空');
            return false;
          }
          this.schoolRecharge.rechargeCourse = res.data;
        });
      },
      //重置
      rest() {
        this.$refs.dataQuery.resetFields();
        this.regTime = '';
        this.fetchData();
      },

      // 查询提现列表
      fetchData() {
        const that = this;
        that.tableLoading = true;
        var a = that.regTime;
        if (a != null) {
          that.dataQuery.startRegTime = a[0];
          that.dataQuery.endRegTime = a[1];
        }
        console.log(that.roleTag);
        schoolApi.schoolPage(that.tablePage.currentPage, that.tablePage.size, that.dataQuery).then((res) => {
          // that.tableData = res.data.data;
          // 为培训缴费增加loading状态
          that.tableData = res.data.data.map((item) => {
            return {
              ...item,
              trainingBtnLoading: false
            };
          });
          that.tableLoading = false;
          // 设置后台返回的分页参数
          pageParamNames.forEach((name) => that.$set(that.tablePage, name, parseInt(res.data[name])));
        });
      },
      //1all  2cost  3free
      allGone(item, goneType, renew) {
        if (!renew && item.isPay == 0) {
          schoolList.getTrainingPayInfo(item.merchantCode).then((res) => {
            const split = dxSource.split('##');
            res.data.dxSource = res.data.registerCallIInfo.appSource + '##' + split[1] + '##' + split[2];
            let params = JSON.stringify(res.data);
            let req = 'token=' + this.token + '&params=' + params + '&back=' + window.location.href;
            //需要编码两遍，避免出现+号等
            var encode = Base64.encode(Base64.encode(req));
            window.open(this.setpayUrl + 'product?' + encode, '_blank');
          });
        } else {
          if (goneType === 3) {
            this.$confirm('此操作将消耗您一个免费名额给该合伙人开通或续费一年, 是否继续?', '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            })
              .then(() => {
                schoolApi.allGone(item.id, goneType, renew).then((res) => {
                  if (renew) {
                    this.$message.success('操作成功');
                    this.fetchData();
                  } else {
                    this.$message.success('操作成功');
                    this.fetchData();
                  }
                });
              })
              .catch(() => {
                this.$message({
                  type: 'info',
                  message: '已取消操作'
                });
              });
            return;
          }
          schoolApi.allGone(id, goneType, renew).then((res) => {
            if (checkPermission(['b:merchant:OperationsVersion'])) {
              const split = dxSource.split('##');
              res.data.dxSource = res.data.registerCallIInfo.appSource + '##' + split[1] + '##' + split[2];
              let params = JSON.stringify(res.data);
              let req = 'token=' + this.token + '&params=' + params + '&back=' + window.location.href;
              //需要编码两遍，避免出现+号等
              var encode = Base64.encode(Base64.encode(req));
              window.open(this.setpayUrl + 'product?' + encode, '_blank');
            } else {
              this.$message.success('操作成功');
              this.fetchData();
            }
          });
        }
      },
      exportSchool() {
        const that = this;
        that.exportLoading = true;
        schoolApi.exportAreasSchool(that.dataQuery).then((response) => {
          console.log(response);
          if (!response) {
            this.$notify.error({
              title: '操作失败',
              message: '文件下载失败'
            });
          }
          const url = window.URL.createObjectURL(response);
          const link = document.createElement('a');
          link.style.display = 'none';
          link.href = url; // 获取服务器端的文件名
          link.setAttribute('download', '门店表.xls');
          document.body.appendChild(link);
          link.click();
          that.exportLoading = false;
        });
      },
      async openAdd() {
        let isShow = await this.isNeedRenew();
        if (!isShow) {
          return;
        }
        const that = this;
        ls.setItem('addOrUpdate3', true);
        that.$router.push({
          path: '/areas/areasSchoolEdit',
          query: {
            addOrUpdate: true
          }
        });
      },
      editSchool(id) {
        const that = this;
        ls.setItem('addOrUpdate3', false);
        ls.setItem('areasSchoolId', id);
        that.$router.push({
          path: '/areas/areasSchoolEdit',
          query: {
            id: id,
            addOrUpdate: false
          }
        });
      },

      recharge(merchantCode) {
        const that = this;
        schoolApi.getMerchantAccount().then((res) => {
          that.schoolRecharge.balance = res.data.balance;
          that.schoolRecharge.merchantCode = merchantCode;
          that.showSchoolRecharge = true;
          that.schoolRecharge = {
            balance: that.schoolRecharge.balance,
            merchantCode: that.schoolRecharge.merchantCode,
            rechargeMoney: 0,
            rechargeCourse: 0,
            rechargeRemark: ''
          };
        });
      },
      submitRecharge(secondPassWord) {
        const that = this;
        if (!secondPassWord) {
          this.$message.error('二级密码不能为空');
          return false;
        }

        merchantAccountFlowApi.checkSecondPwd(secondPassWord).then((res) => {
          if (!res.success) {
            this.$message.error('二级密码验证失败');
            return false;
          }
          this.$confirm('确定操作吗?', '提交充值', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            schoolApi.submitRechargeCourse(that.schoolRecharge).then((res) => {
              that.$nextTick(() => that.fetchData());
              that.$message.success('充值成功');
              that.dialogVisible = false;
            });
            that.showSchoolRecharge = false;
          });
        });
      },

      // 分页
      handleSizeChange(val) {
        this.tablePage.size = val;
        this.fetchData();
      },
      handleCurrentChange(val) {
        this.tablePage.currentPage = val;
        this.fetchData();
      },
      async isNeedRenew() {
        let res = await schoolList.getCurrentAdmin();
        if (res.data.expiry != null && res.data.expiry && res.data.roleTag != 'admin' && res.data.schoolType == 3) {
          this.$message.error('门店到期未续费，不能新增门店，请先完成续费');
          return false;
        }
        return true;
      }
    },
    beforeDestroy() {
      if (this.payIntervalId) {
        clearInterval(this.payIntervalId);
        this.payIntervalId = null;
      }
    }
  };
</script>

<style>
  .container-card {
    padding: 15px 15px 15px 15px;
    margin-bottom: 15px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  }
  .info {
    color: #dcdfe6 !important;
  }
  .image {
    width: 250px !important;
    height: 250px !important;
    margin: 0 auto;
    display: block;
  }
  @media screen and (max-width: 767px) {
    .recharge-dialog .el-dialog {
      width: 90% !important;
    }

    .el-message-box {
      width: 80% !important;
    }
  }
  .contract-title {
    text-align: center;
  }
  .contract-title h5 {
    margin-bottom: 0;
  }
  .img-area {
    width: 250px;
    height: 250px;
    margin: 0 auto;
  }
  .qrcode {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
</style>
