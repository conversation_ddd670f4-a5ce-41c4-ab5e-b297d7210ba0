<template>
  <div class="container-card" v-loading="formLoading" element-loading-text="门店新增中" element-loading-spinner="el-icon-loading">
    <!-- 步骤条 -->
    <el-row class="mt-2">
      <el-col :span="20" :offset="2" style="text-align: left">
        <el-steps :active="steps - 1" finish-status="success" process-status="finish">
          <el-step title="基础信息" description="请完成基础信息填写"></el-step>
          <el-step title="补充信息" description="请完成补充信息填写"></el-step>
          <el-step title="完成" description="创建成功"></el-step>
        </el-steps>
      </el-col>
    </el-row>
    <el-divider content-position="left" class="mt-4">
      <i class="el-icon-edit"></i>
      {{ steps == 1 ? '基础信息' : steps == 2 ? '补充信息' : '创建成功' }}
    </el-divider>
    <el-row class="mt-4 container-box">
      <el-col :xs="24" :lg="18">
        <el-form ref="addMarketDate" :rules="steps === 1 ? ruless : rules" :model="addMarketDate" label-position="right" label-width="120px" style="width: 100%">
          <!-- 1 -->
          <el-row v-show="steps === 1">
            <el-form-item label="登录账号：" prop="name">
              <el-col :xs="24" :span="18">
                <el-input v-model="addMarketDate.name" />
              </el-col>
              <!-- <el-col v-if="!addOrUpdate" :xs="24" :span="6">
              <el-button v-if="!addOrUpdate" type="success" style="margin-left: 20px;" @click="openLogin">修改登录账号</el-button>
            </el-col> -->
            </el-form-item>
            <el-form-item label="门店类型" v-if="checkPermission(['admin'])">
              <el-select v-model="addMarketDate.schoolType" filterable value-key="value" placeholder="请选择" clearable @change="changeSchoolType">
                <el-option label="超级俱乐部门店" :value="3" />
              </el-select>
            </el-form-item>
            <el-form-item label="门店名称：" prop="merchantName">
              <el-col :xs="24" :span="18">
                <el-input v-model="addMarketDate.merchantName" placeholder="请输入：市+商圈/路名/学校名称+门店" />
                <span>（例：合肥市祥源广场门店）</span>
              </el-col>
              <!-- <el-col :span="5">合肥市祥源广场门店</el-col>  -->
            </el-form-item>
            <el-form-item label="身份证号码：" prop="idCard">
              <el-col :xs="24" :span="18">
                <el-input v-model="addMarketDate.idCard" />
              </el-col>
            </el-form-item>
            <!-- checkPermission(['admin', 'Operations']) -->
            <!-- 暂时隐藏委托回本计划类型 -->
            <!-- <el-form-item label="回本计划类型：" prop="paybackPlanType" v-if="false">
              <template>
                <el-radio :disabled="isDisabledPaybackPlanType" v-model="paybackPlanType" :label="0" @change="changeReturnPlanType(paybackPlanType)" class="return_plantype">
                  委托回本
                </el-radio>
                <el-tooltip :disabled="isDisabledPaybackPlanType" :visible-arrow="false" placement="bottom-start" effect="light" style="padding-left: 3px">
                  <div slot="content" style="line-height: 18px">
                    可以委托总部资转中心帮您做试课→正课的生源转化，用专业
                    <br />
                    赋能成交，不浪费资源，也不走弯路，轻松起步，快速回本！
                  </div>
                  <i class="el-icon-question return_plantype_radio"></i>
                </el-tooltip>
                <el-radio :disabled="isDisabledPaybackPlanType" v-model="paybackPlanType" :label="1" @change="changeReturnPlanType(paybackPlanType)">自行回本</el-radio>
              </template>
            </el-form-item> -->
          </el-row>
          <!-- 2 -->
          <el-row v-show="steps === 2">
            <el-form-item label="负责人姓名：" prop="realName">
              <el-col :xs="24" :span="18">
                <el-input v-model="addMarketDate.realName" placeholder="请填写真实姓名" :disabled="isSign" :maxlength="50" />
              </el-col>
            </el-form-item>
            <el-form-item label="推广大使编号：" prop="refereeCode">
              <el-col :xs="24" :span="18">
                <el-input v-model="addMarketDate.refereeCode" placeholder="请输入推广大使门店编号" :disabled="isSign" />
              </el-col>
            </el-form-item>
            <el-form-item label="商户类型 ：" v-if="!checkPermission(['b:merchant:OperationsVersion'])" prop="canTeachNum">
              <template>
                <el-radio v-model="merchantType" label="3" @change="changeMerchantType(merchantType)">个人</el-radio>
                <el-radio v-model="merchantType" label="2" @change="changeMerchantType(merchantType)">企业</el-radio>
              </template>
            </el-form-item>
            <el-form-item label="是否教学交付能力：" prop="canTeach" v-if="!checkPermission(['b:merchant:OperationsVersion'])">
              <template>
                <el-radio v-model="radio" label="1" @change="change(radio)">开通</el-radio>
                <el-radio v-model="radio" label="0" @change="change(radio)">暂停</el-radio>
              </template>
            </el-form-item>
            <!-- <el-form-item label="是否集中交付：" prop="centralizedDeliver" v-if="checkPermission(['b:merchant:OperationsVersion'])">
            <template>
              <el-col :span="14">
                <el-radio v-model="deliverRadio" label="1" @change="changeDelivery(deliverRadio)">是</el-radio>
                <el-radio v-model="deliverRadio" label="0" @change="changeDelivery(deliverRadio)">否</el-radio>
              </el-col>
              <el-col :xs="14" type="warning" :span="10">选择是，则该门店所有的学员自动提交到交付中心交付</el-col>
            </template>
          </el-form-item> -->
            <!-- <el-form-item label="签约时间：" v-if="checkPermission(['admin'])" prop="">
            <el-col :xs="24" :span="18">
              <el-date-picker v-if="!addOrUpdate" v-model="updateMarketDate.signupDate" value-format="yyyy-MM-dd HH:mm:ss" type="datetime" placeholder="选择日期"></el-date-picker>
            </el-col>
          </el-form-item>
          <el-form-item label="到期时间：" v-if="checkPermission(['admin'])" prop="">
            <el-col :xs="24" :span="18">
              <el-date-picker v-if="!addOrUpdate" v-model="updateMarketDate.expireDate" value-format="yyyy-MM-dd HH:mm:ss" type="datetime" placeholder="选择日期"></el-date-picker>
            </el-col>
          </el-form-item>
          <el-form-item label="合同照片:">
            <el-col :span="20">
              <el-upload
                ref="clearupload"
                v-loading="uploadLoading"
                list-type="picture-card"
                action=""
                element-loading-text="图片上传中"
                :limit="10"
                :on-exceed="justPictureNum"
                :file-list="fileContractDetailList.name"
                :http-request="uploadDetailHttpContract"
                :on-preview="handlePictureCardPreviewContract"
                :on-remove="handleRemoveDetailContract"
              >
                <i class="el-icon-plus" />
              </el-upload>
            </el-col>
            <el-col :xs="24" :span="4">(*支持多张)</el-col>
          </el-form-item> -->
            <el-form-item label="身份证照片:" prop="idCardPhoto">
              <el-col :span="20">
                <el-upload
                  ref="clearupload"
                  v-loading="uploadLoadingIdCard"
                  list-type="picture-card"
                  action=""
                  element-loading-text="图片上传中"
                  :limit="10"
                  :on-exceed="justPictureNum"
                  :file-list="fileIdCardDetailList.name"
                  :http-request="uploadDetailHttpIdCard"
                  :on-preview="handlePictureCardPreviewIdCard"
                  :on-remove="handleRemoveDetailIdCard"
                >
                  <i class="el-icon-plus" />
                </el-upload>
              </el-col>
              <el-col :xs="24" :span="2">(*支持多张)</el-col>
            </el-form-item>
            <el-form-item label="门店环境照片:" v-if="!checkPermission(['b:merchant:OperationsVersion'])">
              <el-col :span="20">
                <el-upload
                  ref="clearupload"
                  v-loading="uploadLoadingShop"
                  list-type="picture-card"
                  action=""
                  element-loading-text="图片上传中"
                  :limit="10"
                  :on-exceed="justPictureNum"
                  :file-list="fileShopDetailList.name"
                  :http-request="uploadDetailHttpShop"
                  :on-preview="handlePictureCardPreviewShop"
                  :on-remove="handleRemoveDetailShop"
                >
                  <i class="el-icon-plus" />
                </el-upload>
              </el-col>
              <el-col :xs="24" :span="4">(*支持多张)</el-col>
            </el-form-item>

            <el-form-item label="个人职业：" prop="occupation">
              <el-col :span="18" :xs="24">
                <el-select v-model="addMarketDate.occupation" placeholder="请选择" style="width: 100%">
                  <el-option v-for="item in occupationList" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </el-col>
            </el-form-item>

            <el-form-item label="业务开展地：" prop="province">
              <el-col :xs="24" :span="18">
                <el-cascader
                  :disabled="!addOrUpdate"
                  style="width: 100%"
                  :options="regionData"
                  v-model="selectedOptions"
                  :props="{ value: 'label' }"
                  @change="handleRegionChange"
                ></el-cascader>
                <!-- <el-row :gutter="10">
                <el-col :xs="24" :span="8">
                  <el-input
                    placeholder="安徽省"
                    disabled
                    v-if="!addOrUpdate"
                    v-model="updateMarketDate.province"
                  />
                  <el-input
                    placeholder="安徽省"
                    disabled
                    v-if="addOrUpdate"
                    v-model="addMarketDate.province"
                  />
                </el-col>
                <el-col :xs="24" :span="8">
                  <el-input
                    placeholder="合肥市"
                    disabled
                    v-if="!addOrUpdate"
                    v-model="updateMarketDate.city"
                  />
                  <el-input
                    placeholder="合肥市"
                    v-if="addOrUpdate"
                    disabled
                    v-model="addMarketDate.city"
                  />
                </el-col>
                <el-col :xs="24" :span="8">
                  <el-input
                    placeholder="包河区"
                    disabled
                    v-if="!addOrUpdate"
                    v-model="updateMarketDate.area"
                  />
                  <el-input
                    placeholder="包河区"
                    disabled
                    v-if="addOrUpdate"
                    v-model="addMarketDate.area"
                  />
                </el-col>
              </el-row> -->
              </el-col>
              <!-- <el-col :xs="24" :span="17">
                <span style="">请按照真实所在地填写，一旦填写不予更改</span>
              </el-col> -->
            </el-form-item>
            <el-form-item label="经营地址：" prop="address">
              <el-col :xs="24" :span="18">
                <el-input v-model="addMarketDate.address" placeholder="请输入" />
              </el-col>
              <!-- <el-col :xs="24" :span="11" :offset="1"> -->
              <!-- <el-button type="success" size="mini">确定</el-button>
          <el-button type="info" size="mini"  @click="clearAddress()" >清空</el-button> -->
              <!-- <span class="green">(请在此输入详细地址！)</span> -->
              <!-- </el-col> -->
            </el-form-item>
            <!-- <el-form-item label="地图标记" prop="isEnable">
            <div class="amap-page-container">
              <div :style="{ width: '100%', height: '450px' }">
                <el-amap-search-box class="search-box" :search-option="searchOption"
                  :on-search-result="onSearchResult"></el-amap-search-box>
                <el-amap vid="amap" :plugin="plugin" :center="center" class="amap-demo" :events="events">
                  <el-amap-info-window :position="currentWindow.position" :content="currentWindow.content"
                    :visible="currentWindow.visible" :events="currentWindow.events">
                  </el-amap-info-window>
                 
                  <el-amap-marker v-for="(marker, index) in markers" :position="marker"
                    :key="index + '-only'"></el-amap-marker>
               
                  <el-amap-marker v-for="(marker, index) in markers2" :key="index" :position="marker.position"
                    :events="marker.events"></el-amap-marker>
                  <el-amap-info-window v-if="window" :position="window.position" :visible="window.visible"
                    :content="window.content"></el-amap-info-window>
                </el-amap>
              </div>
            </div>
          </el-form-item>
          <el-form-item label="所在经度" prop="longitude">
            <el-col :span="7">
              <el-input disabled v-if="addOrUpdate" v-model="addMarketDate.longitude" />
              <el-input disabled v-if="!addOrUpdate" v-model="updateMarketDate.longitude" />
            </el-col>
          </el-form-item>
          <el-form-item label="所在维度" prop="latitude">
            <el-col :span="7">
              <el-input disabled v-if="addOrUpdate" v-model="addMarketDate.latitude" />
              <el-input disabled v-if="!addOrUpdate" v-model="updateMarketDate.latitude" />
            </el-col>
          </el-form-item> -->
            <el-form-item label="是否完款：" prop="paymentIsComplete" v-if="false">
              <template>
                <el-radio v-model="addMarketDate.paymentIsComplete" @change="changePaymentIsComplete(addMarketDate.paymentIsComplete)" label="1">完款</el-radio>
                <el-radio v-model="addMarketDate.paymentIsComplete" @change="changePaymentIsComplete(addMarketDate.paymentIsComplete)" label="0">未完款</el-radio>
              </template>
            </el-form-item>
            <el-form-item v-if="showPrepaidMoney" label="上级编号：" prop="refereeCode">
              <el-col :xs="24" :span="18">
                <el-input v-model="addMarketDate.refereeCode" />
              </el-col>
            </el-form-item>
            <el-form-item v-if="showPrepaidMoney" label="定金：" prop="prepaidMoney">
              <el-col :xs="24" :span="18">
                <el-input v-model="addMarketDate.prepaidMoney" type="number" :maxlength="10" isNumber="true" min="1" />
              </el-col>
            </el-form-item>
            <el-form-item label="开户金额" prop="openMoney" v-if="!checkPermission(['b:merchant:OperationsVersion'])">
              <el-col :span="7">
                <el-input v-model="addMarketDate.openMoney" type="number" :maxlength="20" isNumber2="true" min="1" />
              </el-col>
            </el-form-item>
            <el-form-item label="孩子的年龄：" prop="childAge">
              <el-col :xs="24" :span="18">
                <el-input v-model="addMarketDate.childAge" placeholder="请输入" :maxlength="50" />
              </el-col>
            </el-form-item>
            <el-form-item label="所在行业：" prop="industry">
              <el-col :xs="24" :span="18">
                <el-input v-model="addMarketDate.industry" placeholder="请输入" :maxlength="50" />
              </el-col>
            </el-form-item>
            <el-form-item label="特长或能力：" prop="ability">
              <el-col :xs="24" :span="18">
                <el-input v-model="addMarketDate.ability" placeholder="请输入" :maxlength="100" />
              </el-col>
            </el-form-item>
            <el-form-item label="从事行业：" prop="engagedIndustry">
              <el-col :xs="24" :span="18">
                <el-input v-model="addMarketDate.engagedIndustry" placeholder="请输入" />
              </el-col>
            </el-form-item>
            <el-form-item label="行业资源：" prop="industryResources">
              <el-col :xs="24" :span="18">
                <el-input v-model="addMarketDate.industryResources" placeholder="请输入" />
              </el-col>
            </el-form-item>
            <el-form-item label="年收入：" prop="annualIncome">
              <el-col :xs="24" :span="18">
                <el-input v-model="addMarketDate.annualIncome" placeholder="请输入" :maxlength="50" />
              </el-col>
            </el-form-item>
            <el-form-item label="个人资源：" prop="personalResources">
              <el-col :xs="24" :span="18">
                <el-input v-model="addMarketDate.personalResources" placeholder="请输入" />
              </el-col>
            </el-form-item>
            <el-form-item label="个人成就：" prop="personalAchievements">
              <el-col :xs="24" :span="18">
                <el-input v-model="addMarketDate.personalAchievements" placeholder="请输入" />
              </el-col>
            </el-form-item>
            <el-form-item label="通过何种方式了解鼎校：" prop="knowChannel">
              <el-col :xs="24" :span="18">
                <el-input v-model="addMarketDate.knowChannel" placeholder="请输入" />
              </el-col>
            </el-form-item>
            <el-form-item label="是否参加过春笋行动：" prop="attendedSpringMeeting">
              <el-col :xs="24" :span="18">
                <el-select v-model="addMarketDate.attendedSpringMeeting" placeholder="请选择" style="width: 100%">
                  <el-option label="是" value="1"></el-option>
                  <el-option label="否" value="0"></el-option>
                </el-select>
              </el-col>
            </el-form-item>
          </el-row>
        </el-form>
      </el-col>
    </el-row>

    <div class="container-footer" style="margin: 30px 0 30px 120px">
      <el-button v-if="steps === 1" size="mini" type="primary" @click="nextStep" class="button-d">下一步</el-button>
      <el-button v-if="steps === 2" size="mini" type="primary" @click="addActiveFun('addMarketDate')" class="button-d">新增</el-button>
      <el-button size="mini" @click="close()" class="button-d">返回</el-button>
    </div>
    <!-- 修改登陆账号弹窗 -->
    <el-dialog title="修改登录账号" :visible.sync="showLoginAccount" width="30%" :close-on-click-modal="false" @close="closeLogin">
      <el-form :ref="'addMarketDate'" :rules="rules" :model="addMarketDate" label-position="left" label-width="120px" style="width: 80%">
        <el-form-item label="原登录账号：" prop="name">
          <!-- <el-input v-if="updateLoginName" v-model="updateLoginName.name" /> -->
          <span>{{ updateLoginName.name }}</span>
        </el-form-item>
        <el-form-item v-show="false">
          <el-input v-model="updateLoginName.id" />
        </el-form-item>
        <el-form-item label="新登录账号：" prop="merchantName">
          <el-input v-model="updateLoginName.newName" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="updateSchoolLogin('updateLoginName')">确定</el-button>
        <el-button size="mini" @click="closeLogin">关闭</el-button>
      </div>
    </el-dialog>
    <el-dialog :visible.sync="dialogUploadVisible" :close-on-click-modal="false">
      <img width="100%" :src="dialogImageUrl" alt />
    </el-dialog>
    <!-- 弹出窗口：选择委托回本时 -->
    <el-dialog :modal="false" title="温馨提示" :visible.sync="paybackPlanTypeDialogVisible" width="30%" center :before-close="handleCloseCommission">
      <p class="dialog-backPlan-title">选择委托回本，签署委托合同，可以委托总部资转中心帮您做试课一正课的生源转化，不浪费资源，也不走弯路，轻松起步，快速回本!</p>
      <p class="dialog-backPlan-tip" title="180天">
        <span class="dialog-backPlan-tip-title">合同有效期：</span>
        <span>180天</span>
        <span style="color: #888">（自门店开通时间起）</span>
      </p>
      <p class="dialog-backPlan-tip" title="10%">
        <span class="dialog-backPlan-tip-title">正课成交金额抽取比例：</span>
        <span>10%</span>
      </p>
      <p class="dialog-backPlan-tip" title="合同到期后或未到期但已回本10000元后，即可自动终止委托回本;若合同签署成功或门店开通后则不可修改回本计划类型">
        <span class="dialog-backPlan-tip-title">其他：</span>
        <span>合同到期后或未到期但已回本10000元后，即可自动终止委托回本，</span>
        <span style="color: red">若合同签署成功或门店开通后则不可修改回本计划类型</span>
      </p>
      <el-row align="middle" justify="center" type="flex" class="mt-20">
        <el-button @click="handleCloseCommission" class="button-d">取消</el-button>
        <el-col :span="2"></el-col>
        <el-button @click="handleConfirmCommission" type="primary" class="button-d">我已知晓</el-button>
      </el-row>
    </el-dialog>
    <!-- 新增门店推广大使冲突确认框 -->
    <el-dialog :visible="dialogRefereeCodeVisible" width="25%" center :show-close="false">
      <p style="font-size: 16px; margin-bottom: 30px">
        登录账号已签署推广大使协议，与您填写的推广大使编号不致，正确编号为
        <span class="red">{{ realRefereeCode }}</span>
        ，请修改后重试
      </p>
      <el-row align="middle" justify="center" type="flex" class="mt-20">
        <el-button @click="dialogRefereeCodeVisible = false" type="primary" class="button-d">确认</el-button>
      </el-row>
    </el-dialog>
    <!-- 合同购买 -->
    <purchaseContract ref="spurchaseDialogVisible" />
  </div>
</template>

<script>
  import { regionData } from 'element-china-area-data';
  import schoolApi from '@/api/schoolList';
  import areasschoolApi from '@/api/areasSchoolList';
  import { ossPrClient } from '@/api/alibaba';
  import ls from '@/api/sessionStorage';
  import store from '@/store';
  import { isvalidPhone, idCard } from '@/utils/validate';
  import checkPermission from '@/utils/permission';
  import dynamicFunction from '@/utils/dynamicFunction';
  import purchaseContract from '@/components/purchaseContract/index.vue'; // 合同购买组件
  import dealerListApi from '@/api/operationsList';
  export default {
    components: {
      purchaseContract
    },
    data() {
      //手机号验证
      var validPhone = (rule, value, callback) => {
        if (this.updateMarketDate.schoolType === 3) {
          callback();
        } else if (!value) {
          callback(new Error('请输入电话号码'));
        } else if (!isvalidPhone(value)) {
          callback(new Error('请输入正确的11位手机号码'));
        } else {
          callback();
        }
      };
      //身份证表达式
      var isIdCard = (rule, value, callback) => {
        if (!value) {
          callback(new Error('请输入身份证号码'));
        } else if (!idCard(value)) {
          callback(new Error('请输入正确的18位身份证号'));
        } else {
          callback();
        }
      };
      //身份证照片校验
      var validateIdCardPhoto = (rule, value, callback) => {
        if (this.fileIdCardDetailList.length === 0) {
          callback(new Error('请上传身份证照片'));
        } else {
          callback();
        }
      };
      //业务开展地校验
      var validateProvince = (rule, value, callback) => {
        console.log(this.selectedOptions, 'this.selectedOptions');
        if (!this.selectedOptions || this.selectedOptions.length === 0) {
          callback(new Error('请选择业务开展地'));
        } else {
          callback();
        }
      };
      const self = this;
      return {
        steps: 1, //步骤条
        token: store.getters.token,
        isSign: false, //是否签署合同
        dialogRefereeCodeVisible: false, //推广大使冲突确认框
        realRefereeCode: '', // 正确的推广大使编号
        //from加载
        formLoading: false,
        showPrepaidMoney: false,
        esignCode: '', //签署合同二维码
        esignLoading: true, //图片加载
        esignStatus: '', //签署状态
        intervalId: null, //轮询查询状态定时器
        conDialogVisible: false, //签署合同弹窗
        paybackPlanTypeDialogVisible: false, //选择委托回本弹窗
        signingStatus: 1, //委托回本合同签署状态 *0已签署1未签署2无需签署//已签署不可修改，其他可修改
        addStoreId: '',
        // 地图搜索分页相关参数
        pageNum: 1,
        pageSize: 5,
        result: [],
        regionData,
        selectedOptions: [],
        currentResult: -1,

        tableLoading: false,
        // 分页
        tablePage: {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null
        },
        realname: localStorage.getItem('sysUserInfo') ? JSON.parse(localStorage.getItem('sysUserInfo')).realname : '',
        ruless: {
          name: [
            {
              required: true,
              message: '请输入登录账号',
              trigger: 'blur'
            },
            {
              validator: validPhone,
              trigger: 'blur'
            }
          ],
          merchantName: [
            {
              required: true,
              message: '请输入门店名称',
              trigger: 'blur'
            }
          ],
          idCard: [
            { required: true, message: '请输入身份证号码', trigger: 'blur' },
            {
              validator: isIdCard,
              trigger: 'blur'
            }
          ]

          // 暂时隐藏委托回本计划类型
          // paybackPlanType: [
          //   {
          //     required: true,
          //     message: '必填',
          //     trigger: 'blur'
          //   }
          // ]
        },
        rules: {
          name: [
            {
              required: true,
              message: '请输入登录账号',
              trigger: 'blur'
            },
            {
              validator: validPhone,
              trigger: 'blur'
            }
          ],
          // 暂时隐藏委托回本计划类型
          // paybackPlanType: [
          //   {
          //     required: true,
          //     message: '必填',
          //     trigger: 'blur'
          //   }
          // ],
          merchantName: [
            {
              required: true,
              message: '请输入门店名称',
              trigger: 'blur'
            }
          ],
          signupDate: [
            {
              required: true,
              message: '必填',
              trigger: 'blur'
            }
          ],
          deliverRadio: [
            {
              required: true,
              message: '必填',
              trigger: 'change'
            }
          ],
          realName: [
            {
              required: true,
              message: '请填写真实姓名',
              trigger: 'blur'
            }
          ],
          idCard: [
            {
              required: true,
              message: '请输入身份证号码',
              trigger: 'change'
            }
          ],
          refereeCode: [],

          openBank: [
            {
              required: true,
              message: '必填',
              trigger: 'change'
            }
          ],
          bankName: [
            {
              required: true,
              message: '必填',
              trigger: 'change'
            }
          ],
          openMoney: [
            {
              required: true,
              message: '必填',
              trigger: 'change'
            }
          ],
          province: [
            {
              required: true,
              message: '请选择业务开展地',
              trigger: 'change'
            },
            {
              validator: validateProvince,
              trigger: 'change'
            }
          ],
          Rank: [
            {
              required: true,
              message: '必填',
              trigger: 'change'
            }
          ],
          merchantType: [
            {
              required: true,
              message: '必填',
              trigger: 'change'
            }
          ],
          areaCoverRange: [
            {
              required: true,
              message: '必填',
              trigger: 'change'
            }
          ],

          expireDate: [
            {
              required: true,
              message: '必填',
              trigger: 'change'
            }
          ],

          contractPhoto: [
            {
              required: true,
              message: '必填',
              trigger: 'change'
            }
          ],
          idCardPhoto: [
            {
              required: true,
              message: '请输入身份证号码',
              trigger: 'change'
            },
            {
              validator: validateIdCardPhoto,
              trigger: 'change'
            }
          ],
          shopPhoto: [
            {
              required: true,
              message: '必填',
              trigger: 'change'
            }
          ],
          city: [
            {
              required: true,
              message: '必填',
              trigger: 'change'
            }
          ],
          area: [
            {
              required: true,
              message: '必填',
              trigger: 'change'
            }
          ],
          address: [
            {
              required: true,
              message: '请填写详细详细经营地址',
              trigger: 'change'
            }
          ],
          prepaidMoney: [
            {
              required: true,
              message: '请填写定金',
              trigger: 'blur'
            }
          ],
          occupation: [
            {
              required: true,
              message: '请选择个人职业',
              trigger: 'change'
            }
          ]
        },
        tableData: [],
        dataQuery: {},
        dialogVisible: false,
        updateMarketDate: {
          paymentIsComplete: '1'
        },
        addOrUpdate: true,
        addMarketDate: {
          address: '',
          merchantType: '3',
          paymentIsComplete: '1',
          paybackPlanType: 1
        },
        showLoginAccount: false,
        deliverRadio: '', //单选框状态 值必须是字符串
        paybackPlanType: 1, //单选框状态
        radio: '0', //单选框状态 值必须是字符串
        fileListPending: [], // 待处理已上传图片信息
        fileDetailListPending: [], // 待处理已上传图片信息
        fileContractDetailList: [], // 上传图片已有图片列表
        fileIdCardDetailList: [],
        fileShopDetailList: [],
        merchantType: '3',
        dialogUploadVisible: false,
        uploadLoading: false, // 上传图片加载按钮
        dialogImageUrl: '', // 上传图片预览
        fileList: [], // 上传图片已有图片列表
        uploadLoadingIdCard: false,
        uploadLoadingShop: false,
        updateLoginName: {},
        merchantCode: '',
        //地图开始
        currentWindow: {
          position: [0, 0],
          content: '',
          events: {},
          visible: false
        },
        markers: [],
        roleTag: '',
        //搜索结果标注
        markers2: [],
        windows: [],
        window: '',
        searchOption: {
          city: '全国',
          citylimit: false
        },
        center: [117.26696, 31.87869],
        zoom: 12,
        lng: 0,
        lat: 0,
        address: '',
        province: '',
        city: '',
        district: '',
        loaded: false,
        circles: [],
        events: {
          click(e) {
            let { lng, lat } = e.lnglat;
            self.lng = lng;
            self.lat = lat;
            // 这里通过高德 SDK 完成。
            var geocoder = new AMap.Geocoder({
              radius: 1000,
              extensions: 'all'
            });
            self.showWindow = false;
            geocoder.getAddress([e.lnglat.lng, e.lnglat.lat], function (status, result) {
              if (status === 'complete' && result.info === 'OK') {
                if (result && result.regeocode) {
                  let city = null;
                  if (result.regeocode.addressComponent.city == '') {
                    city = result.regeocode.addressComponent.district;
                  } else {
                    if (
                      result.regeocode.addressComponent.province == '重庆市' ||
                      result.regeocode.addressComponent.province == '天津市' ||
                      result.regeocode.addressComponent.province == '北京市' ||
                      result.regeocode.addressComponent.province == '上海市'
                    ) {
                      city = result.regeocode.addressComponent.province;
                    } else {
                      city = result.regeocode.addressComponent.city;
                    }
                  }
                  self.markers = [[lng, lat]];
                  if (self.addMarketDate) {
                    self.addMarketDate.latitude = lat;
                    self.addMarketDate.longitude = lng;
                    self.addMarketDate.address = result.regeocode.formattedAddress;
                    self.addMarketDate.province = result.regeocode.addressComponent.province;
                    self.addMarketDate.city = city;
                    self.addMarketDate.area = result.regeocode.addressComponent.district;
                  } else {
                    self.updateMarketDate.latitude = lat;
                    self.updateMarketDate.longitude = lng;
                    self.updateMarketDate.address = result.regeocode.formattedAddress;
                    self.updateMarketDate.province = result.regeocode.addressComponent.province;
                    self.updateMarketDate.city = city;
                    self.updateMarketDate.area = result.regeocode.addressComponent.district;
                  }
                  self.$nextTick();
                }
              } else {
                alert('地址获取失败');
              }
            });
          }
        },
        plugin: [
          {
            enableHighAccuracy: true, //是否使用高精度定位，默认:true
            timeout: 100, //超过10秒后停止定位，默认：无穷大
            maximumAge: 0, //定位结果缓存0毫秒，默认：0
            convert: true, //自动偏移坐标，偏移后的坐标为高德坐标，默认：true
            showButton: true, //显示定位按钮，默认：true
            buttonPosition: 'RB', //定位按钮停靠位置，默认：'LB'，左下角
            showMarker: true, //定位成功后在定位到的位置显示点标记，默认：true
            showCircle: true, //定位成功后用圆圈表示定位精度范围，默认：true
            panToLocation: true, //定位成功后将定位到的位置作为地图中心点，默认：true
            zoomToAccuracy: true, //定位成功后调整地图视野范围使定位位置及精度范围视野内可见，默认：f
            extensions: 'all',
            expandZoomRange: true,
            keyboardEnable: true,
            pName: 'Geolocation',
            campus: [],
            events: {
              init(o) {
                // o 是高德地图定位插件实例
                o.getCurrentPosition((status, result) => {
                  if (result && result.position) {
                    self.lng = result.position.lng;
                    self.lat = result.position.lat;
                    self.markers = [[result.position.lng, result.position.lat]];

                    self.loaded = true;
                    self.$nextTick();
                  }
                });
              }
            }
          }
        ],
        contractEsignLoading: false,
        contractEsignCode: '',
        contractIntervalId: null,
        contractEsignStatus: '',
        signNum: undefined,
        isPayOpen: true, // 培训费相关接口是否开放
        payStatus: -1, // 培训费合同签署状态
        hasSchoolType: '',
        occupationList: []
      };
    },
    created() {
      //this.fetchData();
      ossPrClient();
      this.getCirl();
      this.getOccupation();
      // 获取当前时间，如果接口开放，则培训缴费相关功能放开
      schoolApi.getTrainingOpenTime().then((res) => {
        const openTime = res.data;
        // const isPayOpen = this.dynamicFunction(openTime);
        this.isPayOpen = openTime == 'Y' ? true : false;
      });
      this.signNum = new Set();

      this.getRoleTag();
      //如果当前用户角色是'超级俱乐部-培训中心'或'门店',则放开回本计划类型选项卡
      if (checkPermission(['Operations']) || checkPermission(['School'])) {
        this.paybackPlanType = '';
        this.addMarketDate.paybackPlanType = '';
      }
    },
    computed: {
      // 是否禁用-回本计划类型选项卡
      isDisabledPaybackPlanType() {
        /**
         * 先判断"1.新增"还是"2.修改"
         * │
         * ├─1.新增：
         * │ ├──1.1 如果当前用户角色是'超级俱乐部-培训中心'或'门店'--功能放开
         * │ └──1.2 否则判断是否新门店
         * │    ├──新门店--放开
         * │    └──老门店--禁用
         * └─2.修改：
         *   ├──2.1 如果当前编辑门店不是新门店--功能禁用
         *   └──2.2 否则判断是否未签署合同且未开通门店
         *      ├──未签署合同且未开通门店--放开
         *      └──否则--禁用
         */
        if (this.addOrUpdate) {
          if (checkPermission(['Operations']) || checkPermission(['School'])) {
            return false;
          }
          if (this.addMarketDate.schoolType != '3') {
            return true;
          } else {
            return false;
          }
        } else {
          if (this.updateMarketDate.schoolType != 3) {
            console.log('禁用');

            return true;
          } else {
            if (this.updateMarketDate.isEnable == -1 && (this.signingStatus == 1 || this.signingStatus == 2)) {
              return false;
            } else {
              console.log('禁用2');

              return true;
            }
          }
        }
      }
    },
    methods: {
      handleRegionChange(value) {
        if (value && value.length > 0) {
          this.$nextTick(() => {
            this.$refs.addMarketDate.clearValidate(['province']);
          });
        }
      },
      // 获取个人职业
      getOccupation() {
        dealerListApi.getPersonalOccupation('occupation').then((res) => {
          if (res.data) {
            this.occupationList = res.data.map((item) => ({
              label: item.label,
              value: item.label
            }));
          } else {
            this.occupationList = [];
          }
        });
      },
      fetchData() {},
      checkPermission,
      dynamicFunction,
      // 动态设置标签页标题
      setTitle(title) {
        let i = 0;
        let visitedViews = this.$store.getters.visitedViews;
        visitedViews.forEach((route, index) => {
          if (this.$route.path == route.path) {
            i = index;
          }
        });
        this.$route.meta.title = title;
        visitedViews[i].title = title;
      },
      formatDate(current_datetime) {
        return (
          current_datetime.getFullYear() +
          '-' +
          (current_datetime.getMonth() + 1) +
          '-' +
          current_datetime.getDate() +
          ' ' +
          current_datetime.getHours() +
          ':' +
          current_datetime.getMinutes() +
          ':' +
          current_datetime.getSeconds()
        );
      },
      getCirl() {},
      // 下一步
      nextStep() {
        const that = this;
        if (this.stepsLoading) {
          that.$message.warning('请勿频繁点击');
          return;
        }
        this.stepsLoading = true;
        // if (!that.addMarketDate.name) {
        //   that.$message.error('登录账号不能为空');
        //   return false;
        // }
        // if (!isvalidPhone(that.addMarketDate.name)) {
        //   that.$message.error('登录账号必须为正确的11位手机号码');
        //   return false;
        // }
        // if (!that.addMarketDate.merchantName) {
        //   that.$message.error('门店名称不能为空');
        //   return false;
        // }
        // if (!that.addMarketDate.idCard) {
        //   that.$message.error('身份证号码不能为空');
        //   return false;
        // }
        // if (!that.addMarketDate.paybackPlanType & (that.addMarketDate.paybackPlanType !== 0) & checkPermission(['admin', 'Operations'])) {
        //   that.$message.error('回本计划类型不能为空');
        //   return false;
        // }
        that.$refs['addMarketDate'].validate((valid) => {
          if (valid) {
            areasschoolApi
              .getMerchantPhoneCheck(that.addMarketDate.name)
              .then((res) => {
                if (res.data) {
                  // that.$message.error('该登录账号已存在');
                  this.isSign = true;
                  that.addMarketDate.refereeCode = res.data.refereeCode;
                  that.addMarketDate.realName = res.data.realName;
                }
                this.steps++;
                that.$nextTick(() => {
                  that.stepsLoading = false;
                  that.$refs['addMarketDate'].clearValidate();
                });
              })
              .catch((err) => {
                that.stepsLoading = false;
              });
          } else {
            that.stepsLoading = false;
          }
        });
      },
      // 新增
      addActiveFun(ele) {
        const that = this;
        // 暂时隐藏委托回本计划类型
        // if (!that.addMarketDate.paybackPlanType & (that.addMarketDate.paybackPlanType !== 0) & checkPermission(['admin', 'Operations'])) {
        //   that.$message.error('回本计划类型不能为空');
        //   return false;
        // }
        // if (that.fileContractDetailList.length <= 0) {
        //   that.$message.error('合同照片不能为空');
        //   return false;
        // }
        // if (that.fileIdCardDetailList.length <= 0) {
        //   that.$message.error('身份证照片不能为空');
        //   return false;
        // }
        if (!that.addMarketDate.openMoney && !checkPermission(['b:merchant:OperationsVersion'])) {
          that.$message.error('开户金额不能为空');
          return false;
        }
        if (!that.addMarketDate.name) {
          that.$message.error('登录账户不能为空');
          return false;
        }
        // if (that.addMarketDate.name.length < 11) {
        //   that.$message.error('登录账户不能小于11位');
        //   return false;
        // }
        // if (that.addMarketDate.openMoney < 10000) {
        //   that.$message.error("开户金额不能少于10000");
        //   return false;
        // }
        that.addMarketDate.province = that.selectedOptions[0];
        if (that.selectedOptions[1] == '市辖区') {
          that.addMarketDate.city = that.selectedOptions[0];
        } else {
          that.addMarketDate.city = that.selectedOptions[1];
        }
        that.addMarketDate.area = that.selectedOptions[2];
        // if (that.addMarketDate.province == '' || typeof that.addMarketDate.province === 'undefined') {
        //   that.$message.error('省份不能为空');
        //   return false;
        // }
        // if (that.addMarketDate.city == '' || typeof that.addMarketDate.city === 'undefined') {
        //   that.$message.error('市区不能为空');
        //   return false;
        // }
        that.addMarketDate.contractPhoto = [];
        for (let i = 0; i < that.fileContractDetailList.length; i++) {
          that.addMarketDate.contractPhoto.push(that.fileContractDetailList[i].name);
        }
        that.addMarketDate.idCardPhoto = [];

        for (var i = 0; i < that.fileIdCardDetailList.length; i++) {
          that.addMarketDate.idCardPhoto.push(that.fileIdCardDetailList[i].name);
        }
        that.addMarketDate.shopPhoto = [];
        for (var i = 0; i < that.fileShopDetailList.length; i++) {
          that.addMarketDate.shopPhoto.push(that.fileShopDetailList[i].name);
        }
        // 有数据时清除身份证照片和业务开展地的校验信息
        if (this.fileIdCardDetailList.length > 0) {
          this.$refs.addMarketDate.clearValidate(['idCardPhoto']);
        }
        if (this.selectedOptions && this.selectedOptions.length > 0) {
          this.$refs.addMarketDate.clearValidate(['province']);
        }

        that.$refs[ele].validate((valid) => {
          if (valid) {
            // this.$confirm(
            //   '确定操作吗?',
            //   this.paybackPlanType === 0
            //     ? `新增并前往签署委托回本合同${this.isPayOpen && that.addMarketDate.schoolType ? '及培训费合同' : ''}`
            //     : this.isPayOpen && that.addMarketDate.schoolType
            //     ? '新增并前往签署培训费合同'
            //     : '提交新增',
            //   {
            //     confirmButtonText: '确定',
            //     cancelButtonText: '取消',
            //     type: 'warning'
            //   }
            // )
            // this.$confirm('确定操作吗?', this.paybackPlanType === 0 ? `新增并前往签署委托回本合同` : '提交新增', {
            this.$confirm('确定操作吗?', '提交新增', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            }).then(() => {
              this.addMarketDate.isCheck = 0;
              this.addMarketDate.isEnable = 0;
              // 暂时隐藏委托回本计划类型
              this.addMarketDate.paybackPlanType = 1; // 默认回本计划：自行回本
              this.formLoading = true;
              schoolApi
                .addSchool(this.addMarketDate)
                .then((res) => {
                  console.log(res, 999666);

                  if (!res.success) {
                    that.$message.error(res.message);
                    that.formLoading = false;
                    return false;
                  }
                  that.$nextTick(() => that.fetchData());
                  that.$message.success(res.message);

                  /*              //去除门店审核流程
                            schoolApi.startAndTakeUserTaskByAddSchool({
                            approvalType: "agree",
                            masterData: {
                              open_type: "addDirectSchool",
                              relation_id: res.data,
                              create_time: this.formatDate(new Date())
                            }
                          }).then(() => {
                            that.$message.success("直营门店审批流程开启~");
                          })*/
                  //默认开通
                  if (!checkPermission(['b:merchant:OperationsVersion'])) {
                    schoolApi
                      .openEnable(res.data, 1)
                      .then((response) => {
                        // that.$message.success('开通成功');
                        // ------
                      })
                      .catch((err) => {});
                  }
                  this.formLoading = false;
                  // 暂时隐藏委托回本计划类型
                  // if (this.paybackPlanType === 0) {
                  //   //如果是'委托回本'
                  //   that.addStoreId = res.data;
                  //   that.getEsignCode();
                  // }
                  this.$router.go(-1); //返回上一层
                  store.dispatch('getContract'); // 刷新合同数
                  // 签署合同 --弃用
                  // if (this.paybackPlanType === 0) {
                  //   //如果是'委托回本'
                  //   that.addStoreId = res.data;
                  //   that.getEsignCode();
                  // } else {
                  //   // 自行回本
                  //   that.addStoreId = res.data;
                  //   this.$router.go(-1); //返回上一层
                  // }
                })
                .catch((err) => {
                  console.log(err);

                  that.formLoading = false;
                  let magList = err.message.split('@');
                  let type = magList[1];
                  /**
                   * type: 2:推广大使编号冲突 1:无对应推荐大使信息 3:合同数量不足
                   */
                  if (type === '3') {
                    this.$confirm('您的剩余合同数量不足，如需创建门店，请购买合同后重试', '提示', {
                      confirmButtonText: '去购买',
                      cancelButtonText: '取消',
                      type: 'none'
                    })
                      .then(() => {
                        this.$refs.spurchaseDialogVisible.open();
                      })
                      .catch(() => {
                        // this.$message({
                        //   type: 'info',
                        //   message: '已取消删除'
                        // });
                      });
                    return false;
                  } else if (type === '2') {
                    let msg = magList[0] * 1;
                    // console.log(msg, 666);
                    that.dialogRefereeCodeVisible = true; // 打开推广大使编号冲突 弹窗
                    this.realRefereeCode = msg; // 将真正的推广大使编号冲突的编号赋值
                    return false;
                  }
                  that.$message.error(magList[0]);
                });
            });
          }
        });
      },
      // 获取《委托回本计划合同》e签宝二维码
      async getEsignCode() {
        const that = this;
        // console.log(this.addStoreId,"code");
        // this.esignLoading = true;
        // if (!this.conDialogVisible) {
        //   this.conDialogVisible = true;
        //   setTimeout(() => window.scrollTo(0, 0), 0);
        // }
        //获取e签宝二维码
        let res = await schoolApi.getEsignCode(this.addStoreId);

        // this.esignCode = res.data;
        // this.esignLoading = false;
        // //查询轮询时间
        // schoolApi.getEsignPollingTime(that.addStoreId).then((res) => {
        //   // 定时器轮询
        //   that.intervalId = setInterval(that.getEsignStatus, 5000);
        //   // 超时清除定时器
        //   let s = res.data.time * 60 * 1000;
        //   const timeoutId = setTimeout(() => {
        //     that.clearEsignInterval();
        //   }, s);
        // });
      },
      // 是否签署
      getEsignStatus() {
        const that = this;
        schoolApi
          .getEsignStatus(this.addStoreId)
          .then((res) => {
            that.esignStatus = res.data.status;
            if (res.data.status == 0) {
              // that.$message.error("未签署");
            } else {
              that.$message.success('签署成功!');
              // 成功清除定时器
              that.clearEsignInterval();
              // if (that.isPayOpen && that.hasSchoolType && that.signNum.size < 1) {
              //   that.signNum.add('weituo');
              // } else {
              that.$router.go(-1);
              // }
            }
          })
          .catch((err) => {
            that.$message.error(err.message ? err.message : err.data.message);
          });
      },
      // 点击查看是否签署
      handleIsSign() {
        const that = this;
        this.esignLoading = true;
        schoolApi
          .getEsignStatus(this.addStoreId)
          .then((res) => {
            this.esignLoading = false;
            that.esignStatus = res.data.status;
            if (res.data.status == 0) {
              // that.$message.error("未签署");
              this.$alert('请使用手机扫码，签署《委托回本计划合同》', '提示', {
                confirmButtonText: '确定'
              });
            } else {
              that.$message.success('签署成功!');
              // 成功清除定时器
              that.clearEsignInterval();
              // if (that.isPayOpen && that.hasSchoolType && that.signNum.size < 2) {
              //   that.signNum.add('weituo');
              // } else {
              that.$router.go(-1);
              // }
            }
          })
          .catch((err) => {
            that.$message.error(err.message ? err.message : err.data.message);
          });
      },
      //清除轮询定时器
      clearEsignInterval() {
        clearInterval(this.intervalId);
        this.intervalId = null;
      },
      clearAddress() {
        const that = this;
        that.updateMarketDate.address = '';
      },
      schoolStatus(id, enable) {
        const that = this;
        this.$confirm('确定操作吗?', '修改状态', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          schoolApi.openEnable(id, enable).then((res) => {
            that.$nextTick(() => that.fetchData());
            that.$message.success('修改成功!');
          });
        });
      },
      // 状态改变事件
      changeReturnPlanType(radio) {
        if (radio == '1') {
          this.addMarketDate.paybackPlanType = 1;
          setTimeout(() => {
            this.paybackPlanType = 1;
          });
        } else {
          this.paybackPlanTypeDialogVisible = true;
        }
      },
      //确认委托回本计划
      handleConfirmCommission() {
        this.paybackPlanType = 0;
        this.addMarketDate.paybackPlanType = 0;
        this.paybackPlanTypeDialogVisible = false;
      },
      // 取消委托回本计划
      handleCloseCommission() {
        this.paybackPlanType = '';
        this.addMarketDate.paybackPlanType = '';
        this.paybackPlanTypeDialogVisible = false;
      },
      // 门店类型状态改变
      changeSchoolType(val) {
        this.hasSchoolType = val;
        if (val != '3') {
          this.paybackPlanType = 1;
          this.addMarketDate.paybackPlanType = 1; // 默认回本计划
        }
      },
      // 状态改变事件
      change(radio) {
        if (radio == '1') {
          this.addMarketDate.canTeach = true;
        } else {
          this.addMarketDate.canTeach = false;
        }
      },
      // 状态改变事件
      changeMerchantType(radio) {
        if (radio == '2') {
          this.addMarketDate.merchantType = '3';
        } else {
          this.addMarketDate.merchantType = '2';
        }
      },
      changePaymentIsComplete(paymentIsComplete) {
        console.log('changePaymentIsComplete', paymentIsComplete);
        if (paymentIsComplete == 0) {
          this.showPrepaidMoney = true;
        } else {
          this.showPrepaidMoney = false;
        }
      },

      // 打开修改登陆账号
      // openLogin() {
      //   this.showLoginAccount = true;
      //   this.updateLoginName.id = this.updateMarketDate.id
      // },
      updateSchoolLogin(ele) {
        const that = this;
        that.$refs[ele].validate((valid) => {
          that.updateLoginName.name = that.updateLoginName.newName;
          schoolApi.schoolUpdateLogin(that.updateLoginName).then(() => {
            that.showLoginAccount = false;
            that.$nextTick(() => that.fetchData());
            that.$message.success('修改登录账号成功');
          });
        });
      },

      //--合同照片开始
      // 删除上传图片
      handleRemoveDetailContract(file, fileList) {
        const that = this;
        if (!that.addOrUpdate) {
          that.fileContractDetailList = fileList;
        } else {
          for (let a = 0; a < that.fileDetailListPending.length; a++) {
            that.fileDetailListPending[a].uid === file.uid ? that.fileContractDetailList.splice(a, 1) : '';
          }
        }
      },
      getRoleTag() {
        areasschoolApi.getCurrentAdmin().then((res) => {
          this.roleTag = res.data.roleTag;
          this.merchantCode = res.data.merchantCode;
        });
      },
      // 上传图片预览
      handlePictureCardPreviewContract(file) {
        this.dialogImageUrl = file.url;
        this.dialogUploadVisible = true;
      },
      // 上传图片数量超限
      justPictureNum(file, fileList) {
        this.$message.warning(`当前限制选择1个文件`);
      },

      uploadDetailHttpContract({ file }) {
        this.uploadLoading = true;
        const that = this;
        const fileName = 'manage/' + Date.parse(new Date());
        that.$nextTick(function () {
          ossPrClient()
            .put(fileName, file)
            .then(({ res, url, name }) => {
              if (res && res.status === 200) {
                console.log(`阿里云OSS上传图片成功回调1`, res, url, name);
                if (!that.addOrUpdate) {
                  that.fileContractDetailList.push({
                    uid: file.uid,
                    url: url
                  });
                } else {
                  // 新增上传图片
                  that.fileContractDetailList.push({
                    name
                  });
                  that.updateMarketDate.contractPhoto = name;
                  that.uploadLoading = false;
                }
                that.$nextTick(() => {
                  that.uploadLoading = false;
                });
              }
            })
            .catch((err) => {
              that.$message.error('上传图片失败请检查网络或者刷新页面');
              console.log(`阿里云OSS上传图片失败回调`, err);
              that.uploadLoading = false;
            });
        });
      },
      //---合同上传图片结束

      //--身份证照片开始
      // 删除上传图片
      handleRemoveDetailIdCard(file, fileList) {
        const that = this;
        // 如果上传图片数量小于等于0 则清空数组
        if (that.fileDetailListPending.length <= 0) {
          that.fileIdCardDetailList = [];
          return false;
        }
        for (let a = 0; a < that.fileDetailListPending.length; a++) {
          console.log(that.fileDetailListPending[a].uid, file.uid);

          that.fileDetailListPending[a].uid === file.uid ? that.fileIdCardDetailList.splice(a, 1) : '';
        }
        if (that.fileIdCardDetailList.length === 0) {
          that.$nextTick(() => {
            that.$refs.addMarketDate.validateField('idCardPhoto');
          });
        }
      },
      // 上传图片预览
      handlePictureCardPreviewIdCard(file) {
        this.dialogImageUrl = file.url;
        this.dialogUploadVisible = true;
      },

      uploadDetailHttpIdCard({ file }) {
        this.uploadLoadingIdCard = true;
        const that = this;
        const fileName = 'manage/' + Date.parse(new Date());
        that.$nextTick(function () {
          ossPrClient()
            .put(fileName, file)
            .then(({ res, url, name }) => {
              if (res && res.status === 200) {
                console.log(`阿里云OSS上传图片成功回调1`, res, url, name);
                if (!that.addOrUpdate) {
                  that.fileIdCardDetailList.push({
                    uid: file.uid,
                    url: url
                  });
                } else {
                  // 新增上传图片
                  that.fileIdCardDetailList.push({
                    name
                  });
                  that.updateMarketDate.idCardPhoto = name;
                  that.uploadLoadingIdCard = false;
                }
                that.$nextTick(() => {
                  that.uploadLoadingIdCard = false;
                  that.$refs.addMarketDate.clearValidate(['idCardPhoto']);
                });
              }
            })
            .catch((err) => {
              that.$message.error('上传图片失败请检查网络或者刷新页面');
              console.log(`阿里云OSS上传图片失败回调`, err);
            });
        });
      },
      //---身份证上传图片结束

      //--校区照片开始
      // 删除上传图片
      handleRemoveDetailShop(file, fileList) {
        const that = this;
        if (!that.addOrUpdate) {
          that.fileIdCardDetailList = fileList;
        } else {
          for (let a = 0; a < that.fileDetailListPending.length; a++) {
            that.fileDetailListPending[a].uid === file.uid ? that.fileShopDetailList.splice(a, 1) : '';
          }
        }
      },
      // 上传图片预览
      handlePictureCardPreviewShop(file) {
        this.dialogImageUrl = file.url;
        this.dialogUploadVisible = true;
      },

      uploadDetailHttpShop({ file }) {
        this.uploadLoadingShop = true;
        const that = this;
        const fileName = 'manage/' + Date.parse(new Date());
        that.$nextTick(function () {
          ossPrClient()
            .put(fileName, file)
            .then(({ res, url, name }) => {
              if (res && res.status === 200) {
                console.log(`阿里云OSS上传图片成功回调1`, res, url, name);
                if (!that.addOrUpdate) {
                  that.fileShopDetailList.push({
                    uid: file.uid,
                    url: url
                  });
                } else {
                  // 新增上传图片
                  that.fileShopDetailList.push({
                    name
                  });
                  that.updateMarketDate.shopPhoto = name;
                  that.uploadLoadingShop = false;
                }
                that.$nextTick(() => {
                  that.uploadLoadingShop = false;
                });
              }
            })
            .catch((err) => {
              that.$message.error('上传图片失败请检查网络或者刷新页面');
              console.log(`阿里云OSS上传图片失败回调`, err);
            });
        });
      },
      //---身份证上传图片结束

      // 关闭弹窗
      close() {
        const that = this;
        // that.$router.push({
        //   path: '/areas/areasSchoolList'
        // })
        // 关闭当前标签页
        that.$store.dispatch('delVisitedViews', this.$route);
        that.$router.go(-1);
      },
      // 打开修改登陆账号
      // openLogin() {
      //   this.showLoginAccount = true
      // },
      closeLogin() {
        this.showLoginAccount = false;
      },

      onSearchResult(pois) {
        let latSum = 0;
        let lngSum = 0;
        let markers = [];
        let windows = [];
        let that = this;
        that.result = [];
        if (pois.length > 0) {
          // console.log(pois)
          pois.forEach((poi, index) => {
            const { lng, lat } = poi;
            lngSum += lng;
            latSum += lat;
            markers.push({
              position: [poi.lng, poi.lat],
              events: {
                click() {
                  // console.log(poi)
                  that.windows.forEach((window) => {
                    window.visible = false;
                  });
                  that.window = that.windows[index];
                  that.$nextTick(() => {
                    that.window.visible = true;
                    that.getMarkAddress(poi.lng, poi.lat);
                  });
                }
              }
            });
            // ${ index }<img src="" style="">
            windows.push({
              position: [poi.lng, poi.lat],
              content: `<div class="prompt"><span>${poi.name}</span></div>`,
              visible: false
            });
            that.result.push(poi);
          });
          const center = {
            lng: lngSum / pois.length,
            lat: latSum / pois.length
          };
          this.mapCenter = [center.lng, center.lat];
          this.center = [center.lng, center.lat];
          this.markers2 = markers;
          this.windows = windows;
        }
      },
      getMarkAddress(lng, lat) {
        // 这里通过高德 SDK 完成。
        var that = this;
        var geocoder = new AMap.Geocoder();
        geocoder.getAddress([lng, lat], function (status, result) {
          if (status === 'complete' && result.info === 'OK') {
            if (result && result.regeocode) {
              // console.log(result)
              // that.searchOption.city = result.regeocode.addressComponent.city
              that.center = [lng, lat];
              // 判断是添加还是编辑
              if (that.addOrUpdate) {
                that.addMarketDate.longitude = lng;
                that.addMarketDate.latitude = lat;
                that.addMarketDate.address = result.regeocode.formattedAddress;
                that.addMarketDate.province = result.regeocode.addressComponent.province;
                var reg = RegExp(/省/);
                if (that.addMarketDate.province.match(reg) && result.regeocode.addressComponent.city == '') {
                  that.addMarketDate.city = result.regeocode.addressComponent.district;
                } else {
                  if (
                    result.regeocode.addressComponent.province == '重庆市' ||
                    result.regeocode.addressComponent.province == '天津市' ||
                    result.regeocode.addressComponent.province == '北京市' ||
                    result.regeocode.addressComponent.province == '上海市'
                  ) {
                    that.addMarketDate.city = result.regeocode.addressComponent.province;
                  } else {
                    // 市
                    that.addMarketDate.city = result.regeocode.addressComponent.city;
                  }
                }
                that.addMarketDate.area = result.regeocode.addressComponent.district;
              } else {
                that.updateMarketDate.longitude = lng;
                that.updateMarketDate.latitude = lat;
                that.updateMarketDate.address = result.regeocode.formattedAddress;
                that.updateMarketDate.province = result.regeocode.addressComponent.province;
                var reg = RegExp(/省/);
                if (that.updateMarketDate.province.match(reg) && result.regeocode.addressComponent.city == '') {
                  that.updateMarketDate.city = result.regeocode.addressComponent.district;
                } else {
                  if (
                    result.regeocode.addressComponent.province == '重庆市' ||
                    result.regeocode.addressComponent.province == '天津市' ||
                    result.regeocode.addressComponent.province == '北京市' ||
                    result.regeocode.addressComponent.province == '上海市'
                  ) {
                    that.updateMarketDate.city = result.regeocode.addressComponent.province;
                  } else {
                    // 市
                    that.updateMarketDate.city = result.regeocode.addressComponent.city;
                  }
                }
                that.updateMarketDate.area = result.regeocode.addressComponent.district;
              }
              that.$nextTick();
            }
          } else {
            // alert('地址获取失败')
          }
        });
      },
      // 标注列表
      markList(lng, lat, index) {
        if (this.currentResult != index) {
          this.currentResult = index;
        } else {
          this.currentResult = -1;
        }
        this.getMarkAddress(lng, lat, index);
      },
      changeSizeHandler(size) {
        this.pageSize = size;
      },
      handleCurrentChange(currentPage) {
        this.pageNum = currentPage;
      }
    },
    beforeDestroy() {
      if (this.contractIntervalId) {
        clearInterval(this.contractIntervalId);
        this.contractIntervalId = null;
      }
      if (this.intervalId) {
        clearInterval(this.intervalId);
        this.intervalId = null;
      }
    }
  };
</script>

<style lang="scss" scoped>
  .container-card {
    overflow: hidden;
    height: calc(100vh - 85px);
    padding: 15px 15px 15px 15px;
    margin: 0;

    box-sizing: border-box;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  }

  .map-box {
    position: relative;
  }

  .search-box {
    position: absolute !important;
    top: 10px;
    left: 10px;
  }

  .prompt {
    padding: 10px;
  }

  .result {
    position: absolute;
    top: 0;
    left: 100%;
    width: 300px;
    /* height: 450px; */
    margin-left: 10px;
    background-color: #ffffff;
    border: 1px solid silver;
  }

  .result-list {
    display: flex;
    align-items: center;
    /* margin-bottom: 10px; */
    line-height: 1.6;
    overflow: hidden;
    cursor: pointer;
  }

  .result label {
    display: block;
    width: 19px;
    height: 33px;
    margin-right: 10px;
    line-height: 20px;
    text-align: center;
    font-size: 12px;
    color: #ffffff;
    background: url('https://webapi.amap.com/theme/v1.3/markers/n/mark_bs.png') no-repeat center/cover;
  }

  .result-list.active label {
    background: url('http://webapi.amap.com/theme/v1.3/markers/n/mark_r.png') no-repeat center/cover;
  }

  .list-right {
    flex: 1;
  }

  .result .name {
    font-size: 14px;
    color: #565656;
  }

  .result .address {
    color: #999;
  }

  .search-table {
    height: 380px;
    margin-bottom: 10px !important;
  }

  .search-table th {
    display: none;
  }

  .search-table td {
    padding: 5px 0 !important;
    border-bottom: none;
  }

  .return_plantype {
    margin-right: 0;
  }

  .return_plantype_radio {
    margin-right: 30px;
  }
  .image-d {
    width: 250px !important;
    height: 250px !important;
    margin: 0 auto;
    display: block;
  }
  .border-none {
    padding-top: 50px !important;
    margin-bottom: 0 !important;
    border: none !important;
    height: 100% !important;
    color: #fff !important;
  }

  .v-modal-d {
    position: absolute !important;
    opacity: 1 !important;
    height: 100% !important;
    color: #fff !important;
  }

  .mt-20 {
    margin-top: 20px !important;
  }

  .mt-10 {
    margin-top: 10px !important;
  }

  .mt-5 {
    margin-top: 5px !important;
  }

  .el-dialog__wrapper {
    background-color: rgba(0, 0, 0, 0.4) !important;
  }

  .dialog-backPlan-title {
    font-size: 18px;
    line-height: 30px;
    margin-top: -10px;
    color: #333;
  }
  .dialog-backPlan-tip {
    color: #333;
  }
  .dialog-backPlan-tip .dialog-backPlan-tip-title {
    color: #666;
  }
  .button-d {
    width: 100px !important;
  }
  input[aria-hidden='true'] {
    display: none !important;
  }

  .el-radio:focus:not(.is-focus):not(:active):not(.is-disabled) .el-radio__inner {
    box-shadow: none;
  }

  @media (max-width: 1500px) {
    .dialog-backPlan-title {
      font-size: 16px;
      line-height: 25px;
    }

    .dialog-backPlan-tip {
      font-size: 12px;
    }
  }

  @media (max-width: 767px) {
    .el-message-box {
      width: 80% !important;
    }

    .amap-geolocation-con {
      z-index: 999 !important;
    }

    .dialog-backPlan-title {
      font-size: 14px;
      line-height: 20px;
    }
  }
  .qrCodes {
    display: flex;
    justify-content: center;
  }
  .commission {
    margin-right: 100px;
  }
  .sign-toast {
    text-align: center;
    color: #333;
    font-size: 16px;
  }
  .mt-2 {
    margin-top: 20px;
  }
  .mt-4 {
    margin-top: 40px;
  }
  .mt-6 {
    margin-top: 60px;
  }
  :deep(.el-step__description) {
    color: #a7a2a2; /* 设置描述文字颜色 */
  }
  // :deep(.el-step__description.is-success) {
  //   color: #13ce66;
  // }
  :deep(.el-step__description.is-wait) {
    color: #c0c4cc;
  }
  :deep(.el-step__main) {
    margin-top: 10px;
  }
  .button_d {
    width: 100px !important;
  }
  .container-box {
    width: 100%;
    max-height: calc(100vh - 340px);
    overflow-y: scroll;
  }
  // .container-footer {
  //   position: absolute;
  //   bottom: 0;
  // }
  ::v-deep .is-finish {
    .is-text {
      color: #fff;
      background-color: #1890ff;
    }
  }
  // ::v-deep.el-step__icon.is-icon {
  //   width: auto;
  // }
</style>
