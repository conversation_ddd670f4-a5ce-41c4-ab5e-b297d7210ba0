/**
 * 商品相关接口
 */
import request from '@/utils/request'
// import request from '@/utils/request'

export default {

  // 商品分页查询
  productList(pageNum, pageSize, data) {
    return request({
      url: '/mall/product/page?pageNum=' + pageNum + '&pageSize=' + pageSize + data,
      method: 'GET'
    })
  },

  // 活动新增
  addActive(data) {
    return request({
      url: '/mall/active/limit',
      method: 'POST',
      data
    })
  },
  // 活动修改
  updateActive(data) {
    return request({
      url: '/mall/active/limit',
      method: 'PUT',
      data
    })
  },
  // 活动查询
  queryActive(id) {
    return request({
      url: '/mall/active/limit/' + id,
      method: 'GET'
    })
  },
  // 根据活动类型查询
  queryActiveType(type) {
    return request({
      url: '/mall/active/limit/list/' + type,
      method: 'GET'
    })
  },

  // 商品删除
  deleteAdvice(id) {
    return request({
      url: '/mall/active/limit/' + id,
      method: 'DELETE'
    })
  },
  // 活动分页查询
  activeList(pageNum, pageSize, data) {
    return request({
      url: '/mall/active/limit/list/' + pageNum + '/' + pageSize,
      method: 'GET',
      params: data
    })
  },
  // 活动类型
  activeType() {
    return request({
      url: '/mall/active/limit/type',
      method: 'GET'
    })
  },
  // 商品单位类型
  productUnit() {
    return request({
      url: '/mall/product/unit',
      method: 'GET'
    })
  },

  // 赠品接口
  // 新增接口
  addFreebie(data) {
    return request({
      url: '/mall/freebie',
      method: 'POST',
      data
    })
  },

  // 修改赠品
  updateFreebie(data) {
    return request({
      url: '/mall/freebie',
      method: 'PUT',
      data
    })
  },

  // 删除赠品
  deleteFreebie(id) {
    return request({
      url: '/mall/freebie/' + id,
      method: 'DELETE'
    })
  },

  // 赠品查询列表
  freebieList(pageNum, pageSize, data) {
    return request({
      url: '/mall/freebie/page/' + pageNum + '/' + pageSize + data,
      method: 'GET'
    })
  },
  // 返回赠品类型
  freebieType() {
    return request({
      url: '/mall/freebie/type',
      method: 'GET'
    })
  }

}
