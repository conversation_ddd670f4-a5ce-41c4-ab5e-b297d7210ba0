
import request from '@/utils/request'

export default {

  // 分页查询
  pageList(data) {
    return request({
      url: '/studyroom/web/lamp/voice/list',
      method: 'GET',
      params: data
    })
  },
  //添加
  create(data) {
    return request({
      url: '/studyroom/web/lamp/voice',
      method: 'POST',
      data
    })
  },
  //编辑
  edit(data){
    return request({
      url: '/studyroom/web/lamp/voice',
      method: 'PUT',
      data
    })
  },
  //详情
  detail(id){
    return request({
      url: '/studyroom/web/lamp/voice',
      method: 'GET',
      params:{
        id:id
      }
    })
  },
  //修改状态
  changeStatus(id,enabled){
    return request({
      url: '/studyroom/web/lamp/voice/changeStatus',
      method: 'POST',
      params:{
        id:id,
        enabled:enabled
      }
    })
  }
}
