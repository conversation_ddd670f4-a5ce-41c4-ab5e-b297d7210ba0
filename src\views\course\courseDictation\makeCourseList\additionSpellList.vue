<!--附加题 上传单词 -->
<template>
  <div class="container_box">
    <el-button type="primary" style="margin-bottom: 10px" @click="onAddspell" icon="el-icon-plus" size="mini">添加</el-button>
    <el-table :data="spellData" class="common-table" stripe border v-loading="spellLoading" default-expand-all>
      <el-table-column label="单词ID" prop="id"></el-table-column>
      <el-table-column label="单词" prop="word"></el-table-column>
      <el-table-column label="单词拆分" prop="wordSplit"></el-table-column>
      <el-table-column label="操作" prop="id">
        <template slot-scope="{ row}">
          <el-button type="primary" size="mini" icon="el-icon-edit" @click="editspellItem(row)">编辑</el-button>
          <el-button type="danger" size="mini" icon="el-icon-delete" @click="delSyllableItem(row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog v-loading="submiteStatus2" :title="spellTitle" :visible.sync="spellDialogVisible" width="70%" :close-on-click-modal="false" @close="spellDialogClose">
      <el-form :model="stepReadForm" ref="stepReadForm" label-width="160px" :rules="rules">
        <el-form-item label="单词" prop="word">
          <el-row>
            <el-col :span="12">
              <el-input v-model="stepReadForm.word" type="textarea" @change="inputChange(stepReadForm.word, '0')" placeholder="请输入单词" :rows="3" :spellcheck="false"></el-input>
            </el-col>
          </el-row>
        </el-form-item>
        <el-form-item label="单词拆分" prop="wordSplit">
          <el-row>
            <el-col :span="12">
              <el-input
                v-model="stepReadForm.wordSplit"
                type="textarea"
                @change="inputChange(stepReadForm.wordSplit, '2')"
                :rows="3"
                :spellcheck="false"
                placeholder="请输入单词拆分"
              ></el-input>
            </el-col>
          </el-row>
        </el-form-item>
        <el-form-item>
          <div class="upload_box" v-if="!this.stepReadForm.id">
            <el-upload
              class="upload-demo"
              action="#"
              :show-file-list="true"
              ref="uploadReadWord"
              :file-list="fileListReadWord"
              :on-remove="handlSetRemoveReadWord"
              :http-request="uploadDetailHttp"
              :before-upload="beforeWordUpload"
            >
              <div class="el-upload__text">
                <el-link icon="el-icon-upload2" :underline="false" class="upload_link">excel文件上传</el-link>
              </div>
            </el-upload>
            <el-link class="download_link" :underline="false" icon="el-icon-download" href="https://document.dxznjy.com/applet/zhimi/config/pd_word_addition.xls">模板下载</el-link>
          </div>
        </el-form-item>
        <el-form-item>
          <el-button size="default" @click="spellDialogClose">取消</el-button>
          <el-button size="default" type="primary" @click="submit">确定</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>
<script>
  import courseAdditionList from '@/api/courseAdditionList';
  import { ossPrClient } from '@/api/alibaba';
  export default {
    components: {},
    props: {},
    data() {
      return {
        fileListReadWord: [],
        spellData: [],
        spellTitle: '添加',
        spellLoading: false,
        spellDialogVisible: false,
        courseData: JSON.parse(this.$route.query.courseData),
        submiteStatus2: false,
        stepReadForm: { word: '', wordSplit: '' },
        rules: {
          word: [{ required: true, message: '请输入单词', trigger: 'blur' }],
          wordSplit: [{ required: true, message: '请输入单词拆分', trigger: 'blur' }]
        }
      };
    },
    created() {},
    mounted() {
      this.getTableData();
    },
    watch: {},
    methods: {
      //单词拼读删除
      handlSetRemoveReadWord() {
        this.readWordUrl = '';
      },
      async submit() {
        this.$refs['stepReadForm'].validate(async (valid) => {
          if (valid) {
            if (this.stepReadForm.id) {
              let a = this.stepReadForm.word.split(',');
              if (a.length > 1) return this.$message.warning('请输入一个单词');
              let wordSplit = this.stepReadForm.wordSplit.replaceAll(',', '');
              if (wordSplit != this.stepReadForm.word) {
                this.$message.warning(this.stepReadForm.word + '单词拆分有误');
                return;
              }
              let obj = {
                id: this.stepReadForm.id,
                word: this.stepReadForm.word,
                wordSplit: this.stepReadForm.wordSplit,
                courseCode: this.stepReadForm.courseCode,
                wordAudioUrl: this.stepReadForm.wordAudioUrl,
                isDeleted: this.stepReadForm.isDeleted
              };
              await courseAdditionList.editWord(obj);
            } else {
              let obj = {
                courseCode: this.courseData.courseCode,
                isDeleted: 0,
                wordInfoList: []
              };
              let wordList = this.stepReadForm.word.split(',');
              wordList.forEach((item) => {
                obj.wordInfoList.push({
                  word: item
                });
              });
              let wordSplitList = this.stepReadForm.wordSplit.split('-');
              if (wordList.length < wordSplitList.length) {
                this.$message.warning('请检查单词拆分数据');
                return;
              }
              if (wordList.length > wordSplitList.length) {
                this.$message.warning('划分词不能为空');
                return;
              }
              wordSplitList.forEach((item, i) => {
                obj.wordInfoList[i].wordSplit = item;
              });
              let errWord = this.getMatchWord(obj.wordInfoList);
              if (errWord) {
                this.$message.warning(errWord + '单词拆分有误');
                return;
              }
              await courseAdditionList.addWord(obj);
            }
            this.$message.success('操作成功');
            this.spellDialogClose();
            this.getTableData();
          }
        });
      },
      getMatchWord(data) {
        let errWord = '';
        for (let i = 0; i < data.length; i++) {
          let wordSplit = data[i].wordSplit.replaceAll(',', '');
          console.log('wordSplit', wordSplit);
          if (wordSplit != data[i].word) {
            errWord += data[i].word + ' ';
          }
        }
        return errWord;
      },
      beforeWordUpload(file) {
        if (!this.isExcel(file)) {
          this.$message.error('只能上传excel文件！');
          return false;
        }
      },
      //必须是excel文件
      isExcel(file) {
        return /\.(xlsx|xls)$/.test(file.name);
      },
      inputChange(val, type) {
        let key = type == '0' ? 'word' : type == '1' ? 'syllable' : 'wordSplit';
        let newValue = val.replace(/[^a-zA-Z,-]/g, '');
        newValue = newValue.replace(/,*$/, '');
        newValue = newValue.replace(/,+/g, ',');
        this.stepReadForm[key] = newValue;
      },
      //单词拼读上传成功
      uploadDetailHttp({ file, fileList }) {
        const that = this;
        console.log(file);
        this.wordFile = `https://document.dxznjy.com/applet/zhimi/usercode_${file.uid}.xls`;
        const fileType = file.type.substring(file.type.lastIndexOf('/') + 1);
        const fileName = `manage/${Date.parse(new Date())}.${fileType}`;
        that.$nextTick(function () {
          ossPrClient()
            .put(fileName, file)
            .then(({ res, url, name }) => {
              if (res && res.status === 200) {
                console.log(`阿里云OSS上传图片成功回调1`, file, res, url, name);
                const formData = new FormData();
                formData.append('file', file);
                //   formData.append('analysisType','1');
                this.$refs.uploadReadWord.clearFiles();
                courseAdditionList
                  .AnalysisFile(formData)
                  .then((res) => {
                    if (res.code === 20000) {
                      console.log(res.data);
                      that.stepReadForm.word = res.data.words;
                      that.stepReadForm.wordSplit = res.data.splitWords;
                    }
                  })
                  .catch((err) => {
                    this.fileListReadWord = [];
                    console.log(`解析失败`, err);
                  });
                that.$nextTick(() => {});
              }
            })
            .catch((err) => {
              that.$message.error('上传图片失败请检查网络或者刷新页面');
              console.log(`阿里云OSS上传图片失败回调`, err);
            });
        });
      },
      //删除
      async delSyllableItem(row) {
        await this.$confirm('此操作将永久删除该单词, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        });
        let obj = {
          id: row.id,
          courseCode: row.courseCode,
          isDeleted: 1
        };
        await courseAdditionList.deleteWord(obj);
        this.$message.success('删除成功');
        this.getTableData();
      },

      async getTableData() {
        this.spellLoading = true;
        courseAdditionList.searchWord({ courseCode: this.courseData.courseCode, pageSize: 500, pageNum: 1 }).then((res) => {
          this.spellLoading = false;
          if (res.code === 20000) {
            this.spellData = res.data.data;
          }
        });
      },

      //添加划拼音规则
      onAddspell() {
        this.spellDialogVisible = true;
        this.spellTitle = '添加';
      },
      //编辑划音节
      editspellItem(row) {
        this.spellDialogVisible = true;
        this.stepReadForm = row;
        this.spellTitle = '编辑';
      },
      //关闭弹窗
      spellDialogClose() {
        this.stepReadForm = { word: '', wordSplit: '' };
        this.spellDialogVisible = false;
        this.getTableData();
      }
    }
  };
</script>
<style lang="less" scoped>
  .download_link {
    color: #13ce66;
  }

  .upload_link {
    color: #1890ff;
  }
</style>
