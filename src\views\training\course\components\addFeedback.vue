<template>
  <div class="add-feedback">
    <el-dialog :title="titleStatus" :visible.sync="dialogVisible" width="35%" @close="handleClose">
      <div class="dialog-content">
        <el-form ref="form" label-width="60px" :model="form" :rules="formRules">
          <el-form-item label="备注" prop="remark">
            <el-input type="textarea" v-model="form.remark" placeholder="请输入备注" rows="8" maxlength="200" show-word-limit></el-input>
          </el-form-item>
          <el-form-item style="text-align: center">
            <el-button v-loading="saveLoading" type="primary" @click="onSave">保存</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import courseApi from '@/api/training/course';
  export default {
    name: 'AddFeedback',
    components: {},
    props: {
      dialogVisible: {
        type: <PERSON>olean,
        default: false
      },
      isEdit: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        form: {
          remark: ''
        },
        courseFeedbackId: '',
        formRules: {
          remark: [{ required: true, message: '请输入备注', trigger: 'blur' }]
        },
        saveLoading: false
      };
    },
    computed: {
      titleStatus() {
        return this.isEdit ? '修改备注' : '添加备注';
      }
    },
    methods: {
      open(info) {
        this.form.remark = info.remark;
        this.courseFeedbackId = info.id;
      },
      handleClose() {
        this.$refs['form'].resetFields();
        this.$emit('closeDialog');
      },
      // 保存备注信息
      onSave() {
        this.$refs['form'].validate((valid) => {
          if (valid) {
            this.saveLoading = true;
            let params = { ...this.form, id: this.courseFeedbackId };
            courseApi
              .feedbackRemark(params)
              .then((res) => {
                this.$message.success('保存成功！');
                this.$emit('closeDialog', 'success');
              })
              .finally(() => {
                this.saveLoading = false;
              });
          }
        });
      }
    }
  };
</script>

<style scoped></style>
