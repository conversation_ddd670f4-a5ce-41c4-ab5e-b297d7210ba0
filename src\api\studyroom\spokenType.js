import request from '@/utils/request'
//口语测评类型
export default {

  getType(data) {
    return request({
      url: '/studyroom/web/spoken/getType',
      method: 'GET',
      params: data
    })
  },
  //添加type
  createType(data) {
    return request({
      url: '/studyroom/web/spoken/createType',
      method: 'POST',
      params: data
    })
  },
  //详情
  detail(id) {
    return request({
      url: '/studyroom/web/spoken/typeDetail',
      method: 'GET',
      params: {
        id:id
      }
    })
  },

}
