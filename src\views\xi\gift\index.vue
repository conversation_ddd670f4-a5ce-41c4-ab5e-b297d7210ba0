<template>
  <div class="app-container">
    <el-form :inline="true" ref="queryForm" class="container-card" label-width="110px">
      <el-form-item label="礼包名称：">
        <el-input v-model="dataQuery.name" placeholder="请输入名称" clearable />
      </el-form-item>
      <el-form-item label="类型：">
        <el-select v-model="dataQuery.type" clearable>
          <el-option v-for="item in typeList" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-re" @click="resetQuery()">重置</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="search()">搜索</el-button>
      </el-form-item>
    </el-form>
    <el-col :span="24" style="margin-bottom: 30px">
      <el-button type="success" icon="el-icon-plus" size="mini" @click="addBtn()">新增</el-button>
    </el-col>
    <el-table class="common-table" v-loading="tableLoading" :data="tableData" style="width: 100%;margin-bottom: 20px;"
      row-key="id" border default-expand-all>
      <el-table-column prop="id" label="ID"></el-table-column>
      <el-table-column prop="name" label="礼包名称" />
      <el-table-column prop="withdrawnBonus" label="操作" width="480">
        <template slot-scope="scope">
          <el-button type="primary" size="mini" @click="editBtn(scope.row.id)">编辑
          </el-button>
          <el-button type="danger" size="mini" @click="handleDelete(scope.row.id)">删除
          </el-button>
        </template>
      </el-table-column>
      <el-table-column prop="type" label="礼包类型" :formatter="typeFormat" />
      <el-table-column prop="completeNum" label="完成项数"></el-table-column>
      <el-table-column prop="createTime" label="创建时间"></el-table-column>
    </el-table>
    <!-- 分页 -->
    <el-col :span="24" style="margin-bottom: 20px">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>

    <!-- 修改或者新增弹窗 -->
    <el-dialog :title="form.id != null ? '编辑' : '添加' + '礼包'" :visible.sync="open" width="70%" @close="close">
      <el-form ref="form" :model="form" :rules="rules" label-width="120px" style="width: 70%;">
        <el-form-item label="礼包名称：" prop="name">
          <el-input v-model="form.name" />
        </el-form-item>
        <el-form-item label="礼包类型：" prop="type">
          <el-select v-model="form.type">
            <el-option v-for="item in typeList" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="完成项数：" prop="completeNum" v-if="form.type === 1">
          <el-input-number v-model="form.completeNum" :min="0" :step="1" />
        </el-form-item>
        <el-form-item label="选择物品：" prop="goodsIds">
          <el-tag :key="goods.id" v-for="(goods, index) in currentGoods" closable :disable-transitions="false"
            @close="handleClose(index)">
            {{ goods.name }}
          </el-tag>
          <el-button class="button-new-tag" size="small" @click="goodsOpen"> + 查看物品</el-button>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="open = false">取 消</el-button>
      </div>
    </el-dialog>

    <el-drawer title="选择虚拟物品" :visible.sync="goods.open" direction="rtl" size="50%">
      <div style="padding-left: 25px">
        <el-form :model="goods.dataQuery" ref="queryForm" :inline="true">
          <el-form-item label="物品名称：">
            <el-input v-model="goods.dataQuery.name" clearable></el-input>
          </el-form-item>
          <el-form-item label="商品分类：">
            <el-select v-model="goods.dataQuery.classify" clearable>
              <el-option v-for="item in goods.classifyList" :label="item.name" :value="item.ident" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="goodsSearch">查询</el-button>
            <el-button @click="resetGoodsSearch">重置</el-button>
          </el-form-item>
        </el-form>
        <el-table v-loading="goods.tableLoading" :data="goods.tableData" ref="multipleTable"
          @selection-change="handleSelectionChange" border fit highlight-current-row style="width: 100%">
          <el-table-column type="selection" width="40"></el-table-column>
          <el-table-column prop="id" label="Id" width="180px" />
          <el-table-column prop="name" label="商品名称" />
          <el-table-column prop="type" label="商品类型" :formatter="goodsTypeFormat" />
          <el-table-column prop="isShow" label="前端展示">
            <template slot-scope="scope">
              {{ scope.row.isShow ? '是' : '否' }}
            </template>
          </el-table-column>
          <el-table-column prop="validDays" label="有效天数">
            <template slot-scope="scope">
              <span v-if="scope.row.isAll || scope.row.type === 1">永久</span>
              <span v-else>{{ scope.row.validDays }}</span>
            </template>
          </el-table-column>
        </el-table>
        <!-- 分页 -->
        <el-col :span="24" style="margin-bottom: 20px">
          <el-pagination :current-page="goods.tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
            layout="total, sizes, prev, pager, next, jumper" :total="goods.tablePage.totalItems"
            @size-change="goodsHandleSizeChange" @current-change="goodsHandleCurrentChange" />
        </el-col>
        <span slot="footer" class="dialog-footer">
          <el-button @click="goods.open = false">取 消</el-button>
          <el-button type="primary" @click="confirmQuestionSelect">确定</el-button>
        </span>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import giftApi from '@/api/xi/gift'
import { pageParamNames } from '@/utils/constants'
import goodsApi from '@/api/xi/goods'
import goodsclassifyApi from '@/api/xi/goodsclassify'

export default {
  name: 'goods',
  data() {
    return {
      currentGoods: [],
      goods: {
        multipleSelection: [],
        classifyList: [],
        dataQuery: {
          name: ''
        },
        tableLoading: false,
        // 分页
        tablePage: {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null
        },
        open: false,
        tableData: []
      },
      typeList: [{ label: '每日礼包', value: 1 }, { label: '其他礼包', value: 2 }],
      dataQuery: {
        name: ''
      },
      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      open: false,
      tableData: [],
      form: {},
      // 表单校验
      rules: {
        name: [{ required: true, message: '请输入礼包名称', trigger: 'blur' }],
        type: [{ required: true, message: '请选择礼包类型', trigger: 'blur' }],
        completeNum: [{ required: true, message: '请输入完成项数', trigger: 'blur' }],
        goodsIds: [{ required: true, message: '请选择物品', trigger: 'blur' }]
      }
    }
  },
  updated() {
    if (this.goods.open) {
      this.toggleSelection()
    }
  },
  created() {
    this.getPageList()
  },
  methods: {
    goodsOpen() {
      this.goods.open = true
    },
    handleClose(index) {
      this.currentGoods.splice(index, 1)
    },
    toggleSelection() {
      this.currentGoods.forEach(item => {
        this.$nextTick(() => {
          this.goods.tableData.find(obj => {
            if (item.id === obj.id) {
              this.$refs.multipleTable.toggleRowSelection(obj, true)
            }
          })
        })
      })
    },
    confirmQuestionSelect() {
      this.goods.multipleSelection.forEach(q => {
        if (this.currentGoods.findIndex(item => item.id === q.id) === -1) {
          this.currentGoods.push(q)
        }
      })
      this.goods.open = false
    },
    handleSelectionChange(val) {
      this.goods.multipleSelection = val
    },
    resetGoodsSearch() {
      this.goods.dataQuery = {
        name: ''
      }
      this.goods.tablePage = {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      }
      this.getGoodsPageList()
    },
    goodsSearch() {
      this.goods.tablePage = {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      }
      this.getGoodsPageList()
    },
    goodsTypeFormat(row, column) {
      for (let a = 0; a < this.goods.classifyList.length; a++) {
        if (this.goods.classifyList[a].ident === row.classify) {
          return this.goods.classifyList[a].name
        }
      }
      return '未知'
    },
    getGoodsPageList() {
      this.goods.tableLoading = true
      this.goods.dataQuery.pageNum = this.goods.tablePage.currentPage
      this.goods.dataQuery.pageSize = this.goods.tablePage.size
      this.goods.dataQuery.type = 2
      goodsApi.list(this.goods.dataQuery).then(res => {
        this.goods.tableData = res.data.data
        this.goods.tableLoading = false
        // 设置后台返回的分页参数
        pageParamNames.forEach(name =>
          this.$set(this.goods.tablePage, name, parseInt(res.data[name]))
        )
      })
    },
    getClassify() {
      goodsclassifyApi.list().then(res => {
        if (res.success) {
          this.goods.classifyList = res.data
          this.getGoodsPageList()
        }
      }).catch(err => {
      })
    },
    typeFormat(row, column) {
      for (let i = 0; i < this.typeList.length; i++) {
        if (this.typeList[i].value === row.type) {
          return this.typeList[i].label
        }
      }
      return '未知'
    },
    search() {
      this.tablePage = {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      }
      this.getPageList()
    },
    handleDelete(id) {
      this.$confirm('确定要删除吗？', '删除礼包', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        giftApi.delete(id).then(res => {
          this.$message.success('删除成功！')
          this.getPageList()
        })
      }).catch(() => {

      })
    },
    addBtn() {
      this.reset()
      this.getClassify()
      this.open = true
    },
    editBtn(id) {
      this.reset()
      giftApi.detail(id).then(res => {
        this.form = res.data
        this.open = true
        this.currentGoods = this.form.goodsList
      })
      this.getClassify()
    },
    submitForm() {
      this.$refs['form'].validate(valid => {
        let arr = []
        this.currentGoods.forEach(i => arr.push(i.id))
        this.form.goodsIds = arr
        if (valid) {
          giftApi.saveOrUpdate(this.form).then(response => {
            this.$message.success('提交成功！')
            this.open = false
            this.getPageList()
          })
        }
      })
    },
    getPageList() {
      this.tableLoading = true
      this.dataQuery.pageNum = this.tablePage.currentPage
      this.dataQuery.pageSize = this.tablePage.size
      giftApi.list(this.dataQuery).then(res => {
        this.tableData = res.data.data
        this.tableLoading = false
        // 设置后台返回的分页参数
        pageParamNames.forEach(name =>
          this.$set(this.tablePage, name, parseInt(res.data[name]))
        )
      })
    },
    handlePage() {
      (this.tablePage = {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      }),
        this.getPageList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dataQuery = {
        name: '',
        enable: null
      }
      this.getPageList()
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val
      this.getPageList()
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val
      this.getPageList()
    },
    // 分页
    goodsHandleSizeChange(val) {
      this.goods.tablePage.size = val
      this.getGoodsPageList()
    },
    goodsHandleCurrentChange(val) {
      this.goods.tablePage.currentPage = val
      this.getGoodsPageList()
    },
    close() {
      this.open = false
      this.reset()
    },
    reset() {
      this.currentGoods = []
      this.form = {
        name: null,
        completeNum: undefined,
        goodsIds: [],
        type: 1
      }
      if (this.$refs['form']) {
        this.$refs['form'].resetFields()
      }
    }
  }
}
</script>

<style>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.period-table td,
.period-table th {
  text-align: center;
}

@media (max-width: 767px) {
  .el-message-box {
    width: 80% !important;
  }
}

.el-tooltip__popper {
  max-width: 800px;
}

.el-tag+.el-tag {
  margin-left: 10px;
}

.button-new-tag {
  margin-left: 10px;
  height: 32px;
  line-height: 30px;
  padding-top: 0;
  padding-bottom: 0;
}
</style>
