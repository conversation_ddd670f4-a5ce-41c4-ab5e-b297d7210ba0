<template>
  <div>
    <iframe :src="src" id="mobsf" scrolling="no" frameborder="0" style="position:absolute;top:80px;left: 120px;"></iframe>
  </div>
</template>

<script>

export default {
  data() {
    return {
      src: ""
    };
  },
  created() {
    this.fetchData();
  },
  methods: {
    fetchData() {
      this.src = window.localStorage.getItem("link");
    }
  },

  mounted() {
    /**
    * iframe-宽高自适应显示   
    */
    function changeMobsfIframe() {
      const mobsf = document.getElementById('mobsf');
      const deviceWidth = document.body.clientWidth;
      const deviceHeight = document.body.clientHeight;
      mobsf.style.width = (Number(deviceWidth) - 120) + 'px'; //数字是页面布局宽度差值
      mobsf.style.height = (Number(deviceHeight) - 80) + 'px'; //数字是页面布局高度差
    }

    changeMobsfIframe()

    window.onresize = function () {
      changeMobsfIframe()
    }
  },
};
</script>

<style>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.period-table td,
.period-table th {
  text-align: center;
}

.mt20 {
  margin-top: 20px;
}
</style>
