<template>
  <div class="app-container">
    <el-form :inline="true" class="container-card" label-width="110px" label-position="left" ref="dataQuery" :model="dataQuery">
      <el-row>
        <el-col :span="8" :xs="24">
          <el-form-item label="学员编号：" prop="studentCode">
            <el-input v-model="dataQuery.studentCode" placeholder="请输入学员编号" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="登录账号：" prop="loginName">
            <el-input v-model="dataQuery.loginName" placeholder="请输入登录账号" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="学员手机号：" prop="memberPhone">
            <el-input v-model="dataQuery.memberPhone" placeholder="请输入学员手机号" clearable />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8" :xs="24">
          <el-form-item label="添加时间：" prop="regTime">
            <el-date-picker
              style="width: 100%"
              clearable
              value-format="yyyy-MM-dd hh:mm:ss"
              v-model="regTime"
              type="daterange"
              align="right"
              unlink-panels
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="dateVal"
            ></el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="姓名：" prop="realName">
            <el-input v-model="dataQuery.realName" placeholder="请输入姓名" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="small" @click="fetchData01()">搜索</el-button>
            <el-button icon="el-icon-refresh-left" size="small" @click="rest()">重置</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-col :span="24" style="margin-bottom: 30px">
      <el-button type="warning" icon="el-icon-document-copy" @click="openBigRecharge()" size="mini">学员充值时长</el-button>
      <el-button type="success" size="mini" icon="el-icon-edit" v-if="currentAdmin.schoolType !== 3" @click="jumpOpenCoursePackage()">开通课程包</el-button>
    </el-col>

    <el-table
      ref="table"
      class="common-table"
      v-loading="tableLoading"
      :data="tableData"
      style="width: 100%; margin-bottom: 20px"
      @selection-change="handleSelectionChange"
      row-key="id"
      border
      default-expand-all
      :tree-props="{ list: 'children', hasChildren: 'true' }"
    >
      <el-table-column type="selection" />
      <el-table-column prop="studentCode" label="学员编号" width="120"></el-table-column>
      <el-table-column prop="loginName" label="登录账号" width="120"></el-table-column>
      <el-table-column prop="withdrawnBonus" label="操作" width="1200">
        <template slot-scope="scope">
          <el-button type="warning" size="mini" icon="el-icon-s-tools" @click="studentListBtn(scope.row)">学员信息表</el-button>
          <el-button type="warning" size="mini" icon="el-icon-s-tools" @click="changeListBtn(scope.row)">咨询记录表</el-button>
          <el-button type="primary" size="mini" icon="el-icon-s-tools" v-if="currentAdmin.schoolType !== 3" @click="jiaofufangshi(scope.row.studentCode)">交付方式</el-button>
          <el-button type="success" size="mini" icon="el-icon-edit-outline" @click="jumpOpenCourse(scope.row.studentCode)">开通课程</el-button>
          <el-button type="success" size="mini" icon="el-icon-edit" v-if="currentAdmin.schoolType !== 3" @click="jumpOpenCoursePackage(scope.row.studentCode)">
            开通课程包
          </el-button>
          <el-button type="primary" size="mini" icon="el-icon-sell" @click="openRecharge(scope.row)">充值时长</el-button>
          <el-button type="danger" v-if="currentAdmin.schoolType !== 3" size="mini" icon="el-icon-sold-out" @click="openBackRecharge(scope.row)">退课</el-button>
          <el-button type="danger" v-if="currentAdmin.schoolType === 3" size="mini" icon="el-icon-sold-out" @click="openBackRechargePage(scope.row, 2)">退时长</el-button>
          <el-button type="warning" size="mini" icon="el-icon-view" @click="enterChildrenList(scope.row.studentCode)">查看课程</el-button>
          <el-button type="success" size="mini" icon="el-icon-edit-outline" @click="jumpOpenNewReadCourse(scope.row.studentCode)">开通超级阅读课程</el-button>
          <el-button type="success" size="mini" icon="el-icon-edit-outline" @click="jumpOpenNewListenCourse(scope.row.studentCode)">开通全能听力</el-button>
          <el-button type="warning" v-if="scope.row.archived" size="mini" icon="el-icon-sold-out" @click="unarchive(scope.row.studentCode)">恢复学生归档数据</el-button>
          <!--          <el-button type="warning" size="mini" icon="el-icon-position" @click="moveChildren(scope.row)">-->
          <!--            学员转移-->
          <!--          </el-button>-->
        </template>
      </el-table-column>
      <el-table-column prop="realName" label="姓名" width="100">
        <template slot-scope="scope">
          <span style="color: #46a6ff; cursor: pointer" @click="editStudent(scope.row)">{{ scope.row.realName }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="memberPhone" label="学员手机号" width="150">
        <template #header>
          <span>学员手机号</span>
          <i
            :class="phoneShowStatus == '0' ? 'el-icon-lock' : 'el-icon-unlock'"
            style="color: #1890ff; margin-left: 5px; font-size: 15px"
            @click="phoneShow"
            v-if="currentRole == 'School'"
          ></i>
        </template>
        <template slot-scope="scope">
          <span>
            {{ scope.row.memberPhone }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="gradeName" label="年级" width="120"></el-table-column>
      <el-table-column prop="school" label="学校" width="180" show-overflow-tooltip></el-table-column>
      <el-table-column prop="totalCourseHours" label="已购鼎英语课时（节）" width="180"></el-table-column>
      <el-table-column prop="haveCourseHours" label="剩余自有课时（节）" width="150"></el-table-column>
      <el-table-column label="剩余自行交付课时（节）" width="180" v-if="currentAdmin.schoolType == '3'">
        <template slot-scope="scope">
          <el-button type="text" @click="changeSelfDeliverHour(scope.row)" :disabled="parseInt(scope.row.deliverSelfFreeCourse) < 1">{{ scope.row.deliverSelfCourse }}</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="haveDeliverHours" label="剩余交付课时（节）" width="150"></el-table-column>
      <el-table-column label="课程包交付时长（节）" width="170">
        <template slot-scope="scope">
          <span>{{ scope.row.haveDeliverNum - scope.row.haveDeliverHours }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="coursePackage" label="已购课程包" width="150" :formatter="packageFormatter"></el-table-column>
      <el-table-column prop="addTime" label="添加时间" width="200"></el-table-column>
      <el-table-column prop="deliverType" label="交付方式" :formatter="ifendcase" width="200"></el-table-column>
    </el-table>

    <!--  编辑学员信息  -->
    <el-dialog
      class="recharge-dialog"
      :title="canEditStudent ? '学员信息' : '学员信息修改'"
      :visible.sync="editStudentInfoVisible"
      width="550px"
      style="text-align: center"
      :close-on-click-modal="false"
    >
      <el-form v-model="editStudentInfo" label-width="100px" style="width: 100%">
        <el-form-item label="学员姓名：" prop="realName">
          <el-input v-model="editStudentInfo.realName" clearable :disabled="canEditStudent" />
        </el-form-item>
        <el-form-item label="年级：" prop="grade">
          <el-select style="width: 100%" v-model="editStudentInfo.grade" :disabled="canEditStudent">
            <el-option v-for="(item, index) in gradeList" :key="index" :label="item.value" :value="item.grade" />
          </el-select>
        </el-form-item>
        <el-form-item label="学校：" prop="school">
          <el-input v-model="editStudentInfo.school" clearable :disabled="canEditStudent" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer" style="text-align: center" v-if="!canEditStudent">
        <el-button size="small" type="primary" @click="editConfirm">确定</el-button>
      </div>
    </el-dialog>

    <el-dialog class="recharge-dialog" title="手机号开通语法" :visible.sync="studentQueryVisible" width="70%" :close-on-click-modal="false">
      <el-form v-model="grammarData" label-position="left" label-width="150px" style="width: 100%">
        <el-row>
          <el-col :span="10" :xs="24">
            <el-form-item label="手机号：">
              <el-input v-model="phoneNum" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="2" :xs="24" style="text-align: right">
            <el-form-item>
              <el-button type="primary" icon="el-icon-search" @click="findStudent(phoneNum)">搜索</el-button>
              <el-button size="mini" icon="el-icon-refresh-left" @click="rest()">重置</el-button>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="学段：" prop="grammarType">
          <el-select style="width: 100%" v-model="grammarData.phase">
            <el-option v-for="(item, index) in phaseType" :key="index" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="姓名：" prop="studentCode">
          <el-select style="width: 100%" v-model="grammarData.studentName">
            <el-option v-for="(item, index) in studentInfo" :key="index" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="openGrammar()">确定</el-button>
      </div>
    </el-dialog>

    <!-- 课程包 -->
    <el-dialog :close-on-press-escape="false" class="recharge-dialog" title="开通课程" :visible.sync="courseDeduction" width="70%" :close-on-click-modal="false">
      <el-form ref="form" :model="coursePackageData" label-width="120px">
        <el-form-item label="分类:">
          <el-cascader v-model="coursePackageData.categoryId" :options="options" @change="getTheCourseSort(coursePackageData.categoryId)"></el-cascader>
        </el-form-item>
        <el-form-item label="课程:">
          <el-select @change="getCourse" v-model="courseValue" placeholder="请选择">
            <el-option v-for="item in courseOptions" :key="item.id" :label="item.productName" :value="item.id"></el-option>
          </el-select>

          <el-select @change="course" v-model="courseDetailsValue" placeholder="请选择">
            <el-option v-for="item in courseDetailsOptions" :key="item.id" :label="item.name" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="学员编号：" v-show="needInputStudentCode">
          <el-input v-model="studentCode" style="width: 42vw" clearable placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="包含的开通课程:">
          <el-tag style="margin-left: 10px" v-for="(item, index) in openAttributeList" :key="item.courseId" :label="item.courseName" :value="item.courseId">
            课程{{ index + 1 }} : {{ item.courseName }}
          </el-tag>
        </el-form-item>
        <el-form-item label="规格:">
          <el-tag style="margin-left: 10px" type="success" v-for="item in attributeList" :key="item.value" :label="item.name" :value="item.value">
            {{ item.name }} : {{ item.value }}
          </el-tag>
        </el-form-item>
        <el-form-item label="价格:">{{ courseMoney }}</el-form-item>
        <el-form-item label="课时:">{{ schoolHour }}</el-form-item>
        <el-form-item label="课程结算方式:">
          <span v-if="radio === 0">按每课时结算</span>
          <span v-if="radio === 1">不限制课时</span>
          <span v-if="radio === -100">请先您选择课程</span>
        </el-form-item>

        <el-form-item>
          <el-button @click="cancelData()" size="mini">取 消</el-button>
          <el-button size="mini" type="primary" @click="addSyntax()">确定</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>

    <el-dialog class="recharge-dialog" title="开通语法" :visible.sync="grammarDeduction" width="70%" :close-on-click-modal="false">
      <el-form v-model="grammarData" label-position="left" label-width="150px" style="width: 100%">
        <el-form-item label="学段：" prop="grammarType">
          <el-select style="width: 100%" v-model="grammarData.phase">
            <el-option v-for="(item, index) in phaseType" :key="index" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="姓名：" prop="studentCode">
          <el-input v-model="grammarData.studentName" disabled />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="openGrammar()">确定</el-button>
      </div>
    </el-dialog>

    <!-- 充值时长 -->
    <el-dialog class="recharge-dialog" title="充值时长" :visible.sync="showRecharge" :close-on-click-modal="false" width="70%" :before-close="closeRechargeDialog">
      <el-form v-model="recharge" label-position="left" :label-width="screenWidth > 1300 ? '140px' : '90px'" style="width: 100%">
        <!-- <el-form-item
      v-if="haveCourseHours&&deliverMode=='CENTER'"
        >
        <div style="color:red;fontSize:24px">当前已拥有的时长根据{{oldDivliverPirce/100}}元/时的价格计算，当时长消耗完时，充值交付时长变更为90元/时</div>
        </el-form-item> -->

        <el-form-item label="门店剩余课时(节)：" v-if="currentAdmin.schoolType !== 3" prop="course">
          <el-input v-model="recharge.course" readonly="readonly" disabled />
        </el-form-item>
        <el-form-item label="充值账户：" prop="searchName" v-if="isFlexRecharge">
          <el-input v-model="recharge.studentCode" :disabled="true" />
        </el-form-item>
        <el-row v-else>
          <el-col :span="20" :xs="24">
            <el-form-item label="充值账户：" prop="searchName">
              <el-input class="inputpre" v-if="!showStudentName" v-model="rechargeAccount" placeholder="请输入学员编号或手机号点击搜索查询学员信息" />
              <el-input class="inputpre" v-else v-model="recharge.studentCode" placeholder="请输入学员编号或手机号点击搜索查询学员信息" />
            </el-form-item>
          </el-col>
          <el-col :span="4" :xs="24">
            <el-button
              v-if="!showStudentName"
              :size="screenWidth > 1300 ? 'medium' : 'mini'"
              type="primary"
              icon="el-icon-search"
              style="margin-left: 5px"
              @click="searchStudent(rechargeAccount)"
            ></el-button>
            <el-button
              v-else
              :size="screenWidth > 1300 ? 'medium' : 'mini'"
              icon="el-icon-search"
              type="primary"
              style="margin-left: 5px"
              @click="searchStudent(recharge.studentCode)"
            ></el-button>
          </el-col>
        </el-row>

        <el-form-item v-if="showStudentName" label="学员姓名：">
          <el-input v-model="chooseStudentInfo.realName" disabled="true" />
        </el-form-item>
        <el-form-item v-if="currentAdmin.schoolType === 3" prop="deliverMode" label="交付方式：">
          <el-select @change="modeChage" v-model="deliverMode" placeholder="请选择">
            <el-option v-for="item in deliverOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item v-if="currentAdmin.schoolType === 3" prop="level" label="学段：">
          <el-select @change="levelChage" v-model="level" placeholder="请选择">
            <el-option v-for="item in levelOptionsList" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="充值时长：" prop="courseLength">
          <el-input v-model="recharge.courseLength" type="number" maxlength="20" isNumber2="true" min="1" @input="blurCourseLength(recharge.courseLength)" />
          <!-- <div style="color:red"     :class="recharge.courseLength<2?'reded':''" v-if="recharge.courseLength<2">充值未达到20课时</div> -->
        </el-form-item>
        <el-form-item v-if="currentAdmin.schoolType === 3" prop="deliverCourseLength" label="充值交付时长(节)：">
          <el-input
            v-model="recharge.deliverCourseLength"
            type="number"
            id="deliverCourseLength"
            maxlength="20"
            isNumber2="true"
            min="1"
            @input="blurDeliverCourseLength(recharge.deliverCourseLength)"
          />
          <!-- <div style="color:red" :disabled="deliverCourseDisable"  :class="recharge.deliverCourseLength<2?'reded':''"
             placeholder="最低充值20课时"  v-if="recharge.deliverCourseLength<2">充值未达到20课时</div> -->
        </el-form-item>
        <el-form-item label="合计金额 ：" v-if="currentAdmin.schoolType !== 3" prop="sumCoursePrice">
          <el-input v-model="recharge.sumCoursePrice" disabled />
        </el-form-item>
        <el-form-item label="合计总金额 ：" v-if="currentAdmin.schoolType == 3" prop="recharge.totalPrice">
          <el-input v-model="recharge.totalPrice" disabled />
        </el-form-item>
        <el-form-item label="课时单价：" prop="coursePrice" v-if="currentAdmin.schoolType !== 3">
          <el-input
            v-model="recharge.coursePrice"
            @blur="blurCoursePrice(recharge.coursePrice)"
            type="number"
            :disabled="currentAdmin.schoolType == 3"
            maxlength="20"
            isNumber2="true"
            min="1"
          />
          <div style="color: #ef3a41; font-size: 14px">温馨提示:课时单价中10.5元为学员运营费，非合伙人收益</div>
        </el-form-item>
        <el-form-item label="课时单价：" prop="coursePrice" v-if="currentAdmin.schoolType == 3">
          <el-row :gutter="0">
            <el-col :span="8">
              <el-input
                v-model="recharge.coursePrice"
                @blur="blurCoursePrice(recharge.coursePrice)"
                type="number"
                :disabled="currentAdmin.schoolType == 3"
                maxlength="20"
                isNumber2="true"
                min="1"
              />
              <div style="color: #ef3a41; font-size: 12px">温馨提示:课时单价中10.5元为学员运营费，非合伙人收益</div>
            </el-col>
            <el-col style="margin-right: 10px" :span="3" align="right">
              <span>合计课时金额</span>
            </el-col>
            <el-col :span="8">
              <el-input v-model="recharge.sumCoursePrice" type="number" :disabled="currentAdmin.schoolType == 3" maxlength="20" isNumber2="true" min="1" />
            </el-col>
          </el-row>
        </el-form-item>
        <el-form-item label="交付时长单价(节)：" v-if="currentAdmin.schoolType === 3">
          <el-row :gutter="0">
            <el-col :span="8">
              <el-input v-model="recharge.deliverCoursePrice" type="number" :disabled="currentAdmin.schoolType == 3" maxlength="20" min="1" />
            </el-col>
            <el-col style="margin-right: 10px" v-if="currentAdmin.schoolType === 3" :span="3" align="right">
              <span>合计交付时长金额</span>
            </el-col>
            <el-col :span="8">
              <el-input v-model="recharge.sumDeliverPrice" type="number" :disabled="currentAdmin.schoolType === 3" maxlength="20" min="1" />
            </el-col>
          </el-row>
        </el-form-item>
        <el-form-item label="到账学时 ：" v-if="currentAdmin.schoolType !== 3" prop="rechargeCourse">
          <el-input v-model="recharge.toAccountCourse" readonly="readonly" disabled />
        </el-form-item>
        <el-form-item label="充值说明 ：" prop="remark">
          <el-input v-model="recharge.remark" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <!-- <el-button size="mini" type="primary" @click="innerVisible=true">确定</el-button> -->
        <el-button size="mini" type="primary" @click="openDialogVisible()">确定</el-button>
      </div>

      <el-dialog width="380px" title="支付试课奖励" :visible.sync="innerVisible" append-to-body center :close-on-click-modal="false">
        <div style="display: flex; align-items: center; justify-content: center; margin: 0 10px">
          <i class="el-icon-warning" style="color: #fac915; font-size: 26px; margin-right: 5px"></i>
          <span style="line-height: 24px; padding: 0 10px">{{ '由于该学员有试课并且是首次充值，需支付' + amountRewards + '元的试课奖励，才可充值' }}</span>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button size="small" @click="innerVisible = false">取 消</el-button>
          <el-button size="small" type="primary" @click="rewardsPay">确 定</el-button>
        </span>
      </el-dialog>
    </el-dialog>

    <!--  学员选择弹窗  -->
    <el-dialog class="recharge-dialog" title="选择学员" :visible.sync="showStudentListVisible" width="550px" style="text-align: center" :close-on-click-modal="false">
      <el-table ref="multipleTable" :data="studentListByPhone" tooltip-effect="dark" @row-click="singleElection" style="width: 100%" border>
        <el-table-column label="" align="center" width="120">
          <template slot-scope="scope">
            <el-radio v-model="chooseStudentCode" class="hidden-label" :label="scope.row.studentCode" :label-disabled="true">&nbsp;</el-radio>
          </template>
        </el-table-column>
        <el-table-column prop="realName" label="学员姓名" align="center"></el-table-column>
        <el-table-column prop="studentCode" label="学员编号" align="center"></el-table-column>
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" type="primary" :loading="loading" style="color: #fff" @click="confirmChooseStudent">
          <span v-if="!loading">确 定</span>
          <span v-else>提 交 中...</span>
        </el-button>
        <el-button size="small" @click="closeChooseStudent">取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog title="退课" :visible.sync="showBackRecharge" width="70%" :close-on-click-modal="false">
      <el-form v-model="backRecharge" label-position="left" label-width="120px" style="width: 100%">
        <el-form-item label="学员编号：" prop="course">
          <el-col :xs="24" :span="18">
            <el-input v-model="backRecharge.studentCode" readonly="readonly" disabled />
          </el-col>
        </el-form-item>
        <el-form-item label="剩余课时(节)：" prop="haveCourseHours">
          <el-col :xs="24" :span="18">
            <el-input v-model="backRecharge.haveCourseHours" readonly="readonly" disabled />
          </el-col>
        </el-form-item>
        <el-form-item label="退课时(节)：" prop="tuiHours">
          <el-col :xs="24" :span="18">
            <el-input v-model="backRecharge.tuiHours" type="number" maxlength="20" isNumber2="true" min="1" />
          </el-col>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" :disabled="disabledA" @click="submitBackRecharge()">确定</el-button>
      </div>
    </el-dialog>

    <!--    交易密码确认  -->
    <el-dialog class="remark-dialog" title="交易密码确认" :visible.sync="dialogVisible" width="20%" :close-on-click-modal="false">
      <el-input v-model="secondPassWord" show-password></el-input>

      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" :disabled="disabledF" @click="submitRecharge(secondPassWord)">确认</el-button>
      </div>
    </el-dialog>

    <el-dialog
      title="退交付课时。"
      :visible.sync="showBackRechargeDeliver"
      width="70%"
      v-loading="tui_keshi_loading"
      element-loading-text="拼命加载中"
      element-loading-spinner="el-icon-loading"
      element-loading-background="rgba(0, 0, 0, 0.8)"
      :close-on-click-modal="false"
    >
      <el-form v-model="backRecharge" label-position="left" label-width="140px">
        <el-form-item label="学员编号：" prop="course">
          <el-col :xs="24" :span="18">
            <el-input v-model="backRecharge.studentCode" readonly="readonly" disabled />
          </el-col>
        </el-form-item>
        <el-form-item label="剩余交付课时(节)：" v-if="currentAdmin.schoolType !== 3" prop="haveCourseHours">
          <el-col :xs="24" :span="18">
            <el-input v-model="backRecharge.haveDeliverHours" readonly="readonly" disabled />
          </el-col>
        </el-form-item>
        <el-form-item label="退交付课时(节)：" v-if="currentAdmin.schoolType !== 3" prop="tuiHours">
          <el-col :xs="24" :span="18">
            <el-input v-model="backRecharge.tuiDeliverHours" type="number" maxlength="20" isNumber2="true" min="1" />
          </el-col>
        </el-form-item>
      </el-form>
      <el-table
        :data="refundPage.courseRefundInfoDtoList"
        :cell-style="{ 'text-align': 'center' }"
        :header-cell-style="{ 'text-align': 'center' }"
        v-if="currentAdmin.schoolType === 3"
      >
        <el-table-column property="createTime" label="创建时间"></el-table-column>
        <el-table-column property="courseSum" label="购买时长数">
          <template slot-scope="scope">
            {{ scope.row.courseSum }}
          </template>
        </el-table-column>
        <el-table-column property="deliverCourseSum" label="购买交付时长数">
          <template slot-scope="scope">
            {{ parseInt(scope.row.deliverCourseSum) < 1 ? scope.row.selfDeliverCourseSum : scope.row.deliverCourseSum }}
          </template>
        </el-table-column>
        <el-table-column label="时长单价">
          <template slot-scope="scope">
            {{ scope.row.coursePrice / 100 }}
          </template>
        </el-table-column>
        <el-table-column label="交付时长单价(节)">
          <template slot-scope="scope">
            {{ scope.row.deliverCoursePrice / 100 }}
          </template>
        </el-table-column>
        <el-table-column property="courseCanRefundSum">
          <template slot="header">
            <el-tooltip class="item" effect="dark" content="页面展示可退时长数并非最终可退课时数，如有不对，请在本次退款完成后再次进入本页面核对" placement="left-end">
              <el-button type="text" style="color: #e6a23c">可退时长数</el-button>
            </el-tooltip>
          </template>
          <template slot-scope="scope">
            {{ scope.row.courseCanRefundSum }}
          </template>
          =
        </el-table-column>

        <el-table-column label="可退交付时长数">
          <template slot-scope="scope">
            {{ parseInt(scope.row.deliverCourseSum) < 1 ? scope.row.selfDeliverCourseCanRefundSum : scope.row.deliverCourseCanRefundSum }}
          </template>
        </el-table-column>
        <el-table-column label="退课时数">
          <template slot-scope="scope">
            <el-input
              v-model="scope.row.refundCourseNum"
              oninput="value=value.replace(/[^0-9]/g,'')"
              @change="(val) => checkRefundCourseNum(val, scope.row)"
              :disabled="parseInt(scope.row.courseCanRefundSum) < 1"
            ></el-input>
          </template>
        </el-table-column>
        <el-table-column label="退交付时长数">
          <template slot-scope="scope">
            <el-input
              v-model="scope.row.refundDeliverNum"
              oninput="value=value.replace(/[^0-9]/g,'')"
              @change="(val) => checkRefundDeliverNum(val, scope.row)"
              :disabled="scope.row.deliverCourseCanRefundSum == '' || parseInt(scope.row.deliverCourseCanRefundSum) < 1"
            ></el-input>
          </template>
        </el-table-column>
        <el-table-column label="退自行交付时长数">
          <template slot-scope="scope">
            <el-input
              v-model="scope.row.refundSelfDeliverNum"
              oninput="value=value.replace(/[^0-9]/g,'')"
              @change="(val) => checkRefundSelfDeliverNum(val, scope.row)"
              :disabled="scope.row.selfDeliverCourseCanRefundSum == '' || parseInt(scope.row.selfDeliverCourseCanRefundSum) < 1"
            ></el-input>
          </template>
        </el-table-column>
      </el-table>
      <el-row>
        <el-pagination
          :current-page="refundPage.currentPage"
          :page-sizes="[5, 10]"
          :page-size="refundPage.size"
          layout="total, sizes, prev, pager, next, jumper"
          :total="refundPage.totalItems"
          @size-change="refundHandleSizeChange"
          @current-change="refundHandleCurrentChange"
        />
      </el-row>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" :disabled="disabledA" @click="submitBackRecharge()">确定</el-button>
      </div>
    </el-dialog>
    <!-- 资转退课弹窗 -->
    <el-dialog :visible.sync="transferDropDialog" width="27%">
      <p class="text-zztk" style="font-size: 20px">
        请确认是否为以下学生退课时？您还需充值
        <b>{{ SupplementaryPaymentData.amount / 100 }}</b>
        元
      </p>
      <p class="text-zztk">
        学生编号：
        <b>{{ newWithdrawalStudents.studentCode }}</b>
      </p>
      <p class="text-zztk">
        退课时数：
        <b>{{ transferDrop.refundCourseSum }}</b>
        &emsp; 退交付时长数：
        <b>{{ transferDrop.refundDeliverCourseSum }}</b>
      </p>
      <p class="text-zztk" v-if="transferDrop.refundSelfDeliverCourseSum > 0">
        退自行交付时长数：
        <b>{{ transferDrop.refundSelfDeliverCourseSum }}</b>
      </p>
      <el-row slot="footer" class="dialog-footer">
        <el-button @click="closeTransferDropDialog">取 消</el-button>
        <el-button type="primary" @click="transferDropClick">去充值</el-button>
      </el-row>
    </el-dialog>
    <el-dialog title="授权码录入" :visible.sync="showAuthCode" width="30%" :close-on-click-modal="false">
      <el-form :inline="true" style="margin-bottom: 20px" model="form">
        <h3 style="text-align: center">超出异地登录开课限制，如需继续操作，请输入授权码</h3>
        <br />
        <el-form-item label="风控授权码:" prop="authCode" style="width: 100%; text-align: center">
          <el-input placeholder="请输入授权码" v-model="authCode" clearable></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="info" @click="closeAuthCode()">取消</el-button>
        <el-button size="mini" type="primary" :disabled="disabledA" @click="BindingCode()">确定</el-button>
      </div>
    </el-dialog>

    <!--  转移学员  -->
    <el-dialog
      class="recharge-dialog"
      title="转移学员"
      :visible.sync="moveStudentVisible"
      :before-close="closeMoveDialog"
      width="550px"
      style="text-align: center"
      :close-on-click-modal="false"
    >
      <el-form v-model="moveStudentInfo" label-width="100px" style="width: 100%">
        <el-row style="margin: 20px 0">
          <el-col :span="20">
            <el-form-item label="门店账号：" prop="searchName">
              <el-input v-model="moveStudentInfo.searchName" placeholder="请输入门店账号或门店编号" />
            </el-form-item>
          </el-col>

          <el-col :span="4">
            <el-button size="medium" icon="el-icon-search" style="margin-left: 5px" @click="getStoreInfo"></el-button>
          </el-col>
        </el-row>

        <div v-if="showStoreInfo" style="text-align: left; line-height: 45px; padding-left: 20px">
          <div>
            <span class="label">门店编号：</span>
            {{ showMerchantInfo.merchantCode }}
          </div>
          <div>
            <span class="label">门店名称：</span>
            {{ showMerchantInfo.merchantName }}
          </div>
          <div>
            <span class="label">门店地址：</span>
            {{ showMerchantInfo.address }}
          </div>
        </div>
      </el-form>
      <div slot="footer" class="dialog-footer" style="text-align: center">
        <el-button size="small" type="primary" @click="moveStudentConfirm">确定</el-button>
      </div>
    </el-dialog>

    <!-- 分页 -->
    <el-col :span="24" style="overflow-x: auto" :xs="24">
      <el-pagination
        :current-page="tablePage.currentPage"
        :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="tablePage.totalItems"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-col>
    <!-- 弹窗 -->
    <el-dialog title="选择交付方式：" center :visible.sync="btndialog" width="380px">
      <el-form ref="form" label-position="left" content :model="Parameters" style="margin-left: 20px" label-width="80px">
        <el-form-item label="交付方式">
          <el-radio v-model="Parameters.type" :label="1" size="medium ">自行交付</el-radio>
          <el-radio v-model="Parameters.type" :label="2" size="">集中交付</el-radio>
        </el-form-item>
        <el-form-item label="交付课时" v-if="Parameters.type === 2">
          <el-input-number v-model="Parameters.hours" :min="1" label="交付课时"></el-input-number>
        </el-form-item>
      </el-form>
      <el-row type="flex" justify="center" style="margin-top: 2vh">
        <el-button @click="btndialog = false">取 消</el-button>
        <el-button type="primary" @click="jiaofuBtn">确 定</el-button>
      </el-row>
    </el-dialog>
    <ShowPhone :dialogVisible.sync="dialogVisiblePhone" :phoneShowStatus.sync="phoneShowStatus" @fetchData="fetchData"></ShowPhone>
    <!-- 购买正式课成功弹窗 -->
    <el-dialog title=" " :visible.sync="reminderDialogVisible" width="36%" @close="reminderDialogClose" append-to-body :close-on-click-modal="false" class="reminderDialog">
      <div class="content-area">
        <div class="toast1">恭喜你购买正式课成功</div>
        <div class="toast2">请保存二维码并分享给家长，邀请家长及时下载鼎</div>
        <div class="toast3">校甄选APP，否则学员将无法上课</div>
        <div class="qr-img">
          <img class="image" :src="dxAppQrCode" alt="" />
        </div>
        <div class="download-btn" @click="downloadAppQrCode">下载二维码</div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import { getCourseCateList } from '@/api/courseCate';
  import { buyDeliverHour } from '@/api/studentjf';
  import studentApi from '@/api/areasStudentCourseList';
  import store from '@/store';
  import { dxSource, pageParamNames } from '@/utils/constants';
  import merchantAccountFlowApi from '@/api/merchantAccountFlow';
  import ls from '@/api/sessionStorage';
  import grammarConsumeHoursApi from '@/api/GrammarConsumeHours';
  import riskAuthCodeApi from '@/api/riskAuthCode';
  import { Base64 } from '@/utils/base64';
  import fa from 'element-ui/src/locale/lang/fa';
  import systemApi from '@/api/systemConfiguration';
  import schoolList from '@/api/schoolList';
  import ShowPhone from '@/components/ShowPhone/index.vue';
  import { de } from 'pinyin/data/dict-zi-web';
  import { int } from 'mockjs/src/mock/random/basic';
  import { get } from 'jquery';
  import checkPermission from '@/utils/permission';
  import userApi from '@/api/user';

  export default {
    name: 'aaa',
    components: { ShowPhone },
    computed: {
      fa() {
        return fa;
      }
    },
    data() {
      return {
        newWithdrawalStudents: {}, //最新点击的退课学员信息
        currentAdmin: '',
        level: '',
        // haveCourseHours: 0,
        // oldDivliverPirce: "",
        levelOptions: [],
        levelOptionsList: [
          {
            value: 1,
            label: '小学'
          },
          { value: 2, label: '初中' },
          { value: 3, label: '高中' }
        ],
        deliverMode: '',
        deliverOptions: [
          { value: 'CENTER', label: '集中交付' }
          // { value: "SELF", label: "自行交付" },
        ],
        showRechargeOperations: false,
        token: store.getters.token,
        setpayUrl: store.getters.setpayUrl,
        showBackRechargeDeliver: false,
        cityPrice: {},
        refundPage: { size: 5 },
        deliverCoursePrice: 0,
        //级联菜单选择内容
        categoryId: [],
        needInputStudentCode: false,
        radio: -100,
        //课程价格
        courseMoney: 0,
        nextFlag: '',
        loading: false,
        //课时
        schoolHour: 0,
        tui_keshi_loading: false,
        courseValue: '',
        courseDetailsValue: '',
        //授权码录入
        showAuthCode: false,
        authCode: undefined,
        deliverCourseDisable: false,
        //分类数据
        options: [],
        //课程
        courseOptions: [],
        //课程明细
        courseDetailsOptions: [],
        attributeList: [],
        courseDetails: null,
        //包含课程
        openAttributeList: [],
        customProps: {
          emitPath: false, // 只返回该节点的值
          value: 'categoryId', // 自定义当前数组的键名
          label: 'name',
          children: 'attributeList'
        },
        coursePackageData: {
          skuId: null,
          categoryId: null,
          productId: null,
          studentCode: ''
        },
        showBackRecharge: false,
        showRecharge: false,
        isFlexRecharge: false, //是否是固定学员充值时长
        recharge: {
          course: 0,
          toAccountCourse: 0,
          sumCoursePrice: 0
        },
        rechargeAccount: '', //充值账户
        showStudentName: false, //是否显示学员姓名
        rechargeStudentInfo: {}, //充值学员信息
        showStudentListVisible: false, //选择学员列表弹窗是否显示
        studentListByPhone: [], //手机号下对应的学员
        chooseStudentCode: '', //学员列表选择的学员code
        chooseStudentInfo: '', //学员列表选择的学员信息
        grammarDeduction: false,
        courseDeduction: false,
        backRecharge: {},
        tableLoading: false,
        disabledF: false,
        disabledA: false,

        moveStudentVisible: false,
        showStoreInfo: false,
        moveStudentInfo: {
          //转移学员信息
          studentCode: '',
          merchantCode: '', //转移学员老校区code
          searchName: ''
        },
        showMerchantInfo: {},
        // 分页
        tablePage: {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null
        },
        grammarData: {
          studentCode: '',
          phase: '',
          studentName: ''
        },
        studentInfo: [],
        tableData: [],
        dataQuery: {
          studentCode: '',
          startDate: '',
          endDate: '',
          isEnable: '',
          isFormal: '',
          loginName: '',
          realName: '',
          memberCode: '',
          memberPhone: '',
          restrictedUse: ''
        },
        resPrice: 0,
        phaseType: '',
        dialogVisible: false,
        updateMarketDate: {},
        addMarketDate: {},
        showLoginAccount: false,
        secondPassWord: '',
        regTime: '',
        studentQueryVisible: false,
        phoneNum: '',
        studentCode: '',
        btndialog: false,
        Parameters: {
          studentCode: '',
          type: '',
          hours: 0
        },
        editStudentInfoVisible: false,
        canEditStudent: false, //是否可编辑
        editStudentInfo: {
          realName: '',
          grade: '',
          school: ''
        },
        gradeList: [
          { grade: '18', value: '幼儿园' },
          { grade: '1', value: '一年级' },
          { grade: '2', value: '二年级' },
          { grade: '3', value: '三年级' },
          { grade: '4', value: '四年级' },
          { grade: '5', value: '五年级' },
          { grade: '6', value: '六年级' },
          { grade: '7', value: '七年级' },
          { grade: '8', value: '八年级' },
          { grade: '9', value: '九年级' },
          { grade: '10', value: '高一' },
          { grade: '11', value: '高二' },
          { grade: '12', value: '高三' }
        ],
        // reminderDialogVisible: true,
        reminderDialogVisible: false,
        // dxAppQrCode: "https://document.dxznjy.com/manage/1744191145000.png"
        dxAppQrCode: 'https://document.dxznjy.com/course/7b20a35bbdbd4afeb54e42d84e3a5cbc.png',

        innerVisible: false, // 试课奖励弹窗
        amountRewards: 0, // 试课奖励支付金额

        screenWidth: window.screen.width,
        transferDropDialog: false, // 退课补款弹窗
        SupplementaryPaymentData: {}, // 补交费用数据
        //退课补款数据
        transferDrop: {
          refundCourseSum: 0, //退课总课时
          refundDeliverCourseSum: 0, //退课总交付课时
          refundSelfDeliverCourseSum: 0 //退课总自行交付课时
        },
        dialogVisiblePhone: false,
        phoneShowStatus: localStorage.getItem('phoneShowStatus'),
        currentPhone: '',
        // 当前权限
        currentRole: window.localStorage.getItem('roleTag')
      };
    },
    async created() {
      if (this.currentRole == 'School') {
        await this.getPhoneNum();
      }
      this.newPartner();
      this.getAccount();
      this.fetchData(this.phoneShowStatus);
      this.getRoleTag();
      this.classification();
      this.getSelect();
      this.checkPayTrue();
    },
    methods: {
      checkPayTrue() {
        let queryString = '';

        // 先判断是 hash 路由模式还是 history 路由模式
        if (window.location.hash) {
          // Hash模式，从 hash 里取 ? 后面的参数
          const hash = window.location.hash;
          queryString = hash.includes('?') ? hash.split('?')[1] : '';
        } else {
          // History模式，直接用 search
          queryString = window.location.search.slice(1); // 去掉开头的 '?'
        }

        const urlParams = new URLSearchParams(queryString);
        const payTrue = urlParams.get('payTrue');
        console.log('payTrue', payTrue);

        if (payTrue === 'true') {
          this.reminderDialogVisible = true; // 打开充值成功提示弹窗
        }
      },
      // 判断是否是新合伙人
      newPartner() {
        console.log(this.$store.getters.JlbInfo.closeSelfDeliver);

        if (this.$store.getters.JlbInfo.closeSelfDeliver) {
          this.deliverOptions = [{ value: 'CENTER', label: '集中交付' }];
        } else {
          this.deliverOptions = [
            { value: 'CENTER', label: '集中交付' },
            { value: 'SELF', label: '自行交付' }
          ];
        }
      },
      //  获取课程类型列表长度
      async getCourseList() {
        let allData = await getCourseCateList(this.selectObj);

        let newArr = allData.data.data.filter((i) => i.isEnable != 0);
        this.courseCateList = this.courseCateList.concat(newArr);
        this.optionTotal = Number(allData.data.totalPage);
      },
      // 下拉加载
      handleLoadmore() {
        if (!this.loadingShip) {
          if (this.selectObj.pageNum == this.optionTotal) return; //节流防抖
          this.selectObj.pageNum++;
          this.getCourseList();
        }
      },
      checkRefundCourseNum(val, row) {
        if (Number(row.courseCanRefundSum) < Number(val)) {
          this.$message.warning('退课数量不能超高可退数量，已自动调整到最大可退数量');
          row.refundCourseNum = row.courseCanRefundSum;
        } else {
          row.refundCourseNum = val;
        }
      },
      checkRefundDeliverNum(val, row) {
        if (Number(row.deliverCourseCanRefundSum) < Number(val)) {
          this.$message.warning('退课数量不能超高可退数量，已自动调整到最大可退数量');
          row.refundDeliverNum = row.deliverCourseCanRefundSum;
        } else {
          row.refundDeliverNum = val;
        }
      },
      checkRefundSelfDeliverNum(val, row) {
        if (Number(row.selfDeliverCourseCanRefundSum) < Number(val)) {
          this.$message.warning('退课数量不能超高可退数量，已自动调整到最大可退数量');
          row.refundSelfDeliverNum = row.selfDeliverCourseCanRefundSum;
        } else {
          row.refundSelfDeliverNum = val;
        }
      },
      getRoleTag() {
        let that = this;
        schoolList.getCurrentAdmin().then((res) => {
          this.currentAdmin = res.data;
          if (res.data.schoolType == 3) {
            systemApi.coursePriceLevel().then((res) => {
              that.levelOptions = res.data;
            });
          }
        });
        // systemApi.queryDetail('CITY_DELIVER_PRICE').then((res) => {
        //   this.deliverCoursePrice = res.data.configState / 100
        // })
      },
      //点击学员姓名编辑学员信息弹窗
      editStudent(row) {
        //自行交付可编辑
        if (row.deliverType != '1') {
          this.canEditStudent = true;
        } else {
          this.canEditStudent = false;
        }
        this.editStudentInfo = row;
        this.editStudentInfoVisible = true;
      },
      changeSelfDeliverHour(data) {
        this.$prompt('请输入需要转移到自有时长的数量，最大' + parseInt(data.deliverSelfFreeCourse) + '时长', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          inputPattern: /^\d{0,6}$/,
          inputErrorMessage: '输入不正确'
        })
          .then(({ value }) => {
            this.$confirm('是否确定将' + value + '个免自行交付时长转为自有时长？转换后将无法再转回，后续如需自行交付则需要充值自行交付费用！', '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            }).then(() => {
              studentApi.moveSelfToHave(data.studentCode, value).then((res) => {
                this.$message({
                  type: 'success',
                  message: '操作成功!'
                });
                this.fetchData(this.phoneShowStatus);
              });
            });
          })
          .catch(() => {
            this.$message({
              type: 'info',
              message: '取消操作'
            });
          });
      },
      studentListBtn(row) {
        let studentCode = row.studentCode;
        let merchantCode = row.merchantCode;
        window.localStorage.setItem('studentCode', studentCode);
        window.localStorage.setItem('merchantCode', merchantCode);
        this.$router.push({
          path: '/student/xueyuanxinxi',
          query: {}
        });
        // this.$router.push('/student/xueyuanxinxi')
      },
      changeListBtn(row) {
        let studentCode = row.studentCode;
        let merchantCode = row.merchantCode;
        window.localStorage.setItem('studentCode', studentCode);
        window.localStorage.setItem('merchantCode', merchantCode);
        this.$router.push({
          path: '/student/shikejilu',
          query: {}
        });
      },
      //交付时长change事件
      blurDeliverCourseLength(courseLength) {
        this.recharge.deliverCourseLength = courseLength;
        this.getPicer();
        if (!this.level && this.currentAdmin.schoolType === 3) {
          this.$message.error('请先选择学段');
          return;
        }
        // if(this.haveCourseHours&&this.deliverMode=='CENTER'){
        //   if(this.recharge.deliverCourseLength<=this.haveCourseHours){
        //     this.recharge.sumDeliverPrice=this.multiply(
        //       (this.oldDivliverPirce/100),
        //   this.recharge.deliverCourseLength
        // );
        //   }else {
        //     this.recharge.sumDeliverPrice = this.multiply(
        //   this.recharge.deliverCoursePrice,
        //   this.recharge.deliverCourseLength
        // );
        //     let num1 = this.multiply(
        //   (this.oldDivliverPirce/100),
        //   this.haveCourseHours
        // );
        // console.log(num1);

        // let num2 = this.multiply(
        //   this.recharge.deliverCoursePrice,
        //   (this.recharge.deliverCourseLength-this.haveCourseHours)
        // );
        // this.recharge.sumDeliverPrice=(
        //   parseFloat(num1) +
        //   parseFloat(num2)
        // ).toFixed(2);;
        // }
        // }else {
        this.recharge.sumDeliverPrice = this.multiply(this.recharge.deliverCoursePrice, this.recharge.deliverCourseLength);
        // }
        this.recharge.totalPrice = (parseFloat(this.recharge.sumCoursePrice) + parseFloat(this.recharge.sumDeliverPrice)).toFixed(2);
      },
      //获取价格
      getPicer() {
        // if(this.recharge.courseLength<2){
        //   this.recharge.coursePrice = 0
        //   return
        // }

        studentApi.profitPrice().then((res) => {
          console.log(res.data.adjustCoursePrice, 'lllllllllll');

          this.resPrice = res.data.adjustCoursePrice;
        });
        var obj;
        this.levelOptions.forEach((e) => {
          if (this.recharge.courseLength >= e.startNum - 0) {
            if (this.recharge.courseLength <= e.endNum - 0) {
              obj = e;
            }
          }
        });
        if (!obj) {
          if (this.recharge.courseLength <= this.levelOptions[0].startNum - 0) {
            obj = this.levelOptions[0];
          } else {
            obj = this.levelOptions[this.levelOptions.length - 1];
          }
        }
        const item = obj.selectResultList[this.level - 1];
        const values = item.value.split('#');
        this.recharge.level = values[0];

        this.recharge.coursePrice = Number(values[1] * 1 + this.resPrice / 100).toFixed(2);
        const deliverConfig = JSON.parse(item.ext);
        if (this.deliverMode == 'SELF') {
          this.deliverCoursePrice = deliverConfig.SELF / 100;
          this.recharge.deliverCoursePrice = this.deliverCoursePrice;
          // this.recharge.deliverCourseLength = this.recharge.toAccountCourse;
        } else {
          this.deliverCoursePrice = deliverConfig.CENTER / 100;
          this.recharge.deliverCoursePrice = this.deliverCoursePrice;
        }
      },
      // 课时时长change事件
      blurCourseLength(courseLength) {
        this.recharge.toAccountCourse = courseLength;
        this.getPicer();
        if (!this.level && this.currentAdmin.schoolType === 3) {
          this.$message.error('请先选择学段');
          return;
        }
        if (!this.recharge.deliverCourseLength) {
          this.recharge.deliverCourseLength = 0;
        }
        if (this.deliverCourseDisable) {
          this.recharge.deliverCourseLength = this.recharge.toAccountCourse;
        }
        // if(this.haveCourseHours&&this.deliverMode=='CENTER'){
        //   if(this.recharge.deliverCourseLength<=this.haveCourseHours){
        //     this.recharge.sumDeliverPrice=this.multiply(
        //       (this.oldDivliverPirce/100),
        //   this.recharge.deliverCourseLength
        // );
        // }else {
        //     let num1 = this.multiply(
        //   (this.oldDivliverPirce/100),
        //   this.haveCourseHours
        // );
        // console.log(num1);

        // let num2 = this.multiply(
        //   this.recharge.deliverCoursePrice,
        //   (this.recharge.deliverCourseLength-this.haveCourseHours)
        // );
        // console.log(num2);
        // this.recharge.sumDeliverPrice=(
        //   parseFloat(num1) +
        //   parseFloat(num2)
        // ).toFixed(2);;
        // this.recharge.sumDeliverPrice = this.multiply(
        //   this.recharge.deliverCoursePrice,
        //   this.recharge.deliverCourseLength
        // );
        // }
        // }else {
        this.recharge.sumDeliverPrice = this.multiply(this.recharge.deliverCoursePrice, this.recharge.deliverCourseLength);
        // }

        this.recharge.sumCoursePrice = this.multiply(this.recharge.coursePrice, this.recharge.toAccountCourse);
        this.recharge.totalPrice = (parseFloat(this.recharge.sumCoursePrice) + parseFloat(this.recharge.sumDeliverPrice)).toFixed(2);
      },
      // 交付方式变化事件
      modeChage(item) {
        this.levelChage(this.level);
        // console.log(this.level);
      },
      // 学段变化事件
      levelChage(item) {
        if (!this.deliverMode) {
          this.$message.error('请先选择交付方式');
          return;
        }
        // item = this.levelOptions.find((s) => s.value == item);
        // console.log(this.levelOptions);
        this.deliverCourseDisable = false;
        if (this.recharge.courseLength) {
          this.blurCourseLength(this.recharge.toAccountCourse);
        }
        if (this.recharge.deliverCourseLength) {
          this.blurDeliverCourseLength(this.recharge.deliverCourseLength);
        }
      },
      multiply(num1, num2) {
        if (!num1) {
          num1 = 0;
        }
        if (!num2) {
          num2 = 0;
        }
        const result = parseFloat(num1).toFixed(2) * parseFloat(num2).toFixed(2);
        return result.toFixed(2);
      },
      // 交付方式
      jiaofufangshi(studentCode) {
        ls.setItem('studentCode', studentCode);
        this.btndialog = true;
        this.Parameters.studentCode = studentCode;
      },
      jiaofuBtn() {
        buyDeliverHour(this.Parameters).then((res) => {
          if (res.success) {
            const split = dxSource.split('##');
            res.data.dxSource = res.data.registerCallIInfo.appSource + '##' + split[1] + '##' + split[2];
            let params = JSON.stringify(res.data);
            let req = 'token=' + this.token + '&params=' + params + '&back=' + window.location.href;
            //需要编码两遍，避免出现+号等
            var encode = Base64.encode(Base64.encode(req));
            this.disabledF = false;
            window.open(this.setpayUrl + 'product?' + encode, '_blank');
            // window.open("https://cm.ngrok.dxznjy.com/product?" + encode, "_blank");
          }
          this.btndialog = false;
        });
        // this.fetchData()
      },
      //课程下拉框2
      course() {
        this.coursePackageData.skuId = this.courseDetailsValue;
        let size = this.courseDetailsOptions.length;
        for (let i = 0; i < size; i++) {
          const a = this.courseDetailsOptions[i].id;
          const b = this.coursePackageData.skuId;
          if (a === b) {
            this.openAttributeList = this.courseDetailsOptions[i].openAttributeList;
            this.courseDetails = this.courseDetailsOptions[i];
            this.courseMoney = Number.parseFloat(this.courseDetails.price) / 100.0;
            this.schoolHour = Number.parseFloat(this.courseDetails.coursePrice) / 100.0;
            this.radio = this.courseDetails.useCourseType;
          }
        }
      },
      packageFormatter(row) {
        if (row.coursePackage) {
          return row.coursePackage.join('  ');
        }
      },
      ifendcase(val) {
        if (val.deliverType == '1') {
          return '自行交付';
        } else if (val.deliverType == '2') {
          return '集中交付';
        }
      },
      //课程下拉框1
      getCourse() {
        this.coursePackageData.productId = this.courseValue;
        this.radio = -100;
        this.courseMoney = 0;
        //课时
        this.schoolHour = 0;
        this.openAttributeList = [];
        this.courseDetailsOptions = [];
        this.courseDetailsValue = '';
        for (let i = 0; i < this.courseOptions.length; i++) {
          if (this.courseOptions[i].productId === this.coursePackageData.productId) {
            this.attributeList = this.courseOptions[i].attributeList;
          }
          studentApi.getCourseProductSpecVo(this.coursePackageData.productId).then((res) => {
            //给课程下拉框赋值
            this.courseDetailsOptions = res.data;
          });
        }
      },

      //取消
      cancelData() {
        this.courseDeduction = false;
        this.blankData();
      },
      //置空数据
      blankData() {
        this.courseDetailsValue = '';
        this.courseValue = '';
        this.coursePackageData = {};
        this.openAttributeList = [];
        this.attributeList = [];
        this.courseOptions = [];
        this.courseDetailsOptions = [];
        this.radio = -100;
        this.courseMoney = '请您先选择课程';
        //课时
        this.schoolHour = '请您先选择课程';
      },
      getTheCourseSort(categoryId) {
        if (!categoryId) {
          return;
        }
        this.blankData();
        this.coursePackageData.categoryId = categoryId;
        studentApi.getProductVo(this.coursePackageData.categoryId).then((res) => {
          //给课程下拉框赋值
          this.courseOptions = res.data;
        });
      },
      //获取课程分类
      getCategory(value) {
        studentApi.getProductCateGory(value).then((res) => {});
      },
      classification() {
        studentApi.getProductCateGory(1).then((res) => {
          //给分类赋值
          this.options = res.data;
        });
      },
      jumpOpenCourse(studentCode) {
        window.localStorage.setItem('areasOpenStudentCode', studentCode);
        const that = this;
        that.$router.push({
          path: '/student/areasOpenCourse',
          query: {
            studentCode: studentCode
          }
        });
      },
      //开通超级语法课程
      jumpOpenNewReadCourse(studentCode) {
        const that = this;
        that.$router.push({
          path: '/student/areasOpenSuperReadCourse',
          query: {
            studentCode: studentCode
          }
        });
      },
      //开通全能听力
      jumpOpenNewListenCourse(studentCode) {
        const that = this;
        window.localStorage.setItem('areasOpenListenStudentCode', studentCode);
        that.$router.push({
          path: '/student/areasOpenListenCourse',
          query: {
            studentCode: studentCode
          }
        });
      },

      //添加语法
      addSyntax() {
        if (this.coursePackageData.categoryId === null || this.coursePackageData.categoryId === 0) {
          this.$message.error('课程分类未选择，请选择');
          return false;
        }
        if (this.coursePackageData.productId === null || this.coursePackageData.productId === '') {
          this.$message.error('课程未选择，请选择');
          return false;
        }
        if (this.coursePackageData.skuId === null || this.coursePackageData.skuId === '') {
          this.$message.error('规则未选择，请选择');
          return false;
        }
        if (!this.studentCode) {
          this.$message.error('请选择或输入学员编号');
          return false;
        }
        this.coursePackageData.studentCode = this.studentCode;
        //开通课程包 packageType = 1
        this.coursePackageData.packageType = 1;
        studentApi.buyCoursePackage(this.coursePackageData).then((res) => {
          if (!res.data.userCode) {
            this.dialogVisible = false;
            this.disabledA = false;
            this.showAuthCode = true;
            this.nextFlag = 'buyCoursePackage';
            return;
          }
          if (res.success) {
            const split = dxSource.split('##');
            res.data.dxSource = res.data.registerCallIInfo.appSource + '##' + split[1] + '##' + split[2];
            let params = JSON.stringify(res.data);

            let req = 'token=' + this.token + '&params=' + params + '&back=' + window.location.href;
            //需要编码两遍，避免出现+号等
            var encode = Base64.encode(Base64.encode(req));
            this.disabledF = false;
            window.open(this.setpayUrl + 'product?' + encode, '_blank');
            // window.open('https://cm.ngrok.dxznjy.com/product?' + encode, '_blank')
          }
        });
        // this.categoryId = null;
        // this.courseDeduction = false;
      },
      findStudent(phoneNum) {
        const that = this;
        that.grammarData = {
          studentCode: '',
          studentName: ''
        };
        if (phoneNum === null || phoneNum.length === 0) {
          this.$message.error('手机号不能为空');
          return false;
        }
        studentApi.findStudent(phoneNum).then((res) => {
          that.studentInfo = res.data;
        });
      },

      // 支付试课奖励
      async rewardsPay() {
        let data = {
          studentCode: this.recharge.studentCode,
          amount: this.amountRewards
        };
        let allData = await getCourseCateList({ pageNum: 1, pageSize: 9999 });

        data.curriculumId = allData.data.data.find((e) => e.enName == '鼎英语').id;
        studentApi.expRewardSave(data).then((res) => {
          const split = dxSource.split('##');
          res.data.lineCollectInfo.dxSource = res.data.lineCollectInfo.registerCallIInfo.appSource + '##' + split[1] + '##' + split[2];
          let params = JSON.stringify(res.data.lineCollectInfo);
          let req = 'token=' + this.token + '&params=' + params + '&back=' + window.location.href;
          this.disabledF = false;
          window.open(this.setpayUrl + 'product?' + Base64.encode(Base64.encode(req)), '_blank');

          // window.open(
          //   'https://cm.ngrok.dxznjy.com/product?' +
          //   Base64.encode(Base64.encode(req)),
          //   '_blank'
          // )
        });
      },

      // 充值时长
      async openDialogVisible() {
        if (!this.level && this.currentAdmin.schoolType == 3) {
          this.$message.error('请选择学段');
          return false;
        }
        if (!this.recharge.studentCode) {
          if (!this.isFlexRecharge && this.rechargeAccount) {
            this.$message.error('请点击搜索按钮查询学员信息');
          } else {
            this.$message.error('学员账户不能空');
          }
          return false;
        }
        if (!this.recharge.course) {
          this.$message.error('门店剩余课时不能为空');
          return false;
        }
        if (this.recharge.course <= 0) {
          this.$message.error('门店剩余课时不能为0');
          return false;
        }
        if (!this.recharge.courseLength) {
          if (this.currentAdmin.schoolType !== 3) {
            this.$message.error('充值时长不能为空');
            this.disabledF = false;
            return false;
          }
        }
        // if (this.recharge.courseLength <= 2) {
        //   if (this.currentAdmin.schoolType !== 3) {
        //     this.$message.error("充值时长不能低于20");
        //     this.disabledF = false;
        //     return false;
        //   }
        // }
        // if(this.recharge.deliverCourseLength<2)  {
        //   this.$message.error("交付学时最低充值20");
        //   return false;
        // }
        if (!this.recharge.coursePrice) {
          this.$message.error('课时单价不能为空');
          return false;
        }
        if (this.recharge.coursePrice <= 0) {
          this.$message.error('课时单价不能为0');
          return false;
        }
        // 判断是否需要支付试课奖励
        let data = {
          studentCode: this.recharge.studentCode,
          curriculumId: '1244777039673577472'
        };
        let allData = await getCourseCateList({ pageNum: 1, pageSize: 9999 });

        data.curriculumId = allData.data.data.find((e) => e.enName == '鼎英语').id;

        //如果当前后台用户是合伙人且该学生是资转,则不能充值
        let merchantCode = JSON.parse(localStorage.getItem('sysUserInfo')).merchantCode;
        let isTransfer = await this.getStudentIsTransfer({ studentCode: this.recharge.studentCode, merchantCode });
        if (isTransfer) return;
        await studentApi.payExpReward(data).then((res) => {
          if (res.data.needCharge) {
            this.innerVisible = true;
            this.amountRewards = res.data.amount;
          } else {
            if (this.currentAdmin.schoolType === 3) {
              var subData = JSON.parse(JSON.stringify(this.recharge));
              if (this.deliverMode == 'SELF') {
                subData.selfDeliverCourseLength = subData.deliverCourseLength;
                subData.deliverCourseLength = 0;
              }
              subData.deliverMode = this.deliverMode;
              studentApi.submitRecharge(subData).then(async (res) => {
                let a = (res.data.lineCollectInfo.otherInfo && JSON.parse(res.data.lineCollectInfo.otherInfo)) || {};
                if (a.profitVersion == 6 && a.adjustHours) {
                  await this.$confirm(`因系统判断,你的账户仍有 节自用课时未消耗完，现充值交付课时需补齐差价购买，是否确定充值。当前价格+10.5元/节`, '温馨提示', {
                    confirmButtonText: '立即支付',
                    cancelButtonText: '取消',
                    type: 'warning'
                  });
                }
                const split = dxSource.split('##');
                res.data.lineCollectInfo.dxSource = res.data.lineCollectInfo.registerCallIInfo.appSource + '##' + split[1] + '##' + split[2];
                let params = JSON.stringify(res.data.lineCollectInfo);
                let req = 'token=' + this.token + '&params=' + params + '&back=' + window.location.href + '?payTrue=' + 'true';
                this.disabledF = false;
                // window.open(
                //   'https://cm.ngrok.dxznjy.com/product?' +
                //   Base64.encode(Base64.encode(req)),
                //   '_blank'
                // )

                window.open(this.setpayUrl + 'product?' + Base64.encode(Base64.encode(req)), '_blank');
              });
            } else {
              // 打开交易密码弹窗
              this.dialogVisible = true;
            }
          }
        });
        this.secondPassWord = '';
      },
      //开通语法
      jumpOpenCoursePackage(studentCode) {
        this.studentCode = studentCode;
        this.classification(studentCode);
        this.isFlexRecharge = false;
        if (studentCode) {
          this.needInputStudentCode = false;
        } else {
          this.needInputStudentCode = true;
        }
        this.courseDeduction = true;
      },

      //获取学段
      getSelect() {
        grammarConsumeHoursApi.getSelect().then((res) => {
          this.phaseType = res.data;
        });
      },
      //确认退课
      submitBackRecharge() {
        const that = this;
        if (this.showBackRechargeDeliver && this.currentAdmin.schoolType === 3) {
          let refundCourseSum = 0;
          let refundDeliverCourseSum = 0;
          let refundSelfDeliverCourseSum = 0;
          let price = 0;
          let list = that.refundPage.courseRefundInfoDtoList.filter((ele) => {
            return ele.refundCourseNum || ele.refundDeliverNum || ele.refundSelfDeliverNum;
          });
          list.forEach((ele) => {
            if (!ele.refundCourseNum) {
              ele.refundCourseNum = 0;
            }
            refundCourseSum += parseInt(ele.refundCourseNum);
            if (!ele.refundDeliverNum) {
              ele.refundDeliverNum = 0;
            }
            if (!ele.deliverCoursePrice) {
              ele.deliverCoursePrice = 0;
            }
            refundDeliverCourseSum += parseInt(ele.refundDeliverNum);
            if (ele.refundCourseNum > 0) {
              price += Number.parseFloat(that.multiply(ele.coursePrice / 100, ele.refundCourseNum));
            }
            if (ele.refundDeliverNum > 0) {
              price += Number.parseFloat(that.multiply(ele.deliverCoursePrice / 100, ele.refundDeliverNum));
            }
            if (!ele.refundSelfDeliverNum) {
              ele.refundSelfDeliverNum = 0;
            }
            refundSelfDeliverCourseSum += parseInt(ele.refundSelfDeliverNum);
            if (ele.refundSelfDeliverNum > 0) {
              price += Number.parseFloat(that.multiply(ele.deliverCoursePrice / 100, ele.refundSelfDeliverNum));
            }
          });
          //;
          this.transferDrop.refundCourseSum = refundCourseSum; //记录退课时长
          this.transferDrop.refundDeliverCourseSum = refundDeliverCourseSum; //记录退交付时长
          this.transferDrop.refundSelfDeliverCourseSum = refundSelfDeliverCourseSum; //记录退自行交付时长
          //;
          let message = '本次共退时长' + refundCourseSum;
          if (refundDeliverCourseSum && refundDeliverCourseSum > 0) {
            message += '，退交付时长' + refundDeliverCourseSum;
          }
          if (refundSelfDeliverCourseSum && refundSelfDeliverCourseSum > 0) {
            message += '，退自行交付时长' + refundSelfDeliverCourseSum;
          }
          message += '，共计' + Number.parseFloat(price).toFixed(2) + '元';
          this.$confirm(message, '退课期间学生账户将被锁定！无法上课', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          })
            .then(() => {
              this.tui_keshi_loading = true;
              if (that.currentAdmin.schoolType === 3) {
                that.backRecharge.courseRefundInfoDtoList = that.refundPage.courseRefundInfoDtoList;
              }
              that.backRecharge.pageNum = that.refundPage.currentPage;
              that.backRecharge.pageSize = that.refundPage.size;
              studentApi
                .submitBackRecharge(that.backRecharge)
                .then((res) => {
                  this.tui_keshi_loading = false;

                  if (!res.data || !res.data.sourceOrderId) {
                    that.$message.success(res.message);
                    this.showBackRechargeDeliver = false;
                    that.$nextTick(() => that.fetchData(this.phoneShowStatus));
                  } else {
                    //打开退课补款弹窗
                    this.transferDropDialog = true;
                    this.SupplementaryPaymentData = res.data;
                    // this.$confirm(res.data.remark, '是否确定', {
                    //   confirmButtonText: '确定',
                    //   cancelButtonText: '取消',
                    //   type: 'warning'
                    // })
                    // .then(() => {
                    //   const split = dxSource.split('##');
                    //   res.data.dxSource = res.data.registerCallIInfo.appSource + '##' + split[1] + '##' + split[2];
                    //   let params = JSON.stringify(res.data);
                    //   let req = 'token=' + this.token + '&params=' + params + '&back=' + window.location.href;
                    //   //需要编码两遍，避免出现+号等
                    //   var encode = Base64.encode(Base64.encode(req));
                    //   window.open(this.setpayUrl + 'product?' + encode, '_blank');
                    //   // window.open(
                    //   //   "https://pay.dxznjy.com/product?" + encode,
                    //   //   "_blank"
                    //   // );
                    //   // window.open(
                    //   //   "http://192.168.5.118:8000/product?" + encode,
                    //   //   "_blank"
                    //   // );
                    // })
                    // .catch(() => {
                    //   studentApi.cancel(res.data.otherInfo).then((res) => {
                    //     this.$message({
                    //       type: 'info',
                    //       message: '已取消退課'
                    //     });
                    //   });
                    // });
                  }
                })
                .catch((ex) => {
                  this.dialogVisible = false;
                  this.tui_keshi_loading = false;
                });
            })
            .catch(() => {
              this.$message({
                type: 'info',
                message: '已取消退課'
              });
              this.tui_keshi_loading = false;
            });
          return;
        }
        that.disabledA = true;
        if (!that.backRecharge.studentCode) {
          that.$message.error('学员编号不能为空');
          return false;
        }
        if (that.backRecharge.tuiDeliverHours && parseInt(that.backRecharge.tuiDeliverHours) > 0) {
          that.backRecharge.tuiHours = that.backRecharge.tuiDeliverHours;
        }
        if (!that.backRecharge.tuiHours) {
          that.$message.error('退课时不能为空');
          return false;
        }
        studentApi.submitBackRecharge(that.backRecharge).then((res) => {
          if (!res.data.userCode) {
            this.dialogVisible = false;
            this.disabledA = false;
            this.showAuthCode = true;
            this.nextFlag = 'submitBackRecharge';
            return;
          }

          that.showBackRecharge = false;
          that.disabledA = false;
          that.$nextTick(() => that.fetchData(this.phoneShowStatus));
          that.$message.success('退课时成功');
        });
        // this.$confirm('确定操作吗?', '退课时', {
        //   confirmButtonText: '确定',
        //   cancelButtonText: '取消',
        //   type: 'warning'
        // }).then(() => {

        //})
      },
      //确认退课补款
      transferDropClick() {
        let that = this;
        const split = dxSource.split('##');
        let res = {
          data: that.SupplementaryPaymentData
        };
        res.data.dxSource = res.data.registerCallIInfo.appSource + '##' + split[1] + '##' + split[2];
        let params = JSON.stringify(res.data);
        let req = 'token=' + this.token + '&params=' + params + '&back=' + window.location.href;
        //需要编码两遍，避免出现+号等
        var encode = Base64.encode(Base64.encode(req));
        window.open(this.setpayUrl + 'product?' + encode, '_blank');
      },
      //取消退课补款
      closeTransferDropDialog() {
        let that = this;
        let res = {
          data: that.SupplementaryPaymentData
        };
        this.transferDropDialog = false;
        studentApi.cancel(res.data.otherInfo).then((res) => {
          this.$message({
            type: 'info',
            message: '已取消退課'
          });
        });
      },
      BindingCode() {
        if (!this.authCode) {
          this.$message.warning('授权码不能为空');
          return;
        }
        riskAuthCodeApi
          .bindingCode(this.authCode)
          .then((res) => {
            if (res.success) {
              this.$message.success('授权码录入成功');
              this.authCode = '';
              this.showAuthCode = false;
              if (this.nextFlag == 'submitBackRecharge') {
                this.submitBackRecharge();
              } else if (this.nextFlag == 'buyCoursePackage') {
                this.addSyntax();
              } else if (this.nextFlag == 'submitRecharge') {
                this.submitRecharge(this.secondPassWord);
              }
            }
          })
          .catch((err) => {});
      },
      closeAuthCode() {
        this.showAuthCode = false;
        this.showBackRecharge = false;
        this.authCode = '';
      },
      //充值确认密码
      submitRecharge(secondPassWord) {
        this.disabledF = true;
        if (!secondPassWord) {
          this.$message.error('二级密码不能为空');
          this.disabledF = false;
          return false;
        }
        if (!this.recharge.course) {
          this.$message.error('门店剩余课时不能为空');
          this.disabledF = false;
          return false;
        }
        if (!this.recharge.studentCode) {
          this.$message.error('学员账户不能空');
          this.disabledF = false;
          return false;
        }
        if (this.recharge.course <= 0) {
          this.$message.error('门店剩余课时不能为0');
          this.disabledF = false;
          return false;
        }
        if (!this.recharge.courseLength) {
          if (this.currentAdmin.schoolType !== 3) {
            this.$message.error('充值时长不能为空');
            this.disabledF = false;
            return false;
          }
        }
        if (this.recharge.courseLength <= 0) {
          if (this.currentAdmin.schoolType !== 3) {
            this.$message.error('充值时长不能0');
            this.disabledF = false;
            return false;
          }
        }
        if (!this.recharge.coursePrice) {
          this.$message.error('课时单价不能为空');
          this.disabledF = false;
          return false;
        }
        if (this.recharge.coursePrice <= 0) {
          this.$message.error('课时单价不能为0');
          this.disabledF = false;
          return false;
        }
        merchantAccountFlowApi.checkSecondPwd(secondPassWord).then(
          (res) => {
            if (!res.success) {
              this.$message.error('二级密码验证失败');
              this.disabledF = false;
              return false;
            }

            studentApi.submitRecharge(this.recharge).then(
              (res) => {
                if (res.data.lineCollectInfo && !res.data.lineCollectInfo.userCode) {
                  this.dialogVisible = false;
                  this.disabledF = false;
                  this.showAuthCode = true;
                  this.nextFlag = 'submitRecharge';

                  return;
                }
                if (res.data.needLineCollect) {
                  const split = dxSource.split('##');
                  res.data.lineCollectInfo.dxSource = res.data.lineCollectInfo.registerCallIInfo.appSource + '##' + split[1] + '##' + split[2];

                  let params = JSON.stringify(res.data.lineCollectInfo);
                  let req = 'token=' + this.token + '&params=' + params + '&back=' + window.location.href;
                  this.disabledF = false;
                  window.open(this.setpayUrl + 'product?' + Base64.encode(Base64.encode(req)), '_blank');
                  // window.open(
                  //   'https://cm.ngrok.dxznjy.com/product?' +
                  //   Base64.encode(Base64.encode(req)),
                  //   '_blank'
                  // )
                } else {
                  this.disabledF = false;
                  this.showRecharge = false;
                  this.dialogVisible = false;
                  this.fetchData01();
                  this.$message.success('充值成功!');
                }
              },
              (sd) => {
                this.disabledF = false;
              }
            );
          },
          (s) => {
            if (s === 'error') {
              this.disabledF = false;
            }
          }
        );
      },

      blurCoursePrice(coursePrice) {
        if (coursePrice !== '' && coursePrice !== 0 && coursePrice !== '') {
          this.recharge.sumCoursePrice = coursePrice * this.recharge.toAccountCourse;
        }
      },
      getAccount() {
        studentApi.getSchoolAccount().then((res) => {
          if (res.data != null) {
            this.recharge.course = res.data.course;
          }
        });
      },
      openBigRecharge() {
        let that = this;
        console.log(that.studentCode);
        that.level = '';
        that.recharge = {
          level: '',
          studentCode: that.studentCode,
          course: that.recharge.course,
          courseLength: 0,
          deliverCourseLength: 0,
          coursePrice: 0,
          sumCoursePrice: 0,
          toAccountCourse: 0,
          remark: ''
        };
        that.rechargeAccount = that.studentCode;
        console.log(that.recharge);
        that.showRecharge = true;
        that.showStudentName = false;
        // if(that.recharge.studentCode){
        //   that.showStudentName = true;
        // }
      },
      filterGrade(val) {
        let result = '';
        let arr = this.gradeList.filter((i) => val == i.grade);
        if (arr.length > 0) {
          result = arr[0].grade;
        } else {
          result = '';
        }
        return result;
      },
      //编辑学员信息保存
      editConfirm() {
        this.editStudentInfo.gradeName = this.filterGrade(this.editStudentInfo.grade);
        // this.gradeList[this.editStudentInfo.grade - 1].value;
        if (!this.editStudentInfo.realName) {
          this.$message.warning('请输入学员姓名');
          return;
        }
        if (!this.editStudentInfo.school) {
          this.$message.warning('请输入学校名称');
          return;
        }
        studentApi.editStudentInfo(this.editStudentInfo).then((res) => {
          this.fetchData(this.phoneShowStatus);
          this.editStudentInfoVisible = false;
          this.canEditStudent = false;
        });
      },
      phoneNumOpenGrammar() {
        (this.phoneNum = ''),
          (this.grammarData = {
            studentCode: '',
            phase: '',
            studentName: ''
          }),
          (this.studentInfo = []),
          (this.studentQueryVisible = true);
        this.recharge = {
          studentCode: '',
          course: this.recharge.course,
          courseLength: 0,
          deliverCourseLength: 0,
          coursePrice: 0,
          sumCoursePrice: 0,
          toAccountCourse: 0,
          remark: ''
        };
      },
      refundHandleSizeChange(val) {
        this.refundPage.size = val;
        this.openBackRechargePage();
      },
      refundHandleCurrentChange(val) {
        this.refundPage.currentPage = val;
        this.openBackRechargePage();
      },
      unarchive(studentCode) {
        this.$message.success('恢复归档处理中。。。。。');
        studentApi.unarchive(studentCode).then((res) => {
          this.$message.success('恢复成功');
        });
      },
      // getBackRechargeData() {
      //   let that = this;
      //   const studentCode = this.newWithdrawalStudents ? this.newWithdrawalStudents.studentCode : '';
      //   that.refundPage.studentCode = studentCode;
      //   studentApi.getBackRechargePage(that.refundPage.currentPage, that.refundPage.size, that.refundPage.studentCode, true).then((res) => {
      //     this.refundPage = res.data;
      //     this.showBackRechargeDeliver = true;
      //     this.backRecharge = res.data;
      //     // 设置后台返回的分页参数
      //     pageParamNames.forEach((name) => that.$set(that.refundPage, name, parseInt(res.data[name])));
      //   });
      // },
      //打开退课时窗口
      openBackRechargePage(row) {
        //最新点击的退课学员信息
        if (row && row.id) {
          this.newWithdrawalStudents = row;
        }
        const studentCode = row ? row.studentCode : this.newWithdrawalStudents.studentCode ? this.newWithdrawalStudents.studentCode : '';
        const that = this;
        if (!that.refundPage.currentPage) {
          that.refundPage.currentPage = 1;
        }
        if (studentCode) {
          that.refundPage.studentCode = studentCode;
        }
        if (!that.refundPage.size) {
          that.refundPage.size = 5;
        }
        studentApi.getBackRechargePage(that.refundPage.currentPage, that.refundPage.size, that.refundPage.studentCode, true).then((res) => {
          this.refundPage = res.data;
          this.showBackRechargeDeliver = true;
          this.backRecharge = res.data;
          // 设置后台返回的分页参数
          pageParamNames.forEach((name) => that.$set(that.refundPage, name, parseInt(res.data[name])));
        });
      },
      //打开退课窗口
      openBackRecharge(row) {
        //最新点击的退课学员信息
        this.newWithdrawalStudents = row;

        const studentCode = row ? row.studentCode : '';
        const that = this;
        if (!that.refundPage.currentPage) {
          that.refundPage.currentPage = 1;
        }
        if (studentCode) {
          that.backRecharge.studentCode = studentCode;
        }
        if (!that.refundPage.size) {
          that.refundPage.size = 5;
        }
        this.disabledA = false;
        //查询退课
        studentApi.getBackRecharge(studentCode).then((res) => {
          this.backRecharge.haveCourseHours = res.data.haveCourseHours;
          this.showBackRecharge = true;
        });
        this.backRecharge = {
          studentCode: this.backRecharge.studentCode,
          haveCourseHours: this.backRecharge.haveCourseHours,
          tuiHours: 0
        };
      },
      // async setHaveCourseHours(row) {
      //   let obj= {
      //     value:row
      //   }
      //   let res = await systemApi.getStudentCourseHours(obj)
      //   if(res.data[0].haveCourseHours){
      //     this.haveCourseHours=res.data[0].haveCourseHours-0
      //     this.oldDivliverPirce=(res.data[0].deliverCoursePrice-0)
      //   }else {
      //     this.haveCourseHours=0
      //   }
      // },
      //
      //查询是否是是资转学员且180天内
      async getStudentIsTransfer(row) {
        //如果当前后台用户是合伙人且该学生是资转,则不能充值
        let isTransfer = await studentApi.getStudentIsTransfer(row);
        if (isTransfer.data) {
          this.$alert('该学生已进入委托回本流程，若需要充值，请联系咨单人员', '提示', {
            confirmButtonText: '确定'
          });
          return true;
        } else {
          return false;
        }
      },
      //充值时长
      async openRecharge(row) {
        //如果当前后台用户是合伙人且该学生是资转,则不能充值
        let isTransfer = await this.getStudentIsTransfer(row);
        if (isTransfer) return;
        // this.setHaveCourseHours(row.studentCode)
        this.recharge.studentCode = row.studentCode;
        this.isFlexRecharge = true;
        this.showRecharge = true;
        this.level = '';
        this.recharge = {
          level: '',
          studentCode: row.studentCode,
          course: this.recharge.course,
          courseLength: 0,
          deliverCourseLength: 0,
          coursePrice: 0,
          sumCoursePrice: 0,
          toAccountCourse: 0,
          remark: ''
        };
      },
      fetchData01() {
        this.tablePage = {
          currentPage: 1,
          size: this.tablePage.size,
          totalPage: null,
          totalItems: null
        };
        this.fetchData(this.phoneShowStatus);
      },
      // 获取起始时间
      dateVal(e) {
        this.dataQuery.startTime = e[0];
        this.dataQuery.endTime = e[1];
      },
      // 查询提现列表
      fetchData(phoneShowStatus) {
        const that = this;
        var a = that.regTime;
        if (a != null) {
          that.dataQuery.startRegTime = a[0];
          that.dataQuery.endRegTime = a[1];
        } else {
          that.dataQuery.startRegTime = '';
          that.dataQuery.endRegTime = '';
        }
        that.tableLoading = true;
        studentApi.studentList(that.tablePage.currentPage, that.tablePage.size, that.dataQuery, phoneShowStatus == '0' ? '' : that.currentPhone).then((res) => {
          that.tableData = res.data.data;
          // that.tableData = [{merchantCode:6241111010}];
          that.tableLoading = false;
          // 设置后台返回的分页参数
          pageParamNames.forEach((name) => that.$set(that.tablePage, name, parseInt(res.data[name])));
        });
      },
      // 分页
      handleSizeChange(val) {
        this.tablePage.size = val;
        this.fetchData(this.phoneShowStatus);
      },
      handleCurrentChange(val) {
        this.tablePage.currentPage = val;
        this.fetchData(this.phoneShowStatus);
      },
      //进入查看课程
      enterChildrenList(studentCode) {
        const that = this;
        // ls.setItem('areaStudentCode', studentCode)
        that.$router.push({
          path: '/student/areasStudentCourseRecord',
          query: {
            studentCode: studentCode
          }
        });
      },
      //  点击学员转移按钮
      moveChildren(row) {
        this.moveStudentInfo.merchantCode = row.merchantCode;
        this.moveStudentInfo.studentCode = row.studentCode;
        this.moveStudentInfo.searchName = '';
        this.showMerchantInfo = {};
        this.moveStudentVisible = true;
      },
      //  查询门店账号
      getStoreInfo() {
        if (!this.moveStudentInfo.searchName) {
          this.$message.warning('请输入门店账号或门店编号');
          return;
        }
        let name = '',
          value = '';
        //登录账号查询
        if (this.moveStudentInfo.searchName.length > 9) {
          name = 'name';
        } else {
          name = 'merchantCode';
        }
        value = this.moveStudentInfo.searchName;
        studentApi.getMerchantInfo(name, value).then((res) => {
          if (!res.data) {
            this.$message.warning('暂未查询到门店');
            return;
          }
          this.showStoreInfo = true;
          this.showMerchantInfo = res.data;
        });
      },
      //  确认转移学员
      moveStudentConfirm() {
        if (!this.moveStudentInfo.searchName) {
          this.$message.warning('请输入门店账号或门店编号');
          return;
        }
        if (!this.showMerchantInfo.merchantCode) {
          this.$message.warning('请点击搜索按钮查询门店信息');
          return;
        }
        if (this.moveStudentInfo.merchantCode == this.showMerchantInfo.merchantCode) {
          this.$message.warning('请勿将本门店学员转移至本门店');
          return;
        }
        studentApi.moveStudent(this.moveStudentInfo.studentCode, this.moveStudentInfo.merchantCode, this.showMerchantInfo.merchantCode).then((res) => {
          this.closeMoveDialog();
          if (res.success) {
            this.$message.success('学员转移成功');
            this.fetchData(this.phoneShowStatus);
          }
        });
      },
      //  关闭转移学员弹窗
      closeMoveDialog() {
        this.showStoreInfo = false;
        this.showMerchantInfo = {};
        this.moveStudentVisible = false;
      },
      //  充值账号点击搜索功能
      searchStudent(ele) {
        if (!ele) {
          this.$message.warning('请输学员账号获取手机号码');
          return;
        }
        let name = '',
          value = '';
        //登录账号查询
        if (ele.length == 11) {
          name = 'phone';
        } else {
          // this.setHaveCourseHours(ele)
          name = 'studentCode';
        }
        value = ele;
        studentApi
          .getStudentInfo(value)
          .then((res) => {
            this.studentListByPhone = res.data;
            console.log(res.data);
            if (this.studentListByPhone.length == 0) {
              this.$message.warning('暂未查询到学员账号');
              this.showStudentName = false;
              this.rechargeAccount = '';
              this.chooseStudentInfo = {};
              this.chooseStudentCode = '';
              this.recharge.studentCode = '';
            } else if (this.studentListByPhone.length == 1) {
              this.chooseStudentInfo = this.studentListByPhone[0];
              this.recharge.studentCode = this.chooseStudentInfo.studentCode;
              this.showStudentName = true;
            } else {
              this.chooseStudentInfo = {};
              this.showStudentListVisible = true;
            }
          })
          .catch((err) => {
            console.log(err);
            this.showStudentName = false;
          });
      },
      //学员选择弹窗点击表格
      singleElection(row) {
        this.chooseStudentInfo = row;
        this.chooseStudentCode = row.studentCode;
      },
      //  关闭充值弹窗
      closeRechargeDialog() {
        this.showRecharge = false;
        this.isFlexRecharge = false;
        // this.rechargeAccount = '';
      },

      //  确定选择学员
      confirmChooseStudent() {
        //如果没有学员的情况下
        if (this.studentListByPhone.length == 0) {
          this.closeChooseStudent();
          return;
        }

        if (!this.chooseStudentInfo.studentCode) {
          this.$message.warning('请选择学员');
          return;
        }
        this.showStudentName = true;
        // this.setHaveCourseHours(this.chooseStudentInfo.studentCode)
        this.recharge.studentCode = this.chooseStudentInfo.studentCode;
        this.closeChooseStudent();
      },
      //  关闭选择学员弹窗
      closeChooseStudent() {
        this.showStudentListVisible = false;
        this.chooseStudentCode = '';
      },

      // 选择
      handleSelectionChange(val) {
        if (val.length == 0) {
          this.studentCode = '';
        } else if (val.length > 1) {
          this.$refs.table.clearSelection();
          this.$refs.table.toggleRowSelection(val.pop());
          this.studentCode = val[1].studentCode;
          this.rechargeAccount = val[1].studentCode;
        } else {
          this.studentCode = val[0].studentCode;
          this.rechargeAccount = val[0].studentCode;
        }
      },
      async getPhoneNum() {
        const res = await userApi.getPhone();
        this.currentPhone = res.data.phone;
      },
      // 手机号码显示隐藏
      phoneShow() {
        console.log(this.phoneShowStatus);
        if (this.tableData.length == 0) return;
        if (this.phoneShowStatus == '0') {
          console.log(this.phoneShowStatus, 11111);
          this.dialogVisiblePhone = true;
        } else {
          this.phoneShowStatus = '0';
          localStorage.setItem('phoneShowStatus', '0');
          this.fetchData(this.phoneShowStatus);
        }
      },

      //重置
      rest() {
        this.$refs.dataQuery.resetFields();
        this.regTime = '';
        this.fetchData01();
      },
      // 关闭甄选APP下载提示弹窗
      reminderDialogClose() {
        this.reminderDialogVisible = false;
        // 直接简单暴力清除 URL 后的 ?payTrue=true
        if (window.location.hash.includes('?')) {
          window.location.hash = window.location.hash.split('?')[0];
        }
        // 更新列表数据
        that.fetchData01();
      },
      // 下载图片
      downloadByBlob(imgUrl) {
        let image = new Image();
        image.setAttribute('crossOrigin', 'anonymous');
        image.src = imgUrl ? imgUrl : this.qrCode;
        let name = '二维码';
        image.onload = () => {
          let canvas = document.createElement('canvas');
          canvas.width = image.width;
          canvas.height = image.height;
          let ctx = canvas.getContext('2d');
          ctx.drawImage(image, 0, 0, image.width, image.height);
          canvas.toBlob((blob) => {
            let url = URL.createObjectURL(blob);
            this.download(url, name);
            // 用完释放URL对象
            URL.revokeObjectURL(url);
          });
        };
      },
      // 下载图片
      download(href, name) {
        let eleLink = document.createElement('a');
        eleLink.download = name;
        eleLink.href = href;
        eleLink.click();
        eleLink.remove();
      },
      downloadAppQrCode() {
        this.downloadByBlob(this.dxAppQrCode);
      },

      restList() {
        this.grammarData = {
          studentCode: '',
          phase: '',
          studentName: ''
        };
        this.phoneNum = '';
      }
    }
  };
</script>

<style lang="scss" scoped>
  /* .container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
} */

  .period-table td,
  .period-table th {
    text-align: center;
  }

  .mt20 {
    margin-top: 20px;
  }

  .red {
    color: red;
  }

  .green {
    color: green;
  }

  .demonstration_dev {
    margin-bottom: 10px;
    font-size: 18px;
  }

  @media screen and (max-width: 767px) {
    .recharge-dialog .el-dialog {
      width: 90% !important;
    }

    .el-message-box {
      width: 80% !important;
    }
  }

  .hidden-label .el-radio__label {
    display: inline-block;
    width: 0;
    visibility: hidden;
  }

  .reded {
    ::v-deep input {
      color: red !important;
    }
  }

  .inputpre {
    position: relative;
  }

  .btnpob {
    position: absolute;
    right: 0;
    top: 1px;
  }
  .text-zztk {
    font-size: 18px;
    text-align: center;
    b {
      color: red;
    }
  }

  ::v-deep .el-table__header-wrapper .el-checkbox {
    /* display: none; //设置不成功，页面卡顿 */
    visibility: hidden;
  }

  .reminderDialog {
    .content-area {
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    .toast1 {
      color: #1d755c;
      margin-bottom: 24px;
      font-size: 18px;
    }

    .toast2,
    .toast3 {
      font-size: 15px;
      font-weight: 500;
      line-height: 30px;
      text-align: center;
      color: #000;
    }

    .download-btn {
      padding: 0 20px;
      height: 30px;
      border-radius: 15px;
      background-color: #1d755c;
      display: inline-block;
      color: #fff;
      font-size: 14px;
      line-height: 30px;
      cursor: pointer;
    }

    .image {
      width: 200px;
      height: 200px;
      object-fit: contain;
      margin-top: 20px;
      margin-bottom: 20px;
    }
  }
</style>
