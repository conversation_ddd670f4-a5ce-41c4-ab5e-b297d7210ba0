/**
 *自然拼写课程
 */
import request from '@/utils/request';

export default {
  //拼写课程新增
  pdCourseSave(data) {
    return request({
      url: '/znyy/pd/pdCourse/save',
      method: 'POST',
      data
    });
  },
  checkSyllableSplit(data) {
    return request({
      url: 'znyy/pd/pdCourse/checkSyllableSplit',
      method: 'POST',
      data
    });
  },

  // 拼写课程编辑
  pdCourseUpdate(data) {
    return request({
      url: '/znyy/pd/pdCourse/update',
      method: 'POST',
      data
    });
  },
  // 听写检测配置详情
  getInfoById(data) {
    return request({
      url: '/znyy/pd/pdCourse/getInfoById',
      method: 'POST',
      data
    });
  },
  // 拼读课程列表
  pdCoursePage(data) {
    return request({
      url: `/znyy/pd/pdCourse/page/${data.currentPage}/${data.size}?courseCode=${data.courseCode}&courseName=${data.courseName}&categoryCode=${data.categoryCode}&isEnable=${data.isEnable}&addStartTime=${data.addStartTime}&addEndTime=${data.addEndTime}&courseType=${data.courseType}&courseLevel=${data.courseLevel}`,
      method: 'GET'
    });
  },
  // 拼读文件解析
  analysisFile(data) {
    return request({
      url: `/znyy/pd/pdCourse/analysisFile`,
      method: 'POST',
      data
    });
  },
  // 拼读文件解析
  analysisFile2(data) {
    return request({
      url: `/znyy/pd/pdCourse/analysisFile/new`,
      method: 'POST',
      data
    });
  },

  // 拼读课程-添加体验课程
  saveExperienceCourse(data) {
    return request({
      url: `/znyy/pd/pdCourse/saveExperienceCourse`,
      method: 'POST',
      data
    });
  },
  //添加课程 - 查询全部课程
  getAllCourse() {
    return request({
      url: `/znyy/pd/pdCourse/getAllCourse`,
      method: 'GET'
    });
  },

  //拼读课程-根据id查询体验课程详情
  getExperienceCourseById(data) {
    return request({
      url: `/znyy/pd/pdCourse/getExperienceCourseById`,
      method: 'GET',
      data
    });
  },

  // 修改体验课程
  updateExperienceCourse(data) {
    return request({
      url: `/znyy/pd/pdCourse/updateExperienceCourse`,
      method: 'POST',
      data
    });
  },
  //课程启用禁用状态
  updateEnable(data) {
    return request({
      url: `/znyy/pd/pdCourse/updateEnable`,
      method: 'POST',
      data
    });
  },
  /////////////////////////////////////////////////////////////制作课程///////////////////////////////////////////
  //删除
  deleteWordById(id, deleteType) {
    return request({
      url: `/znyy/pd/pdCourse/deleteWordById?id=${id}&deleteType=${deleteType}`,
      method: 'GET'
    });
  },
  //视频列表/详情
  getVideoUrlByCourseCode(data) {
    return request({
      url: `/znyy/pd/pdCourse/getVideoUrlByCourseCode?courseCode=${data}`,
      method: 'GET'
    });
  },
  //
  //修改/添加课程视频
  updateVideoUrlByCourseCode(data) {
    return request({
      url: `/znyy/pd/pdCourse/updateVideoUrlByCourseCode`,
      method: 'POST',
      data
    });
  },
  //查询元辅音列表
  getVowelAndConsonantList(data) {
    return request({
      url: `/znyy/pd/pdCourse/getVowelAndConsonantList?courseCode=${data}`,
      method: 'GET'
    });
  },
  //添加元辅音
  saveVowelAndConsonant(id, consonantInfo, vowelInfo) {
    return request({
      url: `/znyy/pd/pdCourse/saveVowelAndConsonant?id=${id}&consonantInfo=${consonantInfo}&vowelInfo=${vowelInfo}`,
      method: 'GET'
    });
  },
  //查询元辅音
  getVowelAndConsonant(id) {
    return request({
      url: `/znyy/pd/pdCourse/getVowelAndConsonant?id=${id}`,
      method: 'GET'
    });
  },
  //修改元辅音
  updateVowelAndConsonant(id, word) {
    return request({
      url: `/znyy/pd/pdCourse/updateVowelAndConsonant?id=${id}&wordInfo=${word}`,
      method: 'GET'
    });
  },
  //查询音节列表
  getSyllableList(courseCode) {
    return request({
      url: `/znyy/pd/pdCourse/getSyllableList?courseCode=${courseCode}`,
      method: 'GET'
    });
  },
  //添加音节
  saveSyllable(data) {
    console.log(data.vowelInfo, 'vowelInfovowelInfo');
    return request({
      url: `/znyy/pd/pdCourse/saveSyllable`,
      method: 'POST',
      data
    });
  },
  //查询音节详情
  getSyllable(id) {
    return request({
      url: `/znyy/pd/pdCourse/getSyllable?id=${id}`,
      method: 'GET'
    });
  },
  //修改音节
  updateSyllable(id, oldSyllableInfo, newSyllableInfo) {
    return request({
      url: `/znyy/pd/pdCourse/updateSyllable?id=${id}&oldSyllableInfo=${oldSyllableInfo}&newSyllableInfo=${newSyllableInfo}`,
      method: 'GET'
    });
  },
  ///////////////////////////////////////////////////体验课//////////////////////////////////////////////////
  //体验课-查询拼读拼写列表
  getReadAndWriteList(code) {
    return request({
      url: `/znyy/pd/pdCourse/getReadAndWriteList?courseCode=${code}`,
      method: 'GET'
    });
  },
  //添加拼读拼写单词
  saveReadAndWrite(data) {
    return request({
      url: `/znyy/pd/pdCourse/saveReadAndWrite`,
      method: 'POST',
      data
    });
  },
  //查询拼读拼写
  getReadAndWriteById(id) {
    return request({
      url: `/znyy/pd/pdCourse/getReadAndWriteById?id=${id}`,
      method: 'GET'
    });
  },
  //修改拼读拼写单词
  updateReadAndWrite(data) {
    return request({
      url: `/znyy/pd/pdCourse/updateReadAndWrite`,
      method: 'POST',
      data
    });
  },
  //体验课-查询全部拼读拼写单词
  getAllReadWriteInfo(data) {
    return request({
      url: `/znyy/pd/pdCourse/getAllReadWriteInfo`,
      method: 'GET',
      data
    });
  },
  //体验课-选择拼读拼写单词
  selectReadAndWriteInfo(data) {
    return request({
      url: `/znyy/pd/pdCourse/selectReadAndWriteInfo`,
      method: 'GET',
      data
    });
  },
  //
  //通用-制作课程-查询全部拼读拼写单词
  // 体验课：
  // 传参id 和 wordType（2或者3）即可
  // 正式课：
  // 划拼音规则：传参id和wordType（4）即可
  //       划拼读规则（无划音节）：传参id和wordType（2）和listId（规则列表中id）即可
  getAllRuleWord(id, wordType, listId) {
    return request({
      url: `/znyy/pd/pdCourse/getAllRuleWord?id=${id}&wordType=${wordType}&listId=${listId}`,
      method: 'GET'
    });
  },
  // 通用 - 拼读拼写词选中  拼读拼写检测词选中
  updateAllRuleWord(data) {
    return request({
      url: `/znyy/pd/pdCourse/updateAllRuleWord`,
      method: 'POST',
      data
    });
  },
  // wordType 4划音节2划拼读  videoType 1规则视频 (划音节循环) 2规则视频 (拼读循环)
  //划音节列表 videoType  = 4 ，courseCode =1 划拼读列表 videoType  = 2 ，courseCode = 2
  //查询视频 正式课-通用  划拼音/划拼读拼写
  getRuleListByCourseCode(courseCode, wordType, videoType) {
    return request({
      url: `/znyy/pd/pdCourse/getRuleListByCourseCode?courseCode=${courseCode}&wordType=${wordType}&videoType=${videoType}`,
      method: 'GET'
    });
  },
  //正式课-添加划拼音规则
  pdCourseSaveRule(data) {
    return request({
      url: `/znyy/pd/pdCourse/saveRule`,
      method: 'POST',
      data
    });
  },
  //正式课-划拼音规则详情
  getVideoUrlById(id, listId) {
    return request({
      url: `/znyy/pd/pdCourse/getVideoUrlById?id=${id}&listId=${listId}`,
      method: 'GET'
    });
  },
  //正式课-修改划拼音规则
  updateRule(data) {
    return request({
      url: `/znyy/pd/pdCourse/updateRule`,
      method: 'POST',
      data
    });
  },
  //正式课-无划拼音 添加拼读拼写
  saveRuleReadAndWrite(data) {
    return request({
      url: `/znyy/pd/pdCourse/saveRuleReadAndWrite`,
      method: 'POST',
      data
    });
  },
  //
  //正式课-无划拼音 编辑拼读拼写
  updateRuleReadAndWrite(data) {
    return request({
      url: `/znyy/pd/pdCourse/updateRuleReadAndWrite`,
      method: 'POST',
      data
    });
  },
  //正式课-无划拼音 查询拼读拼写
  getRuleReadAndWrite(id, listId, type) {
    return request({
      url: `/znyy/pd/pdCourse/getRuleReadAndWrite?id=${id}&listId=${listId}&wordType=${type}`,
      method: 'GET'
    });
  },

  //制作课程 - 查询全部单词音频
  getAllAudioUrl(id) {
    return request({
      url: `/znyy/pd/pdCourse/getAllAudioUrl?id=${id}`,
      method: 'GET'
    });
  },

  //制作课程 - 上传修改全部单词音频
  updateAllAudioUrl(data) {
    return request({
      url: `/znyy/pd/pdCourse/updateAllAudioUrl`,
      method: 'POST',
      data
    });
  },
  //制作课程 - 删除保利威视频信息
  deleteVideo(vid) {
    return request({
      url: `/znyy/pd/pdCourse/deleteVideo?vid=${vid}`,
      method: 'GET'
    });
  },

  //制作课程 - 查询保利威视频信息
  getVideoInfo(vid) {
    return request({
      url: `/znyy/pd/pdCourse/getVideoInfo?vid=${vid}`,
      method: 'GET'
    });
  },

  // 课程开通与暂停
  updateStatus(id, status) {
    return request({
      url: '/znyy/course/big/class/openAndSuspension?id=' + id + '&isEnable=' + status,
      method: 'PUT'
    });
  },
  // 获取分类列表
  categoryType() {
    return request({
      url: '/znyy/course/category/check/list',
      method: 'GET'
    });
  },
  //元辅音/音节上传解析
  baseAnalysisFile(data) {
    return request({
      url: '/znyy/pd/pdCourse/baseAnalysisFile',
      method: 'POST',
      data
    });
  },

  // 上移
  wordUp(data) {
    return request({
      url: '/znyy/pd/courseWord/moveUp',
      method: 'PUT',
      data
    });
  },

  // 下移
  wordDown(data) {
    return request({
      url: '/znyy/pd/courseWord/moveDown',
      method: 'PUT',
      data
    });
  },
};
