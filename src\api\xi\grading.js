import request from '@/utils/request'

export default {
  // 分页查询
  list(data) {
    return request({
      url: '/xi/web/grading/list',
      method: 'GET',
      params: data
    })
  },
  selectBigGradingList() {
    return request({
      url: '/xi/web/grading/selectBigGradingList',
      method: 'GET'
    })
  },
  detail(id){
    return request({
      url: '/xi/web/grading',
      method: 'GET',
      params:{
        id:id
      }
    })
  },
//添加
  saveOrUpdate(data) {
    return request({
      url: '/xi/web/grading',
      method: 'POST',
      data
    })
  },
  //删除
  delete(id){
    return request({
      url: '/xi/web/grading',
      method: 'DELETE',
      params:{
        id:id
      }
    })
  },
  //删除
  getLessCredits(credits){
    return request({
      url: '/xi/web/grading/getLessCredits',
      method: 'GET',
      params:{
        credits:credits
      }
    })
  }
}
