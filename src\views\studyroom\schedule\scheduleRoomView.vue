<template>
  <div class="app-container">
    <el-col :span="24" style="margin-bottom: 30px">
      <el-button type="warning" icon="el-icon-back" size="mini" @click="goBack()">返回</el-button>
    </el-col>

    <el-table class="common-table" v-loading="tableLoading" :data="tableData" style="width: 100%;margin-bottom: 20px;"
      row-key="id" border default-expand-all>
      <el-table-column prop="roomId" label="房间ID"></el-table-column>
      <el-table-column prop="roomName" label="房间名称"></el-table-column>
      <el-table-column label="操作">
        <template slot-scope="scope">
          <el-button  type="text" size="mini" @click="changeCommitteeman(scope.row)">更换学委</el-button>
          <el-button type="text" size="mini" v-if="scope.row.roomId">
            <router-link :to="'/schedule/fixTrtcRoom/'+scope.row.scheduleId">进入房间</router-link>
          </el-button>
        </template>
      </el-table-column>
      <el-table-column prop="courseType" label="课程分类">
        <template slot-scope="scope">
          <span v-if="scope.row.courseType == 1">系统课</span>
          <span v-else>非系统课</span>
        </template>
      </el-table-column>
      <el-table-column prop="weeks" label="日期"></el-table-column>
      <el-table-column label="房间人数">
        <template slot-scope="scope">
          {{ scope.row.currentCount }}/{{ scope.row.maxUserCount }}
        </template>
      </el-table-column>
      <el-table-column prop="times" label="规划时间"></el-table-column>
      <el-table-column label="剩余时间">
        <template slot-scope="scope">
          <span v-if="scope.row.minute === '-1'">
            --
          </span>
          <span v-else>{{ scope.row.minute }}分钟</span>
        </template>
      </el-table-column>
      <el-table-column label="状态">
        <template slot-scope="scope">
          <span v-if="scope.row.status === 1">
            未开始
          </span>
          <span v-else>进行中</span>
        </template>
      </el-table-column>
    </el-table>

    <!-- 更换学委 -->
    <el-dialog title="更换学委" :visible.sync="open" width="60%" :close-on-click-modal="false" @close="close">
      <el-table class="common-table" v-loading="loading" :data="pageData" style="width: 100%;margin-bottom: 20px;"
        row-key="id" border ref="singleTable" highlight-current-row :row-class-name="tableRowClassName"
        @current-change="handleRowChange">
        <el-table-column prop="id" label="学委ID"></el-table-column>
        <el-table-column prop="name" label="姓名"></el-table-column>
        <el-table-column prop="phone" label="手机号"></el-table-column>
        <el-table-column prop="gender" label="性别">
          <template slot-scope="scope">
            <span v-if="scope.row.gender === '1'">男</span>
            <span v-else>女</span>
          </template>
        </el-table-column>
        <el-table-column prop="classNum" label="班级数量"></el-table-column>
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm()">更换学委</el-button>
      </div>
      <!-- 分页 -->
      <div style="padding-top: 10px">
        <el-col :span="24" style="margin-bottom: 20px">
          <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
            layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
            @current-change="handleCurrentChange" />
        </el-col>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import studyScheduleApi from "@/api/studyroom/studySchedule";
import { pageParamNames } from "@/utils/constants";
import committeemanApi from "@/api/studyroom/committeeman";

export default {
  name: 'Schedule',
  data() {
    return {
      tableLoading: false,
      loading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      open: false,
      tableData: [],
      pageData: [],
      committeemanCode: null,
      parentScheduleId: null,
      scheduleId: null,
      committeemanId: null,
    };
  },
  created() {
    if (this.$route.params && this.$route.params.scheduleId) {
      this.parentScheduleId = this.$route.params.scheduleId;
      this.getScheduleRooms();
    }

  },
  methods: {
    goBack() {
      this.$store.dispatch('delVisitedViews', this.$route);
      this.$router.push("/schedule/gradeList");
    },
    submitForm() {
      if (this.committeemanId == null) {
        this.$message.error("请选择学委！")
        return;
      }
      studyScheduleApi.appointCommittee(this.scheduleId, this.committeemanId).then(res => {
        this.$message.success("更换成功！")
        this.open = false;
        this.getScheduleRooms();
      })
    },
    //更换学委
    changeCommitteeman(row) {
      this.reset();
      this.committeemanCode = row.studyCommitteemanCode;
      this.scheduleId = row.scheduleId;
      this.getPageList();
      this.open = true;
    },

    getScheduleRooms() {
      this.tableLoading = true;
      studyScheduleApi.getScheduleRooms(this.parentScheduleId).then(res => {
        this.tableData = res.data;
        this.tableLoading = false;
      })
    },
    tableRowClassName({ row, rowIndex }) {
      if (row.merchantCode === this.committeemanCode) {
        return 'success-row';
      }
      return '';
    },
    getPageList() {
      this.loading = true;
      committeemanApi.committeemanList(this.tablePage.currentPage, this.tablePage.size).then(res => {
        this.pageData = res.data.data;
        this.loading = false;
        // 设置后台返回的分页参数
        pageParamNames.forEach(name => this.$set(this.tablePage, name, parseInt(res.data[name])))
      })
    },
    handleRowChange(row) {
      if (row) {
        this.committeemanCode = row.merchantCode;
        this.committeemanId = row.id;
      } else {
        this.committeemanCode = null;
        this.committeemanId = null;
      }
    },
    close() {
      this.reset();
      this.setCurrent();
    },
    reset() {
      this.committeemanCode = null;
      this.scheduleId = null;
      this.committeemanId = null;
    },
    setCurrent(row) {
      this.$refs.singleTable.setCurrentRow(row);
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val;
      this.getPageList();
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val;
      this.getPageList();
    },
  }
};
</script>

<style>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.period-table td,
.period-table th {
  text-align: center;
}

@media (max-width:767px) {
  .el-message-box {
    width: 80% !important;
  }
}

.el-table .success-row {
  background: #e8f4ff;
}
</style>
