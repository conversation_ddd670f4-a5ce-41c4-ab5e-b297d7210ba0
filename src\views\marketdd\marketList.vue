<template>
  <div class="app-container">
    <el-form :inline="true" class="container-card" label-width="96px" label-position="left">
      <el-row>
        <el-col :span="8" :xs="24">
          <el-form-item label="商户编号:">
            <el-input id="merchantCode" v-model="dataQuery.merchantCode" name="id" clearable placeholder="请输入商户编号：" />
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="登录账号：">
            <el-input id="name" v-model="dataQuery.name" name="id" clearable placeholder="请输入登录账号：" />
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="商户名称：">
            <el-input id="merchantName" v-model="dataQuery.merchantName" name="id" clearable placeholder="请输入服务商名称" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8" :xs="24">
          <el-form-item label="负责人：">
            <el-input id="realName" v-model="dataQuery.realName" name="id" clearable placeholder="请输入负责人" />
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="所在地区：">
            <el-input id="address" v-model="dataQuery.address" name="id" clearable placeholder="请输入所在地区：" />
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="级别：">
            <el-select v-model="dataQuery.rank" filterable value-key="value" placeholder="请选择">
              <el-option v-for="(item, index) in [
                { value: 'A', label: 'A级别' },
                { value: 'B', label: 'B级别' },
                { value: 'C', label: 'C级别' },
                { value: 'D', label: 'D级别' },
                { value: 'E', label: 'E级别' },
                { value: 'F', label: 'F级别' },
              ]"
                         :key="index" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8" :xs="24">
          <el-form-item label="状态：">
            <el-select v-model="dataQuery.isEnable" filterable value-key="value" placeholder="请选择">
              <el-option v-for="(item, index) in [
                { value: '1', label: '开通' },
                { value: '0', label: '暂停' },
              ]"
                         :key="index" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      <el-col :span="13" :xs="24">
        <el-form-item label="添加时间：">
          <el-date-picker style="width: 100%;" v-model="regTime" type="daterange" align="right" unlink-panels range-separator="至"
                          start-placeholder="开始日期" end-placeholder="结束日期" value-format="yyyy-MM-dd HH:mm:ss">
          </el-date-picker>
        </el-form-item>
      </el-col>

      <el-col :span="3" style="text-align: right">
        <el-button type="primary" icon="el-icon-search" @click="fetchData()">搜索</el-button>
      </el-col>
    </el-row>
      <!-- <el-form-item label="上级编号：">
        <el-input id="realName" v-model="dataQuery.realName" name="id" placeholder="请输入上级编号："
          clearable />
      </el-form-item>
      <el-form-item label="推荐人编号：">
        <el-input id="marketPartner" v-model="dataQuery.marketPartner" name="id"  placeholder="请输入推荐人编号："
          clearable/>
      </el-form-item> -->

    </el-form>
    <!-- <el-col :span="24" style="margin-bottom: 30px">
      <el-button
        type="warning"
        icon="el-icon-document-copy"
        @click="exportFlow"
        v-loading="exportLoading"
        size="mini"
        >导出</el-button
      >
    </el-col> -->
    <el-table class="common-table" v-loading="tableLoading" :data="tableData" style="width: 100%; margin-bottom: 20px"
      row-key="id" border default-expand-all>
      <el-table-column prop="merchantCode" label="编号" width="150" align="center" :show-overflow-tooltip="true"></el-table-column>
      <el-table-column prop="name" label="登录账号" align="center" width="150"></el-table-column>
      <el-table-column prop="merchantName" label="名称" align="center" width="200" show-overflow-tooltip></el-table-column>
      <el-table-column prop="marketPartner" label="推荐人编号" align="center" width="150" :show-overflow-tooltip="true"></el-table-column>
      <el-table-column prop="refereeCode" label="上级编号" align="center" width="150" :show-overflow-tooltip="true"></el-table-column>>
      <el-table-column prop="realName" label="负责人" align="center" width="150" :show-overflow-tooltip="true"></el-table-column>
      <el-table-column prop="address" label="所在地区" align="center" width="300" :show-overflow-tooltip="true"></el-table-column>
      <el-table-column prop="rank" label="级别" align="center" :show-overflow-tooltip="true"></el-table-column>
      <el-table-column prop="sumChargeMoney" label="累计充值金额（元）" align="center" width="150">
        <template slot-scope="scope">
          <span v-if="
              scope.row.sumChargeMoney === null ||
              scope.row.sumChargeMoney === ''
            ">0.00</span>
          <span v-else>{{ scope.row.sumChargeMoney }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="sumRebateMoney" label="累计返点金额（元）" align="center" width="150">
        <template slot-scope="scope">
          <span v-if="
              scope.row.sumRebateMoney === null ||
              scope.row.sumRebateMoney === ''
            ">0.00</span>
          <span v-else>{{ scope.row.sumRebateMoney }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="sumGiveMoney" label="累计赠送金额（元）" align="center" width="150">
        <template slot-scope="scope">
          <span v-if="
              scope.row.sumGiveMoney === null ||
              scope.row.sumGiveMoney === ''
            ">0.00</span>
          <span v-else>{{ scope.row.sumGiveMoney }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="accountMoney" label="账户余额（元）" align="center" width="150">
        <template slot-scope="scope">
          <span v-if="
              scope.row.accountMoney === null ||
              scope.row.accountMoney === ''
            ">0.00</span>
          <span v-else>{{ scope.row.accountMoney }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="withdrawMoney" label="账下余额（元）" align="center" width="150"> <template slot-scope="scope">
          <span v-if="
              scope.row.withdrawMoney === null ||
              scope.row.withdrawMoney === ''
            ">0.00</span>
          <span v-else>{{ scope.row.withdrawMoney }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="withdrawMoneyOl" label="线上余额（元）" align="center" width="150">
        <template slot-scope="scope">
          <span v-if="
              scope.row.withdrawMoneyOl === null ||
              scope.row.withdrawMoneyOl === ''
            ">0.00</span>
          <span v-else>{{ scope.row.withdrawMoneyOl }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="subDealerCount" label="下级托管中心" align="center" width="150"></el-table-column>
      <el-table-column prop="subSchoolCount" label="下级门店" align="center" width="150"></el-table-column>
      <el-table-column prop="isCheck" label="审核状态" align="center" width="120">
        <template slot-scope="scope">
          <span class="green" v-if="scope.row.isCheck === 1">已审核</span>
          <span class="red" v-if="scope.row.isCheck === 0">未审核</span>
          <span class="blue" v-if="scope.row.isCheck === 2">未通过审核</span>
        </template>
      </el-table-column>
      <el-table-column prop="isEnable" label="账户状态" align="center" width="120">
        <template slot-scope="scope">
          <span class="green" v-if="scope.row.isEnable === 1">开通</span>
          <span class="red" v-if="scope.row.isEnable === 0">暂停</span>
        </template>
      </el-table-column>
      <el-table-column prop="regTime" label="添加时间" width="200" align="center"></el-table-column>
    </el-table>

    <!-- 分页 -->
    <el-col :span="20">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]" layout="total, sizes, prev, pager, next, jumper"
        :total="tablePage.totalItems" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    </el-col>
  </div>
</template>

<script>
  import {
    queryOfficialAccountLink
  } from "@/api/wechatPublicAccount";
  import dealerListApi from "@/api/dealerList";
  import marketApi from "@/api/marketList";
  import Tinymce from "@/components/Tinymce";
  import {
    pageParamNames
  } from "@/utils/constants";
  import {
    getAddOrUpdate,
    setAddOrUpdate,
    setDealerId
  } from "@/utils/auth";
  export default {
    data() {
      return {
        tableLoading: false,
        // 分页
        tablePage: {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null,
        },
        exportLoading: false, //导出加载
        RegTime: '',
        tableData: [],
        dataQuery: {
          merchantCode: "",
          name: "",
          merchantName: '',
          realName: '',
          address: '',
        },
        dialogVisible: false,
        updateMarketDate: {},
        addMarketDate: {},
        showLoginAccount: false,
        regTime: '',
        ruls: [],
        disabled: true,
        isCheckStatu: {}, //审核状态
        rules: {
          rank: [{
            required: true,
            message: '必填',
            trigger: 'blur'
          }],
          isCheck: [{
            required: true,
            message: '必填',
            trigger: 'blur'
          }],
        },
      };
    },
    created() {
      this.fetchData();
    },
    methods: {
      // 查询+搜索课程列表
      fetchData() {
        const that = this;
        var a = that.regTime;
        if (a != null) {
          that.dataQuery.startDate = a[0];
          that.dataQuery.endDate = a[1];
        }
        that.tableLoading = true;
        marketApi
          .areaMarketList(
            that.tablePage.currentPage,
            that.tablePage.size,
            that.dataQuery
          )
          .then((res) => {
            that.tableData = res.data.data;
            // console.log(res)
            that.tableLoading = false;
            // 设置后台返回的分页参数
            pageParamNames.forEach((name) =>
              that.$set(that.tablePage, name, parseInt(res.data[name]))
            );
          });
      },


      // 分页
      handleSizeChange(val) {
        this.tablePage.size = val;
        this.fetchData();
      },
      handleCurrentChange(val) {
        this.tablePage.currentPage = val;
        this.fetchData();
      },
      //导出
      //  exportFlow() {
      //   const that = this;
      //   that.exportLoading = true;
      //   //
      //   dealerListApi.simpleMarketExecl(that.dataQuery).then((res) => {
      //     //           this.$notify.error({
      //     //               title: "操作失败",
      //     //               message: "文件下载失败"
      //     //             });
      //     const url = window.URL.createObjectURL(res);
      //     const link = document.createElement("a");
      //     link.style.display = "none";
      //     link.href = url; // 获取服务器端的文件名
      //     link.setAttribute("download", "托管中心列表.xls");
      //     document.body.appendChild(link);
      //     link.click();
      //     that.exportLoading = false;
      //   });
      // },
      //导出
      exportFlow() {
        const that = this;
        that.exportLoading = true;
        //
        dealerListApi.simpleMarketExecl(that.dataQuery).then((res) => {
          //           this.$notify.error({
          //               title: "操作失败",
          //               message: "文件下载失败"
          //             });
          const url = window.URL.createObjectURL(res);
          const link = document.createElement("a");
          link.style.display = "none";
          link.href = url; // 获取服务器端的文件名
          link.setAttribute("download", "托管中心列表.xls");
          document.body.appendChild(link);
          link.click();
          that.exportLoading = false;
        });
      },

    },
  };
</script>

<style>
  .container-card {
    padding: 15px 15px 15px 15px;
    margin-bottom: 15px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  }

  .red {
    color: red;
  }

  .green {
    color: green;
  }

  .blue {
    color: blue;
  }
</style>
