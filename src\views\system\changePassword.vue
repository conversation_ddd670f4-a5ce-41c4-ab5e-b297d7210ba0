<template>
  <div class="app-container">
    <el-form :model="ruleForm" :rules="rules" ref="ruleForm"  class="recharge-form" label-width="160px" label-position="right">
      <el-form-item label="新登录密码：" prop="">
        <el-input v-model="dataQuery.merchantCode" show-password />
      </el-form-item>
      <el-form-item label="重复新登录密码：" prop="">
        <el-input v-model="dataQuery.merchantCode" show-password/>
      </el-form-item>
      <el-form-item label="新交易密码：">
        <el-input v-model="dataQuery.merchantCode" show-password/>
      </el-form-item>
      <el-form-item label="重复新交易密码：" prop="">
        <el-input v-model="dataQuery.merchantCode"  show-password/>
      </el-form-item>
      <el-form-item label="输入当前登录密码：" prop="">
        <el-input  v-model="dataQuery.merchantCode"  show-password/>
      </el-form-item>
      <el-form-item>
        <el-button type="success" icon="el-icon-document" >保存</el-button>
        <el-button type="warning" @click="resetForm('ruleForm')">清除</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
  export default {
    data() {
      return {
        ruleForm: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dataQuery:{},
        rules: {}
      }
    },
    created() {

    },
    methods: {
      resetForm(formName) {
        this.$refs[formName].resetFields();
      }
    }
  };
</script>

<style scoped>
  .recharge-form input{
    width: 185px;
  }
  .recharge-form textarea{
    width: 400px;
  }
</style>
