<template>
  <div class="app-container">
    <!-- 新增按钮 -->
    <el-row class="container-card">
      <el-col :span="12">
        <el-col :span="6" class="marginbottom" style="line-height: 36px;">商户列表编号</el-col>
        <el-col :span="6">
          <el-input v-model="queryMerchantCode" name="id" placeholder="商户列表编号" />
        </el-col>
      </el-col>
      <el-col :span="12" style="text-align: right;">
        <el-button type="primary" icon="el-icon-search" @click="fetchData()" size="mini">搜索</el-button>
      </el-col>
    </el-row>

    <el-row class="app-container">
      <el-col :span="24">
        <el-col :span="4" class="marginbottom" style="line-height: 36px;">请输入商户列表编号</el-col>
        <el-col :span="10" style="padding-left: 10px">
          <el-form :model="qrCodeCreate">
            <el-input id="name" v-model="qrCodeCreate.merchantCode" name="id" placeholder="请输入MerchantCode值" />
          </el-form>
        </el-col>

        <el-col :span="10" style="text-align: right">
          <el-button type="danger" @click="create(qrCodeCreate)" size="mini">生成</el-button>
        </el-col>
      </el-col>

    </el-row>

    <el-table v-loading="tableLoading" :data="tableData" border stripe style="width: 100%;margin-bottom: 30px;">
      <el-table-column prop="merchantCode" label="商户编号" align="center"  :show-overflow-tooltip="true"/>
      <el-table-column prop="merchantName" label="校名" align="center"  :show-overflow-tooltip="true"/>
      <el-table-column label="操作" align="center">
        <template slot-scope="scope">
          <el-tooltip content="咨询师码详情" placement="top">
            <el-button type="warning" @click="detail(scope.row.memberId)" size="mini">详情</el-button>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间" align="center"  :show-overflow-tooltip="true"/>
    </el-table>

    <!-- 分页 -->
    <el-col :span="20">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]" layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    </el-col>

    <!-- 生成0元订单弹窗-->
<!--    <el-dialog title="赠送托管中心" :visible.sync="dialogRefereeVisible">-->
<!--      <el-form :model="qrCodeCreate" :label-position="left">-->
<!--        <el-form-item label="商户编号" prop="merchantCode">-->
<!--          <el-input v-model="qrCodeCreate.merchantCode" placeholder="请输入MerchantCode值" />-->
<!--        </el-form-item>-->

<!--        <el-form-item label="选择推荐人" prop="refereeId">-->
<!--          <el-select v-model="qrCodeCreate.refereeId" placeholder="请选择">-->
<!--            <el-option-->
<!--              v-for="item in options"-->
<!--              :key="item.value"-->
<!--              :label="item.label"-->
<!--              :value="item.value"-->
<!--            />-->
<!--          </el-select>-->
<!--        </el-form-item>-->
<!--      </el-form>-->

<!--      <div slot="footer" class="dialog-footer">-->
<!--        <el-button type="danger" size="mini" @click="creatZeroOrder">生 成</el-button>-->
<!--      </div>-->
<!--    </el-dialog>-->

    <!-- 体验课二维码列表 -->
    <el-dialog v-loading="tableLoading" title="校区咨询师码" :visible.sync="dialogCodeVisible" width="70%"  :close-on-click-modal="false" @close="dialogCodeVisible=false">
      <el-table v-loading.body="tableLoading" :data="schoolCodeList" border stripe fit highlight-current-row style="width: 100%">
        <el-table-column label="是否使用" prop="isUsed" align="center">
          <template slot-scope="scope">
            <el-button :type="scope.row.isUsed==0? 'primary':'default'" size="mini" plain disabled>{{ scope.row.isUsed==0? '未使用':'使用' }}</el-button>
          </template>
        </el-table-column>
        <el-table-column label="二维码" prop="qrcodePath" align="center">
          <template slot-scope="scope"><img width="40px" :src="scope.row.qrcodePath"></template>
        </el-table-column>
        <el-table-column prop="id" label="二维码编号" align="center" width="180" />
        <el-table-column prop="memberName" label="使用人" align="center" :show-overflow-tooltip="true"/>
        <el-table-column prop="memberMobile" label="联系方式" align="center" :show-overflow-tooltip="true"/>
        <el-table-column prop="useTime" label="使用时间" align="center" :show-overflow-tooltip="true"/>
      </el-table>
    </el-dialog>
    <el-dialog
      title="请选择咨询师地址"
      :visible.sync="showAddressSelect"
      width="30%"
      :show-close="false"
    >
      <span>
        <div class="block">
          <span class="grid-content">地址:</span>
          <el-cascader
            placeholder="试试搜索：庐阳区"
            :props="optionsAddressProps"
            :options="optionsAddress"
            filterable

            @change="addressCheck"
          />
        </div>

      </span>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="addressCreateLoad" @click="addressConfirm" size="mini">确 定</el-button>
      </span>
    </el-dialog>

  </div>

</template>

<script>
import experienceCodeApi from '@/api/experienceCode'
import {
  pageParamNames
} from '@/utils/constants'

export default {
  data() {
    return {
      qrCodeCreate: {
        merchantCode: ''
      },
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      optionsAddressProps: {
        checkStrictly: true,
        value: 'name',
        label: 'name',
        children: 'children'
      },
      address: '',
      addressArray: [],
      optionsAddress: [],
      showAddressSelect: false,
      addressCreateLoad: false,
      queryMerchantCode: '',
      tableLoading: false,
      tableData: [],

      dialogRefereeVisible:false,   //生成0元订单弹窗

      dialogCodeVisible: false, // 校区二维码列表弹窗是否显示
      schoolCodeList: []
    }
  },
  created() {
    this.fetchData()
    this.queryAllRegionEx()
  },
  methods: {
    addressCheck(value) {
      this.address = value
    },
    addressConfirm() {
      if (this.address.length < 3 || this.address[2] === undefined || this.address[2] == null) {
        this.$message.error('请选择地址')
      } else {
        this.addressCreateLoad = true
        this.qrCodeCreate.province = this.address[0]
        this.qrCodeCreate.city = this.address[1]
        this.qrCodeCreate.area = this.address[2]
        experienceCodeApi.createCode(this.qrCodeCreate).then(res => {
          this.$nextTick(() => this.fetchData())
          this.$message.success('生成成功')
          this.addressCreateLoad = false
          this.showAddressSelect = false
        }).catch(res => {
          this.addressCreateLoad = false
        })
      }
    },
    create(ele) {
      const that = this
      // console.log(ele)
      // this.dialogRefereeVisible = true;
      experienceCodeApi.checkMemberAddress('merchant', that.qrCodeCreate.merchantCode).then(res => {
        if (!res.data) {
          that.showAddressSelect = true
        } else {
          this.addressCreateLoad = true
          experienceCodeApi.createCode(ele).then(res => {
            that.$nextTick(() => that.fetchData())
            that.$message.success('生成成功')
            this.addressCreateLoad = false
            this.showAddressSelect = false
          }).catch(res => {
            this.addressCreateLoad = false
          })
        }
      })
    },
    //确认生成0元订单
    creatZeroOrder() {
      console.log('确认生成0元订单')
    },
    queryAllRegionEx() {
      experienceCodeApi.queryAllRegion().then(res => {
        this.optionsAddress = res.data
      })
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val
      this.fetchData()
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val
      this.fetchData()
    },
    // 查询
    fetchData() {
      const that = this
      that.tableLoading = true
      experienceCodeApi.codeSourceList(that.tablePage.currentPage, that.tablePage.size, that.queryMerchantCode).then(res => {
        that.tableData = res.data.data
        that.tableLoading = false
        // 设置后台返回的分页参数
        pageParamNames.forEach(name => that.$set(that.tablePage, name, parseInt(res.data[name])))
      })
    },
    detail(id) {
      const that = this
      that.tableLoading = true
      experienceCodeApi.queryCodeSource(id).then(res => {
        that.dialogCodeVisible = true
        that.tableLoading = false
        that.schoolCodeList = res.data
      })
    }
  }

}

</script>

<style>
  .container-card {
    padding: 15px 15px 15px 15px;
    margin-bottom: 15px ;
    box-shadow: 0 2px 4px rgba(0, 0, 0, .12), 0 0 6px rgba(0, 0, 0, .04)
  }

</style>
