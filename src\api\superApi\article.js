import request from "@/utils/request";

// 文章分页
export function listArticleAPI(query) {
  return request({
    url: "/znyy/superReadArticle/list",
    method: "get",
    params: query,
  });
}

// 文章编辑回显
export function findArticleAPI(query) {
  return request({
    url: "/znyy/superReadArticle/find",
    method: "get",
    params: query,
  });
}

// 文章新增
export function addArticleAPI(query) {
  return request({
    url: "/znyy/superReadArticle/add",
    method: "put",
    data: query,
  });
}

// 文章编辑
export function editArticleAPI(query) {
  return request({
    url: "/znyy/superReadArticle/edit",
    method: "post",
    data: query,
  });
}

// 文章删除
export function deleteArticleAPI(query) {
  return request({
    url: "/znyy/superReadArticle/delete",
    method: "delete",
    params: query,
  });
}
