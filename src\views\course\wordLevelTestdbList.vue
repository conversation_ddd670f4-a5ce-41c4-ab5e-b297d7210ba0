<template>
  <div class="app-container">
    <!-- 搜索 -->
    <el-form :inline="true" ref="form" class="SearchForm" label-width="120px" style="padding: 20px 30px 0;">
      <el-row style="margin-top: 20px">
        <el-col :span="8" :xs="24">
          <el-form-item label="单词:">
            <el-input v-model="dataQuery.word" @keyup.enter.native="fetchData()" style="width: 200px;" placeholder="请输入单词:" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="中文:">
            <el-input v-model="dataQuery.chinese" @keyup.enter.native="fetchData()" style="width: 200px;" placeholder="请输入中文:" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="正确答案:">
            <el-input v-model="dataQuery.rightAnswer" @keyup.enter.native="fetchData()" style="width: 200px;" placeholder="请输入正确答案:" clearable></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="18" :xs="24">
          <el-form-item label="现学时段:">
            <el-select v-model="dataQuery.studySegment" filterable value-key="value" placeholder="请选择">
              <el-option v-for="(item,index) in courseStageType" :key="index" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6" :xs="24">
          <el-form-item>
            <el-button type="warning" icon="el-icon-search" @click="fetchData01()">搜索</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <div class="SearchForm">
      <!-- 添加 -->
      <div class="btn-add" style="margin-bottom: 20px">
        <el-button size="small" type="primary" icon="el-icon-plus" @click="clickAdd">添加</el-button>
      </div>
      <!-- 表格 -->
      <el-table class="common-table" :data="tableData" stripe border :default-sort="{prop: 'date', order: 'descending'}" v-loading="tableLoading">
        <el-table-column prop="wordLevelTestCode" label="测试题库编号" sortable ></el-table-column>
        <el-table-column prop="word" label="单词" sortable></el-table-column>
        <el-table-column prop="id" label="操作" sortable >
          <template slot-scope="scope">
            <el-button type="success" size="mini" icon="el-icon-edit-outline" @click="handleUpdate(scope.row.id)">编辑</el-button>
            <el-button type="danger" size="mini" icon="el-icon-delete" @click="handleDelete(scope.row.id)">删除</el-button>
          </template>
        </el-table-column>
        <el-table-column prop="chinese" label="中文"  sortable></el-table-column>
        <el-table-column prop="rightAnswer" label="正确答案" sortable></el-table-column>
        <el-table-column prop="studySegmentName" label="现实学段" sortable></el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <el-col :span="20" style="margin-bottom: 20px">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]" layout="total, sizes, prev, pager, next, jumper"
        :total="tablePage.totalItems" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    </el-col>

    <!-- 编辑或者添加弹窗 -->
    <el-dialog :title="addOrUpdate?'添加单词水平测试题库':'编辑单词水平测试题库'" :visible.sync="dialogVisible" width="70%"
      :close-on-click-modal="false" @close="close">
      <el-form :ref="addOrUpdate?'addWordTestDb':'updateWordDbTest'" :rules="rules" :model="addOrUpdate?addWordTestDb:updateWordDbTest"
        label-position="left" label-width="90px" style="width: 100%;">
        <el-form-item label="单词" prop="word">
          <el-col :xs="24" :span="18">
            <el-input v-if="addOrUpdate" v-model="addWordTestDb.word"></el-input>
            <el-input v-if="!addOrUpdate" v-model="updateWordDbTest.word"></el-input>
          </el-col>
        </el-form-item>
        <el-form-item label="中文" prop="chinese">
          <el-col :xs="24" :span="18">
            <el-input type="textarea" resize="none" :rows="4" v-if="addOrUpdate" v-model="addWordTestDb.chinese" />
            <el-input type="textarea" resize="none" :rows="4" v-if="!addOrUpdate" v-model="updateWordDbTest.chinese" />
          </el-col>
        </el-form-item>
        <el-form-item label="正确答案" prop="rightAnswer">
          <el-col :xs="24" :span="18">
            <el-input v-if="addOrUpdate" v-model="addWordTestDb.rightAnswer"></el-input>
            <el-input v-if="!addOrUpdate" v-model="updateWordDbTest.rightAnswer"></el-input>
          </el-col>
        </el-form-item>
        <el-form-item label="现实学段" prop="studySegment">
          <el-col :xs="24" :span="18">
            <el-select style="width: 100%;" v-if="addOrUpdate" v-model="addWordTestDb.studySegment" filterable value-key="value" placeholder="请选择">
              <el-option v-for="(item,index) in courseStageType" :key="index" :label="item.label" :value="item.value" />
            </el-select>
            <el-select style="width: 100%;" v-if="!addOrUpdate" v-model="updateWordDbTest.studySegment" filterable value-key="value"
              placeholder="请选择">
              <el-option v-for="(item,index) in courseStageType" :key="index" :label="item.label" :value="item.value" />
            </el-select>
          </el-col>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button v-if="addOrUpdate" size="mini" type="primary" @click="addActiveFun('addWordTestDb')">新增</el-button>
        <el-button v-if="!addOrUpdate" size="mini" type="primary" @click="updateWordDbTestFun('updateWordDbTest')">修改</el-button>
        <el-button size="mini" @click="close">关闭</el-button>
      </div>
    </el-dialog>


  </div>
</template>

<script>
  import courseApi from '@/api/wordLevelTestdbList'
  import {
    pageParamNames
  } from "@/utils/constants";
  import enTypes from '@/api/bstatus'

  export default {
    data() {
      return {
        // 分页
        tablePage: {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null
        },
        tableLoading: false,
        dataQuery: {
          word: '',
          chinese: '',
          rightAnswer: '',
          studySegment: ''
        },
        activeType: [], // 活动类型
        // 分页
        tablePage: {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null
        },
        tableData: [], //表格数据
        dialogVisible: false, // 修改弹窗是否展示
        addOrUpdate: true, // 是新增还是修改
        addWordTestDb: {}, // 新增课程
        updateWordDbTest: {}, // 修改数据
        rules: { // 表单提交规则
          addWordTestDb: [{
            required: true,
            message: '必填',
            trigger: 'blur'
          }],
          chinese: [{
            required: true,
            message: '必填',
            trigger: 'blur'
          }],
          rightAnswer: [{
            required: true,
            message: '必填',
            trigger: 'blur'
          }],
          studySegment: [{
            required: true,
            message: '必填',
            trigger: 'change'
          }],
          word: [{
            required: true,
            message: '必填',
            trigger: 'blur'
          }]
        },
        courseStageType: []

      }

    },
    created() {
      this.fetchData01();
      this.getStady();
    },
    methods: {
      // 查询+搜索课程列表
      fetchData01() {
        this.tablePage = {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null
        }
        this.fetchData();
      },
      fetchData() {
        const that = this
        that.tableLoading = true
        courseApi.wordTestDbList(that.tablePage.currentPage, that.tablePage.size, that.dataQuery).then(res => {
          that.tableData = res.data.data
          // console.log(res)
          that.tableLoading = false
          // 设置后台返回的分页参数
          pageParamNames.forEach(name => that.$set(that.tablePage, name, parseInt(res.data[name])))
        })
      },
      //添加操作
      clickAdd() {
        this.addWordTestDb = {
          'rightAnswer': '',
          'word': '',
          'chinese': '',
          'categoryDescription': '',
          'addOrUpdate': '',
          'studySegment': '',
        }
        this.radio = '0'
        this.dialogVisible = true
        this.addOrUpdate = true
        if (this.fileList.length !== 0) {
          this.$refs.clearupload.clearFiles()
        }
        this.$nextTick(() => this.$refs['addWordTestDb'].clearValidate())
      },
      // 新增单词测试题提交
      addActiveFun(ele) {
        const that = this
        that.$refs[ele].validate(valid => { // 表单验证
          if (valid) {
            const loading = this.$loading({
              lock: true,
              text: '新增单词测试题提交',
              spinner: 'el-icon-loading',
              background: 'rgba(0, 0, 0, 0.7)'
            })
            courseApi.addWordTestDb(that.addWordTestDb).then(() => {
              that.dialogVisible = false
              loading.close()
              that.$nextTick(() => that.fetchData())
              that.$message.success('新增单词测试题库成功')
            }).catch(err => {
              loading.close()
            })
          } else {
            console.log('error submit!!')
            //loading.close();
            return false
          }
        })
      },
      // 点击编辑按钮
      handleUpdate(id) {
        const that = this
        that.dialogVisible = true
        that.addOrUpdate = false
        courseApi.queryActive(id).then(res => {
          console.log(res);
          that.updateWordDbTest = res.data
        }).catch(err => {

        })
      },

      // 修改单词水平测试题
      updateWordDbTestFun(ele) {
        const that = this
        that.$refs[ele].validate(valid => { // 表单验证
          if (valid) {
            const loading = this.$loading({
              lock: true,
              text: '修改单词水平测试题信息提交',
              spinner: 'el-icon-loading',
              background: 'rgba(0, 0, 0, 0.7)'
            })
            courseApi.updateWordLevelDb(that.updateWordDbTest).then(() => {
              that.dialogVisible = false
              loading.close()
              that.$nextTick(() => that.fetchData())
              that.$message.success('修改单词水平测试题成功')
            }).catch(err => {
              // 关闭提示弹框
              loading.close()
            })
          } else {
            console.log('error submit!!')
            // loading.close();
            return false
          }
        })
      },
      //获取学段下拉框
      getStady() {
        var enType = "CourseStage";
        enTypes.getEnumerationAggregation(enType).then(res => {
          this.courseStageType = res.data;
        })
      },
      courseStatus(id, status) {
        if (status == 0) {
          status = 1;
        } else {
          status = 0
        }
        const that = this;

      },
      //删除操作
      handleDelete(id) {
        this.$confirm('确定操作吗?', '删除操作', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          courseApi.wordLevelTestDelete(id).then(res => {
            this.$nextTick(() => this.fetchData())
            this.$message.success('删除成功!')
          }).catch(err => {

          })
        }).catch(err => {

        })
      },
      // 分页
      handleSizeChange(val) {
        this.tablePage.size = val;
        this.fetchData();
      },
      handleCurrentChange(val) {
        this.tablePage.currentPage = val;
        this.fetchData();
      },
      // 关闭弹窗
      close() {
        this.dialogVisible = false
      }
    }
  }
</script>

<style>
  .lh36 {
    line-height: 36px;
    font-size: 14px;
  }

  .SearchForm {
    width: 100%;
    margin-bottom: 15px;
    background: #fff;
    border-radius: 6px;
    padding: 5px 0;
    padding-left: 5px;
    box-shadow: 1px 1px 3px rgba(0, 0, 0, .2);
  }

  .btn-add {
    padding: 5px;
  }

  .red {
    color: red;
  }

  .green {
    color: green;
  }

  .course-table {
    text-align: center;
  }

  .course-table td,
  .course-table th {
    padding: 5px 0;
    text-align: center;
  }

  .course-table button {
    padding: 2px;
  }

  .icon-stop {
    float: left;
    display: inline-block;
    width: 12px;
    height: 12px;
    background: url('../../icons/stop.png') no-repeat top center/contain;
  }

  .mt22 {
    margin-top: 22px;
  }
</style>
