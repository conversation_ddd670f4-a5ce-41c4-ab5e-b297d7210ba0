<template>
  <div class="app-container">
    <el-form :model="dataQuery" ref="queryForm" :inline="true">
      <el-form-item label="ID查询：" prop="difficulty">
        <el-input v-model="dataQuery.id" type="number"></el-input>
      </el-form-item>
      <el-form-item label="维度筛选：" prop="difficulty">
        <el-select v-model="dataQuery.categoryId" clearable>
          <el-option v-for="(item, index) in categoryList" :key="index" :label="item.name" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="时间筛选：" prop="difficulty">
        <el-date-picker v-model="timeValue" type="datetimerange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
      </el-form-item>
      <el-form-item label="年级筛选：" prop="difficulty">
        <el-select v-model="dataQuery.grade" placeholder="" clearable>
          <el-option v-for="item in gradeList" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="queryData">查询</el-button>
        <el-button type="primary" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-col :span="24" style="margin-bottom: 30px">
      <el-popover placement="bottom" trigger="click">
        <el-button
          type="warning"
          size="mini"
          v-for="(item, index) in editUrlEnum"
          :key="index"
          style="margin-bottom: 5px"
          @click="$router.push({ path: item.value, query: { courseType: 1 } })"
        >
          {{ item.name }}
        </el-button>
        <el-button slot="reference" type="primary" class="link-left">添加</el-button>
      </el-popover>
    </el-col>
    <el-table class="common-table" v-loading="tableLoading" :data="tableData" style="width: 100%; margin-bottom: 20px" row-key="id" border default-expand-all>
      <el-table-column prop="id" label="ID"></el-table-column>
      <el-table-column prop="title" label="题目" width="480px"></el-table-column>
      <el-table-column prop="withdrawnBonus" label="操作" width="280">
        <template slot-scope="scope">
          <el-button type="success" size="mini" icon="el-icon-edit-outline" @click="editQuestion(scope.row)">编辑</el-button>
          <el-button type="danger" size="mini" icon="el-icon-delete" @click="handleDelete(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="grade" label="年级">
        <template slot-scope="scope">
          {{ getGrade(scope.row.grade) || '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="categoryId" label="维度" width="180px" :formatter="categoryFormatter" />
      <el-table-column label="题型" prop="questionType" :formatter="questionTypeFormatter"></el-table-column>
      <el-table-column label="创建时间" prop="createTime" />
      <el-table-column label="当前状态" prop="isEnable">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.isEnable"
            :active-value="1"
            :inactive-value="0"
            active-color="#13ce66"
            inactive-color="#ff4949"
            @change="handleStatusChange(scope.row)"
          ></el-switch>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <el-col :span="24" style="margin-bottom: 20px">
      <el-pagination
        :current-page="tablePage.currentPage"
        :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="tablePage.totalItems"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-col>
  </div>
</template>

<script>
  import dayjs from 'dayjs';
  import courseApi from '@/api/paper/course';
  import { pageParamNames } from '@/utils/constants';
  import { mapGetters, mapState } from 'vuex';
  import subjectApi from '@/api/paper/subject';
  import categoryApi from '@/api/paper/train/category';
  import questionApi from '@/api/paper/train/question';

  export default {
    data() {
      return {
        categoryList: [],
        timeValue: [],
        dataQuery: {
          id: '',
          startTime: '',
          endTime: '',
          categoryId: '',
          grade: ''
        },
        tableLoading: false,
        // 分页
        tablePage: {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null
        },
        tableData: [],
        gradeList: [
          { label: '1-3年级', value: 1 },
          { label: '4-6年级', value: 2 },
          { label: '初高中', value: 3 }
        ]
      };
    },
    created() {
      this.getList();
      this.initCategory();
    },
    methods: {
      getGrade(grade) {
        switch (grade) {
          case 1:
            return '1-3年级';
          case 2:
            return '4-6年级';
          case 3:
            return '初高中';
          default:
            return '-';
        }
      },
      initCategory() {
        categoryApi.list().then((res) => {
          console.log(res.data.data);
          this.categoryList = res.data.data;
        });
      },
      categoryFormatter(row, column, cellValue, index) {
        for (let item of this.categoryList) {
          if (item.id === cellValue) {
            return item.name;
          }
        }
        return '无';
      },
      questionTypeFormatter(row, column, cellValue, index) {
        for (let item of this.editUrlEnum) {
          console.log(this.editUrlEnum,'*****************************************************')
          if (item.questionType === cellValue) {
            return item.name;
          }
        }
        return '无';
      },
      editQuestion(row) {
        let url = this.pageFormat(this.editUrlEnum, row.questionType);
        console.log('🚀 ~ editQuestion ~ row.questionType:', row.questionType);
        console.log('🚀 ~ editQuestion ~ this.editUrlEnum:', this.editUrlEnum);
        console.log(url, '111111111');
        this.$router.push({ path: url, query: { id: row.id } });
      },

      handleDelete(id) {
        this.$confirm('是否删除该题目吗？', '删除', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          questionApi.delete(id).then((re) => {
            if (re.success) {
              this.$message.success(re.message);
              this.getList();
            } else {
              this.$message.error(re.message);
            }
          });
        });
      },
      //分页查询
      getList() {
        this.loading = true;
        this.dataQuery.pageNum = this.tablePage.currentPage;
        this.dataQuery.pageSize = this.tablePage.size;
        if (this.timeValue && this.timeValue.length > 0) {
          this.dataQuery.startTime = this.getFormatTime(this.timeValue[0]);
          this.dataQuery.endTime = this.getFormatTime(this.timeValue[1]);
        }
        courseApi.trainAfterCourseList(this.dataQuery).then((res) => {
          this.tableData = res.data.data;
          this.loading = false;
          // 设置后台返回的分页参数
          pageParamNames.forEach((name) => this.$set(this.tablePage, name, parseInt(res.data[name] || 0)));
        });
      },

      getFormatTime(time) {
        let date = new Date(time);
        let year = date.getFullYear();
        let month = date.getMonth() + 1; // getMonth()返回值是0~11
        let day = date.getDate();
        let hour = date.getHours();
        let minute = date.getMinutes();
        let second = date.getSeconds();

        let result =
          year +
          '-' +
          (month < 10 ? '0' + month : month) +
          '-' +
          (day < 10 ? '0' + day : day) +
          ' ' +
          (hour < 10 ? '0' + hour : hour) +
          ':' +
          (minute < 10 ? '0' + minute : minute) +
          ':' +
          (second < 10 ? '0' + second : second);
        return result;
      },

      queryData() {
        this.tablePage.currentPage = 1;
        this.getList();
      },
      resetQuery() {
        this.timeValue = [];
        this.dataQuery = {
          id: '',
          startTime: '',
          endTime: '',
          categoryId: ''
        };
        this.tablePage.currentPage = 1;
        this.getList();
      },

      handleStatusChange(data) {
        questionApi.saveOrUpdate(data).then((response) => {
          this.$message.success('修改成功！');
          this.getList();
        });
      },

      // 分页
      handleSizeChange(val) {
        this.tablePage.size = val;
        this.getList();
      },
      handleCurrentChange(val) {
        this.tablePage.currentPage = val;
        this.getList();
      }
    },
    computed: {
      ...mapGetters('enumItem', ['enumFormat', 'subjectFormat', 'pageFormat']),
      ...mapState('enumItem', {
        editUrlEnum: (state) => state.train.allTrainUrlEnum
      })
    }
  };
</script>
