<template>
  <div class="app-container">
    <el-form :inline="true" class="container-card" label-width="110px" label-position="left">
      <el-row>
        <el-col :span="6">
          <el-form-item label="交易流水号：">
            <el-input id="flowCode" v-model="dataQuery.flowCode" name="id" placeholder="交易流水号" />
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="会员编号：">
            <el-input id="memberCode" v-model="dataQuery.memberCode" name="id" placeholder="会员编号" />
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="用户等级：">
            <el-select v-model="dataQuery.rank" placeholder="全部" style="width: 185px;">
              <el-option v-for="(item, index) in memberRank" :key="index" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="6">
          <el-form-item label="变动方式：">
            <el-select v-model="dataQuery.direction" placeholder="全部" style="width: 185px;">
              <el-option v-for="(item, index) in [{ value: true, label: '转入' }, { value: false, label: '转出' }]"
                :key="index" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="变动类型：">
            <el-select v-model="dataQuery.accountsType" placeholder="全部" style="width: 185px;">
              <el-option
                v-for="(item, index) in [{ value: 'RecommendMember', label: '邀请会员' }, { value: 'ApplyDrawCash', label: '会员提现' }]"
                :key="index" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="10">
          <el-form-item label="变动时间：">
            <el-date-picker style="width: 100%;" v-model="addTime" type="daterange" value-format="yyyy-MM-dd HH:mm:ss"
              align="right" unlink-panels range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="2">
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="fetchData01()">搜索</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <el-table class="period-table" v-loading="tableLoading" :data="tableData" style="width: 100%;margin-bottom: 20px;"
      row-key="id" border default-expand-all :tree-props="{ list: 'children', hasChildren: 'true' }">
      <el-table-column prop="flowCode" label="交易流水号" width="180"></el-table-column>
      <el-table-column prop="memberCode" label="会员编号" width="180"></el-table-column>
      <el-table-column prop="rank" label="会员等级" width="180"></el-table-column>
      <el-table-column prop="directionStr" label="变动方式" width="180"></el-table-column>
      <el-table-column prop="accountsType" label="变动类型"></el-table-column>
      <el-table-column prop="flowBeforeMoney" label="变动前金额（元）" width="180"></el-table-column>
      <el-table-column prop="flowMoney" label="变动金额（元）" width="180"></el-table-column>
      <el-table-column prop="flowAfterMoney" label="变动后金额（元）" width="180"></el-table-column>
      <el-table-column prop="addTime" label="变动时间" width="180"></el-table-column>
      <el-table-column prop="description" label="收益说明"></el-table-column>
    </el-table>
    <!-- 分页 -->
    <el-col :span="24" style="overflow-x: auto;" :xs="24">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>
  </div>
</template>

<script>
import {
  queryOfficialAccountLink
} from "@/api/wechatPublicAccount";
import fundApi from "@/api/memberFundStatement.js";
import {
  pageParamNames
} from "@/utils/constants";
import memberApi from '@/api/member'
export default {
  data() {
    return {
      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      tableData: [{
        name: 1,
        title: '1111'
      }],
      dataQuery: {},
      memberRank: [],//会员等级
      dialogVisible: false,
      updateMarketDate: {},
      addMarketDate: {},
      showLoginAccount: false,
      addTime: []
    };
  },
  created() {
    this.fetchData();
    this.getRank();
  },
  methods: {
    fetchData01() {
      this.tablePage = {
        currentPage: 1,
        size: this.tablePage.size,
        totalPage: null,
        totalItems: null
      }
      this.fetchData();
    },
    // 查询提现列表
    fetchData() {
      const that = this;
      that.tableLoading = true
      var a = that.addTime;
      if (a != null) {
        that.dataQuery.addStartTime = a[0];
        that.dataQuery.addEndTime = a[1];
      }
      fundApi.fundStatement(
        that.tablePage.currentPage,
        that.tablePage.size,
        that.dataQuery
      ).then(res => {
        console.log(res);
        that.tableData = res.data.data;
        that.tableLoading = false;
        // 设置后台返回的分页参数
        pageParamNames.forEach(name =>
          that.$set(that.tablePage, name, parseInt(res.data[name]))
        );
      });
    },
    getRank() {
      memberApi.memberRank(
      ).then(res => {
        this.memberRank = res.data

      });
    },
    //新增操作
    clickAdd() {
      this.dialogVisible = true;
    },
    // 打开修改登陆账号
    openLogin() {
      this.showLoginAccount = true
    },
    closeLogin() {
      this.showLoginAccount = false
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val;
      this.fetchData();
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val;
      this.fetchData();
    }
  }
};
</script>

<style>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.period-table td,
.period-table th {
  text-align: center;
}
</style>
