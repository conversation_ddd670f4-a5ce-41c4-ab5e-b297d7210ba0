/**
 * 课程相关接口
 */
import request from '@/utils/request'

export default {
  // 课程分页查询
  courseList(pageNum, pageSize, data) {
    return request({
      url: '/znyy/course/edition/list/' + pageNum + '/' + pageSize,
      method: 'GET',
      params: data
    })
  },

  // 教材版本新增
  addTextbook(data) {
    return request({
      url: '/znyy/course/edition',
      method: 'POST',
      data
    //   headers: {
    //     'Content-Type': 'multipart/form-data'
    //   }
    })
  },
  // 课程编辑
  updateTextbook(data) {
    return request({
      url: '/znyy/course/edition',
      method: 'PUT',
      data
    })
  },

}
