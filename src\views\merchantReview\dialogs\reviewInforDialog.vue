<template>
  <div>
    <el-dialog
      :title="title"
      :visible.sync="dialogVisible"
      width="500px"
      top="5%"
      :close-on-click-modal="title == '查看' ? true : false"
      @close="handleClose"
      class="review-dialog"
    >
      <div v-loading="reviewLoading">
        <div class="dialog-tabs">
          <div class="tab-header">
            <div class="tab-item" :class="{ active: activeTab === 'reviewInfo' }" @click="activeTab = 'reviewInfo'">审核信息</div>
            <div class="tab-item" :class="{ active: activeTab === 'reviewProcess' }" @click="activeTab = 'reviewProcess'">审核流程</div>
          </div>

          <div class="tab-content">
            <!-- 审核信息tab -->
            <div v-show="activeTab === 'reviewInfo'" class="review-info">
              <div class="info-section">
                <div class="info-row">
                  <div class="info-item">
                    <span class="label">{{ storeClubType == '俱乐部' ? '俱乐部' : '门店' }}名称：</span>
                    <span class="value">{{ reviewDetailedData.merchantName }}</span>
                  </div>
                </div>

                <div class="info-row">
                  <div class="info-item">
                    <span class="label">负责人姓名：</span>
                    <span class="value">{{ reviewDetailedData.realName }}</span>
                  </div>
                </div>

                <div class="info-row">
                  <div class="info-item">
                    <span class="label">个人职业：</span>
                    <span class="value">{{ reviewDetailedData.occupation }}</span>
                  </div>
                </div>

                <div class="info-row">
                  <div class="info-item">
                    <span class="label">登录账号：</span>
                    <span class="value">{{ reviewDetailedData.name }}</span>
                  </div>
                </div>

                <div class="info-row">
                  <div class="info-item">
                    <span class="label">身份证号码：</span>
                    <span class="value">{{ reviewDetailedData.idCard }}</span>
                  </div>
                </div>

                <div class="info-row">
                  <div class="info-item id-card-section">
                    <span class="label">身份证照片：</span>
                    <div class="id-card-photos">
                      <div v-for="(item,index) in reviewDetailedData.idCardPhoto" :key="index">
                        <el-image fit="cover" class="photo-placeholder" :src="item" :preview-src-list="[item]"></el-image>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="info-row">
                  <div class="info-item">
                    <span class="label">{{ storeClubType == '俱乐部' ? '所属品牌：' : '所属俱乐部：' }}</span>
                    <span class="value">{{ storeClubType == '俱乐部' ? reviewDetailedData.brandName : reviewDetailedData.operationsName }}</span>
                  </div>
                </div>

                <div class="info-row">
                  <div class="info-item">
                    <span class="label">{{ storeClubType == '俱乐部' ? '渠道合作伙伴：' : '课程推广大使：' }}</span>
                    <span class="value">{{ reviewDetailedData.refereeName }}</span>
                  </div>
                </div>

                <div class="info-row">
                  <div class="info-item">
                    <span class="label">业务开展地：</span>
                    <span class="value">{{ reviewDetailedData.businessArea }}</span>
                  </div>
                </div>

                <div class="info-row">
                  <div class="info-item">
                    <span class="label">经营地址：</span>
                    <span class="value">{{ reviewDetailedData.businessAddress }}</span>
                  </div>
                </div>

                <div class="info-row">
                  <div class="info-item">
                    <span class="label businessLocation">
                      业务开展地是
                      <br />
                      否一致：
                    </span>
                    <span class="value" style="line-height: 17px">
                      <br />
                      {{ reviewDetailedData.isBusinessArea === 1 ? '是' : '否' }}
                    </span>
                  </div>
                </div>
                <div class="info-row">
                  <div class="info-item">
                    <span class="label">补充材料：</span>
                    <div style="display: flex; flex-direction: column">
                      <span class="value">{{ reviewDetailedData.supplementType == 1 ? '居住证' : '营业执照' }}</span>
                      <el-image
                        fit="cover"
                        :src="reviewDetailedData.supplementPhoto[0]"
                        :preview-src-list="[reviewDetailedData.supplementPhoto[0]]"
                        class="photo-placeholder"
                        style="margin-top: 8px"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 审核流程tab -->
            <div v-show="activeTab === 'reviewProcess'" class="review-process">
              <div class="process-timeline">
                <template v-for="(item, index) in auditProcessData">
                  <div
                    :key="`step-${index}`"
                    class="process-step"
                    :class="{
                      completed: item.auditStatus == 2,
                      rejected: item.auditStatus == 3,
                      current: item.auditStatus == 1
                    }"
                  >
                    <div class="step-icon">
                      <i class="el-icon-check"></i>
                    </div>
                    <div class="step-content">
                      <div class="step-title">
                        <span>审批人 · {{ item.auditStatus == 1 ? '审核中' : item.auditStatus == 2 ? '已通过' : '已驳回' }}</span>
                        <span style="margin-right: 16px">{{ item.approveTime }}</span>
                      </div>
                      <div class="step-subtitle">{{ item.approveName }}</div>
                      <div v-if="item.rejectReason" class="refuse">{{ item.rejectReason }}</div>
                    </div>
                  </div>
                  <div v-if="index < auditProcessData.length - 1" :key="`line-${index}`" class="process-line"></div>
                </template>
              </div>
            </div>
          </div>
        </div>
        <div slot="footer" class="dialog-footer" v-if="activeTab === 'reviewInfo' && title == '审批'">
          <el-button @click="handleReject(0)" type="danger" class="reject-btn">驳回</el-button>
          <el-button @click="handleApprove(1)" type="primary" class="approve-btn">通过</el-button>
        </div>
      </div>
    </el-dialog>

    <!-- 驳回原因弹框 -->
    <el-dialog title="驳回原因" :visible.sync="rejectDialogVisible" width="400px" :close-on-click-modal="false" class="reject-dialog">
      <div class="reject-content">
        <el-input v-model="rejectReason" type="textarea" :rows="6" placeholder="请输入驳回原因" maxlength="100" show-word-limit class="reject-textarea"></el-input>
      </div>
      <div slot="footer" class="reject-footer">
        <el-button @click="cancelReject" class="cancel-btn">取消</el-button>
        <el-button @click="confirmReject" type="primary" class="submit-btn">提交</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import registrationCodeApi from '@/api/registrationCodeApi/registrationCodeApi';
  import checkPermission from '@/utils/permission';
  export default {
    name: 'ReviewInforDialog',
    data() {
      return {
        activeTab: 'reviewInfo',
        dialogVisible: false,
        rejectDialogVisible: false,
        rejectReason: '',
        isAdmin: false,
        reviewDetailedData: {
          idCardPhoto: [],
          supplementPhoto: []
        },
        // 审核流程
        auditProcessData: [],
        reviewLoading: false,
        title: '',
        processInstanceId: '',
        auditResultType: 0,
        // 门店/俱乐部
        storeClubType: ''
        // srcList: []
      };
    },
    mounted() {
      this.isAdmin = checkPermission(['admin']);
    },
    methods: {
      open(id, title, storeClubType) {
        this.dialogVisible = true;
        this.title = title;
        this.storeClubType = storeClubType;
        this.processInstanceId = id;
        this.activeTab = 'reviewInfo';
        this.getDetail(id);
      },

      handleClose() {
        this.dialogVisible = false;
        this.processInstanceId = '';
        this.auditResultType = 0;
        this.rejectReason = '';
      },
      getDetail(id) {
        this.reviewLoading = true;
        registrationCodeApi[this.storeClubType == '门店' ? 'StoreListDataDetail' : 'ClubReviewDataDetail']({ id })
          .then((res) => {
            if (res.success) {
              this.reviewDetailedData = res.data;
              this.auditProcessData = res.data.auditDetailInfoList;
              this.reviewLoading = false;
            } else {
              this.reviewDetailedData = {
                idCardPhoto: [],
                supplementPhoto: []
              };
              this.auditProcessData = [];
            }
          })
          .catch(() => {
            this.reviewLoading = false;
          });
      },
      handleReject(type) {
        this.rejectDialogVisible = true;
        this.auditResultType = type;
      },
      cancelReject() {
        this.rejectDialogVisible = false;
        this.rejectReason = '';
      },
      // 确认驳回
      confirmReject() {
        if (!this.rejectReason.trim()) {
          this.$message.warning('请输入驳回原因');
          return;
        }
        let params = {
          id: this.processInstanceId,
          auditResult: this.auditResultType,
          rejectReason: this.rejectReason.trim()
        };
        registrationCodeApi[this.storeClubType == '门店' ? 'StoreListDataRejectOrPass' : 'ClubReviewRejectOrPass'](params).then((res) => {
          if (res.success) {
            this.$message.success(res.message);
            this.handleClose();
            this.cancelReject();
            this.refreshParentList();
          }
        });
      },
      handleApprove(type) {
        this.auditResultType = type;
        let params = {
          id: this.processInstanceId,
          auditResult: this.auditResultType
        };
        this.$confirm('请确认是否信息已核对准确且审核通过？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'success'
        }).then(() => {
          registrationCodeApi[this.storeClubType == '门店' ? 'StoreListDataRejectOrPass' : 'ClubReviewRejectOrPass'](params).then((res) => {
            if (res.success) {
              this.$message.success(res.message);
              this.handleClose();
              this.cancelReject();
              this.refreshParentList();
            }
          });
        });
      },
      refreshParentList() {
        this.$emit('refresh-list');
      }
    }
  };
</script>

<style scoped lang="less">
  .review-dialog {
    ::v-deep .el-dialog__header {
      text-align: center;
      padding: 20px 20px 0px;
      .el-dialog__title {
        font-size: 16px;
        font-weight: 500;
        color: #303133;
      }
    }
    ::v-deep .el-dialog__body {
      padding: 10px 20px 20px;
    }
  }
  .dialog-tabs {
    .tab-header {
      display: flex;
      background-color: #efefef;
      margin-bottom: 0;
      .tab-item {
        flex: 1;
        text-align: center;
        padding: 10px 0;
        cursor: pointer;
        color: #909399;
        font-size: 14px;
        // border-bottom: 2px solid transparent;
        transition: all 0.3s;
        position: relative;
        &.active {
          color: #409eff;
          font-weight: 500;
        }
        &:hover {
          color: #409eff;
        }
      }
    }
    .tab-content {
      min-height: 350px;
      padding: 20px 0 0;
    }
  }
  .review-info {
    .info-section {
      .info-row {
        margin-bottom: 16px;
        .info-item {
          display: flex;
          align-items: flex-start;
          .label {
            min-width: 120px;
            color: #000000;
            font-size: 14px;
            margin-right: 8px;
          }
          .value {
            color: #606266;
            font-size: 14px;
            flex: 1;
          }
          &.id-card-section {
            flex-direction: column;
            align-items: flex-start;
            .label {
              margin-bottom: 8px;
            }
          }
          .id-card-photos {
            display: flex;
            flex-wrap: wrap;
            column-gap: 30px;
            row-gap: 10px;
          }
          .photo-placeholder {
            width: 200px;
            height: 100px;
            border-radius: 4px;
            object-fit: cover;
          }
          .businessLocation {
            line-height: 1.2;
            white-space: normal;
          }
        }
      }
    }
  }
  .review-process {
    .process-timeline {
      padding: 0;
      position: relative;
      .process-step {
        display: flex;
        align-items: flex-start;
        margin-bottom: 20px;
        position: relative;
        .step-icon {
          width: 20px;
          height: 20px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 15px;
          flex-shrink: 0;
          position: relative;
          z-index: 2;
          i {
            color: white;
            font-size: 10px;
          }
        }
        &.completed .step-icon {
          background: transparent;
          border: 1px solid #409eff;
          i {
            color: #409eff;
          }
        }
        &.current .step-icon {
          background: #409eff;
          i {
            display: none;
          }
        }
        &.rejected .step-icon {
          background: #ff9292;
          i {
            display: none;
          }
        }
        .step-content {
          flex: 1;
          padding-top: 0;
          .step-title {
            display: flex;
            justify-content: space-between;
            align-items: start;
            align-items: center;
            font-size: 14px;
            color: #5c5c5c;
            margin-bottom: 2px;
            line-height: 1.4;
          }
          .step-subtitle {
            font-size: 12px;
            color: #909399;
            line-height: 1.4;
          }
          .refuse {
            background-color: #ff9292;
            color: #fff;
            padding: 4px 10px;
            font-size: 12px;
            border-radius: 4px;
            margin-bottom: 6px;
          }
        }
      }
      .process-line {
        position: absolute;
        left: 10px;
        top: 20px;
        width: 1px;
        height: calc(100% - 68px);
        min-height: 40px;
        background: #e4e7ed;
        z-index: 1;
      }
    }
  }
  .dialog-footer {
    text-align: center;
    padding: 10px 0 10px;
    .el-button {
      margin: 0 15px;
      padding: 10px 30px;
      font-size: 14px;
      border-radius: 4px;
      &.reject-btn {
        background: #f56c6c;
        border-color: #f56c6c;
        color: white;
      }
      &.approve-btn {
        background: #409eff;
        border-color: #409eff;
        color: white;
      }
    }
  }
  // 驳回原因弹框样式
  .reject-dialog {
    ::v-deep .el-dialog__header {
      text-align: center;
      padding: 20px 20px 10px;
      .el-dialog__title {
        font-size: 16px;
        font-weight: 500;
        color: #303133;
      }
    }
    ::v-deep .el-dialog__body {
      padding: 10px 20px 20px;
    }
  }
  .reject-content {
    .reject-textarea {
      ::v-deep .el-textarea__inner {
        border: 1px solid #dcdfe6;
        border-radius: 4px;
        padding: 12px;
        font-size: 14px;
        line-height: 1.5;
        resize: none;
        &:focus {
          border-color: #409eff;
        }
      }
    }
  }
  .reject-footer {
    text-align: center;
    padding: 10px 0;
    .el-button {
      margin: 0 10px;
      padding: 8px 20px;
      font-size: 14px;
      border-radius: 4px;
      &.cancel-btn {
        background: #ffffff;
        border: 1px solid #dcdfe6;
        color: #606266;
        &:hover {
          color: #409eff;
          border-color: #c6e2ff;
          background-color: #ecf5ff;
        }
      }
      &.submit-btn {
        background: #409eff;
        border-color: #409eff;
        color: white;
        &:hover {
          background: #66b1ff;
          border-color: #66b1ff;
        }
      }
    }
  }
</style>
