<template>
  <div class="app-container">
    <el-form :inline="true" class="container-card" label-width="110px" label-position="left">
      <el-row>
        <el-col :span="8" :xs="24">
          <el-form-item label="交易流水号：">
            <el-input v-model="dataQuery.flowCode" name="id" placeholder="请输入交易流水号：" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="商户编号：">
            <el-input id="merchantCode" v-model="dataQuery.merchantCode" name="id" placeholder="请输入商户编号：" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="商户名称：">
            <el-input id="merchantName" v-model="dataQuery.merchantName" name="id" placeholder="请输入商户名称:" clearable />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="8" :xs="24">
          <el-form-item label="商户类型：">
            <el-select v-model="dataQuery.RoleTagName" placeholder="全部" style="width: 185px;" clearable>
              <el-option v-for="item in [{ value: 'School', label: '门店' }, { value: 'Student', label: '学员' }]"
                :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="课程类型:">
            <el-select v-model="dataQuery.productName" placeholder="请选择">
              <el-option v-for="(item, index) in bigClassType" :key="index" :label="item.productName"
                :value="item.productName" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="课程包名称：">
            <el-input id="skuName" v-model="dataQuery.skuName" name="id" placeholder="请输入课程包名称:" clearable />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="16" :xs="24">
          <el-form-item>
            <el-form-item label="变动时间：">
              <el-date-picker style="width: 100%;" v-model="RegTime" type="daterange" value-format="yyyy-MM-dd HH:mm:ss"
                align="right" unlink-panels range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                clearable>
              </el-date-picker>
            </el-form-item>
          </el-form-item>
        </el-col>

        <el-col :span="8" :xs="24">
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="fetchData01()">搜索</el-button>
          </el-form-item>
        </el-col>
      </el-row>
      <!-- <el-form-item label="学员编号">
        <el-input id="studentCode" v-model="dataQuery.studentCode" name="id" placeholder="请输入学员编号：" clearable/>
      </el-form-item> -->
    </el-form>
    <el-form :inline="true" style="margin-bottom: 20px;">
      <el-button type="warning" icon="el-icon-document-copy" @click="exportFlow()" v-loading="exportLoading">导出
      </el-button>
    </el-form>

    <el-table class="period-table" v-loading="tableLoading" :data="tableData" style="width: 100%;margin-bottom: 20px;"
      row-key="id" border default-expand-all :tree-props="{ list: 'children', hasChildren: 'true' }">
      <el-table-column prop="flowCode" label="交易流水号" width="200" :show-overflow-tooltip="true"></el-table-column>
      <el-table-column prop="merchantCode" label="商户编号" width="120"></el-table-column>
      <el-table-column prop="merchantName" label="商户名称" width="200"></el-table-column>
      <el-table-column prop="roleTagName" label="商户类型" width="120"></el-table-column>
      <el-table-column prop="skuName" label="课程包名称" width="120">
      </el-table-column>
      <el-table-column prop="productName" label="课程类型" width="120"></el-table-column>
      <el-table-column prop="flowBeforeCourse" label="变动前学时（节）" width="150"></el-table-column>
      <el-table-column prop="flowCourse" label="变动学时（节）" width="150"></el-table-column>
      <el-table-column prop="flowAfterCourse" label="变动后学时（节）" width="150"></el-table-column>
      <el-table-column prop="addTime" label="变动时间" width="200" :show-overflow-tooltip="true"></el-table-column>
      <el-table-column prop="description" label="变动描述" width="400" :show-overflow-tooltip="true"></el-table-column>
    </el-table>
    <!-- 分页 -->
    <el-col :span="24" style="overflow-x: auto;" :xs="24">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>
    <!--    <el-col :span="24" class="mt20">-->
    <!--      本次总计变动前学时：{{classHoursBeforeTheChange}}节，变动学时：{{changeOfClassHours}}节，变动后学时：{{classHoursAfterTheChange}}节-->
    <!--    </el-col>-->
  </div>
</template>

<script>
import merchantAccountFlowApi from "@/api/merchantAccountFlow";
import enTypes from '@/api/bstatus'
import { pageParamNames } from "@/utils/constants";
import studentApi from "@/api/areasStudentCourseList";
import checkPermission from "@/utils/permission";

export default {
  name: 'schoolFlowPackageList',
  data() {
    return {
      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      tableData: [{
        name: 1,
        title: '1111'
      }],
      RegTime: [],
      classHoursBeforeTheChange: 0,//变动前学时
      changeOfClassHours: 0,//变动学时
      classHoursAfterTheChange: 0,//变动后学时
      dataQuery: {},
      dialogVisible: false,
      updateMarketDate: {},
      addMarketDate: {},
      showLoginAccount: false,
      exportLoading: false,//导出加载
      financeAccountsType: [],
      bigClassType: [], //课程类型
      courseStageType: [], //课程包名称
    };
  },
  created() {
    this.fetchData();
    //获取变动类型
    this.getFinanceAccountsType();
    this.getBigClassType();
  },
  methods: {
    checkPermission,
    fetchData01() {
      this.tablePage = {
        currentPage: 1,
        size: this.tablePage.size,
        totalPage: null,
        totalItems: null
      }
      this.fetchData();
    },
    // 查询提现列表
    fetchData() {
      const that = this;
      that.tableLoading = true
      var a = that.RegTime;
      if (a != null) {
        that.dataQuery.startDate = a[0];
        console.log(that.dataQuery.startDate = a[0]);
        that.dataQuery.endDate = a[1];
      } else {
        that.dataQuery.startDate = '';
        that.dataQuery.endDate = '';
      }
      that.dataQuery.direction = 0;
      that.dataQuery.courseAccountsType = 'CourseCut';
      that.dataQuery.packageType = '1';//1;课程包
      //列表
      merchantAccountFlowApi.schoolCourseList(that.tablePage.currentPage, that.tablePage.size, that.dataQuery).then((res) => {
        that.tableData = res.data.data;
        that.tableLoading = false;
        // 设置后台返回的分页参数
        pageParamNames.forEach((name) =>
          that.$set(that.tablePage, name, parseInt(res.data[name]))
        );
      });
    },
    //导出
    exportFlow() {
      const that = this;
      that.exportLoading = true;
      merchantAccountFlowApi.simplePackageExecl(that.dataQuery).then(res => {
        const url = window.URL.createObjectURL(res);
        const link = document.createElement("a");
        link.style.display = "none";
        link.href = url;// 获取服务器端的文件名
        link.setAttribute("download", "课程流水表.xls");
        document.body.appendChild(link);
        link.click();
        that.exportLoading = false;
      })
    },
    //新增操作
    clickAdd() {
      this.dialogVisible = true;
    },
    // 打开修改登陆账号
    openLogin() {
      this.showLoginAccount = true
    },
    closeLogin() {
      this.showLoginAccount = false
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val;
      this.fetchData();
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val;
      this.fetchData();
    },
    //获取变动类型
    getFinanceAccountsType() {
      var enType = "CourseAccountsType";
      enTypes.getEnumerationAggregation(enType).then(res => {
        this.financeAccountsType = res.data;
      })
    },
    //获取课程类型
    getBigClassType() {
      studentApi.getProductVo('1').then(res => {
        this.bigClassType = res.data;
      }
      )
    },
    //根据课程类型获取课程包
    // getCourseProductSpecVo(productId) {
    //   studentApi.getCourseProductSpecVo(productId).then(res => {
    //     this.courseStageType = res.data;
    //   })
    // },
  }
};
</script>

<style>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.period-table td,
.period-table th {
  text-align: center;
}

.mt20 {
  margin-top: 20px;
}
</style>
