<!--采购管理-发货管理-->
<template>
  <div class="app-container">
    <el-form :inline="true" class="container-card" label-width="96px" label-position="left" ref="dataQuery" :model="dataQuery">
      <el-row type="flex" style="flex-wrap: wrap">
        <el-col :span="6" :xs="24">
          <el-form-item label="采购订单编号:" prop="orderNo" label-width="110px">
            <el-input v-model="dataQuery.orderNo" clearable placeholder="请输入" size="small" style="width: 11vw"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="4" :xs="24">
          <el-form-item label="状态:" prop="status" label-width="50px">
            <el-select v-model="dataQuery.status" filterable value-key="value" placeholder="请选择" clearable>
              <el-option
                v-for="(item, index) in [
                  { value: 1, label: '待支付' },
                  { value: 2, label: '待发货' },
                  { value: 3, label: '待收货' },
                  { value: 4, label: '已收货' }
                ]"
                :key="index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="创建时间：" prop="regTime">
            <el-date-picker
              style="width: 100%"
              v-model="regTime"
              type="datetimerange"
              value-format="yyyy-MM-dd HH:mm:ss"
              align="right"
              unlink-panels
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            ></el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="6" :xs="24">
          <el-form-item label="申请人:" prop="application" label-width="110px">
            <el-input v-model="dataQuery.application" clearable placeholder="请输入" size="small" style="width: 11vw"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6" :xs="24">
          <el-form-item :label="isAdmin ? '品牌名称' : '俱乐部名称'" prop="merchantName" label-width="110px">
            <el-input v-model="dataQuery.merchantName" clearable placeholder="请输入" size="small" style="width: 11vw"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6" :xs="24" v-if="hasNoPermissionField('是否改价')">
          <el-form-item label="是否改价:" prop="changedPrice" label-width="96px">
            <el-select v-model="dataQuery.changedPrice" filterable value-key="value" placeholder="请选择" clearable>
              <el-option
                v-for="(item, index) in [
                  { value: 1, label: '是' },
                  { value: 0, label: '否' }
                ]"
                :key="index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6" :xs="24" v-if="hasNoPermissionField('采购类型')">
          <el-form-item label="采购类型:" prop="purchaseWay" label-width="96px">
            <el-select v-model="dataQuery.purchaseWay" filterable value-key="value" placeholder="请选择" clearable>
              <el-option
                v-for="(item, index) in [
                  { value: 1, label: '申请' },
                  { value: 2, label: '分配' }
                ]"
                :key="index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="4" :xs="20" style="margin-left: auto">
          <el-form-item>
            <el-button
              type="primary"
              icon="el-icon-search"
              @click="
                () => {
                  tablePage.currentPage = 1;
                  fetchData();
                }
              "
            >
              搜索
            </el-button>
            <el-button icon="el-icon-refresh" @click="rest()">重置</el-button>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row></el-row>
      <!-- ----------------------------- -->
    </el-form>
    <el-row style="margin-bottom: 20px" v-if="isZxBrand">
      <p class="card">
        <i class="el-icon-s-tools"></i>
        &nbsp;剩余合伙人智能学习管理系统数量：
        <el-tag type="info" effect="plain">{{ systemNumber || 0 }}</el-tag>
      </p>
    </el-row>
    <!-- 表格 -->
    <el-table
      class="common-table"
      v-loading="tableLoading"
      :data="tableData"
      max-height="400px"
      style="width: 100%; margin-bottom: 20px"
      row-key="id"
      border
      default-expand-all
      :header-cell-style="{ color: '#666', height: '60px', background: '#f5f5f5' }"
      :row-style="{ height: '50px' }"
    >
      <el-table-column align="center" header-align="center" v-for="(item, index) in tableHeader" :key="index" :prop="item.prop" :label="item.label" :width="item.width">
        <!-- 采购类型 -->
        <!-- <template v-if="item.label == '采购类型'" v-slot="{ row }">
          <span>{{ row.updateNum >= 3 ? '申请' : '分配' }}</span>
        </template> -->
        <!-- 支付方式 -->
        <!-- <template v-else-if="item.label == '支付方式'" v-slot="{ row }">
          <span v-if="row.payment == 2">
            线下支付
            <el-link type="primary" @click="handlePictureCardPreview(row)">查看凭证</el-link>
          </span>
          <span v-else>在线支付</span>
        </template> -->

        <template v-if="item.label == '支付方式'" v-slot="{ row }">
          <span v-if="row.payTypeDesc == '线下支付'">
            线下支付
            <el-link type="primary" @click="handlePictureCardPreview(row)">查看凭证</el-link>
          </span>
          <span v-else-if="row.payTypeDesc == '在线支付'">在线支付</span>
        </template>
        <!-- 实付金额 -->
        <template v-else-if="item.label == '实付金额（元）'" v-slot="{ row }">
          <span v-if="row.actualAmount == '-'">-</span>
          <span v-else>{{ (row.actualAmount * 0.01).toFixed(2) }}</span>
        </template>
        <template v-else-if="item.label == '总金额（元）'" v-slot="{ row }">
          <span v-if="row.actualAmount == '-'">-</span>
          <span v-else>{{ row.purchaseTotalAmount }}</span>
        </template>
        <template v-else-if="item.label == '采购单价（元）'" v-slot="{ row }">
          <span v-if="row.purchasePrice == '0.00'">-</span>
          <span v-else>{{ row.purchasePrice }}</span>
        </template>
        <!-- 状态 -->
        <template v-else-if="item.label == '状态'" v-slot="{ row }">
          <!-- <el-tag type="warning" v-if="row.status == '1'">待支付</el-tag>
          <el-tag type="danger" v-else-if="row.status == '2'">待发货</el-tag>
          <el-tag type="primary" v-else-if="row.status == '3'">待收货</el-tag>
          <el-tag type="success" v-else-if="row.status == '4'">已收货</el-tag> -->
          <el-tag :type="row.status == '待支付' ? 'warning' : row.status == '待发货' ? 'danger' : row.status == '已收货' ? 'success' : 'primary'">
            {{ row.status }}
          </el-tag>
        </template>
        <!-- 是否改价 -->
        <!-- <template v-else-if="item.label == '是否改价'" v-slot="{ row }">
          <span>{{ row.updateNum >= 3 ? '否' : '是' }}</span>
        </template> -->
      </el-table-column>
      <el-table-column label="操作" fixed="right" :min-width="isAdmin ? 200 : 130">
        <template v-slot="{ row, $index }">
          <el-button v-if="row.status == '待发货'" icon="el-icon-position" type="primary" size="mini" @click="openDelivery(row, $index)" plain>发货</el-button>
          <el-button
            v-if="isAdmin && (row.status == '待支付' || (row.payTypeDesc == '线下支付' && row.status == '待发货')) && row.restChangePriceCount > 0"
            icon="el-icon-edit"
            type="primary"
            size="mini"
            @click="openChangePrice(row, $index)"
            plain
          >
            改价
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 查看凭证 -->
    <el-dialog :visible.sync="dialogVisible">
      <img width="100%" :src="dialogImageUrl" alt="" />
    </el-dialog>
    <!-- 修改价格 -->
    <el-dialog :visible.sync="updateDialogVisible" width="30%" title="修改价格" :before-close="restUpdateForm" :close-on-click-modal="false">
      <el-form :inline="true" label-width="110px" label-position="right" ref="updateForm" :model="updateForm" :rules="updateRules">
        <el-form-item label="原价格（元）">
          <el-input v-model="updateItem.amount" disabled></el-input>
        </el-form-item>
        <el-form-item label="新价格（元）" prop="newPrice">
          <el-input v-model="updateForm.newPrice" placeholder="请输入新价格" type="text" @input="formatDecimal"></el-input>
        </el-form-item>
        <!-- 提示 -->
        <el-form-item label="" style="text-align: left">
          <span class="red">单笔订单最多可改价3次，当前剩余{{ updateItem.restChangePriceCount }}次</span>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="restUpdateForm">取消</el-button>
        <el-button type="primary" @click="submitChangePrice">确认</el-button>
      </div>
    </el-dialog>
    <!-- 发货确认采购单 -->
    <el-dialog :visible.sync="submitDialogVisible" width="30%" title="发货确认单" :before-close="cancelSend" center>
      <el-form :inline="true" label-width="110px" label-position="left" ref="sendForm" :model="sendForm">
        <el-form-item label="采购内容">
          <span>{{ sendForm.purchaseType }}</span>
        </el-form-item>
        <el-form-item label="采购单价（元）">
          <span>{{ sendForm.purchasePrice }}</span>
        </el-form-item>
        <el-form-item label="数量" prop="num">
          <span>{{ sendForm.purchaseCount }}</span>
        </el-form-item>
        <el-form-item label="总金额（元）">
          <span>{{ sendForm.purchaseTotalAmount }}</span>
        </el-form-item>
        <el-form-item :label="isAdmin ? '品牌名称' : '俱乐部名称'" prop="num">
          <span>{{ sendForm.merchantName }}</span>
        </el-form-item>
        <el-form-item label="申请人" prop="num">
          <span>{{ sendForm.applicant }}</span>
        </el-form-item>
        <el-form-item label="支付方式" prop="payTypeDesc">
          <span label="1">{{ sendForm.payTypeDesc }}</span>
        </el-form-item>

        <el-form-item label=" " v-if="sendForm.payTypeDesc == '线下支付'">
          <div class="avatar-uploader">
            <div class="avatar-uploader-icon" @click="handlePictureCardPreview(sendForm)">
              <i class="el-icon-zoom-in"></i>
            </div>
            <img :src="sendForm.paymentDocument" class="avatar" />
          </div>
        </el-form-item>
        <el-form-item label="实付金额（元）" v-else>
          <span label="1">{{ (sendForm.actualAmount * 0.01).toFixed(2) }}</span>
        </el-form-item>
        <!-- 提示 -->
        <el-form-item label=" ">
          <span class="red">发货后将无法撤销，请核对发货确认单</span>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelSend">取消</el-button>
        <el-button type="primary" @click="submitSend">确认</el-button>
      </div>
    </el-dialog>
    <!-- 分页 -->
    <el-col :span="24" style="overflow-x: auto" :xs="24">
      <el-pagination
        :current-page="tablePage.currentPage"
        :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="tablePage.totalItems"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-col>
  </div>
</template>

<script>
  import { mapGetters } from 'vuex';
  import { ossPrClient } from '@/api/alibaba';
  import orderApi from '@/api/purchase/deliveryManagement';
  import { dxSource, pageParamNames } from '@/utils/constants';
  import checkPermission from '@/utils/permission';

  import store from '@/store';
  import dayjs from 'dayjs';

  export default {
    name: 'deliveryManagement',
    data() {
      return {
        // systemNumber: 0, // 剩余管理系统数量
        // 查询条件
        dataQuery: {
          orderNo: '', // 采购订单编号
          purchaseType: '', // 采购类型
          status: '',
          application: '', // 申请人
          merchantName: '' // 俱乐部名称/品牌名称
        },
        // 表头
        tableHeader: [
          { label: '采购订单编号', prop: 'orderNo', width: 170 },
          { label: '采购内容', prop: 'purchaseType', width: 110 },
          { label: '采购单价（元）', prop: 'purchasePrice', width: 130 },
          { label: '数量', prop: 'purchaseCount' },
          { label: '总金额（元）', prop: 'purchaseTotalAmount', width: 130 },
          { label: '支付方式', prop: 'payTypeDesc', width: 180 },
          { label: '实付金额（元）', prop: 'actualAmount', width: 130 },
          { label: '申请人', prop: 'applicant' },
          { label: '状态', prop: 'status' },
          { label: '创建时间', prop: 'createTime', width: 150 },
          { label: '完成时间', prop: 'finishTime', width: 150 }
        ],
        regTime: [],
        updateForm: {
          amount: '',
          newPrice: ''
        },
        updateRules: {
          newPrice: [{ required: true, message: '请输入新价格', trigger: 'blur' }]
        },
        updateItem: {}, // 修改价格
        submitItem: {}, // 发货确认采购单
        sendForm: {
          name: '学习管理系统',
          price: '5000',
          num: 1,
          amount: '',
          payment: '2',
          imageUrl: ''
        },
        // imageList: [{ url: 'http://dx-applet-mall.oss-cn-shanghai.aliyuncs.com/manage/1741229815000' }],
        imageList: [],
        updateDialogVisible: false, // 修改价格弹窗
        submitDialogVisible: false, // 发货确认采购单弹窗
        dialogVisible: false, // 查看凭证弹窗
        tableLoading: false, // 表格加载
        uploadLoading: false, // 上传图片loading
        dialogImageUrl: '/static/img/avator.c61856d8.png', // 查看凭证图片
        // 采购申请列表
        tableData: [],
        // 分页
        tablePage: {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null
        },
        isAdmin: null, // 是否是管理员
        isZxBrand: null // 是否是品牌
      };
    },
    computed: {
      ...mapGetters(['setpayUrl', 'systemNumber'])
    },
    activated() {
      this.fetchData();
    },
    mounted() {
      // 管理员(admin)还是品牌(zxBrand)
      this.isAdmin = checkPermission(['admin']);
      this.isZxBrand = checkPermission(['zxBrand']);
      if (this.isAdmin) {
        this.tableHeader.splice(9, 0, { label: '是否改价', prop: 'ifChangedAmount' }, { label: '操作人', prop: 'changeAmountPeople' });
        this.tableHeader.splice(7, 0, { label: '品牌名称', prop: 'merchantName', width: 150 });
      } else if (this.isZxBrand) {
        this.tableHeader.splice(2, 0, { label: '采购类型', prop: 'purchaseWay' });
        this.tableHeader.splice(7, 0, { label: '俱乐部名称', prop: 'merchantName', width: 150 });
      }
      if (!this.isZxBrand && !this.isAdmin) {
        // this.$alert('权限不足，无法访问');
        this.$message.warning('权限不足，无法访问');
        this.tableHeader = [];
        return;
      }
      console.log(this.isAdmin, 'isAdmin');

      this.fetchData();
      ossPrClient();
      // this.submitDialogVisible = true;
    },
    methods: {
      checkPermission, // 权限判断
      // 查看表头列表是否有该字段
      hasNoPermissionField(field) {
        let has = this.tableHeader.some((i) => {
          if (i.prop == field || i.label == field) {
            return true;
          }
          return false;
        });
        return has;
      },
      // 移除掉没有权限的字段 或只保留 有权限的字段
      /**
       *移除掉没有权限的字段 或只保留 有权限的字段
       * @param array 需要移除或保留的字段
       * @param how true:保留 false:移除
       */
      removeNoPermissionField(array, how = false) {
        this.tableHeader = this.tableHeader.filter((i) => {
          for (let j = 0; j < array.length; j++) {
            if (i.prop == array[j] || i.label == array[j]) {
              return how;
            }
          }
          return !how;
        });
      },
      // 确定修改价格
      async submitChangePrice() {
        let that = this;
        if (this.updateForm.newPrice <= 0) {
          this.$message.warning('请输入大于0的价格');
        }
        this.$refs['updateForm'].validate((valid) => {
          if (!valid) {
            this.$message.warning('请输入新价格');
            return false;
          }
          orderApi
            .changePrice(this.updateForm)
            .then((res) => {
              this.restUpdateForm();
              this.fetchData();
            })
            .catch((err) => {
              console.log(err);
              this.restUpdateForm();
            });
        });
      },
      //修改价格重置
      restUpdateForm() {
        this.$refs.updateForm.resetFields();
        this.updateDialogVisible = false;
      },
      // 打开修改价格弹窗
      openChangePrice(row, index) {
        if (row.restChangePriceCount <= 0) {
          this.$message.warning('次数已消耗完，无法修改');
        }
        console.log(row, index, 666);
        this.updateForm = { orderNo: row.orderNo, newPrice: row.actualAmount == '-' ? row.purchaseTotalAmount * 1 : row.actualAmount * 0.01 };
        this.updateDialogVisible = true;
        this.updateItem = { restChangePriceCount: row.restChangePriceCount, amount: row.actualAmount == '-' ? row.purchaseTotalAmount * 1 : row.actualAmount * 0.01 };
      },
      // 确认发货
      submitSend() {
        let that = this;
        orderApi
          .send({ orderNo: that.sendForm.orderNo, purchaseType: that.sendForm.purchaseType })
          .then((res) => {
            this.submitDialogVisible = false;
            this.restSendForm();
          })
          .catch((err) => {
            console.log(err);
            this.submitDialogVisible = false;
          });
      },
      // 取消发货申请
      cancelSend() {
        let that = this;
        that.submitDialogVisible = false;
      },
      // 打开发货确认采购单
      openDelivery(row) {
        this.sendForm = JSON.parse(JSON.stringify(row));
        this.submitDialogVisible = true;
      },
      //重置
      restSendForm() {
        this.$refs.sendForm.resetFields();
        console.log(this.tablePage);

        // this.rest();
        this.fetchData();
      },
      // 查看图片
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.paymentDocument;
        this.dialogVisible = true;
      },
      // 获取数据
      fetchData() {
        const that = this;
        that.tableLoading = true;
        // 设置查询参数
        if (!this.regTime) {
          this.regTime = [];
        }
        this.dataQuery.startTime = this.regTime[0];
        this.dataQuery.endTime = this.regTime[1];

        orderApi.queryList(that.tablePage.currentPage, that.tablePage.size, that.dataQuery).then((res) => {
          this.tableData = res.data.data;
          this.tableLoading = false;
          // 设置分页
          pageParamNames.forEach((name) => this.$set(this.tablePage, name, Number(res.data[name])));
        });
        // // 品牌需要查询剩余学习系统数量
        // if (this.isZxBrand) {

        //   orderApi.queryStock().then((res) => {
        //     that.systemNumber = res.data;
        //   });
        // }
        store.dispatch('getSystem'); // 查询剩余学习系统数量
      },
      //重置
      rest() {
        this.$refs.dataQuery.resetFields();
        this.regTime = '';
        this.tablePage.currentPage = 1;
        this.fetchData();
      },
      // 分页
      handleSizeChange(val) {
        this.tablePage.size = val;
        this.tablePage.currentPage = 1;
        this.fetchData();
      },
      handleCurrentChange(val) {
        this.tablePage.currentPage = val;
        this.fetchData();
      },
      // 只能输入数字且可以两位小数点
      formatDecimal(value) {
        // 1. 移除非数字和非法字符
        let cleaned = value.replace(/[^\d.]/g, '');

        // 2. 分割整数和小数部分
        let parts = cleaned.split('.');
        if (parts.length > 1) {
          // 限制小数部分最多两位
          parts[1] = parts[1].slice(0, 2);
          cleaned = parts[0] + '.' + parts[1];
        }
        this.updateForm.newPrice = cleaned; // 更新输入框的值
      }
    }
  };
</script>

<style lang="scss" scoped>
  ::v-deep.el-dialog__wrapper {
    text-align: center;
    white-space: nowrap;
    overflow: auto;
    &:after {
      content: '';
      display: inline-block;
      vertical-align: middle;
      height: 100%;
    }
    .el-dialog {
      margin: 30px auto !important;
      display: inline-block;
      vertical-align: middle;
      text-align: center;
      white-space: normal;
    }
  }
  .container-card {
    padding: 15px 15px 20px 15px;
    margin-bottom: 15px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  }

  ::v-deep.el-link .el-link--inner {
    font-size: 12px;
  }
  .card {
    background-color: #f4f4f5;
    color: #666;
    // background-color: #f4f4f5;
    // color: #909399;
    padding: 10px 15px;
    border-radius: 5px;
  }
  .avatar-uploader {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    width: 178px;
    height: 178px;
    &:hover {
      .avatar-uploader-icon {
        display: block;
      }
    }
  }
  .avatar-uploader-icon {
    position: absolute;
    top: 0;
    left: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: none;
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
    i {
      color: #fff;
    }
  }
  .avatar {
    width: 178px;
    height: 178px;
    display: block;
  }
  :deep(.el-form-item--small.el-form-item) {
    display: flex;
    overflow: hidden;
  }
  :deep(.el-form-item--small .el-form-item__label) {
    flex-shrink: 0;
  }
  .dialog {
    :deep(.el-form-item--small .el-form-item__label) {
      position: absolute;
      flex-shrink: 0;
    }
  }
</style>
