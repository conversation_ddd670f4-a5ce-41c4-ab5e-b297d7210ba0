/**
 * 分润级别接口
 */
import request from '@/utils/request'

export default {
  // 续充分润等级
  reProfintRankList(pageNum, pageSize) {
    return request({
      url: '/znyy/profit/rank/page/re/' + pageNum + '/' + pageSize,
      method: 'GET'
    })
  },
  // 续充分润级别新增/修改
  addReProfitRank(data) {
    return request({
      url: '/znyy/profit/rank/add/re',
      method: 'POST',
      data
    })
  },
  //获取续充分润等级
  queryReProfitRank(id) {
    return request({
      url: '/znyy/profit/rank/check/re/' + id,
      method: 'GET'
    })
  },
  //删除续充托管中心级别
  deleteReProfitRank(id){
   return request({
       url:'/znyy/profit/rank/delete/re/'+id,
       method: 'DELETE'
   })
  },
  getCurrentTime() {
    var _this = this;
    let yy = new Date().getFullYear();
    let mm = new Date().getMonth() + 1;
    let dd = new Date().getDate();
    let hh = new Date().getHours();
    let mf = new Date().getMinutes() < 10 ? '0' + new Date().getMinutes() : new Date().getMinutes();
    let ss = new Date().getSeconds() < 10 ? '0' + new Date().getSeconds() : new Date().getSeconds();
    _this.gettime = yy + '-' + mm + '-' + dd + ' ' + hh + ':' + mf + ':' + ss;
    return _this.gettime
  }
}
