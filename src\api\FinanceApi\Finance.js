import request from '@/utils/request'

// 个人流水
export const getProfitOrderPage = (data) => {
    return request({
        url: '/deliver/web/finance/getProfitOrderPage',
        method: 'GET',
        params: data
    })
}

// 渠道交付中心订单列表
export const getDeliverOrderPage = (data) => {
    return request({
        url: '/deliver/web/finance/getDeliverOrderPage',
        method: 'GET',
        params: data
    })
}

// 渠道交付中心订单列表统计
export const getDeliverOrderStat = (data) => {
    return request({
        url: '/deliver/web/finance/getDeliverOrderStat',
        method: 'GET',
        params: data
    })
}