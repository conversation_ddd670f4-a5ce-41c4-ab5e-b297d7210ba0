import request from '@/utils/request'

export default {
  // 分页查询
  list(data) {
    return request({
      url: '/xi/web/pendant/list',
      method: 'GET',
      params: data
    })
  },
  detail(id){
    return request({
      url: '/xi/web/pendant',
      method: 'GET',
      params:{
        id:id
      }
    })
  },
// 段位要求
  rankList(data) {
    return request({
      url: '/xi/web/grading/list',
      method: 'GET',
      params: data
    })
  },
//移动
  saveOrUpdate(data) {
    return request({
      url: '/xi/web/pendant',
      method: 'POST',
      data
    })
  },
  //删除
  delete(data){
    return request({
      url: '/xi/web/pendant',
      method: 'DELETE',
      data
    })
  }
}
