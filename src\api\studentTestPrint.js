/**
 * 学员测验打印-单词相关接口
 */
import request from '@/utils/request'

export default {
  // 分页查询
  studentList(data) {
    return request({
      url: '/znyy/areas/student/word/print/printWords',
      method: 'GET',
      params:data
    })
  },

  // 分页查询
  studentDayPrint(data) {
    return request({
      url: '/znyy/student/print/detail/day',
      method: 'GET',
      params:data
    })
  },
    // 结业单词打印
    studentGraduation(data) {
      return request({
        url: 'znyy/student/print/detail/graduation',
        method: 'GET',
        params:data
      })
    },
        // 结业单词下载
        studentZip(data) {
          return request({
            url: 'znyy/student/print/download/file',
            method: 'GET',
            params:data
          })
        },
  // 导出
  printExport(wordPrintCode) {
    return request({
      url: '/znyy/areas/student/word/print/to/excel?wordPrintCode=' + wordPrintCode,
      method: 'GET',
      responseType: 'blob',
    })
  },
}
