<template>
  <div class="app-container">
    <!-- 搜索 -->
    <el-card  class="mb20">
      <el-row type="flex" justify="center">
        <el-col :xs="24" :lg="16">
          <el-form :ref="'item'" :rules="rulesAnswers" label-position="right" label-width="120px" :model="item">
            <el-form-item label="题目id" prop="id" v-show="false">
              <el-col :span="18">
                <el-input v-model="item.id"></el-input>
              </el-col>
            </el-form-item>
            <el-form-item label="课程编号" prop="courseCode" v-show="false">
              <el-col :span="18">
                <el-input v-model="item.courseCode"></el-input>
              </el-col>
            </el-form-item>
            <el-form-item label="题目编号" prop="topicCode" >
              <el-col :span="18">
                <el-input v-model="item.topicCode" disabled></el-input>
              </el-col>
            </el-form-item>
            <el-form-item label="题目小节" prop="topicPart">
              <el-col :span="18">
                <el-input v-model="item.topicPart"></el-input>
              </el-col>
            </el-form-item>
             <el-form-item label="题目序号" prop="serialNumber">
              <el-col :span="18">
                <el-input v-model="item.serialNumber"></el-input>
              </el-col>
            </el-form-item>
            <el-form-item label="题目标题" prop="questionText">
              <el-col :span="18">
                <el-input v-model="item.questionText"></el-input>
              </el-col>
            </el-form-item>
            <el-form-item label="答案A" prop="answerForA">
              <el-col :span="18">
                <el-input v-model="item.answerForA"></el-input>
              </el-col>
            </el-form-item>
            <el-form-item label="答案B" prop="answerForB">
              <el-col :span="18">
                <el-input v-model="item.answerForB"></el-input>
              </el-col>
            </el-form-item>
            <el-form-item label="答案C" prop="answerForC">
              <el-col :span="18">
                <el-input v-model="item.answerForC"></el-input>
              </el-col>
            </el-form-item>
            <el-form-item label="答案D" prop="answerForD">
              <el-col :span="18">
                <el-input v-model="item.answerForD"></el-input>
              </el-col>
            </el-form-item>
            <el-form-item label="题目正确答案" prop="correctAnswer">
              <el-col :span="18">
                <el-input v-model="item.correctAnswer"></el-input>
              </el-col>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
    </el-card>
    <el-col :offset="7">
      <el-button size="mini" type="primary" @click="updateReadingFun('item')">编辑</el-button>
    </el-col>
  </div>
</template>

<script>
  import courseListeningApi from "@/api/courseListening";
  export default {
    data() {
      return {
        // 分页
        addReadingData: {},
        updateReadingData: {},
        courseName: '',
        courseCode: '',
        addOrUpdate: true,
        items: [],

        items: [{
          topicCode: '',
          questionText: "",
          answerForA: "",
          answerForB: "",
          answerForC: "",
          answerForD: "",
          correctAnswer: "",
          topicPart: "",
          courseCode: ""
        }],
        rulesAnswers: {
          topicCode: [{
            required: true,
            message: "必填",
            trigger: "blur",
          }, ],
          questionText: [{
            required: true,
            message: "必填",
            trigger: "blur",
          }, ],
          answerForA: [{
            required: true,
            message: "必填",
            trigger: "blur",
          }, ],
          answerForB: [{
            required: true,
            message: "必填",
            trigger: "blur",
          }, ],
          answerForC: [{
            required: true,
            message: "必填",
            trigger: "blur",
          }, ],
          // answerForD: [

          //   {
          //     required: true,
          //     message: "必填",
          //     trigger: "blur",
          //   },
          // ]
          correctAnswer: [{
            required: true,
            message: "必填",
            trigger: "blur",
          }, ],
          topicPart: [{
            required: true,
            message: "必填",
            trigger: "blur",
          }, ],
          serialNumber: [{
            required: true,
            message: "必填",
            trigger: "blur",
          }, ],

        },
        radio: 0,
        item: {}
      }

    },
    created() {
      const that = this;
      that.tableLoading = true;
      that.courseName = window.localStorage.getItem("courseListeningUpdateName");
      that.courseCode = window.localStorage.getItem("courseListeningUpdateCode");
      that.openEdit();
    },
    methods: {
      // 查询+搜索课程列表
      //编辑
      // 打开编辑听力
      openEdit(id) {
        var id = window.localStorage.getItem("courseListeningUpdateId");

        courseListeningApi.queryListening(id).then((res) => {
          this.item = res.data;
          console.log(id);
        });
      },

      //修改
      updateReadingFun(ele) {
        const that = this;
        that.$refs[ele].validate((valid) => {
          // 表单验证
          if (valid) {
            const loading = this.$loading({
              lock: true,
              text: "修改",
              spinner: "el-icon-loading",
              background: "rgba(0, 0, 0, 0.7)",
            });
            courseListeningApi
              .updateListening(this.item)
              .then((res) => {
                loading.close();
        
                that.getList();
              })
              .catch((err) => {
                loading.close();
              });
          } else {
            console.log("error submit!!");
            return false;
          }
        });
      },
      getList() {
        this.$store.dispatch('delVisitedViews', this.$route);
        this.$router.go(-1);
        this.$router.push({
          path: "/course/courseListening",
          query: {
            courseCode: this.courseCode,
            courseName: this.courseName,
            courseContentType: "Listening"
          }
        });
      },
      // 状态改变事件
      change(radio) {
        if (radio == "1") {
          this.addReadingData.isEnable = 1;
        } else {
          this.addReadingData.isEnable = 0;
        }
      },

    }
  }
</script>

<style>
  .lh36 {
    line-height: 36px;
    font-size: 14px;
  }

  .SearchForm {
    width: 100%;
    margin-bottom: 15px;
    background: #fff;
    border-radius: 6px;
    padding: 5px 0;
    padding-left: 5px;
    box-shadow: 1px 1px 3px rgba(0, 0, 0, .2);
  }

  .btn-add {
    padding: 5px;
  }

  .red {
    color: red;
  }

  .green {
    color: green;
  }

  .course-table {
    text-align: center;
  }

  .course-table td,
  .course-table th {
    padding: 5px 0;
    text-align: center;
  }

  .course-table button {
    padding: 2px;
  }

  .icon-stop {
    float: left;
    display: inline-block;
    width: 12px;
    height: 12px;
    background: url('../../icons/stop.png') no-repeat top center/contain;
  }

  .mt22 {
    margin-top: 22px;
  }
</style>
