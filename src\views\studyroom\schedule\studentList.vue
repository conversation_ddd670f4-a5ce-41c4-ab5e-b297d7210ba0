<template>
  <div class="app-container">
    <el-form :inline="true" ref="queryForm" class="container-card" label-width="110px">
      <el-form-item label="学员名称：" prop="studentName">
        <el-input v-model="dataQuery.studentName" placeholder="请输入学员名称" clearable />
      </el-form-item>
      <el-form-item label="性别：" prop="gender">
        <el-select v-model="dataQuery.gender" placeholder="全部" clearable>
          <el-option v-for="(item, index) in [{ label: '男', value: '1' }, { label: '女', value: '0' }]" :key="index"
            :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handlePage()">搜索</el-button>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-re" @click="resetQuery()">重置</el-button>
      </el-form-item>

    </el-form>

    <el-table class="common-table" v-loading="tableLoading" :data="tableData" style="width: 100%;margin-bottom: 20px;"
      row-key="id" border default-expand-all>
      <el-table-column prop="studentCode" label="学号"></el-table-column>
      <el-table-column prop="studentName" label="学员名称"></el-table-column>
      <el-table-column prop="gender" label="性别">
        <template slot-scope="scope">
          <span v-if="scope.row.gender === '1'">男</span>
          <span v-if="scope.row.gender === '0'">女</span>
        </template>
      </el-table-column>
      <el-table-column prop="targetName" label="目标名称"></el-table-column>
      <el-table-column prop="targetDuration" label="目标学时">
        <template slot-scope="scope">
          <span v-if="Math.floor(scope.row.targetDuration / 60) > 0">{{ Math.floor(scope.row.targetDuration / 60) }}小时</span>
          <span v-if="scope.row.targetDuration % 60 >= 0">{{ scope.row.targetDuration % 60 }}分钟</span>
        </template>
      </el-table-column>
      <el-table-column prop="planNum" label="计划数量"></el-table-column>
      <el-table-column prop="allStudyTime" label="自学学时">
        <template slot-scope="scope">
          <span v-if="Math.floor(scope.row.allStudyTime / 60) > 0">{{ Math.floor(scope.row.allStudyTime / 60) }}小时</span>
          <span v-if="scope.row.allStudyTime % 60 >= 0">{{ scope.row.allStudyTime % 60 }}分钟</span>
        </template>
      </el-table-column>
      <el-table-column prop="allRestTime" label="休息学时">
        <template slot-scope="scope">
          <span v-if="Math.floor(scope.row.allRestTime / 60) > 0">{{ Math.floor(scope.row.allRestTime / 60) }}小时</span>
          <span v-if="scope.row.allRestTime % 60 >= 0">{{ scope.row.allRestTime % 60 }}分钟</span>
        </template>
      </el-table-column>
      <el-table-column prop="clockDays" label="打卡天数"></el-table-column>
      <!-- <el-table-column label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="mini" @click="handleView(scope.row)">查看详情</el-button>
        </template>
      </el-table-column>-->
    </el-table>
    <!-- 分页 -->
    <el-col :span="24" style="margin-bottom: 20px">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>

  </div>
</template>

<script>
import studyScheduleApi from "@/api/studyroom/studySchedule";
import { pageParamNames } from "@/utils/constants";
import ls from '@/api/sessionStorage'

export default {
  name: 'studentList',
  data() {
    return {
      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      tableData: [],
      dataQuery: {
        studentName: '',
        gender: ''
      },
    };
  },
  created() {
    this.getStudents();
  },
  methods: {
    /** 详情按钮操作 */
    handleView(row) {
      const id = row.scheduleId;
      ls.setItem("scheduleId", id);
      this.$router.push({ path: '/schedule/planList' })
    },

    getStudents() {
      this.tableLoading = true;
      this.dataQuery.pageNum = this.tablePage.currentPage;
      this.dataQuery.pageSize = this.tablePage.size;
      studyScheduleApi.getStudents(this.dataQuery).then(res => {
        this.tableData = res.data.data;
        this.tableLoading = false;
        // 设置后台返回的分页参数
        pageParamNames.forEach(name => this.$set(this.tablePage, name, parseInt(res.data[name])))
      })
    },
    handlePage() {
      this.tablePage = {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
        this.getStudents();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dataQuery = {
        studentName: '',
        gender: '',
      };
      this.getStudents();
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val;
      this.getStudents();
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val;
      this.getStudents();
    },
  }
};
</script>

<style>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.period-table td,
.period-table th {
  text-align: center;
}

@media (max-width: 767px) {
  .el-message-box {
    width: 80% !important;
  }
}
</style>
