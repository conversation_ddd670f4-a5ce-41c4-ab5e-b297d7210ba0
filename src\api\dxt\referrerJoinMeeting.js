/**
 * 参会人员列表
 */
import request from '@/utils/request'

export default {
  // 新增
  addPresenterData(data) {
    return request({
      url: '/dxt/presenter/add',
      method: 'POST',
      data
    })
  },
  // 修改回显
  updatePresenterData(id) {
    return request({
      url: '/dxt/presenter/detail/' + id,
      method: 'GET'
    })
  },
  // 删除主讲老师
  deletePresenter(id) {
    return request({
      url: '/dxt/presenter/delete/' + id,
      method: 'DELETE'
    })
  },
  // 编辑
  updatePresenterDataList(data) {
    return request({
      url: '/dxt/presenter/update',
      method: 'PUT',
      data
    })
  },
  // 获取主讲老师
  presenterType() {
    return request({
      url: '/dxt/presenter/get/presenter',
      method: 'GET'
    })
  },
  // 分页查询
  referrerJoinMeetingList(pageNum, pageSize, data) {
    return request({
      url: '/dxt/web/referre/join/page/list/' + pageNum + '/' + pageSize,
      method: 'GET',
      params: data
    })
  },


}

