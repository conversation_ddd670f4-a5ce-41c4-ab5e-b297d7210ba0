<template>
  <div>
    <el-upload
      class="avatar-uploader"
      action
      v-loading="loading"
      :show-file-list="false"
      :http-request="uploadHttp"
      :on-success="handleSuccess"
      :before-upload="handleBeforeUpload"
    >
      <img v-if="fileList" :src="fileList" class="avatar"/>
      <i v-else class="el-icon-plus avatar-uploader-icon"></i>
    </el-upload>
  </div>
</template>

<script>
import { ossPrClient } from '@/api/alibaba'

export default {
  name: 'OneImageUpload',
  props: {
    // 展示的图片列表
    fileList: {
      type: String,
      default() {
        return ''
      }
    },
    dialogVisible: false
  },
  data() {
    return {
      url: '',
      loading: false
    }
  },
  created() {
    ossPrClient()
  },
  methods: {
    uploadHttp({ file }) {
      this.loading = true
      let suf = file.name.substring(file.name.lastIndexOf('.'))
      const fileName = 'paper/' + Date.parse(new Date()) + suf
      ossPrClient().put(fileName, file).then(({ res, url, name }) => {
        if (res && res.status === 200) {
          console.log(`阿里云OSS上传图片成功回调1`, res, url, name)
          this.fileList=this.aliUrl + name
          this.handleSuccess(this.aliUrl + name)
          this.loading = false
        }
      })
        .catch((err) => {
          this.$message.error('上传图片失败请检查网络或者刷新页面')
          console.log(`阿里云OSS上传图片失败回调`, err)
        })
    },
    /* 上传图片开始 */
    // 图片上传之前的校验
    handleBeforeUpload(file) {
      const isJPG = file.type === 'image/jpeg'
      const isPNG = file.type === 'image/png'
      const isGIF = file.type === 'image/gif'
      if (!isJPG && !isPNG && !isGIF) {
        this.$message.error('上传的图片只能是 JPG、JPEG、PNG、GIF 格式')
        return false
      }
      return (isJPG || isPNG || isGIF)
    },
    // 图片上传失败
    handleError(err) {
      this.$message.error('图片上传失败')
      console.log(err)
    },
    // 图片上传成功
    handleSuccess(res) {
      this.$emit('handleSuccess', res || '')
    }
    /* 上传图片结束 */
  }
}
</script>
<style>
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader .el-upload:hover {
  border-color: #409EFF;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}

.avatar {
  width: 178px;
  height: 178px;
  display: block;
}
</style>
