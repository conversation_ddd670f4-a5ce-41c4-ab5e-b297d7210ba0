<template>
  <div class="app-wrapper" :class="classObj">
    <div v-if="device === 'mobile' && sidebar.opened" class="drawer-bg" @click="handleClickOutside" />
    <sidebar class="sidebar-container" />
    <div class="main-container">
      <navbar />
      <tags-view />
      <app-main />
    </div>
    <!-- 老门店合同弹窗 -->
    <purchaseContract ref="spurchaseDialogVisible"></purchaseContract>
  </div>
</template>

<script>
  import { Navbar, Sidebar, AppMain, TagsView } from './components';
  import purchaseContract from '@/components/purchaseContract/dialog.vue';

  import ResizeMixin from './mixin/ResizeHandler';
  import { mapMutations } from 'vuex';
  import '@/api/activiti/staticDict/onlineStaticDict';

  export default {
    name: 'Layout',
    components: {
      Navbar,
      Sidebar,
      AppMain,
      TagsView,
      purchaseContract
    },
    data() {
      return {
        isShow: false
      };
    },
    mixins: [ResizeMixin],
    computed: {
      sidebar() {
        return this.$store.state.app.sidebar;
      },
      device() {
        return this.$store.state.app.device;
      },
      classObj() {
        return {
          hideSidebar: !this.sidebar.opened || this.sidebar.hiddenMenu,
          hideMenu: this.sidebar.hiddenMenu,
          withoutAnimation: this.sidebar.withoutAnimation,
          mobile: this.device === 'mobile'
        };
      }
    },
    beforeDestroy() {
      // window.removeEventListener('keydown', this.disableF12);
      // window.removeEventListener('contextmenu', this.disableRightClick);
      // window.removeEventListener('keydown', this.disableShortcuts);
    },
    mounted() {
      // window.addEventListener('keydown', this.disableF12);
      // window.addEventListener('contextmenu', this.disableRightClick);
      // window.addEventListener('keydown', this.disableShortcuts);
      let resetHeight = this.resetDocumentClientHeight();
      resetHeight();
      window.onresize = () => {
        resetHeight();
      };
    },
    methods: {
      disableF12(event) {
        if (event.key === 'F12' || (event.ctrlKey && event.shiftKey && event.key === 'I')) {
          event.preventDefault();
          alert('开发者工具已禁用');
        }
      },
      disableRightClick(event) {
        event.preventDefault();
      },
      disableShortcuts(event) {
        if (event.ctrlKey && event.shiftKey && event.key === 'I') {
          event.preventDefault();
          // alert('开发者工具已禁用');
        }
        if (event.ctrlKey && event.key === 'U') {
          event.preventDefault();
          // alert('查看页面源代码已禁用');
        }
      },
      close(done) {},
      handleClickOutside() {
        this.$store.dispatch('closeSideBar', { withoutAnimation: false });
      },
      resetDocumentClientHeight() {
        let timerID;
        let _this = this;
        return function () {
          clearTimeout(timerID);
          timerID = setTimeout(() => {
            var h = document.documentElement['clientHeight'];
            var w = document.documentElement['clientWidth'];
            _this.$store.commit('setClientHeight', h);
            _this.$store.commit('setClientWidth', w);
          }, 50);
        };
      }
    }
  };
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
  @import 'src/styles/mixin.scss';
  .app-wrapper {
    @include clearfix;
    position: relative;
    height: 100%;
    width: 100%;
  }
  .drawer-bg {
    background: #000;
    opacity: 0.3;
    width: 100%;
    top: 0;
    height: 100%;
    position: absolute;
    z-index: 999;
  }
</style>
