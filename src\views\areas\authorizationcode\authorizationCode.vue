<template>
  <div class="app-container">

    <div class="SearchForm">

      <el-button type="primary" @click="dialogFormVisible = true">绑定授权码</el-button>

      <el-dialog title="授权码绑定" :close-on-press-escape="false" :show-close="false" :close-on-click-modal="false"
                 :visible.sync="dialogFormVisible">
        <el-form :model="form">
          <el-form-item label="SN号:">
            <el-input v-model="form.sn" placeholder="请输入SN号"></el-input>
          </el-form-item>
          <el-form-item label="IMEI号:">
            <el-input v-model="form.imei" placeholder="请输入IMEI号"></el-input>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="cancelBind()">取消绑定</el-button>
          <el-button type="primary" :plain="true" @click="bind()">开始绑定</el-button>
        </div>
      </el-dialog>

      <!-- 表格 -->
      <el-table class="common-table" :data="tableData" stripe border :default-sort="{prop: 'date', order: 'descending'}"
                v-loading="tableLoading"
      >
        <el-table-column prop="authorizationCode" label="SN码"></el-table-column>
        <el-table-column prop="imei" label="IMEI码"></el-table-column>
        <el-table-column prop="authType" label="授权码类型">
          <template slot-scope="scope">
            <span v-if="scope.row.authType==='PAD'">平板pad</span>
            <span v-if="scope.row.authType==='LAMP'">智能台灯</span>
          </template>
        </el-table-column>
        <el-table-column prop="addTime" label="门店绑定时间" show-overflow-tooltip></el-table-column>
        <el-table-column prop="isEnable" label="绑定设备">
          <template slot-scope="scope">
            <span v-if="scope.row.isEnable===0">未绑定</span>
            <span v-if="scope.row.isEnable===1">已绑定</span>
          </template>
        </el-table-column>
        <el-table-column prop="equipmentNumber" label="绑定设备码"></el-table-column>
        <el-table-column prop="studentCode" label="学员编码"></el-table-column>
        <el-table-column prop="studentAddTime" label="学员绑定时间"></el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-row>
        <el-col :span="20">
          <el-pagination :current-page="tablePage.pageNum" :page-sizes="[10, 20, 30, 40, 50]"
                         layout="total, sizes, prev, pager, next, jumper"
                         :total="tablePage.totalItems" @size-change="handleSizeChange"
                         @current-change="handleCurrentChange"/>
        </el-col>
      </el-row>

    </div>
  </div>
</template>

<script>
import authorizationCodeApi from '@/api/authorizationCode'
import {pageParamNames} from "@/utils/constants";

export default {
  data() {
    return {
      dialogFormVisible: false,
      form: {
        sn: '',
        imei: ''
      },
      // 分页
      tablePage: {
        pageNum: 1,
        pageSize: 10,
        totalPage: null,
        totalItems: null
      },
      regTime: '',
      tableLoading: false,
      dataQuery: {
        title: '',
        wordUpperLimit: '',
        wordLowerLimit: ''
      },
      tableData: [], //表格数据
      dialogVisible: false, // 修改弹窗是否展示
      dialogVisibleBinding: false,
    }

  },
  created() {
    this.fetchData()
  },
  methods: {
    cancelBind() {
      this.dialogFormVisible = false;
      this.form.imei = '';
      this.form.sn = '';
    },
    bind() {
      const loading = this.$loading({
          lock: true,
          text: '绑定中...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
      })
      authorizationCodeApi.bindingAuthorizationCode(this.form).then((res) => {
        this.dialogFormVisible = false
        this.form = {
          sn: '',
          imei: ''
        }
        loading.close();
        this.fetchData();
      }).catch((err) =>{
        loading.close();
      })
    },
    // 查询+搜索
    fetchData() {
      this.tableLoading = true
      authorizationCodeApi.authorizationByCode(this.tablePage.pageNum, this.tablePage.pageSize, this.dataQuery).then(res => {
        this.tableData = res.data.data
        console.log(res)
        this.tableLoading = false
        // 设置后台返回的分页参数
        // this.tablePage.totalItems = res.data.totalItems
        pageParamNames.forEach(name => this.$set(this.tablePage, name, parseInt(res.data[name])))
      })
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.pageSize = val
      this.fetchData()
    },
    handleCurrentChange(val) {
      this.tablePage.pageNum = val
      this.fetchData()
    },
    // 关闭弹窗
    close() {
      this.dialogVisible = false
      this.dialogVisibleBinding = false
    }
  }
}
</script>

<style>
.lh36 {
  line-height: 36px;
  font-size: 14px;
}

.SearchForm {
  width: 100%;
  margin-bottom: 15px;
  background: #fff;
  border-radius: 6px;
  padding: 5px 0;
  padding-left: 5px;
  box-shadow: 1px 1px 3px rgba(0, 0, 0, .2);
}

.common-table {
  margin-top: 20px;
}

.btn-add {
  padding: 5px;
}

.red {
  color: red;
}

.green {
  color: green;
}

.course-table {
  text-align: center;
}

.course-table td,
.course-table th {
  padding: 5px 0;
  text-align: center;
}

.course-table button {
  padding: 2px;
}


.mt22 {
  margin-top: 22px;
}

@media (max-width: 767px) {
  .el-message-box {
    width: 80% !important;
  }
}

</style>
