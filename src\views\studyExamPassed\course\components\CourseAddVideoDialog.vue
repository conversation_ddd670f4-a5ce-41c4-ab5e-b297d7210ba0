<template>
  <el-dialog title="添加视频" :visible.sync="myVisible" width="70%" @close="cancel" :close-on-click-modal="closeOnClickModal">
    <CourseVideoConfigItemQuery
      queryRef="queryAddForm"
      :data-query="dataQuery"
      :grade-list="gradeList"
      :version-list="versionList"
      @queryData="queryData"
      @resetQuery="resetQuery"
    />
    <el-table
      ref="courseAddMultipleTable"
      class="common-table"
      v-loading="tableLoading"
      :data="tableData"
      @selection-change="handleSelectionChange"
      style="width: 100%; margin-bottom: 20px"
      row-key="id"
      border
      default-expand-all
    >
      <el-table-column type="selection" reserve-selection width="55" />
      <el-table-column type="index" label="序号" />
      <el-table-column prop="id" label="视频ID" />
      <el-table-column prop="videoName" label="视频名称" show-overflow-tooltip />
      <el-table-column prop="gradeLevel" label="学段" :formatter="gradeLevelName" />
      <el-table-column prop="subjectName" label="学科" />
      <el-table-column prop="versionName" label="版本" />
    </el-table>
    <!-- 分页 -->
    <div>
      <el-pagination
        :current-page="tablePage.currentPage"
        :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="tablePage.totalItems"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script>
  import CourseVideoConfigItemQuery from './CourseVideoConfigItemQuery.vue';
  import courseApi from '@/api/studyExamPassed/course';
  import { pageParamNames } from '@/utils/constants';
  import { mapGetters } from 'vuex';
  export default {
    name: 'CourseAddVideoDialog',
    components: { CourseVideoConfigItemQuery },
    props: {
      visible: {
        type: Boolean,
        default: false
      },
      addVideoInfo: {
        type: Object,
        default() {
          return { courseId: '', curriculumId: '', gradeLevel: '' };
        }
      },
      closeOnClickModal: {
        type: Boolean,
        default: true
      }
    },
    data() {
      return {
        dataQuery: {},

        needInitList: true,

        gradeList: [],
        gradeOnlyList: [],
        versionList: [],

        tableLoading: false,

        tableData: [],

        selectionTableData: [],

        tablePage: {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null
        }
      };
    },
    computed: {
      ...mapGetters('enumItem', ['enumFormat']),
      myVisible: {
        get() {
          return this.visible;
        },
        set(val) {
          this.$emit('update:visible', val);
        }
      }
    },
    watch: {
      myVisible: {
        handler(val) {
          if (val && this.addVideoInfo.courseId) {
            if (this.needInitList) {
              this.initGradeList();
              this.initVersionList();
              this.needInitList = false;
            }
            this.resetQuery();
          }
        },
        immediate: true
      },
      'addVideoInfo.courseId': {
        handler(newVal, oldVal) {
          if (newVal !== oldVal) {
            this.needInitList = true;
          }
        }
      }
    },
    methods: {
      initGradeList() {
        courseApi.getGradeAndSubjectList(this.addVideoInfo.curriculumId, this.addVideoInfo.gradeLevel).then((res) => {
          this.gradeList = res.data;
          if (this.gradeOnlyList.length == 0) {
            let gradeOnlyList = [];
            res.data.forEach((item) => {
              gradeOnlyList.push({ label: item.label, value: item.value });
            });
            this.gradeOnlyList = gradeOnlyList;
          }
        });
      },
      initVersionList() {
        courseApi.getVersionList(this.addVideoInfo.curriculumId, this.addVideoInfo.gradeLevel).then((res) => {
          this.versionList = res.data;
        });
      },
      queryData(dataQuery) {
        this.dataQuery = dataQuery;
        this.tablePage.currentPage = 1;
        this.getList();
      },
      resetQuery() {
        this.tablePage.currentPage = 1;
        this.dataQuery = {
          grade: [this.addVideoInfo.gradeLevel],
          versionId: '',
          courseId: this.addVideoInfo.courseId,
          curriculumId: this.addVideoInfo.curriculumId
        };
        this.getList();
      },

      // 分页
      handleSizeChange(val) {
        this.tablePage.size = val;
        this.getList();
      },
      handleCurrentChange(val) {
        this.tablePage.currentPage = val;
        this.getList();
      },
      getList() {
        this.tableLoading = true;
        this.$refs['courseAddMultipleTable']?.clearSelection();
        console.log('dataQuery', this.dataQuery);
        let dataQuery = {
          ...this.dataQuery,
          pageNum: this.tablePage.currentPage,
          pageSize: this.tablePage.size
        };
        if (this.dataQuery.grade.length > 0) {
          dataQuery.gradeLevel = dataQuery.grade[0];
          if (this.dataQuery.grade.length > 1) {
            dataQuery.subjectId = dataQuery.grade[1];
          }
        }
        delete dataQuery.grade;

        courseApi.courseAddVideoList(dataQuery).then((res) => {
          this.tableData = res.data.data;
          this.tableLoading = false;

          // 设置后台返回的分页参数
          pageParamNames.forEach((name) => this.$set(this.tablePage, name, parseInt(res.data[name])));
        });
      },
      handleSelectionChange(selectionData) {
        this.selectionTableData = selectionData;
      },
      submitForm() {
        console.log('submitForm', this.selectionTableData);
        if (this.selectionTableData.length === 0) {
          this.$message.error('请选择视频');
          return;
        }
        let courseVideoIdList = this.selectionTableData.map((item) => item.id);
        courseApi.courseBatchAddVideo({ courseVideoIdList, courseId: this.addVideoInfo.courseId }).then((res) => {
          this.$refs['courseAddMultipleTable'].clearSelection();
          this.$message.success('添加成功');
          this.myVisible = false;
          this.$emit('submitFormSuccess', courseVideoIdList);
        });
      },
      cancel() {
        this.myVisible = false;
      },
      gradeLevelName(row, column, cellValue, index) {
        return this.enumFormat(this.gradeOnlyList, cellValue) || '无';
      }
    }
  };
</script>

<style></style>
