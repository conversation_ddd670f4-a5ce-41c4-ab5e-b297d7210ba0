<template>
  <div class="app-container">
    <el-dialog :title="`${drawSyllableTitle}划音节规则`" :visible.sync="syllableDialogVisible" width="70%" :close-on-click-modal="false" @close="closeSyllableDialogClose">
      <el-steps :active="active" finish-status="success" align-cente>
        <el-step title="规则视频" :active="0"></el-step>
        <el-step title="划音节" :active="1"></el-step>
        <el-step title="划弱音/定长短" :active="2"></el-step>
        <el-step title="练习拼读词" :active="3"></el-step>
        <el-step title="练习拼写词" :active="4"></el-step>
        <el-step title="学后读检测词" :active="5"></el-step>
        <el-step title="学后写测词" :active="6"></el-step>
      </el-steps>
      <StepForm
        v-if="syllableDialogVisible"
        ref="stepAddForm"
        @submitNext="submitNext"
        :active="active"
        :listId="spellId"
        :readWriteobj="readWriteobj"
        :syllableDialogVisible="syllableDialogVisible"
        :formData="formData"
        :makeCourseType="courseType"
      />
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="closeSyllableDialogClose">关闭</el-button>
        <el-button size="mini" type="primary" :disabled="active <= 5 || btnStatus" @click="onSubmit">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
  import StepForm from '@/views/course/courseDictation/components/StepForm.vue';
  import courseDictationListApi from '@/api/courseDictationList';
  export default {
    components: { StepForm },
    props: {
      makeCourseType: {
        type: String,
        default: ''
      },
      drawSyllableDialogVisible: {
        type: Boolean,
        default: false
      },
      readWriteobj: {
        type: Object,
        default: null
      },
      listId: {
        type: String,
        default: ''
      },
      wordCode: {
        type: String,
        default: ''
      },
      editWordId: {
        type: String,
        default: ''
      }
    },
    data() {
      return {
        active: 0,
        editWord: '',
        readWriteActive: 0,
        btnStatus: false,
        fd: '',
        formData: {}, //详情
        id: this.$route.query.id,
        courseType: '',
        syllableDialogVisible: false,
        drawSyllableTitle: '新增',
        spellId: '',
        notEditItem: []
      };
    },
    watch: {
      makeCourseType(val) {
        if (val) {
          this.courseType = val;
        }
        // console.log(val,'type00000');
      },
      //点击添加或编辑
      drawSyllableDialogVisible(val) {
        if (val) {
          this.syllableDialogVisible = val;
        }
      },
      syllableDialogVisible(val) {
        if (val == false) {
          this.$bus.$emit('clearsyllableData');
        }
      },
      //编辑 id
      listId(val) {
        if (val) {
          this.spellId = val;
          this.drawSyllableTitle = '编辑';
          this.getView(val);
          console.log(22222222222222);
        } else {
          this.drawSyllableTitle = '新增';
        }
      },
      editWordId(val) {
        if (val) {
          console.log('editWordId', val);
        }
      }
    },
    methods: {
      //获取详情
      getView(listId) {
        console.log(this.id, listId, this.editWordId);
        courseDictationListApi.getVideoUrlById(this.id, listId).then((res) => {
          if (res.code === 20000) {
            let tempData = JSON.parse(JSON.stringify(res.data.data));
            tempData.pdWordRuleDto.forEach((item, index) => {
              item.ruleWordTypeDtoList.forEach((e) => {
                if (e.type == 5) {
                  e.type = 0;
                }
              });
            });
            this.formData = tempData;
            this.notEditItem = this.formData.pdWordRuleDto.filter((item) => item.id !== this.editWordId);
            this.formData.pdWordRuleDto = this.formData.pdWordRuleDto.filter((item) => item.id === this.editWordId);
            //选中哪个单词编辑的时候就带上哪个
          }
          console.log(this.formData, 'res0000000000', this.notEditItem);
        });
      },
      closeSyllableDialogClose() {
        this.active = 0;
        this.btnStatus = false;
        this.syllableDialogVisible = false;
        this.$refs.stepAddForm.resetForm();
        this.$emit('submitSuc');
      },
      async onSubmit() {
        this.btnStatus = true;
        // this.$refs.stepAddForm.stepForm.videoUrl ="http://dx-applet-mall.oss-cn-shanghai.aliyuncs.com/manage/1712823441000.jpeg"
        const submitApi = this.listId ? courseDictationListApi.updateRule : courseDictationListApi.pdCourseSaveRule;
        let stepForm = this.$refs.stepAddForm.stepForm;
        let readTestForm = this.$refs.stepAddForm.readTestForm;
        if (stepForm.pdWordRuleDto.length == 0) {
          this.$message.error('请划分音节');
          return;
        }
        // 处理前期去除的重复音节 加上[索引]
        stepForm.pdWordRuleDto.forEach((item) => {
          item.ruleWordTypeDtoList.forEach((e) => {
            if (e.too) {
              e.syllable = e.syllable + e.too;
              if (e.info) {
                if (e.info.includes('(')) {
                  e.info = e.syllable + '(' + e.info.split('(')[1];
                } else {
                  e.info = e.syllable;
                }
              }
            }
          });
        });
        if (this.listId) {
          // console.log(stepForm.pdWordRuleDto[0].id);
          stepForm.pdWordRuleDto[0].id = this.editWordId;
          stepForm.pdWordRuleDto = stepForm.pdWordRuleDto.concat(this.notEditItem);
          this.notEditItem = [];
        }
        if (this.wordCode) {
          stepForm.pdWordRuleDto[0].wordCode = this.wordCode;
        }
        // return
        var res = await submitApi(stepForm);
        var res2;
        if (res.code === 20000) {
          if (this.listId) {
            // 修改
            let arr = res.data.data
              .filter((e) => e.word == stepForm.pdWordRuleDto[0].word)
              .map((e) => {
                return {
                  id: e.id,
                  word: e.word,
                  isReadSelect: 0,
                  isWriteSelect: 0,
                  isSelect: 0
                };
              });
            arr = this.filterarr(arr, readTestForm);
            try {
              res2 = await courseDictationListApi.updateAllRuleWord(arr);
            } catch (error) {
              this.btnStatus = false;
              this.syllableDialogVisible = false;
              return;
            }
          } else {
            // 新增
            let arr = res.data.data.map((e) => {
              return {
                id: e.id,
                word: e.word,
                isReadSelect: 0,
                isWriteSelect: 0,
                isSelect: 0
              };
            });
            arr = this.filterarr(arr, readTestForm);
            try {
              res2 = await courseDictationListApi.updateAllRuleWord(arr);
            } catch (error) {
              this.btnStatus = false;
              this.syllableDialogVisible = false;
              return;
            }
          }
          this.btnStatus = false;
          this.$message.success('操作成功');
          this.syllableDialogVisible = false;
          this.$emit('submitSuc');
        }
      },
      extractAndRemoveCommonValues(arr1, arr2) {
        // 使用 filter 和 includes 找到并移除arr1中的相同值，同时构造新数组存放相同值
        const commonValues = arr1.filter((value) => arr2.includes(value));
        const arr1WithoutCommon = arr1.filter((value) => !commonValues.includes(value));

        // 移除arr2中的相同值（假设我们想同时处理arr2，使其也不包含相同值）
        const arr2WithoutCommon = arr2.filter((value) => !commonValues.includes(value));

        return { commonValues, arr1WithoutCommon, arr2WithoutCommon };
      },
      // 处理拼读 、拼写、学后读、学后写
      filterarr(arr, obj) {
        console.log(obj);
        let obj1 = this.extractAndRemoveCommonValues(obj.postReadcheckList, obj.postWittecheckList);

        arr.forEach((e) => {
          if (obj.readTestcheckList.includes(e.word)) {
            e.isReadSelect = 1;
          }
          if (obj.witreTestcheckList.includes(e.word)) {
            e.isWriteSelect = 1;
          }
          if (obj1.commonValues.includes(e.word)) {
            e.isSelect = 3;
          }
          if (obj1.arr1WithoutCommon.includes(e.word)) {
            e.isSelect = 1;
          }
          if (obj1.arr2WithoutCommon.includes(e.word)) {
            e.isSelect = 2;
          }
        });
        return arr;
      },
      submitNext(data) {
        console.log(data, 'data');
        this.active = data.active + 1;
      },
      next() {
        if (this.active++ > 2) this.active = 0;
      }
    }
  };
</script>
<style lang="less" scope="scope"></style>
