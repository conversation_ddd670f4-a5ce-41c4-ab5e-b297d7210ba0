<template>
  <div class="app-container">
    <el-form :inline="true" ref="queryForm" class="container-card" label-width="110px">
      <el-form-item label="登录账号：" prop="name">
        <el-input v-model="dataQuery.name" placeholder="请输入渠道管理员账号" clearable />
      </el-form-item>
      <el-form-item label="姓名：" prop="name">
        <el-input v-model="dataQuery.realName" placeholder="请输入渠道管理员名称" clearable />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handlePage()">搜索</el-button>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-re" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-col :span="24" style="margin-bottom: 30px">
      <el-button type="success" icon="el-icon-plus" size="mini" @click="handleAdd()">新增</el-button>
    </el-col>
    <el-table class="common-table" v-loading="tableLoading" :data="tableData" style="width: 100%; margin-bottom: 20px" row-key="id" border default-expand-all>
      <el-table-column prop="merchantCode" label="渠道管理员ID"></el-table-column>
      <el-table-column prop="name" label="登录账号"></el-table-column>
      <el-table-column prop="realName" label="姓名"></el-table-column>
      <el-table-column prop="regions" label="地区">
        <template slot-scope="scope">
          {{ scope.row.regions }}
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间"></el-table-column>
      <el-table-column prop="status" label="状态">
        <template slot-scope="scope">
          <span v-if="scope.row.isEnable === 1" class="green">开通</span>
          <span v-else class="red">暂停</span>
        </template>
      </el-table-column>
      <el-table-column prop="club" label="俱乐部">
        <template slot-scope="scope">
          {{ scope.row.clubNameStrings || '-' }}
        </template>
      </el-table-column>
      <el-table-column label="操作">
        <template slot-scope="scope">
          <el-button type="primary" size="mini" icon="el-icon-edit" @click="getDetail(scope.row.id)">编辑</el-button>
          <el-button type="success" size="mini" icon="el-icon-paperclip" @click="selectArea(scope.row.merchantCode)" title="指派地区和角色">指派地区</el-button>
          <el-button type="success" size="mini" icon="el-icon-paperclip" @click="selectCompony(scope.row.merchantCode)" title="指派分公司/事业部">指派分公司/事业部</el-button>
          <el-button type="success" size="mini" icon="el-icon-paperclip" @click="assignmentClub(scope.row)" title="分配俱乐部">分配俱乐部</el-button>
          <el-button type="warning" size="mini" icon="el-icon-switch-button" v-if="scope.row.isEnable === 0" @click="agentStatus(scope.row.id, scope.row.isEnable)">开通</el-button>
          <el-button type="danger" size="mini" icon="el-icon-video-pause" v-if="scope.row.isEnable === 1" @click="agentStatus(scope.row.id, scope.row.isEnable)">暂停</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!--弹出窗口：新增/编辑用户-->
    <el-dialog :title="textStudyMap[dialogStatus]" :visible.sync="dialogFormVisible" width="60%" :close-on-click-modal="false" @close="close">
      <el-form :rules="rules" ref="temp" :model="temp" label-position="left" label-width="120px">
        <el-form-item label="手机号" prop="mobile">
          <el-input v-model="temp.mobile" :disabled="dialogStatus != 'create'"></el-input>
        </el-form-item>
        <el-form-item label="真实姓名" prop="realName">
          <el-input v-model="temp.realName"></el-input>
        </el-form-item>
        <!--        <el-form-item label="密码" prop="password" v-if="dialogStatus=='create'">
          <el-input type="password" v-model="temp.password" auto-complete="new-password"></el-input>
        </el-form-item>

        <el-form-item label="确认密码" prop="password2" v-if="dialogStatus=='create'">
          <el-input type="password" v-model="temp.password2"></el-input>
        </el-form-item>-->
        <el-form-item label="身份证号：" prop="idCard">
          <el-col :xs="24" :span="18">
            <el-input v-model="temp.idCard" />
          </el-col>
        </el-form-item>
        <el-form-item label="身份证照片:" prop="idCardFilePaths">
          <el-col :span="20">
            <el-upload
              ref="clearupload"
              v-loading="uploadLoadingIdCard"
              list-type="picture-card"
              action=""
              element-loading-text="图片上传中"
              :limit="10"
              :on-exceed="justPictureNumIdCard"
              :file-list="fileList"
              :http-request="uploadIdCardDetailHttp"
              :on-preview="handlePictureCardPreview"
              :on-remove="handleRemoveDetailIdCard"
            >
              <i class="el-icon-plus" />
            </el-upload>
          </el-col>
          <el-col :xs="24" :span="2">(*支持多张)</el-col>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="close">取消</el-button>
        <el-button v-if="dialogStatus == 'create'" type="primary" :disabled="disableC" @click="createData">创建</el-button>
        <el-button v-else type="primary" @click="updateData">确定</el-button>
      </div>
    </el-dialog>
    <el-dialog :visible.sync="dialogUploadVisible" :close-on-click-modal="false">
      <img width="100%" :src="dialogImageUrl" alt />
    </el-dialog>

    <!--弹出窗口：指派地区和角色-->
    <el-dialog :title="textStudyMap[dialogStatus]" :visible.sync="dialogFormVisibleForAssign" width="40%" :close-on-click-modal="false" @close="close">
      <el-form :rules="rules" ref="temp" :model="temp" label-position="left" label-width="120px">
        <el-form-item label="地区: ">
          <el-cascader
            v-model="assign.regions"
            style="width: 100%"
            :options="addressOptions"
            :props="{ multiple: true, checkStrictly: true, expandTrigger: 'hover' }"
            clearable
          ></el-cascader>
        </el-form-item>
        <el-form-item label="角色: ">
          <el-cascader v-model="assign.roles" style="width: 100%" :options="options" :props="{ multiple: true, checkStrictly: true }" clearable></el-cascader>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisibleForAssign = false">取消</el-button>
        <el-button type="primary" @click="assignArea">确定</el-button>
      </div>
    </el-dialog>

    <!--弹出窗口：指派分公司/事业部-->
    <el-dialog
      :title="textStudyMap[dialogStatus]"
      :visible.sync="dialogFormVisibleForAssignCompony"
      width="40%"
      :close-on-click-modal="false"
      @close="dialogFormVisibleForAssignCompony = false"
    >
      <template>
        <el-input placeholder="请输入编号" style="width: 40%" v-model="searchMerchantCode" clearable></el-input>
        <el-button type="primary" mini style="margin-left: 10px" title="搜索" @click="searchChannel()">搜索</el-button>

        <el-table :data="tableDataAss" v-loading="tableLoading2" highlight-current-row ref="singleTable" height="550" border style="width: 100%">
          <el-table-column type="index" width="50"></el-table-column>
          <el-table-column prop="merchantCode" label="编号" width="180"></el-table-column>
          <el-table-column prop="merchantName" label="名称" width="180"></el-table-column>
          <el-table-column prop="roleTag" label="类型"></el-table-column>
          <el-table-column prop="channelManagerCode" label="渠道管理员ID"></el-table-column>
          <el-table-column label="操作">
            <template slot-scope="scope">
              <el-button type="primary" mini title="指派至渠道管理员" @click="assignCD(scope.row.merchantCode)">指派</el-button>
            </template>
          </el-table-column>
        </el-table>
      </template>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisibleForAssignCompony = false">取消</el-button>
      </div>
    </el-dialog>

    <!-- 弹出窗口：分配俱乐部 -->
    <el-dialog title="分配俱乐部" :visible.sync="dialogFormVisibleForClub" width="40%" :close-on-click-modal="false" @close="close">
      <el-form :rules="rules" ref="clubDialogForm" :model="clubDialogForm" label-position="left" label-width="120px">
        <el-form-item label="渠道管理员: ">
          <el-input v-model="clubDialogForm.realName" disabled></el-input>
        </el-form-item>
        <el-form-item label="分配俱乐部: ">
          <el-select v-model="clubDialogForm.clubCodeList" multiple filterable placeholder="请选择" style="width: 100%">
            <el-option v-for="item in clubOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
          <!-- <el-select v-model="clubDialogForm.clubCodeList" style="width: 100%" :options="clubOptions" :props="{ multiple: true, checkStrictly: true }" clearable></el-select> -->
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisibleForClub = false">取消</el-button>
        <el-button type="primary" @click="assignClub">确定</el-button>
      </div>
    </el-dialog>
    <!-- 分页 -->
    <el-col :span="20">
      <el-pagination
        :current-page="tablePage.currentPage"
        :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="tablePage.totalItems"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-col>
  </div>
</template>
<script>
  import { pageParamNames } from '@/utils/constants';
  import { ossPrClient } from '@/api/alibaba';
  import channelManagerApi from '@/api/channel/channelManager';
  import committeemanApi from '@/api/studyroom/committeeman';
  import channelAuthorityApi from '@/api/channel/channelAuthority';

  export default {
    name: 'studentList',
    data() {
      let validatePass2 = (rule, value, callback) => {
        if (value === '') {
          callback(new Error('请再次输入密码'));
        } else if (value !== this.temp.password) {
          callback(new Error('两次输入密码不一致!'));
        } else {
          callback();
        }
      };
      return {
        tableDataAss: [],
        options: [],
        cache: {
          roles: []
        },
        disableC: false,
        textStudyMap: {
          update: '编辑渠道管理员',
          create: '新增渠道管理员',
          assign: '指派地区和角色',
          assignCompony: '指派分公司/事业部'
        },
        fileList: [],
        dialogStatus: '',
        dialogFormVisible: false,
        uploadLoadingIdCard: false,
        dialogUploadVisible: false,
        dialogFormVisibleForAssign: false,
        dialogFormVisibleForAssignCompony: false,
        dialogFormVisibleForClub: false,
        searchMerchantCode: '',
        dialogImageUrl: '',
        channelManagerId: '',
        temp: {
          id: null, //tableData中的下标
          mobile: null,
          password: null,
          realName: null,
          address: '',
          city: '',
          area: '',
          province: '',
          idCardFilePaths: []
        },
        assign: {
          roles: [],
          regions: [],
          merchantCode: ''
        },
        addressOptions: [],
        tableLoading: false,
        tableLoading2: false,
        // 分页
        tablePage: {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null
        },
        tableData: [],
        dataQuery: {
          name: '',
          realName: ''
        },
        committeemanId: null,
        clubDialogForm: {
          clubCodeList: [],
          merchantCode: ''
        },
        clubOptions: [],
        rules: {
          mobile: [{ required: true, message: '必填', trigger: 'blur' }],
          realName: [{ required: true, message: '必填', trigger: 'blur' }],
          idCard: [{ required: true, message: '必填', trigger: 'blur' }],
          password: [{ required: true, message: '必填', trigger: 'blur' }],
          password2: [{ required: true, validator: validatePass2, trigger: 'blur' }],
          idCardFilePaths: [{ required: true, type: 'array', message: '证件照片必传', triger: 'blur' }]
        },
        channelManager: ''
      };
    },
    created() {
      ossPrClient();
      this.fetchData();
      committeemanApi.getAllArea().then((res) => {
        let temp = [];
        res.data.map((i) => {
          temp.push({
            label: i.name,
            value: i.name,
            id: i.id,
            children: i.children.map((item) => {
              return {
                label: item.name,
                value: item.name,
                id: item.id,
                children: item.children.map((leaf) => {
                  return {
                    label: leaf.name,
                    value: leaf.name,
                    id: leaf.id,
                    leaf: true
                  };
                })
              };
            })
          });
        });
        this.addressOptions = temp;
      });

      //获取渠道列表
      channelManagerApi.getChannelList().then((res) => {
        let data = res.data;
        for (let i = 0; i < data.length; i++) {
          this.options.push({
            label: data[i].name,
            value: data[i].value
          });
          let role = [];
          role.push(data[i].value);
          this.cache.roles.push(role);
        }
      });
    },
    methods: {
      // 分页
      handleSizeChange(val) {
        this.tablePage.size = val;
        this.fetchData();
      },
      handleCurrentChange(val) {
        this.tablePage.currentPage = val;
        this.fetchData();
      },
      // 重置
      resetQuery() {
        this.dataQuery = {
          name: '',
          realName: ''
        };
        this.handlePage();
      },
      handleAdd() {
        this.dialogStatus = 'create';
        this.dialogFormVisible = true;
      },
      handlePage() {
        this.tablePage = {
          currentPage: 1,
          size: this.tablePage.size,
          totalPage: null,
          totalItems: null
        };
        this.fetchData();
      },
      selectCompony(merchantCode) {
        const that = this;
        that.dialogFormVisibleForAssignCompony = true;
        that.dialogStatus = 'assignCompony';
        that.channelManager = merchantCode;
        channelManagerApi
          .getCompanyAndDivision()
          .then((res) => {
            that.tableDataAss = res.data.data;
          })
          .catch((err) => {});
      },
      // 分配俱乐部查询
      assignmentClub(row) {
        console.log(row, 9999999999111);
        this.clubDialogForm.merchantCode = row.merchantCode;
        this.clubDialogForm.realName = row.realName;
        this.dialogFormVisibleForClub = true;
        this.clubDialogForm.clubCodeList = [];
        row.clubNameList.forEach((item) => {
          this.clubDialogForm.clubCodeList.push(item.merchantCode);
        });
        channelManagerApi.getClubList(this.clubDialogForm.merchantCode).then((res) => {
          console.log(res.data.clubeList, 888888);
          this.clubOptions = res.data.clubeList.map((item) => {
            return {
              label: item.merchantName,
              value: item.merchantCode
            };
          });
        });
      },
      // 分配俱乐部保存
      assignClub() {
        let data = {};
        data.clubCodeList = this.clubDialogForm.clubCodeList;
        data.merchantCode = this.clubDialogForm.merchantCode;
        console.log(data, 19999999999);
        channelManagerApi
          .saveClubAssign(data)
          .then((res) => {
            console.log(res, 29999999999);
            this.$message.success('分配俱乐部成功');
            this.dialogFormVisibleForClub = false;
            this.fetchData();
          })
          .catch((err) => {
            console.log(err);
          });
      },
      assignCD(merchantCode) {
        this.tableLoading2 = true;
        channelManagerApi
          .assignCD(this.channelManager, merchantCode)
          .then((res) => {
            this.tableLoading2 = false;
            this.$message.success('指派成功');
            this.dialogFormVisibleForAssignCompony = false;
            this.fetchData();
          })
          .catch((err) => {});
      },
      //表格数据选中和跳转到指定位置
      searchChannel() {
        for (let i = 0; i < this.tableDataAss.length; i++) {
          if (this.tableDataAss[i].merchantCode === this.searchMerchantCode) {
            if (!this.$refs['singleTable']) return; //不存在这个表格则返回
            let elTable = this.$refs['singleTable'].$el;
            if (!elTable) return;
            const scrollParent = elTable.querySelector('.el-table__body-wrapper');
            const targetTop = elTable.querySelectorAll('.el-table__body tr')[i].getBoundingClientRect().top; //该行的位置
            const containerTop = elTable.querySelector('.el-table__body').getBoundingClientRect().top; //body的位置
            //跳转
            scrollParent.scrollTop = targetTop - containerTop; //跳转到存下编辑行的ScrollTop
            this.$refs.singleTable.setCurrentRow(this.tableDataAss[i]);
          }
        }
      },
      //查询
      fetchData() {
        const that = this;
        this.tableLoading = true;
        channelManagerApi.queryChannelManagerList(this.dataQuery, this.tablePage).then((res) => {
          console.log(res, 999);
          this.tableData = res.data.data;
          this.tableData.forEach((item) => {
            item.clubNameStrings = item.clubNameList.map((i) => i.merchantName).join('、');
          });
          //地区格式转换
          this.tableData.forEach((p) => {
            if (p.regions) {
              let reg = '';
              p.regions.forEach((i) => {
                reg += i.toString().replaceAll(',', '/') + ' , ';
              });
              //去掉最后一个,号
              p.regions = reg.substring(0, reg.length - 2);
            }
          });
          this.tableLoading = false;
          // 设置后台返回的分页参数
          pageParamNames.forEach((name) => this.$set(this.tablePage, name, parseInt(res.data[name])));
        });
      },
      createData() {
        const that = this;
        this.$refs.temp.validate((valid) => {
          if (valid) {
            this.disableC = true;
            channelManagerApi
              .addChannerManager(that.temp)
              .then((res) => {
                this.dialogFormVisible = false;
                if (res.success) {
                  that.$message.success('渠道管理员添加成功');
                  this.disableC = false;
                  this.fetchData();
                }
              })
              .catch((err) => {
                this.disableC = false;
              });
          }
        });
      },
      getDetail(id) {
        this.channelManagerId = id;
        this.dialogStatus = 'update';
        this.dialogFormVisible = true;
        let that = this;
        channelManagerApi
          .getDetail(id)
          .then((res) => {
            that.temp = res.data;
            that.temp.mobile = res.data.name;
            if (res.data.idCardPhoto && res.data.idCardPhoto.length > 0) {
              if (!that.temp.idCardFilePaths) {
                that.temp.idCardFilePaths = [];
                that.fileList = [];
              }
              for (let i = 0; i < res.data.idCardPhoto.length; i++) {
                that.fileList.push({ uid: i, url: 'https://document.dxznjy.com/' + res.data.idCardPhoto[i] });
                that.temp.idCardFilePaths.push(res.data.idCardPhoto[i]);
              }
            }
          })
          .catch((err) => {});
      },
      updateData() {
        this.temp.id = this.channelManagerId;
        channelManagerApi
          .updateChannerManager(this.temp)
          .then((res) => {
            this.dialogFormVisible = false;
            if (res.success) {
              this.$message.success('渠道管理员更新成功');
              this.fetchData();
            }
          })
          .catch((err) => {});
      },
      agentStatus(id, isEnable) {
        if (isEnable == 0) {
          isEnable = 1;
        } else {
          isEnable = 0;
        }
        const that = this;
        this.$confirm('确定操作吗?', '修改状态', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            channelManagerApi.updateStatus(id, isEnable).then((res) => {
              if (res.success) {
                that.fetchData();
                that.$message.success('操作成功');
              }
            });
          })
          .catch((err) => {});
      },
      //指派区域和角色
      selectArea(merchantCode) {
        const that = this;
        that.dialogFormVisibleForAssign = true;
        that.dialogStatus = 'assign';
        that.assign.merchantCode = merchantCode;
        channelAuthorityApi
          .queryByMerchantCode(merchantCode)
          .then((res) => {
            if (res.success) {
              this.assign.regions = res.data.regions;
              if (res.data.roles) {
                that.assign.roles = res.data.roles;
                console.log('roles', this.assign.roles);
              } else {
                //未指派角色的账号,默认选中所有角色
                that.assign.roles = this.cache.roles;
              }
            }
          })
          .catch((err) => {});
      },
      assignArea() {
        // if(!this.assign.regions){
        //   this.$message.warning("请选择地区");
        //   return;
        // }
        if (!this.assign.roles) {
          this.$message.warning('请选择角色');
          return;
        }
        let flag = false;
        for (let i = 0; i < this.assign.roles.length; i++) {
          if (this.assign.roles[i][0] === 'm:merchant') {
            flag = true;
          }
        }
        if (!flag) {
          this.assign.roles.push(['m:merchant']);
        }
        channelManagerApi
          .addChannelAuthority(this.assign)
          .then((res) => {
            const that = this;
            this.close();
            that.$message.success('权限分配成功');
            this.fetchData();
          })
          .catch((err) => {});
      },
      // 上传图片数量超限
      justPictureNumIdCard(file, fileList) {
        this.$message.warning(`当前限制选择10个文件`);
      },
      uploadIdCardDetailHttp({ file }) {
        this.uploadLoadingIdCard = true;
        const that = this;
        const fileName = 'idcard/study/committed/' + Date.parse(new Date());
        that.$nextTick(function () {
          ossPrClient()
            .put(fileName, file)
            .then(({ res, url, name }) => {
              if (res && res.status === 200) {
                console.log(`阿里云OSS上传图片成功回调1`, res, url, name);
                if (!that.temp.idCardFilePaths) {
                  that.temp.idCardFilePaths = [];
                }
                this.fileList.push({ uid: file.uid, url: url });
                this.temp.idCardFilePaths.push(name);
                that.$nextTick(() => {
                  that.uploadLoadingIdCard = false;
                });
              }
            })
            .catch((err) => {
              that.$message.error('上传图片失败请检查网络或者刷新页面');
              that.uploadLoadingIdCard = false;
              console.log(`阿里云OSS上传图片失败回调`, err);
            });
        });
      },

      // 上传图片预览
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url;
        this.dialogUploadVisible = true;
      },
      handleRemoveDetailIdCard(file, fileList) {
        var index = this.fileList.findIndex((item) => {
          if (item.uid === file.uid) {
            return true;
          }
        });
        this.fileList.splice(index, 1);
        this.temp.idCardFilePaths.splice(index, 1);
        console.log(this.fileList);
        console.log(this.temp);
      },
      close() {
        this.dialogFormVisible = false;
        this.temp = {};
        this.fileList = [];
        this.assign = {};
        this.dialogFormVisibleForAssign = false;
        this.dialogFormVisibleForClub = false;
      }
    }
  };
</script>

<style>
  .container-card {
    padding: 15px 15px 15px 15px;
    margin-bottom: 15px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  }

  .period-table td,
  .period-table th {
    text-align: center;
  }

  @media (max-width: 767px) {
    .el-message-box {
      width: 80% !important;
    }
  }
</style>
