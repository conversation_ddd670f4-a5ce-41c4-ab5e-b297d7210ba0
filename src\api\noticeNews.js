const qs = window.sessionStorage;
export
default {
    // 获取
    getItem(key) {
        try {
            return JSON.parse(qs.getItem(key));
        } catch (err) {
            return null;
        }
    },
    // 设置
    setItem(key, val) {
        qs.setItem(key, JSON.stringify(val));
    },
    // 清除所有
    clear() {
        qs.clear();
    },
    // 获取指定下标对应数据的key名
    keys(index) {
        return qs.key(index);
    },
    // 移除某一个指定key的对应数据
    removeItem(key) {
        qs.removeItem(key);
    }
}
