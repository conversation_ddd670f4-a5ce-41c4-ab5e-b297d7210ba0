<template>
  <div class="dashboard-editor-container">
    <!-- 首页信息 -->
    <div class="no-data-page">
      <img style="width: 380px; height: 380px" src="https://document.dxznjy.com/course/0c2af0adcadb47dfb4a1e46a27a6883b.png" />
      <div style="font-size: 24px">暂无数据，敬请期待</div>
    </div>
  </div>
</template>

<style lang="scss">
  .emptyGif {
    display: block;
    width: 45%;
    margin: 0 auto;
  }
  // .isLogo {
  //   height: 80vh;
  //   .logoTitle {
  //     font-size: 20px;
  //     margin-bottom: 20px;
  //   }
  // }
  .dashboard-editor-container {
    background-color: #e3e3e3;
    color: #676a6c;
    padding: 20px 20px 0px;
    .no-data-page {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background-color: #fff;
      height: calc(100vh - 120px);
    }
  }

  @media screen and (max-width: 767px) {
    .dashboard-editor-container {
      padding: 20px 20px 0;
    }

    .remark-dialog .el-dialog {
      width: 70% !important;
    }
  }
</style>
