<template>
  <!--  <el-card>-->
  <div class="app-container">
    <el-row>
      <el-col class="paike" style="font-size: 30px" :span="8" :xs="24">
        <span>排课中心</span>
      </el-col>
    </el-row>

    <el-steps
      :space="200"
      :active="activeIndex"
      finish-status="success"
      align-center="center"
      class="width:600px"
    >
      <el-step title="完善基础信息"></el-step>
      <el-step title="付款"></el-step>
      <el-step title="完善详细信息"></el-step>
      <el-step title="预排课"></el-step>
    </el-steps>

    <el-form
      class="recharge-form"
      label-width="110px"
      label-position="right"
      :ref="'dataQuery'"
      :rules="rules"
      :model="dataQuery"
    >
      <el-form-item label="家长手机号：" prop="loginName">
        <el-input
          @blur="queryInfo(loginName)"
          oninput="value=value.replace(/[^\d]/g,'')"
          maxlength="20"
          isNumber2="true"
          min="1"
          id="loginName"
          v-model="loginName"
          name="id"
        />
      </el-form-item>
      <el-form-item label="家长姓名：" prop="nickName">
        <el-input
          maxlength="20"
          isNumber2="true"
          min="1"
          id="nickName"
          v-model="dataQuery.nickName"
          name="id"
        />
      </el-form-item>
      <el-form-item label="家长性别：" prop="gender">
        <el-select
          v-model="dataQuery.gender"
          filterable
          value-key="value"
          placeholder="请选择"
          id="nickName"
          @change="check(dataQuery.gender)"
          clearable
        >
          <el-option
            v-for="(item, index) in genderList"
            :key="index"
            :label="item.value"
            :value="item.type"
          />
        </el-select>
      </el-form-item>

      <!--      <el-form-item-->
      <!--        label="选择地区："-->
      <!--        prop="site"-->
      <!--      >-->
      <!--        <el-input-->
      <!--          maxlength="20"-->
      <!--          isNumber2="true"-->
      <!--          min="1"-->
      <!--          id="site"-->
      <!--          v-model="dataQuery.site"-->
      <!--          name="site"-->
      <!--        />-->
      <!--      </el-form-item>-->
      <el-form-item label="所在地区：" prop="address">
        <el-col :xs="24" :span="18">
          <el-row :gutter="10">
            <el-col :xs="24" :span="8">
              <el-input v-model="dataQuery.province" disabled />
              <!--              <el-input v-else v-model="updateData.province" disabled />-->
            </el-col>
            <el-col :xs="24" :span="8">
              <el-input v-model="dataQuery.city" disabled />
              <!--              <el-input v-else v-model="updateData.city" disabled />-->
            </el-col>
            <el-col :xs="24" :span="8">
              <el-input v-model="dataQuery.area" disabled />
              <!--              <el-input v-else v-model="updateData.area" disabled />-->
            </el-col>
          </el-row>
        </el-col>
      </el-form-item>
      <el-form-item label="家庭地址：" prop="address">
        <el-col :xs="24" :span="18">
          <el-input v-model="dataQuery.homeAddress" />
          <!--          <el-input v-else v-model="updateData.address" />-->
        </el-col>
      </el-form-item>
      <el-form-item label="地图选点：" prop="isEnable">
        <el-col :span="24">
          <div class="amap-page-container">
            <div :style="{ width: '100%', height: '450px' }" class="map-box">
              <el-amap-search-box
                class="search-box"
                :search-option="searchOption"
                :on-search-result="onSearchResult"
              ></el-amap-search-box>
              <el-amap
                vid="amap"
                :plugin="plugin"
                :center="center"
                class="amap-demo"
                :events="events"
              >
                <el-amap-circle
                  v-for="(circle, index) in circles"
                  :key="index"
                  :center="circle.center"
                  :radius="circle.radius"
                  :fill-opacity="circle.fillOpacity"
                  :events="circle.events"
                ></el-amap-circle>
                <!-- 定位点标注 -->
                <el-amap-marker
                  vid="component-marker"
                  :position="center"
                ></el-amap-marker>
                <!-- 搜索结果标注 -->
                <el-amap-marker
                  v-for="(marker, index) in markers2"
                  :key="index"
                  :position="marker.position"
                  :events="marker.events"
                ></el-amap-marker>
                <el-amap-info-window
                  v-if="window"
                  :position="window.position"
                  :visible="window.visible"
                  :content="window.content"
                ></el-amap-info-window>
              </el-amap>
              <!-- 搜索结果右侧列表 -->
              <div class="result" v-if="result != ''">
                <el-table
                  class="search-table"
                  :data="
                    result.slice(
                      (this.pageNum - 1) * this.pageSize,
                      this.pageNum * this.pageSize
                    )
                  "
                  style="margin-bottom: 20px"
                >
                  <el-table-column prop="name,address">
                    <template slot-scope="scope">
                      <div
                        class="result-list"
                        @click="
                          markList(scope.row.lng, scope.row.lat, scope.$index)
                        "
                        :class="[currentResult == scope.$index ? 'active' : '']"
                      >
                        <label>{{ scope.$index + 1 }}</label>
                        <div class="list-right">
                          <div class="name">{{ scope.row.name }}</div>
                          <div class="address">{{ scope.row.address }}</div>
                        </div>
                      </div>
                    </template>
                  </el-table-column>
                </el-table>
                <el-pagination
                  background
                  :current-page.sync="pageNum"
                  :page-size="pageSize"
                  layout="total,prev, pager, next"
                  :total="result.length"
                  @current-change="handleCurrentChange"
                  @size-change="changeSizeHandler"
                  size="small"
                  style="text-align: right"
                >
                </el-pagination>
              </div>
            </div>
          </div>
        </el-col>
      </el-form-item>

      <el-form-item>
        <el-button
          v-if="!dataQuery.isRegister"
          type="success"
          @click="addByLoginName()"
          >一键注册</el-button
        >
      </el-form-item>
      <el-form-item label="学员姓名：" prop="studentCode">
        <el-select
          v-model="dataQuery.studentCode"
          filterable
          value-key="value"
          placeholder="请选择"
          id="studentName"
          @change="check(dataQuery.studentCode)"
          clearable
        >
          <el-option
            v-for="(item, index) in studentList"
            :key="index"
            :label="item.studentName"
            :value="item.studentCode"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="学员年级：" prop="grade">
        <el-select
          v-model="dataQuery.grade"
          filterable
          value-key="value"
          placeholder="请选择"
          id="grade"
          @change="check(dataQuery.grade)"
          clearable
        >
          <el-option
            v-for="(item, index) in gradeList"
            :key="index"
            :label="item.value"
            :value="item.grade"
          />
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="success" @click="addStudent()">新增学员</el-button>
      </el-form-item>

      <el-form-item label="选择学时：" prop="totalCourse">
        <el-input
          oninput="value=value.replace(/[^\d]/g,'')"
          maxlength="20"
          isNumber2="true"
          min="1"
          id="totalCourse"
          v-model="dataQuery.totalCourse"
          name="id"
        />
      </el-form-item>

      <!--      <div>-->
      <!--        <el-form-item>-->
      <!--          <el-button type="success" @click="saveInfo()">下一步</el-button>-->
      <!--          <el-button type="waring" @click="clearInfo('dataQuery')"-->
      <!--          >清除-->
      <!--          </el-button-->
      <!--          >-->
      <!--        </el-form-item>-->
      <!--      </div>-->

      <el-row>
        <el-col :span="24" style="margin-bottom: 30px; padding-left: 970px">
          <el-button type="warning" size="100px" @click="saveInfo()"
            >下一步</el-button
          >
        </el-col>
      </el-row>
    </el-form>

    <!-- 新增弹窗 -->
    <el-dialog
      title="新增学员"
      :visible.sync="showAddStudent"
      width="60%"
      :close-on-click-modal="false"
      @close="closeEdit"
    >
      <el-form
        :ref="'studentAdd'"
        :rules="updateSingle"
        :model="studentAdd"
        label-position="left"
        label-width="110px"
        style="width: 100%"
      >
        <el-form-item label="学员姓名：" prop="studentName">
          <el-col :xs="24" :span="12">
            <el-input
              resize="none"
              :rows="4"
              v-model="studentAdd.studentName"
            ></el-input>
          </el-col>
        </el-form-item>

        <el-form-item label="性别：" prop="gender">
          <el-select
            v-model="studentAdd.gender"
            filterable
            value-key="value"
            placeholder="请选择"
            id="nickName"
            @change="check(studentAdd.gender)"
            clearable
          >
            <el-option
              v-for="(item, index) in genderList"
              :key="index"
              :label="item.value"
              :value="item.type"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="出生日期：" prop="dateOfBirth">
          <el-col :xs="26" :span="18">
            <el-date-picker
              v-model="studentAdd.dateOfBirth"
              value-format="yyyy-MM-dd"
              type="date"
              placeholder="选择日期"
            ></el-date-picker>
          </el-col>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button
          size="mini"
          type="primary"
          @click="addStudentCommit(loginName)"
          >确定</el-button
        >
        <el-button size="mini" @click="closeEdit">关闭</el-button>
      </div>
    </el-dialog>
  </div>
  <!--  </el-card>-->
</template>

<script>
import cousysApi from "@/api/cousys";
import VueAMap from "vue-amap";
export default {
  data() {
    const self = this;
    return {
      activeIndex: 1, //进度
      showAddStudent: false,
      courseOrderId: "",
      loginName: "", //后台返回的有**，所以自定义一个
      dataQuery: {
        loginName: "",
        nickName: "",
        memberCode: "",
        gender: "",
        province: "",
        city: "",
        area: "",
        studentCode: "",
        totalCourse: "",
        grade: "",
        isRegister: true,
        hasDpk: false,
        latitude: "",
        longitude: "",
        homeAddress: "",
      },
      cousysInfoAdd: {
        courseOrderId: "",
        memberCode: "",
        nickName: "",
        gender: "",
        studentCode: "",
        totalCourse: "",
        grade: "",
        province: "",
        city: "",
        area: "",
        longitude: "",
        latitude: "",
        homeAddress: "",
      },
      studentAdd: {
        loginName: "",
        studentName: "",
        gender: "",
        grade: "",
        dateOfBirth: "",
      },
      chargeCourseShow: false, //课程显示
      disabled: false,
      backeMoneyShow: false, //金额显示
      accountType: [], //扣除方式
      merchantCodeType: [], //账户方式
      queryTrueShow: true,
      dialogVisible: false, //密码弹框
      checkSecondPwdShow: false, //密码成功
      genderList: [
        { type: "1", value: "男" },
        { type: "0", value: "女" },
      ],
      studentList: [],
      gradeList: [
        { grade: "18", value: "幼儿园" },
        { grade: "1", value: "一年级" },
        { grade: "2", value: "二年级" },
        { grade: "3", value: "三年级" },
        { grade: "4", value: "四年级" },
        { grade: "5", value: "五年级" },
        { grade: "6", value: "六年级" },
        { grade: "7", value: "七年级" },
        { grade: "8", value: "八年级" },
        { grade: "9", value: "九年级" },
        { grade: "10", value: "高一" },
        { grade: "11", value: "高二" },
        { grade: "12", value: "高三" },
      ],
      rules: {
        // loginName: [{
        //   required: true,
        //   message: '请输入手机号',
        //   trigger: 'blur'
        // },
        //   {
        //     min: 11,
        //     max: 11,
        //     message: '请输入11位手机号',
        //     trigger: 'blur'
        //   }
        // ],
        nickName: [
          {
            required: true,
            message: "请输入姓名",
            trigger: "blur",
          },
        ],
        gender: [
          {
            required: true,
            message: "请选择性别",
            trigger: "change",
          },
        ],
        totalCourse: [
          {
            required: true,
            message: "请填写学时数",
            trigger: "blur",
          },
        ],
        grade: [
          {
            required: true,
            message: "请选择年级",
            trigger: "change",
          },
        ],
        studentCode: [
          {
            required: true,
            message: "请选择学员",
            trigger: "change",
          },
        ],
        address: [
          {
            required: true,
            message: "请填写地址",
            trigger: "blur",
          },
        ],
        // phoneNum: [{
        //   required: true,
        //   message: '请填写手机号',
        //   trigger: 'blur'
        // }],
      },
      //新增学员验证
      updateSingle: {
        studentCode: [
          {
            required: true,
            message: "必填",
            trigger: "blur",
          },
        ],
        gender: [
          {
            required: true,
            message: "必填",
            trigger: "change",
          },
        ],
        dateOfBirth: [
          {
            required: true,
            message: "必填",
            trigger: "blur",
          },
        ],
      },

      // 地图搜索分页相关参数
      pageNum: 1,
      pageSize: 5,
      lng: 0,
      lat: 0, // 经纬度
      result: [],
      currentResult: -1,
      center: [117.26696, 31.87869],
      zoom: 14,
      loaded: false,
      circles: [
        {
          center: [117.26696, 31.87869],
          radius: 200,
          fillOpacity: 0.5,
          events: {
            click: () => {
              // alert("click");
            },
          },
        },
      ],
      //搜索结果标注
      markers2: [],
      windows: [],
      window: "",
      searchOption: {
        city: "全国",
        citylimit: false, //是否限制城市内搜索
      },
      mapCenter: [121.59996, 31.197646],
      events: {
        click(e) {
          const { lng, lat } = e.lnglat;
          self.lng = lng;
          self.lat = lat;
          self.getMarkAddress(e.lnglat.lng, e.lnglat.lat);
        },
        init: (o) => {
          this.$nextTick(() => {
            // 获取当前城市的城市名
            let geocoder = new AMap.Geocoder({
              radius: 1000,
              extensions: "all",
            });
            geocoder.getAddress(
              [o.Ce.center.lng, o.Ce.center.lat],
              (status, result) => {
                if (status === "complete" && result.info === "OK") {
                  if (result && result.regeocode) {
                    if (self.addOrUpdate) {
                      self.center = [o.Ce.center.lng, o.Ce.center.lat];
                    } else {
                      // self.center = [self.updateData.longitude, self.updateData.latitude]
                    }
                  }
                }
              }
            );
          });
        },
      },

      plugin: [
        {
          enableHighAccuracy: true, // 是否使用高精度定位，默认:true
          timeout: 100, // 超过10秒后停止定位，默认：无穷大
          maximumAge: 0, // 定位结果缓存0毫秒，默认：0
          convert: true, // 自动偏移坐标，偏移后的坐标为高德坐标，默认：true
          showButton: true, // 显示定位按钮，默认：true
          buttonPosition: "RB", // 定位按钮停靠位置，默认：'LB'，左下角
          showMarker: true, // 定位成功后在定位到的位置显示点标记，默认：true
          showCircle: true, // 定位成功后用圆圈表示定位精度范围，默认：true
          panToLocation: true, // 定位成功后将定位到的位置作为地图中心点，默认：true
          zoomToAccuracy: true, // 定位成功后调整地图视野范围使定位位置及精度范围视野内可见，默认：f
          extensions: "all",
          expandZoomRange: true,
          keyboardEnable: true,
          pName: "Geolocation",
          campus: [],
          events: {
            // click(e) {
            click: (e) => {
              // console.log(e);
              alert(e.lnglat);
              const { lng, lat } = e.lnglat;
              self.lng = lng;
              self.lat = lat;
            },
            init(o) {
              // o 是高德地图定位插件实例
              o.getCityInfo((status, result) => {
                if (self.addOrUpdate) {
                  if (
                    result != null &&
                    result.center != null &&
                    result.center.length > 0
                  ) {
                    self.lng = result.center[0];
                    self.lat = result.center[1];
                  } else {
                    self.lng = 117.283042;
                    self.lat = 31.86119;
                  }
                  self.center = [result.center[0], result.center[1]];
                } else {
                  self.lng = self.updateData.longitude;
                  self.lat = self.updateData.latitude;
                  if (
                    self.updateData.longitude != "" &&
                    self.updateData.latitude != ""
                  ) {
                    // self.center = [self.updateData.longitude, self.updateData.latitude]
                  }
                }
                self.loaded = true;
                self.$nextTick();
              });
            },
          },
        },
      ],
      // 地图结束
    };
  },
  created() {},
  methods: {
    //根据手机号查询信息
    queryInfo(loginName) {
      const that = this;
      cousysApi.queryInfo(loginName).then((res) => {
        // that.dataQuery.isRegister=res.data.isRegister
        // if (res.data.isRegister){//如果判断的话，重填手机号不会清空
        that.dataQuery = res.data;
        that.studentList = res.data.studentVos;
        //   if (res.data.studentVos!=null&&res.data.studentVos.length>0){
        //     that.dataQuery.studentCode=res.data.studentVos[0].studentCode
        //   }
        // that.dataQuery.loginName=loginName
        // }
      });
    },
    //家长注册
    addByLoginName() {
      const that = this;
      cousysApi.addByLoginName(this.dataQuery).then((res) => {
        that.dataQuery.memberCode = res.data;
      });
    },
    //打开新增学员弹窗
    addStudent() {
      this.showAddStudent = true;
    },
    //新增学员
    addStudentCommit(loginName) {
      const that = this;
      that.studentAdd.loginName = loginName;
      that.studentAdd.grade = that.dataQuery.grade;
      cousysApi.addStudent(this.studentAdd).then((res) => {
        that.queryInfo(loginName);
        that.showAddStudent = false;
      });
    },
    closeEdit() {
      this.studentAdd = {};
      this.showAddStudent = false;
    },
    //录入基本信息
    saveInfo() {
      const that = this;
      // that.$refs[ele].validate((valid) => {
      // 表单验证
      that.cousysInfoAdd.courseOrderId = that.dataQuery.courseOrderId;
      that.cousysInfoAdd.memberCode = that.dataQuery.memberCode;
      that.cousysInfoAdd.nickName = that.dataQuery.nickName;
      that.cousysInfoAdd.gender = that.dataQuery.gender;
      that.cousysInfoAdd.studentCode = that.dataQuery.studentCode;
      that.cousysInfoAdd.totalCourse = that.dataQuery.totalCourse;
      that.cousysInfoAdd.grade = that.dataQuery.grade;

      that.cousysInfoAdd.province = that.dataQuery.province;
      that.cousysInfoAdd.city = that.dataQuery.city;
      that.cousysInfoAdd.area = that.dataQuery.area;
      that.cousysInfoAdd.longitude = that.dataQuery.longitude;
      that.cousysInfoAdd.latitude = that.dataQuery.latitude;
      that.cousysInfoAdd.homeAddress = that.dataQuery.homeAddress;
      // if (valid) {
      cousysApi.addCousysInfo(that.cousysInfoAdd).then((res) => {
        that.dataQuery = {};
        // that.$message.success('提交成功')
        window.localStorage.setItem("courseOrderId", res.data);
        that.$router.push({
          path: "/cousys/qrPayCode",
        });
      });
    },
    //删除
    clearInfo(ele) {
      this.dataQuery = {};
    },

    //地图选点相关
    // 鼠标滑过
    markerMouse(e) {
      infoWindow.setContent(e.target.content);
      infoWindow.open(map, e.target.getPosition());
    },
    // 鼠标滑过
    markerOut(e) {
      map.clearInfoWindow();
    },
    onSearchResult(pois) {
      let latSum = 0;
      let lngSum = 0;
      let markers = [];
      let windows = [];
      let that = this;
      that.result = [];
      if (pois.length > 0) {
        // console.log(pois)
        pois.forEach((poi, index) => {
          const { lng, lat } = poi;
          lngSum += lng;
          latSum += lat;
          markers.push({
            position: [poi.lng, poi.lat],
            events: {
              click() {
                // console.log(poi)
                that.windows.forEach((window) => {
                  window.visible = false;
                });
                that.window = that.windows[index];
                that.$nextTick(() => {
                  that.window.visible = true;
                  that.getMarkAddress(poi.lng, poi.lat);
                });
              },
            },
          });
          // ${ index }<img src="" style="">
          windows.push({
            position: [poi.lng, poi.lat],
            content: `<div class="prompt"><span>${poi.name}</span></div>`,
            visible: false,
          });
          that.result.push(poi);
        });
        const center = {
          lng: lngSum / pois.length,
          lat: latSum / pois.length,
        };
        this.mapCenter = [center.lng, center.lat];
        this.center = [center.lng, center.lat];
        this.markers2 = markers;
        this.windows = windows;
      }
    },
    getMarkAddress(lng, lat) {
      // 这里通过高德 SDK 完成。
      var that = this;
      var geocoder = new AMap.Geocoder();
      geocoder.getAddress([lng, lat], function (status, result) {
        if (status === "complete" && result.info === "OK") {
          if (result && result.regeocode) {
            // console.log(result)
            // that.searchOption.city = result.regeocode.addressComponent.city
            that.center = [lng, lat];
            // 判断是添加还是编辑
            // if (that.addOrUpdate) {
            that.dataQuery.longitude = lng;
            that.dataQuery.latitude = lat;
            that.dataQuery.homeAddress = result.regeocode.formattedAddress;
            that.dataQuery.province =
              result.regeocode.addressComponent.province;

            var reg = RegExp(/省/);
            if (
              that.dataQuery.province.match(reg) &&
              result.regeocode.addressComponent.city == ""
            ) {
              that.dataQuery.city = result.regeocode.addressComponent.district;
            } else {
              if (
                result.regeocode.addressComponent.province == "重庆市" ||
                result.regeocode.addressComponent.province == "天津市" ||
                result.regeocode.addressComponent.province == "北京市" ||
                result.regeocode.addressComponent.province == "上海市"
              ) {
                that.dataQuery.city =
                  result.regeocode.addressComponent.province;
              } else {
                // 市
                that.dataQuery.city = result.regeocode.addressComponent.city;
              }
            }
            //that.addData.city = result.regeocode.addressComponent.city;
            that.dataQuery.area = result.regeocode.addressComponent.district;
            // } else {
            //   that.updateData.longitude = lng;
            //   that.updateData.latitude = lat;
            //   that.updateData.address = result.regeocode.formattedAddress;
            //   that.updateData.province =
            //     result.regeocode.addressComponent.province;
            //
            //   var reg = RegExp(/省/);
            //   if (
            //     that.updateData.province.match(reg) &&
            //     result.regeocode.addressComponent.city == ""
            //   ) {
            //     that.updateData.city =
            //       result.regeocode.addressComponent.district;
            //   } else {
            //     if (
            //       result.regeocode.addressComponent.province == "重庆市" ||
            //       result.regeocode.addressComponent.province == "天津市" ||
            //       result.regeocode.addressComponent.province == "北京市" ||
            //       result.regeocode.addressComponent.province == "上海市"
            //     ) {
            //       that.updateData.city =
            //         result.regeocode.addressComponent.province;
            //     } else {
            //       // 市
            //       that.updateData.city = result.regeocode.addressComponent.city;
            //     }
            //   }
            //   // that.updateData.city = result.regeocode.addressComponent.city
            //   that.updateData.area = result.regeocode.addressComponent.district;
            // }
            that.$nextTick();
          }
        } else {
          // alert('地址获取失败')
        }
      });
    },
    // 标注列表
    markList(lng, lat, index) {
      if (this.currentResult != index) {
        this.currentResult = index;
      } else {
        this.currentResult = -1;
      }
      this.getMarkAddress(lng, lat, index);
    },
    changeSizeHandler(size) {
      this.pageSize = size;
    },
    handleCurrentChange(currentPage) {
      this.pageNum = currentPage;
    },
  },
};
</script>

<style scoped>
.paike {
  padding-bottom: 50px;
  padding-top: 20px;
}

.studentClass {
  padding-left: 20px;
  padding-top: 20px;
  padding-bottom: 20px;
}

.peopleClass {
  padding-top: 20px;
  padding-bottom: 20px;
}
</style>
