<template>
  <div class="add-video-dialog">
    <el-dialog width="70%" :visible.sync="dialogParam.visible" @closed="closeAddDialog()" :close-on-click-modal="false" @submit.native.prevent>
      <div slot="title" class="dialog-title">
        <span v-if="dialogParam.type === 'add'">新增课程</span>
        <span v-if="dialogParam.type === 'edit'">编辑课程</span>
        <span v-if="dialogParam.type === 'detail'">课程详情</span>
      </div>
      <!-- <div v-if="dialogParam.type === 'detail'">
        <div class="content"><span class="contentTxt">基本信息</span></div>
        <div class="detailContent">课程名称：{{ labelForm.courseName }}</div>
        <div class="detailContent">考核人员：{{ labelForm.examinee }}</div>
        <div class="detailContent">类型：{{ labelForm.type }}</div>
        <div class="content"><span class="contentTxt">课程信息</span></div>
        <div class="downContent">封面上传：{{ labelForm.courseCover }}</div>
        <div class="downContent">视频上传：{{ labelForm.video }}</div>
        <div class="downContent">文档上传：{{ labelForm.uploadFile }}</div>
        <div class="downContent">简介：{{ labelForm.courseOverview }}</div>
        <div class="downContent">是否进行考试：{{ labelForm.radio }}</div>
      </div> -->
      <div>
        <el-form :model="labelForm" ref="addForm" :disabled="dialogParam.type === 'detail'" label-width="120px" label-position="right" :rules="rules">
          <div class="content"><span class="contentTxt">基本信息</span></div>
          <el-row :gutter="24">
            <el-col :span="8">
              <el-form-item label="课程名称：" prop="courseName">
                <el-input style="width: 100%" placeholder="请输入课程名称" maxlength="100" v-model="labelForm.courseName"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="考核人员：" prop="examinee">
                <el-select style="width: 100%" v-model="labelForm.examinee" @change="getExaminee" multiple placeholder="请选择">
                  <el-option v-for="item in examinerList" :key="item.id" :label="item.rname" :value="item.id"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="8">
              <el-form-item label="类型：" prop="courseType">
                <el-select style="width: 100%" v-model="labelForm.courseType" placeholder="请选择">
                  <el-option v-for="item in courseType" :key="item.dictCode" :label="item.dictLabel" :value="item.dictValue"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <div class="content"><span class="contentTxt">课程信息</span></div>
          <el-form-item label="封面上传：" prop="courseCover">
            <OneImageUpload
              @handleSuccess="handleSuccess"
              @handleRemove="handleRemove"
              :disabled="dialogParam.type === 'detail'"
              :fileList="labelForm.courseCover"
              :showTip="false"
              :limit="1"
            />
            <el-input type="hidden" v-model="labelForm.courseCover"></el-input>
          </el-form-item>
          <el-form-item label="视频上传：" prop="videoList">
            <UploadVideo
              @videoSucceed="videoSucceed"
              ref="uploadVideoRef"
              :videoList="labelForm.videoList"
              :disabled="dialogParam.type === 'detail'"
              @videoRemove="videoRemove"
            ></UploadVideo>
            <el-input type="hidden" v-model="labelForm.videoList"></el-input>
          </el-form-item>
          <el-form-item label="文档上传：" prop="fileList">
            <UploadFile
              @handleRemove="handleRemoveFile"
              @handleSuccess="handleSuccessFile"
              :disabled="dialogParam.type === 'detail'"
              :file-list="labelForm.fileList"
              :limit="100"
              :showTip="false"
            ></UploadFile>
            <el-input type="hidden" v-model="labelForm.uploadFile"></el-input>
          </el-form-item>
          <el-form-item label="简介：" prop="courseOverview">
            <!-- <Tinymce ref="editor"  v-model="labelForm.courseOverview" :disabled="dialogParam.type === 'detail'"  :height="400" /> -->
            <el-input type="textarea" placeholder="请输入简介" v-model="labelForm.courseOverview" rows="7" maxlength="255" show-word-limit></el-input>
          </el-form-item>
          <el-form-item label="是否进行考试：" prop="paperConfigured">
            <el-radio v-model="labelForm.paperConfigured" :label="1">是</el-radio>
            <el-radio v-model="labelForm.paperConfigured" :label="0">否</el-radio>
          </el-form-item>
        </el-form>
      </div>
      <!-- 底部按钮栏 -->
      <div slot="footer" class="dialog-footer">
        <!-- <el-button
          v-show="labelForm.radio === '1'"
          type="primary"
          size="small"
          @click="closeAddDialog()"
          >录入考题</el-button
        > -->
        <el-button v-if="dialogParam.type === 'add' || dialogParam.type === 'edit'" type="primary" size="small" :loading="loading" @click="submitForm()">确认</el-button>
        <el-button size="small" @click="closeAddDialog()">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
  import UploadFile from '@/components/Upload/UploadFile';
  import OneImageUpload from '@/components/Upload/OneImageUpload';
  import Tinymce from '@/components/Tinymce';
  import UploadVideo from '@/components/UploadVideo';
  import courseApi from '@/api/training/course';
  // courseCreateOrUpdate
  export default {
    name: 'addCourse',
    components: {
      UploadVideo,
      Tinymce,
      OneImageUpload,
      UploadFile
    },
    props: {
      dialogParam: {
        type: Object,
        default() {
          return {};
        }
      }
    },
    data() {
      return {
        courseType: [],
        fileList: '',
        loading: false,
        labelForm: {
          courseName: '',
          examinee: [],
          videoList: [],
          courseOverview: '',
          paperConfigured: '',
          courseCover: '',
          fileList: []
        },
        examinerList: [],
        rules: {
          courseName: [{ required: true, message: '请输入课程名称', trigger: 'blur' }],
          examinee: [{ required: true, message: '请至少选择一个考核人员', trigger: 'change' }],
          courseType: [{ required: true, message: '请选择类型', trigger: 'change' }],
          //
          type: [{ required: true, message: '请选择类型', trigger: 'change' }],
          view: [{ required: true, message: '请至少选择一个观看范围', trigger: 'change' }],
          uploadimage: [{ required: true, message: '请上传封面', trigger: 'blur' }],
          videoList: [{ required: true, message: '请上传视频', trigger: 'change' }],
          fileList: [{ required: true, message: '请上传文档', trigger: 'change' }],
          paperConfigured: [{ required: true, message: '请选择是否考试', trigger: 'change' }]
          //  { min: 3, max: 5, message: '长度在 3 到 5 个字符', trigger: 'blur' }
        }
      };
    },
    mounted() {
      this.getQueryByType();
      this.getQueryRoles();
    },
    methods: {
      getQueryByType() {
        courseApi.dictQueryByType({ dictType: 'paper_course_category' }).then((res) => {
          this.courseType = res.data;
        });
      },
      getQueryRoles() {
        courseApi.dictQueryRoles().then((res) => {
          this.examinerList = res.data;
        });
      },
      open(id) {
        courseApi.courseDetail({ id: id }).then((res) => {
          this.labelForm = { ...this.labelForm, ...res.data };
          // this.labelForm.videoList=[]
          // this.labelForm.fileList=[]
          // this.labelForm.examinee=[]
          this.fileList = '';
          if (this.labelForm.courseCover) {
            this.fileList = this.labelForm.courseCover;
          }
          this.labelForm.courseType = this.labelForm.courseType + '';
          this.labelForm.paperConfigured = this.labelForm.needExam;
          this.labelForm.examinee = [];
          this.labelForm.examineeList.forEach((item) => {
            this.labelForm.examinee.push(item.id);
          });
          this.labelForm.attachmentList.forEach((item) => {
            if (item.fileType == 1) {
              this.labelForm.videoList.push({ ...item, id: item.filePath });
            } else {
              this.labelForm.fileList.push({ name: item.fileName, ...item });
            }
          });
          // console.log(this.labelForm)
        });
      },
      // 处理照片成功
      handleSuccess(url) {
        // console.log(url, 'courseCover')
        // this.labelForm.courseCover=''
        // console.log('------------------------------')
        this.labelForm.courseCover = url;
      },
      // 处理照片删除
      handleRemove(file) {
        // console.log(file, "file");
      },
      // 处理文件成功
      handleSuccessFile(url, name, info) {
        // console.log(url, "url");
        this.labelForm.uploadFile = url;
        // console.log(name, info)
        info.filePath = url;
        this.videoRemove(info, 1);
      },
      // 处理文件删除
      handleRemoveFile(file) {
        // console.log(file, "file");
        if (file.fileId) {
          this.videoRemove(file, 1);
        }
      },
      videoRemove(info, key) {
        if (this.labelForm.id) {
          let param = {
            courseId: this.labelForm.id,
            operatorId: this.labelForm.operatorId
          };
          if (!key && !info.fileId) {
            return;
          }
          if (key == 2) {
            param.videoList = [
              {
                fileName: info.fileName,
                filePath: info.fileId ? info.filePath : info.id
              }
            ];
          } else {
            param.fileList = [
              {
                fileName: info.name,
                filePath: info.filePath
              }
            ];
          }
          if (info.fileId) {
            param.id = info.fileId;
          }
          courseApi.courseAttachment(param).then((res) => {});

          // 更新labelForm.videoList的值
          this.labelForm.videoList = this.labelForm.videoList.filter((item) => item.id !== info.id);
        }
      },
      // 处理视频成功
      videoSucceed(data) {
        this.labelForm.videoList = [];
        data.forEach((item) => {
          this.labelForm.videoList.push({ fileName: item.fileName, filePath: item.filePath ? item.filePath : item.id });
          if (!item.fileId && item.success) {
            this.videoRemove(item, 2);
          }
        });
      },
      getExaminee(val) {
        this.labelForm.examinee = val;
      },
      // 提交
      submitForm() {
        // this.labelForm.fileList
        let param = { fileList: [] };
        this.labelForm.fileList.forEach((item) => {
          param.fileList.push({ fileName: item.name, filePath: item.filePath ? item.filePath : item.url, name: item.name });
        });
        this.labelForm.fileList = param.fileList;
        if (this.labelForm.courseType) {
          this.labelForm.courseType = Number(this.labelForm.courseType);
        }
        let tableData = this.$refs.uploadVideoRef.$data.tableData;
        this.$refs.addForm.validate((valid) => {
          if (valid) {
            if (tableData.length != this.labelForm.videoList.length) {
              this.$message.error('请上传添加的视频');
              return;
            }
            courseApi.courseCreateOrUpdate(this.labelForm).then((res) => {
              this.$emit('closeDialog');
            });
          } else {
            console.log('error submit!!');
            return false;
          }
        });
      },
      closeAddDialog() {
        this.$emit('closeDialog');
      }
    }
  };
</script>
<style scoped>
  .content {
    line-height: 30px;
    width: 100%;
    height: 30px;
    background-color: #d7d7d7;
    margin-bottom: 20px;
  }

  .contentTxt {
    margin-left: 10px;
  }

  .detailContent {
    width: 50%;
    display: inline-block;
    margin-bottom: 10px;
  }

  .downContent {
    margin-bottom: 10px;
  }
</style>
