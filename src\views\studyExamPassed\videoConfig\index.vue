<template>
  <div class="app-container">
    <el-form :model="dataQuery" ref="queryForm" :inline="true">
      <el-form-item label="视频ID：" prop="id">
        <el-input
          v-model="dataQuery.id"
          type="number"
          @keydown.native="handleNumberInputE"
          @change="handleChangeNumber"
          clearable
          placeholder="请输入视频ID"
          class="id-input"
        ></el-input>
      </el-form-item>
      <el-form-item label="视频名称：" prop="videoName">
        <el-input v-model="dataQuery.videoName" v-trim clearable placeholder="请输入视频名称"></el-input>
      </el-form-item>
      <el-form-item label="课程大类：" prop="curriculumId">
        <el-select v-model="dataQuery.curriculumId" @change="handleCourseCategoryQueryChange" clearable>
          <el-option v-for="item in courseCategoryList" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="学段：" prop="grade">
        <el-cascader v-model="dataQuery.grade" :options="dataQueryDict.gradeList" :props="{ checkStrictly: true, expandTrigger: 'hover' }" clearable></el-cascader>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="queryData">查询</el-button>
        <el-button type="primary" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-col :span="24" style="margin-bottom: 30px">
      <el-button type="success" icon="el-icon-plus" size="mini" @click="handleAdd()">新增</el-button>
    </el-col>
    <el-table class="common-table" v-loading="tableLoading" :data="tableData" style="width: 100%; margin-bottom: 20px" row-key="id" border default-expand-all>
      <el-table-column type="index" label="序号" />
      <el-table-column prop="id" label="视频ID" />
      <el-table-column prop="videoName" label="视频名称" show-overflow-tooltip />
      <el-table-column prop="curriculumName" label="课程大类" />
      <el-table-column prop="gradeLevel" label="学段" :formatter="gradeLevelName" />
      <el-table-column prop="subjectName" label="学科" />
      <el-table-column prop="versionName" label="版本" />
      <el-table-column prop="createTime" label="创建时间" />
      <el-table-column prop="withdrawnBonus" label="操作" width="420">
        <template slot-scope="scope">
          <el-button type="success" size="mini" icon="el-icon-edit-outline" @click="handleUpdate(scope.row)">编辑</el-button>
          <el-button type="danger" size="mini" icon="el-icon-delete" @click="handleDelete(scope.row.id)">删除</el-button>
          <el-button type="primary" size="mini" icon="el-icon-edit-outline" @click="handleOpenEdit(scope.row)">编辑视频节点</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <el-col :span="24" style="margin-bottom: 20px">
      <el-pagination
        :current-page="tablePage.currentPage"
        :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="tablePage.totalItems"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-col>

    <el-dialog
      title="编辑视频节点"
      :visible.sync="editDialogVisible"
      width="35%"
      :before-close="handleEditClose"
      v-loading="editDialogLoading"
      element-loading-text="视频节点加载中..."
      element-loading-spinner="el-icon-loading"
      element-loading-background="rgba(0, 0, 0, 0.8)"
    >
      <div style="height: 300px; margin: 0 auto" v-if="editDialogVisible">
        <PlayVideo ref="playVideo" :vid="videold" :videoKeyframes="videoKeyframesPlay" :duration.sync="duration"></PlayVideo>
      </div>
      <el-row style="margin-top: 10px">
        <el-table :data="videoKeyframes" max-height="250">
          <!-- 排序 -->
          <el-table-column label="排序" width="60">
            <template v-slot="{ row, column, $index }">
              <el-tag type="info" effect="plain">{{ $index + 1 }}</el-tag>
            </template>
          </el-table-column>
          <!-- 视频节点时间 -->
          <!-- selectableRange: `00:00:00 - ${duration}` -->
          <el-table-column label="视频节点时间" width="150">
            <template v-slot="{ row, column, $index }">
              <el-time-picker
                :editable="false"
                v-model="row.nodeTime"
                :default-value="defaultTime"
                value-format="HH:mm:ss"
                @change="timeChange(row)"
                :picker-options="{
                  selectableRange: `00:00:00 - ${duration}`
                }"
                placeholder="任意时间点"
              ></el-time-picker>
            </template>
          </el-table-column>
          <!-- 视频节点名称 -->
          <el-table-column label="视频节点名称">
            <template v-slot="{ row, column, $index }">
              <el-input v-model="row.nodeName" type="textarea" placeholder="请输入视频节点名称" maxlength="50" show-word-limit resize="none"></el-input>
            </template>
          </el-table-column>
          <!-- 删除视频节点图标 -->
          <el-table-column label="" width="100">
            <template v-slot="{ row, column, $index }">
              <el-button type="danger" size="mini" icon="el-icon-delete" @click="handleDeleteVideo(row, $index)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-button class="add-video-node-btn" type="none" size="medium" icon="el-icon-plus" @click="handleAddVideoNode()">新增视频节点</el-button>
      </el-row>
    </el-dialog>

    <el-dialog :title="isEdit ? '编辑视频' : '添加视频'" :visible.sync="open" width="50%" @close="cancel" :close-on-click-modal="false">
      <el-form ref="form" :model="form" :rules="rules" label-width="120px" style="width: 70%">
        <el-form-item label="视频名称：" prop="videoName">
          <el-input v-model="form.videoName" type="textarea" maxlength="50" show-word-limit placeholder="请输入" />
        </el-form-item>
        <el-form-item label="课程大类：" prop="curriculumId">
          <el-select :key="'form-curriculumId-' + formItemKey" v-model="form.curriculumId" :disabled="isEdit" @change="handleCourseCategoryFormChange">
            <el-option v-for="item in courseCategoryList" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="学段/学科：" prop="grade">
          <el-cascader
            :key="'form-grade-' + formItemKey"
            v-model="form.grade"
            :options="formDict.gradeList"
            :props="{ expandTrigger: 'hover' }"
            :disabled="isEdit"
            @focus="handleGradeFormFocus(form.curriculumId)"
            @change="handleGradeFormChange"
          ></el-cascader>
        </el-form-item>
        <el-form-item ref="formVersion" label="版本：" prop="versionId">
          <el-select :key="'form-versionId-' + formItemKey" v-model="form.versionId" :disabled="isEdit" @focus="handleVersionFormFocus(form.grade)">
            <el-option v-for="item in formDict.versionList" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="视频上传：" prop="videoList">
          <el-tabs v-model="activeName" @tab-click="handleClick" type="card">
            <el-tab-pane label="本地上传" name="1">
              <BaseUploadVideo :video-list="form.videoList" :cata-type="form.curriculumId" @addVideo="handleAddVideo" />
            </el-tab-pane>
            <el-tab-pane label="保利威vid" name="2">
              <el-input v-model="form.videoVid" clearable placeholder="请输入视频vid" class="id-input"></el-input>
            </el-tab-pane>
          </el-tabs>
          <!-- <el-input v-model="form.vid" clearable placeholder="请输入视频vid" class="id-input"></el-input> -->
        </el-form-item>
      </el-form>
      <template slot="footer">
        <div class="dialog-footer">
          <el-button @click="cancel">取 消</el-button>
          <el-button v-if="isEdit" type="primary" @click="submitForm(false)">保 存</el-button>
          <template v-else>
            <el-button type="primary" @click="submitForm(false)">保 存</el-button>
            <el-button type="primary" @click="submitForm(true)">保存并继续新增</el-button>
          </template>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
  import PlayVideo from './components/PlayVideo.vue';
  import BaseUploadVideo from './components/BaseUploadVideo.vue';
  import { pageParamNames } from '@/utils/constants';
  import videoConfigApi from '@/api/studyExamPassed/videoConfig';
  import courseApi from '@/api/studyExamPassed/course';
  import { detailAPI } from '@/api/uploadVideoFile';
  import { mapGetters } from 'vuex';
  export default {
    name: 'VideoConfig',
    components: { BaseUploadVideo, PlayVideo },
    data() {
      return {
        dataQuery: {
          grade: []
        },
        dataQueryDict: {
          gradeList: [],
          versionList: []
        },
        courseCategoryList: [],
        gradeOnlyList: [],

        tableLoading: false,

        tablePage: {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null
        },

        tableData: [],

        isEdit: false,
        open: false,

        editDialogVisible: false, // 编辑视频节点弹窗
        editDialogLoading: false, // 编辑视频节点弹窗loading
        videold: '', // 当前编辑视频vid
        videoDetail: {}, // 当前编辑视频详情
        duration: '', // 视频时长
        videoKeyframes: [], // 视频节点列表
        videoKeyframesOld: [], // 编辑前的视频节点列表
        videoKeyframesPlay: [], // 视频播放节点列表
        defaultTime: new Date(0, 0, 0, 0, 0, 0), // 默认时间

        formItemKey: 0,

        formDict: {
          gradeList: [],
          versionList: []
        },

        form: {
          grade: [],
          videoList: [],
          videoVid: ''
        },

        // 表单校验
        rules: {
          videoName: [{ required: true, message: '请输入名称', trigger: 'blur' }],
          curriculumId: [{ required: true, message: '请选择', trigger: 'change' }],
          versionId: [{ required: true, message: '请选择', trigger: 'change' }],
          grade: [{ required: true, message: '请选择', trigger: 'change' }],
          videoList: [
            {
              validator: (rule, value, callback) => {
                // if (this.form.videoList.length == 0 && !this.form.videoVid) {
                //   callback(new Error('请上传视频或输入视频vid'));
                // } else if (this.form.videoList.length > 0 && this.form.videoVid && this.form.videoList[0].vid != this.form.videoVid) {
                //   callback(new Error('只能选择一种方式上传视频'));
                // } else {
                //   callback();
                // }
                if ((this.activeName == '1' && this.form.videoList.length == 0) || (this.activeName == '2' && !this.form.videoVid)) {
                  callback(new Error('请上传视频或输入视频vid'));
                } else {
                  callback();
                }
              },
              trigger: 'change'
            }
          ]
          // videoList: [{ required: true, message: '请上传', trigger: 'change' }]
        },
        activeName: '1'
      };
    },
    computed: {
      ...mapGetters('enumItem', ['enumFormat'])
    },
    created() {
      this.initCourseCategoryList();
      this.initGradeList();
      this.getList();
    },
    methods: {
      initCourseCategoryList() {
        courseApi.getCourseCategory().then((res) => {
          this.courseCategoryList = res.data.map((item) => {
            return { label: item.enName, value: item.id };
          });
        });
      },
      initGradeList() {
        courseApi.getGradeAndSubjectList().then((res) => {
          console.log('ly', res);
          this.dataQueryDict.gradeList = res.data;
          let gradeOnlyList = [];
          res.data.forEach((item) => {
            gradeOnlyList.push({ label: item.label, value: item.value });
          });
          this.gradeOnlyList = gradeOnlyList;
        });
      },
      handleCourseCategoryQueryChange(curriculumId) {
        courseApi.getGradeAndSubjectList(curriculumId).then((res) => {
          this.dataQueryDict.gradeList = res.data;
        });
        this.dataQueryDict.gradeList = [];
        this.dataQuery.grade = [];
      },
      queryData() {
        this.tablePage.currentPage = 1;
        this.getList();
      },
      resetQuery() {
        this.tablePage.currentPage = 1;
        this.dataQuery = {
          grade: []
        };
        this.handleCourseCategoryQueryChange();
        this.getList();
      },

      // 分页
      handleSizeChange(val) {
        this.tablePage.size = val;
        this.tablePage.currentPage = 1;
        this.getList();
      },
      handleCurrentChange(val) {
        this.tablePage.currentPage = val;
        this.getList();
      },
      getList() {
        this.tableLoading = true;
        let dataQuery = {
          ...this.dataQuery,
          pageNum: this.tablePage.currentPage,
          pageSize: this.tablePage.size
        };
        if (this.dataQuery.grade.length > 0) {
          dataQuery.gradeLevel = dataQuery.grade[0];
          if (this.dataQuery.grade.length > 1) {
            dataQuery.subjectId = dataQuery.grade[1];
          }
        }
        delete dataQuery.grade;
        videoConfigApi.videoList(dataQuery).then((res) => {
          this.tableData = res.data.data;
          this.tableLoading = false;

          // 设置后台返回的分页参数
          pageParamNames.forEach((name) => this.$set(this.tablePage, name, parseInt(res.data[name])));
        });
      },
      handleUpdate(row) {
        this.restForm();
        let videoList = [];
        let videoVid = '';
        row.vid && videoList.push({ vid: row.vid, url: '', progress: 100 }) && (videoVid = row.vid);
        this.form = {
          ...row,
          grade: [row.gradeLevel, row.subjectId],
          videoList,
          videoVid
        };
        console.log('handleUpdate', this.form);
        let promiseArry = [];
        promiseArry.push(courseApi.getGradeAndSubjectList(row.curriculumId));
        promiseArry.push(courseApi.getVersionList(row.curriculumId, row.gradeLevel, row.subjectId));
        Promise.all(promiseArry).then((res) => {
          this.formDict.gradeList = res[0].data;
          this.formDict.versionList = res[1].data;
          this.isEdit = true;
          this.open = true;
        });
      },
      handleDelete(id) {
        this.$confirm('您确定删除视频吗？', '删除', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            videoConfigApi.deleteVideo(id).then(() => {
              this.$message.success('删除视频成功');
              this.getList();
            });
          })
          .catch(() => {
            this.$message.info('已取消删除视频');
          });
      },
      handleAdd() {
        this.formItemKey++;
        this.formDict.gradeList = [];
        this.formDict.versionList = [];
        this.restForm();
        this.isEdit = false;
        this.open = true;
      },
      restForm() {
        this.form = {
          grade: [],
          versionId: '',
          videoList: [],
          videoVid: ''
        };
        this.$refs['form']?.clearValidate();
        this.$refs['form']?.clearValidate(['videoList']);
      },
      handleCourseCategoryFormChange(curriculumId) {
        if (curriculumId) {
          courseApi.getGradeAndSubjectList(curriculumId).then((res) => {
            this.formDict.gradeList = res.data;
          });
        }
        this.formDict.gradeList = [];
        this.formDict.versionList = [];
        this.form.grade = [];
        this.handleGradeFormChange();
      },
      // 点击学段/学科时，判断是否先选择了课程大类
      handleGradeFormFocus(curriculumId) {
        curriculumId || this.$message.warning('请先选择课程大类');
      },
      handleGradeFormChange(grade) {
        if (grade && grade.length == 2) {
          courseApi.getVersionList(this.form.curriculumId, grade[0], grade[1]).then((res) => {
            this.formDict.versionList = res.data;
          });
        }
        this.formDict.versionList = [];
        this.form.versionId = '';
        // this.form.vid = '';
        setTimeout(() => {
          this.$refs['formVersion']?.clearValidate();
        });
      },
      handleVersionFormFocus(grade) {
        grade.length == 0 && this.$message.warning('请先选择学段/学科');
      },
      submitForm(continueAdd) {
        this.$refs['form'].validate((valid) => {
          if (valid) {
            console.log('videoList', this.form.videoList);
            if (this.activeName == '1' && this.form.videoList[0] && this.form.videoList[0].progress != 100) {
              this.$message.warning('请等待视频上传完成');
              return;
            }
            const fn = this.isEdit ? videoConfigApi.updateVideo : videoConfigApi.addVideo;
            // let form = {
            //   ...this.form,
            //   vid: this.form.videoList[0].vid,
            //   gradeLevel: this.form.grade[0],
            //   subjectId: this.form.grade[1]
            // };
            // 判断当前tab拿到vid
            let vid;
            if (this.activeName == '1') {
              vid = this.form.videoList[0].vid;
            } else if (this.activeName == '2') {
              vid = this.form.videoVid;
            }
            let form = {
              ...this.form,
              vid,
              gradeLevel: this.form.grade[0],
              subjectId: this.form.grade[1]
            };
            delete form.videoList;
            delete form.videoVid;
            delete form.grade;
            console.log('submitForm', form);
            fn(form).then(() => {
              this.$message.success(`${this.isEdit ? '编辑' : '新增'}视频成功`);
              if (!continueAdd) {
                this.open = false;
              }
              this.form.videoList = [];
              this.form.videoVid = '';
              this.getList();
            });
          } else {
            this.$message.error('请按规范填写数据');
          }
        });
      },
      cancel() {
        this.isEdit && this.open && this.$message.info('已取消修改视频');
        this.open = false;
      },
      gradeLevelName(row, column, cellValue, index) {
        return this.enumFormat(this.gradeOnlyList, cellValue) || '无';
      },
      handleNumberInputE(event) {
        if (event.key == 'e' || event.key == 'E') {
          event.returnValue = false;
          return false;
        }
        return true;
      },

      // id输入框输入数字时，限制小于20位，且不允许输入非数字
      handleChangeNumber() {
        if (this.dataQuery.id) {
          let newValue = '';
          if (this.dataQuery.id.length > 19) {
            newValue = this.dataQuery.id.substring(0, 19);
          } else if (/[^\d]/g.test(this.dataQuery.id)) {
            newValue = this.dataQuery.id.replaceAll(/[^\d]/g, '');
          }
          newValue && this.$set(this.dataQuery, 'id', newValue);
        }
      },
      handleAddVideo() {
        this.$refs['form'].validateField('videoList');
      },
      handleClick(tab, event) {
        // if (tab.name == '2') {
        //   this.form.vid = this.form.videoList[0].vid ? this.form.videoList[0].vid : '';
        // } else {
        //   this.form.videoList[0].vid = this.form.vid ? this.form.vid : '';
        // }
      },
      // 编辑视频节点弹窗显示
      async handleOpenEdit(row) {
        const loading = this.$loading({
          lock: true,
          text: '时间节点加载中...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        let res = await detailAPI(row.vid);
        console.log('🚀 ~ handleOpenEdit ~ res:', res);
        let url = res.data.videoUrl;
        if (!url || !url.includes(res.data.vid)) {
          this.$message.info('视频审核中，无法预览');
          url = '';
          loading.close();
          return;
        }
        courseApi
          .getVideoNode({ id: row.id })
          .then((res) => {
            console.log('🚀 ~ courseApi.getVideoNode ~ res:', res.data.nodes);
            this.videoKeyframes = res.data.nodes || [];
            this.videoKeyframesOld = JSON.parse(JSON.stringify(this.videoKeyframes)) || [];
            // this.videoKeyframesPlay = this.videoKeyframes.map((item) => {
            //   return {
            //     // 打点出现时间
            //     keytime: item.nodeTime.split(':').reduce((acc, cur) => acc * 60 + +cur),
            //     // 打点提示内容
            //     keycontent: item.nodeName
            //   };
            // });
            this.editDialogVisible = true;
            loading.close();
          })
          .catch((err) => {
            loading.close();
          });
        this.videold = row.vid;
        this.videoDetail = row;
      },
      // 编辑视频节点弹窗关闭
      handleEditClose() {
        // 是否有视频节点未填写内容
        // console.log('🚀 ~ handleEditClose ~ this.videoKeyframes:', this.videoKeyframes);
        let isEmpty = this.videoKeyframes.some((item) => !item.nodeName || !item.nodeTime);
        console.log('🚀 ~ handleEditClose ~ isEmpty:', isEmpty);
        if (isEmpty) {
          this.$confirm('有视频节点内容或时间未填写，退出将不会保存该节点，是否继续？', '提示', {
            confirmButtonText: '去填写',
            cancelButtonText: '继续关闭',
            type: 'warning'
          })
            .then(() => {
              // 取消操作
            })
            .catch(() => {
              this.handleSaveVideoNode();
            });
          return;
        }
        this.handleSaveVideoNode();
      },
      // 保存视频节点
      handleSaveVideoNode() {
        this.editDialogVisible = false;
        const nodes = this.videoKeyframes
          .map((item, index) => {
            return {
              sortNo: index + 1,
              nodeTime: item.nodeTime,
              nodeName: item.nodeName
            };
          })
          .filter((item) => item.nodeName && item.nodeTime);
        console.log('🚀 ~ handleEditClose ~ nodes:', nodes, JSON.stringify(this.videoKeyframesOld));

        // 判断新旧视频节点数据是否一致
        if (JSON.stringify(nodes) === JSON.stringify(this.videoKeyframesOld)) {
          this.editDialogVisible = false;
          return;
        }
        console.log('🚀 ~ handleEditClose ~ nodes:', nodes);
        const loading = this.$loading({
          lock: true,
          text: '时间节点保存中...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        courseApi
          .saveVideoNode({ id: this.videoDetail.id, nodes })
          .then((res) => {
            loading.close();

            if (res.success) {
            }
          })
          .catch((err) => {
            loading.close();
          });
      },
      // 时间修改
      timeChange(row) {
        // 时分秒转换成秒数
        if (row.nodeTime) {
          row.keytime = row.nodeTime.split(':').reduce((acc, cur) => acc * 60 + +cur);
          let isExist = this.handleCheckRepeat(row);
          if (isExist) {
            this.$message.warning('该时间节点已存在，请重新选择');
            row.nodeTime = '';
            row.keytime = '';
            return;
          }
        } else {
          row.keytime = '';
        }

        // console.log('🚀 ~ timeChange ~ row:', row);
      },
      // 删除视频节点
      handleDeleteVideo(row, index) {
        // 二次确认
        this.$confirm('确定删除该节点吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            this.videoKeyframes.splice(index, 1);
          })
          .catch(() => {
            // 取消操作
          });
      },
      // 添加视频节点
      handleAddVideoNode() {
        if (this.videoKeyframes.length >= 50) {
          this.$message.warning('视频节点不能超过50个');
          return;
        }
        let { keytime, nodeTime } = this.$refs.playVideo.getCurrentTime();
        // 查看是否有相同的节点;
        let sortNo = (this.videoKeyframes[this.videoKeyframes.length - 1]?.sortNo || 0) + 1;
        let isExist = this.handleCheckRepeat({ keytime, nodeTime, sortNo });
        if (isExist) {
          this.$message.warning('请勿添加时间重复的节点');
          return;
        }
        this.videoKeyframes.push({
          keytime, // 秒数
          nodeTime, // 时分秒
          nodeName: '', // 节点内容
          sortNo
        });
      },
      // 判断是否有重复节点
      handleCheckRepeat({ keytime, nodeTime, sortNo }) {
        let isExist = this.videoKeyframes.some((item) => item.sortNo != sortNo && (item.keytime === keytime || item.nodeTime == nodeTime));
        return isExist;
      }
    }
  };
</script>

<style lang="less" scoped>
  .add-video-node-btn {
    width: 90%;
    margin: 10px 5% 0;
  }
  /* 取消[type='number']的input的上下箭头 */
  /deep/.id-input input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
  }

  /deep/.id-input input::-webkit-outer-spin-button {
    -webkit-appearance: none !important;
  }

  /deep/.id-input input[type='number'] {
    -moz-appearance: textfield;
    appearance: none;
  }
  ::v-deep {
    .el-textarea__inner {
      padding: 5px 40px 5px 5px;
    }
  }
</style>
