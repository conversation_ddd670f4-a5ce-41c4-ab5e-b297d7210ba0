<template>
  <div class="app-container">
    <el-form :model="dataQuery" ref="queryForm" :inline="true">
      <el-form-item label="视频ID：" prop="id">
        <el-input
          v-model="dataQuery.id"
          type="number"
          @keydown.native="handleNumberInputE"
          @change="handleChangeNumber"
          clearable
          placeholder="请输入视频ID"
          class="id-input"
        ></el-input>
      </el-form-item>
      <el-form-item label="视频名称：" prop="videoName">
        <el-input v-model="dataQuery.videoName" v-trim clearable placeholder="请输入视频名称"></el-input>
      </el-form-item>
      <el-form-item label="课程大类：" prop="curriculumId">
        <el-select v-model="dataQuery.curriculumId" @change="handleCourseCategoryQueryChange" clearable>
          <el-option v-for="item in courseCategoryList" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="学段：" prop="grade">
        <el-cascader v-model="dataQuery.grade" :options="dataQueryDict.gradeList" :props="{ checkStrictly: true, expandTrigger: 'hover' }" clearable></el-cascader>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="queryData">查询</el-button>
        <el-button type="primary" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-col :span="24" style="margin-bottom: 30px">
      <el-button type="success" icon="el-icon-plus" size="mini" @click="handleAdd()">新增</el-button>
    </el-col>
    <el-table class="common-table" v-loading="tableLoading" :data="tableData" style="width: 100%; margin-bottom: 20px" row-key="id" border default-expand-all>
      <el-table-column type="index" label="序号" />
      <el-table-column prop="id" label="视频ID" />
      <el-table-column prop="videoName" label="视频名称" show-overflow-tooltip />
      <el-table-column prop="curriculumName" label="课程大类" />
      <el-table-column prop="gradeLevel" label="学段" :formatter="gradeLevelName" />
      <el-table-column prop="subjectName" label="学科" />
      <el-table-column prop="versionName" label="版本" />
      <el-table-column prop="createTime" label="创建时间" />
      <el-table-column prop="withdrawnBonus" label="操作" width="300">
        <template slot-scope="scope">
          <el-button type="success" size="mini" icon="el-icon-edit-outline" @click="handleUpdate(scope.row)">编辑</el-button>
          <el-button type="danger" size="mini" icon="el-icon-delete" @click="handleDelete(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <el-col :span="24" style="margin-bottom: 20px">
      <el-pagination
        :current-page="tablePage.currentPage"
        :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="tablePage.totalItems"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-col>

    <el-dialog :title="isEdit ? '编辑视频' : '添加视频'" :visible.sync="open" width="50%" @close="cancel" :close-on-click-modal="false">
      <el-form ref="form" :model="form" :rules="rules" label-width="120px" style="width: 70%">
        <el-form-item label="视频名称：" prop="videoName">
          <el-input v-model="form.videoName" type="textarea" maxlength="50" show-word-limit placeholder="请输入" />
        </el-form-item>
        <el-form-item label="课程大类：" prop="curriculumId">
          <el-select :key="'form-curriculumId-' + formItemKey" v-model="form.curriculumId" :disabled="isEdit" @change="handleCourseCategoryFormChange">
            <el-option v-for="item in courseCategoryList" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="学段/学科：" prop="grade">
          <el-cascader
            :key="'form-grade-' + formItemKey"
            v-model="form.grade"
            :options="formDict.gradeList"
            :props="{ expandTrigger: 'hover' }"
            :disabled="isEdit"
            @focus="handleGradeFormFocus(form.curriculumId)"
            @change="handleGradeFormChange"
          ></el-cascader>
        </el-form-item>
        <el-form-item ref="formVersion" label="版本：" prop="versionId">
          <el-select :key="'form-versionId-' + formItemKey" v-model="form.versionId" :disabled="isEdit" @focus="handleVersionFormFocus(form.grade)">
            <el-option v-for="item in formDict.versionList" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="视频上传：" prop="videoList">
          <el-tabs v-model="activeName" @tab-click="handleClick" type="card">
            <el-tab-pane label="本地上传" name="1">
              <BaseUploadVideo :video-list="form.videoList" :cata-type="form.curriculumId" @addVideo="handleAddVideo" />
            </el-tab-pane>
            <el-tab-pane label="保利威vid" name="2">
              <el-input v-model="form.videoVid" clearable placeholder="请输入视频vid" class="id-input"></el-input>
            </el-tab-pane>
          </el-tabs>
          <!-- <el-input v-model="form.vid" clearable placeholder="请输入视频vid" class="id-input"></el-input> -->
        </el-form-item>
      </el-form>
      <template slot="footer">
        <div class="dialog-footer">
          <el-button @click="cancel">取 消</el-button>
          <el-button v-if="isEdit" type="primary" @click="submitForm(false)">保 存</el-button>
          <template v-else>
            <el-button type="primary" @click="submitForm(false)">保 存</el-button>
            <el-button type="primary" @click="submitForm(true)">保存并继续新增</el-button>
          </template>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
  import BaseUploadVideo from './components/BaseUploadVideo.vue';
  import { pageParamNames } from '@/utils/constants';
  import videoConfigApi from '@/api/studyExamPassed/videoConfig';
  import courseApi from '@/api/studyExamPassed/course';
  import { mapGetters } from 'vuex';
  export default {
    name: 'VideoConfig',
    components: { BaseUploadVideo },
    data() {
      return {
        dataQuery: {
          grade: []
        },
        dataQueryDict: {
          gradeList: [],
          versionList: []
        },
        courseCategoryList: [],
        gradeOnlyList: [],

        tableLoading: false,

        tablePage: {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null
        },

        tableData: [],

        isEdit: false,
        open: false,

        formItemKey: 0,

        formDict: {
          gradeList: [],
          versionList: []
        },

        form: {
          grade: [],
          videoList: [],
          videoVid: ''
        },

        // 表单校验
        rules: {
          videoName: [{ required: true, message: '请输入名称', trigger: 'blur' }],
          curriculumId: [{ required: true, message: '请选择', trigger: 'change' }],
          versionId: [{ required: true, message: '请选择', trigger: 'change' }],
          grade: [{ required: true, message: '请选择', trigger: 'change' }],
          videoList: [
            {
              validator: (rule, value, callback) => {
                if (this.form.videoList.length == 0 && !this.form.videoVid) {
                  callback(new Error('请上传视频或输入视频vid'));
                } else if (this.form.videoList.length > 0 && this.form.videoVid && this.form.videoList[0].vid != this.form.videoVid) {
                  callback(new Error('只能选择一种方式上传视频'));
                } else {
                  callback();
                }
              },
              trigger: 'change'
            }
          ]
          // videoList: [{ required: true, message: '请上传', trigger: 'change' }]
        },
        activeName: '1'
      };
    },
    computed: {
      ...mapGetters('enumItem', ['enumFormat'])
    },
    created() {
      this.initCourseCategoryList();
      this.initGradeList();
      this.getList();
    },
    methods: {
      initCourseCategoryList() {
        courseApi.getCourseCategory().then((res) => {
          this.courseCategoryList = res.data.map((item) => {
            return { label: item.enName, value: item.id };
          });
        });
      },
      initGradeList() {
        courseApi.getGradeAndSubjectList().then((res) => {
          this.dataQueryDict.gradeList = res.data;
          let gradeOnlyList = [];
          res.data.forEach((item) => {
            gradeOnlyList.push({ label: item.label, value: item.value });
          });
          this.gradeOnlyList = gradeOnlyList;
        });
      },
      handleCourseCategoryQueryChange(curriculumId) {
        courseApi.getGradeAndSubjectList(curriculumId).then((res) => {
          this.dataQueryDict.gradeList = res.data;
        });
        this.dataQueryDict.gradeList = [];
        this.dataQuery.grade = [];
      },
      queryData() {
        this.tablePage.currentPage = 1;
        this.getList();
      },
      resetQuery() {
        this.tablePage.currentPage = 1;
        this.dataQuery = {
          grade: []
        };
        this.handleCourseCategoryQueryChange();
        this.getList();
      },

      // 分页
      handleSizeChange(val) {
        this.tablePage.size = val;
        this.tablePage.currentPage = 1;
        this.getList();
      },
      handleCurrentChange(val) {
        this.tablePage.currentPage = val;
        this.getList();
      },
      getList() {
        this.tableLoading = true;
        let dataQuery = {
          ...this.dataQuery,
          pageNum: this.tablePage.currentPage,
          pageSize: this.tablePage.size
        };
        if (this.dataQuery.grade.length > 0) {
          dataQuery.gradeLevel = dataQuery.grade[0];
          if (this.dataQuery.grade.length > 1) {
            dataQuery.subjectId = dataQuery.grade[1];
          }
        }
        delete dataQuery.grade;
        videoConfigApi.videoList(dataQuery).then((res) => {
          this.tableData = res.data.data;
          this.tableLoading = false;

          // 设置后台返回的分页参数
          pageParamNames.forEach((name) => this.$set(this.tablePage, name, parseInt(res.data[name])));
        });
      },
      handleUpdate(row) {
        this.restForm();
        let videoList = [];
        let videoVid = '';
        row.vid && videoList.push({ vid: row.vid, url: '', progress: 100 }) && (videoVid = row.vid);
        this.form = {
          ...row,
          grade: [row.gradeLevel, row.subjectId],
          videoList,
          videoVid
        };
        console.log('handleUpdate', this.form);
        let promiseArry = [];
        promiseArry.push(courseApi.getGradeAndSubjectList(row.curriculumId));
        promiseArry.push(courseApi.getVersionList(row.curriculumId, row.gradeLevel, row.subjectId));
        Promise.all(promiseArry).then((res) => {
          this.formDict.gradeList = res[0].data;
          this.formDict.versionList = res[1].data;
          this.isEdit = true;
          this.open = true;
        });
      },
      handleDelete(id) {
        this.$confirm('您确定删除视频吗？', '删除', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            videoConfigApi.deleteVideo(id).then(() => {
              this.$message.success('删除视频成功');
              this.getList();
            });
          })
          .catch(() => {
            this.$message.info('已取消删除视频');
          });
      },
      handleAdd() {
        this.formItemKey++;
        this.formDict.gradeList = [];
        this.formDict.versionList = [];
        this.restForm();
        this.isEdit = false;
        this.open = true;
      },
      restForm() {
        this.form = {
          grade: [],
          versionId: '',
          videoList: [],
          videoVid: ''
        };
        this.$refs['form']?.clearValidate();
        this.$refs['form']?.clearValidate(['videoList']);
      },
      handleCourseCategoryFormChange(curriculumId) {
        if (curriculumId) {
          courseApi.getGradeAndSubjectList(curriculumId).then((res) => {
            this.formDict.gradeList = res.data;
          });
        }
        this.formDict.gradeList = [];
        this.formDict.versionList = [];
        this.form.grade = [];
        this.handleGradeFormChange();
      },
      // 点击学段/学科时，判断是否先选择了课程大类
      handleGradeFormFocus(curriculumId) {
        curriculumId || this.$message.warning('请先选择课程大类');
      },
      handleGradeFormChange(grade) {
        if (grade && grade.length == 2) {
          courseApi.getVersionList(this.form.curriculumId, grade[0], grade[1]).then((res) => {
            this.formDict.versionList = res.data;
          });
        }
        this.formDict.versionList = [];
        this.form.versionId = '';
        // this.form.vid = '';
        setTimeout(() => {
          this.$refs['formVersion']?.clearValidate();
        });
      },
      handleVersionFormFocus(grade) {
        grade.length == 0 && this.$message.warning('请先选择学段/学科');
      },
      submitForm(continueAdd) {
        this.$refs['form'].validate((valid) => {
          if (valid) {
            console.log('videoList', this.form.videoList);
            if (this.activeName == '1' && this.form.videoList[0] && this.form.videoList[0].progress != 100) {
              this.$message.warning('请等待视频上传完成');
              return;
            }
            const fn = this.isEdit ? videoConfigApi.updateVideo : videoConfigApi.addVideo;
            // let form = {
            //   ...this.form,
            //   vid: this.form.videoList[0].vid,
            //   gradeLevel: this.form.grade[0],
            //   subjectId: this.form.grade[1]
            // };
            let form = {
              ...this.form,
              vid: this.form.videoVid || this.form.videoList[0].vid,
              gradeLevel: this.form.grade[0],
              subjectId: this.form.grade[1]
            };
            delete form.videoList;
            delete form.videoVid;
            delete form.grade;
            console.log('submitForm', form);
            fn(form).then(() => {
              this.$message.success(`${this.isEdit ? '编辑' : '新增'}视频成功`);
              if (!continueAdd) {
                this.open = false;
              }
              this.form.videoList = [];
              this.form.videoVid = '';
              this.getList();
            });
          } else {
            this.$message.error('请按规范填写数据');
          }
        });
      },
      cancel() {
        this.isEdit && this.open && this.$message.info('已取消修改视频');
        this.open = false;
      },
      gradeLevelName(row, column, cellValue, index) {
        return this.enumFormat(this.gradeOnlyList, cellValue) || '无';
      },
      handleNumberInputE(event) {
        if (event.key == 'e' || event.key == 'E') {
          event.returnValue = false;
          return false;
        }
        return true;
      },

      // id输入框输入数字时，限制小于20位，且不允许输入非数字
      handleChangeNumber() {
        if (this.dataQuery.id) {
          let newValue = '';
          if (this.dataQuery.id.length > 19) {
            newValue = this.dataQuery.id.substring(0, 19);
          } else if (/[^\d]/g.test(this.dataQuery.id)) {
            newValue = this.dataQuery.id.replaceAll(/[^\d]/g, '');
          }
          newValue && this.$set(this.dataQuery, 'id', newValue);
        }
      },
      handleAddVideo() {
        this.$refs['form'].validateField('videoList');
      },
      handleClick(tab, event) {
        // if (tab.name == '2') {
        //   this.form.vid = this.form.videoList[0].vid ? this.form.videoList[0].vid : '';
        // } else {
        //   this.form.videoList[0].vid = this.form.vid ? this.form.vid : '';
        // }
      }
    }
  };
</script>

<style lang="less" scoped>
  /* 取消[type='number']的input的上下箭头 */
  /deep/.id-input input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
  }

  /deep/.id-input input::-webkit-outer-spin-button {
    -webkit-appearance: none !important;
  }

  /deep/.id-input input[type='number'] {
    -moz-appearance: textfield;
    appearance: none;
  }
</style>
