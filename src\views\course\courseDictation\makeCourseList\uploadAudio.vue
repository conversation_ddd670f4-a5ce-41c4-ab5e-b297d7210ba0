<template>
  <!-- 上传音频 -->
  <div class="container_box">
    <!-- 元辅音列表 -->
    <div class="uploadAudio_table">
      <el-table
        :data="vowelConsonantList"
        style="width: 80%; margin-right: 8px"
        class="common-table"
        stripe
        border
        :default-sort="{ prop: 'date', order: 'descending' }"
        v-loading="vcTableLoading"
        :span-method="vcSpanMethod"
      >
        <el-table-column label="元辅音" prop="wordSyllable">
          <template slot-scope="scope">
            {{ scope.row.wordSyllable }}
            <el-tag type="success">
              {{ scope.row.wordSyllableType === '0' ? '元音' : '辅音' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="上传状态" prop="status">
          <template slot-scope="scope">
            <i v-if="scope.row.wordSyllableAudioUrl" class="el-icon-circle-check" style="color: #13ce66; font-size: 16px"></i>
          </template>
        </el-table-column>
        <el-table-column label="批量上传" prop="id">
          <template>
            <el-upload
              multiple
              :http-request="vowelUploadHttp"
              v-loading="vowelUploadLoading"
              ref="uploadVideo"
              class="upload-demo"
              :show-file-list="true"
              :file-list="vowelAudioFileList"
              action=""
              :on-change="handleChangeVowel"
              :on-success="vowelHandleSuccess"
              :on-remove="vowelHandleRemove"
              :before-upload="vowelBeforeUpload"
            >
              <div class="el-upload__text">
                <el-button size="small" type="primary" :disabled="vowelUploadDisabled">
                  <i class="el-icon-plus"></i>
                  上传
                </el-button>
              </div>
            </el-upload>
          </template>
        </el-table-column>
        <el-table-column label="操作" prop="id" width="160">
          <template>
            <el-button type="success" @click="onVcSave" size="small" :disabled="vowelDisabled">保存</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- 音节列表 -->
    <div class="uploadAudio_table">
      <el-table
        :data="syllableList"
        style="width: 80%; margin-right: 8px"
        class="common-table"
        stripe
        border
        :default-sort="{ prop: 'date', order: 'descending' }"
        v-loading="syTableLoading"
        :span-method="sySpanMethod"
      >
        <el-table-column label="音节" prop="wordSyllable">
          <template slot-scope="scope">
            {{ scope.row.wordSyllable }}
            <el-tag type="success">
              {{ scope.row.wordSyllableType === '1' ? '音节' : '拆分' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="上传状态" prop="status">
          <template slot-scope="scope">
            <i v-if="scope.row.wordSyllableAudioUrl" class="el-icon-circle-check" style="color: #13ce66; font-size: 16px"></i>
          </template>
        </el-table-column>
        <el-table-column label="批量上传" prop="id" style="overflow-y: auto">
          <template slot-scope="scope">
            <el-upload
              multiple
              ref="uploadVideo"
              class="upload-demo"
              :http-request="(params) => syllableUploadHttp(params, scope.row)"
              v-loading="syllableUploadLoading"
              :show-file-list="true"
              :file-list="syllableAudioFileList"
              action=""
              :on-change="handleChangeSyllable"
              :on-success="syllableHandleSuccess"
              :on-remove="syllableHandleRemove"
              :before-upload="syllableBeforeUpload"
            >
              <div class="el-upload__text">
                <el-button size="small" type="primary" :disabled="syllableUploadDisabled">
                  <i class="el-icon-plus"></i>
                  上传
                </el-button>
              </div>
            </el-upload>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="160">
          <template>
            <el-button type="success" @click="onSySave" size="small" :disabled="syllableDisabled">保存</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- 划音节拼读列表上传 -->
    <div class="uploadAudio_table">
      <el-table
        :data="readList"
        style="width: 80%; margin-right: 8px"
        class="common-table"
        stripe
        border
        :default-sort="{ prop: 'date', order: 'descending' }"
        v-loading="uploadTableLoading"
        :span-method="videoSpanMethod"
      >
        <el-table-column v-for="item in tableColumn" :key="item.prop" :prop="item.prop" :label="item.label">
          <template slot-scope="scope">
            <!-- && scope.row.wordSyllable != scope.row.word  -->
            <div v-if="item.prop === 'wordSyllable'">
              {{ scope.row.wordSyllable }}
              <el-tag type="success">
                {{ wordSyllableTypeData.filter((item) => item.value === scope.row.wordSyllableType)[0].label }}
              </el-tag>
            </div>
            <div v-else-if="item.prop === 'status'">
              <i v-if="scope.row.wordSyllableAudioUrl" class="el-icon-circle-check" style="color: #13ce66; font-size: 16px"></i>
            </div>
            <div v-else-if="item.prop === 'wordId'">
              <!-- :multiple="false" 
                        :http-request="(params) => readUploadHttp(params,scope.row)" -->
              <el-upload
                multiple
                ref="uploadVideo"
                class="upload-demo"
                v-loading="readUploadLoading"
                :http-request="(params) => readUploadHttp(params, scope.row)"
                :show-file-list="true"
                :file-list="readFileList.find((e) => e.id == scope.row.wordId).list"
                action=""
                :on-success="readHandleSuccess"
                :on-remove="(file, fileList) => readHandleRemove(file, fileList, scope.row)"
                :on-change="(file, fileList) => videoHandleUpload(file, fileList, scope.row)"
                :before-upload="(file, fileList) => readBeforeUpload(file, fileList, scope.row)"
              >
                <div class="el-upload__text">
                  <el-button size="small" type="primary">
                    <i class="el-icon-plus"></i>
                    上传
                  </el-button>
                </div>
                <!-- <div v-if="scope.row.uploadedFileName">
                  {{ scope.row.uploadedFileName }}
                </div> -->
              </el-upload>
            </div>
            <div v-else-if="item.prop === 'submitId'">
              <el-button type="success" @click="onPySave(scope.row)" size="small" :disabled="scope.row.readDisabled">保存</el-button>
            </div>
            <div v-else>
              {{ scope.row[item.prop] }}
              <div>
                <!-- <i v-if="scope.row.fullUrl" class="el-icon-circle-check" style="color: #13ce66; font-size: 16px"></i> -->
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- 划音节拼写列表上传 -->
    <!-- <div class="uploadAudio_table">
      <el-table
        :data="writeList"
        style="width: 80%; margin-right: 8px"
        class="common-table"
        stripe
        border
        :default-sort="{ prop: 'date', order: 'descending' }"
        v-loading="uploadTableLoading"
        :span-method="videoSpanMethod2"
      >
        <el-table-column
          v-for="(item, index) in tableColumn"
          :key="item.prop"
          :prop="item.prop"
          :label="index == 0 ? '拼写单词' : item.label"
        >
          <template slot-scope="scope">
            <div v-if="item.prop === 'wordSyllable'">
              {{ scope.row.wordSyllable }}
             
              <el-tag type="success" style="margin-left: 10px">
                单词拼写</el-tag
              >
            </div>
            <div v-else-if="item.prop === 'status'">
              <i
                v-if="scope.row.wordSyllableAudioUrl"
                class="el-icon-circle-check"
                style="color: #13ce66; font-size: 16px"
              ></i>
            </div>
            <div v-else-if="item.prop === 'wordId'">
              <el-upload
                multiple
                ref="uploadVideo"
                class="upload-demo"
                :http-request="(params) => writeUploadHttp(params, scope.row)"
                v-loading="writeUploadLoading"
                :show-file-list="true"
                :file-list="
                  writeFileList.find((e) => e.id == scope.row.wordId).list
                "
                action=""
                :on-success="handleWordVideoSuccess"
                :on-remove="
                  (file, fileList) =>
                    writeHandleRemove(file, fileList, scope.row)
                "
                :before-upload="
                  (file, fileList) =>
                    writeBeforeUpload(file, fileList, scope.row)
                "
                :on-change="
                  (file, fileList) => writeHandleUpload(file, fileList)
                "
              >
                <div class="el-upload__text">
                  <el-button size="small" type="primary"
                    ><i class="el-icon-plus"></i>上传</el-button
                  >
                </div>
              </el-upload>
            </div>
            <div v-else-if="item.prop === 'submitId'">
              <el-button
                type="success"
                @click="onWriteSave(scope.row)"
                size="small"
                :disabled="scope.row.writeDisabled"
                >保存</el-button
              >
            </div>
            <div v-else>
              {{ scope.row[item.prop] }}
            </div>
          </template>
        </el-table-column>
      </el-table> -->
  </div>
</template>
<script>
  import { getMergeCells } from '../components/table.js';
  import courseDictationListApi from '@/api/courseDictationList';
  import { ossPrClient } from '@/api/alibaba';
  import { log } from 'bpmn-js-token-simulation';
  export default {
    name: 'uploadAudio',
    props: {
      dialogVisible: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        fileOne: '',
        wordSyllableTypeData: [
          { label: '单词音', value: '1' },
          { label: '基础音节', value: '2' },
          { label: '重音节', value: '3' },
          { label: '单词拼读', value: '4' },
          { label: '弱音', value: '5' },
          { label: '定长短', value: '6' }
        ], //元辅音列表
        // audioDialogVisible:false,
        uploadData: [
          { word: 'red', status: 0, id: '0000000' },
          { word: 'blue', status: 0, id: '1111111' },
          { word: 'white', status: 0, id: '2222222' }
        ], //上传音频弹窗
        tableColumn: [
          { prop: 'word', label: '单词' },
          { prop: 'wordSyllable', label: '拆分词' },
          { prop: 'status', label: '上传状态' },
          { prop: 'wordId', label: '上传音频' },
          { prop: 'submitId', label: '操作' }
        ],
        readList: [], //单词划拼读,划音节
        syllableList: [], //音节列表
        vowelConsonantList: [], //元辅音列表
        writeList: [], //拼写列表
        audioUrl: {
          vowelConsonantList: [], //元辅音
          syllableList: [], //音节
          writeList: [], //
          readList: []
        },
        courseType: this.$route.query.courseType,
        audioFileList: [], //已上传音频列表
        vowelAudioFileList: [], //元辅音音频上传列表
        syllableAudioFileList: [], //音节音频上传
        uploadTableLoading: false,
        vcTableLoading: false,
        syTableLoading: false,
        colspanLength: {},
        vowelUploadLoading: false,
        syllableUploadLoading: false,
        vowelDisabled: true, //元辅音保存
        voweSave: false,
        syllableSave: false,
        readDisabled: true, //划音节保存
        syllableDisabled: true, //音节保存
        oldReadList: [], //拼读划音节
        oldWriteList: [], //拼写划音节
        vowelUploadDisabled: false, //元辅音上传按钮
        syllableUploadDisabled: false,
        readUploadLoading: false, //拼读上传
        readWordList: [],
        writeWordList: [],
        readFileList: [],
        writeFileList: [],
        writeUploadLoading: false,
        tampFileList: [], //临时存放上传文件
        voweltampFileList: [], //元辅音临时存放上传文件
        readtampFileList: [], //拼读临时存放上传文件
        writetampFileList: [], //拼写临时存放上传文件
        num: []
      };
    },
    computed: {
      spanArr() {
        if (!this.tableColumn.length) return [];
        const mergeCols = ['word', 'wordId', 'submitId']; // 需要合并的列（字段）
        return getMergeCells(this.readList, this.tableColumn, mergeCols);
      },
      spanArr2() {
        if (!this.tableColumn.length) return [];
        const mergeCols = ['word', 'wordId', 'submitId']; // 需要合并的列（字段）
        return getMergeCells(this.writeList, this.tableColumn, mergeCols);
      }
    },
    watch: {},
    created() {
      ossPrClient();
      //   this.colspanLength = this.getRepeatNum()
      this.getTableData();
    },
    methods: {
      justPictureNum(file, fileList) {
        this.$message.warning(`当前限制选择1个文件`);
      },
      //获取音频列表信息
      getTableData() {
        this.vcTableLoading = true;
        this.uploadTableLoading = true;
        this.syTableLoading - true;
        courseDictationListApi.getAllAudioUrl(this.$route.query.id).then((res) => {
          if (res.code === 20000) {
            // readList:[],//单词划拼读,划音节
            // syllableList:[],//音节列表
            // vowelConsonantList:[],//元辅音列表
            // writeList:[],//拼写列表
            this.readList = [];
            const map = new Map();
            this.oldReadList = [];
            if (Array.isArray(res.data.data.readList)) {
              res.data.data.readList.forEach((res) => {
                let vodata = res.voList;
                vodata = vodata.filter((word) => word.wordSyllableType === '4');
                vodata = this.addSuffixToRepeatedSyllables(vodata);
                vodata.map((item, index) => {
                  this.readList.push({
                    wordId: res.id,
                    submitId: res.id,
                    id: item.id,
                    word: res.wordSyllable,
                    wordSyllable: item.wordSyllable,
                    wordSyllableType: item.wordSyllableType,
                    wordSyllableAudioUrl: item.wordSyllableAudioUrl,
                    readDisabled: true
                  });

                  if (index == vodata.length - 1) {
                    this.readList.push({
                      wordId: res.id,
                      submitId: res.id,
                      id: item.id,
                      word: res.wordSyllable,
                      wordSyllable: res.wordSyllable,
                      wordSyllableType: '4',
                      wordSyllableAudioUrl: item.wordSyllableAudioUrl,
                      readDisabled: true
                    });
                  }
                });
              });
            }

            this.oldReadList = res.data.data.readList;
            if (this.oldReadList[0] && this.oldReadList[0].wordType == 4) {
              this.tableColumn[0].label = '划音节单词';
            } else {
              this.tableColumn[0].label = '拼读单词';
            }
            this.oldReadList.forEach((e) => {
              this.readFileList.push({ id: e.id, list: [] });
            });
            this.oldWriteList = [];
            this.writeList = [];
            if (Array.isArray(res.data.data.writeList)) {
              res.data.data.writeList.forEach((res) => {
                let vodata = res.voList;
                vodata = vodata.filter((word) => word.wordSyllableType === '4');
                vodata = this.addSuffixToRepeatedSyllables(vodata);
                console.log('vodata', vodata);
                vodata.map((item, i) => {
                  this.writeList.push({
                    wordId: res.id,
                    submitId: res.id,
                    id: item.id,
                    word: res.wordSyllable,
                    wordSyllable: item.wordSyllable,
                    wordSyllableType: item.wordSyllableType,
                    wordSyllableAudioUrl: item.wordSyllableAudioUrl,
                    writeDisabled: true
                  });
                  if (i == vodata.length - 1) {
                    this.writeList.push({
                      wordId: res.id,
                      submitId: res.id,
                      id: item.id,
                      word: res.wordSyllable,
                      wordSyllable: res.wordSyllable,
                      wordSyllableType: '4',
                      wordSyllableAudioUrl: item.wordSyllableAudioUrl,
                      readDisabled: true
                    });
                  }
                });
              });
            }
            this.oldWriteList = res.data.data.writeList;

            this.oldWriteList &&
              this.oldWriteList.forEach((e) => {
                this.writeFileList.push({ id: e.id, list: [] });
              });
            this.syllableList = res.data.data.syllableList.filter((v) => map.set(v.wordSyllable, v));
            this.syllableList = this.addSuffixToRepeatedSyllables(this.syllableList);
            this.vowelConsonantList = res.data.data.vowelConsonantList.filter((v) => map.set(v.wordSyllable, v));
            this.vcTableLoading = false;
            this.uploadTableLoading = false;
            this.syTableLoading = false;
          }
        });
      },

      addSuffixToRepeatedSyllables(objects) {
        // 创建一个对象来跟踪每个wordSyllable出现的次数
        const syllableCounts = {};
        return objects.map((obj, index) => {
          const syllable = obj.wordSyllable;

          // 如果wordSyllable已经出现过，则增加计数并添加后缀
          if (syllableCounts[syllable]) {
            syllableCounts[syllable]++;
            return {
              ...obj,
              wordSyllable: `${syllable}(${syllableCounts[syllable]})`
            };
          } else {
            // 如果是第一次出现，则记录该wordSyllable并设置计数为1
            syllableCounts[syllable] = 1;
            return obj;
          }
        });
      },
      //元辅音列表合并单元格
      vcSpanMethod({ row, column, rowIndex, columnIndex }) {
        if (columnIndex === 2 || columnIndex === 3) {
          if (rowIndex % this.vowelConsonantList.length === 0) {
            return {
              rowspan: this.vowelConsonantList.length,
              colspan: 1
            };
          } else {
            return {
              rowspan: 0,
              colspan: 0
            };
          }
        }
      },
      sySpanMethod({ row, column, rowIndex, columnIndex }) {
        if (columnIndex === 2 || columnIndex === 3) {
          if (rowIndex % this.syllableList.length === 0) {
            return {
              rowspan: this.syllableList.length,
              colspan: 1
            };
          } else {
            return {
              rowspan: 0,
              colspan: 0
            };
          }
        }
      },
      //音频上传成功
      handleWordVideoSuccess(response, file, fileList) {
        this.audioStatus(fileList, 1);
        this.audioFileList = fileList;
      },
      syllableHandleSuccess(response, file, fileList) {
        // 更新 syllableAudioFileList 数组
      },
      vowelHandleSuccess(response, file, fileList) {
        console.log('00000000');
        // this.audioFileList = fileList
      },

      //上传音频列表批量上传操作合并
      videoSpanMethod({ row, column, rowIndex, columnIndex }) {
        return this.spanArr[rowIndex][columnIndex];
      },
      videoSpanMethod2({ row, column, rowIndex, columnIndex }) {
        return this.spanArr2[rowIndex][columnIndex];
      },
      //拼写单词音频上传↓↓↓↓↓
      writeUploadHttp({ file }, row) {
        const randomString = this.generateRandomString(6);
        const fileName = `manage/${Date.parse(new Date()) + '_' + randomString}.${file.name.split('.')[1]}`;
        const isWriteRepeat = this.writeFileList.find((e) => e.id == row.wordId).list.filter((f) => f.name === file.name).length > 0;
        console.log(isWriteRepeat, 'write');
        if (isWriteRepeat) {
          this.writeFileList.find((e) => e.id == row.wordId).list = Array.from(new Set(this.writeFileList.find((e) => e.id == row.wordId).list));
          return;
        }
        console.log('yichu');
        this.$nextTick(function () {
          if (ossPrClient() && ossPrClient().put) {
            ossPrClient()
              .put(fileName, file)
              .then(({ res, url, name }) => {
                if (res && res.status === 200) {
                  this.writeFileList.find((e) => e.id == row.wordId).list = this.writetampFileList;
                  this.writeAudioStatus(file, url, row);
                  this.writeUploadLoading = false;
                  this.$nextTick(() => {
                    this.writeUploadLoading = false;
                  });
                }
              })
              .catch((err) => {
                this.writeUploadLoading = false;
              });
          }
        });
      },
      writeAudioStatus(file, url, row) {
        this.writeUploadLoading = true;
        // const voList = this.oldReadList.filter(item => item.wordSyllable === row.word)?.voList
        if (file && file.name) {
          this.writeList.forEach((f) => {
            if (f.wordSyllable === file.name.split('.')[0] && f.word == row.word) {
              f.wordSyllableAudioUrl = url;
            }
          });
          this.writeUploadLoading = false;
          this.writeWordList = this.writeList.filter((res) => res.word === row.word);
          row['writeDisabled'] = !this.writeWordList.every(
            (item) => item['wordSyllableAudioUrl'] !== null && item['wordSyllableAudioUrl'] !== undefined && item['wordSyllableAudioUrl'] !== ''
          );
        }
      },
      //随机字符串
      generateRandomString(length) {
        const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        let result = '';
        const charactersLength = characters.length;
        for (let i = 0; i < length; i++) {
          result += characters.charAt(Math.floor(Math.random() * charactersLength));
        }
        return result;
      },
      //拼读单词音频上传↓↓↓↓↓
      readUploadHttp({ file }, row) {
        const randomString = this.generateRandomString(6);
        const fileName = `manage/${Date.parse(new Date()) + '_' + randomString}${file.name}`;
        const isReadRepeat = this.readFileList.find((e) => e.id == row.wordId).list.filter((f) => f.name === file.name).length > 0;
        if (isReadRepeat) {
          this.readFileList.find((e) => e.id == row.wordId).list = Array.from(new Set(this.readFileList.find((e) => e.id == row.wordId).list));
          return;
        }
        this.$nextTick(function () {
          if (ossPrClient() && ossPrClient().put) {
            ossPrClient()
              .put(fileName, file)
              .then(({ res, url, name }) => {
                if (res && res.status === 200) {
                  this.readFileList.find((e) => e.id == row.wordId).list = this.readtampFileList;
                  console.log(`上传音频3`, res, url, name);
                  this.readAudioStatus(file, url, row);
                  this.readUploadLoading = false;
                  this.$nextTick(() => {
                    this.readUploadLoading = false;
                  });
                }
              })
              .catch((err) => {
                this.readUploadLoading = false;
              });
          }
        });
      },
      readAudioStatus(file, url, row) {
        console.log(this.readWordList, 22222);
        this.readUploadLoading = true;
        let isWord = false;
        if (file && file.name) {
          this.readList.forEach((f) => {
            if (f.wordSyllable === file.name.split('.')[0] && f.word == row.word) {
              f.wordSyllableAudioUrl = url;
              if (f.wordSyllable == f.word) {
                isWord = true;
              }
            }
          });
          this.readWordList.push(...this.readList.filter((res) => res.wordSyllable === file.name.split('.')[0] && res.word === row.word));

          let arr = this.readList.filter((res) => res.word === row.word);
          row['readDisabled'] = !arr.every((item) => item['wordSyllableAudioUrl'] !== null && item['wordSyllableAudioUrl'] !== undefined && item['wordSyllableAudioUrl'] !== '');
          console.log(row['readDisabled'], isWord);

          if (!row['readDisabled']) {
            row['fullUrl'] = true;
          } else {
            row['fullUrl'] = false;
          }
        }
      },
      //元辅音保存
      onVcSave() {
        const loading = this.$loading({
          lock: true,
          text: 'Loading',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        // const {vowelConsonantList,syllableList,writeList,readList} = this.
        this.audioUrl = {
          vowelConsonantList: this.vowelConsonantList,
          syllableList: this.syllableList,
          writeList: this.oldWriteList,
          readList: this.oldReadList
        };
        courseDictationListApi
          .updateAllAudioUrl(this.audioUrl)
          .then((res) => {
            if (res.code === 20000) {
              loading.close();
              this.$message.success('保存成功');
              this.voweSave = true;
              this.vowelDisabled = true;
            } else {
              loading.close();
            }
          })
          .catch(() => {
            loading.close();
          });
      },
      //音节保存
      onSySave() {
        const loading = this.$loading({
          lock: true,
          text: 'Loading',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        this.audioUrl = {
          vowelConsonantList: this.vowelConsonantList,
          syllableList: this.syllableList,
          writeList: this.oldWriteList,
          readList: this.oldReadList
        };
        courseDictationListApi
          .updateAllAudioUrl(this.audioUrl)
          .then((res) => {
            if (res.code === 20000) {
              loading.close();
              this.$message.success('保存成功');
              this.syllableDisabled = true;
              this.syllableSave = true;
            } else {
              loading.close();
            }
          })
          .catch(() => {
            loading.close();
          });
      },
      // 拼读保存
      onPySave(row) {
        const loading = this.$loading({
          lock: true,
          text: 'Loading',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        let readList = [];
        readList = this.oldReadList.filter((item) => item.id === row.wordId);
        console.log(readList);

        this.readWordList.forEach((res) => {
          readList[0].voList.forEach((item) => {
            let a = item.wordSyllable.replace(/\[([^\]]+)\]/g, '');
            let b = res.wordSyllable.replace(/\w+\([^)]*\)/g, (match) => match.split('(')[0]);

            if (res.id === item.id && a === b) {
              item.wordSyllableAudioUrl = res.wordSyllableAudioUrl;
            }
          });
        });
        console.log(readList);

        readList[0].wordSyllableAudioUrl = this.readWordList.find((f) => f.wordSyllable === readList[0].wordSyllable)?.wordSyllableAudioUrl;
        for (let i = 0; i < this.oldReadList.length; i++) {
          if (this.oldReadList[i].id === row.wordId) {
            this.oldReadList[i] = readList[0];
          }
        }
        console.log(this.oldReadList);

        this.audioUrl = {
          vowelConsonantList: this.vowelConsonantList,
          syllableList: this.syllableList,
          writeList: this.oldWriteList,
          readList: this.oldReadList
        };
        courseDictationListApi
          .updateAllAudioUrl(this.audioUrl)
          .then((res) => {
            if (res.code === 20000) {
              loading.close();
              this.$message.success('保存成功');
              row.readDisabled = true;
            } else {
              loading.close();
            }
          })
          .catch(() => {
            loading.close();
          });
      },
      //拼写保存
      onWriteSave(row) {
        let writeList = [];
        writeList = this.oldWriteList.filter((item) => item.id === row.wordId);
        this.writeWordList.forEach((res) => {
          writeList[0].voList.forEach((item) => {
            if (res.wordSyllable === item.wordSyllable) {
              item.wordSyllableAudioUrl = res.wordSyllableAudioUrl;
            }
          });
        });
        writeList[0].wordSyllableAudioUrl = this.readWordList.find((f) => f.wordSyllable === writeList[0].wordSyllable)?.wordSyllableAudioUrl;
        this.audioUrl = {
          vowelConsonantList: this.vowelConsonantList,
          syllableList: this.syllableList,
          writeList: this.oldWriteList,
          readList: this.oldReadList
        };
        courseDictationListApi.updateAllAudioUrl(this.audioUrl).then((res) => {
          if (res.code === 20000) {
            this.$message.success('保存成功');
            row.writeDisabled = true;
          }
        });
      },
      readHandleSuccess(file, fileList, row) {
        // 处理上传成功后的逻辑
        row.uploadedFileName = file.name; // 更新已上传文件名
      },
      //拼读上传移除
      readHandleRemove(file, fileList, row) {
        this.readUploadLoading = true;
        if (file && file.name) {
          let arr = this.oldReadList.filter((item) => item.id === row.wordId)[0];
          if (arr.wordSyllableAudioUrl && arr.voList.findIndex((e) => !e.wordSyllableAudioUrl) == -1) {
          } else {
            this.readList.forEach((f) => {
              if (f.wordSyllable === file.name.split('.')[0] && row.word === f.word) {
                f.status = '';
                f.wordSyllableAudioUrl = '';
              }
            });
            row.readDisabled = true;
          }
        }
        this.readUploadLoading = false;

        this.readFileList.find((e) => e.id == row.wordId).list = this.readFileList.find((e) => e.id == row.wordId).list.filter((e) => e.name != file.name);
      },
      writeHandleRemove(file, fileList, row) {
        this.writeUploadLoading = true;
        if (file && file.name) {
          this.writeFileList
            .find((e) => e.id == row.wordId)
            .list.forEach((f) => {
              if (f.wordSyllable === file.name.split('.')[0]) {
                f.status = '';
                f.wordSyllableAudioUrl = '';
              }
            });
          row.writeDisabled = true;
        }
        this.writeUploadLoading = false;
        this.writeFileList.find((e) => e.id == row.wordId).list = this.writeFileList.find((e) => e.id == row.wordId).list.filter((e) => e.name != file.name);
      },
      // 音节音频上传
      async syllableUploadHttp({ file, fileList }, row) {
        const isRepeat = this.syllableAudioFileList.filter((f) => f.name === file.name).length > 0;
        if (isRepeat) {
          this.syllableAudioFileList = Array.from(new Set(this.syllableAudioFileList));
          return;
        }
        const fileName = `manage/${Date.parse(new Date())}${file.name}`;
        this.$nextTick(function () {
          if (ossPrClient() && ossPrClient().put) {
            ossPrClient()
              .put(fileName, file)
              .then(({ res, url, name }) => {
                if (res && res.status === 200) {
                  this.tampFileList.push({
                    name: file.name
                  });
                  this.tampFileList = this.uniqueObjectsByPropertyMap(this.tampFileList, 'name');
                  this.syllableAudioFileList = this.tampFileList;
                  this.syllableAudioStatus(file, url);
                  this.syllableUploadLoading = false;
                  this.$nextTick(() => {
                    this.syllableUploadLoading = false;
                  });
                }
              })
              .catch((err) => {
                this.syllableUploadLoading = false;
              });
          }
        });
      },
      uniqueObjectsByPropertyMap(arr, property) {
        const map = new Map();
        arr.forEach((item) => {
          const key = JSON.stringify(item[property]);
          map.set(key, item);
        });
        return Array.from(map.values());
      },
      syllableAudioStatus(file, url) {
        this.syllableUploadLoading = true;
        if (file && file.name) {
          this.syllableList.forEach((f) => {
            if (f.wordSyllable === file.name.split('.')[0]) {
              f.wordSyllableAudioUrl = url;
            }
          });
          this.syllableUploadLoading = false;
          this.syllableDisabled = !this.syllableList.every(
            (item) => item['wordSyllableAudioUrl'] !== null && item['wordSyllableAudioUrl'] !== undefined && item['wordSyllableAudioUrl'] !== ''
          );
        }
      },
      vowelUploadHttp({ file }) {
        const vowelIsRepeat = this.vowelAudioFileList.filter((f) => f.name === file.name).length > 0;
        if (vowelIsRepeat) {
          this.vowelAudioFileList = Array.from(new Set(this.vowelAudioFileList));
          return;
        }
        const fileName = `manage/${Date.parse(new Date())}${file.name}`;
        this.$nextTick(function () {
          if (ossPrClient() && ossPrClient().put) {
            ossPrClient()
              .put(fileName, file)
              .then(({ res, url, name }) => {
                if (res && res.status === 200) {
                  this.voweltampFileList.push({
                    name: file.name
                  });
                  this.voweltampFileList = this.uniqueObjectsByPropertyMap(this.voweltampFileList, 'name');
                  this.vowelAudioFileList = this.voweltampFileList;
                  console.log(`上传音频1`, res, url, name);
                  this.vowelAudioStatus(file, url);
                  this.vowelUploadLoading = false;
                  this.$nextTick(() => {
                    this.vowelUploadLoading = false;
                  });
                }
              })
              .catch((err) => {
                this.vowelUploadLoading = false;
                console.log(`阿里云OSS上传图片失败回调`, err);
              });
          }
        });
      },

      syllableHandleRemove(file, fileList) {
        this.syllableUploadLoading = true;
        if (file && file.name) {
          if (!this.syllableSave) {
            this.syllableList.forEach((f) => {
              if (f.wordSyllable === file.name.split('.')[0]) {
                f.status = '';
                f.wordSyllableAudioUrl = '';
              }
            });
            this.syllableDisabled = true;
          }
        }
        this.syllableUploadLoading = false;
        this.syllableAudioFileList = this.syllableAudioFileList.filter((e) => e.name != file.name);
        this.syllableAudioFileList = [...new Set(this.syllableAudioFileList)];
        // this.syllableAudioFileList = fileList;
        console.log('syllableAudioFileList', this.syllableAudioFileList);
      },
      vowelHandleRemove(file, fileList) {
        this.vowelUploadLoading = true;
        console.log(this.vowelConsonantList, '移除');
        if (file && file.name && !this.voweSave) {
          this.vowelConsonantList.forEach((f) => {
            if (f.wordSyllable === file.name.split('.')[0]) {
              f.status = '';
              f.wordSyllableAudioUrl = '';
            }
          });
        }
        this.vowelAudioFileList = this.vowelAudioFileList.filter((e) => e.name != file.name);
        this.vowelUploadLoading = false;
      },
      vowelAudioStatus(file, url) {
        this.vowelUploadLoading = true;
        if (file && file.name) {
          this.vowelConsonantList.forEach((f) => {
            if (f.wordSyllable === file.name.split('.')[0]) {
              f.wordSyllableAudioUrl = url;
            }
          });
          this.vowelUploadLoading = false;
          this.vowelDisabled = !this.vowelConsonantList.every(
            (item) => item['wordSyllableAudioUrl'] !== null && item['wordSyllableAudioUrl'] !== undefined && item['wordSyllableAudioUrl'] !== ''
          );
        }
      },
      //音频上传,移除状态显示
      audioStatus(file, status) {
        if (Array.isArray(file)) {
          if (status === 1) {
            this.uploadData.forEach((res) => {
              file.forEach((f) => {
                if (res.word === f.name.split('.')[0]) {
                  res.status = status;
                }
              });
            });
          }
        } else if (file.name) {
          console.log(file, '');
          this.uploadData.forEach((f) => {
            if (f.word === file.name.split('.')[0]) {
              f.status = status;
            }
          });
        }
      },
      //音频移除
      handleRemoveVideoWord(file, fileList) {
        this.audioStatus(file, 0);
        this.audioFileList.filter((f) => (f.name === file.name ? this.audioFileList.splice(this.audioFileList.indexOf(f), 1) : this.audioFileList));
      },
      isAudio(file) {
        return /\.(mp3|wav|mid|wma|ra|vqf|mov|amr)$/.test(file.name);
      },
      beforeWordVideoUpload() {},
      readBeforeUpload(file, fileList, row) {
        const arr = this.readList.filter((res) => res.word === row.word);
        const isFileName = arr.filter((f) => f.wordSyllable === file.name.split('.')[0])?.length > 0;

        const isRepeat = this.readFileList.find((e) => e.id == row.wordId).list.filter((f) => f.name === file.name).length > 0;

        if (!this.isAudio(file)) {
          this.$message.warning('只能上传音频文件！');
          return false;
        } else if (!isFileName) {
          this.$message.warning('只能上传检测库内此单词文件！');
          return false;
        } else if (isRepeat) {
          this.$message.warning('已经上传过该单词文件！');
          return;
        }
      },
      writeBeforeUpload(file, fileList, row) {
        const arr = this.writeList.filter((res) => res.word === row.word);
        const isFileName = arr.filter((f) => f.wordSyllable === file.name.split('.')[0])?.length > 0;
        const isRepeat = this.writeFileList.find((e) => e.id == row.wordId).list.filter((f) => f.name === file.name).length > 0;
        if (!this.isAudio(file)) {
          this.$message.warning('只能上传音频文件！');
          return false;
        } else if (!isFileName) {
          this.$message.warning('只能上传检测库内此单词文件！');
          return false;
        } else if (isRepeat) {
          this.$message.warning('已经上传过该单词文件！');
          return;
        }
      },
      syllableBeforeUpload(file) {
        const isFileName = this.syllableList.filter((f) => f.wordSyllable === file.name.split('.')[0]).length > 0;
        const isRepeat = this.syllableAudioFileList.filter((f) => f.name === file.name).length > 0;
        if (!this.isAudio(file)) {
          this.$message.warning('只能上传音频文件！');
          return false;
        } else if (!isFileName) {
          console.log(this.num, 222222222222222);
          this.$message.warning('只能上传检测库内单词文件！');
          return false;
        } else if (isRepeat) {
          this.$message.warning('已经上传过该单词文件！');
          return;
        }
      },
      //元辅音音频上传
      vowelBeforeUpload(file) {
        console.log(this.vowelConsonantList, '上传前');
        const isFileName = this.vowelConsonantList.filter((f) => f.wordSyllable === file.name.split('.')[0]).length > 0;
        const isRepeat = this.vowelAudioFileList.filter((f) => f.name === file.name).length > 0;
        if (!this.isAudio(file)) {
          this.$message.warning('只能上传音频文件！');
          return false;
        } else if (!isFileName) {
          this.$message.warning('只能上传检测库内单词文件！');
          return false;
        } else if (isRepeat) {
          this.$message.warning('已经上传过该单词文件！');
          return;
        }
      },
      handleChangeVowel(file, fileList) {
        // this.voweltampFileList = fileList;
      },
      videoHandleUpload(file, fileList, id) {
        this.readtampFileList = fileList;
        this.readtampFileList = this.uniqueObjectsByPropertyMap(this.readtampFileList, 'name');
      },
      writeHandleUpload(file, fileList) {
        this.writetampFileList = fileList;
      },
      //音节音频上传监听
      handleChangeSyllable(file, fileList) {
        // this.tampFileList = fileList;
      }
    }
  };
</script>
<style lang="less" scoped>
  .uploadAudio_table {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
  }
  ::v-deep .el-upload-list {
    width: auto !important;
  }
</style>
