<template>
  <div class="app-container">
    <div class="disposeBox1">
      <el-button @click="openInfo" type="primary" size="mini">
        查看详情
      </el-button>
      <el-button v-if="disposeShow" type="primary" icon="el-icon-plus" size="mini" @click="handleAdd">新增处理记录
      </el-button>
    </div>

    <el-table v-loading="loading" :data="noticeList" style="width: 100%;" @selection-change="handleSelectionChange">
      <el-table-column label="处理人" align="center" prop="dispose" />
      <el-table-column label="处理时间" align="center" prop="createTime" min-width="120" />
      <el-table-column label="内容" align="center" prop="content" width="220" :show-overflow-tooltip="true" />
      <el-table-column label="图片" align="center" prop="imager" width="100" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <el-image v-if="scope.row.imager && scope.row.imager.length > 0" class="imageList img"
            :src="scope.row.imager[0]" :preview-src-list="scope.row.imager">
          </el-image>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 新增处理记录对话框 -->
    <el-dialog title="新增处理记录" :visible.sync="open" width="620px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="姓名" prop="disposeName" width="100px">
              <el-input v-model="form.disposeName" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="处理内容" prop="content">
              <el-input type="textarea" :rows="4" v-model="form.content" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="上传图片" prop="imageList">
              <el-upload v-loading="uploadLoading" action="" list-type="picture-card" element-loading-text="图片上传中"
                :limit="10" :http-request="uploadImage" :on-preview="handlePicture" :on-remove="handleRemoveDetailIdCard">
                <i class="el-icon-plus" />
              </el-upload>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog :visible.sync="uploadVisible">
      <img width="100%" :src="imageUrl" alt="">
    </el-dialog>

  </div>
</template>

<script>
import riskApi from "@/api/risk/risk";
import pagination from '@/components/Pagination/index'
import { ossPrClient } from "@/api/alibaba";

export default {
  name: "riskDispose",
  components: {
    pagination
  },
  props: {
    riskId: {
      type: String,
      default: 1
    },
    disposeShow: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 公告表格数据
      noticeList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        riskId: undefined
      },
      // 表单参数
      form: {
        imageList: []
      },
      // 表单校验
      rules: {
        disposeName: [
          { required: true, message: "姓名不能为空", trigger: "blur" }
        ]
      },
      uploadLoading: false,
      imageUrl: undefined,
      uploadVisible: undefined,
      fileDetailList: []
    };
  },
  created() {
    ossPrClient();
    this.getList();
  },
  methods: {
    /** 查询风控处理记录列表 */
    getList() {
      this.loading = true;
      this.queryParams.riskId = this.riskId;
      riskApi.disposeListPage(this.queryParams).then(response => {
        this.noticeList = response.data.data;
        for (let i = 0; i < this.noticeList.length; i++) {
          let element = this.noticeList[i];
          if (element.imager) {
            element.imager = element.imager.split(",")
            for (let j = 0; j < element.imager.length; j++) {
              element.imager[j] = this.aliUrl + element.imager[j];
            }
          } else {
            element.imager = null
          }
        }
        this.total = Number(response.data.totalItems);
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        noticeId: undefined,
        noticeTitle: undefined,
        noticeType: undefined,
        noticeContent: undefined,
        status: "0"
      };
      if (this.$refs['form']) {
        this.$refs['form'].resetFields();
      }
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.noticeId)
      this.single = selection.length != 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      riskApi.getRiskInfo(row.id).then(response => {
        this.form = response.data;
        this.open = true;
      });
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate(valid => {
        if (valid) {
          let img = undefined;
          if (this.fileDetailList.length > 0) {
            img = this.fileDetailList[0].url
            if (this.fileDetailList.length > 1) {
              for (let i = 1; i < this.fileDetailList.length; i++) {
                img = img + "," + this.fileDetailList[i].url
              }
            }
          }
          this.form.riskId = this.riskId
          this.form.imageList = img
          riskApi.addDisposeRecord(this.form).then(res => {
            this.$message.success("添加成功");
            this.open = false;
            this.getList();
          })
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const noticeIds = row.noticeId || this.ids
      /*      this.$modal.confirm('是否确认删除公告编号为"' + noticeIds + '"的数据项？').then(function() {
              return delNotice(noticeIds);
            }).then(() => {
              this.getList();
              this.$modal.msgSuccess("删除成功");
            }).catch(() => {});*/
    },

    uploadImage({ file }) {

      this.uploadLoading = true
      const fileName = 'manage/' + Date.parse(new Date())
      ossPrClient().put(fileName, file).then(({ res, url, name }) => {
        if (res && res.status === 200) {
          this.fileDetailList.push({
            uid: file.uid,
            url: name
          })
          this.uploadLoading = false
        }
      }).catch(err => {
        this.$message.error('上传图片失败请检查网络或者刷新页面')
        console.log(`阿里云OSS上传图片失败回调`, err)
        this.uploadLoading = false
      })
    },
    // 上传图片预览
    handlePicture(file) {
      this.imageUrl = file.url
      this.uploadVisible = true
    },
    // 删除上传图片
    handleRemoveDetailIdCard(file, fileList) {
      for (let i = 0; i < this.fileDetailList.length; i++) {
        if (this.fileDetailList[i].uid === file.uid) {
          this.fileDetailList.splice(i, 1)
        }
      }
    },
    openInfo() {
      this.$emit("riskOpenInfo", this.riskId)
    }
  }
};
</script>
<style lang="scss" scoped>
.disposeBox1 {
  display: flex;
  align-items: center;
  //justify-content: space-between;
  margin-bottom: 20px;
}

.imageList {
  width: 60px;
  height: 30px;

  ::v-deep .el-icon-circle-close:before {
    color: red !important;
  }
}
</style>
