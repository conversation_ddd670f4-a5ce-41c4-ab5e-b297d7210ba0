
import request from '@/utils/request'

export default {

  // 分页查询
  schedulePlanList(pageNum, pageSize, data) {
    return request({
      url: '/studyroom/webSchedulePlan/list/' + pageNum + '/' + pageSize,
      method: 'GET',
      params: data
    })
  },
  //修改状态
  updateEnable(id,enable){
    return request({
      url: '/studyroom/webSchedulePlan/changeEnable',
      method: 'PUT',
      params:{
        planId:id,
        isEnable:enable
      }
    })
  },
  //添加
  addPlan(data){
    return request({
      url: '/studyroom/webSchedulePlan/addPlan',
      method: 'POST',
      data
    })
  },
  //详情
  planDetail(id){
    return request({
      url: '/studyroom/webSchedulePlan/planDetail',
      method: 'GET',
      params:{
        planId:id
      }
    })
  },
  editPlan(data){
    return request({
      url: '/studyroom/webSchedulePlan/editPlan',
      method: 'PUT',
      data
    })
  },
}
