<template>
  <div class="app-container">
    <el-form :inline="true" ref="queryForm" class="container-card" label-width="110px">
      <el-form-item label="">
        <el-input v-model="dataQuery.title" placeholder="请输入搜索关键词">
          <i class="el-icon-search el-input__icon" slot="prefix">
          </i>
        </el-input>

      </el-form-item>
      <el-form-item label="">
        <el-input v-model="dataQuery.nickname" placeholder="用户名">
        </el-input>
      </el-form-item>
      <el-form-item label="板块分类">
        <el-select v-model="dataQuery.plateId" @change="idChange" placeholder="请选择">
          <el-option v-for="item in post_options" :key="item.id" :label="item.name" :value="item.id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-re" @click="resetQuery()">重置</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="search()">搜索</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="open_user">推荐用户</el-button>
      </el-form-item>
    </el-form>
    <el-col :span="24" style="margin-bottom: 30px">
      <el-dropdown>
        <span class="el-dropdown-link">
          <el-button type="primary" icon="el-icon-arrow-bottom">批量操作</el-button>
        </span>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item @click.native="allRecommend">推荐</el-dropdown-item>
          <el-dropdown-item @click.native="allMove">移动板块</el-dropdown-item>
          <el-dropdown-item @click.native="allDelete">删除</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </el-col>

    <el-table class="common-table" v-loading="tableLoading" :data="tableData" style="width: 100%;margin-bottom: 20px;"
      row-key="id" border default-expand-all @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55">
      </el-table-column>
      <el-table-column prop="name" label="发帖人">
        <template slot-scope="scope">
          <div class="input_title">
            <span @click="taboo(scope.row.nickname, scope.row.studentCode)">{{ scope.row.nickname }}</span>
            <div class="input_title_bot">
              <span>{{ scope.row.name }} </span>
              <span> Lv.{{ scope.row.level }}</span>
            </div>
          </div>
        </template>
      </el-table-column>


      <el-table-column prop="name" label="帖子管理" width="450px">
        <template slot-scope="scope">
          <div class="post_title">
            <div class="post_head">
              <span class="post_h1">{{ scope.row.title }}</span>
              <div class="post_title_bot">
                <span>{{ contentData.imagePath }} </span>
              </div>
            </div>
            <el-image v-if="scope.row.mainPhoto" style="width: 60px; height: 50px" :src="scope.row.mainPhoto"
              :preview-src-list="[scope.row.mainPhoto]">
            </el-image>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="withdrawnBonus" label="操作">
        <template slot-scope="scope">
          <el-dropdown>
            <span class="el-dropdown-link" style="color:#108EE9">
              <el-button type="text">更多<i class="el-icon-arrow-down" /></el-button>
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item
                @click.native="postrecommend(scope.row.id, scope.row.isRecommend)">{{ scope.row.isRecommend }}</el-dropdown-item>
              <el-dropdown-item
                @click.native="posttopping(scope.row.id, scope.row.isTop)">{{ scope.row.isTop }}</el-dropdown-item>
              <el-dropdown-item @click.native="movepost(scope.row.id)">移动</el-dropdown-item>
              <el-dropdown-item @click.native="singleDelete(scope.row.id)">删除</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>

      <el-table-column prop="plateName" label="所属板块" show-overflow-tooltip></el-table-column>

      <el-table-column prop="orderNum" label="指标">
        <template slot-scope="scope">
          <div class="index_title">
            <div>
              <i class="el-icon-view blue" title="阅读量"></i>
              <span>{{ scope.row.readNum }}</span>
            </div>
            <div>
              <i class="el-icon-tickets green" title="评论量"></i>
              <span>{{ scope.row.commentNum }}</span>
            </div>
            <div>
              <i class="el-icon-thumb red" title="点赞量"></i>
              <span>{{ scope.row.likeNum }}</span>
            </div>
            <div>
              <i class="el-icon-star-off yellow" title="收藏量"></i>
              <span>{{ scope.row.collectNum }}</span>
            </div>

          </div>
        </template>

      </el-table-column>

      <el-table-column prop="status" label="状态" show-overflow-tooltip>
        <template slot-scope="scope">
          <div class="index_title">
            <div v-if="scope.row.status === 1">
              <span class="green">正常</span>
            </div>
            <div v-else-if="scope.row.status === 2">
              <span class="yellow">待审核<el-button style="margin-left: 10px" type="text"
                  @click="auditAdopt(scope.row.id)">审核通过</el-button></span>
            </div>
            <div v-else-if="scope.row.status === 3">
              <span class="red">已删除<el-button style="margin-left: 10px" type="text"
                  @click="recovery(scope.row.id)">恢复</el-button></span>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="发帖时间" show-overflow-tooltip></el-table-column>
    </el-table>

    <!-- 分页 -->
    <el-col :span="24" style="margin-bottom: 20px">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>
    <!-- 移动窗口 -->
    <el-dialog title="移动" :visible.sync="post_open" width="70%" @close="close">
      <el-form ref="form" :model="form" :rules="rules" style="width: 70%;">
        <el-form-item label="需要将帖子移动至" prop="name">
          <el-select v-model="form.danGradingId" @change="idChange" placeholder="请选择">
            <el-option v-for="item in post_options" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitMove">确 定</el-button>
        <el-button @click="post_open = false">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 推荐用户 -->
    <el-dialog title="推荐用户" :visible.sync="recommended_Users" width="70%" @close="close">
      <el-col :span="24" style="margin-bottom: 30px">
        <el-button type="success" icon="el-icon-plus" size="mini" @click="addBtn()">添加</el-button>
      </el-col>
      <el-table class="common-table" v-loading="user_tableLoading" :data="user_tableData"
        style="width: 100%;margin-bottom: 20px;" height="250" row-key="id" default-expand-all>
        <el-table-column prop="orderNum" type="index" label="序号" width="50"></el-table-column>
        <el-table-column prop="nickname" label="用户名称"></el-table-column>
        <el-table-column prop="" label="操作" show-overflow-tooltip>.
          <template slot-scope="scope">
            <span @click="no_recommend(scope.row.id)" style="color:rgb(16, 142, 233)">取消推荐</span>
          </template>
        </el-table-column>
        <el-table-column prop="recommendReason" label="推荐语"></el-table-column>
        <el-table-column prop="" label="排序" show-overflow-tooltip>
          <i class="el-icon-rank"></i>
        </el-table-column>
      </el-table>

    </el-dialog>

    <!-- 禁言窗口 -->
    <el-dialog title="用户管理" :visible.sync="isTaboo" width="70%" @close="close">
      <el-form ref="form" :model="userMangent" :rules="rules" style="width: 70%;margin: 0 auto;" label-width="110px">
        <el-form-item label="用户名称：" prop="name" style="display: inline-block;">
          <el-input readonly="readonly" v-model="userMangent.name" />
        </el-form-item>
        <el-form-item label="学号：" prop="studentCode" style="display: inline-block;">
          <el-input readonly="readonly" v-model="userMangent.studentCode" />
        </el-form-item>
        <el-form-item label="禁言：" prop="name">
          <el-select v-model="userMangent.bannedDays" placeholder="请选择">
            <el-option v-for="item in tatoo_options" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="" prop="name">
          <span style="color:#aaa;">被禁言的用户，将无法在社区中发帖，评论，点赞操作</span>
        </el-form-item>
        <el-form-item>
          <el-table class="common-table" v-loading="taboo_tableLoading" :data="taboo_tableData"
            style="width: 100%;margin-bottom: 20px;" height="250" row-key="id" default-expand-all>
            <el-table-column prop="startTime" label="禁言日期"></el-table-column>
            <el-table-column prop="bannedDays" label="禁言天数"></el-table-column>
            <el-table-column label="操作">
              <template slot-scope="scope">
                <el-button v-if="scope.row.isShowRelieveBan" type="text" @click="unban(scope.row.id)">解除禁言</el-button>
              </template>
            </el-table-column>
            <el-table-column prop="endTime" label="解封日期" show-overflow-tooltip></el-table-column>
            <el-table-column prop="isShowRelieveBan" label="状态">
              <template slot-scope="scope">
                <span v-if="!scope.row.isShowRelieveBan" class="green">已解除</span>
                <span v-else class="red">禁言中</span>
              </template>
            </el-table-column>
          </el-table>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitTaboo">确 定</el-button>
        <el-button @click="isTaboo = false">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 添加推荐用户 -->
    <el-dialog title="搜索用户" :visible.sync="open" width="70%" @close="close">
      <el-form ref="form" :model="addrecommended_user" :rules="rules" label-width="120px" style="width: 70%;">
        <el-form-item label="姓名：" prop="name">
          <el-autocomplete class="inline-input" v-model="state2" :fetch-suggestions="querySearch" placeholder="请输入内容"
            :trigger-on-focus="false" @select="handleSelect" @input="changeData"></el-autocomplete>
        </el-form-item>
        <el-form-item label="推荐语：" prop="recommendReason">
          <el-input v-model="addrecommended_user.recommendReason" maxlength="6" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitAdduser">确 定</el-button>
        <el-button @click="open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import taskApi from '@/api/xi/xiTask'
import postApi from '@/api/xi/community/post'
import { pageParamNames } from '@/utils/constants'
import platetApi from '@/api/xi/community/plate'
import { mapGetters, mapState } from 'vuex'

export default {
  name: 'post',
  data() {
    return {
      restaurants: [],
      state2: '',
      recommended_Users: false,
      tatoo_options: [{
        id: '3',
        name: '3天'
      }, {
        id: '7',
        name: '7天'
      }, {
        id: '30',
        name: '30天'
      }, {
        id: '999999',
        name: '永久'
      }],
      userMangent: {},
      // 禁言窗口
      isTaboo: false,
      post_list: '',
      post_options: '',
      post_open: false,
      dataQuery: {
        enable: null,
        name: ''
      },
      userQuery: {
        enable: null,
        name: ''
      },
      taboo_tableLoading: false,
      user_tableLoading: false,
      tableLoading: false,
      // 分页1
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      // 分页2
      tablePage2: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      open: false,
      form: {},
      addrecommended_user: {},
      // 表单校验
      rules: {
        explainInfo: [{ required: true, message: '请输入推荐语', trigger: 'blur' }],
        studyDay: [{ required: true, message: '请选择', trigger: 'blur' }],
        restDay: [{ required: true, message: '请选择', trigger: 'blur' }]
      },
      tableData: [],
      taboo_tableData: [],
      user_tableData: [],
      contentData: []
    }
  },
  created() {
    this.getPageList()
    this.plateList()
  },
  mounted() {
    // this.restaurants = this.tatoo_options();

  },
  methods: {
    recovery(id) {
      this.$confirm('确认恢复帖子?', '恢复', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        postApi.recovery(id).then(res => {
          if (res.success) {
            this.$message.success('恢复成功！')
            this.getPageList();
          }
        }).catch(err => { })
      }).catch(err => { })
    },
    //解除禁言
    unban(id) {
      this.$confirm('确认解除禁言?', '解除禁言', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        postApi.unban(id).then(res => {
          if (res.success) {
            this.$message.success('解除禁言完成！')
            taboo();
          }
        }).catch(err => { })
      }).catch(err => { })
    },
    auditAdopt(id) {
      this.$confirm('确认审核通过?', '审核', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        postApi.auditAdopt(id).then(res => {
          if (res.success) {
            this.$message.success('审核通过！')
            this.getPageList();
          }
        }).catch(err => { })
      }).catch(err => { })
    },
    submitAdduser() {
      postApi.nickname(this.state2).then(res => {
        this.restaurants = res.data.map(item => {
          return {
            value: item.nickname,
            label: item.studentCode
          }
        })
        if (res.data.length < 1) {
          this.$message.warning('该用户不存在')
        } else {
          this.$refs['form'].validate(valid => {
            if (valid) {
              postApi.userRecommend(this.addrecommended_user.studentCode, this.addrecommended_user.recommendReason).then(response => {
                this.$message.success('提交成功！')
                this.open = false
                this.user_list()
              })
            }
          })
        }
      })

    },
    handleSelect(item) {
      this.state2 = item.value
      this.select = item.label
      this.addrecommended_user.studentCode = item.label
      console.log(item)
    },
    handleChange() {

      postApi.nickname(this.state2).then(res => {
        this.restaurants = res.data.map(item => {
          return {
            value: item.nickname,
            label: item.studentCode
          }
        })
        if (res.data.length < 1) {
        }
      })
    },

    querySearch(queryString, cb) {
      var restaurants = this.restaurants;
      var results = queryString ? restaurants.filter(this.createStateFilter(queryString)) : restaurants;
      // 调用 callback 返回建议列表的数据
      cb(results);
      console.log(queryString, cb)
    },

    createStateFilter(queryString) {
      return (state) => {
        return (state.value.toLowerCase().indexOf(queryString.toLowerCase()) > -1);
      };
    },
    changeData(value) {
      if (value != '') {
        this.handleChange()
      } else {
        this.image = ''
        this.select = ""
      }
    },
    // 取消推荐
    no_recommend(id) {
      this.$confirm('确认取消推荐?', '推荐', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        postApi.userUnRecommend(id).then(res => {
          this.$message.success('取消成功!')
          this.user_list()
        }).catch(err => {

        })
      }).catch(err => {

      })
    },
    user_list() {
      this.userQuery.pageNum = 1
      this.userQuery.pageSize = 10
      postApi.userList(this.userQuery).then(res => {
        this.user_tableData = res.data.data
        this.user_tableLoading = false
        console.log(res)
        // 设置后台返回的分页参数
        pageParamNames.forEach(name =>
          this.$set(this.tablePage, name, parseInt(res.data[name]))
        )
      })
    },
    open_user() {
      this.recommended_Users = true
      this.user_list()
    },
    // 提交禁言
    submitTaboo() {
      if (!this.userMangent.bannedDays) {
        this.$message.warning("请选择禁言天数");
        return
      }
      this.$refs['form'].validate(valid => {
        if (valid) {
          postApi.subtaboo(this.userMangent.studentCode, this.userMangent.name, this.userMangent.bannedDays).then(response => {
            this.$message.success('提交成功！')
            postApi.detail(this.userMangent.studentCode).then(res => {
              if (res.data[0].bannedDays == "999999") {
                res.data[0].bannedDays = '永久'
              }
              this.taboo_tableData = res.data
              this.taboo_tableLoading = false
              // 设置后台返回的分页参数
              pageParamNames.forEach(name =>
                this.$set(this.tablePage01, name, parseInt(res.data[name]))
              )
            })
          })
        }
      })
    },

    taboo(name, studentCode) {
      this.isTaboo = true
      this.userMangent.name = name
      this.userMangent.studentCode = studentCode

      postApi.detail(studentCode).then(res => {
        if (res.data[0].bannedDays == 999999) {
          res.data[0].bannedDays = "永久"
        }
        this.taboo_tableData = res.data
        this.taboo_tableLoading = false
        // // 设置后台返回的分页参数
        pageParamNames.forEach(name =>
          this.$set(this.tablePage2, name, parseInt(res.data[name]))
        )
      })

    },
    // 批量推荐
    allRecommend() {
      if (this.post_list.length >= 1) {
        let arr = []
        this.post_list.forEach(element => {
          arr.push(element.id)
        });
        let isRecommend = true
        console.log(arr)
        this.$confirm('确定将该贴加入推荐?', '推荐', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          postApi.recommend(arr, isRecommend).then(res => {
            this.$message.success('推荐成功!')
            this.getPageList()
          }).catch(err => {

          })
        }).catch(err => {

        })
      } else {
        this.$message({
          message: '请至少选择一个帖子',
          type: 'warning'
        });
      }
    },
    // 批量移动板块
    allMove() {
      if (this.post_list.length >= 1) {
        this.post_open = true;
      } else {
        this.$message({
          message: '请至少选择一个帖子',
          type: 'warning'
        });
      }
    },
    // 批量删除
    allDelete() {
      if (this.post_list.length >= 1) {
        let arr = []
        console.log(this.post_list)
        for (let index = 0; index < this.post_list.length; index++) {
          arr.push(this.post_list[index].id);
        }
        console.log(arr)
        this.$confirm('确认删除?', '删除', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          postApi.delete(arr).then(res => {
            this.$message.success('删除成功!')
            this.getPageList()
          }).catch(err => {

          })
        }).catch(err => {

        })
      } else {
        this.$message({
          message: '请至少选择一个帖子',
          type: 'warning'
        });
      }

    },
    // 帖子推荐
    postrecommend(id, isRecommend) {
      if (isRecommend == "推荐") {
        isRecommend = true
      } else {
        isRecommend = false
      }
      let arr = []
      arr.push(id)
      this.$confirm('确定将该贴加入推荐?', '推荐', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        postApi.recommend(arr, isRecommend).then(res => {
          if (isRecommend) {
            this.$message.success('推荐成功!')
          } else {
            this.$message.success('取消成功!')
          }
          this.getPageList()
        }).catch(err => {

        })
      }).catch(err => {

      })
    },
    // 帖子置顶
    posttopping(id, isTop) {
      if (isTop == "置顶") {
        isTop = true
      } else {
        isTop = false
      }
      this.$confirm('确认将该贴置顶?', '置顶', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        postApi.topping(id, isTop).then(res => {
          if (isTop) {
            this.$message.success('置顶成功!')
          } else {
            this.$message.success('取消成功!')
          }
          this.getPageList()
        }).catch(err => {

        })
      }).catch(err => {

      })
    },
    // 确认移动
    submitMove() {
      let ids = []
      if (this.post_list.length >= 1) {
        for (let index = 0; index < this.post_list.length; index++) {
          ids.push(this.post_list[index].id)
        }
      } else {
        ids.push(this.form.id)
      }
      let plateId = this.form.danGradingId
      let obg = { ids, plateId }
      console.log(obg)
      this.$refs['form'].validate(valid => {
        if (valid) {
          postApi.move(ids, plateId).then(response => {
            this.$message.success('提交成功！')
            this.post_open = false
            this.getPageList()
          })
        }
      })
    },
    handleSelectionChange(val) {
      this.post_list = val
      console.log(val)
    },
    plateList() {
      this.dataQuery.pageNum = this.tablePage.currentPage
      this.dataQuery.pageSize = this.tablePage.size
      this.dataQuery.type = 1
      platetApi.list(this.dataQuery).then(res => {
        this.post_options = res.data.data
        // 设置后台返回的分页参数
        pageParamNames.forEach(name =>
          this.$set(this.tablePage, name, parseInt(res.data[name]))
        )
      })
    },
    idChange(val) {
      console.log(val)
    },
    // 单个移动
    movepost(id) {
      this.form = {}
      this.form.id = id;
      this.post_open = true;
      this.plateList()
    },
    // 单个删除
    singleDelete(id) {
      const that = this;
      let arr = []
      arr.push(id)
      console.log(id)
      this.$confirm('确定操作吗?', '删除状态', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        postApi.delete(arr).then(res => {
          that.$nextTick(() => that.fetchData())
          that.$message.success('删除成功!')
          this.getPageList()
        }).catch(err => {

        })
      }).catch(err => {

      })
    },
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      // if(columnIndex){
      //   return {
      //     rowspan: 2,
      //     colspan: 1
      //   }
      // }
      // if (columnIndex === 0) {
      //   if (rowIndex % 2 === 0) {
      //     return {
      //       rowspan: 2,
      //       colspan: 1
      //     };
      //   } else {
      //     return {
      //       rowspan: 0,
      //       colspan: 0
      //     };
      //   }
      // }
    },
    search() {
      this.tablePage = {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      }
      this.getPageList();
    },
    addBtn() {
      this.reset()
      this.open = true
    },
    editBtn(id) {
      this.reset()
      taskApi.detail(id).then(res => {
        this.form = res.data
        this.open = true
      })
    },
    changeEnable(row) {
      let msg = row.enable ? '停用' : '启用'
      let enable = row.enable ? 0 : 1
      this.$confirm('确定要' + msg + '吗?', '修改状态', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        taskApi.changeEnable(row.id, enable).then(res => {
          this.$nextTick(() => this.getPageList())
          this.$message.success('修改成功!')
        }).catch(err => {

        })
      }).catch(err => {

      })
    },
    submitForm() {
      console.log(this.form)
      this.$refs['form'].validate(valid => {
        if (valid) {
          taskApi.saveOrUpdate(this.form).then(response => {
            this.$message.success('提交成功！')
            this.open = false
            this.getPageList()
          })
        }
      })
    },

    getPageList() {
      this.tableLoading = true
      this.dataQuery.pageNum = this.tablePage.currentPage
      this.dataQuery.pageSize = this.tablePage.size
      postApi.list(this.dataQuery).then(res => {
        this.tableData = res.data.data
        this.tableData.map(item => {
          if (item.isTop) {
            item.isTop = "取消置顶"
          } else {
            item.isTop = "置顶"
          }
          if (item.isRecommend) {
            item.isRecommend = "取消推荐"
          } else {
            item.isRecommend = "推荐"
          }
          if (item.content != '') {
            this.contentData.push(JSON.parse(item.content))
            if (!item.mainPhoto) {
              item.mainPhoto = JSON.parse(item.content)[0].imagePath;
            }
          }
        })
        this.tableLoading = false
        // 设置后台返回的分页参数
        pageParamNames.forEach(name =>
          this.$set(this.tablePage, name, parseInt(res.data[name]))
        )
      })
    },
    handlePage() {
      (this.tablePage = {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      }),
        this.getPageList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dataQuery = {
        name: '',
        enable: null
      }
      this.getPageList()
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val
      this.getPageList()
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val
      this.getPageList()
    },
    close() {
      this.open = false
      this.reset()
    },
    reset() {
      this.form = {
        name: '',
        explainInfo: '',
        orderNum: 1,
        studyDay: true,
        restDay: true
      }
      this.taboo_tableData = [];
      if (this.$refs['form']) {
        this.$refs['form'].resetFields()
      }
    }
  }
}
</script>

<style>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.period-table td,
.period-table th {
  text-align: center;
}

@media (max-width: 767px) {
  .el-message-box {
    width: 80% !important;
  }
}

td input {
  border: none;
}

.el-tooltip__popper {
  max-width: 800px;
}

.input_title_bot span {
  font-size: 11px;
  color: #000 !important;
}

.input_title {
  text-align: left;
}

.input_title span {
  color: #409eff;
}

.post_title {
  text-align: left;
  display: flex;
}

.post_title span {
  font-weight: bold;
  font-size: 15px;
}

.el-image-viewer__close {
  font-weight: bold;
  font-size: 35px !important;
  color: #fff;

}

.post_title .post_h1 {
  white-space: nowrap;
  /*强制单行显示*/
  text-overflow: ellipsis;
  /*超出部分省略号表示*/
  overflow: hidden;
  /*超出部分隐藏*/
  width: 260px;
  /*设置显示的最大宽度*/
  display: inline-block;
}

.post_title_bot span {
  font-weight: normal;
  font-size: 13px;
}

.post_title_bot span:first-child {
  white-space: nowrap;
  /*强制单行显示*/
  text-overflow: ellipsis;
  /*超出部分省略号表示*/
  overflow: hidden;
  /*超出部分隐藏*/
  width: 360px;
  /*设置显示的最大宽度*/
  display: inline-block;
}

.index_title {
  display: flex;
  justify-content: space-around;
}

.index_title div {
  text-align: center;
}

.index_title span {
  display: block;
}
</style>
