<template>
  <el-card class="process-card">
    <div slot="header" class="card-header">
      <div class="card-title">
        <i class="el-icon-video-play" style="color: #bbbbbb"></i>
        <span style="margin-left: 6px">视频播放</span>
      </div>
      <el-button type="text" class="delete-button" icon="el-icon-delete" @click="$emit('delete')"></el-button>
    </div>
    <el-form ref="videoPlayForm" :model="formData" label-width="100px" :rules="rules" class="process-form-content">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="排序:" prop="sortsNum">
            <el-input-number v-model="formData.sortsNum" :min="1"></el-input-number>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="阶段时长:" prop="stageDuration">
            <el-select v-model="formData.stageDuration" placeholder="请选择阶段时长">
              <el-option v-for="i in 60" :key="i" :label="i" :value="i"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="阶段名称:" prop="stageName">
            <el-input v-model="formData.stageName" placeholder="请输入阶段名称" maxlength="10" show-word-limit></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="阶段提醒:" prop="solveStageRemind">
            <el-radio-group v-model="formData.solveStageRemind">
              <el-radio label="1">阶段结束时需要弹框提醒</el-radio>
              <el-radio label="0">阶段结束时不做提醒</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="选择视频" prop="courseProcessVideoCoList">
            <span style="color: #646d7b; font-size: 12px; margin-right: 10px">最多支持添加8个视频</span>
            <el-button type="primary" size="mini" @click="openVideoDialog">添加</el-button>
            <div class="">
              <el-table :data="formData.courseProcessVideoCoList" ref="table" size="small" style="width: 100%" row-key="id">
                <el-table-column type="index" label="序号" width="60" align="center"></el-table-column>
                <el-table-column prop="videoId" label="视频ID" width="" align="center"></el-table-column>
                <el-table-column prop="videoName" label="视频名称" align="center"></el-table-column>
                <el-table-column prop="courseVersionNodeName" label="版本" align="center"></el-table-column>
                <el-table-column prop="coursePeriodNodeName" label="学段" align="center"></el-table-column>
                <el-table-column prop="coursePeriodNodeName" label="操作" align="center">
                  <template slot-scope="scope">
                    <el-button size="mini" @click="moveUp(scope.$index)" :disabled="scope.$index === 0">上移</el-button>
                    <el-button size="mini" @click="moveDown(scope.$index)" :disabled="scope.$index === formData.courseProcessVideoCoList.length - 1">下移</el-button>
                    <el-button type="text" size="small" @click="removeVideo(scope.row)">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <!-- 添加视频弹框组件 -->
    <add-video-dialog ref="addVideoDialog" @confirm="handleVideoConfirm" :selectedVideoList="formData.courseProcessVideoCoList"></add-video-dialog>
  </el-card>
</template>

<script>
  import AddVideoDialog from './components/addVideoDialog.vue';
  export default {
    name: 'VideoComponent',
    components: {
      AddVideoDialog
    },
    props: {
      index: {
        type: Number,
        required: true
      }
    },
    data() {
      return {
        rules: {
          stageDuration: [{ required: true, message: '请选择阶段时长', trigger: 'change' }],
          stageName: [{ required: true, message: '请输入阶段名称', trigger: 'blur' }],
          solveStageRemind: [{ required: true, message: '请选择阶段提醒方式', trigger: 'change' }],
          courseProcessVideoCoList: [{ required: true, message: '请选择视频', trigger: 'change' }]
        }
      };
    },
    watch: {
      formData: {
        handler(newVal) {
          // 当表单数据变化时，通知父组件
          this.$emit('update', newVal);
        },
        deep: true,
        immediate: true
      }
    },
    props: {
      formData: {
        type: Object,
        default: () => ({
          stageDuration: '',
          stageName: '',
          solveStageRemind: '',
          courseProcessVideoCoList: []
        })
      }
    },
    methods: {
      moveUp(index) {
        if (index === 0) return;
        const data = JSON.parse(JSON.stringify(this.formData.courseProcessVideoCoList));
        [data[index - 1], data[index]] = [data[index], data[index - 1]];
        console.log(data);
        this.formData.courseProcessVideoCoList = data;
      },
      moveDown(index) {
        const data = JSON.parse(JSON.stringify(this.formData.courseProcessVideoCoList));

        if (index === data.length - 1) return;
        [data[index + 1], data[index]] = [data[index], data[index + 1]];
        console.log(data);
        this.formData.courseProcessVideoCoList = data;
      },
      // 打开视频选择弹框
      openVideoDialog() {
        console.log('打开视频选择弹框', this.formData.courseProcessVideoCoList);
        if (this.formData.courseProcessVideoCoList && this.formData.courseProcessVideoCoList.length < 8) {
          // this.$refs.addVideoDialog.videoDialogVisible = true;
          // this.$refs.addVideoDialog.getVideoList();
          this.$refs.addVideoDialog.open();
        } else {
          this.$message.warning('视频数量不能超过8个');
        }
      },

      // 处理视频选择确认
      handleVideoConfirm(selectedVideos) {
        console.log(selectedVideos, '2222222222222222222222222222');
        let newArr = JSON.parse(JSON.stringify(selectedVideos));
        // 直接使用选中的视频列表替换现有列表
        newArr.forEach((item) => {
          console.log(item, '123333333333333333333333333333');
          item.videoId = item.videoId ? item.videoId : item.id;
        });
        this.formData.courseProcessVideoCoList = newArr;
      },

      // 移除视频
      removeVideo(row) {
        this.formData.courseProcessVideoCoList = this.formData.courseProcessVideoCoList.filter((i) => i.id != row.id);
      },

      // 表单校验方法
      validate() {
        return new Promise((resolve, reject) => {
          this.$refs.videoPlayForm.validate((valid) => {
            if (valid) {
              resolve(true);
            } else {
              reject(false);
            }
          });
        });
      }
    }
  };
</script>

<style lang="less" scoped>
  .process-card {
    margin-top: 10px;
    margin-bottom: 20px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0;
  }

  .card-title {
    display: flex;
    align-items: center;
    font-size: 14px;
    font-weight: bold;
  }

  .delete-button {
    padding: 0;
    font-size: 20px;
    color: #2a2a3b;
  }

  .process-form-content {
    padding: 0 20px 0 0;
  }

  /deep/ .el-card__header {
    padding: 5px 20px;
  }

  .process-card /deep/ .el-form-item {
    margin-bottom: 12px;
  }

  .process-card /deep/ .el-select {
    width: 100%;
  }

  .tips {
    margin-top: 5px;
  }

  .videoMent {
    width: 100px;
    height: 100px;
    line-height: 100px;
    text-align: center;
    border: 1px solid #dcdfe6;
    cursor: pointer;
  }

  .video-dialog {
    :deep(.el-dialog__header) {
      padding: 15px 20px;
      border-bottom: 1px solid #ebeef5;
      text-align: center;
    }

    :deep(.el-dialog__title) {
      font-size: 16px;
      font-weight: 500;
      color: #303133;
    }

    :deep(.el-dialog--center .el-dialog__body) {
      padding: 10px 25px 0 !important;
      border-top: 1px solid #cecece;
    }
    :deep(.el-dialog__footer) {
      padding: 10px 20px;
      border-top: 1px solid #ebeef5;
    }
  }

  .search-container {
    padding: 0 0 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #ebeef5;
  }

  .search-input {
    width: 220px;
  }

  .search-btns {
    display: flex;
    gap: 8px;
  }

  .pagination-container {
    margin-top: 15px;
    text-align: right;
    padding: 10px 0;
  }

  :deep(.el-table) {
    font-size: 13px;
  }

  :deep(.el-table th) {
    background-color: #f5f7fa;
    color: #606266;
    font-weight: 500;
  }

  :deep(.el-pagination) {
    font-size: 13px;
  }

  :deep(.el-input__inner) {
    height: 32px;
    line-height: 32px;
  }

  :deep(.el-button--small) {
    padding: 8px 15px;
  }

  .selected-videos {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
  }

  .video-item {
    position: relative;
    padding: 5px 25px 5px 10px;
    background-color: #f4f4f5;
    border-radius: 4px;
    font-size: 12px;
    line-height: 20px;
    color: #909399;
  }

  .video-item .el-icon-close {
    position: absolute;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    color: #909399;
  }

  .video-item .el-icon-close:hover {
    color: #409eff;
  }
</style>
