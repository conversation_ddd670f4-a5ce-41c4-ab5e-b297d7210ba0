import { __VLS_componentsOption, __VLS_name } from './refundCourseIndexa.vue';

function __VLS_template() {
let __VLS_ctx!: InstanceType<__VLS_PickNotAny<typeof __VLS_internalComponent, new () => {}>> & {};
/* Components */
let __VLS_otherComponents!: NonNullable<typeof __VLS_internalComponent extends { components: infer C; } ? C : {}> & typeof __VLS_componentsOption;
let __VLS_own!: __VLS_SelfComponent<typeof __VLS_name, typeof __VLS_internalComponent & (new () => { $scopedSlots: typeof __VLS_slots; })>;
let __VLS_localComponents!: typeof __VLS_otherComponents & Omit<typeof __VLS_own, keyof typeof __VLS_otherComponents>;
let __VLS_components!: typeof __VLS_localComponents & __VLS_GlobalComponents & typeof __VLS_ctx;
/* Style Scoped */
type __VLS_StyleScopedClasses = {} &
{ 'normal'?: boolean; } &
{ 'error'?: boolean; } &
{ 'warning'?: boolean; } &
{ 'frame'?: boolean; } &
{ 'el-date-editor'?: boolean; } &
{ 'el-input'?: boolean; };
let __VLS_styleScopedClasses!: __VLS_StyleScopedClasses | keyof __VLS_StyleScopedClasses | (keyof __VLS_StyleScopedClasses)[];
/* CSS variable injection */
/* CSS variable injection end */
let __VLS_resolvedLocalAndGlobalComponents!: {} &
__VLS_WithComponent<'ElCard', typeof __VLS_localComponents, "ElCard", "elCard", "el-card"> &
__VLS_WithComponent<'ElForm', typeof __VLS_localComponents, "ElForm", "elForm", "el-form"> &
__VLS_WithComponent<'ElRow', typeof __VLS_localComponents, "ElRow", "elRow", "el-row"> &
__VLS_WithComponent<'ElCol', typeof __VLS_localComponents, "ElCol", "elCol", "el-col"> &
__VLS_WithComponent<'ElFormItem', typeof __VLS_localComponents, "ElFormItem", "elFormItem", "el-form-item"> &
__VLS_WithComponent<'ElInput', typeof __VLS_localComponents, "ElInput", "elInput", "el-input"> &
__VLS_WithComponent<'ElDatePicker', typeof __VLS_localComponents, "ElDatePicker", "elDatePicker", "el-date-picker"> &
__VLS_WithComponent<'ElButton', typeof __VLS_localComponents, "ElButton", "elButton", "el-button"> &
__VLS_WithComponent<'ElTable', typeof __VLS_localComponents, "ElTable", "elTable", "el-table"> &
__VLS_WithComponent<'ElTableColumn', typeof __VLS_localComponents, "ElTableColumn", "elTableColumn", "el-table-column"> &
__VLS_WithComponent<'ElPagination', typeof __VLS_localComponents, "ElPagination", "elPagination", "el-pagination"> &
__VLS_WithComponent<'ElDialog', typeof __VLS_localComponents, "ElDialog", "elDialog", "el-dialog">;
__VLS_intrinsicElements.div; __VLS_intrinsicElements.div; __VLS_intrinsicElements.div; __VLS_intrinsicElements.div; __VLS_intrinsicElements.div; __VLS_intrinsicElements.div; __VLS_intrinsicElements.div; __VLS_intrinsicElements.div;
__VLS_components.ElCard; __VLS_components.ElCard; __VLS_components.elCard; __VLS_components.elCard; __VLS_components["el-card"]; __VLS_components["el-card"];
// @ts-ignore
[ElCard, ElCard,];
__VLS_components.ElForm; __VLS_components.ElForm; __VLS_components.elForm; __VLS_components.elForm; __VLS_components["el-form"]; __VLS_components["el-form"];
// @ts-ignore
[ElForm, ElForm,];
__VLS_components.ElRow; __VLS_components.ElRow; __VLS_components.ElRow; __VLS_components.ElRow; __VLS_components.elRow; __VLS_components.elRow; __VLS_components.elRow; __VLS_components.elRow; __VLS_components["el-row"]; __VLS_components["el-row"]; __VLS_components["el-row"]; __VLS_components["el-row"];
// @ts-ignore
[ElRow, ElRow, ElRow, ElRow,];
__VLS_components.ElCol; __VLS_components.ElCol; __VLS_components.ElCol; __VLS_components.ElCol; __VLS_components.ElCol; __VLS_components.ElCol; __VLS_components.ElCol; __VLS_components.ElCol; __VLS_components.ElCol; __VLS_components.ElCol; __VLS_components.elCol; __VLS_components.elCol; __VLS_components.elCol; __VLS_components.elCol; __VLS_components.elCol; __VLS_components.elCol; __VLS_components.elCol; __VLS_components.elCol; __VLS_components.elCol; __VLS_components.elCol; __VLS_components["el-col"]; __VLS_components["el-col"]; __VLS_components["el-col"]; __VLS_components["el-col"]; __VLS_components["el-col"]; __VLS_components["el-col"]; __VLS_components["el-col"]; __VLS_components["el-col"]; __VLS_components["el-col"]; __VLS_components["el-col"];
// @ts-ignore
[ElCol, ElCol, ElCol, ElCol, ElCol, ElCol, ElCol, ElCol, ElCol, ElCol,];
__VLS_components.ElFormItem; __VLS_components.ElFormItem; __VLS_components.ElFormItem; __VLS_components.ElFormItem; __VLS_components.ElFormItem; __VLS_components.ElFormItem; __VLS_components.ElFormItem; __VLS_components.ElFormItem; __VLS_components.elFormItem; __VLS_components.elFormItem; __VLS_components.elFormItem; __VLS_components.elFormItem; __VLS_components.elFormItem; __VLS_components.elFormItem; __VLS_components.elFormItem; __VLS_components.elFormItem; __VLS_components["el-form-item"]; __VLS_components["el-form-item"]; __VLS_components["el-form-item"]; __VLS_components["el-form-item"]; __VLS_components["el-form-item"]; __VLS_components["el-form-item"]; __VLS_components["el-form-item"]; __VLS_components["el-form-item"];
// @ts-ignore
[ElFormItem, ElFormItem, ElFormItem, ElFormItem, ElFormItem, ElFormItem, ElFormItem, ElFormItem,];
__VLS_components.ElInput; __VLS_components.ElInput; __VLS_components.ElInput; __VLS_components.ElInput; __VLS_components.ElInput; __VLS_components.ElInput; __VLS_components.elInput; __VLS_components.elInput; __VLS_components.elInput; __VLS_components.elInput; __VLS_components.elInput; __VLS_components.elInput; __VLS_components["el-input"]; __VLS_components["el-input"]; __VLS_components["el-input"]; __VLS_components["el-input"]; __VLS_components["el-input"]; __VLS_components["el-input"];
// @ts-ignore
[ElInput, ElInput, ElInput, ElInput, ElInput, ElInput,];
__VLS_components.ElDatePicker; __VLS_components.ElDatePicker; __VLS_components.elDatePicker; __VLS_components.elDatePicker; __VLS_components["el-date-picker"]; __VLS_components["el-date-picker"];
// @ts-ignore
[ElDatePicker, ElDatePicker,];
__VLS_components.ElButton; __VLS_components.ElButton; __VLS_components.ElButton; __VLS_components.ElButton; __VLS_components.ElButton; __VLS_components.ElButton; __VLS_components.elButton; __VLS_components.elButton; __VLS_components.elButton; __VLS_components.elButton; __VLS_components.elButton; __VLS_components.elButton; __VLS_components["el-button"]; __VLS_components["el-button"]; __VLS_components["el-button"]; __VLS_components["el-button"]; __VLS_components["el-button"]; __VLS_components["el-button"];
// @ts-ignore
[ElButton, ElButton, ElButton, ElButton, ElButton, ElButton,];
__VLS_components.ElTable; __VLS_components.ElTable; __VLS_components.elTable; __VLS_components.elTable; __VLS_components["el-table"]; __VLS_components["el-table"];
// @ts-ignore
[ElTable, ElTable,];
__VLS_components.ElTableColumn; __VLS_components.ElTableColumn; __VLS_components.ElTableColumn; __VLS_components.ElTableColumn; __VLS_components.ElTableColumn; __VLS_components.ElTableColumn; __VLS_components.ElTableColumn; __VLS_components.ElTableColumn; __VLS_components.ElTableColumn; __VLS_components.ElTableColumn; __VLS_components.ElTableColumn; __VLS_components.ElTableColumn; __VLS_components.ElTableColumn; __VLS_components.ElTableColumn; __VLS_components.ElTableColumn; __VLS_components.ElTableColumn; __VLS_components.elTableColumn; __VLS_components.elTableColumn; __VLS_components.elTableColumn; __VLS_components.elTableColumn; __VLS_components.elTableColumn; __VLS_components.elTableColumn; __VLS_components.elTableColumn; __VLS_components.elTableColumn; __VLS_components.elTableColumn; __VLS_components.elTableColumn; __VLS_components.elTableColumn; __VLS_components.elTableColumn; __VLS_components.elTableColumn; __VLS_components.elTableColumn; __VLS_components.elTableColumn; __VLS_components.elTableColumn; __VLS_components["el-table-column"]; __VLS_components["el-table-column"]; __VLS_components["el-table-column"]; __VLS_components["el-table-column"]; __VLS_components["el-table-column"]; __VLS_components["el-table-column"]; __VLS_components["el-table-column"]; __VLS_components["el-table-column"]; __VLS_components["el-table-column"]; __VLS_components["el-table-column"]; __VLS_components["el-table-column"]; __VLS_components["el-table-column"]; __VLS_components["el-table-column"]; __VLS_components["el-table-column"]; __VLS_components["el-table-column"]; __VLS_components["el-table-column"];
// @ts-ignore
[ElTableColumn, ElTableColumn, ElTableColumn, ElTableColumn, ElTableColumn, ElTableColumn, ElTableColumn, ElTableColumn, ElTableColumn, ElTableColumn, ElTableColumn, ElTableColumn, ElTableColumn, ElTableColumn, ElTableColumn, ElTableColumn,];
__VLS_intrinsicElements.template; __VLS_intrinsicElements.template;
__VLS_components.ElPagination; __VLS_components.ElPagination; __VLS_components.elPagination; __VLS_components.elPagination; __VLS_components["el-pagination"]; __VLS_components["el-pagination"];
// @ts-ignore
[ElPagination, ElPagination,];
__VLS_components.ElDialog; __VLS_components.ElDialog; __VLS_components.elDialog; __VLS_components.elDialog; __VLS_components["el-dialog"]; __VLS_components["el-dialog"];
// @ts-ignore
[ElDialog, ElDialog,];
__VLS_intrinsicElements.span; __VLS_intrinsicElements.span;
__VLS_intrinsicElements.ul; __VLS_intrinsicElements.ul;
__VLS_intrinsicElements.li; __VLS_intrinsicElements.li; __VLS_intrinsicElements.li; __VLS_intrinsicElements.li; __VLS_intrinsicElements.li; __VLS_intrinsicElements.li; __VLS_intrinsicElements.li; __VLS_intrinsicElements.li; __VLS_intrinsicElements.li; __VLS_intrinsicElements.li;
{
const __VLS_0 = __VLS_intrinsicElements["div"];
const __VLS_1 = __VLS_elementAsFunctionalComponent(__VLS_0);
const __VLS_2 = __VLS_1({ ...{}, }, ...__VLS_functionalComponentArgsRest(__VLS_1));
({} as (props: __VLS_FunctionalComponentProps<typeof __VLS_0, typeof __VLS_2> & Record<string, unknown>) => void)({ ...{}, });
const __VLS_3 = __VLS_pickFunctionalComponentCtx(__VLS_0, __VLS_2)!;
let __VLS_4!: __VLS_NormalizeEmits<typeof __VLS_3.emit>;
{
const __VLS_5 = ({} as 'ElCard' extends keyof typeof __VLS_ctx ? { 'ElCard': typeof __VLS_ctx.ElCard; } : 'elCard' extends keyof typeof __VLS_ctx ? { 'ElCard': typeof __VLS_ctx.elCard; } : 'el-card' extends keyof typeof __VLS_ctx ? { 'ElCard': (typeof __VLS_ctx)["el-card"]; } : typeof __VLS_resolvedLocalAndGlobalComponents).ElCard;
const __VLS_6 = __VLS_asFunctionalComponent(__VLS_5, new __VLS_5({ ...{}, class: ("frame"), shadow: ("never"), }));
({} as { ElCard: typeof __VLS_5; }).ElCard;
({} as { ElCard: typeof __VLS_5; }).ElCard;
const __VLS_7 = __VLS_6({ ...{}, class: ("frame"), shadow: ("never"), }, ...__VLS_functionalComponentArgsRest(__VLS_6));
({} as (props: __VLS_FunctionalComponentProps<typeof __VLS_5, typeof __VLS_7> & Record<string, unknown>) => void)({ ...{}, class: ("frame"), shadow: ("never"), });
const __VLS_8 = __VLS_pickFunctionalComponentCtx(__VLS_5, __VLS_7)!;
let __VLS_9!: __VLS_NormalizeEmits<typeof __VLS_8.emit>;
{
const __VLS_10 = ({} as 'ElForm' extends keyof typeof __VLS_ctx ? { 'ElForm': typeof __VLS_ctx.ElForm; } : 'elForm' extends keyof typeof __VLS_ctx ? { 'ElForm': typeof __VLS_ctx.elForm; } : 'el-form' extends keyof typeof __VLS_ctx ? { 'ElForm': (typeof __VLS_ctx)["el-form"]; } : typeof __VLS_resolvedLocalAndGlobalComponents).ElForm;
const __VLS_11 = __VLS_asFunctionalComponent(__VLS_10, new __VLS_10({ ...{}, labelWidth: ("120px"), }));
({} as { ElForm: typeof __VLS_10; }).ElForm;
({} as { ElForm: typeof __VLS_10; }).ElForm;
const __VLS_12 = __VLS_11({ ...{}, labelWidth: ("120px"), }, ...__VLS_functionalComponentArgsRest(__VLS_11));
({} as (props: __VLS_FunctionalComponentProps<typeof __VLS_10, typeof __VLS_12> & Record<string, unknown>) => void)({ ...{}, labelWidth: ("120px"), });
const __VLS_13 = __VLS_pickFunctionalComponentCtx(__VLS_10, __VLS_12)!;
let __VLS_14!: __VLS_NormalizeEmits<typeof __VLS_13.emit>;
{
const __VLS_15 = ({} as 'ElRow' extends keyof typeof __VLS_ctx ? { 'ElRow': typeof __VLS_ctx.ElRow; } : 'elRow' extends keyof typeof __VLS_ctx ? { 'ElRow': typeof __VLS_ctx.elRow; } : 'el-row' extends keyof typeof __VLS_ctx ? { 'ElRow': (typeof __VLS_ctx)["el-row"]; } : typeof __VLS_resolvedLocalAndGlobalComponents).ElRow;
const __VLS_16 = __VLS_asFunctionalComponent(__VLS_15, new __VLS_15({ ...{}, }));
({} as { ElRow: typeof __VLS_15; }).ElRow;
({} as { ElRow: typeof __VLS_15; }).ElRow;
const __VLS_17 = __VLS_16({ ...{}, }, ...__VLS_functionalComponentArgsRest(__VLS_16));
({} as (props: __VLS_FunctionalComponentProps<typeof __VLS_15, typeof __VLS_17> & Record<string, unknown>) => void)({ ...{}, });
const __VLS_18 = __VLS_pickFunctionalComponentCtx(__VLS_15, __VLS_17)!;
let __VLS_19!: __VLS_NormalizeEmits<typeof __VLS_18.emit>;
{
const __VLS_20 = ({} as 'ElCol' extends keyof typeof __VLS_ctx ? { 'ElCol': typeof __VLS_ctx.ElCol; } : 'elCol' extends keyof typeof __VLS_ctx ? { 'ElCol': typeof __VLS_ctx.elCol; } : 'el-col' extends keyof typeof __VLS_ctx ? { 'ElCol': (typeof __VLS_ctx)["el-col"]; } : typeof __VLS_resolvedLocalAndGlobalComponents).ElCol;
const __VLS_21 = __VLS_asFunctionalComponent(__VLS_20, new __VLS_20({ ...{}, span: ((5)), }));
({} as { ElCol: typeof __VLS_20; }).ElCol;
({} as { ElCol: typeof __VLS_20; }).ElCol;
const __VLS_22 = __VLS_21({ ...{}, span: ((5)), }, ...__VLS_functionalComponentArgsRest(__VLS_21));
({} as (props: __VLS_FunctionalComponentProps<typeof __VLS_20, typeof __VLS_22> & Record<string, unknown>) => void)({ ...{}, span: ((5)), });
const __VLS_23 = __VLS_pickFunctionalComponentCtx(__VLS_20, __VLS_22)!;
let __VLS_24!: __VLS_NormalizeEmits<typeof __VLS_23.emit>;
{
const __VLS_25 = ({} as 'ElFormItem' extends keyof typeof __VLS_ctx ? { 'ElFormItem': typeof __VLS_ctx.ElFormItem; } : 'elFormItem' extends keyof typeof __VLS_ctx ? { 'ElFormItem': typeof __VLS_ctx.elFormItem; } : 'el-form-item' extends keyof typeof __VLS_ctx ? { 'ElFormItem': (typeof __VLS_ctx)["el-form-item"]; } : typeof __VLS_resolvedLocalAndGlobalComponents).ElFormItem;
const __VLS_26 = __VLS_asFunctionalComponent(__VLS_25, new __VLS_25({ ...{}, label: ("门店编号:"), }));
({} as { ElFormItem: typeof __VLS_25; }).ElFormItem;
({} as { ElFormItem: typeof __VLS_25; }).ElFormItem;
const __VLS_27 = __VLS_26({ ...{}, label: ("门店编号:"), }, ...__VLS_functionalComponentArgsRest(__VLS_26));
({} as (props: __VLS_FunctionalComponentProps<typeof __VLS_25, typeof __VLS_27> & Record<string, unknown>) => void)({ ...{}, label: ("门店编号:"), });
const __VLS_28 = __VLS_pickFunctionalComponentCtx(__VLS_25, __VLS_27)!;
let __VLS_29!: __VLS_NormalizeEmits<typeof __VLS_28.emit>;
{
const __VLS_30 = ({} as 'ElInput' extends keyof typeof __VLS_ctx ? { 'ElInput': typeof __VLS_ctx.ElInput; } : 'elInput' extends keyof typeof __VLS_ctx ? { 'ElInput': typeof __VLS_ctx.elInput; } : 'el-input' extends keyof typeof __VLS_ctx ? { 'ElInput': (typeof __VLS_ctx)["el-input"]; } : typeof __VLS_resolvedLocalAndGlobalComponents).ElInput;
const __VLS_31 = __VLS_asFunctionalComponent(__VLS_30, new __VLS_30({ ...{}, value: ((__VLS_ctx.searchNum.studentCode)), disabled: ((!!__VLS_ctx.stuudentCode)), clearable: (true), placeholder: ("请输入"), size: ("small"), style: ({}), }));
({} as { ElInput: typeof __VLS_30; }).ElInput;
({} as { ElInput: typeof __VLS_30; }).ElInput;
const __VLS_32 = __VLS_31({ ...{}, value: ((__VLS_ctx.searchNum.studentCode)), disabled: ((!!__VLS_ctx.stuudentCode)), clearable: (true), placeholder: ("请输入"), size: ("small"), style: ({}), }, ...__VLS_functionalComponentArgsRest(__VLS_31));
({} as (props: __VLS_FunctionalComponentProps<typeof __VLS_30, typeof __VLS_32> & Record<string, unknown>) => void)({ ...{}, value: ((__VLS_ctx.searchNum.studentCode)), disabled: ((!!__VLS_ctx.stuudentCode)), clearable: (true), placeholder: ("请输入"), size: ("small"), style: ({}), });
const __VLS_33 = __VLS_pickFunctionalComponentCtx(__VLS_30, __VLS_32)!;
let __VLS_34!: __VLS_NormalizeEmits<typeof __VLS_33.emit>;
}
(__VLS_28.slots!).default;
}
(__VLS_23.slots!).default;
}
{
const __VLS_35 = ({} as 'ElCol' extends keyof typeof __VLS_ctx ? { 'ElCol': typeof __VLS_ctx.ElCol; } : 'elCol' extends keyof typeof __VLS_ctx ? { 'ElCol': typeof __VLS_ctx.elCol; } : 'el-col' extends keyof typeof __VLS_ctx ? { 'ElCol': (typeof __VLS_ctx)["el-col"]; } : typeof __VLS_resolvedLocalAndGlobalComponents).ElCol;
const __VLS_36 = __VLS_asFunctionalComponent(__VLS_35, new __VLS_35({ ...{}, span: ((5)), }));
({} as { ElCol: typeof __VLS_35; }).ElCol;
({} as { ElCol: typeof __VLS_35; }).ElCol;
const __VLS_37 = __VLS_36({ ...{}, span: ((5)), }, ...__VLS_functionalComponentArgsRest(__VLS_36));
({} as (props: __VLS_FunctionalComponentProps<typeof __VLS_35, typeof __VLS_37> & Record<string, unknown>) => void)({ ...{}, span: ((5)), });
const __VLS_38 = __VLS_pickFunctionalComponentCtx(__VLS_35, __VLS_37)!;
let __VLS_39!: __VLS_NormalizeEmits<typeof __VLS_38.emit>;
{
const __VLS_40 = ({} as 'ElFormItem' extends keyof typeof __VLS_ctx ? { 'ElFormItem': typeof __VLS_ctx.ElFormItem; } : 'elFormItem' extends keyof typeof __VLS_ctx ? { 'ElFormItem': typeof __VLS_ctx.elFormItem; } : 'el-form-item' extends keyof typeof __VLS_ctx ? { 'ElFormItem': (typeof __VLS_ctx)["el-form-item"]; } : typeof __VLS_resolvedLocalAndGlobalComponents).ElFormItem;
const __VLS_41 = __VLS_asFunctionalComponent(__VLS_40, new __VLS_40({ ...{}, label: ("门店名称:"), }));
({} as { ElFormItem: typeof __VLS_40; }).ElFormItem;
({} as { ElFormItem: typeof __VLS_40; }).ElFormItem;
const __VLS_42 = __VLS_41({ ...{}, label: ("门店名称:"), }, ...__VLS_functionalComponentArgsRest(__VLS_41));
({} as (props: __VLS_FunctionalComponentProps<typeof __VLS_40, typeof __VLS_42> & Record<string, unknown>) => void)({ ...{}, label: ("门店名称:"), });
const __VLS_43 = __VLS_pickFunctionalComponentCtx(__VLS_40, __VLS_42)!;
let __VLS_44!: __VLS_NormalizeEmits<typeof __VLS_43.emit>;
{
const __VLS_45 = ({} as 'ElInput' extends keyof typeof __VLS_ctx ? { 'ElInput': typeof __VLS_ctx.ElInput; } : 'elInput' extends keyof typeof __VLS_ctx ? { 'ElInput': typeof __VLS_ctx.elInput; } : 'el-input' extends keyof typeof __VLS_ctx ? { 'ElInput': (typeof __VLS_ctx)["el-input"]; } : typeof __VLS_resolvedLocalAndGlobalComponents).ElInput;
const __VLS_46 = __VLS_asFunctionalComponent(__VLS_45, new __VLS_45({ ...{}, value: ((__VLS_ctx.searchNum.studentCode)), disabled: ((!!__VLS_ctx.stuudentCode)), clearable: (true), placeholder: ("请输入"), size: ("small"), style: ({}), }));
({} as { ElInput: typeof __VLS_45; }).ElInput;
({} as { ElInput: typeof __VLS_45; }).ElInput;
const __VLS_47 = __VLS_46({ ...{}, value: ((__VLS_ctx.searchNum.studentCode)), disabled: ((!!__VLS_ctx.stuudentCode)), clearable: (true), placeholder: ("请输入"), size: ("small"), style: ({}), }, ...__VLS_functionalComponentArgsRest(__VLS_46));
({} as (props: __VLS_FunctionalComponentProps<typeof __VLS_45, typeof __VLS_47> & Record<string, unknown>) => void)({ ...{}, value: ((__VLS_ctx.searchNum.studentCode)), disabled: ((!!__VLS_ctx.stuudentCode)), clearable: (true), placeholder: ("请输入"), size: ("small"), style: ({}), });
const __VLS_48 = __VLS_pickFunctionalComponentCtx(__VLS_45, __VLS_47)!;
let __VLS_49!: __VLS_NormalizeEmits<typeof __VLS_48.emit>;
}
(__VLS_43.slots!).default;
}
(__VLS_38.slots!).default;
}
{
const __VLS_50 = ({} as 'ElCol' extends keyof typeof __VLS_ctx ? { 'ElCol': typeof __VLS_ctx.ElCol; } : 'elCol' extends keyof typeof __VLS_ctx ? { 'ElCol': typeof __VLS_ctx.elCol; } : 'el-col' extends keyof typeof __VLS_ctx ? { 'ElCol': (typeof __VLS_ctx)["el-col"]; } : typeof __VLS_resolvedLocalAndGlobalComponents).ElCol;
const __VLS_51 = __VLS_asFunctionalComponent(__VLS_50, new __VLS_50({ ...{}, span: ((5)), }));
({} as { ElCol: typeof __VLS_50; }).ElCol;
({} as { ElCol: typeof __VLS_50; }).ElCol;
const __VLS_52 = __VLS_51({ ...{}, span: ((5)), }, ...__VLS_functionalComponentArgsRest(__VLS_51));
({} as (props: __VLS_FunctionalComponentProps<typeof __VLS_50, typeof __VLS_52> & Record<string, unknown>) => void)({ ...{}, span: ((5)), });
const __VLS_53 = __VLS_pickFunctionalComponentCtx(__VLS_50, __VLS_52)!;
let __VLS_54!: __VLS_NormalizeEmits<typeof __VLS_53.emit>;
{
const __VLS_55 = ({} as 'ElFormItem' extends keyof typeof __VLS_ctx ? { 'ElFormItem': typeof __VLS_ctx.ElFormItem; } : 'elFormItem' extends keyof typeof __VLS_ctx ? { 'ElFormItem': typeof __VLS_ctx.elFormItem; } : 'el-form-item' extends keyof typeof __VLS_ctx ? { 'ElFormItem': (typeof __VLS_ctx)["el-form-item"]; } : typeof __VLS_resolvedLocalAndGlobalComponents).ElFormItem;
const __VLS_56 = __VLS_asFunctionalComponent(__VLS_55, new __VLS_55({ ...{}, label: ("门店手机号:"), }));
({} as { ElFormItem: typeof __VLS_55; }).ElFormItem;
({} as { ElFormItem: typeof __VLS_55; }).ElFormItem;
const __VLS_57 = __VLS_56({ ...{}, label: ("门店手机号:"), }, ...__VLS_functionalComponentArgsRest(__VLS_56));
({} as (props: __VLS_FunctionalComponentProps<typeof __VLS_55, typeof __VLS_57> & Record<string, unknown>) => void)({ ...{}, label: ("门店手机号:"), });
const __VLS_58 = __VLS_pickFunctionalComponentCtx(__VLS_55, __VLS_57)!;
let __VLS_59!: __VLS_NormalizeEmits<typeof __VLS_58.emit>;
{
const __VLS_60 = ({} as 'ElInput' extends keyof typeof __VLS_ctx ? { 'ElInput': typeof __VLS_ctx.ElInput; } : 'elInput' extends keyof typeof __VLS_ctx ? { 'ElInput': typeof __VLS_ctx.elInput; } : 'el-input' extends keyof typeof __VLS_ctx ? { 'ElInput': (typeof __VLS_ctx)["el-input"]; } : typeof __VLS_resolvedLocalAndGlobalComponents).ElInput;
const __VLS_61 = __VLS_asFunctionalComponent(__VLS_60, new __VLS_60({ ...{}, value: ((__VLS_ctx.searchNum.studentCode)), disabled: ((!!__VLS_ctx.stuudentCode)), clearable: (true), placeholder: ("请输入"), size: ("small"), style: ({}), }));
({} as { ElInput: typeof __VLS_60; }).ElInput;
({} as { ElInput: typeof __VLS_60; }).ElInput;
const __VLS_62 = __VLS_61({ ...{}, value: ((__VLS_ctx.searchNum.studentCode)), disabled: ((!!__VLS_ctx.stuudentCode)), clearable: (true), placeholder: ("请输入"), size: ("small"), style: ({}), }, ...__VLS_functionalComponentArgsRest(__VLS_61));
({} as (props: __VLS_FunctionalComponentProps<typeof __VLS_60, typeof __VLS_62> & Record<string, unknown>) => void)({ ...{}, value: ((__VLS_ctx.searchNum.studentCode)), disabled: ((!!__VLS_ctx.stuudentCode)), clearable: (true), placeholder: ("请输入"), size: ("small"), style: ({}), });
const __VLS_63 = __VLS_pickFunctionalComponentCtx(__VLS_60, __VLS_62)!;
let __VLS_64!: __VLS_NormalizeEmits<typeof __VLS_63.emit>;
}
(__VLS_58.slots!).default;
}
(__VLS_53.slots!).default;
}
{
const __VLS_65 = ({} as 'ElCol' extends keyof typeof __VLS_ctx ? { 'ElCol': typeof __VLS_ctx.ElCol; } : 'elCol' extends keyof typeof __VLS_ctx ? { 'ElCol': typeof __VLS_ctx.elCol; } : 'el-col' extends keyof typeof __VLS_ctx ? { 'ElCol': (typeof __VLS_ctx)["el-col"]; } : typeof __VLS_resolvedLocalAndGlobalComponents).ElCol;
const __VLS_66 = __VLS_asFunctionalComponent(__VLS_65, new __VLS_65({ ...{}, span: ((5)), }));
({} as { ElCol: typeof __VLS_65; }).ElCol;
({} as { ElCol: typeof __VLS_65; }).ElCol;
const __VLS_67 = __VLS_66({ ...{}, span: ((5)), }, ...__VLS_functionalComponentArgsRest(__VLS_66));
({} as (props: __VLS_FunctionalComponentProps<typeof __VLS_65, typeof __VLS_67> & Record<string, unknown>) => void)({ ...{}, span: ((5)), });
const __VLS_68 = __VLS_pickFunctionalComponentCtx(__VLS_65, __VLS_67)!;
let __VLS_69!: __VLS_NormalizeEmits<typeof __VLS_68.emit>;
{
const __VLS_70 = ({} as 'ElFormItem' extends keyof typeof __VLS_ctx ? { 'ElFormItem': typeof __VLS_ctx.ElFormItem; } : 'elFormItem' extends keyof typeof __VLS_ctx ? { 'ElFormItem': typeof __VLS_ctx.elFormItem; } : 'el-form-item' extends keyof typeof __VLS_ctx ? { 'ElFormItem': (typeof __VLS_ctx)["el-form-item"]; } : typeof __VLS_resolvedLocalAndGlobalComponents).ElFormItem;
const __VLS_71 = __VLS_asFunctionalComponent(__VLS_70, new __VLS_70({ ...{}, label: ("时间筛选:"), }));
({} as { ElFormItem: typeof __VLS_70; }).ElFormItem;
({} as { ElFormItem: typeof __VLS_70; }).ElFormItem;
const __VLS_72 = __VLS_71({ ...{}, label: ("时间筛选:"), }, ...__VLS_functionalComponentArgsRest(__VLS_71));
({} as (props: __VLS_FunctionalComponentProps<typeof __VLS_70, typeof __VLS_72> & Record<string, unknown>) => void)({ ...{}, label: ("时间筛选:"), });
const __VLS_73 = __VLS_pickFunctionalComponentCtx(__VLS_70, __VLS_72)!;
let __VLS_74!: __VLS_NormalizeEmits<typeof __VLS_73.emit>;
{
const __VLS_75 = ({} as 'ElDatePicker' extends keyof typeof __VLS_ctx ? { 'ElDatePicker': typeof __VLS_ctx.ElDatePicker; } : 'elDatePicker' extends keyof typeof __VLS_ctx ? { 'ElDatePicker': typeof __VLS_ctx.elDatePicker; } : 'el-date-picker' extends keyof typeof __VLS_ctx ? { 'ElDatePicker': (typeof __VLS_ctx)["el-date-picker"]; } : typeof __VLS_resolvedLocalAndGlobalComponents).ElDatePicker;
const __VLS_76 = __VLS_asFunctionalComponent(__VLS_75, new __VLS_75({ ...{}, value: ((__VLS_ctx.timeAll)), type: ("month"), placeholder: ("选择月份"), size: ("small"), }));
({} as { ElDatePicker: typeof __VLS_75; }).ElDatePicker;
({} as { ElDatePicker: typeof __VLS_75; }).ElDatePicker;
const __VLS_77 = __VLS_76({ ...{}, value: ((__VLS_ctx.timeAll)), type: ("month"), placeholder: ("选择月份"), size: ("small"), }, ...__VLS_functionalComponentArgsRest(__VLS_76));
({} as (props: __VLS_FunctionalComponentProps<typeof __VLS_75, typeof __VLS_77> & Record<string, unknown>) => void)({ ...{}, value: ((__VLS_ctx.timeAll)), type: ("month"), placeholder: ("选择月份"), size: ("small"), });
const __VLS_78 = __VLS_pickFunctionalComponentCtx(__VLS_75, __VLS_77)!;
let __VLS_79!: __VLS_NormalizeEmits<typeof __VLS_78.emit>;
}
(__VLS_73.slots!).default;
}
(__VLS_68.slots!).default;
}
{
const __VLS_80 = ({} as 'ElCol' extends keyof typeof __VLS_ctx ? { 'ElCol': typeof __VLS_ctx.ElCol; } : 'elCol' extends keyof typeof __VLS_ctx ? { 'ElCol': typeof __VLS_ctx.elCol; } : 'el-col' extends keyof typeof __VLS_ctx ? { 'ElCol': (typeof __VLS_ctx)["el-col"]; } : typeof __VLS_resolvedLocalAndGlobalComponents).ElCol;
const __VLS_81 = __VLS_asFunctionalComponent(__VLS_80, new __VLS_80({ ...{}, span: ((4)), }));
({} as { ElCol: typeof __VLS_80; }).ElCol;
({} as { ElCol: typeof __VLS_80; }).ElCol;
const __VLS_82 = __VLS_81({ ...{}, span: ((4)), }, ...__VLS_functionalComponentArgsRest(__VLS_81));
({} as (props: __VLS_FunctionalComponentProps<typeof __VLS_80, typeof __VLS_82> & Record<string, unknown>) => void)({ ...{}, span: ((4)), });
const __VLS_83 = __VLS_pickFunctionalComponentCtx(__VLS_80, __VLS_82)!;
let __VLS_84!: __VLS_NormalizeEmits<typeof __VLS_83.emit>;
{
const __VLS_85 = ({} as 'ElButton' extends keyof typeof __VLS_ctx ? { 'ElButton': typeof __VLS_ctx.ElButton; } : 'elButton' extends keyof typeof __VLS_ctx ? { 'ElButton': typeof __VLS_ctx.elButton; } : 'el-button' extends keyof typeof __VLS_ctx ? { 'ElButton': (typeof __VLS_ctx)["el-button"]; } : typeof __VLS_resolvedLocalAndGlobalComponents).ElButton;
const __VLS_86 = __VLS_asFunctionalComponent(__VLS_85, new __VLS_85({ ...{ onClick: {} as any, }, type: ("primary"), icon: ("el-icon-search"), size: ("small"), }));
({} as { ElButton: typeof __VLS_85; }).ElButton;
({} as { ElButton: typeof __VLS_85; }).ElButton;
const __VLS_87 = __VLS_86({ ...{ onClick: {} as any, }, type: ("primary"), icon: ("el-icon-search"), size: ("small"), }, ...__VLS_functionalComponentArgsRest(__VLS_86));
({} as (props: __VLS_FunctionalComponentProps<typeof __VLS_85, typeof __VLS_87> & Record<string, unknown>) => void)({ ...{ onClick: {} as any, }, type: ("primary"), icon: ("el-icon-search"), size: ("small"), });
const __VLS_88 = __VLS_pickFunctionalComponentCtx(__VLS_85, __VLS_87)!;
let __VLS_89!: __VLS_NormalizeEmits<typeof __VLS_88.emit>;
let __VLS_90 = { 'click': __VLS_pickEvent(__VLS_89['click'], ({} as __VLS_FunctionalComponentProps<typeof __VLS_86, typeof __VLS_87>).onClick) };
__VLS_90 = { click: (__VLS_ctx.initData01) };
(__VLS_88.slots!).default;
}
{
const __VLS_91 = ({} as 'ElButton' extends keyof typeof __VLS_ctx ? { 'ElButton': typeof __VLS_ctx.ElButton; } : 'elButton' extends keyof typeof __VLS_ctx ? { 'ElButton': typeof __VLS_ctx.elButton; } : 'el-button' extends keyof typeof __VLS_ctx ? { 'ElButton': (typeof __VLS_ctx)["el-button"]; } : typeof __VLS_resolvedLocalAndGlobalComponents).ElButton;
const __VLS_92 = __VLS_asFunctionalComponent(__VLS_91, new __VLS_91({ ...{ onClick: {} as any, }, icon: ("el-icon-refresh"), size: ("small"), }));
({} as { ElButton: typeof __VLS_91; }).ElButton;
({} as { ElButton: typeof __VLS_91; }).ElButton;
const __VLS_93 = __VLS_92({ ...{ onClick: {} as any, }, icon: ("el-icon-refresh"), size: ("small"), }, ...__VLS_functionalComponentArgsRest(__VLS_92));
({} as (props: __VLS_FunctionalComponentProps<typeof __VLS_91, typeof __VLS_93> & Record<string, unknown>) => void)({ ...{ onClick: {} as any, }, icon: ("el-icon-refresh"), size: ("small"), });
const __VLS_94 = __VLS_pickFunctionalComponentCtx(__VLS_91, __VLS_93)!;
let __VLS_95!: __VLS_NormalizeEmits<typeof __VLS_94.emit>;
let __VLS_96 = { 'click': __VLS_pickEvent(__VLS_95['click'], ({} as __VLS_FunctionalComponentProps<typeof __VLS_92, typeof __VLS_93>).onClick) };
__VLS_96 = {
click: $event => {
__VLS_ctx.rest();
// @ts-ignore
[searchNum, stuudentCode, searchNum, stuudentCode, searchNum, stuudentCode, searchNum, stuudentCode, searchNum, stuudentCode, searchNum, stuudentCode, searchNum, stuudentCode, searchNum, stuudentCode, searchNum, stuudentCode, timeAll, timeAll, timeAll, initData01, rest,];
}
};
(__VLS_94.slots!).default;
}
(__VLS_83.slots!).default;
}
(__VLS_18.slots!).default;
}
(__VLS_13.slots!).default;
}
(__VLS_8.slots!).default;
}
{
const __VLS_97 = ({} as 'ElTable' extends keyof typeof __VLS_ctx ? { 'ElTable': typeof __VLS_ctx.ElTable; } : 'elTable' extends keyof typeof __VLS_ctx ? { 'ElTable': typeof __VLS_ctx.elTable; } : 'el-table' extends keyof typeof __VLS_ctx ? { 'ElTable': (typeof __VLS_ctx)["el-table"]; } : typeof __VLS_resolvedLocalAndGlobalComponents).ElTable;
const __VLS_98 = __VLS_asFunctionalComponent(__VLS_97, new __VLS_97({ ...{}, data: ((__VLS_ctx.luyouclassCard)), style: ({}), id: ("out-table"), headerCellStyle: ((__VLS_ctx.getRowClass)), cellStyle: (({ 'text-align': 'center' })), }));
({} as { ElTable: typeof __VLS_97; }).ElTable;
({} as { ElTable: typeof __VLS_97; }).ElTable;
const __VLS_99 = __VLS_98({ ...{}, data: ((__VLS_ctx.luyouclassCard)), style: ({}), id: ("out-table"), headerCellStyle: ((__VLS_ctx.getRowClass)), cellStyle: (({ 'text-align': 'center' })), }, ...__VLS_functionalComponentArgsRest(__VLS_98));
({} as (props: __VLS_FunctionalComponentProps<typeof __VLS_97, typeof __VLS_99> & Record<string, unknown>) => void)({ ...{}, data: ((__VLS_ctx.luyouclassCard)), style: ({}), id: ("out-table"), headerCellStyle: ((__VLS_ctx.getRowClass)), cellStyle: (({ 'text-align': 'center' })), });
const __VLS_100 = __VLS_pickFunctionalComponentCtx(__VLS_97, __VLS_99)!;
let __VLS_101!: __VLS_NormalizeEmits<typeof __VLS_100.emit>;
__VLS_directiveFunction(__VLS_ctx.vLoading)((__VLS_ctx.tableLoading));
{
const __VLS_102 = ({} as 'ElTableColumn' extends keyof typeof __VLS_ctx ? { 'ElTableColumn': typeof __VLS_ctx.ElTableColumn; } : 'elTableColumn' extends keyof typeof __VLS_ctx ? { 'ElTableColumn': typeof __VLS_ctx.elTableColumn; } : 'el-table-column' extends keyof typeof __VLS_ctx ? { 'ElTableColumn': (typeof __VLS_ctx)["el-table-column"]; } : typeof __VLS_resolvedLocalAndGlobalComponents).ElTableColumn;
const __VLS_103 = __VLS_asFunctionalComponent(__VLS_102, new __VLS_102({ ...{}, prop: ("merchantCode"), minWidth: ("200"), label: ("门店编号"), headerAlign: ("center"), }));
({} as { ElTableColumn: typeof __VLS_102; }).ElTableColumn;
const __VLS_104 = __VLS_103({ ...{}, prop: ("merchantCode"), minWidth: ("200"), label: ("门店编号"), headerAlign: ("center"), }, ...__VLS_functionalComponentArgsRest(__VLS_103));
({} as (props: __VLS_FunctionalComponentProps<typeof __VLS_102, typeof __VLS_104> & Record<string, unknown>) => void)({ ...{}, prop: ("merchantCode"), minWidth: ("200"), label: ("门店编号"), headerAlign: ("center"), });
const __VLS_105 = __VLS_pickFunctionalComponentCtx(__VLS_102, __VLS_104)!;
let __VLS_106!: __VLS_NormalizeEmits<typeof __VLS_105.emit>;
}
{
const __VLS_107 = ({} as 'ElTableColumn' extends keyof typeof __VLS_ctx ? { 'ElTableColumn': typeof __VLS_ctx.ElTableColumn; } : 'elTableColumn' extends keyof typeof __VLS_ctx ? { 'ElTableColumn': typeof __VLS_ctx.elTableColumn; } : 'el-table-column' extends keyof typeof __VLS_ctx ? { 'ElTableColumn': (typeof __VLS_ctx)["el-table-column"]; } : typeof __VLS_resolvedLocalAndGlobalComponents).ElTableColumn;
const __VLS_108 = __VLS_asFunctionalComponent(__VLS_107, new __VLS_107({ ...{}, prop: ("merchantName"), minWidth: ("200"), label: ("所属门店"), headerAlign: ("center"), }));
({} as { ElTableColumn: typeof __VLS_107; }).ElTableColumn;
const __VLS_109 = __VLS_108({ ...{}, prop: ("merchantName"), minWidth: ("200"), label: ("所属门店"), headerAlign: ("center"), }, ...__VLS_functionalComponentArgsRest(__VLS_108));
({} as (props: __VLS_FunctionalComponentProps<typeof __VLS_107, typeof __VLS_109> & Record<string, unknown>) => void)({ ...{}, prop: ("merchantName"), minWidth: ("200"), label: ("所属门店"), headerAlign: ("center"), });
const __VLS_110 = __VLS_pickFunctionalComponentCtx(__VLS_107, __VLS_109)!;
let __VLS_111!: __VLS_NormalizeEmits<typeof __VLS_110.emit>;
}
{
const __VLS_112 = ({} as 'ElTableColumn' extends keyof typeof __VLS_ctx ? { 'ElTableColumn': typeof __VLS_ctx.ElTableColumn; } : 'elTableColumn' extends keyof typeof __VLS_ctx ? { 'ElTableColumn': typeof __VLS_ctx.elTableColumn; } : 'el-table-column' extends keyof typeof __VLS_ctx ? { 'ElTableColumn': (typeof __VLS_ctx)["el-table-column"]; } : typeof __VLS_resolvedLocalAndGlobalComponents).ElTableColumn;
const __VLS_113 = __VLS_asFunctionalComponent(__VLS_112, new __VLS_112({ ...{}, prop: ("address"), label: ("操作"), headerAlign: ("center"), width: ("200"), }));
({} as { ElTableColumn: typeof __VLS_112; }).ElTableColumn;
({} as { ElTableColumn: typeof __VLS_112; }).ElTableColumn;
const __VLS_114 = __VLS_113({ ...{}, prop: ("address"), label: ("操作"), headerAlign: ("center"), width: ("200"), }, ...__VLS_functionalComponentArgsRest(__VLS_113));
({} as (props: __VLS_FunctionalComponentProps<typeof __VLS_112, typeof __VLS_114> & Record<string, unknown>) => void)({ ...{}, prop: ("address"), label: ("操作"), headerAlign: ("center"), width: ("200"), });
const __VLS_115 = __VLS_pickFunctionalComponentCtx(__VLS_112, __VLS_114)!;
let __VLS_116!: __VLS_NormalizeEmits<typeof __VLS_115.emit>;
{
const __VLS_117 = __VLS_intrinsicElements["template"];
const __VLS_118 = __VLS_elementAsFunctionalComponent(__VLS_117);
const __VLS_119 = __VLS_118({ ...{}, }, ...__VLS_functionalComponentArgsRest(__VLS_118));
({} as (props: __VLS_FunctionalComponentProps<typeof __VLS_117, typeof __VLS_119> & Record<string, unknown>) => void)({ ...{}, });
{
const [{ row }] = __VLS_getSlotParams((__VLS_115.slots!).default);
{
const __VLS_120 = ({} as 'ElButton' extends keyof typeof __VLS_ctx ? { 'ElButton': typeof __VLS_ctx.ElButton; } : 'elButton' extends keyof typeof __VLS_ctx ? { 'ElButton': typeof __VLS_ctx.elButton; } : 'el-button' extends keyof typeof __VLS_ctx ? { 'ElButton': (typeof __VLS_ctx)["el-button"]; } : typeof __VLS_resolvedLocalAndGlobalComponents).ElButton;
const __VLS_121 = __VLS_asFunctionalComponent(__VLS_120, new __VLS_120({ ...{ onClick: {} as any, }, type: ("success"), size: ("mini"), }));
({} as { ElButton: typeof __VLS_120; }).ElButton;
({} as { ElButton: typeof __VLS_120; }).ElButton;
const __VLS_122 = __VLS_121({ ...{ onClick: {} as any, }, type: ("success"), size: ("mini"), }, ...__VLS_functionalComponentArgsRest(__VLS_121));
({} as (props: __VLS_FunctionalComponentProps<typeof __VLS_120, typeof __VLS_122> & Record<string, unknown>) => void)({ ...{ onClick: {} as any, }, type: ("success"), size: ("mini"), });
const __VLS_123 = __VLS_pickFunctionalComponentCtx(__VLS_120, __VLS_122)!;
let __VLS_124!: __VLS_NormalizeEmits<typeof __VLS_123.emit>;
let __VLS_125 = { 'click': __VLS_pickEvent(__VLS_124['click'], ({} as __VLS_FunctionalComponentProps<typeof __VLS_121, typeof __VLS_122>).onClick) };
__VLS_125 = {
click: $event => {
__VLS_ctx.LeaveBtn(row);
// @ts-ignore
[luyouclassCard, getRowClass, luyouclassCard, getRowClass, luyouclassCard, getRowClass, tableLoading, LeaveBtn,];
}
};
(__VLS_123.slots!).default;
}
__VLS_115.slots!['' /* empty slot name completion */];
}
}
}
{
const __VLS_126 = ({} as 'ElTableColumn' extends keyof typeof __VLS_ctx ? { 'ElTableColumn': typeof __VLS_ctx.ElTableColumn; } : 'elTableColumn' extends keyof typeof __VLS_ctx ? { 'ElTableColumn': typeof __VLS_ctx.elTableColumn; } : 'el-table-column' extends keyof typeof __VLS_ctx ? { 'ElTableColumn': (typeof __VLS_ctx)["el-table-column"]; } : typeof __VLS_resolvedLocalAndGlobalComponents).ElTableColumn;
const __VLS_127 = __VLS_asFunctionalComponent(__VLS_126, new __VLS_126({ ...{}, prop: ("createTime"), label: ("时间"), minWidth: ("200"), headerAlign: ("center"), }));
({} as { ElTableColumn: typeof __VLS_126; }).ElTableColumn;
({} as { ElTableColumn: typeof __VLS_126; }).ElTableColumn;
const __VLS_128 = __VLS_127({ ...{}, prop: ("createTime"), label: ("时间"), minWidth: ("200"), headerAlign: ("center"), }, ...__VLS_functionalComponentArgsRest(__VLS_127));
({} as (props: __VLS_FunctionalComponentProps<typeof __VLS_126, typeof __VLS_128> & Record<string, unknown>) => void)({ ...{}, prop: ("createTime"), label: ("时间"), minWidth: ("200"), headerAlign: ("center"), });
const __VLS_129 = __VLS_pickFunctionalComponentCtx(__VLS_126, __VLS_128)!;
let __VLS_130!: __VLS_NormalizeEmits<typeof __VLS_129.emit>;
}
{
const __VLS_131 = ({} as 'ElTableColumn' extends keyof typeof __VLS_ctx ? { 'ElTableColumn': typeof __VLS_ctx.ElTableColumn; } : 'elTableColumn' extends keyof typeof __VLS_ctx ? { 'ElTableColumn': typeof __VLS_ctx.elTableColumn; } : 'el-table-column' extends keyof typeof __VLS_ctx ? { 'ElTableColumn': (typeof __VLS_ctx)["el-table-column"]; } : typeof __VLS_resolvedLocalAndGlobalComponents).ElTableColumn;
const __VLS_132 = __VLS_asFunctionalComponent(__VLS_131, new __VLS_131({ ...{}, prop: ("studentName"), label: ("退课次数"), minWidth: ("150"), headerAlign: ("center"), }));
({} as { ElTableColumn: typeof __VLS_131; }).ElTableColumn;
({} as { ElTableColumn: typeof __VLS_131; }).ElTableColumn;
const __VLS_133 = __VLS_132({ ...{}, prop: ("studentName"), label: ("退课次数"), minWidth: ("150"), headerAlign: ("center"), }, ...__VLS_functionalComponentArgsRest(__VLS_132));
({} as (props: __VLS_FunctionalComponentProps<typeof __VLS_131, typeof __VLS_133> & Record<string, unknown>) => void)({ ...{}, prop: ("studentName"), label: ("退课次数"), minWidth: ("150"), headerAlign: ("center"), });
const __VLS_134 = __VLS_pickFunctionalComponentCtx(__VLS_131, __VLS_133)!;
let __VLS_135!: __VLS_NormalizeEmits<typeof __VLS_134.emit>;
}
{
const __VLS_136 = ({} as 'ElTableColumn' extends keyof typeof __VLS_ctx ? { 'ElTableColumn': typeof __VLS_ctx.ElTableColumn; } : 'elTableColumn' extends keyof typeof __VLS_ctx ? { 'ElTableColumn': typeof __VLS_ctx.elTableColumn; } : 'el-table-column' extends keyof typeof __VLS_ctx ? { 'ElTableColumn': (typeof __VLS_ctx)["el-table-column"]; } : typeof __VLS_resolvedLocalAndGlobalComponents).ElTableColumn;
const __VLS_137 = __VLS_asFunctionalComponent(__VLS_136, new __VLS_136({ ...{}, prop: ("studentCode"), label: ("已退学时"), minWidth: ("150"), headerAlign: ("center"), }));
({} as { ElTableColumn: typeof __VLS_136; }).ElTableColumn;
({} as { ElTableColumn: typeof __VLS_136; }).ElTableColumn;
const __VLS_138 = __VLS_137({ ...{}, prop: ("studentCode"), label: ("已退学时"), minWidth: ("150"), headerAlign: ("center"), }, ...__VLS_functionalComponentArgsRest(__VLS_137));
({} as (props: __VLS_FunctionalComponentProps<typeof __VLS_136, typeof __VLS_138> & Record<string, unknown>) => void)({ ...{}, prop: ("studentCode"), label: ("已退学时"), minWidth: ("150"), headerAlign: ("center"), });
const __VLS_139 = __VLS_pickFunctionalComponentCtx(__VLS_136, __VLS_138)!;
let __VLS_140!: __VLS_NormalizeEmits<typeof __VLS_139.emit>;
}
{
const __VLS_141 = ({} as 'ElTableColumn' extends keyof typeof __VLS_ctx ? { 'ElTableColumn': typeof __VLS_ctx.ElTableColumn; } : 'elTableColumn' extends keyof typeof __VLS_ctx ? { 'ElTableColumn': typeof __VLS_ctx.elTableColumn; } : 'el-table-column' extends keyof typeof __VLS_ctx ? { 'ElTableColumn': (typeof __VLS_ctx)["el-table-column"]; } : typeof __VLS_resolvedLocalAndGlobalComponents).ElTableColumn;
const __VLS_142 = __VLS_asFunctionalComponent(__VLS_141, new __VLS_141({ ...{}, prop: ("studentCode"), label: ("已退交付学时"), minWidth: ("150"), headerAlign: ("center"), }));
({} as { ElTableColumn: typeof __VLS_141; }).ElTableColumn;
({} as { ElTableColumn: typeof __VLS_141; }).ElTableColumn;
const __VLS_143 = __VLS_142({ ...{}, prop: ("studentCode"), label: ("已退交付学时"), minWidth: ("150"), headerAlign: ("center"), }, ...__VLS_functionalComponentArgsRest(__VLS_142));
({} as (props: __VLS_FunctionalComponentProps<typeof __VLS_141, typeof __VLS_143> & Record<string, unknown>) => void)({ ...{}, prop: ("studentCode"), label: ("已退交付学时"), minWidth: ("150"), headerAlign: ("center"), });
const __VLS_144 = __VLS_pickFunctionalComponentCtx(__VLS_141, __VLS_143)!;
let __VLS_145!: __VLS_NormalizeEmits<typeof __VLS_144.emit>;
}
{
const __VLS_146 = ({} as 'ElTableColumn' extends keyof typeof __VLS_ctx ? { 'ElTableColumn': typeof __VLS_ctx.ElTableColumn; } : 'elTableColumn' extends keyof typeof __VLS_ctx ? { 'ElTableColumn': typeof __VLS_ctx.elTableColumn; } : 'el-table-column' extends keyof typeof __VLS_ctx ? { 'ElTableColumn': (typeof __VLS_ctx)["el-table-column"]; } : typeof __VLS_resolvedLocalAndGlobalComponents).ElTableColumn;
const __VLS_147 = __VLS_asFunctionalComponent(__VLS_146, new __VLS_146({ ...{}, prop: ("studentCode"), label: ("门店负责人"), minWidth: ("200"), headerAlign: ("center"), }));
({} as { ElTableColumn: typeof __VLS_146; }).ElTableColumn;
({} as { ElTableColumn: typeof __VLS_146; }).ElTableColumn;
const __VLS_148 = __VLS_147({ ...{}, prop: ("studentCode"), label: ("门店负责人"), minWidth: ("200"), headerAlign: ("center"), }, ...__VLS_functionalComponentArgsRest(__VLS_147));
({} as (props: __VLS_FunctionalComponentProps<typeof __VLS_146, typeof __VLS_148> & Record<string, unknown>) => void)({ ...{}, prop: ("studentCode"), label: ("门店负责人"), minWidth: ("200"), headerAlign: ("center"), });
const __VLS_149 = __VLS_pickFunctionalComponentCtx(__VLS_146, __VLS_148)!;
let __VLS_150!: __VLS_NormalizeEmits<typeof __VLS_149.emit>;
}
{
const __VLS_151 = ({} as 'ElTableColumn' extends keyof typeof __VLS_ctx ? { 'ElTableColumn': typeof __VLS_ctx.ElTableColumn; } : 'elTableColumn' extends keyof typeof __VLS_ctx ? { 'ElTableColumn': typeof __VLS_ctx.elTableColumn; } : 'el-table-column' extends keyof typeof __VLS_ctx ? { 'ElTableColumn': (typeof __VLS_ctx)["el-table-column"]; } : typeof __VLS_resolvedLocalAndGlobalComponents).ElTableColumn;
const __VLS_152 = __VLS_asFunctionalComponent(__VLS_151, new __VLS_151({ ...{}, prop: ("studentCode"), label: ("门店手机号"), minWidth: ("200"), headerAlign: ("center"), }));
({} as { ElTableColumn: typeof __VLS_151; }).ElTableColumn;
({} as { ElTableColumn: typeof __VLS_151; }).ElTableColumn;
const __VLS_153 = __VLS_152({ ...{}, prop: ("studentCode"), label: ("门店手机号"), minWidth: ("200"), headerAlign: ("center"), }, ...__VLS_functionalComponentArgsRest(__VLS_152));
({} as (props: __VLS_FunctionalComponentProps<typeof __VLS_151, typeof __VLS_153> & Record<string, unknown>) => void)({ ...{}, prop: ("studentCode"), label: ("门店手机号"), minWidth: ("200"), headerAlign: ("center"), });
const __VLS_154 = __VLS_pickFunctionalComponentCtx(__VLS_151, __VLS_153)!;
let __VLS_155!: __VLS_NormalizeEmits<typeof __VLS_154.emit>;
}
(__VLS_100.slots!).default;
}
{
const __VLS_156 = ({} as 'ElRow' extends keyof typeof __VLS_ctx ? { 'ElRow': typeof __VLS_ctx.ElRow; } : 'elRow' extends keyof typeof __VLS_ctx ? { 'ElRow': typeof __VLS_ctx.elRow; } : 'el-row' extends keyof typeof __VLS_ctx ? { 'ElRow': (typeof __VLS_ctx)["el-row"]; } : typeof __VLS_resolvedLocalAndGlobalComponents).ElRow;
const __VLS_157 = __VLS_asFunctionalComponent(__VLS_156, new __VLS_156({ ...{}, type: ("flex"), justify: ("center"), align: ("middle"), style: ({}), }));
({} as { ElRow: typeof __VLS_156; }).ElRow;
({} as { ElRow: typeof __VLS_156; }).ElRow;
const __VLS_158 = __VLS_157({ ...{}, type: ("flex"), justify: ("center"), align: ("middle"), style: ({}), }, ...__VLS_functionalComponentArgsRest(__VLS_157));
({} as (props: __VLS_FunctionalComponentProps<typeof __VLS_156, typeof __VLS_158> & Record<string, unknown>) => void)({ ...{}, type: ("flex"), justify: ("center"), align: ("middle"), style: ({}), });
const __VLS_159 = __VLS_pickFunctionalComponentCtx(__VLS_156, __VLS_158)!;
let __VLS_160!: __VLS_NormalizeEmits<typeof __VLS_159.emit>;
if (__VLS_ctx.tableIshow) {
{
const __VLS_161 = ({} as 'ElPagination' extends keyof typeof __VLS_ctx ? { 'ElPagination': typeof __VLS_ctx.ElPagination; } : 'elPagination' extends keyof typeof __VLS_ctx ? { 'ElPagination': typeof __VLS_ctx.elPagination; } : 'el-pagination' extends keyof typeof __VLS_ctx ? { 'ElPagination': (typeof __VLS_ctx)["el-pagination"]; } : typeof __VLS_resolvedLocalAndGlobalComponents).ElPagination;
const __VLS_162 = __VLS_asFunctionalComponent(__VLS_161, new __VLS_161({ ...{ onSizeChange: {} as any, onCurrentChange: {} as any, }, currentPage: ((__VLS_ctx.searchNum.pageNum)), pageSizes: (([10, 20, 30, 40, 50])), pageSize: ((__VLS_ctx.searchNum.pageSize)), layout: ("total, sizes, prev, pager, next, jumper"), total: ((__VLS_ctx.total)), }));
({} as { ElPagination: typeof __VLS_161; }).ElPagination;
({} as { ElPagination: typeof __VLS_161; }).ElPagination;
const __VLS_163 = __VLS_162({ ...{ onSizeChange: {} as any, onCurrentChange: {} as any, }, currentPage: ((__VLS_ctx.searchNum.pageNum)), pageSizes: (([10, 20, 30, 40, 50])), pageSize: ((__VLS_ctx.searchNum.pageSize)), layout: ("total, sizes, prev, pager, next, jumper"), total: ((__VLS_ctx.total)), }, ...__VLS_functionalComponentArgsRest(__VLS_162));
({} as (props: __VLS_FunctionalComponentProps<typeof __VLS_161, typeof __VLS_163> & Record<string, unknown>) => void)({ ...{ onSizeChange: {} as any, onCurrentChange: {} as any, }, currentPage: ((__VLS_ctx.searchNum.pageNum)), pageSizes: (([10, 20, 30, 40, 50])), pageSize: ((__VLS_ctx.searchNum.pageSize)), layout: ("total, sizes, prev, pager, next, jumper"), total: ((__VLS_ctx.total)), });
const __VLS_164 = __VLS_pickFunctionalComponentCtx(__VLS_161, __VLS_163)!;
let __VLS_165!: __VLS_NormalizeEmits<typeof __VLS_164.emit>;
let __VLS_166 = { 'size-change': __VLS_pickEvent(__VLS_165['size-change'], ({} as __VLS_FunctionalComponentProps<typeof __VLS_162, typeof __VLS_163>).onSizeChange) };
__VLS_166 = { "size-change": (__VLS_ctx.handleSizeChange) };
let __VLS_167 = { 'current-change': __VLS_pickEvent(__VLS_165['current-change'], ({} as __VLS_FunctionalComponentProps<typeof __VLS_162, typeof __VLS_163>).onCurrentChange) };
__VLS_167 = { "current-change": (__VLS_ctx.handleCurrentChange) };
}
// @ts-ignore
[tableIshow, searchNum, searchNum, total, searchNum, searchNum, total, searchNum, searchNum, total, handleSizeChange, handleCurrentChange,];
}
(__VLS_159.slots!).default;
}
{
const __VLS_168 = ({} as 'ElDialog' extends keyof typeof __VLS_ctx ? { 'ElDialog': typeof __VLS_ctx.ElDialog; } : 'elDialog' extends keyof typeof __VLS_ctx ? { 'ElDialog': typeof __VLS_ctx.elDialog; } : 'el-dialog' extends keyof typeof __VLS_ctx ? { 'ElDialog': (typeof __VLS_ctx)["el-dialog"]; } : typeof __VLS_resolvedLocalAndGlobalComponents).ElDialog;
const __VLS_169 = __VLS_asFunctionalComponent(__VLS_168, new __VLS_168({ ...{ onClose: {} as any, }, visible: ((__VLS_ctx.refundDetailShow)), width: ("40%"), }));
({} as { ElDialog: typeof __VLS_168; }).ElDialog;
({} as { ElDialog: typeof __VLS_168; }).ElDialog;
const __VLS_170 = __VLS_169({ ...{ onClose: {} as any, }, visible: ((__VLS_ctx.refundDetailShow)), width: ("40%"), }, ...__VLS_functionalComponentArgsRest(__VLS_169));
({} as (props: __VLS_FunctionalComponentProps<typeof __VLS_168, typeof __VLS_170> & Record<string, unknown>) => void)({ ...{ onClose: {} as any, }, visible: ((__VLS_ctx.refundDetailShow)), width: ("40%"), });
const __VLS_171 = __VLS_pickFunctionalComponentCtx(__VLS_168, __VLS_170)!;
let __VLS_172!: __VLS_NormalizeEmits<typeof __VLS_171.emit>;
let __VLS_173 = { 'close': __VLS_pickEvent(__VLS_172['close'], ({} as __VLS_FunctionalComponentProps<typeof __VLS_169, typeof __VLS_170>).onClose) };
__VLS_173 = { close: (__VLS_ctx.close) };
{
const __VLS_174 = __VLS_intrinsicElements["span"];
const __VLS_175 = __VLS_elementAsFunctionalComponent(__VLS_174);
const __VLS_176 = __VLS_175({ ...{}, slot: ("title"), }, ...__VLS_functionalComponentArgsRest(__VLS_175));
({} as (props: __VLS_FunctionalComponentProps<typeof __VLS_174, typeof __VLS_176> & Record<string, unknown>) => void)({ ...{}, slot: ("title"), });
const __VLS_177 = __VLS_pickFunctionalComponentCtx(__VLS_174, __VLS_176)!;
let __VLS_178!: __VLS_NormalizeEmits<typeof __VLS_177.emit>;
{
const __VLS_179 = __VLS_intrinsicElements["div"];
const __VLS_180 = __VLS_elementAsFunctionalComponent(__VLS_179);
const __VLS_181 = __VLS_180({ ...{}, }, ...__VLS_functionalComponentArgsRest(__VLS_180));
({} as (props: __VLS_FunctionalComponentProps<typeof __VLS_179, typeof __VLS_181> & Record<string, unknown>) => void)({ ...{}, });
const __VLS_182 = __VLS_pickFunctionalComponentCtx(__VLS_179, __VLS_181)!;
let __VLS_183!: __VLS_NormalizeEmits<typeof __VLS_182.emit>;
(__VLS_ctx.rowData.studentName);
(__VLS_182.slots!).default;
}
{
const __VLS_184 = __VLS_intrinsicElements["div"];
const __VLS_185 = __VLS_elementAsFunctionalComponent(__VLS_184);
const __VLS_186 = __VLS_185({ ...{}, }, ...__VLS_functionalComponentArgsRest(__VLS_185));
({} as (props: __VLS_FunctionalComponentProps<typeof __VLS_184, typeof __VLS_186> & Record<string, unknown>) => void)({ ...{}, });
const __VLS_187 = __VLS_pickFunctionalComponentCtx(__VLS_184, __VLS_186)!;
let __VLS_188!: __VLS_NormalizeEmits<typeof __VLS_187.emit>;
(__VLS_ctx.rowData.studentName);
(__VLS_187.slots!).default;
}
(__VLS_177.slots!).default;
}
{
const __VLS_189 = __VLS_intrinsicElements["div"];
const __VLS_190 = __VLS_elementAsFunctionalComponent(__VLS_189);
const __VLS_191 = __VLS_190({ ...{}, }, ...__VLS_functionalComponentArgsRest(__VLS_190));
({} as (props: __VLS_FunctionalComponentProps<typeof __VLS_189, typeof __VLS_191> & Record<string, unknown>) => void)({ ...{}, });
const __VLS_192 = __VLS_pickFunctionalComponentCtx(__VLS_189, __VLS_191)!;
let __VLS_193!: __VLS_NormalizeEmits<typeof __VLS_192.emit>;
(__VLS_ctx.rowData.studentName);
(__VLS_192.slots!).default;
}
{
const __VLS_194 = __VLS_intrinsicElements["ul"];
const __VLS_195 = __VLS_elementAsFunctionalComponent(__VLS_194);
const __VLS_196 = __VLS_195({ ...{}, }, ...__VLS_functionalComponentArgsRest(__VLS_195));
({} as (props: __VLS_FunctionalComponentProps<typeof __VLS_194, typeof __VLS_196> & Record<string, unknown>) => void)({ ...{}, });
const __VLS_197 = __VLS_pickFunctionalComponentCtx(__VLS_194, __VLS_196)!;
let __VLS_198!: __VLS_NormalizeEmits<typeof __VLS_197.emit>;
{
const __VLS_199 = __VLS_intrinsicElements["li"];
const __VLS_200 = __VLS_elementAsFunctionalComponent(__VLS_199);
const __VLS_201 = __VLS_200({ ...{}, }, ...__VLS_functionalComponentArgsRest(__VLS_200));
({} as (props: __VLS_FunctionalComponentProps<typeof __VLS_199, typeof __VLS_201> & Record<string, unknown>) => void)({ ...{}, });
const __VLS_202 = __VLS_pickFunctionalComponentCtx(__VLS_199, __VLS_201)!;
let __VLS_203!: __VLS_NormalizeEmits<typeof __VLS_202.emit>;
(__VLS_ctx.rowData.studentName);
(__VLS_ctx.rowData.merchantCode);
(__VLS_ctx.rowData.studentName);
}
} __VLS_ctx.节; __VLS_ctx.退交付时长; { { __VLS_ctx.rowData.studentName; } } __VLS_ctx.节; li >
<__VLS_ctx.li>{{ __VLS_ctx, : .rowData.studentName }} );
(__VLS_202.slots!).default;
}
{ }
const __VLS_204 = __VLS_intrinsicElements["li"];
const __VLS_205 = __VLS_elementAsFunctionalComponent(__VLS_204);
const __VLS_206 = __VLS_205({...{}, }, ...__VLS_functionalComponentArgsRest(__VLS_205));
( as (props: __VLS_FunctionalComponentProps<typeof __VLS_204 />, typeof __VLS_206> & Record<string />, unknown>) => void)({...{}, });
const __VLS_207 = __VLS_pickFunctionalComponentCtx(__VLS_204, __VLS_206)!;
let __VLS_208!: __VLS_NormalizeEmits<typeof __VLS_207 />.emit>;
( __VLS_ctx.rowData.studentName );
(__VLS_207.slots!).default;
}
{ }
const __VLS_209 = __VLS_intrinsicElements["li"];
const __VLS_210 = __VLS_elementAsFunctionalComponent(__VLS_209);
const __VLS_211 = __VLS_210({...{}, }, ...__VLS_functionalComponentArgsRest(__VLS_210));
( as (props: __VLS_FunctionalComponentProps<typeof __VLS_209 />, typeof __VLS_211> & Record<string />, unknown>) => void)({...{}, });
const __VLS_212 = __VLS_pickFunctionalComponentCtx(__VLS_209, __VLS_211)!;
let __VLS_213!: __VLS_NormalizeEmits<typeof __VLS_212 />.emit>;
( __VLS_ctx.rowData.studentName );
(__VLS_212.slots!).default;
}
{ }
const __VLS_214 = __VLS_intrinsicElements["li"];
const __VLS_215 = __VLS_elementAsFunctionalComponent(__VLS_214);
const __VLS_216 = __VLS_215({...{}, }, ...__VLS_functionalComponentArgsRest(__VLS_215));
( as (props: __VLS_FunctionalComponentProps<typeof __VLS_214 />, typeof __VLS_216> & Record<string />, unknown>) => void)({...{}, });
const __VLS_217 = __VLS_pickFunctionalComponentCtx(__VLS_214, __VLS_216)!;
let __VLS_218!: __VLS_NormalizeEmits<typeof __VLS_217 />.emit>;
( __VLS_ctx.rowData.studentName );
(__VLS_217.slots!).default;
}
{ }
const __VLS_219 = __VLS_intrinsicElements["li"];
const __VLS_220 = __VLS_elementAsFunctionalComponent(__VLS_219);
const __VLS_221 = __VLS_220({...{}, }, ...__VLS_functionalComponentArgsRest(__VLS_220));
( as (props: __VLS_FunctionalComponentProps<typeof __VLS_219 />, typeof __VLS_221> & Record<string />, unknown>) => void)({...{}, });
const __VLS_222 = __VLS_pickFunctionalComponentCtx(__VLS_219, __VLS_221)!;
let __VLS_223!: __VLS_NormalizeEmits<typeof __VLS_222 />.emit>;
( __VLS_ctx.rowData.studentName );
(__VLS_222.slots!).default;
}
(__VLS_197.slots!).default;
}
(__VLS_171.slots!).default;
}
(__VLS_3.slots!).default;
}
if (typeof __VLS_styleScopedClasses === 'object' && !Array.isArray(__VLS_styleScopedClasses)) {__VLS_styleScopedClasses["frame"]};
}
var __VLS_slots!:;
// @ts-ignore
[refundDetailShow,refundDetailShow,refundDetailShow,close,rowData,rowData,rowData,rowData,rowData,rowData,节,退交付时长,rowData,节,li,rowData,rowData,rowData,rowData,rowData,];
return __VLS_slots;
}
let __VLS_internalComponent!: typeof import('./refundCourseIndexa.vue')['default'];
</>;
}
}
}
