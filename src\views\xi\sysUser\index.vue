<template>
  <div class="app-container">
    <el-form :inline="true" ref="queryForm" class="container-card" label-width="110px">
      <el-form-item label="手机号：">
        <el-input v-model="dataQuery.username" placeholder="手机号" clearable />
      </el-form-item>
      <el-form-item label="角色：">
        <el-select v-model="dataQuery.roleVal" clearable placeholder="请选择">
          <el-option v-for="item in roleValList" :key="item.roleVal" :label="item.roleName" :value="item.roleVal">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-re" @click="getPageList()">查询</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="success" @click="addBtn()">新增</el-button>
      </el-form-item>
    </el-form>
    <el-table class="common-table" v-loading="tableLoading" :data="tableData" style="width: 100%;margin-bottom: 20px;"
      row-key="id" border default-expand-all>
      <el-table-column prop="id" label="ID"></el-table-column>
      <el-table-column prop="username" label="手机号" />
      <el-table-column prop="withdrawnBonus" label="操作" width="480">
        <template slot-scope="scope">
          <el-button type="primary" size="mini" @click="editBtn(scope.row.id)">编辑
          </el-button>
        </template>
      </el-table-column>
      <el-table-column prop="realName" label="姓名" />
      <el-table-column prop="roleVal" label="角色">
        <template slot-scope="scope">
          <el-tag style="margin: 2px;">{{ roleFormat(scope.row.roleVal) }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="gender" label="性别">
        <template slot-scope="scope">
          {{ scope.row.gender === 1 ? '男' : '女' }}
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间" />
    </el-table>
    <!-- 分页 -->
    <el-col :span="24" style="margin-bottom: 20px">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>

    <!-- 修改或者新增弹窗 -->
    <el-dialog title="用户编辑" :visible.sync="open" width="70%" @close="close">
      <el-form ref="form" :model="form" :rules="rules" label-width="130px" style="width: 70%;">
        <el-form-item label="手机号：" prop="username">
          <el-input v-model="form.username" :step="1" :min="1" placeholder="请输入手机号" :disabled="form.id != null" />
        </el-form-item>
        <el-form-item label="角色：" prop="roleVal">
          <el-select v-model="form.roleVal" placeholder="请选择" :disabled="form.id != null">
            <el-option v-for="item in roleValList" :key="item.roleVal" :label="item.roleName" :value="item.roleVal">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="姓名：" prop="realName">
          <el-input v-model="form.realName" :step="1" :min="1" placeholder="请输入姓名" />
        </el-form-item>
        <el-form-item label="性别" prop="gender">
          <el-select v-model="form.gender">
            <el-option label="男" :value="1" />
            <el-option label="女" :value="0" />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import sysUserApi from '@/api/xi/sysUser'
import { pageParamNames } from '@/utils/constants'

export default {
  name: 'sysUser',
  data() {
    return {
      roleValList: [],
      dataQuery: {},
      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      open: false,
      tableData: [],
      form: {},
      // 表单校验
      rules: {
        username: [{ required: true, message: '请输入手机号', trigger: 'blur' }],
        roleVal: [{ required: true, message: '请选择角色', trigger: 'blur' }],
        realName: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
        gender: [{ required: true, message: '请选择性别', trigger: 'blur' }]
      }
    }
  },
  created() {
    this.getRoleList()
    this.getPageList()
  },
  methods: {
    roleFormat(val) {
      for (let item of this.roleValList) {
        if (item.roleVal === val) {
          return item.roleName
        }
      }
      return null
    },
    getRoleList() {
      sysUserApi.roleList().then(res => {
        this.roleValList = res.data
      })
    },
    handleDelete(id) {
      this.$confirm('确定要删除吗？', '删除用户', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        sysUserApi.delete(id).then(res => {
          this.$message.success('删除成功！')
          this.getPageList()
        })
      })
    },
    addBtn() {
      this.reset()
      this.open = true
    },
    editBtn(id) {
      this.reset()
      sysUserApi.detail(id).then(res => {
        this.form = res.data
        this.open = true
      })
    },
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          sysUserApi.saveOrUpdate(this.form).then(response => {
            this.$message.success('提交成功！')
            this.open = false
            this.getPageList()
          })
        }
      })
    },
    getPageList() {
      this.tableLoading = true
      this.dataQuery.pageNum = this.tablePage.currentPage
      this.dataQuery.pageSize = this.tablePage.size
      sysUserApi.list(this.dataQuery).then(res => {
        this.tableData = res.data.data
        this.tableLoading = false
        // 设置后台返回的分页参数
        pageParamNames.forEach(name =>
          this.$set(this.tablePage, name, parseInt(res.data[name]))
        )
      })
    },
    handlePage() {
      (this.tablePage = {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      }),
        this.getPageList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dataQuery = {
        name: '',
        enable: null
      }
      this.getPageList()
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val
      this.getPageList()
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val
      this.getPageList()
    },
    close() {
      this.open = false
      this.reset()
    },
    reset() {
      this.audioFileList = []
      this.form = {
        id: null,
        username: undefined,
        roleVal: undefined,
        realName: undefined
      }
      if (this.$refs['form']) {
        this.$refs['form'].resetFields()
      }
    }
  }
}
</script>

<style>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.period-table td,
.period-table th {
  text-align: center;
}

@media (max-width: 767px) {
  .el-message-box {
    width: 80% !important;
  }
}

.el-tooltip__popper {
  max-width: 800px;
}
</style>
