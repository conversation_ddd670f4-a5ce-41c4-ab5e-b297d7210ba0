<!--采购管理-采购申请-->
<template>
  <div class="app-container">
    <el-form :inline="true" class="container-card" label-width="96px" label-position="left" ref="dataQuery" :model="dataQuery">
      <el-row type="flex" style="flex-wrap: wrap">
        <!-- 采购订单编号 -->
        <el-col :span="5" :xs="24">
          <el-form-item label="采购订单编号:" prop="orderNo" label-width="110px">
            <el-input v-model="dataQuery.orderNo" clearable placeholder="请输入" size="small" style="width: 11vw"></el-input>
          </el-form-item>
        </el-col>
        <!-- 采购类型 -->
        <el-col :span="6" :xs="24" v-if="hasNoPermissionField('采购类型')">
          <el-form-item label="采购类型:" prop="purchaseWay" label-width="96px">
            <el-select v-model="dataQuery.purchaseWay" filterable value-key="value" placeholder="请选择" clearable>
              <el-option
                v-for="(item, index) in [
                  { value: 1, label: '申请' },
                  { value: 2, label: '分配' }
                ]"
                :key="index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="5" :xs="24">
          <el-form-item label="采购内容:" prop="purchaseType" label-width="80px">
            <el-select v-model="dataQuery.purchaseType" filterable value-key="value" placeholder="请选择" clearable>
              <el-option
                v-for="(item, index) in [
                  { value: '定金俱乐部智能学习管理系统', label: '定金俱乐部智能学习管理系统' },
                  { value: '全款俱乐部智能学习管理系统', label: '全款俱乐部智能学习管理系统' },
                  { value: '合伙人智能学习管理系统', label: '合伙人智能学习管理系统' }
                ]"
                :key="index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <!-- 状态 -->
        <el-col :span="5" :xs="24">
          <el-form-item label="状态:" prop="status" label-width="50px">
            <el-select v-model="dataQuery.status" filterable value-key="value" placeholder="请选择" clearable>
              <el-option
                v-for="(item, index) in [
                  { value: 1, label: '待支付' },
                  { value: 2, label: '待发货' },
                  { value: 3, label: '待收货' },
                  { value: 4, label: '已收货' },
                  { value: 5, label: '已失效' }
                ]"
                :key="index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="创建时间：" prop="regTime" label-width="96px">
            <el-date-picker
              style="width: 100%"
              v-model="regTime"
              type="datetimerange"
              value-format="yyyy-MM-dd HH:mm:ss"
              align="right"
              unlink-panels
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            ></el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="4" :xs="20" style="margin-left: auto">
          <el-form-item>
            <el-button
              type="primary"
              icon="el-icon-search"
              @click="
                () => {
                  tablePage.currentPage = 1;
                  fetchData();
                }
              "
            >
              搜索
            </el-button>
            <el-button icon="el-icon-refresh" @click="rest">重置</el-button>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row></el-row>
      <!-- ----------------------------- -->
    </el-form>
    <el-row style="margin-bottom: 20px">
      <el-button size="small" type="primary" icon="el-icon-plus" @click="addPurchase()">新增采购单</el-button>
    </el-row>
    <el-table
      class="common-table"
      v-loading="tableLoading"
      :data="tableData"
      max-height="400px"
      style="width: 100%; margin-bottom: 20px"
      row-key="id"
      border
      default-expand-all
      :header-cell-style="{ color: '#666', height: '60px', background: '#f5f5f5' }"
      :row-style="{ height: '50px' }"
    >
      <el-table-column v-for="(item, index) in tableHeader" :key="index" :prop="item.prop" :label="item.label" :width="item.width">
        <!-- 采购类型 -->
        <!-- <template v-if="item.label == '采购类型'" v-slot="{ row }">
          <span>{{ row.purchaseType == 1 ? '申请' : row.purchaseType == 2 ? '分配' : '其他' }}</span>
        </template> -->
        <!-- 支付方式 -->
        <template v-if="item.label == '支付方式'" v-slot="{ row }">
          <span v-if="row.payTypeDesc == '线下支付'">
            线下支付
            <el-link type="primary" @click="handlePictureCardPreview(row)">查看凭证</el-link>
          </span>
          <span v-else-if="row.payTypeDesc == '在线支付'">在线支付</span>
        </template>
        <!-- 实付金额 -->
        <template v-else-if="item.label == '实付金额（元）'" v-slot="{ row }">
          <span v-if="row.actualAmount == '-'">-</span>
          <span v-else>{{ (row.actualAmount * 0.01).toFixed(2) }}</span>
        </template>
        <!-- 采购单价 -->
        <template v-else-if="item.label == '采购单价（元）'" v-slot="{ row }">
          <span v-if="row.purchasePrice == '0.00'">-</span>
          <span v-else>{{ row.purchasePrice }}</span>
        </template>
        <!-- 总金额 -->
        <template v-else-if="item.label == '总金额（元）'" v-slot="{ row }">
          <span v-if="row.purchaseTotalAmount == '0.00'">-</span>
          <span v-else>{{ row.purchaseTotalAmount }}</span>
        </template>
        <!-- 状态 -->
        <template v-else-if="item.label == '状态'" v-slot="{ row }">
          <el-tag :type="row.status == '待支付' ? 'warning' : row.status == '待发货' ? 'danger' : row.status == '已收货' ? 'success' : 'primary'">
            {{ row.status }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="orderId" label="操作" fixed="right">
        <template v-slot="{ row }">
          <el-button icon="el-icon-check" type="primary" size="mini" @click="confirmReceipt(row)" v-if="row.status == '待收货'" plain>确认收货</el-button>
          <el-button icon="el-icon-position" type="primary" size="mini" @click="goPay(row)" v-else-if="row.status == '待支付' && row.payTypeDesc == '在线支付'" plain>
            去支付
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 查看凭证 -->
    <el-dialog :visible.sync="dialogVisible" width="40%">
      <img width="100%" :src="dialogImageUrl" alt="" />
    </el-dialog>
    <!-- 新增采购单 -->
    <el-dialog :visible.sync="addDialogVisible" width="30%" title="新增采购单" :before-close="restAddForm">
      <el-form label-width="110px" label-position="left" ref="addForm" :model="addForm" style="text-align: left">
        <el-form-item label="采购内容">
          <el-radio-group v-model="addForm.commodityType" class="radio-container" @change="changeRadio">
            <el-radio :label="2" v-if="!isOperations">全款俱乐部智能学习管理系统</el-radio>
            <div class="box-center"></div>
            <el-radio :label="1">合伙人智能学习管理系统</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="采购单价（元）">
          <el-input v-if="isOperations" v-model="clubPurchasePrice" disabled></el-input>
          <el-input v-else v-model="purchasePrice" disabled></el-input>
        </el-form-item>
        <el-form-item label="采购数量" prop="count">
          <el-input-number v-model="addForm.count" :min="1" :max="40" label="采购数量"></el-input-number>
        </el-form-item>
        <el-form-item label="总金额（元）">
          <el-input v-model="total" disabled></el-input>
        </el-form-item>
        <el-form-item label="支付方式" prop="payType">
          <el-radio-group v-model="addForm.payType">
            <el-radio :label="1">在线支付</el-radio>
            <el-radio :label="2">线下支付</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="" v-if="addForm.payType == 2">
          <el-upload
            list-type="picture-card"
            element-loading-text="图片上传中"
            v-loading="uploadLoading"
            action=""
            :limit="1"
            :on-exceed="justPictureNum"
            :file-list="imageList"
            :before-upload="beforeAvatarUpload"
            :http-request="uploadDetailHttpContract"
            :on-preview="handlePictureCardPreviewContract"
            :on-remove="handleRemoveDetailContract"
          >
            <i class="el-icon-plus"></i>
          </el-upload>
          <div class="">上传最大限制10MB</div>
        </el-form-item>
        <el-form-item>
          <el-button @click="restAddForm">取消</el-button>
          <el-button type="primary" @click="submitAdd">提交</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
    <!-- 分页 -->
    <el-col :span="24" style="overflow-x: auto" :xs="24">
      <el-pagination
        :current-page="tablePage.currentPage"
        :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="tablePage.totalItems"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-col>
  </div>
</template>

<script>
  import { mapGetters } from 'vuex';
  import { ossPrClient } from '@/api/alibaba';
  import orderApi from '@/api/purchase/purchaseApply';
  import { dxSource, pageParamNames } from '@/utils/constants';
  import checkPermission from '@/utils/permission';

  import store from '@/store';
  import dayjs from 'dayjs';
  export default {
    name: 'purchaseApply',
    data() {
      return {
        clubPurchasePrice: 0,
        // 查询条件
        dataQuery: {
          orderNo: '',
          purchaseWay: '',
          status: ''
        },
        tableHeader: [
          { label: '采购订单编号', prop: 'orderNo', width: 170 },
          { label: '采购内容', prop: 'purchaseType', width: 130 },
          { label: '采购类型', prop: 'purchaseWay', width: 130 },
          { label: '采购单价（元）', prop: 'purchasePrice', width: 130 },
          { label: '数量', prop: 'purchaseCount', width: 130 },
          { label: '总金额（元）', prop: 'purchaseTotalAmount', width: 130 },
          { label: '支付方式', prop: 'payTypeDesc', width: 130 },
          { label: '实付金额（元）', prop: 'actualAmount', width: 130 },
          { label: '状态', prop: 'status', width: 130 },
          { label: '创建时间', prop: 'createTime', width: 150 },
          { label: '完成时间', prop: 'finishTime', width: 150 }
        ],
        regTime: [],
        purchasePrice: 0, // 采购单价
        addForm: {
          commodityType: 1, //采购类型
          name: '学习管理系统',
          price: '5000',
          count: 1,
          amount: '',
          payType: '',
          imageUrl: ''
        },
        // imageList: [{ url: 'http://dx-applet-mall.oss-cn-shanghai.aliyuncs.com/manage/1741229815000' }],
        imageList: [],
        addDialogVisible: false,
        dialogVisible: false,
        tableLoading: false,
        uploadLoading: false,
        dialogImageUrl: '/static/img/avator.c61856d8.png',
        // 采购申请列表
        tableData: [],
        // 分页
        tablePage: {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null
        },
        isOperations: null, // 是否是俱乐部
        isZxBrand: null, // 是否是品牌
        maxNum: 0,
        brandWholesalePrice: 0,
        oldPrice: 0
      };
    },
    computed: {
      ...mapGetters(['setpayUrl', 'token']),
      // 计算总金额
      total() {
        if (this.isOperations) {
          return (this.clubPurchasePrice * this.addForm.count).toFixed(2);
        } else {
          return (this.purchasePrice * this.addForm.count).toFixed(2);
        }
      }
    },
    activated() {
      this.fetchData();
    },
    mounted() {
      // 俱乐部(Operations)还是品牌(zxBrand)
      this.isOperations = checkPermission(['Operations']);
      this.isZxBrand = checkPermission(['zxBrand']);
      // 品牌
      if (this.isZxBrand) {
        // 删除一些字段
        this.removeNoPermissionField(['采购类型']);
        // this.tableHeader.splice(2, 0, { label: '采购类型', prop: 'name' });
      }
      if (!this.isZxBrand && !this.isOperations) {
        // 只有俱乐部和品牌可以访问
        // this.$alert('权限不足，无法访问');
        this.$message.warning('权限不足，无法访问');
        this.tableHeader = [];
        return;
      }
      this.fetchData();
      ossPrClient();
      // this.addDialogVisible = true;
    },
    watch: {
      'addForm.count': {
        handler(newVal, oldVal) {
          if (newVal == 40) {
            if (this.addForm.commodityType == 2) {
              this.purchasePrice = this.oldPrice;
            } else {
              this.purchasePrice = (this.brandWholesalePrice * 0.01).toFixed(2);
            }
          }
          if (newVal <= 39) {
            this.purchasePrice = this.oldPrice;
          }
        },
        immediate: true
      }
    },
    methods: {
      changeRadio(value) {
        this.addForm.count = 1;
        this.addForm.commodityType = value;

        let data = {
          commodityType: this.addForm.commodityType
        };
        orderApi.queryPurchasePrice(data).then((res) => {
          console.log(res.data.clubPurchasePrice, 'res.data.clubPurchasePrice');

          this.purchasePrice = (res.data.brandPurchasePrice / 100).toFixed(2);
          this.oldPrice = (res.data.brandPurchasePrice / 100).toFixed(2);
          this.brandWholesalePrice = res.data.brandWholesalePrice ? res.data.brandWholesalePrice : 0;
        });
      },

      checkPermission, // 权限判断
      // 查看表头列表是否有该字段
      hasNoPermissionField(field) {
        let has = this.tableHeader.some((i) => {
          if (i.prop == field || i.label == field) {
            return true;
          }
          return false;
        });
        return has;
      },
      // 移除掉没有权限的字段 或只保留 有权限的字段
      /**
       *移除掉没有权限的字段 或只保留 有权限的字段
       * @param array 需要移除或保留的字段
       * @param how true:保留 false:移除
       */
      removeNoPermissionField(array, how = false) {
        this.tableHeader = this.tableHeader.filter((i) => {
          for (let j = 0; j < array.length; j++) {
            if (i.prop == array[j] || i.label == array[j]) {
              return how;
            }
          }
          return !how;
        });
      },
      goPay(row) {
        // console.log(row);
        // console.log(this.setpayUrl);
        orderApi.addPurchaseApplyOnline({ orderNo: row.orderNo }).then((res) => {
          console.log(res.data);

          const split = dxSource.split('##'); //["ZNYY", "BROWSER", "WEB"]
          res.data.dxSource = res.data.registerCallIInfo.appSource + '##' + split[1] + '##' + split[2];
          let params = JSON.stringify(res.data);
          let req = 'token=' + this.token + '&params=' + params + '&back=' + window.location.href;
          //需要编码两遍，避免出现+号等
          var encode = Base64.encode(Base64.encode(req));
          window.open(this.setpayUrl + 'product?' + encode, '_blank');
        });
      },
      // 确认收货
      confirmReceipt(row) {
        let that = this;
        this.$confirm('', '请确认是否签收本次采购申请？', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          center: true,
          type: 'none'
        })
          .then(() => {
            const loading = this.$loading({
              lock: true,
              text: 'Loading',
              spinner: 'el-icon-loading',
              background: 'rgba(0, 0, 0, 0.7)'
            });
            console.log(row, 'this.row');

            orderApi.confirmReceipt({ orderNo: row.orderNo, purchaseType: row.purchaseType }).then((res) => {
              loading.close();
              // console.log(res.data);
              that.fetchData();
              store.dispatch('getSystem');
            });
          })
          .catch(() => {
            loading.close(); // 关闭loading
          });
      },
      // 提交采购申请
      submitAdd() {
        let that = this;
        if (!this.addForm.payType) {
          return this.$message.warning(`请选择支付方式`);
        }
        // 如果是 线下采购，需要上传凭证
        if (this.addForm.payType == 2 && (this.imageList.length <= 0 || !this.imageList[0].url)) {
          return this.$message.warning('请上传凭证');
        }
        let obj = {
          payType: this.addForm.payType,
          count: this.addForm.count,
          commodityType: this.addForm.commodityType
        };
        if (this.addForm.payType == 2) {
          obj.paymentDocument = this.imageList[0].url;
        }
        orderApi
          .addPurchaseApply(obj)
          .then((res) => {
            if (this.addForm.payType == 1) {
              const split = dxSource.split('##'); //["ZNYY", "BROWSER", "WEB"]
              res.data.dxSource = res.data.registerCallIInfo.appSource + '##' + split[1] + '##' + split[2];
              let params = JSON.stringify(res.data);
              let req = 'token=' + this.token + '&params=' + params + '&back=' + window.location.href;
              //需要编码两遍，避免出现+号等
              var encode = Base64.encode(Base64.encode(req));
              window.open(this.setpayUrl + 'product?' + encode, '_blank');
            }
            this.restAddForm();
            this.rest(); // 刷新数据
          })
          .catch((err) => {
            this.$message.error(err.message);
            // console.log(err);
            // if (this.isOperations) {
            //   this.$alert('您的上级品牌剩余学习管理系统数量不足，请联系上级品牌补充数量后重新采购', '温馨提示', {
            //     confirmButtonText: '确定',
            //     callback: (action) => {}
            //   });
            // } else {
            this.restAddForm();
            // }
          });
      },
      //重置/撤销采购申请
      restAddForm() {
        this.addDialogVisible = false; // 关闭弹窗
        this.$refs.addForm.resetFields();
        this.imageList = []; // 清空图片
        // console.log(this.addForm);
      },
      // 上传图片数量超限
      justPictureNum(file, fileList) {
        this.$message.warning(`当前限制选择1个文件`);
      },
      // 删除图片
      handleRemoveDetailContract(file, fileList) {
        const that = this;
        that.imageList = fileList;
      },
      // 上传图片预览
      handlePictureCardPreviewContract(file) {
        this.dialogImageUrl = file.url;
        this.dialogVisible = true;
      },
      // 上传图片
      uploadDetailHttpContract({ file }) {
        this.uploadLoading = true;
        const that = this;
        const fileName = 'manage/' + Date.parse(new Date());
        that.$nextTick(function () {
          ossPrClient()
            .put(fileName, file)
            .then(({ res, url, name }) => {
              if (res && res.status === 200) {
                console.log(`阿里云OSS上传图片成功回调1`, res, url, name);
                that.imageList.push({
                  uid: file.uid,
                  url: url
                });
                that.$nextTick(() => {
                  that.uploadLoading = false;
                });
              }
            })
            .catch((err) => {
              that.$message.error('上传图片失败请检查网络或者刷新页面');
              console.log(`阿里云OSS上传图片失败回调`, err);
              that.uploadLoading = false;
            });
        });
      },
      // 打开添加采购申请
      addPurchase() {
        this.uploadLoading = false; // 关闭上传loading
        // 获取采购单价
        let data = {
          commodityType: this.addForm.commodityType
        };
        orderApi.queryPurchasePrice(data).then((res) => {
          this.clubPurchasePrice = res.data.clubPurchasePrice / 100;
          this.purchasePrice = (res.data.brandPurchasePrice / 100).toFixed(2);
          this.brandWholesalePrice = res.data.brandWholesalePrice ? res.data.brandWholesalePrice : 0;
          this.addDialogVisible = true;
        });
      },
      beforeAvatarUpload(file) {
        const isJPG = file.type === 'image/png' || file.type === 'image/jpeg';
        const isLt2M = file.size / 1024 / 1024 < 10;

        if (!isJPG) {
          this.$message.error('上传图片只能是 jpg/jpeg/png 格式!');
        }
        if (!isLt2M) {
          this.$message.error('上传图片大小不能超过 10MB!');
        }
        return isJPG && isLt2M;
      },
      // 查看图片
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.paymentDocument;
        this.dialogVisible = true;
      },
      // 获取数据
      fetchData() {
        const that = this;
        that.tableLoading = true;
        // 设置查询参数
        if (!this.regTime) {
          this.regTime = [];
        }
        this.dataQuery.startTime = this.regTime[0];
        this.dataQuery.endTime = this.regTime[1];
        // console.log(this.dataQuery);
        orderApi.queryList(that.tablePage.currentPage, that.tablePage.size, that.dataQuery).then((res) => {
          this.tableData = res.data.data;
          this.tableLoading = false;
          // 设置分页
          pageParamNames.forEach((name) => this.$set(this.tablePage, name, Number(res.data[name])));
        });
      },
      //重置
      rest() {
        this.dataQuery = {
          orderNo: '',
          purchaseWay: '',
          status: ''
        };
        // console.log(this.dataQuery);
        this.regTime = '';
        this.tablePage.currentPage = 1;
        this.fetchData();
      },
      // 分页
      handleSizeChange(val) {
        this.tablePage.size = val;
        this.tablePage.currentPage = 1;
        this.fetchData();
      },
      handleCurrentChange(val) {
        this.tablePage.currentPage = val;
        this.fetchData();
      }
    }
  };
</script>

<style lang="scss" scoped>
  ::v-deep.el-dialog__wrapper {
    text-align: center;
    white-space: nowrap;
    overflow: auto;
    &:after {
      content: '';
      display: inline-block;
      vertical-align: middle;
      height: 100%;
    }
    .el-dialog {
      margin: 30px auto !important;
      display: inline-block;
      vertical-align: middle;
      text-align: center;
      white-space: normal;
    }
  }
  .container-card {
    padding: 15px 15px 20px 15px;
    margin-bottom: 15px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  }

  ::v-deep.el-link .el-link--inner {
    font-size: 12px;
  }
  :deep(.el-form-item--small.el-form-item) {
    display: flex;
    overflow: hidden;
  }
  :deep(.el-form-item--small .el-form-item__label) {
    line-height: 32px;
    flex-shrink: 0;
  }
  ::v-deep .el-radio .radio-container {
    display: block;
    margin: 10px 0;
  }
  .box-center {
    height: 15px;
  }
</style>
