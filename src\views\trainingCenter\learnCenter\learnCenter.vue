<template>
  <div></div>
</template>
<script>
  import courseApi from '@/api/training/course';
  import { mapGetters } from 'vuex';
  export default {
    data() {
      return {};
    },
    computed: {
      ...mapGetters(['studyUrl'])
    },
    mounted() {
      console.log('mounted');
      console.log(this.studyUrl, 'this.studyUrl this.studyUrl');
      courseApi
        .noSecretDirect({ loginUserName: localStorage.getItem('ZNYYName'), roleTag: localStorage.getItem('roleTag') })
        .then((res) => {
          console.log(res, '99999999');
          if (res.success) {
            // 线上学习中心跳转地址
            window.open(
              this.studyUrl +
                '#/trainingCenter/courseCenter?isNoSecretLogin=true&appSource=ZNYY&loginUserName=' +
                localStorage.getItem('ZNYYName') +
                '&roleTag=' +
                localStorage.getItem('roleTag')
            );
            this.$router.push({ path: '/dashboard' });
          } else {
            this.$message.error(res.message);
          }
        })
        .catch((err) => {
          console.log('error', err);
        });
    }
  };
</script>
