<template>
  <div class="app-container">
    <el-form :inline="true" class="container-card" label-width="110px" label-position="left">
      <el-form-item label="服务商编号：">
        <el-input v-model="dataQuery.merchantCode" placeholder="请输入服务商编号：" clearable />
      </el-form-item>
      <el-form-item label="登录账号：">
        <el-input v-model="dataQuery.name" placeholder="请输入登录账号：" clearable />
      </el-form-item>
      <el-form-item label="服务商名称：">
        <el-input v-model="dataQuery.merchantName" placeholder="请输入服务商名称：" clearable />
      </el-form-item>
      <el-form-item label="负责人：">
        <el-input v-model="dataQuery.realName" placeholder="请输入负责人：" clearable />
      </el-form-item>
      <el-form-item label="添加时间：">
        <el-date-picker style="width: 100%;" v-model="value1" value-format="yyyy-MM-dd HH:mm:ss" type="daterange"
          align="right" unlink-panels range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
          @change="dateVal" />
      </el-form-item>
      <el-form-item label="推荐人编号：">
        <el-input v-model="dataQuery.marketPartner" placeholder="请输入推荐人编号：" clearable />
      </el-form-item>
      <el-form-item label="状态：">
        <el-select v-model="dataQuery.isEnable" value-key="value" placeholder="请选择" style="width: 200px" clearable>
          <el-option v-for="(item, index) in [
            { label: '开通', value: 1 },
            { label: '暂停', value: 0 },
            { label: '系统关闭', value: -1 },
          ]" :key="index" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="所在地区：">
        <el-input v-model="dataQuery.address" placeholder="请输入所在地区：" clearable />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="fetchData01()">搜索</el-button>
      </el-form-item>
    </el-form>

    <el-col :span="24" style="margin-bottom: 30px">
      <el-button type="success" icon="el-icon-plus" size="mini" @click="clickAdd">添加</el-button>
      <el-button type="warning" icon="el-icon-document-copy" size="mini" v-loading="exportLoading"
        @click="exportList()">导出
      </el-button>
      <!-- <el-button type="warning" icon="el-icon-document-copy" size="mini" v-loading="exportLoading1" @click="exportList1()">导出市级服务商运行数据</el-button> -->
    </el-col>

    <!-- 渲染表格 -->
    <el-table v-loading="tableLoading" class="common-table" :data="tableData" hstyle="width: 100%; margin-bottom: 20px"
      row-key="id" border default-expand-all :tree-props="{ list: 'children', hasChildren: 'true' }">
      <el-table-column prop="merchantCode" label="编号" align="center" width="100px" />
      <el-table-column prop="name" label="登陆账号" align="center" width="120px" />
      <el-table-column label="操作" align="center" width="300">
        <template slot-scope="scope">
          <el-button type="success" icon="el-icon-edit-outline" size="mini" @click="update(scope.row.id)">编辑</el-button>
          <!-- <el-button type="primary" icon="el-icon-edit-outline" size="mini" v-if="scope.row.isCheck == 0" @click="openExamine(scope.row.id)">审核</el-button> -->
          <el-button type="warning" size="mini" icon="el-icon-switch-button"
            v-if="scope.row.isEnable === 0 && scope.row.isCheck === 1" @click="agentStatus(scope.row.id, 1)">开通
          </el-button>
          <el-button type="danger" size="mini" icon="el-icon-video-pause"
            v-if="scope.row.isEnable === 1 && scope.row.isCheck === 1" @click="agentStatus(scope.row.id, 0)">暂停
          </el-button>
          <el-button type="danger" size="mini" icon="el-icon-circle-check"
            v-if="checkPermission(['b:risk:user:clear']) && (scope.row.isEnable === -1 || scope.row.isEnable === -2)"
            @click="agentStatus(scope.row.id, 1)">解封
          </el-button>
          <el-button type="success" size="mini" icon="el-icon-video-pause"
            v-if="scope.row.isEnable === 1 && scope.row.isCheck === 1" @click="onlineCharge(scope.row.merchantCode)">充值服务商
          </el-button>
        </template>
      </el-table-column>
      <el-table-column prop="merchantName" label="名称" align="center" min-width="120px" />
      <el-table-column prop="realName" label="负责人" align="center" width="100px" />
      <el-table-column prop="marketPartner" label="推荐人编号" align="center" width="100px">
      </el-table-column>
      <el-table-column prop="sumChargeMoney" label="累计充值金额（元）" align="center" width="110px">
        <template slot-scope="scope">
          <span v-if="scope.row.sumChargeMoney === null ||
            scope.row.sumChargeMoney === ''
            ">0.00</span>
          <span v-else>{{ scope.row.sumChargeMoney }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="sumRebateMoney" label="累计返点金额（元）" align="center" width="110px">
        <template slot-scope="scope">
          <span v-if="scope.row.sumRebateMoney === null ||
            scope.row.sumRebateMoney === ''
            ">0.00</span>
          <span v-else>{{ scope.row.sumRebateMoney }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="sumGiveMoney" label="累计赠送金额（元）" align="center" width="110px">
        <template slot-scope="scope">
          <span v-if="scope.row.sumGiveMoney === null || scope.row.sumGiveMoney === ''
            ">0.00</span>
          <span v-else>{{ scope.row.sumGiveMoney }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="accountMoney" label="账户余额（元）" align="center" width="110px">
        <template slot-scope="scope">
          <span v-if="scope.row.accountMoney === null || scope.row.accountMoney === ''
            ">0.00</span>
          <span v-else>{{ scope.row.accountMoney }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="address" label="所在地区" align="center" />
      <el-table-column prop="subDealerCount" label="下级托管中心" align="center" />
      <el-table-column prop="subSchoolCount" label="下级门店" align="center" />
      <el-table-column prop="isCheck" label="审核状态" align="center">
        <template slot-scope="scope">
          <span v-if="scope.row.isCheck === 2" class="red">未通过</span>
          <span v-if="scope.row.isCheck === 1" class="green">通过</span>
          <span v-if="scope.row.isCheck === 0" class="red">未审核</span>
        </template>
      </el-table-column>
      <el-table-column prop="isEnable" label="账户状态" align="center">
        <template slot-scope="scope">
          <span v-if="scope.row.isEnable === -1" class="red">系统关闭</span>
          <span v-else-if="scope.row.isEnable === 1" class="green">开通</span>
          <span v-else class="red">暂停</span>
        </template>
      </el-table-column>
      <el-table-column prop="regTime" label="添加时间" align="center" />
    </el-table>

    <el-dialog class="recharge-dialog" title="充值" :visible.sync="showAgentRecharge" width="70%"
      :close-on-click-modal="false">
      <el-form v-model="agentRecharge" label-position="left" label-width="120px" style="width: 100%;">
        <el-form-item label="账户余额(元)：" prop="balance">
          <el-input v-model="agentRecharge.balance" readonly='readonly' disabled />
        </el-form-item>
        <el-form-item label="充值账户：" prop="merchantCode">
          <el-input v-model="agentRecharge.merchantCode" readonly="readonly" disabled />
        </el-form-item>
        <el-form-item label="充值金额(元) ：" prop="rechargeMoney">
          <el-input v-model="agentRecharge.rechargeMoney" type="number" maxlength="20" isNumber2="true" min="1"
            @blur="rechargeMoneyChange()" />
        </el-form-item>
        <el-form-item label="返点金额(元) ：" prop="rechargeBackMoney">
          <el-input v-model="agentRecharge.rechargeBackMoney" readonly="readonly" disabled />
        </el-form-item>
        <el-form-item label="到账金额(元) ：" prop="rechargeSumMoney">
          <el-input v-model="agentRecharge.rechargeSumMoney" readonly="readonly" disabled />
        </el-form-item>
        <el-form-item label="充值说明 ：" prop="rechargeRemark">
          <el-input v-model="agentRecharge.rechargeRemark" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="openDialogVisible()">确定</el-button>
      </div>
    </el-dialog>

    <!-- 审核页面 -->
    <el-dialog title="审核" :visible.sync="showExamine" width="70%" :close-on-click-modal="false" @close="closeExamine">
      <el-form :ref="examine" :rules="rules" :model="examine" label-position="left" label-width="100px"
        style="width: 100%">
        <el-form-item label="是否通过：" prop="isCheck">
          <template>
            <el-radio v-model="isCheck" label="1" @change="change(isCheck)">通过</el-radio>
            <el-radio v-model="isCheck" label="0" @change="change(isCheck)">不通过</el-radio>
          </template>
        </el-form-item>
        <el-form-item label="审核意见">
          <el-col :xs="24" :span="18">
            <el-input type="textarea" resize="none" :rows="4" v-model="examine.checkReason" />
          </el-col>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="
          toExamine(examine.id, examine.checkReason, examine.isCheck, examine)
          ">确定
        </el-button>
        <el-button size="mini" @click="closeExamine">关闭</el-button>
      </div>
    </el-dialog>
    <el-dialog class="remark-dialog" title="交易密码确认" :visible.sync="dialogVisible" width="20%"
      :close-on-click-modal="false" @close="close">
      <el-input v-model="secondPassWord" type="password" id="password" show-password></el-input>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="submitRecharge(secondPassWord)">确认
        </el-button>
      </div>
    </el-dialog>

    <!-- 分页 -->
    <el-col :span="20">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>
  </div>
</template>

<script>
import agentApi from "@/api/agentList";
import { pageParamNames } from "@/utils/constants";
import { ossPrClient } from "@/api/alibaba";
import merchantAccountFlowApi from "@/api/merchantAccountFlow";
import ls from '@/api/sessionStorage'
import checkPermission from '@/utils/permission'

export default {
  name: 'agentList',
  data() {
    return {
      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null,
      },
      agentRecharge: [],
      showAgentRecharge: false,
      uploadLoading: true,
      addOrUpdate: true, // true为添加，false为修改
      dialogVisible: false,
      secondPassWord: '',//二级密码
      dataQuery: {
        merchantCode: "",
        name: "",
        realName: "",
        txtStartTime: "",
        txtEndRegTime: "",
        marketPartner: "",
        address: "",
        isEnable: "",
      },
      value1: "",

      tableData: [],
      updateData: {}, // 更新的数据
      addData: {}, // 新增的数据
      rules: {
        isCheck: [{
          required: true,
          message: "请选择是否通过",
          trigger: "change",
        },],
      },
      content: "",
      isUploadSuccess: true, // 是否上传成功
      showLoginAccount: false,
      showExamine: false, //审核页面
      //审核字段
      examine: {
        isCheck: "",
        checkReason: "",
        id: "",
      },
      isCheck: "",
      exportLoading: false, //导出加载
      exportLoading1: false,
    };
  },
  created() {
    ossPrClient();
    this.fetchData01();
  },
  methods: {
    checkPermission,
    fetchData01() {
      this.tablePage = {
        currentPage: 1,
        size: this.tablePage.size,
        totalPage: null,
        totalItems: null
      }
      this.fetchData();
    },
    // 查询+搜索课程列表
    fetchData() {
      const that = this;
      that.tableLoading = true;
      agentApi
        .agentList(
          that.tablePage.currentPage,
          that.tablePage.size,
          that.dataQuery
        )
        .then((res) => {
          that.tableData = res.data.data;
          // console.log(that.dataQuery.isEnable);
          that.tableLoading = false;
          // 设置后台返回的分页参数
          pageParamNames.forEach((name) =>
            that.$set(that.tablePage, name, parseInt(res.data[name]))
          );
        });
    },
    BlurText1(e) {
      if (e != undefined && e != '' && e != "" && e != null) {
        let boolean = new RegExp("^[1-9][0-9]*$").test(e);
        if (!boolean) {
          this.$message.warning("请输入正整数");
          this.form.waringNumber = "";
        }
        ;
      }
    },
    // 获取起始时间
    dateVal(e) {
      // console.log(e[0]);
      if (e != null) {
        if (e.length > 0) {
          this.dataQuery.txtStartTime = e[0];
          this.dataQuery.txtEndRegTime = e[1];
        } else {
          this.dataQuery.txtStartTime = '';
          this.dataQuery.txtEndRegTime = '';
        }
      }


    },
    // 进入添加页面
    clickAdd() {
      const that = this;
      that.addOrUpdate = true;
      // localStorage.setItem("agentId", "");
      // localStorage.setItem("addOrUpdate", JSON.stringify(that.addOrUpdate));

      ls.setItem('addOrUpdate', that.addOrUpdate);
      ls.removeItem('promoterAgentId')

      that.$router.push({
        path: "/areas/areasAgentAdd",
        query: {
          addOrUpdate: that.addOrUpdate,
        },
      });
    },
    //充值按钮
    onlineCharge(merchantCode) {
      agentApi.getRecharge().then(res => {
        this.agentRecharge.balance = res.data.balance
        this.agentRecharge.merchantCode = merchantCode
        this.showAgentRecharge = true;

        this.agentRecharge = {
          balance: this.agentRecharge.balance,
          merchantCode: this.agentRecharge.merchantCode,
          rechargeMoney: 0,
          rechargeBackMoney: 0,
          rechargeSumMoney: 0,
          rechargeRemark: ""
        }
      })
    },
    //获取返点
    rechargeMoneyChange() {
      if (this.agentRecharge.rechargeMoney !== null && this.agentRecharge.rechargeMoney !== undefined && this.agentRecharge.rechargeMoney !== "") {
        let boolean = new RegExp("^[1-9][0-9]*$").test(this.agentRecharge.rechargeMoney);
        if (!boolean) {
          this.$message.warning("请输入正整数");
          this.agentRecharge.rechargeMoney = "";
          return false;
        }
        ;

        const money = {
          money: this.agentRecharge.rechargeMoney,
          type: "RechargeMoney",
          merchantCode: this.agentRecharge.merchantCode,
        };
        merchantAccountFlowApi.accordingToTheRole(money).then((res) => {
          this.agentRecharge.rechargeSumMoney = res.data.data.finance;
          this.agentRecharge.rechargeBackMoney = res.data.data.money;

        });
      }
    },
    //充值账户确定按钮
    openDialogVisible() {
      if (!this.agentRecharge.merchantCode) {
        this.$message.error("充值账户不能为空");
        return false;
      }
      if (!this.agentRecharge.balance) {
        this.$message.error("账户余额不能为空");
        return false;
      }
      if (!this.agentRecharge.rechargeMoney) {
        this.$message.error("充值金额为空");
        return false;
      }

      if (this.agentRecharge.rechargeMoney <= 0) {
        this.$message.error("充值金额不能为0");
        return false;
      }
      if (!this.agentRecharge.rechargeSumMoney) {
        this.$message.error("到账金额不能为空");
      }
      this.dialogVisible = true;
      this.secondPassWord = ""
    },
    // 进入编辑页面
    update(id) {
      const that = this;
      that.addOrUpdate = false;
      // localStorage.setItem("agentId", id);
      // localStorage.setItem("addOrUpdate", JSON.stringify(that.addOrUpdate));

      ls.setItem('promoterAgentId', id);
      ls.setItem('addOrUpdate', that.addOrUpdate);

      that.$router.push({
        path: "/areas/areasAgentAdd",
        query: {
          addOrUpdate: that.addOrUpdate,
          id: id,
        },
      });
    },
    //二级密码 验证
    submitRecharge(secondPassWord) {
      if (!secondPassWord) {
        this.$message.error('二级密码不能为空')
        return false;
      }
      merchantAccountFlowApi.checkSecondPwd(secondPassWord).then(res => {
        if (!res.success) {
          this.$message.error('二级密码验证失败')
          return false;
        }
        this.$confirm('确定操作吗?', '提交充值', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          agentApi.submitRecharge(this.agentRecharge).then(res => {
            if (!res.success) {
              this.$message.error(res.message)
              return false
            }
            this.$nextTick(() => this.fetchData())
            this.$message.success('充值成功')

          })
          this.showAgentRecharge = false
          this.dialogVisible = false;
        })
      })
    },
    // 分页
    handleSizeChange(val) {
      console.log(val);
      this.tablePage.size = val;
      this.fetchData();
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val;
      this.fetchData();
    },
    exportList1() {
      const that = this;

      if (that.dataQuery.txtStartTime === "" || that.dataQuery.txtStartTime === '') {
        that.$message.info("开始时间不能为空");
        return false;
      }
      if (that.dataQuery.txtEndRegTime === '' || that.dataQuery.txtEndRegTime === "") {
        that.$message.info("结束时间不能为空");
        return false;
      }
      that.exportLoading1 = true;
      const data = {
        'startDate': that.dataQuery.txtStartTime,
        'endDate': that.dataQuery.txtEndRegTime
      }
      agentApi.agentExport1(data).then((response) => {
        console.log(response);
        if (!response) {
          this.$notify.error({
            title: "操作失败",
            message: "文件下载失败",
          });
        }
        const url = window.URL.createObjectURL(response);
        const link = document.createElement("a");
        link.style.display = "none";
        link.href = url; // 获取服务器端的文件名
        link.setAttribute("download", "市级服务商运营数据表.xls");
        document.body.appendChild(link);
        link.click();
        this.exportLoading1 = false;
      });
    },
    // 导出
    exportList() {
      const that = this;
      that.exportLoading = true;
      agentApi.agentExport(that.dataQuery).then((response) => {
        console.log(response);
        if (!response) {
          this.$notify.error({
            title: "操作失败",
            message: "文件下载失败",
          });
        }
        const url = window.URL.createObjectURL(response);
        const link = document.createElement("a");
        link.style.display = "none";
        link.href = url; // 获取服务器端的文件名
        link.setAttribute("download", "市级服务商表.xls");
        document.body.appendChild(link);
        link.click();
        this.exportLoading = false;
      });
    },
    agentPaymentIsComplete(id, paymentIsComplete) {
      if (paymentIsComplete == 0) {
        paymentIsComplete = 1;
      }
      const that = this;
      this.$confirm("确定操作吗？", "完款", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        agentApi.updatePaymentIsComplete(id, paymentIsComplete).then((res) => {
          if (res.success) {
            that.fetchData01();
            that.$message.success("操作成功");
          }
        });
      })
    },
    // 开通与暂停
    agentStatus(id, status) {
      const that = this;
      this.$confirm("确定操作吗?", "修改状态", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          agentApi.updateStatus(id, status).then((res) => {
            if (res.success) {
              that.fetchData01();
              that.$message.success("操作成功");
              // 判断该地区是否已存在开通的市级服务商
              // if (!res.data.success) {
              //   that.fetchData01()
              //   that.$message.error(res.data.errMessage);
              // } else {
              //   that.fetchData01();
              //   that.$message.success(res.message);
              // }
            }
          });
        })
        .catch((err) => {
        });
    },

    updateReProfitRank(id, reProfitRank) {
      if (reProfitRank === 0) {
        reProfitRank = 1;
      } else {
        reProfitRank = 0;
      }
      const that = this;
      this.$confirm("确定操作吗?", "开通推荐二级分润", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          agentApi.updateReProfitRank(id, reProfitRank).then((res) => {
            if (res.success) {
              that.fetchData01();
              that.$message.success("操作成功");
            }
          });
        })
        .catch((err) => {
        });
    },
    // 打开审核弹框
    openExamine(id) {
      this.showExamine = true;
      this.examine.id = id;
      this.isCheck = "";
    },
    // 关闭审核弹框
    closeExamine() {
      this.showExamine = false;
      this.examine.id = "";
    },
    // 状态改变事件
    change(radio) {
      if (radio == "1") {
        this.examine.isCheck = 1;
      } else {
        this.examine.isCheck = 0;
      }
    },
    // 审核
    toExamine(id, checkReason, isCheck, ele) {
      // console.log(typeof long)
      const that = this;
      that.$refs[ele].validate((valid) => {
        // 表单验证
        if (valid) {
          const loading = this.$loading({
            lock: true,
            text: "审核中",
            spinner: "el-icon-loading",
            background: "rgba(0, 0, 0, 0.7)",
          });
          agentApi
            .examine(id, checkReason, isCheck)
            .then((res) => {
              if (res.success) {
                loading.close();
                that.showExamine = false;
                that.$nextTick(() => that.fetchData());
                that.$message.success("审核成功！");
              } else {
                that.$message.warning(res.message);
              }
            })
            .catch((err) => {
              loading.close();
            });
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
  },
};
</script>

<style>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

@media (max-width: 767px) {
  .el-message-box {
    width: 80% !important;
  }
}
</style>
