<template>
  <el-dialog :visible.sync="viewDiaOpen" width="500px" close="close">
    <div v-loading="getDataLoading">
      <div>
        <span>{{ userInfo.isZxBrand ? '俱乐部注册码' : '合伙人注册码' }}</span>
      </div>
      <p style="color: red">使用微信扫码</p>
      <img :src="userInfo.isZxBrand ? zxBrandCodeInfo.qrUrl : userInfo.isOperations ? operationsCodeInfo.qrUrl : ''" alt="" style="width: 200px; height: 200px; margin: 0 auto" />
      <div v-if="userInfo.isZxBrand">
        <p>所属品牌：{{ zxBrandCodeInfo.brandName }}</p>
        <p>渠道合作伙伴：{{ zxBrandCodeInfo.refereeName }}</p>
      </div>
      <div v-if="userInfo.isOperations">
        <p>所属俱乐部：{{ operationsCodeInfo.operationsName }}</p>
        <p>课程推广大使：{{ operationsCodeInfo.refereeName }}</p>
      </div>
      <div style="padding-top: 10px">
        <el-button type="primary" @click="downloadCode">保存二维码</el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script>
  import registrationCodeApi from '@/api/registrationCodeApi/registrationCodeApi';
  export default {
    data() {
      return {
        viewDiaOpen: false,
        userInfo: {
          isZxBrand: false,
          isOperations: false
        },
        operationsCodeInfo: [],
        zxBrandCodeInfo: [],
        getDataLoading: false
      };
    },
    methods: {
      // 获取二维码
      getQR(info, type, code) {
        this.getDataLoading = true;
        let params = {};
        if (info.isOperations) {
          params.isOperationsCode = type;
        } else if (info.isZxBrand) {
          params.isBrandCode = type;
        }
        if (type === 1) {
          params.merchantCode = window.localStorage.getItem('loginMerchantCode');
        } else if (type === 0) {
          params.merchantCode = code;
        }
        console.log('params1111111111111111111', params);
        if (info.isZxBrand) {
          registrationCodeApi
            .ClubReviewCode(params)
            .then((res) => {
              if (res.success) {
                this.zxBrandCodeInfo = res.data;
              } else {
                this.zxBrandCodeInfo = [];
              }
              this.getDataLoading = false;
            })
            .catch(() => {
              this.getDataLoading = false;
            });
        } else if (info.isOperations) {
          registrationCodeApi
            .StoreQRCode(params)
            .then((res) => {
              if (res.success) {
                this.operationsCodeInfo = res.data;
              } else {
                this.operationsCodeInfo = [];
              }
              this.getDataLoading = false;
            })
            .catch(() => {
              this.getDataLoading = false;
            });
        }
      },
      openDialogVisible(userInfo) {
        if (userInfo) {
          this.userInfo = userInfo;
        }
        this.viewDiaOpen = true;
      },
      close() {
        this.viewDiaOpen = false;
      },
      downloadCode() {
        // 创建 canvas
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        const devicePixelRatio = window.devicePixelRatio || 1;
        const scale = 2;

        // 设置实际显示尺寸
        const displayWidth = 375;
        const displayHeight = 812;
        canvas.width = displayWidth * scale;
        canvas.height = displayHeight * scale;
        canvas.style.width = displayWidth + 'px';
        canvas.style.height = displayHeight + 'px';
        ctx.scale(scale, scale);

        // 背景图
        const bgImg = new Image();
        bgImg.crossOrigin = 'anonymous';
        bgImg.src = this.userInfo.isZxBrand ? require('@/assets/clubQR.png') : this.userInfo.isOperations ? require('@/assets/partnerBg.png') : '';
        bgImg.onload = () => {
          ctx.drawImage(bgImg, 0, 0, displayWidth, displayHeight);
          // 二维码
          const img = new Image();
          img.crossOrigin = 'anonymous';
          img.src = this.userInfo.isOperations ? this.operationsCodeInfo.qrUrl : this.userInfo.isZxBrand ? this.zxBrandCodeInfo.qrUrl : '';

          // 绘制图片
          img.onload = () => {
            const imgSize = 144;
            const padding = 10;
            const cornerRadius = 8;
            const containerX = 106;
            const containerY = 445;
            const containerSize = imgSize + padding * 2;

            // 绘制圆角
            ctx.fillStyle = '#ffffff';
            ctx.beginPath();
            ctx.roundRect(containerX, containerY, containerSize, containerSize, cornerRadius);
            ctx.fill();
            const imgX = containerX + padding;
            const imgY = containerY + padding;
            ctx.drawImage(img, imgX, imgY, imgSize, imgSize);

            // 绘制信息
            ctx.fillStyle = '#666666';
            ctx.font = '12px Arial';
            ctx.height = 17;
            ctx.textAlign = 'right';

            // 固定宽度显示省略号
            const truncateTextByWidth = (text, maxWidth = 180) => {
              if (!text) return text;
              const textWidth = ctx.measureText(text).width;
              if (textWidth <= maxWidth) {
                return text;
              }
              let truncatedText = text;
              while (ctx.measureText(truncatedText + '...').width > maxWidth && truncatedText.length > 0) {
                truncatedText = truncatedText.slice(0, -1);
              }
              return truncatedText + '...';
            };

            if (this.userInfo.isZxBrand) {
              ctx.fillText(truncateTextByWidth(this.zxBrandCodeInfo.brandName), displayWidth - 45, displayHeight - 98);
              ctx.fillText(truncateTextByWidth(this.zxBrandCodeInfo.refereeName), displayWidth - 45, displayHeight - 66);
            } else if (this.userInfo.isOperations) {
              ctx.fillText(truncateTextByWidth(this.operationsCodeInfo.operationsName), displayWidth - 45, displayHeight - 98);
              ctx.fillText(truncateTextByWidth(this.operationsCodeInfo.refereeName), displayWidth - 45, displayHeight - 66);
            }

            // 下载图片
            canvas.toBlob((blob) => {
              const url = URL.createObjectURL(blob);
              const link = document.createElement('a');
              link.href = url;
              link.download = this.userInfo.isZxBrand ? '俱乐部注册码.png' : '合伙人注册码.png';
              link.click();
              URL.revokeObjectURL(url);
              this.close();
            });
          };
        };
      }
    }
  };
</script>

<style lang="less" scoped>
  ::v-deep .el-dialog__body {
    padding: 0 20px 30px !important;
    line-height: 1;
    text-align: center;
  }
  ::v-deep .el-dialog__headerbtn {
    top: 10px;
  }
</style>
