<template>
  <!--正式课划音节流程 上传单词流程     （ -2基础视频 -1辅音 0元音 1音节拼读词库 2拼读/拼写  4拼读检测选中）-->
  <div class="StepForm">
    <!-- 上传规则视频 -->
    <el-form :model="ruleVideoForm" ref="ruleVideoForm" :rules="ruleVideoRules" label-width="160px" v-if="activeCode === 0">
      <el-form-item label="规则视频" prop="videoUrl">
        <el-col :span="12">
          <div>
            <input type="file" class="upload" accept="video/mp4,video/webm,video/ogg" @change="doUpload('videoUrl', 'videoUrlInputer', 0)" ref="videoUrlInputer" multiple />
            <!-- <el-button type="primary" size="small" @click="startBaseUrl('videoUrl',0)" v-if="imageForm[0].id || ruleVideoForm.videoUrl">上传</el-button> -->
            <el-button type="danger" size="small" @click="delBaseUrl('videoUrl', 'videoUrlInputer', 0, 'click')" v-if="imageForm[0].id || ruleVideoForm.videoUrl">删除</el-button>
            <div v-if="ruleVideoForm.videoUrl && !imageForm[0].videoUrl && isUploadSuccess">
              <el-image style="width: 148px; height: 148px; background: #f5f7fa">
                <div slot="error" class="course_image_slot">
                  <i class="el-icon-picture-outline"></i>
                  <span>审核中...</span>
                </div>
              </el-image>
            </div>
            <div v-if="imageForm[0].videoUrl">
              <el-image :src="imageForm[0].videoUrl" style="width: 148px; height: 148px" fit="cover "></el-image>
            </div>
          </div>

          <!-- <video
                    v-if="ruleVideoForm.videoUrl"
                    v-bind:src="ruleVideoForm.videoUrl"
                    class="video_upload"
                    :autoplay="false"
                    controls="controls"

                ></video>
                <div class="video_del" @click="videoDel" v-if="ruleVideoForm.videoUrl">
                    <i class="el-icon-delete" color="white"></i>
                </div> -->
          <!-- <el-upload :show-file-list="false" action="" list-type="picture-card" class="upload-demo"  v-loading="uploadLoading"
                        :http-request="uploadRuleHttp" :before-upload="beforeUpload"  >
                        <i class="el-icon-plus"></i>
                        <span>视频上传</span>
                </el-upload> -->
          <!--   -->
          <el-button size="small " type="success" :disabled="!isUploadSuccess" @click="submitVideoValidte(0, 'ruleVideoForm')">下一环节</el-button>
        </el-col>
      </el-form-item>
    </el-form>
    <!-- 划音节 -->
    <el-form :model="drawSyllableForm" v-if="activeCode === 1" ref="drawSyllableForm" :rules="drawSyllableRules" label-width="160px">
      <el-form-item label="单词" prop="word">
        <el-row>
          <el-col :span="12">
            <el-input
              v-model="drawSyllableForm.word"
              type="textarea"
              :spellcheck="false"
              @change="inputChange(drawSyllableForm.word, '0')"
              placeholder="请输入单词"
              :rows="3"
            ></el-input>
          </el-col>
        </el-row>
      </el-form-item>
      <el-form-item label="划音节" prop="syllable">
        <el-row>
          <el-col :span="12">
            <el-input
              v-model="drawSyllableForm.syllable"
              type="textarea"
              @change="inputChange(drawSyllableForm.syllable, '1')"
              :rows="3"
              :spellcheck="false"
              placeholder="请输入音节"
            ></el-input>
          </el-col>
        </el-row>
      </el-form-item>
      <el-form-item label="单词拆分" prop="splitInfo">
        <el-row>
          <el-col :span="12">
            <el-input
              v-model="drawSyllableForm.splitInfo"
              type="textarea"
              :spellcheck="false"
              @change="inputChange(drawSyllableForm.splitInfo, '2')"
              :rows="3"
              placeholder="请输入单词拼读"
            ></el-input>
          </el-col>
        </el-row>
      </el-form-item>
      <el-form-item label="">
        <!-- :on-success="handleSetReadWordSuccess" -->

        <div class="upload_box">
          <el-upload
            class="upload-demo"
            action="#"
            :show-file-list="true"
            :limit="1"
            :http-request="uploadDetailHttp"
            :on-remove="handlSetRemoveReadWord"
            :before-upload="beforeWordUpload"
            ref="myUpload"
          >
            <div class="el-upload__text">
              <el-link icon="el-icon-upload2" :underline="false" class="upload_link">excel文件上传</el-link>
            </div>
          </el-upload>
          <el-link class="download_link" :underline="false" icon="el-icon-download" href="https://document.dxznjy.com/applet/zhimi/config/pd_word.xls">模板下载</el-link>
        </div>
      </el-form-item>
      <el-form-item>
        <el-button size="small " type="success" @click="syllableSubmiteValidte(1, 'drawSyllableForm')">下一环节</el-button>
      </el-form-item>
    </el-form>
    <!-- 划弱音/定长短 -->
    <el-form ref="stressForm" :model="stressForm" v-if="activeCode === 2" :rules="stressRules" label-width="160px">
      <div v-for="(item, topIndex) in stepForm.pdWordRuleDto" :key="item.splitInfo + topIndex">
        <el-divider content-position="left">{{ item.word }}</el-divider>
        <!-- 重音节 -->
        <el-form-item label="重音节" prop="stressWord1">
          <el-row>
            <el-col :span="16">
              <!-- <el-checkbox-group v-model="stressForm.stressWord" @change="onStressChange(item)" > -->
              <el-checkbox-group v-model="stressWordChange[topIndex].stressWord" @change="onStressChange(item, '1', $event, topIndex)">
                <el-checkbox
                  v-for="(f, fIndex) in item.ruleWordTypeDtoList"
                  :label="computeLabel(f.syllable, item.ruleWordTypeDtoList, fIndex)"
                  :disabled="f.disabled"
                  :key="fIndex"
                ></el-checkbox>
              </el-checkbox-group>
            </el-col>
            <el-col :span="8">
              <el-button type="success" :disabled="item.disabled" @click="onStressConfirm(item, topIndex, true)">确定</el-button>
              <el-button type="primary" @click="onStressRevise(item, topIndex)">修改</el-button>
            </el-col>
          </el-row>
        </el-form-item>
        <!-- 重音节 -->
        <el-form-item label="次重音节" prop="stressWord1">
          <el-row>
            <el-col :span="16">
              <!-- <el-checkbox-group v-model="stressForm.stressWord" @change="onStressChange(item)" > -->
              <el-checkbox-group v-model="stressWordChange[topIndex].secondaryStressedWords" @change="onStressChange(item, '2', $event, topIndex)">
                <el-checkbox
                  v-for="(f, fIndex) in item.ruleWordTypeDtoList"
                  :label="computeLabel(f.syllable, item.ruleWordTypeDtoList, fIndex)"
                  :disabled="f.disabled"
                  :key="fIndex"
                ></el-checkbox>
              </el-checkbox-group>
            </el-col>
          </el-row>
        </el-form-item>
        <!-- 弱音从非重音音节中选字母 -->
        <el-form-item label="划弱音" v-if="item.checkWordStatus == '0'">
          <span style="float: right; color: red; margin-right: 40px">请按顺序选择弱音</span>
          <!-- <div v-if="weakenedTypesList[topIndex][0].stressItem && weakenedTypesList[topIndex][0].stressItem.weakenedWords"> -->
          <div class="stress_data" v-for="(stressItem, weakIndex) in weakenedTypesList[topIndex]" :key="stressItem.type">
            <div>
              <span class="stress_data_item">{{ stressItem.name }}</span>
              <!-- <span>{{ weakenedWords }}</span> -->
              <el-checkbox-group v-model="stressItem.weakTone" @change="onWeakChange(stressItem, topIndex, weakIndex)">
                <!-- weakLabel(fs, stressItem.syllable.split(''), fsIndex) -->
                <el-checkbox v-for="(fs, fsIndex) in stressItem.weakenedWords" :label="fs.too ? fs.syllable + fs.too : fs.syllable" :key="fsIndex"></el-checkbox>
              </el-checkbox-group>
            </div>
            <!-- </div> -->
          </div>
        </el-form-item>
        <!-- 长短音从重音音节中选字母 -->
        <el-form-item label="重音定长短" v-if="item.checkWordStatus == '0'">
          <!-- showStressWord -->
          <div v-if="getShowStressWordBool(topIndex)">
            <div v-for="types in fixedTypes" :key="types.lengthType">
              <div class="long_title_css">
                <span>({{ types.lengthType }})</span>
                <span>{{ types.name }}</span>
              </div>
              <!-- <div  class="stress_data_flex " > -->
              <div class="stress_data" v-for="(stressItem, lsIndex) in item.ruleWordTypeDtoList" :key="stressItem.syllable + lsIndex">
                <div style="padding-right: 50px" v-if="stressItem.type === '1' && stressItem.disabled">
                  <span class="stress_data_item">{{ stressItem.syllable }}</span>
                  <el-radio-group v-model="wordMsgchange[topIndex].wordItem[lsIndex].longShortTone" @change="onLongShortChange(stressItem, topIndex, lsIndex)">
                    <el-radio
                      v-for="(fl, flIndex) in stressItem.syllable.split('')"
                      :label="longLabel(fl, stressItem.syllable.split(''), flIndex, types.lengthType)"
                      :key="flIndex"
                    >
                      {{ longLabel(fl, stressItem.syllable.split(''), flIndex, '') }}
                    </el-radio>
                  </el-radio-group>
                </div>
              </div>
              <!-- </div> -->
            </div>
          </div>
        </el-form-item>
        <el-form-item label="次重音定长短" v-if="item.checkWordStatus == '0' && courseLevel == 2">
          <!-- showStressWord -->
          <div v-if="getShowCiStressWordBool(topIndex)">
            <div v-for="types in fixedTypes" :key="types.lengthType">
              <div class="long_title_css">
                <span>({{ types.lengthType }})</span>
                <span>{{ types.name }}</span>
              </div>
              <!-- <div  class="stress_data_flex " > -->
              <div class="stress_data" v-for="(stressItem, lsIndex) in item.ruleWordTypeDtoList" :key="stressItem.syllable + lsIndex">
                <div style="padding-right: 50px" v-if="stressItem.type === '2' && stressItem.disabled">
                  <span class="stress_data_item">{{ stressItem.syllable }}</span>
                  <el-radio-group v-model="wordMsgchange[topIndex].wordItem[lsIndex].cilongShortTone" @change="onCiLongShortChange(stressItem, topIndex, lsIndex)">
                    <el-radio
                      v-for="(fl, flIndex) in stressItem.syllable.split('')"
                      :label="longLabel(fl, stressItem.syllable.split(''), flIndex, types.lengthType)"
                      :key="flIndex"
                    >
                      {{ longLabel(fl, stressItem.syllable.split(''), flIndex, '') }}
                    </el-radio>
                  </el-radio-group>
                </div>
              </div>
              <!-- </div> -->
            </div>
          </div>
        </el-form-item>
      </div>
      <el-form-item>
        <el-button size="small " type="success" @click="syllableSubmiteValidte2(2)">下一环节</el-button>
      </el-form-item>
    </el-form>
    <!-- 练习拼读词 -->
    <el-form ref="readTestForm" :model="readTestForm" :rules="readTestRules" v-if="activeCode === 3">
      <el-form-item label="划音节词:" :prop="listId ? '' : 'readTestcheckList'">
        <el-checkbox-group style="margin-top: 40px" v-model="readTestForm.readTestcheckList">
          <el-checkbox v-for="(city, index) in cities" :label="city" :key="index">{{ city }}</el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="练习拼读词:" prop="writeWord" style="margin-top: 40px">
        <div></div>
        <el-input readonly disabled type="textarea" :value="readTestForm.readTestcheckList.join(',')" aria-label="练习拼读词"></el-input>
      </el-form-item>
      <el-form-item label="">
        <el-button size="small" type="success" @click="syllableSubmiteValidte3(3)">下一环节</el-button>
      </el-form-item>
    </el-form>
    <!-- 练习拼写词 -->
    <el-form ref="readTestForm" :model="readTestForm" :rules="readTestRules" v-if="activeCode === 4">
      <el-form-item label="划音节词:">
        <el-checkbox-group style="margin-top: 40px" v-model="readTestForm.witreTestcheckList">
          <el-checkbox v-for="(city, index) in cities" :label="city" :key="index">{{ city }}</el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="练习拼写词:" prop="writeWord" style="margin-top: 40px">
        <div></div>
        <el-input readonly disabled type="textarea" :value="readTestForm.witreTestcheckList.join(',')" aria-label="练习拼写词"></el-input>
      </el-form-item>
      <el-form-item label="">
        <el-button size="small" type="success" @click="syllableSubmiteValidte3(4)">下一环节</el-button>
      </el-form-item>
    </el-form>
    <!-- 学后读检测词 -->
    <el-form ref="readTestForm" :model="readTestForm" :rules="readTestRules" v-if="activeCode === 5">
      <el-form-item label="划音节词:" :prop="listId ? '' : 'postReadcheckList'">
        <el-checkbox-group style="margin-top: 40px" v-model="readTestForm.postReadcheckList">
          <el-checkbox v-for="(city, index) in cities" :label="city" :key="index">{{ city }}</el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="学后读检测词:" prop="'writeWord'" style="margin-top: 40px">
        <div></div>
        <el-input readonly disabled type="textarea" :value="readTestForm.postReadcheckList.join(',')" aria-label="学后读检测词"></el-input>
      </el-form-item>
      <el-form-item label="">
        <el-button size="small" type="success" @click="syllableSubmiteValidte3(5)">下一环节</el-button>
      </el-form-item>
    </el-form>
    <!-- 学后写检测词 -->
    <el-form ref="readTestForm" :model="readTestForm" :rules="readTestRules" v-if="activeCode === 6">
      <el-form-item label="划音节词:">
        <el-checkbox-group style="margin-top: 40px" v-model="readTestForm.postWittecheckList">
          <el-checkbox v-for="(city, index) in cities" :label="city" :key="index">{{ city }}</el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="学后写检测词:" prop="writeWord" style="margin-top: 40px">
        <div></div>
        <el-input readonly disabled type="textarea" :value="readTestForm.postWittecheckList.join(',')" aria-label="学后写检测词"></el-input>
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
  import UploadFile from '@/components/Upload/UploadFile.vue';
  import { ossPrClient } from '@/api/alibaba';
  import courseDictationListApi from '@/api/courseDictationList';
  import md5 from 'js-md5';
  import PlvVideoUpload from '@polyv/vod-upload-js-sdk';
  import { Loading } from 'element-ui';
  import { t } from '@wangeditor/editor';
  export default {
    components: { UploadFile },
    props: {
      makeCourseType: {
        type: String,
        default: ''
      },
      readWriteobj: {
        type: Object,
        default: {}
      },
      listId: {
        type: String,
        default: 0
      },
      //弹窗
      syllableDialogVisible: {
        type: Boolean,
        default: false
      },
      //进度条
      active: {
        type: Number,
        default: 0
      },
      formData: {
        type: Object,
        default: {}
      }
    },
    name: 'StepForm',
    data() {
      return {
        //规则视频
        ruleVideoForm: {
          videoUrl: ''
        },
        cities: [],
        // showStressWord:true,
        showStressWordList: [],
        showCiStressWordList: [],
        videoUpload: null, //
        userid: '1723c88563', //从点播后台查看获取
        secretkey: 'Jkk4ml1Of8', //从点播后台查看获取
        writeToken: '94064a19-4d28-4ce9-aa05-96d476016215',
        imageForm: [{ name: 'videoUrl', videoUrl: '', id: '', status: '' }],
        ruleVideoRules: {
          videoUrl: [{ required: true, message: '请上传规则视频', trigger: 'blur' }]
        },
        drawSyllableRules: {
          word: [
            {
              validator: (rule, value, callback) => {
                this.stepWordValidate(rule, value, callback, this.drawSyllableForm);
              },
              trigger: 'blur'
            }
          ],
          //音节不做限制  有的单词没有音节
          // syllable:[{
          //     validator: (rule,value,callback)=> {
          //         this.stepSyllableValidate(rule,value,callback,this.drawSyllableForm)
          //     },
          //     trigger: 'blur' }],
          splitInfo: [
            {
              validator: (rule, value, callback) => {
                this.stepSplitInfoValidate(rule, value, callback, this.drawSyllableForm);
              },
              trigger: 'blur'
            }
          ]
        },
        drawSyllableForm: {
          word: '',
          syllable: '',
          splitInfo: ''
        },
        //划音节
        stressForm: {
          stressWord: [],
          weakTone: [],
          longShortTones: [],
          cilongShortTone: []
        },
        spellForm: {
          readCheckWord: '',
          writeCheckWord: ''
        },
        spellRules: {}, //拼读拼写规则
        stressRules: {}, //划弱音定长短规则
        readTestRules: {
          readTestcheckList: [{ required: true, message: '请选择练习拼读词', trigger: 'change' }],
          postReadcheckList: [{ required: true, message: '请选择学后读检测词', trigger: 'change' }]
        },
        readTestForm: {
          //练习拼读词数组
          readTestcheckList: [],
          //练习拼写词数组
          witreTestcheckList: [],
          // 学后读数组
          postReadcheckList: [],
          postWittecheckList: []
        }, //拼读检测词
        writeTestForm: {}, //拼写检测词
        writeTestRules: {}, //拼写检测词规则
        weakToneData: [], //非重音音节
        stressIsDisabled: false, //重音是否禁用
        fileListPending: [], // 待处理已上传图片信息
        fileList: [], // 上传图片已有图片列表
        uploadLoading: false, // 上传图片加载按钮
        isUploadSuccess: false, // 是否上传成功
        fileDetailList: [],
        addOrUpdate: true,
        addBroadcastData: { url: '' },
        //化弱音
        weakenedWords: [],
        fixedLengthWords: [], //定长短
        // weakenedTypes:[{name:'ur音',type:0,weakenedWords:[],weakTone:[]},{name:'i音',type:3,weakenedWords:[],weakTone:[]}],
        weakenedTypesList: [],
        fixedTypes: [],
        fd: '',
        activeCode: 0,
        videoUrlList: [],
        filterWords: [], //拼读检测词
        filterData: [],
        //元音
        yForm: { vowel: '' },
        yRules: {
          vowel: [{ required: true, message: '请输入元音词库', trigger: 'blur' }]
        },
        //辅音
        fForm: { consonant: '' },
        fRules: {
          consonant: [{ required: true, message: '请输入辅音词库', trigger: 'blur' }]
        },
        //音节拼读词库
        syllableForm: { word: '' },
        syllableRules: {
          word: [{ required: true, message: '请输入音节拼读词库', trigger: 'blur' }]
        },
        //音节拼读 拼读拼写重复部分
        repeatForm: { videoUrl: '', readWord: '', writeWord: '' },
        repeatRules: {
          readWord: [{ required: true, message: '请输入拼读单词', trigger: 'blur' }],
          writeWord: [{ required: true, message: '请输入拼写单词', trigger: 'blur' }]
        },
        //拼读拼写检测
        testForm: { writeCheckWord: '', readCheckWord: '' },
        rules: {
          yuanWord: [{ required: true, message: '请输入元辅音词库', trigger: 'blur' }],
          videoUrl: [
            {
              required: true,
              message: '请上传学习规则录播视频',
              trigger: 'blur'
            }
          ],
          yinReadWord: [
            {
              required: true,
              message: '请上传学习规则录播视频',
              trigger: 'blur'
            }
          ],
          readWord: [{ required: true, message: '请输入拼读单词', trigger: 'blur' }],
          writeWord: [{ required: true, message: '请输入拼写词', trigger: 'blur' }],
          writeCheckWord: [{ required: true, message: '请输入拼写检测词', trigger: 'blur' }],
          readCheckWord: [{ required: true, message: '请输入拼读检测词', trigger: 'blur' }]
        },
        //后端需要数据格式
        stepForm: {
          id: this.$route.query.id,
          wordType: '4',
          videoUrl: '',
          pdWordRuleDto: []
        },
        makeData: {
          id: '',
          courseType: ''
        },
        studyWord: [],
        showWriteSelectWord: false, //拼写单词
        showReadSelectWord: false, //拼读单词
        nextRead: false, //进入拼读阶段
        nextWrite: false, //进入拼写阶段
        dialogVisible: false, //弹窗状态  关闭弹窗需要表单重置
        formDetail: {},
        readSyllableData: [],
        stressWordChange: [], //重音选中
        wordMsgchange: [], //单词的弱音、长短音
        longShortTonesChange: [], //长短音选中
        vid: '',
        hsaStress: false, //有重音
        // checkWordStatus:0,//选中重音节状态
        loadingInstance: null,
        courseLevel: 0
      };
    },
    created() {
      this.courseLevel = this.$route.query.courseLevel;
      if (this.courseLevel == 2) {
        this.fixedTypes = [
          { lengthType: 1, name: 'a-e' },
          { lengthType: 2, name: 'i-e' },
          { lengthType: 3, name: 'e-e' },
          { lengthType: 4, name: 'o-e' },
          { lengthType: 5, name: 'u-e' },
          { lengthType: 6, name: 'oo' }
        ];
      } else {
        this.fixedTypes = [
          { lengthType: 1, name: 'a-e' },
          { lengthType: 2, name: 'i-e' },
          { lengthType: 3, name: 'e-e' },
          { lengthType: 4, name: 'o-e' },
          { lengthType: 5, name: 'u-e' }
        ];
      }
      ossPrClient();
      //上传规则视频，保利威视频初始化
      this.videoUpload = new PlvVideoUpload({
        region: 'line1', // (可选)上传线路, 默认line1
        events: {
          Error: (err) => {
            // 错误事件回调
            console.log(err);
            let errMag = `${err.message}`;
            this.$alert(errMag, '标题名称', {
              confirmButtonText: '确定',
              type: 'error'
            });
          },
          UploadComplete: () => {
            // 全部上传任务完成回调

            this.$message({
              message: '上传成功',
              type: 'success'
            });
          }
        }
      });
    },
    mounted() {
      this.autoUpdateUserData(null, this.videoUpload);
      this.$bus.$on('clearsyllableData', () => {
        this.stressWordChange = [];
        this.stressForm.stressWord = [];
        this.stressForm.weakTone = [];
        this.wordMsgchange = [];
        this.stressForm.longShortTones = [];
        this.stressForm.cilongShortTone = [];
        this.longShortTonesChange = [];
      });
    },
    watch: {
      makeCourseType(val) {
        if (val) {
          this.makeData.courseType = val;
        }
      },
      syllableDialogVisible(val) {
        this.dialogVisible = val;
        this.stressWordChange = [];
        this.wordMsgchange = [];
      },

      //进度条
      active(val) {
        this.activeCode = val;
      },
      handleCheckedCitiesChange(e) {},
      //详情内容
      formData(val) {
        if (val) {
          this.formDetail = val;
          // this.stepForm = val
          this.ruleVideoForm.videoUrl = val.videoUrl;
          if (this.ruleVideoForm.videoUrl) {
            courseDictationListApi.getVideoInfo(this.ruleVideoForm.videoUrl).then((res) => {
              if (res.code === 20000) {
                this.imageForm[0].videoUrl = res.data.data.data[0].basicInfo.coverURL;
                this.imageForm[0].status = res.data.data.data[0].basicInfo.status;
              }
            });
          }
          this.stepForm['listId'] = this.formDetail.listId;
          // this.stepForm.id = this.$route.query.id;
          const word = [];
          let syllable = [];
          const splitInfo = [];
          //单词,音节,单词拼读转化成页面展示格式   例:  word,read  wo,rd-re,ad  wo,word-re,read
          this.formDetail.pdWordRuleDto.forEach((item, Iindex) => {
            // if (!item.isNotEdit) {
            word.push(item.word);
            // weakenedTypes
            item.ruleWordTypeDtoList.forEach((res, Rindex) => {
              Iindex === Rindex ? syllable.push(res.syllable) : Iindex > Rindex ? syllable.push(`-${res.syllable}`) : syllable.push(res.syllable);
            });
            Iindex === 0 ? splitInfo.push(item.splitInfo) : splitInfo.push(`-${item.splitInfo}`);
            // }else{
            //   console.log('不要编辑这个');
            //   notEditItem.push(item)//这是不用编辑的单词
            // }
          });
          // 去除带进来[]
          syllable = syllable.map((e) => e.replace(/\[.*\]$/, ''));
          this.drawSyllableForm.word = word.join(',');
          this.drawSyllableForm.syllable = syllable.join(',').replace(/,-/g, '-');
          this.drawSyllableForm.splitInfo = splitInfo.join(',').replace(/,-/g, '-');
          this.isUploadSuccess = true;
          this.stressWordChange = [];
          this.stressForm.stressWord = [];
          this.stressForm.secondaryStressedWords = [];
          this.stressForm.weakTone = [];
          this.wordMsgchange = [];
          this.stressForm.longShortTones = [];
          this.stressForm.cilongShortTone = [];
          this.longShortTonesChange = [];

          // this.stepForm.notEditItem = notEditItem
          console.log(this.stepForm, 'stepFormstepFormstepFormstepForm');
        }
      }
    },

    methods: {
      weakLabel(syllable, syllableList, fsIndex) {
        // 处理重音节重复字母
        let label = '';
        let repeatCount = 0;
        syllableList.forEach((item, Index) => {
          if (item == syllable) {
            repeatCount++;
            if (Index == fsIndex) {
              label = repeatCount > 1 ? `${syllable}[${repeatCount}]` : `${syllable}`;
            }
          }
        });
        return label;
      },
      longLabel(syllable, syllableList, fsIndex, lengthType) {
        // 处理重音节重复字母
        let label = '';
        let repeatCount = 0;
        syllableList.forEach((item, Index) => {
          if (item == syllable) {
            repeatCount++;
            if (Index == fsIndex) {
              label = repeatCount > 1 ? `${syllable}[${repeatCount}]` : `${syllable}`;
            }
          }
        });
        return lengthType ? label + '|' + lengthType : label + lengthType;
      },
      computeLabel(syllable, ruleWordTypeDtoList, fIndex) {
        // 处理重音节重复字母
        let label = '';
        let repeatCount = 0;
        ruleWordTypeDtoList.forEach((item, Index) => {
          if (item.syllable == syllable) {
            repeatCount++;
            if (Index == fIndex) {
              label = repeatCount > 1 ? `${syllable}[${repeatCount}]` : `${syllable}`;
            }
          }
        });
        return label;
      },
      //禁止输入单词时输入中文和中文符号
      inputChange(val, type) {
        console.log('val', val, type);
        let key = type == '0' ? 'word' : type == '1' ? 'syllable' : 'splitInfo';
        let newValue = val.replace(/[^a-zA-Z,-]/g, '');
        newValue = newValue.replace(/,*$/, '');
        newValue = newValue.replace(/,+/g, ',');
        // const chineseRegex = /[\u4e00-\u9fa5\u3000-\u303F\uFF00-\uFFEF]+/g;
        // let newValue = val.replace(chineseRegex, ''); // 新的值
        // newValue = newValue.replace(/\d/g, '');  //去除数字  使用replace方法替换掉所有匹配到的中文和中文标点符号为空字符串
        this.drawSyllableForm[key] = newValue;
      },
      autoUpdateUserData(timer, videoUpload) {
        // 启动获取用户信息
        this.getUserData(videoUpload);
        if (timer) {
          clearTimeout(timer);
          timer = null;
        }
        timer = setTimeout(() => {
          this.autoUpdateUserData(timer, videoUpload);
        }, 3 * 50 * 1000);
      },
      getUserData() {
        // 获取用户详细信息
        this.ptime = new Date().getTime();
        let userData = {
          userid: this.userid,
          ptime: this.ptime,
          sign: this.getSignData().sign,
          hash: this.getSignData().hash
        };
        this.videoUpload.updateUserData(userData);
      },
      getSignData() {
        // 加密信息参数
        let hash = md5(this.ptime + this.writeToken);
        let sign = md5(this.secretkey + this.ptime);
        return {
          hash: hash,
          sign: sign
        };
      },
      transformSize(bytes) {
        // 文件大小转换
        const bt = parseInt(bytes);
        let result;
        if (bt === 0) {
          result = '0B';
        } else {
          const k = 1024;
          const sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
          const i = Math.floor(Math.log(bt) / Math.log(k));
          if (typeof i !== 'number') {
            result = '-';
          } else {
            result = (bt / Math.pow(k, i)).toFixed(2) + sizes[i];
          }
        }
        return result;
      },
      loadPlayerScript(callback) {
        if (!window.polyvPlayer) {
          const myScript = document.createElement('script');
          myScript.setAttribute('src', this.vodPlayerJs);
          myScript.onload = callback;
          document.body.appendChild(myScript);
        } else {
          callback();
        }
      },
      loadPlayerSummaryUrl() {
        const polyvPlayer = window.polyvPlayer;
        this.player = polyvPlayer({
          wrap: `#summaryUrlPlayer`,
          width: 148,
          height: 148,
          vid: this.videoForm.summaryUrl
        });
      },
      delBaseUrl(value, refName, Index, msg) {
        if (this.videoUpload) {
          courseDictationListApi.deleteVideo(this.ruleVideoForm[value]).then((res) => {
            if (res.code === 20000) {
              if (msg == 'click') {
                this.$message.success('删除成功');
                this.isUploadSuccess = false;
              }
            }
          });
          this.videoUpload.removeFile(this.imageForm[Index].id);
          setTimeout(() => {
            (this.$refs[refName].value = ''), (this.imageForm[Index][value] = '');
            this.imageForm[Index].id = '';
            this.ruleVideoForm[value] = '';
          }, 0);
          // this.videoUpload.clearAll();
        }
      },
      startBaseUrl(value, Index) {
        // 全部上传
        if (this.videoUpload) {
          // this.videoUpload.resumeFile(this.videoForm[value]);
          this.videoUpload.resumeFile(this.imageForm[Index].id);
          // this.videoUpload.startAll();
        }
      },
      doUpload(value, refName, Index) {
        let that = this;
        // this.loadingInstance = Loading.service({
        //   lock: true,
        //   text: '视频上传中...',
        //   spinner: 'el-icon-loading',
        //   background: 'rgba(0, 0, 0, 0.7)'
        // });
        let inputDOM = this.$refs[refName]; // 通过DOM取文件数据
        let data = Object.values(inputDOM.files);
        const videoFormat = ['video/mp4', 'video/webm', 'video/ogg'];
        if (!videoFormat.includes(data[0].type)) {
          console.log('选择了非视频格式文件', inputDOM);
          this.$refs[refName].value = null;
          this.$message.warning('请选择视频格式文件');
          return;
        } else {
          console.log('选择的是视频格式文件');
        }
        if (data.length > 0) {
          console.log(process.env, 2222222222222);

          data.forEach((file, index, arr) => {
            let fileSetting = {
              // 文件上传相关信息设置
              title: file.name, // 标题
              desc: '划拼音规则', // 描述
              cataid: process.env.VUE_APP_BASE_CATAID, // 上传分类目录ID
              tag: '', // 标签
              luping: 0, // 是否开启视频课件优化处理，对于上传录屏类视频清晰度有所优化：0为不开启，1为开启
              keepsource: 0, // 是否源文件播放（不对视频进行编码）：0为编码，1为不编码
              state: '' //用户自定义信息，如果提交了该字段，会在服务端上传完成回调时透传返回。
            };
            let uploadManager = this.videoUpload.addFile(
              file, // file 为待上传的文件对象
              {
                FileStarted: this.onFileStarted, // 文件开始上传回调
                FileProgress: this.onFileProgress, // 文件上传中回调
                FileSucceed: (data) => this.onFileSucceed(data, value, Index), // 文件上传成功回调
                FileFailed: this.onFileFailed, // 文件上传失败回调
                FileStopped: this.onFileStopped // 文件上传停止回调
              },
              fileSetting
            );
            console.log(uploadManager, 'uploadManageruploadManager');
            // this.loadPlayerScript(this.loadPlayer);

            this.addTableData(uploadManager, value, Index);
            this.videoUpload.resumeFile(this.imageForm[Index].id);
          });
        }
      },
      onFileStarted(data) {
        console.log('文件上传开始: ', data);
        // this.tableData.filter((item) => item.id === data.uploaderid)[0].progress = 0
      },
      onFileProgress(data) {
        let p = parseInt(data.progress * 100); // 上传的进度条
        // this.tableData.filter((item) => item.id === data.uploaderid)[0].progress = p
      },
      addTableData(data, value, Index) {
        console.log(data, value, Index, 'imageFormimageForm');
        this.imageForm[Index].id = data.id;
        this.ruleVideoForm[value] = data.id;
      },
      onFileSucceed(data, value, Index) {
        this.vid = data.fileData.vid;
        if (this.vid) {
          setTimeout(() => {
            courseDictationListApi.getVideoInfo(this.vid).then((res) => {
              if (res.code === 20000) {
                this.ruleVideoForm.videoUrl = this.vid;
                this.isUploadSuccess = true;
                this.imageForm[Index][value] = res.data.data.data[0].basicInfo && res.data.data.data[0].basicInfo.coverURL;

                // console.log(this.imageForm.filter(res => res.name === value)?.status,'121212');
                // this.imageForm.filter(item => item.name === value)?.status = res.data.data.data[0].basicInfo.status
                this.$nextTick(() => {
                  // 以服务的方式调用的 Loading 需要异步关闭
                  if (this.loadingInstance) {
                    this.loadingInstance.close();
                  }
                });
              }
            });
          }, 1000);
        }
      },
      onFileFailed(data) {
        console.log('文件上传失败: ', data);
        this.$nextTick(() => {
          // 以服务的方式调用的 Loading 需要异步关闭
          if (this.loadingInstance) {
            this.loadingInstance.close();
          }
        });
      },
      onFileStopped(data) {
        console.log('文件上传停止: ', data);
      },
      resetForm() {
        if (this.$refs['ruleVideoForm'] && this.$refs['ruleVideoForm'].resetFields()) {
          this.$refs['ruleVideoForm'].resetFields();
        }
        for (let i in this.ruleVideoForm) {
          this.ruleVideoForm[i] = '';
        }
        for (let i in this.drawSyllableForm) {
          this.drawSyllableForm[i] = '';
        }
        for (let i in this.stressForm) {
          this.stressForm[i] = '';
        }
        if (this.imageForm[0].id) {
          this.delBaseUrl('videoUrl', 'videoUrlInputer', 0);
        }
      },
      //拼读拼写校验
      stepWordValidate(rule, value, callback, data) {
        if (!value) {
          callback(new Error('请输入单词'));
        } else {
          callback();
        }
      },
      stepSyllableValidate(rule, value, callback, data) {
        if (!value) {
          callback(new Error('请输入音节'));
        } else {
          const wordArr = data.word.includes(',') ? data.word.split(',') : [data.word];
          const syllableArr = data.syllable.includes('-') ? data.syllable.split('-') : [data.syllable];
          if (syllableArr.length !== wordArr.length) {
            callback(new Error('请正确输入音节'));
          } else {
            callback();
          }
        }
      },
      stepSplitInfoValidate(rule, value, callback, data) {
        if (!value) {
          callback(new Error('请输入单词拼读'));
        } else {
          const wordArr = data.word.includes(',') ? data.word.split(',') : [data.word];
          const splitInfoArr = data.splitInfo.includes('-') ? data.splitInfo.split('-') : [data.splitInfo];
          if (splitInfoArr.length !== wordArr.length) {
            callback(new Error('请正确输入单词拼读'));
          } else {
            callback();
          }
        }
      },
      //清空拼写词
      clearWriteWord() {
        this.spellForm.writeCheckWord = '';
      },
      //选择弱音单词
      onWeakChange(item, topIndex, itemIndex) {
        this.stressForm.weakTone = this.wordMsgchange;
        this.stepForm.pdWordRuleDto[topIndex].ruleWordTypeDtoList.forEach((word, selectIndex) => {
          let syllable = word.too ? word.syllable + word.too : word.syllable;
          if (word.type != 1 && word.type != 2) {
            let weakenedTypes = this.getWeakenedTypes(topIndex);
            if (item.name == 'ur音') {
              if (weakenedTypes[0] && weakenedTypes[0].weakTone.includes(syllable)) {
                if (weakenedTypes[1] && weakenedTypes[1].weakTone.includes(syllable)) {
                  weakenedTypes[1].weakTone = weakenedTypes[1].weakTone.filter((info) => info != syllable);
                }
                if (weakenedTypes[2] && weakenedTypes[2].weakTone.includes(syllable)) {
                  weakenedTypes[2].weakTone = weakenedTypes[2].weakTone.filter((info) => info != syllable);
                }
                if (weakenedTypes[3] && weakenedTypes[3].weakTone.includes(syllable)) {
                  weakenedTypes[3].weakTone = weakenedTypes[3].weakTone.filter((info) => info != syllable);
                }
                if (weakenedTypes[4] && weakenedTypes[4].weakTone.includes(syllable)) {
                  weakenedTypes[4].weakTone = weakenedTypes[4].weakTone.filter((info) => info != syllable);
                }
                if (weakenedTypes[5] && weakenedTypes[5].weakTone.includes(syllable)) {
                  weakenedTypes[5].weakTone = weakenedTypes[5].weakTone.filter((info) => info != syllable);
                }
                if (weakenedTypes[6] && weakenedTypes[6].weakTone.includes(syllable)) {
                  weakenedTypes[6].weakTone = weakenedTypes[6].weakTone.filter((info) => info != syllable);
                }
                word.info = word.syllable;
                word.status = '1';
                word.type = weakenedTypes[0].type;
              } else if (word.status == '1' && word.type == weakenedTypes[0].type) {
                word.info = '';
                word.status = '0';
                word.type = 0;
              }
            } else if (item.name == 'u·r音') {
              if (weakenedTypes[2] && weakenedTypes[2].weakTone.includes(syllable)) {
                if (weakenedTypes[1].weakTone.includes(syllable)) {
                  weakenedTypes[1].weakTone = weakenedTypes[1].weakTone.filter((info) => info != syllable);
                }
                if (weakenedTypes[0] && weakenedTypes[0].weakTone.includes(syllable)) {
                  weakenedTypes[0].weakTone = weakenedTypes[0].weakTone.filter((info) => info != syllable);
                }
                if (weakenedTypes[3] && weakenedTypes[3].weakTone.includes(syllable)) {
                  weakenedTypes[3].weakTone = weakenedTypes[3].weakTone.filter((info) => info != syllable);
                }
                if (weakenedTypes[4] && weakenedTypes[4].weakTone.includes(syllable)) {
                  weakenedTypes[4].weakTone = weakenedTypes[4].weakTone.filter((info) => info != syllable);
                }
                if (weakenedTypes[5] && weakenedTypes[5].weakTone.includes(syllable)) {
                  weakenedTypes[5].weakTone = weakenedTypes[5].weakTone.filter((info) => info != syllable);
                }
                if (weakenedTypes[6] && weakenedTypes[6].weakTone.includes(syllable)) {
                  weakenedTypes[6].weakTone = weakenedTypes[6].weakTone.filter((info) => info != syllable);
                }
                word.info = word.syllable;
                word.status = '1';
                word.type = weakenedTypes[2].type;
              } else if (word.status == '1' && word.type == weakenedTypes[2].type) {
                word.info = '';
                word.status = '0';
                word.type = 0;
              }
            } else if (item.name == 'oo音') {
              if (weakenedTypes[3] && weakenedTypes[3].weakTone.includes(syllable)) {
                if (weakenedTypes[1] && weakenedTypes[1].weakTone.includes(syllable)) {
                  weakenedTypes[1].weakTone = weakenedTypes[1].weakTone.filter((info) => info != syllable);
                }
                if (weakenedTypes[2] && weakenedTypes[2].weakTone.includes(syllable)) {
                  weakenedTypes[2].weakTone = weakenedTypes[2].weakTone.filter((info) => info != syllable);
                }
                if (weakenedTypes[0] && weakenedTypes[0].weakTone.includes(syllable)) {
                  weakenedTypes[0].weakTone = weakenedTypes[0].weakTone.filter((info) => info != syllable);
                }
                if (weakenedTypes[4] && weakenedTypes[4].weakTone.includes(syllable)) {
                  weakenedTypes[4].weakTone = weakenedTypes[4].weakTone.filter((info) => info != syllable);
                }
                if (weakenedTypes[5] && weakenedTypes[5].weakTone.includes(syllable)) {
                  weakenedTypes[5].weakTone = weakenedTypes[5].weakTone.filter((info) => info != syllable);
                }
                if (weakenedTypes[6] && weakenedTypes[6].weakTone.includes(syllable)) {
                  weakenedTypes[6].weakTone = weakenedTypes[6].weakTone.filter((info) => info != syllable);
                }
                word.info = word.syllable;
                word.status = '1';
                word.type = weakenedTypes[3].type;
              } else if (word.status == '1' && word.type == weakenedTypes[3].type) {
                word.info = '';
                word.status = '0';
                word.type = 0;
              }
            } else if (item.name == 'a-e音') {
              if (weakenedTypes[4] && weakenedTypes[4].weakTone.includes(syllable)) {
                if (weakenedTypes[1].weakTone.includes(syllable)) {
                  weakenedTypes[1].weakTone = weakenedTypes[1].weakTone.filter((info) => info != syllable);
                }
                if (weakenedTypes[2] && weakenedTypes[2].weakTone.includes(syllable)) {
                  weakenedTypes[2].weakTone = weakenedTypes[2].weakTone.filter((info) => info != syllable);
                }
                if (weakenedTypes[3] && weakenedTypes[3].weakTone.includes(syllable)) {
                  weakenedTypes[3].weakTone = weakenedTypes[3].weakTone.filter((info) => info != syllable);
                }
                if (weakenedTypes[0] && weakenedTypes[0].weakTone.includes(syllable)) {
                  weakenedTypes[0].weakTone = weakenedTypes[0].weakTone.filter((info) => info != syllable);
                }
                if (weakenedTypes[5] && weakenedTypes[5].weakTone.includes(syllable)) {
                  weakenedTypes[5].weakTone = weakenedTypes[5].weakTone.filter((info) => info != syllable);
                }
                if (weakenedTypes[6] && weakenedTypes[6].weakTone.includes(syllable)) {
                  weakenedTypes[6].weakTone = weakenedTypes[6].weakTone.filter((info) => info != syllable);
                }
                word.info = word.syllable;
                word.status = '1';
                word.type = weakenedTypes[4].type;
              } else if (word.status == '1' && word.type == weakenedTypes[4].type) {
                word.info = '';
                word.status = '0';
                word.type = 0;
              }
            } else if (item.name == 'o-e音') {
              if (weakenedTypes[5] && weakenedTypes[5].weakTone.includes(syllable)) {
                if (weakenedTypes[1] && weakenedTypes[1].weakTone.includes(syllable)) {
                  weakenedTypes[1].weakTone = weakenedTypes[1].weakTone.filter((info) => info != syllable);
                }
                if (weakenedTypes[2] && weakenedTypes[2].weakTone.includes(syllable)) {
                  weakenedTypes[2].weakTone = weakenedTypes[2].weakTone.filter((info) => info != syllable);
                }
                if (weakenedTypes[3] && weakenedTypes[3].weakTone.includes(syllable)) {
                  weakenedTypes[3].weakTone = weakenedTypes[3].weakTone.filter((info) => info != syllable);
                }
                if (weakenedTypes[4] && weakenedTypes[4].weakTone.includes(syllable)) {
                  weakenedTypes[4].weakTone = weakenedTypes[4].weakTone.filter((info) => info != syllable);
                }
                if (weakenedTypes[0] && weakenedTypes[0].weakTone.includes(syllable)) {
                  weakenedTypes[0].weakTone = weakenedTypes[0].weakTone.filter((info) => info != syllable);
                }
                if (weakenedTypes[6] && weakenedTypes[6].weakTone.includes(syllable)) {
                  weakenedTypes[6].weakTone = weakenedTypes[6].weakTone.filter((info) => info != syllable);
                }
                word.info = word.syllable;
                word.status = '1';
                word.type = weakenedTypes[5].type;
              } else if (word.status == '1' && word.type == weakenedTypes[5].type) {
                word.info = '';
                word.status = '0';
                word.type = 0;
              }
            } else if (item.name == 'u-e音') {
              if (weakenedTypes[6] && weakenedTypes[6].weakTone.includes(syllable)) {
                if (weakenedTypes[1].weakTone.includes(syllable)) {
                  weakenedTypes[1].weakTone = weakenedTypes[1].weakTone.filter((info) => info != syllable);
                }
                if (weakenedTypes[2] && weakenedTypes[2].weakTone.includes(syllable)) {
                  weakenedTypes[2].weakTone = weakenedTypes[2].weakTone.filter((info) => info != syllable);
                }
                if (weakenedTypes[3] && weakenedTypes[3].weakTone.includes(syllable)) {
                  weakenedTypes[3].weakTone = weakenedTypes[3].weakTone.filter((info) => info != syllable);
                }
                if (weakenedTypes[4] && weakenedTypes[4].weakTone.includes(syllable)) {
                  weakenedTypes[4].weakTone = weakenedTypes[4].weakTone.filter((info) => info != syllable);
                }
                if (weakenedTypes[5] && weakenedTypes[5].weakTone.includes(syllable)) {
                  weakenedTypes[5].weakTone = weakenedTypes[5].weakTone.filter((info) => info != syllable);
                }
                if (weakenedTypes[0] && weakenedTypes[0].weakTone.includes(syllable)) {
                  weakenedTypes[0].weakTone = weakenedTypes[0].weakTone.filter((info) => info != syllable);
                }
                word.info = word.syllable;
                word.status = '1';
                word.type = weakenedTypes[6].type;
              } else if (word.status == '1' && word.type == weakenedTypes[6].type) {
                word.info = '';
                word.status = '0';
                word.type = 0;
              }
            } else {
              if (weakenedTypes[1] && weakenedTypes[1].weakTone.includes(syllable)) {
                if (weakenedTypes[0] && weakenedTypes[0].weakTone.includes(syllable)) {
                  weakenedTypes[0].weakTone = weakenedTypes[0].weakTone.filter((info) => info != syllable);
                }
                if (weakenedTypes[2] && weakenedTypes[2].weakTone.includes(syllable)) {
                  weakenedTypes[2].weakTone = weakenedTypes[2].weakTone.filter((info) => info != syllable);
                }
                if (weakenedTypes[3] && weakenedTypes[3].weakTone.includes(syllable)) {
                  weakenedTypes[3].weakTone = weakenedTypes[3].weakTone.filter((info) => info != syllable);
                }
                if (weakenedTypes[4] && weakenedTypes[4].weakTone.includes(syllable)) {
                  weakenedTypes[4].weakTone = weakenedTypes[4].weakTone.filter((info) => info != syllable);
                }
                if (weakenedTypes[5] && weakenedTypes[5].weakTone.includes(syllable)) {
                  weakenedTypes[5].weakTone = weakenedTypes[5].weakTone.filter((info) => info != syllable);
                }
                if (weakenedTypes[6] && weakenedTypes[6].weakTone.includes(syllable)) {
                  weakenedTypes[6].weakTone = weakenedTypes[6].weakTone.filter((info) => info != syllable);
                }
                word.info = word.syllable;
                word.status = '1';
                word.type = weakenedTypes[1].type;
              } else if (word.status == '1' && word.type == weakenedTypes[1].type) {
                word.info = '';
                word.status = '0';
                word.type = 0;
              }
            }
          }

          // if(this.weakenedTypes[0].weakTone.includes(word.syllable)&&this.weakenedTypes[1].weakTone.includes(word.syllable)){
          //   console.log('qqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqq')
          // }else{
          //   console.log(word.syllable)
          //   console.log(word.status)
          //   console.log('-------------------------------------------------')
          //   if(item.name=='ur音'){
          //     if(this.weakenedTypes[0].weakTone.length>0){
          //       this.weakenedTypes[0].weakTone.forEach(info=>{
          //         if(word.syllable==info&&word.type==this.weakenedTypes[0].type&&word.status!='0'){
          //           console.log('9999999999999999999999999999999999999')
          //           word.info=''
          //           word.status = "0";
          //           word.type = 0
          //         }else if(word.syllable==info){
          //           console.log('1111111111111111111111111111111111')
          //           word.info=word.syllable
          //           word.status = "1";
          //           word.type = this.weakenedTypes[0].type
          //         }
          //       })
          //     }else{
          //       word.info=''
          //       word.status = "0";
          //       word.type = 0
          //     }

          //   }else{
          //     if(this.weakenedTypes[1].weakTone.length>0){
          //       this.weakenedTypes[1].weakTone.forEach(info=>{
          //        if(word.syllable==info&&word.type==this.weakenedTypes[1].type&&word.status!='0'){
          //         console.log('555555555555555555555555555555555555')
          //         word.info=''
          //         word.status = "0";
          //         word.type = 0
          //       }else if(word.syllable==info&&word.type!=this.weakenedTypes[1].type){
          //         console.log('77777777777777777777777777777777777777777777777')
          //         word.info=word.syllable
          //         word.status = "1";
          //         word.type = this.weakenedTypes[1].type
          //       }
          //     })
          //     }else{
          //       word.info=''
          //       word.status = "0";
          //       word.type = 0
          //     }
          //   }
          // }
        });
        // let arr = [];
        // // syllableObj.syllable.split('')
        // console.log(syllableObj.weakTone, syllableObj.syllable);
        // syllableObj.syllable.split("").forEach((e) => {
        //   if (syllableObj.weakTone.includes(e)) {
        //     arr.push(e);
        //   }
        // });
        // console.log(arr);
        // console.log(syllableObj.weakTone);
        // const info = syllableObj.weakTone.join("|");
        // item.info = info ? info : "";
        // if (info) {
        //   this.stepForm.pdWordRuleDto[topIndex].ruleWordTypeDtoList[selectIndex].info = item.syllable + "(" + item.info + ")";
        //   this.stepForm.pdWordRuleDto[topIndex].ruleWordTypeDtoList[selectIndex].info .status = "1";
        // } else {
        //   this.stepForm.pdWordRuleDto[topIndex].ruleWordTypeDtoList[selectIndex].info .info = "";
        //   this.stepForm.pdWordRuleDto[topIndex].ruleWordTypeDtoList[selectIndex].info .status = "0";
        // }
      },
      //选择长短音
      onLongShortChange(item, topIndex, itemIndex) {
        // longShortTone
        this.stressForm.longShortTones = this.wordMsgchange;
        let syllableObj = {};
        this.wordMsgchange.forEach((word) => {
          if (word.index == topIndex) {
            word.wordItem.forEach((ele) => {
              if (ele.itemIndex === itemIndex && ele.syllable == item.syllable) {
                syllableObj = ele;
              }
            });
          }
        });
        let info = '';
        if (syllableObj.longShortTone.includes('|')) {
          info = syllableObj.longShortTone.split('|')[0];
          item.lengthType = syllableObj.longShortTone.split('|')[1];
        } else {
          info = syllableObj.longShortTone;
        }
        item.info = info ? info : '';
        item.info = item.syllable + '(' + item.info + ')';
        item.status = '1';
      },
      onCiLongShortChange(item, topIndex, itemIndex) {
        // longShortTone
        this.stressForm.cilongShortTone = this.wordMsgchange;
        let syllableObj = {};
        this.wordMsgchange.forEach((word) => {
          if (word.index == topIndex) {
            word.wordItem.forEach((ele) => {
              if (ele.itemIndex === itemIndex && ele.syllable == item.syllable) {
                syllableObj = ele;
              }
            });
          }
        });
        let info = '';
        if (syllableObj.cilongShortTone.includes('|')) {
          info = syllableObj.cilongShortTone.split('|')[0];
          item.lengthType = syllableObj.cilongShortTone.split('|')[1];
        } else {
          info = syllableObj.cilongShortTone;
        }
        item.info = info ? info : '';
        item.info = item.syllable + '(' + item.info + ')';
        item.status = '1';
      },
      addSuffixToRepeatedSyllables(objects) {
        // 创建一个对象来跟踪每个syllable出现的次数
        const syllableCounts = {};
        return objects.map((obj, index) => {
          const syllable = obj.syllable;

          // 如果syllable已经出现过，则增加计数并添加后缀
          if (syllableCounts[syllable]) {
            syllableCounts[syllable]++;
            return {
              ...obj,
              syllable: `${syllable}[${syllableCounts[syllable]}]`
            };
          } else {
            // 如果是第一次出现，则记录该syllable并设置计数为1
            syllableCounts[syllable] = 1;
            return obj;
          }
        });
      },
      onStressChange2(val, type, event) {
        let ruleWordTypeDtoList = this.addSuffixToRepeatedSyllables(val.ruleWordTypeDtoList);
        // this.stressForm.stressWord = this.stressWordChange[topIndex].stressWord;
        ruleWordTypeDtoList.map((res) => {
          if (res.type && res.type == 0) {
          } else {
            res.type == 0;
          }
          if (event == res.syllable) {
            res.type = type;
            res.status = '1';
          }
        });
        if (type == 2) {
          if (this.stressWordChange[0].stressWord.length > 0) {
            if (this.stressWordChange[0].stressWord.includes(event[event.length - 1])) {
              this.stressWordChange[0].stressWord = this.stressWordChange[0].stressWord.filter((data) => data != event[event.length - 1]);
            }
          }
        } else {
          if (this.stressWordChange[0].secondaryStressedWords.length > 0) {
            if (this.stressWordChange[0].secondaryStressedWords.includes(event[event.length - 1])) {
              this.stressWordChange[0].secondaryStressedWords = this.stressWordChange[0].secondaryStressedWords.filter((data) => data != event[event.length - 1]);
            }
          }
        }
        val.ruleWordTypeDtoList = ruleWordTypeDtoList.map((item) => {
          // 先存入重复的音节
          if (item.syllable.includes('[')) {
            item.too = '[' + item.syllable.split('[')[1];
          }
          // 使用正则表达式匹配括号及其内的内容，并替换为空字符串
          let syllable = item.syllable.replace(/\[.*\]$/, '');
          // 返回处理后的对象
          return { ...item, syllable }; // 使用扩展运算符来复制其他属性
        });
        console.log(val, '选择重音', this.stressForm.stressWord);
      },
      onStressChange1(val, type, event) {
        let ruleWordTypeDtoList = this.addSuffixToRepeatedSyllables(val.ruleWordTypeDtoList);
        // this.stressForm.stressWord = this.stressWordChange[topIndex].stressWord;
        ruleWordTypeDtoList.map((res) => {
          if (res.type && res.type == 0) {
          } else {
            res.type == 0;
          }
          if (event == res.syllable) {
            res.type = type;
            res.status = '1';
          }
        });
        if (type == 1) {
          if (this.stressWordChange[0].secondaryStressedWords.length > 0) {
            if (this.stressWordChange[0].secondaryStressedWords.includes(event[event.length - 1])) {
              this.stressWordChange[0].secondaryStressedWords = this.stressWordChange[0].secondaryStressedWords.filter((data) => data != event[event.length - 1]);
            }
          }
        } else {
          if (this.stressWordChange[0].stressWord.length > 0) {
            if (this.stressWordChange[0].stressWord.includes(event[event.length - 1])) {
              this.stressWordChange[0].stressWord = this.stressWordChange[0].stressWord.filter((data) => data != event[event.length - 1]);
            }
          }
        }
        val.ruleWordTypeDtoList = ruleWordTypeDtoList.map((item) => {
          // 先存入重复的音节
          if (item.syllable.includes('[')) {
            item.too = '[' + item.syllable.split('[')[1];
          }
          // 使用正则表达式匹配括号及其内的内容，并替换为空字符串
          let syllable = item.syllable.replace(/\[.*\]$/, '');
          // 返回处理后的对象
          return { ...item, syllable }; // 使用扩展运算符来复制其他属性
        });
        console.log(val, '选择重音', this.stressForm.stressWord);
      },

      //选择重音
      onStressChange(val, type, event, topIndex) {
        let ruleWordTypeDtoList = this.addSuffixToRepeatedSyllables(val.ruleWordTypeDtoList);
        // this.stressForm.stressWord = this.stressWordChange[topIndex].stressWord;
        ruleWordTypeDtoList.map((res) => {
          if (res.type == type) {
            res.type = '0';
          }

          // this.stressForm.stressWord.map(item => {
          //     if(res.syllable === item){
          //         res.type = '1'
          //         res.status = '1'
          //     }
          // })

          if (event.includes(res.syllable)) {
            res.type = type;
            res.status = '1';
          }
        });
        if (type == 1) {
          if (this.stressWordChange[topIndex].secondaryStressedWords.includes(event[event.length - 1])) {
            this.stressWordChange[topIndex].secondaryStressedWords = this.stressWordChange[topIndex].secondaryStressedWords.filter((data) => data != event[event.length - 1]);
          }
        } else {
          if (this.stressWordChange[topIndex].stressWord.includes(event[event.length - 1])) {
            this.stressWordChange[topIndex].stressWord = this.stressWordChange[topIndex].stressWord.filter((data) => data != event[event.length - 1]);
          }
        }
        val.ruleWordTypeDtoList = ruleWordTypeDtoList.map((item) => {
          // 先存入重复的音节
          if (item.syllable.includes('[')) {
            item.too = '[' + item.syllable.split('[')[1];
          }
          // 使用正则表达式匹配括号及其内的内容，并替换为空字符串
          let syllable = item.syllable.replace(/\[.*\]$/, '');
          // 返回处理后的对象
          return { ...item, syllable }; // 使用扩展运算符来复制其他属性
        });
        console.log(val, '选择重音', this.stressForm.stressWord);
      },

      //确定重音
      onStressConfirm(val, index, showWord) {
        console.log();

        if ((index > 0 && !this.stepForm.pdWordRuleDto[index - 1].disabled) || (index > 0 && !this.stepForm.pdWordRuleDto[0].disabled)) {
          return this.$message.success('请按顺序依次确定单词的重节音');
        }
        // this.wordMsgchange = []
        // this.longShortTonesChange = []
        let Index = index; //Index表示的上传的第几个单词
        val.checkWordStatus = '0';
        let exists = this.wordMsgchange.some((obj) => obj.index === index);
        let Obj = {
          index: Index,
          wordItem: []
        };
        if (showWord) {
          this.addShowStressWordBool(Index, false);
          this.addShowCiStressWordBool(Index, false);
          let weakenedTypes = this.getWeakenedTypes(Index);
          weakenedTypes[0].weakTone = [];
          weakenedTypes[1].weakTone = [];
          if (this.courseLevel == 2) {
            weakenedTypes[2].weakTone = [];
            weakenedTypes[3].weakTone = [];
            weakenedTypes[4].weakTone = [];
            weakenedTypes[5].weakTone = [];
            weakenedTypes[6].weakTone = [];
          }
        }
        val.ruleWordTypeDtoList.map((res, index) => {
          res['disabled'] = true;
          res.info = '';
          let itemObj = {
            itemIndex: index, //index表示的第几个音节 有可能一个单词会有重复的音节
            syllable: res.syllable
          };
          if (res.type == '1') {
            this.addShowStressWordBool(Index, true);
            itemObj.longShortTone = [];
          } else if (res.type == '2') {
            this.addShowCiStressWordBool(Index, true);
            itemObj.cilongShortTone = [];
          } else {
            itemObj.weakTone = [];
          }
          Obj.wordItem.push(itemObj);
        });
        this.weakenedWords = val.ruleWordTypeDtoList.filter(
          (item) => item.type == 0 || item.type == 3 || item.type == 14 || item.type == 15 || item.type == 16 || item.type == 17 || item.type == 18
        );
        let weakenedTypes;
        if (this.courseLevel == 2) {
          weakenedTypes = [
            { name: 'ur音', type: 0, weakenedWords: [], weakTone: [] },
            { name: 'i音', type: 3, weakenedWords: [], weakTone: [] },
            { name: 'u·r音', type: 14, weakenedWords: [], weakTone: [] },
            { name: 'oo音', type: 15, weakenedWords: [], weakTone: [] },
            { name: 'a-e音', type: 16, weakenedWords: [], weakTone: [] },
            { name: 'o-e音', type: 17, weakenedWords: [], weakTone: [] },
            { name: 'u-e音', type: 18, weakenedWords: [], weakTone: [] }
          ];
          weakenedTypes[0].weakenedWords = val.ruleWordTypeDtoList.filter(
            (item) => item.type == 0 || item.type == 3 || item.type == 14 || item.type == 15 || item.type == 16 || item.type == 17 || item.type == 18
          );
          weakenedTypes[1].weakenedWords = val.ruleWordTypeDtoList.filter(
            (item) => item.type == 0 || item.type == 3 || item.type == 14 || item.type == 15 || item.type == 16 || item.type == 17 || item.type == 18
          );
          weakenedTypes[2].weakenedWords = val.ruleWordTypeDtoList.filter(
            (item) => item.type == 0 || item.type == 3 || item.type == 14 || item.type == 15 || item.type == 16 || item.type == 17 || item.type == 18
          );
          weakenedTypes[3].weakenedWords = val.ruleWordTypeDtoList.filter(
            (item) => item.type == 0 || item.type == 3 || item.type == 14 || item.type == 15 || item.type == 16 || item.type == 17 || item.type == 18
          );
          weakenedTypes[4].weakenedWords = val.ruleWordTypeDtoList.filter(
            (item) => item.type == 0 || item.type == 3 || item.type == 14 || item.type == 15 || item.type == 16 || item.type == 17 || item.type == 18
          );
          weakenedTypes[5].weakenedWords = val.ruleWordTypeDtoList.filter(
            (item) => item.type == 0 || item.type == 3 || item.type == 14 || item.type == 15 || item.type == 16 || item.type == 17 || item.type == 18
          );
          weakenedTypes[6].weakenedWords = val.ruleWordTypeDtoList.filter(
            (item) => item.type == 0 || item.type == 3 || item.type == 14 || item.type == 15 || item.type == 16 || item.type == 17 || item.type == 18
          );
        } else {
          weakenedTypes = [
            { name: 'ur音', type: 0, weakenedWords: [], weakTone: [] },
            { name: 'i音', type: 3, weakenedWords: [], weakTone: [] }
          ];
          weakenedTypes[0].weakenedWords = val.ruleWordTypeDtoList.filter((item) => item.type == 0 || item.type == 3);
          weakenedTypes[1].weakenedWords = val.ruleWordTypeDtoList.filter((item) => item.type == 0 || item.type == 3);
        }

        this.addWeakToList(Index, weakenedTypes);
        this.fixedLengthWords == val.ruleWordTypeDtoList.filter((item) => item.type == 1);
        if (!exists) {
          this.wordMsgchange.push(Obj);
        } else {
          this.wordMsgchange = this.wordMsgchange.filter((obj) => obj.index !== Index);
          console.log('this.wordMsgchange', this.wordMsgchange);
          this.wordMsgchange.push(Obj);
        }

        // this.hsaStress = true
        // if (this.stressForm.stressWord.length == 0 ) {
        //   val.checkWordStatus = '-1' //没有重音节
        // } else if(val.ruleWordTypeDtoList.length == this.stressForm.stressWord.length) {
        //   val.checkWordStatus = '1' //全部都是重音节
        // }
        val.disabled = true;
        this.wordMsgchange = this.wordMsgchange.sort((a, b) => a.index - b.index); //修改之后排序
      },

      addWeakToList(index, weakenedTypes) {
        console.log(index);
        this.weakenedTypesList.splice(index, 1, weakenedTypes);
        console.log('this.weakenedTypesList', this.weakenedTypesList);
      },

      getWeakenedTypes(index) {
        let data;
        try {
          data = this.weakenedTypesList[index];
          if (!data) {
            if (this.courseLevel == 2) {
              data = [
                { name: 'ur音', type: 0, weakenedWords: [], weakTone: [] },
                { name: 'i音', type: 3, weakenedWords: [], weakTone: [] },
                { name: 'u·r音', type: 14, weakenedWords: [], weakTone: [] },
                { name: 'oo音', type: 15, weakenedWords: [], weakTone: [] },
                { name: 'a-e音', type: 16, weakenedWords: [], weakTone: [] },
                { name: 'o-e音', type: 17, weakenedWords: [], weakTone: [] },
                { name: 'u-e音', type: 18, weakenedWords: [], weakTone: [] }
              ];
            } else {
              data = [
                { name: 'ur音', type: 0, weakenedWords: [], weakTone: [] },
                { name: 'i音', type: 3, weakenedWords: [], weakTone: [] }
              ];
            }
          }
        } catch (e) {
          if (this.courseLevel == 2) {
            data = [
              { name: 'ur音', type: 0, weakenedWords: [], weakTone: [] },
              { name: 'i音', type: 3, weakenedWords: [], weakTone: [] },
              { name: 'u·r音', type: 14, weakenedWords: [], weakTone: [] },
              { name: 'oo音', type: 15, weakenedWords: [], weakTone: [] },
              { name: 'a-e音', type: 16, weakenedWords: [], weakTone: [] },
              { name: 'o-e音', type: 17, weakenedWords: [], weakTone: [] },
              { name: 'u-e音', type: 18, weakenedWords: [], weakTone: [] }
            ];
          } else {
            data = [
              { name: 'ur音', type: 0, weakenedWords: [], weakTone: [] },
              { name: 'i音', type: 3, weakenedWords: [], weakTone: [] }
            ];
          }
        }
        return data;
      },

      addShowStressWordBool(index, val) {
        this.showStressWordList.splice(index, 1, val);
      },
      cleanShowStressWordBool() {
        this.showStressWordList = [];
      },
      getShowStressWordBool(index) {
        try {
          return this.showStressWordList[index];
        } catch (e) {
          return false;
        }
      },
      addShowCiStressWordBool(index, val) {
        this.showCiStressWordList.splice(index, 1, val);
      },
      cleanCiShowStressWordBool() {
        this.showCiStressWordList = [];
      },
      getShowCiStressWordBool(index) {
        try {
          return this.showCiStressWordList[index];
        } catch (e) {
          return false;
        }
      },
      //修改重音
      onStressRevise(val, index) {
        let Index = index; //Index表示的上传的第几个单词
        // this.wordMsgchange = this.wordMsgchange.filter(obj => obj.index !== Index);
        this.wordMsgchange.forEach((item) => {
          if (item.index === Index) {
            item.wordItem.forEach((obj) => {
              if (obj.weakTone) {
                obj.weakTone = [];
              }
              if (obj.longShortTone) {
                obj.longShortTone = [];
              }
              if (obj.cilongShortTone) {
                obj.cilongShortTone = [];
              }
            });
          }
        });
        val.ruleWordTypeDtoList.map((res) => {
          res['disabled'] = false;
          console.log(res, '选中重音节');
        });
        // this.hsaStress = false
        val.disabled = false;
        val.checkWordStatus = null;
        console.log(val, 'this.wordMsgchange', this.wordMsgchange);
      },
      //上传视频下一环节
      submitVideoValidte(val, refName) {
        this.$refs['ruleVideoForm'].validate((valid) => {
          if (valid) {
            this.stepForm.videoUrl = this.ruleVideoForm.videoUrl;
            this.activeCode = val + 1;
            this.$emit('submitNext', { active: val });
          }
        });
      },
      //划音节词,下一环节
      syllableSubmiteValidte(val) {
        this.$refs['drawSyllableForm'].validate(async (valid) => {
          if (valid) {
            await courseDictationListApi.checkSyllableSplit({
              wordSyllable: this.drawSyllableForm.syllable,
              wordSplit: this.drawSyllableForm.splitInfo
            });
            this.activeCode = val + 1;
            this.$emit('submitNext', { active: val });
            //多个单词
            //    if(this.drawSyllableForm.word.indexOf(',')!==-1){
            //分割音节
            this.readSyllableData = this.drawSyllableForm.syllable.indexOf('-') !== -1 ? this.drawSyllableForm.syllable.split('-') : [this.drawSyllableForm.syllable];
            //分割单词
            this.readSyllableData = this.readSyllableData.map((e) => (e = e.replace(/,*$/, '')));
            // this.readSyllableData.forEach((e) => {

            //   console.log(e);
            // });
            console.log(this.readSyllableData);
            this.wordData = this.drawSyllableForm.word.indexOf(',') !== -1 ? this.drawSyllableForm.word.split(',') : [this.drawSyllableForm.word];
            //分割拼读
            const splitInfoArr = this.drawSyllableForm.splitInfo.indexOf('-') ? this.drawSyllableForm.splitInfo.split('-') : [this.drawSyllableForm.splitInfo];
            //根据后端数据格式处理
            this.stepForm.pdWordRuleDto = [];
            this.wordData.forEach((item, index) => {
              this.stepForm.pdWordRuleDto.push({
                word: item,
                splitInfo: splitInfoArr[index],
                ruleWordTypeDtoList: []
              });
            });
            //相应单词对象中添加音节
            this.readSyllableData.forEach((res, index) => {
              //单词有多个音节
              if (res.indexOf(',') !== -1) {
                res.split(',').forEach((item) => {
                  this.stepForm.pdWordRuleDto[index].ruleWordTypeDtoList.push({
                    disabled: false,
                    syllable: item,
                    type: '0',
                    info: '',
                    status: '0'
                  });
                });
              } else {
                //单词有单个音节
                this.stepForm.pdWordRuleDto[index].ruleWordTypeDtoList.push({
                  disabled: false,
                  syllable: res,
                  type: '0',
                  info: '',
                  status: '0'
                });
              }
            });
            this.wordData.forEach((word, index) => {
              let stressObj = {
                index: index,
                word: word,
                stressWord: [],
                secondaryStressedWords: []
              };
              this.stressWordChange.push(stressObj);
            });
            // this.stepForm.pdWordRuleDto.forEach((e) => {
            //   this.wordMsgchange.push([inde]);
            // });
            // 回显选中重音节

            if (this.stepForm.listId) {
              this.cleanShowStressWordBool();
              // 判断是否修改了音节
              console.log(this.stepForm);
              console.log(this.formData.pdWordRuleDto[0].ruleWordTypeDtoList.map((e) => e.syllable.replace(/\[.*\]$/, '')).join(',') == this.drawSyllableForm.syllable);
              console.log(this.drawSyllableForm.syllable);
              console.log(',,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,');
              if (this.formData.pdWordRuleDto[0].ruleWordTypeDtoList.map((e) => e.syllable.replace(/\[.*\]$/, '')).join(',') == this.drawSyllableForm.syllable) {
                // 回显重音节
                this.formData.pdWordRuleDto[0].ruleWordTypeDtoList.forEach((e, i) => {
                  if (e.type == 1) {
                    this.addShowStressWordBool(0, true);
                    this.stressWordChange[0].stressWord.push(e.syllable);
                    this.onStressChange1(this.stepForm.pdWordRuleDto[0], '1', e.syllable, i);
                  } else if (e.type == 2) {
                    this.addShowCiStressWordBool(0, true);
                    this.stressWordChange[0].secondaryStressedWords.push(e.syllable);
                    this.onStressChange2(this.stepForm.pdWordRuleDto[0], '2', e.syllable, i);
                  } else {
                    this.stepForm.pdWordRuleDto[0].ruleWordTypeDtoList.forEach((item) => {
                      //区分重复音节
                      let syllable = item.too ? item.syllable + item.too : item.syllable;
                      if (syllable == e.syllable) {
                        item.type = e.type;
                      }
                    });
                  }
                });
                // 点击确定按钮
                this.onStressConfirm(this.stepForm.pdWordRuleDto[0], 0);
                // 回显定长短和划弱音
                this.formData.pdWordRuleDto[0].ruleWordTypeDtoList.forEach((e, i) => {
                  this.stepForm.pdWordRuleDto[0].ruleWordTypeDtoList[i].info = e.info;
                  this.stepForm.pdWordRuleDto[0].ruleWordTypeDtoList[i].status = e.status;
                  this.stepForm.pdWordRuleDto[0].ruleWordTypeDtoList[i].lengthType = e.lengthType;
                  if (e.info.includes('(')) {
                    let a = e.info.split('(')[1].split(')')[0];
                    console.log(a, e, '回显');

                    if (e.type == 1 && e.lengthType > 0) {
                      this.wordMsgchange[0].wordItem[i].longShortTone = a + '|' + e.lengthType;
                    }
                    if (e.type == 2 && e.lengthType > 0) {
                      this.wordMsgchange[0].wordItem[i].cilongShortTone = a + '|' + e.lengthType;
                    }
                  }
                  //划弱音回显
                  if (e.type != 1 && e.type != 2) {
                    console.log(e.status, 'eeeeee划弱音回显', e);
                    let weakenedTypes = this.getWeakenedTypes(0);
                    if (e.type == 0 && e.status == '1' && e.info) {
                      weakenedTypes[0].weakTone.push(e.syllable);
                    } else if (e.type == 3 && e.info) {
                      weakenedTypes[1].weakTone.push(e.syllable);
                    } else if (e.type == 14 && e.info) {
                      weakenedTypes[2].weakTone.push(e.syllable);
                    } else if (e.type == 15 && e.info) {
                      weakenedTypes[3].weakTone.push(e.syllable);
                    } else if (e.type == 16 && e.info) {
                      weakenedTypes[4].weakTone.push(e.syllable);
                    } else if (e.type == 17 && e.info) {
                      weakenedTypes[5].weakTone.push(e.syllable);
                    } else if (e.type == 18 && e.info) {
                      weakenedTypes[6].weakTone.push(e.syllable);
                    }
                  }
                });
              }
              // 判断是否修改了单词
              if (this.formData.pdWordRuleDto[0].word == this.drawSyllableForm.word) {
                // 拼读 拼写 学后读 学后写 回显
                if (this.readWriteobj.isReadSelect == 1) {
                  this.readTestForm.readTestcheckList.push(this.formDetail.pdWordRuleDto[0].word);
                }
                if (this.readWriteobj.isWriteSelect == 1) {
                  this.readTestForm.witreTestcheckList.push(this.formDetail.pdWordRuleDto[0].word);
                }
                if (this.readWriteobj.isSelect == 1) {
                  this.readTestForm.postReadcheckList.push(this.formDetail.pdWordRuleDto[0].word);
                } else if (this.readWriteobj.isSelect == 2) {
                  this.readTestForm.postWittecheckList.push(this.formDetail.pdWordRuleDto[0].word);
                } else if (this.readWriteobj.isSelect == 3) {
                  this.readTestForm.postWittecheckList.push(this.formDetail.pdWordRuleDto[0].word);
                  this.readTestForm.postReadcheckList.push(this.formDetail.pdWordRuleDto[0].word);
                }
              }
            }
          }
        });
      },
      // 划弱音，定长短
      syllableSubmiteValidte2(val) {
        let a = [];
        this.stepForm.pdWordRuleDto.forEach((e) => {
          if (!e.disabled) {
            a.push(e.word);
          }
        });
        if (a.length > 0) return this.$message.error(`${a.join(',')}单词选择重弱音，如果没有，直接点击确定`);
        this.activeCode = val + 1;
        this.$emit('submitNext', { active: val });
        // 存入后面选择的所有词
        this.cities = this.stepForm.pdWordRuleDto.map((e) => e.word);
      },
      // 练习拼读词，下一环节
      async syllableSubmiteValidte3(val) {
        console.log(this.stepForm, 'stepFormstepFormstepFormstepFormstepFormstepForm');
        await this.$refs['readTestForm'].validate();
        this.activeCode = val + 1;
        this.$emit('submitNext', { active: val });
      },
      //关闭
      closeDialog() {
        this.$emit('closeDialog');
      },
      //选中拼写单词
      onWriteRightCheckChange(checkItem, closeItem) {
        const list = [];
        checkItem.map((f) => {
          list.push(this.filterData[f].label);
        });
        this.testForm.writeCheckWord = list.join(',');
        console.log(this.testForm.readCheckWord, 'checkItem,closeItem,拼写');
      },
      //选中拼读单词
      onReadRightCheckChange(checkItem, closeItem) {
        const list = [];
        checkItem.map((f) => {
          list.push(this.filterData[f].label);
        });
        this.spellForm.readCheckWord = list.join(',');
        this.spellForm.writeCheckWord = list.join(',');
        console.log(this.spellForm.readCheckWord, 'checkItem,closeItem,拼读');
      },

      //选择拼写单词
      onWriteSelectWord() {
        this.showWriteSelectWord = true;
      },
      //选择拼读单词
      onReadSelectWord() {
        this.showReadSelectWord = true;
      },
      filterChange(value, direction, movedKeys) {
        console.log(value, direction, movedKeys, 'filterChangevalue');
      },
      //左侧选中/取消选中
      leftCheckChange(item, newItem) {
        console.log(item, newItem, 'leftCheckChange');
      },
      //搜索拼读检测词
      filterMethod(query, item) {
        console.log(query, item, 'filterWords');
      },
      //继续制作
      onContinue() {
        this.activeCode = 0;
        //     const arr = ['videoUrl','readWord','writeWord']
        //    this.activeCode = 2;
        //    this.nextRead = false;
        //    this.nextWrite = false;
        //    arr.forEach(item => {
        //     this.studyWord.push({item:this.form[item]})
        //     this.form[item] = ''
        //    })
        //    console.log(this.studyWord,'studyWord00000000');
      },
      //进入拼读环节
      onNext() {
        this.nextRead = true;

        console.log('拼读');
      },
      handleUploadSuccess(res, file) {
        console.log(file, 'filevideo');
      },
      //上传视频
      handleVideoSuccess(url, fileName) {
        this.form.expandInfo.videoInfo = url;
        this.form.expandInfo.videoName = fileName;
      },
      handleVideoRemove() {
        this.form.expandInfo.videoInfo = '';
        this.form.expandInfo.videoName = '';
      },
      handleRemoveDetail() {
        this.fd = '';
      },
      //必须是excel文件
      isExcel(file) {
        return /\.(xlsx|xls)$/.test(file.name);
      },
      //音节拼读
      beforeWordUpload(file) {
        console.log(file, 'file0000000000000');
        if (!this.isExcel(file)) {
          this.$message.error('只能上传excel文件！');
          return false;
        }
      },

      //音节拼读上传成功
      //上传元辅音文件解析
      uploadDetailHttp({ file }) {
        const that = this;
        this.wordFile = `https://document.dxznjy.com/applet/zhimi/usercode_${file.uid}.xls`;
        const fileType = file.type.substring(file.type.lastIndexOf('/') + 1);
        const fileName = `manage/${Date.parse(new Date())}.${fileType}`;
        that.$nextTick(function () {
          ossPrClient()
            .put(fileName, file)
            .then(({ res, url, name }) => {
              if (res && res.status === 200) {
                console.log(`阿里云OSS上传图片成功回调1`, file, res, url, name);
                const formData = new FormData();
                formData.append('file', file);
                courseDictationListApi
                  .analysisFile2(formData)
                  .then((res) => {
                    if (res.code === 20000) {
                      this.drawSyllableForm.word = '';
                      this.drawSyllableForm.syllable = '';
                      this.drawSyllableForm.splitInfo = '';
                      const { word, syllable, split } = { ...res.data.data };
                      if (this.drawSyllableForm.word) {
                        this.drawSyllableForm.word = this.drawSyllableForm.word + ',' + word;
                      } else {
                        this.drawSyllableForm.word = word;
                      }
                      if (this.drawSyllableForm.syllable) {
                        this.drawSyllableForm.syllable = this.drawSyllableForm.syllable + '-' + syllable;
                      } else {
                        this.drawSyllableForm.syllable = syllable;
                      }
                      if (this.drawSyllableForm.splitInfo) {
                        this.drawSyllableForm.splitInfo = this.drawSyllableForm.splitInfo + '-' + split;
                      } else {
                        this.drawSyllableForm.splitInfo = split;
                      }
                      this.$refs.myUpload.clearFiles();
                    }
                  })
                  .catch((err) => {
                    console.log(`err--------------------`, err);
                    this.$refs.myUpload.clearFiles();
                  });
              }
            })
            .catch((err) => {
              that.$message.error('上传图片失败请检查网络或者刷新页面');
              console.log(`阿里云OSS上传图片失败回调`, err);
            });
        });
      },

      //音节拼读删除
      handlSetRemoveReadWord() {
        this.readFile = '';
      },
      //上传模板
      beforeUplpad01(file) {
        this.fd = file.raw;
        console.log(file, 'file');
      },
      //限制文件
      handleExceed(files, fileList) {
        this.$message.warning(`当前限制选择 1个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`);
      },
      videoDel() {
        this.repeatForm.videoUrl = '';
      },
      beforeUpload(file) {
        console.log(file, 'filefilefilefilefilefilefile');
        const isMp4 = file.type === 'video/mp4';
        // 限制文件最大不能超过 50M
        const isLt50M = file.size / 1024 / 1024 < 50;
        if (!isMp4) {
          this.$message.error('视频只能是mp4格式!');
        }
        //   if (!isLt50M) {
        //     this.$message.error("上传头像图片大小不能超过 300MB!");
        //   }
        return isMp4;
      },
      //上传录频
      uploadRuleHttp({ file }) {
        this.uploadLoading = true;
        const fileName = 'manage/' + Date.parse(new Date()) + '.mp4';
        this.$nextTick(function () {
          console.log('000000000');
          if (ossPrClient() && ossPrClient().put) {
            console.log('11111111111111');
            ossPrClient()
              .put(fileName, file)
              .then(({ res, url, name }) => {
                if (res && res.status === 200) {
                  console.log(`阿里云OSS上传图片成功回调1`, res, url, name);
                  this.ruleVideoForm.videoUrl = url;
                  this.preschoolLoading = false;
                  this.$nextTick(() => {
                    this.uploadLoading = false;
                  });
                }
              })
              .catch((err) => {
                that.$message.error('上传图片失败请检查网络或者刷新页面');
                this.uploadLoading = false;
                console.log(`阿里云OSS上传图片失败回调`, err);
              });
          }
        });
      }
    }
  };
</script>
<style lang="less" scoped>
  .spell_checkWord {
    display: flex;
    align-items: center;
    margin-bottom: 16pxu;
  }

  .spell_checkWord_btn {
    margin-left: 16px;
  }

  .stress_data {
    display: flex;
  }
  .stress_data_flex {
    display: flex;
    justify-content: flex-start;
  }
  .long_title_css {
    font-size: 14px;
    span {
      display: inline-block;
      margin-right: 15px;
      color: #999;
    }
  }
  .stress_data_item {
    margin-right: 16px;
  }

  .upload_box {
    // display: flex;
    // justify-content: space-around;
  }

  .download_link {
    // height: 32px;
    // margin: 0;
    // padding: 0 15px;
    color: #13ce66;
  }

  .upload_link {
    color: #1890ff;
  }

  .video_upload {
    position: absolute;
    z-index: 99999;
    width: 148px;
    height: 148px;
    border-radius: 8px;
    background-color: black;
  }

  .video_del {
    margin-left: 130px;
    position: absolute;
    z-index: 99999;
    color: white;
  }

  .footer_btn {
    flex-direction: row-reverse;
  }

  ::v-deep .el-form-item__label {
    font-weight: bold !important;
  }

  .StepForm {
    margin-top: 16px;

    .submintBtn {
      margin: 8px 0;
    }
  }
</style>
