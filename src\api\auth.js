/**
 * 登录相关接口
 */
import request from "@/utils/request";

export default {
  verificationCode(gloadId) {
    return request({
      url: "/new/security/login/verification/code/" + gloadId,
      method: "get",
    });
  },

  loginByUsername(username, password, userType, code, appId, role) {
    return request({
      url: "/new/security/login/backstage",
      method: "get",

      params: { password, role, username, userType, code, appId },
    });
  },
  loginByNoCode(username, password, userType, code, appId, role) {
    return request({
      url: "/new/security/login/backstageNoCode",
      method: "get",

      params: { password, role, username, userType, code, appId },
    });
  },
  getLogo(id) {
    return request({
      url: "/znyy/operations/center/getAvatar?secondaryDomainName=" + id,
      method: "get",
    });
  },

  logout() {
    return request({
      url: "/znyy/user/logout",
      method: "PUT",
    });
  },
  getSelect(ele) {
    return request({
      url: "/znyy/bvadmin/getSelect/roleTag?loginName=" + ele,
      method: "GET",
    });
  },
  byLoginNameQueryRoleTag(loginName) {
    return request({
      url: "/znyy/bvadmin/getSelect/roleTag/" + loginName,
      method: "GET",
    });
  },
  getUserInfo(token) {
    return request({
      url: "/znyy/sys/user/info",
      method: "GET",
    });
  },
  getJlbInfo() {
    return request({
      url: "/znyy/school/currentAdmin",
      method: "GET",
    });
  },
};
