<template>
  <div class="app-container">
    <el-form :inline="true" ref="queryForm" class="container-card" label-width="110px">
      <el-form-item>
        <el-button type="primary" @click="getPageList()">刷新</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="success" icon="el-icon-plus" size="mini" @click="addBtn()">新增</el-button>
      </el-form-item>
    </el-form>

    <el-table class="common-table" v-loading="tableLoading" :data="tableData" style="width: 100%;margin-bottom: 20px;"
      row-key="id" border default-expand-all>
      <el-table-column prop="id" label="ID"></el-table-column>
      <el-table-column prop="taskName" label="任务名"></el-table-column>
      <el-table-column prop="withdrawnBonus" label="操作" width="240">
        <template slot-scope="scope">
          <el-button type="primary" size="mini" @click="editBtn(scope.row.id)">编辑
          </el-button>
          <el-button type="danger" size="mini" @click="handleDelete(scope.row.id)">删除
          </el-button>
        </template>
      </el-table-column>
      <el-table-column prop="minTime" label="分钟最小值" />
      <el-table-column prop="maxTime" label="分钟最大值" />
      <el-table-column prop="goldRate" label="金币比率" />
      <el-table-column prop="expRate" label="经验比率" />
      <el-table-column prop="creditsRate" label="学分比率" />
    </el-table>
    <!-- 分页 -->
    <el-col :span="24" style="margin-bottom: 20px">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>

    <!-- 修改或者新增弹窗 -->
    <el-dialog title="编辑比例配置" :visible.sync="open" width="70%" @close="close">
      <el-form ref="form" :model="form" :rules="rules" label-width="130px" style="width: 70%;">
        <el-form-item label="任务：" prop="taskId">
          <el-select v-model="form.taskId" placeholder="请选择任务">
            <el-option v-for="item in taskList" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="分钟最小值：" prop="minTime">
          <el-input-number v-model="form.minTime" :step="1" :min="1" placeholder="请输入分钟最小值" />
        </el-form-item>
        <el-form-item label="分钟最大值：" prop="maxTime">
          <el-input-number v-model="form.maxTime" :step="1" :min="1" placeholder="请输入分钟最大值" />
        </el-form-item>
        <el-form-item label="金币比例值：" prop="goldRate">
          <el-input-number v-model="form.goldRate" :step="1" :min="0" placeholder="请输入金币比例值" />
        </el-form-item>
        <el-form-item label="学分比例值：" prop="creditsRate">
          <el-input-number v-model="form.creditsRate" :step="1" :min="0" placeholder="请输入学分比例值" />
        </el-form-item>
        <el-form-item label="经验比例值：" prop="expRate">
          <el-input-number v-model="form.expRate" :step="1" :min="0" placeholder="请输入经验比例值" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import ratioconfigApi from '@/api/xi/ratioconfig'
import xiTaskApi from '@/api/xi/xiTask'
import { pageParamNames } from '@/utils/constants'

export default {
  name: 'ratioConfig',
  data() {
    return {
      dataQuery: {},
      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      open: false,
      tableData: [],
      taskList: [],
      form: {},
      // 表单校验
      rules: {
        minTime: [{ required: true, message: '请输入分钟最小值', trigger: 'blur' }],
        maxTime: [{ required: true, message: '请输入分钟最大值', trigger: 'blur' }],
        goldRate: [{ required: true, message: '请输入金币比例值', trigger: 'blur' }],
        creditsRate: [{ required: true, message: '请输入学分比例值', trigger: 'blur' }],
        expRate: [{ required: true, message: '请输入经验比例值', trigger: 'blur' }],
        taskId: [{ required: true, message: '请选择任务', trigger: 'blur' }],
      }
    }
  },
  created() {
    this.getPageList();
    xiTaskApi.allList().then(resp => {
      this.taskList = resp.data;
    }).catch(err => { });
  },
  methods: {
    handleDelete(id) {
      this.$confirm('确定要删除吗？', '删除比例配置', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        ratioconfigApi.delete(id).then(res => {
          this.$message.success('删除成功！')
          this.getPageList()
        })
      })
    },
    addBtn() {
      this.reset()
      this.open = true
    },
    editBtn(id) {
      this.reset()
      ratioconfigApi.detail(id).then(res => {
        this.form = res.data
        this.open = true
      })
    },
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          ratioconfigApi.saveOrUpdate(this.form).then(response => {
            this.$message.success('提交成功！')
            this.open = false
            this.getPageList()
          })
        }
      })
    },
    getPageList() {
      this.tableLoading = true
      this.dataQuery.pageNum = this.tablePage.currentPage
      this.dataQuery.pageSize = this.tablePage.size
      ratioconfigApi.list(this.dataQuery).then(res => {
        this.tableData = res.data.data
        this.tableLoading = false
        // 设置后台返回的分页参数
        pageParamNames.forEach(name =>
          this.$set(this.tablePage, name, parseInt(res.data[name]))
        )
      })
    },
    handlePage() {
      (this.tablePage = {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      }),
        this.getPageList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dataQuery = {
        name: '',
        enable: null
      }
      this.getPageList()
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val
      this.getPageList()
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val
      this.getPageList()
    },
    close() {
      this.open = false
      this.reset()
    },
    reset() {
      this.audioFileList = []
      this.form = {
        id: null,
        min: undefined,
        max: undefined,
        ratio: undefined,
      }
      if (this.$refs['form']) {
        this.$refs['form'].resetFields()
      }
    }
  }
}
</script>

<style>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.period-table td,
.period-table th {
  text-align: center;
}

@media (max-width: 767px) {
  .el-message-box {
    width: 80% !important;
  }
}

.el-tooltip__popper {
  max-width: 800px;
}
</style>
