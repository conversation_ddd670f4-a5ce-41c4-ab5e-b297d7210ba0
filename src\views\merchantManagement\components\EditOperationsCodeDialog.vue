<template>
  <div>
    <el-dialog title="修改渠道合作伙伴" center :visible.sync="isShowDialog" width="700px" @close="handleOuterClose" :close-on-press-escape="false" :close-on-click-modal="false">
      <el-form ref="channerManagerRef" :model="form" label-width="180px" :rules="rules">
        <el-form-item label="俱乐部名称" prop="merchantName">
          <el-input disabled v-model="form.merchantName"></el-input>
        </el-form-item>
        <el-form-item label="原渠道合作伙伴" prop="operationsName">
          <el-input disabled v-model="form.operationsName"></el-input>
        </el-form-item>
        <el-form-item label="新渠道合作伙伴编号" prop="newChannelCode">
          <el-input v-model.trim="form.newChannelCode" maxlength="30" placeholder="请输入新渠道合作伙伴编号" show-word-limit @blur="handleNewRefereeCodeBlur"></el-input>
        </el-form-item>
        <el-form-item label="新渠道合作伙伴名称" prop="newRefereeName">
          <el-input disabled v-model="form.newRefereeName"></el-input>
        </el-form-item>
        <div style="color: red; text-align: center; margin-bottom: 20px">提示：新渠道合作伙伴需要为所属品牌下已开通的俱乐部或所属俱乐部品牌编号</div>
        <div class="dialog-footer">
          <el-button @click="handleOuterClose">取 消</el-button>
          <el-button :loading="loading" type="primary" @click="handleConfirm">确 定</el-button>
        </div>
      </el-form>
    </el-dialog>
    <InabilityReason
      v-if="dialogReasonVisible"
      :dialogReasonVisible.sync="dialogReasonVisible"
      :workStep="workStep"
      showTitleStatus="4"
      :reasonType="reasonType"
      :reasonContent="reasonContent"
      @close="handleOuterClose"
      @handleSubmit="handleSubmit"
    />
  </div>
</template>

<script>
  import InabilityReason from '@/views/merchantManagement/components/InabilityReason.vue';
  import schoolApi from '@/api/schoolList';
  import dealerListApi from '@/api/operationsList';

  export default {
    name: 'EditOperationFormDialog',
    components: {
      InabilityReason
    },
    props: {
      dialogVisible: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        form: {
          merchantName: '', //俱乐部名称
          operationsName: '', //原渠道合作伙伴名称
          newChannelCode: '', //新渠道合作伙伴编号
          newRefereeName: '', //新渠道合作伙伴名称
          merchantCode: '' //商户编号
        },
        loading: false,
        rules: {
          newChannelCode: [
            { required: true, message: '新渠道合作伙伴编号未输入', trigger: ['blur', 'change'] },
            {
              pattern: /^\d{1,30}$/,
              message: '新渠道合作伙伴编号必须是数字',
              trigger: ['blur', 'change']
            }
          ],
          newRefereeName: [{ required: true, message: '新渠道合作伙伴名称未输入', trigger: ['blur', 'change'] }]
        },
        dialogReasonVisible: false,
        reasonContent: [],
        workStep: 1,
        reasonType: 0
      };
    },
    computed: {
      isShowDialog: {
        get() {
          return this.dialogVisible;
        },
        set(val) {
          this.$emit('update:isShowCityDialog', val);
        }
      }
    },

    methods: {
      /**
       * 新渠道合作伙伴编号失去焦点时触发
       * @returns {Promise<void>}
       */
      async handleNewRefereeCodeBlur() {
        if (!this.form.newChannelCode) {
          this.$set(this.form, 'newRefereeName', '');
          return;
        }

        try {
          const res = await schoolApi.getMerchantNameApi({ merchantCode: this.form.newChannelCode });
          if (res && res.code === 20000) {
            this.$set(this.form, 'newRefereeName', res.data || '');
            return;
          }
          this.$set(this.form, 'newRefereeName', '');
          if (res?.message) {
            this.$message.warning(res.message);
          }
        } catch (error) {
          console.error('🚀 ~ getMerchantNameApi error:', error);
          this.$message.error('获取渠道合作伙伴名称失败');
          this.$set(this.form, 'newRefereeName', '');
        }
      },

      handleOuterClose(val) {
        if (val === 'closeDialog') {
          this.dialogReasonVisible = false;
          return;
        }
        this.resetAllStates();
        this.$emit('handleCancel');
      },

      /**
       * 重置所有状态的统一方法
       */
      resetAllStates() {
        this.loading = false;
        this.dialogReasonVisible = false;
        this.reset();
        this.reasonContent = [];
        this.workStep = 1;
      },

      async handleConfirm() {
        if (this.loading) return;
        this.loading = true;
        try {
          await this.$refs.channerManagerRef.validate();
          const params = {
            merchantCode: this.form.merchantCode,
            newChannelCode: this.form.newChannelCode
          };

          const { data } = await dealerListApi.checkNewChanel(params);

          this.dialogReasonVisible = true;

          if (!data.canChange) {
            this.reasonContent = data.reasons || [];
            this.workStep = 2;
            return;
          }
          this.workStep = 4;
          this.reasonType = 4;
        } catch (error) {
          if (error && error.message && /validate|验证/.test(error.message)) {
            console.log('🚀 ~ handleConfirm ~ 表单验证失败:', error);
            this.$message.warning('请检查表单输入是否正确');
            return;
          }
          console.error('🚀 ~ handleConfirm ~ checkNewChanel API error:', error);
          // this.$message.error('检查渠道合作伙伴失败，请稍后重试');
        } finally {
          this.loading = false;
        }
      },

      /**
       * 最终提交方法
       */
      async handleSubmit() {
        if (this.loading) return;
        this.loading = true;
        try {
          await this.$refs.channerManagerRef.validate();
          const params = {
            merchantCode: this.form.merchantCode,
            newChannelCode: this.form.newChannelCode
          };
          console.log(`🚀🥶💩🚀~ handleSubmit ~ params:`, params);
          const res = await dealerListApi.confirmChangeChannel(params);
          if (res && res.code !== 20000) {
            this.$message.error(res.message || '提交失败，请稍后重试');
            return;
          }
          this.$message.success('操作成功');
          this.resetAllStates();
          this.$emit('handleCancel', true);
        } catch (error) {
          console.error('🚀 ~ handleSubmit ~ error:', error);
          this.$message.error('提交失败，请稍后重试');
        } finally {
          this.loading = false;
        }
      },

      setData(data) {
        Object.keys(data).forEach((key) => {
          if (this.form.hasOwnProperty(key)) {
            this.$set(this.form, key, data[key]);
          }
        });
      },

      /**
       * 重置表单数据
       */
      reset() {
        this.form = {
          merchantName: '',
          operationsName: '',
          newChannelCode: '',
          newRefereeName: '',
          merchantCode: ''
        };

        this.$nextTick(() => {
          if (this.$refs.channerManagerRef) {
            this.$refs.channerManagerRef.resetFields();
          }
        });

        setTimeout(() => {
          if (this.$refs.channerManagerRef) {
            this.$refs.channerManagerRef.resetFields();
          }
        }, 50);
      }
    }
  };
</script>

<style scoped>
  .dialog-footer {
    text-align: center;
  }
</style>
