<p align="center">
  <img width="320" src="https://wpimg.wallstcn.com/ecc53a42-d79b-42e2-8852-5126b810a4c8.svg">
</p>

<p align="center">
  <a href="https://github.com/vuejs/vue">
    <img src="https://img.shields.io/badge/vue-2.6.10-brightgreen.svg" alt="vue">
  </a>
  <a href="https://github.com/ElemeFE/element">
    <img src="https://img.shields.io/badge/element--ui-2.7.0-brightgreen.svg" alt="element-ui">
  </a>
  <a href="https://travis-ci.org/PanJiaChen/vue-element-admin" rel="nofollow">
    <img src="https://travis-ci.org/PanJiaChen/vue-element-admin.svg?branch=master" alt="Build Status">
  </a>
  <a href="https://github.com/PanJiaChen/vue-element-admin/blob/master/LICENSE">
    <img src="https://img.shields.io/github/license/mashape/apistatus.svg" alt="license">
  </a>
  <a href="https://github.com/PanJiaChen/vue-element-admin/releases">
    <img src="https://img.shields.io/github/release/PanJiaChen/vue-element-admin.svg" alt="GitHub release">
  </a>
  <a href="https://gitter.im/vue-element-admin/discuss">
    <img src="https://badges.gitter.im/Join%20Chat.svg" alt="gitter">
  </a>
  <a href="https://panjiachen.github.io/vue-element-admin-site/donate">
    <img src="https://img.shields.io/badge/%24-donate-ff69b4.svg" alt="donate">
  </a>
</p>

English | [简体中文](./README.zh-CN.md) | [日本語](./README.ja.md) | [Spanish](./README.es.md)

## Introduction

[vue-element-admin](https://panjiachen.github.io/vue-element-admin) is a production-ready front-end solution for admin interfaces. It is based on [vue](https://github.com/vuejs/vue) and uses the UI Toolkit [element-ui](https://github.com/ElemeFE/element).

[vue-element-admin](https://panjiachen.github.io/vue-element-admin) is based on the newest development stack of vue and it has a built-in i18n solution, typical templates for enterprise applications, and lots of awesome features. It helps you build large and complex Single-Page Applications. I believe whatever your needs are, this project will help you.

- [Preview](https://panjiachen.github.io/vue-element-admin)

- [Documentation](https://panjiachen.github.io/vue-element-admin-site/)

- [Gitter](https://gitter.im/vue-element-admin/discuss)

- [Donate](https://panjiachen.github.io/vue-element-admin-site/donate/)

- [Wiki](https://github.com/PanJiaChen/vue-element-admin/wiki)

- [Gitee](https://panjiachen.gitee.io/vue-element-admin/) 国内用户可访问该地址在线预览

- Base template recommends using: [vue-admin-template](https://github.com/PanJiaChen/vue-admin-template)
- Desktop: [electron-vue-admin](https://github.com/PanJiaChen/electron-vue-admin)
- Typescript: [vue-typescript-admin-template](https://github.com/Armour/vue-typescript-admin-template) (Credits: [@Armour](https://github.com/Armour))
- [awesome-project](https://github.com/PanJiaChen/vue-element-admin/issues/2312)

**After the `v4.1.0+` version, the default master branch will not support i18n. Please use [i18n Branch](https://github.com/PanJiaChen/vue-element-admin/tree/i18n), it will keep up with the master update**

**The current version is `v4.0+` build on `vue-cli`. If you find a problem, please put [issue](https://github.com/PanJiaChen/vue-element-admin/issues/new). If you want to use the old version , you can switch branch to [tag/3.11.0](https://github.com/PanJiaChen/vue-element-admin/tree/tag/3.11.0), it does not rely on `vue-cli`**

**This project does not support low version browsers (e.g. IE). Please add polyfill by yourself.**

## Preparation

You need to install [node](https://nodejs.org/) and [git](https://git-scm.com/) locally. The project is based on [ES2015+](https://es6.ruanyifeng.com/), [vue](https://cn.vuejs.org/index.html), [vuex](https://vuex.vuejs.org/zh-cn/), [vue-router](https://router.vuejs.org/zh-cn/), [vue-cli](https://github.com/vuejs/vue-cli) , [axios](https://github.com/axios/axios) and [element-ui](https://github.com/ElemeFE/element), all request data is simulated using [Mock.js](https://github.com/nuysoft/Mock).
Understanding and learning this knowledge in advance will greatly help the use of this project.

[![Edit on CodeSandbox](https://codesandbox.io/static/img/play-codesandbox.svg)](https://codesandbox.io/s/github/PanJiaChen/vue-element-admin/tree/CodeSandbox)

<p align="center">
  <img width="900" src="https://wpimg.wallstcn.com/a5894c1b-f6af-456e-82df-1151da0839bf.png">
</p>

## Sponsors

Become a sponsor and get your logo on our README on GitHub with a link to your site. [[Become a sponsor]](https://www.patreon.com/panjiachen)

<a href="https://flatlogic.com/admin-dashboards?from=vue-element-admin"><img width="150px" src="https://wpimg.wallstcn.com/9c0b719b-5551-4c1e-b776-63994632d94a.png" /></a><p>Admin Dashboard Templates made with Vue, React and Angular.</p>

## Features

```
- Login / Logout

- Permission Authentication
  - Page permission
  - Directive permission
  - Permission configuration page
  - Two-step login

- Multi-environment build
  - Develop (dev)
  - sit
  - Stage Test (stage)
  - Production (prod)

- Global Features
  - I18n
  - Multiple dynamic themes
  - Dynamic sidebar (supports multi-level routing)
  - Dynamic breadcrumb
  - Tags-view (Tab page Support right-click operation)
  - Svg Sprite
  - Mock data
  - Screenfull
  - Responsive Sidebar

- Editor
  - Rich Text Editor
  - Markdown Editor
  - JSON Editor

- Excel
  - Export Excel
  - Upload Excel
  - Visualization Excel
  - Export zip

- Table
  - Dynamic Table
  - Drag And Drop Table
  - Inline Edit Table

- Error Page
  - 401
  - 404

- Components
  - Avatar Upload
  - Back To Top
  - Drag Dialog
  - Drag Select
  - Drag Kanban
  - Drag List
  - SplitPane
  - Dropzone
  - Sticky
  - CountTo

- Advanced Example
- Error Log
- Dashboard
- Guide Page
- ECharts
- Clipboard
- Markdown to html
```

## Getting started

```bash
# clone the project
git clone -b i18n **************:PanJiaChen/vue-element-admin.git

# enter the project directory
cd vue-element-admin

# install dependency
npm install

# develop
npm run dev
```

This will automatically open http://localhost:9527

## Build

```bash
# build for test environment
npm run build:stage

# build for production environment
npm run build:prod
```

## Advanced

```bash
# preview the release environment effect
npm run preview

# preview the release environment effect + static resource analysis
npm run preview -- --report

# code format check
npm run lint

# code format check and auto fix
npm run lint -- --fix
```

Refer to [Documentation](https://panjiachen.github.io/vue-element-admin-site/guide/essentials/deploy.html) for more information

## Changelog

Detailed changes for each release are documented in the [release notes](https://github.com/PanJiaChen/vue-element-admin/releases).

## Online Demo

[Preview](https://panjiachen.github.io/vue-element-admin)

## Donate

If you find this project useful, you can buy author a glass of juice :tropical_drink:

![donate](https://wpimg.wallstcn.com/bd273f0d-83a0-4ef2-92e1-9ac8ed3746b9.png)

[Paypal Me](https://www.paypal.me/panfree23)

[Buy me a coffee](https://www.buymeacoffee.com/Pan)

## Browsers support

Modern browsers and Internet Explorer 10+.

| [<img src="https://raw.githubusercontent.com/alrra/browser-logos/master/src/edge/edge_48x48.png" alt="IE / Edge" width="24px" height="24px" />](https://godban.github.io/browsers-support-badges/)</br>IE / Edge | [<img src="https://raw.githubusercontent.com/alrra/browser-logos/master/src/firefox/firefox_48x48.png" alt="Firefox" width="24px" height="24px" />](https://godban.github.io/browsers-support-badges/)</br>Firefox | [<img src="https://raw.githubusercontent.com/alrra/browser-logos/master/src/chrome/chrome_48x48.png" alt="Chrome" width="24px" height="24px" />](https://godban.github.io/browsers-support-badges/)</br>Chrome | [<img src="https://raw.githubusercontent.com/alrra/browser-logos/master/src/safari/safari_48x48.png" alt="Safari" width="24px" height="24px" />](https://godban.github.io/browsers-support-badges/)</br>Safari |
| --------- | --------- | --------- | --------- |
| IE10, IE11, Edge | last 2 versions | last 2 versions | last 2 versions |

## License

[MIT](https://github.com/PanJiaChen/vue-element-admin/blob/master/LICENSE)

Copyright (c) 2017-present PanJiaChen
卷 Windows 的文件夹 PATH 列表
卷序列号为 3AA0-4D0B
C:.
│  App.vue
│  errorLog.js
│  list.txt
│  main.js
│  permission.js
│  settings.js
│  
├─api 接口地址
│  │  active.js
│  │  agentAddList.js
│  │  agentList.js
│  │  alibaba.js
│  │  allinOneMember.js
│  │  appVersionControl.js
│  │  areaAgentList.js
│  │  areasDealerList.js
│  │  areasMarketList.js
│  │  areasSchoolList.js
│  │  areasStudentCourseList.js
│  │  areasStudentExperienceList.js
│  │  areasStudentForgetList.js
│  │  areasStudentTestResultList.js
│  │  areasStudentWordPrintList.js
│  │  article.js
│  │  auth.js
│  │  authentication.js
│  │  authorizationCode.js
│  │  broadcast.js
│  │  bstatus.js
│  │  bvAdminApi.js
│  │  cardApi.js
│  │  cardTypeApi.js
│  │  checkCourse.js
│  │  checkOrder.js
│  │  classRecord.js
│  │  companyList.js
│  │  complain.js
│  │  courseAdditionList.js
│  │  courseCate.js
│  │  courseCategory.js
│  │  courseChildren.js
│  │  courseClock.js
│  │  courseDictationList.js
│  │  courseList.js
│  │  courseListening.js
│  │  courseMake.js
│  │  courseOrder.js
│  │  coursePackage.js
│  │  coursePackageList.js
│  │  CoursependingPayment.js
│  │  coursePrice.js
│  │  courseReading.js
│  │  cousys.js
│  │  dealerList.js
│  │  dealerRankList.js
│  │  deliverSetting.js
│  │  dictationWordLevel.js
│  │  dictationWordTestList.js
│  │  dictationWordType.js
│  │  distributor.js
│  │  divisionAdd.js
│  │  divisionList.js
│  │  divisionProfitRank.js
│  │  divisionRank.js
│  │  elasticSearch.js
│  │  experience.js
│  │  experienceBonusWithdrawRecoder.js
│  │  experienceCode.js
│  │  expertsApplicantsList.js
│  │  expertsFillList.js
│  │  feedback.js
│  │  flow.js
│  │  forgot.js
│  │  grammar.js
│  │  GrammarConsumeHours.js
│  │  homeApi.js
│  │  investmentRecruitmentDetailsList.js
│  │  ledgerOrderList.js
│  │  levelConfig.js
│  │  marketList.js
│  │  marketProfitAccount.js
│  │  marketProfitAmout.js
│  │  marketProfitRankFlows.js
│  │  member.js
│  │  memberFundStatement.js
│  │  memberRechargeOrder.js
│  │  menu.js
│  │  merchantAccountFlow.js
│  │  merProfitConfig.js
│  │  news.js
│  │  newUtil.js
│  │  noticeEdit.js
│  │  noticeNews.js
│  │  offSiteApi.js
│  │  offSiteReferrerApi.js
│  │  operationsList.js
│  │  option.js
│  │  order.js
│  │  performance.js
│  │  perm.js
│  │  product.js
│  │  profitRank.js
│  │  profitWithdrawRecoder.js
│  │  promoterAdd.js
│  │  promoterList.js
│  │  qiniu.js
│  │  rangCode.js
│  │  rebateRecordList.js
│  │  rechargeCourseOrder.js
│  │  recommendationChain.js
│  │  relieveAuthentication.js
│  │  remote-search.js
│  │  reProfitRank.js
│  │  riskAuthCode.js
│  │  role.js
│  │  scheduleRelation.js
│  │  schooldeliverconfig.js
│  │  schoolList.js
│  │  scoreConfig.js
│  │  sessionStorage.js
│  │  studentCourseProgress.js
│  │  studentCourseRecord.js
│  │  studentjf.js
│  │  studentList.js
│  │  studentMarketingCourseRecord.js
│  │  studentPrintClose.js
│  │  studentReviewPrint.js
│  │  studentTestPrint.js
│  │  studentTestPrintReading.js
│  │  studentWordReviewList.js
│  │  studentWordsTest.js
│  │  studentWordView.js
│  │  superReadCourseList.js
│  │  systemConfiguration.js
│  │  textbookVersionList.js
│  │  tutor.js
│  │  uploadVideoFile.js
│  │  user.js
│  │  userOptionLogsApi.js
│  │  userself.js
│  │  wechatPublicAccount.js
│  │  wordBookView.js
│  │  wordLevelList.js
│  │  wordLevelTestdbList.js
│  │  
│  ├─abacusMentalCalc
│  │      category.js
│  │      courseQuestion.js
│  │      report.js
│  │      
│  ├─activiti
│  │  │  definition.js
│  │  │  flowCategory.js
│  │  │  flowController.js
│  │  │  flowDictionary.js
│  │  │  flowEntry.js
│  │  │  flowEntryVariable.js
│  │  │  flowOnlineOperation.js
│  │  │  flowOperation.js
│  │  │  flowStart.js
│  │  │  myTask.js
│  │  │  onlineColumn.js
│  │  │  onlineDatasource.js
│  │  │  onlineDatasourceRelation.js
│  │  │  onlineDblink.js
│  │  │  onlineDict.js
│  │  │  onlineForm.js
│  │  │  onlinePage.js
│  │  │  onlineRule.js
│  │  │  onlineVirtualColumn.js
│  │  │  openCommon.js
│  │  │  
│  │  └─staticDict
│  │          flowStaticDict.js
│  │          index.js
│  │          onlineStaticDict.js
│  │          
│  ├─channel
│  │      channelAuthority.js
│  │      channelManager.js
│  │      
│  ├─delivery
│  │      deliveryCenter.js
│  │      
│  ├─dxt
│  │      feedback.js
│  │      flow.js
│  │      meetingApi.js
│  │      meetingCode.js
│  │      meetingInvitation.js
│  │      meetingorder.js
│  │      presenterList.js
│  │      referrerJoinMeeting.js
│  │      
│  ├─FinanceApi
│  │      Finance.js
│  │      
│  ├─grammar
│  │      configUration.js
│  │      grammarPoint.js
│  │      grammarQuestion.js
│  │      knowledgePoint.js
│  │      phaseCredentialList.js
│  │      xmind.js
│  │      
│  ├─learnTube
│  │      learnTube.js
│  │      
│  ├─mps
│  │      refund.js
│  │      
│  ├─paper
│  │  │  abilityReport.js
│  │  │  complex-question.js
│  │  │  course.js
│  │  │  grade.js
│  │  │  knowledge.js
│  │  │  paper.js
│  │  │  question.js
│  │  │  report.js
│  │  │  subject.js
│  │  │  
│  │  └─train
│  │          category.js
│  │          course.js
│  │          difficulty.js
│  │          guidePage.js
│  │          image.js
│  │          learnmaster.js
│  │          learnmasterstudents.js
│  │          question.js
│  │          
│  ├─risk
│  │      risk.js
│  │      riskWhiteList.js
│  │      
│  ├─studyroom
│  │      committeeman.js
│  │      feedback.js
│  │      sceneInfo.js
│  │      scheduleplan.js
│  │      spokenTest.js
│  │      spokenType.js
│  │      studentList.js
│  │      studyRoom.js
│  │      studySchedule.js
│  │      timer.js
│  │      voiceAnswers.js
│  │      
│  ├─superApi
│  │      article.js
│  │      question.js
│  │      
│  ├─training
│  │      course.js
│  │      exam.js
│  │      
│  └─xi
│      │  bigGrading.js
│      │  classify.js
│      │  classifyType.js
│      │  commondict.js
│      │  exchangerecord.js
│      │  extradate.js
│      │  getmethod.js
│      │  gift.js
│      │  goods.js
│      │  goodsclassify.js
│      │  grading.js
│      │  guide.js
│      │  incentives.js
│      │  level.js
│      │  manage.js
│      │  pendant.js
│      │  ratioconfig.js
│      │  remarkContent.js
│      │  report.js
│      │  reward.js
│      │  scoreContent.js
│      │  showconfig.js
│      │  subject.js
│      │  sysUser.js
│      │  target.js
│      │  tree.js
│      │  xiParentsSysMessage.js
│      │  xiTask.js
│      │  xiTree.js
│      │  
│      └─community
│              banner_run.js
│              official.js
│              plate.js
│              post.js
│              posterbackground.js
│              punishconfig.js
│              studentInfo.js
│              systemavatar.js
│              taskawrad.js
│              
├─assets
│  │  bg.jpg
│  │  bg.png
│  │  czpc.png
│  │  echarts-macarons.js
│  │  gzpc.png
│  │  <EMAIL>
│  │  imgs.png
│  │  loginFromBg.png
│  │  loginFromBg2.png
│  │  logo.png
│  │  logo2.png
│  │  no_data.png
│  │  phoneBG.png
│  │  phoneBG1.png
│  │  pic.png
│  │  pic1.png
│  │  <EMAIL>
│  │  <EMAIL>
│  │  pic_qudao2.png
│  │  <EMAIL>
│  │  shadow.png
│  │  xxpc.png
│  │  zip.png
│  │  
│  ├─401_images
│  │      401.gif
│  │      
│  ├─404_images
│  │      404.png
│  │      404_cloud.png
│  │      
│  ├─custom-theme
│  │  │  index.css
│  │  │  
│  │  └─fonts
│  │          element-icons.ttf
│  │          element-icons.woff
│  │          
│  ├─image
│  │      blockPuzzle.png
│  │      clickWord.png
│  │      default.jpg
│  │      getKey.png
│  │      liucheng.png
│  │      logo2.png
│  │      ziti.png
│  │      
│  └─images
│      │  mapMark.png
│      │  
│      └─studyRoom
│              bg.png
│              change.png
│              chatClose.png
│              chatLine.png
│              chatOpen.png
│              dialogBg.png
│              dialogBtn.png
│              emoji.png
│              img.png
│              in.png
│              laba.png
│              like.png
│              micoClose.png
│              micoOpen.png
│              noLike.png
│              onlinePerson.png
│              out.png
│              refresh.png
│              right.png
│              videoClose.png
│              videoOpen.png
│              wrong.png
│              
├─components
│  ├─BackToTop  //没有调用
│  │      index.vue
│  │      
│  ├─Breadcrumb //项目顶部面包屑组件
│  │      index.vue
│  │      
│  ├─Charts   //没有调用
│  │  │  Keyboard.vue
│  │  │  LineMarker.vue
│  │  │  MixChart.vue
│  │  │  
│  │  └─mixins
│  │          resize.js
│  │          
│  ├─DateRange //main.js全局引用
│  │      index.vue
│  │      
│  ├─Dialog  //main.js全局引用
│  │      index.js
│  │      
│  ├─DndList //没有调用
│  │      index.vue
│  │      
│  ├─DragSelect  //没有调用
│  │      index.vue
│  │      
│  ├─Dropzone  //没有调用
│  │      index.vue
│  │      
│  ├─ErrorLog   //   layout/components/Navbar页面调用
│  │      index.vue
│  │      
│  ├─GithubCorner //没有调用
│  │      index.vue
│  │      
│  ├─Hamburger  ////项目顶部收起菜单按钮组件
│  │      index.vue
│  │      
│  ├─HeaderSearch  // layout/components/Navbar页面调用
│  │      index.vue
│  │      
│  ├─ImageCropper //没有调用
│  │  │  index.vue
│  │  │  
│  │  └─utils
│  │          data2blob.js
│  │          effectRipple.js
│  │          language.js
│  │          mimes.js
│  │          
│  ├─JsonEditor //没有调用
│  │      index.vue
│  │      
│  ├─Kanban //没有调用
│  │      index.vue
│  │      
│  ├─LangSelect  //没有调用
│  │      index.vue
│  │      
│  ├─MarkdownEditor   //没有调用
│  │      default-options.js
│  │      index.vue
│  │      
│  ├─MDinput   //没有调用
│  │      index.vue
│  │      
│  ├─Pagination  //只有监管体系模块调用 src/view/risk
│  │      index.vue
│  │      
│  ├─PanThumb
│  │      index.vue
│  │      
│  ├─RightPanel  //src/layout/index.vue调用
│  │      index.vue
│  │      
│  ├─Screenfull  //src/layout/Navbar.vue调用
│  │      index.vue
│  │      
│  ├─ScrollPane //src/layout/components/TagsView.vue调用 项目顶部记录点过页面的记录模块使用组件
│  │      index.vue
│  │      
│  ├─Share
│  │      DropdownMenu.vue
│  │      
│  ├─SizeSelect
│  │      index.vue
│  │      
│  ├─Sticky
│  │      index.vue
│  │      
│  ├─SvgIcon
│  │      index.vue
│  │      
│  ├─TextHoverEffect  //src/views/dashboard/admin/BoxCard页面引用
│  │      Mallki.vue
│  │      
│  ├─ThemePicker //没有调用
│  │      index.vue
│  │      
│  ├─Tinymce  //富文本编辑器
│  │  │  dynamicLoadScript.js
│  │  │  index.vue
│  │  │  plugins.js
│  │  │  toolbar.js
│  │  │  
│  │  └─components
│  │          EditorImage.vue
│  │          
│  ├─Ueditor
│  │      index.vue
│  │      
│  ├─Upload   //图片上传组件
│  │      DragUpload.vue
│  │      ExcelUpload.vue
│  │      FeatureGraphicsUpload.vue
│  │      FGUploadWithSelect.vue
│  │      JsonUpload.vue
│  │      MultipleUpload.vue
│  │      MyUpload.vue
│  │      OneImageUpload.vue
│  │      SingleImage.vue
│  │      SingleImage2.vue
│  │      SingleImage3.vue
│  │      UploadCompressedFile.vue
│  │      UploadFile.vue
│  │      UploadWithNumber.vue
│  │      UploadWithSelect.vue
│  │      ZipUpload.vue
│  │      
│  ├─UploadExcel 
│  │      index.vue
│  │      
│  ├─UploadVideo  //保利威视频上传组件  课件上传
│  │      index.vue
│  │      singleUpload.vue
│  │      
│  ├─uploadVideoFile   //保利威视频上传组件
│  │      index.vue
│  │      
│  └─verifition  //验证码组件
│      │  Verify.vue
│      │  
│      ├─api
│      │      index.js
│      │      
│      ├─utils  //公共js 组件
│      │      ase.js
│      │      axios.js
│      │      util.js
│      │      
│      └─Verify
│              VerifyPoints.vue
│              VerifySlide.vue
│              
├─directive
│  │  sticky.js
│  │  
│  ├─clipboard
│  │      clipboard.js
│  │      index.js
│  │      
│  ├─el-drag-dialog
│  │      drag.js
│  │      index.js
│  │      
│  ├─el-dragDialog
│  │      drag.js
│  │      index.js
│  │      
│  ├─el-table
│  │      adaptive.js
│  │      index.js
│  │      
│  ├─lazy
│  │      index.js
│  │      
│  ├─permission
│  │      hasPerm.js
│  │      index.js
│  │      perm.js
│  │      permission.js
|  |
|  |─trim  //去除输入框首尾空格
│  │     index.js
│  │      
│  └─waves
│          index.js
│          waves.css
│          waves.js
│          
├─filters
│      index.js
│      timeDown.js
│      
├─icons
│  │  index.js
│  │  stop.png
│  │  svgo.yml
│  │  
│  └─svg      //菜单图标
│          21_day.svg
│          404.svg
│          agent.svg
│          allInMember.svg
│          alllline_charge.svg
│          app_version.svg
│          authentication.svg
│          authorization.svg
│          authorization_code.svg
│          banner.svg
│          base_info.svg
│          broadcast.svg
│          bug.svg
│          busitcode.svg
│          buycode.svg
│          card_dzy.svg
│          card_type.svg
│          chart.svg
│          clipboard.svg
│          company.svg
│          component.svg
│          course.svg
│          course_category.svg
│          course_clock.svg
│          course_level.svg
│          course_test.svg
│          dashboard.svg
│          dealer.svg
│          dealer_rank.svg
│          detail_info.svg
│          divisionList.svg
│          documentation.svg
│          drag.svg
│          edit.svg
│          education.svg
│          email.svg
│          example.svg
│          excel.svg
│          exit-fullscreen.svg
│          eye-open.svg
│          eye.svg
│          feed_back.svg
│          form.svg
│          fullscreen.svg
│          grammar.svg
│          grammar_pwd_config.svg
│          grammar_question.svg
│          grammer.svg
│          guide.svg
│          hand_out.svg
│          home.svg
│          icon.svg
│          international.svg
│          invitationList.svg
│          key.svg
│          knowledge.svg
│          language.svg
│          leder_order.svg
│          leve.svg
│          level_config.svg
│          link.svg
│          list.svg
│          lock.svg
│          lubo.svg
│          makertList.svg
│          market_dealer.svg
│          market_flow.svg
│          market_manage.svg
│          meeting.svg
│          meetingList.svg
│          meeting_code.svg
│          meeting_order.svg
│          member_flow.svg
│          member_list.svg
│          member_order.svg
│          message.svg
│          money.svg
│          nested.svg
│          online_charge.svg
│          order.svg
│          paike.svg
│          parents.svg
│          password.svg
│          pdf.svg
│          people.svg
│          peoples.svg
│          person.svg
│          post.svg
│          presenterList.svg
│          profit.svg
│          profit_account.svg
│          profit_flow.svg
│          profit_manage.svg
│          promoter.svg
│          qq.svg
│          QR_code.svg
│          referee.svg
│          refund.svg
│          risk.svg
│          risk2.svg
│          roletag.svg
│          roleTagManage.svg
│          role_perm.svg
│          school.svg
│          school_course_flow.svg
│          score_config.svg
│          search.svg
│          shop.svg
│          shopping.svg
│          size.svg
│          skill.svg
│          star.svg
│          student.svg
│          studentList.svg
│          student_course.svg
│          student_course_flow.svg
│          student_course_print.svg
│          student_course_progress.svg
│          student_course_test.svg
│          student_grammar.svg
│          system.svg
│          system_figuration.svg
│          tab.svg
│          table.svg
│          test_print.svg
│          theme.svg
│          tree-table.svg
│          tree.svg
│          tutor.svg
│          update_pwd.svg
│          user.svg
│          userCode.svg
│          usermanage.svg
│          vip.svg
│          wechat.svg
│          whiteList.svg
│          word_book.svg
│          yidi.svg
│          yi_order.svg
│          yupaike.svg
│          zhibo.svg
│          zhiyuan.svg
│          zip.svg
│          
├─lang  //超文本标记语言
│      en.js
│      es.js
│      index.js
│      ja.js
│      zh.js
│      
├─layout   //项目菜单
│  │  index.vue
│  │  
│  ├─components
│  │  │  AppMain.vue
│  │  │  index.js
│  │  │  Navbar.vue
│  │  │  
│  │  ├─Settings
│  │  │      index.vue
│  │  │      
│  │  ├─Sidebar
│  │  │      FixiOSBug.js
│  │  │      index.vue
│  │  │      Item.vue
│  │  │      Link.vue
│  │  │      Logo.vue
│  │  │      SidebarItem.vue
│  │  │      
│  │  └─TagsView
│  │          index.vue
│  │          ScrollPane.vue
│  │          
│  └─mixin
│          ResizeHandler.js
│          
├─plugs
│      print.js
│      
├─router  //路由
│  │  index.js
│  │  _import_development.js
│  │  _import_production.js
│  │  
│  └─modules  
│          charts.js
│          components.js
│          nested.js
│          table.js
│          
├─store
│  │  getters.js
│  │  index.js
│  │  
│  └─modules
│          app.js
│          enumItem.js
│          errorLog.js
│          permission.js
│          settings.js
│          tagsView.js
│          user.js
│          
├─styles   //公共样式
│  │  btn.scss
│  │  element-ui.scss
│  │  element-variables.scss
│  │  index.scss
│  │  mixin.scss
│  │  sidebar.scss
│  │  transition.scss
│  │  variables.scss
│  │  
│  ├─activiti
│  │      index.scss
│  │      process-designer.scss
│  │      process-panel.scss
│  │      
│  └─icon
│          demo.css
│          demo_index.html
│          iconfont.css
│          iconfont.js
│          iconfont.json
│          iconfont.ttf
│          iconfont.woff
│          iconfont.woff2
│          
├─utils
│      auth.js
│      base64.js
│      clipboard.js  //没有调用  
│      code.js
│      constants.js  //存储dxSource和后端服务地址
│      defaultImg.js //检测图片是否有效
         dynamicFunction.js  //  动态开启培训费相关功能
│      error-log.js    //没有调用
│      get-page-title.js //没有调用
│      htmlToPdf.js  //没有调用  导出页面为PDF格式
│      i18n.js
│      index.js
│      open-window.js
│      permission.js
│      request.js  //axios封装
│      requestuser.js  //axios封装
│      scroll-to.js
│      validate.js
│      widget.js
│      
├─vendor
│      Export2Excel.js     //没有调用
│      Export2Zip.js   //没有调用
│      
└─views
    ├─abacusMentalCalc
    │  │  category_index.vue  //珠心算/维度管理
    │  │  course_index.vue     //珠心算/课程管理
    │  │  question.vue     //珠心算/课程题目
    │  │  question_index.vue      //珠心算/题型管理
    │  │  report.vue        //珠心算/报告维度编辑
    │  │  report_index.vue      //珠心算/报告管理
    │  │  subQuestion.vue   //珠心算/附加题型
    │  │  
    │  └─question
    │          audioAnswer.vue  //珠心算/音频答案题
    │          countdown.vue  //珠心算/倒计时题
    │          duibushu_img.vue  //珠心算/对部数图片
    │          duibushu_num.vue  //珠心算/对部数数学
    │          only_question.vue  //珠心算/算式题
    │          only_score.vue     //珠心算/听写题
    │          question_answer.vue  //珠心算/算式答案题
    │          video_audio.vue   //珠心算/音视频题
    │          
    ├─active
    │      active.vue //没有引用
    │      
    ├─activiti
    │  │  mytask.vue             //工作流/我的代办
    │  │  
    │  ├─definition
    │  │      index.vue   //工作流/流程定义
    │  │      
    │  ├─onlineForm
    │  │  │  index.vue          //在线表单/在线表单
    │  │  │  
    │  │  ├─components
    │  │  │      customFilterWidget.vue  //表单组件
    │  │  │      customImage.vue //图片展示组件  ----  在线表单/表单管理（新建/编辑组件-在线表单子表单列表组件）
    │  │  │      customTable.vue //列表组件
    │  │  │      customText.vue //文案展示组件  ----  在线表单/表单管理（新建/编辑组件-在线表单子表单列表组件）
    │  │  │      customUpload.vue  //上传图片和文件组件
    │  │  │      customWidget.vue  //表单输入组件
    │  │  │      dragableFilterBox.vue   //在线表单子表单列表里的组件  ----  在线表单/表单管理（新建/编辑组件-在线表单子表单列表组件）
    │  │  │      editableWidgetItem.vue   //在线表单子表单列表里的组件  ----  在线表单/表单管理（新建/编辑组件-在线表单子表单列表组件）
    │  │  │      editDictParamValue.vue //字典参数设置组件  ----  在线表单/表单管理（新建/编辑组件-在线表单子表单列表组件点击参数值里的按钮）
    │  │  │      editFormParam.vue   //添加表单参数组件  ----  在线表单/表单管理（新建/编辑组件-在线表单子表单列表组件点击添加查询参数按钮）
    │  │  │      editTableQueryParam.vue  //添加查询参数组件  ----  在线表单/表单管理（新建/编辑组件-在线表单子表单列表组件）
    │  │  │      editWidgetTableColumn.vue//编辑添加字段组件  ----  在线表单/表单管理（新建/编辑组件-在线表单子表单列表组件点击添加表格字段按钮）
    │  │  │      editWidgetTableOperation.vue //编辑操作/新建操作组件 ---在线表单/表单管理（新建/编辑组件-在线表单子表单列表组件点击添加自定义操作按钮）
    │  │  │      formGenerator.vue  //       ----  在线表单/表单管理（新建/编辑组件）
    │  │  │      
    │  │  ├─data
    │  │  │      onlineFormOptions.js
    │  │  │      
    │  │  ├─formOnlineDict
    │  │  │      editDictDataButton.vue // 自定义字典类型数据字典键值列表组件   ---  在线表单/字典管理 新增/编辑弹窗组件
    │  │  │      editOnlineDict.vue            //新增/编辑弹窗组件    ---  在线表单/字典管理
    │  │  │      index.vue        //在线表单/字典管理
    │  │  │      
    │  │  ├─formRender  //在线表单内部组件 ----- 在线表单/在线表单
    │  │  │      onlineEditForm.vue   
    │  │  │      onlineFormMixins.js
    │  │  │      onlineQueryForm.vue
    │  │  │      onlineWorkOrder.vue
    │  │  │      workflowForm.vue
    │  │  │      
    │  │  ├─onlinePage
    │  │  │      editOnlineForm.vue // 编辑表单组件 ------------ 在线表单/表单管理（新建/编辑组件）
    │  │  │      editOnlinePageDatasource.vue  //  新增数据表按钮 新建数据源组件 -----------   //在线表单/表单管理（新建/编辑组件）
    │  │  │      editOnlinePageDatasourceRelation.vue  //新增数据表按钮添加关联组件 -----------   //在线表单/表单管理（新建/编辑组件）
    │  │  │      editVirtualColumnFilter.vue 添加过滤条件组件  //在线表单/表单管理（新建/编辑组件-- 数据源虚拟字段设置组件）
    │  │  │      index.vue                              //在线表单/表单管理
    │  │  │      onlinePageSetting.vue     //新建/编辑组件     ----  在线表单/表单管理
    │  │  │      onlinePageTableColumnRule.vue    //编辑数据表字段验证规则 组件 ----------   //在线表单/表单管理（新建/编辑组件）
    │  │  │      onlinePageVirtualColumn.vue           // 数据源虚拟字段设置组件 ----------   //在线表单/表单管理（新建/编辑组件）
    │  │  │      setOnlineTableColumnRule.vue         //编辑字段验证规则/设置字段验证规则 组件 ----------   //在线表单/表单管理（新建/编辑组件）
    │  │  │      
    │  │  └─utils
    │  │          index.js
    │  │          
    │  └─workFlow
    │      ├─components
    │      │      FormAddDealer.vue                 //开通托管中心组件---任务管理/任务办理（组件）
    │      │      FormAddDirectSchool.vue              //直营门店组件---任务管理/任务办理（组件）
    │      │      FormAddDivision.vue              //事业部审核组件---任务管理/任务办理（组件）
    │      │      FormDeliveryCenter.vue             //交付中心组件---任务管理/任务办理（组件）
    │      │      FormOpenAreaAgent.vue            //区县服务商组件---任务管理/任务办理（组件）
    │      │      FormOpenCityAgent.vue            //市级服务商组件---任务管理/任务办理（组件）
    │      │      FormOpenCompany.vue            //分公司开通组件---任务管理/任务办理（组件）
    │      │      FormOpenExperience.vue           //体验中心审核组件---任务管理/任务办理（组件）
    │      │      FormOpenLearnTube.vue          //学管师表单组件---任务管理/任务办理（组件）
    │      │      FormOperations.vue            //超级俱乐部组件---任务管理/任务办理（组件）
    │      │      HandlerFlowTask.vue                //任务管理/任务办理（组件）
    │      │      ProcessDesigner.vue              //流程管理/流程设计  （内部组件）
    │      │      ProcessViewer.vue             //流程管理/流程设计  （内部组件）
    │      │      TagSelect.vue                  //组件
    │      │      TaskCommit.vue    //预处理工作流操作弹框组件
    │      │      TaskGroupSelect.vue         //组件没有引用
    │      │      TaskPostSelect.vue       //组件没有引用
    │      │      TaskUserSelect.vue      //组件没有引用
    │      │      
    │      ├─flowCategory
    │      │      formEditFlowCategory.vue     //新建/编辑组件 ----------- 流程管理/流程分类
    │      │      formFlowCategory.vue         //流程管理/流程分类
    │      │      
    │      ├─flowEntry
    │      │      formEditFlowEntry.vue           //新建/编辑组件 ------------ 流程管理/流程设计
    │      │      formEditFlowEntryVariable.vue   //流程变量添加变量组件 ----------- 流程管理/流程分类（新建/编辑组件）
    │      │      formFlowEntry.vue                 //流程管理/流程设计
    │      │      formPublishedFlowEntry.vue  //基础信息组件 ----------- 流程管理/流程分类（新建/编辑组件）
    │      │      
    │      ├─handlerFlowTask
    │      │      index.vue              //任务管理/任务办理
    │      │      
    │      ├─mixins
    │      │      flowMixins.js
    │      │      
    │      ├─package
    │      │  │  index.js
    │      │  │  utils.js
    │      │  │  
    │      │  ├─highlight
    │      │  │      index.js
    │      │  │      
    │      │  ├─process-designer
    │      │  │  │  index.js                  //引用ProcessDesigner
    │      │  │  │  ProcessDesigner.vue  //组件 ----- 流程管理/流程设计  （内部组件）
    │      │  │  │  
    │      │  │  └─plugins
    │      │  │      │  defaultEmpty.js
    │      │  │      │  
    │      │  │      ├─content-pad
    │      │  │      │      contentPadProvider.js
    │      │  │      │      index.js
    │      │  │      │      
    │      │  │      ├─descriptor
    │      │  │      │      activitiDescriptor.json
    │      │  │      │      camundaDescriptor.json
    │      │  │      │      flowableDescriptor.json
    │      │  │      │      
    │      │  │      ├─extension-moddle
    │      │  │      │  ├─activiti
    │      │  │      │  │      activitiExtension.js
    │      │  │      │  │      index.js
    │      │  │      │  │      
    │      │  │      │  ├─camunda
    │      │  │      │  │      extension.js
    │      │  │      │  │      index.js
    │      │  │      │  │      
    │      │  │      │  └─flowable
    │      │  │      │          flowableExtension.js
    │      │  │      │          index.js
    │      │  │      │          
    │      │  │      ├─palette
    │      │  │      │      index.js
    │      │  │      │      paletteProvider.js
    │      │  │      │      
    │      │  │      └─translate
    │      │  │              customTranslate.js
    │      │  │              zh.js
    │      │  │              
    │      │  ├─refactor
    │      │  │  │  index.js
    │      │  │  │  PropertiesPanel.vue
    │      │  │  │  
    │      │  │  ├─base
    │      │  │  │      ElementBaseInfo.vue  //常规组件     views/activiti/workFlow/package/refactor/PropertiesPanel.vue引用
    │      │  │  │      
    │      │  │  ├─flow-condition
    │      │  │  │      FlowCondition.vue     //流转条件组件     views/activiti/workFlow/package/refactor/PropertiesPanel.vue引用
    │      │  │  │      
    │      │  │  ├─form
    │      │  │  │      ElementForm.vue  views/activiti/workFlow/package/refactor/PropertiesPanel.vue隐藏引用
    │      │  │  │      flowFormConfig.vue             //任务表单组件     views/activiti/workFlow/package/refactor/PropertiesPanel.vue引用
    │      │  │  │      formEditOperation.vue          //点击编辑和添加按钮组件
    │      │  │  │      
    │      │  │  ├─form-variable
    │      │  │  │      index.vue                 //任务变量组件     views/activiti/workFlow/package/refactor/PropertiesPanel.vue引用
    │      │  │  │      
    │      │  │  ├─listeners
    │      │  │  │      ElementListeners.vue          //执行监听器组件     views/activiti/workFlow/package/refactor/PropertiesPanel.vue引用
    │      │  │  │      template.js
    │      │  │  │      UserTaskListeners.vue          //任务监听器组件     views/activiti/workFlow/package/refactor/PropertiesPanel.vue引用
    │      │  │  │      utilSelf.js
    │      │  │  │      
    │      │  │  ├─multi-instance
    │      │  │  │      ElementMultiInstance.vue
    │      │  │  │      ElementMultiInstanceAssignee.vue        //组件没有引用
    │      │  │  │      
    │      │  │  ├─other
    │      │  │  │      ElementOtherConfig.vue           //其他组件     views/activiti/workFlow/package/refactor/PropertiesPanel.vue引用
    │      │  │  │      
    │      │  │  ├─properties
    │      │  │  │      ElementProperties.vue       //扩展属性组件     views/activiti/workFlow/package/refactor/PropertiesPanel.vue引用
    │      │  │  │      
    │      │  │  ├─signal-message
    │      │  │  │      SignalAndMessage.vue            //消息与信号组件     views/activiti/workFlow/package/refactor/PropertiesPanel.vue引用
    │      │  │  │      
    │      │  │  └─task
    │      │  │      │  ElementTask.vue          //任务设置组件     views/activiti/workFlow/package/refactor/PropertiesPanel.vue引用
    │      │  │      │  
    │      │  │      └─task-components
    │      │  │              ReceiveTask.vue   //消息接收任务组件     views/activiti/workFlow/package/refactor/PropertiesPanel.vue引用
    │      │  │              ScriptTask.vue    //脚本任务配置组件     views/activiti/workFlow/package/refactor/PropertiesPanel.vue引用
    │      │  │              UserTask.vue      //用户任务配置组件     views/activiti/workFlow/package/refactor/PropertiesPanel.vue引用 
    │      │  │              
    │      │  └─theme
    │      │          flow-element-variables.scss
    │      │          index.scss
    │      │          process-designer.scss
    │      │          process-panel.scss
    │      │          
    │      └─taskManager
    │              formAllInstance.vue            //流程管理/流程实例
    │              formMyApprovedTask.vue  //任务管理/ 已办任务
    │              formMyHistoryTask.vue  //任务管理/ 历史任务
    │              formMyTask.vue                  //任务管理/ 代办任务
    │              formTaskProcessViewer.vue    //点击流程图按钮弹窗组件----------------流程管理/流程实例
    │              stopTask.vue            //点击终止按钮弹窗组件----------------流程管理/流程实例
    │              
    ├─areas
    │  ├─authorizationcode
    │  │      authorizationCode.vue   //  授权码列表
    │  │      
    │  ├─businessCode
    │  │      areasBusinessDistrictCode.vue      // 没有引用
    │  │      businessDistrictCode.vue              //商圈码管理/商圈码列表
    │  │      
    │  ├─dealer
    │  │      addMoreInfo.vue                            // 商户管理/批量新增    （直推门店-批量新增）
    │  │      areasAgentDealerList.vue          // 商户管理/托管中心列表
    │  │      areasAgentSchoolList.vue            // 商户管理/门店列表
    │  │      areasDealerAdd.vue            // 商户管理/托管中心新增
    │  │      areasDealerList.vue            // 商户管理/托管中心列表
    │  │      areasSchoolAdd.vue                  // 商户管理/门店新增  没有引用
    │  │      areasSchoolEdit.vue                // 商户管理/门店编辑
    │  │      areasSchoolList.vue               // 商户管理/直推门店
    │  │      
    │  ├─offsite
    │  │      order.vue
    │  │      referrerOrder.vue        //商户管理/推荐异地开店列表
    │  │      
    │  ├─promoter
    │  │      areasAgentAdd.vue         // 商户管理/市级服务商新增
    │  │      areasPromoterAgentList.vue         // 商户管理 /市级服务商列表
    │  │      
    │  ├─recommendationChain  
    │  │      recommendationChain.vue       //完整推荐链
    │  │      
    │  ├─referrer
    │  │      areasMarketList.vue             //没有引用
    │  │      
    │  └─school
    │          addCourseList.vue          //空白页面没有引用
    │          areasOpenCourse.vue            //学员管理/开通课程
    │          areasOpenSuperReadCourse.vue   //学员管理/学员课程记录
    │          areasStudentCourseFlow.vue   //学员管理/学员销课记录
    │          areasStudentCourseList.vue  //学员管理/学员列表 （学员鼎英语学时充值/开通课程包信息
    │          areasStudentCourseRecord.vue      //学员管理/学员课程记录
    │          areasStudentExperienceList.vue        //学员管理/学员试课报告
    │          areasStudentForgetList.vue                   //学员管理/21天抗遗忘报告
    │          areasStudentTestResultList.vue     //学员管理/学员词汇测试
    │          areasStudentWordPrintList.vue     //学员管理/学员测验打印
    │          areaStudentWordReviewPrint.vue    //学员管理/21天抗遗忘复习计划
    │          
    ├─broadcast
    │      banner.vue                          //录播系统/横幅配置
    │      broadcast.vue                   //录播系统/录播管理
    │      broadcastCode.vue         //趣味复习/趣味复习购买码
    │      
    ├─channel
    │      channelManager.vue         // 渠道管理/渠道管理员列表
    │      
    ├─collect
    │  └─pay
    │          pay.vue       //跳转到支付登录页面
    │          
    ├─collect.pay
    │      pay.vue       //没有引用
    │      
    ├─course
    │  │  articleList.vue                                  //文章列表-课程管理/超级阅读课程列表 (点击制作文章按钮)
    │  │  courseCate.vue                              //课程管理/课程类型
    │  │  courseCategoryList.vue            //课程管理/课程分类  （创建拼音法/鼎英语/鼎英语等类型）
    │  │  courseChildrenList.vue              //课程子类 -  课程管理/课程分类
    │  │  courseList.vue                              //课程管理/课程列表（创建不同课程分类对应的课程）
    │  │  courseListening.vue                   //听力 --- 课程管理/课程列表（创建不同课程分类对应的课程 鼎英语课程 听力课程类型点击制作课程）
    │  │  courseListeningAdd.vue      //听力的新增--- 课程管理/课程列表（创建不同课程分类对应的课程 鼎英语课程 听力课程类型点击制作课程到听力页点击添加按钮）
    │  │  courseListeningUpdate.vue       //听力的修改 -- 课程管理/课程列表（创建不同课程分类对应的课程 鼎英语课程 听力课程类型点击制作课程到听力页点击编辑按钮）
    │  │  courseMake.vue                         //制作课程---课程管理/课程列表（创建不同课程分类对应的课程 鼎英语课程 AMC冲外常用词汇/单词等课程类型点击制作课程）
    │  │  coursePackageList.vue             //课程管理/课程包  （鼎英语的语法/单词/阅读/听力的课程包）
    │  │  courseReading.vue                   //阅读理解 ---课程管理/课程列表（创建不同课程分类对应的课程 鼎英语课程 完形填空程类型点击制作课程）
    │  │  courseReadingDetails.vue    //查看详情----课程管理/课程列表（创建不同课程分类对应的课程 鼎英语课程 完形填空程类型点击制作课程到阅读理解页点击查看详情按钮）
    │  │  courseReadingList.vue           //编辑阅读理解详情/完型填空/增加阅读理解详情/完型填空----课程管理/课程列表（创建不同课程分类对应的课程 鼎英语课程 完形填空程类型点击制作课程到阅读理解页点击添加或编辑按钮）
    │  │  courseReadingMake.vue         //课程管理/阅读理解制作课程   （已经隐藏）
    │  │  dictationWordLevel.vue          //课程管理/听写单词水平列表
    │  │  dictationWordLevelForm.vue           //听写单词水平新增编辑弹窗组件--课程管理/听写单词水平列表
    │  │  dictationWordTest.vue                 //课程管理/听写单词测试题库
    │  │  dictationWordType.vue                //课程管理/听写单词类型列表
    │  │  dictationWordTypeForm.vue    //听写单词类型新增编辑弹窗组件--课程管理/听写单词类型列表 已经隐藏不用
    │  │  listenQuestionDialog.vue           //添加题目 -  //听力题目列表-课程管理/新版听力课程列表（点击制作课程按钮到听力列表页创建听力课程点击制作题目按到听力题目列表新增编辑题目）
    │  │  listenQuestionList.vue                    //听力题目列表-课程管理/新版听力课程列表（点击制作课程按钮到听力列表页创建听力课程点击制作题目按到听力题目列表）
    │  │  newListenCourseList.vue            //课程管理/新版听力课程列表 （kingSchool鼎英语新版听力模块课程题目创建 ）
    │  │  newListenList.vue                          //听力列表-课程管理/新版听力课程列表（点击制作课程按钮）
    │  │  questionDialog.vue                         //添加试题/编辑试题 -  课程管理/超级阅读课程列表 (点击制作文章按钮到文章列表页创建文章点击制作试题按钮到试题列表页点击添加/编辑按钮)
    │  │  superReadCourseList.vue        //课程管理/超级阅读课程列表（kingSchool鼎英语阅读模块课程题目创建 ）
    │  │  testQuestionList.vue                 //试题列表-  课程管理/超级阅读课程列表 (点击制作文章按钮到文章列表页创建文章点击制作试题按钮到试题列表页)
    │  │  textbookVersionList.vue         //课程管理/教材版本列表
    │  │  wordLevelList.vue                        //课程管理/词汇水平列表
    │  │  wordLevelTestdbList.vue               //课程管理/单词水平测试题库
    │  │  
    │  └─courseDictation
    │      │  addDictationWord.vue
    │      │  courseDictationList.vue           //课程管理/自然拼写课程列表
    │      │  
    │      ├─additionQuestion
    │      │      additionCourseList.vue       //课程管理/拼音法课程列表
    │      │      
    │      ├─components
    │      │      additionCourseAddForm.vue   //课程管理/拼音法课程列表 （编辑或者添加弹窗组件）
    │      │      courseAddForm.vue             //课程管理/自然拼写课程列表   （编辑或者添加弹窗组件）
    │      │      experienceStepForm.vue       // 拼读拼写列表（内部组件）
    │      │      hasSpellStepForm.vue          //课程管理/制作课程 （有划拼音-拼读拼写弹窗组件）
    │      │      noSpellStepForm.vue        //拼读拼写列表（内部组件）
    │      │      searchCourse.vue               //课程管理/自然拼写课程列表 （内部搜索组件）
    │      │      StepForm.vue       //课程管理/制作课程（内部组件）
    │      │      table.js
    │      │      
    │      └─makeCourseList
    │              additionSpellList.vue        //课程管理/制作课程 （上传单词组件）
    │              additionuploadAudio.vue         //课程管理/制作课程 （上传音频组件）
    │              drawSyllable.vue                          //课程管理/制作课程 （划音节列表组件）
    │              experienceSpellList.vue              //课程管理/制作课程 （拼读拼写列表组件）
    │              makeAdditionCourseList.vue               //制作课程 - 课程管理/拼音法课程列表 （创建拼音法课程-点击制作课程按钮）
    │              makeCourseDictationList.vue             //制作课程-课程管理/自然拼写课程列表（创建拼写课程每个课程对应 元辅音/音节/划音节/拼读拼写/上传音频）
    │              SpellList.vue           //课程管理/制作课程（拼读拼写列表组件）
    │              syllableList.vue     //课程管理/制作课程（音节列表组件）
    │              uploadAudio.vue     //课程管理/制作课程（上传音频组件）
    │              vowelList.vue       //课程管理/制作课程（元辅音列表组件）
    │              
    ├─course-sys
    │  ├─classRecord
    │  │      index.vue          //没有引用
    │  │      
    │  ├─complain
    │  │      index.vue        //没有引用
    │  │      
    │  ├─coursePrice
    │  │      index.vue     //没有引用
    │  │      
    │  ├─feedback
    │  │      index.vue     //没有引用
    │  │      
    │  ├─performance
    │  │      index.vue            //没有引用
    │  │      
    │  └─scheduleRelation
    │          index.vue                          //没有引用
    │          
    ├─cousys                   //没有引用
    │      checkCourse.vue       //没有引用
    │      commitCourse.vue       //没有引用
    │      cousysBaseInfo.vue       //没有引用
    │      cousysDetailInfo.vue       //没有引用
    │      qrPayCode.vue       //没有引用
    │      reStatusCourse.vue       //没有引用
    │      
    ├─cousysOrder
    │      cousysOrder.vue       //没有引用
    │      
    ├─cousysParent
    │      parentCentre.vue      //没有引用
    │      
    ├─cousysPermission
    │      permissionList.vue        //没有引用
    │      
    ├─dashboard
    │  │  index.vue
    │  │  
    │  ├─admin
    │  │  │  index.vue
    │  │  │  
    │  │  └─components
    │  │      │  BarChart.vue                 /views/dashboard/admin/index  (内部组件)
    │  │      │  BoxCard.vue              /views/dashboard/admin/index  (内部组件)
    │  │      │  experienceChart.vue   //学员管理/学员试课详情 （内部组件）
    │  │      │  experienceChart1.vue           //学员管理/学员试课详情 （内部组件）
    │  │      │  LineChart.vue             /views/dashboard/admin/index  (内部组件)
    │  │      │  PanelGroup.vue          /views/dashboard/admin/index  (内部组件)
    │  │      │  PieChart.vue            /views/dashboard/admin/index  (内部组件)
    │  │      │  RaddarChart.vue                /views/dashboard/admin/index  (内部组件)
    │  │      │  TransactionTable.vue               /views/dashboard/admin/index  (内部组件)
    │  │      │  
    │  │      ├─mixins
    │  │      │      resize.js
    │  │      │      
    │  │      └─TodoList
    │  │              index.scss
    │  │              index.vue      //没有引用
    │  │              Todo.vue     //没有引用
    │  │              
    │  └─editor
    │      │  index.vue     //没有引用
    │      │  IsLogo.vue    /views/dashboard/editor/index (内部组件)
    │      │  
    │      └─form
    │              admissionsData.vue   //我的首页/我的首页 （内部组件）
    │              classFlowData.vue   //我的首页/我的首页 （内部组件）
    │              InvestmentData.vue   //我的首页/我的首页 （内部组件）
    │              investmentFlow.vue   //我的首页/我的首页 （内部组件）
    │              
    ├─distributor
    │      distributor.vue    没有引用
    │      distributorFlow.vue   //查看流水
    │      
    ├─dxt
    │  ├─feedback             //会议管理/反馈意见列表
    │  │      feedBackList.vue
    │  │      
    │  ├─flow       //会员管理/会议流水
    │  │      flow.vue
    │  │      
    │  ├─meeting   //会议管理/会议列表
    │  │      meeting.vue
    │  │      
    │  ├─meetingcode               //会议管理/物料码列表
    │  │      meetingCode.vue
    │  │      
    │  ├─meetinginvitation       //会议管理/邀请语列表
    │  │      meetingInvitationList.vue
    │  │      
    │  ├─meetingorder        //会议管理/会议订单
    │  │      meetingOrder.vue
    │  │      
    │  ├─presenter       //会议管理/主讲老师列表
    │  │      presenterList.vue
    │  │      
    │  └─referrer               //会议管理/推荐会议人员
    │          referreJoinMeetingList.vue
    │          
    ├─dzy
    │  ├─applicants
    │  │      expertsApplicantsAdd.vue      //产品列表/高报师新增编辑列表 （菜单隐藏）
    │  │      expertsApplicantsList.vue      //产品列表/高报师列表 （菜单隐藏）
    │  │      expertsApplicantsPerson.vue      //产品列表/高报师个人列表 （菜单隐藏）
    │  │      expertsArrearsList.vue              //产品列表/欠费列表 （菜单隐藏）
    │  │      expertsFillList.vue              //产品列表/专家填报 （菜单隐藏）
    │  │      
    │  └─card
    │          CanLearnMeeting.vue         //产品列表/学能师课程 （菜单隐藏）
    │          card.vue                    //产品列表/志愿卡列表 （菜单隐藏）
    │          cardType.vue           //产品列表/志愿卡类型列表 （菜单隐藏）
    │          ExpertsFillMeeting.vue         //产品列表/高报师课程 （菜单隐藏）
    │          
    ├─error-log
    │  │  index.vue
    │  │  
    │  └─components    /ErrorLog
    │          ErrorTestA.vue         //ErrorLog（内部组件）
    │          ErrorTestB.vue           //ErrorLog（内部组件）
    │          
    ├─error-page
    │  │  401.vue
    │  │  404.vue
    │  │  
    │  └─vendor
    │          Export2Excel.js
    │          Export2Zip.js
    │          
    ├─finance
    │      assistantFlowList.vue           //财务列表/学员课程变动明细
    │      coursePackageProfit.vue      //财务列表/收益
    │      financeInforM.vue                   //财务列表/门店交付订单
    │      investmentRecruitmentDetailsList.vue         //财务列表/招商招生明细
    │      marketProfiAccountFlows.vue            //财务列表/推荐人分润流水
    │      memberFundStatement.vue                 //财务列表/会员资金流水
    │      merchantFlowCourseList.vue             //财务列表/商户资金学时流水
    │      merchantFlowExcelList.vue        //没有引用
    │      merchantFlowList.vue                //财务列表/商户资金流水
    │      merchantFlowListDealer.vue       //财务列表/托管中心充值资金流水
    │      offsiteDepositOrder.vue            //财务列表/异地定金订单管理
    │      onlineCharge.vue                      //财务列表/在线扣款
    │      onlineCompanyCharge.vue    //财务列表/分公司在线扣款
    │      onlineCompanyRecharge.vue        //财务列表/分公司在线充值
    │      onlineRecharge.vue                   //财务列表/在线充值
    │      pendingPayList.vue                    //财务列表/资转待支付列表
    │      periodValList.vue                   //财务管理  （没有引用）
    │      schoolFlowList.vue           //财务列表/门店学时变动明细
    │      schoolFlowPackageList.vue         //财务列表/课程包变动明细
    │      
    ├─forgot
    │      index.vue  //修改密码页面
    │      
    ├─gift
    │      dealer.vue    //没有引用
    │      experienceCode.vue    //没有引用
    │      
    ├─grammar  // 语法管理 - kingSchool老版本语法上课学习内容配置模块
    │      grammarConfig.vue    //语法管理/语法点配置
    │      grammarConsumeHours.vue   //语法管理/语法包消费学时配置
    │      grammarMessage.vue    //语法管理/学员寄语列表
    │      grammarPoint.vue   //语法管理/语法点管理
    │      grammarPwdConfig.vue         //语法管理/教练 授权码配置
    │      grammarQuestion.vue   //语法管理/题库管理
    │      grammarStudent.vue          //语法管理/开通语法学员列表
    │      handoutsPrintList.vue    //语法管理/学员课件打印列表
    │      knowledgePoint.vue   //语法管理/知识点管理
    │      phaseCreadentialPrint.vue   //语法管理/学员结业证书打印列表
    │      phaseCredentialList.vue        //语法管理/学员结业证书打印列表
    │      questionConfig.vue               //语法管理/题目配置
    │      studentHandoutsPrint.vue    //语法管理/学员课件打印列表-打印
    │      studyRateConfig.vue     //语法管理/语法掌握度配置
    │      
    ├─interest
    │      levelConfig.vue  //趣味复习/关数配置
    │      scoreConfig.vue      //趣味复习/等级配置
    │      
    ├─layout
    │  │  Layout.vue
    │  │  
    │  ├─components
    │  │  │  AppMain.vue
    │  │  │  index.js
    │  │  │  Navbar.vue
    │  │  │  TagsView.vue
    │  │  │  
    │  │  └─Sidebar
    │  │          FixiOSBug.js
    │  │          index.vue      //layout/compponents/index.js引用
    │  │          Item.vue
    │  │          Link.vue
    │  │          Logo.vue
    │  │          SidebarItem.vue
    │  │          
    │  └─mixin
    │          ResizeHandler.js
    │          
    ├─learnTube
    │      learnTubeList.vue   //学管师管理/学管师列表
    │      
    ├─login
    │  │  auth-redirect.vue   //鼎校渠道系统登录页面
    │  │  auto_login.vue  //UIAuto-渠道后台(仅内部使用)登录页面
    │  │  index.vue   //登录页面
    │  │  
    │  └─components
    │          SocialSignin.vue    //没有引用
    │          
    ├─marketdd
    │      marketList.vue   //推荐管理/推荐人列表
    │      
    ├─markets
    │      marketListRank.vue  //推荐关系管理/推荐关系管理
    │      marketProfitAccountList.vue   //推荐关系管理/推荐人分润流水
    │      marketProfitAmount.vue       //推荐关系管理/推荐人列表 （隐藏不用了）
    │      marketProfitRankFlows.vue      //推荐关系管理/分润流水
    │      merchantAccountFlows.vue        //推荐关系管理/资金流水
    │      
    ├─member
    │      allInOneMember.vue         //会员管理/通联会员
    │      memberList.vue     //会员管理/会员列表
    │      
    ├─merchantManagement
    │      agentAddList.vue  //   商户管理 /市级服务商新增
    │      agentList.vue  //   商户管理/市级服务商列表
    │      Amap.vue   //没有引用 地图
    │      areaAgent.vue  // 商户管理/区县服务商列表
    │      areaAgentAddList.vue    // 商户管理 /区县服务商新增
    │      branchOfficeAdd.vue // 商户管理/分公司新增
    │      branchOfficeList.vue  // 商户管理/分公司列表
    │      dealerAdd copy 2.vue   //没有引用
    │      dealerAdd copy.vue    //没有引用
    │      dealerAdd.vue       // 商户管理/ 添加/修改托管中心
    │      dealerList.vue      // 商户管理/托管中心列表
    │      deliveryCenterAdd.vue    // 商户管理/交付中心新增
    │      deliveryCenterList.vue  // 商户管理/交付中心列表
    │      divisionAdd.vue  // 商户管理/事业部新增
    │      divisionList.vue  // 商户管理/事业部列表
    │      experienceAdd.vue       // 商户管理/添加/修改体验中心
    │      experienceList.vue    // 商户管理/体验中心列表
    │      marketList.vue  // 商户管理/推荐人列表  已经隐藏不用
    │      operationAdd.vue   //没有引用
    │      operationsAdd.vue      // 商户管理添加/修改超级俱乐部
    │      operationsList.vue    // 商户管理/超级俱乐部列表
    │      promoterAdd.vue    //商户管理/推广人新增  已经隐藏不用
    │      promoterList.vue     //商户管理/推广人列表  已经隐藏不用
    │      schoolCheck.vue          //商户管理/门店审核
    │      schoolEdit.vue          //商户管理/门店编辑
    │      schoolList.vue  // 商户管理/门店列表
    │      
    ├─mps
    │      refund.vue    //mps订单退款/mps订单退款
    │      
    ├─news
    │      list.vue     //消息管理/消息通知
    │      newContent.vue  //消息管理/通知内容
    │      newsAdd.vue  //消息管理/消息新增
    │      newsEdit.vue  //消息管理/编辑消息
    │      sendNews.vue  //消息管理/消息管理
    │      
    ├─order
    │      CoursependingPaymentList.vue  //财务列表/课程等待付款列表
    │      ledgerOrderList.vue
    │      memberRechargeOrder.vue   //订单管理/会员充值订单
    │      order.vue                   //没有引用
    │      rechargeCourseIndex.vue              //财务列表/课程购买列表 （渠道门店购买课程记录）
    │      refundCourseIndex.vue                   //财务列表/退课列表    
    │      refundCourseIndexa.vue                     //财务列表/门店退课列表
    │      __VLS_template.js
    │      
    ├─paper
    │  ├─abilityReport
    │  │      index.vue    //试卷管理/报告管理
    │  │      save.vue  //试卷管理/报告新增/修改
    │  │      
    │  ├─complex
    │  │  │  audio-index.vue     //试卷管理/音频复杂题  （菜单隐藏）
    │  │  │  card-index.vue         //试卷管理/答题卡复杂题  （菜单隐藏）
    │  │  │  img-text-index.vue   //试卷管理/图文复杂题  （菜单隐藏）
    │  │  │  
    │  │  ├─audio
    │  │  │      audio-listen-trans.vue    //试卷管理/音频听识题  （菜单隐藏）
    │  │  │      audio-listen.vue        //试卷管理/音频听译题  （菜单隐藏）
    │  │  │      audio-repeat.vue     //试卷管理/音频找重题  （菜单隐藏）
    │  │  │      audio-spell.vue          //试卷管理/音频拼组题  （菜单隐藏）
    │  │  │      
    │  │  ├─card
    │  │  │      card.vue       //试卷管理/答题卡题  （菜单隐藏）
    │  │  │      
    │  │  └─img-text
    │  │          img-repeat.bak.vue //没有引用
    │  │          img-text-drag.vue   //试卷管理/图文拖到题 （菜单隐藏）
    │  │          img-text-filling.vue      //试卷管理/图文填空题（菜单隐藏）
    │  │          img-text-mark-number.vue   //试卷管理/图文连续序题（菜单隐藏）
    │  │          img-text-mark-repeat.vue     //试卷管理/图文标记找重题（菜单隐藏）
    │  │          img-text-repeat.vue         //试卷管理/图文找重题（菜单隐藏）
    │  │          img-text-serial-calculate.vue     //试卷管理/图文连续计算题（菜单隐藏）
    │  │          img-text-serial-mark-number.vue       //试卷管理/图文标序题（菜单隐藏）
    │  │          
    │  ├─course
    │  │      index.vue    //试卷管理/课程管理
    │  │      question.vue     //试卷管理/问题列表
    │  │      
    │  ├─grade
    │  │      index.vue   //试卷管理/学段管理
    │  │      
    │  ├─knowledge
    │  │      index.vue   //试卷管理/知识点管理
    │  │      
    │  ├─paper
    │  │      edit.vue   //试卷管理/试卷编辑（菜单隐藏）
    │  │      index.vue     //试卷管理/试卷列表（菜单隐藏）
    │  │      random.vue   //试卷管理/智能组卷（菜单隐藏）
    │  │      view.vue          //试卷管理/试卷查看（菜单隐藏）
    │  │      
    │  ├─question
    │  │  │  index.vue           //试卷管理/评估题目
    │  │  │  
    │  │  ├─components
    │  │  │      question-add.vue     //没有引用
    │  │  │      Show.vue             //试卷分数组件
    │  │  │      
    │  │  └─edit
    │  │          article-memory.vue       // 试卷管理/物品记忆题        试卷管理/评估题目（点击添加按钮）
    │  │          audio-fill.vue           // 试卷管理/音频填空题       试卷管理/评估题目（点击添加按钮）
    │  │          card.vue               // 试卷管理/答题卡题       试卷管理/评估题目（点击添加按钮）
    │  │          clue.vue            // 试卷管理/线索题       试卷管理/评估题目（点击添加按钮）
    │  │          combination.vue    // 试卷管理/组合题编辑           试卷管理/评估题目（点击添加按钮）
    │  │          gap-filling.vue       // 试卷管理/填空题编辑           试卷管理/评估题目（点击添加按钮）
    │  │          grid.vue         // 试卷管理/跳格子题       试卷管理/评估题目（点击添加按钮）
    │  │          icon-move.vue         // 试卷管理/硬币移动题        试卷管理/评估题目（点击添加按钮）
    │  │          image-answer.vue       // 试卷管理/图片问答题        试卷管理/评估题目（点击添加按钮）
    │  │          listen-answer.vue       // 试卷管理/听力问答题        试卷管理/评估题目（点击添加按钮）
    │  │          match-move.vue            // 试卷管理/火柴移动题         试卷管理/评估题目（点击添加按钮）
    │  │          multiple-choice.vue      // 试卷管理/多选题编辑           试卷管理/评估题目（点击添加按钮）
    │  │          short-answer.vue         // 试卷管理/简答题编辑           试卷管理/评估题目（点击添加按钮）
    │  │          single-choice.vue    // 试卷管理/单选题编辑           试卷管理/评估题目（点击添加按钮）
    │  │          str-memory.vue           // 试卷管理/字符串记忆题           试卷管理/评估题目（点击添加按钮）
    │  │          text-repeat.vue            // 试卷管理/文字找重题       试卷管理/评估题目（点击添加按钮）
    │  │          true-false.vue       // 试卷管理/判断题编辑           试卷管理/评估题目（点击添加按钮）
    │  │          word-memory.vue          // 试卷管理/词语记忆题           试卷管理/评估题目（点击添加按钮）
    │  │          
    │  ├─report
    │  │      index.vue          //试卷管理/结果报告  （菜单隐藏）
    │  │      knowledge-report.vue   //试卷管理/知识点型
    │  │      report-template.vue   //试卷管理/知识点型（内部组件）
    │  │      score-report.vue          //试卷管理/分数型（菜单隐藏）
    │  │      
    │  ├─subject
    │  │      index.vue    //试卷管理/维度管理
    │  │      
    │  ├─train
    │  │  ├─category
    │  │  │      index.vue  //训练管理/维度管理
    │  │  │      
    │  │  ├─course
    │  │  │      index.vue   //训练管理/课程管理
    │  │  │      pass.vue   //训练管理/关卡管理
    │  │  │      question.vue  //训练管理/关卡问题
    │  │  │      
    │  │  ├─difficulty
    │  │  │      index.vue   //训练管理/难度管理
    │  │  │      
    │  │  ├─guide
    │  │  │      data.vue       //训练管理/引导问答设置
    │  │  │      index.vue     //训练管理/引导管理
    │  │  │      
    │  │  ├─image
    │  │  │      index.vue   //训练管理/题库图片
    │  │  │      
    │  │  ├─learnmaster
    │  │  │      index.vue   //训练管理/学能师管理
    │  │  │      students.vue    //训练管理/学员管理
    │  │  │      
    │  │  └─question
    │  │      ├─auditory
    │  │      │      answer.vue           //训练管理/听力回答题
    │  │      │      audio_filling.vue    //训练管理/音频文本填空题
    │  │      │      distinguish.vue     //训练管理/听力辨别题
    │  │      │      filling.vue     //训练管理/听力填空题
    │  │      │      follow.vue     //训练管理/跟读题
    │  │      │      handle.vue     //训练管理/听力动手题
    │  │      │      index.vue         //训练管理/听力题
    │  │      │      leak.vue           //训练管理/听力漏读题
    │  │      │      repeat.vue     //训练管理/找重题
    │  │      │      
    │  │      ├─stare
    │  │      │      fingerCalc.vue    //训练管理/手指计算题
    │  │      │      index.vue            //训练管理/课外练习
    │  │      │      miniGame.vue   //训练管理/课前小游戏
    │  │      │      point.vue          //训练管理/盯点练习
    │  │      │      video.vue          //训练管理/课前视频
    │  │      │      
    │  │      ├─upload
    │  │      │      copy.vue    //训练管理/内容抄写题
    │  │      │      drawing.vue    //训练管理/看图画画题
    │  │      │      index.vue    //训练管理/拍照上传题
    │  │      │      
    │  │      └─visual
    │  │              anti_interference.vue    //训练管理/视觉转移题
    │  │              choice.vue              //训练管理/图片找不同题
    │  │              different.vue            //训练管理/数字找不同题
    │  │              distinguish.vue          //训练管理/视觉分辨题
    │  │              featureGraphics.vue    //训练管理/特征找图形题
    │  │              fillLeak.vue                  //训练管理/填漏题
    │  │              findWord.vue          //训练管理/圈数字题
    │  │              graphicMatching.vue   //训练管理/图形匹配题
    │  │              index.vue                   //训练管理/视觉题
    │  │              leak.vue                          //训练管理/方格正序题
    │  │              numMemory.vue            //训练管理/数字记忆题
    │  │              pattern.vue               //训练管理/找图形个数题
    │  │              picMemory.vue        //训练管理/图片记忆题
    │  │              pictureGrid.vue          //训练管理/图片方格题
    │  │              read.vue                      //训练管理/朗读题
    │  │              step.vue                    //训练管理/题型步骤方法
    │  │              textRepeat.vue        //训练管理/文字找重题
    │  │              wordMemory.vue            //训练管理/单词记忆题
    │  │              
    │  └─train-after-class
    │          question-bank.vue    //训练管理/一课一练课程
    │          
    ├─profitaccount
    │      bvProfitAccountList.vue       //没有引用
    │      marketProfitAccount.vue        //分润账户管理/分润账户管理
    │      profitAccountList.vue                 //分润账户管理/分润账户
    │      profitWithdrawRecoder.vue     //分润账户管理/提现列表
    │      
    ├─redirect
    │      index.vue     （菜单隐藏）
    │      
    ├─risk
    │      index.vue           //监管体系/低危列表-中危列表-高危列表
    │      riskDispose.vue// 风控处理组件 - 监管体系/低危列表-中危列表-高危列表
    │      riskWhiteList.vue         //监管体系/ 风控白名单
    │      
    ├─student
    │      classCard.vue            //学员管理/集中交付学员  （内部组件）
    │      deliverClassFlowList.vue        //学员管理/交付学时充值流水
    │      jiaofufangshi.vue   //学员管理/集中交付学员
    │      reviewSchedule.vue  //学员管理/待完善复习时间表  (	已经隐藏)
    │      shikejilu.vue              //学员管理/咨询记录表
    │      studentCourseProgress.vue   //学员管理/学员课程进度
    │      studentCourseRecord.vue  //学员管理/学员课程记录
    │      studentDayPrint.vue       //学员管理/学员测验打印 （点击打印当天所有按钮打印）
    │      studentExperience.vue   //学员管理/学员试课详情
    │      studentExperienceEdit.vue   //学员管理/学员试课报告编辑
    │      studentForget.vue   //学员管理/21天抗遗忘报告打印详情
    │      studentGraduation.vue  //学员管理/学员测验打印  -（点击结业单词按钮打印）
    │      studentList.vue           //学员管理/学员列表 （学员信息）
    │      studentMarketingCourseRecord.vue   //学员销课记录   (已经隐藏 )
    │      studentTestPrint.vue   //学员管理/学员测验打印（单词课程类型打印）
    │      studentTestPrintReading.vue  //学员管理/学员测验打印-（阅读理解课程类型打印）
    │      studentTestPrintReport.vue //学员管理/学员测验打印（结业报告课程类型打印）
    │      studentWordReviewList.vue   //学员管理/21天抗遗忘复习计划（点击21天抗遗忘记录按钮）
    │      studentWordReviewPrint.vue  //学员管理/21天抗遗忘复习计划（点击打印21天抗遗忘按钮）
    │      studentWordsTest.vue    //学员管理/学员词汇测试  （点击打印按钮）
    │      studentWordViewList.vue      //学员管理/21天抗遗忘复习计划（点击21天抗遗忘记录按钮到21天抗遗忘页面点击查看详情按钮）
    │      teamAuditing.vue          //学员管理/更换交付小组审核
    │      trialClassOrderList.vue   //学员管理/待完善上课信息表  （学员第一次购买后填写课程规划和上课时间）
    │      trialClassOrderLista.vue    //没有引用
    │      trialClassOrderListb.vue   //没有引用
    │      viewProgress.vue             //没有引用
    │      viewStudentCourse.vue        //没有引用
    │      wordBookView.vue         //学员管理/单词本查看
    │      xueyuaninfoForm.vue    //没有内容 已经删除
    │      xueyuanxinxi.vue           //学员管理/学员信息
    │      
    ├─studyroom
    │  │  fixTrtcRoom.vue   //自习室管理/自习室管理
    │  │  selfstudyRoom.vue   //自习室管理/自习室管理 （没有使用-非固定）
    │  │  trtcRoom.vue   //自习室管理/自习室管理（没有使用-非固定）
    │  │  trtcRoom_1.vue  //自习室管理/自习室管理（没有使用-非固定）
    │  │  
    │  ├─committeeman
    │  │      committeemanAdd.vue    //新增学委组件   --自习室管理/学委列表
    │  │      committeemanList.vue        //自习室管理/学委列表
    │  │      committeemanView.vue  //查看详情组件   --自习室管理/学委列表
    │  │      roomList.vue             //指派自习室组件   --自习室管理/学委列表
    │  │      
    │  ├─feedback
    │  │      index.vue   //自习室意见反馈/自习室意见反馈
    │  │      
    │  ├─room
    │  │      list.vue      //自习室管理/房间列表
    │  │      list_bak.vue       //没有引用
    │  │      
    │  ├─scene
    │  │      sceneInfo.vue    //自习室管理/场景列表
    │  │      
    │  ├─schedule
    │  │      fixSchedule.vue       （没有引用）
    │  │      gradeAdd.vue           // 编辑页面组件  -    自习室管理/规划列表
    │  │      gradeList.vue            //自习室管理/规划列表
    │  │      scheduleRoomView.vue         //自习室管理/规划房间详情
    │  │      studentList.vue      //自习室管理/人员列表
    │  │      targetList.vue              //自习室管理/自学目标
    │  │      unFixSchedule.vue   //自习室管理/非固定共享班
    │  │      unFixScheduleRoomView.vue        //自习室管理/非固定班房间详情
    │  │      
    │  ├─scheduleplan
    │  │      planList.vue      //自习室管理/计划列表
    │  │      
    │  ├─spoken-test
    │  │      index.vue              //自习室管理/口语评测
    │  │      type.vue                 //自习室管理/口语类型
    │  │      
    │  ├─studycommitteed
    │  │      index.vue                //自习室管理/学委管理
    │  │      
    │  ├─timer
    │  │      TimerList.vue           //自习室管理/计算器列表
    │  │      
    │  └─voice-answers
    │          content.vue           //自习室管理/回答内容
    │          index.vue              //自习室管理/语音问答
    │          
    ├─suggest
    │      suggest.vue           //用户意见反馈/用户意见反馈
    │      
    ├─syntax
    │  │  grammarPoint.vue    //新版语法管理/语法点管理
    │  │  grammarQuestion.vue  //新版语法管理/知识点题库
    │  │  grammarQuestionGra.vue    //新版语法管理/语法点和结业检测题库
    │  │  handoutsPrintList.vue         //新版语法管理/学员课件打印列表
    │  │  knowledgePoint.vue       //新版语法管理/知识点管理
    │  │  phaseCreadentialPrint.vue   //新版语法管理/学员结业证书打印列表-打印
    │  │  phaseCredentialList.vue      //新版语法管理/学员结业证书打印列表
    │  │  questionConfig.vue        //新版语法管理/题目配置
    │  │  studentHandoutsPrint.vue   //新版语法管理/学员课件打印列表-打印
    │  │  studyRateConfig.vue               //新版语法管理/语法掌握度配置
    │  │  
    │  └─component
    │          xmindVue.vue //知识点管理内的新增思维导图组件--新版语法管理/知识点管理
    │          
    ├─system
    │  │  appVersionControl.vue      //系统管理/App版本控制
    │  │  authentication.vue              //系统管理/实名认证 
    │  │  authorizationCodeExport.vue               //系统管理/授权码列表
    │  │  businessDistrictCode.vue                     //系统管理/商圈码列表
    │  │  changePassword.vue                             //和修改密码页相似没有引用
    │  │  cityCourseConfiguration.vue                //系统管理/学时价格配置 （学时单价设置）
    │  │  dataDictionary.vue                          //没有引用
    │  │  deliverConfig.vue                       //系统管理/会员交付方式
    │  │  deliverSetting.vue                    //系统管理/交付中心配置
    │  │  divisionProfitRankList.vue                 //系统管理/事业部推荐反润比例列表
    │  │  divisionRankList.vue                //系统管理/事业部级别列表
    │  │  experienceLevelList.vue               //系统管理/托管中心级别列表
    │  │  functionList.vue                             //没有引用 功能没有完成
    │  │  link.vue                                                //系统管理/通联云链接
    │  │  managersList.vue                          //没有引用
    │  │  merProfitConfig.vue                   //系统管理/渠道分润比例配置
    │  │  messageList.vue                           //没有引用
    │  │  profitRank.vue                               // 直充分润等级 - 没有引用
    │  │  rebateRecordlList.vue                //给指定商户充值 - 没有引用
    │  │  relieveAuthentication copy.vue           //复制 - 系统管理/实名认证解绑
    │  │  relieveAuthentication.vue                  //系统管理/实名认证解绑
    │  │  reProfitRank.vue                              //续充分润等级 - 没有引用
    │  │  riskAuthCodeList.vue           //系统管理/风控授权码列表
    │  │  roleList.vue                                //用户角色 -  没有引用
    │  │  systemConfiguration.vue  // 系统管理/系统配置
    │  │  tree.js                                      //和_system内容一样已删除
    │  │  updatePssword.vue               //系统管理/修改密码 （登录密码/交易密码）
    │  │  userOptionLog.vue                //系统管理/用户操作日志
    │  │  
    │  ├─perm             //和_system内容一样已删除
    │  │      index.vue
    │  │      
    │  ├─role            //和_system内容一样已删除
    │  │      assign_perm.vue
    │  │      index.vue
    │  │      
    │  └─user               //和_system内容一样已删除
    │          index.vue
    │          
    ├─training
    │  ├─course
    │  │  │  course.vue        //培训管理/课程管理（菜单隐藏）
    │  │  │  courseType.vue           //培训管理/课程分类（菜单隐藏）
    │  │  │  feedback.vue         //培训管理/课程反馈（菜单隐藏）
    │  │  │  
    │  │  └─components
    │  │          addCourse.vue        //培训管理/课程管理（内部组件）
    │  │          addCourseSettings.vue        //培训管理/课程管理（内部组件）
    │  │          addCourseType.vue          //培训管理/课程分类（内部组件）
    │  │          addFeedback.vue          //培训管理/课程反馈（内部组件）
    │  │          selectCourse.vue      //培训管理/课程分类（内部组件）
    │  │          uploadLessonFile.vue      //没有引用
    │  │          
    │  ├─exam
    │  │  │  examManagement.vue                  //培训管理/考题管理（菜单隐藏）
    │  │  │  testPaperManagement.vue            //培训管理/试卷管理（菜单隐藏）
    │  │  │  
    │  │  └─components
    │  │          addExamManagement.vue     //培训管理/考题管理 （内部添加弹窗组件 ）
    │  │          addTestPaperManagement.vue        //培训管理/试卷管理（内部添加试卷）
    │  │          batchAddManagement.vue     //培训管理/考题管理 （内部批量添加弹窗组件 ）
    │  │          viewTestPaper.vue   //培训管理/试卷管理（内部查看试卷详情组件）
    │  │          
    │  ├─home
    │  │      home.vue          //培训管理/首页（菜单隐藏）
    │  │      
    │  └─traineeStatistics
    │      │  traineeStatistics.vue          //培训管理/学员统计（菜单隐藏）
    │      │  
    │      └─components
    │              traineeDetail.vue  //培训管理/学员统计（内部组件）
    │              
    ├─trainingCenter
    │  ├─courseCenter
    │  │  │  courseCenter.vue      //学习中心/课程中心
    │  │  │  
    │  │  └─components
    │  │          courseDetail.vue    //学习中心/课程详情
    │  │          playVideo.vue      //学习中心/课程学习
    │  │          testPaper.vue    //学习中心/课程详情（内部组件）
    │  │          
    │  └─personalCenter
    │      │  examRecords.vue   //考试记录
    │      │  personalCenter.vue    //学习中心/个人中心
    │      │  
    │      └─components
    │              paperDetail.vue       //考试记录（内部试卷详情组件）
    │              
    ├─tutor
    │      cousysClock.vue      //没有引用
    │      tutorCentre.vue      //没有引用
    │      
    ├─xi
    │  ├─bigGrading
    │  │      index.vue     //习功能管理/大段位管理
    │  │      
    │  ├─classify
    │  │      index.vue         //习功能管理/分类管理
    │  │      type.vue
    │  │      
    │  ├─commondict
    │  │      index.vue              //习功能管理/提醒文本
    │  │      
    │  ├─community
    │  │      banner_run.vue   //社区管理/banner管理
    │  │      official.vue            //社区管理/官方推荐
    │  │      plate.vue            //社区管理/板块管理
    │  │      post.vue               //社区管理/帖子管理
    │  │      posterbackground.vue
    │  │      punishconfig.vue               //习功能管理/休息超时惩罚配置
    │  │      studentInfo.vue      //社区管理/用户管理
    │  │      systemavatar.vue            //社区管理/系统头像
    │  │      taskawrad.vue            //社区管理/任务奖励配置
    │  │      
    │  ├─exchangerecord
    │  │      index.vue            //习功能管理/兑换记录
    │  │      
    │  ├─extradate
    │  │      index.vue
    │  │      student.vue        //习功能管理/学员作息日历
    │  │      
    │  ├─getmethod
    │  │      index.vue              //习功能管理/如何获取经验值  （菜单隐藏）  
    │  │      
    │  ├─gift
    │  │      index.vue               //习功能管理/礼包设置
    │  │      
    │  ├─goods
    │  │      index.vue    //习功能管理/商品管理
    │  │      
    │  ├─grading
    │  │      index.vue  //习功能管理/段位管理
    │  │      
    │  ├─guide
    │  │      index.vue          //习功能管理/新手引导
    │  │      
    │  ├─incentives
    │  │      index.vue            //习功能管理/激励语文本
    │  │      
    │  ├─level
    │  │      index.vue    //习功能管理/等级管理
    │  │      
    │  ├─manage
    │  │      index.vue                //习功能管理/学管师学员
    │  │      student.vue               //习功能管理/添加学管师学员
    │  │      
    │  ├─parentsSysMessage
    │  │      index.vue                   //习功能管理/后台管理家长系统消息
    │  │      
    │  ├─pendant
    │  │      index.vue          //习功能管理/挂件配置
    │  │      
    │  ├─ratioconfig
    │  │      index.vue            //习功能管理/专注时长奖励配置
    │  │      
    │  ├─remarkContent
    │  │      index.vue                       //习功能管理/备注语管理
    │  │      
    │  ├─report
    │  │      index.vue           //习功能管理/举报列表配置
    │  │      
    │  ├─reward
    │  │      index.vue        //习功能管理/奖励管理
    │  │      
    │  ├─scoreContent
    │  │      index.vue                //习功能管理/点评语管理
    │  │      
    │  ├─show
    │  │      index.vue          //习功能管理/标准作息日历
    │  │      
    │  ├─subject
    │  │      index.vue       //习功能管理/学科管理
    │  │      
    │  ├─sysUser
    │  │      index.vue                //习功能管理/用户管理
    │  │      
    │  ├─target
    │  │      index.vue                 //没有引用
    │  │      
    │  ├─task
    │  │      index.vue      //习功能管理/任务管理
    │  │      
    │  └─tree
    │          index.vue       //习功能管理/种树管理
    │          
    └─_system
        │  tree.js //菜单数据
        │  
        ├─perm  //权限管理  （菜单权限由页面路由定义，不提供任何编辑功能）
        │      index.vue
        │      
        ├─role  //角色管理  新增用户角色和用户角色菜单权限
        │      assign_perm.vue
        │      index.vue
        │      
        └─user   //用户管理  （登录用户和密码；用户角色）
                index.vue
                
