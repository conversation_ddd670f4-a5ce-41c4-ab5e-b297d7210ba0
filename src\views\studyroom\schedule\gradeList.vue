<template>
  <div class="app-container">
    <el-form :inline="true" ref="queryForm" class="container-card" label-width="110px">
      <el-form-item label="名称：" prop="className">
        <el-input v-model="dataQuery.className" placeholder="请输入名称" clearable />
      </el-form-item>
      <el-form-item label="分类：" prop="courseType">
        <el-select v-model="dataQuery.courseType" placeholder="全部" clearable>
          <el-option v-for="(item, index) in [{ label: '系统课', value: '1' }, { label: '非系统课', value: '2' }]" :key="index"
            :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="学段：" prop="level">
        <el-select v-model="dataQuery.level" placeholder="全部" clearable>
          <el-option v-for="(item, index) in [{ label: '小学', value: '1' }, { label: '初中', value: '2' }, { label: '高中', value: '3' }]"
            :key="index" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="地区：" prop="address" v-if="addressBtn">
        <el-cascader :options="addressOptions" v-model="address" filterable></el-cascader>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handlePage()">搜索</el-button>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-re" @click="resetQuery()">重置</el-button>
      </el-form-item>

    </el-form>
    <el-col :span="24" style="margin-bottom: 30px">
      <el-button type="success" icon="el-icon-plus" size="mini" @click="handleAdd()" v-if="btn">新增</el-button>
    </el-col>

    <el-table class="common-table" v-loading="tableLoading" :data="tableData" style="width: 100%;margin-bottom: 20px;"
      row-key="id" border default-expand-all>
      <el-table-column prop="id" label="ID"></el-table-column>
      <el-table-column prop="className" label="规划名称"></el-table-column>
      <el-table-column label="操作" v-if="btn">
        <template slot-scope="scope">
          <el-button type="text" size="mini">
            <router-link :to="'/schedule/scheduleRoomView/' + scope.row.id">查看详情</router-link>
          </el-button>
          <el-button type="text" size="mini" @click="handleEdit(scope.row)" v-if="btn">编辑</el-button>
          <el-button type="text" size="mini" @click="changeStatus(scope.row)" v-if="scope.row.status && btn"
            style="color: red">停用
          </el-button>
          <el-button type="text" size="mini" @click="changeStatus(scope.row)" v-if="!scope.row.status && btn">启用
          </el-button>
        </template>
      </el-table-column>
      <el-table-column prop="courseType" label="课程分类">
        <template slot-scope="scope">
          <span v-if="scope.row.courseType == 1">系统课</span>
          <span v-else>非系统课</span>
        </template>
      </el-table-column>
      <el-table-column prop="tranWeeks" label="日期"></el-table-column>
      <el-table-column prop="times" label="时间"></el-table-column>
      <el-table-column prop="roomNum" label="自学室数量"></el-table-column>
      <el-table-column prop="level" label="学段">
        <template slot-scope="scope">
          <span v-if="scope.row.level === '1'">小学</span>
          <span v-if="scope.row.level === '2'">初中</span>
          <span v-if="scope.row.level === '3'">高中</span>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间"></el-table-column>
      <el-table-column prop="address" label="地区" v-if="addressBtn">
        <template slot-scope="scope">
          {{ scope.row.province }}-{{ scope.row.city }}
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <el-col :span="24" style="margin-bottom: 20px">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>

    <grade-add ref="add" @ok="getPageList" />
  </div>
</template>

<script>
import studyScheduleApi from "@/api/studyroom/studySchedule";
import { pageParamNames } from "@/utils/constants";
import gradeAdd from "./gradeAdd";
import ls from '@/api/sessionStorage'
import committeemanApi from "@/api/studyroom/committeeman";

export default {
  name: 'gradeList',
  components: { gradeAdd },
  data() {
    return {
      props: {
        lazy: true,
        lazyLoad(node, resolve) {
          const { level, data } = node;
          if (level >= 2) {
            let nodes = [];
            committeemanApi.getProvince(data.id).then(res => {
              res.data.map(i => {
                nodes.push({
                  label: i.name,
                  value: i.name,
                  leaf: true //配置是否为叶子节点
                })
              });
              // 通过调用resolve将子节点数据返回，通知组件数据加载完成
              resolve(nodes);
            });
          } else {
            resolve(node);
          }
        }
      },
      address: [],
      addressOptions: [],
      btn: false,
      addressBtn: false,
      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      tableData: [],
      dataQuery: {
        className: '',
        courseType: '',
        level: '',
      },
      scheduleId: null,//是否编辑
      // 表单校验
      rules: {
        courseType: [
          { required: true, message: "请选择分类", trigger: "blur" }
        ],
        level: [
          { required: true, message: "请选择阶段等级", trigger: "blur" }
        ],
        className: [
          { required: true, message: "请输入班级名称", trigger: "blur" }
        ],
      }
    };
  },
  created() {
    this.btnShow();
    this.getPageList();
    committeemanApi.getAllRegion().then(res => {
      let temp = [];
      res.data.map(i => {
        temp.push({
          label: i.name,
          value: i.name,
          id: i.id,
          children: i.children.map((item) => {
            return {
              label: item.name,
              value: item.name,
              id: item.id,
              leaf: true
            }
          })
        })
      });
      this.addressOptions = temp;
    });
  },
  methods: {
    btnShow() {
      this.$store.getters.roles.forEach(element => {
        if (element.val === "admin" || element.val === 'Agent') {
          this.btn = true;
        } else {
          this.btn = false;
        }
        if (element.val === 'admin') {
          this.addressBtn = true;
        } else {
          this.addressBtn = false;
        }

      });
    },

    changeStatus(row) {
      let msg = row.status ? '停用' : '启用';
      let status = row.status ? 0 : 1;
      this.$confirm('确定要' + msg + '吗?', '修改状态', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        studyScheduleApi.updateStatus(row.id, status).then(res => {
          this.$nextTick(() => this.getPageList())
          this.$message.success('修改成功!')
        }).catch(err => {

        })
      }).catch(err => {

      })
    },
    /** 编辑按钮*/
    handleEdit(row) {
      this.$refs.add.show(row.id);
    },
    /** 详情按钮操作 */
    handleView(row) {
      const id = row.id;
      ls.setItem('scheduleId', id);
      this.$router.push({ path: '/studyroom/trtcRoom' })
    },

    getPageList() {
      this.tableLoading = true;
      this.dataQuery.pageNum = this.tablePage.currentPage;
      this.dataQuery.pageSize = this.tablePage.size;
      if (this.address) {
        this.dataQuery.province = this.address[0];
        this.dataQuery.city = this.address[1];
      }
      studyScheduleApi.scheduleList(this.dataQuery).then(res => {
        this.tableData = res.data.data;
        this.tableLoading = false;
        // 设置后台返回的分页参数
        pageParamNames.forEach(name => this.$set(this.tablePage, name, parseInt(res.data[name])))
      })
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.$refs.add.show();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dataQuery = {
        className: '',
        courseType: '',
        level: '',
        city: '',
        province: ''
      };
      this.address = [];
      this.getPageList();
    },
    /** 搜索按钮操作 */
    handlePage() {
      this.tablePage = {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
        this.getPageList();
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val;
      this.getPageList();
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val;
      this.getPageList();
    },
  }
};
</script>

<style>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.period-table td,
.period-table th {
  text-align: center;
}

@media (max-width: 767px) {
  .el-message-box {
    width: 80% !important;
  }
}
</style>
