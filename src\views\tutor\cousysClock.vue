<template>
  <div class="app-container">
    <el-form :inline="true" class="container-card" label-width="96px" label-position="left">
      <el-form-item label="教练 名称：">
        <el-input v-model="dataQuery.tutorName" placeholder="请输入教练 名称" clearable />
      </el-form-item>
      <el-form-item label="性别:">
        <el-select v-model="dataQuery.tutorSex" filterable value-key="value" placeholder="请选择" clearable>
          <el-option v-for="(item,index) in [{ value :1,label:'男'},{value: 0, label: '女'}]" :key="index"
            :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="手机号：">
        <el-input v-model="dataQuery.tutorTel" placeholder="请输入教练 手机号" clearable />
      </el-form-item>
      <el-form-item>
        <el-form-item label="打卡时间：" clearable>
          <el-date-picker style="width: 100%;" value-format="yyyy-MM-dd" clearable v-model="clockTime" type="daterange"
            align="right" unlink-panels range-separator="至" start-placeholder="开始时间" end-placeholder="结束时间">
          </el-date-picker>
        </el-form-item>
      </el-form-item>

      <el-form-item label="打卡类型:">
        <el-select v-model="dataQuery.clockStatus" filterable value-key="value" placeholder="请选择" clearable>
          <el-option v-for="(item,index) in [{ value :1,label:'正常'},{value: 2, label: '迟到'},{value: 3, label: '缺卡'}]"
            :key="index" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="getList()">搜索</el-button>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-re" @click="resetQuery()">重置</el-button>
      </el-form-item>
    </el-form>

    <el-table class="common-table" v-loading="tableLoading" :data="tableData" style="width: 100%;margin-bottom: 20px;"
      row-key="id" border default-expand-all :tree-props="{list: 'children', hasChildren: 'true'}">
      <el-table-column prop="tutorId" label="教练 ID" sortable></el-table-column>
      <el-table-column prop="tutorName" label="教练 名称" sortable></el-table-column>
      <el-table-column prop="tutorSex" label="教练 性别" sortable>
        <template slot-scope="scope">
          <span v-if="scope.row.tutorSex===0">女</span>
          <span v-if="scope.row.tutorSex===1">男</span>
        </template>
      </el-table-column>
      <el-table-column prop="tutorTel" label="教练 手机号" sortable></el-table-column>
      <el-table-column prop="clockDate" label="打卡时间" sortable></el-table-column>
      <el-table-column prop="clockStatus" label="打卡类型" sortable>
        <template slot-scope="scope">
          <span v-if="scope.row.clockStatus===1">正常</span>
          <span v-if="scope.row.clockStatus===2">迟到</span>
          <span v-if="scope.row.clockStatus===3">缺卡</span>
        </template>
      </el-table-column>

    </el-table>

    <!-- 分页 -->
    <el-col :span="20" style="overflow-x: auto;" :xs="24">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>
  </div>
</template>

<script>

import courseClockApi from '@/api/courseClock'
import { pageParamNames } from '@/utils/constants'
import cousysApi from '@/api/cousys'

export default {
  data() {
    return {
      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      tableData: [],
      dataQuery: {
        startTime: '',
        endTime: '',
        tutorName: '',
        tutorSex: '',
        tutorTel: '',
        clockStatus: '',
      },
      clockTime: []
    }
  },
  created() {
    this.getList()
  },
  methods: {
    //获取列表
    getList() {
      const that = this
      if (that.clockTime != '' && that.clockTime != null && that.clockTime != undefined) {
        if (that.clockTime.length > 0) {
          that.dataQuery.startTime = that.clockTime[0]
          that.dataQuery.endTime = that.clockTime[1]
        } else {
          that.dataQuery.startTime = ''
          that.dataQuery.endTime = ''
        }
      } else {
        that.dataQuery.startTime = ''
        that.dataQuery.endTime = ''
      }
      that.tableLoading = true
      courseClockApi.getTutorClockList(that.tablePage.currentPage, that.tablePage.size, that.dataQuery).then(res => {
        that.tableData = res.data.data
        console.log(res.data.data)
        that.tableLoading = false
        // 设置后台返回的分页参数
        pageParamNames.forEach(name => this.$set(this.tablePage, name, parseInt(res.data[name])))
      })
    },

    //重置按钮
    resetQuery() {
      this.clockTime = []
      this.dataQuery = {
        startTime: '',
        endTime: '',
        tutorName: '',
        tutorSex: '',
        tutorTel: '',
        clockStatus: '',
      },
        this.getList()
    },
    reset() {
      this.tutorInfoVo = {}
    },
    cancel() {
      this.reset()
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val
      this.getList()
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val
      this.getList()
    }
  }
}
</script>

<style>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

@media screen and (max-width: 767px) {
  .recharge-dialog .el-dialog {
    width: 90% !important;
  }

  .el-message-box {
    width: 80% !important;
  }
}
</style>
