<template>
  <div class="app-container">
    <el-form :inline="true" ref="queryForm" class="container-card" label-width="110px">
      <el-form-item label="目标名称：" prop="targetName">
        <el-input v-model="dataQuery.targetName" placeholder="请输入名称" clearable />
      </el-form-item>
      <el-form-item label="学段：" prop="courseType">
        <el-select v-model="dataQuery.level" placeholder="全部" clearable>
          <el-option v-for="(item, index) in levelList" :key="index" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handlePage()">搜索</el-button>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-re" @click="resetQuery()">重置</el-button>
      </el-form-item>

    </el-form>
    <el-col :span="24" style="margin-bottom: 30px">
      <el-button type="success" icon="el-icon-plus" size="mini" @click="handleAdd()" v-if="btn">新增目标</el-button>
    </el-col>

    <el-table class="common-table" v-loading="tableLoading" :data="tableData" style="width: 100%;margin-bottom: 20px;"
      row-key="id" border default-expand-all>
      <el-table-column prop="id" label="自学目标ID"></el-table-column>
      <el-table-column prop="targetName" label="自学目标名称"></el-table-column>
      <el-table-column label="操作" width="260">
        <template slot-scope="scope">
          <el-button type="text" size="mini" @click="handleView(scope.row)">查看计划</el-button>
          <el-button type="text" size="mini" @click="toTimer(scope.row)">查看计时器</el-button>
          <el-button type="text" size="mini" @click="handleEdit(scope.row)" v-if="btn">编辑</el-button>
          <el-button type="text" size="mini" @click="changeStatus(scope.row)" v-if="scope.row.status && btn"
            style="color: red">停用
          </el-button>
          <el-button type="text" size="mini" @click="changeStatus(scope.row)" v-if="!scope.row.status && btn">启用
          </el-button>
        </template>
      </el-table-column>
      <el-table-column prop="studentName" label="学员姓名"></el-table-column>
      <el-table-column prop="level" label="学段">
        <template slot-scope="scope">
          <span v-if="scope.row.level === '1'">小学</span>
          <span v-if="scope.row.level === '2'">初中</span>
          <span v-if="scope.row.level === '3'">高中</span>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间"></el-table-column>
      <el-table-column prop="targetDuration" label="目标学时">
        <template slot-scope="scope">
          <span v-if="Math.floor(scope.row.targetDuration / 60) > 0">{{ Math.floor(scope.row.targetDuration / 60) }}小时</span>
          <span v-if="scope.row.targetDuration % 60 >= 0">{{ scope.row.targetDuration % 60 }}分钟</span>
        </template>
      </el-table-column>
      <el-table-column prop="targetRate" label="进度比">
        <template slot-scope="scope">
          {{ scope.row.targetRate }}%
        </template>
      </el-table-column>
      
    </el-table>
    <!-- 分页 -->
    <el-col :span="24" style="margin-bottom: 20px">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>

    <!-- 添加弹窗 -->
    <el-dialog :title="title" :visible.sync="open" width="60%" top="6vh" :close-on-click-modal="false" @close="close">
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-row>
          <el-col :span="16">
            <el-form-item label="选择规划:" prop="studyScheduleId">
              <el-input v-model="form.studyScheduleId" type="hidden" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item>
              <el-input v-model="searchName" placeholder="输入关键字搜索" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item style="margin-left:-80px">
          <el-table class="common-table" :data="scheduleData.filter(data => (!searchName || data.className.toLowerCase().includes(searchName.toLowerCase()))
            && (!level || data.level.toLowerCase().includes(level.toLowerCase()))
          )" style="width: 100%;margin-bottom: 20px;" ref="singleTable" v-loading="scheduleLoading"
            :row-class-name="tableRowClassName" row-key="id" border :highlight-current-row="isAdd" max-height="300"
            @current-change="handleRowChange">
            <el-table-column prop="className" label="规划名称"></el-table-column>
            <el-table-column prop="courseType" label="课程分类">
              <template slot-scope="scope">
                <span v-if="scope.row.courseType == 1">系统课</span>
                <span v-else>非系统课</span>
              </template>
            </el-table-column>
            <el-table-column prop="level" label="学段">
              <template slot-scope="scope">
                <span v-if="scope.row.level === '1'">小学</span>
                <span v-if="scope.row.level === '2'">初中</span>
                <span v-if="scope.row.level === '3'">高中</span>
              </template>
            </el-table-column>
            <el-table-column prop="tranWeeks" label="日期"></el-table-column>
            <el-table-column prop="times" label="时间"></el-table-column>
          </el-table>
        </el-form-item>
        <el-row>
          <el-col :span="12">
            <el-form-item label="目标名称:" prop="targetName">
              <el-input v-model="form.targetName" />
            </el-form-item>
            <p style="marginTop:-10px;marginLeft:42px;fontSize:12px;color:#aaa">例：学习XXX目标一小时</p>
          </el-col>
          <!--          <el-col :span="8">
            <el-form-item label="阶段等级:" prop="level">
              <el-select v-model="form.level" :disabled="!isAdd">
                <el-option
                  v-for="(item,index) in [{label:'小学',value:'1'},{label:'初中',value:'2'},{label:'高中',value:'3'}]"
                  :key="index" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>-->
          <el-col :span="12" v-if="isAdd">
            <el-form-item label="手机号码:">
              <el-input v-model="phoneNum" maxlength="11" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-form-item label="学员学号:" prop="studentCode" v-if="isAdd">
            <el-radio-group v-model="form.studentCode" v-for="(item, index) in studentCodeList" :key="index"
              @change="radioChange">
              <el-radio :disabled="!isAdd" :label="`${item.studentCode}`"
                style="margin-right:12px;">{{ `${item.realName}(${item.studentCode})` }}</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-col :span="12" v-if="!isAdd">
            <el-form-item label="学员学号:" prop="studentCode">
              <el-input v-model="form.studentCode" :disabled="!isAdd" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="submitForm()" :disabled="isDisabled">确定</el-button>
        <el-button size="mini" @click="close">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import studyScheduleApi from "@/api/studyroom/studySchedule";
import { pageParamNames } from "@/utils/constants";
import ls from '@/api/sessionStorage'

export default {
  name: 'targetList',
  data() {
    return {
      isDisabled: false,
      btn: false,
      isAdd: true,
      title: '',
      level: '',
      searchName: '',
      scheduleData: [],
      scheduleLoading: false,
      levelList: [{ label: '小学', value: '1' }, { label: '初中', value: '2' }, { label: '高中', value: '3' }],
      open: false,
      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      schedulePage: {
        currentPage: 1,
        size: 5,
        totalPage: null,
        totalItems: null
      },
      tableData: [],
      dataQuery: {
        targetName: '',
        level: '',
      },
      form: {},
      phoneNum: '',
      studentCodeList: [], // 学员手机号码下的学号
      // 表单校验
      rules: {
        studyScheduleId: [
          { required: true, message: "请选择规划", trigger: "blur" }
        ],
        targetName: [
          { required: true, message: "请输入目标名称", trigger: "blur" }
        ],
        level: [
          { required: true, message: "请选择阶段等级", trigger: "blur" }
        ],
        studentCode: [
          { required: true, message: "请输入学员学号", trigger: "blur" }
        ],
      }
    };
  },
  created() {
    this.getPageList();
    this.btnShow();
  },
  methods: {
    btnShow() {
      this.$store.getters.roles.forEach(element => {
        if (element.val === "admin" || element.val === 'Dealer' || element.val === 'School') {
          this.btn = true;
        } else {
          this.btn = false;
        }
      });
    },
    tableRowClassName({ row, rowIndex }) {
      if (row.id === this.form.studyScheduleId) {
        return 'success-row';
      }
      return '';
    },

    blurChange(phoneNum) {
      if (phoneNum) {
        studyScheduleApi.getStudentCode(phoneNum).then(res => {
          if (res.data != null) {
            this.studentCodeList = res.data;
          }
        })
      }
    },
    radioChange(studentCode) {
      if (studentCode) {
        studyScheduleApi.getStudentLevel(studentCode).then(res => {
          if (res.data != null) {
            this.level = res.data;
            this.form.studyScheduleId = null;
            if (this.level !== res.data) {
              this.level = res.data;
              this.form.studyScheduleId = null;
            }
          }
        })
      } else {
        this.level = '';
        this.form.studyScheduleId = null;
      }
    },
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.isDisabled = true;
          if (this.form.id) {
            studyScheduleApi.editTarget(this.form).then(response => {
              this.$message.success("提交成功！")
              this.open = false;
              this.isDisabled = false;
              this.getPageList();
            }).catch(err => {
              this.isDisabled = false;
            });
          } else {
            studyScheduleApi.addTarget(this.form).then(response => {
              this.$message.success("提交成功！")
              this.open = false;
              this.isDisabled = false;
              this.getPageList();
            }).catch(err => {
              this.isDisabled = false;
            });
          }
        }
      });
    },
    handleRowChange(row) {
      if (this.isAdd) {
        if (row) {
          this.level = row.level
          this.form.studyScheduleId = row.id;
        } else {
          this.form.studyScheduleId = null;
        }
      }
    },
    getScheduleList() {
      this.scheduleLoading = true;
      studyScheduleApi.scheduleList().then(res => {
        this.scheduleData = res.data.data;
        this.scheduleData = this.scheduleData.reverse();
        this.scheduleLoading = false;
        // 设置后台返回的分页参数
        pageParamNames.forEach(name => this.$set(this.schedulePage, name, parseInt(res.data[name])))
      })
    },
    changeStatus(row) {
      let msg = row.status ? '停用' : '启用';
      let status = row.status ? 0 : 1;
      this.$confirm('确定要' + msg + '吗?', '修改状态', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        studyScheduleApi.updateStatus(row.id, status).then(res => {
          this.$nextTick(() => this.getPageList())
          this.$message.success('修改成功!')
        }).catch(err => {

        })
      }).catch(err => {

      })
    },
    /** 编辑按钮*/
    handleEdit(row) {
      this.title = '编辑目标';
      this.isAdd = false;
      this.getScheduleList();
      studyScheduleApi.getTarget(row.id).then(res => {
        this.form = res.data;
        this.open = true;
      })
    },
    /** 详情按钮操作 */
    handleView(row) {
      const id = row.id;
      ls.setItem('scheduleId', id);
      this.$router.push({ path: '/schedule/planList' })
    },
    toTimer(row) {
      const id = row.id;
      ls.setItem('scheduleId', id);
      this.$router.push({ path: '/schedule/timerList' })
    },

    getPageList() {
      this.tableLoading = true;
      studyScheduleApi.targetList(this.tablePage.currentPage, this.tablePage.size, this.dataQuery).then(res => {
        this.tableData = res.data.data;
        this.tableLoading = false;
        // 设置后台返回的分页参数
        pageParamNames.forEach(name => this.$set(this.tablePage, name, parseInt(res.data[name])))
      })
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.title = '新建目标';
      this.isAdd = true;
      this.reset();
      this.getScheduleList();
      this.open = true;
    },
    handlePage() {
      this.tablePage = {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
        this.getPageList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dataQuery = {
        className: '',
        courseType: '',
        level: '',
      };
      this.getPageList();
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val;
      this.getPageList();
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val;
      this.getPageList();
    },
    close() {
      this.open = false;
      this.reset();
      this.setCurrent();
      this.studentCodeList = [];
      this.phoneNum = '';
    },
    reset() {
      this.searchName = '';
      this.level = '';
      this.form = {
        studyScheduleId: null,
        targetName: null,
        studentCode: null
      };
      if (this.$refs['form']) {
        this.$refs['form'].resetFields();
      }
    },
    setCurrent(row) {
      this.$refs.singleTable.setCurrentRow(row);
    },
  },
  watch: {
    phoneNum(val) {
      if (val.length === 11) {
        this.blurChange(val);
      } else {
        this.studentCodeList = [];
        this.level = '';
        this.form.studyScheduleId = null;
      }
    }
  }
};
</script>

<style lang="less" scoped>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.period-table td,
.period-table th {
  text-align: center;
}

@media (max-width: 767px) {
  .el-message-box {
    width: 80% !important;
  }
}

.el-table .success-row {
  background: #e8f4ff;
}

/deep/.el-dialog__body {
  padding: 0 20px !important;
}
</style>
