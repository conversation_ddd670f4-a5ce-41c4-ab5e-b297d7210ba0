<template>
    <div class="app-container">

        <el-col :span="24" style="margin-bottom: 30px;">
            <el-button @click="newsType()">全部消息</el-button>
            <el-button @click="newsType(1)">系统消息</el-button>
            <el-button @click="newsType(2)">公司消息</el-button>
            <!-- <el-button type="warning" icon="el-icon-document-copy" @click="openBigRecharge()" size="mini">学员充值时长
            </el-button>
            <el-button type="success" size="mini" icon="el-icon-edit" @click="jumpOpenCoursePackage(null)">开通课程包
            </el-button> -->
        </el-col>

        <el-table class="common-table" :data="tableData" style="width: 100%;margin-bottom: 20px;" border>
            <el-table-column prop="title" label="消息标题" width="" show-overflow-tooltip>
                <template slot-scope="scope">
                    <el-link :underline="false" type="primary" @click="lookNotifications(scope.row)">{{
                        scope.row.title }}</el-link>
                    <!-- <span style="color: #0078b9;" @click="lookNotifications(scope.row)"></span> -->
                </template>
            </el-table-column>
            <el-table-column prop="loginName" label="状态" width="120">
                <template slot-scope="scope">
                    <span>{{ scope.row.isEnable == 0 ? '已读' : '未读' }}</span>
                </template>
            </el-table-column>
            <el-table-column prop="realName" label="类型" width="100">
                <template slot-scope="scope">
                    <span>{{ scope.row.noticeType == 1 ? '系统消息' : '公司消息' }}</span>
                </template>
            </el-table-column>
            <el-table-column prop="addTime" label="发布时间" width="200"></el-table-column>
        </el-table>


        <!-- 分页 -->
        <el-col :span="24" style="overflow-x: auto;" :xs="24">
            <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
                :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]" :page-size="10"
                layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems">
            </el-pagination>
        </el-col>
    </div>
</template>
  
<script>
import fa from 'element-ui/src/locale/lang/fa'
import newsApi from '@/api/news'
import ls from '@/api/noticeNews'
import Middle from '@/api/newUtil';


export default {
    name: 'news',
    computed: {
        fa() {
            return fa
        }
    },
    data() {
        return {
            // 分页
            tablePage: {
                currentPage: 1,
                size: 10,
                totalPage: null,
                totalItems: null
            },
            grammarData: {
                studentCode: '',
                phase: '',
                studentName: ''
            },
            studentInfo: [],
            tableData: [],
            type: '', // 消息类型
            currentPage4: 4
        }
    },
    created() {
        this.index();
    },
    watch: {
        '$route': 'initData'
    },

    methods: {
        initData() {
            console.log('路由变化了')
            this.index();
        },
        newsType(val) {
            this.type = val;
            this.index();

        },
        index() {
            console.log(this.tablePage)

            let that = this;
            let data = {
                noticeType: this.type
            }
            newsApi.newsList(that.tablePage.currentPage, that.tablePage.size, data).then((res) => {
                this.tableData = res.data.data;
                this.tablePage.totalItems= Number(res.data.totalItems);
                Middle.$emit('methodA');
            })
        },
        // 分页
        handleSizeChange(val) {
            this.tablePage.size = val;
            this.index();
        },
        handleCurrentChange(val) {
            this.tablePage.currentPage = val;
            this.index();
        },

        lookNotifications(row) {
            console.log(row)
            ls.setItem('notificationId', row.id)
            this.$router.push({
                path: '/news/newContent',
                query: { id: row.id }
            });
        }


    }
}
</script>
  
<style>
/* .container-card {
    padding: 15px 15px 15px 15px;
    margin-bottom: 15px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  } */

.period-table td,
.period-table th {
    text-align: center;
}

.mt20 {
    margin-top: 20px;
}

.red {
    color: red;
}

.green {
    color: green;
}

.demonstration_dev {
    margin-bottom: 10px;
    font-size: 18px;
}

@media screen and (max-width: 767px) {
    .recharge-dialog .el-dialog {
        width: 90% !important;
    }

    .el-message-box {
        width: 80% !important;
    }
}

.hidden-label .el-radio__label {
    display: inline-block;
    width: 0;
    visibility: hidden;
}

.inputpre {
    position: relative;
}

.btnpob {
    position: absolute;
    right: 0;
    top: 0;
}
</style>
  