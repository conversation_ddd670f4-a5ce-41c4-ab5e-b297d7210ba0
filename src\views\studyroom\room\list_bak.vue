<template>
  <div class="app-container">
    <el-form :inline="true" ref="queryForm" class="container-card" label-width="110px">
    </el-form>

    <el-table class="common-table" v-loading="tableLoading" :data="tableData" style="width: 100%;margin-bottom: 20px;"
      row-key="id" border default-expand-all>
      <el-table-column prop="roomId" label="房间ID"></el-table-column>
      <el-table-column prop="roomName" label="房间名称"></el-table-column>
      <el-table-column label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="mini" v-if="scope.row.roomId">
            <router-link :to="'/schedule/fixTrtcRoom/' + scope.row.scheduleId">查看详情</router-link>
          </el-button>
        </template>
      </el-table-column>
      <el-table-column prop="courseType" label="课程分类">
        <template slot-scope="scope">
          <span v-if="scope.row.courseType == 1">系统课</span>
          <span v-else>非系统课</span>
        </template>
      </el-table-column>
      <el-table-column prop="weeks" label="日期"></el-table-column>
      <el-table-column label="房间人数">
        <template slot-scope="scope">
          {{ scope.row.currentCount }}/{{ scope.row.maxUserCount }}
        </template>
      </el-table-column>
      <el-table-column prop="times" label="规划时间"></el-table-column>
      <el-table-column label="剩余时间">
        <template slot-scope="scope">
          <span v-if="scope.row.minute === '-1'">
            --
          </span>
          <span v-else>{{ scope.row.minute }}分钟</span>
        </template>
      </el-table-column>
      <el-table-column label="状态">
        <template slot-scope="scope">
          <span v-if="scope.row.status === 1">
            未开始
          </span>
          <span v-else>进行中</span>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <el-col :span="24" style="margin-bottom: 20px">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>
  </div>
</template>

<script>
import studyScheduleApi from "@/api/studyroom/studySchedule";
import { pageParamNames } from "@/utils/constants";

export default {
  name: 'Schedule',
  data() {
    return {
      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      open: false,
      tableData: [],
    };
  },
  created() {
    this.getPageList();
  },
  methods: {
    /** 详情按钮操作 */
    handleView(row) {
      this.reset();
    },
    getPageList() {
      this.tableLoading = true;
      studyScheduleApi.getRoomList(this.tablePage.currentPage, this.tablePage.size).then(res => {
        console.log(res.data.data);
        this.tableData = res.data.data;
        this.tableLoading = false;
        // 设置后台返回的分页参数
        pageParamNames.forEach(name => this.$set(this.tablePage, name, parseInt(res.data[name])))
      })
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val;
      this.getPageList();
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val;
      this.getPageList();
    },
  }
};
</script>

<style>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.period-table td,
.period-table th {
  text-align: center;
}

@media (max-width:767px) {
  .el-message-box {
    width: 80% !important;
  }
}</style>
