<template>
  <div class="app-container">
    <el-form :inline="true" class="container-card" label-width="96px" label-position="left">
      <el-form-item label="起止时间">
        <el-date-picker style="width: 100%;" v-model="createTime" type="daterange" value-format="yyyy-MM-dd HH:mm:ss"
          align="right" unlink-panels range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="教练 名称：">
        <el-input v-model="dataQuery.tutorName" placeholder="请输入教练 名称" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="getList()">搜索</el-button>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-re" @click="resetQuery()">重置</el-button>
      </el-form-item>
    </el-form>
    <el-table class="common-table" v-loading="tableLoading" :data="tableData" height="400px"
      style="width: 100%;margin-bottom: 20px;" row-key="id" border default-expand-all
      :tree-props="{list: 'children', hasChildren: 'true'}">
      <el-table-column prop="id" label="ID"></el-table-column>
      <el-table-column prop="tutorName" label="被评审人"></el-table-column>
      <el-table-column label="操作" align="center" width="200">
        <template slot-scope="scope">
          <el-button v-if="scope.row.status===0" type="success" @click="handleView(scope.row)"
            size="mini">评审</el-button>
          <span v-else>已评审</span>
        </template>
      </el-table-column>
      <el-table-column prop="startTime" label="学时开始时间"></el-table-column>
      <el-table-column prop="normalPic" label="合规图片">
        <template slot-scope="scope">
          <div  v-if="scope.row.normalPic">
            <el-image v-for="(item,index) in scope.row.normalPic.split(',')" :key="index" :src="item"
            :preview-src-list="scope.row.normalPic.split(',')" class="illegalPic" />
          </div>
          <span v-else>无</span>
        </template>
      </el-table-column>
      <el-table-column prop="illegalPic" label="不合规图片">
        <template slot-scope="scope">
          <div  v-if="scope.row.illegalPic" >
            <el-image v-for="item in scope.row.illegalPic.split(',')" :key="item" :src="item"
            :preview-src-list="scope.row.illegalPic.split(',')" class="illegalPic" />
          </div>
          <span v-else>无</span>
        </template>
      </el-table-column>

    </el-table>

    <el-dialog class="recharge-dialog" title="上课行为评审" :visible.sync="open" width="50%" :close-on-click-modal="false">
      <el-form ref="form" :model="form" label-width="30px" :rules="rules" style="width: 100%;" @close="reset">
        <el-form-item v-if="files">
          <el-image v-for="img in files" :key="img" style="width: 100px; height: 100px;margin-left:40px;margin-right:40px"
            :src="img" :preview-src-list="files">
          </el-image>
        </el-form-item>
        <el-form-item v-if="files">
          <el-radio-group v-for="(img,index) in files" v-model="radio[img]" :key="index" size="mini" style="margin-right:40px">
            <el-radio :label="1">合规</el-radio>
            <el-radio :label="2">不合规</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="testSave()">确定</el-button>
        <el-button size="mini" @click="cancel()">关闭</el-button>
      </div>
    </el-dialog>

    <!-- 分页 -->
    <el-col :span="20" style="overflow-x: auto;" :xs="24">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>
  </div>
</template>

<script>

import classRecordApi from '@/api/classRecord'
import { pageParamNames } from "@/utils/constants";
export default {
  data() {
    return {
      radio: {},
      files: [],
      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      tableData: [],
      open: false,
      dataQuery: {
        status: null,
        tutorName: null,
        startTime: null,
        endTime: null
      },
      createTime: [],
      form: {},
      statusList: {
        1: "已结算",
        2: "待结算"
      },
      rules: {
        handingResult: [
          { required: true, message: "请输入处理方案", trigger: "blur" }
        ],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    testSave() {
      var arr = [];
      for (var i = 0; i < this.files.length; i++) {
        var data = {};
        data.key = this.files[i];
        data.value = this.radio[this.files[i]];
        if (data.value === '' || data.value === undefined) {
          this.$message.error('请选择是否合规!');
          return false;
        }
        arr.push(data);
      }
      classRecordApi.appraisal(this.form.id, arr).then(res => {
        this.open = false;
        this.getList();
        this.$message.success('评审成功!')
      }).catch(err => {
      })
    },


    //获取列表
    getList() {
      const that = this;
      var a = that.createTime;
      if (a != null) {
        that.dataQuery.startTime = a[0];
        that.dataQuery.endTime = a[1];
      } else {
        that.dataQuery.startTime = null;
        that.dataQuery.endTime = null;
      }
      that.tableLoading = true;
      classRecordApi.classRecordList(that.tablePage.currentPage, that.tablePage.size, that.dataQuery).then(res => {
        that.tableData = res.data.data
        that.tableLoading = false
        // 设置后台返回的分页参数
        pageParamNames.forEach(name => this.$set(this.tablePage, name, parseInt(res.data[name])))
      })
    },

    //重置按钮
    resetQuery() {
      this.createTime = [];
      this.dataQuery = {
        status: null,
        tutorName: null,
        startTime: null,
        endTime: null
      };
      this.getList();
    },
    //查看按钮
    handleView(row) {
      this.reset();
      classRecordApi.classRecordDetail(row.id).then(response => {
        this.form = response.data;
        if (response.data.illegalPic !== null && response.data.illegalPic !== '') {
          this.files = response.data.illegalPic.split(",");
        }
        this.open = true;
      });
    },
    //删除按钮
    handleDel(row) {
      this.$confirm('确定要删除该绩效吗?', '删除绩效', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        classRecordApi.classRecordDel(row.id).then(res => {
          this.getList();
          this.$message.success('删除成功!')
        }).catch(err => {
        })
      }).catch(err => {
      })
    },
    reset() {
      this.form = {
        id: null,
        parentName: null,
        parentTel: null,
        tutorName: null,
        status: null,
        reason: null,
        remark: null,
        pic: null,
        handingResult: null,
      };
      this.radio = {};
      this.files = [];
    },
    cancel() {
      this.open = false;
      this.reset();
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val;
      this.getList();
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val;
      this.getList();
    },
  }
};
</script>

<style>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}
@media screen and (max-width: 767px) {
  .recharge-dialog .el-dialog {
    width: 90% !important;
  }

  .el-message-box {
    width: 80% !important;
  }
}
.illegalPic {
  width: 50px;
  height: 50px;
  margin-right: 5px;
}
</style>
