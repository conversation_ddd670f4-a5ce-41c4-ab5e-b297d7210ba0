<template>
  <div class="app-container">
    <!-- 新增按钮 -->
    <el-row class="container-card" style="margin-bottom: 30px;">
      <el-col :span="8">
        <el-col :span="8" class="marginbottom" style="line-height: 36px;">经销商等级</el-col>
        <el-col :span="15">

          <el-select v-model="dataQuery.commissionGrade" placeholder="请选择">
            <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-col>
      </el-col>
      <el-col :span="13" style="height: 1px;" />

      <el-col :span="3" style="text-align: right;">
        <el-button type="primary" icon="el-icon-search" @click="fetchData()" size="mini">搜索</el-button>
      </el-col>
    </el-row>

    <el-table v-loading="tableLoading" stripe :data="tableData" border style="width: 100%;margin-bottom: 30px;">
      <el-table-column prop="distributorId" label="经销商编号" align="center" width="180" />
      <el-table-column prop="memberId" label="会员编号" align="center" />
      <el-table-column label="操作" align="center" width="160">
        <template slot-scope="scope">
          <!--          <el-tooltip content="新增推荐人" placement="top">-->
          <!--            <el-button type="warning" @click="addReferee(scope.row.distributorCommissionId)" size="mini">新增推荐人</el-button>-->
          <!--          </el-tooltip>-->
          <el-tooltip content="查看流水" placement="top">
            <el-button type="warning" @click="queryFlow(scope.row.distributorCommissionId)" size="mini">查看流水</el-button>
          </el-tooltip>
          <!--          <el-tooltip content="等级变更为金牌" placement="top">-->
          <!--            <el-button type="danger" @click="changeLevel(scope.row.distributorCommissionId)" size="mini">变更等级</el-button>-->
          <!--          </el-tooltip>-->
        </template>
      </el-table-column>
      <el-table-column prop="nickName" label="会员名称" align="center" />
      <el-table-column prop="commissionGrade" label="经销商等级" align="center">
        <template slot-scope="scope">
          <span
            :style="scope.row.commissionGrade === '未生效' ? 'color:#666666' : 'color:#f2c82a'">{{ scope.row.commissionGrade }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="loginName" label="手机号" align="center" :show-overflow-tooltip="true" />
      <el-table-column prop="waitCommission" label="待分润" align="center">
        <template slot-scope="scope">
          <span style="color:#409eff">{{ scope.row.waitCommission }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="alreadyCommission" label="可提现" align="center">
        <template slot-scope="scope">
          <span style="color:#e6a23c">{{ scope.row.alreadyCommission }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="totalRevenue" label="总收益" align="center">
        <template slot-scope="scope">
          <span style="color:#f56c6c">{{ scope.row.totalRevenue }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间" align="center" :show-overflow-tooltip="true" />
    </el-table>

    <!-- 分页 -->
    <el-col :span="20">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>

    <!-- 新增推荐人弹窗-->
    <el-dialog title="新增推荐人" :visible.sync="dialogRefereeVisible">
      <el-form>
        <el-form-item label="选择推荐人" prop="refereeData">
          <el-select v-model="refereeData" placeholder="请选择">
            <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button type="success" size="mini" @click="confirmAddReferee">确 定</el-button>
      </div>
    </el-dialog>


  </div>
</template>

<script>
import distributorApi from '@/api/distributor'

import {
  pageParamNames
} from '@/utils/constants'

export default {
  components: {},
  data() {
    return {
      tableLoading: false,
      dataQuery: {
        commissionGrade: ''
      },
      options: [{
        value: 'A',
        label: '铜'
      }, {
        value: 'B',
        label: '银'
      }, {
        value: 'C',
        label: '金'
      }, {
        value: 'Z',
        label: '未生效'
      }, {
        value: '',
        label: '全部'
      }],
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      tableData: [],

      dialogRefereeVisible: false, // 新增推荐人弹窗是否展示
      refereeData: '',             //推荐人数据
    }
  },
  created() {
    this.fetchData()
  },
  methods: {
    // 获取UUid
    getUUid: function () {
      const s = []
      const hexDigits = '0123456789abcdef'
      for (let i = 0; i < 36; i++) {
        s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1)
      }
      s[14] = '4'
      s[19] = hexDigits.substr((s[19] & 0x3) | 0x8, 1)
      s[8] = s[13] = s[18] = s[23] = '-'
      const UUid = s.join('')
      return UUid
    },

    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val
      this.fetchData()
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val
      this.fetchData()
    },

    // 查询
    fetchData() {
      const that = this
      that.tableLoading = true
      distributorApi.queryPageDistributors(that.tablePage.currentPage, that.tablePage.size, that.dataQuery).then(res => {
        that.tableData = res.data.data
        that.tableLoading = false
        // 设置后台返回的分页参数
        pageParamNames.forEach(name => that.$set(that.tablePage, name, parseInt(res.data[name])))
      })
    },
    //查看流水
    queryFlow(id) {
      this.$router.push({ path: '/distributor/distributorFlow', query: { distributorCommissionId: id } })
    },
    //  新增推荐人
    addReferee() {
      console.log('新增推荐人')
      this.dialogRefereeVisible = true;
    },
    //  确认新增推荐人
    confirmAddReferee() {
      console.log('确认新增推荐人')
    },



    //  变更等级
    changeLevel(id) {
      console.log('变更等级')
      this.$confirm('是否确认当前经销商等级变更为金牌?', '变更等级', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        confirmButtonClass: 'dangerConfirm'
      }).then(() => {
        this.$message({
          type: 'success',
          message: '变更成功 !'
        });
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消变更'
        });
      });
    },

  }
}
</script>
<style>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, .12), 0 0 6px rgba(0, 0, 0, .04)
}

.dangerConfirm {
  background: #f56c6c !important;
  border-color: #f56c6c !important;
  padding: 7px 15px !important;
}
</style>
