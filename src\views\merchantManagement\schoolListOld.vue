<template>
  <div class="app-container">
    <el-form :inline="true" class="container-card" label-width="130px" label-position="right" ref="dataQuery" :model="dataQuery">
      <el-row>
        <el-col :span="8" :xs="24">
          <el-form-item label="门店编号：" prop="merchantCode">
            <el-input id="merchantCode" v-model="dataQuery.merchantCode" name="id" placeholder="请输入门店编号" @input="$forceUpdate()" clearable />
          </el-form-item>
        </el-col>

        <el-col :span="8" :xs="24">
          <el-form-item label="门店名称：" prop="merchantName">
            <el-input id="merchantName" v-model="dataQuery.merchantName" name="id" placeholder="请输入门店名称" @input="$forceUpdate()" clearable />
          </el-form-item>
        </el-col>

        <el-col :span="8" :xs="24" v-if="!isAdminManger">
          <el-form-item label="登录账号：" prop="name">
            <el-input id="name" v-model="dataQuery.name" name="id" placeholder="请输入登录账号" @input="$forceUpdate()" clearable />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="8" :xs="24">
          <el-form-item label="交付中心名称：" prop="schoolBingDevlierName">
            <el-input id="schoolBingDevlierName" v-model.trim="dataQuery.schoolBingDevlierName" @input="$forceUpdate()" name="id" placeholder="请输入交付中心名称" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="交付中心编号：" prop="schoolBingDevlierCode">
            <el-input id="schoolBingDevlierCode" v-model.trim="dataQuery.schoolBingDevlierCode" @input="$forceUpdate()" name="id" placeholder="请输入交付中心编号" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="交付中心指派状态:" prop="schoolBing">
            <el-select v-model="dataQuery.schoolBing" placeholder="请选择" @change="$forceUpdate()" clearable>
              <el-option v-for="(item, index) in options" :key="index" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <!--        <el-col :span="8" :xs="24">-->
      <!--          <el-form-item label="推荐人编号：" prop="marketPartner">-->
      <!--            <el-input id="marketPartner" v-model="dataQuery.marketPartner" name="id" clearable/>-->
      <!--          </el-form-item>-->
      <!--        </el-col>-->
      <el-row>
        <el-col :span="8" :xs="24">
          <el-form-item label="状态:" prop="isEnable" v-if="checkPermission(['b:merchant:dataQueryIsEnable:choose'])">
            <el-select v-model="dataQuery.isEnable" filterable value-key="value" placeholder="请选择" @change="$forceUpdate()" clearable>
              <el-option
                v-for="(item, index) in [
                  { value: 1, label: '开通' },
                  { value: 0, label: '暂停' },
                  { value: -1, label: '系统关闭' },
                  { value: -2, label: '年审关闭' },
                  { value: -3, label: '终止' },
                  { value: -5, label: '到期未续费' }
                ]"
                :key="index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="负责人：" prop="realName">
            <el-input id="realName" v-model="dataQuery.realName" name="id" placeholder="请输入负责人" @input="$forceUpdate()" clearable />
          </el-form-item>
        </el-col>

        <el-col :span="8" :xs="24">
          <el-form-item label="推荐人编号：" prop="refereeCode">
            <el-input id="refereeCode" v-model="dataQuery.refereeCode" name="id" placeholder="请输入推荐人编号" @input="$forceUpdate()" clearable />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- <el-row>
        <el-col :span="8" :xs="24">
          <el-form-item label="门店类型:" prop="schoolType">
            <el-select v-model="dataQuery.schoolType" filterable value-key="value" placeholder="请选择" clearable>
              <el-option label="直营门店" :value="1"/>
              <el-option label="加盟门店" :value="2"/>
              <el-option label="超级俱乐部门店" :value="3"/>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row> -->

      <el-row>
        <el-col :span="8" :xs="24">
          <el-form-item label="所在地区：" prop="address">
            <el-input id="address" v-model="dataQuery.address" name="id" placeholder="请输入所在地区" @input="$forceUpdate()" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="注册时间：" prop="regTime">
            <el-date-picker
              style="width: 260px"
              v-model="regTime"
              type="daterange"
              value-format="yyyy-MM-dd HH:mm:ss"
              align="right"
              unlink-panels
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="$forceUpdate()"
              clearable
            ></el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="所属俱乐部：" prop="operationsName">
            <el-input id="operationsName" v-model="dataQuery.operationsName" name="id" placeholder="请输入" @input="$forceUpdate()" clearable />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-row>
          <el-col :span="8" :xs="24">
            <el-form-item label="完款状态:" prop="paymentIsComplete" v-if="checkPermission(['b:merchant:paymentIsComplete:choose'])">
              <el-select v-model="dataQuery.paymentIsComplete" filterable value-key="value" placeholder="请选择" clearable>
                <el-option label="全部" value=" " />
                <el-option label="已完款" :value="1" />
                <el-option label="等待完款" :value="0" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="培训是否缴费：" prop="isPay" v-if="isNeedPay">
              <el-select v-model="dataQuery.isPay" value-key="value" placeholder="请选择" clearable>
                <el-option
                  v-for="(item, index) in [
                    { value: 0, label: '未缴费' },
                    { value: 1, label: '已缴费' },
                    { value: 2, label: '无需缴费' }
                  ]"
                  :key="index"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" style="text-align: right; padding-right: 16vw">
            <el-form-item>
              <el-button type="primary" icon="el-icon-search" @click="fetchData01()">搜索</el-button>
              <el-button icon="el-icon-refresh" @click="rest()">重置</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-row>
    </el-form>

    <el-col :span="24" style="margin-bottom: 30px">
      <el-button type="warning" icon="el-icon-document-copy" @click="exportSchool()" v-loading="exportLoading" v-if="checkPermission(['b:merchant:schoolList:export'])" size="mini">
        导出
      </el-button>
    </el-col>

    <el-table
      class="common-table"
      v-loading="tableLoading"
      :data="tableData"
      height="400px"
      style="width: 100%; margin-bottom: 20px"
      row-key="id"
      border
      default-expand-all
      :tree-props="{ list: 'children', hasChildren: 'true' }"
      size="mini"
    >
      <el-table-column prop="refereeCode" label="推荐人编号" width="150"></el-table-column>
      <el-table-column prop="refereeName" label="推荐人姓名" width="150"></el-table-column>

      <el-table-column label="操作" min-width="1100" v-if="!isAdminManger">
        <template slot-scope="scope">
          <el-row>
            <el-col :span="6">
              <el-button-group>
                <el-tooltip content="使用剩余的免费门店名额抵用" placement="top" v-if="checkPermission(['b:merchant:copartnerDeductions'])">
                  <el-button
                    type="success"
                    size="mini"
                    icon="el-icon-switch-button"
                    v-if="scope.row.paymentIsComplete === '0' && operationsFreeSchoolNum > 0 && checkPermission(['b:merchant:OperationsVersion'])"
                    @click="allGone(scope.row, 3, false)"
                  >
                    账号开通抵扣
                  </el-button>
                </el-tooltip>
                <el-tooltip content="使用剩余的免费门店名额抵用" placement="top" v-if="checkPermission(['b:merchant:copartnerDRenew'])">
                  <el-button
                    type="success"
                    size="mini"
                    icon="el-icon-switch-button"
                    v-if="scope.row.schoolType === 3 && scope.row.paymentIsComplete !== '0' && operationsFreeSchoolNum > 0 && checkPermission(['b:merchant:OperationsVersion'])"
                    @click="allGone(scope.row, 3, true)"
                  >
                    账号开通抵扣续费
                  </el-button>
                </el-tooltip>

                <el-button
                  type="primary"
                  icon="el-icon-view"
                  @click="recharge(scope.row.merchantCode)"
                  size="mini"
                  v-if="scope.row.schoolType !== 3 && checkPermission(['b:merchant:areas:areasSchoolList:recharge'])"
                >
                  充值
                </el-button>
              </el-button-group>
            </el-col>
            <el-col :span="18">
              <el-button-group>
                <el-button
                  type="primary"
                  icon="el-icon-view"
                  v-if="roleTag != 'Agent' && checkPermission(['b:areas:areasSchoolListEdit:edit']) && checkPermission(['b:merchant:OperationsVersion'])"
                  @click="editSchool(scope.row.id)"
                  size="mini"
                >
                  编辑
                </el-button>
                <el-button
                  type="primary"
                  v-if="scope.row.schoolType !== 3 && checkPermission(['b:merchant:areasSchoolListInvert:invert'])"
                  icon="el-icon-view"
                  @click="convertToOperationsSchool(scope.row.merchantCode)"
                  size="mini"
                >
                  转为运营门店
                </el-button>
                <el-button
                  type="warning"
                  size="mini"
                  icon="el-icon-switch-button"
                  v-if="
                    checkPermission(['b:merchant:schoolList:open']) &&
                    ((scope.row.isEnable === 0 && scope.row.isCheck === 1) ||
                      (scope.row.isEnable === 0 && scope.row.flowEndStatus === 1) ||
                      scope.row.isEnable === -3 ||
                      scope.row.isEnable === -4)
                  "
                  @click="schoolStatus(scope.row.id, scope.row.isEnable, 1)"
                >
                  开通
                </el-button>

                <el-button
                  type="danger"
                  size="mini"
                  icon="el-icon-video-pause"
                  v-if="
                    checkPermission(['b:merchant:schoolList:pause']) &&
                    ((scope.row.isEnable === 1 && scope.row.isCheck === 1) || (scope.row.isEnable === 1 && scope.row.flowEndStatus === 1))
                  "
                  @click="schoolStatus(scope.row.id, scope.row.isEnable, 0)"
                >
                  暂停
                </el-button>
                <el-button
                  type="danger"
                  size="mini"
                  icon="el-icon-video-pause"
                  v-if="
                    checkPermission(['b:areas:areasSchoolListTermination:termination']) &&
                    ((scope.row.isEnable === 1 && scope.row.isCheck === 1) || (scope.row.isEnable === 1 && scope.row.flowEndStatus === 1))
                  "
                  @click="schoolStatus(scope.row.id, scope.row.isEnable, -3)"
                >
                  终止
                </el-button>
                <el-button
                  type="danger"
                  size="mini"
                  icon="el-icon-circle-check"
                  v-if="checkPermission(['b:risk:user:clear']) && (scope.row.isEnable === -1 || scope.row.isEnable === -2)"
                  @click="schoolStatus(scope.row.id, scope.row.isEnable, 1)"
                >
                  解封
                </el-button>
                <el-button
                  type="warning"
                  size="mini"
                  icon="el-icon-switch-button"
                  v-if="scope.row.isCheck == 3 && roleTag === 'admin' && (scope.row.companyCode === '' || scope.row.companyCode === undefined)"
                  @click="schoolCheck(scope.row.id)"
                >
                  审核门店
                </el-button>
                <el-button type="warning" size="mini" icon="el-icon-switch-button" v-if="scope.row.isCheck == 3 && roleTag === 'Company'" @click="schoolCheck(scope.row.id)">
                  审核门店
                </el-button>

                <el-button
                  v-if="checkPermission(['b:risk:user:deliverHistory1'])"
                  type="success"
                  size="mini"
                  @click="deliverHistory(scope.row, 1)"
                  :disabled="scope.row.belongDeliverCode == '' ? true : false"
                >
                  主交付中心指派历史
                </el-button>
                <el-button
                  v-if="checkPermission(['b:risk:user:deliverHistory2'])"
                  type="success"
                  size="mini"
                  @click="deliverHistory(scope.row, 0)"
                  :disabled="scope.row.spareBelongDeliverCode == '' ? true : false"
                >
                  备用交付中心指派历史
                </el-button>
                <!-- <el-button type="success" size="mini" @click="deliverHistory(scope.row)">交付中心指派历史</el-button> -->

                <el-button
                  type="warning"
                  size="mini"
                  icon="el-icon-link"
                  v-if="
                    false &&
                    checkPermission(['b:merchant:areasSchoolList:assignDelivery']) &&
                    ((scope.row.isOldData === 1 && scope.row.isCheck === 1) || (scope.row.isOldData === 0 && scope.row.flowEndStatus === 1))
                  "
                  @click="openAssignDelivery(scope.row.merchantCode)"
                >
                  指定交付中心
                </el-button>
                <el-button
                  type="warning"
                  size="mini"
                  icon="el-icon-link"
                  v-if="
                    false &&
                    checkPermission(['b:merchant:areasSchoolList:liftDelivery']) &&
                    ((scope.row.isOldData === 1 && scope.row.isCheck === 1) || (scope.row.isOldData === 0 && scope.row.flowEndStatus === 1))
                  "
                  @click="liftDelivery(scope.row.merchantCode)"
                >
                  解除交付中心
                </el-button>
              </el-button-group>
            </el-col>
          </el-row>
        </template>
      </el-table-column>
      <el-table-column prop="merchantCode" label="门店编号" width="130px"></el-table-column>
      <el-table-column prop="name" label="登录账号" width="130px" v-if="!isAdminManger"></el-table-column>
      <el-table-column prop="merchantName" label="门店名称" width="200px"></el-table-column>

      <el-table-column prop="address" v-if="!checkPermission(['b:merchant:OperationsVersion']) || checkPermission(['admin'])" label="所在地区" width="200px"></el-table-column>
      <!-- <el-table-column prop="refereeCode" label="上级编号" width="180"></el-table-column>-->
      <el-table-column prop="realName" label="负责人" show-overflow-tooltip></el-table-column>
      <el-table-column prop="belongDeliverCode" v-if="checkPermission(['b:risk:user:deliverHistory1'])" label="主交付中心编号" min-width="150" header-align="center" />
      <el-table-column prop="belongDeliverName" v-if="checkPermission(['b:risk:user:deliverHistory1'])" min-width="150" label="主交付中心名称" header-align="center">
        <template slot-scope="scope">
          <span v-if="scope.row.belongDeliverCode">
            {{ scope.row.belongDeliverName }}
            <i class="el-icon-edit" style="color: #409eff; margin-left: 10px" @click="editDelivery(scope.row, true, 1)"></i>
          </span>
          <el-button v-else type="primary" size="mini" @click="editDelivery(scope.row, false, 1)">指派</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="spareBelongDeliverCode" v-if="checkPermission(['b:risk:user:deliverHistory2'])" label="备用交付中心编号" min-width="150" header-align="center" />
      <el-table-column prop="spareBelongDeliverName" v-if="checkPermission(['b:risk:user:deliverHistory2'])" min-width="150" label="备用交付中心名称" header-align="center">
        <template slot-scope="scope">
          <span v-if="scope.row.spareBelongDeliverName">
            {{ scope.row.spareBelongDeliverName }}
            <i class="el-icon-edit" style="color: #409eff; margin-left: 10px" @click="editDelivery(scope.row, true, 0)"></i>
          </span>
          <el-button v-else type="primary" size="mini" @click="editDelivery(scope.row, false, 0)">指派</el-button>
        </template>
      </el-table-column>
      <el-table-column label="累计充值学时（节）" v-if="!checkPermission(['b:merchant:OperationsVersion']) || checkPermission(['admin'])" width="180px">
        <template slot-scope="scope">
          <span style="margin-left: 10px">{{ scope.row.schoolType === 3 ? '/' : scope.row.totalCourseHours }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="saleCourseHours" label="已售学时（节）" width="150px"></el-table-column>
      <el-table-column prop="haveCourseHours" label="剩余学时（节）" v-if="!checkPermission(['b:merchant:OperationsVersion']) || checkPermission(['admin'])" width="150px">
        <template slot-scope="scope">
          <span style="margin-left: 10px">{{ scope.row.schoolType == 3 ? '/' : scope.row.haveCourseHours }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="operationsName" label="所属俱乐部" v-if="checkPermission(['b:merchant:OperationsVersion'])" width="150px"></el-table-column>
      <el-table-column v-if="!checkPermission(['b:merchant:OperationsVersion'])" prop="withdrawMoney" label="账户余额（元）" width="150px"></el-table-column>
      <el-table-column prop="deliveryCenterCode" label="所属交付中心" v-if="checkPermission(['b:risk:user:deliverHistory1'])" width="100px"></el-table-column>
      <el-table-column prop="isEnable" label="账户状态" v-if="checkPermission(['b:merchant:accountStatus:table'])" width="100px">
        <template slot-scope="scope">
          <span v-if="scope.row.isEnable === -1 && scope.row.paymentIsComplete === '1'" class="red">系统关闭</span>
          <span v-else-if="scope.row.expireStatus === 1 && isExpireDate(scope.row.expireDate)" class="red">到期未续费</span>
          <span v-else-if="scope.row.isEnable === 1" class="green">开通</span>
          <span v-else-if="scope.row.isEnable === -2" class="red">年审关闭</span>
          <span v-else-if="scope.row.isEnable === -3" class="red">终止</span>
          <span v-else-if="scope.row.isEnable === -1 && scope.row.paymentIsComplete === '0'" class="red">等待完款</span>
          <span v-else class="red">暂停</span>
        </template>
      </el-table-column>
      <el-table-column prop="we" label="培训是否缴费" v-if="isNeedPay">
        <template slot-scope="scope">{{ scope.row.isPay == 0 ? '未缴费' : scope.row.isPay == 1 ? '已缴费' : '无需缴费' }}</template>
      </el-table-column>
      <el-table-column prop="channelManagerName" label="渠道管理员" align="center" v-if="checkPermission(['b:risk:user:channelManagerName'])">
        <template slot-scope="scope">
          <span>{{ scope.row.channelManagerName ? scope.row.channelManagerName : '' }}</span>
          <el-link v-if="scope.row.channelManagerName" type="primary" @click="showChannelManagerDetail(scope.row)">详情</el-link>
        </template>
      </el-table-column>
      <el-table-column prop="regTime" label="添加时间" width="150px" show-overflow-tooltip></el-table-column>
      <el-table-column prop="expireDate" v-if="checkPermission(['b:merchant:OperationsVersion'])" width="150px" label="到期时间" show-overflow-tooltip></el-table-column>
      <el-table-column prop="schoolType" width="150px" label="门店类型">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.schoolType === 1">直营门店</el-tag>
          <el-tag v-if="scope.row.schoolType === 2">加盟门店</el-tag>
          <el-tag v-if="scope.row.schoolType === 3">超级俱乐部门店</el-tag>
        </template>
      </el-table-column>
    </el-table>

    <!--弹出窗口：指派交付中心-->
    <el-dialog :title="textMap[dialogStatu]" :visible.sync="dialogFormDelivery" width="40%" :close-on-click-modal="false" @close="dialogFormDelivery = false">
      <template>
        <el-input placeholder="请输入编号" style="width: 40%" v-model="searchMerchantCode" clearable></el-input>
        <el-button type="primary" mini style="margin-left: 10px" title="搜索" @click="searchChannel()">搜索</el-button>

        <el-table :data="tableDataDelivery" v-loading="tableLoading2" highlight-current-row ref="singleTable" height="550" border style="width: 100%">
          <el-table-column type="index" width="50"></el-table-column>
          <el-table-column sortable prop="merchantCode" label="编号" width="180"></el-table-column>
          <el-table-column sortable prop="merchantName" label="名称" width="180"></el-table-column>
          <el-table-column label="操作">
            <template slot-scope="scope">
              <el-button type="primary" mini title="指派至该交付中心" @click="assignDelivery(scope.row.merchantCode)">指派</el-button>
            </template>
          </el-table-column>
          <el-table-column sortable prop="regTime" label="添加时间"></el-table-column>
        </el-table>
      </template>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormDelivery = false">取消</el-button>
      </div>
    </el-dialog>

    <!-- 添加或修改弹窗 -->
    <el-dialog :title="addOrUpdate ? '添加托管中心' : '修改门店'" :visible.sync="dialogVisible" width="70%" :close-on-click-modal="false">
      <el-form
        :ref="addOrUpdate ? 'addMarketDate' : 'updateMarketDate'"
        :rules="rules"
        :model="addOrUpdate ? addMarketDate : updateMarketDate"
        label-position="left"
        label-width="150px"
      >
        <el-form-item label="登录账号：" prop="name">
          <el-col :span="18">
            <el-input v-if="updateMarketDate" readonly="readonly" v-model="updateMarketDate.name" />
          </el-col>
          <el-button type="success" style="margin-left: 20px" @click="openLogin">修改登录账号</el-button>
        </el-form-item>
        <el-form-item label="门店名称：" prop="merchantName">
          <el-input v-if="updateMarketDate" v-model="updateMarketDate.merchantName" />
        </el-form-item>
        <el-form-item label="负责人：" prop="realName">
          <el-input v-if="updateMarketDate" v-model="updateMarketDate.realName" />
        </el-form-item>
        <el-form-item label="身份证：" prop="idCard">
          <el-input v-if="updateMarketDate" v-model="updateMarketDate.idCard" />
        </el-form-item>
        <el-form-item label="上级编号：" prop="refereeCode">
          <el-input v-if="updateMarketDate" v-model="updateMarketDate.refereeCode" />
        </el-form-item>
        <el-form-item label="推荐人：" prop="merchantName">
          <el-input v-if="updateMarketDate" v-model="updateMarketDate.marketPartner" />
        </el-form-item>
        <el-form-item label="是否教学交付能力：" prop="canTeachNum">
          <template>
            <el-radio v-model="radio" label="1" @change="change(radio)">开通</el-radio>
            <el-radio v-model="radio" label="0" @change="change(radio)">暂停</el-radio>
          </template>
        </el-form-item>
        <el-form-item label="签约时间：" prop="">
          <el-date-picker v-model="updateMarketDate.signupDate" value-format="yyyy-MM-dd HH:mm:ss" type="datetime" placeholder="选择日期"></el-date-picker>
        </el-form-item>
        <el-form-item label="到期时间：" prop="">
          <el-date-picker v-model="updateMarketDate.expireDate" value-format="yyyy-MM-dd HH:mm:ss" type="datetime" placeholder="选择日期"></el-date-picker>
        </el-form-item>
        <el-form-item label="合同照片:">
          <el-col :span="20">
            <el-upload
              ref="clearupload"
              v-loading="uploadLoading"
              list-type="picture-card"
              action=""
              element-loading-text="图片上传中"
              :limit="10"
              :on-exceed="justPictureNum"
              :file-list="!addOrUpdate ? fileContractDetailList : fileContractDetailList.name"
              :http-request="uploadDetailHttpContract"
              :on-preview="handlePictureCardPreviewContract"
              :on-remove="handleRemoveDetailContract"
            >
              <i class="el-icon-plus" />
            </el-upload>
          </el-col>
          <el-col :span="4">(*支持多张)</el-col>
        </el-form-item>
        <el-form-item label="证件照片:">
          <el-col :span="20">
            <el-upload
              ref="clearupload"
              v-loading="uploadLoadingIdCard"
              list-type="picture-card"
              action=""
              element-loading-text="图片上传中"
              :limit="10"
              :on-exceed="justPictureNum"
              :file-list="!addOrUpdate ? fileIdCardDetailList : fileIdCardDetailList.name"
              :http-request="uploadDetailHttpIdCard"
              :on-preview="handlePictureCardPreviewIdCard"
              :on-remove="handleRemoveDetailIdCard"
            >
              <i class="el-icon-plus" />
            </el-upload>
          </el-col>
          <el-col :span="4">(*支持多张)</el-col>
        </el-form-item>
        <el-form-item label="门店环境照片:">
          <el-col :span="20">
            <el-upload
              ref="clearupload"
              v-loading="uploadLoadingShop"
              list-type="picture-card"
              action=""
              element-loading-text="图片上传中"
              :limit="10"
              :on-exceed="justPictureNum"
              :file-list="!addOrUpdate ? fileShopDetailList : fileShopDetailList.name"
              :http-request="uploadDetailHttpShop"
              :on-preview="handlePictureCardPreviewShop"
              :on-remove="handleRemoveDetailShop"
            >
              <i class="el-icon-plus" />
            </el-upload>
          </el-col>
          <el-col :span="4">(*支持多张)</el-col>
        </el-form-item>
        <el-form-item label="所在地区：" prop="name">
          <el-row :gutter="10">
            <el-col :span="7">
              <el-input v-if="updateMarketDate" v-model="updateMarketDate.province" />
            </el-col>
            <el-col :span="7">
              <el-input v-if="updateMarketDate" v-model="updateMarketDate.city" />
            </el-col>
            <el-col :span="7">
              <el-input v-if="updateMarketDate" v-model="updateMarketDate.area" />
            </el-col>
          </el-row>
        </el-form-item>
        <el-form-item label="地址：" prop="address">
          <el-col :span="12">
            <el-input v-if="updateMarketDate" v-model="updateMarketDate.address" />
          </el-col>
          <el-col :span="11" :offset="1">
            <el-button type="success" size="mini">确定</el-button>
            <el-button type="info" size="mini" @click="clearAddress()">清空</el-button>
            <span>(请在此输入详细地址！)</span>
          </el-col>
        </el-form-item>
        <el-form-item label="地图标记" prop="isEnable">
          <div class="amap-page-container">
            <div :style="{ width: '50%', height: '300px' }">
              <el-amap-search-box class="search-box" :search-option="searchOption" :on-search-result="onSearchResult"></el-amap-search-box>
              <el-amap vid="amap" :plugin="plugin" :center="center" class="amap-demo" :events="events">
                <el-amap-circle
                  v-for="(circle, index) in circles"
                  :key="index + '-only'"
                  :center="circle.center"
                  :radius="circle.radius"
                  :fill-opacity="circle.fillOpacity"
                  :events="circle.events"
                ></el-amap-circle>
                <el-amap-marker v-for="(marker, index) in markers" :position="marker" :key="index + '-only'"></el-amap-marker>
              </el-amap>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="所在经度" prop="longitude" v-show="false">
          <el-col :span="7">
            <el-input v-if="addOrUpdate" v-model="addMarketDate.longitude" />
            <el-input v-if="!addOrUpdate" v-model="updateMarketDate.longitude" />
          </el-col>
        </el-form-item>
        <el-form-item label="所在维度" prop="latitude" v-show="false">
          <el-col :span="7">
            <el-input v-if="addOrUpdate" v-model="addMarketDate.latitude" />
            <el-input v-if="!addOrUpdate" v-model="updateMarketDate.latitude" />
          </el-col>
        </el-form-item>
        <el-form-item label="开户金额" prop="openMoney" v-show="false">
          <el-col :span="7">
            <el-input v-if="addOrUpdate" v-model="addMarketDate.openMoney" />
            <el-input v-if="!addOrUpdate" v-model="updateMarketDate.openMoney" />
          </el-col>
        </el-form-item>
        <el-form-item label="门店简介：" prop="name">
          <el-input type="textarea" resize="none" :rows="4" v-if="updateMarketDate" v-model="updateMarketDate.description" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button v-if="updateMarketDate" size="mini" type="primary" @click="updateActiveFun('updateMarketDate')">修改</el-button>
        <el-button size="mini">关闭</el-button>
      </div>
    </el-dialog>

    <!--渠道管理员信息-->
    <el-dialog title="渠道管理员信息" :visible.sync="dialogVisibleForChannelManager" width="30%">
      <el-form ref="temp" label-position="left" label-width="120px">
        <el-form-item label="名称: ">
          <el-input disabled v-model="channelManager.realName" placeholder="请输入内容"></el-input>
        </el-form-item>
        <el-form-item label="编号: ">
          <el-input disabled v-model="channelManager.channelManagerCode" placeholder="请输入内容"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisibleForChannelManager = false">关 闭</el-button>
      </span>
    </el-dialog>

    <!-- 修改登陆账号弹窗 -->
    <el-dialog title="修改登录账号" :visible.sync="showLoginAccount" width="70%" :close-on-click-modal="false">
      <el-form
        :ref="addOrUpdate ? 'addMarketDate' : 'updateMarketDate'"
        :rules="rules"
        :model="addOrUpdate ? addMarketDate : updateLoginName"
        label-position="left"
        label-width="120px"
        style="width: 80%"
      >
        <el-form-item label="原登录账号：" prop="name">
          <el-input v-if="updateLoginName" v-model="updateLoginName.name" />
        </el-form-item>
        <el-form-item label="新登录账号：" prop="merchantName">
          <el-input v-if="updateLoginName" v-model="updateLoginName.newName" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="updateSchoolLogin()">确定</el-button>
      </div>
    </el-dialog>

    <!-- 分页 -->
    <el-col :span="20">
      <el-pagination
        :current-page="tablePage.currentPage"
        :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="tablePage.totalItems"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-col>

    <el-dialog :visible.sync="dialogUploadVisible" :close-on-click-modal="false">
      <img width="100%" :src="dialogImageUrl" alt />
    </el-dialog>

    <!-- 交付中心指派 -->
    <el-dialog title="指派交付中心" :visible.sync="dialogPopup" width="30%" center :close-on-click-modal="false" :before-close="closeDialog">
      <el-form :rules="rules" ref="form" :model="form" label-width="180px">
        <el-form-item label="交付中心名称：" prop="deliverMerchantCode" v-if="!deliverStatus">
          <el-select v-model="form.deliverMerchantCode" placeholder="请选择" filterable>
            <el-option v-for="item in deliverCenterList" :key="item.merchantCode" :label="item.merchantName" :value="item.merchantCode"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="当前交付中心名称：" prop="deliverName" v-if="deliverStatus">
          <el-input style="width: 58%" v-model="deliverName" disabled></el-input>
        </el-form-item>
        <el-form-item label="重新指派交付中心名称：" prop="deliverMerchantCode" v-if="deliverStatus">
          <el-select v-model="form.deliverMerchantCode" placeholder="请选择" filterable>
            <el-option v-for="item in deliverCenterList" :key="item.merchantCode" :label="item.merchantName" :value="item.merchantCode"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <div style="margin-top: 50px">
            <el-button @click="closeDialog">取 消</el-button>
            <el-button type="primary" @click="onSubmit">确 定</el-button>
          </div>
        </el-form-item>
      </el-form>
    </el-dialog>

    <!-- 交付中心指派历史 -->
    <el-dialog :title="form.isMain == 1 ? '主交付中心指派历史' : '备用交付中心指派历史'" :visible.sync="dialogHistory" width="30%" center>
      <div>
        <ul class="infinite-list" style="overflow: auto; margin: 30px 0; height: 400px">
          <el-steps direction="vertical" :active="0">
            <!-- <el-step v-for="(item, index) in assignmentList" :key="index" :title="item.createTime"
              icon="iconfont icon-luyin"
              :description="item.lastDeliverMerchantName + '指派交付中心为' + item.thisDeliverMerchantName"></el-step> -->
            <el-step v-for="(item, index) in assignmentList" :key="index" :title="item.createTime" icon="iconfont icon-luyin" :description="item.thisDeliverMerchantName"></el-step>
          </el-steps>
        </ul>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogHistory = false">取 消</el-button>
        <el-button type="primary" @click="dialogHistory = false">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
  import { mapGetters } from 'vuex';
  import schoolApi from '@/api/schoolList';
  import areaSchoolApi from '@/api/areasSchoolList';
  import { ossPrClient } from '@/api/alibaba';
  import { dxSource, pageParamNames } from '@/utils/constants';
  import ls from '@/api/sessionStorage';
  import authenticationApi from '@/api/authentication';
  import checkPermission from '@/utils/permission';
  import deliveryCenterApi from '@/api/delivery/deliveryCenter';
  import { Base64 } from '@/utils/base64';
  import store from '@/store';
  import schoolList from '@/api/schoolList';

  export default {
    name: 'schoolList',
    computed: {
      ...mapGetters(['setpayUrl']),
      isNeedPay() {
        return checkPermission(['admin', 'Operations', 'School']) && this.isPayOpen;
      }
    },
    data() {
      const self = this;
      return {
        currentAdmin: '',
        operationsFreeSchoolNum: window.localStorage.getItem('operationsFreeSchoolNum'),
        token: store.getters.token,
        setpayUrl: store.getters.setpayUrl,
        channelManager: {},
        dialogVisibleForChannelManager: false,
        searchMerchantCode: '',
        branchOfficeMerchantCode: '',
        dialogFormDelivery: false,
        textMap: {
          assign: '指定交付中心'
        },
        tableDataDelivery: [],
        tableLoading2: false,
        dialogStatu: 'assign',
        addOrUpdate: false,
        tableLoading: false,
        roleTag: '',
        // 分页
        tablePage: {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null
        },

        regTime: [],
        tableData: [],
        dataQuery: {
          merchantCode: '',
          name: '',
          merchantName: '',
          refereeCode: '',
          marketPartner: '',
          realName: '',
          address: '',
          isEnable: '',
          schoolType: '',
          paymentIsComplete: ''
        },
        dialogVisible: false,
        exportLoading: false,
        updateMarketDate: {},
        addMarketDate: {},
        showLoginAccount: false,
        radio: '0', //单选框状态 值必须是字符串
        fileListPending: [], // 待处理已上传图片信息
        fileDetailListPending: [], // 待处理已上传图片信息
        fileContractDetailList: [], // 上传图片已有图片列表
        fileIdCardDetailList: [],
        fileShopDetailList: [],

        dialogUploadVisible: false,
        uploadLoading: false, // 上传图片加载按钮
        dialogImageUrl: '', // 上传图片预览
        fileList: [], // 上传图片已有图片列表
        uploadLoadingIdCard: false,
        uploadLoadingShop: false,
        updateLoginName: {},

        //地图开始
        markers: [
          [121.59996, 31.197646],
          [121.40018, 31.197622],
          [121.69991, 31.207649]
        ],
        searchOption: {
          city: '上海',
          citylimit: true
        },
        center: [121.59996, 31.197646],
        zoom: 12,
        lng: 0,
        lat: 0,
        address: '',
        province: '',
        city: '',
        district: '',
        loaded: false,
        circles: [
          {
            center: [117.26696, 31.87869],
            radius: 100,
            fillOpacity: 0.5,
            events: {
              click(e) {
                //alert(e.lnglat);
                let { lng, lat } = e.lnglat;
                self.lng = lng;
                self.lat = lat;
                console.log(e);
                // 这里通过高德 SDK 完成。
                var geocoder = new AMap.Geocoder({
                  radius: 1000,
                  extensions: 'all'
                });
                geocoder.getAddress([e.lnglat.lng, e.lnglat.lat], function (status, result) {
                  if (status === 'complete' && result.info === 'OK') {
                    if (result && result.regeocode) {
                      if (self.addOrUpdate) {
                        // 具体地址
                        self.addMarketDate.longitude = lng;
                        self.addMarketDate.latitude = lat;

                        self.addMarketDate.address = result.regeocode.formattedAddress;
                        // 省
                        self.addMarketDate.province = result.regeocode.addressComponent.province;
                        // 市
                        self.addMarketDate.city = result.regeocode.addressComponent.city;
                        // 区
                        self.addMarketDate.area = result.regeocode.addressComponent.district;
                      } else {
                        self.updateMarketDate.latitude = lat;
                        self.updateMarketDate.longitude = lng;
                        self.updateMarketDate.address = result.regeocode.formattedAddress;
                        // 省
                        self.updateMarketDate.province = result.regeocode.addressComponent.province;
                        // 市
                        self.updateMarketDate.city = result.regeocode.addressComponent.city;
                        // 区
                        self.updateMarketDate.area = result.regeocode.addressComponent.district;
                      }
                      self.$nextTick();
                    }
                  } else {
                    //alert('地址获取失败')
                  }
                });
              }
            }
          }
        ],
        events: {
          click(e) {
            //alert(e.lnglat);
            let { lng, lat } = e.lnglat;
            self.lng = lng;
            self.lat = lat;
            console.log(e);
            // 这里通过高德 SDK 完成。
            var geocoder = new AMap.Geocoder({
              radius: 1000,
              extensions: 'all'
            });
            geocoder.getAddress([e.lnglat.lng, e.lnglat.lat], function (status, result) {
              if (status === 'complete' && result.info === 'OK') {
                if (result && result.regeocode) {
                  if (self.addOrUpdate) {
                    // 具体地址
                    self.addMarketDate.longitude = lng;
                    self.addMarketDate.latitude = lat;

                    self.addMarketDate.address = result.regeocode.formattedAddress;
                    // 省
                    self.addMarketDate.province = result.regeocode.addressComponent.province;
                    // 市
                    self.addMarketDate.city = result.regeocode.addressComponent.city;
                    // 区
                    self.addMarketDate.area = result.regeocode.addressComponent.district;
                  } else {
                    self.updateMarketDate.latitude = lat;
                    self.updateMarketDate.longitude = lng;
                    self.updateMarketDate.address = result.regeocode.formattedAddress;
                    // 省
                    self.updateMarketDate.province = result.regeocode.addressComponent.province;
                    // 市
                    self.updateMarketDate.city = result.regeocode.addressComponent.city;
                    // 区
                    self.updateMarketDate.area = result.regeocode.addressComponent.district;
                  }
                  self.$nextTick();
                }
              } else {
                //alert('地址获取失败')
              }
            });
          }
        },
        isPayOpen: true,
        plugin: [
          {
            enableHighAccuracy: true, //是否使用高精度定位，默认:true
            timeout: 100, //超过10秒后停止定位，默认：无穷大
            maximumAge: 0, //定位结果缓存0毫秒，默认：0
            convert: true, //自动偏移坐标，偏移后的坐标为高德坐标，默认：true
            showButton: true, //显示定位按钮，默认：true
            buttonPosition: 'RB', //定位按钮停靠位置，默认：'LB'，左下角
            showMarker: true, //定位成功后在定位到的位置显示点标记，默认：true
            showCircle: true, //定位成功后用圆圈表示定位精度范围，默认：true
            panToLocation: true, //定位成功后将定位到的位置作为地图中心点，默认：true
            zoomToAccuracy: true, //定位成功后调整地图视野范围使定位位置及精度范围视野内可见，默认：f
            extensions: 'all',
            expandZoomRange: true,
            keyboardEnable: true,
            pName: 'Geolocation',
            campus: [],
            events: {
              init(o) {
                // o 是高德地图定位插件实例
                o.getCurrentPosition((status, result) => {
                  console.log(result);
                  if (result && result.position) {
                    self.lng = result.position.lng;
                    self.lat = result.position.lat;

                    self.center = [result.position.lng, result.position.lat];
                    self.loaded = true;
                    self.$nextTick();
                  }
                });
              }
            }
          }
        ],

        isAdmin: false,
        isAdminManger: false,
        dialogPopup: false, // 交付中心指派弹窗
        deliverName: '', // 交付中心指派名称
        dialogHistory: false, // 交付中心指派历史弹窗
        deliverStatus: false, // 交付中心指派状态 false未指派

        form: {
          merchantCode: '',
          deliverMerchantCode: '',
          isMain: 1
        },

        deliverCenterList: {}, // 可指派交付中心列表
        rules: {
          deliverMerchantCode: [{ required: true, message: '请选择交付中心', trigger: 'blur' }]
        },

        assignmentList: {}, // 指派交付中心历史
        options: [
          { value: '0', label: '未指派' },
          { value: '1', label: '指派' }
        ]
      };
    },
    created() {
      // 获取当前时间，如果接口开放，则培训缴费相关功能放开
      schoolList.getTrainingOpenTime().then((res) => {
        const openTime = res.data;
        // const isPayOpen = this.dynamicFunction(openTime);
        this.isPayOpen = openTime == 'Y' ? true : false;
      });
      this.fetchData();
      ossPrClient();
      let role = localStorage.getItem('roleTag');
      console.log(role, '=====================');
      this.isAdminManger = role == 'HeadquartersStoreManagement';
      this.isAdmin = localStorage.getItem('roleTag') === 'admin' || 'HeadquartersStoreManagement';
      // this.initData();
    },

    activated() {
      this.fetchData();
    },
    mounted() {
      authenticationApi.checkAccountBalance().then((res) => {
        this.roleTag = res.data.data.roleTag;
      });
    },
    methods: {
      checkPermission,
      //判断时间是否到期
      isExpireDate(date) {
        if (!date) {
          return false;
        }
        let data = (new Date(date) * 1000) / 1000;
        let now = Date.now();
        console.log(data, now);

        if (data - now > 0) {
          return false;
        } else {
          return true;
        }
      },
      exportSchool() {
        const that = this;
        that.exportLoading = true;
        schoolApi.exportSchool(that.dataQuery).then((response) => {
          console.log(response);
          if (!response) {
            this.$notify.error({
              title: '操作失败',
              message: '文件下载失败'
            });
          }
          const url = window.URL.createObjectURL(response);
          const link = document.createElement('a');
          link.style.display = 'none';
          link.href = url; //获取服务器端的文件名
          link.setAttribute('download', '门店表.xls');
          document.body.appendChild(link);
          link.click();
          that.exportLoading = false;
        });
      },
      getRoleTag() {
        schoolList.getCurrentAdmin().then((res) => {
          this.currentAdmin = res.data;
        });
      },
      showChannelManagerDetail(row) {
        this.channelManager.realName = row.channelManagerRealName;
        this.channelManager.channelManagerCode = row.channelManagerCode;
        this.dialogVisibleForChannelManager = true;
      },
      //打开指定交付中心窗口
      openAssignDelivery(merchantCode) {
        this.dialogFormDelivery = true;
        this.branchOfficeMerchantCode = merchantCode;
        //查询所有交付中心
        deliveryCenterApi
          .allList()
          .then((res) => {
            this.tableDataDelivery = res.data;
            console.log(this.tableDataDelivery, 'this.tableDataDelivery');
          })
          .catch((err) => {});
      },
      //表格数据选中和跳转到指定位置
      searchChannel() {
        for (let i = 0; i < this.tableDataDelivery.length; i++) {
          if (this.tableDataDelivery[i].merchantCode === this.searchMerchantCode) {
            if (!this.$refs['singleTable']) return; //不存在这个表格则返回
            let elTable = this.$refs['singleTable'].$el;
            if (!elTable) return;
            const scrollParent = elTable.querySelector('.el-table__body-wrapper');
            const targetTop = elTable.querySelectorAll('.el-table__body tr')[i].getBoundingClientRect().top; //该行的位置
            const containerTop = elTable.querySelector('.el-table__body').getBoundingClientRect().top; //body的位置
            //跳转
            scrollParent.scrollTop = targetTop - containerTop; //跳转到存下编辑行的ScrollTop
            this.$refs.singleTable.setCurrentRow(this.tableDataDelivery[i]);
          }
        }
      },
      //1 全额  2 成本  3免费
      allGone(item, goneType, renew) {
        if (!renew && item.isPay == 0) {
          schoolList.getTrainingPayInfo(item.merchantCode).then((res) => {
            const split = dxSource.split('##');
            res.data.dxSource = res.data.registerCallIInfo.appSource + '##' + split[1] + '##' + split[2];
            let params = JSON.stringify(res.data);
            let req = 'token=' + this.token + '&params=' + params + '&back=' + window.location.href;
            //需要编码两遍，避免出现+号等
            var encode = Base64.encode(Base64.encode(req));
            window.open(this.setpayUrl + 'product?' + encode, '_blank');
          });
        } else {
          if (goneType === 3) {
            this.$confirm('此操作将消耗您一个免费名额给该合伙人开通或续费一年, 是否继续?', '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            })
              .then(() => {
                areaSchoolApi.allGone(item.id, goneType, renew).then((res) => {
                  if (renew) {
                    this.$message.success('操作成功');
                    this.fetchData();
                  } else {
                    this.$message.success('操作成功');
                    this.fetchData();
                  }
                });
              })
              .catch((e) => {
                this.$message({
                  type: 'info',
                  message: e
                });
              });
            return;
          }
          areaSchoolApi.allGone(item.id, goneType, renew).then((res) => {
            if (checkPermission(['b:merchant:OperationsVersion'])) {
              const split = dxSource.split('##');
              res.data.dxSource = res.data.registerCallIInfo.appSource + '##' + split[1] + '##' + split[2];
              let params = JSON.stringify(res.data);
              let req = 'token=' + this.token + '&params=' + params + '&back=' + window.location.href;
              //需要编码两遍，避免出现+号等
              var encode = Base64.encode(Base64.encode(req));

              window.open(this.setpayUrl + 'product?' + encode, '_blank');
              // window.open("http://192.168.5.35:8000/product?" + encode, "_blank");
            } else {
              this.$message.success('操作成功');
              this.fetchData();
            }
          });
        }
      },
      assignDelivery(merchantCode) {
        this.tableLoading2 = true;
        deliveryCenterApi
          .assignDelivery(this.branchOfficeMerchantCode, merchantCode)
          .then((res) => {
            this.tableLoading2 = false;
            this.$message.success('指派成功');
            this.dialogFormDelivery = false;
            this.fetchData();
          })
          .catch((err) => {});
      },
      liftDelivery(merchantCode) {
        this.$confirm('解除通过该账户绑定的交付中心?')
          .then((_) => {
            deliveryCenterApi
              .liftDelivery(merchantCode)
              .then((res) => {
                this.$message.success('解除绑定成功');
                this.fetchData();
              })
              .catch((err) => {});
          })
          .catch((_) => {});
      },
      editSchool(id) {
        const that = this;
        ls.setItem('schoolId', id);
        that.$router.push({
          path: '/merchantManagement/schoolEdit',
          query: {
            id: id
          }
        });
      },
      //重置
      rest() {
        this.$refs.dataQuery.resetFields();
        this.regTime = '';
        this.fetchData01();
      },
      fetchData01() {
        this.tablePage = {
          currentPage: 1,
          size: this.tablePage.size,
          totalPage: null,
          totalItems: null
        };
        this.fetchData();
      },
      // 查询提现列表
      fetchData() {
        const that = this;
        that.tableLoading = true;
        var a = that.regTime;
        if (a != null) {
          that.dataQuery.startRegTime = a[0];
          that.dataQuery.endRegTime = a[1];
        } else {
          that.dataQuery.startRegTime = '';
          that.dataQuery.endRegTime = '';
        }
        schoolApi.schoolPage(that.tablePage.currentPage, that.tablePage.size, that.dataQuery).then((res) => {
          that.tableData = res.data.data;
          that.tableLoading = false;
          // 设置后台返回的分页参数
          pageParamNames.forEach((name) => that.$set(that.tablePage, name, parseInt(res.data[name])));
        });
      },

      clearAddress() {
        const that = this;
        that.updateMarketDate.address = '';
      },
      convertToOperationsSchool(merchantCode) {
        const that = this;
        this.$confirm('将门店' + merchantCode + '转为超级俱乐部门店吗', '转换门店', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          schoolApi.convertToOperationsSchool(merchantCode).then((res) => {
            if (!res.success) {
              that.$message.error(res.message);
              return;
            }
            that.$nextTick(() => that.fetchData());
            that.$message.success(res.message);
          });
        });
      },
      schoolStatus(id, isEnable, enable) {
        // return console.log(id, isEnable, enable)
        const that = this;
        this.$confirm('确定操作吗?', '修改状态', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          schoolApi.openEnable(id, isEnable, enable).then((res) => {
            if (!res.success) {
              that.$message.error(res.message);
              return;
            }
            that.$nextTick(() => that.fetchData());
            that.$message.success(res.message);
          });
        });
      },
      // 状态改变事件
      change(radio) {
        if (radio == '1') {
          this.updateMarketDate.canTeach = true;
        } else {
          this.updateMarketDate.canTeach = false;
        }
      },
      //修改操作
      updateActiveFun(ele) {
        const that = this;
        if (that.fileContractDetailList.length <= 0) {
          that.$message.error('合同照片不能为空');
          return false;
        }
        if (that.fileIdCardDetailList.length <= 0) {
          that.$message.error('证件照片不能为空');
          return false;
        }
        if (!that.updateMarketDate.signupDate) {
          that.$message.error('签约时间不能为空');
          return false;
        }

        /*  for(let i=0;i<that.fileContractDetailList.length;i++){
          console.log( "!!!:"+that.fileContractDetailList[i].url);
        }*/

        that.updateMarketDate.contractPhoto = [];
        for (let i = 0; i < that.fileContractDetailList.length; i++) {
          let index = that.fileContractDetailList[i].url.lastIndexOf('manage');
          that.updateMarketDate.contractPhoto.push(that.fileContractDetailList[i].url.substring(index, that.fileContractDetailList[i].url.length));
        }
        that.updateMarketDate.idCardPhoto = [];

        for (var i = 0; i < that.fileIdCardDetailList.length; i++) {
          let idCardIndex = that.fileIdCardDetailList[i].url.lastIndexOf('manage');
          that.updateMarketDate.idCardPhoto.push(that.fileIdCardDetailList[i].url.substring(idCardIndex, that.fileIdCardDetailList[i].url.length));
        }
        that.updateMarketDate.shopPhoto = [];
        for (var i = 0; i < that.fileShopDetailList.length; i++) {
          let shopindex = that.fileShopDetailList[i].url.lastIndexOf('manage');
          that.updateMarketDate.shopPhoto.push(that.fileShopDetailList[i].url.substring(shopindex, that.fileShopDetailList[i].url.length));
        }

        that.$refs[ele].validate((valid) => {
          // 表单验证
          if (valid) {
            const loading = this.$loading({
              lock: true,
              text: '修改校区',
              spinner: 'el-icon-loading',
              background: 'rgba(0, 0, 0, 0.7)'
            });
            schoolApi.addSchool(that.updateMarketDate).then((res) => {
              loading.close();
              that.$nextTick(() => that.fetchData());
              that.$message.success(res.message);
            });
          } else {
            console.log('error submit!!');
            //loading.close();
            return false;
          }
        });
      },

      clickAdd(id) {
        this.dialogVisible = true;
        const that = this;
        that.fileContractDetailList = [];
        that.fileIdCardDetailList = [];
        that.fileShopDetailList = [];

        schoolApi.schoolDetail(id).then((res) => {
          that.updateMarketDate = res.data;
          that.radio = res.data.canteachNum;
          that.updateLoginName.name = res.data.name;
          that.updateLoginName.id = res.data.id;
          if (that.updateMarketDate.contractPhoto) {
            for (let i = 0; i < that.updateMarketDate.contractPhoto.length; i++) {
              that.fileContractDetailList.push({
                url: that.aliUrl + that.updateMarketDate.contractPhoto[i]
              });
            }
          } else {
            that.fileContractDetailList = [];
          }
          if (that.updateMarketDate.idCardPhoto) {
            for (let i = 0; i < that.updateMarketDate.idCardPhoto.length; i++) {
              that.fileIdCardDetailList.push({
                url: that.aliUrl + that.updateMarketDate.idCardPhoto[i]
              });
            }

            /*that.fileIdCardDetailList = [{
            url: that.aliUrl + that.updateMarketDate.idCardPhoto
          }]*/
          } else {
            that.fileIdCardDetailList = [];
          }

          if (that.updateMarketDate.shopPhoto) {
            for (let i = 0; i < that.updateMarketDate.shopPhoto.length; i++) {
              that.fileShopDetailList.push({
                url: that.aliUrl + that.updateMarketDate.shopPhoto[i]
              });
            }

            /*   that.fileShopDetailList = [{
               url: that.aliUrl + that.updateMarketDate.shopPhoto
             }]*/
          } else {
            that.fileShopDetailList = [];
          }
        });
      },
      schoolCheck(id) {
        const that = this;
        window.localStorage.setItem('schoolId', id);
        that.$router.push({
          path: '/merchantManagement/schoolCheck',
          query: {
            id: id
          }
        });
      },

      onSearchResult(pois) {
        let latSum = 0;
        let lngSum = 0;
        if (pois.length > 0) {
          pois.forEach((poi) => {
            let { lng, lat } = poi;
            lngSum += lng;
            latSum += lat;
            this.markers.push([poi.lng, poi.lat]);
          });
          let center = {
            lng: lngSum / pois.length,
            lat: latSum / pois.length
          };
          this.center = [center.lng, center.lat];
        }
      },
      addMarker: function () {
        let lng = 121.5 + Math.round(Math.random() * 1000) / 10000;
        let lat = 31.197646 + Math.round(Math.random() * 500) / 10000;
        this.markers.push([lng, lat]);
      },
      /*    //获取托管中心级别列表
        getSelectResultList() {
          dealerListApi
            .getSelectResult()
            .then((res) => {
              this.rankType = res.data;
            })
            .catch((err) => {});
        },*/

      // 分页
      handleSizeChange(val) {
        this.tablePage.size = val;
        this.fetchData();
      },
      handleCurrentChange(val) {
        this.tablePage.currentPage = val;
        this.fetchData();
      },
      // 打开修改登陆账号
      openLogin() {
        this.showLoginAccount = true;
      },
      updateSchoolLogin() {
        const that = this;
        that.updateLoginName.name = that.updateLoginName.newName;
        schoolApi.schoolUpdateLogin(that.updateLoginName).then(() => {
          that.showLoginAccount = false;
          that.$nextTick(() => that.fetchData());
          that.$message.success('修改登录账号成功');
        });
      },

      //--合同照片开始
      // 删除上传图片
      handleRemoveDetailContract(file, fileList) {
        const that = this;
        if (!that.addOrUpdate) {
          that.fileContractDetailList = fileList;
        } else {
          for (let a = 0; a < that.fileDetailListPending.length; a++) {
            that.fileDetailListPending[a].uid === file.uid ? that.fileContractDetailList.splice(a, 1) : '';
          }
        }
      },
      // 上传图片预览
      handlePictureCardPreviewContract(file) {
        this.dialogImageUrl = file.url;
        this.dialogUploadVisible = true;
      },
      // 上传图片数量超限
      justPictureNum(file, fileList) {
        this.$message.warning(`当前限制选择1个文件`);
      },

      uploadDetailHttpContract({ file }) {
        this.uploadLoading = true;
        const that = this;
        const fileName = 'manage/' + Date.parse(new Date());
        that.$nextTick(function () {
          ossPrClient()
            .put(fileName, file)
            .then(({ res, url, name }) => {
              if (res && res.status === 200) {
                console.log(`阿里云OSS上传图片成功回调1`, res, url, name);
                if (!that.addOrUpdate) {
                  that.fileContractDetailList.push({
                    uid: file.uid,
                    url: url
                  });
                } else {
                  // 新增上传图片
                  that.fileContractDetailList.push({
                    name
                  });
                  that.updateMarketDate.contractPhoto = name;
                  that.uploadLoading = false;
                }
                that.$nextTick(() => {
                  that.uploadLoading = false;
                });
              }
            })
            .catch((err) => {
              that.$message.error('上传图片失败请检查网络或者刷新页面');
              console.log(`阿里云OSS上传图片失败回调`, err);
              that.uploadLoading = false;
            });
        });
      },
      //---合同上传图片结束

      //--身份证照片开始
      // 删除上传图片
      handleRemoveDetailIdCard(file, fileList) {
        const that = this;
        if (!that.addOrUpdate) {
          that.fileIdCardDetailList = fileList;
        } else {
          for (let a = 0; a < that.fileDetailListPending.length; a++) {
            that.fileDetailListPending[a].uid === file.uid ? that.fileIdCardDetailList.splice(a, 1) : '';
          }
        }
      },
      // 上传图片预览
      handlePictureCardPreviewIdCard(file) {
        this.dialogImageUrl = file.url;
        this.dialogUploadVisible = true;
      },

      uploadDetailHttpIdCard({ file }) {
        this.uploadLoadingIdCard = true;
        const that = this;
        const fileName = 'manage/' + Date.parse(new Date());
        that.$nextTick(function () {
          ossPrClient()
            .put(fileName, file)
            .then(({ res, url, name }) => {
              if (res && res.status === 200) {
                console.log(`阿里云OSS上传图片成功回调1`, res, url, name);
                if (!that.addOrUpdate) {
                  that.fileIdCardDetailList.push({
                    uid: file.uid,
                    url: url
                  });
                } else {
                  // 新增上传图片
                  that.fileIdCardDetailList.push({
                    name
                  });
                  that.updateMarketDate.idCardPhoto = name;
                  that.uploadLoadingIdCard = false;
                }
                that.$nextTick(() => {
                  that.uploadLoadingIdCard = false;
                });
              }
            })
            .catch((err) => {
              that.$message.error('上传图片失败请检查网络或者刷新页面');
              console.log(`阿里云OSS上传图片失败回调`, err);
            });
        });
      },
      //---身份证上传图片结束

      //--校区照片开始
      // 删除上传图片
      handleRemoveDetailShop(file, fileList) {
        const that = this;
        if (!that.addOrUpdate) {
          that.fileIdCardDetailList = fileList;
        } else {
          for (let a = 0; a < that.fileDetailListPending.length; a++) {
            that.fileDetailListPending[a].uid === file.uid ? that.fileShopDetailList.splice(a, 1) : '';
          }
        }
      },
      // 上传图片预览
      handlePictureCardPreviewShop(file) {
        this.dialogImageUrl = file.url;
        this.dialogUploadVisible = true;
      },

      uploadDetailHttpShop({ file }) {
        this.uploadLoadingShop = true;
        const that = this;
        const fileName = 'manage/' + Date.parse(new Date());
        that.$nextTick(function () {
          ossPrClient()
            .put(fileName, file)
            .then(({ res, url, name }) => {
              if (res && res.status === 200) {
                console.log(`阿里云OSS上传图片成功回调1`, res, url, name);
                if (!that.addOrUpdate) {
                  that.fileShopDetailList.push({
                    uid: file.uid,
                    url: url
                  });
                } else {
                  // 新增上传图片
                  that.fileShopDetailList.push({
                    name
                  });
                  that.updateMarketDate.shopPhoto = name;
                  that.uploadLoadingShop = false;
                }
                that.$nextTick(() => {
                  that.uploadLoadingShop = false;
                });
              }
            })
            .catch((err) => {
              that.$message.error('上传图片失败请检查网络或者刷新页面');
              console.log(`阿里云OSS上传图片失败回调`, err);
            });
        });
      },
      //---身份证上传图片结束

      // 获取可指派交付中心列表
      editDelivery(row, state, isMain) {
        // return console.log(row)
        schoolApi.belongDeliverAndAllDeliver().then((res) => {
          this.deliverCenterList = res.data;
        });
        this.form.merchantCode = row.merchantCode;
        this.form.isMain = isMain;
        if (isMain) {
          this.deliverName = row.belongDeliverName;
        } else {
          this.deliverName = row.spareBelongDeliverName;
        }
        this.deliverStatus = state;
        this.dialogPopup = true;
      },

      // 交付中心历史
      deliverHistory(row, isMain) {
        this.form.isMain = isMain;
        let data = {
          merchantCode: row.merchantCode,
          isMain
        };
        schoolApi.deliverBindRecordList(data).then((res) => {
          // console.log('交付中心历史')
          // console.log(res)
          // console.log('交付中心历史')
          this.assignmentList = res.data;
          console.log('交付中心历史');
          console.log(this.assignmentList);
          console.log('交付中心历史');
        });

        this.dialogHistory = true;
      },

      // 指派交付中心
      onSubmit() {
        const that = this;
        that.$refs['form'].validate((valid) => {
          // 表单验证
          if (valid) {
            const loading = this.$loading({
              lock: true,
              text: '指派交付中心',
              spinner: 'el-icon-loading',
              background: 'rgba(0, 0, 0, 0.7)'
            });
            console.log(that.form);
            schoolApi
              .assignDeliverAndAllDeliver(that.form)
              .then(() => {
                loading.close();
                that.$nextTick(() => {
                  that.closeDialog();
                  that.fetchData();
                });
                that.$message.success('指派交付中心成功');
              })
              .catch((err) => {
                loading.close();
              });
          } else {
            console.log('error submit!!');
            return false;
          }
        });
      },

      closeDialog() {
        this.$refs.form.resetFields();
        this.dialogPopup = false;
        this.form.merchantCode = '';
        this.form.deliverMerchantCode = '';
      }
    }
  };
</script>

<style lang="scss" scoped>
  .container-card {
    padding: 15px 15px 15px 15px;
    margin-bottom: 15px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  }

  .map-box {
    position: relative;
  }

  .search-box {
    position: absolute !important;
    top: 10px;
    left: 10px;
  }

  .container-card {
    padding: 15px 15px 15px 15px;
    margin-bottom: 15px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  }

  .red {
    color: red;
  }

  .green {
    color: green;
  }

  .blue {
    color: blue;
  }

  .prompt {
    padding: 10px;
  }

  .result {
    position: absolute;
    top: 0;
    left: 100%;
    width: 300px;
    /* height: 450px; */
    margin-left: 10px;
    background-color: #ffffff;
    border: 1px solid silver;
  }

  .result-list {
    display: flex;
    align-items: center;
    /* margin-bottom: 10px; */
    line-height: 1.6;
    overflow: hidden;
    cursor: pointer;
  }

  .result label {
    display: block;
    width: 19px;
    height: 33px;
    margin-right: 10px;
    line-height: 20px;
    text-align: center;
    font-size: 12px;
    color: #ffffff;
    background: url('https://webapi.amap.com/theme/v1.3/markers/n/mark_bs.png') no-repeat center/cover;
  }

  .result-list.active label {
    background: url('http://webapi.amap.com/theme/v1.3/markers/n/mark_r.png') no-repeat center/cover;
  }

  .list-right {
    flex: 1;
  }

  .result .name {
    font-size: 14px;
    color: #565656;
  }

  .result .address {
    color: #999;
  }

  .search-table {
    height: 380px;
    margin-bottom: 10px !important;
  }

  .search-table th {
    display: none;
  }

  .search-table td {
    padding: 5px 0 !important;
    border-bottom: none;
  }

  .el-vue-search-box-container {
    width: 90% !important;
  }

  .el-date-editor.el-input {
    width: 100% !important;
  }

  @media (max-width: 767px) {
    .el-message-box {
      width: 80% !important;
    }
  }

  .timeFn {
    position: relative;
  }

  .serchFn {
    position: absolute;
    right: 231px;
    top: 55px;
  }

  .el-step.is-vertical {
    ::v-deep .el-step__main {
      padding-bottom: 40px !important;
    }
  }
</style>
