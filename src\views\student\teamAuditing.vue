<!--交付中心-接单管理-试课新学员列表-->
<template>
  <div>
    <!-- 管理员头部 -->
    <div class="frame">
      <el-form label-width="110px" ref="querydata" :model="querydata">
        <el-row>
          <el-col :span="5" :xs="24">
            <el-form-item label="姓名：" prop="studentName">
              <el-input v-model.trim="querydata.studentName" style="width:70%" size="small"
                placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="5" :xs="24">
            <el-form-item label="学员编号：" prop="studentCode">
              <el-input v-model.trim="querydata.studentCode" size="small" style="width:70%"
                placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="5" :xs="24">
            <el-form-item label="审核状态" prop="status">
              <el-select v-model="querydata.status" clearable size="small" placeholder="请选择" style="width: 10vw">
                <el-option label="全部" value=""></el-option>
                <el-option label="已通过" value="1"></el-option>
                <el-option label="已拒绝" value="2"></el-option>
                <el-option label="待审核" value="3"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="10" :xs="24">
            <el-form-item label="申请时间:" prop="source">
              <el-date-picker size="small" v-model="times" style="width:310px;" type="datetimerange"
                format="yyyy-MM-dd HH:mm" value-format="yyyy-MM-dd HH:mm" range-separator="至" start-placeholder="开始日期"
                end-placeholder="结束日期">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="4" style="padding-left: 20px">
            <el-button type="primary" icon="el-icon-search" size="mini" @click="feachData">查询</el-button>
            <el-button size="mini" icon="el-icon-refresh-left" @click="rest()">重置</el-button>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="table">
      <el-table :data="tableData" style="width: 100%" id="out-table" :header-cell-style="getRowClass"
        :cell-style="{ 'text-align': 'center' }" size="mini" fit>
        <el-table-column v-for="(item, index) in tableHeaderList" :key="index" :prop="item.value" :label="item.name"
          header-align="center" min-width="150">
          <template v-slot="{ row }">
            <div v-if="item.value == 'changeStatus'">
              <span :class="statusClass(row.changeStatus)">{{transformStatus(row[item.value])}}</span>
            </div>
            <div v-else-if="item.value == 'operate'">
              <el-button size="mini" type="primary" v-if="row.changeStatus==3" @click="openEdit(row)">审核</el-button>
              <span v-else>-</span>
            </div>
            <span v-else>{{ row[item.value] }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- 审核 -->
    <el-dialog title="审核" :visible.sync="dialogShow" width="30%" :before-close="handleClose">
      <div>
        <el-form ref="form" :model="dialogForm" label-width="80px">
          <el-form-item label="当前小组">
            <el-input v-model="dialogForm.teamName" disabled></el-input>
          </el-form-item>
          <el-form-item label="更换小组">
            <el-input v-model="dialogForm.changeTeamName" disabled></el-input>
          </el-form-item>
          <el-form-item label="审批原因">
            <el-input type="textarea" v-model="reason" placeholder="" maxlength="60" show-word-limit></el-input>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="danger " @click="onSubmit(2)">拒绝</el-button>
        <el-button type="primary" @click="onSubmit(1)">通过</el-button>
      </div>
    </el-dialog>
    <!-- 分页器 -->
    <el-row type="flex" justify="center" align="middle" style="height: 60px">
      <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
        :current-page="querydata.pageNum" :page-sizes="[10, 20, 30, 40, 50]" :page-size="querydata.pageSize"
        layout="total, sizes, prev, pager, next, jumper" :total="total"></el-pagination>
    </el-row>
    <!-- 表头设置 -->
  </div>
</template>

<script>
import ls from '@/api/sessionStorage'
import { deliverlist, changeTeamList, checkChange } from '@/api/courseCate'
export default {
  name: 'teamAuditing',
  data() {
    return {
      isAdmin: false,
      screenWidth: window.screen.width, //屏幕宽度
      reason: '',
      dialogForm: {
        teamName: "",
        changeTeamName: ''
      }, //试课单表单
      dialogShow: false,
      // 搜索表单
      querydata: {
        pageNum: 1,
        pageSize: 10,//页容量
        studentName: '',
        studentCode: '',
        status: '',
        startTime: '',
        endTime: '',
      },
      times: [],
      // 表格数据总数量
      total: null,
      // 表格数据
      tableData: [],
      tableHeaderList: [

        {
          name: '学员姓名',
          value: 'studentName'
        },
        {
          name: '学员编号',
          value: 'studentCode'
        },
        {
          name: '操作',
          value: 'operate'
        },
        {
          name: '交付中心编号',
          value: 'deliverMerchantCode'
        },
        {
          name: '交付中心名称',
          value: 'deliverMerchantName'
        },
        {
          name: '当前小组',
          value: 'teamName'
        },
        {
          name: '修改小组',
          value: 'changeTeamName'
        },
        {
          name: '审核状态',
          value: 'changeStatus'
        },
        {
          name: '申请时间',
          value: 'createTime'
        },
        {
          name: '审批原因',
          value: 'changeReason'
        }
      ],
    }
  },
  created() {
    this.isAdmin = ls.getItem('rolesVal') === 'admin' || ls.getItem('rolesVal') == 'JiaofuManager'
    this.initData()
  },
  watch: {
    times: {
      immediate: true,
      deep: true,
      handler(val) {
        console.log(val != null && val.length > 0);
        if (val != null && val.length > 0) {
          this.querydata.startTime = val[0]
          this.querydata.endTime = val[1]
        }
      }
    }
  },
  methods: {
    onSubmit(checkFlag) {
      let obj = {
        id: this.dialogForm.id,
        checkFlag,
        changeReason: this.reason
      }
      checkChange(obj).then(res => {
        console.log(res);
        if (res.success) {
          this.$message.success(res.message)
          this.initData()
          this.handleClose()
        }
      })
    },
    openEdit(row) {
      // this.dialogForm.changeTeamName = row.changeTeamName
      this.dialogForm = row
      // this.dialogForm.teamName = row.teamName
      this.dialogShow = true
    },
    feachData() {
      this.querydata.pageNum = 1
      this.initData()
    },
    handleClose() {
      this.reason = ''
      this.dialogForm = {
        teamName: "",
        changeTeamName: ''
      }
      this.dialogShow = false
    },
    // 初始化表格
    async initData() {
      let that = this
      let { data } = await changeTeamList(this.querydata)
      // console.log(data, '=======================')
      this.tableData = data.data
      this.total = Number(data.totalItems)
      // pageParamNames.forEach(name => that.$set(that.tablePage, name, parseInt(data[name])))

    },
    // 动态class
    getRowClass({ rowIndex, columnIndex }) {
      if (rowIndex == 0) {
        return 'background:#f5f7fa'
      }
    },
    // 动态class
    transformStatus(status) {
      switch (status) {
        case 1:
          return "已通过";
        case 2:
          return "已拒绝";
        case 3:
          return "待审核";
      }
    },
    // 动态class
    statusClass(status) {
      switch (status) {
        case 3:
          return "primary";
        case 1:
          return "normal";
        case 2:
          return "error";
      }
    },
    //重置
    rest() {
      this.times = []
      this.querydata = {
        pageNum: 1,
        pageSize: 10,//页容量
        studentName: '',
        studentCode: '',
        status: '',
        startTime: '',
        endTime: '',
      }
      this.initData()
    },
    // 分页
    handleSizeChange(val) {
      this.querydata.pageSize = val
      this.initData()
    },
    handleCurrentChange(val) {
      this.querydata.pageNum = val
      this.initData()
    },
  }
}
</script>

<style lang="scss" scoped>
.primary {
  color: #46a6ff;
}
.normal {
  color: rgb(28, 179, 28);
}

.error {
  color: rgba(234, 36, 36, 1);
}

body {
  background-color: #f5f7fa;
}

.frame {
  // margin:  0 30px;
  background-color: rgba(255, 255, 255);
  padding: 20px;
}

.el-button--success {
  color: #ffffff;
  background-color: #6ed7c4;
  border-color: #6ed7c4;
}

.transferred {
  color: #ea2424;
}

.no_transferred {
  color: #1cb31c;
}
</style>
