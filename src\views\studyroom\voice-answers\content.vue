<template>
  <div class="app-container">
    <el-form :inline="true" ref="queryForm" class="container-card" label-width="110px">
      <el-form-item label="所属：">
        <el-input v-model="parentName" readonly />
      </el-form-item>
      <el-form-item label="名称：">
        <el-input v-model="dataQuery.name" placeholder="请输入名称" clearable />
      </el-form-item>
      <el-form-item label="状态：" prop="status">
        <el-select v-model="dataQuery.status" placeholder="全部" clearable>
          <el-option v-for="(item, index) in [{ label: '启用', value: '1' }, { label: '禁用', value: '0' }]" :key="index"
            :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handlePage()">搜索</el-button>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-re" @click="resetQuery()">重置</el-button>
      </el-form-item>

    </el-form>
    <el-col :span="24" style="margin-bottom: 30px">
      <el-button type="success" icon="el-icon-plus" size="mini" @click="handleAdd()">新增</el-button>
      <el-button type="warning" icon="el-icon-back" size="mini" @click="goBack()">返回</el-button>
    </el-col>

    <el-table class="common-table" v-loading="tableLoading" :data="tableData" style="width: 100%;margin-bottom: 20px;"
      row-key="id" border default-expand-all>
      <el-table-column prop="id" label="ID"></el-table-column>
      <el-table-column prop="name" label="名称"></el-table-column>
      <el-table-column label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="mini" @click="handleEdit(scope.row)">编辑</el-button>
          <el-button type="text" size="mini" @click="changeStatus(scope.row)" v-if="scope.row.enabled"
            style="color: red">停用
          </el-button>
          <el-button type="text" size="mini" @click="changeStatus(scope.row)" v-if="!scope.row.enabled">启用
          </el-button>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间"></el-table-column>
    </el-table>
    <!-- 分页 -->
    <el-col :span="24" style="margin-bottom: 20px">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>

    <!-- 添加弹窗 -->
    <el-dialog :title="`${form.id ? '编辑' : '新建'}问答内容`" :visible.sync="open" width="700px" :close-on-click-modal="false"
      @close="close">
      <el-form ref="form" :model="form" :rules="rules" label-width="120px" style="width: 70%;">
        <el-form-item label="名称:" prop="name">
          <el-input v-model="form.name" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="submitForm()">确定</el-button>
        <el-button size="mini" @click="close">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import voiceAnswersApi from '@/api/studyroom/voiceAnswers'
import { pageParamNames } from '@/utils/constants'

export default {
  data() {
    return {
      parentId: null,
      parentName: null,
      open: false,
      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      tableData: [],
      dataQuery: {
        name: '',
        enabled: '',
        parentId: '0'
      },
      form: {},
      // 表单校验
      rules: {
        name: [
          { required: true, message: '请输入名称', trigger: 'blur' }
        ],
      }
    }
  },
  created() {
    let parentId = this.$route.query.id
    this.parentId = parentId;
    voiceAnswersApi.detail(parentId).then(res => {
      this.parentName = res.data.name
    })
    this.getPageList();

  },
  methods: {
    goBack() {
      this.$store.dispatch('delVisitedViews', this.$route);
      this.$router.go(-1);
    },
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (!this.form.id) {
            voiceAnswersApi.create(this.form).then(response => {
              this.$message.success('提交成功！')
              this.open = false
              this.getPageList()
            }).catch(err => {
            })
          } else {
            voiceAnswersApi.edit(this.form).then(response => {
              this.$message.success('提交成功！')
              this.open = false
              this.getPageList()
            }).catch(err => {
            })
          }
        }
      })
    },
    changeStatus(row) {
      let msg = row.enabled ? '停用' : '启用'
      let enabled = row.enabled ? 0 : 1
      this.$confirm('确定要' + msg + '吗?', '修改状态', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        voiceAnswersApi.changeStatus(row.id, enabled).then(res => {
          this.$nextTick(() => this.getPageList())
          this.$message.success('修改成功!')
        }).catch(err => {

        })
      }).catch(err => {

      })
    },
    /** 编辑按钮*/
    handleEdit(row) {
      voiceAnswersApi.detail(row.id).then(res => {
        this.form = res.data
        this.open = true
      })
    },

    getPageList() {
      this.tableLoading = true
      this.dataQuery.parentId = this.parentId;
      this.dataQuery.pageNum = this.tablePage.currentPage
      this.dataQuery.pageSize = this.tablePage.size
      voiceAnswersApi.pageList(this.dataQuery).then(res => {
        this.tableData = res.data.data
        this.tableLoading = false
        // 设置后台返回的分页参数
        pageParamNames.forEach(name => this.$set(this.tablePage, name, parseInt(res.data[name])))
      })
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
    },
    handlePage() {
      this.tablePage = {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
        this.getPageList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dataQuery = {
        name: '',
        enabled: '',
        parentId: '0'
      }
      this.getPageList()
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val
      this.getPageList()
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val
      this.getPageList()
    },
    close() {
      this.open = false
      this.reset()
    },
    reset() {
      this.form = {
        id: null,
        name: null,
        parentId: this.parentId,
      }
      if (this.$refs['form']) {
        this.$refs['form'].resetFields()
      }
    },
  },
};
</script>

<style>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.period-table td,
.period-table th {
  text-align: center;
}

@media (max-width: 767px) {
  .el-message-box {
    width: 80% !important;
  }
}
</style>
