<template>
  <div class="app-container">
    <el-form :inline="true" class="container-card" label-width="82px" label-position="left">
      <el-form-item label="下单时间">
        <el-date-picker style="width: 100%;" v-model="createTime" type="daterange" value-format="yyyy-MM-dd HH:mm:ss"
          align="right" unlink-panels range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="审核状态：">
        <el-select v-model="dataQuery.checkStatus" filterable value-key="value" placeholder="请选择">
          <el-option v-for="(item, index) in checkStatus" :key="index" :label="item.lable" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="课程状态：">
        <el-select v-model="dataQuery.courseStatus" filterable value-key="value" placeholder="请选择">
          <el-option v-for="(item, index) in courseStatus" :key="index" :label="item.lable" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="教练 名称：">
        <el-input v-model="dataQuery.tutorName" placeholder="请输入教练 名称" />
      </el-form-item>
      <el-form-item label="校区：">
        <el-input v-model="dataQuery.tutorName" placeholder="请输入教练 名称" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="getList()">搜索</el-button>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-re" @click="resetQuery()">重置</el-button>
      </el-form-item>
    </el-form>
    <el-table class="common-table" v-loading="tableLoading" :data="tableData" height="400px"
      style="width: 100%;margin-bottom: 20px;" row-key="id" border default-expand-all
      :tree-props="{ list: 'children', hasChildren: 'true' }">
      <el-table-column prop="courseOrderId" label="课程ID"></el-table-column>
      <el-table-column prop="totalCourse" label="总学时"></el-table-column>
      <el-table-column label="操作" align="center" width="200">
        <template slot-scope="scope">
          <el-button type="primary" icon="el-icon-view" @click="handleView(scope.row)" size="mini">查看</el-button>
          <el-button type="danger" icon="el-icon-del" @click="handleDel(scope.row)" size="mini">删除</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="totalPrice" label="总金额"></el-table-column>
      <el-table-column prop="schoolMerchantName" label="校区"></el-table-column>
      <el-table-column prop="tutorName" label="教练 名称"></el-table-column>
      <el-table-column prop="consultantName" label="咨询师名称"></el-table-column>
      <el-table-column prop="payTime" label="下单时间"></el-table-column>
      <el-table-column prop="checkStatusName" label="审核状态"></el-table-column>
      <el-table-column prop="courseStatusName" label="课程状态"></el-table-column>
    </el-table>

    <el-dialog class="recharge-dialog" title="课程详情" :visible.sync="open" width="70%" :close-on-click-modal="false">
      <el-form ref="form" :model="form" label-width="130px" :rules="rules" style="width: 100%;" @close="reset">
        <span><strong>课程信息</strong></span>
        <el-form-item label="课程名称：">
          鼎英语
        </el-form-item>
        <el-form-item label="剩余课程：">
          {{ form.remainCourseTime }}学时/{{ form.totalCourse }}学时
        </el-form-item>
        <el-form-item label="开始时间 ：" prop="courseStartDate">
          {{ form.courseStartDate }}
        </el-form-item>
        <el-form-item label="结束时间 ：" prop="courseEndDate">
          {{ form.courseEndDate }}
        </el-form-item>
        <el-form-item label="学员姓名 ：" prop="studentName">
          {{ form.studentName }}
        </el-form-item>
        <el-form-item label="年级 ：" prop="studentGrade">
          {{ form.studentGrade }}
        </el-form-item>
        <el-divider></el-divider>
        <span><strong>其他信息</strong></span>
        <el-form-item label="校区名称 ：" prop="merchantName">
          {{ form.merchantName }}
        </el-form-item>
        <el-form-item label="教练 名称 ：" prop="merchantName">
          {{ form.tutorName }}
        </el-form-item>
        <el-form-item label="联系电话 ：" prop="tutorPhoneNumber">
          {{ form.tutorPhoneNumber }}
        </el-form-item>
        <el-form-item label="咨询师名称 ：" prop="consultantName">
          {{ form.consultantName }}
        </el-form-item>
        <el-form-item label="联系电话 ：" prop="consultantPhoneNumber">
          {{ form.consultantPhoneNumber }}
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="cancel()">关闭</el-button>
      </div>
    </el-dialog>

    <!-- 分页 -->
    <el-col :span="20" style="overflow-x: auto;" :xs="24">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>
  </div>
</template>

<script>

import scheduleApi from '@/api/scheduleRelation'
import { pageParamNames } from "@/utils/constants";
export default {
  data() {
    return {
      checkStatus: [],
      courseStatus: [],
      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      tableData: [],
      open: false,
      dataQuery: {
        merchantCode: null,
        startTime: null,
        endTime: null,
        checkStatus: null,
        courseStatus: null,
        tutorName: null,
      },
      createTime: [],
      form: {},
      statusList: {
        0: "进行中",
        1: "已处理"
      },
      rules: {
        handingResult: [
          { required: true, message: "请输入处理方案", trigger: "blur" }
        ],
      }
    };
  },
  created() {
    this.getList();
    this.getCheckStatus();
    this.getCourseStatus();
  },
  methods: {
    //获取列表
    getList() {
      const that = this;
      var a = that.createTime;
      if (a != null) {
        that.dataQuery.startTime = a[0];
        that.dataQuery.endTime = a[1];
      } else {
        that.dataQuery.startTime = null;
        that.dataQuery.endTime = null;
      }
      that.tableLoading = true;
      scheduleApi.scheduleList(that.tablePage.currentPage, that.tablePage.size, that.dataQuery).then(res => {
        that.tableData = res.data.data
        console.log(res.data.data);
        that.tableLoading = false
        // 设置后台返回的分页参数
        pageParamNames.forEach(name => this.$set(this.tablePage, name, parseInt(res.data[name])))
      })
    },
    //审核状态
    getCheckStatus() {
      scheduleApi.checkStatus().then(res => {
        var arr = res.data;
        arr.forEach(i => {
          var item = {};
          for (let key in i) {
            item.lable = i[key];
            item.value = key;
          }
          this.checkStatus.push(item)
        });
      })
    },
    //课程状态
    getCourseStatus() {
      scheduleApi.courseStatus().then(res => {
        var arr = res.data;
        arr.forEach(i => {
          var item = {};
          for (let key in i) {
            item.lable = i[key];
            item.value = key;
          }
          this.courseStatus.push(item)
        });
      })
    },

    //重置按钮
    resetQuery() {
      this.createTime = [];
      this.dataQuery = {
        merchantCode: null,
        startTime: null,
        endTime: null,
        checkStatus: null,
        courseStatus: null,
        tutorName: null,
      };
      this.getList();
    },
    //查看按钮
    handleView(row) {
      this.reset();
      scheduleApi.scheduleDetail(row.courseOrderId).then(response => {
        this.form = response.data;
        this.open = true;
      });
    },
    //删除按钮
    handleDel(row) {
      this.$confirm('确定要删除该课程吗?', '删除课程', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        complainApi.complainDel(row.id).then(res => {
          this.getList();
          this.$message.success('删除成功!')
        }).catch(err => {
        })
      }).catch(err => {
      })
    },
    reset() {
      this.form = {
        totalCourse: null,
        remainCourseTime: null,
        courseStartDate: null,
        courseEndDate: null,
        studentName: null,
        studentGrade: null,
        merchantName: null,
        tutorName: null,
        tutorPhoneNumber: null,
        consultantName: null,
        consultantPhoneNumber: null,
      };
    },
    cancel() {
      this.open = false;
      this.reset();
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val;
      this.getList();
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val;
      this.getList();
    },
  }
};
</script>

<style>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

@media screen and (max-width: 767px) {
  .recharge-dialog .el-dialog {
    width: 90% !important;
  }

  .el-message-box {
    width: 80% !important;
  }
}
</style>
