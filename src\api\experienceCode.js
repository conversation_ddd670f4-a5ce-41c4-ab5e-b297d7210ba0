import request from '@/utils/request'
// import request from '@/utils/request'

export default {

  // 生成二维码
  createCode(data) {
    return request({
      url: '/mall/qrcode/addDisCode',
      method: 'POST',
      data
    })
  },
  codeSourceList(pageNum, pageSize, data) {
    return request({
      url: '/mall/qrcode/codeSourceList?pageNum=' + pageNum + '&pageSize=' + pageSize + '&merchantCode=' + data,
      method: 'GET',
      params: {
        merchantName: data.merchantName
      }
    })
  },
  // 根据id查询校区生成的码
  queryCodeSource(id) {
    return request({
      url: '/mall/qrcode/merchantCode?memberId=' + id,
      method: 'GET'
    })
  },
  queryAllRegion() {
    return request({
      url: '/mall/queryAllRegion',
      method: 'GET'
    })
  },
  checkMemberAddress(type, code) {
    return request({
      url: '/mall/member/region/' + type + '/check?code=' + code,
      method: 'GET'
    })
  }
}
