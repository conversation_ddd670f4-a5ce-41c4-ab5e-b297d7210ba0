/**
 *
 */
import request from '@/utils/request'

export default {
  // 分页查询
  levelList() {
    return request({
      url: '/znyy/course/getFunReviewLevelConfigList',
      method: 'GET'
    })
  },

  // 新增
  addConfig(data) {
    return request({
      url: '/znyy/course/addFunReviewLevelConfig',
      method: 'POST',
      data
    })
  },

  // 更新
  updateConfig(data) {
    return request({
      url: '/znyy/course/editFunReviewLevelConfig',
      method: 'POST',
      data
    })
  },

  //删除
  deleteConfig(id) {
    return request({
      url: '/znyy/course/deleteFunReviewLevelConfig?id=' + id,
      method: 'GET'
    })
  },

  // 分页查询
  getFunReviewOpenList(pageNum, pageSize,data) {
    return request({
      url: 'v2/mall/funReview/open/list/'+ pageNum + '/' + pageSize,
      method: 'GET',
      params: data
    })
  },

}
