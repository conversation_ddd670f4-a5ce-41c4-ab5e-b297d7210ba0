<template>
  <div class="app-container">
    <el-form :inline="true" class="container-card" label-width="110px" label-position="left">
      <el-row>
        <el-col :span="8" :xs="24">
          <el-form-item label="订单号：">
            <el-input v-model="dataQuery.orderVipCode" clearable/>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="会员手机号：">
            <el-input v-model="dataQuery.loginName" clearable/>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="会员姓名：">
            <el-input v-model="dataQuery.bankAccName" clearable/>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="8" :xs="24">
          <el-form-item label="邀请人手机号：">
            <el-input  v-model="dataQuery.referPhone" clearable/>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="订单状态：">
            <el-select v-model="dataQuery.orderStatus" placeholder="全部" style="width: 185px;">
              <el-option v-for="(item,index) in orderType" :key="index" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="分账状态：">
            <el-select v-model="dataQuery.splitStatus" placeholder="全部" style="width: 185px;">
              <el-option v-for="(item,index) in [{label:'分账完结',value:'Finish'},{label:'分账中',value:'SplitIng'},{label:'分账失败',value:'SplitSubmitFail'},{label:'分账成功',value:'SplitSuccess'}]" :key="index" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="12" :xs="24">
          <el-form-item label="下单时间：">
            <el-date-picker
              v-model="value1"
              value-format="yyyy-MM-dd"
              @change="dateVal"
              type="daterange"
              align="right"
              unlink-panels
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              style="width: 100%;"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12" :xs="24">
          <el-form-item label="支付时间：">
            <el-date-picker
              v-model="value2"
              value-format="yyyy-MM-dd"
              @change="datePayVal"
              type="daterange"
              align="right"
              unlink-panels
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              style="width: 100%;"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="24" style="text-align: right">
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="fetchData01()">搜索</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <el-col :span="24" style="margin-bottom: 30px;">
      <el-button type="warning" icon="el-icon-document-copy" size="mini"  v-loading="exportLoading" @click="exportList()">导出</el-button>
    </el-col>
    <!-- 渲染表格 -->
    <el-table
      v-loading="tableLoading"
      class="common-table"
      :data="tableData"
      style="width: 100%;margin-bottom: 20px;"
      row-key="id"
      border
      default-expand-all
      :tree-props="{list: 'children', hasChildren: 'true'}"
    >
      <el-table-column prop="orderVipCode" label="订单号" />
      <el-table-column prop="bankAccName" label="会员姓名" />
      <el-table-column prop="loginName" label="会员手机号" />
      <el-table-column prop="referPhone" label="邀请人手机号" />
      <el-table-column prop="payMoney" label="支付金额（元）" />
      <el-table-column prop="addTime" label="下单时间" />
      <el-table-column prop="payTime" label="支付时间" />
      <el-table-column prop="orderStatus" label="订单状态">
        <template slot-scope="scope">
          <span class="green" v-if="scope.row.orderStatus === '成功'">支付成功</span>
          <span class="red" v-else>未付款</span>
        </template>
      </el-table-column>
      <el-table-column prop="splitStatus" label="分账状态">
        <template slot-scope="scope">
          <span class="green" v-if="scope.row.splitStatus === 'Finish'">分账完结</span>
          <span class="green" v-if="scope.row.splitStatus === 'SplitSuccess'">分账成功</span>
          <span class="green" v-if="scope.row.splitStatus === 'SplitIng'">分账中</span>
          <span class="red" v-if="scope.row.splitStatus === 'SplitSubmitFail'">分账失败</span>
        </template>
      </el-table-column>
   <!--   <el-table-column prop="addTime" label="操作">
        <template slot-scope="scope">
          <el-tooltip content="编辑" placement="top">
            &lt;!&ndash; <el-button type="warning" icon="el-icon-edit-outline" size="mini" @click="update(scope.row.id)">编辑</el-button> &ndash;&gt;
          </el-tooltip>
        </template>
      </el-table-column>-->
    </el-table>
    <!-- 分页 -->
    <el-col :span="20">
      <el-pagination
        :current-page="tablePage.currentPage"
        :page-sizes="[10, 20, 30, 40, 50]"
        
        layout="total, sizes, prev, pager, next, jumper"
        :total="tablePage.totalItems"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-col>
  </div>
</template>

<script>
import orderApi from '@/api/memberRechargeOrder'
import Tinymce from "@/components/Tinymce";
import { pageParamNames } from '@/utils/constants'

export default {
  name:'memberRechargeOrder',
  data() {
    return {
      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      uploadLoading: true,
      addOrUpdate: false,
      dataQuery: {
        orderVipCode:'',
        loginName:'',
        bankAccName:'',
        referPhone:'',
        txtStartAddTime:'',
        txtEndAddTime:'',
        txtStartPayTime:'',
        txtEndPayTime:'',
        orderStatus:'',
        splitStatus:''
      },
      dialogVisible: false,
      tableData: [],
      value1:'',
      value2:'',
      orderType:[],//订单状态
      exportLoading:false,//导出加载
    }
  },
  created() {
    this.getType()
    this.fetchData()
  },
  methods: {
    // 订单状态
    getType(){
      orderApi.orderType('PayOrderStatus').then(res => {
        this.orderType = res.data
        // console.log(this.appType)
      })
    },
    // 获取起始时间
    dateVal(e) {
      this.dataQuery.txtStartAddTime = e[0]
      this.dataQuery.txtEndAddTime = e[1]
    },
    // 获取起始时间
    datePayVal(e) {
      this.dataQuery.txtStartPayTime = e[0]
      this.dataQuery.txtEndPayTime = e[1]
    },
  fetchData01(){
         this.tablePage= {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null
        }
        this.fetchData();
      },
    // 查询提现列表
    fetchData() {
      const that = this;
      that.tableLoading = true
      orderApi.memberOrder(that.tablePage.currentPage, that.tablePage.size, that.dataQuery).then(res => {
        that.tableData = res.data.data
        that.tableLoading = false
        // 设置后台返回的分页参数
        pageParamNames.forEach(name => that.$set(that.tablePage, name, parseInt(res.data[name])))
      })
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val
      this.fetchData()
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val
      this.fetchData()
    },
    // 导出
    exportList() {
      const that = this
      that.exportLoading = true;
      orderApi.orderExport(that.dataQuery).then(response => {
        console.log(response)
        if (!response) {
          this.$notify.error({
            title: '操作失败',
            message: '文件下载失败'
          })
        }
        const url = window.URL.createObjectURL(response)
        const link = document.createElement('a')
        link.style.display = 'none'
        link.href = url // 获取服务器端的文件名
        link.setAttribute('download', '会员充值订单表.xls')
        document.body.appendChild(link)
        link.click()
        this.exportLoading = false
      })
    },
  }
}
</script>

<style>
  .container-card {
    padding: 15px 15px 15px 15px;
    margin-bottom: 15px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  }
  .order-table {
    text-align: center;
  }

  .order-table td,
  .order-table th {
    padding: 5px 0;
    text-align: center;
  }

  .order-table button {
    padding: 2px;
  }
</style>
