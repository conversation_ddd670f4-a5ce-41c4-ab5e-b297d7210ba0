/**
 * 课程单词相关接口
 */
import request from '@/utils/request'

export default {
  //课程单词分页查询
  courseWordList(pageNum, pageSize, data) {
    return request({
      url: '/znyy/course/word/list/' + pageNum + '/' + pageSize,
      method: 'GET',
      params: data
    })
  },
  // 课程单词新增
  addCourseWordList(data,courseWordCo) {
    return request({
      url: '/znyy/course/word/upload',
      method: 'POST',
      data,
      params: courseWordCo,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
    })
  },
 // 课程单词的批量删除
 batchDeletion(data){
  return request({
    url: '/znyy/course/word/delete/words',
    method: 'DELETE',
    data
  })
 },
 //单词本更新
 updateWordBook(data){
   return request({
     url:'/znyy/course/word/update/word',
     method: 'PUT',
     data
   })
 },
 //单词删除
 deleteCourseWord(id){
  return request({
    url:'/znyy/course/word/delete/single/'+id,
    method : 'DELETE',
  })
 },
  // 单词编辑
  updateCourseWord(courseWordDelete) {
    return request({
      url: '/znyy/course/big/class',
      method: 'PUT',
      params:courseWordDelete
    })
  },
  // 单词查询
  queryActive(id) {
    return request({
      url: '/znyy/course/word/echo/' + id,
      method: 'GET'
    })
  },
  // 课程开通与暂停
  updateStatus(id,status) {
    return request({
      url: '/znyy/course/big/class/openAndSuspension?id=' + id + '&isEnable=' + status,
      method: 'PUT'
    })
  },
  // 获取分类列表
  categoryType() {
    return request({
      url: '/znyy/course/category/check/list',
      method: 'GET'
    })
  },
  //单个单词编辑
  singleWordEditing(data){
    return request({
      url: '/znyy/course/word/update/single',
      method: 'PUT',
      data
    })
  },

}
