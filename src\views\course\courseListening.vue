<template>
  <div class="app-container">
    <!-- 搜索 -->
    <el-form :inline="true" ref="form" class="SearchForm" style="padding: 20px 20px 0;" label-width="100px">
      <el-row>
        <el-col :span="4">
          <el-form-item label="所属课程:">
            <router-link :to="{ path: 'courseList' }" class="blue lh36">{{
              courseName
            }}</router-link>
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="课程编号:">
            <el-input v-model="dataQuery.courseCode" @keyup.enter.native="fetchData()" placeholder="请输入课程编号"
              clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="题目编号:">
            <el-input v-model="dataQuery.topicCode" @keyup.enter.native="fetchData()" placeholder="请输入题目编号"
              clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="开通状态:">
            <el-select v-model="dataQuery.isEnable" filterable value-key="value" placeholder="请选择">
              <el-option v-for="(item, index) in options" :key="index" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="2">
          <el-button type="warning" size="small" icon="el-icon-search" @click="fetchData01()">搜索</el-button>
        </el-col>
      </el-row>
    </el-form>

    <div class="SearchForm">
      <!-- 添加 -->
      <div class="btn-add">
        <el-button size="small" type="success" @click="goBack()">返回</el-button>
        <el-button size="small" type="primary" icon="el-icon-plus" @click="clickAdd">添加</el-button>
      </div>
      <!-- 表格 -->
      <el-table class="course-table" :data="tableData" stripe border
        :default-sort="{ prop: 'addTime', order: 'descending' }">
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column prop="courseCode" label="课程编号" sortable width="200"></el-table-column>
        <el-table-column prop="topicCode" label="题目编号" sortable></el-table-column>
        <el-table-column prop="id" label="操作" sortable width="300">
          <template slot-scope="scope">
            <el-button type="warning" size="mini" icon="el-icon-switch-button" v-if="scope.row.isEnable === 0"
              @click="courseStatus(scope.row.id, scope.row.isEnable)">开通</el-button>
            <el-button type="danger" size="mini" icon="el-icon-video-pause" v-else
              @click="courseStatus(scope.row.id, scope.row.isEnable)">暂停</el-button>
            <el-button type="success" size="mini" icon="el-icon-edit-outline"
              @click="openEdit(scope.row.id)">编辑</el-button>
            <el-button type="danger" size="mini" icon="el-icon-delete" @click="deleteReading(scope.row.id)">删除</el-button>
          </template>
        </el-table-column>
        <el-table-column prop="topicPart" label="题目小节" sortable></el-table-column>
        <el-table-column prop="serialNumber" label="题目序号" sortable></el-table-column>
        <el-table-column prop="isEnable" label="状态" sortable>
          <template slot-scope="scope">
            <span class="green" v-if="scope.row.isEnable === 1">开通</span>
            <span class="red" v-else>暂停</span>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <el-col :span="20">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>


  </div>
</template>

<script>
import courseListeningApi from "@/api/courseListening";
import {
  pageParamNames
} from "@/utils/constants";
export default {
  data() {

    return {
      categoryType: [], // 所属分类

      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null,
      },
      tableLoading: false,
      dataQuery: {
        courseCode: "",
        topicCode: "",
        isEnable: ""
      },
      isRouterAlive: true, //局部刷新
      activeType: [], // 活动类型
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null,
      },
      tableData: [], //表格数据
      dialogVisible: false, // 修改弹窗是否展示


      rulesWordBook: {},

      addOrUpdate: true, // 是新增还是修改
      addReadingData: {}, // 新增阅读理解
      updateReadingData: {}, //修改阅读理解
      rules: {},
      radio: "0", //单选框状态 值必须是字符串
      memberId: undefined,
      courseName: "",
      courseCode: "",
      fd: "",
      //增加内容
      rulseReading: {
        topicTitle: [{
          required: true,
          message: "必填",
          trigger: "blur",
        },],
        showName: [{
          required: true,
          message: "必填",
          trigger: "blur",
        },],
        topicContent: [{
          required: true,
          message: "必填",
          trigger: "blur",
        },],
        suggestedTime: [{
          required: true,
          message: "必填",
          trigger: "blur",
        },],
        sort: [{
          required: true,
          message: "必填",
          trigger: "blur",
        },],
        level: [{
          required: true,
          message: "必填",
          trigger: "blur",
        },],
      },
      items: [{
        fillNumber: '',
        questionText: "",
        answerForA: "",
        answerForB: "",
        answerForC: "",
        answerForD: "",
        correctAnswer: "",
        answerRemark: ""
      }],
      rulesAnswers: {
        fillNumber: [{
          required: true,
          message: "必填",
          trigger: "blur",
        },],
        questionText: [{
          required: true,
          message: "必填",
          trigger: "blur",
        },],
        answerForA: [{
          required: true,
          message: "必填",
          trigger: "blur",
        },],
        answerForB: [{
          required: true,
          message: "必填",
          trigger: "blur",
        },],
        answerForC: [{
          required: true,
          message: "必填",
          trigger: "blur",
        },],
        answerForD: [{
          required: true,
          message: "必填",
          trigger: "blur",
        },],
        correctAnswer: [{
          required: true,
          message: "必填",
          trigger: "blur",
        },],
        answerRemark: [{
          required: true,
          message: "必填",
          trigger: "blur",
        },],

      },

      options: [{
        value: 1,
        label: '开通'
      }, {
        value: 0,
        label: '暂停'
      }],
    };
  },
  created() {
    this.fetchData();
  },
  methods: {
    fetchData01() {
      this.tablePage = {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      }
      this.fetchData();
    },
    // 查询表格列表
    fetchData() {
      const that = this;
      that.tableLoading = true;
      window.localStorage.getItem("courseContentTypeListening");
      that.courseName = window.localStorage.getItem("courseNameListening");
      that.courseCode = window.localStorage.getItem("courseCodeListening");
      that.dataQuery.courseCode = window.localStorage.getItem("courseCodeListening");
      that.addReadingData.courseCode = window.localStorage.getItem("courseCodeListening");
      courseListeningApi
        .courseListeningList(
          that.tablePage.currentPage,
          that.tablePage.size,
          that.dataQuery
        )
        .then((res) => {
          that.tableData = res.data.data;
          that.tableLoading = false;
          // 设置后台返回的分页参数
          pageParamNames.forEach((name) =>
            that.$set(that.tablePage, name, parseInt(res.data[name]))
          );
        });
    },

    // 课程开通与暂停
    courseStatus(id, status) {
      if (status == 0) {
        status = 1;
      } else {
        status = 0
      }
      const that = this;
      this.$confirm('确定操作吗?', '修改状态', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        courseListeningApi.updateStatus(id, status).then(res => {
          that.fetchData01();
          that.$nextTick(() => that.fetchData())
          that.$message.success('修改成功!')
        }).catch(err => {

        })
      }).catch(err => {

      })
    },
    //局部刷新
    reload() {
      this.isRouterAlive = false;
      this.$nextTick(function () {
        this.isRouterAlive = true;
      });
    },
    //添加操作
    clickAdd() {
      window.localStorage.setItem("courseListeningAddName", "增加听力");
      window.localStorage.setItem("courseListeningAddCode", this.courseCode);
      window.localStorage.setItem("courseListeningAddAddOrUpdate", true);
      this.$router.push({
        path: "/course/courseListeningAdd",
        query: {
          courseCode: this.$route.query.courseCode,
          courseName: this.courseName,
          addOrUpdate: true
        }
      })
    },
    //查看详情
    openDetail(id) {
      this.$router.push({
        path: "/course/courseReadingDetails",
        query: {
          courseCode: this.$route.query.courseCode,
          courseName: this.courseName,
          addOrUpdate: false,
          id: id
        }
      })
    },

    //删除阅读理解和完型填空
    deleteReading(id) {
      const that = this;
      this.$confirm('确定操作吗?', '删除听力', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        courseListeningApi.deleteListening(id).then(res => {
          that.fetchData01();
          that.$message.success('删除成功!')
        }).catch(err => {

        })
      }).catch(err => {

      })

    },

    // 打开编辑阅读理解和完型填空
    openEdit(id) {
      window.localStorage.setItem("courseListeningUpdateName", "修改听力");
      window.localStorage.setItem("courseListeningUpdateCode", this.courseCode);
      window.localStorage.setItem("courseListeningUpdateId", id);
      this.$router.push({
        path: "/course/courseListeningUpdate",
        query: {
          courseCode: this.courseCode,
          courseName: this.courseName,
          id: id
        }
      })

    },

    // 状态改变事件
    change(radio) {
      if (radio == "1") {
        this.addReadingData.isEnable = 1;
      } else {
        this.addReadingData.isEnable = 0;
      }
    },

    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val;
      this.fetchData();
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val;
      this.fetchData();
    },
    // 关闭弹窗
    close() {
      this.dialogVisible = false;
    },

    // 返回课程列表
    goBack() {
      this.dataQuery.courseCode = '';
      this.$router.push({
        path: "/course/courseList",
      });

    },
    //新增
    handleAddFilterAttr() {
      this.filterProductAttrList.push({
        value: null,
        key: Date.now(),
      });
    },

  },
};
</script>

<style>
.lh36 {
  line-height: 36px;
  font-size: 14px;
}

.SearchForm {
  width: 100%;
  margin-bottom: 15px;
  background: #fff;
  border-radius: 6px;
  padding: 5px 0;
  padding-left: 5px;
  box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.2);
}

.btn-add {
  padding: 5px;
}

.red {
  color: red;
}

.green {
  color: green;
}

.course-table {
  text-align: center;
}

.course-table td,
.course-table th {
  padding: 5px 0;
  text-align: center;
}

.course-table button {
  padding: 2px;
}

.icon-stop {
  float: left;
  display: inline-block;
  width: 12px;
  height: 12px;
  background: url("../../icons/stop.png") no-repeat top center/contain;
}

.mt22 {
  margin-top: 22px;
}

.blue {
  color: #409eff;
}

.clearfix {
  color: red;
}

@media (max-width:767px) {
  .el-message-box {
    width: 80% !important;
  }
}</style>
