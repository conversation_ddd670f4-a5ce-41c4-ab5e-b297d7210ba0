<template>
  <el-card class="process-card">
    <div slot="header" class="card-header">
      <div class="card-title">
        <i class="el-icon-s-grid" style="color: #bbbbbb"></i>
        <span style="margin-left: 6px">固定图片</span>
      </div>
      <el-button type="text" class="delete-button" icon="el-icon-delete" @click="$emit('delete')"></el-button>
    </div>
    <el-form ref="form" :model="formData" label-width="100px" :rules="rules" class="process-form-content">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="排序:" prop="sortsNum">
            <el-input-number v-model="formData.sortsNum" :min="1"></el-input-number>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="阶段时长:" prop="stageDuration">
            <el-select v-model="formData.stageDuration" placeholder="请选择阶段时长">
              <el-option v-for="i in 60" :key="i" :label="i" :value="i"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="阶段名称:" prop="stageName">
            <el-input v-model="formData.stageName" placeholder="请输入阶段名称" maxlength="10" show-word-limit></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="阶段提醒:" prop="stageRemind">
            <el-radio-group v-model="formData.stageRemind">
              <el-radio label="0">阶段结束时需要弹框提醒</el-radio>
              <el-radio label="1">阶段结束时不作提醒</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="图片：" prop="imageCover">
            <my-upload :showTip="false" :isKbOrMb="30000" @handleSuccess="handlePicSuccess" @handleRemove="handlePicRemove" :fullUrl="true" :file-list="fileList" :limit="1" />
            <div class="tips" style="font-size: 12px; color: #909399">支持jpg, jpeg, png格式，小于30MB</div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </el-card>
</template>

<script>
  import MyUpload from '@/components/Upload/MyUpload.vue';
  export default {
    name: 'FixedImageComponent',
    components: { MyUpload },
    props: {
      index: {
        type: Number,
        required: true
      }
    },
    data() {
      return {
        rules: {
          stageDuration: [{ required: true, message: '请选择阶段时长', trigger: 'change' }],
          stageName: [{ required: true, message: '请输入阶段名称', trigger: 'blur' }],
          stageRemind: [{ required: true, message: '请选择阶段提醒方式', trigger: 'change' }],
          imageCover: [{ required: true, message: '请上传图片', trigger: 'change' }]
        },
        // formData: {
        //   stageDuration: '',
        //   stageName: '',
        //   stageRemind: '',
        //   imageCover: ''
        // },
        fileList: [] // 上传图片已有图片列表
      };
    },
    props: {
      formData: {
        type: Object,
        default: () => ({
          stageDuration: '',
          stageName: '',
          stageRemind: '',
          imageCover: ''
        })
      }
    },
    watch: {
      formData: {
        handler(newVal) {
          console.log('formData', newVal);
          if (newVal.imageCover) {
            this.fileList = [
              {
                url: newVal.imageCover
              }
            ];
          }

          // 当表单数据变化时，通知父组件
          this.$emit('update', newVal);
        },
        deep: true,
        immediate: true // 添加immediate: true，确保组件创建时就触发一次
      }
    },
    methods: {
      handlePicSuccess(url) {
        this.formData.imageCover = this.fileList[0].url;
      },
      handlePicRemove() {
        this.formData.imageCover = '';
      },
      resetForm() {
        this.$refs.form.resetFields();
      },
      // 添加表单校验方法
      validate() {
        return new Promise((resolve, reject) => {
          this.$refs.form.validate((valid) => {
            if (valid) {
              resolve(true);
            } else {
              reject(false);
            }
          });
        });
      }
    }
  };
</script>

<style scoped>
  .process-card {
    margin-top: 10px;
    margin-bottom: 20px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0;
  }

  .card-title {
    display: flex;
    align-items: center;
    font-size: 14px;
    font-weight: bold;
  }

  .delete-button {
    padding: 0;
    font-size: 20px;
    color: #2a2a3b;
  }

  .process-form-content {
    padding: 0 20px 0 0; /* 调整内边距，减少右边距 */
  }

  /* 修复/deep/选择器 */
  :deep(.el-card__header) {
    padding: 5px 20px;
  }

  :deep(.el-form-item) {
    margin-bottom: 12px;
  }

  :deep(.el-select) {
    width: 100%;
  }
</style>
