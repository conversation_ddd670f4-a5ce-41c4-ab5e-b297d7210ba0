<template>
  <div class="app-container">
    <el-form :inline="true" class="container-card" label-width="110px" label-position="left">
      <el-form-item label="账期：">
        <el-input id="merchantCode" v-model="dataQuery.merchantCode" name="id" placeholder="请输入服务商编号：：" />
      </el-form-item>
      <el-form-item label="用户编号：">
        <el-input id="merchantName" v-model="dataQuery.name" name="id" placeholder="请输入登录账号：" />
      </el-form-item>
      <el-form-item label="用户名称：">
        <el-input id="merchantName" v-model="dataQuery.merchantName" name="id" placeholder="请输入服务商名称" />
      </el-form-item>
      <el-form-item label="用户等级：">
        <el-select v-model="dataQuery.IsEnable" placeholder="全部" style="width: 185px;">
          <el-option v-for="item in [{ '2': '会员' }, { '1': '教练 ' }, { '0': '散户' }]" :key="item.value"
            :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="fetchData()">搜索</el-button>
      </el-form-item>
    </el-form>
    <el-form :inline="true">
      <el-form-item label="生成账期：">
        <el-input id="merchantCode" v-model="dataQuery.merchantCode" name="id" />
      </el-form-item>
      <el-button type="warning" icon="el-icon-document-copy">报表生成</el-button>
    </el-form>

    <el-table class="period-table" v-loading="tableLoading" :data="tableData" style="width: 100%;margin-bottom: 20px;"
      row-key="id" border default-expand-all :tree-props="{ list: 'children', hasChildren: 'true' }">
      <el-table-column prop="loginName" label="账期编号" width="180"></el-table-column>
      <el-table-column prop="nickName" label="用户编号" width="180"></el-table-column>
      <el-table-column prop="totalBonus" label="用户名称" width="180"></el-table-column>
      <el-table-column prop="alreadyBonus" label="用户类型" width="180"></el-table-column>
      <el-table-column prop="withdrawnBonus" label="期初余额"></el-table-column>
      <el-table-column prop="loginName" label="收益金额（元）" width="180"></el-table-column>
      <el-table-column prop="nickName" label="提现金额（元）" width="180"></el-table-column>
      <el-table-column prop="totalBonus" label="退款金额（元）" width="180"></el-table-column>
      <el-table-column prop="alreadyBonus" label="期末余额（元）" width="180"></el-table-column>
      <el-table-column prop="withdrawnBonus" label="生成时间"></el-table-column>
    </el-table>
    <!-- 分页 -->
    <el-col :span="20">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>
  </div>
</template>

<script>
import {
  queryOfficialAccountLink
} from "@/api/wechatPublicAccount";
import Tinymce from "@/components/Tinymce";
import {
  pageParamNames
} from "@/utils/constants";
export default {
  data() {
    return {
      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      tableData: [{
        name: 1,
        title: '1111'
      }],
      dataQuery: {},
      dialogVisible: false,
      updateMarketDate: {},
      addMarketDate: {},
      showLoginAccount: false
    };
  },
  created() {
    this.fetchData();
  },
  methods: {
    // 查询提现列表
    fetchData() {
      const that = this;
      //that.tableLoading = true
      queryOfficialAccountLink(
        that.tablePage.currentPage,
        that.tablePage.size
      ).then(res => {
        console.log(res);
        that.tableData = res.data;
        that.tableLoading = false;
        // 设置后台返回的分页参数
        pageParamNames.forEach(name =>
          that.$set(that.tablePage, name, parseInt(res.data[name]))
        );
      });
    },
    //新增操作
    clickAdd() {
      this.dialogVisible = true;
    },
    // 打开修改登陆账号
    openLogin() {
      this.showLoginAccount = true
    },
    closeLogin() {
      this.showLoginAccount = false
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val;
      this.fetchData();
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val;
      this.fetchData();
    }
  }
};
</script>

<style>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.period-table td,
.period-table th {
  text-align: center;
}
</style>
