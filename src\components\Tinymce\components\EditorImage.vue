<template>
  <div class="upload-container">
    <el-button :style="{background:color,borderColor:color}" icon="el-icon-upload" type="primary" @click=" dialogVisibleha=true" size="mini">
      上传
    </el-button>
    <el-dialog :visible.sync="dialogVisibleha" :modal="false">
      <el-upload :disabled="disabled"
        v-loading="uploadLoading"
        :multiple="true"
        :file-list="fileList"
        :show-file-list="true"
        :on-remove="handleRemove"
        :on-success="handleSuccess"
        :before-upload="beforeUpload"
        class="editor-slide-upload"
        :http-request="uploadPrHttp"
        action=""
        list-type="picture-card"
      >
        <el-button size="small" type="primary">
          点击上传
        </el-button>
      </el-upload>
      <div style="text-align: right;">
        <el-button type="warning" size="mini" @click="dialogVisibleha = false">
          取消
        </el-button>
        <el-button type="success" size="mini" @click="handleSubmit">
          确认
        </el-button>
      </div>

    </el-dialog>
  </div>
</template>

<script>
// import { getToken } from 'api/qiniu'
import { ossPrClient } from '@/api/alibaba'

export default {
  name: 'EditorSlideUpload',
  props: {
    color: {
      type: String,
      default: '#1890ff'
    },
    disabled:{
      type:Boolean,
      default:false
    }
  },
  data() {
    return {
      dialogVisibleha: false,
      listObj: {},
      fileList: [],
      uploadLoading: false
    }
  },
  methods: {
    // 获取uuid
    getUuid: function() {
      const s = [];
      const hexDigits = '0123456789abcdef';
      for (let i = 0; i < 36; i++) {
        s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1)
      }
      s[14] = '4'
      s[19] = hexDigits.substr((s[19] & 0x3) | 0x8, 1)
      s[8] = s[13] = s[18] = s[23] = '-'
      const uuid = s.join('');
      return uuid
    },
    checkAllSuccess() {
      return Object.keys(this.listObj).every(item => this.listObj[item].hasSuccess)
    },
    handleSubmit() {
      const arr = Object.keys(this.listObj).map(v => this.listObj[v])
      // if (!this.checkAllSuccess()) {
      //   this.$message('Please wait for all images to be uploaded successfully. If there is a network problem, please refresh the page and upload again!')
      //   return
      // }
      this.$emit('successCBK', arr)
      this.listObj = {}
      this.fileList = []
      this.dialogVisibleha = false
    },

    // 上传图片请求
    uploadPrHttp({ file }) {
      this.uploadLoading = true
      const that = this
      const fileName = 'manage/' + that.getUuid()
      console.log(file)
      console.log(fileName)

      that.$nextTick(function() {
        console.log(file)
        console.log(fileName)
        ossPrClient().put(fileName, file).then(({ res, url,  name }) => {
          console.log(123);
          if (res && res.status === 200) {
            console.log(`阿里云OSS上传图片成功回调1`, res, url, name)
            console.log(file)

            console.log(name)

            that.fileList.push({'name':file.name, 'uid': file.uid, 'url': url,'type':file.type })
            const objKeyArr = Object.keys(that.listObj)
            console.log(898988998)
            console.log(that.listObj)

            console.log(objKeyArr)
            this.uploadLoading = false
            this.listObj = that.fileList
            // for (let i = 0, len = objKeyArr.length; i < len; i++) {
            //   if (this.listObj[objKeyArr[i]].uid === file.uid) {
            //     this.listObj[objKeyArr[i]].url = url
            //     this.listObj[objKeyArr[i]].type = file.type
            //     this.listObj[objKeyArr[i]].name = file.name
            //     this.listObj[objKeyArr[i]].hasSuccess = true
            //     return
            //   }
            // }
            that.$nextTick(() => {
              that.uploadLoading = false
            })

            console.log(that.fileList)
          }
        }).catch(err => {
          that.$message.error('上传图片失败请检查网络或者刷新页面')
          console.log(`阿里云OSS上传图片失败回调`, err)
        })
      })
    },

    handleSuccess(response, file) {
      const uid = file.uid
      const objKeyArr = Object.keys(this.listObj)
      console.log(11111111)
      console.log(objKeyArr)
      for (let i = 0, len = objKeyArr.length; i < len; i++) {
        if (this.listObj[objKeyArr[i]].uid === uid) {
          this.listObj[objKeyArr[i]].url = response.files.file
          this.listObj[objKeyArr[i]].hasSuccess = true
          return
        }
      }
    },
    handleRemove(file) {
      const uid = file.uid
      const that = this
      const objKeyArr = Object.keys(this.listObj)
      that.fileList.forEach((item,index) =>{
        if( item.uid===uid){
          that.fileList.splice(index, 1)
        }
      })
      for (let i = 0, len = objKeyArr.length; i < len; i++) {
        if (that.listObj[objKeyArr[i]].uid === uid) {
          delete that.listObj[objKeyArr[i]]
          return
        }
      }
    },
    beforeUpload(file) {
      const _self = this
      const _URL = window.URL || window.webkitURL
      const fileName = file.uid
      this.listObj[fileName] = {}
      return new Promise((resolve, reject) => {
        const img = new Image()
        img.src = _URL.createObjectURL(file)
        img.onload = function() {
          _self.listObj[fileName] = { hasSuccess: false, uid: file.uid, width: this.width, height: this.height,type:file.type,name:file.name }
        }
        resolve(true)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.editor-slide-upload {
  margin-bottom: 20px;
  ::v-deep .el-upload--picture-card {
    // width: 100%;
  }
}
</style>
