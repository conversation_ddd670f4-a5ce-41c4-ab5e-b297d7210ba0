<template>
  <div>
    <div class="brain_content">
      <div class="header_btn">
        <el-button size="mini" @click="addNode">添加节点</el-button>
        <el-button size="mini" @click="onRemoveNode">删除节点</el-button>
      </div>
      <js-mind
        class="js_mind"
        :options="options"
        :values="mind"
        ref="jsMind"
        height="300px"
      ></js-mind>
    </div>
    <el-row style="text-align: right; margin-top: 30px">
      <el-button
        style="margin-top: 12px"
        type="info"
        size="mini"
        @click="cancelClick"
        >取消</el-button
      >
      <el-button
        style="margin-top: 12px"
        type="primary"
        size="mini"
        @click="xMindClick"
        >确定</el-button
      >
    </el-row>
  </div>
</template>

<script>
import "../../../../public/jsmind/jsmind.menu";
import { addOrUpdateAPI } from "@/api/grammar/xmind";

export default {
  name: "DialogForm",
  props: ["DialogFlag", "row", "rowId"],
  data() {
    return {
      // 思维导图
      form: {
        valueCascader: [],
      },
      optionsCascader: [],
      drawer: false,
      direction: "rtl",
      theme_value: "",
      mind: {
        meta: {
          name: "",
          author: "",
          version: "",
        },
        format: "node_tree",
        data: {
          id: "root",
          topic: "请输入标题",
          children: [],
        },
      },
      options: {
        container: "jsmind_container", // [必选] 容器的ID
        editable: false, // [可选] 是否启用编辑
        theme: "clouds", // [可选] 主题
        support_html: true, // 是否支持节点里的HTML元素
        view: {
          engine: "canvas", // 思维导图各节点之间线条的绘制引擎
          hmargin: 100, // 思维导图距容器外框的最小水平距离
          vmargin: 50, // 思维导图距容器外框的最小垂直距离
          line_width: 1, // 思维导图线条的粗细
          line_color: "#C18AFB", // 思维导图线条的颜色
        },
        menuOpts: {
          showMenu: false,
          injectionList: [
            {
              target: "edit",
              text: "编辑节点",
              callback: function (node) {
                console.log(node);
              },
            },
            {
              target: "addChild",
              text: "添加子节点",
              callback: function (node) {
                console.log(node);
              },
            },
            {
              target: "addBrother",
              text: "添加兄弟节点",
              callback: function (node) {
                console.log(node);
              },
            },
            {
              target: "delete",
              text: "删除节点",
              callback: function (node, next) {
                console.log(node);
              },
            },
            {
              target: "screenshot",
              text: "下载导图",
              callback: function (node, next) {
                console.log(node);
              },
            },
            {
              target: "showAll",
              text: "展开全部节点",
              callback: function (node, next) {
                console.log(node);
              },
            },
            {
              target: "hideAll",
              text: "收起全部节点",
              callback: function (node, next) {
                console.log(node);
              },
            },
          ],
        },
      },
      isShow: true,
      isLoad: false,
      hasNoteStatus: "",
    };
  },
  watch: {
    DialogFlag() {
      this.dialogXmind = this.DialogFlag;
    },
  },
  mounted() {
    // 阻止浏览器默认右键事件
    document.oncontextmenu = function () {
      return false;
    };
    this.$nextTick(() => {
      this.jm = this.$refs.jsMind.jm;
      this.jm.enable_edit();
      if (this.row && this.row.data) {
        this.mind.data = this.row.data;
        this.jm.show(this.mind);
      } else {
        const rootId = this.jm.get_root().id;
        if (rootId) {
          this.jm.update_node(rootId, this.row.name || "");
        }
      }
    });
  },
  created() {},
  methods: {
    // 思维导图
    xMindClick() {
      const mind_data = this.jm.get_data("node_tree");
      const mind_string = jsMind.util.json.merge(mind_data);
      this.mind = mind_string;

      // 包裹在 noteData 对象中
      const wrappedContent = {
        noteData: {
          content: this.mind.data.children,
        },
      };
      let hasThreeLevels = false;
      let checkThreeLevels = (node) => {
        if (node.children && node.children.length > 0) {
          if (
            node.children.some(
              (child) => child.children && child.children.length > 0
            )
          ) {
            hasThreeLevels = true;
            return;
          }
          node.children.forEach((child) => {
            checkThreeLevels(child);
          });
        }
      };

      checkThreeLevels(this.mind.data);

      if (!hasThreeLevels) {
        this.$message.error("必须有至少三个层级的节点才能进行确定操作");
        return;
      }

      addOrUpdateAPI({
        knowledgeId: this.rowId,
        mindJson: JSON.stringify(this.mind),
        noteJson: JSON.stringify(wrappedContent),
      }).then((res) => {
        this.$emit("closeDialog");
        this.$emit("refreshList");
      });
    },
    cancelClick() {
      this.$emit("closeDialog");
    },

    // 新增节点
    addNode() {
      this.get_selected_nodeid();
      var selectedNode = this.jm.get_selected_node();

      // 获取选中节点
      var selectedNode = this.jm.get_selected_node();
      if (!selectedNode) {
        this.$message.error("请先选择一个节点");
        return;
      }

      // 检查子节点数量是否达到限制
      if (selectedNode.children && selectedNode.children.length >= 14) {
        this.$message.error("每个节点最多只能有 14 个子节点");
        return;
      }

      // 计算当前节点的层级
      let currentLevel = 1;
      let parent = selectedNode.parent;
      while (parent) {
        currentLevel++;
        parent = parent.parent;
      }

      // 检查层级是否达到限制
      if (currentLevel >= 4 && selectedNode.id !== "root") {
        this.$message.error("根节点之后最多只能有3层节点");
        return;
      }

      var nodeid = this.jsMind.util.uuid.newid();
      var topic = "请输入子节点名称（最多 60 个字符）";

      // 检查输入的字符数量是否超过 60 个
      if (topic.length > 60) {
        this.$message.error("节点名称不能超过 60 个字符");
        return;
      }
      let childCountAfterAdd = selectedNode.children
        ? selectedNode.children.length + 1
        : 1;
      if (
        (childCountAfterAdd >= 14 && selectedNode !== this.jm.get_root()) ||
        (currentLevel >= 4 && selectedNode.id !== "root")
      ) {
        this.$message.error("节点数量或层级超出限制");
        return;
      }
      this.jm.add_node(selectedNode, nodeid, topic);
      const newMind = { ...this.mind };

      newMind.data.children.push({
        id: nodeid,
        topic: topic,
      });

      this.mind = newMind;
    },
    // 删除节点
    onRemoveNode() {
      var selectedId = this.get_selected_nodeid();
      if (!selectedId) {
        this.$message.error("请先选择一个节点");
        return;
      }
      this.jm.remove_node(selectedId);
    },
    // 获取选中标签的 ID
    get_selected_nodeid() {
      var selectedNode = this.jm.get_selected_node();
      if (selectedNode) {
        return selectedNode;
      } else {
        return null;
      }
    },
  },
};
</script>

<style lang="less" scoped>
.brain_content {
  /deep/ jmnodes.theme-clouds jmnode {
    background-color: #f0fadd;
    color: #333;
  }

  /deep/ jmnodes.theme-clouds jmnode:hover {
    background-color: #d6e3bd;
  }

  /deep/ jmnodes.theme-clouds jmnode.selected {
    background-color: #b6d47e;
    color: #333;
  }

  height: 100%;
  padding: 10px;
  display: flex;
  flex-direction: column;

  .header_btn {
    height: 40px;
    line-height: 40px;
  }

  .js_mind {
    // flex: 1;
    width: 100%;
    height: 500px;
  }
}
</style>
