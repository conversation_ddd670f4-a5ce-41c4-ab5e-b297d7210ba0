<template>
    <div class="app-container">
       <!-- 搜索 -->
    <el-form :inline="true" ref="form" class="SearchForm" style="padding: 20px 30px 0;">
      <el-row>
        <el-col :span="8" >
          <el-form-item label="单词测试类型:">
            <el-select v-model="searchData.wordType" filterable value-key="value" placeholder="请选择" clearable>
              <el-option v-for="(item, index) in wordTypeData" :key="index" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8" >
          <el-form-item label="单词类型:">
            <el-select v-model="searchData.studyType" filterable value-key="value" placeholder="请选择" clearable>
              <el-option v-for="(item, index) in enTypeData" :key="index" :label="item.label" :value="item.code" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8" >
          <el-form-item label="正确率:">
                  <el-row>
                      <el-col :span="10">
                          <el-input style="width:70%" v-model="searchData.rightRateStart" :min="0" :max="100" :controls="false"></el-input>%
                          <!-- <el-input-number style="width:70%" v-model="searchData.rightRateStart" :min="0" :max="100" :controls="false"></el-input-number> %? -->
                      </el-col>
                      <el-col :span="2"> - </el-col>
                      <el-col :span="10">
                          <el-input style="width:70%" v-model="searchData.rightRateEnd" :min="0" :max="100" :controls="false"></el-input>%
                          <!-- <el-input-number style="width:70%" v-model="searchData.rightRateEnd" :min="0" :max="100" :controls="false"></el-input-number> % -->
                      </el-col>
                  </el-row>
                </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8" :xs="24">
          <el-form-item>
            <el-button type="warning" size="small" icon="el-icon-search" @click="onSearchTable()">搜索</el-button>
            <el-button size="small" icon="el-icon-refresh" @click="rest()">重置</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div class="SearchForm">
      <!-- 添加 -->
      <div class="btn-add">
        <!-- <el-button size="small" type="primary" icon="el-icon-plus" @click="onAdd">添加</el-button> -->
      </div>
      <!-- 列表 -->
      <el-table class="common-table" :data="tableData" stripe border :default-sort="{ prop: 'date', order: 'descending' }"
        v-loading="tableLoading">
        <el-table-column prop="id" label="编号" width="180" sortable></el-table-column>
        <el-table-column prop="studyType" label="单词类型"  sortable show-overflow-tooltip>
            <template slot-scope="scope">
                <span> {{ enTypeData.filter(item => item.code === scope.row.studyType )[0]?.label }}</span>
            </template>
        </el-table-column>
        <el-table-column prop="wordType" label="单词测试类型"  sortable show-overflow-tooltip>
            <template slot-scope="scope">
                <span> {{ wordTypeData.filter(item => item.value === scope.row.wordType )[0]?.label }}</span>
            </template>
        </el-table-column>

        <!-- <el-table-column prop="id" label="操作" sortable width="240">
          <template slot-scope="scope">
           <el-button type="success" size="small" icon="el-icon-edit-outline" @click="onUpdate(scope.row.id)">编辑</el-button>
           <el-button type="danger" size="small" icon="el-icon-edit-outline" @click="onDel(scope.row.id)">删除</el-button>
          </template>
        </el-table-column> -->
        <el-table-column prop="rightRateStart" label="正确率"  sortable show-overflow-tooltip>
          <template slot-scope="scope">
             {{ scope.row.rightRateStart }}% - {{ scope.row.rightRateEnd }}%
          </template>
        </el-table-column>
        <!-- <el-table-column prop="wordNumber" label="测试单词个数" sortable show-overflow-tooltip></el-table-column> -->
        <el-table-column prop="description" label="描述" sortable show-overflow-tooltip></el-table-column>
<!--        <el-table-column prop="analysis" label="测试分析"  sortable show-overflow-tooltip></el-table-column>-->
        <el-table-column prop="learningAdvice" label="学习建议"  sortable show-overflow-tooltip></el-table-column>
        <el-table-column prop="createTime" label="添加时间" width="180" sortable :show-overflow-tooltip="true"></el-table-column>
      </el-table>
    </div>
        <!-- 分页 -->
    <el-row>
      <el-col :span="20">
        <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
          layout="total, sizes, prev, pager, next, jumper" :total="totalItems" @size-change="handleSizeChange"
          @current-change="handleCurrentChange" />
      </el-col>
    </el-row>
    <!-- 新增 -->
    <DWordTypeAddForm :formDialogVisible="formDialogVisible" :id="id" @onClose="onClose"></DWordTypeAddForm>
    </div>
</template>
<script>
import DWordTypeAddForm from './dictationWordTypeForm.vue'
import dictationWordType from '@/api/dictationWordType'
   export default {
    components:{DWordTypeAddForm},
      data(){
        return {
          totalItems:0,
          tablePage: {
              currentPage: 1,
              size: 10
            },
            //搜索
            searchData:{
              rightRateStart:'',
              rightRateEnd:'',
              studyType:'',
              wordType:'',
            },
            tableData:[],
            enTypeData:[
                {label:'单音节',code:'DYJ1',type:'PD_WORD_TYPE'},
                {label:'多音节',code:'DYJ2',type:'PD_WORD_TYPE'},
                {label:'前后缀',code:'QHZ',type:'PD_WORD_TYPE'},
                // {label:'变形',code:'BX',type:'PD_WORD_TYPE'},
            ],
            wordTypeData:[
              {label:'发音',value:'0'},
              {label:'拼写',value:'1'},
            ],
            enStageData:[],
            tableLoading:false,
            formDialogVisible:false,
            id:''
        }
      },
      created(){
        this.getTableData()
      },
      methods:{
        onDel(id){
          this.$confirm("确定操作吗?", "删除单词", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          })
          .then(() => {
            dictationWordType.deleteById(id).then(res => {
            if(res.code === 20000){
              this.$message.success('删除成功')
              if(this.tableData.length <= 1 && this.tablePage.currentPage > 1){
                 this.tablePage.currentPage--
              }
              this.getTableData()
            }

          })
          })
          .catch((err) => {});

        },
        onSearchTable(){
          this.tablePage.currentPage = 1
          this.getTableData()
        },
          // 分页
        handleSizeChange(val) {
          this.tablePage.size = val;
          this.getTableData();
        },
        handleCurrentChange(val) {
          this.tablePage.currentPage = val;
          this.getTableData();
        },
        //重置
        rest() {
          for(let searchVal in this.searchData){
            this.searchData[searchVal] = ''
          }
          this.tablePage.currentPage = 1
          this.getTableData()
        },
        getTableData(){
          this.tableLoading = true;

          const data = {...this.tablePage,...this.searchData}
          dictationWordType.pdCourseConfigGetInfo(data).then(res => {
            if(res.code === 20000){
              this.tableData = res.data.data
              this.totalItems = Number(res.data.totalItems)
              this.tableLoading = false
            }

          })
        },
        //新增
        onAdd(){
            this.formDialogVisible = true
        },
        onUpdate(id){
            this.formDialogVisible = true;
            this.id = id

        },
        onClose(){
            this.id = ''
            this.formDialogVisible = false
            this.getTableData()
        },
      }
   }
</script>
<style lang="less" scoped>

</style>
