<template>
  <div class="app-container">
    <el-col :span="24" style="margin-bottom: 30px">
      <el-button
        type="success"
        icon="el-icon-plus"
        size="mini"
        @click="addBtn()"
        >新增</el-button
      >
    </el-col>
    <el-table
      class="common-table"
      v-loading="tableLoading"
      :data="tableData"
      style="width: 100%; margin-bottom: 20px"
      row-key="id"
      border
      default-expand-all
    >
      <el-table-column prop="id" label="ID"></el-table-column>
      <el-table-column
        prop="taskId"
        label="任务模块"
        :formatter="taskFormatter"
      />
      <el-table-column prop="withdrawnBonus" label="操作" width="480">
        <template slot-scope="scope">
          <el-button type="primary" size="mini" @click="editBtn(scope.row.id)"
            >编辑
          </el-button>
          <el-button
            type="danger"
            size="mini"
            @click="handleDelete(scope.row.id)"
            >删除
          </el-button>
        </template>
      </el-table-column>
      <el-table-column prop="guideVoice" label="引导语" show-overflow-tooltip />
    </el-table>
    <!-- 分页 -->
    <el-col :span="24" style="margin-bottom: 20px">
      <el-pagination
        :current-page="tablePage.currentPage"
        :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="tablePage.totalItems"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-col>

    <!-- 修改或者新增弹窗 -->
    <el-dialog title="编辑任务" :visible.sync="open" width="70%" @close="close">
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        label-width="130px"
        style="width: 70%"
      >
        <el-form-item label="任务模块：" prop="taskId">
          <el-select v-model="form.taskId">
            <el-option
              v-for="item in taskList"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="引导语：" prop="guideVoice">
          <el-input
            type="textarea"
            v-model="form.guideVoice"
            :autosize="{ minRows: 4, maxRows: 6 }"
          />
        </el-form-item>
        <el-form-item label="思维导图JSON：" prop="mindJson">
          <el-input
            type="textarea"
            v-model="form.mindJson"
            :autosize="{ minRows: 10, maxRows: 20 }"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import guideApi from "@/api/xi/guide";
import taskApi from "@/api/xi/xiTask";
import { pageParamNames } from "@/utils/constants";

export default {
  name: "grading",
  data() {
    return {
      taskList: [],
      dataQuery: {},
      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null,
      },
      open: false,
      tableData: [],
      form: {},
      // 表单校验
      rules: {
        guideVoice: [
          { required: true, message: "请输入引导语", trigger: "blur" },
        ],
        mindJson: [
          { required: true, message: "请输入思维导图json", trigger: "blur" },
        ],
        taskId: [{ required: true, message: "请选择", trigger: "blur" }],
      },
    };
  },
  created() {
    this.getPageList();
    this.getTaskList();
  },
  methods: {
    taskFormatter(row, column, cellValue, index) {
      for (let item of this.taskList) {
        if (item.id === cellValue) {
          return item.name;
        }
      }
      return null;
    },
    getTaskList() {
      taskApi.allList().then((res) => {
        this.taskList = res.data;
      });
    },
    handleDelete(id) {
      this.$confirm("确定要删除吗？", "删除段位", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        guideApi.delete(id).then((res) => {
          this.$message.success("删除成功！");
          this.getPageList();
        });
      });
    },
    addBtn() {
      this.reset();
      this.open = true;
    },
    editBtn(id) {
      this.reset();
      guideApi.detail(id).then((res) => {
        this.form = res.data;
        this.open = true;
      });
    },
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          guideApi.saveOrUpdate(this.form).then((response) => {
            this.$message.success("提交成功！");
            this.open = false;
            this.getPageList();
          });
        }
      });
    },
    getPageList() {
      this.tableLoading = true;
      this.dataQuery.pageNum = this.tablePage.currentPage;
      this.dataQuery.pageSize = this.tablePage.size;
      guideApi.list(this.dataQuery).then((res) => {
        this.tableData = res.data.data;
        this.tableLoading = false;
        // 设置后台返回的分页参数
        pageParamNames.forEach((name) =>
          this.$set(this.tablePage, name, parseInt(res.data[name]))
        );
      });
    },
    handlePage() {
      (this.tablePage = {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null,
      }),
        this.getPageList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dataQuery = {
        name: "",
        enable: null,
      };
      this.getPageList();
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val;
      this.getPageList();
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val;
      this.getPageList();
    },
    close() {
      this.open = false;
      this.reset();
    },
    reset() {
      this.form = {
        guideVoice: null,
        mindJson: null,
      };
      if (this.$refs["form"]) {
        this.$refs["form"].resetFields();
      }
    },
  },
};
</script>

<style>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.period-table td,
.period-table th {
  text-align: center;
}

@media (max-width: 767px) {
  .el-message-box {
    width: 80% !important;
  }
}

.el-tooltip__popper {
  max-width: 800px;
}
</style>
