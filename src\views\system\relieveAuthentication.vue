<template>
  <div class="app-container">
    <div class="SearchForm">
      <!-- 搜索 -->
      <el-form :inline="true" ref="form" label-width="120px">
        <el-form-item label="登录账号" prop="userMobile">
          <el-input size="small" v-model="query.userMobile" placeholder="请输入" clearable></el-input>
        </el-form-item>
        <el-form-item label="认证类型" prop="endDate">
          <el-select v-model="query.userRole" size="small" placeholder="请选择">
            <el-option v-for="status in statusList" :key="status.label" :label="status.label"
              :value="status.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="审核状态" prop="endDate">
          <el-select v-model="query.unbindStatus" size="small" placeholder="请选择">
            <el-option v-for="status in rstatusList" :key="status.label" :label="status.label"
              :value="status.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" size="small" @click="handleSearch">查询</el-button>
          <el-button size="small" @click="reast">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="SearchForm">
      <!--表单-->
      <el-table v-loadng="loading" class="common-table" :data="tableData">
        <el-table-column prop="verifiedUserCode" label="编号" width="180"></el-table-column>
        <el-table-column prop="realName" label="姓名" width="180"></el-table-column>
        <el-table-column label="操作" align="center">
          <template slot-scope="{ row }">
            <span style="color: #a2a2a2" v-if="row.unbindStatus == 3">已取消</span>
            <span style="color: #cd5c53" v-if="row.unbindStatus == 2">已拒绝</span>
            <span style="color: #58b94f" v-if="row.unbindStatus == 1">已通过</span>
            <el-button v-if="row.unbindStatus == 0" type="success" icon="el-icon-edit-outline" size="mini"
              @click="editCourse(row)">审核
            </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="unbindSource" label="解绑板块"></el-table-column>

        <el-table-column label="认证类型">
          <template slot-scope="{ row }">
            <span>{{ row.userRole == "Member" ? "个人" : "企业" }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="userMobile" label="登录账号"></el-table-column>
        <el-table-column prop="price" label="解除个人认证绑定确定书">
          <template slot-scope="{ row }">
            <el-image style="width: 50px; height: 50px; margin: 10px 0 20px" :src="row.certUrl" fit="fill" :lazy="true"
              :preview-src-list="[row.certUrl]" v-if="row.certUrl"></el-image>
            <div v-else>{{'-'}}</div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-col :span="20">
      <el-pagination :current-page="query.pageNum" :page-size="query.pageSize" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="total" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>
    <el-dialog title="审核" :visible.sync="isShow" width="30%" @close="close">
      <div>
        <el-row :gutter="20">
          <el-col :span="5" :offset="0">
            <div style="font-size: 14px; color: #808080">解绑认证书：</div>
          </el-col>
          <el-col :span="12" :offset="0">
            <div v-if="form.image">
              <el-image style="width: 100px; height: 100px; margin: 10px 0 20px" :src="form.image" fit="fill"
                :lazy="true" :preview-src-list="[form.image]"></el-image>
            </div>
            <span v-else>无</span>

          </el-col>
        </el-row>

        <div style="font-size: 14px; color: #808080; margin-bottom: 10px">
          审核原因
        </div>
        <div>
          <el-input v-model="form.approveRemake" placeholder="请输入" type="textarea" :rows="3" size="normal"
            clearable></el-input>
        </div>
      </div>
      <span slot="footer" style="display: flex; justify-content: center">
        <el-button type="primary" @click="btnOK(1)">通过</el-button>
        <el-button @click="btnOK(2)">拒绝</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import relieveAuthentication from "@/api/relieveAuthentication";
export default {
  name: "relieveAuthentication",
  data() {
    return {
      loading: false,
      // 分页
      total: 0,
      query: {
        pageNum: 1,
        pageSize: 10,
        userMobile: "",
        unbindStatus: "",
        userRole: "",
      },
      isShow: false,
      // 认证类型
      statusList: [
        {
          label: "全部",
          value: '',
        },
        {
          label: "企业",
          value: "Merchant",
        },
        {
          label: "个人",
          value: "Member",
        },
      ],
      // 审核状态
      rstatusList: [
        {
          label: "全部",
          value: '',
        },
        {
          label: "未审核",
          value: 0,
        },
        {
          label: "已取消",
          value: 3,
        },
        {
          label: "已通过",
          value: 1,
        },
        {
          label: "已拒绝",
          value: 2,
        },
      ],
      tableData: [],
      form: {},
      rules: {},
    };
  },
  //创建时调用的方法
  created() {
    this.initData();
  },
  mounted() {
    document.addEventListener("click", function (e) {
      if (e.target.className == "el-image-viewer__mask") {
        let close = document.querySelector(".el-icon-circle-close");
        close.click();
      }
    });
  },
  //方法区
  methods: {
    // 审核打开
    editCourse(row) {
      this.isShow = true;
      this.form.id = row.id;
      this.form.source = row.unbindSource == '商户平台' ? 1 : 2;
      this.form.image = row.certUrl;
    },
    handleSearch() {
      this.query.pageNum = 1;
      this.query.pageSize = 10;
      this.initData();
    },
    reast() {
      this.query = {
        pageNum: 1,
        pageSize: 10,
      };
      this.initData();
    },
    // 初始化数据
    async initData() {
      this.loading = true;
      let res = await relieveAuthentication.ystUnbindList(this.query);
      this.tableData = res.data.data;
      this.total = res.data.totalItems - 0;
      this.loading = false;
    },
    close() {
      this.form = {};
    },
    async btnOK(val) {
      let obj = {
        id: this.form.id,
        approveRemake: this.form.approveRemake,
        source: this.form.source,
      };
      if (val == 1) {
        obj.unbindStatus = 1;
        await relieveAuthentication.approveYstUnbind(obj);
        // 通过
      } else {
        obj.unbindStatus = 2;
        await relieveAuthentication.approveYstUnbind(obj);
        // 拒绝
      }
      this.$message({
        message: "操作成功",
        type: "success",
      });
      this.initData();
      this.isShow = false;
    },
    handleSizeChange(val) {
      this.query.pageSize = val;
      this.initData();
    },
    handleCurrentChange(val) {
      this.query.pageNum = val;
      this.initData();
    },
  },
};
</script>

<style scoped>
.common-table {
  margin-top: 20px;
}

.SearchForm {
  width: 100%;
  margin-bottom: 15px;
  background: #fff;
  border-radius: 6px;
  padding: 20px 30px;
  box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.2);
}

.choose {
  display: inline-block;
  margin-right: 12px;
}
</style>
