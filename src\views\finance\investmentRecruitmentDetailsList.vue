<!--招商招生明细-->
<template>
  <div class="app-container">
    <el-form :inline="true" class="container-card" label-width="110px" label-position="left">
      <el-row>
        <el-col :span="6" :xs="24">
          <el-form-item label="查看类型：">
            <el-select v-model="dataQuery.direction" placeholder="全部" @change="fetchData01()">
              <el-option v-for="item in [{ value: 1, label: '招商' }, { value: 0, label: '招生' }]" :key="item.value" :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6" :xs="24">
          <el-form-item label="详情类型：">
            <el-select v-model="dataQuery.detailsType" placeholder="全部" @change="fetchData01()">
              <el-option v-for="item in [{ value: 1, label: '账号详情' }, { value: 0, label: '消耗详情' }]" :key="item.value"
                :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6" :xs="24" v-if="dataQuery.direction === 1">
          <el-form-item label="商户类型：">
            <el-select v-model="dataQuery.merchantRoleTag" placeholder="全部" @change="fetchData01()">
              <el-option v-for="item in [{ value: 'Agent', label: '市级服务商' }, { value: 'Dealer', label: '托管中心' },
              { value: 'School', label: '门店' }, { value: 'Market', label: '推荐人' },
              { value: 'Division', label: '事业部' }, { value: 'Company', label: '分公司' }]"
                :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6" :xs="24" v-if="dataQuery.detailsType === 0">
          <el-form-item label="消耗类型：">
            <el-select v-model="dataQuery.consumType" placeholder="全部" @change="fetchData01()">
              <el-option v-for="item in [{ value: 1, label: '充值' }, { value: 0, label: '消耗' }]" :key="item.value" :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="6" :xs="24"
          v-if="dataQuery.direction === 1 || (dataQuery.direction === 0 && dataQuery.detailsType === 0)">
          <el-form-item label="商户编号：">
            <el-input id="merchantCode" v-model="dataQuery.merchantCode" name="id" placeholder="请输入商户编号" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="6" :xs="24" v-if="dataQuery.direction === 1">
          <el-form-item label="商户名称：">
            <el-input id="merchantName" v-model="dataQuery.merchantName" name="id" placeholder="请输入服务商名称" clearable />
          </el-form-item>
        </el-col>

        <el-col :span="6" :xs="24" v-if="dataQuery.direction === 0">
          <el-form-item label="学员编号：">
            <el-input id="merchantCode" v-model="dataQuery.studentCode" name="id" placeholder="请输入商户编号" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="6" :xs="24" v-if="dataQuery.direction === 0 && dataQuery.detailsType === 1">
          <el-form-item label="会员编号：">
            <el-input id="merchantName" v-model="dataQuery.memberCode" name="id" placeholder="请输入服务商名称" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="6" :xs="24" v-if="dataQuery.direction === 0 && dataQuery.detailsType === 1">
          <el-form-item label="姓名：">
            <el-input id="merchantName" v-model="dataQuery.realName" name="id" placeholder="请输入服务商名称" clearable />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="12" :xs="24">
          <el-form-item>
            <el-form-item label="变动时间：">
              <el-date-picker style="width: 100%;" v-model="RegTime" type="daterange" value-format="yyyy-MM-dd HH:mm:ss"
                align="right" unlink-panels range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" clearable>
              </el-date-picker>
            </el-form-item>
          </el-form-item>
        </el-col>
        <el-col :span="12" :xs="24" style="text-align: right">
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="fetchData01()">搜索</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-form :inline="true" style="margin-bottom: 20px;">
      <el-button type="warning" icon="el-icon-document-copy" @click="exportFlow()"
        v-loading="exportLoading">导出</el-button>
    </el-form>

    <el-table class="period-table" v-if="dataQuery.direction === 1 && dataQuery.detailsType === 1"
      v-loading="tableLoading" :data="tableData" style="width: 100%;margin-bottom: 20px;" row-key="id" border
      default-expand-all :tree-props="{ list: 'children', hasChildren: 'true' }">
      <el-table-column prop="merchantCode" label="商户编号"></el-table-column>
      <el-table-column prop="name" label="账号"></el-table-column>
      <el-table-column prop="merchantName" label="商户名称"></el-table-column>
      <el-table-column prop="roleTag" label="商户类型"></el-table-column>
      <el-table-column prop="refereeCode" label="上级账号"></el-table-column>
      <el-table-column prop="province" label="省">
        <template slot-scope="scope">
          <span>{{ scope.row.province }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="city" label="市">
        <template slot-scope="scope">
          <span>{{ scope.row.city }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="area" label="县">
        <template slot-scope="scope">
          <span>{{ scope.row.area }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="isEnable" label="账号状态">
        <template slot-scope="scope">
          <span v-if="scope.row.isEnable === -1" class="red">系统关闭</span>
          <span v-else-if="scope.row.isEnable === -3" class="red">终止</span>
          <span v-else-if="scope.row.isEnable === 1" class="green">开通</span>
          <span v-else class="red">暂停</span>
        </template>
      </el-table-column>
      <el-table-column prop="regTime" label="注册时间"></el-table-column>
    </el-table>

    <el-table class="period-table" v-if="dataQuery.direction === 1 && dataQuery.detailsType === 0"
      v-loading="tableLoading" :data="tableData" style="width: 100%;margin-bottom: 20px;" row-key="id" border
      default-expand-all :tree-props="{ list: 'children', hasChildren: 'true' }">
      <el-table-column prop="flowCode" label="交易流水号"></el-table-column>
      <el-table-column prop="merchantCode" label="商户编号"></el-table-column>
      <el-table-column prop="merchantName" label="商户名称"></el-table-column>
      <el-table-column prop="flowBeforeMoney" label="变动前金额"></el-table-column>
      <el-table-column prop="flowMoney" label="变动金额"></el-table-column>
      <el-table-column prop="flowAfterMoney" label="变动后金额">
        <template slot-scope="scope">
          <span>{{ scope.row.flowAfterMoney }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="addTime" label="交易时间">
        <template slot-scope="scope">
          <span>{{ scope.row.addTime }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="description" label="变动描述">
        <template slot-scope="scope">
          <span>{{ scope.row.description }}</span>
        </template>
      </el-table-column>
    </el-table>

    <el-table class="period-table" v-if="dataQuery.direction === 0 && dataQuery.detailsType === 1"
      v-loading="tableLoading" :data="tableData" style="width: 100%;margin-bottom: 20px;" row-key="id" border
      default-expand-all :tree-props="{ list: 'children', hasChildren: 'true' }">
      <el-table-column prop="studentCode" label="学员账号"></el-table-column>
      <el-table-column prop="memberCode" label="会员账号"></el-table-column>
      <el-table-column prop="realName" label="姓名"></el-table-column>
      <el-table-column prop="school" label="学校"></el-table-column>
      <el-table-column prop="merchantCode" label="门店账号"></el-table-column>
      <el-table-column prop="isFormal" label="是否正式学员">
        <template slot-scope="scope">
          <span v-if="scope.row.isFormal === '1'" class="green">是</span>
          <span v-else-if="scope.row.isFormal === '0'" class="red">否</span>
        </template>
      </el-table-column>
      <el-table-column prop="addTime" label="注册时间">
        <template slot-scope="scope">
          <span>{{ scope.row.addTime }}</span>
        </template>
      </el-table-column>
    </el-table>

    <el-table class="period-table" v-if="dataQuery.direction === 0 && dataQuery.detailsType === 0"
      v-loading="tableLoading" :data="tableData" style="width: 100%;margin-bottom: 20px;" row-key="id" border
      default-expand-all :tree-props="{ list: 'children', hasChildren: 'true' }">
      <el-table-column prop="flowCode" label="交易流水号"></el-table-column>
      <el-table-column prop="merchantCode" label="商户编号"></el-table-column>
      <el-table-column prop="studentCode" label="学员编号"></el-table-column>
      <el-table-column prop="beforeHours" label="变动前学时"></el-table-column>
      <el-table-column prop="userHours" label="变动学时"></el-table-column>
      <el-table-column prop="afterHours" label="变动后学时">
        <template slot-scope="scope">
          <span>{{ scope.row.afterHours }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="startTime" label="交易时间">
        <template slot-scope="scope">
          <span>{{ scope.row.startTime }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="remark" label="变动描述">
        <template slot-scope="scope">
          <span>{{ scope.row.remark }}</span>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <el-col :span="24" style="overflow-x: auto;" :xs="24">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>
  </div>
</template>

<script>
import merchantAccountFlowApi from "@/api/merchantAccountFlow";
import { pageParamNames } from "@/utils/constants";
import investmentRecruitmentDetailsListApi from "@/api/investmentRecruitmentDetailsList";

export default {
  name: "investmentRecruitmentDetailsList",
  data() {
    return {
      RegTime: '',
      dataQuery: {
        direction: 1,
        detailsType: 1,
        merchantRoleTag: 'Agent',
        consumType: 1
      },
      exportLoading: false,
      tableLoading: false,
      tableData: [],
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
    }
  },
  mounted() {
    this.fetchData01();
  },
  methods: {
    fetchData01() {
      this.tablePage = {
        currentPage: 1,
        size: this.tablePage.size,
        totalPage: null,
        totalItems: null
      }
      this.fetchData();
    },
    fetchData() {
      const that = this;
      that.tableLoading = true
      var a = that.RegTime;
      if (a != null) {
        that.dataQuery.startDate = a[0];
        console.log(that.dataQuery.startDate = a[0]);
        that.dataQuery.endDate = a[1];
      } else {
        that.dataQuery.startDate = '';
        that.dataQuery.endDate = '';
      }
      investmentRecruitmentDetailsListApi.getInvestmentRecruitmentDetails(
        that.tablePage.currentPage,
        that.tablePage.size,
        that.dataQuery).then(res => {
          that.tableLoading = false;
          that.tableData = res.data.data.data;
          // 设置后台返回的分页参数
          pageParamNames.forEach((name) =>
            that.$set(that.tablePage, name, parseInt(res.data.data[name]))
          );
        }).catch(err => { })
    },
    exportFlow() {
      const that = this;
      that.exportLoading = true;
      merchantAccountFlowApi.getInvestmentRecruitmentDetails(that.dataQuery).then(res => {
        const url = window.URL.createObjectURL(res);
        const link = document.createElement("a");
        link.style.display = "none";
        link.href = url; // 获取服务器端的文件名
        link.setAttribute("download", "资金商户流水.xls");
        document.body.appendChild(link);
        link.click();
        that.exportLoading = false;
      })
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val;
      this.fetchData();
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val;
      this.fetchData();
    },
  }
}
</script>

<style scoped></style>
