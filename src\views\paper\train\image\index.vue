<template>
  <div class="app-container">
    <el-form :model="dataQuery" ref="queryForm" :inline="true">
      <el-form-item label="名称：">
        <el-input v-model="dataQuery.name" clearable></el-input>
      </el-form-item>
      <el-form-item label="图库类型：" prop="questionType">
        <el-select v-model="dataQuery.questionType" clearable>
          <el-option v-for="(item, index) in questionTypeList" :key="index" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="难度：" prop="difficulty">
        <el-select v-model="dataQuery.difficulty" clearable>
          <el-option v-for="(item, index) in difficultyList" :key="index" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getPageList">查询</el-button>
      </el-form-item>
    </el-form>
    <el-col :span="24" style="margin-bottom: 30px">
      <el-button type="success" icon="el-icon-plus" size="mini" @click="addBtn()">新增</el-button>
    </el-col>
    <el-table class="common-table" v-loading="tableLoading" :data="tableData" style="width: 100%;margin-bottom: 20px;"
      row-key="id" border default-expand-all>
      <el-table-column prop="id" label="ID"></el-table-column>
      <el-table-column prop="questionType" label="题型" :formatter="questionTypeFormatter"></el-table-column>
      <el-table-column prop="withdrawnBonus" label="操作" width="480">
        <template slot-scope="scope">
          <el-button type="success" size="mini" icon="el-icon-edit-outline" @click="editBtn(scope.row.id)">编辑
          </el-button>
          <el-button type="danger" size="mini" icon="el-icon-delete" @click="delBtn(scope.row.id)">删除
          </el-button>
        </template>
      </el-table-column>
      <el-table-column prop="difficulty" label="难度" :formatter="difficultyFormatter"></el-table-column>
      <el-table-column prop="name" label="名称"></el-table-column>
      <!--      <el-table-column prop="image" label="图片" align="center">-->
      <!--        <template slot-scope="scope">-->
      <!--          <el-image v-for="item in scope.row.fileList" :src="item" :preview-src-list="[item]" class="img-lg"/>-->
      <!--        </template>-->
      <!--      </el-table-column>-->
    </el-table>
    <!-- 分页 -->
    <el-col :span="24" style="margin-bottom: 20px">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>

    <!-- 修改或者新增弹窗 -->
    <el-dialog title="编辑图库" :visible.sync="open" width="70%" @close="close">
      <el-form ref="form" :model="form" :rules="rules" label-width="120px" style="width: 70%;">
        <el-form-item label="名称：" prop="name">
          <el-input v-model="form.name" />
        </el-form-item>
        <el-form-item label="图库类型：" prop="questionType">
          <el-select v-model="form.questionType">
            <el-option v-for="(item, index) in questionTypeList" :key="index" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="难度：" prop="difficulty">
          <el-select v-model="form.difficulty">
            <el-option v-for="(item, index) in difficultyList" :key="index" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="图片" required>
          <DragUpload @handleSuccess="handleSuccess" @handleRemove="handleRemove" :file-list="fileList" :showTip="false"
            :limit="1" />
          <el-input type="hidden" v-model="form.image"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import imageApi from '@/api/paper/train/image'
import { pageParamNames } from '@/utils/constants'
import DragUpload from '../../../../components/Upload/DragUpload'
import { mapGetters, mapState } from 'vuex'

export default {
  components: { DragUpload },
  name: 'subject',
  data() {
    return {
      questionTypeList: [
        { label: '图片找不同题', value: 'VISUAL_CHOICE' },
        { label: '看图画画题', value: 'UPLOAD_DRAWING' }
      ],
      fileList: [],
      dataQuery: {
        questionType: null,
        difficulty: null
      },
      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      open: false,
      submitFalse:false,
      tableData: [],
      form: {},
      // 表单校验
      rules: {
        name: [{ required: true, message: '请输入名称', trigger: 'blur' }],
        questionType: [{ required: true, message: '请选择题库类型', trigger: 'blur' }],
        difficulty: [{ required: true, message: '请选择难度', trigger: 'blur' }]
      }
    }
  },
  created() {
    this.getPageList()
  },
  methods: {
    questionTypeFormatter(row, column, cellValue, index) {
      return this.enumFormat(this.questionTypeList, cellValue)
    },
    difficultyFormatter(row, column, cellValue, index) {
      return this.enumFormat(this.difficultyList, cellValue)
    },
    addBtn() {
      this.reset()
      this.open = true
      this.submitFalse = false
    },
    editBtn(id) {
      this.reset()
      imageApi.detail(id).then(res => {
        this.form = res.data
        this.form.fileList.forEach(a => {
          this.fileList.push({ url: a })
        })
        this.open = true
        this.submitFalse = false
      })
    },
    delBtn(id) {
      this.$confirm('确定要删除吗？', '删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        imageApi.delete(id).then(res => {
          this.$message.success('删除成功！')
          this.getPageList()
        })
      })
    },
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          this.form.fileList = []
          this.fileList.forEach(a => {
            this.form.fileList.push(a.url);
          })
          if(this.submitFalse){
            return
          }
          this.submitFalse = true
          imageApi.saveOrUpdate(this.form).then(response => {
            this.$message.success('提交成功！')
            this.open = false
            this.getPageList()
          })
        }
      })
    },
    getPageList() {
      this.tableLoading = true
      this.dataQuery.pageNum = this.tablePage.currentPage
      this.dataQuery.pageSize = this.tablePage.size
      imageApi.list(this.dataQuery).then(res => {
        this.tableData = res.data.data
        this.tableData.forEach(i => {
          i.image = this.aliUrl + i.image
        })
        this.tableLoading = false
        // 设置后台返回的分页参数
        pageParamNames.forEach(name =>
          this.$set(this.tablePage, name, parseInt(res.data[name]))
        )
      })
    },
    handlePage() {
      (this.tablePage = {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      }),
        this.getPageList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dataQuery = {
        questionType: null,
        difficulty: null
      }
      this.getPageList()
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val
      this.getPageList()
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val
      this.getPageList()
    },
    close() {
      this.open = false
      this.reset()
    },
    reset() {
      this.form = {
        difficulty: null,
        questionType: null,
        name: '',
        fileList: [],
      }
      this.fileList = []
    },
    handleSuccess(url) {
    },
    handleRemove(file) {
    }
  },
  computed: {
    ...mapGetters('enumItem', ['enumFormat', 'trainUrlFormat']),
    ...mapState('enumItem', {
      difficultyList: state => state.train.difficultyList,
    }),
  }
}
</script>

<style>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.period-table td,
.period-table th {
  text-align: center;
}

@media (max-width: 767px) {
  .el-message-box {
    width: 80% !important;
  }
}

.el-tooltip__popper {
  max-width: 800px;
}

.img-lg {
  width: 100px;
  height: 100px
}
</style>
