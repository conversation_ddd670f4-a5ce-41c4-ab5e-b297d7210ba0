<template>
  <div class="app-container">
    <el-form :inline="true" ref="queryForm" class="container-card" label-width="110px">
      <el-form-item label="学号：">
        <el-input v-model="dataQuery.studentCode" placeholder="请输入学号" clearable />
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-refresh" @click="resetQuery()">重置</el-button>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" type="primary" @click="search()">搜索</el-button>
      </el-form-item>
    </el-form>
    <el-table class="common-table" v-loading="tableLoading" :data="tableData" style="width: 100%;margin-bottom: 20px;"
      row-key="id" border default-expand-all>
      <el-table-column prop="id" label="ID"></el-table-column>
      <el-table-column prop="nickname" label="昵称" />
      <el-table-column prop="withdrawnBonus" label="操作" width="210">
        <template slot-scope="scope">
          <el-button type="primary" size="mini" @click="editBtn(scope.row.id)">查看
          </el-button>
        </template>
      </el-table-column>
      <el-table-column prop="studentCode" label="学号"></el-table-column>
      <el-table-column prop="grade" label="年级"></el-table-column>
      <el-table-column prop="address" label="地区"></el-table-column>
      <el-table-column prop="credits" label="学分"></el-table-column>
      <el-table-column prop="gold" label="金币"></el-table-column>
      <el-table-column prop="exp" label="经验值"></el-table-column>
    </el-table>
    <!-- 分页 -->
    <el-col :span="24" style="margin-bottom: 20px">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>

    <!-- 查看弹窗 -->
    <el-dialog title="查看" :visible.sync="open" width="90%" @close="close">
      <el-form ref="form" :model="form" label-width="80px" style="width: 70%;">
        <el-row>
          <el-col :xs="24" :span="8">
            <el-form-item label="昵称：">
              <el-input v-model="form.nickname" />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :span="8">
            <el-form-item label="年级：">
              <el-input v-model="form.grade" />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :span="8">
            <el-form-item label="地区：">
              <el-input v-model="form.address" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :xs="24" :span="8">
            <el-form-item label="学分：">
              <el-input v-model="form.credits" />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :span="8">
            <el-form-item label="金币：">
              <el-input v-model="form.gold" />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :span="8">
            <el-form-item label="经验值：">
              <el-input v-model="form.exp" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-radio-group v-model="tabPosition" style="margin-bottom: 30px;margin-left: 30px;margin-top: 10px"
          @change="handleChange">
          <el-radio-button label="1">积分明细</el-radio-button>
          <el-radio-button label="2">商品兑换</el-radio-button>
        </el-radio-group>
      </el-form>
      <div v-show="tabPosition === '1'">
        <el-table class="common-table" v-loading="flow.tableLoading" :data="flow.tableData"
          style="width: 80%;margin-bottom: 20px;margin-left: 30px" row-key="id" border default-expand-all>
          <el-table-column type="index" label="序号" width="50"></el-table-column>
          <el-table-column prop="createTime" label="日期"></el-table-column>
          <el-table-column prop="accountsType" label="获取途径" :formatter="typeFormatter"></el-table-column>
          <el-table-column prop="flowCredits" label="学分">
            <template slot-scope="scope">
              {{ scope.row.flowCredits > 0 ? '+' : scope.row.flowCredits < 0 ? '-' : '' }} {{ scope.row.flowCredits }}
                </template>
          </el-table-column>
          <el-table-column prop="flowExp" label="经验值">
            <template slot-scope="scope">
              {{ scope.row.flowExp > 0 ? '+' : scope.row.flowExp < 0 ? '-' : '' }} {{ scope.row.flowExp }} </template>
          </el-table-column>
          <el-table-column prop="flowGold" label="金币">
            <template slot-scope="scope">
              {{ scope.row.flowGold > 0 ? '+' : scope.row.flowGold < 0 ? '-' : '' }} {{ scope.row.flowGold }} </template>
          </el-table-column>
        </el-table>
        <!-- 分页 -->
        <el-row style="margin-left: 30px">
          <el-col :span="24">
            <el-pagination :current-page="flow.tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
              layout="total, sizes, prev, pager, next, jumper" :total="flow.tablePage.totalItems"
              @size-change="flowSizeChange" @current-change="flowCurrentChange" />
          </el-col>
        </el-row>
      </div>
      <div v-show="tabPosition === '2'">
        <el-table class="common-table" v-loading="exchange.tableLoading" :data="exchange.tableData"
          style="width: 80%;margin-bottom: 20px;margin-left: 30px" row-key="id" border default-expand-all>
          <el-table-column type="index" label="序号" width="50"></el-table-column>
          <el-table-column prop="createTime" label="兑换时间"></el-table-column>
          <el-table-column prop="id" label="订单号"></el-table-column>
          <el-table-column prop="goodsDto.name" label="商品名称"></el-table-column>
          <el-table-column prop="payType" label="兑换方式">
            <template slot-scope="scope">
              <span v-if="scope.row.payType === 1" class="1">金币支付</span>
              <span v-else class="2">其他</span>
            </template>
          </el-table-column>
          <el-table-column prop="validDays" label="有效期">
            <template slot-scope="scope">
              {{ scope.row.goodsDto.isAll ? '永久' : scope.row.goodsDto.validDays + '天' }}
            </template>
          </el-table-column>
        </el-table>
        <!-- 分页 -->
        <el-row style="margin-left: 30px">
          <el-col :span="24">
            <el-pagination :current-page="exchange.tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
              layout="total, sizes, prev, pager, next, jumper" :total="exchange.tablePage.totalItems"
              @size-change="exchangeSizeChange" @current-change="exchangeCurrentChange" />
          </el-col>
        </el-row>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import studentInfoApi from '@/api/xi/community/studentInfo'
import { pageParamNames } from '@/utils/constants'
import exchangerecordApi from '@/api/xi/exchangerecord'

export default {
  name: 'studentInfo',
  data() {
    return {
      typeList: [{ label: '知识复习', value: 'FX' }, { label: '签到', value: 'QD' },
      { label: '完成当日所有学习任务', value: 'ALLTASK' },
      { label: '学习总结', value: 'ZJ' }, { label: '量化目标', value: 'LH' }, { label: '段位继承', value: 'DWJC' },
      { label: '炫耀一下', value: 'XY' }, { label: '发送家长并炫耀一下', value: 'FSXY' }, { label: '每日作业', value: 'ZY' },
      { label: '段位升级', value: 'danUpdate' }, { label: '自我提升', value: 'TS' }, { label: '每日任务', value: 'MRRW' },
      { label: '赠送金币', value: 'ZSJB' }, { label: '礼包奖励', value: 'LBJL' }, { label: '评分反馈', value: 'PFFK' },
      { label: '家长签到分享', value: 'QDFX' }, { label: '休息超时', value: 'XXCS' }],
      tabPosition: '1',
      flow: {
        tableLoading: false,
        tableData: [],
        dataQuery: {
          studentCode: '',
          pageNum: '',
          pageSize: ''
        },
        // 分页
        tablePage: {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null
        }
      },
      exchange: {
        tableLoading: false,
        tableData: [],
        dataQuery: {
          studentCode: '',
          pageNum: '',
          pageSize: ''
        },
        // 分页
        tablePage: {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null
        }
      },
      dataQuery: {
        name: ''
      },
      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      open: false,
      tableData: [],
      form: {}
    }
  },
  created() {
    this.getPageList()
  },
  methods: {
    handleChange(val) {
      if (val === '1') {
        this.flow.tablePage.currentPage = 1
        this.flow.tablePage.size = 10
        this.getFlowPageList()
      }
      if (val === '2') {
        this.exchange.tablePage.currentPage = 1
        this.exchange.tablePage.size = 10
        this.getExchangePage()
      }
    },

    typeFormatter(row, column, cellValue, index) {
      for (let item of this.typeList) {
        if (item.value === cellValue) {
          return item.label
        }
      }
    },
    getFlowPageList() {
      this.flow.tableLoading = true
      this.flow.dataQuery.pageNum = this.flow.tablePage.currentPage
      this.flow.dataQuery.pageSize = this.flow.tablePage.size
      this.flow.dataQuery.studentCode = this.form.studentCode
      studentInfoApi.flowList(this.flow.dataQuery).then(res => {
        this.flow.tableData = res.data.data
        this.flow.tableLoading = false
        // 设置后台返回的分页参数
        pageParamNames.forEach(name =>
          this.$set(this.flow.tablePage, name, parseInt(res.data[name]))
        )
      })
    },
    getExchangePage() {
      this.exchange.tableLoading = true
      this.exchange.dataQuery.pageNum = this.exchange.tablePage.currentPage
      this.exchange.dataQuery.pageSize = this.exchange.tablePage.size
      exchangerecordApi.list(this.exchange.dataQuery).then(res => {
        this.exchange.tableData = res.data.data
        this.exchange.tableLoading = false
        // 设置后台返回的分页参数
        pageParamNames.forEach(name =>
          this.$set(this.exchange.tablePage, name, parseInt(res.data[name]))
        )
      })
    },
    search() {
      this.tablePage.currentPage = 1
      this.tablePage.size = 10
      this.getPageList()
    },
    handleDelete(id) {
      this.$confirm('确定要删除吗？', '删除段位', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        getmethodApi.delete(id).then(res => {
          this.$message.success('删除成功！')
          this.getPageList()
        })
      })
    },
    addBtn() {
      this.reset()
      this.open = true
    },
    editBtn(id) {
      this.reset()
      studentInfoApi.detail(id).then(res => {
        this.form = res.data
        this.open = true
        this.getFlowPageList()
      })
    },
    submitForm() {

    },
    getPageList() {
      this.tableLoading = true
      this.dataQuery.pageNum = this.tablePage.currentPage
      this.dataQuery.pageSize = this.tablePage.size
      studentInfoApi.list(this.dataQuery).then(res => {
        this.tableData = res.data.data
        this.tableLoading = false
        // 设置后台返回的分页参数
        pageParamNames.forEach(name =>
          this.$set(this.tablePage, name, parseInt(res.data[name]))
        )
      })
    },
    handlePage() {
      (this.tablePage = {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      }),
        this.getPageList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dataQuery = {
        name: '',
        enable: null
      }
      this.getPageList()
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val
      this.getPageList()
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val
      this.getPageList()
    },
    // 分页
    flowSizeChange(val) {
      this.flow.tablePage.size = val
      this.getFlowPageList()
    },
    flowCurrentChange(val) {
      this.flow.tablePage.currentPage = val
      this.getFlowPageList()
    },
    // 分页
    exchangeSizeChange(val) {
      this.exchange.tablePage.size = val
      this.getExchangePage()
    },
    exchangeCurrentChange(val) {
      this.exchange.tablePage.currentPage = val
      this.getExchangePage()
    },
    close() {
      this.open = false
      this.reset()
    },
    reset() {
      this.tabPosition = '1'
      this.form = {
        id: null,
        title: null,
        subTitle: null,
        num: undefined,
        routePath: null,
        sortNum: undefined,
        enable: true
      }
      if (this.$refs['form']) {
        this.$refs['form'].resetFields()
      }
    }
  }
}
</script>

<style>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.period-table td,
.period-table th {
  text-align: center;
}

@media (max-width: 767px) {
  .el-message-box {
    width: 80% !important;
  }
}

.el-tooltip__popper {
  max-width: 800px;
}
</style>
