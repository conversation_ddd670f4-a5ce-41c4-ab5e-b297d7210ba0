<template>
  <div class="app-container">
    <el-form :model="form" ref="form" label-width="100px" v-loading="formLoading" :rules="rules">
      <el-form-item label="所属试卷：" required >
        <el-input v-model="form.paperName" style="width: 80%" @click.native="addQuestion()"/>
        <el-input v-model="form.paperId" type="hidden"></el-input>
      </el-form-item>

      <el-form-item label="分值范围：" required>
          <div class="question-item-label" :key="index"  v-for="(item,index) in form.items">
            <el-input-number v-model="item.scopeMin" style="marginRight:5px;" :min="0"/>
            <el-input-number v-model="item.scopeMax" style="marginRight:5px;" :min="0"/>
            <el-button class="link-left" style="margin-left: 20px"  @click="inputClick(item,'content')">
              编辑/查看报告内容
            </el-button>
            <el-button style="marginLeft:5px" type="danger" size="mini" icon="el-icon-delete" @click="removeReport(index)"></el-button>
          </div>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="submitForm">提交</el-button>
        <el-button @click="resetForm">重置</el-button>
        <el-button @click="addItems()">添加范围</el-button>
      </el-form-item>
    </el-form>
    <el-dialog  :visible.sync="richEditor.dialogVisible"  append-to-body :close-on-click-modal="false" style="width: 100%;height: 100%"   :show-close="false" center>
    <div style="padding-bottom: 10px">
        <el-button @click="templateShow = true">模板参考</el-button>
      </div>
      <Ueditor @ready="editorReady"/>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="editorConfirm">确 定</el-button>
        <el-button @click="richEditor.dialogVisible = false">取 消</el-button>
      </span>
    </el-dialog>


    <el-dialog :visible.sync="paperPage.showDialog"  width="70%">
      <el-form :inline="true" ref="queryForm" class="container-card" label-width="100px">
        <el-form-item label="年级：">
          <el-select v-model="paperPage.dataQuery.grade" placeholder="年级" @change="levelChange" clearable>
            <el-option v-for="item in gradeList" :key="item.value" :value="item.value" :label="item.label"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="学科：">
          <el-select v-model="paperPage.dataQuery.subjectId" clearable>
            <el-option v-for="item in subjectFilter" :key="item.id" :value="item.id"
                       :label="item.name"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="试卷类型：">
          <el-select v-model="paperPage.dataQuery.paperType" clearable>
            <el-option v-for="item in paperTypeList"
                       :key="item.value" :value="item.value" :label="item.label"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="试卷状态：">
          <el-select v-model="paperPage.dataQuery.status" clearable>
            <el-option v-for="item in statusList"
                       :key="item.value" :value="item.value" :label="item.label"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button icon="el-icon-re" @click="resetQuery()">重置</el-button>
          <el-button type="primary" icon="el-icon-search" @click="search()">搜索</el-button>
        </el-form-item>
      </el-form>
      <el-table v-loading="paperPage.listLoading" :data="paperPage.tableData"
                ref="singleTable"
                highlight-current-row
                @current-change="selectCurrentChange"
                :row-class-name="tableRowClassName"
                style="width: 100%">
        <el-table-column prop="name" label="名称" show-overflow-tooltip/>
        <el-table-column prop="grade" label="年级名称" :formatter="gradeListFormatter"/>
        <el-table-column prop="subjectId" label="学科名称" :formatter="subjectFormatter"/>
        <el-table-column prop="paperType" label="试卷类型" :formatter="paperTypeFormatter"/>
        <el-table-column prop="score" label="总分"/>
        <el-table-column prop="status" label="试卷状态" :formatter="statusFormatter"/>
      </el-table>
      <!-- 分页 -->
      <el-col :span="24" style="margin-bottom: 20px">
        <el-pagination :current-page="paperPage.tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
                       layout="total, sizes, prev, pager, next, jumper"
                       :total="paperPage.tablePage.totalItems" @size-change="handleSizeChange" @current-change="handleCurrentChange"
        />
      </el-col>
      <span slot="footer" class="dialog-footer">
          <el-button @click="paperPage.showDialog = false">取 消</el-button>
          <el-button type="primary" @click="confirmSelect">确定</el-button>
     </span>
    </el-dialog>

    <el-dialog :visible.sync="templateShow"  width="70%">
      <report-template :q-type="form.type" @useTemplate="useTemplate" @handleClose="templateShow=false"></report-template>
    </el-dialog>
  </div>
</template>

<script>

import Ueditor from '@/components/Ueditor'
import { mapGetters, mapState } from 'vuex'
import Pagination from '@/components/Pagination'
import QuestionShow from '../question/components/Show'
import subjectApi from '@/api/paper/subject'
import paperApi from '@/api/paper/paper'
import reportApi from '@/api/paper/report'
import { pageParamNames } from '@/utils/constants'
import ReportTemplate from '@/views/paper/report/report-template'

export default {
  components: { ReportTemplate, Pagination, QuestionShow,Ueditor },
  data () {
    return {
      isAdd: true,
      templateShow:false,
      richEditor: {
        dialogVisible: false,
        object: null,
        parameterName: '',
        instance: null
      },
      contentShow:false,
      subjectFilter: null,
      paperTypeList:[{label: '固定试卷',value:1},{label: '时段试卷',value:2},{label: '任务试卷',value:3}],
      statusList:[{label: '未发布',value:0},{label: '已发布',value:1},{label: '已撤回',value:2}],
      formLoading: false,
      form: {
        paperId: null,
        paperName: null,
        grade: null,
        type:"SCORE",
        items:[{
          id:null,
          scopeMin: undefined,
          scopeMax: undefined,
          content: '',
        }],
        removeItems:[]
      },
      rules: {},
      paperPage: {
        selectData: null,
        showDialog: false,
        dataQuery:{
          grade: null,
          subjectId: null,
          paperType:null,
          status:null
        },
        // 分页
        tablePage: {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null
        },
        listLoading: true,
        tableData: [],
        total: 0,
      },
    }
  },
  created () {
    this.initSubject()
    let paperId = this.$route.query.paperId
    if (paperId && parseInt(paperId) !== 0) {
      this.isAdd = false;
      this.formLoading = true
      this.subjectFilter = this.subjectList
      this.getPaperReports(paperId);
    }
  },
  methods: {
    //获取试卷的报告
    getPaperReports(paperId){
      reportApi.detail(paperId,this.form.type).then(re => {
        this.form = re.data
        if (this.form.items.length===0){
          this.form.items=[{
            id:null,
            scopeMin: undefined,
            scopeMax: undefined,
            content: '',
          }]
        }
        this.form.removeItems = [];
        this.formLoading = false
      })
    },
    removeReport(index){
      let item = this.form.items[index];
      if (item.id!=null){
        this.form.removeItems.push(item.id);
      }
      this.form.items.splice(index,1)
    },
    addItems(){
      this.form.items.push({
        id:null,
        scopeMin: undefined,
        scopeMax: undefined,
        content: '',
      })
    },

    useTemplate(val){
      this.richEditor.instance.setContent(val)
      // 光标定位到Ueditor
      this.richEditor.instance.focus(true)
    },
    editorReady (instance) {
      this.richEditor.instance = instance
      let currentContent = this.richEditor.object[this.richEditor.parameterName]
      this.richEditor.instance.setContent(currentContent)
      // 光标定位到Ueditor
      this.richEditor.instance.focus(true)
    },
    inputClick (object, parameterName) {
      this.richEditor.object = object
      this.richEditor.parameterName = parameterName
      this.richEditor.dialogVisible = true
    },
    editorConfirm () {
      let content = this.richEditor.instance.getContent()
      this.richEditor.object[this.richEditor.parameterName] = content
      this.richEditor.dialogVisible = false
    },
    editContent(){
      this.contentShow = true;
    },
    gradeListFormatter(row, column, cellValue, index) {
      return this.subjectFormat(this.gradeList, cellValue)
    },
    subjectFormatter(row, column, cellValue, index) {
      return this.subjectFormat(this.subjectList, cellValue)
    },
    paperTypeFormatter(row, column, cellValue, index){
      return this.enumFormat(this.paperTypeList, cellValue)
    },
    statusFormatter(row, column, cellValue, index){
      return this.enumFormat(this.statusList, cellValue)
    },
    selectCurrentChange (val) {
      if (val){
        this.paperPage.selectData = val
        this.form.paperId = '';
      }else {
        this.paperPage.selectData = null
      }
    },
    initSubject() {
      subjectApi.pageList().then(res => {
        this.subjectList = res.data.data
      })
    },
    levelChange() {
      this.paperPage.dataQuery.subjectId = null
      this.subjectFilter = this.subjectList.filter(data => data.grade === this.paperPage.dataQuery.grade)
    },
    tableRowClassName({row, rowIndex}) {
      if (row.id === this.form.paperId) {
        return 'current-row';
      }
      return '';
    },
    addQuestion () {
      this.resetQuery();
      this.paperPage.showDialog = true;
    },
    confirmSelect () {
      if (this.paperPage.selectData){
        this.getPaperReports(this.paperPage.selectData.id);
      }
      if (this.form.paperId==null){
        this.$message.error("请选择试卷！")
        return;
      }
      this.paperPage.showDialog = false
    },
    search () {
      this.paperPage.listLoading = true
      this.paperPage.dataQuery.pageNum = this.paperPage.tablePage.currentPage;
      this.paperPage.dataQuery.pageSize = this.paperPage.tablePage.size;
      paperApi.pageList(this.paperPage.dataQuery).then(res => {
        this.paperPage.tableData = res.data.data;
        this.paperPage.listLoading = false
        // 设置后台返回的分页参数
        pageParamNames.forEach(name => this.$set(this.paperPage.tablePage, name, parseInt(res.data[name])))
      })
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.paperPage.dataQuery = {
        grade: null,
        subjectId: null,
        paperType:null,
        status:null
      }
      this.search()
    },
// 分页
    handleSizeChange(val) {
      this.paperPage.tablePage.size = val
      this.search()
    },
    handleCurrentChange(val) {
      this.paperPage.tablePage.currentPage = val
      this.search()
    },
    submitForm () {
      this.$refs.form.validate((valid) => {
        if (valid) {
          if (this.form.paperId === null) {
            return this.$message.error("请选择试卷");
          }
          if (this.form.scopeMin === null) {
            return this.$message.error("请输入最小范围");
          }
          if (this.form.scopeMax === null) {
            return this.$message.error("请输入最大范围");
          }
          if (this.form.content === null) {
            return this.$message.error("请编辑报告内容");
          }
          this.formLoading = true
          reportApi.edit(this.form).then(re => {
            if (re.success) {
              this.$message.success(re.message)
              this.$router.push({path: '/paper/report'})
            } else {
              this.$message.error(re.message)
              this.formLoading = false
            }
          }).catch(e => {
            this.formLoading = false
          })
        } else {
          return false
        }
      })
    },

    resetForm () {
      let lastId = this.form.id
      this.$refs['form'].resetFields()
      this.form = {
        paperId: null,
        paperName: null,
        grade: null,
        type:"SCORE",
        items:[{
          id:null,
          scopeMin: undefined,
          scopeMax: undefined,
          content: '',
        }],
      }
      this.form.id = lastId
    },
  },
  computed: {
    ...mapGetters('enumItem', ['enumFormat','subjectFormat','pageFormat']),
    ...mapState('enumItem', {
      gradeList: state => state.question.gradeList,
      questionType: state => state.question.typeList,
      editUrlEnum: state => state.question.editUrlEnum,
    }),
  }
}
</script>

<style>
.el-table .success-row{
  background: #e8f4ff;
}
</style>
<style lang="less" scoped>
.question-item-label {
  display: flex;
  margin-bottom: 12px;
}
</style>
