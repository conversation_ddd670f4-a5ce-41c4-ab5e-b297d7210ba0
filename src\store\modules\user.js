import authApi from '@/api/auth';
import courseApi from '@/api/training/course';
import { queryRestLearningSysCount, contractsNumber } from '@/api/partnerPromotion';
import { getToken, setToken, removeToken } from '@/utils/auth';
import avatorImg from '../../../static/image/avator.png';
import { Message } from 'element-ui';
import ls from '@/api/sessionStorage';

const user = {
  state: {
    // 在线表单查询页面缓存
    onlineFormCache: {},
    documentClientHeight: 100,
    documentClienWidth: undefined,
    globalId: '',
    user: '',
    status: '',
    JlbInfo: {},
    code: '',
    // payUrl: 'https://pay.dxznjy.com/',
    // payUrl: 'https://pay.dxznjy.com/',
    // payUrl: 'https://uat-dxpay.ngrok.dxznjy.com/',
    payUrl: 'http://*************:8000/',
    // payUrl: 'https://pay179.ngrok.dxznjy.com/',
    studyUrl: 'https://study.dxznjy.com/',
    // studyUrl: 'http://uat-learn.ngrok.dxznjy.com/',
    token: getToken(),

    name: '',
    nick: '',
    avatar: avatorImg,
    introduction: '',
    visitor: false,
    roles: [],
    perms: [],
    setting: {
      articlePlatform: []
    },
    examStatus: false,
    contractNum: 0,
    systemNumber: 0
  },

  mutations: {
    addOnlineFormCache: (state, data) => {
      state.onlineFormCache[data.key] = data.value;
    },
    setJlbInfo: (state, JlbInfo) => {
      state.JlbInfo = JlbInfo;
    },
    setpayUrl: (state, payUrl) => {
      state.payUrl = payUrl;
    },
    removeOnlineFormCache: (state, key) => {
      delete state.onlineFormCache[key];
    },
    clearOnlineFormCache: (state) => {
      state.onlineFormCache = {};
    },
    setClientHeight: (state, height) => {
      state.documentClientHeight = height;
    },
    setClientWidth: (state, width) => {
      state.documentClienWidth = width;
    },
    SET_CODE: (state, code) => {
      state.code = code;
    },
    SET_globalId: (state, globalId) => {
      state.globalId = globalId;
    },
    SET_TOKEN: (state, token) => {
      state.token = token;
    },
    SET_INTRODUCTION: (state, introduction) => {
      state.introduction = introduction;
    },
    SET_SETTING: (state, setting) => {
      state.setting = setting;
    },
    SET_STATUS: (state, status) => {
      state.status = status;
    },
    SET_NAME: (state, name) => {
      state.name = name;
    },
    SET_NICK: (state, nick) => {
      state.nick = nick;
    },
    SET_AVATAR: (state, avatar) => {
      state.avatar = avatar;
    },
    SET_ROLES: (state, roles) => {
      state.roles = roles;
    },
    SET_PERMS: (state, perms) => {
      state.perms = perms;
    },
    SET_VISITOR: (state, visitor) => {
      state.visitor = visitor;
    },
    SET_EXAMSTATUS: (state, examStatus) => {
      state.examStatus = examStatus;
    },
    SET_CONTRACTNUM: (state, contractNum) => {
      state.contractNum = contractNum;
    },
    SET_SYSTEMNUMBER: (state, systemNumber) => {
      state.systemNumber = systemNumber;
    }
  },

  actions: {
    loginNoCode({ commit }, userInfo) {
      const username = userInfo.username.trim();
      return new Promise((resolve, reject) => {
        authApi
          .loginByNoCode(username, userInfo.password, 8, '', user.state.globalId, userInfo.role)
          .then((response) => {
            const data = response.data;
            commit('SET_TOKEN', data.token);
            setToken(response.data.token);
            resolve(response.data.token);
          })
          .catch((error) => {
            console.log(error);
            reject(error);
          });
      });
    },
    // 用户名登录
    LoginByUsername({ commit }, userInfo) {
      localStorage.setItem('AllSignatures', JSON.stringify(false)); // 重置签署状态
      const username = userInfo.username.trim();
      return new Promise((resolve, reject) => {
        authApi
          .loginByUsername(username, userInfo.password, 8, userInfo.verificationCode, user.state.globalId, userInfo.role)
          .then((response) => {
            const data = response.data;
            if (data != null) {
              commit('SET_TOKEN', data.token);
              setToken(response.data.token);
              resolve(response.data.token);
              localStorage.setItem('firstLoginTag', data.firstLoginTag);
              localStorage.removeItem('loginContractSigningFlag');
            } else {
              Message({ message: response.message, type: 'error', duration: 2000 });
              reject(response.message);
            }
          })
          .catch((error) => {
            console.log(error);
            reject(error);
          });
      });
    },
    GetJlbUserInfo({ commit }) {
      return new Promise((resolve, reject) => {
        authApi
          .getJlbInfo()
          .then((response) => {
            const data = response.data;
            const host = window.location.host;
            const subdomain = host.split('.')[0];
            const isLocalhost = /^([0-9.]+|localhost):[0-9]+$/.test(host);
            const isNgrokDomain = host.includes('ngrok');
            const isTestDomain = host.includes('test');
            const isManageDomain = subdomain.includes('manage');
            commit('setJlbInfo', data);
            if (isManageDomain || isNgrokDomain || isTestDomain) {
            } else if (isLocalhost) {
              // let url = 'https://pay.dxznjy.com/';
              let url = 'http://*************:8000/';
              commit('setpayUrl', url);
            } else {
              let a = window.location.host.split('.')[0].slice(0, -1);
              let b = `https://${a}p.dxznjy.com/`;
              commit('setpayUrl', b);
            }
            // commit("SET_JLBNAME", data.jlbName);
            // commit("SET_JLBLOGO", data.isJlbLogo);
            resolve();
          })
          .catch((error) => {
            console.log(error);
            reject(error);
          });
      });
    },
    // 获取用户信息,
    GetUserInfo({ commit, state }) {
      return new Promise((resolve, reject) => {
        authApi
          .getUserInfo(state.token)
          .then((res) => {
            console.log(res, 22222222222);
            if (!res) reject('res is null');
            if (!res.data) reject('res.data is null');
            if (!res.data.perms || res.data.perms.length == 0 || !res.data.perms || res.data.perms.length == 0) {
              commit('SET_VISITOR', true);
            } else {
              commit('SET_VISITOR', false);
            }

            commit('SET_CODE', res.data.merchantCode);
            commit('SET_ROLES', res.data.roles);
            commit('SET_PERMS', res.data.perms);
            commit('SET_NICK', res.data.nick);
            commit('SET_NAME', res.data.username);
            window.localStorage.setItem('set_roles', JSON.stringify(res.data.roles));
            window.localStorage.setItem('sysUserRoles', JSON.stringify(res.data.sysUserRoles));
            window.localStorage.setItem('sysUserInfo', JSON.stringify(res.data));
            window.localStorage.setItem('loginMerchantCode', res.data.merchantCode);
            resolve(res);
          })
          .catch((error) => {
            reject(error);
          });
      });
    },

    // 登出
    LogOut({ commit, state }) {
      return new Promise((resolve, reject) => {
        authApi
          .logout(state.token)
          .then(() => {
            commit('SET_TOKEN', '');
            commit('SET_ROLES', []);
            removeToken();
            resolve();
          })
          .catch((error) => {
            reject(error);
          });
      });
    },

    // 前端 登出
    FedLogOut({ commit }) {
      return new Promise((resolve) => {
        commit('SET_TOKEN', '');
        removeToken();
        resolve();
      });
    },

    // 动态修改权限
    ChangeRoles({ commit }, role) {
      return new Promise((resolve) => {
        commit('SET_TOKEN', role);
        setToken(role);
        authApi.getUserInfo(role).then((response) => {
          const data = response.data;
          commit('SET_ROLES', data.roles);
          commit('SET_NAME', data.name);
          commit('SET_AVATAR', data.avatar);
          commit('SET_INTRODUCTION', data.introduction);
          resolve();
        });
      });
    },

    // 判断账号对应考试是否全部通过
    checkExamStatus({ commit, state }) {
      let sysUserRoles = JSON.parse(window.localStorage.getItem('sysUserRoles'));
      let roleTag = state.roles[0].val;
      let userParams = {
        userId: sysUserRoles[0].userId,
        roleTag
      };
      return new Promise((resolve, reject) => {
        courseApi
          .checkCourseCompleted(userParams)
          .then((res) => {
            commit('SET_EXAMSTATUS', res.data);
            resolve(res);
          })
          .catch((err) => {
            reject(err);
          });
      });
    },
    //获取合同数
    getContract({ commit }) {
      return new Promise(async (resolve) => {
        let res = await contractsNumber();
        commit('SET_CONTRACTNUM', res.data ?? 0);
        resolve(res.data);
      });
    },
    //获取系统数
    getSystem({ commit }) {
      return new Promise((resolve) => {
        queryRestLearningSysCount().then((res) => {
          commit('SET_SYSTEMNUMBER', res.data ?? 0);
        });
        resolve();
      });
    }
  }
};

export default user;
