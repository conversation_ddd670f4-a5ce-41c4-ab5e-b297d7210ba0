
import request from '@/utils/request'
import { Select } from 'element-ui'

export default {

  // 分页查询
  pageList(data) {
    return request({
      url: '/paper/web/ques/list',
      method: 'GET',
      params: data
    })
  },

  // 添加||编辑问题
  edit(data) {
    return request({
      url: '/paper/web/ques',
      method: data.id ? 'PUT':'POST',
      data
    })
  },

  // 删除题目
  deleteQuestion(ids) {
    return request({
      url:'/paper/web/ques?ids='+ids,
      method:'DELETE'
    })
  },

  // 问题详情
  select(id) {
    return request({
      url:'/paper/web/ques?id='+id,
      method:'GET'
    })
  },

  // 知识点树
  konwledgeTree(gradeId,subjectId) {
    return request({
      url:"/paper/web/knowledge/tree?gradeId="+gradeId+'&subjectId='+subjectId,
      method:'GET'
    })
  },
//随机获取题目
  randomList(data){
    return request({
      url: '/paper/web/ques/random',
      method: 'POST',
      data
    })
  },
//模板导入
  importData(data){
    return request({
      url: '/paper/web/ques/import',
      method: 'POST',
      data
    })
  },
  // 组合问题所选列表
  combinationList(data) {
    return request({
      url: '/paper/web/ques/combinationList',
      method: 'GET',
      params: data
    })
  },
  uploadJson(data) {
    return request({
      url: '/paper/web/ques/uploadJson',
      method: 'POST',
      data
    })
  },
}
