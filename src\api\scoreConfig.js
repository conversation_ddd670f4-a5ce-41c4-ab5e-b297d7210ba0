/**
 *
 */
import request from '@/utils/request'

export default {
  // 分页查询
  scoreList() {
    return request({
      url: '/znyy/stats/review/getFunReviewScoreConfigList',
      method: 'GET'
    })
  },

  // 新增
  addConfig(data) {
    return request({
      url: '/znyy/stats/review/addFunReviewScoreConfig',
      method: 'POST',
      data
    })
  },

  // 更新
  updateConfig(data) {
    return request({
      url: '/znyy/stats/review/editFunReviewScoreConfig',
      method: 'POST',
      data
    })
  },

  //删除
  deleteConfig(id) {
    return request({
      url: '/znyy/stats/review/deleteFunReviewScoreConfig?id=' + id,
      method: 'GET'
    })
  },

}
