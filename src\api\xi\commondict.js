import request from '@/utils/request'

export default {
  // 分页查询
  list(data) {
    return request({
      url: '/xi/web/dict/list',
      method: 'GET',
      params: data
    })
  },
  detail(id){
    return request({
      url: '/xi/web/dict',
      method: 'GET',
      params:{
        id:id
      }
    })
  },
//添加
  saveOrUpdate(data) {
    return request({
      url: '/xi/web/dict',
      method: 'POST',
      data
    })
  },
  //删除
  delete(id){
    return request({
      url: '/xi/web/dict',
      method: 'DELETE',
      params:{
        id:id
      }
    })
  }
}
