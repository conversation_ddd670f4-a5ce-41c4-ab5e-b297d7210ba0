<template>
  <div class="app-container">
    <el-form :inline="true" class="container-card" label-width="110px" label-position="left">
      <el-form-item label="推荐人编号：">
        <el-input v-model="dataQuery.merchantCode" placeholder="请输入推荐人编号：" clearable />
      </el-form-item>
      <el-form-item label="添加时间：">
        <el-date-picker style="width: 100%;" v-model="value1" value-format="yyyy-MM-dd HH:mm:ss" type="daterange" align="right"
                        unlink-panels range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" clearable
                       />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="fetchData01()">搜索</el-button>
      </el-form-item>
    </el-form>
    <el-col :span="24" style="margin-bottom: 30px">
<!--      "-->
      <el-button type="success" icon="el-icon-plus" size="mini" v-if="checkPermission(['b:profitaccount:marketProfitAccount:add'])" @click="clickAdd">添加</el-button>
    </el-col>
    <!-- 搜索 -->
    <div class="SearchForm">
      <!-- 表格 -->
      <el-table class="common-table" :data="tableData" stripe border >
        <el-table-column prop="marketPartner" label="推荐人编号" sortable/>
        <el-table-column prop="phone" label="商户手机号" align="center">
        </el-table-column>
        <el-table-column prop="id" label="操作" sortable width="200px">
          <template slot-scope="scope">
            <el-button type="success" size="mini" icon="el-icon-edit-outline" @click="handleUpdate(scope.row.id)">编辑</el-button>
            <el-button type="danger" size="mini" icon="el-icon-edit-outline" @click="deleteData(scope.row.id)">删除</el-button>
          </template>
        </el-table-column>
        <el-table-column prop="totalAmount" label="总金额" align="center">
        </el-table-column>
        <el-table-column prop="paidAmount" label="已付款金额" align="center">
        </el-table-column>
        <el-table-column prop="waitPaidAmount" label="待付款金额" align="center">
        </el-table-column>
        <el-table-column prop="profitAmount" label="收益" align="center">
        </el-table-column>
        <el-table-column prop="remark" label="备注" align="center">
        </el-table-column>
        <el-table-column prop="direction" label="描述" align="center">
        </el-table-column>
        <el-table-column prop="addTime" label="添加时间" align="center">
        </el-table-column>
        <el-table-column prop="updateTime" label="修改时间" align="center">
        </el-table-column>
      </el-table>
    </div>

<!--     分页-->
        <el-col :span="20">
          <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]" layout="total, sizes, prev, pager, next, jumper"
                         :total="tablePage.totalItems" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
        </el-col>

    <!-- 编辑或者添加弹窗 -->
    <el-dialog :title="addOrUpdate?'添加账户记录':'编辑账户记录'" :visible.sync="dialogVisible" width="70%" :close-on-click-modal="false"
               @close="close">
      <el-form :ref="addOrUpdate?'addMarketProfitAccount':'updateMarketProfitAccount'" :rules="rules" :model="addOrUpdate?addMarketProfitAccount:updateMarketProfitAccount"
               label-position="left" label-width="120px" style="width: 100%;">
        <el-form-item label="推荐人编号：" prop="marketPartner" >
          <el-col :xs="24" :lg="18">
            <el-input v-if="!addOrUpdate" v-model="updateMarketProfitAccount.marketPartner" />
            <el-input v-if="addOrUpdate" v-model="addMarketProfitAccount.marketPartner" />
          </el-col>
        </el-form-item>
        <el-form-item label="市级服务商手机号：" prop="phone" >
          <el-col :xs="24" :lg="18">
            <el-input v-if="!addOrUpdate" v-model="updateMarketProfitAccount.phone" />
            <el-input v-if="addOrUpdate" v-model="addMarketProfitAccount.phone" />
          </el-col>
        </el-form-item>
        <el-form-item label="总金额：" prop="totalAmount">
          <el-col :xs="24" :lg="18">
            <el-input v-if="!addOrUpdate" v-model="updateMarketProfitAccount.totalAmount" @blur="BlurText(updateMarketProfitAccount.totalAmount)"/>
            <el-input v-if="addOrUpdate" v-model="addMarketProfitAccount.totalAmount" @blur="BlurText(addMarketProfitAccount.totalAmount)"/>
          </el-col>
        </el-form-item>
        <el-form-item label="已付款金额：" prop="paidAmount" >
          <el-col :xs="24" :lg="18">
            <el-input v-if="!addOrUpdate" v-model="updateMarketProfitAccount.paidAmount" @blur="BlurText1(updateMarketProfitAccount.paidAmount)"/>
            <el-input v-if="addOrUpdate" v-model="addMarketProfitAccount.paidAmount" @blur="BlurText1(addMarketProfitAccount.paidAmount)"/>
          </el-col>
        </el-form-item>
        <el-form-item label="待付款金额：" prop="waitPaidAmount" >
          <el-col :xs="24" :lg="18">
            <el-input v-if="!addOrUpdate" v-model="updateMarketProfitAccount.waitPaidAmount" @blur="BlurText2(updateMarketProfitAccount.waitPaidAmount)"/>
            <el-input v-if="addOrUpdate" v-model="addMarketProfitAccount.waitPaidAmount" @blur="BlurText2(addMarketProfitAccount.waitPaidAmount)"/>
          </el-col>
        </el-form-item>
        <el-form-item label="收益：" prop="profitAmount" >
          <el-col :xs="24" :lg="18">
            <el-input v-if="!addOrUpdate" v-model="updateMarketProfitAccount.profitAmount" @blur="BlurText3(updateMarketProfitAccount.profitAmount)"/>
            <el-input v-if="addOrUpdate" v-model="addMarketProfitAccount.profitAmount" @blur="BlurText3(addMarketProfitAccount.profitAmount)"/>
          </el-col>
        </el-form-item>
        <el-form-item label="备注：" prop="remark" >
          <el-col :xs="24" :lg="18">
            <el-input v-if="!addOrUpdate" v-model="updateMarketProfitAccount.remark" type="textarea" />
            <el-input v-if="addOrUpdate" v-model="addMarketProfitAccount.remark" type="textarea" />
          </el-col>
        </el-form-item>
        <el-form-item label="描述：" prop="direction" >
          <el-col :xs="24" :lg="18">
            <el-input v-if="!addOrUpdate" v-model="updateMarketProfitAccount.direction" type="textarea" />
            <el-input v-if="addOrUpdate" v-model="addMarketProfitAccount.direction" type="textarea" />
          </el-col>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
<!--        -->
        <el-button v-if="addOrUpdate" size="mini" type="primary" @click="addActiveFun('addMarketProfitAccount')" >新增</el-button>
        <el-button v-if="!addOrUpdate" size="mini" type="primary" @click="updateMarketProfitAccountFun('updateMarketProfitAccount')">修改</el-button>
        <el-button size="mini" @click="close">关闭</el-button>
      </div>
    </el-dialog>

    <el-dialog :visible.sync="dialogUploadVisible" :close-on-click-modal="false">
      <img width="100%" :src="dialogImageUrl" alt>
    </el-dialog>
  </div>
</template>

<script>
import marketProfitAccountApi from '@/api/marketProfitAccount'
import {
  pageParamNames
} from "@/utils/constants";
import {
  ossPrClient
} from '@/api/alibaba'
import checkPermission from '@/utils/permission'
export default {
  data() {
    var validPhone = (rule, value, callback) => {
      if (!value) {
        callback(new Error("请输入电话号码"));
      } else if (value.length<11) {
        callback(new Error("请输入正确的11位手机号码"));
      } else {
        callback();
      }
    };
    return {
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      tableLoading: false,
      dataQuery: {
        merchantCode: '',
      },
      activeType: [], // 活动类型
      // 分页
      tableData: [], //表格数据
      dialogVisible: false, // 修改弹窗是否展示
      addOrUpdate: true, // 是新增还是修改
      addMarketProfitAccount: {}, // 新增Banner
      updateMarketProfitAccount: {}, // 修改数据
      rules: {
        marketPartner: [{
          required: true,
          message: "必填项",
          trigger: "change",
        }, ],
        phone: [{
          required: true,
          message: "必填项",
          trigger: "change",
        },{
          validator: validPhone,
          trigger: "blur",
        },],
        totalAmount: [{
          required: true,
          message: "必填项",
          trigger: "change",
        }],
        paidAmount: [{
          required: true,
          message: "必填项",
          trigger: "change",
        }],
        waitPaidAmount: [{
          required: true,
          message: "必填项",
          trigger: "change",
        }],
        profitAmount: [{
          required: true,
          message: "必填项",
          trigger: "change",
        }],
      },
      value1:'',//时间
      fileListPending: [], // 待处理已上传图片信息
      fileList: [], // 上传图片已有图片列表

      fileDetailListPending: [], // 待处理已上传图片信息
      fileDetailList: [], // 上传图片已有图片列表

      dialogUploadVisible: false,
      dialogImageUrl: '', // 上传图片预览

      uploadLoading: false, // 上传图片加载按钮
      fullscreenLoading: false, // 保存啥的加载

      content: '',
      isUploadSuccess: true, // 是否上传成功
      isShowRelevance: true, // 新增或修改是否展示关联产品

      radio: '0', //单选框状态 值必须是字符串
      gettime: '' //获取当前时间
    }

  },
  created() {
    this.fetchData01();
    ossPrClient();
  },
  methods: {
    checkPermission,
    fetchData01() {
      this.tablePage = {
        currentPage: 1,
        size: this.tablePage.size,
        totalPage: null,
        totalItems: null
      }
      this.fetchData()
    },
    // 查询+搜索课程列表
    fetchData() {
      const that = this
      if (that.value1!=null) {
        that.dataQuery.startDate = that.value1[0];
        that.dataQuery.endDate = that.value1[1];
      } else {
        that.dataQuery.startDate = '';
        that.dataQuery.endDate = '';
      }
      that.tableLoading = true
      marketProfitAccountApi.marketProfitAccountList(that.tablePage.currentPage,
        that.tablePage.size,
        that.dataQuery).then(res => {
        that.tableData = res.data.data
        that.tableLoading = false
        // 设置后台返回的分页参数
        pageParamNames.forEach(name => that.$set(that.tablePage, name, parseInt(res.data[name])))
      })
    },
    BlurText(e) {
      if(e!=undefined && e!='' && e!="" && e!=null){
        let boolean = new RegExp("^[1-9][0-9]*$").test(e);
        if (!boolean) {
          this.$message.warning("请输入正整数");
          if(this.addOrUpdate){
            this.addMarketProfitAccount.totalAmount = "";
          }else{
            this.updateMarketProfitAccount.totalAmount = "";
          }

        };
      }
    },
    BlurText1(e) {
      if(e!=undefined && e!='' && e!="" && e!=null){
        let boolean = new RegExp("^[1-9][0-9]*$").test(e);
        if (!boolean) {
          this.$message.warning("请输入正整数");
          if(this.addOrUpdate){
            this.addMarketProfitAccount.paidAmount = "";
          }else{
            this.updateMarketProfitAccount.paidAmount = "";
          }

        };
      }
    },
    BlurText2(e) {
      if(e!=undefined && e!='' && e!="" && e!=null){
        let boolean = new RegExp("^[1-9][0-9]*$").test(e);
        if (!boolean) {
          this.$message.warning("请输入正整数");
          if(this.addOrUpdate){
            this.addMarketProfitAccount.waitPaidAmount = "";
          }else{
            this.updateMarketProfitAccount.waitPaidAmount = "";
          }

        };
      }
    },
    BlurText3(e) {
      if(e!=undefined && e!='' && e!="" && e!=null){
        let boolean = new RegExp("^[1-9][0-9]*$").test(e);
        if (!boolean) {
          this.$message.warning("请输入正整数");
          if(this.addOrUpdate){
            this.addMarketProfitAccount.profitAmount = "";
          }else{
            this.updateMarketProfitAccount.profitAmount = "";
          }

        };
      }
    },
    //添加操作
    clickAdd() {
      this.addMarketProfitAccount = {}
      this.dialogVisible = true
      this.addOrUpdate = true
      this.$nextTick(() => this.$refs['addMarketProfitAccount'].clearValidate())
    },
    // 新增banner
    addActiveFun(ele) {
      const that = this
      that.$refs[ele].validate(valid => { // 表单验证
        if (valid) {
          const loading = this.$loading({
            lock: true,
            text: '新增分润账户',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          })
          marketProfitAccountApi.addMarketProfitAmount(that.addMarketProfitAccount).then(() => {
            that.dialogVisible = false
            loading.close()
            that.fetchData01()
            that.$message.success('新增成功')
          }, s => {
            if (s === 'error') {
              loading.close()
              that.$message.error('新增失败');
            }
          }).catch(err => {
            loading.close()
          })
        } else {
          console.log('error submit!!')
          //loading.close();
          return false
        }
      })
    },
    // 点击编辑按钮
    handleUpdate(id) {
      const that = this
      that.dialogVisible = true
      that.addOrUpdate = false
      marketProfitAccountApi.queryActive(id).then(res => {
        that.updateMarketProfitAccount = res.data
      },s=>{
        if(s==='error'){
          this.$message.info("回显失败");
        }
      }).catch(err => {
      })
    },
    // 状态改变事件
    change(radio) {
      if (radio == '1') {
        this.addMarketProfitAccount.isEnable = 1
      } else {
        this.addMarketProfitAccount.isEnable = 0
      }
    },
    // 修改banner图提交
    updateMarketProfitAccountFun(ele) {
      const that = this
      that.$refs[ele].validate(valid => { // 表单验证
        if (valid) {
          const loading = this.$loading({
            lock: true,
            text: '修改分润账户',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          })
          marketProfitAccountApi.updateMarketProfitAmount(
            that.updateMarketProfitAccount
          ).then(() => {
            that.dialogVisible = false
            loading.close()
             that.fetchData01()
            that.$message.success('修改成功');
          },s=>{
            if(s==='error'){
              loading.close();
              that.$message.error("修改失败");
            }
          }).catch(err => {
            // 关闭提示弹框
            loading.close()
          })
        } else {
          console.log('error submit!!')
          // loading.close();
          return false
        }
      })
    },

    //删除
    deleteData(id) {
      const that = this
      this.$confirm('确定操作吗?', '删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        marketProfitAccountApi.deleteProfitAccount(id).then(res => {
          that.fetchData01()
          that.$message.success('删除成功!')
        },s=>{
          if(s==='error'){
            that.$message.error("删除失败");
          }
        }).catch(err => {

        })
      }).catch(err => {

      })
    },
    // 删除上传图片
    handleRemove(file, fileList) {
      const that = this
      if (!that.addOrUpdate) {
        that.fileList = fileList
      } else {
        for (let a = 0; a < that.fileDetailList4.length; a++) {
          if (that.fileDetailList4[a].substring(7,17) == parseInt(file.uid/1000)){
            that.fileDetailList4.splice(a, 1)
          }
        }
      }
    },

    // 删除上传图片
    handleRemoveDetail(file, fileList) {
      const that = this
      if (!that.addOrUpdate) {
        that.fileDetailList = fileList
      } else {
        for (let a = 0; a < that.fileDetailList4.length; a++) {
          if (that.fileDetailList4[a].substring(7,17) == parseInt(file.uid/1000)){
            that.fileDetailList4.splice(a, 1)
          }
        }
      }
    },
    // 上传图片预览
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogUploadVisible = true
    },
    // 上传图片数量超限
    justPictureNum(file, fileList) {
      this.$message.warning(`当前限制选择1个文件`)
    },
    // 上传图片请求
    uploadPrHttp({
      file
    }) {
      this.uploadLoading = true
      const that = this
      const fileName = 'manage/' + Date.parse(new Date())
      that.$nextTick(function() {
        ossPrClient().put(fileName, file).then(({
          res,
          url,
          name
        }) => {
          if (res && res.status === 200) {
            console.log(`阿里云OSS上传图片成功回调1`, res, url, name)
            if (!that.addOrUpdate) {
              that.fileList.push({
                'uid': file.uid,
                'url': url
              })
            } else { // 新增上传图片
              that.fileList.push({
                name
              })
              that.addMarketProfitAccount.imageListFilePath = name
            }
            that.$nextTick(() => {
              that.uploadLoading = false
            })
          }
        }).catch(err => {
          that.$message.error('上传图片失败请检查网络或者刷新页面')
          console.log(`阿里云OSS上传图片失败回调`, err)
        })
      })
    },

    uploadDetailHttp({
      file
    }) {
      this.uploadLoading = true
      const that = this
      const fileName = 'manage/' + Date.parse(new Date())
      that.$nextTick(function() {
        ossPrClient().put(fileName, file).then(({
          res,
          url,
          name
        }) => {
          if (res && res.status === 200) {
            console.log(`阿里云OSS上传图片成功回调1`, res, url, name)
            if (!that.addOrUpdate) {
              that.fileDetailList.push({
                'uid': file.uid,
                'url': url
              })
              that.updateMarketProfitAccount.url = name
              that.uploadLoading = false
            } else { // 新增上传图片
              that.fileDetailList.push({
                name
              })
              that.addMarketProfitAccount.url = name
            }
            that.$nextTick(() => {
              that.uploadLoading = false
            })
          }
        }).catch(err => {
          that.$message.error('上传图片失败请检查网络或者刷新页面')
          console.log(`阿里云OSS上传图片失败回调`, err)
          that.updateMarketProfitAccountupdateMarketProfitAccount.url = name
        })
      })
    },

    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val;
      this.fetchData();
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val;
      this.fetchData();
    },
    // 关闭弹窗
    close() {
      this.dialogVisible = false
    }
  }
}
</script>

<style>
.lh36 {
  line-height: 36px;
  font-size: 14px;
}

.SearchForm {
  width: 100%;
  margin-bottom: 15px;
  background: #fff;
  border-radius: 6px;
  padding: 5px 0;
  padding-left: 5px;
  box-shadow: 1px 1px 3px rgba(0, 0, 0, .2);
}

.btn-add {
  padding: 5px;
}

.red {
  color: red;
}

.green {
  color: green;
}

.course-table {
  text-align: center;
}

.course-table td,
.course-table th {
  padding: 5px 0;
  text-align: center;
}

.course-table button {
  padding: 2px;
}

.icon-stop {
  float: left;
  display: inline-block;
  width: 12px;
  height: 12px;
  background: url('../../icons/stop.png') no-repeat top center/contain;
}

.mt22 {
  margin-top: 22px;
}

@media screen and (max-width: 767px) {
  .el-upload-list--picture-card .el-upload-list__item,
  .el-upload--picture-card{
    width: 100px;
    height: 100px;
    line-height: 100px;
  }
  .el-message-box{
    width: 80%!important;
  }
}

</style>
