/**
 * 推广人相关接口
 */
import request from '@/utils/request'

export default {
  // 新增
  addPromoter(data) {
    return request({
      url: '/znyy/promoter/save',
      method: 'POST',
      data
    })
  },
  // 修改回显
  echoPromoter(id) {
    return request({
      url: '/znyy/promoter/check/detail/' + id,
      method: 'GET'
    })
  },
  // 编辑
  updatePromoter(data) {
    return request({
      url: '/znyy/promoter/save',
      method: 'POST',
      data
    })
  },
  // 获取银行分类列表
  categoryType(bankType) {
    return request({
      url: '/znyy/bvstatus/' + bankType,
      method: 'GET'
    })
  },
  // 加载符近的托管中心
  loadOtherExperienceStores(lat, lon) {
    return request({
      url: '/znyy/dealer/load/dealer?lat=' + lat + '&lon=' + lon,
      method: 'GET'
    })
  },


}
