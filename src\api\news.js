/**
 * 商品相关接口
 */
import request from '@/utils/request'
// import request from '@/utils/request'

export default {

  // 商品分页查询
  newsList(pageNum, pageSize,params) {
    return request({
      url: `/znyy/notice/query/page/`+ pageNum +'/' + pageSize,
      method: 'GET',
      params:params
    })
  },

  NoticeNum() {
    return request({
      url: '/znyy/notice/getNoticeNum',
      method: 'GET'
    })
  },

 

  // 通知新增
  addNotice(title,contents,userRole) {
    return request({
      url: '/znyy/notice/create',
      method: 'POST',
      data: {
        title: title,
        contents: contents,
        userRole: userRole
      }
    })
  },

  updateNotice(id,title,contents,userRole) {
    return request({
      url: '/znyy/notice/update',
      method: 'PUT',
      data: {
        id: id,
        title: title,
        contents: contents,
        userRole: userRole
      }
    })
  },
  // 删除
  deleteNotice(id) {
    return request({
      url: '/znyy/notice/delect/' + id,
      method: 'DELETE'
    })
  },


   lookNotice(id) {
    return request({
      url: '/znyy/notice/show/notice/' + id,
      method: 'GET'
    })
  },

  readNotice(readIds){
    return request({
      url: '/znyy/notice/make/read',
      method: 'PUT',
      params: {
        readIds: readIds
      }
    })
  },

  getoneNotice(){
    return request({
      url: '/znyy/notice/getOneNotice',
      method: 'GET'
    })
  }

}
