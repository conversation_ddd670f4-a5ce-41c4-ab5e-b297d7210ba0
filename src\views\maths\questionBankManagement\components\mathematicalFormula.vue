<template>
  <div>
    <!-- <Editor style="height: auto" :defaultConfig="editorConfig" v-model="html" @onChange="onChange" @onCreated="onCreated" /> -->
    <div v-html="processedHtml"></div>
    <!-- <div>{{ processedHtml }}</div> -->
  </div>
</template>

<script>
  import { Boot } from '@wangeditor/editor';
  import { Editor, Toolbar } from '@wangeditor/editor-for-vue';
  import formulaModule from '@wangeditor/plugin-formula';
  import katex from 'katex';
  // 注册公式插件
  Boot.registerModule(formulaModule);

  export default {
    name: 'MyEditorWithWordPaste',
    components: { Editor, Toolbar },
    props: {
      formulaText: {
        type: String,
        default: ''
      }
    },
    watch: {
      formulaText: {
        immediate: true,
        handler(newVal) {
          console.log('🚀 ~ handler ~ newVal:', newVal);
          this.html = newVal; // 确保在数据变化时重新渲染公式
        }
      }
    },
    data() {
      return {
        editor: null,
        html: this.formulaText,
        toolbarConfig: {
          insertKeys: {
            index: 0,
            keys: ['insertFormula']
          }
        },
        editorConfig: {
          placeholder: '请粘贴 Word 内容...',
          MENU_CONF: {
            // 公式配置
            insertFormula: {
              // 自定义公式预设
              presetFormulas: ['E = mc^2', 'a^2 + b^2 = c^2', '\\frac{1}{2}', '\\frac{1}{3}', '\\sqrt{a^2 + b^2}', '\\sqrt[3]{64}']
            },
            uploadImage: {
              server: '/api/upload', // 替换为你的图片上传接口
              fieldName: 'file',
              maxFileSize: 10 * 1024 * 1024, // 10MB
              allowedFileTypes: ['image/*'],
              customInsert(res, insertFn) {
                // res 即服务端的返回结果
                const url = res.data.url;
                insertFn(url);
              }
            }
          },
          // 粘贴相关配置
          paste: {
            // 粘贴过滤配置
            filterStyle: false, // 不过滤 style 样式
            // 忽略图片
            ignoreImg: true,
            // 自定义处理粘贴内容
            customPaste(editor, event) {
              const html = event.clipboardData.getData('text/html');
              const text = event.clipboardData.getData('text/plain');
              if (html) {
                // 处理 Word 文档中的图片
                console.log('paste html:', html);
                const processedHtml = html.replace(/<img[^>]*src="data:image\/[^;]+;base64,[^"]+"[^>]*>/g, (imgTag) => {
                  // 提取 base64 数据
                  const base64Match = imgTag.match(/src="data:image\/([^;]+);base64,([^"]+)"/);
                  if (base64Match) {
                    const [, type, data] = base64Match;
                    // 将 base64 图片转换为 Blob
                    const byteCharacters = atob(data);
                    const byteArrays = [];
                    for (let i = 0; i < byteCharacters.length; i++) {
                      byteArrays.push(byteCharacters.charCodeAt(i));
                    }
                    const blob = new Blob([new Uint8Array(byteArrays)], { type: `image/${type}` });
                    // 创建临时 URL
                    const url = URL.createObjectURL(blob);
                    // 替换 img 标签的 src
                    return imgTag.replace(/src="[^"]*"/, `src="${url}"`);
                  }
                  return imgTag;
                });
                // 判断是否包含可能是 LaTeX 的内容
                if (processedHtml.includes('\\(') || processedHtml.includes('$$') || processedHtml.includes('\\frac')) {
                  // 阻止默认粘贴
                  event.preventDefault();

                  // 清理空格、转换必要转义（你可自定义更强的转换逻辑）
                  const cleanText = processedHtml
                    .replace(/\\\\/g, '\\') // 处理双反斜线
                    .replace(/\n/g, '<br>'); // 保留换行

                  // 插入进编辑器
                  // editor.txt.append(cleanText);
                  // 插入处理后的 HTML
                  editor.dangerouslyInsertHtml(processedHtml);
                  // 可选：强制触发 onchange
                  editor.config.onchange(editor.txt.html());

                  return false; // 阻止默认行为
                }
                // 默认允许
                return true;
              } else if (text) {
                console.log('paste text:', text);
                // 判断是否包含可能是 LaTeX 的内容
                if (text.includes('\\(') || text.includes('$$') || text.includes('\\frac')) {
                  // 阻止默认粘贴
                  event.preventDefault();

                  // 清理空格、转换必要转义（你可自定义更强的转换逻辑）
                  const cleanText = text
                    .replace(/\\\\/g, '\\') // 处理双反斜线
                    .replace(/\n/g, '<br>'); // 保留换行

                  // 插入进编辑器
                  editor.txt.append(cleanText);

                  // 可选：强制触发 onchange
                  editor.config.onchange(editor.txt.html());

                  return false; // 阻止默认行为
                }
                // 处理纯文本内容
                // editor.insertText(text)
                return true;
              }
              return false; // 使用默认的粘贴处理
            }
          }
        }
      };
    },
    mounted() {
      console.log('mounted', this.html);
      // 测试 KaTeX 是否正常工作
      try {
        const testFormula = katex.renderToString('\\frac{22}{7}', {
          throwOnError: false
        });
        // console.log("KaTeX test successful:", testFormula)
      } catch (error) {
        console.error('KaTeX test failed:', error);
      }
    },
    computed: {
      processedHtml() {
        return this.processFormulas(this.html);
      }
    },
    methods: {
      onCreated(editor) {
        this.editor = Object.seal(editor);
      },
      onChange(editor) {
        this.$nextTick(() => {
          if (window.MathJax) {
            window.MathJax.typesetPromise();
          }
        });
        // console.log("content changed:", editor.getHtml())
      },
      processFormulas(html) {
        if (!html) return html;

        // 1. 先去掉所有行尾的 \\
        html = html.replace(/\\\\\s*$/gm, '');
        // 2. 再去掉所有多余的 \\
        html = html.replace(/\\\\/g, '');

        // 3. 统一处理所有公式包裹
        // 支持 $...$、$$...$$、\(...\)、\[...\]
        const renderFormula = (tex, displayMode = false) => {
          try {
            return katex.renderToString(tex, {
              throwOnError: false,
              displayMode,
              strict: false,
              trust: true
            });
          } catch (e) {
            console.error('KaTeX error:', e, tex);
            return tex;
          }
        };

        // $$...$$ 块级公式
        html = html.replace(/\$\$([^$]+)\$\$/g, (m, tex) => renderFormula(tex, true));
        // \[...\] 块级公式
        html = html.replace(/\\\[(.+?)\\\]/g, (m, tex) => renderFormula(tex, true));
        // $...$ 行内公式
        html = html.replace(/\$([^\$]+)\$/g, (m, tex) => renderFormula(tex, false));
        // \(...\) 行内公式
        html = html.replace(/\\\((.+?)\\\)/g, (m, tex) => renderFormula(tex, false));

        return html;
      }
    },
    beforeDestroy() {
      const editor = this.editor;
      if (editor == null) return;
      editor.destroy();
    }
  };
</script>

<style lang="css">
  @import '~katex/dist/katex.min.css';

  .editor-container {
    max-width: 1000px;
    margin: 0 auto;
  }
</style>
