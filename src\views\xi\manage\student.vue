<template>
  <div class="app-container">
    <el-form
      :inline="true"
      ref="queryForm"
      class="container-card"
      label-width="110px"
    >
      <el-form-item label="姓名：">
        <el-input v-model="dataQuery.realName" placeholder="姓名" clearable />
      </el-form-item>
      <el-form-item label="学员编号：">
        <el-input
          v-model="dataQuery.studentCode"
          placeholder="学员编号"
          clearable
        />
      </el-form-item>
      <el-form-item label="年级：">
        <el-select v-model="dataQuery.grade" clearable>
          <el-option
            v-for="item in gradeList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-re" type="primary" @click="getPageList()"
          >查询</el-button
        >
      </el-form-item>
    </el-form>
    <el-col :span="24" style="margin-bottom: 30px">
      <el-button
        type="success"
        icon="el-icon-plus"
        size="mini"
        @click="addBtn()"
        >新增</el-button
      >
      <el-button
        type="warning"
        icon="el-icon-back"
        size="mini"
        @click="$router.push('/xi/manager')"
        >返回</el-button
      >
    </el-col>
    <el-table
      class="common-table"
      v-loading="tableLoading"
      :data="tableData"
      style="width: 100%; margin-bottom: 20px"
      row-key="id"
      border
      default-expand-all
    >
      <el-table-column prop="id" label="ID"></el-table-column>
      <el-table-column prop="studentName" label="姓名" />
      <el-table-column prop="withdrawnBonus" label="操作" width="480">
        <template slot-scope="scope">
          <el-button type="primary" size="mini" @click="editBtn(scope.row.id)"
            >查看
          </el-button>
          <el-button
            type="danger"
            size="mini"
            @click="handleDelete(scope.row.id)"
            >删除
          </el-button>
        </template>
      </el-table-column>
      <el-table-column prop="studentCode" label="学员编号" />
      <el-table-column prop="grade" label="年级" />
      <el-table-column prop="createTime" label="创建时间" />
    </el-table>
    <!-- 分页 -->
    <el-col :span="24" style="margin-bottom: 20px">
      <el-pagination
        :current-page="tablePage.currentPage"
        :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="tablePage.totalItems"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-col>

    <!-- 修改或者新增弹窗 -->
    <el-dialog title="用户编辑" :visible.sync="open" width="70%" @close="close">
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        label-width="130px"
        style="width: 70%"
      >
        <el-form-item label="学员编号：" prop="studentCode">
          <el-input
            v-model="form.studentCode"
            placeholder="学员编号"
            :disabled="form.id !== ''"
            @blur="handleBlur(form.studentCode)"
          />
        </el-form-item>
        <el-form-item label="姓名：" prop="studentName">
          <el-input
            v-model="form.studentName"
            placeholder="请先输入学员编号"
            readonly
          />
        </el-form-item>
        <el-form-item label="年级：" prop="grade">
          <el-input
            v-model="form.grade"
            placeholder="请先输入学员编号"
            readonly
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm" v-show="form.id === ''"
          >确 定</el-button
        >
        <el-button @click="open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import manageApi from "@/api/xi/manage";
import { pageParamNames } from "@/utils/constants";

export default {
  name: "manageStudent",
  data() {
    return {
      gradeList: [
        { grade: "18", value: "幼儿园" },
        { label: "一年级", value: 1 },
        { label: "二年级", value: 2 },
        { label: "三年级", value: 3 },
        { label: "四年级", value: 4 },
        { label: "五年级", value: 5 },
        { label: "六年级", value: 6 },
        { label: "七年级", value: 7 },
        { label: "八年级", value: 8 },
        { label: "九年级", value: 9 },
        { label: "高一", value: 10 },
        { label: "高二", value: 11 },
        { label: "高三", value: 12 },
      ],
      managerId: null,
      roleValList: [],
      dataQuery: {},
      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null,
      },
      open: false,
      tableData: [],
      form: {},
      // 表单校验
      rules: {
        studentCode: [
          { required: true, message: "请输入学员编号", trigger: "blur" },
        ],
        studentName: [
          { required: true, message: "请先输入学员编号", trigger: "blur" },
        ],
        grade: [
          { required: true, message: "请先输入学员编号", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    let id = this.$route.query.id;
    if (id) {
      this.managerId = id;
    } else {
      this.$message.error("请返回上级页面选择学管师！");
      return;
    }
    this.getPageList();
  },
  methods: {
    handleBlur(studentCode) {
      if (studentCode) {
        manageApi.preview(studentCode).then((res) => {
          this.form = res.data;
        });
      } else {
        this.form.studentName = "";
        this.form.grade = "";
      }
    },
    handleDelete(id) {
      this.$confirm("确定要删除吗？", "删除用户", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        manageApi.deleteStudent(id).then((res) => {
          this.$message.success("删除成功！");
          this.getPageList();
        });
      });
    },
    addBtn() {
      this.reset();
      this.open = true;
    },
    editBtn(id) {
      this.reset();
      manageApi.studentDetail(id).then((res) => {
        this.form = res.data;
        this.open = true;
      });
    },
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          manageApi
            .saveStudent(this.managerId, this.form.studentCode)
            .then((response) => {
              this.$message.success("提交成功！");
              this.open = false;
              this.getPageList();
            });
        }
      });
    },
    getPageList() {
      this.tableLoading = true;
      this.dataQuery.pageNum = this.tablePage.currentPage;
      this.dataQuery.pageSize = this.tablePage.size;
      this.dataQuery.managerId = this.managerId;
      manageApi.studentList(this.dataQuery).then((res) => {
        this.tableData = res.data.data;
        this.tableLoading = false;
        // 设置后台返回的分页参数
        pageParamNames.forEach((name) =>
          this.$set(this.tablePage, name, parseInt(res.data[name]))
        );
      });
    },
    handlePage() {
      (this.tablePage = {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null,
      }),
        this.getPageList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dataQuery = {
        name: "",
        enable: null,
      };
      this.getPageList();
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val;
      this.getPageList();
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val;
      this.getPageList();
    },
    close() {
      this.open = false;
      this.reset();
    },
    reset() {
      this.audioFileList = [];
      this.form = {
        id: "",
        studentCode: "",
      };
      if (this.$refs["form"]) {
        this.$refs["form"].resetFields();
      }
    },
  },
};
</script>

<style>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.period-table td,
.period-table th {
  text-align: center;
}

@media (max-width: 767px) {
  .el-message-box {
    width: 80% !important;
  }
}

.el-tooltip__popper {
  max-width: 800px;
}
</style>
