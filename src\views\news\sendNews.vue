<template>
    <div class="app-container" style="width: 70%;">

        <el-col :span="24" style="margin-bottom: 30px;">
            <div>
                <span>标题</span><el-input placeholder="请输入内容" v-model="title" style="width: 200px;margin-right: 40px;margin-left: 20px;"></el-input>
                <!-- <span>状态</span>
                <el-select v-model="value" placeholder="请选择" style="margin-left: 20px;">
                    <el-option v-for="item in options" :key="item.value" :label="item.label"
                        :value="item.value"></el-option>
                </el-select> -->
                <el-button type="primary" style="margin-left: 40px;" @click="index">搜索</el-button>
            </div>

            <!-- <el-button type="warning" icon="el-icon-document-copy" @click="openBigRecharge()" size="mini">学员充值时长
            </el-button>
            <el-button type="success" size="mini" icon="el-icon-edit" @click="jumpOpenCoursePackage(null)">开通课程包
            </el-button> -->
        </el-col>

        <el-button type="primary" @click="update">新增</el-button>

        <el-table class="common-table" :data="tableData" style="width: 100%;margin-bottom: 20px;margin-top: 30px;" border>
            <el-table-column prop="id" label="序号" width="180"></el-table-column>
            <el-table-column prop="title" label="标题" width="" show-overflow-tooltip></el-table-column>
            <el-table-column prop="memberPhone" label="操作" width="280">
                <template slot-scope="scope">
                    <el-button type="success" plain size="mini" @click="lookNotifications(scope.row)">查看</el-button>
                    <el-button type="primary" plain size="mini" @click="editNews(scope.row)">编辑</el-button>
                    <el-button type="danger" plain size="mini" @click="openDialog(scope.row)">删除</el-button>
                </template>
            </el-table-column>
            <!-- <el-table-column prop="realName" label="状态" width="140">
                <template slot-scope="scope">
                    <span>{{ scope.row.sendType==0?'未发送':'已读'}}</span>
                </template>
            </el-table-column> -->
            <el-table-column prop="memberPhone" label="可见范围" width="160">
                <template slot-scope="scope">
                    <span>{{ scope.row.userRole!='All'?(scope.row.userRole=='School'?'门店':'超级俱乐部'):'全部'}}</span>
                </template>
            </el-table-column>
        </el-table>


        <!-- 分页 -->
        <el-col :span="24" style="overflow-x: auto;" :xs="24">
            <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
                layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems"
                @size-change="handleSizeChange" @current-change="handleCurrentChange" />
        </el-col>

        <el-dialog
            title="提示"
            :visible.sync="dialogVisible"
            width="30%"
            :before-close="handleClose">
            <span>是否删除当前信息？</span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="deleteNews">确 定</el-button>
            </span>
        </el-dialog>

    </div>
</template>
  
<script>

import { Base64 } from '@/utils/base64'
import newsApi from '@/api/news'
import ls from '@/api/noticeEdit'
import qs from '@/api/noticeNews'
import { de } from 'pinyin/data/dict-zi-web'





export default {
    name: 'sendNews',
    data() {
        return {

            // 分页
            tablePage: {
                currentPage: 1,
                size: 10,
                totalPage: null,
                totalItems: null
            },
            grammarData: {
                studentCode: '',
                phase: '',
                studentName: ''
            },
            studentInfo: [],
            tableData: [],
            type: 1, // 消息类型

            options: [{
                value: '0',
                label: '未读'
            }, {
                value: 1,
                label: '已读'
            }],
            value: '',
            type:'', // 消息类型
            title:"", // 标题

            dialogVisible: false,
            id:''

        }
    },
    watch: {
        '$route': 'initData'
    },
    created(){},
    mounted() {
        this.index();
    },
    methods: {
        initData(){
            console.log('路由变化了')
            this.index();
        },
        newsType(val){
            this.type =val;
            this.index();
        },
        index(){
            let that = this;
            let data ={
                noticeType:this.type,
                title:this.title,
                isEnable:this.value
            }
            newsApi.newsList(that.tablePage.currentPage, that.tablePage.size,data).then((res) => {
                this.tableData = res.data.data;
                this.tablePage.totalItems = Number(res.data.totalItems);
            })
        },

        openDialog(row){
            this.dialogVisible = true;
            this.id = row.id;
        },

        // 删除
        deleteNews(){
            newsApi.deleteNotice(this.id).then((res) => {
                console.log(res)
				this.$message({message: '操作成功', type: 'success'})
                this.dialogVisible= false;
                this.index()
            })
        },
        // 编辑
        editNews(row){
            ls.setItem('notificationEditId', row.id);
            this.$router.push({
                path:'/news/newsEdit'
            });
        },
        // 分页
        handleSizeChange(val) {
            this.tablePage.size = val
            this.index()
        },
        handleCurrentChange(val) {
            this.tablePage.currentPage = val
            this.index()
        },

        update() {
            this.$router.push({
                path: '/news/newsAdd',
                query: { type: 1 }
            });
        },

        lookNotifications(row){
            qs.setItem('notificationId', row.id);
            this.$router.push({
                path:'/news/newContent'
            });
        }

    }
}
</script>
  
<style>
/* .container-card {
    padding: 15px 15px 15px 15px;
    margin-bottom: 15px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  } */

.period-table td,
.period-table th {
    text-align: center;
}

.mt20 {
    margin-top: 20px;
}

.red {
    color: red;
}

.green {
    color: green;
}

.demonstration_dev {
    margin-bottom: 10px;
    font-size: 18px;
}

@media screen and (max-width: 767px) {
    .recharge-dialog .el-dialog {
        width: 90% !important;
    }

    .el-message-box {
        width: 80% !important;
    }
}

.hidden-label .el-radio__label {
    display: inline-block;
    width: 0;
    visibility: hidden;
}

.inputpre {
    position: relative;
}

.btnpob {
    position: absolute;
    right: 0;
    top: 0;
}

</style>
  