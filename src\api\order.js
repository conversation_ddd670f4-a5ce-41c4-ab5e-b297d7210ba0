/**
 * 经销商相关接口
 */
import request from '@/utils/request'
// import request from '@/utils/request'

export default {

  // 商品分页查询
  queryOrderPage(pageNum, pageSize, data) {
    return request({
      url: '/mall/queryOrderPage?pageNum=' + pageNum + '&pageSize=' + pageSize,
      method: 'GET',
      params: {
        orderType: data.orderType,
        orderStatus: data.orderStatus,
        orderSource: data.orderSource
      }
    })
  },

  // 订单类型查询
  queryOrderType(data) {
    return request({
      url: '/mall/getOrderType',
      method: 'GET'
    })
  },
  // 订单状态查询
  queryOrderStatus(data) {
    return request({
      url: '/mall/getOrderStatus',
      method: 'GET'
    })
  },
  // 订单发货
  deliverGoods(data) {
    return request({
      url: '/mall/deliverGoods',
      method: 'PUT',
      data
    })
  },
  // 订单发货
  orderDetail(data) {
    return request({
      url: '/mall/queryOrderDetail/' + data,
      method: 'GET'

    })
  },
  // 订单来源类型
  getSourceType() {
    return request({
      url: '/mall/getOrderSource',
      method: 'GET'
    })
  },

  //  新增学习机经销商订单
  addMachineOrder(data) {
    return request({
      url: '/mall/order/giveMachineOrder',
      method: 'POST',
      data
    })
  },
  //  查询订单相关二维码
  queryQrcode(id, memberId) {
    return request({
      url: '/mall/queryQrcode',
      method: 'GET',
      params: {
        orderId: id,
        memberId: memberId
      }
    })
  }

}
