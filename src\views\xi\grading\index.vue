<template>
  <div class="app-container">
    <el-form :inline="true" ref="queryForm" class="container-card" label-width="110px">
      <el-form-item label="段位名称：">
        <el-input v-model="dataQuery.name" placeholder="请输入名称" clearable />
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-re" @click="resetQuery()">重置</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="search()">搜索</el-button>
      </el-form-item>
    </el-form>
    <el-col :span="24" style="margin-bottom: 30px">
      <el-button type="success" icon="el-icon-plus" size="mini" @click="addBtn()">新增</el-button>
    </el-col>
    <el-table class="common-table" v-loading="tableLoading" :data="tableData" style="width: 100%;margin-bottom: 20px;"
      row-key="id" border default-expand-all>
      <el-table-column prop="orderNum" type="index" label="序号" width="50"></el-table-column>
      <el-table-column prop="id" label="ID"></el-table-column>
      <el-table-column prop="withdrawnBonus" label="操作" width="220">
        <template slot-scope="scope">
          <el-button type="primary" size="mini" @click="editBtn(scope.row.id); getAfterDemotion(scope.row.credits)">编辑
          </el-button>
          <el-button type="danger" size="mini" @click="handleDelete(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="name" label="段位名称" />
      <el-table-column prop="credits" label="晋升所需学分" show-overflow-tooltip></el-table-column>
      <el-table-column prop="awardGold" label="奖励金币" show-overflow-tooltip></el-table-column>
      <el-table-column prop="awardCredits" label="奖励学分" show-overflow-tooltip></el-table-column>
      <el-table-column prop="awardExp" label="奖励经验" show-overflow-tooltip></el-table-column>
      <el-table-column prop="achieveText" label="段位达成提示" show-overflow-tooltip></el-table-column>
      <el-table-column prop="danGradingColor" label="段位经验条颜色" show-overflow-tooltip>
        <template slot-scope="scope">
          <div style="width:100%;height:68px" :style="getStyle(scope.row.danGradingColor)">
          </div>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <el-col :span="24" style="margin-bottom: 20px">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>

    <!-- 修改或者新增弹窗 -->
    <el-dialog title="编辑段位" :visible.sync="open" width="70%" @close="close">
      <el-form ref="form" :model="form" :rules="rules" label-width="120px" style="width: 70%;">
        <el-form-item label="段位名称：" prop="name">
          <el-input v-model="form.name" />
        </el-form-item>
        <el-form-item label="晋升学分：" prop="exp">
          <el-input-number v-model="form.credits" :min="0" @change="getAfterDemotion(form.credits)" />
        </el-form-item>
        <el-form-item label="奖励金币：" prop="awardGold">
          <el-input-number v-model="form.awardGold" :min="0" />
        </el-form-item>
        <el-form-item label="奖励学分：" prop="awardCredits">
          <el-input-number v-model="form.awardCredits" :min="0" />
        </el-form-item>
        <el-form-item label="奖励经验：" prop="awardExp">
          <el-input-number v-model="form.awardExp" :min="0" />
        </el-form-item>
        <el-form-item label="段位达成提示：" prop="achieveText">
          <el-input v-model="form.achieveText" />
        </el-form-item>
        <el-form-item label="段位经验条颜色：" prop="danGradingColor">
          <el-input v-model="form.danGradingColor" />
        </el-form-item>
        <el-form-item label="是否降级：" prop="isDemotion">
          <el-radio-group v-model="form.isDemotion">
            <el-radio :label="true">是</el-radio>
            <el-radio :label="false">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="降级后段位：" v-if="form.isDemotion" prop="afterDemotion">
          <el-col :xs="24" :span="18">
            <el-select style="width: 100%;" v-model="form.afterDemotion">
              <el-option v-for="(item, index) in gradingCodeArry" :key="index" :label="item.name" :value="item.id" />
            </el-select>
          </el-col>
        </el-form-item>
        <el-form-item label="所属大段位" prop="gradingCode">
          <el-col :xs="24" :span="18">
            <el-select style="width: 100%;" v-model="form.gradingCode">
              <el-option v-for="(item, index) in bigGradingCodeArry" :key="index" :label="item.gradingName"
                :value="item.gradingCode" />
            </el-select>
          </el-col>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import gradingApi from '@/api/xi/grading'
import { pageParamNames } from '@/utils/constants'
import appApi from "@/api/appVersionControl";


export default {
  name: 'grading',
  data() {
    return {
      dataQuery: {
        name: ''
      },
      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      open: false,
      tableData: [],
      form: {},
      bigGradingCodeArry: [],
      gradingCodeArry: [],
      // 表单校验
      rules: {
        name: [{ required: true, message: '请输入任务名称', trigger: 'blur' }],
        credits: [{ required: true, message: '请输入学分', trigger: 'blur' }],
      }
    }
  },
  created() {
    this.getBigGradingList()
    this.getPageList()
  },
  methods: {
    getStyle(color) {
      return 'background:' + color
    },
    getBigGradingList() {
      gradingApi.selectBigGradingList().then(res => {
        this.bigGradingCodeArry = res.data
        // console.log(this.appType)
      })
    },
    search() {
      this.tablePage = {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      }
      this.getPageList()
    },
    handleDelete(id) {
      this.$confirm('确定要删除吗？', '删除段位', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        gradingApi.delete(id).then(res => {
          this.$message.success('删除成功！')
          this.getPageList()
        })
      })
    },
    getAfterDemotion(credits) {
      //获取小于该学分的段位
      if (credits && credits > 0) {
        gradingApi.getLessCredits(credits).then(res => {
          this.gradingCodeArry = res.data;
        }).catch(err => { })
      }
    },
    addBtn() {
      this.reset();
      this.open = true;
    },
    editBtn(id) {
      this.reset();
      gradingApi.detail(id).then(res => {
        this.form = res.data;
        this.open = true;
      })
    },
    submitForm() {
      const that = this;
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (that.form.isDemotion == false) {
            that.form.afterDemotion = '';
          }
          gradingApi.saveOrUpdate(that.form).then(response => {
            that.$message.success('提交成功！')
            that.open = false
            that.getPageList()
          })
        }
      })
    },
    getPageList() {
      this.tableLoading = true
      this.dataQuery.pageNum = this.tablePage.currentPage
      this.dataQuery.pageSize = this.tablePage.size
      gradingApi.list(this.dataQuery).then(res => {
        this.tableData = res.data.data
        this.tableLoading = false
        // 设置后台返回的分页参数
        pageParamNames.forEach(name =>
          this.$set(this.tablePage, name, parseInt(res.data[name]))
        )
      })
    },
    handlePage() {
      (this.tablePage = {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      }),
        this.getPageList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dataQuery = {
        name: '',
        enable: null
      }
      this.getPageList()
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val
      this.getPageList()
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val
      this.getPageList()
    },
    close() {
      this.open = false
      this.reset()
    },
    reset() {
      this.form = {
        credits: 0,
        awardGold: 0,
        awardCredits: 0,
        awardExp: 0,
        afterDemotion: "",
        isDemotion: false
      }
      if (this.$refs['form']) {
        this.$refs['form'].resetFields();
      }
    }
  },
}
</script>

<style>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.period-table td,
.period-table th {
  text-align: center;
}

@media (max-width: 767px) {
  .el-message-box {
    width: 80% !important;
  }
}

.el-tooltip__popper {
  max-width: 800px;
}
</style>
