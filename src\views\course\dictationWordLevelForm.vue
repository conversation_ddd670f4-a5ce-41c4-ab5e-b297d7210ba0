<template>
  <div class="addForm">
    <el-dialog :title="id ? '修改词汇水平' : '添加词汇水平'" :visible.sync="dialogVisible" width="70%" :close-on-click-modal="false" @close="dialogClose">
      <el-form ref="form" :rules="rules" :model="form" label-position="left" label-width="120px" style="width: 100%">
        <el-form-item label="单词测试类型" prop="wordType">
          <el-col :xs="24" :lg="18">
            <el-select style="width: 100%" v-model="form.wordType" filterable value-key="value" placeholder="请选择">
              <el-option v-for="(item, index) in wordTypeData" :key="index" :label="item.label" :value="item.value" />
            </el-select>
          </el-col>
        </el-form-item>
        <el-form-item label="水平等级" prop="wordRank">
          <el-col :xs="24" :lg="18">
            <el-select style="width: 100%" v-model="form.wordRank" filterable value-key="value" placeholder="请选择">
              <el-option v-for="(item, index) in wordRankData" :key="index" :label="item.label" :value="item.value" />
            </el-select>
          </el-col>
        </el-form-item>
        <el-form-item label="正确率" prop="rightRateStart">
          <el-row>
            <el-col :span="4">
              <el-input-number style="width: 80%" v-model="form.rightRateStart" :min="0" :max="100" :controls="false"></el-input-number>
              %
            </el-col>
            <el-col :span="1">-</el-col>
            <el-col :span="4">
              <el-input-number style="width: 80%" v-model="form.rightRateEnd" :min="0" :max="100" :controls="false"></el-input-number>
              %
            </el-col>
          </el-row>
        </el-form-item>
        <el-form-item label="水平分析" prop="analysis">
          <el-col :xs="24" :lg="18">
            <el-input type="textarea" resize="none" :rows="4" v-model="form.analysis" />
          </el-col>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="dialogClose">关闭</el-button>
        <el-button size="mini" type="primary" @click="onSubmit">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
  import dictationWordLevel from '@/api/dictationWordLevel';
  export default {
    props: {
      id: {
        type: String,
        default: ''
      },
      formDialogVisible: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        dialogVisible: false,
        categoryTypeList: [],
        wordTypeData: [
          { label: '发音', value: '0' },
          { label: '拼写', value: '1' }
        ],
        wordRankData: [
          { label: 'Lv1', value: 'Lv1' },
          { label: 'Lv2', value: 'Lv2' },
          { label: 'Lv3', value: 'Lv3' },
          { label: 'Lv4', value: 'Lv4' }
          // {label:'Lv5',value:'Lv5'},
        ],
        form: {
          wordType: '',
          wordRank: '',
          rightRateStart: '',
          rightRateEnd: '',
          analysis: ''
        },
        rules: {
          wordType: [{ required: true, message: '请选择单词类型', trigger: 'blur' }],
          wordRank: [{ required: true, message: '请选择水平等级', trigger: 'blur' }],
          rightRateStart: [{ required: true, message: '请输入正确率', trigger: 'blur' }],
          analysis: [{ required: true, message: '请输入水平分析', trigger: 'blur' }]
        }
      };
    },
    watch: {
      id: {
        handler(val) {
          if (val) {
            this.form.id = val;
            this.getDetail();
          }
        },
        immediate: true,
        deep: true
      },
      formDialogVisible: {
        handler(val) {
          this.dialogVisible = val ? true : false;
        },
        immediate: true,
        deep: true
      }
    },
    methods: {
      getDetail() {
        dictationWordLevel.getWordRankById(this.form.id).then((res) => {
          if (res.code === 20000) {
            this.form = res.data.data;
          }
        });
      },
      //关闭弹窗
      dialogClose() {
        this.dialogVisible = false;
        for (let val in this.form) {
          this.form[val] = '';
        }
        this.$refs.form.resetFields();
        this.$emit('onClose');
      },
      //提交
      onSubmit() {
        this.$refs.form.validate((valid) => {
          if (valid) {
            dictationWordLevel.pdWordRankAnalysisSave(this.form).then((res) => {
              if (res.code === 20000) {
                this.$message.success('操作成功');
                this.form.id = '';
                this.$emit('onClose');
                this.dialogClose();
              }
            });
          }
        });
      }
    }
  };
</script>
