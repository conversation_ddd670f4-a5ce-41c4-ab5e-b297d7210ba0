<template>
  <div v-if="courseId" class="app-container">
    <CourseVideoConfigItemQuery
      queryRef="queryItem"
      :data-query="dataQuery"
      :grade-list="dataQueryDict.gradeList"
      :version-list="versionList"
      @queryData="queryData"
      @resetQuery="resetQuery"
    />
    <el-col :span="24" style="margin-bottom: 30px">
      <el-button type="success" icon="el-icon-plus" size="mini" @click="handleAdd()">添加</el-button>
    </el-col>
    <el-table class="common-table" v-loading="tableLoading" :data="tableData" style="width: 100%; margin-bottom: 20px" row-key="id" border default-expand-all>
      <el-table-column type="index" label="序号" />
      <el-table-column prop="videoId" label="视频ID" />
      <el-table-column prop="videoName" label="视频名称" show-overflow-tooltip />
      <el-table-column prop="gradeLevel" label="学段" :formatter="gradeLevelName" />
      <el-table-column prop="subjectName" label="学科" />
      <el-table-column prop="versionName" label="版本" />
      <el-table-column prop="sortNo" label="排序">
        <template slot-scope="scope">
          <el-popover placement="top" width="160" v-model="scope.row.showPopover">
            <div v-if="scope.row.showPopover">
              <p style="margin: 0">修改排序号：</p>
              <el-input-number style="margin-bottom: 10px" v-model="scope.row.sortNoTemp" controls-position="right" :min="1" :max="999" :step="1" step-strictly />
              <div style="text-align: right; margin: 0">
                <el-button size="mini" type="text" @click="handleCancelSortNo(scope.row)">取消</el-button>
                <el-button type="primary" size="mini" @click="handleChangeSortNo(scope.row)">确定</el-button>
              </div>
            </div>
            <el-button slot="reference" style="width: 90%">{{ scope.row.sortNo }}</el-button>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间" />
      <el-table-column prop="withdrawnBonus" label="操作" width="200">
        <template slot-scope="scope">
          <el-button type="danger" size="mini" icon="el-icon-delete" @click="handleDelete(scope.row.videoId)">移除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <el-col :span="24" style="margin-bottom: 20px">
      <el-pagination
        :current-page="tablePage.currentPage"
        :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="tablePage.totalItems"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-col>
    <CourseAddVideoDialog :visible.sync="open" :add-video-info="addVideoInfo" @submitFormSuccess="submitForm" />
  </div>
</template>

<script>
  import courseApi from '@/api/studyExamPassed/course';
  import CourseVideoConfigItemQuery from './components/CourseVideoConfigItemQuery.vue';
  import CourseAddVideoDialog from './components/CourseAddVideoDialog.vue';
  import { pageParamNames } from '@/utils/constants';
  import { mapGetters } from 'vuex';
  import ls from '@/api/sessionStorage';
  export default {
    name: 'CourseVideoConfig',
    components: { CourseVideoConfigItemQuery, CourseAddVideoDialog },
    data() {
      return {
        courseId: '',
        curriculumId: '',
        courseGradeLevel: '',
        courseType: '',
        afterCreate: false,
        dataQuery: {
          grade: []
        },
        dataQueryDict: {
          gradeList: []
        },

        gradeOnlyList: [],
        versionList: [],

        tableLoading: false,

        tablePage: {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null
        },

        tableData: [],

        open: false
      };
    },
    computed: {
      ...mapGetters('enumItem', ['enumFormat']),
      addVideoInfo() {
        return {
          courseId: this.courseId,
          curriculumId: this.curriculumId,
          gradeLevel: this.courseGradeLevel
        };
      }
    },
    created() {
      this.init('created');
      setTimeout(() => {
        this.afterCreate = true;
      });
    },
    activated() {
      if (!this.afterCreate) return;
      this.init('activated');
    },
    methods: {
      init(type) {
        let courseVideoInfo = ls.getItem('courseVideoInfo');
        this.courseId = courseVideoInfo?.id;
        this.curriculumId = courseVideoInfo?.curriculumId;
        this.courseGradeLevel = courseVideoInfo?.gradeLevel;
        this.courseType = courseVideoInfo?.courseType;
        if (this.courseId) {
          console.log('courseId:', this.courseId);
          console.log('curriculumId:', this.curriculumId);
          console.log('courseGradeLevel:', this.courseGradeLevel);
          console.log('courseType:', this.courseType);
          this.initGradeList(type);
          this.initVersionList();
          this.resetQuery();
        } else {
          this.$message.error('获取课程id失败');
        }
      },
      initGradeList(type) {
        courseApi.getGradeAndSubjectList(this.curriculumId, this.courseGradeLevel).then((res) => {
          this.dataQueryDict.gradeList = res.data;
          if (type === 'created') {
            let gradeOnlyList = [];
            res.data.forEach((item) => {
              gradeOnlyList.push({ label: item.label, value: item.value });
            });
            this.gradeOnlyList = gradeOnlyList;
          }
        });
      },
      initVersionList() {
        courseApi.getVersionList(this.curriculumId, this.courseGradeLevel).then((res) => {
          this.versionList = res.data;
        });
      },
      mounted() {
        this.initVersionList();
      },
      queryData(dataQuery) {
        this.dataQuery = dataQuery;
        this.tablePage.currentPage = 1;
        this.getList();
      },
      resetQuery() {
        this.tablePage.currentPage = 1;
        this.dataQuery = {
          id: '',
          videoName: '',
          grade: [this.courseGradeLevel],
          versionId: '',
          courseId: this.courseId,
          curriculumId: this.curriculumId
        };
        this.getList();
      },

      // 分页
      handleSizeChange(val) {
        this.tablePage.size = val;
        this.getList();
      },
      handleCurrentChange(val) {
        this.tablePage.currentPage = val;
        this.getList();
      },
      getList() {
        this.tableLoading = true;
        let dataQuery = {
          ...this.dataQuery,
          pageNum: this.tablePage.currentPage,
          pageSize: this.tablePage.size
        };
        if (this.dataQuery.grade.length > 0) {
          dataQuery.gradeLevel = dataQuery.grade[0];
        }
        if (this.dataQuery.grade.length > 1) {
          dataQuery.subjectId = dataQuery.grade[1];
        }
        delete dataQuery.grade;
        courseApi.courseVideoList(dataQuery).then((res) => {
          console.log(res.data);
          if (res.data.data !== null) {
            this.tableData = res.data.data.map((data) => {
              return { ...data, showPopover: false, sortNoTemp: data.sortNo };
            });
            this.tableLoading = false;
          } else {
            this.tableData = [];
            this.tableLoading = false;
          }
          // 设置后台返回的分页参数
          pageParamNames.forEach((name) => this.$set(this.tablePage, name, parseInt(res.data[name])));
        });
      },
      handleChangeSortNo(row) {
        if (!row.sortNoTemp) {
          row.sortNoTemp = row.sortNo;
        } else if (row.sortNo == row.sortNoTemp) {
          row.showPopover = false;
        } else {
          courseApi.changeVideoSortNo({ id: row.id, sortNo: row.sortNoTemp }).then(() => {
            row.showPopover = false;
            this.$message.success('修改成功！');
            this.getList();
          });
        }
      },
      handleCancelSortNo(row) {
        row.showPopover = false;
      },
      handleDelete(id) {
        this.$confirm('您确定移除视频吗？', '移除', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          courseApi.courseVideoDelete(id).then(() => {
            this.$message.success('成功移除视频');
            this.getList();
          });
        });
      },
      handleAdd() {
        if (this.courseType != '2' || this.tableData.length == 0) {
          this.open = true;
        } else {
          this.$message.warning('试课课程只能添加一个视频');
        }
      },
      submitForm() {
        this.getList();
      },
      gradeLevelName(row, column, cellValue, index) {
        return this.enumFormat(this.gradeOnlyList, cellValue) || '无';
      }
    }
  };
</script>

<style></style>
