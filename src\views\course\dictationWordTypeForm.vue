<template>
    <div class="addForm">
      <el-dialog :title="id ? '修改词汇类型': '添加词汇类型'" :visible.sync="dialogVisible" width="70%" :close-on-click-modal="false" @close="dialogClose">
        <el-form ref="form" :rules="rules" :model="form" label-position="left" label-width="120px" style="width: 100%">
        <el-form-item label="单词类型" prop="studyType">
          <el-col :xs="24" :lg="18">
            <el-select style="width: 100%;" v-model="form.studyType" filterable value-key="value" placeholder="请选择">
              <el-option v-for="(item, index) in enTypeData" :key="index" :label="item.label" :value="item.code" />
            </el-select>
          </el-col>
        </el-form-item> 
        <el-form-item label="单词测评类型" prop="studyType">
          <el-col :xs="24" :lg="18">
            <el-select style="width: 100%;" v-model="form.wordType" filterable value-key="value" placeholder="请选择">
              <el-option v-for="(item, index) in wordTypeData" :key="index" :label="item.label" :value="item.value" />
            </el-select>
          </el-col>
        </el-form-item> 
        <el-form-item label="正确率" prop="rightRateStart">
          <el-row>
             <el-col :span="4">
                <el-input-number style="width:80%" v-model="form.rightRateStart" :min="0" :max="100" :controls="false"></el-input-number> %
             </el-col>
             <el-col :span="1"> - </el-col>
             <el-col :span="4">
                <el-input-number style="width:80%" v-model="form.rightRateEnd" :min="0" :max="100" :controls="false"></el-input-number> %
             </el-col>
          </el-row>
        </el-form-item>
        <!-- <el-form-item label="测评单词个数" prop="wordNumber">
            <el-input v-model="form.wordNumber" placeholder="请输入" style="width: 40%;"></el-input>
        </el-form-item> -->
        <el-form-item label="描述" prop="description">
          <el-col :xs="24" :lg="18">
            <el-input type="textarea" resize="none" :rows="4" v-model="form.description" />
          </el-col>
        </el-form-item>
        <el-form-item label="测评分析" prop="analysis">
          <el-col :xs="24" :lg="18">
            <el-input type="textarea" resize="none" :rows="4" v-model="form.analysis" />
          </el-col>
        </el-form-item>
        <el-form-item label="学习建议" prop="learningAdvice">
          <el-col :xs="24" :lg="18">
            <el-input type="textarea" resize="none" :rows="4" v-model="form.learningAdvice" />
          </el-col>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="dialogClose">关闭</el-button>
        <el-button size="mini" type="primary" @click="onSubmit">确定</el-button>
      </div>
      </el-dialog>
    </div>
</template>
<script>
import dictationWordType from '@/api/dictationWordType'
export default {
    props:{
        id:{
            type:String,
            default:''
        },
        formDialogVisible:{
            type:Boolean,
            default:false
        }
    },
    data(){
        return{
            dialogVisible:false,
            categoryTypeList:[],
            enTypeData:[
                {label:'单音节',code:'DYJ1',type:'PD_WORD_TYPE'},
                {label:'多音节',code:'DYJ2',type:'PD_WORD_TYPE'},
                {label:'前后缀',code:'QHZ',type:'PD_WORD_TYPE'},
                // {label:'变形',code:'BX',type:'PD_WORD_TYPE'},
            ],
            wordTypeData:[
              {label:'发音',value:'0'},
              {label:'拼写',value:'1'},
            ],
            form:{
                studyType:'',
                wordType:"",
                rightRateStart:'',
                rightRateEnd:'',
                // wordNumber:'',
                description:'',
                analysis:'',
                learningAdvice:''
            },
            rules:{
                studyType: [{ required: true,message: '请选择单词类型',trigger: 'blur' }],
                rightRateStart: [{ required: true,message: '请输入正确率',trigger: 'blur' }],
                // wordNumber: [{ required: true,message: '请输入测评单词个数',trigger: 'blur' }],
                description: [{ required: true,message: '请输入描述',trigger: 'blur' }],
                analysis: [{ required: true,message: '请输入测评分析',trigger: 'blur' }],
                learningAdvice: [{ required: true,message: '请输入学习建议',trigger: 'blur' }],
                
            }

        }
    },
    watch:{
        id:{
            handler(val){
              if(val){
                this.form.id = val
                console.log('00000000');
                this.getDetail()
              }
            },
            immediate:true,
            deep:true
        },
        formDialogVisible:{
            handler(val){
                this.dialogVisible = val ? true : false
            },
            immediate:true,
            deep:true
        }
    },
    methods:{
        getDetail(){
          dictationWordType.getWordTypeById(this.form.id).then(res => {
            if(res.code === 20000){
              this.form = res.data.data
            }
          })
        },
        //关闭弹窗
        dialogClose(){
           this.dialogVisible = false;
           for(let val in this.form){
             this.form[val] = ''
           }
           this.$refs.form.resetFields();
           this.$emit('onClose');
        },
       //提交
        onSubmit(){
          console.log(this.form,'form');
          const submitApi = this.form.id ? dictationWordType.pdWordTypeAnalysisUpdate :dictationWordType.pdWordTypeAnalysisSave
          this.$refs['form'].validate(valid => {
            if(valid){
              submitApi(this.form).then(res => {
                 if(res.code === 20000){
                    this.$message.success('操作成功')
                    this.form.id = ''
                    this.$emit('onClose');
                    this.dialogClose()
                 }
              })
              console.log(this.form);
            }
          })
            // this.$refs.form.validate(valid => {
            //     if(valid){
            //        console.log(this.form,'form');
            //        this.dialogClose()
            //     }
            // })
        }
    }
}
</script>