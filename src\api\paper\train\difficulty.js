
import request from '@/utils/request'

export default {
  // 分页查询
  list(data) {
    return request({
      url: '/paper/web/difficulty/list',
      method: 'GET',
      params: data
    })
  },
//添加
  saveOrUpdate(data) {
    return request({
      url: '/paper/web/difficulty/create',
      method: 'POST',
      data
    })
  },
  detail(id){
    return request({
      url: '/paper/web/difficulty/detail',
      method: 'GET',
      params: {
        id:id
      }
    })
  },
  delete(id){
    return request({
      url: '/paper/web/difficulty/delete',
      method: 'DELETE',
      params: {
        id:id
      }
    })
  },
  //判断该题型的难度是否设置
  isSetting(data){
    return request({
      url: '/paper/web/difficulty/isSetting',
      method: 'GET',
      params: data
    })
  },
}
