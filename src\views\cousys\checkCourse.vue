<template>
  <div class="app-container">
    <el-row>
      <el-col class="paike" style="font-size: 30px" :span="8" :xs="24">
        <span>课程详情</span>
      </el-col>
      <el-col class="paike" style="font-size: 30px;float: right" :span="8" :xs="24">
        <span>倒计时：{{ `${hr}:${min}:${sec}` }}</span>
      </el-col>

    </el-row>
    <!--    基础信息-->
    <el-form label-position="left" label-width="10px">
      <el-form-item>
        <el-col :xs="24" :span="12" style="font-size: 22px">
          基本信息
        </el-col>
        <el-col :xs="24" :span="3" style="float: right">
          <el-button type="success" @click="handleView()" v-if="!isView">查看更多</el-button>
        </el-col>
      </el-form-item>
      <el-form-item>
        <el-col :xs="24" :span="3">
          家长姓名：{{ baseInfo.parentName }}
        </el-col>
        <el-col :xs="24" :span="3">
          家长性别：{{ baseInfo.parentGender === '1' ? '男' : '女' }}
        </el-col>
        <el-col :xs="24" :span="4">
          家长手机号：{{ baseInfo.parentPhoneNumber }}
        </el-col>
        <el-col :xs="24" :span="3">
          期望教练 性别：{{ baseInfo.tutorGender === '1' ? '男' : '女' }}
        </el-col>
        <el-col :xs="24" :span="3">
          学员年级：{{ baseInfo.studentGrade }}
        </el-col>
        <el-col :xs="24" :span="4">
          家庭住址：{{ baseInfo.homeAddress }}
        </el-col>
      </el-form-item>
    </el-form>

    <el-form label-position="left" label-width="10px" v-if="isView">
      <el-form-item>
        <el-col :xs="24" :span="12" style="font-size: 22px">
          更多信息
        </el-col>
      </el-form-item>
      <el-form-item>
        <el-row>
          <el-col :xs="24" :span="4">
            学员姓名：{{moreInfo.studentName}}
          </el-col>
          <el-col :xs="24" :span="4">
            学员性别：{{moreInfo.studentGender==='1'?'男':'女'}}
          </el-col>
          <el-col :xs="24" :span="4">
            学员出生日期：{{ moreInfo.dateOfBirth }}
          </el-col>
          <el-col :xs="24" :span="4">
            学员学校：{{ moreInfo.studentSchool}}
          </el-col>
          <el-col :xs="24" :span="4">
            学校类别：{{ moreInfo.schoolType }}
          </el-col>
        </el-row>
        <el-row>
          <el-col :xs="24" :span="4">
            英语成绩：{{ moreInfo.englishScore }}
          </el-col>
          <el-col :xs="24" :span="4">
            班级排名：{{ baseInfo.classRank }}
          </el-col>
          <el-col :xs="24" :span="4">
            学校排名：{{ baseInfo.schoolRank }}
          </el-col>
          <el-col :xs="24" :span="4">
            工作单位：{{ baseInfo.companyAddress }}
          </el-col>
        </el-row>
      </el-form-item>
    </el-form>

    <!--课程信息-->
    <el-form label-position="left" label-width="10px">
      <el-form-item>
        <el-col :xs="24" :span="12" style="font-size: 22px">
          课程信息
        </el-col>
      </el-form-item>
      <el-form-item>
        <el-col :xs="24" :span="4">
          开始时间：{{ baseInfo.courseStartDate }}
        </el-col>
        <el-col :xs="24" :span="3">
          学时总计：{{ baseInfo.totalCourse}}学时
        </el-col>
        <el-col :xs="24" :span="5">
          所属校区：{{ baseInfo.merchantName }}
        </el-col>
        <el-col :xs="24" :span="3">
          合计：<span style="color:red">{{ baseInfo.totalPrice}}元</span>
        </el-col>
      </el-form-item>
    </el-form>
    <!--教练 -->
    <el-form label-position="left" label-width="10px">
      <el-form-item>
        <el-col :xs="24" :span="3" style="float: right">
          <el-button type="success" @click="moreTutor">查看更多教练 </el-button>
        </el-col>
      </el-form-item>
      <el-form-item>

        <el-col :span="8" style="border:1px solid #333333;">
          <el-row>
            <el-col :span="6">
              <el-image :src="url" style="height: 80px;width: 80px;border-radius: 50px"></el-image>
            </el-col>
            <el-col :span="18">
              <el-row>
                <el-col>
                  {{tutorInfo.tutorName}} <i
                    :class="tutorInfo.tutorGender===1?'el-icon-male male-style':'el-icon-female female-style'"></i>
                </el-col>
                <el-col>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="12">
                  学校：安徽理工大学
                </el-col>
                <el-col :span="12">
                  电话：{{tutorInfo.tutorPhoneNumber}}
                </el-col>
              </el-row>
            </el-col>
          </el-row>
          <el-row>
            <el-col>
              现住址：安徽省合肥市
            </el-col>
          </el-row>
        </el-col>
      </el-form-item>
    </el-form>

    <!--    课程表-->
    <el-form label-position="left">
      <el-form-item>
        <el-col class="border1">
          <el-col :span="3" v-for="item in dataList" :key="item.date" class="border_user border1_right">
            <el-col class="border1_bottom" style="background-color: #89b7b1;color:white">{{ item.date }}</el-col>

            <el-col v-for="itemson in item.weekPlanDetailVoList" :key="itemson.time" class="border1_bottom"
              :class="itemson.type==='0'?'':itemson.type==='1'?'user_green':''">
              {{itemson.time}}
            </el-col>

          </el-col>
        </el-col>

        <!-- 分页 -->
        <el-col :span="20" style="overflow-x: auto;" :xs="24">
          <el-pagination style="float: right" :current-page="tablePage.currentPage" :page-size="tablePage.size"
            layout="total, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
            @current-change="handleCurrentChange" />
        </el-col>
      </el-form-item>
    </el-form>

    <div style="text-align: center">
      <el-button type="success" @click="passCourse">完 成</el-button>
      <el-button type="primary" @click="reStatus">重排课</el-button>
    </div>

    <!-- 新增弹窗 -->
    <el-dialog title="查看更多家长信息" :visible.sync="apply" width="60%" @close="applyClose">
      <el-form :model="applyForm" :rules="rules" ref="applyForm" style="width: 100%">
        <el-form-item label="" prop="applyContent">
          <el-input type="textarea" :row="5" placeholder="填写申请查看理由..." v-model="applyForm.applyContent"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="submitApply()">确定</el-button>
      </div>
    </el-dialog>

    <!--更多教练 -->
    <el-dialog class="recharge-dialog" title="查看更多教练 " :visible.sync="open" width="50%" @close="closeTutor">
      <el-table :data="tutorTable" style="width: 100%">
        <el-table-column prop="tutorName" label="姓名">
        </el-table-column>
        <el-table-column prop="gender" label="性别">
          <template slot-scope="scope">
            <span v-if="scope.row.gender==='1'">男</span>
            <span v-else>女</span>
          </template>
        </el-table-column>
        <el-table-column prop="phoneNumber" label="电话">
        </el-table-column>
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="closeTutor">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>

import checkCourseApi from '@/api/checkCourse'
import { pageParamNames } from "@/utils/constants";
export default {
  data() {
    return {
      open: false,
      tutorTable: [],
      url: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg',
      tableLoading: false,
      baseInfo: {},
      moreInfo: {
        studentName: null,
        studentGender: null,
        dateOfBirth: null,
        studentSchool: null,
        schoolType: null,
        englishScore: null,
        classRank: null,
        schoolRank: null,
        companyAddress: null,
      },
      tutorInfo: {
        tutorId: null,
        tutorName: null,
        tutorGender: null,
        tutorPhoneNumber: null,
      },
      courseOrderId: null,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 1,
        totalPage: null,
        totalItems: null
      },
      courseTypePlanVoList: [],
      isView: false,//是否点击查看更多
      apply: false,
      rules: {
        applyContent: [
          { required: true, message: '请输入申请查看理由', trigger: 'blur' },
        ],
      },
      applyForm: {},
      queryWeekPlan: {
        courseOrderId: null,
        tutorId: null,
        pageNum: null
      },
      dataList: [],
      day: 0, hr: 0, min: 0, sec: 0,
      createTime: '2021-09-02 17:08:00'
    }
  },
  mounted() {

  },
  created() {
    // this.courseOrderId=this.$route.query.courseOrderId;
    this.courseOrderId = "883353160788742144";
    this.isViewBtn();
    this.getBaseInfo();
    this.getInfo();
  },
  methods: {
    //基本信息+课程信息
    getBaseInfo() {
      checkCourseApi.getBaseInfo(this.courseOrderId).then(res => {
        this.baseInfo = res.data;
        this.createTime = res.data.createTime;
        this.daojishi();
      })
    },
    //教练 信息
    getInfo() {
      checkCourseApi.getInfo(this.courseOrderId).then(res => {
        this.tutorInfo = res.data;
        this.getWeekPlan();
      })
    },
    //获取课程表
    getWeekPlan() {
      this.queryWeekPlan.courseOrderId = this.courseOrderId;
      this.queryWeekPlan.tutorId = this.tutorInfo.tutorId;
      this.queryWeekPlan.pageNum = this.tablePage.currentPage;
      checkCourseApi.getWeekPlan(this.queryWeekPlan).then(res => {
        this.dataList = res.data.weekPlanVoList;
        this.tablePage.totalItems = res.data.totalPage;
      })
    },
    //更多教练 
    moreTutor() {
      var param = {
        courseType: this.baseInfo.courseType
      };
      checkCourseApi.getMoreTutor(param).then(res => {
        console.log("moreTutor====", res.data);
        this.tutorTable = res.data;
        this.open = true;
      })
    },
    closeTutor() {
      this.open = false;
      this.tutorTable = [];
    },
    //点击同意排课
    passCourse() {
      checkCourseApi.passCourse(this.courseOrderId).then(res => {
        this.$message.success("同意排课")
      })
    },
    //重排课
    reStatus() {
      this.$router.push({ path: '/cousys/reStatusCourse', query: { courseOrderId: this.courseOrderId } })
    },

    //查看更多
    getDetailInfo() {
      checkCourseApi.getDetailInfo(this.courseOrderId, this.applyForm.applyContent).then(res => {
        var token = this.$store.getters.token;
        this.isView = true;
        localStorage.setItem(token, this.isView);
        this.moreInfo = res.data;
        localStorage.setItem("moreInfo", JSON.stringify(this.moreInfo));
      })
    },
    //点击查看更多
    handleView() {
      this.apply = true;
    },
    //提交申请
    submitApply() {
      this.$refs['applyForm'].validate((valid) => {
        if (valid) {
          //提交
          this.apply = false;
          if (!this.isView) {
            this.getDetailInfo()
          }
        }
      });
    },
    //判断是否登录点击了
    isViewBtn() {
      var token = this.$store.getters.token;
      var a = localStorage.getItem(token);
      if (a != null) {
        this.isView = a;
        this.moreInfo = JSON.parse(localStorage.getItem("moreInfo"));
      }
    },

    //重置按钮
    resetQuery() {
      this.getList()
    },
    reset() {
      this.baseInfo = {}
    },
    cancel() {
      this.reset()
    },
    applyClose() {
      this.apply = false;
      if (this.$refs['applyForm']) {
        this.$refs['applyForm'].resetFields();
      };
      this.applyForm = {
        applyContent: null
      }
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val;
      this.getWeekPlan();
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val;
      this.getWeekPlan();
    },

    daojishi() {
      const end = Date.parse(new Date(this.createTime));
      const now = Date.parse(new Date());
      const msec = end - now;
      if (msec >= 0) {
        let hr = parseInt(msec / 1000 / 60 / 60 % 24);
        let min = parseInt(msec / 1000 / 60 % 60);
        let sec = parseInt(msec / 1000 % 60);
        this.hr = hr > 9 ? hr : '0' + hr;
        this.min = min > 9 ? min : '0' + min;
        this.sec = sec > 9 ? sec : '0' + sec;
      } else {
        this.hr = '00';
        this.min = '00';
        this.sec = '00';
      }
      // 等于0的时候不调用
      if (Number(this.hr) === 0 && Number(this.min) === 0 && Number(this.sec) === 0) {
        return
      } else {
        setTimeout(this.daojishi, 1000);
      }
    },
  },
  // beforeDestroy() {
  //   window.localStorage.setItem('courseOrderId', '')
  //   window.localStorage.setItem('cousysMemberCode', '')
  //   window.localStorage.setItem('cousysStudentCode', '')
  // }



}
</script>

<style>
.male-style {
  color: #0a76a4;
}
.female-style {
  color: pink;
}

.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.paike {
  padding-bottom: 50px;
  padding-top: 20px;
}

.studentClass {
  padding-left: 20px;
  padding-top: 20px;
  padding-bottom: 20px;
}

.peopleClass {
  padding-top: 20px;
  padding-bottom: 20px;
}

.typeClass {
  width: 93px;
  height: 34px;
  background: #89b7b1;
  margin-right: 20px;
  color: #297c71;
  font-size: 13px;
  text-align: center;
  line-height: 34px;
}

.courseTypeClass {
  width: 200px;
  height: 100px;
  background: #89b7b1;
  color: #297c71;
  font-size: 13px;
  text-align: center;
  line-height: 34px;
}

.user_gray {
  background: gray;
  color: #ffffff;
}

.user_default {
  background: gray;
  color: #ffffff;
}

.user_green {
  background: #00a691;
  color: #ffffff;
}

.border_user {
  text-align: center;
}

.border1 {
  border-left: 1px solid #333333;
}

.border1_bottom {
  border-bottom: 1px solid #333333;
}

.border1_right {
  border: 1px solid #333333;
}
</style>
