const state = {
  train: {
    difficultyList: [
      { label: '初级', value: 1 },
      { label: '中级', value: 2 },
      { label: '高级', value: 3 }
    ],
    trainType: [
      { label: '听觉题', value: 'AUDITORY_' },
      { label: '视觉题', value: 'VISUAL_' },
      { label: '拍照上传题', value: 'UPLOAD_' },
      { label: '课外练习', value: 'STARE_' }
    ],
    trainQuestionType: [
      { label: '跟读题', value: 'AUDITORY_FOLLOW' },
      { label: '动手题', value: 'AUDITORY_HANDLE' },
      { label: '填空题', value: 'AUDITORY_FILLING' },
      { label: '问答题', value: 'AUDITORY_ANSWER' },
      { label: '漏读题', value: 'AUDITORY_LEAK' },
      { label: '辨别题', value: 'AUDITORY_DISTINGUISH' },
      { label: '找重题', value: 'AUDITORY_REPEAT' },
      { label: '数字找不同题', value: 'VISUAL_DIFFERENT' },
      { label: '找图形个数题', value: 'VISUAL_PATTERN' },
      { label: '视觉分辨题', value: 'VISUAL_DISTINGUISH' },
      { label: '方格正序题', value: 'VISUAL_LEAK' },
      { label: '图片找不同题', value: 'VISUAL_CHOICE' },
      { label: '图片方格题', value: 'VISUAL_PICTURE_GRID' },
      { label: '特征找图形题', value: 'VISUAL_FEATURE_GRAPHICS' },
      { label: '图形匹配题', value: 'VISUAL_GRAPHIC_MATCHING' },
      { label: '填漏题', value: 'VISUAL_FILL_LEAK' },
      { label: '朗读题', value: 'VISUAL_READ' },
      { label: '图片记忆题', value: 'VISUAL_PIC_MEMORY' },
      { label: '单词记忆题', value: 'VISUAL_WORD_MEMORY' },
      { label: '数字记忆题', value: 'VISUAL_NUM_MEMORY' },
      { label: '内容抄写题', value: 'UPLOAD_COPY' },
      { label: '看图画画题', value: 'UPLOAD_DRAWING' },
      { label: '圈数字题', value: 'VISUAL_FIND_WORD' },
      { label: '视觉转移题', value: 'VISUAL_ANTI_INTERFERENCE' },
      { label: '文字找重题', value: 'VISUAL_TEXT_REPEAT' },

      { label: '音频文本填空题', value: 'AUDITORY_WORD_FILLING' },

      { label: '盯点练习', value: 'STARE_POINT' },
      { label: '课前小游戏', value: 'STARE_MINI_GAME' },
      { label: '课前视频', value: 'STARE_VIDEO' },
      { label: '手指计算题', value: 'STARE_FINGER_CALC' },
      { label: '看字组句题', value: 'VISUAL_READ_SENTENCES' },
      { label: '找相同字例题', value: 'VISUAL_FIND_SIMILAR_CHARACTER' }
    ],
    auditoryEditUrlEnum: [
      { questionType: 'AUDITORY_FOLLOW', value: '/train/auditoryFollow', name: '跟读题' },
      { questionType: 'AUDITORY_HANDLE', value: '/train/auditoryHandle', name: '动手题' },
      { questionType: 'AUDITORY_FILLING', value: '/train/auditoryFilling', name: '填空题' },
      { questionType: 'AUDITORY_DISTINGUISH', value: '/train/auditoryDistinguish', name: '辨别题' },
      { questionType: 'AUDITORY_LEAK', value: '/train/auditoryLeak', name: '漏读题' },
      { questionType: 'AUDITORY_ANSWER', value: '/train/auditoryAnswer', name: '问答题' },
      { questionType: 'AUDITORY_REPEAT', value: '/train/auditoryRepeat', name: '找重题' },
      { questionType: 'AUDITORY_WORD_FILLING', value: '/train/auditoryWordFilling', name: '音频文本填空题' }
    ],
    visualEditUrlEnum: [
      { questionType: 'VISUAL_DIFFERENT', value: '/train/visualDifferent', name: '数字找不同题' },
      { questionType: 'VISUAL_PATTERN', value: '/train/visualPattern', name: '找图形个数题' },
      { questionType: 'VISUAL_DISTINGUISH', value: '/train/visualDistinguish', name: '视觉分辨题' },
      { questionType: 'VISUAL_LEAK', value: '/train/visualLeak', name: '方格正序题' },
      { questionType: 'VISUAL_CHOICE', value: '/train/visualChoice', name: '图片找不同题' },
      { questionType: 'VISUAL_FILL_LEAK', value: '/train/visualFillLeak', name: '填漏题' },
      { questionType: 'VISUAL_READ', value: '/train/visualRead', name: '朗读题' },
      { questionType: 'VISUAL_PIC_MEMORY', value: '/train/visualPicMemory', name: '图片记忆题' },
      { questionType: 'VISUAL_WORD_MEMORY', value: '/train/visualWordMemory', name: '单词记忆题' },
      { questionType: 'VISUAL_NUM_MEMORY', value: '/train/visualNumMemory', name: '数字记忆题' },
      { questionType: 'VISUAL_FIND_WORD', value: '/train/visualFindWord', name: '圈数字题' },
      { questionType: 'VISUAL_ANTI_INTERFERENCE', value: '/train/visualAntiInterference', name: '视觉转移题' },
      { questionType: 'VISUAL_TEXT_REPEAT', value: '/train/visualTextRepeat', name: '文字找重题' },
      { questionType: 'VISUAL_PICTURE_GRID', value: '/train/visualPictureGrid', name: '图片方格题' },
      { questionType: 'VISUAL_FEATURE_GRAPHICS', value: '/train/visualFeatureGraphics', name: '特征找图形题' },
      { questionType: 'VISUAL_GRAPHIC_MATCHING', value: '/train/visualGraphicMatching', name: '图形匹配题' },
      { questionType: 'VISUAL_READ_SENTENCES', value: '/train/lookWordsCreateSentences', name: '看字组句题' },
      { questionType: 'VISUAL_FIND_SIMILAR_CHARACTER', value: '/train/findIdenticalWordExamples', name: '找相同字例题' },
      // { label: '看字组句题', value: 'VISUAL_READ_SENTENCES' },//findIdenticalWordExamples
      // { label: '找相同字例题', value: 'VISUAL_FIND_SIMILAR_CHARACTER' }
    ],
    uploadEditUrlEnum: [
      { questionType: 'UPLOAD_COPY', value: '/train/uploadCopy', name: '内容抄写题' },
      { questionType: 'UPLOAD_DRAWING', value: '/train/uploadDrawing', name: '看图画画题' }
    ],
    stareEditUrlEnum: [
      { questionType: 'STARE_POINT', value: '/train/starePoint', name: '盯点练习' },
      { questionType: 'STARE_MINI_GAME', value: '/train/stareMiniGame', name: '课前小游戏' },
      { questionType: 'STARE_VIDEO', value: '/train/stareVideo', name: '课前视频' },
      { questionType: 'STARE_FINGER_CALC', value: '/train/stareFingerCalc', name: '手指计算题' }
    ],
    allTrainUrlEnum: [
      { questionType: 'AUDITORY_FOLLOW', value: '/train/auditoryFollow', name: '听力跟读题', label: 'AUDITORY_FOLLOW' },
      { questionType: 'AUDITORY_HANDLE', value: '/train/auditoryHandle', name: '听力动手题', label: 'AUDITORY_HANDLE' },
      // { questionType: 'AUDITORY_ANSWER', value: '/train/auditoryAnswer', name: '听力问答题', label: 'AUDITORY_ANSWER' },
      { questionType: 'AUDITORY_REPEAT', value: '/train/auditoryRepeat', name: '听力找重题', label: 'AUDITORY_REPEAT' },
      // { questionType: 'AUDITORY_WORD_FILLING', value: '/train/auditoryWordFilling', name: '音频文本填空题', label: 'AUDITORY_WORD_FILLING' },
      // { questionType: 'VISUAL_DIFFERENT', value: '/train/visualDifferent', name: '数字找不同题', label: 'VISUAL_DIFFERENT' },
      { questionType: 'VISUAL_PATTERN', value: '/train/visualPattern', name: '找图形个数题', label: 'VISUAL_PATTERN' },
      // { questionType: 'VISUAL_LEAK', value: '/train/visualLeak', name: '方格正序题', label: 'VISUAL_LEAK' },
      { questionType: 'VISUAL_CHOICE', value: '/train/visualChoice', name: '图片找不同题', label: 'VISUAL_CHOICE' },
      // { questionType: 'VISUAL_READ', value: '/train/visualRead', name: '视觉朗读题', label: 'VISUAL_READ' },
      // { questionType: 'VISUAL_FIND_WORD', value: '/train/visualFindWord', name: '圈数字题', label: 'VISUAL_FIND_WORD' },
      { questionType: 'VISUAL_ANTI_INTERFERENCE', value: '/train/visualAntiInterference', name: '视觉转移题', label: 'VISUAL_ANTI_INTERFERENCE' },
      // { questionType: 'VISUAL_TEXT_REPEAT', value: '/train/visualTextRepeat', name: '文字找重题', label: 'VISUAL_TEXT_REPEAT' },
      { questionType: 'UPLOAD_COPY', value: '/train/uploadCopy', name: '内容抄写题', label: 'UPLOAD_COPY' },
      // { questionType: 'VISUAL_FEATURE_GRAPHICS', value: '/train/visualFeatureGraphics', name: '特征找图形题', label: 'VISUAL_FEATURE_GRAPHICS' },
      { questionType: 'UPLOAD_DRAWING', value: '/train/uploadDrawing', name: '看图画画题', label: 'UPLOAD_DRAWING' },
      { questionType: 'VISUAL_READ_SENTENCES', value: '/train/lookWordsCreateSentences', name: '看字组句题', label: 'VISUAL_READ_SENTENCES' },
      { questionType: 'VISUAL_FIND_SIMILAR_CHARACTER', value: '/train/findIdenticalWordExamples', name: '找相同字例题', label: 'VISUAL_FIND_SIMILAR_CHARACTER' },
      { questionType: 'AUDITORY_ANSWER', value: '/train/auditoryAnswerNew', name: '新听力回答题', label: 'AUDITORY_ANSWER' },
      { questionType: 'AUDITORY_WORD_FILLING', value: '/train/auditoryWordFillingNew', name: '新音频文本填空题', label: 'AUDITORY_WORD_FILLING' },
      { questionType: 'VISUAL_DIFFERENT', value: '/train/visualDifferentNew', name: '新数字找不同题', label: 'VISUAL_DIFFERENT' },
      { questionType: 'VISUAL_TEXT_REPEAT', value: '/train/visualTextRepeatNew', name: '新文字找重题', label: 'VISUAL_TEXT_REPEAT' },
      { questionType: 'VISUAL_FEATURE_GRAPHICS', value: '/train/visualFeatureGraphicsNew', name: '新特征找图形题', label: 'VISUAL_FEATURE_GRAPHICS' },
      { questionType: 'VISUAL_LEAK', value: '/train/visualLeakNew', name: '新方格正序题', label: 'VISUAL_LEAK' },
      { questionType: 'VISUAL_FIND_WORD', value: '/train/visualFindWordNew', name: '新圈数字题', label: 'VISUAL_FIND_WORD' },
      { questionType: 'VISUAL_READ', value: '/train/visualReadNew', name: '新朗读题', label: 'VISUAL_READ' }
      // { questionType: 'AUDITORY_FILLING', value: '/train/auditoryFilling', name: '听力填空题', label: 'AUDITORY_FILLING' },
      // { questionType: 'AUDITORY_DISTINGUISH', value: '/train/auditoryDistinguish', name: '听力辨别题', label: 'AUDITORY_DISTINGUISH' },
      // { questionType: 'AUDITORY_LEAK', value: '/train/auditoryLeak', name: '听力漏读题', label: 'AUDITORY_LEAK' },
      // { questionType: 'VISUAL_DISTINGUISH', value: '/train/visualDistinguish', name: '视觉分辨题', label: 'VISUAL_DISTINGUISH' },
      // { questionType: 'VISUAL_FILL_LEAK', value: '/train/visualFillLeak', name: '视觉填漏题', label: 'VISUAL_FILL_LEAK' },
      // { questionType: 'VISUAL_PIC_MEMORY', value: '/train/visualPicMemory', name: '图片记忆题', label: 'VISUAL_PIC_MEMORY' },
      // { questionType: 'VISUAL_WORD_MEMORY', value: '/train/visualWordMemory', name: '单词记忆题', label: 'VISUAL_WORD_MEMORY' },
      // { questionType: 'VISUAL_NUM_MEMORY', value: '/train/visualNumMemory', name: '数字记忆题', label: 'VISUAL_NUM_MEMORY' },
      // { questionType: 'STARE_POINT', value: '/train/starePoint', name: '盯点练习', label: 'STARE_POINT' },
      // { questionType: 'STARE_MINI_GAME', value: '/train/stareMiniGame', name: '课前小游戏', label: 'STARE_MINI_GAME' },
      // { questionType: 'STARE_VIDEO', value: '/train/stareVideo', name: '课前视频', label: 'STARE_VIDEO' },
      // { questionType: 'STARE_FINGER_CALC', value: '/train/stareFingerCalc', name: '手指计算题', label: 'STARE_FINGER_CALC' },
    ]
  },
  question: {
    operatorList: [
      { label: '+', value: '+' },
      { label: '-', value: '-' },
      { label: '*', value: '*' },
      { label: '/', value: '/' }
    ],
    gradeList: [
      { label: '一年级', value: 1 },
      { label: '二年级', value: 2 },
      { label: '三年级', value: 3 },
      { label: '四年级', value: 4 },
      { label: '五年级', value: 5 },
      { label: '六年级', value: 6 },
      { label: '初一', value: 7 },
      { label: '初二', value: 8 },
      { label: '初三', value: 9 },
      { label: '高一', value: 10 },
      { label: '高二', value: 11 },
      { label: '高三', value: 12 }
    ],
    typeList: [
      { label: '单选题', value: 'SINGLE_CHOICE' },
      { label: '多选题', value: 'MULTIPLE_CHOICE' },
      // { label: '判断题', value: 'JUDGE' },
      { label: '填空题', value: 'FILLING' },
      // { label: '简答题', value: 'EXPLAIN' },
      { label: '组合题', value: 'COMBINATION' },
      { label: '字符串记忆题', value: 'STR_MEMORY' },
      { label: '词语记忆题', value: 'WORD_MEMORY' },
      { label: '火柴移动题', value: 'MATCH_MOVE' },
      { label: '硬币移动题', value: 'ICON_MOVE' },
      { label: '线索题', value: 'CLUE' },
      { label: '跳格子题', value: 'GRID' },
      { label: '音频填空题', value: 'AUDIO_FILL' },
      { label: '文字找重题', value: 'TEXT_REPEAT' },
      { label: '答题卡题', value: 'CARD' },
      { label: '物品记忆题', value: 'ARTICLE_MEMORY' },
      { label: '图片问答题', value: 'IMAGE_ANSWER' },
      { label: '听力回答题', value: 'LISTENING_ANSWER' }
    ],
    editUrlEnum: [
      { label: 'SINGLE_CHOICE', value: '/paper/singleChoice', name: '单选题' },
      { label: 'MULTIPLE_CHOICE', value: '/paper/multipleChoice', name: '多选题' },
      // { label: 'JUDGE', value: '/paper/trueFalse', name: '判断题' },
      { label: 'FILLING', value: '/paper/gapFilling', name: '填空题' },
      // { label: 'EXPLAIN', value: '/paper/shortAnswer', name: '简答题' },
      { label: 'COMBINATION', value: '/paper/combination', name: '组合题' },
      { label: 'STR_MEMORY', value: '/paper/strMemory', name: '字符串记忆题' },
      { label: 'WORD_MEMORY', value: '/paper/wordMemory', name: '词语记忆题' },
      { label: 'MATCH_MOVE', value: '/paper/matchMove', name: '火柴移动题' },
      { label: 'ICON_MOVE', value: '/paper/iconMove', name: '硬币移动题' },
      { label: 'CLUE', value: '/paper/clue', name: '线索题' },
      { label: 'GRID', value: '/paper/grid', name: '跳格子题' },
      { label: 'AUDIO_FILL', value: '/paper/audioFill', name: '音频填空题' },
      { label: 'TEXT_REPEAT', value: '/paper/textRepeat', name: '文字找重题' },
      { label: 'CARD', value: '/paper/card', name: '答题卡题' },
      { label: 'ARTICLE_MEMORY', value: '/paper/articleMemory', name: '物品记忆题' },
      { label: 'IMAGE_ANSWER', value: '/paper/imageAnswer', name: '图片问答题' },
      { label: 'LISTENING_ANSWER', value: '/paper/listenAnswer', name: '听力回答题' }
    ]
  },
  complexQuestion: {
    titleType: [
      { label: '图文类', value: 'IMG_TEXT' },
      { label: '音频类', value: 'AUDIO' },
      { label: '视频类', value: 'VIDEO' },
      { label: '答题卡类', value: 'CARD' }
    ],
    questionType: [
      { label: '拖动题', value: 'DRAG' },
      { label: '找重题', value: 'REPEAT' },
      { label: '标记找重题', value: 'MARK_REPEAT' },
      { label: '填空题', value: 'COMPLEX_FILLING' },
      { label: '连续计算题', value: 'SERIAL_CALCULATE' },
      { label: '标序题', value: 'MARK_NUMBER' },
      { label: '连续标序题', value: 'SERIAL_MARK_NUMBER' },
      { label: '听识题', value: 'LISTEN' },
      { label: '拼组题', value: 'SPELL' },
      { label: '听译题', value: 'LISTEN_TRANSLATION' },
      { label: '答题卡题', value: 'CARD' }
    ],
    editUrlEnum: [
      { questionType: 'DRAG', titleType: 'IMG_TEXT', value: '/paper/imgTextDrag', name: '拖动题' },
      { questionType: 'REPEAT', titleType: 'IMG_TEXT', value: '/paper/imgTextRepeat', name: '找重题' },
      { questionType: 'MARK_REPEAT', titleType: 'IMG_TEXT', value: '/paper/imgTextMark', name: '标记找重题' },
      { questionType: 'COMPLEX_FILLING', titleType: 'IMG_TEXT', value: '/paper/imgTextFilling', name: '填空题' },
      {
        questionType: 'SERIAL_CALCULATE',
        titleType: 'IMG_TEXT',
        value: '/paper/imgTextSerialCalculate',
        name: '连续计算题'
      },
      { questionType: 'MARK_NUMBER', titleType: 'IMG_TEXT', value: '/paper/imgTextMarkNumber', name: '标序题' },
      {
        questionType: 'SERIAL_MARK_NUMBER',
        titleType: 'IMG_TEXT',
        value: '/paper/imgTextSerialMarkNumber',
        name: '连续标序题'
      }
    ],
    audioEditUrlEnum: [
      { questionType: 'REPEAT', titleType: 'AUDIO', value: '/paper/audioRepeat', name: '找重题' },
      { questionType: 'LISTEN', titleType: 'AUDIO', value: '/paper/audioListen', name: '听识题' },
      { questionType: 'SPELL', titleType: 'AUDIO', value: '/paper/audioSpell', name: '拼组题' },
      { questionType: 'LISTEN_TRANSLATION', titleType: 'AUDIO', value: '/paper/audioListenTrans', name: '听译题' }
    ],
    cardEditUrlEnum: [{ questionType: 'CARD', titleType: 'CARD', value: '/paper/card', name: '答题卡题' }]
  },

  zhuXinSuan: {
    questionUrlEnum: [
      { questionType: 'UNDERSTANDING_NUMBERS', value: '/abacusMentalCalc/onlyQuestion', name: '认识数字题' },
      { questionType: 'LISTENING_CONCENTRATION', value: '/abacusMentalCalc/audioAnswer', name: '音频答案题' },
      { questionType: 'NUMBER_BALL_TRANSLATION', value: '/abacusMentalCalc/onlyQuestion', name: '拨珠系统题' },

      { questionType: 'NUMBERS', value: '/abacusMentalCalc/onlyScore', name: '数字输入题' },
      { questionType: 'STUDY_ABACUS', value: '/abacusMentalCalc/onlyScore', name: '步骤讲解题' },

      { questionType: 'CALCULATION', value: '/abacusMentalCalc/questionAnswer', name: '算式答案题' },
      { questionType: 'PLUCKING_BEADS', value: '/abacusMentalCalc/questionAnswer', name: '音频答案题' },
      { questionType: 'PLUCKING_BEADS_FORMAL', value: '/abacusMentalCalc/questionAnswer', name: '拨珠演示题型' },
      { questionType: 'PLUCKING_BEADS_CALCULATION', value: '/abacusMentalCalc/questionAnswer', name: '拨珠算式题' },

      { questionType: 'LISTENING', value: '/abacusMentalCalc/videoAudio', name: '音频讲解' },
      { questionType: 'VIDEO', value: '/abacusMentalCalc/videoAudio', name: '视频讲解题' },

      { questionType: 'NUMBER_CONCENTRATION', value: '/abacusMentalCalc/countdown', name: '数码秒计题' },

      { questionType: 'COMPLEMENT_NUMBER', value: '/abacusMentalCalc/duiBuShuNum', name: '对补数数字题型' },
      { questionType: 'COMPLEMENT_NUMBER_PHOTO', value: '/abacusMentalCalc/duiBuShuImg', name: '对补数图片题型' }
    ]
  }
};
// getters
const getters = {
  enumFormat: (state) => (arrary, key) => {
    return format(arrary, key);
  },
  pageFormat: (state) => (arrary, key) => {
    return pFormat(arrary, key);
  },
  subjectFormat: (state) => (array, key) => {
    return subFormat(array, key);
  },
  complexFormat: (state) => (array, key) => {
    return cFormat(array, key);
  },
  trainUrlFormat: (state) => (array, key) => {
    return tFormat(array, key);
  }
};

const format = function (array, key) {
  for (let item of array) {
    if (item.value === key) {
      return item.label;
    }
  }
  return null;
};
const pFormat = function (array, key) {
  for (let item of array) {
    if (item.label === key) {
      return item.value;
    }
  }
  return null;
};
const subFormat = function (array, key) {
  for (let item of array) {
    if (item.id === key) {
      return item.name;
    }
  }
  return null;
};
const cFormat = function (array, key) {
  for (let item of array) {
    if (item.titleType + item.questionType === key) {
      return item.value;
    }
  }
  return null;
};
const tFormat = function (array, key) {
  for (let item of array) {
    if (item.questionType === key) {
      return item.value;
    }
  }
  return null;
};

export default {
  namespaced: true,
  state,
  getters
};
