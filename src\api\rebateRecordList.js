/**
 *异地返利接口
 */
import request from '@/utils/request'

export default {
  // 分页查询
  pageRebateList(pageNum, pageSize, data) {
    return request({
      url: '/znyy/rebate/record/page/' + pageNum + '/' + pageSize,
      method: 'GET',
      params: data
    })
  },
  // 查看详情
  query(id){
    return request({
      url: '/znyy/rebate/record/check/' + id,
      method: 'GET'
    })
  },
//指定商户返利充值
  addRebate(data){
      return request({
          url:'/znyy/rebate/record/recharge/referee',
          method:'POST',
          data
      })
  },
  // 导出
  orderExport(listQuery) {
    return request({
      url: '/znyy/order/vip/to/excel',
      method: 'GET',
      responseType: 'blob',
      params:listQuery
    })
  },

}
