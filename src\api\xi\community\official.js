import request from '@/utils/request'

export default {
  // 分页查询
  list(data) {
    return request({
      url: '/xi/web/official/list',
      method: 'GET',
      params: data
    })
  },
  detail(id){
    return request({
      url: '/xi/web/official?id='+id,
      method: 'GET',
    })
  },

//新增
  saveOrUpdate(data) {
    return request({
      url: '/xi/web/official/saveOrUpdate',
      method: 'POST',
      data
    })
  },
  // 获取标签
  tagList(){
    return request({
      url: '/xi/web/official/tagList',
      method: 'GET',
    })
  },
  //删除
  delete(id){
    return request({
      url: '/xi/web/official',
      method: 'DELETE',
      params:{
        id:id
      }
    })
  }
}
