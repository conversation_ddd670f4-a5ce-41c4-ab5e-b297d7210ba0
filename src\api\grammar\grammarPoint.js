import request from "@/utils/request";

// 根据字典查询阶段相应的值 {dictType:'grammar_phase'}
export function queryByTypeAPI(query) {
  return request({
    url: "/dyf/web/v2/dict/queryByType",
    method: "get",
    params: query,
  });
}

// 新增语法点
export function addOrUpdateAPI(data) {
  return request({
    url: "/dyf/web/v2/grammar/addOrUpdate",
    method: "post",
    data: data,
  });
}

// 根据 id 查询语法点详情
export function detailAPI(query) {
  return request({
    url: "/dyf/web/v2/grammar/detail",
    method: "get",
    params: query,
  });
}

// 语法点分页查询
export function pageAPI(query, pageParam) {
  return request({
    url: "/dyf/web/v2/grammar/page",
    method: "get",
    params: {
      ...query,
      pageNum: pageParam.currentPage == null ? 1 : pageParam.currentPage,
      pageSize: pageParam.size == null ? 10 : pageParam.size,
    },
  });
}

// 删除语法点
export function deleteAPI(query) {
  return request({
    url: "/dyf/web/v2/grammar/delete",
    method: "delete",
    params: query,
  });
}
