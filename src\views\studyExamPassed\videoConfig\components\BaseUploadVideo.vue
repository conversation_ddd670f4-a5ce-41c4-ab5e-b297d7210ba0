<template>
  <el-upload
    action
    list-type="picture-card"
    v-loading="loading"
    :http-request="uploadHttp"
    :before-upload="handleBeforeUpload"
    :file-list="videoList"
    :limit="limit"
    :multiple="limit == 1 ? false : multiple"
    :on-exceed="onExceed"
    :on-remove="handleRemove"
    name="video"
    :class="{ hideUploadBtn: videoList.length >= limit }"
  >
    <i class="el-icon-plus"></i>

    <div class="video-card" slot="file" slot-scope="{ file }">
      <el-progress v-if="file.progress != 100" type="circle" :percentage="file.progress"></el-progress>
      <video v-else-if="file.url" class="el-upload-list__item-thumbnail video-card-show" :src="file.url"></video>
      <div v-else class="video-card-check">视频审核中</div>
      <span v-if="!disabled" class="el-upload-list__item-actions">
        <span v-if="file.progress == 100 && file.url" class="el-upload-list__item-delete" @click="handleDownload(file)">
          <i class="el-icon-download"></i>
        </span>
        <span class="el-upload-list__item-delete" @click="handleRemove(file)">
          <i class="el-icon-delete"></i>
        </span>
      </span>
    </div>
    <div slot="tip" class="el-upload__tip" style="color: #f56c6c">
      仅支持上传 {{ limit }} 个视频，课程视频最长支持{{ Math.floor(this.videoDuration / 60) }}分钟，大小 ≤ {{ isKbOrMb }}
    </div>
  </el-upload>
</template>

<script>
  import md5 from 'js-md5';
  import PlvVideoUpload from '@polyv/vod-upload-js-sdk';
  import { getUploadTokenAPI, detailAPI, saveAPI } from '@/api/uploadVideoFile';
  export default {
    name: 'BaseUploadVideo',
    props: {
      disabled: {
        type: Boolean,
        default: false
      },
      videoList: {
        type: Array,
        default() {
          return [];
        }
      },
      limit: {
        type: [Number, String],
        default: 1
      },
      multiple: {
        type: Boolean,
        default: false
      },
      videoSize: {
        type: Number,
        default: 5 * 1024 * 1024 * 1024 // 2M=>2*1024*1024 200KB=>200*1024
      },
      videoDuration: {
        type: Number,
        default: 120 * 60 // 1分钟=> 1*60
      }
    },
    data() {
      return {
        timer: null,
        loading: false,
        videoUpload: null,
        userid: '', // 接口动态获取
        secretkey: '', // 接口动态获取
        writeToken: '', // 接口动态获取
        ptime: '' // 当前时间戳
      };
    },
    computed: {
      // 动态显示MB或者KB
      isKbOrMb() {
        return this.videoSize / 1024 / 1024 / 1024 >= 1
          ? `${Number(this.videoSize / 1024 / 1024 / 1024).toFixed(0)}GB`
          : this.videoSize / 1024 / 1024 >= 1
          ? `${Number(this.videoSize / 1024 / 1024).toFixed(0)}MB`
          : `${Number(this.videoSize / 1024).toFixed(0)}KB`;
      }
    },
    watch: {
      videoList: {
        handler(newValue) {
          this.handleEchoVideo(newValue);
        },
        immediate: true
      }
    },
    created() {
      this.videoUpload = new PlvVideoUpload({
        region: 'line1', // (可选)上传线路, 默认line1
        events: {
          Error: (err) => {
            // 错误事件回调
            console.log('错误事件回调', err);
            if (err.code === 110 && this.videoList.length > 0) {
              this.videoList.splice(this.videoList.length - 1, 1);
            }
            let errMag = `(错误代码：${err.code})${err.message}`;
            this.$alert(errMag, '标题名称', {
              confirmButtonText: '确定',
              type: 'error'
            });
          },
          UploadComplete: () => {
            // 全部上传任务完成回调
            this.$emit('videoSucceed', this.videoList);
            this.videoUpload.clearAll();
            this.$message({
              message: '视频上传完成',
              type: 'success'
            });
          }
        }
      });
    },
    mounted() {
      this.autoUpdateUserData(this.videoUpload);
    },
    beforeDestroy() {
      this.timer && clearTimeout(this.timer);
    },
    methods: {
      uploadHttp({ file: video }) {
        this.onUpload(this.renameVideo(video));
      },
      renameVideo(video) {
        let videoName = video.name;
        let lastIndex = videoName.lastIndexOf('.');
        const newName = videoName.substring(0, lastIndex) + '_' + new Date().getTime() + videoName.substring(lastIndex);
        return new File([video], newName, {
          type: video.type,
          lastModified: video.lastModified
        });
      },
      async onUpload(video) {
        if (this.videoDuration > 0) {
          try {
            const duration = await this.getVideoDuration(video);
            if (duration > this.videoDuration) {
              this.$message.error(`请上传${Math.floor(this.videoDuration / 60)}分钟以内的视频`);
              this.addVideoData(null, video);
              this.handleRemove(this.videoList[this.videoList.length - 1]);
              return;
            }
          } catch (error) {
            this.$message.error(`上传的视频时长获取失败`);
            return;
          }
        }

        // 文件上传相关信息设置
        let fileSetting = {
          title: video.name, // 标题
          desc: 'jssdk 插件上传', // 描述
          cataid: process.env.VUE_APP_BASE_CATAID, // 上传分类目录 ID
          tag: '', // 标签
          luping: 0, // 是否开启视频课件优化处理，对于上传录屏类视频清晰度有所优化：0 为不开启，1 为开启
          keepsource: 0, // 是否源文件播放（不对视频进行编码）：0 为编码，1 为不编码
          state: '' // 用户自定义信息，如果提交了该字段，会在服务端上传完成回调时透传返回。
        };
        console.log('添加上传视频 开始 cataid:', process.env.VUE_APP_BASE_CATAID);
        let uploadManager = this.videoUpload.addFile(
          video, // video 为待上传的文件对象
          {
            FileStarted: this.onFileStarted, // 文件开始上传回调
            FileProgress: this.onFileProgress, // 文件上传中回调
            FileSucceed: this.onFileSucceed, // 文件上传成功回调
            FileFailed: this.onFileFailed, // 文件上传失败回调
            FileStopped: this.onFileStopped // 文件上传停止回调
          },
          fileSetting
        );
        this.addVideoData(uploadManager, video);
      },
      onFileStarted(data) {
        console.log('文件上传开始: ');
        this.videoList.find((item) => item.id === data.uploaderid).progress = 0;
      },
      onFileProgress(data) {
        console.log('文件上传中: ');
        let p = parseInt(data.progress * 100); // 上传的进度条
        this.videoList.find((item) => item.id === data.uploaderid).progress = p;
      },
      onFileSucceed(data) {
        console.log('文件上传成功: ');
        const index = this.videoList.findIndex((item) => item.id == data.uploaderid);
        if (index > -1) {
          this.videoList[index].vid = data.fileData.vid;
          this.saveVideo(data.fileData.vid);
        } else {
          this.$message.error('文件丢失，上传失败');
        }
      },
      onFileFailed(data) {
        console.log('文件上传失败: ');
      },
      onFileStopped(data) {
        console.log('文件上传停止: ');
      },
      addVideoData(uploadManager, video) {
        const videoUrl = URL.createObjectURL(video);
        let videoItem = {
          id: uploadManager ? uploadManager.id : video.uid,
          vid: '',
          fileName: uploadManager ? uploadManager.fileData.title : video.name,
          size: uploadManager ? uploadManager.fileData.size : video.size,
          url: videoUrl,
          videoImg: '',
          progress: 0
        };
        this.videoList.push(videoItem);

        // 开始单文件上传
        uploadManager && this.videoUpload.resumeFile(uploadManager.id);
        this.$emit('addVideo');
      },

      // 上传视频保存
      saveVideo(vid) {
        saveAPI({ vid }).then(() => {
          this.$message.success('保存视频信息成功');
          setTimeout(() => {
            detailAPI(vid).then((res) => {
              const index = this.videoList.findIndex((item) => item.vid === vid);
              if (index > -1) {
                this.videoList[index].videoImg = res.data.firstImg;
                this.videoList[index].progress = 100;
                this.$emit('saveVideo', { ...this.videoList[index] });
              }
            });
          }, 3000);
        });
      },
      handleBeforeUpload(video) {
        if (video.type !== 'video/mp4') {
          this.$message.error('请上传 MP4 格式的视频');
          return false;
        }

        // 视频是否小于限定的尺寸
        if (video.size >= this.videoSize) {
          this.$message.error(`请上传 ${this.isKbOrMb} 以内的视频`);
          return false;
        }
        return true;
      },
      getVideoDuration(video) {
        return new Promise((resolve, reject) => {
          const url = URL.createObjectURL(video);
          const videoDom = document.createElement('video');
          videoDom.src = url;
          videoDom.addEventListener('loadedmetadata', () => {
            let duration = videoDom.duration;
            duration ? resolve(duration) : reject(0);
            URL.revokeObjectURL(url);
          });
          videoDom.addEventListener('error', (err) => {
            URL.revokeObjectURL(url);
            reject(err);
          });
        });
      },

      // 文件超过上传个数
      onExceed() {
        this.$message.error(`最多只能上传 ${this.limit} 个视频`);
        return false;
      },

      // 视频下载
      handleDownload(video) {
        const link = document.createElement('a');
        link.target = '_blank';
        link.href = video.url;
        link.download = 'downloaded_video.mp4'; // 设置下载文件的名称
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      },

      // 视频删除
      handleRemove(video) {
        console.log('handleRemove', video);
        var index = this.videoList.findIndex((item) => item.id === video.id);
        if (index > -1) {
          this.videoUpload.removeFile(this.videoList[index].id);
          this.videoList.splice(index, 1);
        }
      },
      handleEchoVideo(videoList = [], tryNum = 0) {
        if (videoList.length <= 0) return;
        setTimeout(() => {
          if (!this.userid) {
            this.handleEchoVideo(videoList, ++tryNum);
            return;
          }
          videoList.forEach((item) => {
            if (!item.url && item.vid) {
              detailAPI(item.vid).then((res) => {
                let url = res.data.videoUrl;
                if (!url || !url.includes(res.data.vid)) {
                  this.$message.info('视频审核中，无法预览');
                  url = '';
                  return;
                }
                this.$set(item, 'url', url);
              });
            }
          });
        }, 50);
      },
      autoUpdateUserData(videoUpload) {
        // 启动获取用户信息
        this.getUserData(videoUpload);
        if (this.timer) {
          clearTimeout(this.timer);
          this.timer = null;
        }
        this.timer = setTimeout(() => {
          this.autoUpdateUserData(videoUpload);
        }, 3 * 50 * 1000);
      },
      async getUserData() {
        await this.getToken();

        // 获取用户详细信息
        let userData = {
          sign: this.getSignData().sign,
          hash: this.getSignData().hash,
          userid: this.userid,
          ptime: this.ptime
        };
        console.log('初始化上传令牌', userData);
        this.videoUpload.updateUserData(userData);
      },

      // 获取 token
      getToken() {
        return new Promise((resolve, reject) => {
          getUploadTokenAPI()
            .then((res) => {
              this.ptime = res.data.ptime;
              this.userid = res.data.userId;
              this.secretkey = res.data.secretKey;
              this.writeToken = res.data.writeToken;
              resolve();
            })
            .catch((error) => {
              reject(error);
            });
        });
      },
      getSignData() {
        // 加密信息参数
        let hash = md5(this.ptime + this.writeToken);
        let sign = md5(this.secretkey + this.ptime);
        return {
          hash: hash,
          sign: sign
        };
      }
    }
  };
</script>

<style lang="scss" scoped>
  ::v-deep.hideUploadBtn .el-upload--picture-card {
    display: none;
  }
  .video-card {
    height: 100%;
    &-show {
      background-color: none;
      opacity: 1;
    }
    &-check {
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
</style>
