
import request from '@/utils/request'
import { Select } from 'element-ui'

export default {

  // 分页查询
  pageList(data) {
    return request({
      url: '/paper/web/complex/list',
      method: 'GET',
      params: data
    })
  },

  // 添加||编辑问题
  edit(data) {
    return request({
      url: '/paper/web/complex',
      method: 'POST',
      data
    })
  },

  // 问题详情
  select(id) {
    return request({
      url:'/paper/web/complex?id='+id,
      method:'GET'
    })
  },

  // 问题详情
  randomStr(length) {
    return request({
      url:'/paper/web/complex/getRandomNumber?length='+length,
      method:'GET'
    })
  },
}
