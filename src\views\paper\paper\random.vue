<template>
  <div class="app-container paper-edit-contain">
    <div class="left">
      <el-card class="box-card">
        <div slot="header" class="clearfix">
          <span>组卷编辑</span>
        </div>
        <el-form :model="randomForm" ref="randomForm" label-width="100px"  :rules="rules">
          <el-form-item label="选择学段：" prop="gradeId" required>
            <el-select v-model="randomForm.gradeId" placeholder="选择学段"  @change="levelChange" clearable>
              <el-option v-for="item in gradeList" :key="item.id" :value="item.id" :label="item.name"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="选择维度：" prop="subjectId" required>
            <el-select v-model="randomForm.subjectId" placeholder="选择维度" @change="getKonwTree">
              <el-option v-for="item in subjectFilter" :key="item.id" :value="item.id" :label="item.name"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="知识点：">
            <treeselect
              v-model="randomForm.knowledgePoints"
              :multiple="true"
              :value-consists-of="valueConsistsOf"
              :options="konwledgeTree"
              :normalizer="normalizer"
              noOptionsText="无可用知识点"
              placeholder="请选择知识点"/>
          </el-form-item>
          <el-form-item label="单选题：">
            <el-input-number v-model="randomForm.singleChoice" :precision="0" :step="1" :max="100"></el-input-number>
          </el-form-item>
          <el-form-item label="多选题：">
          <el-input-number v-model="randomForm.multipleChoice" :precision="0" :step="1" :max="100"></el-input-number>
        </el-form-item>
          <el-form-item label="判断题：">
            <el-input-number v-model="randomForm.judge" :precision="0" :step="1" :max="100"></el-input-number>
          </el-form-item>
          <el-form-item label="填空题：">
            <el-input-number v-model="randomForm.filling" :precision="0" :step="1" :max="100"></el-input-number>
          </el-form-item>
          <el-form-item label="简答题：">
            <el-input-number v-model="randomForm.explain" :precision="0" :step="1" :max="100"></el-input-number>
          </el-form-item>
          <el-form-item label="组合题：">
            <el-input-number v-model="randomForm.combination" :precision="0" :step="1" :max="100"></el-input-number>
          </el-form-item>
          <el-form-item label="难度：">
            <el-rate v-model="randomForm.difficulty" class="question-item-rate"></el-rate>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="startRandom">开始组卷</el-button>
          </el-form-item>
        </el-form>
      </el-card>

    </div>
    <div class="right">
      <el-card>
        <div slot="header" class="clearfix">
          <span>组卷编辑</span>
        </div>
        <el-form :model="form" ref="form" label-width="100px" v-loading="formLoading" :rules="rules">
            <el-form-item label="选择学段：" prop="gradeId" required>
              <el-select v-model="form.gradeId" placeholder="选择学段"  @change="levelChange" clearable>
                <el-option v-for="item in gradeList" :key="item.id" :value="item.id" :label="item.name"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="选择维度：" prop="subjectId" required>
              <el-select v-model="form.subjectId" placeholder="选择维度" @change="getKonwTree">
                <el-option v-for="item in subjectFilter" :key="item.id" :value="item.id" :label="item.name"></el-option>
              </el-select>
            </el-form-item>
          <el-form-item label="试卷类型：" prop="paperType" required>
            <el-select v-model="form.paperType" placeholder="试卷类型">
              <el-option v-for="item in paperTypeList" :key="item.value" :value="item.value" :label="item.label"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="时间限制：" required v-show="form.paperType===4">
            <el-date-picker v-model="form.limitDateTime" value-format="yyyy-MM-dd HH:mm:ss" type="datetimerange"
                            range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="试卷名称："  prop="name" required>
            <el-input v-model="form.name"/>
          </el-form-item>
          <el-form-item label="总分：">
            {{form.allScore}}
          </el-form-item>
          <el-form-item :key="index" :label="'标题'+(index+1)+'：'" required v-for="(titleItem,index) in form.titleItems">
            <el-input v-model="titleItem.name" style="width: 80%"/>
            <el-button type="text" class="link-left" style="margin-left: 20px" size="mini" @click="addQuestion(titleItem)">
              添加题目
            </el-button>
            <el-button type="text" class="link-left" size="mini" @click="form.titleItems.splice(index,1)">删除</el-button>
            <el-card class="exampaper-item-box" v-if="titleItem.questionItems.length!==0">
              <el-form-item :key="questionIndex" :label="'题目'+(questionIndex+1)+'：'"
                            v-for="(questionItem,questionIndex) in titleItem.questionItems" style="margin-bottom: 15px">
                <el-row>
                  <el-col :span="23">
                    <QuestionShow :qType="questionItem.questionType" :question="questionItem"/>
                  </el-col>
                  <el-col :span="1">
                    <el-button type="text" size="mini" @click="titleItem.questionItems.splice(questionIndex,1)">删除
                    </el-button>
                  </el-col>
                </el-row>
              </el-form-item>
            </el-card>
          </el-form-item>
          <el-form-item label="建议学时：" prop="suggestTime" required>
            <el-input-number v-model="form.suggestTime" :precision="0" :step="1"></el-input-number>
          </el-form-item>
          <el-form-item label="及格分数：" prop="passScore" required>
            <el-input-number v-model="form.passScore" :precision="0" :step="1"></el-input-number>
          </el-form-item>
          <el-form-item label="良好分数：" prop="goodScore" required>
            <el-input-number v-model="form.goodScore" :precision="0" :step="1"></el-input-number>
          </el-form-item>
          <el-form-item label="优秀分数：" prop="excellentScore" required>
            <el-input-number v-model="form.excellentScore" :precision="0" :step="1"></el-input-number>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="submitForm">提交</el-button>
            <el-button @click="resetForm">重置</el-button>
            <el-button type="success" @click="addTitle">添加标题</el-button>
          </el-form-item>
        </el-form>
        <el-dialog :visible.sync="questionPage.showDialog"  width="70%">
          <el-form :model="questionPage.dataQuery" ref="queryForm" :inline="true">
            <el-form-item label="ID：">
              <el-input v-model="questionPage.dataQuery.id"  clearable></el-input>
            </el-form-item>
            <el-form-item label="题型：">
              <el-select v-model="questionPage.dataQuery.questionType" clearable>
                <el-option v-for="item in questionType" :key="item.value" :value="item.value" :label="item.label"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="search">查询</el-button>
            </el-form-item>
          </el-form>
          <el-table v-loading="questionPage.listLoading" :data="questionPage.tableData"
                    ref="multipleTable"
                    @selection-change="handleSelectionChange" border fit highlight-current-row style="width: 100%">
            <el-table-column type="selection" width="40"></el-table-column>
            <el-table-column prop="id" label="Id" width="180px"/>
            <el-table-column prop="questionType" label="题型" :formatter="questionTypeFormatter" width="70px"/>
            <el-table-column prop="topic" label="题目" show-overflow-tooltip>
              <template slot-scope="scope">
                <div v-html="scope.row.topic"></div>
              </template>
            </el-table-column>
          </el-table>
          <!-- 分页 -->
          <el-col :span="24" style="margin-bottom: 20px">
            <el-pagination :current-page="questionPage.tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]" layout="total, sizes, prev, pager, next, jumper"
                           :total="questionPage.tablePage.totalItems" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
          </el-col>
          <span slot="footer" class="dialog-footer">
          <el-button @click="questionPage.showDialog = false">取 消</el-button>
          <el-button type="primary" @click="confirmQuestionSelect">确定</el-button>
     </span>
        </el-dialog>
      </el-card>
    </div>

  </div>
</template>

<script>

import { mapGetters, mapState } from 'vuex'
import Pagination from '@/components/Pagination'
import QuestionShow from '../question/components/Show'
import questionApi from '@/api/paper/question'
import subjectApi from '@/api/paper/subject'
import paperApi from '@/api/paper/paper'
import { pageParamNames } from '@/utils/constants'
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'

export default {
  components: { Pagination, QuestionShow,Treeselect },
  data () {
    return {
      gradeList:[],
      valueConsistsOf:'LEAF_PRIORITY',
      normalizer(node) {
        if (node.child==null||node.child.length===0){
          return {
            id: node.id,
            label: node.name,
          }
        } return {
          id: node.id,
          label: node.name,
          children: node.child
        }
      },
      konwledgeTree:[],
      paperTypeList:[{label: '固定试卷',value:1},{label: '时段试卷',value:2},{label: '任务试卷',value:3}],
      randomForm:{
        gradeId: null,
        subjectId: null,
        knowledgePoints:null,
        singleChoice:undefined,
        multipleChoice:undefined,
        filling:undefined,
        judge:undefined,
        explain:undefined,
        combination:undefined,
        difficulty:null,
      },
      form: {
        id: null,
        gradeId: null,
        subjectId: null,
        paperType: null,
        limitDateTime: [],
        name: '',
        suggestTime: undefined,
        titleItems: [],
        typeInfo:null,
        passScore: undefined,
        goodScore: undefined,
        excellentScore:undefined
      },
      subjectList:[],
      subjectFilter: null,
      formLoading: false,
      rules: {
        gradeId: [
          { required: true, message: '请选择学段', trigger: 'change' }
        ],
        subjectId: [
          { required: true, message: '请选择维度', trigger: 'change' }
        ],
        paperType: [
          { required: true, message: '请选择试卷类型', trigger: 'change' }
        ],
        name: [
          { required: true, message: '请输入试卷名称', trigger: 'blur' }
        ],
        suggestTime: [
          { required: true, message: '请输入建议学时', trigger: 'blur' }
        ],
        passScore: [
          { required: true, message: '请输入及格分数线', trigger: 'blur' }
        ],
        goodScore: [
          { required: true, message: '请输入良好分数线', trigger: 'blur' }
        ],
        excellentScore: [
          { required: true, message: '请输入优秀分数线', trigger: 'blur' }
        ]
      },
      questionPage: {
        multipleSelection: [],
        showDialog: false,
        dataQuery:{
          id: null,
          questionType: null,
          level: null,
          subjectId: null,
        },
        // 分页
        tablePage: {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null
        },
        listLoading: true,
        tableData: [],
        total: 0
      },
      currentTitleItem: null
    }
  },
  created () {
    this.initGrade();
    this.initSubject();
    let id = this.$route.query.id
    let _this = this
    if (id && parseInt(id) !== 0) {
      _this.formLoading = true
      this.subjectFilter = this.subjectList
      paperApi.detail(id).then(re => {
        _this.form = re.data
        this.subjectFilter = this.subjectList.filter(data => data.gradeId === this.form.gradeId)
        _this.formLoading = false
      })
    }
  },
  updated () {
    // 在这里调用toggleSelection选中方法
    this.toggleSelection(this.questionPage.tableData)
  },
  methods: {
    initGrade(){
      subjectApi.gradeListAll().then(res=>{
        this.gradeList = res.data;
      })
    },
    startRandom(){
      this.$refs.randomForm.validate((valid) => {
          if (valid) {
            questionApi.randomList(this.randomForm).then(res=>{
              this.form.gradeId = res.data.gradeId;
              this.form.subjectId = res.data.subjectId;
              this.form.allScore = res.data.allScore;
              this.form.titleItems = res.data.titleItems;
            });
              console.log()
          }
      })
    },
    // 获取知识点树
    getKonwTree() {
      questionApi.konwledgeTree(this.randomForm.gradeId,this.randomForm.subjectId).then(re => {
        this.konwledgeTree = re.data;
      })
    },
    toggleSelection (rows) {
      if (rows){
        rows.forEach(row => {
          this.currentTitleItem.questionItems.forEach(a=>{
            if (row.id === a.id) {
              this.$refs.multipleTable.toggleRowSelection(row, true) }
          })
        })
      }
    },
    // 分页
    handleSizeChange(val) {
      this.questionPage.tablePage.size = val
      this.getPageList()
    },
    handleCurrentChange(val) {
      this.questionPage.tablePage.currentPage = val
      this.getPageList()
    },

    initSubject() {
      subjectApi.pageList().then(res => {
        this.subjectList = res.data.data
      })
    },
    levelChange() {
      this.randomForm.subjectId = null
      this.subjectFilter = this.subjectList.filter(data => data.gradeId === this.randomForm.gradeId)
    },

    submitForm () {
      let _this = this
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.formLoading = true
          if (this.form.titleItems.length>0){
            this.form.titleItems.forEach(item=>{
              item.questionIds = item.questionItems.map(x => x.id)
            })
          }
          paperApi.edit(this.form).then(re => {
            if (re.success) {
              _this.$message.success(re.message)
              _this.$router.push({path: '/paper/paperList'})
            } else {
              _this.$message.error(re.message)
              this.formLoading = false
            }
          }).catch(e => {
            this.formLoading = false
          })
        } else {
          return false
        }
      })
    },
    addTitle () {
      this.form.titleItems.push({
        name: '',
        questionItems: [],
        questionIds:[]
      })
    },
    addQuestion (titleItem) {
      if (!this.form.gradeId){
        return this.$message.error('请选择学段！')
      }
      if (!this.form.subjectId){
        return this.$message.error('请选择维度！')
      }
      this.currentTitleItem = titleItem
      this.questionPage.showDialog = true
      this.search()
    },
    removeTitleItem (titleItem) {
      this.form.titleItems.remove(titleItem)
    },
    removeQuestion (titleItem, questionItem) {
      titleItem.questionItems.remove(questionItem)
    },
    confirmQuestionSelect () {
      this.questionPage.multipleSelection.forEach(q => {
        if (this.currentTitleItem.questionItems.findIndex(item=> item.id === q.id)===-1){
          questionApi.select(q.id).then(re => {
            this.currentTitleItem.questionItems.push(re.data)
          })
        }
      })
      this.questionPage.showDialog = false
    },
    search () {
      this.questionPage.dataQuery.subjectId = this.form.subjectId
      this.questionPage.listLoading = true
      this.questionPage.dataQuery.pageNum = this.questionPage.tablePage.currentPage;
      this.questionPage.dataQuery.pageSize = this.questionPage.tablePage.size;
      questionApi.pageList(this.questionPage.dataQuery).then(res => {
        this.questionPage.tableData = res.data.data;
        this.questionPage.listLoading = false
        // 设置后台返回的分页参数
        pageParamNames.forEach(name => this.$set(this.questionPage.tablePage, name, parseInt(res.data[name])))
      })

    },
    handleSelectionChange (val) {
      this.questionPage.multipleSelection = val
    },
    questionTypeFormatter (row, column, cellValue, index) {
      return this.enumFormat(this.questionType, cellValue)
    },
    subjectFormatter (row, column, cellValue, index) {
      return this.subjectFormat(this.subjectList,cellValue)
    },
    resetForm () {
      this.$refs['form'].resetFields()
      let lastId = this.form.id
      this.form = {
        id: null,
        level: null,
        subjectId: null,
        paperType: null,
        limitDateTime: [],
        name: '',
        suggestTime: undefined,
        titleItems: [],
        typeInfo:null,
        passScore: undefined,
        goodScore: undefined,
        excellentScore:undefined
      }
      this.form.id = lastId
    },
  },
  computed: {
    ...mapGetters('enumItem', ['enumFormat','subjectFormat','pageFormat']),
    ...mapState('enumItem', {
      questionType: state => state.question.typeList,
      editUrlEnum: state => state.question.editUrlEnum,
    }),
  }
}
</script>

<style lang="scss">
.paper-edit-contain .left {
  width: 400px;
  margin-right: 20px;
}
.paper-edit-contain .right {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
}
.paper-edit-contain {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
}
</style>
