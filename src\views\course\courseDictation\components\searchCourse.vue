<template>
  <el-form :inline="true" ref="searchData" :model="searchData" class="SearchForm" style="padding: 20px 30px 0">
    <el-row>
      <el-col :span="8" :xs="24">
        <el-form-item label="课程编号:">
          <el-input v-model="searchData.courseCode" @keyup.enter.native="searchTable()" style="width: 200px" placeholder="请输入" clearable></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="8" :xs="24">
        <el-form-item label="课程名称:">
          <el-input v-model="searchData.courseName" @keyup.enter.native="searchTable()" style="width: 200px" placeholder="请输入" clearable></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="8" :xs="24">
        <el-form-item label="课程分类:">
          <el-select v-model="searchData.categoryCode" filterable value-key="value" placeholder="请选择" clearable>
            <el-option v-for="(item, index) in categoryTypeList" :key="index" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="8" :xs="24">
        <el-form-item label="课程状态:">
          <el-select v-model="searchData.isEnable" filterable value-key="value" placeholder="请选择" clearable>
            <el-option v-for="(item, index) in courceStatus" :key="index" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="8" :xs="24">
        <el-form-item label="添加时间:">
          <el-date-picker
            style="width: 100%"
            v-model="courseDate"
            type="datetimerange"
            value-format="yyyy-MM-dd HH:mm:ss"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            clearable
          ></el-date-picker>
        </el-form-item>
      </el-col>
      <el-col :span="8" :xs="24">
        <el-form-item label="课程类型:">
          <el-select v-model="searchData.courseType" filterable value-key="value" placeholder="请选择" clearable>
            <el-option v-for="(item, index) in courseTypeList" :key="index" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="8" :xs="24">
        <el-form-item label="等级分类:">
          <el-select v-model="searchData.courseLevel" filterable value-key="value" placeholder="请选择" clearable>
            <el-option v-for="(item, index) in courseLevelList" :key="index" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
      </el-col>

      <el-col :span="8" :xs="24">
        <el-form-item>
          <el-button type="warning" size="small" icon="el-icon-search" @click="searchTable()">搜索</el-button>
          <el-button size="small" icon="el-icon-refresh" @click="rest()">重置</el-button>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>
<script>
  import courseApitwo from '@/api/courseChildren';
  export default {
    data() {
      return {
        searchData: {
          courseCode: '',
          courseName: '',
          categoryCode: '',
          isEnable: '',
          addStartTime: '',
          addEndTime: '',
          courseType: '',
          courseLevel: ''
        },
        categoryTypeList: [],
        //课程类型
        courseTypeList: [
          { value: '0', label: '体验课' },
          { value: '1', label: '正式课' }
        ],
        //课程状态
        courceStatus: [
          { value: 1, label: '开通' },
          { value: 0, label: '暂停' }
        ],
        // 等级分类
        courseLevelList: [
          { value: '0', label: '启蒙' },
          { value: '1', label: '基础' },
          { value: '2', label: '进阶' }
        ],
        courseDate: ''
      };
    },
    created() {
      this.getCategoryType();
    },
    methods: {
      // 获取分类返回类型
      getCategoryType() {
        courseApitwo.categoryType().then((res) => {
          this.categoryTypeList = res.data;
        });
      },
      //搜索
      searchTable() {
        if (Array.isArray(this.courseDate)) {
          this.searchData.addStartTime = this.courseDate[0];
          this.searchData.addEndTime = this.courseDate[1];
        }
        this.$emit('onSearch', this.searchData);
        this.searchData.addStartTime = '';
        this.searchData.addEndTime = '';
      },
      rest() {
        this.courseDate = '';
        for (let searchKey in this.searchData) {
          this.searchData[searchKey] = '';
        }
        this.$refs.searchData.resetFields();
        this.$emit('onSearch', this.searchData);
      },
      setSearchData(val) {
        this.searchData = JSON.parse(val);
      }
    }
  };
</script>
<style></style>
