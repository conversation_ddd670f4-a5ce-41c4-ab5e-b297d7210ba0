import request from "@/utils/request";

// 试题分页
export function listQuestionAPI(query) {
  return request({
    url: "/znyy/superReadQuestion/list",
    method: "get",
    params: query,
  });
}

// 试题新增
export function addQuestionAPI(query) {
  return request({
    url: "/znyy/superReadQuestion/add",
    method: "put",
    data: query,
  });
}

// 试题编辑
export function editQuestionAPI(query) {
  return request({
    url: "/znyy/superReadQuestion/edit",
    method: "post",
    data: query,
  });
}

// 试题删除
export function deleteQuestionAPI(query) {
  return request({
    url: "/znyy/superReadQuestion/delete",
    method: "delete",
    params: query,
  });
}

// 试题查看详情
export function findQuestionAPI(query) {
  return request({
    url: "/znyy/superReadQuestion/find",
    method: "get",
    params: query,
  });
}
