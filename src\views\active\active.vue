<template>
  <div class="app-container">
    <!-- 新增按钮 -->
    <el-row style="padding: 15px 15px 15px 15px; margin-bottom: 15px ;box-shadow: 0 2px 4px rgba(0, 0, 0, .12), 0 0 6px rgba(0, 0, 0, .04)">
      <el-col :span="10">
        <el-col :span="6" class="marginbottom" style="line-height: 36px;">活动名</el-col>
        <el-col :span="15">
          <el-input id="name" v-model="dataQuery.name" name="id" placeholder="请输入活动名" />
        </el-col>
      </el-col>
      <el-col :span="10">
        <el-col :span="6" class="marginbottom" style="line-height: 36px;">上架活动</el-col>
        <el-col :span="15" style="margin-top: 7px">
          <el-switch v-model="dataQuery.isActive" active-color="#13ce66"/></el-col>
      </el-col>

      <el-col :span="4" style="text-align: right;">
        <el-button type="primary" icon="el-icon-search" size="mini" @click="fetchData()" >搜索</el-button>
      </el-col>
    </el-row>

    <el-row>
      <el-col :span="24" style="text-align: right;margin-bottom: 30px;">
        <el-button type="success" icon="el-icon-plus" size="mini" @click="clickAdd" >新增活动</el-button>
      </el-col>
    </el-row>

    <el-table v-loading="tableLoading" stripe :data="tableData" border style="width: 100%;margin-bottom: 30px;">
      <el-table-column prop="id" label="活动编号" align="center"  width="180"/>
      <el-table-column prop="name" label="活动名" align="center" :show-overflow-tooltip="true"/>
      <el-table-column label="操作" align="center" width="200">
        <template slot-scope="scope">
          <el-tooltip content="编辑" placement="top">
            <el-button size="mini" icon="el-icon-edit" type="warning" @click="handleUpdate(scope.row.id)">编辑</el-button>
          </el-tooltip>
          <el-tooltip content="删除" placement="top">
            <el-button size="mini" type="danger" icon="el-icon-delete" @click="handleDelete(scope.row.id)">删除
            </el-button>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column prop="typeName" label="活动类型"  align="center"  >
        <template slot-scope="scope">
          <el-button v-if="scope.row.typeName ==='学习机'" type="success" size="mini" plain disabled>{{scope.row.typeName}}</el-button>
          <el-button v-if="scope.row.typeName ==='体验课'" type="primary" size="mini" plain disabled>{{scope.row.typeName}}</el-button>
          <el-button v-else-if="scope.row.typeName !=='学习机'&&scope.row.typeName !=='体验课'" type="warning" size="mini" plain disabled>{{scope.row.typeName}}</el-button>
        </template>
      </el-table-column>

      <el-table-column prop="title" label="活动标题" align="center" :show-overflow-tooltip="true"/>
      <el-table-column prop="priceOrigin" label="原价" align="center" />
      <el-table-column prop="price" label="价格"  align="center"  >
        <template slot-scope="scope">
          <span style="color: red">{{scope.row.price}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="timeCreate" label="创建时间" align="center" :show-overflow-tooltip="true"/>
    </el-table>

    <!-- 分页 -->
    <el-col :span="20">
      <el-pagination
        :current-page="tablePage.currentPage"
        :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="tablePage.totalItems"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-col>

    <!-- 修改或者新增弹窗 -->
    <el-dialog
      :title="addOrUpdate?'新增活动':'修改活动'"
      :visible.sync="dialogVisible"
      width="70%"
      :close-on-click-modal="false"
      @close="close"
    >
      <el-form
        :ref="addOrUpdate?'addActiveData':'updateActive'"
        :rules="rules"
        :model="addOrUpdate?addActiveData:updateActive"
        label-position="left"
        label-width="120px"
      >

        <el-tabs v-model="activeName" type="border-card">
          <el-tab-pane label="活动信息" name="first">
            <el-row>
              <el-form-item v-if="!addOrUpdate" label="活动编号" prop="id">
                <el-input v-model="updateActive.id" disabled />
              </el-form-item>

              <el-col :span="11">
                <el-form-item label="活动名" prop="name">
                  <el-input v-if="addOrUpdate" v-model="addActiveData.name" />
                  <el-input v-if="!addOrUpdate" v-model="updateActive.name" />
                </el-form-item>
              </el-col>
              <el-col :span="2" style="height: 1px;" />
              <el-col :span="11">
                <el-form-item label="活动类型" prop="type">
                  <el-select
                    v-if="addOrUpdate"
                    v-model="addActiveData.type"
                    filterable
                    value-key="value"
                    placeholder="请选择"
                  >
                    <el-option
                      v-for="(item,index) in activeType"
                      :key="index"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                  <el-select
                    v-if="!addOrUpdate"
                    v-model="updateActive.type"
                    filterable
                    value-key="value"
                    placeholder="请选择"
                  >
                    <el-option
                      v-for="(item,index) in activeType"
                      :key="index"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-form-item label="活动时间" prop="timeEnd">
                <el-date-picker
                  v-if="addOrUpdate"
                  v-model="addActiveData.timeFrame"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  value-format="yyyy-MM-dd HH:mm:ss"
                />

                <el-date-picker
                  v-if="!addOrUpdate"
                  v-model="updateActive.timeFrame"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  value-format="yyyy-MM-dd HH:mm:ss"
                />
              </el-form-item>


              <el-col :span="11">

                <el-form-item label="包含的产品数量" prop="productNumber">
                  <el-input v-if="addOrUpdate" v-model="addActiveData.productNumber" />
                  <el-input v-if="!addOrUpdate" v-model="updateActive.productNumber" />
                </el-form-item>
                <el-form-item label="活动总库存" prop="inventoryCountTotal">
                  <el-input v-if="addOrUpdate" v-model="addActiveData.inventoryCountTotal" />
                  <el-input v-if="!addOrUpdate" v-model="updateActive.inventoryCountTotal" />
                </el-form-item>
                <el-form-item label="是否上架" prop="isActive">
                  <el-switch v-if="addOrUpdate" v-model="addActiveData.isActive" active-color="#13ce66" />
                  <el-switch v-if="!addOrUpdate" v-model="updateActive.isActive" active-color="#13ce66" />
                </el-form-item>
                <el-form-item v-if="!addOrUpdate" label="实际已售数量" prop="sellerNumberActual">
                  <el-input v-if="!addOrUpdate" v-model="updateActive.sellerNumberActual" readonly="readonly" />
                </el-form-item>
                <el-form-item v-if="isShowRelevance" label="关联产品" prop="productId">
                  <el-select v-if="addOrUpdate" v-model="addActiveData.productId" value-key="value" placeholder="请选择">
                    <el-option v-for="(item,index) in productList" :key="index" :label="item.label" :value="item.value" />
                  </el-select>
                  <el-select v-if="!addOrUpdate" v-model="updateActive.productId" value-key="value" placeholder="请选择">
                    <el-option v-for="(item,index) in productList" :key="index" :label="item.label" :value="item.value" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="2" style="height: 1px;" />
              <el-col :span="11">

                <el-form-item label="原价(元)" prop="priceOrigin">
                  <el-input v-if="addOrUpdate" v-model="addActiveData.priceOrigin" type="number" />
                  <el-input v-if="!addOrUpdate" v-model="updateActive.priceOrigin" type="number" />
                </el-form-item>
                <el-form-item label="现价(元)" prop="price">
                  <el-input v-if="addOrUpdate" v-model="addActiveData.price" type="number" />
                  <el-input v-if="!addOrUpdate" v-model="updateActive.price" type="number" />
                </el-form-item>
                <el-form-item label="限制次数/人" prop="buyerCountLimit">
                  <el-input v-if="addOrUpdate" v-model="addActiveData.buyerCountLimit" />
                  <el-input v-if="!addOrUpdate" v-model="updateActive.buyerCountLimit" />
                </el-form-item>
                <el-form-item label="显示已售数量" prop="sellerNumber">
                  <el-input v-if="addOrUpdate" v-model="addActiveData.sellerNumber" />
                  <el-input v-if="!addOrUpdate" v-model="updateActive.sellerNumber" />
                </el-form-item>

              </el-col>
            </el-row>

            <div>
              <el-form-item label="活动标题" prop="title">
                <el-input v-if="addOrUpdate" v-model="addActiveData.title" />
                <el-input v-if="!addOrUpdate" v-model="updateActive.title" />
              </el-form-item>
              <el-form-item label="活动副标题" prop="titleSub">
                <el-input v-if="addOrUpdate" v-model="addActiveData.titleSub" />
                <el-input v-if="!addOrUpdate" v-model="updateActive.titleSub" />
              </el-form-item>

              <!--              <el-row>-->
              <!--                <el-col :span="4"><span style="font-weight: bold;">活动主图</span></el-col>-->
              <!--                <el-col :span="20">-->
              <!--                  <el-upload-->
              <!--                    ref="clearupload"-->
              <!--                    v-loading="uploadLoading"-->
              <!--                    list-type="picture-card"-->
              <!--                    action=""-->
              <!--                    element-loading-text="图片上传中"-->
              <!--                    :limit="1"-->
              <!--                    :on-exceed="justPictureNum"-->
              <!--                    :file-list="!addOrUpdate? fileList: fileList.name"-->
              <!--                    :http-request="uploadPrHttp"-->
              <!--                    :on-preview="handlePictureCardPreview"-->
              <!--                    :on-remove="handleRemove"-->
              <!--                  >-->
              <!--                    <i class="el-icon-plus" />-->
              <!--                  </el-upload>-->
              <!--                </el-col>-->
              <!--              </el-row>-->
              <el-row>
                <el-col :span="4"><span style="font-weight: bold;">活动详情页图片</span></el-col>
                <el-col :span="20">
                  <el-upload
                    ref="clearupload"
                    v-loading="uploadLoading"
                    list-type="picture-card"
                    action=""
                    element-loading-text="图片上传中"
                    :limit="1"
                    :on-exceed="justPictureNum"
                    :file-list="!addOrUpdate? fileDetailList: fileDetailList.name"
                    :http-request="uploadDetailHttp"
                    :on-preview="handlePictureCardPreview"
                    :on-remove="handleRemoveDetail"
                  >
                    <i class="el-icon-plus" />
                  </el-upload>
                </el-col>
              </el-row>
            </div>

          </el-tab-pane>

        </el-tabs>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="warning" @click="close">取消</el-button>

        <el-button  v-if="addOrUpdate" size="mini" type="success" @click="addActiveFun('addActiveData')">新增</el-button>
        <el-button  v-if="!addOrUpdate" size="mini" type="primary" @click="updateActiveFun('updateActive')">修改</el-button>
      </div>
    </el-dialog>

    <el-dialog :visible.sync="dialogUploadVisible" :close-on-click-modal="false">
      <img width="100%" :src="dialogImageUrl" alt>
    </el-dialog>
  </div>

</template>

<script>
import activeApi from '@/api/active'
import {
  ossPrClient
} from '@/api/alibaba'

import {
  pageParamNames
} from '@/utils/constants'

export default {
  components: {},
  data() {
    return {
      tableLoading: false,
      dataQuery: {
        name: '',
        isActive: true
      },
      activeType: [], // 活动类型
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      tableData: [],
      productList: [], // 商品下拉列表数据

      dialogVisible: false, // 修改弹窗是否展示
      addOrUpdate: true, // 是新增还是修改
      activeName: 'first', // tab默认第一个
      addActiveData: {}, // 新增活动
      updateActive: {}, // 修改数据
      rules: { // 表单提交规则
        name: [{
          required: true,
          message: '必填',
          trigger: 'blur'
        }],
        productId: [{
          required: true,
          message: '必填',
          trigger: 'blur'
        }],
        productNumber: [{
          required: true,
          message: '必填',
          trigger: 'blur'
        }],
        inventoryCountTotal: [{
          required: true,
          message: '必填',
          trigger: 'blur'
        }],
        remainderNumber: [{
          required: true,
          message: '必填',
          trigger: 'blur'
        }],
        type: [{
          required: true,
          message: '必填',
          trigger: 'change'
        }],

        price: [{
          required: true,
          message: '必填',
          trigger: 'blur'
        }],
        priceOrigin: [{
          required: true,
          message: '必填',
          trigger: 'blur'
        }],
        title: [{
          required: true,
          message: '必填',
          trigger: 'blur'
        }],
        titleSub: [{
          required: true,
          message: '必填',
          trigger: 'blur'
        }]
      },

      fileListPending: [], // 待处理已上传图片信息
      fileList: [], // 上传图片已有图片列表

      fileDetailListPending: [], // 待处理已上传图片信息
      fileDetailList: [], // 上传图片已有图片列表

      dialogUploadVisible: false,
      dialogImageUrl: '', // 上传图片预览

      uploadLoading: false, // 上传图片加载按钮
      fullscreenLoading: false, // 保存啥的加载

      content: '',
      isUploadSuccess: true, // 是否上传成功
      isShowRelevance: true // 新增或修改是否展示关联产品

    }
  },
  created() {
    ossPrClient()
    this.fetchData()
    this.getactiveType()
    this.getProductList()
    this.updateActive = {
      'id': '',
      'productId': ''
    }
  },
  methods: {
    // 获取UUid
    getUUid: function() {
      const s = []
      const hexDigits = '0123456789abcdef'
      for (let i = 0; i < 36; i++) {
        s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1)
      }
      s[14] = '4'
      s[19] = hexDigits.substr((s[19] & 0x3) | 0x8, 1)
      s[8] = s[13] = s[18] = s[23] = '-'
      const UUid = s.join('')
      return UUid
    },

    // 获取商品下拉列表
    getProductList() {
      activeApi.productList(1, 10000, '').then(res => {
        const newProduct = []
        let a = ''
        this.productList = res.data.data
        this.productList.forEach(item => {
          a = { value: item.id, label: item.name }
          newProduct.push(a)
        })
        this.productList = newProduct
      })
    },
    // 选择关联产品类型
    changeRelevance(e) {
      if (e === 'ZXSM') {
        this.isShowRelevance = false
        if (this.addOrUpdate) {
          this.addActive.productId = 1
        } else {
          this.updateActive.productId = 1
        }
      } else {
        this.isShowRelevance = true

        if (!this.addOrUpdate) {
          this.updateActive.productId = ''
        }
      }
    },
    // 获取活动返回类型
    getactiveType() {
      activeApi.activeType().then(res => {
        this.activeType = res.data
      })
    },

    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val
      this.fetchData()
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val
      this.fetchData()
    },

    // 查询
    fetchData() {
      const that = this
      that.tableLoading = true
      activeApi.activeList(that.tablePage.currentPage, that.tablePage.size, that.dataQuery).then(res => {
        that.tableData = res.data.data
        that.tableLoading = false
        // 设置后台返回的分页参数
        pageParamNames.forEach(name => that.$set(that.tablePage, name, parseInt(res.data[name])))
      })
    },

    // 删除活动
    handleDelete(id) {
      this.$confirm('您确定要删除此条活动？', '提示', confirm).then(() => {
        activeApi.deleteAdvice(id).then(() => {
          this.$message.success('删除活动成功')
          this.fetchData()
        })
      }).catch(() => {
        this.$message.info('已取消操作')
      })
    },

    // 新增活动板块
    // 点击新增按钮
    clickAdd() {
      this.getactiveType()
      this.getProductList()
      this.addActiveData = {
        'imageListFilePath': '',
        'imageInfoFilePath': '',
        'isActive': true,
        'name': '',
        'inventoryCountTotal': '',
        'sellerNumber': '',
        'sellerNumberActual': '',
        'price': '',
        'priceOrigin': '',
        'title': '',
        'titleSub': '',
        'type': '',
        'remainderNumber': ''

      }
      this.dialogVisible = true
      this.addOrUpdate = true
      if (this.fileList.length !== 0) {
        this.$refs.clearupload.clearFiles()
      }
      // this.activeName= 'first',   //tab默认第一个
      this.$nextTick(() => this.$refs['addActiveData'].clearValidate())
    },
    /* 新增行*/
    // 新增赠品行初始执行事件
    addRawValue(data) {
      data.forEach(item => {
        for (const key in item) {
          if (key !== 'nameFreebie' && key !== 'idFreebie') {
            // 第一次点击新增得到的数据
            if (item.hasOwnProperty(key)) {
              if (item[key].edit === undefined) {
                item[key] = {
                  value: item[key],
                  edit: false
                }
              }
            }
          }
        }
      })
    },



    // 删除上传图片
    handleRemove(file, fileList) {
      const that = this
      if (!that.addOrUpdate) {
        that.fileList = fileList
      } else {
        for (let a = 0; a < that.fileListPending.length; a++) {
          that.fileListPending[a].uid === file.uid ? that.fileList.splice(a, 1) : ''
        }
      }
    },

    // 删除上传图片
    handleRemoveDetail(file, fileList) {
      const that = this
      if (!that.addOrUpdate) {
        that.fileDetailList = fileList
      } else {
        for (let a = 0; a < that.fileDetailListPending.length; a++) {
          that.fileDetailListPending[a].uid === file.uid ? that.fileDeatiList.splice(a, 1) : ''
        }
      }
    },
    // 上传图片预览
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogUploadVisible = true
    },
    // 上传图片数量超限
    justPictureNum(file, fileList) {
      this.$message.warning(`当前限制选择1个文件`)
    },
    // 上传图片请求
    uploadPrHttp({ file }) {
      this.uploadLoading = true
      const that = this
      const fileName = 'manage/' + Date.parse(new Date())
      that.$nextTick(function() {
        ossPrClient().put(fileName, file).then(({
          res,
          url,
          name
        }) => {
          if (res && res.status === 200) {
            console.log(`阿里云OSS上传图片成功回调1`, res, url, name)
            if (!that.addOrUpdate) {
              that.fileList.push({ 'uid': file.uid, 'url': url })
            } else { // 新增上传图片
              that.fileList.push({ name })
              that.addActiveData.imageListFilePath = name
            }
            that.$nextTick(() => {
              that.uploadLoading = false
            })
          }
        }).catch(err => {
          that.$message.error('上传图片失败请检查网络或者刷新页面')
          console.log(`阿里云OSS上传图片失败回调`, err)
        })
      })
    },

    uploadDetailHttp({ file }) {
      this.uploadLoading = true
      const that = this
      const fileName = 'manage/' + Date.parse(new Date())
      that.$nextTick(function() {
        ossPrClient().put(fileName, file).then(({
          res,
          url,
          name
        }) => {
          if (res && res.status === 200) {
            console.log(`阿里云OSS上传图片成功回调1`, res, url, name)
            if (!that.addOrUpdate) {
              that.fileDetailList.push({ 'uid': file.uid, 'url': url })
            } else { // 新增上传图片
              that.fileDetailList.push({ name })
              that.addActiveData.imageInfoFilePath = name
            }
            that.$nextTick(() => {
              that.uploadLoading = false
            })
          }
        }).catch(err => {
          that.$message.error('上传图片失败请检查网络或者刷新页面')
          console.log(`阿里云OSS上传图片失败回调`, err)
        })
      })
    },

    // 新增活动提交后台
    addActiveFun(ele) {
      const that = this
      var a = that.addActiveData.timeFrame
      that.addActiveData.timeStart = a[0]
      that.addActiveData.timeEnd = a[1]
      that.$refs[ele].validate(valid => { // 表单验证
        if (valid) {
          const loading = this.$loading({
            lock: true,
            text: '新增提交活动',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          })
          console.log(that.addActiveData)

          activeApi.addActive(that.addActiveData).then(() => {
            that.dialogVisible = false
            loading.close()
            that.$nextTick(() => that.fetchData())
            that.$message.success('新增活动成功')
          }).catch(err => {
            if (err === 'error') {
              loading.close()
            }
          })
        } else {
          console.log('error submit!!')
          // loading.close();
          return false
        }
      })
    },

    // 编辑修改活动
    // 点击编辑按钮
    handleUpdate(id) {
      const that = this
      that.dialogVisible = true
      that.addOrUpdate = false
      activeApi.queryActive(id).then(res => {
        console.log(res)
        that.updateActive = res.data
        const arr = [res.data.timeStart, res.data.timeEnd]

        this.$set(this.updateActive, 'timeFrame', arr)

        // const arr = [res.data.timeStart, res.data.timeEnd]
        // that.addActiveData.timeFrame = arr
        // that.fileShowList = [{
        //   url: that.aliUrl + that.updateActive.imageListFilePath
        // }]
        // that.fileList = [{
        //   url: that.aliUrl + that.updateActive.imageListFilePath
        // }]
        if (that.updateActive.imageInfoFilePath !== null && that.updateActive.imageInfoFilePath.length > 1) {
          that.fileDetailList = [{
            url: that.aliUrl + that.updateActive.imageInfoFilePath
          }]
        } else {
          that.fileDetailList = []
        }
      })
    },

    // 修改活动提交
    updateActiveFun(ele) {
      const that = this
      that.$refs[ele].validate(valid => { // 表单验证
        if (valid) {
          console.log(that.fileList)
          console.log(that.fileDetailList)
          // const a = that.fileList[0].url.split('manage/')
          // that.updateActive.imageListFilePath = 'manage/' + a[a.length - 1]
          //
          const b = that.fileDetailList[0].url.split('manage/')
          that.updateActive.imageInfoFilePath = 'manage/' + b[b.length - 1]

          const loading = this.$loading({
            lock: true,
            text: '修改活动信息提交',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          })
          console.log(that.updateActive)
          that.updateActive.timeStart = that.updateActive.timeFrame[0]
          that.updateActive.timeEnd = that.updateActive.timeFrame[1]
          activeApi.updateActive(that.updateActive).then(() => {
            that.dialogVisible = false
            loading.close()
            that.$nextTick(() => that.fetchData())
            that.$message.success('修改活动成功')
          }).catch(err => {
            if (err === 'error') {
              loading.close()
            }
          })
        } else {
          console.log('error submit!!')
          // loading.close();
          return false
        }
      })
    },

    // base64转blob
    toBlob(urlData, fileType) {
      const bytes = window.atob(urlData)
      let n = bytes.length
      const u8arr = new Uint8Array(n)
      while (n--) {
        u8arr[n] = bytes.charCodeAt(n)
      }
      return new Blob([u8arr], {
        type: fileType
      })
    },

    // 关闭弹窗
    close() {
      this.dialogVisible = false
    }

  }
}
</script>
