import request from '@/utils/request'

export default {
  getIsSelfDeliver(data) {
    return request({
      url: '/znyy/web/schooldeliverconfig/getIsSelfDeliver',
      method: 'GET',
      params: data
    })
  },
  saveorupdate(merchantCode,isSelfDeliver) {
    return request({
      url: '/znyy/web/schooldeliverconfig/saveorupdate',
      method: 'POST',
      params: {
        "merchantCode":merchantCode,
        "isSelfDeliver":isSelfDeliver
      }
    })
  },
}
