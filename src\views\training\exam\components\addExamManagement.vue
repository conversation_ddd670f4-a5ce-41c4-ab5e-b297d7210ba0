<template>
  <div class="add-video-dialog">
    <el-dialog width="50%" :visible.sync="dialogParam.visible" @closed="closeAddDialog()" :close-on-click-modal="false" @submit.native.prevent>
      <div slot="title" class="dialog-title">
        <span v-if="dialogParam.type === 'add'">新增考题</span>
        <span v-if="dialogParam.type === 'edit'">编辑考题</span>
      </div>
      <el-form :model="labelForm" ref="addForm" label-width="120px" label-position="right" :rules="rules">
        <!-- <el-form-item label="考题类目：" prop="examType">
          <el-select style="width: 100%" v-model="labelForm.examType" placeholder="请选择" >
            <el-option  v-for="item in examTypeList" :key="item.value" :label="item.label"  :value="item.value"  >
            </el-option>
          </el-select>
        </el-form-item> -->
        <el-form-item label="关联课程：" prop="courseId">
          <el-select style="width: 100%" v-model="labelForm.courseId" placeholder="请选择">
            <el-option v-for="item in courseList" :key="item.id" :label="item.courseName" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="题目：" prop="questionName">
          <el-input style="width: 100%" placeholder="请输入题目" v-model.trim="labelForm.questionName"></el-input>
        </el-form-item>
        <el-form-item label="考题类型：" prop="questionType" @change="questionTypeChange">
          <el-select style="width: 100%" v-model="labelForm.questionType" placeholder="请选择">
            <el-option v-for="item in questions" :key="item.dictValue" :label="item.dictLabel" :value="item.dictValue"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="选项设置：" prop="questionOptionAnswer">
          （请选择正确的答案如）：正确答案：
          <el-input
            style="width: 50%"
            placeholder="请输入正确答案"
            :maxlength="labelForm.questionType == 2 ? labelForm.form.list.length : 1"
            v-model.trim="labelForm.questionOptionAnswer"
          ></el-input>
        </el-form-item>
        <el-form-item label="选项：">
          <div class="standars-detail">
            <el-form ref="form" :model="labelForm.form">
              <el-table
                max-height="530px"
                :header-cell-style="{ 'background-color': '#F6F7F8', color: '#424854' }"
                :data="labelForm.form.list"
                class="el-table"
                border
                style="width: 100%"
              >
                <el-table-column prop="questionOptionDescription" label="选项" min-width="40">
                  <template slot-scope="{ row, $index }">
                    <el-form-item label="" :prop="'list.' + $index + '.questionOptionDescription'" :rules="[{ required: true, message: '请输入选项内容', trigger: 'blur' }]">
                      <el-input v-model="row.questionOptionDescription" maxlength="64" placeholder="请输入"></el-input>
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column prop="questionOptionContent" label="请输入选项内容" min-width="100">
                  <template slot-scope="{ row, $index }">
                    <el-form-item label="" :prop="'list.' + $index + '.questionOptionContent'" :rules="[{ required: true, message: '请输入选项内容', trigger: 'blur' }]">
                      <el-input v-model="row.questionOptionContent" maxlength="64" placeholder="请输入"></el-input>
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column label="操作" min-width="30" align="center">
                  <template slot-scope="{ row, $index }">
                    <el-button @click="handleDel(row, $index)" type="text">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
              <el-button style="margin-top: 20px" class="add-class" @click="handleAdd" type="primary" size="small">
                <i class="el-icon-plus"></i>
                添加选项
              </el-button>
            </el-form>
          </div>
        </el-form-item>
        <el-form-item label="分析：">
          <el-input style="width: 100%" placeholder="请输入分析" maxlength="255" v-model.trim="labelForm.questionDescription"></el-input>
        </el-form-item>
      </el-form>
      <!-- 底部按钮栏 -->
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" size="small" @click="submitForm()">确认</el-button>
        <el-button size="small" @click="closeAddDialog()">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
  import examApi from '@/api/training/exam';
  export default {
    name: 'addExamManagement',
    components: {},
    props: {
      dialogParam: {
        type: Object,
        default() {
          return {};
        }
      },
      courseList: {
        type: Array,
        default: []
      },
      questions: {
        type: Array,
        default: []
      }
    },
    data() {
      var validatePass = (rule, value, callback) => {
        console.log(this.labelForm.form, 'this.labelForm.form');
        if (this.labelForm.form.list.length < 1) {
          callback(new Error('请添加选项'));
        }
        callback();
      };
      return {
        labelForm: {
          // examType: "",
          courseId: '',
          questionOptionAnswer: '',
          questionName: '',
          questionType: '',
          questionDescription: '',
          form: {
            list: [{ questionOptionDescription: 'A', questionOptionContent: '' }]
          }
        },
        // 关联课程数据
        rules: {
          // examType: [
          //   {
          //     required: true,
          //     message: "请选择考题类目",
          //     trigger: "change",
          //   },
          // ],
          courseId: [
            {
              required: true,
              message: '请选择关联课程',
              trigger: 'change'
            }
          ],
          questionName: [
            {
              required: true,
              message: '题目不能为空',
              trigger: 'blur'
            }
          ],
          questionType: [
            {
              required: true,
              message: '请选择考题类型',
              trigger: 'change'
            }
          ],
          questionOptionAnswer: [
            {
              required: true,
              message: '正确答案不能为空',
              trigger: 'blur'
            }
          ]
        }
      };
    },
    mounted() {},
    methods: {
      open(info) {
        this.labelForm = Object.assign(this.labelForm, info);
        examApi.questionDetail({ id: info.id }).then((res) => {
          this.labelForm = { ...this.labelForm, ...res.data };
          this.labelForm.form.list = res.data.questionOptionList;
          this.labelForm.questionOptionAnswer = '';
          this.labelForm.questionType = this.labelForm.questionType + '';
          res.data.questionOptionList.forEach((item) => {
            if (item.isAnswer == 1) {
              this.labelForm.questionOptionAnswer += item.questionOptionDescription;
            }
          });
        });
      },
      // 添加选项
      handleAdd() {
        const optionIndex = this.labelForm.form.list.length;
        //  <!--  list: [{ questionOptionDescription: "A", questionOptionContent: "" ,questionOptionSort:1}], -->
        const nextOption = String.fromCharCode(65 + optionIndex); // 将数字转换为对应的字母，65 是大写字母 A 的 ASCII 值
        this.labelForm.form.list.push({
          questionOptionDescription: nextOption,
          questionOptionContent: ''
        });
      },
      // 删除选项
      handleDel(row, index) {
        this.labelForm.form.list.splice(index, 1); // 先删除选项

        // 更新剩余选项的字母
        this.labelForm.form.list.forEach((item, i) => {
          item.option = String.fromCharCode(65 + i); // 重新计算字母
        });
      },
      //切换题目类型
      questionTypeChange() {
        this.labelForm.questionOptionAnswer = '';
      },
      // 提交
      submitForm() {
        let formValid = false;
        let addFormValid = false;
        console.log(this.labelForm);
        this.labelForm.questionOptionList = [];
        let show = true;
        this.labelForm.form.list.forEach((item, index) => {
          item.questionOptionIsAnswer = false;
          item.questionOptionSort = index;
          if (this.labelForm.questionOptionAnswer.indexOf(item.questionOptionDescription) != -1) {
            item.questionOptionIsAnswer = true;
            show = false;
          }
          console.log(index);
          this.labelForm.questionOptionList.push(item);
        });
        if (show) {
          this.labelForm.questionOptionAnswer = '';
        }
        this.labelForm.questionOptionList;
        console.log(this.labelForm.questionOptionAnswer);
        console.log();
        this.$refs.form.validate((valid) => {
          formValid = valid;
        });

        this.$refs.addForm.validate((valid) => {
          addFormValid = valid;
        });
        if (formValid && addFormValid) {
          // delete this.labelForm.form.list
          examApi.questionCreateOrUpdate(this.labelForm).then((res) => {
            this.$emit('closeDialog', this.dialogParam.type);
          });
        } else {
          console.log('error submit!!');
          return false;
        }
      },
      closeAddDialog() {
        this.$emit('closeDialog', this.dialogParam.type);
      }
    }
  };
</script>
<style scoped>
  .content {
    line-height: 30px;
    width: 100%;
    height: 30px;
    background-color: #d7d7d7;
    margin-bottom: 20px;
  }
  .contentTxt {
    margin-left: 10px;
  }
  ::v-deep .el-form-item {
    margin-bottom: 18px;
  }
</style>
