/**
 * 商品相关接口
 */
import request from '@/utils/request'
import da from 'element-ui/src/locale/lang/da'

export default {
  addGrammarWords(data) {
    return request({
      url: '/dyf/web/grammar/question/add',
      method: 'POST',
      data,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },
  getGrammarQuestion(pageNum, pageSize, data) {
    return request({
      url: '/dyf/web/grammar/question/page/' + pageNum + '/' + pageSize,
      method: 'GET',
      params: data
    })
  },
  findOneById(id) {
    return request({
      url: '/dyf/web/grammar/question/one?id=' + id,
      method: 'GET'
    })
  },
  editGrammarQuestion(data) {
    return request({
      url: '/dyf/web/grammar/question/edit',
      method: 'PUT',
      data
    })
  },
  getPhaseType() {
    return request({
      url: '/dyf/web/grammar/phase',
      method: 'GET'
    })
  },
  getQuesType() {
    return request({
      url: '/dyf/web/grammar/type',
      method: 'GET'
    })
  },
  deleteQuestion(id) {
    return request({
      url: '/dyf/web/grammar/question/delete?id=' + id,
      method: 'DELETE'
    })
  },
  getGrammarList(pageNum, pageSize, data) {
    return request({
      url: '/dyf/web/grammar/list/' + pageNum + '/' + pageSize,
      method: 'GET',
      params: data
    })
  },
  deleteGrammar(id) {
    return request({
      url: '/dyf/web/grammar/delete?id=' + id + '&isGrammar=1',
      method: 'DELETE'
    })
  },
  getKnowledgeList(pageNum, pageSize, data) {
    return request({
      url: '/dyf/web/grammar/knowledge/list/' + pageNum + '/' + pageSize,
      method: 'GET',
      params: data
    })
  },
  getKnowledge(id) {
    return request({
      url: '/dyf/web/grammar/knowledge/one?id=' + id,
      method: 'GET'
    })
  },
  editHandouts(data) {
    return request({
      url: '/dyf/web/grammar/knowledge/edit',
      method: 'PUT',
      data
    })
  },
  createGrammarTree(data) {
    return request({
      url: '/dyf/web/grammar/add?phase=' + data,
      method: 'POST'
    })
  },
  editGrammarName(data) {
    return request({
      url: '/dyf/web/grammar/edit',
      method: 'PUT',
      data
    })
  },
  getConfig() {
    return request({
      url: '/dyf/web/grammar/list/config',
      method: 'GET'
    })
  },
  editGrammarConfig(data) {
    return request({
      url: '/dyf/web/grammar/config',
      method: 'PUT',
      data
    })
  },
  getConfigById(id) {
    return request({
      url: '/dyf/web/grammar/config/one?id=' + id,
      method: 'GET'
    })
  },
  getQuestionConfig() {
    return request({
      url: '/dyf/web/grammar/list/question/config',
      method: 'GET'
    })
  },
  deleteConfig(id) {
    return request({
      url: '/dyf/web/grammar/delete/config?id=' + id + '&isGrammar=1',
      method: 'DELETE'
    })
  },
  getPwd() {
    return request({
      url: '/dyf/web/grammar/pwd/one',
      method: 'GET'
    })
  },
  setPassWord(data) {
    return request({
      url: '/dyf/web/grammar/pwd/edit?passWord=' + data,
      method: 'POST'
    })
  },
  getStudentList(pageNum, pageSize, data) {
    return request({
      url: '/dyf/web/grammar/student/list/' + pageNum + '/' + pageSize,
      method: 'GET',
      params: data
    })
  },
  getMessageList(pageNum, pageSize, data) {
    return request({
      url: '/dyf/web/grammar/message/list/' + pageNum + '/' + pageSize,
      method: 'GET',
      params: data
    })
  },
  getMessage(id) {
    return request({
      url: '/dyf/web/grammar/message/one?id=' + id,
      method: 'GET'
    })
  },
  editMessage(data) {
    return request({
      url: '/dyf/web/grammar/set/message',
      method: 'PUT',
      data
    })
  },

  //获取打印信息
  handoutsPrintList(pageNum, pageSize, data) {
    return request({
      url: '/dyf/web/grammar/handouts/print/list/' + pageNum + '/' + pageSize,
      method: 'GET',
      params: data
    })
  },
  editEnable(data) {
    return request({
      url: '/dyf/web/grammar/handouts/print/'+data,
      method: 'PUT',
    })
  },
  getPrintClose(grammarId) {
    return request({
      url: '/dyf/web/grammar/handouts/one/print?relationId=' + grammarId,
      method: 'GET'
    })
  },
  getPhaseCredential(id) {
    return request({
      url: '/dyf/web/grammar/phase/one/print?id=' + id,
      method: 'GET'
    })
  },
  getStudyRateConfig() {
    return request({
      url: '/dyf/web/grammar/list/study/config',
      method: 'GET'
    })
  },
  updateStatus(data) {
    return request({
      url: '/dyf/web/grammar/edit/openStatus',
      method: 'PUT',
      data
    })
  }
}
