import request from '@/utils/request'

export default {
  // 分页查询
  list(data) {
    return request({
      url: '/xi/web/post/list',
      method: 'GET',
      params: data
    })
  },
  //添加推荐用户
  userRecommend(studentCode,recommendReason){
    return request({
      url: '/xi/web/post/userRecommend?studentCode='+studentCode+'&recommendReason='+recommendReason,
      method: 'POST',
    })
  },
  //删除
  delete(data){
    return request({
      url: '/xi/web/post',
      method: 'DELETE',
      data
    })
  },
  //恢复
  recovery(id){
    return request({
      url: '/xi/web/post/recovery',
      method: 'PUT',
      params:{
        "id":id
      }
    })
  },
  //审核通过
  auditAdopt(id){
    return request({
      url: '/xi/web/post/audit',
      method: 'PUT',
      params:{
        "id":id
      }
    })
  },
  // 禁言列表
  detail(id){
    return request({
      url: '/xi/web/post',
      method: 'GET',
      params:{
        studentCode:id
      }
    })
  },
  // 用户推荐列表
  userList(data){
    return request({
      url: '/xi/web/post/userList',
      method: 'GET',
      params: data
    })
  },
  // 用户昵称查找
  nickname(name){
    return request({
      url: '/xi/web/post/nickname?nickname='+name,
      method: 'GET',
    })
  },
  //移动
  move(ids,platid) {
    return request({
      url: '/xi/web/post/move?plateId='+platid,
      method: 'POST',
      data:ids
    })
  },
  // 用户取消推荐
  userUnRecommend(id){
    return request({
      url: '/xi/web/post/userUnRecommend',
      method: 'POST',
      params:{
        recommendId:id
      }
    })
  },
  // 置顶
  topping(id,top){
    return request({
      url: '/xi/web/post/isTop',
      method: 'POST',
      params:{
        postId:id,
        isTop:top
      }
    })
  },
  // 推荐
  recommend(data,isRecommend){
    return request({
      url: '/xi/web/post/recommend?isRecommend='+isRecommend,
      method: 'POST',
      data
    })
  },
  // 禁言提交
  subtaboo(studentCode,nickname,bannedDays){
    return request({
      url: '/xi/web/post/ban?studentCode='+studentCode+"&nickname="+nickname+"&bannedDays="+bannedDays,
      method: 'POST',
    })
  },
  // 解除禁言
  unban(id){
    return request({
      url: '/xi/web/post/unban',
      method: 'POST',
      params:{
        "id":id
      }
    })
  },
}
