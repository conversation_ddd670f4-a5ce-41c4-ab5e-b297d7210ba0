<!--圈数字题-->
<template>
  <div class="app-container">
    <el-form :model="form" ref="form" label-width="100px" v-loading="formLoading" :rules="rules">
      <el-form-item label="所属类型:" prop="type">
        <el-select v-model="form.type" placeholder="全部" disabled>
          <el-option v-for="(item, index) in trainType" :key="index" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="题型:" prop="questionType">
        <el-select v-model="form.questionType" placeholder="全部" disabled>
          <el-option v-for="(item, index) in trainQuestionType" :key="index" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="年级：" prop="grade" v-if="form.courseType == 1">
        <el-select v-model="form.grade" placeholder="" clearable>
          <el-option v-for="item in gradeList" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="选择难度：" prop="difficulty">
        <el-select v-model="form.difficulty" placeholder="全部" clearable @change="handleChange">
          <el-option v-for="(item, index) in difficultyList" :key="index" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="选择维度：" prop="categoryId">
        <el-select v-model="form.categoryId" placeholder="全部" clearable>
          <el-option v-for="(item, index) in categoryList" :key="index" :label="item.name" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item v-if="form.courseType!=1" label="选择类别：" prop="categoryType">
        <el-select v-model="form.categoryType" :disabled="questionlnCourseFalse&&form.categoryType==1" placeholder="全部" clearable>
          <el-option v-for="(item, index) in typeList" :key="index" :label="item.label" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="题干：" prop="title">
        <el-input placeholder="请输入" v-model="form.title" />
      </el-form-item>
      <el-form-item label="问题文案：" required prop="customInfo">
        <el-tooltip content="选择难度" placement="top">
          <el-link type="primary" :underline="false" style="font-size: 12px; vertical-align: baseline">
            <span v-if="difficultyInfo != null">长度为{{ difficultyInfo.questionMin }}-{{ difficultyInfo.questionMax }}的选择字符串</span>
            <span v-else>请选择难度</span>
          </el-link>
        </el-tooltip>
        <div style="display: flex; flex-wrap: wrap">
          <div class="question-item-label" style="border: #6d6d6d" :key="index" v-for="(item, index) in form.customInfo">
            <el-input v-model="item.label" type="textarea" :autosize="true" placeholder="输入问题文案" :readonly="difficultyInfo === null" style="width: 600px; margin-right: 5px; margin-left: 5px" />
          </div>
        </div>
      </el-form-item>
      <el-form-item label="答案：" required prop="answer">
        <el-tooltip content="答案数量" placement="top">
          <el-link type="primary" :underline="false" style="font-size: 12px; vertical-align: baseline">
            <span v-if="difficultyInfo != null">最低上传{{ difficultyInfo.answerMin }}个答案，最多上传{{ difficultyInfo.answerMax }}个答案</span>
            <span v-else>请先选择难度</span>
          </el-link>
        </el-tooltip>
        <el-form-item :key="index" v-for="(item, index) in form.answer">
          <div class="question-item-label" v-if="difficultyInfo !== null && difficultyInfo?.answerMax !== 0">
            <el-input @input.native="(value) => validateFormAnswer(value, undefined, index)" v-model="item.label" placeholder="答案" style="width: 100px; margin-right: 5px; margin-left: 5px" maxlength="2" />
            <el-button style="margin-left: 5px" type="danger" size="mini" icon="el-icon-delete" @click="itemRemove(undefined, index)"></el-button>
          </div>
        </el-form-item>
        <el-button v-if="difficultyInfo !== null && difficultyInfo?.answerMax !== 0" style="margin-left: 5px" type="success" size="mini" icon="el-icon-plus" @click="itemAdd(undefined)"></el-button>
      </el-form-item>
      <el-form-item label="题数：" required>
        <el-input-number v-model="form.score" :precision="0" :step="1" :min="0" :max="100" @input.native="(value) => handleInput(value)"></el-input-number>
      </el-form-item>
      <!-- 循环部分 -->
      <div v-for="(question, qIndex) in form.childQuestions" :key="qIndex" :class="['question-item', { 'question-item-gray': qIndex % 2 === 0 }]">
        <el-row type="flex" justify="flex-start" v-if="showChildQuestion">
          <el-col :xs="24" :lg="16">
            <el-row>
              <el-col :span="20">
                <el-form-item label="题干：" :prop="'childQuestions.' + qIndex + '.title'" :rules="rules.title">
                  <el-input placeholder="请输入" v-model="question.title" />
                </el-form-item>
                <el-form-item label="问题文案：" required :prop="'childQuestions.' + qIndex + '.customInfo'" :rules="rules.customInfo">
                  <el-tooltip content="选择难度" placement="top">
                    <el-link type="primary" :underline="false" style="font-size: 12px; vertical-align: baseline">
                      <span v-if="difficultyInfo != null">长度为{{ difficultyInfo.questionMin }}-{{ difficultyInfo.questionMax }}的选择字符串</span>
                      <span v-else>请选择难度</span>
                    </el-link>
                  </el-tooltip>
                  <div style="display: flex; flex-wrap: wrap">
                    <div class="question-item-label" style="border: #6d6d6d" :key="index" v-for="(item, index) in question.customInfo">
                      <el-input v-model="item.label" type="textarea" placeholder="输入问题文案" :readonly="difficultyInfo === null" style="width: 600px; margin-right: 5px; margin-left: 5px" />
                    </div>
                  </div>
                </el-form-item>
                <el-form-item label="答案：" required :prop="'childQuestions.' + qIndex + '.answer'" :rules="rules.answer">
                  <el-tooltip content="答案数量" placement="top">
                    <el-link type="primary" :underline="false" style="font-size: 12px; vertical-align: baseline">
                      <span v-if="difficultyInfo != null">最低上传{{ difficultyInfo.answerMin }}个答案，最多上传{{ difficultyInfo.answerMax }}个答案</span>
                      <span v-else>请先选择难度</span>
                    </el-link>
                  </el-tooltip>
                  <el-form-item :key="index" v-for="(item, index) in question.answer">
                    <div class="question-item-label" v-if="difficultyInfo !== null && difficultyInfo?.answerMax !== 0">
                      <el-input @input.native="(value) => validateFormAnswer(value, qIndex, index)" v-model="item.label" placeholder="答案" style="width: 100px; margin-right: 5px; margin-left: 5px" maxlength="2" />
                      <el-button style="margin-left: 5px" type="danger" size="mini" icon="el-icon-delete" @click="itemRemove(qIndex, index)"></el-button>
                    </div>
                  </el-form-item>
                  <el-button v-if="difficultyInfo !== null && difficultyInfo?.answerMax !== 0" style="margin-left: 5px" type="success" size="mini" icon="el-icon-plus" @click="itemAdd(qIndex)"></el-button>
                </el-form-item>
                <el-form-item label="题数：" required>
                  <el-input-number v-model="question.score" :precision="0" :step="1" :min="0" :max="100" @input.native="(value) => handleInputChild(value, qIndex)"></el-input-number>
                </el-form-item>
              </el-col>
              <el-col :span="2" :offset="1">
                <el-button type="danger" icon="el-icon-close" @click="removeQuestion(qIndex)">删除</el-button>
              </el-col>
            </el-row>
          </el-col>
        </el-row>
      </div>
      <!-- 循环部分 -->
      <el-form-item label="徽章：" prop="badge">
        <el-input-number v-model="form.badge" :precision="0" :step="1" :min="0" :max="100" @input.native="(value) => validateForm(value)"></el-input-number>
      </el-form-item>
      <el-form-item label="解析：">
        <el-input v-model="form.analysis">
          <i @click="inputClick(form, 'analysis')" slot="suffix" class="el-icon-edit-outline" style="line-height: 36px; font-size: 20px; cursor: pointer"></i>
        </el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="submitForm">提交</el-button>
        <el-button v-show="form.id === null" @click="resetForm">重置</el-button>
        <el-button type="success" v-if="!editId" @click="addQuestion" :disabled="!canAddQuestion || childQuestionsExceedLimit">添加小题</el-button>
      </el-form-item>
      <el-alert v-if="childQuestionsExceedLimit" title="题目数量不能超过10题" type="warning" show-icon></el-alert>
    </el-form>
    <el-dialog :visible.sync="richEditor.dialogVisible" append-to-body :close-on-click-modal="false" style="width: 100%; height: 100%" :show-close="false" center>
      <Ueditor @ready="editorReady" />
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="editorConfirm">确 定</el-button>
        <el-button @click="richEditor.dialogVisible = false">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import questionApi from '@/api/paper/train/question';
import difficultyApi from '@/api/paper/train/difficulty';
import categoryApi from '@/api/paper/train/category';
import { mapGetters, mapState } from 'vuex';
import MultipleUpload from '@/components/Upload/MultipleUpload';
import Ueditor from '@/components/Ueditor';

export default {
  components: {
    MultipleUpload,
    Ueditor
  },
  data() {
    return {
      editId: '',
      richEditor: {
        dialogVisible: false,
        object: null,
        parameterName: '',
        instance: null
      },
      difficultyInfo: null,
      categoryList: [],
      fileList: [],
      typeList: [
        { label: '正式题', id: 1 },
        { label: '附加题', id: 2 }
      ],
      importFrom: {
        file: null
      },
      randomLength: undefined,
      form: {
        id: null,
        title: '',
        categoryId: '',
        categoryType: '',
        difficulty: '',
        difficultyId: '',
        type: 'VISUAL_',
        questionType: 'VISUAL_FIND_WORD',
        isRandom: false,
        analysis: '',
        badge: 3,
        courseType: 0,
        grade: '',
        customInfo: [{ label: '', value: '' }],
        title: '',
        score: undefined,
        answer: [{ label: '', value: '' }],
        childQuestions: []
      },
      showChildQuestion: false,
      formLoading: false,
      rules: {
        type: [{ required: true, message: '请选择', trigger: 'blur' }],
        questionType: [{ required: true, message: '请选择', trigger: 'blur' }],
        grade: [{ required: true, message: '请选择', trigger: 'blur' }],
        title: [{ required: true, message: '请输入', trigger: 'change' }],
        difficulty: [{ required: true, message: '请选择', trigger: 'blur' }],
        categoryId: [{ required: true, message: '请选择', trigger: 'blur' }],
        categoryType: [{ required: true, message: '请选择', trigger: 'blur' }],
        customInfo: [
          { required: true, message: '请输入问题文案', trigger: 'change' },
          {
            validator: (rule, value, callback) => {
              if (!value || !Array.isArray(value) || value.length === 0) {
                return callback(new Error('请输入问题文案'));
              }
              for (let item of value) {
                if (!item.label) {
                  return callback(new Error('请输入问题文案'));
                }
              }
              callback();
            },
            trigger: 'change'
          }
        ],
        answer: [
          { required: true, message: '请输入答案', trigger: 'change' },
          {
            validator: (rule, value, callback) => {
              if (!value || !Array.isArray(value) || value.length === 0) {
                return callback(new Error('请输入答案'));
              }
              for (let item of value) {
                if (!item.label) {
                  return callback(new Error('请输入答案'));
                }
              }
              callback();
            },
            trigger: 'blur'
          }
        ],
        badge: [{ required: true, message: '请输入徽章', trigger: 'blur' }]
      },
      gradeList: [
        { label: '1-3年级', value: 1 },
        { label: '4-6年级', value: 2 },
        { label: '初高中', value: 3 }
      ],
      questionlnCourseFalse: false,
    };
  },
  created() {
    this.getCategoryList();
    this.editId = this.$route.query.id;
    this.form.courseType = this.$route.query.courseType ? 1 : 0;
    if (this.editId && parseInt(this.editId) !== 0) {
      this.formLoading = true;
      questionApi.detail(this.editId).then((re) => {
        this.form = re.data;
        this.form.grade = this.form.grade == 0 ? '' : this.form.grade;
        this.handleChange();
        this.formLoading = false;
      });
      questionApi.checkQuestionlnCourse(id).then((res) => {
        this.questionlnCourseFalse = res.data
      });
    }
  },
  methods: {
    validateForm(value) {
      let inputValue = value.data?.toString().replace(/^(0+)|[^\d]+/g, '');
      this.form.badge = inputValue;
      this.$refs.form.validateField('badge'); // 手动触发校验
    },
    validateFormAnswer(value, qIndex, index) {
      if (qIndex === undefined) {
        this.form.answer[index].label.push(value.data);
      } else {
        this.form.childQuestions[qIndex].answer[index].label.push(value.data);
      }
      this.$refs.form.validateField('answer'); // 手动触发校验
    },
    handleInput(value) {
      let inputValue = value.data?.toString().replace(/^(0+)|[^\d]+/g, '');
      this.form.score.push(inputValue);
      if (value.data === undefined || value.data === null || value.data === '') {
        return this.$message.error('请输入数字');
      }
    },
    handleInputChild(value, qIndex) {
      let inputValue = value.data?.toString().replace(/^(0+)|[^\d]+/g, '');
      this.form.childQuestions[qIndex].score.push(inputValue);
      if (value.data === undefined || value.data === null || value.data === '') {
        return this.$message.error('请输入数字');
      }
    },
    editorReady(instance) {
      this.richEditor.instance = instance;
      let currentContent = this.richEditor.object[this.richEditor.parameterName];
      this.richEditor.instance.setContent(currentContent);
      // 光标定位到Ueditor
      this.richEditor.instance.focus(true);
    },
    inputClick(object, parameterName) {
      this.richEditor.object = object;
      this.richEditor.parameterName = parameterName;
      this.richEditor.dialogVisible = true;
    },
    editorConfirm() {
      let content = this.richEditor.instance.getContent();
      this.richEditor.object[this.richEditor.parameterName] = content;
      this.richEditor.dialogVisible = false;
    },

    getCategoryList() {
      categoryApi.list().then((res) => {
        this.categoryList = res.data.data;
      });
    },
    handleChange() {
      if (!this.form.difficulty) {
        this.difficultyInfo = null;
        return;
      }
      let query = {};
      query.type = this.form.type;
      query.questionType = this.form.questionType;
      query.difficulty = this.form.difficulty;
      difficultyApi
        .isSetting(query)
        .then((res) => {
          this.difficultyInfo = res.data;
        })
        .catch((e) => {
          this.difficultyInfo = null;
          this.fileList = [];
        });
    },
    itemAdd(qIndex) {
      if (this.difficultyInfo === null) {
        this.$message.error('请选择难度！');
        return false;
      }
      if (qIndex === undefined) {
        if (this.form.answer.length >= this.difficultyInfo.answerMax) {
          this.$message.error(`最多上传${this.difficultyInfo.answerMax}个答案！`);
          return false;
        }
        this.form.answer.push({ label: '', value: '' });
      } else {
        if (this.form.childQuestions[qIndex].answer.length >= this.difficultyInfo.answerMax) {
          this.$message.error(`最多上传${this.difficultyInfo.answerMax}个答案！`);
          return false;
        }
        this.form.childQuestions[qIndex].answer.push({ label: '', value: '' });
      }
    },
    itemRemove(qIndex, index) {
      if (qIndex === undefined) {
        this.form.answer.splice(index, 1);
      } else {
        this.form.childQuestions[qIndex].answer.splice(index, 1);
      }
      this.$refs.form.validateField('answer');
    },
    submitForm() {
      if (this.difficultyInfo === null) {
        this.$message.error('请选择难度！');
        return false;
      }
      // 验证题目长度
      if (this.form.customInfo[0].label.length < this.difficultyInfo.questionMin || this.form.customInfo[0].label.length > this.difficultyInfo.questionMax) {
        this.$message.error(`长度为${this.difficultyInfo.questionMin}-${this.difficultyInfo.questionMax}的数字字符串`);
        return false;
      }
      if (this.form.score == undefined || this.form.score == null || this.form.score == '') {
        this.$message.error('请输入题数');
        return false;
      }
      if (!this.editId) {
        this.form.childQuestions.forEach((question, index) => {
          if (question.customInfo[0].length < this.difficultyInfo.questionMin || question.customInfo[0].length > this.difficultyInfo.questionMax) {
            this.$message.error(`长度为${this.difficultyInfo.questionMin}-${this.difficultyInfo.questionMax}的数字字符串`);
            return false;
          }
          if (question.score == undefined || question.score == null || question.score == '') {
            this.$message.error(`子题目 ${index + 1}：请输入题数`);
            return false;
          }
        });
      }
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.formLoading = true;
          questionApi
            .saveOrUpdate(this.form)
            .then((re) => {
              if (re.success) {
                this.$message.success(re.message);
                this.formLoading = false;
                this.$router.push('/train/trainAfterCourse');
              } else {
                this.$message.error(re.message);
                this.formLoading = false;
              }
            })
            .catch((e) => {
              this.formLoading = false;
            });
        } else {
          return false;
        }
      });
    },
    resetForm() {
      this.showChildQuestion = false;
      let lastId = this.form.id;
      let lastCourseType = this.form.courseType;
      this.$refs['form'].resetFields();
      this.form = {
        id: null,
        categoryId: '',
        difficulty: '',
        difficultyId: '',
        type: 'VISUAL_',
        questionType: 'VISUAL_FIND_WORD',
        isRandom: false,
        analysis: '',
        grade: '',
        childQuestions: [
          {
            title: '',
            score: undefined,
            customInfo: [{ label: '', value: '' }],
            answer: [{ label: '', value: '' }]
          }
        ]
      };
      this.form.id = lastId;
      this.form.courseType = lastCourseType;
    },
    addQuestion() {
      if (this.childQuestionsExceedLimit) return;
      const lastQuestion = this.form.childQuestions.length ? this.form.childQuestions[this.form.childQuestions.length - 1] : this.form;
      if (!lastQuestion.title || !lastQuestion.customInfo.length || !lastQuestion.answer.length || !lastQuestion.score) {
        this.$message.error('请填写完整当前题目的内容！');
        return;
      }
      this.showChildQuestion = true;
      this.form.childQuestions.push({
        title: '',
        customInfo: [{ label: '', value: '' }],
        answer: [{ label: '', value: '' }],
        score: undefined
      });
    },
    removeQuestion(index) {
      this.$confirm('请确定是否需要删除已添加的题目，删除后不可恢复', '删除题目', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.form.childQuestions.splice(index, 1);
      });
    }
  },
  computed: {
    ...mapGetters('enumItem', ['enumFormat']),
    ...mapState('enumItem', {
      trainType: (state) => state.train.trainType,
      difficultyList: (state) => state.train.difficultyList,
      trainQuestionType: (state) => state.train.trainQuestionType
    }),
    canAddQuestion() {
      if (this.editId) return false;
      const lastQuestion = this.form.childQuestions.length > 0 ? this.form.childQuestions[this.form.childQuestions.length - 1] : this.form;
      if (!lastQuestion) return false;
      const { title, customInfo, answer, score } = lastQuestion;
      return title && customInfo.length > 0 && answer.length > 0 && score;
    },
    childQuestionsExceedLimit() {
      if (this.editId) return false;
      return this.form.childQuestions.length >= 9;
    }
  }
};
</script>

<style lang="less" scoped>
.question-item-label {
  display: flex;
  margin-bottom: 12px;
}

.img-lg {
  width: 60px;
  height: 60px;
}
.question-item {
  padding: 10px;
  margin-bottom: 10px;
  border-radius: 4px;
}

.question-item-gray {
  background-color: #f9f9f9;
}
</style>
