import Vue from 'vue'
import Vuex from 'vuex'
// import app from './modules/app'
// import errorLog from './modules/errorLog'
// import permission from './modules/permission'
// import tagsView from './modules/tagsView'
// import user from './modules/user'
import getters from './getters'
import createPersistedState from 'vuex-persistedstate'
Vue.use(Vuex)
const modulesFiles = require.context('./modules', true, /\.js$/)
const modules = modulesFiles.keys().reduce((modules, modulePath) => {
  // set './app.js' => 'app'
  const moduleName = modulePath.replace(/^\.\/(.*)\.\w+$/, '$1')
  const value = modulesFiles(modulePath)
  modules[moduleName] = value.default
  return modules
}, {})

const store = new Vuex.Store({
  modules,
  getters,
  // plugins: [
  //   // 使用 vuex-persistedstate 插件
  //   createPersistedState({
  //     key: 'vuex', // 存储到 localStorage 的 key（默认是 'vuex'）
  //     paths: [      // 指定需要持久化的模块或 state
  //       'user.contractNum',     // 例如：持久化 user 模块的所有 state
  //       'user.systemNumber',     // 例如：持久化 user 模块的所有 state
  //       // 'app.sidebar', // 也可以指定子模块的某个 state
  //     ],
  //     // storage: window.sessionStorage, // 可选：使用 sessionStorage 代替 localStorage
  //   })
  // ]
})

export default store
