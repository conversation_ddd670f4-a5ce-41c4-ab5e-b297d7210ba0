/**
 * 课程子类相关接口
 */
import request from '@/utils/request'

export default {
  // 课程子类分页查询
  courseList(pageNum, pageSize, data) {
    return request({
      url: '/znyy/course/big/class/list/' + pageNum + '/' + pageSize,
      method: 'GET',
      params: data
    })
  },
  // 课程子类新增
  addCourse(data) {
    return request({
      url: '/znyy/course/big/class',
      method: 'POST',
      data
    })
  },
  // 课程子类编辑
  updateCourse(data) {
    return request({
      url: '/znyy/course/big/class',
      method: 'PUT',
      data
    })
  },
  // 课程子类查询
  queryActive(id) {
    return request({
      url: '/znyy/course/big/class/checkdetail/' + id,
      method: 'GET'
    })
  },
  // 课程子类开通与暂停
  updateStatus(id,status) {
    return request({
      url: '/znyy/course/big/class/openAndSuspension?id=' + id + '&isEnable=' + status,
      method: 'PUT'
    })
  },
  // 获取分类列表
  categoryType() {
    return request({
      url: '/znyy/course/category/check/list',
      method: 'GET'
    })
  },

}
