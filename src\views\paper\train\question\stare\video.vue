<template>
  <div class="app-container">
    <el-form ref="form" :model="form" :rules="rules" label-width="120px" v-loading="formLoading" style="width: 70%">
      <el-form-item label="所属类型:" prop="type">
        <el-select v-model="form.type" placeholder="全部" disabled>
          <el-option v-for="(item, index) in trainType" :key="index" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="题型:" prop="questionType">
        <el-select v-model="form.questionType" placeholder="全部" disabled>
          <el-option v-for="(item, index) in trainQuestionType" :key="index" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="年级：" prop="grade" v-if="form.courseType == 1">
        <el-select v-model="form.grade" placeholder="" clearable>
          <el-option v-for="item in gradeList" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="选择维度：" prop="categoryId">
        <el-select v-model="form.categoryId" placeholder="全部" clearable>
          <el-option v-for="(item, index) in categoryList" :key="index" :label="item.name" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="名称：" prop="title">
        <el-input v-model="form.title" />
      </el-form-item>
      <el-form-item label="视频：" required>
        <el-form-item>
          <input ref="inputer" type="file" class="upload" @change="doUpload" />
          <div v-if="this.form.expandInfo" style="width: 50%; border-radius: 4px; background-color: rgb(245, 247, 250); padding-left: 10px; padding-right: 10px">
            {{ this.form.expandInfo }}
          </div>
        </el-form-item>
      </el-form-item>
      <el-form-item label="内容：" required>
        <el-input type="textarea" :rows="10" :autosize="{ minRows: 6, maxRows: 10 }" v-model="form.customInfo[0].label" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="startVideo">提交</el-button>
        <el-button v-show="form.id === null" @click="resetForm">重置</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
  import questionApi from '@/api/paper/train/question';
  import { pageParamNames } from '@/utils/constants';
  import { mapGetters, mapState } from 'vuex';
  import MyUpload from '@/components/Upload/MyUpload';
  import categoryApi from '@/api/paper/train/category';
  import UploadFile from '@/components/Upload/UploadFile.vue';
  import PlvVideoUpload from '@polyv/vod-upload-js-sdk';
  export default {
    components: { UploadFile, MyUpload },
    data() {
      return {
        videoUpload: null, // 视频上传实例
        userid: '1723c88563', //从点播后台查看获取
        secretkey: 'Jkk4ml1Of8', //从点播后台查看获取
        writeToken: '94064a19-4d28-4ce9-aa05-96d476016215', //从点播后台查看获取

        categoryList: [],
        videoFileList: [],
        form: {
          id: null,
          title: '',
          type: 'STARE_',
          questionType: 'STARE_VIDEO',
          customInfo: [
            {
              label: '',
              value: ''
            }
          ],
          answer: [],
          formLoading: false,
          expandInfo: '',
          isStep: false,
          courseType: 0,
          grade: ''
        },
        // 表单校验
        rules: {
          categoryId: [{ required: true, message: '请选择', trigger: 'blur' }],
          type: [{ required: true, message: '请选择', trigger: 'blur' }],
          grade: [{ required: true, message: '请选择', trigger: 'blur' }],
          title: [{ required: true, message: '请输入', trigger: 'blur' }]
        },
        gradeList: [
          { label: '1-3年级', value: 1 },
          { label: '4-6年级', value: 2 },
          { label: '初高中', value: 3 }
        ]
      };
    },
    mounted() {
      this.autoUpdateUserData(null, this.videoUpload);
    },
    created() {
      this.getCategoryList();
      let id = this.$route.query.id;
      this.form.courseType = this.$route.query.courseType ? 1 : 0;
      if (id && parseInt(id) !== 0) {
        questionApi.detail(id).then((re) => {
          this.form = re.data;
          this.form.grade = this.form.grade == 0 ? '' : this.form.grade;
        });
      }
      this.videoUpload = new PlvVideoUpload({
        region: 'line1', // (可选)上传线路, 默认line1
        events: {
          Error: (err) => {
            // 错误事件回调
            console.log(err);
            let errMag = `（错误代码：${err.code}）${err.message}`;
            this.$alert(errMag, '标题名称', {
              confirmButtonText: '确定',
              type: 'error'
            });
          },
          UploadComplete: () => {
            // 全部上传任务完成回调
            console.info('上传结束：', this.videoUpload);
            // console.log(this.tableData);
            this.$message({
              message: '全部上传任务完成',
              type: 'success'
            });
          }
        }
      });
    },
    methods: {
      getCategoryList() {
        categoryApi.list().then((res) => {
          this.categoryList = res.data.data;
        });
      },

      startVideo() {
        let inputDOM = this.$refs.inputer; // 通过DOM取文件数据
        let data = Object.values(inputDOM.files);
        if (data.length <= 0) {
          this.$message.error('请选择视频！');
          return false;
        }
        if (this.videoUpload) {
          this.videoUpload.startAll();
        }
      },
      submitForm() {
        if (this.form.customInfo[0].value === '') {
          this.$message.error('请上传视频！');
          return false;
        }
        this.$refs['form'].validate((valid) => {
          if (valid) {
            this.formLoading = true;
            questionApi.saveOrUpdate(this.form).then((response) => {
              this.formLoading = false;
              this.$message.success('提交成功！');
              if (this.form.courseType == 1) {
                this.$router.push('/train/trainAfterCourse');
              } else {
                this.$router.push('/train/stare');
              }
            });
          }
        });
      },

      resetForm() {
        let lastId = this.form.id;
        let lastCourseType = this.form.courseType;
        if (this.$refs['form']) {
          this.$refs['form'].resetFields();
        }
        this.form = {
          id: null,
          title: '',
          type: 'STARE_',
          questionType: 'STARE_VIDEO',
          customInfo: [
            {
              label: '',
              value: ''
            }
          ],
          expandInfo: '',
          isStep: false,
          loading: null,
          grade: ''
        };
        this.form.id = lastId;
        this.form.courseType = lastCourseType;
      },
      autoUpdateUserData(timer, videoUpload) {
        // 启动获取用户信息
        this.getSign();
        if (timer) {
          clearTimeout(timer);
          timer = null;
        }
        timer = setTimeout(() => {
          this.autoUpdateUserData(timer, videoUpload);
        }, 3 * 50 * 1000);
      },
      async getSign() {
        const { data } = await questionApi.getSign();
        let userData = {
          userid: '1723c88563', // Polyv云点播账号的ID
          ptime: data.ptime, // 时间戳
          sign: data.sign, // 是根据将secretkey和ts按照顺序拼凑起来的字符串进行MD5计算得到的值
          hash: data.hash //
        };
        this.videoUpload.updateUserData(userData);
      },
      doUpload() {
        // 选择文件
        let inputDOM = this.$refs.inputer; // 通过DOM取文件数据
        let data = Object.values(inputDOM.files);
        if (data.length > 0) {
          data.forEach((file, index, arr) => {
            let fileSetting = {
              // 文件上传相关信息设置
              title: file.name, // 标题
              desc: 'jssdk插件上传', // 描述
              cataid: process.env.VUE_APP_BASE_CATAID, // 上传分类目录ID
              tag: '', // 标签
              luping: 0, // 是否开启视频课件优化处理，对于上传录屏类视频清晰度有所优化：0为不开启，1为开启
              keepsource: 0, // 是否源文件播放（不对视频进行编码）：0为编码，1为不编码
              state: '' //用户自定义信息，如果提交了该字段，会在服务端上传完成回调时透传返回。
            };
            let uploadManager = this.videoUpload.addFile(
              file, // file 为待上传的文件对象
              {
                FileStarted: this.onFileStarted, // 文件开始上传回调
                FileProgress: this.onFileProgress, // 文件上传中回调
                FileSucceed: this.onFileSucceed, // 文件上传成功回调
                FileFailed: this.onFileFailed, // 文件上传失败回调
                FileStopped: this.onFileStopped // 文件上传停止回调
              },
              fileSetting
            );
            console.log(uploadManager);
          });
        }
      },
      onFileStarted(data) {
        console.log('文件上传开始: ', data);
        this.loading = this.$loading({
          lock: true,
          text: '视频上传中...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
      },
      onFileProgress(data) {
        console.log('文件上传中: ', data);
      },
      onFileSucceed(data) {
        if (this.loading) {
          this.loading.close();
        }
        console.log('文件上传成功: ', data);
        this.form.customInfo[0].value = data.fileData.vid;
        this.form.expandInfo = data.fileData.filename;
        this.submitForm();
      },
      onFileFailed(data) {
        if (this.loading) {
          this.loading.close();
        }
        console.log('文件上传失败: ', data);
      },
      onFileStopped(data) {
        if (this.loading) {
          this.loading.close();
        }
        console.log('文件上传停止: ', data);
      }
    },
    computed: {
      ...mapGetters('enumItem', ['enumFormat']),
      ...mapState('enumItem', {
        trainType: (state) => state.train.trainType,
        trainQuestionType: (state) => state.train.trainQuestionType
      })
    }
  };
</script>
