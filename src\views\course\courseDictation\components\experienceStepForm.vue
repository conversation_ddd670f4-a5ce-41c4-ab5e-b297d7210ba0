<template>
  <!-- 体验课拼读拼写 -->
  <div>
    <el-dialog :title="`${spellDialogTitile}拼读拼写`" :visible.sync="stepDialogVisible" width="70%" v-if="spellDialogVisible" :close-on-click-modal="false" @close="dialogClose">
      <el-steps :active="activeCode" finish-status="success" class="activeStep">
        <el-step title="拼读词" v-if="id ? show === true : true" :active="0"></el-step>
        <el-step title="拼写词" v-if="id ? show === true : true" :active="1"></el-step>
        <el-step title="学后读检测词" v-if="id ? show === true : true" :active="2"></el-step>
        <el-step title="学后写检测词" v-if="id ? show === true : true" :active="3"></el-step>
      </el-steps>
      <div>
        <!-- 拼读词 -->
        <el-form :model="stepReadForm" v-if="activeCode === 0 && !isShowStress" ref="stepReadForm" :rules="stepReadRules" label-width="160px">
          <el-form-item label="单词" prop="word" required>
            <el-row>
              <el-col :span="12">
                <el-input
                  v-model="stepReadForm.word"
                  type="textarea"
                  @change="inputChange(stepReadForm.word, '0')"
                  placeholder="请输入单词"
                  :rows="3"
                  :spellcheck="false"
                ></el-input>
              </el-col>
            </el-row>
          </el-form-item>
          <el-form-item label="划音节" prop="syllable" required>
            <el-row>
              <el-col :span="12">
                <el-input
                  v-model="stepReadForm.syllable"
                  type="textarea"
                  @change="inputChange(stepReadForm.syllable, '1')"
                  :rows="3"
                  placeholder="请输入音节"
                  :spellcheck="false"
                ></el-input>
              </el-col>
            </el-row>
          </el-form-item>
          <el-form-item label="单词拆分" prop="splitInfo" required>
            <el-row>
              <el-col :span="12">
                <el-input
                  v-model="stepReadForm.splitInfo"
                  type="textarea"
                  @change="inputChange(stepReadForm.splitInfo, '2')"
                  :rows="3"
                  :spellcheck="false"
                  placeholder="请输入单词拆分"
                ></el-input>
              </el-col>
            </el-row>
          </el-form-item>
          <el-form-item label="" v-if="spellDialogTitile == '新增'">
            <div class="upload_box">
              <el-upload
                class="upload-demo"
                action="#"
                :show-file-list="true"
                ref="uploadReadWord"
                :file-list="fileListReadWord"
                :on-remove="handlSetRemoveReadWord"
                :http-request="uploadDetailHttp"
                :before-upload="beforeWordUpload"
              >
                <div class="el-upload__text">
                  <el-link icon="el-icon-upload2" :underline="false" class="upload_link">excel文件上传</el-link>
                </div>
              </el-upload>
              <el-link class="download_link" :underline="false" icon="el-icon-download" href="https://document.dxznjy.com/applet/zhimi/config/pd_word.xls">模板下载</el-link>
            </div>
          </el-form-item>
          <el-form-item>
            <el-button size="small " type="success" @click="submitWordValidte('stepReadForm', '0')">下一环节</el-button>
          </el-form-item>
        </el-form>
        <el-form :model="stressReadForm" v-if="activeCode === 0 && isShowStress" ref="stressReadForm" :rules="stressReadRules" label-width="160px">
          <div v-for="(item, topIndex) in stepForm.pdWordRuleDto" :key="topIndex">
            <el-divider content-position="left">{{ item.word }}</el-divider>
            <el-form-item label="重音节" prop="stressWord">
              <el-row>
                <el-col :span="16">
                  <el-checkbox-group v-model="stressReadForm[topIndex].stressWord" @change="readStressChange(item, '1', topIndex, $event)">
                    <el-checkbox v-for="(f, fIndex) in item.ruleWordTypeDtoList" :label="computeLabel(f.syllable, item.ruleWordTypeDtoList, fIndex)" :key="fIndex"></el-checkbox>
                  </el-checkbox-group>
                </el-col>
              </el-row>
            </el-form-item>
            <el-form-item label="次重音节" prop="stressWord" v-if="courseLevel == 2">
              <el-row>
                <el-col :span="16">
                  <el-checkbox-group v-model="stressReadForm[topIndex].secondaryStressedWords" @change="readStressChange(item, '2', topIndex, $event)">
                    <el-checkbox v-for="(f, fIndex) in item.ruleWordTypeDtoList" :label="computeLabel(f.syllable, item.ruleWordTypeDtoList, fIndex)" :key="fIndex"></el-checkbox>
                  </el-checkbox-group>
                </el-col>
              </el-row>
            </el-form-item>
          </div>
          <el-form-item>
            <el-button size="small " type="success" @click="submitValidte2('stressReadForm', '0')">下一环节</el-button>
          </el-form-item>
        </el-form>
        <!-- 拼写词 -->
        <el-form :model="stepWriteForm" v-if="activeCode === 1" ref="stepWriteForm" :rules="stepWriteRules">
          <el-form-item label="选择拼写词:" prop="word" required>
            <el-checkbox-group @change="handleCheckStepWrite" v-model="checkStepWrite">
              <el-checkbox v-for="(item, index) in readList" :label="item" :key="index">{{ item }}</el-checkbox>
            </el-checkbox-group>
            <el-input readonly type="textarea" v-model="stepWriteForm.word" aria-label="拼写词"></el-input>
          </el-form-item>
          <el-form-item>
            <el-button size="small" type="success" @click="submitWordValidte1('stepWriteForm', '1')">下一环节</el-button>
          </el-form-item>
        </el-form>
        <!-- 拼读检测词 -->
        <el-form ref="readTestForm" :model="readTestForm" v-if="activeCode === 2" :rules="readTestRules">
          <el-form-item label="练习拼读和练习拼写词:" prop="word" required>
            <el-checkbox-group @change="handleCheckedCitiesChange" v-model="checkList">
              <el-checkbox v-for="(city, index) in cities" :label="city" :key="index">{{ city }}</el-checkbox>
            </el-checkbox-group>
            <el-input readonly type="textarea" v-model="readTestForm.word" aria-label="学后读检测词"></el-input>
          </el-form-item>
          <el-form-item label="">
            <el-button size="small" type="success" @click="submitValidte5(2, 'readTestForm', readTestWords)">下一环节</el-button>
          </el-form-item>
        </el-form>
        <!-- 拼写检测词 -->
        <el-form ref="writeTestForm" :model="writeTestForm" v-if="activeCode === 3" :rules="writeTestRules">
          <el-form-item label="练习拼读和练习拼写词:" prop="word" required>
            <el-checkbox-group @change="handleCheckedCitiesWrite" v-model="checkWriteList">
              <el-checkbox v-for="(city, index) in cities" :label="city" :key="index">{{ city }}</el-checkbox>
            </el-checkbox-group>
            <el-input readonly type="textarea" v-model="writeTestForm.word" aria-label="学后写检测词"></el-input>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="dialogClose">关闭</el-button>
        <!-- 新增↓ -->
        <el-button size="mini" v-if="!id && activeCode === 3" type="primary" @click="debouncedSubmit">确定</el-button>
        <!-- 编辑↓ -->
        <el-button size="mini" v-if="id && activeCode === 3" type="primary" @click="updateSubmitValidte()">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
  import { debounce } from '@/utils/index.js';
  import courseDictationListApi from '@/api/courseDictationList';
  import { ossPrClient } from '@/api/alibaba';
  export default {
    props: {
      spellDialogVisible: {
        type: Boolean,
        default: false
      },
      spellId: {
        type: String,
        default: ''
      },
      code: {
        type: String,
        default: ''
      },
      spellType: {
        type: Number,
        default: 0
      }
    },
    data() {
      return {
        fileListReadWord: [],
        fileListRemoveReadWord: [],
        activeCode: 0,
        stepDialogVisible: false,
        readWordUrl: '',
        readSyllableUrl: '',
        readSplitInfoUrl: '',
        //单词拼读
        stepReadForm: {
          word: '',
          syllable: '',
          splitInfo: ''
        },
        stepWriteForm: {
          word: ''
        },
        readTestForm: {
          word: ''
        },
        writeTestForm: {
          word: ''
        },
        readTestRules: {
          word: [{ required: true, message: '请选择拼读单词', trigger: 'change' }]
        },
        writeTestRules: {
          word: [{ required: true, message: '请选择拼写单词', trigger: 'change' }]
        },
        courseId: '',
        stepForm: {
          id: this.$route.query.id,
          wordType: '', // 2拼读单词 3拼写单词
          pdWordRuleDto: []
        },
        stepForm1: {
          id: this.$route.query.id,
          wordType: '', // 2拼读单词 3拼写单词
          pdWordRuleDto: []
        },
        //拼读检测词
        readFilterData: [],
        readRightDefaultChecked: [], //拼读右侧默认选中
        //拼写检测词
        writeFilterData: [],
        writeRightDefaultChecked: [], //拼写右侧默认选中
        readFilterWords: [],
        writeFilterWords: [],
        stepWriteRules: {
          word: [{ required: true, message: '请选择拼写词', trigger: 'change' }]
        },
        stepReadRules: {
          word: [
            {
              validator: (rule, value, callback) => {
                this.stepWordValidate(rule, value, callback, this.stepReadForm);
              },
              trigger: 'blur'
            }
          ],
          syllable: [
            {
              validator: (rule, value, callback) => {
                this.stepSyllableValidate(rule, value, callback, this.stepReadForm);
              },
              trigger: 'blur'
            }
          ],
          splitInfo: [
            {
              validator: (rule, value, callback) => {
                this.stepSplitInfoValidate(rule, value, callback, this.stepReadForm);
              },
              trigger: 'blur'
            }
          ]
        },
        //重音
        stressReadRules: {
          stressReadForm: [{ required: true, message: '请选择重音节', trigger: 'blur' }]
        },
        stressWriteRules: {
          stressWriteForm: [{ required: true, message: '请选择重音节', trigger: 'blur' }]
        },
        readSyllableData: [],
        wordData: [],
        stressWord: [],
        secondaryStressedWords: [],
        stressWriteForm: [], //拼写词重音节
        stressReadForm: [], //拼读词重音节
        isShowStress: false,
        showReadSelectWord: false,
        showWriteSelectWord: false,
        readTestWords: [],
        writeTestWords: [],
        spellDialogTitile: '新增',
        id: '',
        wordType: '', //编辑时获取详情拼读拼写类型 2拼读单词 3拼写单词
        spellViewForm: {},
        cities: [],
        checkList: [],
        checkWriteList: [],
        readList: [],
        writeList: [],
        readWord: [],
        writeWord: [],
        show: true,
        checkread: [],
        checkwrite: [],
        readStatus: 0,
        writeStatus: 0,
        concatWriteWord: [],
        checkStepWrite: [], //选择拼写词
        courseLevel: ''
      };
    },
    watch: {
      spellDialogVisible(val) {
        this.stepDialogVisible = val;
      },
      spellId(val) {
        if (val) {
          this.id = val;
          this.getView(val);
        } else {
          console.log('新增----------------------');
        }
      }
    },
    created() {
      this.courseLevel = this.$route.query.courseLevel;
      this.debouncedSubmit = debounce(this.updateSubmitValidte, 1000);
      this.courseId = this.$route.query.id;
      this.$bus.$on('clearData', () => {
        this.stressReadForm = [];
        this.stressWriteForm = [];
        this.readTestForm.word = '';
        this.writeTestForm.word = '';
        this.activeCode = 0;
      });
      this.debouncedSubmit = debounce(this.testReadSubmitValidte, 1000);
    },
    methods: {
      computeLabel(syllable, ruleWordTypeDtoList, fIndex) {
        // 处理重音节重复字母
        let label = '';
        let repeatCount = 0;
        ruleWordTypeDtoList.forEach((item, Index) => {
          if (item.syllable == syllable) {
            repeatCount++;
            if (Index == fIndex) {
              label = repeatCount > 1 ? `${syllable}[${repeatCount}]` : `${syllable}`;
            }
          }
        });
        return label;
      },
      debouncedSubmit() {},
      //禁止输入单词时输入中文和中文符号
      inputChange(val, type) {
        let key = type == '0' ? 'word' : type == '1' ? 'syllable' : 'splitInfo';
        let newValue = val.replace(/[^a-zA-Z,-]/g, '');
        newValue = newValue.replace(/,*$/, '');
        newValue = newValue.replace(/,+/g, ',');
        // 使用replace方法替换掉所有匹配到的中文和中文标点符号为空字符串
        if (this.activeCode === 0) {
          this.stepReadForm[key] = newValue;
        } else {
          this.stepWriteForm[key] = newValue;
        }
      },
      getView(id) {
        courseDictationListApi.getReadAndWriteById(id).then((res) => {
          if (res.code === 20000) {
            // 2拼读单词 3拼写单词
            this.spellDialogTitile = '编辑';
            this.spellViewForm = res.data.data;
            this.wordType = res.data.data.wordType;
            //单词回显
            const wordData = this.wordType === 2 ? this.stepReadForm : this.stepWriteForm;
            // 重音回显
            this.activeCode = this.wordType === 2 ? 0 : 1;
            wordData.word = this.spellViewForm.pdWordRuleDto[0].word;
            this.readTestWords.push({
              id: this.id,
              word: res.data.data.pdWordRuleDto[0].word,
              isSelect: 0,
              isReadSelect: 0,
              isWriteSelect: 0
            });
            // const syllableData = [...new Set(this.spellViewForm.pdWordRuleDto[0].ruleWordTypeDtoList.map(res=> { return res.syllable}))]
            let syllableData = [];

            this.spellViewForm.pdWordRuleDto[0].ruleWordTypeDtoList.forEach((item) => {
              syllableData.push(item.syllable.replace(/\[.*\]$/, ''));
            });
            wordData.syllable = syllableData.join();
            wordData.splitInfo = this.spellViewForm.pdWordRuleDto[0].splitInfo;
            this.stressReadForm = [];
            this.stressWriteForm = [];
          }
        });
      },
      //下一环节存储拼读拼写
      async submitValidte2(type, num) {
        this.activeCode = 1;
        if (this.stepReadForm.word.indexOf(',') !== -1) {
          //多个单词
          //分割单词
          this.readWord = this.stepReadForm.word.split(',');
          //分割拼读
          this.readWord.forEach((item) => {
            this.readList.push(item);
          });
        } else {
          this.readList.push(this.stepReadForm.word);
        }
        if (this.id) {
          this.cities = [];
          this.readTestForm.word = '';
          this.checkList = [];
          this.cities.push(this.stepReadForm.word);
        }
        this.readList = Array.from(new Set(this.readList));
      },
      submitValidte4(type, num) {
        if (type === 'stressWriteForm') {
          this.activeCode = 2;
        } else if (type === 'stressReadForm') {
          this.activeCode = this.id ? 2 : 1;
          this.isShowStress = false;
        } else {
          this.activeCode = 2;
        }
        if (this.stepWriteForm.word.indexOf(',') !== -1) {
          //多个单词
          //分割单词
          this.writeWord = this.stepWriteForm.word.split(',');
          //分割拼读
          this.writeWord.forEach((item) => {
            this.writeList.push(item);
          });
        } else {
          this.writeList.push(this.stepWriteForm.word);
        }
        if (this.id) {
          this.cities = [];
          this.readTestForm.word = '';
          this.checkList = [];
          this.cities.push(this.stepWriteForm.word);
        } else {
          // this.citieList.push(...this.readList, ...this.writeList)
          this.cities = Array.from(new Set(this.readList));
        }
      },
      submitValidte5(type, num, value) {
        if (this.checkList.length === 0) {
          return;
        }
        this.checkWriteList = [];
        this.writeTestForm.word = '';
        this.activeCode = 3;
      },
      //拼读拼写提交
      async updateSubmitValidte() {
        await this.updateWordDetail();
      },
      async updateWordDetail() {
        if (this.checkWriteList.length === 0) {
          return;
        }
        await this.submitStressValidte('stressReadForm', '0');
        await this.testReadSubmitValidte0(2, 'readTestForm', this.readTestWords);
      },
      //拼读检测词提交
      async testReadSubmitValidte() {
        if (this.checkWriteList.length === 0) {
          return;
        }
        await this.submitStressValidte('stressReadForm', '0');
        await this.testReadSubmitValidte0(2, 'readTestForm', this.readTestWords);
      },
      testReadSubmitValidte0(val, refName, wordsList) {
        wordsList.forEach((res) => {
          res.isReadSelect = 1;
          this.checkStepWrite.find((val) => {
            if (res.word === val) {
              res.isWriteSelect = 1;
            }
          });
        });
        const values1 = new Set(this.checkList.map((item) => item));
        const values2 = new Set(this.checkWriteList.map((item) => item));
        let arr1 = [];
        let arr2 = [];
        // 遍历 array1，根据条件设置 isselect
        this.checkList.forEach((item) => {
          console.log(item, '55555555555');
          if (values2.has(item)) {
            arr1.push({
              word: item,
              isSelect: 3
            }); // 在两个数组中都存在
          } else {
            arr1.push({
              word: item,
              isSelect: 1
            });
          }
        });
        // 遍历 array2，设置只在 array2 中存在的 isselect
        this.checkWriteList.forEach((item) => {
          if (!values1.has(item)) {
            arr2.push({
              word: item,
              isSelect: 2
            });
          }
        });
        const arr = [...arr1, ...arr2];
        wordsList.forEach((res) => {
          arr.find((val) => {
            if (res.word === val.word) {
              res.isSelect = val.isSelect;
            }
          });
        });
        courseDictationListApi.updateAllRuleWord(wordsList).then((res) => {
          if (res.code === 20000) {
            if (val === 2 && !this.id) {
              courseDictationListApi.getAllRuleWord(this.$route.query.id, '3', '').then((res) => {
                const map = new Map();
                this.writeTestWords = res.data.data.filter((v) => !map.has(v.word) && map.set(v.word, v));
                this.writeFilterData = this.writeTestWords.map((item) => {
                  return {
                    key: item.word,
                    label: item.word,
                    id: item.id,
                    isReadSelect: item.isReadSelect,
                    isSelect: item.isSelect,
                    isWriteSelect: item.isWriteSelect
                  };
                });
                this.writeFilterData.forEach((res) => {
                  if (res.isWriteSelect === 1) {
                    this.writeFilterWords.push(res.label);
                    this.writeRightDefaultChecked.push(res.label);
                  }
                });
              });
            }
            this.$message.success('操作成功');
            this.dialogClose();
            this.readTestWords = [];
          }
        });
      },

      filterChange(value, direction, movedKeys) {
        console.log(value, direction, movedKeys, 'filterChangevalue');
      },
      //拼读选择单词
      onReadSelectWord() {
        this.showReadSelectWord = true;
      },
      onWriteSelectWord() {
        this.showWriteSelectWord = true;
      },
      handleCheckStepWrite(value) {
        this.stepWriteForm.word = value.join(',');
      },
      handleCheckedCitiesChange(value) {
        this.readTestForm.word = value.join(',');
        for (let i in value) {
          this.checkread.push({
            word: value[i]
          });
        }
        this.checkread.forEach((res) => {
          res.IsReadSelect = value.find((item) => item === res.word) ? 1 : 0;
        });
      },
      handleCheckedCitiesWrite(value) {
        this.writeTestForm.word = value.join(',');
        for (let i in value) {
          this.checkwrite.push({
            word: value[i]
          });
        }
        this.checkwrite.forEach((res) => {
          res.IsWriteSelect = value.find((item) => item === res.word) ? 1 : 0;
        });
      },
      //拼读拼写校验
      stepWordValidate(rule, value, callback, data) {
        if (!value) {
          callback(new Error('请输入单词'));
        } else {
          callback();
        }
      },
      //特殊字符检测
      checkSpecialKey(str) {
        let specialKey = "[`~!#$^&*()=|{}':;'\\[\\].<>/?~！#￥……&*（）——|{}【】‘；：”“'。，、？]‘'";
        for (let i = 0; i < str.length; i++) {
          if (specialKey.indexOf(str.substr(i, 1)) != -1) {
            return false;
          }
        }
        return true;
      },
      stepSyllableValidate(rule, value, callback, data) {
        if (!value) {
          callback(new Error('请输入音节'));
        } else {
          data.syllable = data.syllable.replace(/\[\d+\]/g, '');
          const wordArr = data.word.includes(',') ? data.word.split(',') : [data.word];
          const syllableArr = data.syllable.includes('-') ? data.syllable.split('-') : [data.syllable];
          const words = this.checkSpecialKey(data.syllable);
          if (syllableArr.length !== wordArr.length || !words) {
            callback(new Error('请正确输入音节'));
          } else {
            callback();
          }
        }
      },
      stepSplitInfoValidate(rule, value, callback, data) {
        if (!value) {
          callback(new Error('请输入单词拼读'));
        } else {
          const wordArr = data.word.includes(',') ? data.word.split(',') : [data.word];
          const splitInfoArr = data.splitInfo.includes('-') ? data.splitInfo.split('-') : [data.splitInfo];
          if (splitInfoArr.length !== wordArr.length) {
            callback(new Error('请正确输入单词拼读'));
          } else {
            callback();
          }
        }
      },
      addSuffixToRepeatedSyllables(objects) {
        // 创建一个对象来跟踪每个syllable出现的次数
        const syllableCounts = {};
        return objects.map((obj, index) => {
          const syllable = obj.syllable;

          // 如果syllable已经出现过，则增加计数并添加后缀
          if (syllableCounts[syllable]) {
            syllableCounts[syllable]++;
            return {
              ...obj,
              syllable: `${syllable}[${syllableCounts[syllable]}]`
            };
          } else {
            // 如果是第一次出现，则记录该syllable并设置计数为1
            syllableCounts[syllable] = 1;
            return obj;
          }
        });
      },
      //拼读选择重音
      readStressChange(value, type, topIndex, event) {
        let ruleWordTypeDtoList = this.addSuffixToRepeatedSyllables(value.ruleWordTypeDtoList);
        ruleWordTypeDtoList.map((res) => {
          if (res.type == type) {
            res.type = '0';
          }
          if (event.includes(res.syllable)) {
            res.type = type;
            res.status = '1';
          }
        });
        if (type == 1) {
          if (this.stressReadForm[topIndex].secondaryStressedWords.includes(event[event.length - 1])) {
            this.stressReadForm[topIndex].secondaryStressedWords = this.stressReadForm[topIndex].secondaryStressedWords.filter((data) => data != event[event.length - 1]);
          }
        } else {
          if (this.stressReadForm[topIndex].stressWord.includes(event[event.length - 1])) {
            this.stressReadForm[topIndex].stressWord = this.stressReadForm[topIndex].stressWord.filter((data) => data != event[event.length - 1]);
          }
        }
        value.ruleWordTypeDtoList = ruleWordTypeDtoList.map((item) => {
          if (item.syllable.includes('[')) {
            item.too = '[' + item.syllable.split('[')[1];
          }
          // 使用正则表达式匹配括号及其内的内容，并替换为空字符串
          let syllable = item.syllable.replace(/\[.*\]$/, '');
          // 返回处理后的对象
          return { ...item, syllable }; // 使用扩展运算符来复制其他属性
        });
      },

      stepReadValidate(rule, value, callback) {
        if (!value) {
          return callback(new Error('请输入单词'));
        }
      },
      submitStressValidte(refName, type) {
        const saveWordSubmitApi = this.id ? courseDictationListApi.updateReadAndWrite : courseDictationListApi.saveReadAndWrite;
        this.stepForm = this.id ? { ...this.stepForm, listId: this.id } : this.stepForm;
        this.stepForm.pdWordRuleDto.map((res) => {
          res.ruleWordTypeDtoList.forEach((item) => {
            console.log(item);
            if (item.too) {
              console.log(item.too);
              item.syllable = item.syllable + item.too;
            }
          });
          return res; // 返回修改后的 res 对象
        });
        if (this.code) {
          this.$set(this.stepForm.pdWordRuleDto[0], 'wordCode', this.code);
        }
        return saveWordSubmitApi(this.stepForm)
          .then((res) => {
            if (res.code === 20000) {
              this.stepForm = {
                id: this.$route.query.id,
                wordType: '',
                pdWordRuleDto: []
              };
              let concatWriteWord = [];
              if (refName === 'stressWriteForm') {
                concatWriteWord = this.stepWriteForm.word.split(',');
              }
              return courseDictationListApi.getAllRuleWord(this.$route.query.id, '2', '');
            }
          })
          .then((res) => {
            //获取拼读拼写检测去重
            const map = new Map();
            if (!this.id) {
              type === '1'
                ? (this.writeTestWords = res.data.data.filter((v) => !map.has(v.word) && map.set(v.word, v)))
                : (this.readTestWords = res.data.data.filter((v) => !map.has(v.word) && map.set(v.word, v)));
            }
            const promises1 = [];
            console.log(this.readTestWords, '555555');
            if (this.id) {
              res.data.data.forEach((item) => {
                this.readTestWords.find((val) => {
                  if (item.word === val.word) {
                    val.id = item.id;
                  }
                });
              });
            }
            this.readFilterData = this.readTestWords.map((item) => {
              return {
                key: item.word,
                label: item.word,
                id: item.id,
                isReadSelect: item.isReadSelect,
                isSelect: item.isSelect,
                isWriteSelect: item.isWriteSelect
              };
            });
            if (this.id) {
              this.readFilterWords = [];
              this.readRightDefaultChecked = [];
              this.readFilterData.forEach((res) => {
                if (res.isReadSelect === 1) {
                  this.readFilterWords.push(res.label);
                  this.readRightDefaultChecked.push(res.label);
                }
              });
              this.$nextTick(() => {
                this.readTestForm.word = this.readFilterWords.join(',');
                this.readTestForm.word ? this.onReadSelectWord() : '';
              });
            }

            return Promise.all(promises1);
          });
      },
      submitWordValidte(refName, val) {
        this.stressReadForm = [];
        this.$refs[refName].validate(async (valid) => {
          if (valid) {
            await courseDictationListApi.checkSyllableSplit({
              wordSyllable: this.stepReadForm.syllable,
              wordSplit: this.stepReadForm.splitInfo
            });
            this.stepForm = {
              id: this.$route.query.id,
              wordType: '',
              pdWordRuleDto: []
            };
            // 2拼读单词 3拼写单词
            this.stepForm.wordType = val === '1' ? '3' : '2';
            const data = this.stepReadForm;
            if (data.word.indexOf(',') !== -1) {
              //多个单词
              //分割音节
              this.readSyllableData = data.syllable.split('-');
              // this.readSyllableData.forEach((e) => {
              //   e = e.replace(/,*$/, "");
              // });
              this.readSyllableData = this.readSyllableData.map((e) => (e = e.replace(/,*$/, '')));
              //分割单词
              this.wordData = data.word.split(',');
              //分割拼读
              const splitInfoArr = data.splitInfo.split('-');
              //根据后端数据格式处理
              this.stepForm.pdWordRuleDto = [];
              this.wordData.forEach((item, index) => {
                this.stepForm.pdWordRuleDto.push({
                  word: item,
                  splitInfo: splitInfoArr[index],
                  ruleWordTypeDtoList: []
                });
              });
              //相应单词对象中添加音节
              this.readSyllableData.forEach((res, index) => {
                //单词有多个音节
                if (res.indexOf(',') !== -1) {
                  res.split(',').forEach((item) => {
                    this.stepForm.pdWordRuleDto[index].ruleWordTypeDtoList.push({
                      syllable: item,
                      type: '0',
                      status: 0
                    });
                  });
                } else {
                  //单词有单个音节
                  this.stepForm.pdWordRuleDto[index].ruleWordTypeDtoList.push({
                    syllable: res,
                    type: '0',
                    status: 0
                  });
                }
              });
              this.isShowStress = true;
            } else {
              //单个单词/编辑

              this.stepForm.wordType = val === '1' ? '3' : '2';
              const data = this.stepReadForm;
              this.stepForm.pdWordRuleDto.push({
                word: data.word,
                splitInfo: data.splitInfo,
                ruleWordTypeDtoList: []
              });
              //单个单词中多个音节
              if (data.syllable.indexOf(',') !== -1) {
                data.syllable.split(',').forEach((item) => {
                  this.stepForm.pdWordRuleDto[0].ruleWordTypeDtoList.push({
                    syllable: item,
                    type: '0',
                    status: 0
                  });
                });
              } else {
                //单个单词单个音节
                this.stepForm.pdWordRuleDto[0].ruleWordTypeDtoList.push({
                  syllable: data.syllable,
                  type: '0',
                  status: 0
                });
              }
              this.isShowStress = true;
            }
            //拼读词的重音节的数据绑定
            if (val == '0') {
              this.stepForm.pdWordRuleDto.forEach((item, index) => {
                let stressObj = {
                  index: index,
                  word: item.word,
                  stressWord: [],
                  secondaryStressedWords: []
                };
                this.stressReadForm.push(stressObj);
              });
            }
          }
        });
      },
      submitWordValidte1(refName, val) {
        if (this.checkStepWrite.length === 0) {
          return;
        }
        if (this.id) {
          this.cities = [];
          this.readTestForm.word = '';
          this.checkList = [];
          this.cities.push(this.stepWriteForm.word);
        } else {
          // this.citieList.push(...this.readList, ...this.writeList)
          this.cities = Array.from(new Set(this.readList));
        }
        this.activeCode = 2;
      },
      //必须是excel文件
      isExcel(file) {
        return /\.(xlsx|xls)$/.test(file.name);
      },
      beforeWordUpload(file) {
        if (!this.isExcel(file)) {
          this.$message.error('只能上传excel文件！');
          return false;
        }
      },
      //单词拼读上传成功
      uploadDetailHttp({ file, fileList }) {
        const that = this;
        this.wordFile = `https://document.dxznjy.com/applet/zhimi/usercode_${file.uid}.xls`;
        const fileType = file.type.substring(file.type.lastIndexOf('/') + 1);
        const fileName = `manage/${Date.parse(new Date())}.${fileType}`;
        that.$nextTick(function () {
          ossPrClient()
            .put(fileName, file)
            .then(({ res, url, name }) => {
              if (res && res.status === 200) {
                const formData = new FormData();
                formData.append('file', file);
                //   formData.append('analysisType','1');
                this.$refs.uploadReadWord.clearFiles();
                let fun;
                if (this.courseLevel == '2') {
                  fun = courseDictationListApi.analysisFile2;
                } else {
                  fun = courseDictationListApi.analysisFile;
                }
                fun(formData)
                  .then((res) => {
                    if (res.code === 20000) {
                      const { word, syllable, split } = { ...res.data.data };
                      if (this.stepReadForm.word) {
                        this.stepReadForm.word = this.stepReadForm.word + ',' + word;
                      } else {
                        this.stepReadForm.word = word;
                      }
                      if (this.stepReadForm.syllable) {
                        this.stepReadForm.syllable = this.stepReadForm.syllable + '-' + syllable;
                      } else {
                        this.stepReadForm.syllable = syllable;
                      }
                      if (this.stepReadForm.splitInfo) {
                        this.stepReadForm.splitInfo = this.stepReadForm.splitInfo + '-' + split;
                      } else {
                        this.stepReadForm.splitInfo = split;
                      }
                      console.log('this.stepReadForm---------', this.stepReadForm);
                    }
                  })
                  .catch((err) => {
                    this.fileListReadWord = [];
                    console.log(`解析失败`, err);
                  });
                that.$nextTick(() => {});
              }
            })
            .catch((err) => {
              that.$message.error('上传图片失败请检查网络或者刷新页面');
              console.log(`阿里云OSS上传图片失败回调`, err);
            });
        });
      },
      handleSetReadWordSuccess(response, file, fileList) {
        if (file && file.raw && file.uid) {
          const formData = new FormData();
          formData.append('file', file.raw);
          courseDictationListApi.analysisFile(formData).then((res) => {
            if (res.code === 20000) {
              const { word, syllable, split } = { ...res.data.data };
              if (this.stepReadForm.word) {
                this.stepReadForm.word = this.stepReadForm.word + ',' + word;
              } else {
                this.stepReadForm.word = word;
              }
              if (this.stepReadForm.syllable) {
                this.stepReadForm.syllable = this.stepReadForm.syllable + '-' + syllable;
              } else {
                this.stepReadForm.syllable = syllable;
              }
              if (this.stepReadForm.splitInfo) {
                this.stepReadForm.splitInfo = this.stepReadForm.splitInfo + '-' + split;
              } else {
                this.stepReadForm.splitInfo = split;
              }
            }
          });
        }
      },
      handleSetWriteWordSuccess(response, file, fileList) {
        if (file && file.raw && file.uid) {
          const formData = new FormData();
          formData.append('file', file.raw);
          courseDictationListApi.analysisFile(formData).then((res) => {
            if (res.code === 20000) {
              const { word, syllable, split } = { ...res.data.data };
              if (this.stepWriteForm.word) {
                this.stepWriteForm.word = Array.from(
                  new Set(word.split(',').concat(this.stepWriteForm.word.indexOf(',') !== -1 ? this.stepWriteForm.word.split(',') : this.stepWriteForm.word))
                ).join(',');
              } else {
                this.stepWriteForm.word = Array.from(new Set(word.split(','))).join(',');
              }
              if (this.stepWriteForm.syllable) {
                this.stepWriteForm.syllable = Array.from(
                  new Set(syllable.split('-').concat(this.stepWriteForm.syllable.indexOf('-') !== -1 ? this.stepWriteForm.syllable.split('-') : this.stepWriteForm.syllable))
                ).join('-');
              } else {
                this.stepWriteForm.syllable = Array.from(new Set(syllable.split('-'))).join('-');
              }
              if (this.stepWriteForm.splitInfo) {
                this.stepWriteForm.splitInfo = Array.from(
                  new Set(split.split('-').concat(this.stepWriteForm.splitInfo, indexOf('-') !== -1 ? this.stepWriteForm.splitInfo.split('-') : this.stepWriteForm.splitInfo))
                ).join('-');
              } else {
                this.stepWriteForm.splitInfo = Array.from(new Set(split.split('-'))).join('-');
              }
            }
          });
        }
      },
      //单词拼写上传
      uploadWriteDetailHttp({ file }) {
        const that = this;
        this.wordFile = `https://document.dxznjy.com/applet/zhimi/usercode_${file.uid}.xls`;
        const fileType = file.type.substring(file.type.lastIndexOf('/') + 1);
        const fileName = `manage/${Date.parse(new Date())}.${fileType}`;
        that.$nextTick(function () {
          ossPrClient()
            .put(fileName, file)
            .then(({ res, url, name }) => {
              if (res && res.status === 200) {
                console.log(`阿里云OSS上传图片成功回调1`, file, res, url, name);
                const formData = new FormData();
                formData.append('file', file);
                //   formData.append('analysisType','1');
                courseDictationListApi
                  .analysisFile(formData)
                  .then((res) => {
                    if (res.code === 20000) {
                      const { word, syllable, split } = { ...res.data.data };
                      if (this.stepWriteForm.word) {
                        this.stepWriteForm.word = Array.from(
                          new Set(word.split(',').concat(this.stepWriteForm.word.indexOf(',') !== -1 ? this.stepWriteForm.word.split(',') : this.stepWriteForm.word))
                        ).join(',');
                      } else {
                        this.stepWriteForm.word = Array.from(new Set(word.split(','))).join(',');
                      }
                      if (this.stepWriteForm.syllable) {
                        this.stepWriteForm.syllable = Array.from(
                          new Set(
                            syllable.split('-').concat(this.stepWriteForm.syllable.indexOf('-') !== -1 ? this.stepWriteForm.syllable.split('-') : this.stepWriteForm.syllable)
                          )
                        ).join('-');
                      } else {
                        this.stepWriteForm.syllable = Array.from(new Set(syllable.split('-'))).join('-');
                      }
                      if (this.stepWriteForm.splitInfo) {
                        this.stepWriteForm.splitInfo = Array.from(
                          new Set(
                            split.split('-').concat(this.stepWriteForm.splitInfo, indexOf('-') !== -1 ? this.stepWriteForm.splitInfo.split('-') : this.stepWriteForm.splitInfo)
                          )
                        ).join('-');
                      } else {
                        this.stepWriteForm.splitInfo = Array.from(new Set(split.split('-'))).join('-');
                      }
                      this.$refs.writeUpload.clearFiles();
                    }
                  })
                  .catch((err) => {
                    this.$refs.writeUpload.clearFiles();
                    this.fileListRemoveReadWord = [];
                    console.log(`解析失败`, err);
                  });
              }
            })
            .catch((err) => {
              that.$message.error('上传图片失败请检查网络或者刷新页面');
              console.log(`阿里云OSS上传图片失败回调`, err);
            });
        });
      },
      //音节拼读上传成功
      handleSetReadSyllableSuccess(response, file, fileList) {
        if (file && file.raw && file.uid) {
          this.readSyllableUrl = `https://document.dxznjy.com/applet/zhimi/usercode_${file.raw.uid}.xls`;
        }
      },
      //音节拼读上传成功
      handleSetReadSplitInfoSuccess(response, file, fileList) {
        if (file && file.raw && file.uid) {
          this.readSplitInfoUrl = `https://document.dxznjy.com/applet/zhimi/usercode_${file.raw.uid}.xls`;
        }
      },
      //单词拼读删除
      handlSetRemoveReadWord() {
        this.readWordUrl = '';
      },
      //音节拼读删除
      handlSetRemoveReadSyllable() {
        this.readSyllableUrl = '';
      },
      //单词拼读删除
      handlSetRemoveReadSplitInfo() {
        this.readSplitInfoUrl = '';
      },
      restForm(form) {
        for (let i in form) {
          form[i] = '';
        }
      },
      //字段制空
      emptyList() {
        this.cities = [];
        this.stepDialogVisible = false;
        this.spellDialogTitile = '新增';
        this.activeCode = 0;
        this.id = '';
        this.isShowStress = false;
        this.fileListReadWord = [];
        this.fileListRemoveReadWord = [];
        this.stepForm = {};
        this.stepForm1 = {};
        this.wordType = '';
        this.checkList = [];
        this.checkWriteList = [];
        this.checkStepWrite = [];
        this.readList = [];
        this.writeList = [];
        this.readWord = [];
        this.writeWord = [];
        this.checkread = [];
        this.checkwrite = [];
        this.readTestWords = [];
      },
      //关闭弹窗
      dialogClose() {
        this.emptyList();
        this.restForm(this.stepReadForm);
        this.restForm(this.stepWriteForm);
        this.restForm(this.stressReadForm);
        this.restForm(this.stressWriteForm);
        if (this.$refs['stepReadForm'] && this.$refs['stepReadForm'].resetFields) {
          this.$refs['stepReadForm'].resetFields();
        }
        if (this.$refs['stepWriteForm'] && this.$refs['stepWriteForm'].resetFields) {
          this.$refs['stepWriteForm'].resetFields();
        }
        if (this.$refs['stressReadForm'] && this.$refs['stressReadForm'].resetFields) {
          this.$refs['stressReadForm'].resetFields();
        }
        if (this.$refs['readTestForm'] && this.$refs['readTestForm'].resetFields) {
          this.$refs['readTestForm'].resetFields();
        }
        if (this.$refs['writeTestForm'] && this.$refs['writeTestForm'].resetFields) {
          this.$refs['writeTestForm'].resetFields();
        }

        this.$emit('spellDialogClose');
      }
    }
  };
</script>
<style lang="less" scoped>
  .activeStep {
    padding: 0 80px 16px;
  }

  .download_link {
    // height: 32px;
    // margin: 0;
    // padding: 0 15px;
    color: #13ce66;
  }

  .upload_link {
    color: #1890ff;
  }

  .upload_box {
    // display: flex;
    // justify-content: space-around;
  }
</style>
