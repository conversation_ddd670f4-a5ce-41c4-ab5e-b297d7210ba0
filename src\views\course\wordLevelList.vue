<template>
  <div class="app-container">
    <!-- 搜索 -->
    <el-form :inline="true" ref="form" class="SearchForm">
      <el-row style="padding: 20px 20px 0 20px">
       <el-col :span="8" :xs="24">
         <el-form-item label="标题:">
           <el-input v-model="dataQuery.title" @keyup.enter.native="fetchData01()"  placeholder="请输入标题:" clearable></el-input>
         </el-form-item>
       </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="词汇量下限:">
            <el-input v-model="dataQuery.wordLowerLimit" @keyup.enter.native="fetchData01()" placeholder="请输入词汇量下限:" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="词汇量上限:">
            <el-input v-model="dataQuery.wordUpperLimit" @keyup.enter.native="fetchData01()" placeholder="请输入词汇量上限:" clearable></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="24" :xs="24" style="text-align: right">
          <el-form-item>
            <el-button type="warning" icon="el-icon-search" @click="fetchData01()">搜索</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div class="SearchForm">
      <!-- 添加 -->
      <div class="btn-add" style="margin-bottom: 20px">
        <el-button size="small" type="primary" icon="el-icon-plus" @click="clickAdd">添加</el-button>
      </div>
      <!-- 表格 -->
      <el-table class="common-table" :data="tableData" stripe border :default-sort="{prop: 'date', order: 'descending'}" v-loading="tableLoading">
        <el-table-column prop="wordLevelCode" label="水平编号" width="110" sortable></el-table-column>
        <el-table-column prop="itle" label="标题" width="160" sortable></el-table-column>
        <el-table-column prop="id" label="操作" width="120" sortable>
          <template slot-scope="scope">
            <el-button type="success" size="mini" icon="el-icon-edit-outline" @click="handleUpdate(scope.row.id)">编辑</el-button>
          </template>
        </el-table-column>
        <el-table-column prop="ratio" label="换算比例" width="110" sortable></el-table-column>
        <el-table-column prop="wordLowerLimit" label="词汇量下限" width="120" sortable></el-table-column>
        <el-table-column prop="wordUpperLimit" label="词汇量上限" width="120" sortable></el-table-column>
        <el-table-column prop="score" label="分数" sortable></el-table-column>
        <el-table-column prop="levelDescription" label="描述" width="300" sortable show-overflow-tooltip></el-table-column>
        <el-table-column prop="evaluationAnalysis" label="测评分析" width="300" sortable show-overflow-tooltip></el-table-column>
        <el-table-column prop="learningAdvice" label="学习建议" width="300" sortable show-overflow-tooltip></el-table-column>
        <el-table-column prop="addTime" label="添加时间" width="180" sortable show-overflow-tooltip></el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <el-col :span="20">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]" layout="total, sizes, prev, pager, next, jumper"
        :total="tablePage.totalItems" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    </el-col>

    <!-- 编辑或者添加弹窗 -->
    <el-dialog :title="addOrUpdate?'添加词汇水平':'编辑词汇水平'" :visible.sync="dialogVisible" width="70%" :close-on-click-modal="false"
      @close="close">
      <el-form :ref="addOrUpdate?'addWordData':'updateWordData'" :rules="rules" :model="addOrUpdate?addWordData:updateWordData"
        label-position="left" label-width="120px" style="width: 100%;">
        <el-form-item label="词汇等级" prop="studyRank">
          <el-col :xs="24" :span="18">
            <el-select style="width: 100%;" v-if="addOrUpdate" v-model="addWordData.studyRank" filterable value-key="value"
              placeholder="请选择">
              <el-option v-for="(item,index) in studyRankType" :key="index" :label="item.label" :value="item.value" />
            </el-select>
            <el-select style="width: 100%;" v-if="!addOrUpdate" v-model="updateWordData.studyRank" filterable value-key="value"
              placeholder="请选择">
              <el-option v-for="(item,index) in studyRankType" :key="index" :label="item.label" :value="item.value" />
            </el-select>
          </el-col>
        </el-form-item>
        <el-form-item label="换算比列" prop="ratio">
          <el-col :xs="24" :span="18">
            <el-input v-if="addOrUpdate" type="number"  v-model="addWordData.ratio" maxlength="4" isNumber="true"></el-input>
            <el-input v-if="!addOrUpdate" type="number"  v-model="updateWordData.ratio" maxlength="4" isNumber="true"></el-input>
          </el-col>
        </el-form-item>
        <el-form-item label="词汇量下限 " prop="wordLowerLimit">
          <el-col :xs="24" :span="18">
            <el-input v-if="addOrUpdate" type="number"  v-model="addWordData.wordLowerLimit" maxlength="4" isNumber="true"></el-input>
            <el-input v-if="!addOrUpdate" type="number"  v-model="updateWordData.wordLowerLimit" maxlength="4" isNumber="true"></el-input>
          </el-col>
        </el-form-item>
        <el-form-item label="词汇量上限" prop="wordUpperLimit">
          <el-col :xs="24" :span="18">
            <el-input v-if="addOrUpdate" type="number"  v-model="addWordData.wordUpperLimit" maxlength="4" isNumber="true"></el-input>
            <el-input v-if="!addOrUpdate" type="number"  v-model="updateWordData.wordUpperLimit" maxlength="4" isNumber="true"></el-input>
          </el-col>
        </el-form-item>
        <el-form-item label="分数" prop="score">
          <el-col :xs="24" :span="18">
            <el-input v-if="addOrUpdate" type="number"  v-model="addWordData.score" maxlength="4" isNumber="true"></el-input>
            <el-input v-if="!addOrUpdate" type="number"  v-model="updateWordData.score" maxlength="4" isNumber="true"></el-input>
          </el-col>
        </el-form-item>
        <el-form-item label="描述" prop="levelDescription">
          <el-col :xs="24" :span="18">
            <el-input type="textarea" resize="none" :rows="4" v-if="addOrUpdate" v-model="addWordData.levelDescription" />
            <el-input type="textarea" resize="none" :rows="4" v-if="!addOrUpdate" v-model="updateWordData.levelDescription" />
          </el-col>
        </el-form-item>
        <el-form-item label="测评分析" prop="evaluationAnalysis">
          <el-col :xs="24" :span="18">
            <el-input type="textarea" resize="none" :rows="4" v-if="addOrUpdate" v-model="addWordData.evaluationAnalysis" />
            <el-input type="textarea" resize="none" :rows="4" v-if="!addOrUpdate" v-model="updateWordData.evaluationAnalysis" />
          </el-col>
        </el-form-item>
        <el-form-item label="学习建议" prop="learningAdvice">
          <el-col :xs="24" :span="18">
            <el-input type="textarea" resize="none" :rows="4" v-if="addOrUpdate" v-model="addWordData.learningAdvice" />
            <el-input type="textarea" resize="none" :rows="4" v-if="!addOrUpdate" v-model="updateWordData.learningAdvice" />
          </el-col>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button v-if="addOrUpdate" size="mini" type="primary" @click="addActiveFun('addWordData')">新增</el-button>
        <el-button v-if="!addOrUpdate" size="mini" type="primary" @click="updateActiveFun('updateWordData')">修改</el-button>
        <el-button size="mini" @click="close">关闭</el-button>
      </div>
    </el-dialog>

    <el-dialog :visible.sync="dialogUploadVisible" :close-on-click-modal="false">
      <img width="100%" :src="dialogImageUrl" alt>
    </el-dialog>
  </div>
</template>

<script>
  import courseApi from '@/api/wordLevelList'
  import enTypes from '@/api/bstatus'
  import {
    pageParamNames
  } from "@/utils/constants";
  import {
    ossPrClient
  } from '@/api/alibaba'

  export default {
    data() {
      return {
        // 分页
        tablePage: {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null
        },
        tableLoading: false,
        dataQuery: {
          title: '',
          wordUpperLimit: '',
          wordLowerLimit: ''
        },
        activeType: [], // 活动类型
        // 分页
        tablePage: {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null
        },
        tableData: [], //表格数据
        dialogVisible: false, // 修改弹窗是否展示
        addOrUpdate: true, // 是新增还是修改
        addWordData: {}, // 新增课程
        updateWordData: {}, // 修改数据
        rules: { // 表单提交规则
          ratio: [{
            required: true,
            message: '必填',
            trigger: 'blur'
          }],
          wordLowerLimit: [{
            required: true,
            message: '必填',
            trigger: 'blur'
          }],
          studyRank: [{
            required: true,
            message: '必填',
            trigger: 'change'
          }],
          wordUpperLimit: [{
            required: true,
            message: '必填',
            trigger: 'blur'
          }],
          score: [{
            required: true,
            message: '必填',
            trigger: 'blur'
          }],
          levelDescription: [{
            required: true,
            message: '必填',
            trigger: 'blur'
          }],
          evaluationAnalysis: [{
            required: true,
            message: '必填',
            trigger: 'blur'
          }],
          learningAdvice: [{
            required: true,
            message: '必填',
            trigger: 'blur'
          }]
        },
        studyRankType: [], //词汇水平
        fileListPending: [], // 待处理已上传图片信息
        fileList: [], // 上传图片已有图片列表

        fileDetailListPending: [], // 待处理已上传图片信息
        fileDetailList: [], // 上传图片已有图片列表

        dialogUploadVisible: false,
        dialogImageUrl: '', // 上传图片预览

        uploadLoading: false, // 上传图片加载按钮
        fullscreenLoading: false, // 保存啥的加载

        content: '',
        isUploadSuccess: true, // 是否上传成功
        isShowRelevance: true, // 新增或修改是否展示关联产品

        radio: '0', //单选框状态 值必须是字符串
        value1: [],
      }

    },
    created() {
      this.fetchData();
      this.getStady();
      ossPrClient();
    },
    methods: {
      fetchData01() {
        this.tablePage = {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null
        }
        this.fetchData();
      },
      // 查询+搜索课程列表
      fetchData() {
        const that = this
        that.tableLoading = true
        courseApi.courseList(that.tablePage.currentPage, that.tablePage.size, that.dataQuery).then(res => {
          that.tableData = res.data.data
          // console.log(res)
          that.tableLoading = false
          // 设置后台返回的分页参数
          pageParamNames.forEach(name => that.$set(that.tablePage, name, parseInt(res.data[name])))
        })
      },
      //添加操作
      clickAdd() {
        this.addWordData = {
          'isEnable': 0,
        }
        this.dialogVisible = true
        this.addOrUpdate = true
      },
      // 新增课程提交
      addActiveFun(ele) {
        const that = this
        that.$refs[ele].validate(valid => { // 表单验证
          if (valid) {
            const loading = this.$loading({
              lock: true,
              text: '新增课程',
              spinner: 'el-icon-loading',
              background: 'rgba(0, 0, 0, 0.7)'
            })
            courseApi.addWordLevel(that.addWordData).then(() => {
              that.dialogVisible = false
              loading.close()
              that.$nextTick(() => that.fetchData())
              that.$message.success('新增课程成功')
            }).catch(err => {
              loading.close()
            })
          } else {
            console.log('error submit!!')
            //loading.close();
            return false
          }
        })
      },
      //获取词汇等级下拉框
      getStady() {
        var enType = "StudyRank";
        enTypes.getEnumerationAggregation(enType).then(res => {
          this.studyRankType = res.data;
        })
      },
      // 点击编辑按钮
      handleUpdate(id) {
        const that = this
        that.dialogVisible = true
        that.addOrUpdate = false
        courseApi.queryActive(id).then(res => {
          console.log(res);
          that.updateWordData = res.data
          that.updateWordData.studyRank = res.data.studeyRank.toString();
        }).catch(err => {

        })
      },
      // 状态改变事件
      change(radio) {
        if (radio == "1") {
          this.addWordData.isEnable = 1;
        } else {
          this.addWordData.isEnable = 0;
        }
      },
      // 修改单词提交
      updateActiveFun(ele) {
        const that = this
        that.$refs[ele].validate(valid => { // 表单验证
          if (valid) {
            const loading = this.$loading({
              lock: true,
              text: '修改单词水平信息提交',
              spinner: 'el-icon-loading',
              background: 'rgba(0, 0, 0, 0.7)'
            })
            this.updateWordData.studyRank = parseInt(this.updateWordData.studyRank)
            courseApi.updateWordLevel(that.updateWordData).then(() => {
              that.dialogVisible = false
              loading.close();
              that.$nextTick(() => that.fetchData())
              that.$message.success('修改单词水平成功')
            }).catch(err => {
              console.log("wyy");
              // 关闭提示弹框
              loading.close()
            })
          } else {
            console.log('error submit!!')
            loading.close();
            return false
          }
        })
      },
      // 课程开通与暂停
      courseStatus(id, status) {
        if (status == 0) {
          status = 1;
        } else {
          status = 0
        }
        const that = this;
        this.$confirm('确定操作吗?', '修改状态', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          courseApi.updateStatus(id, status).then(res => {
            that.$nextTick(() => that.fetchData())
            that.$message.success('修改成功!')
          }).catch(err => {

          })
        }).catch(err => {

        })
      },

      // 删除上传图片
      handleRemove(file, fileList) {
        const that = this
        if (!that.addOrUpdate) {
          that.fileList = fileList
        } else {
          for (let a = 0; a < that.fileListPending.length; a++) {
            that.fileListPending[a].uid === file.uid ? that.fileList.splice(a, 1) : ''
          }
        }
      },

      // 删除上传图片
      handleRemoveDetail(file, fileList) {
        const that = this
        if (!that.addOrUpdate) {
          that.fileDetailList = fileList
        } else {
          for (let a = 0; a < that.fileDetailListPending.length; a++) {
            that.fileDetailListPending[a].uid === file.uid ? that.fileDeatiList.splice(a, 1) : ''
          }
        }
      },
      // 上传图片预览
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogUploadVisible = true
      },
      // 上传图片数量超限
      justPictureNum(file, fileList) {
        this.$message.warning(`当前限制选择1个文件`)
      },
      //进入子类
      enterChildrenList(categoryName, categoryCode) {
        const that = this;
        that.$router.push({
          path: "/course/courseMake",
          query: {
            categoryName: categoryName,
            categoryCode: categoryCode
          }
        });
      },
      // 分页
      handleSizeChange(val) {
        this.tablePage.size = val;
        this.fetchData();
      },
      handleCurrentChange(val) {
        this.tablePage.currentPage = val;
        this.fetchData();
      },
      // 关闭弹窗
      close() {
        this.dialogVisible = false
      }
    }
  }
</script>

<style>
  .lh36 {
    line-height: 36px;
    font-size: 14px;
  }

  .SearchForm {
    width: 100%;
    margin-bottom: 15px;
    background: #fff;
    border-radius: 6px;
    padding: 5px 0;
    padding-left: 5px;
    box-shadow: 1px 1px 3px rgba(0, 0, 0, .2);
  }

  .btn-add {
    padding: 5px;
  }

  .red {
    color: red;
  }

  .green {
    color: green;
  }

  .course-table {
    text-align: center;
  }

  .course-table td,
  .course-table th {
    padding: 5px 0;
    text-align: center;
  }

  .course-table button {
    padding: 2px;
  }

  .icon-stop {
    float: left;
    display: inline-block;
    width: 12px;
    height: 12px;
    background: url('../../icons/stop.png') no-repeat top center/contain;
  }

  .mt22 {
    margin-top: 22px;
  }

  @media (max-width:767px) {
	  .el-message-box{
		width: 80%!important;
	  }
  }
</style>
