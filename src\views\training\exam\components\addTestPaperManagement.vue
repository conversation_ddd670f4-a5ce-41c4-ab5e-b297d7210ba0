<template>
  <div class="add-video-dialog">
    <el-dialog width="750px" :visible.sync="dialogParam.visible" @closed="closeAddDialog()" :close-on-click-modal="false" @submit.native.prevent>
      <div slot="title" class="dialog-title">
        <span v-if="dialogParam.type === 'add'">新增试卷</span>
        <span v-else-if="dialogParam.type === 'edit'">编辑试卷</span>
        <span v-else-if="dialogParam.type === 'detail'">试卷详情</span>
      </div>

      <el-form :model="labelForm" ref="addForm" label-width="120px" label-position="right" :rules="rules" :disabled="dialogParam.type === 'detail'">
        <el-form-item label="试卷名称：" prop="examName" style="width: 90%">
          <el-input style="width: 100%" placeholder="请输入试卷名称" v-model.trim="labelForm.examName"></el-input>
        </el-form-item>
        <el-form-item label="试卷总分：" prop="examFullMarks" style="width: 90%">
          <el-input style="width: 100%" placeholder="请输入试卷总分" v-model.trim="labelForm.examFullMarks"></el-input>
        </el-form-item>
        <el-form-item label="限制时间：" prop="examTimeLimit" style="width: 90%">
          <span style="display: inline-flex; width: 100%">
            <el-input style="width: 100%" placeholder="请输入时间" v-model.trim="labelForm.examTimeLimit"></el-input>
            &nbsp;&nbsp;&nbsp;
            <span>分</span>
            <span>钟</span>
          </span>
        </el-form-item>
        <el-form-item label="对应课程：" prop="courseId" style="width: 90%">
          <el-select style="width: 100%" v-model="labelForm.courseId" placeholder="请选择">
            <el-option v-for="item in linkCourseList" :key="item.id" :label="item.courseName" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="题型：" prop="questionType">
          <el-form :inline="true" :model="labelForm" ref="labelForm" label-width="120px" :disabled="dialogParam.type === 'detail'">
            <el-checkbox-group v-model="labelForm.questionType">
              <el-checkbox v-for="(item, index) in courseType" :key="index" :label="item.dictValue">
                <span>{{ item.dictLabel }}</span>
                <el-form-item label="试题数量" :class="showError && !item.num ? 'is-error' : ''">
                  <!-- &nbsp;&nbsp;&nbsp; 试题数量 &nbsp;&nbsp;&nbsp; -->
                  <el-input style="width: 120px" placeholder="请输入数量" v-model="item.num" :disabled="labelForm.questionType.indexOf(item.dictValue) === -1"></el-input>
                </el-form-item>
                <el-form-item label="单个试题分数值" :class="showError && !item.score ? 'is-error' : ''">
                  <!-- &nbsp;&nbsp;&nbsp; 试题数量 &nbsp;&nbsp;&nbsp; -->
                  <el-input style="width: 120px" placeholder="请输入分值" v-model="item.score" :disabled="labelForm.questionType.indexOf(item.dictValue) === -1"></el-input>
                </el-form-item>
                <!-- &nbsp;&nbsp;&nbsp; 单个试题分数值 &nbsp;&nbsp;&nbsp;
                  <el-input  style="width: 120px" placeholder="请输入数量" v-model="item.score" :disabled="labelForm.questionType.indexOf(item.label) === -1" ></el-input> -->
              </el-checkbox>
              <!-- <el-checkbox label="多选题">
                <div>
                  多选题 &nbsp;&nbsp;&nbsp; 试题数量 &nbsp;&nbsp;&nbsp;
                  <el-input  style="width: 120px" placeholder="请输入数量" v-model.trim="labelForm.num" :disabled="labelForm.questionType.indexOf('多选题') === -1" ></el-input>
                  &nbsp;&nbsp;&nbsp; 单个试题分数值 &nbsp;&nbsp;&nbsp;
                  <el-input style="width: 120px" placeholder="请输入数量" v-model.trim="labelForm.score" :disabled="labelForm.questionType.indexOf('多选题') === -1"  ></el-input>
                </div>
              </el-checkbox>
              <el-checkbox label="判断题">
                <div>
                  判断题 &nbsp;&nbsp;&nbsp; 试题数量 &nbsp;&nbsp;&nbsp;
                  <el-input style="width: 120px" placeholder="请输入数量" v-model.trim="labelForm.num" :disabled="labelForm.questionType.indexOf('判断题') === -1" ></el-input>
                  &nbsp;&nbsp;&nbsp; 单个试题分数值 &nbsp;&nbsp;&nbsp;
                  <el-input style="width: 120px" placeholder="请输入数量" v-model.trim="labelForm.multiScore" :disabled="labelForm.questionType.indexOf('判断题') === -1"  ></el-input>
                </div>
              </el-checkbox> -->
            </el-checkbox-group>
          </el-form>
        </el-form-item>
        <el-form-item label="及格分数：" prop="examPassScore" style="width: 90%">
          <span style="display: inline-flex; width: 100%">
            <el-input style="width: 100%" placeholder="请输入及格分数" v-model.trim="labelForm.examPassScore"></el-input>
            &nbsp;&nbsp;&nbsp;
            <span>分</span>
          </span>
        </el-form-item>
        <el-form-item label="试卷题目和排序随机：" label-width="190px" prop="isRecordQuestionRandom" style="width: 90%" required>
          <el-switch v-model="labelForm.isRecordQuestionRandom"></el-switch>
        </el-form-item>
      </el-form>
      <!-- 底部按钮栏 -->
      <div slot="footer" class="dialog-footer" v-if="dialogParam.type === 'add' || dialogParam.type === 'edit'">
        <el-button type="primary" size="small" :loading="loading" @click="submitForm()">确认</el-button>
        <el-button size="small" @click="closeAddDialog()">取消</el-button>
      </div>
      <div slot="footer" class="dialog-footer" v-else-if="dialogParam.type === 'detail'">
        <el-button size="small" @click="closeAddDialog()">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
  import examApi from '@/api/training/exam';
  import { array } from 'js-md5';
  export default {
    name: 'addTestPaperManagement',
    components: {},
    props: {
      dialogParam: {
        type: Object,
        default() {
          return {};
        }
      },
      linkCourseList: {
        type: Array,
        default: []
      },
      courseType: {
        type: Array,
        default: []
      }
    },
    data() {
      return {
        loading: false,
        labelForm: {
          examName: '',
          examTimeLimit: '',
          courseId: '',
          questionType: [],
          examPassScore: '',
          examFullMarks: '',
          isRecordQuestionRandom: true
        },
        showError: false,
        rules: {
          examName: [{ required: true, message: '试卷名称不可为空', trigger: 'blur' }],
          examFullMarks: [{ required: true, message: '试卷总分不可为空', trigger: 'blur' }],
          examTimeLimit: [{ required: true, message: '限制时间不可为空', trigger: 'blur' }],
          courseId: [{ required: true, message: '请选择对应课程', trigger: 'change' }],
          examPassScore: [{ required: true, message: '及格分数不可为空', trigger: 'blur' }],
          questionType: [
            {
              type: 'array',
              required: true,
              message: '请至少选择一个题型',
              trigger: 'change'
            }
          ]
        }
      };
    },
    mounted() {},
    methods: {
      open(id) {
        examApi.trainingDetail({ id: id }).then((res) => {
          for (const key in this.labelForm) {
            if (Object.prototype.hasOwnProperty.call(res.data, key)) {
              const element = res.data[key];
              this.labelForm[key] = element;
              if (key == 'questionType') {
                this.labelForm[key] = [];
              }
            }
          }
          this.labelForm.isRecordQuestionRandom = res.data.isRecordQuestionRandom == 1 ? true : false;
          // 题型数据处理
          this.courseType.forEach((item) => {
            if (item.dictLabel === '单选题') {
              item.num = res.data.examSingleNum;
              item.score = res.data.examScoreSingle;
            } else if (item.dictLabel == '多选题') {
              item.num = res.data.examMultipeNum;
              item.score = res.data.examScoreMultipe;
            } else if (item.dictLabel == '判断题') {
              item.num = res.data.examTofNum;
              item.score = res.data.examScoreTof;
            }
            if (item.num && item.num > 0) {
              this.labelForm.questionType.push(item.dictValue);
            }
            console.log(this.labelForm);
          });
        });
      },
      // 提交
      submitForm() {
        this.labelForm.questionTypeList = [];
        for (var i = 0; i < this.courseType.length; i++) {
          let item = this.courseType[i];
          if (this.labelForm.questionType.indexOf(item.dictValue) != -1) {
            if (!item.num || !item.score) {
              this.showError = true;
              this.$message.error('请输入必填信息');
              return;
            }
            this.labelForm.questionTypeList.push({ questionType: item.dictValue, num: Number(item.num), score: Number(item.score) });
          }
        }
        this.$refs.addForm.validate((valid) => {
          if (valid) {
            // delete this.labelForm.questionType
            let params = { ...this.labelForm };
            params.isRecordQuestionRandom = this.labelForm.isRecordQuestionRandom ? 1 : 0;
            examApi.paperCreate(params).then((res) => {
              this.$emit('closeDialog');
            });
          } else {
            console.log('error submit!!');
            return false;
          }
        });
      },
      closeAddDialog() {
        this.$emit('closeDialog', 'detail');
      }
    }
  };
</script>
<style scoped>
  .content {
    line-height: 30px;
    width: 100%;
    height: 30px;
    background-color: #d7d7d7;
    margin-bottom: 20px;
  }
  .contentTxt {
    margin-left: 10px;
  }
  ::v-deep .el-checkbox {
    display: block;
    margin-bottom: 20px;
  }
  .el-form-item {
    vertical-align: middle;
  }
</style>
