<!-- 待完善复习信息表-->
<template>
  <div class="app-container">
    <el-form
      :inline="true"
      class="container-card"
      label-width="110px"
      label-position="left"
      ref="dataQuery"
      :model="dataQuery"
    >
      <el-row>
        <el-col :span="8" :xs="24">
          <el-form-item label="姓名：" prop="studentName">
            <el-input
              v-model="dataQuery.studentName"
              placeholder="请输入姓名"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="学员编号：" prop="studentCode">
            <el-input
              v-model="dataQuery.studentCode"
              placeholder="请输入学员编号"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item>
            <el-button
              type="primary"
              icon="el-icon-search"
              @click="fetchData01()"
              size="medium"
              >搜索</el-button
            >
            <el-button icon="el-icon-refresh" @click="rest()" size="medium"
              >重置</el-button
            >
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <el-table
      show-header
      v-loading="tableLoading"
      :data="tableData"
      style="width: 100%; margin-bottom: 20px"
      row-key="id"
      :tree-props="{ list: 'children', hasChildren: 'true' }"
    >
      <el-table-column
        prop="orderNo"
        label="订单编号"
        width=""
        align="center"
      ></el-table-column>
      <el-table-column
        prop="studentName"
        label="学员姓名"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="withdrawnBonus"
        label="操作"
        width=""
        align="center"
      >
        <template slot-scope="scope">
          <el-button
            v-if="scope.row.isSubmit == 0"
            type="primary"
            size="mini"
            icon="el-icon-edit-outline"
            @click="fillinOutside(scope.row)"
            >填写复习时间表</el-button
          >
        </template>
      </el-table-column>
      <el-table-column
        prop="studentCode"
        label="学员编号"
        width=""
        align="center"
      ></el-table-column>
      <el-table-column
        prop="phone"
        label="学员手机号"
        width=""
        align="center"
      ></el-table-column>
      <el-table-column
        prop="rechargeReviewTime"
        label="已购复习时长"
        width=""
        align="center"
      ></el-table-column>
      <el-table-column
        prop="createTime"
        label="购买时间"
        width=""
        :show-overflow-tooltip="true"
        align="center"
      ></el-table-column>
    </el-table>

    <el-pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="tablePage.currentPage"
      :page-sizes="[10, 20, 30, 40, 50]"
      :page-size="tablePage.size"
      layout="total, sizes, prev, pager, next, jumper"
      :total="tablePage.totalItems"
    ></el-pagination>

    <el-dialog
      title="复习时间表"
      :visible.sync="dialogVisible"
      :close-on-click-modal="false"
      :before-close="handleClose"
    >
      <el-form
        ref="abutmentList"
        :model="abutmentList"
        label-width="120px"
        :rules="rules"
      >
        <el-form-item label="学员姓名" prop="studentName" style="width: 50%">
          <el-input v-model="abutmentList.studentName" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="学员编号" prop="studentCode" style="width: 50%">
          <el-input v-model="abutmentList.studentCode" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="联系方式" prop="phone" style="width: 50%">
          <el-input v-model="abutmentList.phone" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="年级" prop="grade" style="width: 50%">
          <el-select v-model="abutmentList.grade" placeholder="请选择">
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          label="充值复习包"
          prop="rechargeReviewTime"
          style="width: 50%"
        >
          <el-input
            v-model="abutmentList.rechargeReviewTime"
            placeholder="请输入"
          /><text>分钟</text>
        </el-form-item>
        <el-form-item
          label="复习时间"
          prop
          style="width: 90%; margin-top: 10px"
        >
          <el-form-item prop="reviewWeek">
            <el-checkbox-group
              v-model="abutmentList.reviewWeek"
              size="medium"
              @change="reviewWeekChange"
            >
              <el-checkbox-button
                v-for="item in weeklist"
                :label="item.value"
                :key="item.value"
                :vaule="item.value"
              >
                {{ item.label }}
              </el-checkbox-button>
            </el-checkbox-group>
          </el-form-item>
          <el-form-item
            style="width: 40%; margin-top: 1vw"
            prop="reviewTime"
            :rules="rules.reviewTime"
          >
            <el-time-picker
              v-model="abutmentList.reviewTime"
              placeholder="请选择"
              format="HH:mm"
            ></el-time-picker>
          </el-form-item>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </span>
    </el-dialog>

    <el-dialog
      title="温馨提示"
      :visible.sync="tipsDialogVisible"
      width="30%"
      :before-close="handleClose"
    >
      <span>首次开课的学员必须填写上课信息对接表后，才可正常排课上课。</span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="tipsDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="classSchedule"
          >填写上课信息表</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
import dayjs from "dayjs";
import studentApi from "@/api/studentList";
import courseApi from "@/api/courseList";
import schoolList from "@/api/schoolList";
export default {
  name: "studentList",
  directives: {
    "el-select-loadmore": {
      bind(el, binding) {
        const SELECTWRAP_DOM = el.querySelector(
          ".el-select-dropdown .el-select-dropdown__wrap"
        );
        SELECTWRAP_DOM.addEventListener("scroll", function () {
          //临界值的判断滑动到底部就触发
          const condition =
            this.scrollHeight - this.scrollTop <= this.clientHeight;
          if (condition) {
            binding.value();
          }
        });
      },
    },
  },
  data() {
    return {
      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null,
      },
      exportLoading: false,
      tableData: [],
      dataQuery: {
        studentName: "",
        studentCode: "",
      },

      dialogVisible: false,
      abutmentList: {
        id: "",
        studentName: "",
        studentCode: "",
        phone: "",
        grade: "",
        rechargeHour: "",
        courseProject: [
          {
            courseName: "",
          },
        ],
        reviewWeek: [],
        reviewTime: "",
        studyTimeList: [
          {
            usableWeek: "",
            endTime: "",
            startTime: "",
            usableHourEnd: 0,
            usableMinuteEnd: 0,
            usableHourStart: 0,
            usableMinuteStart: 0,
          },
        ],
        isExp: 0,
        isNewStudent: 0,
        isSubmit: 0,
        wordBase: 0,
        remark: "",
        firstStudyTime: "",
      },

      //年纪
      options: [
        { grade: "18", value: "幼儿园" },
        {
          value: 1,
          label: "一年级",
        },
        {
          value: 2,
          label: "二年级",
        },
        {
          value: 3,
          label: "三年级",
        },
        {
          value: 4,
          label: "四年级",
        },
        {
          value: 5,
          label: "五年级",
        },
        {
          value: 6,
          label: "六年级",
        },
        {
          value: 7,
          label: "初一",
        },
        {
          value: 8,
          label: "初二",
        },
        {
          value: 9,
          label: "初三",
        },
        {
          value: 10,
          label: "高一",
        },
        {
          value: 11,
          label: "高二",
        },
        {
          value: 12,
          label: "高三",
        },
        {
          value: 13,
          label: "大一",
        },
        {
          value: 14,
          label: "大二",
        },
        {
          value: 15,
          label: "大三",
        },
        {
          value: 16,
          label: "大四",
        },
        {
          value: 17,
          label: "其他",
        },
      ],
      //课程
      option: [],
      loadingShip: false,
      selectObj: {
        pageNum: 1,
        pageSize: 10,
        name: "",
      },
      weeklist: [
        {
          value: 0,
          label: "星期一",
        },
        {
          value: 1,
          label: "星期二",
        },
        {
          value: 2,
          label: "星期三",
        },
        {
          value: 3,
          label: "星期四",
        },
        {
          value: 4,
          label: "星期五",
        },
        {
          value: 5,
          label: "星期六",
        },
        {
          value: 6,
          label: "星期天",
        },
      ],

      rules: {
        studentName: {
          required: true,
          message: "请输入学员姓名",
          trigger: "blur",
        },
        studentCode: {
          required: true,
          message: "请输入学员编号",
          trigger: "blur",
        },
        mobile: { required: true, message: "请输入联系方式", trigger: "blur" },
        grade: { required: true, message: "请选择年级", trigger: "change" },
        rechargeHour: {
          required: true,
          message: "请输入充值复习包",
          trigger: "blur",
        },
        reviewWeek: {
          type: "array",
          required: true,
          message: "请选择复习星期",
          trigger: "change",
        },
        reviewTime: {
          required: true,
          message: "请选择复习时间",
          trigger: "change",
        },
      },

      currentAdmin: "",
      tipsDialogVisible: false,
    };
  },
  created() {
    this.fetchData();
    this.getCurrentAdmin();
    this.getCourseList();
  },
  watch: {
    "abutmentList.studyTimeList": {
      handler(n, o) {
        this.getFirstStudyTime();
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    getCurrentAdmin() {
      schoolList.getCurrentAdmin().then((res) => {
        this.currentAdmin = res.data;
      });
    },
    addCourse() {
      this.abutmentList.courseProject.push({
        courseName: "",
      });
    },

    deleteCourse(index) {
      this.abutmentList.courseProject.splice(index, 1);
    },
    /**
     * 下拉加载
     */
    handleLoadmore() {
      if (!this.loadingShip) {
        this.selectObj.pageNum++;
        this.getCourseList();
      }
    },

    filterValue(value) {
      console.log(value);
      this.option = [];
      this.selectObj.pageNum = 1;
      this.selectObj.name = value;
      this.getCourseList();
    },

    //重置
    rest() {
      this.$refs.dataQuery.resetFields();
      this.fetchData01();
    },
    fetchData01() {
      this.tablePage = {
        currentPage: 1,
        size: this.tablePage.size,
        totalPage: null,
        totalItems: null,
      };
      this.fetchData();
    },
    fetchData() {
      const that = this;
      that.tableLoading = true;
      that.dataQuery.pageNum = that.tablePage.currentPage;
      that.dataQuery.pageSize = that.tablePage.size;
      studentApi.selStudentReviewTimeInfoList(that.dataQuery).then((res) => {
        console.log(res);
        that.tableData = res.data.data;
        that.tableLoading = false;
        that.tablePage.totalItems = res.data.totalPage ? res.data.totalPage : 0;
        // 设置后台返回的分页参数
        // pageParamNames.forEach(name => that.$set(that.tablePage, name, parseInt(res.data[name])))
      });
    },

    getReviewDetails() {},

    getCourseList() {
      let that = this;
      let dataQuery = {
        courseCode: "",
        courseName: that.selectObj.name,
        categoryCode: "",
        bigClassCode: "",
        courseStage: "",
        courseLevel: "",
        courseType: "Sys",
        courseEdition: "",
        isEnable: "1",
      };
      courseApi
        .courseList(that.selectObj.pageNum, that.selectObj.pageSize, dataQuery)
        .then((res) => {
          console.log(res);
          that.option = that.option.concat(res.data.data);
        });
    },

    fillTableNormalData(item) {
      const that = this;
      let data = {
        id: item.id,
      };
      studentApi.getStudentReviewTimeInfoDetail(data).then((res) => {
        that.abutmentList = res.data;
        that.isSubmit = res.data.isSubmit;
        // if(this.isDisable==1){
        // 	this.comfireReviewData = {
        // 	    week: JSON.parse(_this.infolist.reviewWeek),
        // 	    startTime:_this.infolist.reviewTime
        // 	}
        // }
        that.abutmentList.reviewWeek = [];
      });
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val;
      this.fetchData();
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val;
      this.fetchData();
    },

    fillinOutside(row) {
      this.fillTableNormalData(row);
      this.dialogVisible = true;
      // this.$nextTick(()=>{
      //     this.$refs['abutmentList'].resetFields();
      // })
      setTimeout(() => {
        this.$refs["abutmentList"].clearValidate();
      }, 200);
    },

    addTime() {
      this.abutmentList.studyTimeList.push({
        usableWeek: "",
        endTime: "",
        startTime: "",
        hour: 1,
      });
    },

    deleteTime(index) {
      this.abutmentList.studyTimeList.splice(index, 1);
    },

    courseVisible(e) {
      console.log(e);
      if (e) {
        this.option = [];
        this.selectObj.pageNum = 1;
        this.selectObj.name = "";
        this.getCourseList();
      }
    },

    clickCourseName(childItem, index) {
      if (this.getRepeatCourse(childItem) !== -1) {
        this.$message.error("已选择该课程");
        this.abutmentList.courseProject[index].courseName = "";
        this.abutmentList.courseProject[index].id = "";
        return;
      }
      if (this.abutmentList.courseProject[index]) {
        this.abutmentList.courseProject.splice(index, 1, childItem);
      } else {
        this.abutmentList.courseProject.push(childItem);
      }
      console.log(this.abutmentList.courseProject);
    },

    getRepeatCourse(item) {
      for (let i = 0; i < this.abutmentList.courseProject.length; i++) {
        if (this.abutmentList.courseProject[i].id === item.id) {
          return i;
        }
      }
      return -1;
    },

    submitForm() {
      const that = this;
      that.$refs["abutmentList"].validate((valid) => {
        console.log(valid);
        if (valid) {
          let data = {
            id: that.abutmentList.id,
            orderNo: that.abutmentList.orderNo,
            studentCode: that.abutmentList.studentCode,
            merchantCode: that.abutmentList.merchantCode,
            studentName: that.abutmentList.studentName,
            reviewWeek: JSON.stringify(that.abutmentList.reviewWeek),
            reviewTime: that.getReviewTime(that.abutmentList.reviewTime),
          };
          console.log(data);
          studentApi.submitStudentReviewTime(data).then((res) => {
            // this.$refs['abutmentList'].resetFields();
            this.dialogVisible = false;
            that.fetchData01();
            this.abutmentList = {};
            this.$message.success("提交成功");
            if (!res.data) {
              that.tipsDialogVisible = true;
            }
          });
        } else {
          return false;
        }
      });
    },

    getReviewTime(time) {
      let date = new Date(time);
      let hour = date.getHours() > 9 ? date.getHours() : "0" + date.getHours();
      let minutes =
        date.getMinutes() > 9 ? date.getMinutes() : "0" + date.getMinutes();
      return `${hour}:${minutes}`;
    },

    getServerCourseProject() {
      let arr = [];
      for (let i = 0; i < this.abutmentList.courseProject.length; i++) {
        if (this.abutmentList.courseProject[i].id) {
          let data = {
            courseName: this.abutmentList.courseProject[i].courseName,
            courseId: this.abutmentList.courseProject[i].id,
          };
          arr.push(data);
        }
      }
      return arr;
    },

    getServiceStudyTimeList(list) {
      for (let i = 0; i < list.length; i++) {
        let data = list[i];
        let [startHours, startMinutes] = data.startTime.split(":").map(Number);
        let [endHours, endMinutes] = data.endTime.split(":").map(Number);
        data.usableHourEnd = endHours;
        data.usableMinuteEnd = endMinutes;
        data.usableHourStart = startHours;
        data.usableMinuteStart = startMinutes;
      }
      return list;
    },

    reviewWeekChange(val) {
      console.log(val);
    },

    studyWeekChange(e, index) {
      if (e != null) {
        if (
          this.abutmentList.studyTimeList[index].startTime &&
          this.abutmentList.studyTimeList[index].endTime
        ) {
          if (
            this.judgeAllBetween(
              this.abutmentList.studyTimeList[index].startTime,
              this.abutmentList.studyTimeList[index].endTime,
              index
            )
          ) {
            this.$message.error("选择时间段与其他时间段重叠");
            this.abutmentList.studyTimeList[index].usableWeek = "";
            return;
          }
        }
      }
    },

    changeHour(e, item, index) {
      if (item.startTime == "") {
        return;
      }
      item.endTime = this.getAutoEndTime(item.startTime, item.hour);
      if (item.startTime != "" && item.endTime != "") {
        if (this.judgeAllBetween(item.startTime, item.endTime, index)) {
          item.startTime = "";
          item.endTime = "";
          this.$message.error("选择时间段与其他时间段重叠");
        }
      }
    },

    //自动获取结束时间
    getAutoEndTime(startTime, addHour) {
      let timeParts = startTime.split(":");
      let hours = parseInt(timeParts[0], 10);
      let minutes = parseInt(timeParts[1], 10);

      hours += addHour;

      if (hours >= 24) {
        hours -= 24;
      }
      let endTime = `${hours < 10 ? "0" + hours : hours}:${
        minutes < 10 ? "0" + minutes : minutes
      }`;
      return endTime;
    },
    studyTimeChange(e, index) {
      console.log(e);
      if (e) {
        this.abutmentList.studyTimeList[index].endTime = this.getAutoEndTime(
          e,
          this.abutmentList.studyTimeList[index].hour
        );
        if (
          this.judgeAllBetween(
            this.abutmentList.studyTimeList[index].startTime,
            this.abutmentList.studyTimeList[index].endTime,
            index
          )
        ) {
          this.$message.error("选择时间段与其他时间段重叠");
          this.abutmentList.studyTimeList[index].startTime = "";
          this.abutmentList.studyTimeList[index].endTime = "";
          return;
        }
      }
    },

    judgeAllBetween(start, end, index) {
      console.log(index);
      let week = this.abutmentList.studyTimeList[index].usableWeek;
      if (week.toString().length == 0) {
        return false;
      }
      let s1 = this.getMinutesForTime(start);
      let e1 = this.getMinutesForTime(end);
      console.log(this.abutmentList.studyTimeList);
      for (let i = 0; i < this.abutmentList.studyTimeList.length; i++) {
        if (
          index == i ||
          this.abutmentList.studyTimeList[i].usableWeek != week
        ) {
          continue;
        }
        let s2 = this.getMinutesForTime(
          this.abutmentList.studyTimeList[i].startTime
        );
        let e2 = this.getMinutesForTime(
          this.abutmentList.studyTimeList[i].endTime
        );
        if (s1 <= s2 && e1 >= e2) {
          return true;
        }
        if (s1 >= s2 && e1 <= e2) {
          return true;
        }
        if (s1 <= s2 && e1 <= e2 && s2 <= e1) {
          return true;
        }
        if (s1 >= s2 && e1 >= e2 && s1 <= e2) {
          return true;
        }
      }
      return false;
    },

    getMinutesForTime(time) {
      let [hours, minutes] = time.split(":").map(Number);
      return hours * 60 + minutes;
    },

    onTestChange(val) {
      console.log(val);
      if (val == 1 && this.abutmentList.wordBase == 0) {
        this.getVocabulary();
      }
    },

    getVocabulary() {
      let that = this;
      let data = {
        studentCode: that.abutmentList.studentCode,
      };
      studentApi.getTestVocabularyet(data).then((res) => {
        console.log(res);
        that.abutmentList.wordBase = res.data.vocabulary;
      });
    },

    handleClose() {
      this.abutmentList = {};
      this.$refs["abutmentList"].resetFields();
      this.dialogVisible = false;
    },

    ////////首次上课时间///////////
    getFirstStudyTime() {
      console.log(this.abutmentList.studyTimeList.length);
      if (this.abutmentList.studyTimeList.length === 0) {
        this.abutmentList.firstWeek = "";
        this.abutmentList.firstTime = "";
        this.abutmentList.firstStudyTime = "";
        return;
      }
      let dateArr = [];
      let dateMinArr = [];
      let nowDate = dayjs().format("YYYY-MM-DD HH:mm");
      for (let i = 0; i < this.abutmentList.studyTimeList.length; i++) {
        if (this.abutmentList.studyTimeList[i].startTime == "") {
          continue;
        }
        let date = this.getChoseDataToOption(
          this.abutmentList.studyTimeList[i]
        );
        dateArr.push(date);
        let dayjs1 = dayjs(date);
        dateMinArr.push(dayjs1.diff(nowDate, "minute"));
      }
      if (dateArr.length === 0) {
        this.abutmentList.firstWeek = "";
        this.abutmentList.firstTime = "";
        this.abutmentList.firstStudyTime = "";
        return;
      }
      console.log(dateArr);
      console.log(dateMinArr);
      let needIndex = -1;
      let minMinVal = Infinity;
      for (let i = 0; i < dateMinArr.length; i++) {
        if (dateMinArr[i] > 24 * 60 && dateMinArr[i] < minMinVal) {
          needIndex = i;
          minMinVal = dateMinArr[i];
        }
      }
      console.log(dateArr);
      console.log(needIndex);
      if (needIndex != -1) {
        this.abutmentList.firstStudyTime = this.getFormatToShow(
          dateArr[needIndex]
        );
      } else {
        let minIndex = dateMinArr.indexOf(Math.min(...dateMinArr));
        let lastDate = dayjs(dateArr[minIndex]).add(7, "day");
        this.abutmentList.firstStudyTime = this.getFormatToShow(lastDate);
      }
    },

    getFormatToShow(date) {
      let str = dayjs(date).format("MM月DD日& HH:mm");
      let weekIndex = this.getLocalTypeWeek(dayjs(date).day());
      this.abutmentList.firstTime = dayjs(date).format("YYYY-MM-DD HH:mm");
      this.abutmentList.firstWeek = weekIndex;
      let allStr = str.replace("&", this.getWeekName(weekIndex));
      return allStr;
    },

    getWeekName(week) {
      for (let i = 0; i < this.weeklist.length; i++) {
        if (this.weeklist[i].value == week) {
          return this.weeklist[i].label;
        }
      }
      return "";
    },

    getChoseDataToOption(data) {
      let date = this.getDateForWeek(data.usableWeek);
      return date + " " + data.startTime;
    },

    getDateForWeek(week) {
      let nowWeek = this.getLocalTypeWeek(dayjs().day());
      let diff = week - nowWeek;
      let date = "";
      if (diff >= 0) {
        date = dayjs().add(Math.abs(diff), "day").format("YYYY-MM-DD");
      } else {
        date = dayjs().subtract(Math.abs(diff), "day").format("YYYY-MM-DD");
      }
      return date;
    },

    getLocalTypeWeek(nowWeek) {
      return (nowWeek + 6) % 7;
    },
    // 去填写上课信息对接表
    classSchedule() {
      this.$router.push({
        path: "/student/trialClassOrderList",
        // query: {

        //     }
      });
    },
  },
};
</script>

<style>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

::v-deep .el-table th {
  text-align: center;
  background-color: #6b9fff !important;
  border-right: 1px solid #fff;
  color: #ffffff;
}

.timeClass {
  border: 1px solid #dfe4ed;
  border-radius: 5px;
  background-color: #fff;
  box-sizing: border-box;
  margin-left: 20px;
}
</style>
