
import request from '@/utils/request'

export default {
  // 分页查询
  pageList(data) {
    return request({
      url: '/paper/web/paper/list',
      method: 'GET',
      params: data
    })
  },
//添加
  create(data) {
    return request({
      url: '/paper/web/paper',
      method: 'POST',
      data
    })
  },
  //编辑
  edit(data) {
    return request({
      url: '/paper/web/paper',
      method: data.id ? 'PUT':'POST',
      data
    })
  },
  detail(id) {
    return request({
      url: '/paper/web/paper',
      method: 'GET',
      params:{
        id:id
      }
    })
  },
  changeStatus(id,status){
    return request({
      url: '/paper/web/paper/changeStatus',
      method: 'POST',
      params:{
        id:id,
        status:status
      }
    })
  },
  changeSort(id,sort){
    return request({
      url: '/paper/web/paper/changeSort',
      method: 'POST',
      params:{
        id:id,
        sort:sort
      }
    })
  }
}
