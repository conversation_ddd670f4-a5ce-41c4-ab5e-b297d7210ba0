<template>
  <div class="app-container">
    <el-form :inline="true" ref="queryForm" class="container-card" label-width="110px">
      <el-row>
        <el-col :span="5">
          <!-- <el-form-item label="课程名称：">
            <el-input v-model="form.courseName"  placeholder="请输入课程名称"  clearable /> 
          </el-form-item> -->
          <el-form-item label="课程状态：">
            <el-select v-model="form.courseLearnStatus" placeholder="请选择课程状态" clearable>
              <el-option v-for="item in couseStateOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="考核状态：">
            <el-select v-model="form.courseExamStatus" placeholder="请选择考核状态" clearable>
              <el-option v-for="item in courseExamStatusOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-form-item>
          <el-button size="small" icon="el-icon-refresh" @click="resetQuery()">重置</el-button>
          <el-button size="small" icon="el-icon-search" type="primary" @click="search()">搜索</el-button>
        </el-form-item>
      </el-row>
    </el-form>
    <div class="SearchForm">
      <!-- 添加 -->
      <!-- <div class="btn-add" style="margin-bottom: 10px">
        <el-button
          size="small"
          type="primary"
          icon="el-icon-plus"
          @click="clickAdd"
          >新增</el-button
        >
      </div> -->
      <!-- <div style="margin-bottom: 20px; margin-top: 20px">
        已报名人数：{{ signUpNum }} ; 已考试人数：{{ examinedNum }} ;
        已通过人数：{{ passedNum }}
        <span style="margin-left: 40%; color: red" >
          温馨提示：课程报名之后需要再一周内完成考试，且每人只有两次机会
        </span>
      </div> -->
      <!-- 表格 -->
      <el-table class="common-table" :data="tableData" stripe border :default-sort="{ prop: 'date', order: 'descending' }">
        <!-- <el-table-column type="index" width="130" label="序号"> </el-table-column> -->
        <el-table-column prop="id" label="课程信息">
          <div class="course-info" slot-scope="scope">
            <img :src="scope.row.courseCover" alt="" class="coursePoster" />
            <div class="info">
              <p class="courseName">{{ scope.row.courseName }}</p>
              <el-tag type="success" effect="dark" size="mini" v-if="scope.row.isMustLearn">必修</el-tag>
              <el-tag type="warning" effect="dark" size="mini" v-else>选修</el-tag>
            </div>
          </div>
        </el-table-column>
        <el-table-column prop="learningStatus" label="课程状态">
          <template slot-scope="scope">
            <span>{{ scope.row.learningStatus == 2 ? '进行中' : scope.row.learningStatus == 3 ? '已完成' : '未开始' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="examStatus" label="考核状态">
          <template slot-scope="scope">
            <span v-if="scope.row.isNeedExam == 0">-</span>
            <span v-else>{{ scope.row.examStatus == 1 ? '已通过' : scope.row.examStatus == 0 ? '未通过' : '' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="startLearningTime" label="开始学习时间"></el-table-column>
        <el-table-column prop="lastExamTime" label="最近一次考试时间"></el-table-column>
        <el-table-column prop="id" label="操作">
          <template slot-scope="scope">
            <div v-if="scope.row.isNeedExam == 1 && scope.row.lastExamTime">
              <el-button type="text" v-if="scope.row.examStatus == 0" @click="continueExam(scope.row)">继续考试</el-button>
              <el-button type="text" @click="showExamRecords(scope.row)">考试记录</el-button>
            </div>
            <div v-else>-</div>
          </template>
        </el-table-column>
        <!-- <el-table-column prop="id" label="操作" width="200">
          <template slot-scope="scope">
            <el-button  type="success" size="mini"  icon="el-icon-edit-outline" @click="handleDetail(scope.row.id)" >查看</el-button>
          </template>
        </el-table-column> -->
      </el-table>
      <!-- <addCourse
        v-if="dialogFrom.visible"
        :dialog-param="dialogFrom"
        @closeDialog="closeDialog"
      ></addCourse> -->
    </div>
    <!-- 分页 -->
    <el-col :span="24" style="margin-bottom: 20px">
      <el-pagination
        :current-page="tablePage.currentPage"
        :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="tablePage.totalItems"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-col>
    <!-- 继续考试弹窗 -->
    <test-paper ref="tesPaperRefs" :dialogParam="dialogParam" @closeDialog="closeDialog"></test-paper>
    <!-- 考试结果 -->
    <el-dialog title="考试结果" :visible.sync="resultDialogVisible" width="30%" @close="confirmResult">
      <span>考试分数为：{{ handPaperInfo && handPaperInfo.examScore }},考试结果：{{ handPaperInfo && handPaperInfo.passFlag == 1 ? '通过' : '未通过' }}</span>
      <span slot="footer" class="dialog-footer">
        <!-- <el-button @click="resultDialogVisible = false">取 消</el-button> -->
        <el-button type="primary" @click="confirmResult">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
  // import addCourse from "./components/addCourse.vue";
  import TestPaper from '../courseCenter/components/testPaper.vue';
  import courseApi from '@/api/training/course';
  import examApi from '@/api/training/exam';
  export default {
    name: 'course',
    components: {
      // addCourse,
      TestPaper
    },
    filters: {
      queryByType(type, list) {
        var index = list.findIndex((item) => {
          if (item.dictValue == type) {
            return true;
          }
        });
        return list[index].dictLabel;
      }
    },
    data() {
      return {
        signUpNum: 20,
        examinedNum: 20,
        passedNum: 20,
        tableLoading: false,
        dialogFrom: {
          visible: false,
          type: 'add'
        },
        // 分页
        tablePage: {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: 0
        },
        form: {
          // courseName: "",
          // type: "全部",
          courseLearnStatus: '',
          courseExamStatus: ''
        },
        tableData: [],
        couseStateOptions: [
          { value: 2, label: '进行中' },
          { value: 3, label: '已完成' }
        ],
        courseExamStatusOptions: [
          { value: 0, label: '未通过' },
          { value: 1, label: '已通过' }
        ],
        dialogParam: {
          visible: false
        },
        trainingPaperInfo: null,
        resultDialogVisible: false, // 考试结果确认弹窗
        handPaperInfo: null,
        actionItem: null // 当前操作记录项
      };
    },
    created() {
      this.getRecordPage();
      this.getQueryByType();
    },
    methods: {
      // 分页
      handleSizeChange(val) {
        this.tablePage.size = val;
        this.getRecordPage();
      },
      getQueryByType() {
        courseApi.dictQueryByType({ dictType: 'paper_course_category' }).then((res) => {
          this.courseType = res.data;
        });
      },
      getRecordPage() {
        let sysUserRoles = JSON.parse(window.localStorage.getItem('sysUserRoles'));
        // let roleList=[]
        // sysUserRoles.forEach(element => {
        //   roleList.push(element.roleId)
        // });
        let param = { pageSize: this.tablePage.size, pageNum: this.tablePage.currentPage };
        param.userId = sysUserRoles[0].userId;
        param = { ...this.form, ...param };
        courseApi.trainingRecordPage(param).then((res) => {
          this.tableData = res.data.data;
          this.tablePage.totalItems = Number(res.data.totalItems);
        });
      },
      handleCurrentChange(val) {
        this.tablePage.currentPage = val;
        this.getRecordPage();
      },
      // 重置
      resetQuery() {
        this.tablePage.currentPage = 1;
        this.form.courseLearnStatus = '';
        this.form.courseExamStatus = '';
        this.getRecordPage();
      },
      // 搜索
      search() {
        this.tablePage.currentPage = 1;
        this.getRecordPage();
      },
      // 考试记录
      showExamRecords(item) {
        this.$router.push({
          path: '/trainingCenter/examRecords',
          query: { courseId: item.id }
        });
      },
      // 继续考试
      continueExam(item) {
        this.actionItem = item;
        const courseId = item.id;
        this.getPaper(courseId);
      },
      // 获取试卷信息
      getPaper(courseId) {
        let sysUserRoles = JSON.parse(window.localStorage.getItem('sysUserRoles'));
        const params = {
          courseId,
          userId: sysUserRoles[0].userId
        };
        this.trainingPaperInfo = null;
        examApi.trainingPaper(params).then((res) => {
          this.trainingPaperInfo = res.data;
          // 打开考试界面
          this.dialogParam.visible = true;
          this.$nextTick(() => {
            this.$refs.tesPaperRefs.open(this.trainingPaperInfo, courseId);
          });
        });
      },
      closeDialog(data) {
        this.dialogParam.visible = false;
        // 考试结果
        if (data && data instanceof Object) {
          this.handPaperInfo = data;
        }
        setTimeout(() => {
          this.resultDialogVisible = true;
        }, 400);
      },
      // 考试结果确认
      confirmResult() {
        this.resultDialogVisible = false;
        this.search();
        this.showExamRecords(this.actionItem);
      }
    }
  };
</script>

<style>
  .SearchForm {
    width: 100%;
    margin-bottom: 15px;
    background: #fff;
    border-radius: 6px;
    padding: 5px 0;
    padding-left: 5px;
    box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.2);
  }
  .container-card {
    padding: 15px 15px 15px 15px;
    margin-bottom: 15px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  }

  .period-table td,
  .period-table th {
    text-align: center;
  }

  @media (max-width: 767px) {
    .el-message-box {
      width: 80% !important;
    }
  }

  .el-tooltip__popper {
    max-width: 800px;
  }

  .course-info {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    text-align: left;
  }
  .coursePoster {
    width: 80px;
    height: 60px;
    object-fit: contain;
    margin-right: 20px;
  }
  .courseName {
    margin: 0;
    padding: 0;
  }
</style>
