<template>
  <div class="app-container">
    <el-form :inline="true" class="container-card" label-width="90px" label-position="left">
      <el-row>
        <el-col :span="5" :xs="24">
          <el-form-item label="门店编号：">
            <el-input v-model="dataQuery.merchantCode" placeholder="请输入门店编号" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="5" :xs="24">
          <el-form-item label="学员编号：">
            <el-input v-model="dataQuery.studentCode" placeholder="请输入学员编号" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="5" :xs="24">
          <el-form-item label="登录账号：">
            <el-input v-model="dataQuery.loginName" placeholder="请输入登录账号" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="5" :xs="24">
          <el-form-item label="姓名：">
            <el-input v-model="dataQuery.realName" placeholder="请输入姓名" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="4" :xs="24">
          <el-form-item style="margin-left: 50px;">
            <el-button type="primary" icon="el-icon-search" @click="fetchData01()">搜索</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-table class="common-table" v-loading="tableLoading" :data="tableData" style="width: 100%;margin-bottom: 20px;"
      row-key="id" border default-expand-all :tree-props="{ list: 'children', hasChildren: 'true' }">
      <el-table-column prop="merchantCode" label="门店编号" width="120"></el-table-column>
      <el-table-column prop="studentCode" label="学员编号" width="120"></el-table-column>
      <el-table-column prop="withdrawnBonus" label="操作" width="180">
        <template slot-scope="scope">
          <el-button type="success" size="mini" icon="el-icon-edit-outline"
            @click="enterChildrenList(scope.row.studentCode)">查看进度</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="loginName" label="登录账号" width="120"></el-table-column>
      <el-table-column prop="realName" label="姓名" width="120"></el-table-column>
      <el-table-column prop="gradeName" label="年级" width="120"></el-table-column>
      <el-table-column prop="school" label="学校" width="120"></el-table-column>
      <el-table-column prop="totalCourseHours" label="已购学时（节）"></el-table-column>
      <el-table-column prop="haveCourseHours" label="剩余学时（节）"></el-table-column>

    </el-table>
    <!-- 分页 -->
    <el-col :span="24" style="overflow-x: auto;" :xs="24">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>
  </div>
</template>

<script>
import studentRecordApi from "@/api/studentCourseRecord";
import Tinymce from "@/components/Tinymce";
import { pageParamNames } from "@/utils/constants";
import ls from '@/api/sessionStorage'

export default {
  data() {
    return {
      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      tableData: [],
      dataQuery: {
        studentCode: '',
        loginName: '',
        merchantCode: '',
        realName: ''
      },
    }
  },
  created() {
    this.fetchData();
    // 获取上个页面的学员编号
    this.dataQuery.studentCode = ls.getItem('studentCode')

  },
  methods: {
    fetchData01() {
      this.tablePage = {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      }
      this.fetchData();
    },
    // 查询列表
    fetchData() {
      const that = this;
      that.tableLoading = true
      studentRecordApi.studentRecord(that.tablePage.currentPage, that.tablePage.size, that.dataQuery).then(res => {
        that.tableData = res.data.data
        that.tableLoading = false
        // 设置后台返回的分页参数
        pageParamNames.forEach(name => that.$set(that.tablePage, name, parseInt(res.data[name])))
      })
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val;
      this.fetchData();
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val;
      this.fetchData();
    },
    //查看进度
    enterChildrenList(studentCode) {
      ls.setItem('courseStudentCode', studentCode);
      const that = this;
      that.$router.push({
        path: "/student/studentCourseProgress",
        query: {
          studentCode: studentCode
        }
      });
    },
  }
};
</script>

<style>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.period-table td,
.period-table th {
  text-align: center;
}

.mt20 {
  margin-top: 20px;
}

.red {
  color: red;
}

.green {
  color: green;
}</style>
