/**
 * 推广人相关接口
 */
import request from '@/utils/request'

export default {
  // 分页查询
  promoterList(pageNum, pageSize, data) {
    return request({
      url: '/znyy/promoter/list/' + pageNum + '/' + pageSize,
      method: 'GET',
      params: data
    })
  },
  // 导出
  agentExport(listQuery) {
    return request({
      url: '/znyy/agent/execle',
      method: 'GET',
      responseType: 'blob',
      params:listQuery
    })
  },
  // 导出
  promoterExport(listQuery) {
    return request({
      url: '/znyy/promoter/execle',
      method: 'GET',
      responseType: 'blob',
      params:listQuery
    })
  },

  // 开通与暂停
  updateStatus(id, status) {
    return request({
      url: '/znyy/promoter/update/isEnable?id=' + id + '&isEnable=' + status,
      method: 'PUT'
    })
  },
  // 审核
  examine(id,checkReason,isCheck){
    return request({
      url: '/znyy/promoter/update/isCheck?id=' + id + '&checkReason=' + checkReason + '&isCheck=' + isCheck ,
      method: 'PUT'
    })
  }
}
