<template>
  <div class="app-container paper-edit-contain" >
    <div class="left">
      <el-card class="box-card el-card-define" style="height: 600px">
        <div slot="header" class="clearfix">
          <el-select v-model="dataQuery.gradeId" placeholder="学段" @change="handleChange" clearable>
            <el-option v-for="item in gradeList" :key="item.value" :value="item.value" :label="item.label"></el-option>
          </el-select>
        </div>
        <el-table
          :data="tableData"
          highlight-current-row
          @current-change="handleSelect"
          :show-header="false"
          style="width: 100%">
          <el-table-column
            prop="paperName"
            width="200">
          </el-table-column>
        </el-table>
        <!-- 分页 -->
        <el-col :span="24" style="margin-bottom: 20px">
          <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10]"
                         layout="total, sizes, prev, pager, next, jumper"
                         :total="tablePage.totalItems"  @current-change="handleCurrentChange"
          />
        </el-col>
      </el-card>
    </div>
    <div class="right">
      <el-card style="height: 600px;overflow:auto">
        <div style="padding-bottom: 20px" v-for="item in dataList">
          <span>{{item.scopeMin}}-{{item.scopeMax}}</span>
          <el-card :body-style="{ height:'140px',overflow:'auto' }" style="height: 30%;overflow:auto">
            <div v-html="item.content"></div>
          </el-card>
          <el-button size="mini" type="success" @click="useTemplate(item.content)"> 使用该模板</el-button>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script>

import { mapState } from 'vuex'
import Pagination from '@/components/Pagination'
import reportApi from '@/api/paper/report'
import { pageParamNames } from '@/utils/constants'


export default {
  name: 'ReportTemplate',
  components: { Pagination},
  props: {
    qType: {
      type: String,
      default: "SCORE",
    },
  },
  data () {
    return {
      dataList:[],
      listLoading:false,
      dataQuery:{
        type:this.qType,
        gradeId:null
      },
      tableData: [],
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
    }
  },
  created () {
    this.getPageList();
  },
  methods:{
    useTemplate(val){
      this.$emit("useTemplate", val || "");
      this.$emit("handleClose", false);
    },
    handleSelect(val){
      this.dataList = val.dataList;
    },
    getPageList(){
      this.listLoading = true
      this.dataQuery.pageNum = this.tablePage.currentPage;
      this.dataQuery.pageSize = this.tablePage.size;
      reportApi.refer(this.dataQuery).then(res => {
        this.tableData = res.data.data;
        if (this.tableData && this.tableData.length>0){
          this.dataList = this.tableData[0].dataList;
        }else {
          this.dataList = [];
        }
        this.listLoading = false
        // 设置后台返回的分页参数
        pageParamNames.forEach(name => this.$set(this.tablePage, name, parseInt(res.data[name])))
      })
    },

    handleChange(val){
      this.type = val;
      this.getPageList();
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val
      this.getPageList()
    },
  },
  computed: {
    ...mapState('enumItem', {
      // gradeList: state => state.question.gradeList,
    }),
  }
}
</script>

<style lang="scss">
.paper-edit-contain .left {
  width: 40%;
  margin-right: 20px;
}
.paper-edit-contain .right {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
}
.paper-edit-contain {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
}
</style>
