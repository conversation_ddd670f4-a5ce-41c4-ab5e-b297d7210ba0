<template>
  <div class="app-container">
    <el-form :model="dataQuery" ref="queryForm" :inline="true">
      <el-form-item label="课程ID：" prop="id">
        <el-input
          v-model="dataQuery.id"
          @keydown.native="handleNumberInputE"
          @change="handleChangeNumber"
          type="number"
          clearable
          placeholder="请输入课程ID"
          class="id-input"
        ></el-input>
      </el-form-item>
      <el-form-item label="课程名称：" prop="courseName">
        <el-input v-model="dataQuery.courseName" clearable placeholder="请输入课程名称" v-trim></el-input>
      </el-form-item>
      <el-form-item label="课程类型：" prop="courseType">
        <el-select v-model="dataQuery.courseType" clearable multiple>
          <el-option v-for="item in courseTypeList" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="课程大类：" prop="curriculumId">
        <el-select v-model="dataQuery.curriculumId" @change="handleCourseCategoryQueryChange" clearable>
          <el-option v-for="item in courseCategoryList" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="学段：" prop="gradeLevel">
        <el-select v-model="dataQuery.gradeLevel" clearable>
          <el-option v-for="item in dataQueryDict.gradeList" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="queryData">查询</el-button>
        <el-button type="primary" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-col :span="24" style="margin-bottom: 30px">
      <el-button type="success" icon="el-icon-plus" size="mini" @click="handleAdd()">新增</el-button>
    </el-col>
    <el-table class="common-table" v-loading="tableLoading" :data="tableData" style="width: 100%; margin-bottom: 20px" row-key="id" border default-expand-all>
      <el-table-column type="index" label="序号" />
      <el-table-column prop="id" label="课程ID" />
      <el-table-column prop="courseName" label="课程名称" show-overflow-tooltip />
      <el-table-column prop="curriculumName" label="课程大类" width="326" show-overflow-tooltip />
      <el-table-column prop="courseType" label="课程类型" :formatter="courseTypeName" />
      <el-table-column prop="gradeLevel" label="学段" width="120" :formatter="gradeLevelName" />
      <el-table-column prop="sortNo" label="排序号" width="120" />
      <el-table-column prop="createTime" label="创建时间" />
      <el-table-column prop="withdrawnBonus" label="操作" width="360">
        <template slot-scope="scope">
          <el-button type="success" size="mini" icon="el-icon-edit-outline" @click="handleUpdate(scope.row)">编辑</el-button>
          <el-button type="success" size="mini" icon="el-icon-edit-outline" @click="handleVideoConfig(scope.row)">课程视频管理</el-button>
          <el-button type="danger" size="mini" icon="el-icon-delete" @click="handleDelete(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <el-col :span="24" style="margin-bottom: 20px">
      <el-pagination
        :current-page="tablePage.currentPage"
        :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="tablePage.totalItems"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-col>

    <!-- 修改或者新增弹窗 -->
    <el-dialog :title="isEdit ? '编辑课程' : '添加课程'" :class="{ 'edit-mode': isEdit }" :visible.sync="open" width="60%" @close="cancel" :close-on-click-modal="false">
      <el-form ref="form" :model="form" :rules="rules" label-width="120px" style="width: 100%">
        <el-form-item label="课程名称：" prop="courseName">
          <el-input v-model="form.courseName" type="textarea" maxlength="50" show-word-limit placeholder="请输入" />
        </el-form-item>
        <el-form-item label="课程类型：" prop="courseType">
          <el-select :key="'form-courseType-' + formItemKey" v-model="form.courseType" :disabled="isEdit" multiple style="width: 100%">
            <el-option v-for="item in courseTypeList" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="课程大类：" prop="curriculumId">
          <el-select
            style="width: 100%"
            :key="'form-curriculumId-' + formItemKey"
            v-model="form.curriculumId"
            @change="handleCourseCategoryFormChange"
            multiple
            :multiple-limit="5"
          >
            <el-option v-for="item in courseCategoryList" :key="item.value" :label="item.label" :value="item.value" :disabled="item.disabled"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item ref="formGradeLevel" label="选择学段：" prop="gradeLevel">
          <el-select :key="'form-gradeLevel-' + formItemKey" v-model="form.gradeLevel" @focus="handleGradeFormFocus(form.curriculumId)" :disabled="isEdit">
            <el-option v-for="item in formDict.gradeList" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="排序：" prop="sortNo">
          <el-input-number v-model="form.sortNo" controls-position="right" :min="1" :step="1" step-strictly />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" v-loading="saveLoading" @click="submitForm">{{ isEdit ? '确 定' : '下一步' }}</el-button>
      </div>
    </el-dialog>
    <CourseAddVideoDialog :visible.sync="addVideoOpen" :add-video-info="addVideoInfo" @submitFormSuccess="handleAddSubmitForm" />
  </div>
</template>

<script>
  import courseApi from '@/api/studyExamPassed/course';
  import { pageParamNames } from '@/utils/constants';
  import CourseAddVideoDialog from './components/CourseAddVideoDialog.vue';
  import { mapGetters } from 'vuex';
  import ls from '@/api/sessionStorage';
  export default {
    name: 'Course',
    components: {
      CourseAddVideoDialog
    },
    data() {
      return {
        dataQuery: {
          id: '',
          courseName: '',
          curriculumId: '',
          gradeLevel: [],
          courseType: null
        },

        dataQueryDict: {
          gradeList: []
        },
        formDict: {
          gradeList: []
        },

        tableLoading: false,

        // 分页
        tablePage: {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null
        },

        tableData: [],

        open: false,
        isEdit: false,

        courseTypeList: [
          { label: '正式课程', value: '1' },
          { label: '试课课程', value: '2' }
        ],
        courseCategoryList: [],
        gradeList: [],

        formItemKey: 0,

        form: {},

        // 表单校验
        rules: {
          courseName: [{ required: true, message: '请输入名称', trigger: 'blur' }],
          curriculumId: [{ required: true, message: '请选择', trigger: 'change' }],
          gradeLevel: [{ required: true, message: '请选择', trigger: 'change' }],
          courseType: [{ required: true, message: '请选择', trigger: 'change' }],
          sortNo: [{ required: true, message: '请输入', trigger: 'blur' }]
        },
        saveLoading: false,

        addVideoOpen: false,
        addVideoInfo: {
          courseId: undefined,
          curriculumId: undefined,
          gradeLevel: undefined
        },
        originalCourseCategoryList: []
      };
    },
    computed: {
      ...mapGetters('enumItem', ['enumFormat'])
    },
    created() {
      this.initCourseCategoryList();
      this.initGradeList();
      this.getList();
    },
    methods: {
      initCourseCategoryList() {
        courseApi.getCourseCategory().then((res) => {
          this.courseCategoryList = this.originalCourseCategoryList = res.data.map((item) => {
            return { label: item.enName, value: item.id };
          });
        });
      },
      initGradeList() {
        courseApi.getGradeAndSubjectList().then((res) => {
          this.dataQueryDict.gradeList = res.data;
          this.gradeList = res.data;
        });
      },
      handleCourseCategoryQueryChange(curriculumId) {
        courseApi.getGradeAndSubjectList(curriculumId).then((res) => {
          this.dataQueryDict.gradeList = res.data;
        });
        this.dataQueryDict.gradeList = [];
        this.dataQuery.gradeLevel = '';
      },
      //分页查询
      getList() {
        this.tableLoading = true;
        this.dataQuery.pageNum = this.tablePage.currentPage;
        this.dataQuery.pageSize = this.tablePage.size;
        let dataQueryTemp = JSON.parse(JSON.stringify(this.dataQuery));
        if (dataQueryTemp.courseType?.length && dataQueryTemp.courseType.length > 1) {
          dataQueryTemp.courseType = 3;
        } else if (dataQueryTemp.courseType?.length && dataQueryTemp.courseType.length == 1) {
          dataQueryTemp.courseType = dataQueryTemp.courseType[0];
        }
        courseApi.courseList(dataQueryTemp).then((res) => {
          this.tableData = res.data.data;
          this.tableLoading = false;

          // 设置后台返回的分页参数
          pageParamNames.forEach((name) => this.$set(this.tablePage, name, parseInt(res.data[name])));
        });
      },
      queryData() {
        this.tablePage.currentPage = 1;
        this.getList();
      },
      resetQuery() {
        this.tablePage.currentPage = 1;
        this.dataQuery = {
          id: '',
          courseName: '',
          gradeLevel: '',
          courseType: null
        };
        this.handleCourseCategoryQueryChange();
        this.getList();
      },

      handleUpdate(row) {
        const { id, curriculumId } = row;
        this.resetForm();
        const promiseArry = [];
        promiseArry.push(courseApi.getGradeAndSubjectList(curriculumId));
        promiseArry.push(courseApi.courseDetail(id));
        Promise.all(promiseArry).then((res) => {
          this.formDict.gradeList = res[0].data;
          this.form = res[1].data;

          let curriculumIdValue = this.form.curriculumId;

          if (typeof curriculumIdValue === 'string') {
            // 如果是逗号拼接的字符串，拆分成数组
            this.form.curriculumId = curriculumIdValue.split(',').map((id) => id.trim());
          } else if (!Array.isArray(curriculumIdValue)) {
            // 如果不是数组也不是字符串，转成数组
            this.form.curriculumId = [curriculumIdValue + ''];
          }
          this.courseCategoryList = this.courseCategoryList.map((item) => ({
            ...item,
            disabled: this.form.curriculumId.some((id) => String(id) === String(item.value))
          }));
          console.log('我是form', this.form);
          console.log('我是课程大类', this.courseCategoryList);

          this.form.courseType = this.form.courseType == 3 ? ['1', '2'] : [this.form.courseType];
          this.isEdit = true;
          this.open = true;
        });
      },
      // 新增修改时,课程大类选择后，根据课程大类获取学段
      handleCourseCategoryFormChange(curriculumId) {
        // ✅ 如果是编辑模式，直接返回，不执行后续逻辑
        if (this.isEdit) {
          return;
        }

        // 🔁 新增模式下的逻辑
        if (curriculumId) {
          courseApi.getGradeAndSubjectList(curriculumId).then((res) => {
            this.formDict.gradeList = res.data;
          });
        }

        // 📌 清空学段数据和校验
        this.formDict.gradeList = [];
        this.form.gradeLevel = '';
        setTimeout(() => {
          this.$refs['formGradeLevel']?.clearValidate();
        });
      },
      handleGradeFormFocus(curriculumId) {
        curriculumId || this.$message.warning('请先选择课程大类');
      },
      submitForm() {
        this.$refs['form'].validate((valid) => {
          if (valid) {
            let fn = this.isEdit ? courseApi.updateCourse : courseApi.addCourse;
            let formData = JSON.parse(JSON.stringify(this.form));
            if (formData.courseType?.length && formData.courseType.length > 1) {
              formData.courseType = 3;
            } else if (formData.courseType?.length && formData.courseType.length == 1) {
              formData.courseType = formData.courseType[0];
            }
            fn(formData).then((res) => {
              this.$message.success(this.isEdit ? '修改课程成功！' : '添加课程成功！');
              this.open = false;
              this.getList();
              if (!this.isEdit) {
                this.addVideoInfo.gradeLevel = this.form.gradeLevel;
                this.addVideoInfo.curriculumId = this.form.curriculumId;
                this.addVideoInfo.courseId = res.data;
                this.addVideoOpen = true;
              }
            });
          } else {
            this.$message.error('请按规范填写数据');
          }
        });
      },
      handleAddSubmitForm() {
        this.getList();
      },
      handleAdd() {
        this.isEdit = false;
        this.open = true;
        this.formItemKey++;
        this.formDict.gradeList = [];
        this.resetForm();
        // 恢复原始数据（深拷贝）
        this.courseCategoryList = JSON.parse(JSON.stringify(this.originalCourseCategoryList));
      },

      // 取消按钮
      cancel() {
        this.isEdit && this.open && this.$message.info('已取消修改课程');
        this.open = false;
      },

      // 表单重置
      resetForm() {
        this.form = {
          courseName: '',
          courseType: [],
          curriculumId: [],
          gradeLevel: '',
          sortNo: ''
        };
        this.$refs['form']?.clearValidate();
        this.$refs['form']?.clearValidate(['courseType']);
      },

      handleVideoConfig(row) {
        const { id, gradeLevel, courseType, curriculumId } = row;
        ls.setItem('courseVideoInfo', { id, gradeLevel, courseType, curriculumId });
        this.$router.push({ path: '/studyExamPassed/courseVideoConfig' });
      },

      // 分页
      handleSizeChange(val) {
        this.tablePage.size = val;
        this.tablePage.currentPage = 1;
        this.getList();
      },
      handleCurrentChange(val) {
        this.tablePage.currentPage = val;
        this.getList();
      },
      handleDelete(id) {
        this.$confirm(`您确定删除课程吗？`, '删除', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            courseApi.courseDelete(id).then(() => {
              this.$message.success(`删除课程成功`);
              this.getList();
            });
          })
          .catch(() => {
            this.$message.info(`已取消删除课程`);
          });
      },
      courseTypeName(row, column, cellValue, index) {
        let tempList = [
          { label: '正式课程', value: '1' },
          { label: '试课课程', value: '2' },
          { label: '正式课程,试课课程', value: '3' }
        ];
        return this.enumFormat(tempList, cellValue) || '无';
      },
      gradeLevelName(row, column, cellValue, index) {
        return this.enumFormat(this.gradeList, cellValue) || '无';
      },
      handleNumberInputE(event) {
        if (event.key == 'e' || event.key == 'E') {
          event.returnValue = false;
          return false;
        }
        return true;
      },

      // id输入框输入数字时，限制小于20位，且不允许输入非数字
      handleChangeNumber() {
        if (this.dataQuery.id) {
          let newValue = '';
          if (this.dataQuery.id.length > 19) {
            newValue = this.dataQuery.id.substring(0, 19);
          } else if (/[^\d]/g.test(this.dataQuery.id)) {
            newValue = this.dataQuery.id.replaceAll(/[^\d]/g, '');
          }
          newValue && this.$set(this.dataQuery, 'id', newValue);
        }
      }
    }
  };
</script>

<style lang="less" scoped>
  /* 取消[type='number']的input的上下箭头 */
  /deep/.id-input input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
  }

  /deep/.id-input input::-webkit-outer-spin-button {
    -webkit-appearance: none !important;
  }

  /deep/.id-input input[type='number'] {
    -moz-appearance: textfield;
    appearance: none;
  }
  .edit-mode ::v-deep .el-tag.el-tag--info .el-tag__close {
    display: none;
  }
</style>
