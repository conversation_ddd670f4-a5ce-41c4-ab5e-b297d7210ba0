<template>
  <div class="app-container">
    <!-- 搜索 -->
    <el-form :inline="true" ref="form" class="SearchForm" style="padding: 20px 30px 0">
      <el-row>
        <el-col :span="8" :xs="24">
          <el-form-item label="课程编号:">
            <el-input v-model="dataQuery.courseCode" @keyup.enter.native="fetchData()" style="width: 200px" placeholder="请输入课程编号：" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="课程名称:">
            <el-input v-model="dataQuery.courseName" @keyup.enter.native="fetchData()" style="width: 200px" placeholder="请输入课程名称：" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="课程分类:">
            <el-select v-model="dataQuery.categoryCode" filterable value-key="value" placeholder="请选择" @change="check(dataQuery.categoryCode)" clearable>
              <el-option v-for="(item, index) in categoryType" :key="index" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8" :xs="24">
          <el-form-item label="课程类型:">
            <el-select v-model="dataQuery.bigClassCode" filterable value-key="value" placeholder="请选择" clearable>
              <el-option v-for="(item, index) in bigClassType" :key="index" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="课程学段:">
            <el-select v-model="dataQuery.courseStage" filterable value-key="value" placeholder="请选择" clearable>
              <el-option v-for="(item, index) in courseStageType" :key="index" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="课程等级:">
            <el-select v-model="dataQuery.courseLevel" filterable value-key="value" placeholder="请选择" clearable>
              <el-option v-for="(item, index) in courseLevelType" :key="index" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8" :xs="24">
          <el-form-item label="课程类型:">
            <el-select v-model="dataQuery.courseType" filterable value-key="value" placeholder="请选择" clearable>
              <el-option v-for="(item, index) in courseTypes" :key="index" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="教材版本:">
            <el-select v-model="dataQuery.courseEdition" filterable value-key="value" placeholder="请选择" clearable>
              <el-option v-for="(item, index) in courseEditionType" :key="index" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="课程状态:">
            <el-select v-model="dataQuery.isEnable" filterable value-key="value" placeholder="请选择" clearable>
              <el-option
                v-for="(item, index) in [
                  { value: 1, label: '开通' },
                  { value: 0, label: '暂停' }
                ]"
                :key="index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="16">
          <el-form-item label="添加时间:">
            <el-date-picker
              style="width: 100%"
              v-model="RegTime"
              type="daterange"
              value-format="yyyy-MM-dd HH:mm:ss"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              clearable
            ></el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item>
            <el-button type="warning" size="small" icon="el-icon-search" @click="fetchData01()">搜索</el-button>
            <el-button size="small" icon="el-icon-refresh" @click="rest()">重置</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <div class="SearchForm">
      <!-- 添加 -->
      <div class="btn-add">
        <el-button size="small" type="primary" icon="el-icon-plus" @click="clickAdd">添加</el-button>
        <el-button size="small" type="primary" icon="el-icon-plus" @click="wordReplace()">单词发音替换</el-button>
      </div>
      <!-- 表格 -->
      <el-table class="common-table" :data="tableData" stripe border :default-sort="{ prop: 'date', order: 'descending' }" v-loading="tableLoading">
        <el-table-column prop="courseCode" label="课程编号" width="140" sortable></el-table-column>
        <el-table-column prop="courseName" label="课程名称" width="180" sortable show-overflow-tooltip></el-table-column>
        <el-table-column prop="id" label="操作" sortable width="400">
          <template slot-scope="scope">
            <el-button type="success" size="mini" icon="el-icon-edit-outline" v-if="scope.row.roleTag === role || roleName === true" @click="handleUpdate(scope.row.id)">
              编辑
            </el-button>
            <el-button
              type="success"
              size="mini"
              icon="el-icon-edit-outline"
              v-if="scope.row.roleTag === role || roleName === true"
              @click="enterChildrenList(scope.row.courseCode, scope.row.courseContentType, scope.row.courseName)"
            >
              制作课程
            </el-button>
            <el-button
              type="warning"
              size="mini"
              icon="el-icon-switch-button"
              v-if="scope.row.isEnable === 0 && (scope.row.roleTag === role || roleName === true)"
              @click="courseStatus(scope.row.id, scope.row.isEnable)"
            >
              开通
            </el-button>
            <el-button
              type="danger"
              size="mini"
              icon="el-icon-video-pause"
              v-if="scope.row.isEnable === 1 && (scope.row.roleTag === role || roleName === true)"
              @click="courseStatus(scope.row.id, scope.row.isEnable)"
            >
              暂停
            </el-button>
            <el-button
              type="warning"
              icon="el-icon-download"
              size="mini"
              v-if="
                scope.row.isEnable === 1 &&
                (scope.row.roleTag === role || roleName === true) &&
                (scope.row.bigClassName == '单词' || scope.row.bigClassName == 'AMC冲外常用词汇' || scope.row.bigClassName == '词组' || scope.row.bigClassName == '考研词汇')
              "
              @click="exportExcel(scope.row.courseCode, scope.row.courseName, scope.row.courseLevelName, scope.row)"
            >
              导出
            </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="courseCover" label="课程封面" width="120" sortable>
          <template slot-scope="scope">
            <div class="demo-image__preview">
              <el-image v-if="scope.row.courseCover" class="table_list_pic" :src="scope.row.courseCover" @click="openImg(scope.row)"></el-image>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="categoryName" label="所属分类" width="110" sortable></el-table-column>
        <el-table-column prop="bigClassName" label="课程类型" width="110" sortable></el-table-column>
        <el-table-column prop="coursStageName" label="课程学段" width="110" sortable></el-table-column>
        <el-table-column prop="courseLevelName" label="课程等级" width="110" sortable></el-table-column>
        <el-table-column prop="courseTypeName" label="课程类型" width="110" sortable></el-table-column>
        <el-table-column prop="courseEditionName" label="教材版本" width="110" sortable></el-table-column>
        <el-table-column prop="courseContentTypeName" label="题目类型" width="110" sortable></el-table-column>
        <el-table-column prop="wordOrder" label="出词顺序" width="110" sortable>
          <template slot-scope="scope">
            <span>{{ scope.row.wordOrder == 0 ? '随机' : '顺序' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="wordCount" label="单词数" width="100" sortable></el-table-column>
        <!-- <el-table-column prop="courseUnitPrice" label="课程报名价" width="120" sortable></el-table-column> -->
        <!-- <el-table-column prop="courseMemberDiscount" label="会员折扣" width="110" sortable></el-table-column> -->
        <el-table-column prop="city" label="城市" width="110" sortable></el-table-column>
        <el-table-column prop="merchantName" label="上传人" width="110" sortable></el-table-column>
        <!--        <el-table-column prop="roleTag" label="角色" width="110" sortable></el-table-column>-->
        <el-table-column prop="addTime" label="添加时间" width="180" sortable :show-overflow-tooltip="true"></el-table-column>
        <el-table-column prop="modifyTime" label="最后编辑时间" width="180" sortable :show-overflow-tooltip="true"></el-table-column>
        <el-table-column prop="isEnable" label="状态" sortable>
          <template slot-scope="scope">
            <span class="green" v-if="scope.row.isEnable === 1">开通</span>
            <span class="red" v-else>暂停</span>
          </template>
        </el-table-column>
        <el-table-column prop="isDisabled" label="禁用" sortable>
          <template slot-scope="scope">
            <span class="red" v-if="scope.row.isDisabled == 1">是</span>
            <span class="green" v-else>否</span>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <el-col :span="20">
      <el-pagination
        :current-page="tablePage.currentPage"
        :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="tablePage.totalItems"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :page-size="tablePage.size"
      />
    </el-col>

    <!-- 编辑或者添加弹窗 -->
    <el-dialog :title="addOrUpdate ? '添加课程分类' : '编辑课程分类'" :visible.sync="dialogVisible" width="70%" :close-on-click-modal="false" @close="close">
      <el-form
        :ref="addOrUpdate ? 'addCourseData' : 'updateCourseData'"
        :rules="rules"
        :model="addOrUpdate ? addCourseData : updateCourseData"
        label-position="left"
        label-width="120px"
        style="width: 100%"
      >
        <el-form-item label="课程分类" prop="categoryCode">
          <el-col :xs="24" :lg="18">
            <el-select
              style="width: 100%"
              v-if="addOrUpdate"
              v-model="addCourseData.categoryCode"
              filterable
              value-key="value"
              placeholder="请选择"
              @change="check(addCourseData.categoryCode)"
            >
              <el-option v-for="(item, index) in categoryType" :key="index" :label="item.label" :value="item.value" />
            </el-select>
            <el-select style="width: 100%" v-if="!addOrUpdate" v-model="updateCourseData.categoryCode" filterable value-key="value" placeholder="请选择">
              <el-option v-for="(item, index) in categoryType" :key="index" :label="item.label" :value="item.value" />
            </el-select>
          </el-col>
        </el-form-item>
        <el-form-item label="课程类型" prop="bigClassCode">
          <el-col :xs="24" :lg="18">
            <el-select style="width: 100%" v-if="addOrUpdate" v-model="addCourseData.bigClassCode" filterable value-key="value" placeholder="请选择">
              <el-option v-for="(item, index) in bigClassType" :key="index" :label="item.label" :value="item.value" />
            </el-select>
            <el-select style="width: 100%" v-if="!addOrUpdate" v-model="updateCourseData.bigClassCode" filterable value-key="value" placeholder="请选择">
              <el-option v-for="(item, index) in bigClassType" :key="index" :label="item.label" :value="item.value" />
            </el-select>
          </el-col>
        </el-form-item>
        <el-form-item label="课程学段" prop="courseStage">
          <el-col :xs="24" :lg="18">
            <el-select style="width: 100%" v-if="addOrUpdate" v-model="addCourseData.courseStage" filterable value-key="value" placeholder="请选择">
              <el-option v-for="(item, index) in courseStageType" :key="index" :label="item.label" :value="item.value" />
            </el-select>
            <el-select style="width: 100%" v-if="!addOrUpdate" v-model="updateCourseData.courseStage" filterable value-key="value" placeholder="请选择">
              <el-option v-for="(item, index) in courseStageType" :key="index" :label="item.label" :value="item.value" />
            </el-select>
          </el-col>
        </el-form-item>
        <el-form-item label="课程等级" prop="courseLevel">
          <el-col :xs="24" :lg="18">
            <el-select style="width: 100%" v-if="addOrUpdate" v-model="addCourseData.courseLevel" filterable value-key="value" placeholder="请选择">
              <el-option v-for="(item, index) in courseLevelType" :key="index" :label="item.label" :value="item.value" />
            </el-select>
            <el-select style="width: 100%" v-if="!addOrUpdate" v-model="updateCourseData.courseLevel" filterable value-key="value" placeholder="请选择">
              <el-option v-for="(item, index) in courseLevelType" :key="index" :label="item.label" :value="item.value" />
            </el-select>
          </el-col>
        </el-form-item>
        <el-form-item label="教材版本" prop="courseEdition">
          <el-col :xs="24" :lg="18">
            <el-select style="width: 100%" v-if="addOrUpdate" v-model="addCourseData.courseEdition" filterable value-key="value" placeholder="请选择">
              <el-option v-for="(item, index) in courseEditionType" :key="index" :label="item.label" :value="item.value" />
            </el-select>
            <el-select style="width: 100%" v-if="!addOrUpdate" v-model="updateCourseData.courseEdition" filterable value-key="value" placeholder="请选择">
              <el-option v-for="(item, index) in courseEditionType" :key="index" :label="item.label" :value="item.value" />
            </el-select>
          </el-col>
        </el-form-item>
        <el-form-item label="课程名称" prop="courseName">
          <el-col :xs="24" :span="18">
            <el-input v-if="addOrUpdate" v-model="addCourseData.courseName" placeholder="请输入"></el-input>
            <el-input v-if="!addOrUpdate" v-model="updateCourseData.courseName" placeholder="请输入"></el-input>
          </el-col>
        </el-form-item>
        <!-- <el-form-item label="课程报价" prop="courseUnitPrice">
          <el-col :xs="24" :span="18">
            <el-input v-if="addOrUpdate" v-model="addCourseData.courseUnitPrice"
              oninput="value=value.replace(/[^\d]/g,'')" />
            <el-input v-if="!addOrUpdate" v-model="updateCourseData.courseUnitPrice"
              oninput="value=value.replace(/[^\d]/g,'')" />
          </el-col>
          <el-col :xs="24" :span="6">元/节</el-col>
        </el-form-item> -->
        <!-- <el-form-item label="会员折扣" prop="courseMemberDiscount">
          <el-col :span="18">
            <el-input v-if="addOrUpdate" v-model="addCourseData.courseMemberDiscount" />
            <el-input v-if="!addOrUpdate" v-model="updateCourseData.courseMemberDiscount" />
          </el-col>
          <el-col :xs="24" :span="6">(例:0.8)</el-col>
        </el-form-item> -->
        <el-form-item label="课程类型" prop="courseType">
          <template>
            <el-radio v-if="addOrUpdate" v-model="addCourseData.courseType" label="Sys">系统课</el-radio>
            <el-radio v-if="!addOrUpdate" v-model="updateCourseData.courseType" label="Sys">系统课</el-radio>
            <el-radio v-if="addOrUpdate && isShowExperience" v-model="addCourseData.courseType" label="Free">体验课</el-radio>
            <el-radio v-if="!addOrUpdate && isShowExperience" v-model="updateCourseData.courseType" label="Free">体验课</el-radio>
          </template>
        </el-form-item>
        <el-form-item label="题目类型" prop="courseContentType">
          <template>
            <el-radio v-if="addOrUpdate" v-model="addCourseData.courseContentType" label="Word">单词</el-radio>
            <el-radio v-if="!addOrUpdate" v-model="updateCourseData.courseContentType" label="Word">单词</el-radio>
            <el-radio v-if="addOrUpdate" v-model="addCourseData.courseContentType" label="Reading">阅读理解</el-radio>
            <el-radio v-if="!addOrUpdate" v-model="updateCourseData.courseContentType" label="Reading">阅读理解</el-radio>
            <el-radio v-if="addOrUpdate" v-model="addCourseData.courseContentType" label="ClozeTest">完型填空</el-radio>
            <el-radio v-if="!addOrUpdate" v-model="updateCourseData.courseContentType" label="ClozeTest">完型填空</el-radio>
            <el-radio v-if="addOrUpdate" v-model="addCourseData.courseContentType" label="Listening">听力</el-radio>
            <el-radio v-if="!addOrUpdate" v-model="updateCourseData.courseContentType" label="Listening">听力</el-radio>
            <el-radio v-if="addOrUpdate" v-model="addCourseData.courseContentType" label="WenZong">文综</el-radio>
            <el-radio v-if="!addOrUpdate" v-model="updateCourseData.courseContentType" label="WenZong">文综</el-radio>
          </template>
        </el-form-item>
        <el-form-item label="出词顺序" prop="wordOrder">
          <template>
            <el-radio v-if="addOrUpdate" v-model="addCourseData.wordOrder" label="0">随机</el-radio>
            <el-radio v-if="!addOrUpdate" v-model="updateCourseData.wordOrder" :label="0">随机</el-radio>
            <el-radio v-if="addOrUpdate" v-model="addCourseData.wordOrder" label="1">顺序</el-radio>
            <el-radio v-if="!addOrUpdate" v-model="updateCourseData.wordOrder" :label="1">顺序</el-radio>
          </template>
        </el-form-item>

        <el-form-item label="课程封面上传" prop="courseCover">
          <el-upload
            ref="upload"
            list-type="picture-card"
            action="#"
            :file-list="fileList"
            :limit="1"
            :on-preview="handlePictureCardPreview"
            :on-remove="handleRemove"
            :class="objClass"
            :on-change="handleChange"
            :http-request="uploadDetailHttp"
            :before-upload="beforeAvatarUpload"
          >
            <!-- <i class="el-icon-plus"></i> -->
          </el-upload>
          <el-dialog :visible.sync="dialogImg" append-to-body class="imgDialog" center>
            <img width="100%" :src="addOrUpdate ? addCourseData.courseCover : updateCourseData.courseCover" style="width: 300px" />
          </el-dialog>
          <div class="tips" style="font-size: 12px">建议尺寸:690*280,支持格式：JPG、PNG格式 支持尺寸：{{ '<' }}300KB</div>
        </el-form-item>
        <!-- <el-form-item label="文件上传">
          <el-upload class="upload-demo" :limit="1" v-loading="uploadLoading" :on-exceed="justPictureNum"
            :file-list="!addOrUpdate ? fileDetailList : fileDetailList.name" :http-request="uploadDetailHttp"
            :on-remove="handleRemoveDetail">
            <el-button size="small" type="primary">点击上传</el-button>
            <div slot="tip" class="el-upload__tip">只能上传jpg/png文件，且不超过500kb</div>
          </el-upload>
        </el-form-item> -->
        <!-- <el-form-item label="文件版本">
          <el-col :span="18">
            <el-input type="number" min="1" v-if="addOrUpdate" v-model="addCourseData.fileVersion"></el-input>
            <el-input type="number" v-if="!addOrUpdate" v-model="updateCourseData.fileVersion"></el-input>
          </el-col>
        </el-form-item> -->
        <el-form-item label="课程介绍" prop="courseDescription">
          <el-col :xs="24" :lg="18">
            <el-input type="textarea" resize="none" :rows="4" v-if="addOrUpdate" v-model="addCourseData.courseDescription" />
            <el-input type="textarea" resize="none" :rows="4" v-if="!addOrUpdate" v-model="updateCourseData.courseDescription" />
          </el-col>
        </el-form-item>
        <el-form-item label="状态" prop="isEnable">
          <el-col :xs="24" :lg="18" v-if="addOrUpdate">
            <el-radio-group v-model="addCourseData.isEnable">
              <el-radio :label="1">开通</el-radio>
              <el-radio :label="0">暂停</el-radio>
            </el-radio-group>
          </el-col>
          <el-col :xs="24" :lg="18" v-if="!addOrUpdate">
            <el-radio-group v-model="updateCourseData.isEnable">
              <el-radio :label="1">开通</el-radio>
              <el-radio :label="0">暂停</el-radio>
            </el-radio-group>
          </el-col>
          <!-- <template>
            <el-radio v-model="isEnable" v-if="addOrUpdate" label="1" @change="change1(isEnable)">开通</el-radio>
            <el-radio v-model="isEnable" v-if="!addOrUpdate" label="1" @change="change2(isEnable)">开通</el-radio>
            <el-radio v-model="isEnable" v-if="addOrUpdate" label="0" @change="change3(isEnable)">暂停</el-radio>
            <el-radio v-model="isEnable" v-if="!addOrUpdate" label="0" @change="change4(isEnable)">暂停</el-radio>
          </template> -->
        </el-form-item>
        <el-form-item label="是否禁用" prop="isDisabled" v-show="addCourseData.courseContentType == 'Word' || updateCourseData.courseContentType == 'Word'">
          <el-col :xs="24" :lg="18" v-if="addOrUpdate">
            <el-radio-group v-model="addCourseData.isDisabled">
              <el-radio :label="0">否</el-radio>
              <el-radio :label="1">是</el-radio>
            </el-radio-group>
          </el-col>
          <el-col :xs="24" :lg="18" v-if="!addOrUpdate">
            <el-radio-group v-model="updateCourseData.isDisabled">
              <el-radio :label="0">否</el-radio>
              <el-radio :label="1">是</el-radio>
            </el-radio-group>
          </el-col>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button v-if="addOrUpdate" size="mini" type="primary" @click="addActiveFun('addCourseData')">新增</el-button>
        <el-button v-if="!addOrUpdate" size="mini" type="primary" @click="updateActiveFun('updateCourseData')">修改</el-button>

        <el-button size="mini" @click="close">关闭</el-button>
      </div>
    </el-dialog>

    <!-- 替换单词发音 -->
    <el-dialog :title="'替换单词发音'" :visible.sync="dialogWordReplace" width="70%" :close-on-click-modal="false" @close="closeWordReplace">
      <el-form label-position="left" label-width="120px" style="width: 100%">
        <el-form-item label="单词发音替换" prop="word">
          <el-col :xs="24" :span="18" style="display: flex">
            <el-input v-model="wordList.word" @input="valVideoForm"></el-input>
            <el-button type="primary" style="margin-left: 20px" @click="fetchWordList">获取音频</el-button>
          </el-col>
        </el-form-item>
        <el-form-item label="单词音标" v-if="wordList.v == '2'">
          <el-col :xs="24" :span="18" style="display: flex">
            <el-input v-model="wordList.phoneme"></el-input>
          </el-col>
        </el-form-item>
        <el-form-item label="发音类型">
          <template>
            <el-radio-group @input="changeRp" v-model="wordList.rp">
              <el-radio :label="false">美式发音</el-radio>
              <el-radio :label="true">英式发音</el-radio>
            </el-radio-group>
          </template>
        </el-form-item>
        <el-form-item label="版本选择">
          <template>
            <el-radio-group @input="changeVersion" v-model="wordList.v">
              <!-- <el-radio label="1">v1</el-radio> -->
              <el-radio label="2">v2</el-radio>
            </el-radio-group>
          </template>
        </el-form-item>
        <el-form-item label="音色" v-if="wordList.v == '2'">
          <template>
            <el-radio-group @input="changeSex" v-model="wordList.sex">
              <el-radio label="W">女声</el-radio>
              <el-radio label="M">男声</el-radio>
            </el-radio-group>
          </template>
        </el-form-item>
        <el-form-item label="音频1.0x">
          <audio
            controlsList="nodownload noplaybackrate"
            :src="videoUrl5"
            :autoplay="false"
            controls
            style="transform: scale(0.7); transform-origin: left top"
            v-if="videoUrl5 != ''"
            ref="audioPlayer"
          ></audio>
        </el-form-item>
        <el-form-item label="音频1.5x">
          <audio
            controlsList="nodownload noplaybackrate"
            :src="videoUrl1"
            :autoplay="false"
            controls
            style="transform: scale(0.7); transform-origin: left top"
            v-if="videoUrl1 != ''"
            ref="audioPlayer"
          ></audio>
        </el-form-item>
        <el-form-item label="音频2.0x">
          <audio
            controlsList="nodownload noplaybackrate"
            :src="videoUrl2"
            :autoplay="false"
            controls
            style="transform: scale(0.7); transform-origin: left top"
            v-if="videoUrl2 != ''"
          ></audio>
        </el-form-item>
        <el-form-item label="音频3.0x">
          <audio
            controlsList="nodownload noplaybackrate"
            :src="videoUrl3"
            :autoplay="false"
            controls
            style="transform: scale(0.7); transform-origin: left top"
            v-if="videoUrl3 != ''"
          ></audio>
        </el-form-item>
        <el-form-item label="音频4.0x">
          <audio
            controlsList="nodownload noplaybackrate"
            :src="videoUrl4"
            :autoplay="false"
            controls
            style="transform: scale(0.7); transform-origin: left top"
            v-if="videoUrl4 != ''"
          ></audio>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="addWordReplace('wordReplaceData')">替换</el-button>
        <el-button size="mini" @click="closeWordReplace">关闭</el-button>
      </div>
    </el-dialog>

    <!-- 图片显示 -->
    <el-dialog title="" :visible.sync="dialogOpenimg" width="30%">
      <!-- <el-dialog title="" :visible.sync="dialogOpenimg" width="30%" :before-close="handleClose"> -->
      <div class="coverimg">
        <el-image class="table_list_pic" :src="coverImg"></el-image>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import courseApi from '@/api/courseList';
  import courseApitwo from '@/api/courseChildren';
  import enTypes from '@/api/bstatus';
  import { mapGetters } from 'vuex';
  import { pageParamNames } from '@/utils/constants';
  import { ossPrClient } from '@/api/alibaba';
  import CryptoJS from 'crypto-js';
  export default {
    name: 'courseList',
    data() {
      return {
        roleTag: localStorage.getItem('roleTag'),
        fileUrl: '',
        fileOne: '',
        fileTwo: '',
        fileThree: '',
        fileFour: '',
        files: [],
        // 分页
        tablePage: {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null
        },
        tableLoading: false,
        roleName: '',
        role: '',
        dataQuery: {
          courseCode: '',
          courseName: '',
          categoryCode: '',
          bigClassCode: '',
          courseStage: '',
          courseLevel: '',
          courseType: '',
          courseEdition: '',
          isEnable: ''
        },
        activeType: [], // 活动类型
        tableData: [], //表格数据
        dialogVisible: false, // 修改弹窗是否展示
        dialogWordReplace: false, // 替换发音弹窗是否展示
        isRouterAlive: true, //局部刷新
        addOrUpdate: true, // 是新增还是修改
        addCourseData: {}, // 新增课程
        wordList: {
          v: '2',
          sex: 'W',
          rp: false,
          sg: '',
          word: '',
          phoneme: ''
        },
        videoUrl1: '',
        videoUrl2: '',
        videoUrl3: '',
        videoUrl4: '',
        videoUrl5: '',
        wordReplaceData: [], // 单词替换数据
        updateCourseData: {}, // 修改数据
        rules: {
          // 表单提交规则
          categoryCode: [
            {
              required: true,
              message: '必填',
              trigger: 'blur'
            }
          ],
          bigClassCode: [
            {
              required: true,
              message: '必填',
              trigger: 'blur'
            }
          ],
          courseStage: [
            {
              required: true,
              message: '必填',
              trigger: 'blur'
            }
          ],
          courseLevel: [
            {
              required: true,
              message: '必填',
              trigger: 'change'
            }
          ],
          courseEdition: [
            {
              required: true,
              message: '必填',
              trigger: 'change'
            }
          ],
          courseName: [
            {
              required: true,
              message: '必填',
              trigger: 'change'
            }
          ],
          wordOrder: [
            {
              required: true,
              message: '必填',
              trigger: 'change'
            }
          ],
          // courseUnitPrice: [{
          //   required: true,
          //   message: '必填',
          //   trigger: 'change'
          // }],
          // courseMemberDiscount: [{
          //   required: true,
          //   message: '必填',
          //   trigger: 'change'
          // }],
          courseContentType: [
            {
              required: true,
              message: '必填',
              trigger: 'change'
            }
          ],
          courseType: [
            {
              required: true,
              message: '必填',
              trigger: 'change'
            }
          ],
          courseCover: [
            {
              required: true,
              message: '必填',
              trigger: 'change'
            }
          ],
          courseDescription: [
            {
              required: true,
              message: '必填',
              trigger: 'change'
            }
          ],
          isEnable: [
            {
              required: true,
              message: '必填',
              trigger: 'change'
            }
          ],
          isDisabled: [
            {
              required: true,
              message: '必填',
              trigger: 'change'
            }
          ]
        },
        RegTime: '',
        fileListPending: [], // 待处理已上传图片信息
        fileList: [], // 上传图片已有图片列表

        fileDetailListPending: [], // 待处理已上传图片信息
        fileDetailList: [], // 上传图片已有图片列表

        uploadLoading: false, // 上传图片加载按钮
        fullscreenLoading: false, // 保存啥的加载

        content: '',
        isUploadSuccess: true, // 是否上传成功
        isShowRelevance: true, // 新增或修改是否展示关联产品
        isPlayVideo: false, //获取的音频是否能播放
        // isEnable: '0', //单选框状态 值必须是字符串
        value1: [],
        categoryType: [], //课程分类
        bigClassType: [], //课程类型
        courseStageType: [], //课程学段类型
        courseEditionType: [], //教材版本
        courseTypes: [], //课程类型
        courseLevelType: [], //课程等级
        exportLoading: false,
        dialogImageUrl: '',
        dialogImg: false,
        objClass: {
          upLoadShow: true,
          upLoadHide: false
        },

        coverImg: '',
        dialogOpenimg: false

        // radio: '0', // 出词顺序
      };
    },
    computed: {
      ...mapGetters(['roles']),
      isShowExperience() {
        if (this.addCourseData.bigClassCode == 'D0001' || this.updateCourseData.bigClassCode == 'D0001') {
          if (this.roleTag == 'admin' || this.roleTag == 'study') {
            return true;
          } else {
            return false;
          }
        } else {
          return true;
        }
      }
    },
    activated() {
      //获取教材版本下拉学段
      this.getCourseEditionType();
    },
    async created() {
      this.fetchData();
      ossPrClient();
      this.getCategoryType();
      //获取学段下拉框
      this.getStady();
      //获取课程等级下拉框
      this.getCourseLevel();
      //获取课程类型下拉学段
      this.getCourseType();
      //获取教材版本下拉学段
      this.getCourseEditionType();
      var jsonData = JSON.stringify(this.roles);
      var s = JSON.stringify(this.roles).includes('admin');
      var y = JSON.stringify(this.roles).includes('教研');
      var obj = eval('(' + jsonData + ')');
      this.roleName = s || y;
      console.log(this.roleName, 'roleName');
      this.role = obj[0].val;
      console.log(this.role, 'role');
      console.log(obj[0].val);
    },
    methods: {
      async checkAudioStatus(url) {
        try {
          const response = await fetch(url, {
            method: 'GET', // 只请求头部，不下载整个音频
            headers: { Range: 'bytes=0-' } // 请求部分内容，检查服务器是否返回 206
          });

          if (response.status !== 206) {
            this.$message.error('音频无法播放,请重新获取');
            this.isPlayVideo = true;
          }
        } catch (error) {
          console.log(error, '失败');

          this.$message.error('音频无法播放,请重新获取');
          this.isPlayVideo = true;
        }
      },
      valVideoForm(event) {
        if (/[\u4e00-\u9fa5\d]/.test(event)) {
          this.$message.error('请输入英文字母');
          this.wordList.word = this.wordList.word.replace(/[\u4e00-\u9fa5\d]/g, '');
        }
      },
      //重置
      rest() {
        this.dataQuery.courseCode = '';
        this.dataQuery.courseName = '';
        this.dataQuery.categoryCode = '';
        this.dataQuery.bigClassCode = '';
        this.dataQuery.courseStage = '';
        this.dataQuery.courseLevel = '';
        this.dataQuery.courseType = '';
        this.dataQuery.courseEdition = '';
        this.dataQuery.isEnable = '';
        this.RegTime = '';
        this.fetchData01();
      },

      // 查询+搜索课程列表
      fetchData01() {
        this.tablePage = {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null
        };
        this.fetchData();
      },
      fetchData() {
        const that = this;
        that.tableLoading = true;
        var a = that.RegTime;
        if (a != null) {
          that.dataQuery.startDate = a[0];
          console.log((that.dataQuery.startDate = a[0]));
          that.dataQuery.endDate = a[1];
        }
        courseApi.courseList(that.tablePage.currentPage, that.tablePage.size, that.dataQuery).then((res) => {
          that.tableData = res.data.data;
          console.log(res);
          that.tableLoading = false;
          // 设置后台返回的分页参数
          pageParamNames.forEach((name) => that.$set(that.tablePage, name, parseInt(res.data[name])));
        });
      },
      //添加操作
      clickAdd() {
        this.fileList = [];
        this.addCourseData = {
          categoryCode: '',
          bigClassCode: '',
          courseStage: '',
          courseLevel: '',
          courseEdition: '',
          courseName: '',
          courseContentType: '',
          courseType: '',
          wordOrder: '',
          courseCover: '',
          courseDescription: '',
          isEnable: '',
          isDisabled: 0
        };
        console.log(this.addCourseData);
        this.objClass.upLoadShow = true; //删除图片后显示上传框
        this.objClass.upLoadHide = false;
        this.dialogVisible = true;
        this.addOrUpdate = true;
        setTimeout(() => {
          this.$refs['addCourseData'].resetFields();
        }, 50);
      },

      //局部刷新
      reload() {
        this.isRouterAlive = false;
        this.$nextTick(function () {
          this.isRouterAlive = true;
        });
      },
      // 单词发音替换按钮
      wordReplace() {
        this.reload();
        (this.wordList = {
          v: '2',
          sex: 'W',
          rp: false,
          word: '',
          phoneme: ''
        }),
          (this.videoUrl1 = '');
        this.videoUrl2 = '';
        this.videoUrl3 = '';
        this.videoUrl4 = '';
        this.videoUrl5 = '';
        this.wordReplaceData = [];
        this.dialogWordReplace = true;
      },
      changeRp() {
        (this.videoUrl1 = ''), (this.videoUrl2 = ''), (this.videoUrl3 = ''), (this.videoUrl4 = ''), (this.videoUrl5 = '');
      },
      changeVersion(value) {
        if (value == '1') {
          this.wordList.phoneme = '';
          this.wordList.sex = 'W';
        }
        (this.videoUrl1 = ''), (this.videoUrl2 = ''), (this.videoUrl3 = ''), (this.videoUrl4 = ''), (this.videoUrl5 = '');
      },
      changeSex() {
        (this.videoUrl1 = ''), (this.videoUrl2 = ''), (this.videoUrl3 = ''), (this.videoUrl4 = ''), (this.videoUrl5 = '');
      },
      // 获取单词音频
      async fetchWordList() {
        if (!this.wordList.word) {
          this.$message.error('单词不能为空');
          return false;
        }
        if (this.wordList.v === '2') {
          // if (!this.wordList.phoneme) {
          //   this.$message.error('音标不能为空');
          //   return false;
          // }
          this.wordList.sg = CryptoJS.SHA1(window.localStorage.getItem('loginMerchantCode') + 'L0anhf').toString();
        }

        const loading = this.$loading({
          lock: true,
          text: '获取音频',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });

        try {
          const res = await courseApi.fetchWordList(this.wordList);
          if (res.code == 20000) {
            this.wordReplaceData = res.data;

            for (const item of this.wordReplaceData) {
              if (this.isPlayVideo) break; // 如果 isPlayVideo 为 true，停止遍历

              switch (item.speed) {
                case '1.5':
                  this.videoUrl1 = item.voiceUrl;
                  // await this.checkAudioStatus(this.videoUrl1);
                  break;
                case '2.0':
                  this.videoUrl2 = item.voiceUrl;
                  // await this.checkAudioStatus(this.videoUrl2);
                  break;
                case '3.0':
                  this.videoUrl3 = item.voiceUrl;
                  // await this.checkAudioStatus(this.videoUrl3);
                  break;
                case '4.0':
                  this.videoUrl4 = item.voiceUrl;
                  // await this.checkAudioStatus(this.videoUrl4);
                  break;
                case '1.0':
                  this.videoUrl5 = item.voiceUrl;
                  // await this.checkAudioStatus(this.videoUrl5);
                  break;
              }
            }
            if (!this.isPlayVideo) {
              this.$message.success('获取成功');
              this.isPlayVideo = false;
            }
          } else {
            this.$message.warning(res.message);
          }
        } catch (err) {
          this.$message.warning(err);
          this.isPlayVideo = false;
        } finally {
          loading.close();
          this.isPlayVideo = false;
        }
      },
      // 单词发音替换提交
      addWordReplace(ele) {
        if (this.videoUrl1 == '') {
          this.$message.error('请先获取音频');
          return false;
        }
        // if (this.wordList.v == '2') {
        //   if (this.wordList.phoneme == '' || this.wordList.phoneme == undefined) {
        //     this.$message.error('请先获取音频');
        //     return false;
        //   }
        // }
        const loading = this.$loading({
          lock: true,
          text: '替换中',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        if (this.wordList.v == '1') {
          this.wordReplaceData.forEach((item) => {
            (item.rp = this.wordList.rp), (item.version = this.wordList.v), (item.version = '1');
          });
        } else {
          this.wordReplaceData.forEach((item) => {
            (item.rp = this.wordList.rp), (item.sex = this.wordList.sex), (item.version = this.wordList.v), (item.phoneme = this.wordList.phoneme);
          });
        }
        courseApi
          .addWordReplace(this.wordReplaceData)
          .then((res) => {
            if (res.success) {
              loading.close();
              this.dialogWordReplace = false;
              this.$message.success('已成功替换');
            } else {
              loading.close();
              this.$message.warning('单词替换失败,请重新替换');
            }
          })
          .catch((err) => {
            loading.close();
            this.$message.warning('单词替换失败,请重新替换');
          });
      },

      // 新增课程提交
      addActiveFun(ele) {
        const that = this;
        console.log(that.addCourseData);
        if (that.addCourseData.courseContentType != 'Word') {
          that.addCourseData.isDisabled = 0;
        }
        that.$refs[ele].validate((valid) => {
          // 表单验证
          if (valid) {
            const loading = this.$loading({
              lock: true,
              text: '新增课程',
              spinner: 'el-icon-loading',
              background: 'rgba(0, 0, 0, 0.7)'
            });
            courseApi
              .addCourse(that.addCourseData)
              .then(() => {
                that.dialogVisible = false;
                loading.close();
                that.$refs['addCourseData'].resetFields();
                that.fileList = [];
                that.addCourseData.courseCover = '';
                that.fetchData();
                that.$message.success('新增课程成功');
              })
              .catch((err) => {
                loading.close();
              });
          } else {
            console.log('error submit!!');
            //loading.close();
            return false;
          }
        });
      },
      // 点击编辑按钮
      handleUpdate(id) {
        const that = this;
        that.dialogVisible = true;
        that.addOrUpdate = false;
        that.fileList = [];
        this.$nextTick(() => {
          this.$refs['updateCourseData'].resetFields();
        });
        that.objClass.upLoadHide = true; //上传图片后置upLoadHide为真，隐藏上传框
        that.objClass.upLoadShow = false;
        courseApi
          .queryActive(id)
          .then((res) => {
            console.log(res);
            that.updateCourseData = res.data;
            that.fileList.push({
              name: that.updateCourseData.filePath,
              url: that.updateCourseData.courseCover
            });
            // that.isEnable = that.updateCourseData.isEnable.toString(); //状态回显
          })
          .catch((err) => {});
      },

      // 状态改变事件
      change(radio) {
        if (radio == '1') {
          this.addCourseData.isEnable = 1;
        } else {
          this.addCourseData.isEnable = 0;
        }
      },
      // 修改课程提交
      updateActiveFun(ele) {
        const that = this;
        if (that.updateCourseData.courseContentType != 'Word') {
          that.updateCourseData.isDisabled = 0;
        }
        that.$refs[ele].validate((valid) => {
          // 表单验证
          if (valid) {
            // if(that.fileDetailList.length>0){
            // const b = that.fileDetailList[0].url.split('manage/')
            // that.updateCourseData.filePath = 'manage/' + b[b.length - 1]
            // }
            // that.updateCourseData.isEnable = that.isEnable;
            const loading = this.$loading({
              lock: true,
              text: '修改课程信息提交',
              spinner: 'el-icon-loading',
              background: 'rgba(0, 0, 0, 0.7)'
            });
            courseApi
              .updateCourse(that.updateCourseData)
              .then(() => {
                that.dialogVisible = false;
                loading.close();
                that.fileList = [];
                that.addCourseData.courseCover = '';
                that.$refs['updateCourseData'].resetFields();
                that.fetchData();
                that.$message.success('修改课程成功');
              })
              .catch((err) => {
                // 关闭提示弹框
                loading.close();
              });
          } else {
            console.log('error submit!!');
            return false;
          }
        });
      },
      // 课程开通与暂停
      courseStatus(id, status) {
        if (status == 0) {
          status = 1;
        } else {
          status = 0;
        }
        const that = this;
        this.$confirm('确定操作吗?', '修改状态', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            courseApi
              .updateStatus(id, status)
              .then((res) => {
                that.$nextTick(() => that.fetchData());
                that.$message.success('修改成功!');
              })
              .catch((err) => {});
          })
          .catch((err) => {});
      },

      // 删除上传图片
      // handleRemove(file, fileList) {
      //   const that = this
      //   if (!that.addOrUpdate) {
      //     that.fileList = fileList
      //   } else {
      //     for (let a = 0; a < that.fileListPending.length; a++) {
      //       that.fileListPending[a].uid === file.uid ? that.fileList.splice(a, 1) : ''
      //     }
      //   }
      // },
      // 获取分类返回类型
      getCategoryType() {
        courseApitwo.categoryType().then((res) => {
          this.categoryType = res.data;
        });
      },
      //根据课程分类获取课程类型的下拉列表
      check(categoryCode) {
        // if (res.data.bigClassType == null) {
        //   this.fetchData();
        // }
        if (categoryCode != '') {
          courseApi.checkClassification(categoryCode).then((res) => {
            this.bigClassType = res.data;
          });
        }
      },

      //获取学段下拉框
      getStady() {
        var enType = 'CourseStage';
        enTypes.getEnumerationAggregation(enType).then((res) => {
          this.courseStageType = res.data;
        });
      },
      //获取课程等级下拉框
      getCourseLevel() {
        var enType = 'CourseLevel';
        enTypes.getEnumerationAggregation(enType).then((res) => {
          this.courseLevelType = res.data;
        });
      },
      //获取课程类型下拉学段
      getCourseType() {
        var enType = 'CourseType';
        enTypes.getEnumerationAggregation(enType).then((res) => {
          this.courseTypes = res.data;
        });
      },
      //获取教材版本下拉学段
      getCourseEditionType() {
        var enType = 'CourseEdition';
        enTypes.getEnumerationcheckList().then((res) => {
          this.courseEditionType = res.data;
          console.log('教材版本下拉学段');
          console.log(this.courseEditionType);
          console.log('教材版本下拉学段');
        });
      },
      //获开通和暂停
      change1(radio) {
        this.addCourseData.isEnable = 1;
      },
      change2(radio) {
        this.updateCourseData.isEnable = 1;
      },
      change4(radio) {
        this.updateCourseData.isEnable = 0;
      },
      change3(radio) {
        this.addCourseData.isEnable = 0;
      },
      // 删除上传图片
      handleRemoveDetail(file, fileList) {
        // console.log(fileList);
        // const that = this
        this.updateCourseData.courseCover = '';
        this.addCourseData.courseCover = '';
        this.fileList = [];

        // if (!that.addOrUpdate) {
        //   that.fileDetailList = fileList
        //   that.fileDeatiList = []
        //   that.updateCourseData.filePath = ""
        // } else {
        //   that.addCourseData.filePath = ""
        //   for (let a = 0; a < that.fileDetailListPending.length; a++) {
        //     that.fileDetailListPending[a].uid === file.uid ? that.fileDeatiList.splice(a, 1) : ''
        //   }
        // }
      },

      // 上传图片数量超限
      justPictureNum(file, fileList) {
        this.$message.warning(`当前限制选择1个文件`);
      },
      // 上传图片请求
      // uploadPrHttp({
      //   file
      // }) {

      //   console.log(file + "wyy");
      //   this.uploadLoading = true
      //   const that = this
      //   const fileName = 'manage/' + Date.parse(new Date())
      //   that.$nextTick(function () {
      //     ossPrClient().put(fileName, file).then(({
      //       res,
      //       url,
      //       name
      //     }) => {
      //       if (res && res.status === 200) {
      //         console.log(`阿里云OSS上传图片成功回调1`, res, url, name)
      //         if (!that.addOrUpdate) {
      //           that.fileList.push({
      //             'uid': file.uid,
      //             'url': url
      //           })
      //         } else { // 新增上传图片
      //           that.fileList.push({
      //             name
      //           })
      //           that.addCourseData.imageListFilePath = name
      //         }
      //         that.$nextTick(() => {
      //           that.uploadLoading = false
      //         })
      //       }
      //     }).catch(err => {
      //       that.$message.error('上传图片失败请检查网络或者刷新页面')
      //       console.log(`阿里云OSS上传图片失败回调`, err)
      //     })
      //   })
      // },

      uploadDetailHttp({ file }) {
        this.uploadLoading = true;
        const that = this;
        const fileName = 'manage/' + Date.parse(new Date());
        that.$nextTick(function () {
          ossPrClient()
            .put(fileName, file)
            .then(({ res, url, name }) => {
              console.log(name);
              if (res && res.status === 200) {
                console.log(`阿里云OSS上传图片成功回调1`, res, url, name);
                if (!that.addOrUpdate) {
                  that.updateCourseData.filePath = name;
                  // console.log(that.updateCourseData.filePath + "wyy");
                  // that.fileDetailList.push({
                  //   'uid': file.uid,
                  //   'url': url
                  // })
                  that.updateCourseData.courseCover = url;
                } else {
                  // 新增上传图片
                  // that.fileDetailList.push({
                  //   name
                  // })
                  that.addCourseData.courseCover = url;
                }
                that.$nextTick(() => {
                  that.uploadLoading = false;
                });
              }
            })
            .catch((err) => {
              that.$message.error('上传图片失败请检查网络或者刷新页面');
              console.log(`阿里云OSS上传图片失败回调`, err);
            });
        });
      },
      //进入子类
      enterChildrenList(courseCode, courseContentType, courseName) {
        console.log(courseContentType + 'wyy');
        const that = this;
        window.localStorage.setItem('courseNameWord', courseName);
        window.localStorage.setItem('courseCodeWord', courseCode);
        window.localStorage.setItem('courseContentTypeWord', courseContentType);
        if (courseContentType === 'Word') {
          that.$router.push({
            path: '/course/courseMake',
            query: {
              courseCode: courseCode,
              courseContentType: courseContentType,
              courseName: courseName
            }
          });
        } else if (courseContentType === 'Reading') {
          window.localStorage.setItem('courseNameReading', courseName);
          window.localStorage.setItem('courseCodeReading', courseCode);
          window.localStorage.setItem('courseContentTypeReading', courseContentType);
          that.$router.push({
            path: '/course/courseReading',
            query: {
              courseCode: courseCode,
              courseContentType: courseContentType,
              courseName: courseName
            }
          });
        } else if (courseContentType === 'ClozeTest') {
          window.localStorage.setItem('courseNameReading', courseName);
          window.localStorage.setItem('courseCodeReading', courseCode);
          window.localStorage.setItem('courseContentTypeReading', courseContentType);
          that.$router.push({
            path: '/course/courseReading',
            query: {
              courseCode: courseCode,
              courseContentType: courseContentType,
              courseName: courseName
            }
          });
        } else if (courseContentType === 'Listening') {
          window.localStorage.setItem('courseNameListening', courseName);
          window.localStorage.setItem('courseCodeListening', courseCode);
          window.localStorage.setItem('courseContentTypeListening', courseContentType);
          that.$router.push({
            path: '/course/courseListening',
            query: {
              courseCode: courseCode,
              courseContentType: courseContentType,
              courseName: courseName
            }
          });
        } else if (courseContentType === 'WenZong') {
        }
      },
      // 分页
      handleSizeChange(val) {
        this.tablePage.size = val;
        this.fetchData();
      },
      handleCurrentChange(val) {
        this.tablePage.currentPage = val;
        this.fetchData();
      },
      // 关闭弹窗
      close() {
        this.dialogVisible = false;
        if (this.addOrUpdate) {
          this.fileList = [];
          this.$refs['addCourseData'].resetFields();
        } else {
          this.fileList = [];
          this.$refs['updateCourseData'].resetFields();
        }
      },
      reset() {
        this.wordReplaceData = {
          word: null
        };
        if (this.$refs['form']) {
          this.$refs['form'].resetFields();
        }
      },
      // 关闭弹窗
      closeWordReplace() {
        this.dialogWordReplace = false;
        this.reset();
      },

      //定义导出Excel表格事件
      exportExcel(courseCode, courseName, courseLevel) {
        this.exportLoading = true;
        let data = {
          courseCode: courseCode,
          courseName: courseName,
          courseLevel: courseLevel
        };

        courseApi
          .courseWordExport(data)
          .then((res) => {
            console.log(window.URL.createObjectURL(res));
            const url = window.URL.createObjectURL(res);
            const link = document.createElement('a');
            link.style.display = 'none';
            link.href = url; //获取服务器端的文件名
            link.setAttribute('download', data.courseName + data.courseLevel + '单词.xls');
            document.body.appendChild(link);
            link.click();
            this.exportLoading = false;
          })
          .catch((error) => {
            console.log(error);
          });
      },

      beforeAvatarUpload(file) {
        const imgType = file.type === 'image/jpeg' || file.type === 'image/png';
        const isLt300k = file.size / 1024 < 300;
        if (!imgType) {
          this.$message.error('上传图片只能是 JPG和png 格式!');
          return false;
        }
        if (!isLt300k) {
          this.$message.error('上传图片大小不能超过 300k!');
          return false;
        }
      },

      handleChange(file, fileList) {
        this.objClass.upLoadHide = true; //上传图片后置upLoadHide为真，隐藏上传框
        this.objClass.upLoadShow = false;
      },
      handleRemove(file, fileList) {
        this.objClass.upLoadShow = true; //删除图片后显示上传框
        this.objClass.upLoadHide = false;
        this.addCourseData.courseCover = '';
      },
      // 点击预览图的放大按钮后会触发handlePictureCardPreview
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url;
        this.dialogImg = true;
      },

      openImg(row) {
        this.coverImg = row.courseCover;
        this.dialogOpenimg = true;
        console.log();
      }
    }
  };
</script>

<style lang="scss" scoped>
  .lh36 {
    line-height: 36px;
    font-size: 14px;
  }

  .SearchForm {
    width: 100%;
    margin-bottom: 15px;
    background: #fff;
    border-radius: 6px;
    padding: 5px 0;
    padding-left: 5px;
    box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.2);
  }

  .btn-add {
    padding: 5px;
  }

  .red {
    color: red;
  }

  .green {
    color: green;
  }

  .course-table {
    text-align: center;
  }

  .course-table td,
  .course-table th {
    padding: 5px 0;
    text-align: center;
  }

  .course-table button {
    padding: 2px;
  }

  // .icon-stop {
  //   float: left;
  //   display: inline-block;
  //   width: 12px;
  //   height: 12px;
  //   background: url('../../icons/stop.png') no-repeat top center/contain;
  // }

  .mt22 {
    margin-top: 22px;
  }

  @media (max-width: 767px) {
    .el-message-box {
      width: 80% !important;
    }
  }

  /*当upLoadShow为true时，启用如下样式，即上传框的样式，若为false则不启用该样式*/
  .upLoadShow .el-upload {
    width: 10rem !important;
    height: 10rem !important;
    line-height: 10rem !important;
  }

  /*当upLoadHide为true时，启用如下样式，即缩略图的样式，若为false则不启用该样式*/
  .upLoadHide .el-upload-list--picture-card .el-upload-list__item {
    width: 10rem !important;
    height: 10rem !important;
    line-height: 10rem !important;
  }

  /*当upLoadHide为true时，启用如下样式，即上传框的样式，若为false则不启用该样式*/
  .upLoadHide .el-upload {
    display: none;
  }

  /* ::v-deep .el-icon-circle-close{
  color:red !important;
} */
  .coverimg {
    text-align: center !important;
    padding: 50px;

    /* .el-dialog__body{
    text-align:center !important;
  } */
  }

  .imgDialog {
    .el-dialog__body {
      text-align: center !important;

      .el-dialog {
        width: 30%;
      }
    }
  }
</style>
