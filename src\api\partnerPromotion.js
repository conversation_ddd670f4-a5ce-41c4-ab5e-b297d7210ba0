import request from '@/utils/request';
//查询合同状态
export const qryContractStatus = (data) => {
  return request({
    url: '/znyy/channel/qryContractStatus',
    method: 'GET',
    params: data
  });
};
//购买合同
export const buyContracts = (data) => {
  return request({
    url: '/znyy/channel/partner/contracts/buy',
    method: 'POST',
    data
  });
};
//查询合同数量
export const contractsNumber = (data) => {
  return request({
    url: '/znyy/channel/partner/contracts/number',
    method: 'GET',
    params: data
  });
};
//查询系统数量
export const queryRestLearningSysCount = (data) => {
  return request({
    url: '/znyy/purchase/board/queryRestLearningSysCount',
    method: 'GET',
    params: data
  });
};
//渠道伙伴列表
export const companionList = (data) => {
  return request({
    url: '/znyy/channel/partner/list',
    method: 'GET',
    params: data
  });
};
//新增渠道伙伴
export const addCompanion = (data) => {
  return request({
    url: '/znyy/channel/partner/add',
    method: 'POST',
    data
  });
};
//新增推广大使
export const addPartnerPromotion = (data) => {
  return request({
    url: '/znyy/partnerPromotion/addPartnerPromotion',
    method: 'post',
    data
  });
};

//推广大使查询
export const partnerPromotionList = (data) => {
  return request({
    url: '/znyy/partnerPromotion/list',
    method: 'GET',
    params: data
  });
};
//查看二维码
export const fetchContractQrLink = (data) => {
  return request({
    url: '/znyy/sign/contract/qr-link',
    method: 'GET',
    params: data
  });
};
//查询合同是否失效
export const contractFailure = (data) => {
  return request({
    url: '/znyy/channel/qryContract/failure',
    method: 'GET',
    params: data
  });
};
//查询合同是否失效
export const buttonStatus = (data) => {
  return request({
    url: '/znyy/channel/qryContract/button/Status',
    method: 'GET',
    params: data
  });
};
export const businessLicense = (data) => {
  return request({
    url: '/znyy/operations/v2/businessLicense',
    method: 'GET',
    params: data
  });
};