<template>
  <div>
    <el-upload
      v-loading="loading"
      class="upload-demo"
      accept=".zip,.rar,.7z,.tar"
      action
      :before-upload="handleBeforeUpload"
      :on-remove="handleRemove"
      :http-request="uploadHttp"
      :on-error="handleError"
      :limit="limit"
      :on-exceed="onExceed"
      :file-list="fileList"
      :show-file-list="false"
    >
      <el-button
        slot="trigger"
        size="small"
        type="primary"
        >点击上传</el-button
      >
    </el-upload>
  </div>
</template>

<script>
import { ossPrClient } from "@/api/alibaba";

export default {
  name: "MyUpload",
  props: {
    //点击错误提示信息
    errMsg: {
      type: String,
      default: undefined,
    },
    // 文件上传数量
    limit: {
      type: [Number, String],
      default: 1,
    },
    tipText: {
      type: String,
      default: "",
    },
    // 文件大小尺寸
    imgSize: {
      type: Number,
      default: 5 * 1024 * 1024, // 5M=>5*1024*1024 500KB=>500*1024
    },
    zipName: {
      type: String,
      default: "",
      require: true,
    },
    // 是否显示文件的tip
    showTip: {
      type: Boolean,
      default: true,
    },
    // 展示的文件列表
    fileList: {
      type: Array,
      default() {
        return [];
      },
    },
    fullUrl: {
      type: Boolean,
      default: false,
    },
    // versions: {
    //   type: Number,
    //   default: 1,
    // },
    dialogVisible: false,
    // 课件上传时所属课时
    lessonIndex: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      loading: false,
    };
  },
  computed: {
    // 动态显示MB或者KB
    isKbOrMb() {
      return this.imgSize / 1024 / 1024 >= 1
        ? `${Number(this.imgSize / 1024 / 1024).toFixed(0)}MB`
        : `${Number(this.imgSize / 1024).toFixed(0)}KB`;
    },
  },
  created() {
    ossPrClient();
  },
  methods: {
    click() {},
    uploadHttp({ file }) {
      this.loading = true;
      const fileName =
        "manage/" +
        this.zipName +
        "_" +
        Date.parse(new Date()) +
        // "_" +
        // this.versions +
        ".zip";
      ossPrClient()
        .put(fileName, file)
        .then(({ res, url, name }) => {
          if (res && res.status === 200) {
            console.log(`阿里云OSS上传文件成功回调1`, res, url, name);
            this.fileList.push({
              uid: file.uid,
              url: this.aliUrl + name,
              name: name.split("/")[1],
            });
            if (this.fullUrl) {
              this.handleSuccess(this.aliUrl + name, file.name);
            } else {
              this.handleSuccess(name, file.name);
            }
            this.loading = false;
          }
        })
        .catch((err) => {
          this.$message.error("上传文件失败请检查网络或者刷新页面");
          console.log(`阿里云OSS上传文件失败回调`, err);
        });
    },
    /* 上传文件开始 */
    // 文件上传之前的校验
    handleBeforeUpload(file) {
      const a = file.name.split(".");
      const isZip = a[a.length - 1] == "zip";
      const isRar = a[a.length - 1] == "rar";
      const is7z = a[a.length - 1] == "7z";
      const isTar = a[a.length - 1] == "tar";
      // if (this.errMsg) {
      //   this.$message.error(this.errMsg);
      //   return false;
      // }

      // const isJPG = file.type === "image/jpeg";
      // const isPNG = file.type === "image/png";
      // const isGIF = file.type === "image/gif";
      // const isSize = file.size < this.imgSize; // 文件是否小于限定的尺寸
      // if (this.tipText) {
      if (!isZip && !isRar && !is7z && !isTar) {
        this.$message.error("上传的文件只能是zip, rar, 7z, tar格式");
        return false;
      }
      // } else {
      //   if (!isJPG && !isPNG && !isGIF) {
      //     this.$message.error("上传的图片只能是 JPG、JPEG、PNG、GIF 格式");
      //     return false;
      //   }
      // }

      // 图片是否小于限定的尺寸
      // if (!isSize) {
      //   this.$message.error(`上传的图片大小不能超过 ${this.isKbOrMb}`);
      //   return false;
      // }
      const typeStatus = isZip || isRar || is7z || isTar;
      
      return typeStatus;
    },
    // 图片上传失败
    handleError(err) {
      this.$message.error("文件上传失败");
      console.log(err);
    },
    // 文件超过上传个数
    onExceed() {
      this.$message.error(`最多只能上传 ${this.limit} 张文件`);
      return false;
    },
    // 文件删除
    handleRemove(file) {
      console.log("前===", this.fileList);
      var index = this.fileList.findIndex((item) => {
        if (item.uid === file.uid) {
          return true;
        }
      });
      this.fileList.splice(index, 1);
      console.log("后===", this.fileList);
      this.$emit("handleRemove", file);
    },
    // 文件上传成功
    handleSuccess(res, fileName) {
      this.$emit("handleSuccess", res || "", fileName, this.lessonIndex);
    },
    /* 上传文件结束 */
  },
};
</script>
<style>
.upload .el-upload--picture-card {
  display: none !important;
}
.el-upload-list {
  /* display: none !important; */
  width: 70px;
}
.icon {
  position: absolute;
  width: 20px;
  height: 20px;
  color: #fff !important;
  display: flex !important;
  justify-content: center;
  align-items: center;
  border-radius: 20px;
  background-color: rgba(0, 0, 0, 0.7);
  top: -5px !important;
  right: -5px !important;
}
</style>
