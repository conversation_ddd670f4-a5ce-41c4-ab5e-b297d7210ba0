<template>
  <el-dialog title="查看学委" :visible.sync="visible" width="60%" top="5vh" append-to-body @close="close">
    <el-form :model="form" ref="form" label-width="78px">
      <el-form-item label="姓名" prop="name">
        <el-input
          v-model="form.name"
          placeholder="姓名"
          readonly
          size="small"/>
      </el-form-item>
      <el-form-item label="手机号" prop="phone">
        <el-input
          v-model="form.phone"
          placeholder="手机号"
          readonly
          size="small"/>
      </el-form-item>
      <el-form-item label="性别" prop="gender">
        <el-select v-model="form.gender" disabled>
          <el-option v-for="(item,index) in [{label:'男',value:'1'},{label:'女',value:'0'}]" :key="index"
                     :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="地区" prop="address">
        <el-input v-model="form.address" readonly></el-input>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>
  import committeemanApi from "@/api/studyroom/committeeman";
  export default {
    name: "committeemanView",
    data() {
      return {
        // 遮罩层
        visible: false,
        form: {},
      };
    },
    created() {
    },
    methods: {
      getDetail(id){
        this.reset();
        committeemanApi.committeemanView(id).then(res=>{
          this.form=res.data;
          this.form.address=this.form.province+this.form.city;
          this.visible = true;
        })
      },
      close() {
        this.reset();
      },
      reset() {
        this.form = {
          name: null,
          phone: null,
          gender: null,
          address: null,
        };
        if (this.$refs['form']) {
          this.$refs['form'].resetFields();
        }
      }
    }
  };
</script>

<style>
  .user_green {
    background: #02a490;
    color: #FFFFFF;
  }

  .user_red {
    background: #ec808d;
    color: #FFFFFF;
  }

  .border_user {
    text-align: center;

  }

  .border1_bottom {
    border-bottom: 1px solid #333333;
  }

  .border1_right {
    border-top: 1px solid #333333;
    border-left: 1px solid #333333;
    border-right: 1px solid #333333;
  }
</style>
