import request from '@/utils/request'

export default {
  // 分页查询
  list(data) {
    return request({
      url: '/xi/web/punishconfig',
      method: 'GET',
      params: data
    })
  },
  // 详情
  detail(id){
    return request({
      url: '/xi/web/punishconfig/details',
      method: 'GET',
      params:{
        id:id
      }
    })
  },
//新增或修改
  saveOrUpdate(data) {
    return request({
      url: '/xi/web/punishconfig/saveOrUpdate',
      method: 'POST',
      data
    })
  },
  //删除
  delete(id){
    return request({
      url: '/xi/web/punishconfig',
      method: 'DELETE',
      params:{
        id:id
      }
    })
  }
}
