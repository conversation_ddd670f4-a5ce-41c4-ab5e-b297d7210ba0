import request from '@/utils/request'

export default {
  // 分页查询
  list(data) {
    return request({
      url: '/xi/web/headprotrait/pageList',
      method: 'GET',
      params: data
    })
  },
  detail(id){
    return request({
      url: '/xi/web/headprotrait',
      method: 'GET',
      params:{
        id:id
      }
    })
  },
//新增或者修改
  saveOrUpdate(data) {
    return request({
      url: '/xi/web/headprotrait/saveOrUpdate',
      method: 'POST',
      data
    })
  },
  //删除
  delete(id){
    return request({
      url: '/xi/web/headprotrait?id='+id,
      method: 'DELETE',
    })
  }
}
