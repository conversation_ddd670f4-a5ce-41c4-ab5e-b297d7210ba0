<!--初高中 多选题-->
<template>
  <div class="app-container">
    <el-form :model="form" ref="form" label-width="100px" v-loading="formLoading"  :rules="rules">
      <el-form-item label="选择学段：" prop="gradeId" required>
        <el-select v-model="form.gradeId" placeholder="选择学段"  @change="levelChange" clearable>
          <el-option v-for="item in gradeList" :key="item.id" :value="item.id" :label="item.name"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="选择维度：" prop="subjectId" required>
        <el-select v-model="form.subjectId" placeholder="选择维度" @change="getKonwTree">
          <el-option v-for="item in subjectFilter" :key="item.id" :value="item.id" :label="item.name"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="子题：" required>
        <el-checkbox v-model="form.isSub">子题</el-checkbox>
      </el-form-item>
      <el-form-item label="有标题：" required>
        <el-radio-group v-model="form.expandInfo.titleFlag">
          <el-radio :label="false">否</el-radio>
          <el-radio :label="true">是</el-radio>
        </el-radio-group>
        <el-form-item v-show="form.expandInfo.titleFlag">
          <el-input v-model="form.expandInfo.titleInfo" placeholder="输入标题"/>
        </el-form-item>
      </el-form-item>
      <el-form-item label="有图片：" required>
        <el-radio-group v-model="form.expandInfo.imgFlag">
          <el-radio :label="false">否</el-radio>
          <el-radio :label="true">是</el-radio>
        </el-radio-group>
        <el-form-item v-show="form.expandInfo.imgFlag">
          <my-upload @handleSuccess="handleSuccess" @handleRemove="handleRemove" :fullUrl="true" :file-list="fileList" :showTip="false"/>
          <el-input type="hidden" v-model="form.expandInfo.imgInfo"/>
        </el-form-item>
      </el-form-item>
      <el-form-item label="有音频：" required>
        <el-radio-group v-model="form.expandInfo.audioFlag">
          <el-radio :label="false">否</el-radio>
          <el-radio :label="true">是</el-radio>
        </el-radio-group>
        <el-form-item v-show="form.expandInfo.audioFlag">
          <upload-file @handleSuccess="handleAudioSuccess" @handleRemove="handleAudioRemove" :file-list="audioFileList" />
          <el-input type="hidden" v-model="form.expandInfo.audioInfo"/>
        </el-form-item>
      </el-form-item>
      <el-form-item label="有视频：" required>
        <el-radio-group v-model="form.expandInfo.videoFlag">
          <el-radio :label="false">否</el-radio>
          <el-radio :label="true">是</el-radio>
        </el-radio-group>
        <el-form-item v-show="form.expandInfo.videoFlag">
          <upload-file @handleSuccess="handleVideoSuccess" @handleRemove="handleVideoRemove" :file-list="videoFileList" />
          <el-input type="hidden" v-model="form.expandInfo.videoInfo"/>
        </el-form-item>
      </el-form-item>
      <el-form-item label="题干：" prop="title" required>
        <el-input v-model="form.title">
<!--          <i @click="inputClick(form,'title')" slot="suffix" class="el-icon-edit-outline"-->
<!--             style="line-height: 36px;font-size: 20px;cursor: pointer;"></i>-->
        </el-input>
      </el-form-item>
      <el-form-item label="选项：" required>
        <el-form-item  :key="item.label"  v-for="(item,index) in form.customInfo">
          <div class="question-item-label">
            <el-input v-model="item.label"  style="width:50px;marginRight:5px" />
            <el-input v-model="item.value" style="width: 60%"/>
            <el-form-item label="是否添加文本框：" label-width="150px">
              <el-radio-group v-model="item.textbox">
                <el-radio :label="false">否</el-radio>
                <el-radio :label="true">是</el-radio>
              </el-radio-group>
            </el-form-item>
<!--            <el-input v-model="item.value"   @focus="inputClick(item,'value')"  class="question-item-content-input"/>-->
            <el-button style="marginLeft:5px" type="danger" size="mini" class="question-item-remove" icon="el-icon-delete" @click="questionItemRemove(index)"></el-button>
          </div>
        </el-form-item>
      </el-form-item>
<!--      <el-form-item label="解析：" prop="analysis" required>-->
<!--        <el-input v-model="form.analysis">-->
<!--          <i @click="inputClick(form,'analysis')" slot="suffix" class="el-icon-edit-outline"-->
<!--             style="line-height: 36px;font-size: 20px;cursor: pointer;"></i>-->
<!--        </el-input>-->
<!--      </el-form-item>-->
      <el-form-item label="分数：" prop="score">
        <el-input-number v-model="form.score" :precision="0" :step="1" :min='0' :max="100"></el-input-number>
      </el-form-item>
<!--      <el-form-item label="难度：" required>-->
<!--        <el-rate v-model="form.difficulty" class="question-item-rate"></el-rate>-->
<!--      </el-form-item>-->
<!--      <el-form-item label="知识点：" prop="knowledgePoints">-->
<!--        <treeselect-->
<!--          v-model="form.knowledgePoints"-->
<!--          :multiple="true"-->
<!--          :value-consists-of="valueConsistsOf"-->
<!--          :customInfo="konwledgeTree"-->
<!--          :normalizer="normalizer"-->
<!--          noOptionsText="无可用知识点"-->
<!--          placeholder="请选择知识点"/>-->
<!--      </el-form-item>-->
      <el-form-item label="正确答案：" prop="answer" required>
        <el-checkbox-group v-model="form.answer">
          <el-checkbox v-for="item in form.customInfo" :label="item.label" :key="item.label">{{item.label}}</el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="计时器：" prop="openTimer" required>
        <el-radio-group v-model="form.openTimer">
          <el-radio :label="false" >关闭</el-radio>
          <el-radio :label="true">开启</el-radio>
        </el-radio-group>
        <el-form-item v-show="form.openTimer" :key="item.label"  v-for="(item,index) in form.openTimerData">
          <div class="question-item-label">
            <el-input v-model="item.min"  onkeyup="value=value.replace(/[^\d]/g,'')" style="width:50px;marginRight:5px;" />分钟-
            <el-input v-model="item.max" style="width:50px;marginRight:5px;" />分钟，完成答题 分值
            <el-select v-model="item.operator" style="width:80px;marginRight:5px;">
              <el-option v-for="item in operatorList" :key="item.value" :value="item.value" :label="item.label"></el-option>
            </el-select>
            <el-input v-model="item.score" style="width:50px;marginRight:5px;" />
          </div>
        </el-form-item>
        <el-button v-show="form.openTimer" icon="el-icon-circle-plus-outline" @click="addOpenTimerData"/>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="submitForm">提交</el-button>
        <el-button @click="resetForm">重置</el-button>
        <el-button type="success" @click="questionItemAdd">添加选项</el-button>
<!--        <el-button type="success" @click="showQuestion">预览</el-button>-->
      </el-form-item>
    </el-form>
    <el-dialog  :visible.sync="richEditor.dialogVisible"  append-to-body :close-on-click-modal="false" style="width: 100%;height: 100%"   :show-close="false" center>
      <Ueditor @ready="editorReady"/>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="editorConfirm">确 定</el-button>
        <el-button @click="richEditor.dialogVisible = false">取 消</el-button>
      </span>
    </el-dialog>
   <div class="q-dailog">
      <el-dialog :visible.sync="questionShow.dialog" style="width: 100%;height: 100%" title="题目预览">
        <QuestionShow :qType="questionShow.qType" :question="questionShow.question" :qLoading="questionShow.loading"/>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import QuestionShow from '../components/Show'
import Ueditor from '@/components/Ueditor'
import { mapGetters, mapState, mapActions } from 'vuex'
import questionApi from '@/api/paper/question'
import subjectApi from "@/api/paper/subject";
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import MyUpload from "@/components/Upload/MyUpload";
import UploadFile from '@/components/Upload/UploadFile'

export default {
  components: {
    Ueditor,
    QuestionShow,
    Treeselect,
    MyUpload,
    UploadFile
  },
  data () {
    return {
      fileList:[],
      videoFileList:[],
      audioFileList:[],
      subjectList:[],
      gradeList:[],
      valueConsistsOf:'LEAF_PRIORITY',
      normalizer(node) {
        if (node.child==null||node.child.length===0){
          return {
            id: node.id,
            label: node.name,
          }
        } return {
          id: node.id,
          label: node.name,
          children: node.child
        }
      },
      form: {
        id: null,
        questionType: 'MULTIPLE_CHOICE',
        isSub: false,
        gradeId: null,
        subjectId: null,
        title: '',
        customInfo: [
          { id: null, label: 'A', value: '',textbox:false },
          { id: null, label: 'B', value: '',textbox:false },
          { id: null, label: 'C', value: '',textbox:false },
          { id: null, label: 'D', value: '',textbox:false }
        ],
        analysis: '',
        correct: '',
        answer: [],
        score: undefined,
        mediaType:'NO',
        difficulty: 0,
        dxSource:'PAPER',
        status:1,
        knowledgePoints:[],
        openTimer: false,
        openTimerData: [
          { min:'',max:'',operator:'+',score:''}
        ],
        expandInfo: {
          titleFlag: false,
          titleInfo: '',
          imgFlag: false,
          imgInfo: '',
          videoFlag: false,
          videoInfo: '',
          videoName:'',
          audioFlag: false,
          audioInfo: '',
          audioName:'',
        }
      },
      subjectFilter: null,
      formLoading: false,
      rules: {
        gradeId: [
          { required: true, message: '请选择学段', trigger: 'change' }
        ],
        subjectId: [
          { required: true, message: '请选择维度', trigger: 'change' }
        ],
        title: [
          { required: true, message: '请输入题干', trigger: 'blur' }
        ],
        analysis: [
          { required: true, message: '请输入解析', trigger: 'blur' }
        ],
        score: [
          { required: true, message: '请输入分数', trigger: 'blur' }
        ],
        answer: [
          { required: true, message: '请选择正确答案', trigger: 'change' }
        ]
      },
      richEditor: {
        dialogVisible: false,
        object: null,
        parameterName: '',
        instance: null
      },
      questionShow: {
        qType: 0,
        dialog: false,
        question: null,
        loading: false
      },
      konwledgeTree:[]
    }
  },
  created () {
    this.initGrade();
    this.initSubject();
    let id = this.$route.query.id
    let _this = this
    if (id && parseInt(id) !== 0) {
      _this.formLoading = true
      this.subjectFilter = this.subjectList
      questionApi.select(id).then(re => {
        _this.form = re.data
        _this.form.answer = re.data.answer.split(',');
        if (_this.form.knowledgePoints!=null && _this.form.knowledgePoints!=='') {
          this.form.knowledgePoints = _this.form.knowledgePoints.split(',')
        }
        _this.formLoading = false
        this.getKonwTree()
        this.subjectFilter = this.subjectList.filter(data => data.gradeId === this.form.gradeId)
        if (_this.form.expandInfo){
          if (this.form.expandInfo.imgFlag){
            this.fileList.push({url:this.form.expandInfo.imgInfo});
          }
          if (this.form.expandInfo.videoFlag){
            this.videoFileList.push({name:this.form.expandInfo.videoName ,url:this.form.expandInfo.videoInfo});
          }
          if (this.form.expandInfo.audioFlag){
            this.audioFileList.push({name:this.form.expandInfo.audioName ,url:this.form.expandInfo.audioInfo});
          }
        }
      })
    }
  },
  methods: {
    handleAudioSuccess(url,fileName){
      this.form.expandInfo.audioInfo=url;
      this.form.expandInfo.audioName=fileName;
    },
    handleAudioRemove(){
      this.form.expandInfo.audioInfo='';
      this.form.expandInfo.audioName='';
    },
    handleVideoSuccess(url,fileName){
      this.form.expandInfo.videoInfo=url;
      this.form.expandInfo.videoName=fileName;
    },
    handleVideoRemove(){
      this.form.expandInfo.videoInfo='';
      this.form.expandInfo.videoName='';
    },
    handleSuccess(url){
      this.form.expandInfo.imgInfo=url;
    },
    handleRemove(){
      this.form.expandInfo.imgInfo='';
    },
    addOpenTimerData(){
      let items = this.form.openTimerData;
      let p = this.operatorList[items.length].value;
      items.push({ min:'',max:'',operator: p ,score:''})
    },
    initGrade(){
      subjectApi.gradeListAll().then(res=>{
        this.gradeList = res.data;
      })
    },
    initSubject(){
      subjectApi.pageList().then(res=>{
        this.subjectList = res.data.data;
      })
    },

    // 获取知识点树
    getKonwTree() {
      questionApi.konwledgeTree(this.form.gradeId,this.form.subjectId).then(re => {
        this.konwledgeTree = re.data;
      })
    },

    levelChange () {
      this.form.subjectId = null
      this.subjectFilter = this.subjectList.filter(data => data.gradeId === this.form.gradeId)
    },
    editorReady (instance) {
      this.richEditor.instance = instance
      let currentContent = this.richEditor.object[this.richEditor.parameterName]
      this.richEditor.instance.setContent(currentContent)
      // 光标定位到Ueditor
      this.richEditor.instance.focus(true)
    },
    inputClick (object, parameterName) {
      this.richEditor.object = object
      this.richEditor.parameterName = parameterName
      this.richEditor.dialogVisible = true
    },
    editorConfirm () {
      let content = this.richEditor.instance.getContent()
      this.richEditor.object[this.richEditor.parameterName] = content
      this.richEditor.dialogVisible = false
    },
    questionItemRemove (index) {
      this.form.customInfo.splice(index, 1)
    },
    questionItemAdd () {
      let items = this.form.customInfo
      let newLastPrefix
      if (items.length > 0) {
        let last = items[items.length - 1]
        newLastPrefix = String.fromCharCode(last.label.charCodeAt() + 1)
      } else {
        newLastPrefix = 'A'
      }
      items.push({ id: null, label: newLastPrefix, value: '',textbox:false})
    },
    submitForm () {
      let _this = this
      if (_this.form.score == '') {
        return _this.$message.error('请添加分数')
      }
      // if (_this.form.difficulty == 0) {
      //   return _this.$message.error('请添加难度')
      // }
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.formLoading = true;
          this.form.answer = this.form.answer.join(',');
          if (this.form.knowledgePoints){
            this.form.knowledgePoints = this.form.knowledgePoints.join(',')
          }
          questionApi.edit(this.form).then(re => {
            if (re.success) {
              _this.$message.success(re.message)
              _this.$router.push({path: '/paper/index'})
            } else {
              _this.$message.error(re.message)
              this.formLoading = false
            }
          }).catch(e => {
            this.formLoading = false
          })
        } else {
          return false
        }
      })
    },
    showQuestion () {
      this.questionShow.dialog = true
      this.questionShow.qType = this.form.questionType
      this.questionShow.question = this.form
    },
    resetForm () {
      let lastId = this.form.id
      this.$refs['form'].resetFields()
      this.form = {
        id: null,
        questionType: 'MULTIPLE_CHOICE',
        isSub: false,
        gradeId: null,
        subjectId: null,
        title: '',
        customInfo: [
          { id: null, label: 'A', value: '',textbox:false },
          { id: null, label: 'B', value: '',textbox:false },
          { id: null, label: 'C', value: '',textbox:false },
          { id: null, label: 'D', value: '',textbox:false }
        ],
        analysis: '',
        correct: '',
        answer: [],
        score: undefined,
        mediaType:'NO',
        difficulty: 0,
        dxSource:'PAPER',
        status:1,
        knowledgePoints:null
      }
      this.form.id = lastId
    },

  },
  computed: {
    ...mapGetters('enumItem', ['enumFormat','subjectFormat']),
    ...mapState('enumItem', {
      // gradeList: state => state.question.gradeList,
      questionType: state => state.question.typeList,
      editUrlEnum: state => state.question.editUrlEnum,
      operatorList: state => state.question.operatorList,
    }),
  }
}
</script>

<style lang="less" scoped>
.question-item-label {
  display: flex;
  margin-bottom: 12px;
}
.q-dailog {
  /deep/.el-dialog__header {
    border-bottom: 1px solid #dfdfdf;
  }
}
</style>
