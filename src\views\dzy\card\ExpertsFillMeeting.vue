<template>
  <div class="app-container">
    <el-form :inline="true" class="container-card" label-width="110px" label-position="left">
      <el-form-item :label="cardName + '卡号：'">
        <el-input v-model="dataQuery.number" :placeholder="'请输入' + cardName + '号：'" clearable/>
      </el-form-item>
      <el-form-item label="使用者手机号：">
        <el-input v-model="dataQuery.userPhone" placeholder="请输入使用者手机号：" clearable/>
      </el-form-item>
      <el-form-item label="姓名：">
        <el-input v-model="dataQuery.realName" placeholder="请输入姓名：" clearable/>
      </el-form-item>
      <el-form-item label="是否已使用：">
        <el-select v-model="dataQuery.used" clearable filterable placeholder="请选择">
          <el-option
            v-for="item in useOption"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="领用商户：">
        <el-select v-model="dataQuery.merchantCode" clearable filterable placeholder="请选择,可以搜索">
          <el-option
            v-for="item in bvAdminOption"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-form-item label="使用时间：">
          <el-date-picker style="width: 100%;" value-format="yyyy-MM-dd HH:mm:ss" format="yyyy-MM-dd HH:mm:ss"
                          type="daterange" v-model="useTime" align="right" unlink-panels range-separator="至"
                          start-placeholder="开始日期" end-placeholder="结束日期" @change="useTimeChange"
                          :picker-options="pickerOptions">
          </el-date-picker>
        </el-form-item>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="fetchData()">搜索</el-button>
      </el-form-item>
    </el-form>

    <el-form :inline="true" style=" margin-bottom: 20px;">
      <el-form-item :inline="true" v-if="checkPermission(['b:dzy:card:buy'])">
        <el-button type="success" @click="showBuy()" v-if="merchantCode != 'A0001'">
          我要购买
        </el-button>
      </el-form-item>

    </el-form>

    <el-table class="common-table" :data="tableData" style="margin-bottom: 20px;" row-key="id" stripe border
              :tree-props="{list: 'children', hasChildren: 'true'}" v-loading="tableLoading">
      <el-table-column prop="number" label="会议卡号"></el-table-column>
<!--
      <el-table-column prop="password" label="密码"></el-table-column>
-->
      <el-table-column prop="idType" label="卡类型" :formatter="typeFormat">
      </el-table-column>
      <el-table-column prop="used" label="是否使用">
        <template slot-scope="scope">
          <span class="red" v-if="scope.row.used">已使用</span>
          <span class="green" v-else>未使用</span>
        </template>
      </el-table-column>
      <el-table-column prop="userPhone" label="使用人手机"></el-table-column>
      <el-table-column prop="merchantName" label="领用商户"></el-table-column>
      <el-table-column prop="status" label="状态" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <span class="green" v-if="scope.row.status===1">正常</span>
          <span class="yellow" v-else-if="scope.row.status===2">已领用</span>
          <span class="red" v-else>禁用</span>
        </template>
      </el-table-column>
      <!-- 更新状态 -->
       <el-table-column prop="withdrawnBonus" label="操作" width="100px">
        <template slot-scope="scope">
          <el-button type="warning" size="mini" icon="el-icon-switch-button"
                     @click="getDetails(scope.row)">详情
          </el-button>
        </template>
      </el-table-column>
    </el-table>


    <!-- 分页 -->
    <el-col :span="24" style="overflow-x: auto;" :xs="24">

      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
                     layout="total, sizes, prev, pager, next, jumper"
                     :total="tablePage.totalItems" @size-change="handleSizeChange"
                     @current-change="handleCurrentChange"/>
    </el-col>


    <el-dialog :visible.sync="showClaim" title="购买高报师会议" width="90%" :before-close="handleClose">
      <el-form label-width="100px" width="100%">
        <el-form-item label="填写购买数量:">
          <el-input-number :disabled="isEdit" :min="0" :max="5" v-model="claimCount" @change="calculateTheTotalPrice"></el-input-number>
        </el-form-item>
        <el-form-item label="选择卡类型:">
          <el-select disabled v-model="claimTypeId" filterable placeholder="请选择" @change="calculateTheTotalPrice"
                     :default-first-option="true">
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <div v-for="(item,index) in users" :key="index">
          <el-form-item label="请填写姓名:" style="width: 20%">
            <el-input :disabled="isEdit" v-model="item.userName"></el-input>
          </el-form-item>
          <el-form-item label="请填写手机号:" style="width: 20%">
            <el-input :disabled="isEdit" v-model="item.userPhone"></el-input>
          </el-form-item>
        </div>
        <el-form-item label-width="0px">
          <el-col :span="11" :xs="24">

            <el-form-item label="单价" style="margin-bottom: 20px">
              <el-input v-model="claimPrice" disabled>
                <template slot="append">学时</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="11" :xs="24">
            <el-form-item label="总价">
              <el-input v-model="claimAllMany" disabled>
                <template slot="append">学时</template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" v-show="!isUpdate" @click="buy">购买</el-button>
        <el-button @click="showClaim=false;typeIndex=4">取消</el-button>
      </div>
    </el-dialog>


  </div>
</template>

<script>

import checkPermission from '@/utils/permission'
import cardApi from "@/api/cardApi";
import bvAdminApi from "@/api/bvAdminApi";
import {baseUrl, pageParamNames} from "@/utils/constants";
import cardTypeApi from "@/api/cardTypeApi";
import store from '@/store'
import schoolApi from "@/api/areasSchoolList";

export default {
  name: 'studentList',
  data() {
    return {
      baseUrl: baseUrl,
      myheard: {
        'x-www-iap-assertion': store.getters.token,
        'www-cid': 'dx_znyy_resource'
      },
      claimPrice: 0,
      claimAllMany: 0,
      showClaim: false,
      claimTypeId: -1,
      claimCount: 0,
      useOption: [
        {label: '已使用', value: '1'},
        {label: '未使用', value: '0'},
      ],
      fileList: '',
      options: [],
      typeValue: '',
      isFalse: true,
      tableLoading: false,
      isUpdate: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      users: [],
      typeText: [],
      tableData: {},
      dataQuery: {
        studentCode: '',
        startDate: '',
        endDate: '',
        isEnable: '',
        isFormal: '',
        loginName: '',
        realName: '',
        memberCode: '',
        memberPhone: '',
        restrictedUse: ''
      },
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }]
      },
      useTime: '',
      bvAdminOption: [],
      merchantCode: '',
      //卡类型
      type: undefined,
      typeIndex: 4,
      cardName: '高报师会议',
      isEdit: false,
    };
  },
  created() {

    this.getCardType();
    this.getBvAdminOption();
    this.getRoleTag();

    if(this.users.length < this.claimCount){
      for (let i = 0; i < this.claimCount-this.users.length; i++) {
        let user = {};
        this.users.push(user);
      }
    }else if(this.users.length < this.claimCount){
      this.users.length = this.claimCount;
    }
  },
  methods: {
    checkPermission,
    handleClose(done) {
      this.$confirm('确认关闭？')
        .then(_ => {
          this.showClaim = false;
        })
        .catch(_ => {
        });
    },
    getDetails(data){
      this.showClaim = true;
      this.isEdit = true;
      cardApi.getDetails(data.number).then(res=>{
        this.users = res.data;
        this.claimCount = res.data.length;
        this.calculateTheTotalPrice();
        this.isUpdate = true;
          })
    },
    showBuy(){
      this.showClaim=true;
      this.isUpdate=false;
      this.isEdit=false;
      this.claimCount = 0;
      this.users = [];
    },
    buy() {

      if (this.claimTypeId == null || this.claimTypeId === -1 || this.claimCount < 1) {
        this.$message.error("请填写数量或选择类型")
        return;
      }
      //校验是否有姓名、手机号未填写
      for (let i = 0; i < this.users.length; i++) {
        if(!this.users[i].userName || !this.users[i].userName){
          this.$message.error("第"+(i+1)+"位客户信息未填写完整，请补充！")
          return;
        }
      }

      let param = {
        "claimTypeId": this.claimTypeId,
        "claimCount": this.claimCount,
        "users": this.users
      };
      cardApi.buyMeeting(param).then(res => {
        if (res.success) {
          this.$message.success("恭喜您购买成功");
          this.fetchData();
          this.showClaim = false
        } else {
          this.$message.error(res.message);
          this.fetchData();
        }
      })
    },
    calculateTheTotalPrice() {
      cardTypeApi.cardById(this.claimTypeId).then(res => {
        if (res.success) {
          this.claimPrice = res.data.priceCourse;
          this.claimAllMany = this.claimCount * res.data.priceCourse;
        } else {
          this.$message.error(res.messsage);
        }
      })

      if(this.users.length < this.claimCount){
        for (let i = 0; i < this.claimCount-this.users.length; i++) {
          let user = {
            userName: undefined,
            userPhone: undefined
          };
          this.users.push(user);
        }
      }else if(this.users.length > this.claimCount){
        this.users.length = this.claimCount;
      }

    },
    //获取角色
    getRoleTag() {
      schoolApi.getCurrentAdmin().then((res) => {
        console.log(res.data.merchantCode + "wyy");
        this.merchantCode = res.data.merchantCode;
      });
    },
    changeStatus(type, state) {
      type.status = state;
      cardApi.cardUpdate(type).then(res => {
        if (res.success) {
          this.fetchData();
        } else {
          this.$message.error(res.message);
        }
      })
    },
    getBvAdminOption() {
      bvAdminApi.selectOption().then(res => {
        this.bvAdminOption = res.data
      })
    },
    // 上传文件数量超限
    justPictureNum(file, fileList) {
      this.$message.warning(`当前限制选择1个文件`)
    },
    typeFormat(row, column) {
      for (let i = 0; i < this.options.length; i++) {
        if (this.options[i].value == row.idType) {
          return this.options[i].label;
        }
      }
      return '未知'
    },
    getCardType() {
      cardApi.cardTypeSelect().then(res => {
        this.options = res.data;
        this.claimTypeId = this.options[this.typeIndex - 1].value;
        this.fetchData();
      })
    },
    beforUpload() {
      if (this.typeValue == null || this.typeValue == '' || this.typeValue == undefined) {
        this.$message.error("请选择卡类型");
        return false;
      }
      return true;
    },
    useTimeChange(value) {
      // console.log(e[0]);
      this.dataQuery.useTimeMin = value[0]
      this.dataQuery.useTimeMax = value[1]
      console.log(this.dataQuery.useTimeMax)
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val;
      this.fetchData();
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val;
      this.fetchData();
    },
    // 查询提现列表
    fetchData() {
      this.tableLoading = true;
      this.dataQuery.type = this.claimTypeId
      cardApi.cardList(this.tablePage.currentPage, this.tablePage.size, this.dataQuery).then(res => {
        this.tableData = res.data.data
        this.tableLoading = false
        // 设置后台返回的分页参数
        pageParamNames.forEach(name => this.$set(this.tablePage, name, parseInt(res.data[name])))
      })
    },
  }
};
</script>

<style>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.period-table td,
.period-table th {
  text-align: center;
}

.mt20 {
  margin-top: 20px;
}

.red {
  color: red;
}

.green {
  color: green;
}

.yellow {
  color: darkorange;
}

@media (max-width: 767px) {
  .el-message-box {
    width: 80% !important;
  }
}
</style>
