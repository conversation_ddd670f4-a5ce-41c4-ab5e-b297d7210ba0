<template>
  <div class="app-container">
    <el-form :inline="true" class="container-card" label-width="130px" label-position="left">
      <el-row>
        <el-col :span="4" :xs="24">
          <el-form-item label="退款金额："> {{ price / 100 }}元 </el-form-item>
        </el-col>
        <el-col :span="4" :xs="24" style="text-align: right">
          <el-form-item>
            <el-button type="primary" size="small" @click="mpsOrderRefund()">扫码支付</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <el-table class="common-table" v-loading="tableLoading" :data="tableData" style="width: 100%; margin-bottom: 20px"
      row-key="id" border default-expand-all :tree-props="{ list: 'children', hasChildren: 'true' }">
    </el-table>

    <!-- 分页 -->
    <el-col :span="24" style="overflow-x: auto" :xs="24">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>
  </div>
</template>

<script>
import store from "@/store";
import { Base64 } from "js-base64";
import orderApi from "@/api/mps/refund";

export default {
  data() {
    return {
      token: store.getters.token,
      setpayUrl: store.getters.setpayUrl,
      tableLoading: false,
      tableData: [],
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null,
      },
      price: 0,
    };
  },
  created() {
    this.getPrice();
  },
  methods: {
    getPrice() {
      orderApi.getPrice().then((res) => {
        this.price = res.data;
      });
    },
    mpsOrderRefund() {
      if (this.price == null || this.price <= 0) {
        this.$message.error("退款金额为0！");
        return;
      }
      orderApi.createLineOrder(this.price).then((res) => {
        const Base64 = require("js-base64").Base64;
        const params = JSON.stringify(res.data);
        let req =
          "token=" +
          this.token +
          "&params=" +
          params +
          "&back=" +
          window.location.href;
        console.log(Base64.encode(req));
        this.disabledF = false;
        window.open(this.setpayUrl + "product?" + Base64.encode(req), "_blank");
        // window.open("http://************:8000/product?" + Base64.encode(req), "_blank");
      });
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val;
      this.fetchData();
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val;
      this.fetchData();
    },
  },
};
</script>
