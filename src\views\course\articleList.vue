<template>
  <div class="app-container">
    <!-- 搜索 -->
    <el-form :inline="true" ref="form" class="SearchForm" style="padding-top: 30px">
      <el-form-item label="课程所属:" style="margin-right: 80px">
        <span>{{ courseName }}</span>
      </el-form-item>
      <el-form-item label="文章标题:" style="margin-right: 80px">
        <el-col :span="24">
          <el-input placeholder="请输入文章标题" clearable v-model="dataQuery.topicTitle" @keyup.enter.native="fetchData()"></el-input>
        </el-col>
      </el-form-item>
      <el-button type="primary" icon="el-icon-search" @click="fetchDataSearch()">搜索</el-button>
      <el-button type="info" icon="el-icon-refresh" @click="fetchDataReset()">重置</el-button>
    </el-form>
    <div class="SearchForm">
      <!-- 添加 -->
      <div class="btn-add">
        <el-button size="small" type="success" @click="goBack()">返回超级阅读课程列表</el-button>
        <el-button size="small" type="primary" icon="el-icon-plus" @click="addArticleHandle()">添加</el-button>
      </div>
      <!-- 表格 -->
      <el-table class="course-table" :data="tableData" stripe border :default-sort="{ prop: 'addTime', order: 'descending' }" v-loading="tableLoading">
        <el-table-column prop="id" label="文章编号"></el-table-column>
        <el-table-column prop="title" label="文章标题"></el-table-column>
        <el-table-column prop="checkpointName" label="关卡名称"></el-table-column>
        <el-table-column label="操作" width="300">
          <template slot-scope="scope">
            <el-button type="primary" size="large" @click="openTestQuestion(scope.row)">制作试题</el-button>
            <el-button type="success" size="large" @click="editArticleHandle(scope.row.id)">编辑</el-button>
            <el-button type="danger" size="large" @click="deleteReading(scope.row.id)">删除</el-button>
          </template>
        </el-table-column>
        <el-table-column prop="sortOrder" label="排序"></el-table-column>
      </el-table>
    </div>
    <!-- 分页 -->
    <el-col :span="20">
      <el-pagination
        :current-page.sync="tablePage.currentPage"
        :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="tablePage.totalItems"
        @size-change="handleSizeChange"
        :page-size="tablePage.size"
        @current-change="handleCurrentChange"
      />
    </el-col>
    <!-- 添加编辑弹窗 -->
    <el-dialog center :close-on-click-modal="false" :close-on-press-escape="false" :title="dialogTitle" :visible.sync="dialogVisible" width="55%" @close="dialogClose('ruleForm')">
      <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="100px" class="demo-ruleForm">
        <el-form-item label="文章排序：" prop="sortOrder">
          <el-input v-model="ruleForm.sortOrder" type="number" @input="handleInput"></el-input>
        </el-form-item>
        <el-form-item label="关卡名称：" prop="checkpointName">
          <el-input v-model="ruleForm.checkpointName"></el-input>
        </el-form-item>
        <el-form-item label="文章标题：" prop="title">
          <el-input v-model="ruleForm.title"></el-input>
        </el-form-item>
        <el-form-item label="文章内容：" prop="content">
          <el-input type="textarea" v-model="ruleForm.content"></el-input>
        </el-form-item>
        <el-form-item label="文章大意：" prop="translateContent">
          <el-input type="textarea" v-model="ruleForm.translateContent"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogClose('ruleForm')">取 消</el-button>
        <el-button type="primary" @click="dialogConfirm('ruleForm')">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
  import { listArticleAPI, findArticleAPI, addArticleAPI, editArticleAPI, deleteArticleAPI } from '@/api/superApi/article';
  import { pageParamNames } from '@/utils/constants';
  export default {
    data() {
      return {
        // 分页
        tablePage: {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null
        },
        tableLoading: false,
        dataQuery: {
          topicTitle: ''
        },
        isRouterAlive: true, //局部刷新
        tableData: [], //表格数据
        dialogTitle: '',
        dialogVisible: false,
        ruleForm: {
          sortOrder: '',
          checkpointName: '',
          title: '',
          content: '',
          translateContent: ''
        },
        rules: {
          sortOrder: [{ required: true, message: '请输入文章排序', trigger: 'blur' }],
          checkpointName: [{ required: true, message: '请输入关卡名称', trigger: 'blur' }],
          title: [{ required: true, message: '请输入文章标题', trigger: 'blur' }],
          content: [{ required: true, message: '请输入文章内容', trigger: 'blur' }],
          translateContent: [{ required: true, message: '请输入文章大意', trigger: 'blur' }]
        },
        operationType: '', // 新增编辑标识符
        courseName: '',
        courseId: '',
        courseContentType: '',
        editId: ''
      };
    },
    created() {
      this.fetchData();
    },

    methods: {
      fetchDataSearch() {
        this.tablePage = {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null
        };
        this.fetchData();
      },
      fetchDataReset() {
        this.dataQuery.topicTitle = '';
        this.fetchDataSearch();
      },
      handleInput(item) {
        // 去除前导零和非数字字符
        this.ruleForm.sortOrder = this.ruleForm.sortOrder.replace(/^(0+)|[^\d]+/g, '');

        // 限制最大值为 100
        if (parseInt(this.ruleForm.sortOrder, 10) > 100) {
          this.$message.warning('文章排序不能超过100');
          this.ruleForm.sortOrder = '100';
        }
      },
      // 查询表格列表
      fetchData() {
        const that = this;
        that.tableLoading = true;
        that.courseName = window.localStorage.getItem('courseNameReading');
        that.courseId = window.localStorage.getItem('courseIdReading');
        that.courseContentType = window.localStorage.getItem('courseContentTypeReading'); // 完型或者是阅读
        let params = {
          courseId: that.courseId,
          title: this.dataQuery.topicTitle,
          pageSize: that.tablePage.size,
          pageNum: that.tablePage.currentPage
        };
        listArticleAPI(params).then((res) => {
          that.tableData = res.data.data;
          that.tableLoading = false;
          pageParamNames.forEach((name) => that.$set(that.tablePage, name, parseInt(res.data[name])));
        });
      },
      // 新增阅读理解 or 完型填空
      addArticleHandle() {
        this.dialogTitle = '添加文章';
        this.ruleForm.sortOrder = '';
        this.ruleForm.checkpointName = '';
        this.ruleForm.title = '';
        this.ruleForm.content = '';
        this.ruleForm.translateContent = '';
        this.operationType = 'add'; // 设置操作类型为新增
        this.dialogVisible = true;
      },
      // 编辑阅读理解 or 完型填空
      editArticleHandle(id) {
        this.editId = id;
        this.dialogTitle = '编辑文章';
        this.operationType = 'edit'; // 设置操作类型为编辑
        findArticleAPI({ id: id })
          .then((res) => {
            this.ruleForm = res.data;
          })
          .then(() => {
            this.dialogVisible = true;
          });
      },
      // 弹窗验证
      dialogConfirm(formName) {
        this.$refs[formName].validate((valid) => {
          if (valid) {
            let textToast = this.operationType === 'add' ? '新增文章' : '编辑文章';
            const loading = this.$loading({
              lock: true,
              text: textToast,
              spinner: 'el-icon-loading',
              background: 'rgba(0, 0, 0, 0.7)'
            });
            let params = {
              courseId: this.courseId,
              sortOrder: this.ruleForm.sortOrder,
              checkpointName: this.ruleForm.checkpointName,
              title: this.ruleForm.title,
              content: this.ruleForm.content,
              translateContent: this.ruleForm.translateContent
            };
            if (this.operationType === 'add') {
              addArticleAPI(params)
                .then((res) => {
                  if (res.success) {
                    this.$message.success('添加成功！');
                    loading.close();
                    this.dialogClose(formName);
                    this.fetchData();
                  }
                })
                .catch((err) => {
                  loading.close();
                });
            } else if (this.operationType === 'edit') {
              editArticleAPI({ ...params, id: this.editId })
                .then((res) => {
                  if (res.success) {
                    this.$message.success('编辑成功！');
                    loading.close();
                    this.dialogClose(formName);
                    this.fetchData();
                  }
                })
                .catch((err) => {
                  loading.close();
                });
            }
          } else {
            return false;
          }
        });
      },
      // 关闭弹窗 重置表单
      dialogClose(formName) {
        this.dialogVisible = false;
        this.$refs[formName].resetFields();
        // this.ruleForm.checkpointName = '';
        // this.ruleForm.title = '';
        // this.ruleForm.content = '';
        // this.ruleForm.translateContent = '';
        // this.ruleForm.sortOrder = '';
      },
      // 制作试题
      openTestQuestion(row) {
        const that = this;
        window.localStorage.setItem('courseName', row.title);
        window.localStorage.setItem('articleId', row.id);
        window.localStorage.setItem('courseId', row.courseId);
        window.localStorage.setItem('courseContentType', that.courseContentType);
        that.$router.push({
          path: '/course/testQuestionList',
          query: {
            // 文章标题
            topicTitle: row.title,
            // 文章id
            articleId: row.id,
            courseId: row.courseId,
            courseContentType: that.courseContentType
          }
        });
      },

      //删除阅读理解和完型填空
      deleteReading(id) {
        const that = this;
        this.$confirm('确定操作吗?', '删除数据', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            deleteArticleAPI({ id: id }).then((res) => {
              that.$message.success('删除成功！');
              that.fetchData();
            });
          })
          .catch((err) => {});
      },

      // 分页
      handleSizeChange(val) {
        this.tablePage.size = val;
        this.fetchData();
      },
      handleCurrentChange(val) {
        this.tablePage.currentPage = val;
        this.fetchData();
      },
      //局部刷新
      reload() {
        this.isRouterAlive = false;
        this.$nextTick(function () {
          this.isRouterAlive = true;
        });
      },
      // 返回超级阅读课程列表
      goBack() {
        this.$store.dispatch('delVisitedViews', this.$route);
        this.$router.push({
          path: '/course/superReadCourseList'
        });
      }
    }
  };
</script>
<style src="@wangeditor/editor/dist/css/style.css"></style>
<style>
  .lh36 {
    line-height: 36px;
    font-size: 14px;
  }

  .SearchForm {
    width: 100%;
    margin-bottom: 15px;
    background: #fff;
    border-radius: 6px;
    padding: 5px 0;
    padding-left: 5px;
    box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.2);
  }

  .btn-add {
    padding: 5px;
    margin: 15px 0;
  }

  .red {
    color: red;
  }

  .green {
    color: green;
  }

  .course-table {
    text-align: center;
  }

  .course-table td,
  .course-table th {
    padding: 5px 0;
    text-align: center;
  }

  .course-table button {
    padding: 2px;
  }

  .icon-stop {
    float: left;
    display: inline-block;
    width: 12px;
    height: 12px;
    /* background: url("../../icons/stop.png") no-repeat top center/contain; */
  }

  .mt22 {
    margin-top: 22px;
  }

  .blue {
    margin-right: 50px;
    color: #409eff;
  }

  .clearfix {
    color: red;
  }

  @media (max-width: 767px) {
    .el-message-box {
      width: 80% !important;
    }
  }
</style>
