import request from "@/utils/request";

export default {
  // 新增
  save(data) {
    return request({
      url: '/znyy/deliverycenter/save',
      method: 'POST',
      data
    })
  },
  // 修改回显
  echoDeliveryCenter(id) {
    return request({
      url: '/znyy/deliverycenter/modifyEcho/' + id,
      method: 'PUT'
    })
  },
  // 编辑
  updateDeliveryCenter(data) {
    return request({
      url: '/znyy/deliverycenter/updateContent',
      method: 'PUT',
      data
    })
  },
  // 获取银行分类列表
  categoryType(bankType) {
    return request({
      url: '/znyy/bvstatus/' + bankType,
      method: 'GET'
    })
  },
  // 分页查询
  deliveryCenterList(pageNum, pageSize, data) {
    return request({
      url: '/znyy/deliverycenter/list/' + pageNum + '/' + pageSize,
      method: 'GET',
      params: data
    })
  },
  // 开通与暂停
  updateStatus(id, status) {
    return request({
      url: '/znyy/deliverycenter/updateStatus?id=' + id + '&isEnable=' + status,
      method: 'PUT'
    })
  },
  allList(){
    return request({
      url: '/znyy/deliverycenter/allList',
      method: 'GET'
    })
  },
  //指定交付中心
  assignDelivery(divisionMerchantCode,deliveryMerchantCode){
    return request({
      url: '/znyy/deliverycenter/assignDelivery',
      method: 'PUT',
      params:{
        "merchantCode":divisionMerchantCode,
        "deliveryMerchantCode":deliveryMerchantCode
      }
    })
  },
  //解除交付中心绑定
  liftDelivery(merchantCode){
    return request({
      url: '/znyy/deliverycenter/liftDelivery',
      method: 'PUT',
      params:{
        "merchantCode": merchantCode
      }
    })
  }
}
