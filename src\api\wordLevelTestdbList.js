/**
 * 单词水平测试题库相关接口
 */
import request from '@/utils/request'

export default {
  // 单词分页分页查询
  wordTestDbList(pageNum, pageSize, data) {
    return request({
      url: '/znyy/word/test/db/list/' + pageNum + '/' + pageSize,
      method: 'GET',
      params: data
    })
  },
  // 单词测试题提交
  addWordTestDb(data) {
    return request({
      url: '/znyy/word/test/db',
      method: 'POST',
      data
    })
  },
  // 单词水平测试编辑
  updateWordLevelDb(data) {
    return request({
      url: '/znyy/word/test/db',
      method: 'PUT',
      data
    })
  },
  // 查询
  queryActive(id) {
    return request({
      url: '/znyy/word/test/db/test/db/' + id,
      method: 'GET'
    })
  },
   // 单词水平测试删除
   wordLevelTestDelete(id) {
    return request({
      url: '/znyy/word/test/db/delete/' + id,
      method: 'DELETE'
    })
  },
  getCurrentTime() {
    var _this = this;
    let yy = new Date().getFullYear();
    let mm = new Date().getMonth() + 1;
    let dd = new Date().getDate();
    let hh = new Date().getHours();
    let mf = new Date().getMinutes() < 10 ? '0' + new Date().getMinutes() : new Date().getMinutes();
    let ss = new Date().getSeconds() < 10 ? '0' + new Date().getSeconds() : new Date().getSeconds();
    _this.gettime = yy + '-' + mm + '-' + dd + ' ' + hh + ':' + mf + ':' + ss;
    return _this.gettime
  }
}
