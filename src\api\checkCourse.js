/**
 * 审核排课
 */
import request from '@/utils/request'

export default {
  //基本信息+课程信息
  getBaseInfo(courseOrderId) {
    return request({
      url: '/cousys/web/schedule/get/base/info?courseOrderId=' + courseOrderId,
      method: 'GET',
    })
  },
  //教练 信息
  getInfo(courseOrderId) {
    return request({
      url: '/cousys/web/schedule/get/info?courseOrderId=' + courseOrderId,
      method: 'GET',
    })
  },

  getDetailInfo(courseOrderId, reason) {
    return request({
      url: '/cousys/web/schedule/get/detail/info',
      method: 'GET',
      params: {
        courseOrderId: courseOrderId,
        reason: reason
      }
    })
  },

  passCourse(courseOrderId) {
    return request({
      url: '/cousys/web/schedule/pass/course?courseOrderId=' + courseOrderId,
      method: 'PUT',
    })
  },
  //获取课表
  getWeekPlan(param) {
    return request({
      url: '/cousys/web/course/week/plan',
      method: 'GET',
      params: param
    })
  },

  //匹配教练 
  getMatchTutor(param) {
    return request({
      url: '/cousys/web/course/match/tutor',
      method: 'GET',
      params: param
    })
  },
  //更多教练 
  getMoreTutor(param) {
    return request({
      url: '/cousys/web/tutor/list/by/type',
      method: 'GET',
      params: param
    })
  },
}
