<template>
  <div class="app-container">
    <el-form :inline="true" ref="queryForm" class="container-card" label-width="110px">
      <el-form-item label="">
        <el-input v-model="dataQuery.title" placeholder="请输入搜索关键词">
          <i class="el-icon-search el-input__icon" slot="prefix">
          </i>
        </el-input>
      </el-form-item>

      <el-form-item label="操作时间：">
        <el-date-picker v-model="dataQuery.operation_value1" type="datetimerange" start-placeholder="开始日期"
          end-placeholder="结束日期" @change="timechange" :default-time="['12:00:00']">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" type="primary" @click="search()">搜索</el-button>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-re" @click="resetQuery()">重置</el-button>
      </el-form-item>

    </el-form>

    <el-col :span="24" style="margin-bottom: 30px">
      <el-button type="success" icon="el-icon-plus" size="mini" @click="addBtn()">新增</el-button>
    </el-col>

    <el-table class="common-table" v-loading="tableLoading" ref="multipleTable" :data="tableData"
      style="width: 100%;margin-bottom: 20px;" row-key="id" border default-expand-all
      @selection-change="handleSelectionChange">
      <el-table-column prop="orderNum" type="index" label="序号" width="50"></el-table-column>
      <el-table-column prop="title" label="名称"></el-table-column>
      <el-table-column prop="withdrawnBonus" label="操作" width="220">
        <template slot-scope="scope">
          <el-button type="primary" size="mini" @click="editBtn(scope.row.id)">编辑
          </el-button>
          <el-button type="danger" size="mini" icon="el-icon-delete" @click="singleDelete(scope.row.id)">删除
          </el-button>
        </template>
      </el-table-column>
      <el-table-column prop="photo" label="图片">
        <template slot-scope="scope">
          <el-image style="width: 60px; height: 50px" :src="scope.row.photo" :preview-src-list="[scope.row.photo]"
            v-if="scope.row.photo != ''">
          </el-image>
        </template>
      </el-table-column>
      <el-table-column prop="openType" label="打开方式" show-overflow-tooltip></el-table-column>
      <el-table-column prop="" label="排序" show-overflow-tooltip>
        <i class="el-icon-rank"></i>
      </el-table-column>
      <el-table-column prop="status" label="状态" show-overflow-tooltip>
        <template slot-scope="scope">
          <el-switch v-model="scope.row.status" active-color="#13ce66"
            @change="switchChange(scope.row.status, scope.row.id)" inactive-color="#ff4949">
          </el-switch>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="操作时间" show-overflow-tooltip></el-table-column>
    </el-table>
    <!-- 分页 -->
    <el-col :span="24" style="margin-bottom: 20px">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>

    <!-- 修改或者新增弹窗 -->
    <el-dialog :title="banner_titile" :visible.sync="open" width="50%" @close="close" v-if="open">

      <el-form ref="form" :model="form" :rules="rules" label-width="120px">

        <el-form-item label="名称：" prop="" style="width: 50%;">
          <el-input v-model="form.title" />
        </el-form-item>

        <el-form-item label="图片：" prop="">
          <el-upload action ref="upload" :on-success="handleSuccess" :file-list="fileList" :on-remove="handleRemove"
            :http-request="uploadHttp" list-type="picture-card" name="file" limit=1 :on-exceed="handleExceed">
            <el-button size="small" type="primary">点击上传</el-button>
          </el-upload>
        </el-form-item>

        <el-form-item label="打开方式：" prop="awardRulesFre">
          <el-radio-group v-model="form.openType" @change="radio_change">
            <el-radio label="1">图文</el-radio>
            <el-radio label="2">URL</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item v-if="isUrl" label="资源：" prop="">
          <Tinymce ref="editor" v-model="content" :height="400" />
        </el-form-item>

        <el-form-item v-else label="URL：" prop="content">
          <el-input v-model="form.content" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="open = false">取 消</el-button>
      </div>
    </el-dialog>


  </div>
</template>
<script>
import bannerApi from '@/api/xi/community/banner_run'
import { pageParamNames } from '@/utils/constants'
import { ossPrClient } from '@/api/alibaba'
import Sortable from "sortablejs";
import Tinymce from '@/components/Tinymce'

export default {
  name: 'pendant',
  props: {
    //点击错误提示信息
    errMsg: {
      type: String,
      default: undefined,
    },

    // 图片大小尺寸
    imgSize: {
      type: Number,
      default: 5 * 1024 * 1024, // 5M=>5*1024*1024 500KB=>500*1024
    },
    // 是否显示图片的tip
    showTip: {
      type: Boolean,
      default: true,
    },
    // 展示的图片列表
    fileList: {
      type: Array,
      default() {
        return [];
      },
    },
    fullUrl: {
      type: Boolean,
      default: false,
    },
    dialogVisible: false,
  },
  components: { Tinymce },
  data() {
    return {
      content: '',
      isUrl: true,
      editor: null,
      toolbarConfig: {},
      editorConfig: { placeholder: '请输入内容...' },
      mode: 'default', // or 'simple'
      banner_titile: '',
      imageList: '',
      dialogImageUrl: '',
      dialogVisible: false,
      disabled: false,
      value: '',
      dataQuery: {
        title: '',
        operation_value1: '',
        startTime: '',
        endTime: '',
      },
      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      open: false,
      tableData: [],
      form: {
      },

      // 表单校验
      rules: {
        danName: [{ required: true, message: '请选择段位要求', trigger: 'blur' }],
      },
      list_all: '',
      img_url: '',
      img_name: ''
    }
  },
  computed: {
    // 动态显示MB或者KB
    isKbOrMb() {
      return this.imgSize / 1024 / 1024 >= 1
        ? `${Number(this.imgSize / 1024 / 1024).toFixed(0)}MB`
        : `${Number(this.imgSize / 1024).toFixed(0)}KB`;
    },
  },
  beforeDestroy() {
    const editor = this.editor
    if (editor == null) return
    editor.destroy() // 组件销毁时，及时销毁编辑器
  },
  mounted() {
    // 表格拖动 行拖动
    const table = document.querySelector('.el-table__body-wrapper tbody')
    const self = this
    Sortable.create(table, {
      onEnd({ newIndex, oldIndex }) {
        const targetRow = self.tableData.splice(oldIndex, 1)[0]
        self.tableData.splice(newIndex, 0, targetRow)
      }
    })
  },
  created() {
    this.getPageList()
    this.getRanklist()
    ossPrClient();
  },
  methods: {
    switchChange(value, id) {
      console.log(value, id)
      bannerApi.detail(id).then(res => {
        this.form = res.data;
        this.form.enable = value
        this.form.status = value
        this.form.id = id
        bannerApi.saveOrUpdate(this.form).then(response => {
          this.$message.success('提交成功！')
          this.getPageList()
        })
        console.log(this.form)
      })

    },
    radio_change(value) {
      if (value == 1) {
        this.isUrl = true
      } else {
        this.isUrl = false
      }
    },
    onCreated(editor) {
      this.editor = Object.seal(editor) // 一定要用 Object.seal() ，否则会报错
      console.log(editor)
    },
    uploadHttp({ file }) {
      this.loading = true;
      let suf = file.name.substring(file.name.lastIndexOf("."));
      const fileName = 'manage/' + Date.parse(new Date()) + suf;
      ossPrClient().put(fileName, file).then(({ res, url, name }) => {
        if (res && res.status === 200) {
          console.log(`阿里云OSS上传图片成功回调1`, res, url, name);
          this.form.photo = url
          if (this.fullUrl) {
            this.handleSuccess(this.aliUrl + name);
          } else {
            this.handleSuccess(name);
          }
          this.loading = false;
        }
      })
        .catch((err) => {
          this.$message.error('上传图片失败请检查网络或者刷新页面')
          console.log(`阿里云OSS上传图片失败回调`, err)
        })
    },
    moveAll() {
      if (this.list_all.length > 0) {
        this.open = true
      } else {
        this.$message({
          message: '请勾选本页全选按钮',
          type: 'warning'
        });
      }
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dataQuery = {
        title: '',
        startTime: null,
        endTime: null,
        operation_value1: '',

      }
      this.getPageList()
    },
    // 图片删除
    handleRemove(file) {
      console.log("前===", this.fileList)
      var index = this.fileList.findIndex(item => {
        if (item.uid === file.uid) {
          return true;
        }
      });
      this.fileList.splice(index, 1);
      console.log("后===", this.fileList)
      this.$emit("handleRemove", file);
    },
    handleExceed(files, fileList) {
      this.$message.warning(`只可选择一张图片`);
    },
    deleteAll() {
      if (this.list_all.length > 0) {
        let arr = []
        for (let index = 0; index < this.list_all.length; index++) {
          arr.push(this.list_all[index].id);
        }
        console.log(arr)
        this.$confirm('确定操作吗?', '删除状态', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          bannerApi.delete(arr).then(res => {
            that.$nextTick(() => that.fetchData())
            that.$message.success('删除成功!')
            this.getPageList()
          })
            .catch(err => {

            })
        }).catch(err => {

        })
      } else {
        this.$message({
          message: '请勾选本页全选按钮',
          type: 'warning'
        });
      }
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
      this.list_all = val
      console.log(val.length)
    },

    // 独立全选
    toggleSelection(selectBottom) {
      selectBottom ? this.$refs.multipleTable.toggleAllSelection() : this.$refs.multipleTable.clearSelection()
      this.select = selectBottom
    },
    //   段位要求列表
    getRanklist() {
      let data = {
        name: '',
        pageNum: 1,
        pageSize: 50
      }
      bannerApi.rankList(data).then(res => {
        this.pendant_options = res.data.data
      })
    },

    // 单个删除
    singleDelete(id) {
      const that = this;
      this.$confirm('确定操作吗?', '删除状态', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        bannerApi.delete(id).then(res => {
          that.$nextTick(() => that.fetchData())
          that.$message.success('删除成功!')
          this.getPageList()
        }).catch(err => {

        })
      }).catch(err => {

      })
    },
    // 图片上传成功
    handleSuccess(res) {
      this.$emit("handleSuccess", res || "");
      console.log(res)
    },
    search() {
      this.tablePage = {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      }
      this.getPageList()
    },
    handleDelete(id) {
      this.$confirm('确定要删除吗？', '删除段位', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        bannerApi.delete(id).then(res => {
          this.$message.success('删除成功！')
          this.getPageList()
        })
      })
    },
    addBtn() {
      this.reset();
      this.banner_titile = "新增"
      this.open = true;
      this.isUrl = true
      this.form.openType = '1'
      this.$refs.editor.setContent('')
    },
    // 修改
    editBtn(id) {
      this.reset();
      this.banner_titile = "修改"
      bannerApi.detail(id).then(res => {
        if (res.data.photo) {
          this.fileList.push({ url: res.data.photo })
        }
        if (res.data.openType == '1') {
          this.form = res.data;
          this.form.openType = '1'
          this.isUrl = true
          this.content = this.form.content
          this.form.content = ''
        } else {
          this.form = res.data;
          this.form.openType = "2";
          this.isUrl = false
        }
        this.open = true;
        console.log(this.form)
      })
    },
    submitForm() {
      if (this.form.openType == '1') {
        this.form.content = this.content
      }
      bannerApi.saveOrUpdate(this.form).then(response => {
        this.$message.success('提交成功！')
        this.open = false
        this.getPageList()
        this.$refs.upload.clearFiles()
        this.fileList = []
      })
    },
    getPageList() {
      this.tableLoading = true
      this.dataQuery.pageNum = this.tablePage.currentPage
      this.dataQuery.pageSize = this.tablePage.size
      bannerApi.list(this.dataQuery).then(res => {
        for (let index = 0; index < res.data.data.length; index++) {
          if (res.data.data[index].openType == '1') {
            res.data.data[index].openType = '图文'
          } else {
            res.data.data[index].openType = 'url'
          };
        }
        this.tableData = res.data.data
        this.tableLoading = false
        // 设置后台返回的分页参数
        pageParamNames.forEach(name =>
          this.$set(this.tablePage, name, parseInt(res.data[name]))
        )
      })
    },
    handlePage() {
      (this.tablePage = {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      }),
        this.getPageList()
    },
    //   获取起始时间
    timechange(value) {
      console.log(value)
      let data = {
        'year': value[0].getFullYear(),
        'month': this.timeP(value[0].getMonth() + 1),
        'day': this.timeP(value[0].getDate()),
        'hour': this.timeP(value[0].getHours()),
        'min': this.timeP(value[0].getMinutes()),
        'sec': this.timeP(value[0].getSeconds()),
      }
      let endtime = {
        'year': value[1].getFullYear(),
        'month': this.timeP(value[1].getMonth() + 1),
        'day': this.timeP(value[1].getDate()),
        'hour': this.timeP(value[1].getHours()),
        'min': this.timeP(value[1].getMinutes()),
        'sec': this.timeP(value[1].getSeconds()),
      }
      this.dataQuery.startTime = data.year + '-' + data.month + '-' + data.day + " " + data.hour + ":" + data.min + ":" + data.sec
      this.dataQuery.endTime = endtime.year + '-' + endtime.month + '-' + endtime.day + " " + endtime.hour + ":" + endtime.min + ":" + endtime.sec
    },
    // 补0
    timeP(s) {
      return s < 10 ? '0' + s : s
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val
      this.getPageList()
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val
      this.getPageList()
    },
    close() {
      this.open = false
      this.pendant_open = false
      this.reset()
    },
    reset() {
      this.form = {
        title: '',
        photo: undefined,
        openType: undefined,
        content: '',
        // photo:''
      }
      this.content = ''
      this.fileList = []
      if (this.$refs['form']) {
        this.$refs['form'].resetFields();
      }
    }
  },
}
</script>

<style src="@wangeditor/editor/dist/css/style.css"></style>
<style>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.period-table td,
.period-table th {
  text-align: center;
}

@media (max-width: 767px) {
  .el-message-box {
    width: 80% !important;
  }
}

.el-upload-list--picture-card {
  display: block;
}

.el-upload {
  border: none;
  width: auto;
  height: 36px;
  line-height: 36px;
}

.el-upload button {
  height: 36px;
}

.el-tooltip__popper {
  max-width: 800px;
}

.el-upload-list {
  position: relative;
}
</style>
