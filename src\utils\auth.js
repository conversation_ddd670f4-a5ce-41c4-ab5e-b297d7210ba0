import Cookies from 'js-cookie'

const TokenKey = 'Admin-Token'

const DealerId="dealerId"

const AddOrUpdate ="addOrUpdate"

export function getToken() {
  return Cookies.get(TokenKey)
}

export function setToken(token) {
  return Cookies.set(TokenKey, token)
}

export function removeToken() {
  return Cookies.remove(TokenKey)
}

//托管中心的增加
export function getDealerId() {
  return Cookies.get(DealerId)
}

export function setDealerId(dealerId) {
  return Cookies.set(DealerId, dealerId)
}

export function removeId() {
  return Cookies.remove(DealerId)
}

//修改显示

export function getAddOrUpdate() {
  return Cookies.get(AddOrUpdate)
}

export function setAddOrUpdate(addOrUpdate) {
  return Cookies.set(AddOrUpdate, addOrUpdate)
}

export function removeAddOrUpdate() {
  return Cookies.remove(AddOrUpdate)
}


