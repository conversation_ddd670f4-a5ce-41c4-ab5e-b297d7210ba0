<template>
    <!-- 拼读拼写流程 -->
    <div>
        <el-dialog :title="`${spellTitle}拼读拼写规则`" :visible.sync="spellDialogVisible"  width="70%" :close-on-click-modal="false" @close="spellDialogClose">
            <el-steps :active="activeCode" finish-status="success">
                <!-- <el-step v-if="isSpell" title="规则视频" :active="0"></el-step> -->
                <el-step title="拼读拼写词" :active="1"></el-step>
                <el-step title="拼读检测词" :active="2"></el-step>
                <el-step title="拼写检测词" :active="3"></el-step>
            </el-steps>
            <div class="stepForm">
                <!-- 拼读 拼写词 有划音节 -->
                <el-form ref="spellForm" :model="spellForm" v-if="activeCode === 1 " :rules="spellRules" label-width="160px">
                    <el-form-item label="拼读词："  prop="readCheckWord">
                        <el-row class="spell_checkWord">
                            <el-col :span="12">
                                <el-input readonly type="textarea" :rows="2" v-model="spellForm.readCheckWord"></el-input>
                            </el-col>
                            <el-button class="spell_checkWord_btn" size="small" type="primary" @click="onReadSelectWord('showReadSelectWord')">选择单词</el-button>
                        </el-row>
                        <el-transfer v-if="showReadSelectWord" :titles="['划音节词', '拼读拼写词']" :right-default-checked="readRightDefaultChecked" filterable filter-placeholder="请输入单词" @left-check-change="leftCheckChange" @right-check-change="onReadRightCheckChange" v-model="filterWords" :data="readFilterData" @change="filterChange"></el-transfer>
                    </el-form-item>
                    <el-form-item label="拼写词" >
                        <el-row class="spell_checkWord">
                        <el-col :span="12">
                            <el-input  readonly type="textarea" :rows="3" v-model="spellForm.writeCheckWord"></el-input>
                        </el-col>
                        <el-button class="spell_checkWord_btn" type="primary" size="small" @click="clearWriteWord">清空</el-button>
                        </el-row>
                    </el-form-item>
                    <el-form-item label="">
                        <el-button size="small " type="success" @click="submitCheckValidate()">下一环节</el-button>
                    </el-form-item>
                </el-form>
                <!-- 拼读检测词 -->
                <el-form ref="readTestForm" :model="readTestForm" v-if="activeCode === 2" :rules="readTestRules" label-width="160px">
                    <el-form-item label="拼读检测："  prop="readCheckWord">
                        <el-row class="spell_checkWord">
                            <el-col :span="12">
                                <el-input readonly type="textarea" v-model="readTestForm.readCheckWord"></el-input>
                            </el-col>
                            <el-button size="small" type="success" class="spell_checkWord_btn" @click="onReadSelectWord('showTestReadSelectWord')">选择单词</el-button>
                        </el-row>
                        <el-transfer v-if="showTestReadSelectWord" :titles="['划音节词', '拼读检测词']" :right-default-checked="readTestRightDefaultChecked" filterable filter-placeholder="请输入单词" @left-check-change="leftCheckChange" @right-check-change="onReadTestRightCheckChange" v-model="readTestFilterWords" :data="readTestfilterData" @change="filterChange"></el-transfer>
                    </el-form-item>
                    <el-form-item label="">
                        <el-button size="small " type="success" @click="submitReadCheckValidate()">下一环节</el-button>
                    </el-form-item>
                </el-form>
                <!-- 拼写检测词 :rules="writeTestRules"-->
                <el-form ref="writeTestForm" :model="writeTestForm" v-if="activeCode === 3"  label-width="160px">
                    <el-form-item label="拼写检测：" prop="writeCheckWord">
                        <el-row class="spell_checkWord">
                            <el-col :span="12">
                                <el-input readonly type="textarea" v-model="writeTestForm.writeCheckWord"></el-input>
                            </el-col>
                            <el-button size="small" type="success" class="spell_checkWord_btn" @click="onWriteSelectWord('showWriteSelectWord')">选择单词</el-button>
                        </el-row>
                        <el-transfer v-if="showWriteSelectWord" :titles="['划音节词', '拼写检测词']" :right-default-checked="writeTestRightDefaultChecked" filterable filter-placeholder="请输入单词"  @left-check-change="leftCheckChange" @right-check-change="onWriteRightCheckChange" v-model="writeTestfilterWords" :data="writeTestFilterData" @change="filterChange"></el-transfer>
                    </el-form-item>
                </el-form>
            </div>
            <div slot="footer" class="dialog-footer">
                <el-button size="mini" @click="spellDialogClose">关闭</el-button>
                <el-button size="mini" type="primary" @click="onSubmit()">确定</el-button>
            </div>
        </el-dialog>
    </div>
</template>
<script>
import courseDictationListApi from '@/api/courseDictationList'
 export default {
    props:{
        //弹窗
        spellStepDialogVisible:{
            type:Boolean,
            default:false
        },   
    },
    data(){
        return {
            streeData:[
                {word:"destionation",syllable:['de','tio','na'],weakToneData:[],stress:[],longShortTones:[],weakTone:[],disabled:false},
                {word:"panda",syllable:['pa','na'],stress:[],weakToneData:[],longShortTones:[],weakTone:[],disabled:false},
            ],
            drawSyllableForm:{
                word:'',
                syllableWord:'',
                spellWord:'',
            },
            stressForm:{

            },
            //无拼音拼写词
            spellWriteForm:{
                word:''
            },
            spellWriteFormRules:{},
            isSpellWriteForm:false,
            stressFormRules:{},
            ruleVideoRules:{
                // ruleVideo:[{ required: true, message: '请上传规则视频', trigger: 'blur' }]
            },
            uploadLoading:false,
            spellTitle:'新增',
            spellDialogVisible:false,
            activeCode:1,
            spellForm:{
                readCheckWord:'',
                writeCheckWord:'',
            },
            spellRules:{
                readCheckWord:[{ required: true, message: '请选择拼读词', trigger: 'blur' }]
            },//拼读拼写规则
            readTestForm:{
                readCheckWord:''
            },//拼读检测词
            readTestRules:{
                readCheckWord:[{ required: true, message: '请选择拼读检测词', trigger: 'blur' }]
            },//拼读检测词规则
            writeTestForm:{
                writeCheckWord:""
            },//拼写检测词
            writeTestRules:{
                writeCheckWord:[{ required: true, message: '请选择拼写检测词', trigger: 'blur' }]
            },//拼写检测词规则
            showWriteSelectWord:false,//拼写单词
            showReadSelectWord:false,//拼读单词
            showTestReadSelectWord:false,//拼读检测词
            isShowStress:false,//有划音节 划重音环节
            filterWords:[],//拼读检测词
            readTestFilterWords:[],
            writeTestfilterWords:[],
            
            readTestfilterData:[],//拼读检测穿梭框
            readTestfilterCheckData:[],//拼读检测穿梭框
            writeTestFilterData:[],//拼写检测词
            writeTestFilterCheckData:[],//拼写检测词
            courseId:"",
            readFilterData:[],//拼读词
            readFilterCheckData:[],//选中拼读词
            readRightDefaultChecked:[],//拼读右侧选中
            readTestRightDefaultChecked:[],//拼读检测右侧选中
            writeTestRightDefaultChecked:[],//拼写检测右侧选中
        }
    },
    watch:{
        spellStepDialogVisible:{
            handler(val){
                this.spellDialogVisible = val
            },
            immediate:true,
            deep:true
        },
        spellDialogVisible(val){
        }
    },
    created(){
        this.courseId = this.$route.query.id
    },
    methods:{
        getAllRuleWord(){
            courseDictationListApi.getAllRuleWord(this.courseId,'4','').then(res=>{
                if(res.code === 20000){
                    const map = new Map();
                    this.readFilterCheckData = res.data.data.filter(v => !map.has(v.word) && map.set(v.word, v));
                    this.readRightDefaultChecked = []
                    this.readTestRightDefaultChecked =[]
                    this.writeTestRightDefaultChecked = []
                    const writeDefaultWord = []
                    res.data.data.forEach(item => {
                        if(item.isReadSelect === 1){
                            this.readTestRightDefaultChecked.push(item.word)
                        }
                        if(item.isWriteSelect === 1){
                            this.writeTestRightDefaultChecked.push(item.word)
                        }
                        if(item.isSelect === 1 || item.isSelect === 2){
                            console.log('00000000');
                            this.readRightDefaultChecked.push(item.word)
                        }
                    })
                    this.spellForm.readCheckWord = this.readRightDefaultChecked.indexOf(',') === -1 ? this.readRightDefaultChecked.join(',') : String(this.readRightDefaultChecked)
                    this.spellForm.writeCheckWord = res.data.data.find(item => item.isSelect === 2) ? this.spellForm.readCheckWord : ''
                    this.readTestForm.readCheckWord = this.readTestRightDefaultChecked.indexOf(',') === -1 ? this.readTestRightDefaultChecked.join(','):String(this.readTestRightDefaultChecked)
                    this.writeTestForm.writeCheckWord = this.writeTestRightDefaultChecked.indexOf(',') === -1 ? this.writeTestRightDefaultChecked.join(','):String(this.writeTestRightDefaultChecked)
                    this.filterWords = this.readRightDefaultChecked
                    this.readTestFilterWords = this.readRightDefaultChecked
                    this.readFilterData = res.data.data && res.data.data.map(res=> {
                       return {
                        label:res.word,
                        key:res.word,
                        id:res.id,
                        isReadSelect:res.isReadSelect,
                        isSelect:res.isSelect,
                        isWriteSelect:res.isWriteSelect
                       }
                    })
                    // const readCheckWord = res.data.data && res.data.data.filter(item => {item.isReadSelect === 1})
                    // this.spellForm.
                    
                    console.log(res.data.data.filter(item => item.isReadSelect === 1),'拼读拼写',this.spellForm);
                }

            })
        },
        //确定选择重音
        onStressConfirm(item){
           item.disabled = false;
        },
        //修改重音
        onStressRevise(item){
            item.disabled = true;
        },
        stressChange(item){
            console.log(item,'item111');
        },
      //音节拼读
      beforeWordUpload(file){
        if(!this.isExcel(file)){
            this.$message.error('只能上传excel文件！')
            return false
        }
      },
      //音节拼读上传成功
      handleSetReadWordSuccess(response, file, fileList){
        if(file && file.raw && file.uid){
        this.readSettingFile =`https://document.dxznjy.com/applet/zhimi/usercode_${file.raw.uid}.xls` 
         }
      },
      //音节拼读删除
      handlSetRemoveReadWord(){
         this.readFile = ''
      },
        dialogClose(){
            for(let i in this.ruleVideoForm){
                this.ruleVideoForm[i] = ''
            }
            for(let i in this.spellForm){
                this.spellForm[i] = ''
            }
            for(let i in this.readTestForm){
                this.readTestForm[i] = ''
            }
            for(let i in this.writeTestForm){
                this.writeTestForm[i] = ''
            }
        },
        nextSpellForm(){
            this.activeCode = 0
            this.isShowStress = false
        },
       //拼读拼写下一环节
        submitCheckValidate(){
            this.$refs.spellForm.validate((valid) => {
                if(valid){
                  courseDictationListApi.updateAllRuleWord(this.readFilterCheckData).then(res => {
                    if(res.code === 20000){
                        this.activeCode = 2
                        this.readTestfilterData = this.readFilterData;//拼读检测词
                        this.readTestfilterCheckData = this.readFilterCheckData;//拼读检测词选中
                        this.readTestFilterWords = []
                        this.readTestfilterCheckData.forEach(item => {
                            if(item.isReadSelect === 1){
                                this.readTestFilterWords.push(item.word)
                            }
                        })
                        console.log(this.readTestfilterCheckData,'readTestfilterCheckData',this.readTestFilterWords);

                    }
                  })
                }
            })
        },
       //拼读检测词下一环节
        submitReadCheckValidate(){
            this.$refs.readTestForm.validate((valid) => {
                if(valid){
                  console.log(this.readTestfilterCheckData,'readFilterCheckDatareadFilterCheckData');
                  courseDictationListApi.updateAllRuleWord(this.readTestfilterCheckData).then(res => {
                    if(res.code === 20000){
                        this.activeCode = 3
                        this.writeTestFilterData = this.readTestfilterData;//拼写检测词
                        this.writeTestFilterCheckData = this.readTestfilterCheckData;//拼写检测词选中
                        this.writeTestfilterWords = []
                        this.writeTestFilterData.forEach(item => {
                            console.log(item);
                            if(item.isWriteSelect === 1){
                                this.writeTestfilterWords.push(item.label)
                            }
                        })
                        console.log(this.writeTestfilterWords,'writeTestfilterWords');
                        console.log(this.writeTestFilterData,'writeTestFilterDatawriteTestFilterData');
                    }
                  })
                }
            })
        },
        //拼写检测词确定按钮
        onSubmit(){
            this.$refs.writeTestForm.validate((valid) => {
                if(valid){
                  console.log(this.readTestfilterCheckData,'readFilterCheckDatareadFilterCheckData');
                  courseDictationListApi.updateAllRuleWord(this.readTestfilterCheckData).then(res => {
                    if(res.code === 20000){
                        this.$message.success('操作成功')
                        this.spellDialogClose()
                    }
                  })
                }
            })
        },
        spellDialogClose(){
            this.spellDialogVisible = false
            this.activeCode =  1;
            this.dialogClose()
            this.$bus.$emit('openSpell')
            // this.$emit('spellDialogClose', this.spellDialogVisible);
        },
        //清空拼写词
        clearWriteWord(){
            this.readFilterCheckData.forEach(res => {
                res.isSelect = this.spellForm.readCheckWord.split(',').find(item => item === res.word) ? '1' : '0';
            });
            this.spellForm.writeCheckWord = ''
        },
        //选择拼写单词
        onWriteSelectWord(){
            this.showWriteSelectWord = true
        },
        //选择拼读单词
        onReadSelectWord(val){
            if(val === 'showTestReadSelectWord'){
               this.showTestReadSelectWord = true
            }else{
                this.getAllRuleWord()
                this.showReadSelectWord = true
            }
        },
        //左侧选中/取消选中
        leftCheckChange(item,newItem){
            console.log(item,newItem,'leftCheckChange');
        },
        //搜索拼读检测词
        filterMethod(query, item) {
          console.log(query,item,'filterWords');
        },
        //拼写检测右侧选中
        onWriteRightCheckChange(checkItem){
            this.writeTestForm.writeCheckWord = checkItem.join(',')
            this.writeTestFilterCheckData.forEach(res => {
                res.isWriteSelect = checkItem.find(item => item === res.word) ? '1' : '0'
            })
        },
        //拼读检测右侧选中
        onReadTestRightCheckChange(checkItem){
           this.readTestForm.readCheckWord = checkItem.join(',')
           this.readTestfilterCheckData.forEach(res => {
             res.isReadSelect = checkItem.find(item => item === res.word) ? '1' : '0'
           })
        },
        //选中拼读单词
        onReadRightCheckChange(checkItem,closeItem){
            this.spellForm.readCheckWord = checkItem.join(',')
            this.spellForm.writeCheckWord = checkItem.join(',')   
            this.readFilterCheckData.forEach(res => {
                res.isSelect = checkItem.find(item => item === res.word) ? '2' : '0';
                
            });
        },
        filterChange(value,direction,movedKeys){
           console.log(value,direction,movedKeys,'filterChangevalue');
        },
    }
 }
</script>
<style lang="less" scoped>
.spell_checkWord{
    display:flex;
    align-items:center;
    margin-bottom:16pxu
}
.spell_checkWord_btn{
    margin-left:16px
}
.stress_data{
    display:flex
}
.stress_data_item{
    margin-right:16px
}
.stepForm{
    margin-top: 16px;
}
.upload_box{
    // display: flex;
    // justify-content: space-around;
}
.download_link{
    // height: 32px;
    // margin: 0;
    // padding: 0 15px;
    color: #13ce66;
}
.upload_link{
    color:#1890ff;
}
</style>