<template>
  <div class="app-container">
    <el-form :inline="true" class="container-card" label-width="140px" label-position="left">
      <el-form-item label="托管中心编号：">
        <el-input id="merchantCode" v-model="dataQuery.merchantCode" name="id" clearable placeholder="请输入服务商编号" />
      </el-form-item>
      <el-form-item label="登录账号：">
        <el-input id="name" v-model="dataQuery.name" name="id" clearable placeholder="请输入登录账号" />
      </el-form-item>
      <el-form-item label="托管中心名称：">
        <el-input id="merchantName" v-model="dataQuery.merchantName" name="id" clearable placeholder="请输入服务商名称" />
      </el-form-item>
      <el-form-item label="上级编号：">
        <el-input id="refereeCode" v-model="dataQuery.refereeCode" name="id" placeholder="请输入上级编号" clearable />
      </el-form-item>
      <el-form-item label="推荐人编号：">
        <el-input id="marketPartner" v-model="dataQuery.marketPartner" name="id" placeholder="请输入推荐人编号" clearable />
      </el-form-item>
      <el-form-item label="负责人：">
        <el-input id="realName" v-model="dataQuery.realName" name="id" clearable placeholder="请输入负责人" />
      </el-form-item>
      <el-form-item label="所在地区：">
        <el-input id="address" v-model="dataQuery.address" name="id" clearable placeholder="请输入推荐人所在地区" />
      </el-form-item>
      <el-form-item label="级别：">
        <el-select v-model="dataQuery.rank" value-key="value" placeholder="请选择" clearable>
          <el-option v-for="(item, index) in [
            { value: 'A', label: 'A级别' },
            { value: 'B', label: 'B级别' },
            { value: 'C', label: 'C级别' },
            { value: 'D', label: 'D级别' },
            { value: 'E', label: 'E级别' },
            { value: 'F', label: 'F级别' },
          ]" :key="index" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="审核状态：">
        <el-select v-model="dataQuery.isCheck" value-key="value" placeholder="请选择" clearable>
          <el-option v-for="(item, index) in [
            { value: '1', label: '通过审核' },
            { value: 'notPass', label: '未通审核' },
            { value: 'awaiting', label: '等待审核' }
          ]" :key="index" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="是否完款：">
        <el-select v-model="dataQuery.paymentIsComplete" value-key="value" placeholder="请选择" clearable>
          <el-option v-for="(item, index) in [
            { value: '1', label: '完款' },
            { value: '0', label: '未完款' }
          ]" :key="index" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="状态：">
        <el-select v-model="dataQuery.isEnable" filterable value-key="value" placeholder="请选择" clearable>
          <el-option v-for="(item, index) in [
            { value: '1', label: '开通' },
            { value: '0', label: '暂停' },
            { value: '-3', label: '终止' },
            { value: '-1', label: '系统关闭' },
          ]" :key="index" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-row>
        <el-col :span="18" :xs="24">
          <el-form-item label="添加时间：">
            <el-date-picker v-model="regTime" type="daterange" align="right" unlink-panels range-separator="至"
              start-placeholder="开始日期" end-placeholder="结束日期" value-format="yyyy-MM-dd HH:mm:ss" style="width: 100%;"
              clearable></el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="6" :xs="24" style="text-align: right">
          <el-button type="primary" icon="el-icon-search" @click="fetchData01()">搜索</el-button>
        </el-col>
      </el-row>
    </el-form>

    <el-col :span="24" style="margin-bottom: 30px">
      <el-button type="success" icon="el-icon-plus" v-if="checkPermission(['b:merchant:dealerList:add'])"
        @click="clickAdd" size="mini">添加</el-button>
      <el-button type="warning" v-if="checkPermission(['b:merchant:dealerList:export'])" icon="el-icon-document-copy"
        @click="exportFlow" v-loading="exportLoading" size="mini">导出
      </el-button>

      <el-button type="warning" icon="el-icon-document-copy" @click="exportFlow1" v-loading="exportLoading1"
        v-if="checkPermission(['b:merchant:dealerList:exportList'])" size="mini">导出托管中心运营数据
      </el-button>
    </el-col>
    <el-table class="common-table" v-loading="tableLoading" :data="tableData" style="width: 100%; margin-bottom: 20px"
      row-key="id" border default-expand-all>
      <el-table-column prop="merchantCode" label="编号" align="center" width="120px"></el-table-column>
      <el-table-column prop="name" label="登录账号" align="center" width="110px"></el-table-column>
      <el-table-column label="操作" align="center" width="300">
        <template slot-scope="scope">
          <!--          <el-button
                      v-if="(scope.row.isCheck ==2||scope.row.isCheck ==-3) && scope.row.checkReason !='' && checkPermission(['b:merchant:dealerList:coerceSuccess'])"
                      type="primary"
                      icon="el-icon-folder-checked"
                      @click="coerceSuccess(scope.row.id)"
                      size="mini"
                    >强制通过
                    </el-button>-->
          <el-button type="success" icon="el-icon-edit-outline" v-if="checkPermission(['b:merchant:dealerList:edit'])"
            @click="updateDealerList(scope.row.id, false)" size="mini">编辑
          </el-button>
          <el-button type="success" icon="el-icon-edit-outline"
            v-if="checkPermission(['b:merchant:dealerList:resubmit']) && scope.row.isOldData == 0 && scope.row.flowIsEnd == 1 && scope.row.flowEndStatus == 0"
            @click="updateDealerList(scope.row.id, true)" size="mini">重新提交
          </el-button>
          <!--          <el-button type="primary" icon="el-icon-folder-checked" v-if="scope.row.isCheck==3 && roleTag==='admin' && (scope.row.companyCode==='' || scope.row.companyCode===undefined  ) " @click="isCheckDealer(scope.row.id, scope.row.openMoney)"
          size="mini">审核</el-button>-->
          <!--          <el-button
                      type="primary"
                      icon="el-icon-folder-checked"
                      v-if="scope.row.isCheck==3 && roleTag==='Company' "
                      @click="isCheckDealer(scope.row.id, scope.row.openMoney)"
                      size="mini"
                    >审核
                    </el-button>

                    <el-button
                      type="primary"
                      icon="el-icon-folder-checked"
                      v-if="scope.row.isCheck==0 && roleName===true"
                      @click="isCheckDealer(scope.row.id, scope.row.openMoney)"
                      size="mini"
                    >审核
                    </el-button>-->

          <!--          <el-button  type="info" size="mini" icon="el-icon-video-pause"  v-if="scope.row.isCheck ===0 && roleName===true" @click="agree(scope.row.id,-4)">不通过</el-button>
          <el-button type="warning" size="mini" icon="el-icon-switch-button"  v-if="scope.row.isCheck === 0 && roleName===true" @click="agree(scope.row.id,4)">通过</el-button>-->
          <!--    -->
          <el-button type="warning" size="mini" icon="el-icon-switch-button"
            v-if="(scope.row.isEnable === 0 && scope.row.isCheck === 1) || (scope.row.isEnable === 0 && scope.row.flowEndStatus === 1) || scope.row.isEnable === -3"
            @click="dealerStatus(scope.row.id, 1)">开通
          </el-button>
          <el-button type="danger" size="mini" icon="el-icon-video-pause"
            v-if="(scope.row.isEnable === 1 && scope.row.isCheck === 1) || (scope.row.isEnable === 1 && scope.row.flowEndStatus === 1)"
            @click="dealerStatus(scope.row.id, 0)">暂停
          </el-button>
          <el-button type="danger" size="mini" icon="el-icon-video-pause"
            v-if="checkPermission(['b:merchant:dealerList:termination']) && ((scope.row.isEnable === 1 && scope.row.isCheck === 1) || (scope.row.isEnable === 1 && scope.row.flowEndStatus === 1))"
            @click="dealerStatus(scope.row.id, -3)">终止
          </el-button>
          <el-button type="danger" size="mini" icon="el-icon-circle-check"
            v-if="checkPermission(['b:risk:user:clear']) && (scope.row.isEnable === -1 || scope.row.isEnable === -2)"
            @click="dealerStatus(scope.row.id, 1)">解封
          </el-button>
          <el-button type="success" size="mini" icon="el-icon-video-pause"
            v-if="scope.row.paymentIsComplete === '0' && checkPermission(['b:merchant:dealerList:allgone'])"
            @click="dealerPaymentIsComplete(scope.row.id, scope.row.isEnable)">完款
          </el-button>
          <el-button type="warning" size="mini" icon="el-icon-link"
            v-if="false && (checkPermission(['b:merchant:dealerList:assignDelivery'])) && ((scope.row.isOldData === 1 && scope.row.isCheck === 1) || (scope.row.isOldData === 0 && scope.row.flowEndStatus === 1))"
            @click="openAssignDelivery(scope.row.merchantCode)">指定交付中心
          </el-button>
          <el-button type="warning" size="mini" icon="el-icon-link"
            v-if="false && (checkPermission(['b:merchant:dealerList:liftDelivery'])) && ((scope.row.isOldData === 1 && scope.row.isCheck === 1) || (scope.row.isOldData === 0 && scope.row.flowEndStatus === 1))"
            @click="liftDelivery(scope.row.merchantCode)">解除交付中心
          </el-button>
        </template>
      </el-table-column>
      <el-table-column prop="merchantName" label="名称" align="center" width="200px"></el-table-column>
      <el-table-column prop="marketPartner" label="推荐人编号" align="center" width="120px"></el-table-column>
      <el-table-column prop="refereeCode" label="上级编号" align="center" width="120px"></el-table-column>
      >
      <el-table-column prop="realName" label="负责人" align="center" width="100px"></el-table-column>
      <el-table-column prop="address" label="所在地区" align="center" width="130px"></el-table-column>
      <el-table-column prop="rank" label="级别" align="center" :show-overflow-tooltip="true"></el-table-column>
      <el-table-column prop="sumChargeMoney" label="累计充值金额（元）" align="center" width="150px">
        <template slot-scope="scope">
          <span v-if="scope.row.sumChargeMoney === null ||
            scope.row.sumChargeMoney === ''
            ">0.00</span>
          <span v-else>{{ scope.row.sumChargeMoney }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="sumRebateMoney" label="累计返点金额（元）" align="center" width="110px">
        <template slot-scope="scope">
          <span v-if="scope.row.sumRebateMoney === null ||
            scope.row.sumRebateMoney === ''
            ">0.00</span>
          <span v-else>{{ scope.row.sumRebateMoney }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="sumGiveMoney" label="累计赠送金额（元）" align="center" width="110px">
        <template slot-scope="scope">
          <span v-if="scope.row.sumGiveMoney === null ||
            scope.row.sumGiveMoney === ''
            ">0.00</span>
          <span v-else>{{ scope.row.sumGiveMoney }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="accountMoney" label="账户余额（元）" align="center" width="110px">
        <template slot-scope="scope">
          <span v-if="scope.row.accountMoney === null ||
            scope.row.accountMoney === ''
            ">0.00</span>
          <span v-else>{{ scope.row.accountMoney }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column prop="withdrawMoney" label="账下余额（元）" align="center" width="110px"> <template slot-scope="scope">
          <span v-if="
              scope.row.withdrawMoney === null ||
              scope.row.withdrawMoney === ''
            ">0.00</span>
          <span v-else>{{ scope.row.withdrawMoney }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="withdrawMoneyOl" label="线上余额（元）" align="center" width="110px">
        <template slot-scope="scope">
          <span v-if="
              scope.row.withdrawMoneyOl === null ||
              scope.row.withdrawMoneyOl === ''
            ">0.00</span>
          <span v-else>{{ scope.row.withdrawMoneyOl }}</span>
        </template>
      </el-table-column>-->
      <el-table-column prop="subDealerCount" label="下级托管中心" align="center"></el-table-column>
      <el-table-column prop="subSchoolCount" label="下级门店" align="center"></el-table-column>
      <el-table-column prop="paymentIsComplete" label="是否完款" align="center">
        <template slot-scope="scope">
          <span v-if="scope.row.paymentIsComplete === '1'" class="green">完款</span>
          <span v-else class="red">未完款</span>
        </template>
      </el-table-column>
      <el-table-column prop="channelManagerName" label="渠道管理员" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.channelManagerName ? scope.row.channelManagerName : '' }}</span>
          <el-link v-if="scope.row.channelManagerName" type="primary"
            @click="showChannelManagerDetail(scope.row)">详情</el-link>
        </template>
      </el-table-column>
      <el-table-column prop="flowStatus" label="流程状态" align="center">
        <!--        <template slot-scope="scope">
                  <span class="green" v-if="scope.row.isCheck === 1">通过审核</span>
                  <span class="red" v-if="scope.row.isCheck === 0">等待总部审核</span>
                  <span class="blue" v-if="scope.row.isCheck === 2">未通过分公司审核</span>
                  <span v-if="scope.row.isCheck === 3" class="red">等待分公司审核</span>
                  <span v-if="scope.row.isCheck === -3" class="red">未通过市级服务商审核</span>
                  <span class="red" v-if="scope.row.isCheck === 4">等待市级服务商审核</span>
                  <span class="blue" v-if="scope.row.isCheck === -4">未通过总部审核</span>
                  <el-button
                    v-if="(scope.row.isCheck ==2||scope.row.isCheck ==-3||scope.row.isCheck ==-4) && scope.row.checkReason !=''"
                    type="text"
                    @click="open(scope.row.checkReason)"
                    showCancelButton="false"
                  >查看详情
                  </el-button>
                </template>
                -->
      </el-table-column>
      <el-table-column label="审核状态" align="center">
        <template slot-scope="scope">
          <span class="green"
            v-if="scope.row.isEnable === 1 && scope.row.isCheck === 0 && !scope.row.flowStatus">上级服务商通过审核</span>
          <span class="green" v-else-if="scope.row.isOldData === 1 && scope.row.isCheck === 1">通过审核</span>
          <span class="red" v-else-if="scope.row.isOldData === 1 && scope.row.isCheck === 0">等待审核</span>
          <span class="blue" v-else-if="scope.row.isOldData === 1 && scope.row.isCheck === 2">未通过分公司审核</span>
          <span v-else-if="scope.row.isOldData === 1 && scope.row.isCheck === 3" class="red">等待分公司审核</span>
          <span v-else-if="scope.row.isOldData === 1 && scope.row.isCheck === -3" class="red">未通过市级服务商审核</span>
          <span class="red" v-else-if="scope.row.isOldData === 1 && scope.row.isCheck === 4">等待市级服务商审核</span>
          <span class="blue" v-else-if="scope.row.isOldData === 1 && scope.row.isCheck === -4">未通过总部审核</span>
          <span class="green" v-else-if="scope.row.isOldData === 0 && scope.row.flowIsEnd === 1">审核流程结束</span>
          <span class="red" v-else-if="scope.row.isOldData === 0 && scope.row.flowIsEnd === 0">审核中</span>
          <el-button
            v-if="(scope.row.isCheck == 2 || scope.row.isCheck == -3 || scope.row.isCheck == -4) && scope.row.checkReason != ''"
            type="text" @click="open(scope.row.checkReason)" showCancelButton="false">查看详情
          </el-button>
        </template>
      </el-table-column>
      <el-table-column prop="isEnable" label="账户状态" align="center">
        <template slot-scope="scope">
          <span class="red" v-if="scope.row.isEnable === -1">系统关闭</span>
          <span class="red" v-else-if="scope.row.isEnable === -3">终止</span>
          <span class="green" v-else-if="scope.row.isEnable === 1">开通</span>
          <span class="red" v-if="scope.row.isEnable === 0">暂停</span>
        </template>
      </el-table-column>
      <el-table-column prop="regTime" label="添加时间" align="center"></el-table-column>

    </el-table>

    <!--渠道管理员信息-->
    <el-dialog title="渠道管理员信息" :visible.sync="dialogVisibleForChannelManager" width="30%">
      <el-form ref="temp" label-position="left" label-width="120px">
        <el-form-item label="名称: ">
          <el-input disabled v-model="channelManager.realName" placeholder="请输入内容"></el-input>
        </el-form-item>
        <el-form-item label="编号: ">
          <el-input disabled v-model="channelManager.channelManagerCode" placeholder="请输入内容"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisibleForChannelManager = false">关 闭</el-button>
      </span>
    </el-dialog>




    <!--弹出窗口：指派交付中心-->
    <el-dialog :title="textMap[dialogStatu]" :visible.sync="dialogFormDelivery" width="40%" :close-on-click-modal='false'
      @close="dialogFormDelivery = false">
      <template>
        <el-input placeholder="请输入编号" style="width: 40%" v-model="searchMerchantCode" clearable>
        </el-input>
        <el-button type="primary" mini style="margin-left: 10px" title="搜索" @click="searchChannel()">搜索</el-button>

        <el-table :data="tableDataDelivery" v-loading="tableLoading2" highlight-current-row ref="singleTable" height="550"
          border style="width: 100%">
          <el-table-column type="index" width="50"></el-table-column>
          <el-table-column sortable prop="merchantCode" label="编号" width="180"></el-table-column>
          <el-table-column sortable prop="merchantName" label="名称" width="180"></el-table-column>
          <el-table-column label="操作">
            <template slot-scope="scope">
              <el-button type="primary" mini title="指派至该交付中心"
                @click="assignDelivery(scope.row.merchantCode)">指派</el-button>
            </template>
          </el-table-column>
          <el-table-column sortable prop="regTime" label="添加时间">
          </el-table-column>

        </el-table>
      </template>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormDelivery = false">取消</el-button>
      </div>
    </el-dialog>



    <el-dialog :show-close="false" :close-on-press-escape="false" :distinguishCancelAndClose="true" title="审核弹框"
      :visible.sync="showLoginAccount" width="70%" :close-on-click-modal="false" @close="close">
      <el-form :ref="'isCheckStatu'" :rules="rules" :model="isCheckStatu" label-position="left" label-width="120px"
        style="width: 100%">
        <el-form-item label="开户金额" prop="openMoney">
          <el-col :xs="24" :span="12">
            <el-input v-model="isCheckStatu.openMoney" :disabled="disabled" />
          </el-col>
        </el-form-item>
        <el-form-item label="托管中心级别：" prop="rank">
          <el-col :xs="24" :span="12">
            <el-select v-model="isCheckStatu.rank" placeholder="请选择" style="width: 100%">
              <el-option v-for="item in [
                { value: 'A', label: 'A级别', disabled: true },
                { value: 'B', label: 'B级别', disabled: true },
                { value: 'C', label: 'C级别', disabled: true },
                { value: 'D', label: 'D级别', disabled: false },
                { value: 'E', label: 'E级别', disabled: false },
                { value: 'F', label: 'F级别', disabled: false },
              ]" :key="item.value" :label="item.label" :value="item.value" :disabled="item.disabled"></el-option>
            </el-select>
          </el-col>
        </el-form-item>
        <el-form-item label="审核" prop="isCheck">
          <el-col :xs="24" :span="12">
            <el-select v-model="isCheckStatu.isCheck" placeholder="请选择" style="width: 100%">
              <el-option v-for="item in [
                { value: 1, label: '通过' },
                { value: 0, label: '不通过' },
              ]" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-col>
        </el-form-item>
        <el-form-item label="审核意见" prop="checkReason">
          <el-col :xs="24" :span="12">
            <el-input type="textarea" resize="none" :rows="4" v-model="isCheckStatu.checkReason" />
          </el-col>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="dealerIsCheck">确定</el-button>
        <el-button size="mini" @click="closeLogin">关闭</el-button>
      </div>
    </el-dialog>
    <!-- 分页 -->
    <el-col :span="20">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>
  </div>
</template>

<script>
import checkPermission from "@/utils/permission";
import authenticationApi from "@/api/authentication";
import dealerListApi from "@/api/dealerList";
import areasDealerApi from "@/api/areasDealerList";
import { mapGetters } from "vuex";
import { pageParamNames } from "@/utils/constants";
import deliveryCenterApi from "@/api/delivery/deliveryCenter";

export default {
  // name: "dealerList",
  data() {
    return {
      channelManager: {},
      dialogVisibleForChannelManager: false,
      searchMerchantCode: "",
      branchOfficeMerchantCode: "",
      dialogFormDelivery: false,
      textMap: {
        assign: '指定交付中心',
      },
      tableDataDelivery: [],
      tableLoading2: false,
      dialogStatu: 'assign',
      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      companyCode: "",
      roleTag: "",
      roleName: "",
      exportLoading: false, //导出加载
      exportLoading1: false,
      RegTime: "",
      tableData: [],
      dataQuery: {
        isCheck: "",
        merchantCode: "",
        name: "",
        merchantName: "",
        realName: "",
        address: "",
        startDate: "",
        endDate: "",
        flowEndStatus: ""
      },
      dialogVisible: false,
      updateMarketDate: {},
      addMarketDate: {},
      showLoginAccount: false,
      regTime: "",
      ruls: [],
      disabled: true,
      isCheckStatu: {}, //审核状态
      rules: {
        rank: [
          {
            required: true,
            message: "必填",
            trigger: "blur"
          }
        ],
        isCheck: [
          {
            required: true,
            message: "必填",
            trigger: "blur"
          }
        ]
      }
    };
  },
  computed: {
    ...mapGetters(["roles"])
  },
  created() {
    var jsonData = JSON.stringify(this.roles);
    var s = JSON.stringify(this.roles).includes("admin");
    console.log(jsonData + "qc");
    this.roleName = s;
    console.log(this.roleName);
    this.fetchData01();
  },
  mounted() {
    authenticationApi.checkAccountBalance().then(res => {
      console.log(res.data.data.roleTag + "qc");
      this.roleTag = res.data.data.roleTag;
    });
  },
  methods: {
    checkPermission,
    fetchData01() {
      this.tablePage = {
        currentPage: 1,
        size: this.tablePage.size,
        totalPage: null,
        totalItems: null
      };
      this.fetchData();
    },
    showChannelManagerDetail(row) {
      this.channelManager.realName = row.channelManagerRealName;
      this.channelManager.channelManagerCode = row.channelManagerCode;
      this.dialogVisibleForChannelManager = true;
    },
    // 查询+搜索课程列表
    fetchData() {
      const that = this;
      console.log();
      var a = that.regTime;
      if (a != null) {
        that.dataQuery.startDate = a[0];
        that.dataQuery.endDate = a[1];
      } else {
        that.dataQuery.startDate = "";
        that.dataQuery.endDate = "";
      }
      that.tableLoading = true;
      dealerListApi
        .dealerList(
          that.tablePage.currentPage,
          that.tablePage.size,
          that.dataQuery
        )
        .then(res => {
          that.tableData = res.data.data;
          // console.log(res)
          that.tableLoading = false;
          // 设置后台返回的分页参数
          pageParamNames.forEach(name =>
            that.$set(that.tablePage, name, parseInt(res.data[name]))
          );
        });
    },
    //打开指定交付中心窗口
    openAssignDelivery(merchantCode) {
      this.dialogFormDelivery = true;
      this.branchOfficeMerchantCode = merchantCode;
      //查询所有交付中心
      deliveryCenterApi.allList().then(res => {
        this.tableDataDelivery = res.data;
      }).catch(err => { })
    },
    //表格数据选中和跳转到指定位置
    searchChannel() {
      for (let i = 0; i < this.tableDataDelivery.length; i++) {
        if (this.tableDataDelivery[i].merchantCode === this.searchMerchantCode) {
          if (!this.$refs['singleTable']) return //不存在这个表格则返回
          let elTable = this.$refs['singleTable'].$el
          if (!elTable) return
          const scrollParent = elTable.querySelector('.el-table__body-wrapper')
          const targetTop = elTable.querySelectorAll('.el-table__body tr')[i].getBoundingClientRect().top //该行的位置
          const containerTop = elTable.querySelector('.el-table__body').getBoundingClientRect().top //body的位置
          //跳转
          scrollParent.scrollTop = targetTop - containerTop; //跳转到存下编辑行的ScrollTop
          this.$refs.singleTable.setCurrentRow(this.tableDataDelivery[i]);
        }
      }
    },
    assignDelivery(merchantCode) {
      this.tableLoading2 = true;
      deliveryCenterApi.assignDelivery(this.branchOfficeMerchantCode, merchantCode).then(res => {
        this.tableLoading2 = false;
        this.$message.success("指派成功")
        this.dialogFormDelivery = false;
        this.fetchData();
      }).catch(err => { });
    },
    liftDelivery(merchantCode) {
      this.$confirm('解除通过该账户绑定的交付中心?')
        .then(_ => {
          deliveryCenterApi.liftDelivery(merchantCode).then(res => {
            this.$message.success("解除绑定成功");
            this.fetchData();
          }).catch(err => { })
        })
        .catch(_ => {
        });
    },
    //新增操作
    clickAdd() {
      const that = this;
      window.localStorage.setItem("addOrUpdateDealer", JSON.stringify(true));
      window.localStorage.removeItem("dealerId");
      that.$router.push({
        path: "/merchantManagement/dealerAdd",
        query: {
          addOrUpdate: true
        }
      });
    },
    // 打开修改登陆账号
    openLogin() {
      this.showLoginAccount = true;
    },
    closeLogin() {
      this.isCheckStatu.isCheck = "";
      this.showLoginAccount = false;
    },
    //审核
    isCheckDealer(id, openMoney) {
      this.showLoginAccount = true;
      this.isCheckStatu.openMoney = openMoney;
      this.isCheckStatu.id = id;
    },
    //审核页面
    dealerIsCheck() {
      dealerListApi.isCheckStatus(this.isCheckStatu).then(res => {
        this.fetchData();
        this.isCheckStatu.isCheck = "";
        this.isCheckStatu.checkReason = "";
        this.showLoginAccount = false;
      });
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val;
      this.fetchData();
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val;
      this.fetchData();
    },
    //  导出
    exportFlow1() {
      const that = this;
      var a = that.regTime;
      if (a != null) {
        that.dataQuery.startDate = a[0];
        that.dataQuery.endDate = a[1];
      } else {
        that.dataQuery.startDate = "";
        that.dataQuery.endDate = "";
      }
      if (
        that.dataQuery.startDate === "" ||
        that.dataQuery.startDate === "" ||
        that.dataQuery.startDate === undefined
      ) {
        that.$message.info("开始时间不能为空");
        return false;
      }
      if (
        that.dataQuery.endDate === "" ||
        that.dataQuery.endDate === "" ||
        that.dataQuery.endDate === undefined
      ) {
        that.$message.info("结束时间不能为空");
        return false;
      }

      that.exportLoading1 = true;
      const data = {
        startDate: that.dataQuery.startDate,
        endDate: that.dataQuery.endDate
      };
      that.exportLoading1 = true;
      //
      dealerListApi.simpleMarketExecl1(data).then(
        res => {
          //           this.$notify.error({
          //               title: "操作失败",
          //               message: "文件下载失败"
          //             });
          const url = window.URL.createObjectURL(res);
          const link = document.createElement("a");
          link.style.display = "none";
          link.href = url; // 获取服务器端的文件名
          link.setAttribute("download", "托管中心运营数据列表.xls");
          document.body.appendChild(link);
          link.click();
          that.exportLoading1 = false;
        },
        s => {
          if (s === "error") {
            that.exportLoading1 = false;
          }
        }
      );
    },
    //导出
    exportFlow() {
      const that = this;
      that.exportLoading = true;
      //
      dealerListApi.simpleMarketExecl(that.dataQuery).then(res => {
        //           this.$notify.error({
        //               title: "操作失败",
        //               message: "文件下载失败"
        //             });
        const url = window.URL.createObjectURL(res);
        const link = document.createElement("a");
        link.style.display = "none";
        link.href = url; // 获取服务器端的文件名
        link.setAttribute("download", "托管中心列表.xls");
        document.body.appendChild(link);
        link.click();
        that.exportLoading = false;
      });
    },
    //审核理由
    open(chenckReason) {
      const h = this.$createElement;
      this.$msgbox({
        title: "审核理由",
        message: h("p", null, [
          h("i", { style: "color: #FF0802" }, chenckReason)
        ]),
        showCancelButton: false,
        confirmButtonText: "确定"
      });
    },
    coerceSuccess(id) {
      this.$confirm("确定强制通过审批吗？", "强制通过", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        dealerListApi.coerceSuccess(id).then(() => {
          this.$nextTick(() => this.fetchData());
          this.$message.success("操作成功");
        })
      });
    },
    //编辑
    updateDealerList(id, resubmit) {
      window.localStorage.setItem("dealerId", id);
      window.localStorage.setItem("addOrUpdateDealer", JSON.stringify(false));
      const that = this;
      that.$router.push({
        path: "/merchantManagement/dealerAdd",
        query: {
          addOrUpdate: false,
          id: id,
          isResubmit: resubmit
        }
      });
    },
    pickerOptions() {
    },
    close() {
    },
    //完款修改
    dealerPaymentIsComplete(id, paymentIsComplete) {
      if (paymentIsComplete == "0") {
        paymentIsComplete = 1;
      }
      const that = this;
      this.$confirm("确定操作吗？", "完款", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        dealerListApi
          .updatePaymentIsComplete(id, paymentIsComplete)
          .then(res => {
            if (res.success) {
              that.fetchData01();
              that.$message.success("操作成功");
            }
          });
      });
    },
    //开通和暂停
    // 课程开通与暂停
    dealerStatus(id, status) {
      const that = this;
      this.$confirm("确定操作吗?", "修改状态", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        dealerListApi.dealerStatus(id, status).then(res => {
          that.$nextTick(() => that.fetchData());
          that.$message.success("修改成功!");
        })
      })
    },
    // //开通二级推荐分润
    //   updateReProfitRank(id,reProfitRank){
    //   if(reProfitRank===0){
    //     reProfitRank = 1;
    //   }else{
    //     reProfitRank = 0;
    //   }
    //     const that = this;
    //   this.$confirm("确定操作吗?", "开通推荐二级分润", {
    //       confirmButtonText: "确定",
    //       cancelButtonText: "取消",
    //       type: "warning",
    //     })
    //     .then(() => {
    //       dealerListApi.updateReProfitRank(id, reProfitRank).then((res) => {
    //         if (res.success) {
    //           that.fetchData01();
    //           that.$message.success("操作成功");
    //         }
    //       });
    //     })
    //     .catch((err) => {});
    // },
    //总部审核
    agree(id, isCheck) {
      const that = this;
      this.$confirm("确定操作吗?", "审核", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          areasDealerApi
            .updateIsCheck(id, isCheck)
            .then(res => {
              that.$nextTick(() => that.fetchData());
              that.$message.success("审核成功!");
            })
            .catch(err => {
            });
        })
        .catch(err => {
        });
    }
  }
};
</script>

<style>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.red {
  color: red;
}

.green {
  color: green;
}

.blue {
  color: blue;
}

@media (max-width: 767px) {
  .el-message-box {
    width: 80% !important;
  }
}
</style>
