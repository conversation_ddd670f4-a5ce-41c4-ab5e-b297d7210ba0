<template>
  <div class="app-container">
    <el-form ref="examinationRef" label-position="right" label-width="110px" :model="examinationData" style="width: 100%; margin: 0 auto" class="mb20">
      <div style="margin-bottom: 20px; margin-right: 50px; display: flex; justify-content: center">{{ listeningName }}</div>
      <div v-for="(item, index) in examinationData.items" :key="index">
        <el-card style="background-color: aliceblue; margin-bottom: 20px">
          <el-row type="flex" justify="center">
            <el-col :xs="24" :lg="16">
              <el-row>
                <el-col :span="20">
                  <el-form-item
                    label="题目序号"
                    :prop="'items.' + index + '.questionNumber'"
                    :rules="[
                      {
                        required: true,
                        message: '必填',
                        trigger: 'change'
                      }
                    ]"
                  >
                    <el-input type="number" @input="handleInput(item)" v-model="item.questionNumber"></el-input>
                  </el-form-item>
                  <el-form-item
                    label="题目标题"
                    :prop="'items.' + index + '.questionText'"
                    :rules="[
                      {
                        required: true,
                        message: '必填',
                        trigger: 'change'
                      }
                    ]"
                  >
                    <el-input v-model="item.questionText"></el-input>
                  </el-form-item>
                  <el-form-item
                    v-for="(option, optionIndex) in item.options"
                    :key="option.choiceOption"
                    :label="'答案' + option.choiceOption"
                    :prop="'items.' + index + '.options.' + optionIndex + '.content'"
                    :rules="
                      option.choiceOption === 'D'
                        ? []
                        : [
                            {
                              required: true,
                              message: '必填',
                              trigger: 'change'
                            }
                          ]
                    "
                  >
                    <el-input v-model="option.content"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="2" :offset="1">
                  <el-button v-if="addOrUpdate" type="danger" icon="el-icon-close" @click="updateDeleteForm(item)">删除</el-button>
                </el-col>
              </el-row>
              <!-- 答案 -->
              <el-row>
                <el-col :span="20">
                  <el-form-item
                    label="正确答案"
                    :prop="'items.' + index + '.correctAnswer'"
                    :rules="[
                      {
                        required: true,
                        message: '必填',
                        trigger: 'blur'
                      }
                    ]"
                  >
                    <el-col :span="24">
                      <el-select clearable style="width: 100%" v-model="item.correctAnswer" class="reading-textarea">
                        <el-option label="A" value="A"></el-option>
                        <el-option label="B" value="B"></el-option>
                        <el-option label="C" value="C"></el-option>
                        <el-option v-show="item.options.some((option) => option.choiceOption === 'D' && option.content)" label="D" value="D"></el-option>
                      </el-select>
                    </el-col>
                  </el-form-item>
                </el-col>
                <el-col :span="3" :offset="1">
                  <el-button v-if="addOrUpdate && !item.hasPerformedAdd" type="primary" icon="el-icon-plus" @click="addForm">新增</el-button>
                </el-col>
              </el-row>
            </el-col>
          </el-row>
        </el-card>
      </div>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-row>
        <el-col :span="4" :offset="6">
          <el-button v-if="addOrUpdate" type="primary" @click="addActiveFun()">确定</el-button>
          <el-button v-if="!addOrUpdate" type="primary" @click="updateReadingFun()">确定</el-button>
        </el-col>
        <el-col :span="4" :offset="4">
          <el-button type="primary" @click="getList">关闭</el-button>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
  import listenQuestionAPI from '@/api/omnipotentListening/listenQuestionAPI';
  export default {
    data() {
      return {
        // 分页
        updateReadingData: {},
        listeningName: '',
        addOrUpdate: true,
        examinationRef: '',
        //增加内容
        examinationData: {
          items: [
            {
              courseId: '',
              questionNumber: '',
              questionText: '',
              options: [
                { choiceOption: 'A', content: '' },
                { choiceOption: 'B', content: '' },
                { choiceOption: 'C', content: '' },
                { choiceOption: 'D', content: '' }
              ],
              correctAnswer: '', // 选项答案
              hasPerformedAdd: false
            }
          ]
        },
        editId: '',
        materialsId: ''
      };
    },
    watch: {
      'examinationData.items': {
        handler(newItems) {
          newItems.forEach((item) => {
            if (item.options.some((option) => option.choiceOption === 'D' && !option.content) && item.correctAnswer === 'D') {
              item.correctAnswer = '';
            }
          });
        },
        deep: true
      }
    },
    created() {
      this.courseId = window.localStorage.getItem('courseId');
      this.listeningName = window.localStorage.getItem('listeningName');
      this.editId = window.localStorage.getItem('editId');
      this.materialsId = window.localStorage.getItem('materialsId');
      this.addOrUpdate = this.$route.query.addOrUpdate;
      this.openEdit();
      if (!this.addOrUpdate) {
        this.setTitle('编辑试题');
      } else {
        this.setTitle('添加试题');
      }
    },
    methods: {
      // 动态设置标签页标题
      setTitle(title) {
        let i = 0;
        let visitedViews = this.$store.getters.visitedViews;
        visitedViews.forEach((route, index) => {
          if (this.$route.path == route.path) {
            i = index;
          }
        });
        this.$route.meta.title = title;
        visitedViews[i].title = title;
      },
      handleInput(item) {
        let numberItem = item;
        // 去除前导零和非数字字符
        numberItem.questionNumber = numberItem.questionNumber.replace(/^(0+)|[^\d]+/g, '');

        // 限制最大值为 100
        if (parseInt(numberItem.questionNumber, 10) > 100) {
          this.$message.warning('试题题目编号不能超过100');
          numberItem.questionNumber = '100';
        }
      },
      //删除表单
      updateDeleteForm: function (ele) {
        if (this.examinationData.items.length === 1) {
          this.$message({
            message: '至少要留一道题目',
            type: 'warning',
            duration: 1000
          });
          return;
        }
        var index = this.examinationData.items.indexOf(ele);
        if (index !== -1) {
          this.examinationData.items.splice(index, 1);
          this.$message({
            message: '删除成功',
            type: 'warning',
            duration: 1000
          });
        }
        this.examinationData.items.forEach((item, idx) => {
          if (index - 1 === idx) {
            this.$set(item, 'hasPerformedAdd', false);
          }
        });
      },
      //增加表单
      addForm: function () {
        this.examinationData.items.push({
          questionNumber: '',
          questionText: '',
          options: [
            { choiceOption: 'A', content: '' },
            { choiceOption: 'B', content: '' },
            { choiceOption: 'C', content: '' },
            { choiceOption: 'D', content: '' }
          ],
          correctAnswer: ''
        });
        // 获取刚刚添加的那一项的索引
        const newItemIndex = this.examinationData.items.length - 2;
        // 将刚刚添加的那一项的hasPerformedAdd属性设置为true
        this.examinationData.items[newItemIndex].hasPerformedAdd = true;
        this.examinationData.items.forEach((val) => {});
      },
      // 编辑
      openEdit() {
        if (!this.addOrUpdate) {
          const loading = this.$loading({
            lock: true,
            text: '请稍等...',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          });
          listenQuestionAPI.findListeningQuestion({ id: this.editId }).then((res) => {
            console.log(res, '编辑');
            if (res.success) {
              const data = res.data;
              this.examinationData.items[0].questionNumber = data.questionNumber;
              this.examinationData.items[0].questionText = data.questionText;
              this.examinationData.items[0].options = data.listeningOptionVoList;
              this.examinationData.items[0].correctAnswer = data.correctAnswer;
              loading.close();
            }
          });
        }
      },

      // 新增表单
      addActiveFun() {
        const that = this;
        let isValidationPassed = true;
        that.$refs['examinationRef'].validate((valid) => {
          if (!valid) {
            return false;
          }

          const paramsList = that.examinationData.items.map((item) => {
            let params = {
              materialsId: that.materialsId,
              courseId: that.courseId,
              questionNumber: item.questionNumber,
              questionText: item.questionText,
              listeningOptionCoList: item.options
                .filter((option) => option.content)
                .map((option, optionIndex) => {
                  return {
                    ...option,
                    optionIsAnswer: item.correctAnswer === option.choiceOption ? 1 : 0
                  };
                }),
              correctAnswer: item.correctAnswer
            };
            return params;
          });
          console.log(paramsList, '新增表单校验数据');
          if (isValidationPassed) {
            const loading = this.$loading({
              lock: true,
              text: '新增试题',
              spinner: 'el-icon-loading',
              background: 'rgba(0, 0, 0, 0.7)'
            });
            // 新增 api 请求
            listenQuestionAPI
              .addListeningQuestion(paramsList)
              .then((res) => {
                if (res.success) {
                  that.$message.success('添加成功');
                  this.$store.dispatch('delVisitedViews', this.$route);
                  that.$router.push({ path: '/course/listenQuestionList' });
                  loading.close();
                }
              })
              .catch((err) => {
                loading.close();
              });
          } else {
            return;
          }
        });
      },
      // 编辑题目
      updateReadingFun() {
        let isValidationPassed = true;
        const that = this;
        that.$refs['examinationRef'].validate((valid) => {
          if (!valid) {
            return false;
          }
          console.log('submit!', that.examinationData.items);
          const paramsList = that.examinationData.items.map((item) => {
            let params = {
              materialsId: that.materialsId,
              id: that.editId,
              courseId: that.courseId,
              questionNumber: String(item.questionNumber),
              questionText: item.questionText,
              listeningOptionCoList: item.options
                .filter((option) => option.content)
                .map((option, optionIndex) => {
                  return {
                    ...option,
                    optionIsAnswer: item.correctAnswer === option.choiceOption ? 1 : 0
                  };
                }),
              correctAnswer: item.correctAnswer
            };

            return params;
          });

          if (isValidationPassed) {
            console.log(paramsList, '编辑表单校验数据');
            const loading = this.$loading({
              lock: true,
              text: '编辑试题',
              spinner: 'el-icon-loading',
              background: 'rgba(0, 0, 0, 0.7)'
            });
            listenQuestionAPI
              .editListeningQuestion(paramsList)
              .then((res) => {
                this.$message.success('编辑成功');
                this.$store.dispatch('delVisitedViews', this.$route);
                that.$router.push({ path: '/course/listenQuestionList' });
                loading.close();
              })
              .catch((err) => {
                loading.close();
              });
          } else {
            return;
          }
        });
      },

      getList() {
        this.$store.dispatch('delVisitedViews', this.$route);
        this.$router.go(-1);
      }
    }
  };
</script>

<style>
  .lh36 {
    line-height: 36px;
    font-size: 14px;
  }

  .SearchForm {
    width: 100%;
    margin-bottom: 15px;
    background: #fff;
    border-radius: 6px;
    padding: 5px 0;
    padding-left: 5px;
    box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.2);
  }

  .btn-add {
    padding: 5px;
  }

  .red {
    color: red;
  }

  .green {
    color: green;
  }

  .course-table {
    text-align: center;
  }

  .course-table td,
  .course-table th {
    padding: 5px 0;
    text-align: center;
  }

  .course-table button {
    padding: 2px;
  }

  .icon-stop {
    float: left;
    display: inline-block;
    width: 12px;
    height: 12px;
    /* background: url('../../icons/stop.png') no-repeat top center/contain; */
  }

  .mt22 {
    margin-top: 22px;
  }

  .mb20 {
    margin-bottom: 20px;
  }

  .pd10 {
    padding: 10px;
  }
  .reading-textarea textarea {
    height: 200px;
  }
</style>
