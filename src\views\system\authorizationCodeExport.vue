<meta http-equiv="Content-Type" content="multipart/form-data; charset=utf-8"/>
<template>

  <div class="app-container">
    <!-- 搜索 -->
    <el-form :inline="true" ref="form" label-position="top" class="SearchForm">
      <el-row style="padding: 10px 10px 0 10px">
        <el-col :span="6">
          <el-form-item label="SN码: ">
            <el-input v-model="dataQuery.authorizationCode" @keyup.enter.native="fetchData01()" placeholder="请输入授权码:"
                      clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="商户编号: ">
            <el-input v-model="dataQuery.merchantCode" @keyup.enter.native="fetchData01()" placeholder="请输入商户编号:"
                      clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="绑定设备状态：">
            <el-select v-model="dataQuery.isEnable" value-key="value" placeholder="请选择" clearable>
              <el-option label="已绑定" :value="1"/>
              <el-option label="未绑定" :value="0"/>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="导出状态：">
            <el-select v-model="dataQuery.importEnable" value-key="value" placeholder="请选择"
                       clearable>
              <el-option label="已导出" :value="1"/>
              <el-option label="未导出" :value="2"/>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="授权码类型：">
            <el-select v-model="dataQuery.authType" value-key="value" placeholder="请选择" clearable>
              <el-option label="平板pad" value="PAD"/>
              <el-option label="智能台灯" value="LAMP"/>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="添加时间: ">
            <el-date-picker v-model="regTime" type="daterange" value-format="yyyy-MM-dd HH:mm:ss"
                            align="right" unlink-panels range-separator="至"
                            clearable start-placeholder="开始日期" end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="fetchData01()">搜索</el-button>
          </el-form-item>
          <el-form-item>
            <el-button style="margin:0 20px" type="primary" size="small" icon="el-icon-download"
                       @click="downloadTemplate()">
              模板下载
            </el-button>
          </el-form-item>
          <el-form-item>
            <el-upload
              :headers="myheard"
              class="upload-demo"
              :on-exceed="justPictureNum"
              :limit="1"
              :on-success="this.addToFetchData"
              :on-error="this.error"
              :action="baseUrl+'znyy/author/code/execel/import'">
              <el-button size="small" type="primary" style="display: inline-block">点击上传</el-button>
              <div slot="tip" style="color: red; font-size: 11px">只能上传Excel文件</div>
            </el-upload>
          </el-form-item>
          <el-form-item>
            <el-button size="small" type="primary" style="margin-right:20px" @click="exportAuthorizationCode">导出
            </el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <div class="SearchForm">
      <!-- 表格 -->
      <el-table class="common-table" :data="tableData" stripe border :default-sort="{prop: 'date', order: 'descending'}"
                v-loading="tableLoading"
      >
        <el-table-column prop="authorizationCode" label="SN码" sortable></el-table-column>
        <el-table-column prop="imei" label="IMEI码" sortable></el-table-column>
        <el-table-column prop="merchantCode" label="绑定商户" sortable>
          <template slot-scope="scope">
            <span v-if="scope.row.merchantCode ==''">未绑定商户</span>
            <span v-if="scope.row.merchantCode !==''">{{ scope.row.merchantCode }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="importEnable" label="导出状态" sortable>
          <template slot-scope="scope">
            <span v-if="scope.row.importEnable===0">未导出</span>
            <span v-if="scope.row.importEnable===1">已导出</span>
          </template>
        </el-table-column>
        <el-table-column prop="isEnable" label="绑定设备状态" sortable>
          <template slot-scope="scope">
            <span v-if="scope.row.isEnable===0">未绑定</span>
            <span v-if="scope.row.isEnable===1">已绑定</span>
          </template>
        </el-table-column>
        <el-table-column prop="authType" label="授权码类型">
          <template slot-scope="scope">
            <span v-if="scope.row.authType==='PAD'">平板pad</span>
            <span v-if="scope.row.authType==='LAMP'">智能台灯</span>
          </template>
        </el-table-column>
        <el-table-column prop="addTime" label="添加时间" sortable show-overflow-tooltip></el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <el-col :span="20">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
                     layout="total, sizes, prev, pager, next, jumper"
                     :total="tablePage.totalItems" @size-change="handleSizeChange" @current-change="handleCurrentChange"
      />
    </el-col>
    <el-dialog title="导出授权码" :visible.sync="dialogVisibleBinding" width="70%" :close-on-click-modal="false"
               @close="close"
    >
      <el-form :ref="'bindingAuthorizationCode'" :rules="rulesCodes" :model="bindingAuthorizationCode"
               label-position="left" label-width="120px" style="width: 100%;"
      >
        <el-form-item label="导出数" prop="score">
          <el-col :xs="24" :span="18">
            <el-input type="number" v-model="bindingAuthorizationCode.count" maxlength="4" isNumber="true"
                      @blur="BlurText(bindingAuthorizationCode.count)"
            ></el-input>
          </el-col>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button v-if="addOrUpdate" size="mini" type="primary" @click="exportList('bindingAuthorizationCode')">导出授权码
        </el-button>
        <el-button size="mini" @click="close">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import authorizationCodeApi from '@/api/authorizationCode'
import {baseUrl, pageParamNames} from '@/utils/constants'
import store from '@/store'

export default {
  data() {
    return {
      baseUrl: baseUrl,
      typeValue: '',
      myheard: {
        'x-www-iap-assertion': store.getters.token,
        'www-cid': 'dx_znyy_resource'
      },

      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      regTime: '',
      tableLoading: false,
      dataQuery: {
        title: '',
        wordUpperLimit: '',
        wordLowerLimit: ''
      },
      activeType: [], // 活动类型
      addCourseWord: {},
      tableData: [], //表格数据
      dialogVisible: false, // 修改弹窗是否展示
      dialogVisibleBinding: false,
      addOrUpdate: true, // 是新增还是修改
      isRouterAlive: true, //局部刷新
      addAuthorizationCodeData: {}, // 生成授权码
      bindingAuthorizationCode: {},
      exportLoading: false,
      rules: { // 表单提交规则
        count: [{
          required: true,
          message: '必填',
          trigger: 'blur'
        }]
      },
      rulesCodes: {
        count: [{
          required: true,
          message: '必填',
          trigger: 'blur'
        }]
      }

    }

  },
  created() {
    this.fetchData()
  },
  methods: {
    downloadTemplate() {
      window.location.href = 'https://document.dxznjy.com/authorizationTemplate/19289e5f-f818-4ce1-ad4b-7349fd32e247.xlsx';
    },
    error(err) {
      this.$message.error(JSON.parse(err.message).message);
    },
    // 上传文件数量超限
    justPictureNum(file, fileList) {
      this.$message.warning(`当前限制选择1个文件`)
    },

    fetchData01() {
      this.tablePage = {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      }
      this.fetchData()
    },
    addToFetchData(response, file, fileList) {
      const that = this
      if (that.regTime != null) {
        if (that.regTime.length > 0) {
          this.dataQuery.startDate = that.regTime[0]
          this.dataQuery.endDate = that.regTime[1]
        } else {
          this.dataQuery.startDate = ''
          this.dataQuery.endDate = ''
        }
      } else {
        this.dataQuery.startDate = ''
        this.dataQuery.endDate = ''
      }
      this.$message({
        message: "上传成功！数量：" + response.data + "条",
        type: 'success'
      });
      that.tableLoading = true
      authorizationCodeApi.authorizationCodeList(that.tablePage.currentPage, that.tablePage.size, that.dataQuery).then(res => {
        that.tableData = res.data.data
        that.tableLoading = false
        // 设置后台返回的分页参数
        pageParamNames.forEach(name => that.$set(that.tablePage, name, parseInt(res.data[name])))
      })
    },
    // 查询+搜索课程列表
    fetchData(response, file, fileList) {
      const that = this
      if (that.regTime != null) {
        if (that.regTime.length > 0) {
          this.dataQuery.startDate = that.regTime[0]
          this.dataQuery.endDate = that.regTime[1]
        } else {
          this.dataQuery.startDate = ''
          this.dataQuery.endDate = ''
        }
      } else {
        this.dataQuery.startDate = ''
        this.dataQuery.endDate = ''
      }
      that.tableLoading = true
      authorizationCodeApi.authorizationCodeList(that.tablePage.currentPage, that.tablePage.size, that.dataQuery).then(res => {
        that.tableData = res.data.data
        that.tableLoading = false
        // 设置后台返回的分页参数
        pageParamNames.forEach(name => that.$set(that.tablePage, name, parseInt(res.data[name])))
      })
    },
    //生成授权码
    clickAdd() {
      this.addAuthorizationCodeData = {
        'count': ''
      }
      this.dialogVisible = true
    },
    clearDate() {
      alert(12)
      this.dataQuery.startDate = ''
      this.dataQuery.endDate = ''
    },
    BlurText1(e) {
      if (e != undefined && e != '' && e != '' && e != null) {
        let boolean = new RegExp('^[1-9][0-9]*$').test(e)
        if (!boolean) {
          this.$message.warning('请输入正整数')
          this.addAuthorizationCodeData.count = ''
        }

      }
    },
    BlurText(e) {
      if (e != undefined && e != '' && e != '' && e != null) {
        let boolean = new RegExp('^[1-9][0-9]*$').test(e)
        if (!boolean) {
          this.$message.warning('请输入正整数')
          this.bindingAuthorizationCode.count = ''
        }

      }
    },

    dateVal(e) {
      if (e != null) {
        if (e.length > 0) {
          this.dataQuery.startDate = e[0]
          this.dataQuery.endDate = e[1]
        } else {
          this.dataQuery.startDate = ''
          this.dataQuery.endDate = ''
        }
      }

    },
    //导出授权码
    exportAuthorizationCode() {
      this.bindingAuthorizationCode = {
        'count': ''
      }
      this.dialogVisibleBinding = true
    },
    // 提交生成授权码
    addActiveFun(ele) {
      const that = this
      if (that.addAuthorizationCodeData.count === '' || that.addAuthorizationCodeData.count === '' || that.addAuthorizationCodeData.count === undefined) {
        this.$message.warning('数量不能为空')
        return false
      }

      that.$refs[ele].validate(valid => { // 表单验证
        if (valid) {
          const loading = this.$loading({
            lock: true,
            text: '生成授权码',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          })
          authorizationCodeApi.addAuthorizationCode(that.addAuthorizationCodeData.count).then(() => {
            that.dialogVisible = false
            loading.close()
            that.fetchData()
            that.$message.success('生成授权码')
          }, s => {
            if (s === 'error') {
              loading.close()
            }
          }).catch(err => {
            loading.close()
          })
        } else {
          console.log('error submit!!')
          //loading.close();
          return false
        }
      })
    },
    exportList(ele) {
      const that = this
      if (that.bindingAuthorizationCode.count === '' || that.bindingAuthorizationCode.count === '' || that.bindingAuthorizationCode.count === undefined) {
        this.$message.warning('数量不能为空')
        return false
      }
      that.$refs[ele].validate(valid => { // 表单验证
        if (valid) {
          const loading = this.$loading({
            lock: true,
            text: '导出授权码',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          })

          authorizationCodeApi.exportAuthorizationCode(that.bindingAuthorizationCode.count).then((response) => {
            if (!response) {
              this.$notify.error({
                title: '操作失败',
                message: '文件下载失败'
              })
            }
            loading.close()
            const url = window.URL.createObjectURL(response)
            const link = document.createElement('a')
            link.style.display = 'none'
            link.href = url // 获取服务器端的文件名
            link.setAttribute('download', '授权码列表.xls')
            document.body.appendChild(link)
            link.click()
            that.dialogVisibleBinding = false
          }, s => {
            if (s === 'error') {
              loading.close()
            }
            loading.close()
            that.$message.error('没有导出授权码')
          }).catch(err => {
            loading.close()
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val
      this.fetchData()
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val
      this.fetchData()
    },
    // 关闭弹窗
    close() {
      this.dialogVisible = false
      this.dialogVisibleBinding = false
    }
  }
}
</script>

<style lang="scss">
.lh36 {
  line-height: 36px;
  font-size: 14px;
}

.SearchForm {
  width: 100%;
  margin-bottom: 15px;
  background: #fff;
  border-radius: 6px;
  padding: 5px 5px;
  box-shadow: 1px 1px 3px rgba(0, 0, 0, .2);
}

.btn-add {
  padding: 5px;
}

.red {
  color: red;
}

.green {
  color: green;
}

.course-table {
  text-align: center;
}

.course-table td,
.course-table th {
  padding: 5px 0;
  text-align: center;
}

.course-table button {
  padding: 2px;
}

.icon-stop {
  float: left;
  display: inline-block;
  width: 12px;
  height: 12px;
  background: url('../../icons/stop.png') no-repeat top center/contain;
}

.mt22 {
  margin-top: 22px;
}

@media (max-width: 767px) {
  .el-message-box {
    width: 80% !important;
  }
}

.el-link.el-link--success {
  display: inline-block;
  padding: 10px 14px;
  margin: 20px 5px 0;
  font-size: 13px;
  background-color: #1890ff;
  border-radius: 3px
}

.el-link.el-link--success:hover {
  background-color: #46a6ff

}
</style>
