export default {
  inserted(el, binding, vnode) {
    // 获取el-input的input元素
    const input = el.querySelector('input') || el.querySelector('textarea');

    // 定义一个失焦事件处理函数
    const handleBlur = function () {
      // 更新v-model绑定的值
      vnode.componentInstance.$emit('input', vnode.componentInstance.value?.trim());
    };

    // 给input元素添加失焦事件监听
    input.addEventListener('blur', handleBlur);

    // 给el元素添加一个属性，保存事件处理函数的引用
    el._handleBlur = handleBlur;
  },

  // 当指令所在组件被销毁时
  unbind(el) {
    // 获取el-input的input元素
    const input = el.querySelector('input') || el.querySelector('textarea');

    // 获取之前保存的事件处理函数的引用
    const handleBlur = el._handleBlur;

    // 移除input元素的失焦事件监听
    input.removeEventListener('blur', handleBlur);

    // 删除el元素的属性
    el._handleBlur = null;
  }
};
