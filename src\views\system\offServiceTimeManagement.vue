<!--停服时间管理-->
<template>
  <div class="app-container">
    <p class="card">
      <i class="el-icon-bell"></i>
      &emsp;停服状态于每天0点，6点，12点，18点更新，点击每条数据状态按钮即可看到目前状态
    </p>
    <el-form :inline="true" class="container-card" label-width="96px" label-position="left" ref="dataQuery" :model="dataQuery">
      <el-row>
        <el-col :span="6" :xs="24">
          <!-- 未开始、进行中、已完成、已失效 -->
          <el-form-item label="停服状态:" prop="status">
            <el-select v-model="dataQuery.status" filterable value-key="value" placeholder="请选择" clearable>
              <el-option
                v-for="(item, index) in [
                  { value: 1, label: '未开始' },
                  { value: 2, label: '进行中' },
                  { value: 3, label: '已完成' },
                  { value: 4, label: '已失效' }
                ]"
                :key="index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="20">
          <el-form-item label="创建时间：" prop="regTime">
            <el-date-picker
              style="width: 100%"
              v-model="regTime"
              type="datetimerange"
              value-format="yyyy-MM-dd HH:mm:ss"
              align="right"
              unlink-panels
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              :picker-options="pickerOptions2"
            ></el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="4" :xs="20" style="text-align: right">
          <el-form-item>
            <el-button
              type="primary"
              icon="el-icon-search"
              @click="
                () => {
                  tablePage.currentPage = 1;
                  fetchData();
                }
              "
            >
              搜索
            </el-button>
            <el-button icon="el-icon-refresh" @click="rest()">重置</el-button>
          </el-form-item>
        </el-col>
        <el-col :span="4" :xs="20" style="text-align: right">
          <el-form-item>
            <el-button type="primary" icon="el-icon-circle-plus-outline" @click="openAdd()">新增停服时间</el-button>
          </el-form-item>
        </el-col>
      </el-row>
      <!-- ----------------------------- -->
    </el-form>
    <el-table
      class="common-table"
      v-loading="tableLoading"
      :data="tableData"
      height="400px"
      style="width: 100%; margin-bottom: 20px"
      row-key="id"
      border
      default-expand-all
      :header-cell-style="{ color: '#666', height: '60px' }"
      :row-style="{ height: '50px' }"
    >
      <el-table-column prop="studentName" label="停服时间">
        <template v-slot="{ row }">
          <span>{{ row.startTime }}</span>
          ~
          <span>{{ row.endTime }}</span>
          <!-- <span>{{row.studentName[0]}}~{{row.studentName[1]}}</span> -->
        </template>
      </el-table-column>
      <el-table-column prop="status" label="停服状态">
        <template v-slot="{ row }">
          <!-- 未开始、进行中、已完成、已失效 -->
          <el-popover placement="top" trigger="click" @show="calculatePercentage(row)">
            <div v-if="typeof percentage == 'number'">
              <el-progress type="dashboard" :percentage="percentage"></el-progress>
              <div style="text-align: center">进行中</div>
            </div>
            <div v-else style="text-align: center">{{ percentage }}</div>

            <el-tag type="info" v-if="row.status == 1" class="red" slot="reference">未开始</el-tag>
            <el-tag type="warning" v-else-if="row.status == 2" class="red" slot="reference">进行中</el-tag>
            <el-tag type="success" v-else-if="row.status == 3" class="green" slot="reference">已完成</el-tag>
            <el-tag type="danger" v-else-if="row.status == 4" class="red" slot="reference">已失效</el-tag>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间">
        <template v-slot="{ row }">
          <el-tag>{{ row.createTime }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作">
        <template v-slot="{ row }">
          <el-button type="text" v-if="row.status == 1" @click="editOffServiceTime(row)">编辑</el-button>
          <el-button type="text" v-if="row.status == 1" class="red" @click="deleteOffServiceTime(row)">终止停服</el-button>
          <span type="text" v-else>-</span>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <el-col :span="24" style="overflow-x: auto" :xs="24">
      <el-pagination
        :current-page="tablePage.currentPage"
        :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="tablePage.totalItems"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-col>
    <el-dialog :title="textMap[dialogStatus]" :visible.sync="dialogVisible" width="30%" @close="closeAdd()">
      <el-form v-loading="dialogLoading" v-show="!isSameDay">
        <el-form-item label="停服时间：" prop="regTime">
          <el-date-picker
            ref="datetime"
            style="width: 80%"
            v-model="RegTime"
            type="datetimerange"
            value-format="yyyy-MM-dd HH:mm"
            format="yyyy-MM-dd HH:mm"
            align="right"
            unlink-panels
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :picker-options="pickerOptions"
            @change="handlePicker"
          ></el-date-picker>
        </el-form-item>
      </el-form>
      <el-form v-loading="dialogLoading" v-show="isSameDay">
        <el-form-item label="停服日期：" prop="regTime" class="w100" label-width="100px">
          <el-date-picker
            v-model="regTimeDate"
            align="center"
            type="date"
            placeholder="选择日期"
            :picker-options="pickerOptions"
            value-format="yyyy-MM-dd"
            clearable
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="停服时间：" prop="regTime" class="w100" label-width="100px">
          <div @click="handleFocus">
            <el-time-picker
              :disabled="!regTimeDate"
              is-range
              v-model="regTimeTime"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              placeholder="选择时间范围"
              format="HH:mm"
              value-format="HH:mm"
              @change="handlePicker2"
              :default-time="regTimeTime"
            ></el-time-picker>
          </div>
        </el-form-item>
        <!-- <el-form-item label="停服时间：" prop="regTime" class="w50" label-width="100px">
          <el-time-picker placeholder="起始时间" v-model="regTimeTime[0]" value-format="HH:mm:ss"></el-time-picker>
          <el-time-picker
            placeholder="结束时间"
            v-model="regTimeTime[1]"
            :picker-options="{
              minTime: regTimeTime[0]
            }"
            value-format="HH:mm:ss"
          ></el-time-picker>
        </el-form-item> -->
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" plain icon="el-icon-refresh" @click="switchTab()" v-if="dialogStatus == 'create'" :disabled="dialogLoading">
          {{ isSameDay ? '跨天' : '同天' }}
        </el-button>
        <el-button @click="closeAdd()" :disabled="dialogLoading">取 消</el-button>
        <el-button type="primary" v-if="dialogStatus == 'create'" @click="confirmAdd" :disabled="dialogLoading">确 定</el-button>
        <el-button type="primary" v-else @click="confirmEdit" :disabled="dialogLoading">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
  import { mapGetters } from 'vuex';
  import orderApi from '@/api/offServiceTimeManagement';
  import { dxSource, pageParamNames } from '@/utils/constants';
  import store from '@/store';
  import dayjs from 'dayjs';
  import dictationWordLevel from '@/api/dictationWordLevel';
  export default {
    name: 'offServiceTimeManagement',
    data() {
      return {
        pickerOptions: {
          // onPick: ({ maxDate, minDate }) => {
          //   this.selectDate2 = minDate.getTime();
          //   if (maxDate) {
          //     this.selectDate2 = '';
          //   }
          // },
          disabledDate: (time) => {
            return time.getTime() < Date.now() - 1 * 24 * 3600 * 1000;
          }
        },
        pickerOptions2: {
          onPick: ({ maxDate, minDate }) => {
            this.selectDate = minDate.getTime();
            if (maxDate) {
              this.selectDate = '';
            }
          },
          disabledDate: (time) => {
            if (this.selectDate !== '') {
              const one = 30 * 24 * 3600 * 1000;
              const minTime = this.selectDate - one;
              const maxTime = this.selectDate + one;
              return time.getTime() < minTime || time.getTime() > maxTime;
            }
          }
        },
        token: store.getters.token,
        //停服时间
        dialogVisible: false,
        dialogLoading: false,
        // 分页
        tablePage: {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null
        },
        tableData: [],
        dataQuery: {
          status: ''
        },
        regTime: '',
        RegTime: null,
        regTimeDate: null,
        regTimeTime: null,
        tableLoading: false,
        //规则
        // rules: {
        //   studentName: [{ min: 0, max: 10, message: '姓名最多输入10个字符', trigger: 'blur' }],
        //   referrerName: [{ min: 0, max: 10, message: '推荐人姓名最多输入10个字符', trigger: 'blur' }],
        //   studentCode: [
        //     { min: 0, max: 10, message: '编号最多输入10个数字', trigger: 'blur' },
        //     { pattern: /^\d*$/, message: '请输入数字编号', trigger: 'blur' }
        //   ],
        //   phone: [
        //     { min: 11, max: 11, message: '请输入11位手机号', trigger: 'blur' },
        //     { pattern: /^1[3456789]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
        //   ],
        //   referrerPhone: [
        //     { min: 11, max: 11, message: '请输入11位手机号', trigger: 'blur' },
        //     { pattern: /^1[3456789]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
        //   ]
        // },
        dialogStatus: '',
        textMap: {
          update: '编辑停服时间',
          create: '新增停服时间'
        },
        //停服时间
        offServiceTime: null,
        percentage: 0,
        isSameDay: true
      };
    },
    computed: {
      ...mapGetters(['setpayUrl'])
    },
    mounted() {
      this.fetchData();
    },
    methods: {
      // 分页
      handleSizeChange(val) {
        this.tablePage.currentPage = 1;
        this.tablePage.size = val;
        this.fetchData();
      },
      handleCurrentChange(val) {
        this.tablePage.currentPage = val;
        this.fetchData();
      },
      //重置
      rest() {
        this.$refs.dataQuery.resetFields();
        this.regTime = '';
        this.tablePage.currentPage = 1;
        this.fetchData();
      },
      // 查询提现列表
      fetchData() {
        this.$refs['dataQuery'].validate((valid) => {
          if (valid) {
            const that = this;
            that.tableLoading = true;
            var a = that.regTime;
            if (a != null) {
              that.dataQuery.startTime = a[0];
              that.dataQuery.endTime = a[1];
            } else {
              that.dataQuery.startTime = '';
              that.dataQuery.endTime = '';
            }
            console.log(that.tablePage);
            orderApi.queryList(this.dataQuery, this.tablePage).then((res) => {
              this.tableData = res.data.data ? res.data.data : [];
              this.tableLoading = false;
              // 设置分页
              pageParamNames.forEach((name) => this.$set(this.tablePage, name, Number(res.data[name])));
            });
          }
        });
      },
      //打开新增停服时间弹窗
      openAdd() {
        this.dialogStatus = 'create';
        this.dialogVisible = true;
        this.isSameDay = false;
      },
      //确认新增停服时间
      confirmAdd() {
        const that = this;
        if (that.RegTime != null) {
          let a = {};
          a.startTime = dayjs(that.RegTime[0]).format('YYYY-MM-DD HH:mm:ss');
          a.endTime = dayjs(that.RegTime[1]).format('YYYY-MM-DD HH:mm:ss');
          var startAt = (new Date(that.RegTime[0]) * 1000) / 1000;
          var EntAt = (new Date(that.RegTime[1]) * 1000) / 1000;

          if (startAt == EntAt) {
            this.$message({
              type: 'info',
              message: '开始时间不可等于结束时间'
            });
            return false;
          }
          that.dialogLoading = true;
          orderApi.addStopServiceTime(a).then((res) => {
            that.dialogLoading = false;
            if (res.success) {
              that.dialogVisible = false;
              that.$message({
                type: 'success',
                message: '新增成功'
              });
              that.fetchData();
            }
          });
        } else {
          this.$message({
            type: 'info',
            message: '请选择停服时间'
          });
        }
      },
      //取消新增/编辑停服时间
      closeAdd() {
        this.offServiceTime;
        this.RegTime = null;
        this.regTimeDate = null;
        this.regTimeTime = null;
        this.dialogVisible = false;
      },
      //编辑停服时间
      editOffServiceTime(row) {
        this.dialogStatus = 'update';
        this.isSameDay = false;
        this.dialogVisible = true;
        this.offServiceTime = row;
        this.RegTime = [row.startTime, row.endTime];
      },
      //确认编辑停服时间
      confirmEdit() {
        const that = this;
        if (that.RegTime != null) {
          let a = {};
          a.id = that.offServiceTime.id;
          a.startTime = dayjs(that.RegTime[0]).format('YYYY-MM-DD HH:mm:ss');
          a.endTime = dayjs(that.RegTime[1]).format('YYYY-MM-DD HH:mm:ss');
          var startAt = (new Date(that.RegTime[0]) * 1000) / 1000;
          var EntAt = (new Date(that.RegTime[1]) * 1000) / 1000;

          if (startAt == EntAt) {
            this.$message({
              type: 'info',
              message: '开始时间不可等于结束时间'
            });
            return false;
          }
          that.dialogLoading = true;
          orderApi
            .updateStopServiceTime(a)
            .then((res) => {
              that.dialogLoading = false;
              if (res.success) {
                that.dialogVisible = false;
                this.$message({
                  type: 'success',
                  message: '修改成功'
                });
                that.fetchData();
              }
            })
            .catch((err) => {
              that.dialogLoading = false;
              that.dialogVisible = false;
              that.fetchData();
            });
        } else {
          this.$message({
            type: 'info',
            message: '请选择停服时间'
          });
        }
      },
      //删除停服时间
      deleteOffServiceTime(row) {
        const that = this;
        this.$confirm('请确认是否终止本次停服计划？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            orderApi.deleteStopServiceTime(row.id).then((res) => {
              if (res.success) {
                that.$message({
                  type: 'success',
                  message: '终止成功!'
                });
                that.fetchData();
              }
            }).catch(() => {
              that.fetchData()
            })
          })
          .catch(() => {
            this.$message({
              type: 'info',
              message: '已取消终止'
            });
          });
      },
      handlePicker() {
        if (this.RegTime == null) {
          return;
        }
        var startAt = (new Date(this.RegTime[0]) * 1000) / 1000;
        var EntAt = (new Date(this.RegTime[1]) * 1000) / 1000;
        // console.log(dayjs(Date.now()+60*10*1000).format('YYYY-MM-DD HH:mm:ss'));

        if (startAt < Date.now()) {
          this.RegTime[0] = dayjs(Date.now() + 60 * 1000).format('YYYY-MM-DD HH:mm');
          if (this.regTimeTime) this.regTimeTime = [dayjs(Date.now() + 60 * 1000).format('HH:mm'), this.regTimeTime[1]];
        }
        if (EntAt < Date.now()) {
          this.RegTime[1] = dayjs(Date.now() + 60 * 11 * 1000).format('YYYY-MM-DD HH:mm');
          if (this.regTimeTime) this.regTimeTime = [this.regTimeTime[0], dayjs(Date.now() + 60 * 11 * 1000).format('HH:mm')];
        }
        if (startAt < Date.now() || EntAt < Date.now()) {
          this.$message({
            type: 'info',
            message: '开始时间或结束时间不能小于当前时间'
          });
        }
      },
      //计算百分比
      calculatePercentage(row) {
        if (row.status == 4) {
          return (this.percentage = '已失效');
        }
        if (dayjs().isBefore(dayjs(row.startTime))) {
          return (this.percentage = '未开始');
        }
        if (dayjs(row.endTime).isBefore(dayjs())) {
          return (this.percentage = '已完成');
        }
        let total = dayjs(row.endTime).diff(dayjs(row.startTime), 'second');
        let now = dayjs(row.endTime).diff(dayjs(), 'second');
        let c = (now / total) * 100;
        console.log('c', c.toFixed(2));
        return (this.percentage = (100 - c).toFixed(2) - 0);
      },
      //切换
      switchTab() {
        this.isSameDay = !this.isSameDay;
      },
      handlePicker2() {
        this.RegTime = [this.regTimeDate + ' ' + this.regTimeTime[0], this.regTimeDate + ' ' + this.regTimeTime[1]];
        this.handlePicker();
      },
      handleFocus() {
        if (!this.regTimeDate) {
          this.$message.info('请先选择日期');
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .container-card {
    padding: 15px 15px 15px 15px;
    margin-bottom: 15px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  }
  .info {
    color: #dcdfe6 !important;
  }
  .image {
    width: 80% !important;
    margin: 0 auto;
    display: block;
  }
  .w100 {
    width: 100% !important;
    display: block !important;
  }
  .el-form-item.w100 > :last-child {
    width: calc(100% - 120px) !important;
    // :last-child {
    //   width: 100% !important;
    // }
    .el-select {
      width: 100% !important;
    }
    .el-input__inner {
      width: 100% !important;
    }
  }
  .el-form-item.w50 > :last-child {
    width: calc(100% - 120px) !important;
    // :last-child {
    //   width: 100% !important;
    // }
    .el-input {
      width: 49% !important;
      margin-right: 2% !important;
    }
    .el-input:last-child {
      width: 49% !important;
      margin-right: 0px !important;
    }
  }
  .card {
    background-color: #fef0f0;
    color: #f56c6c;
    // background-color: #f4f4f5;
    // color: #909399;
    padding: 10px 15px;
    border-radius: 5px;
    margin: 0 0 20px;
  }
  .card:hover {
    // background-color: #ffeded;
    // color: #ff4949;
    background-color: #f4f4f5;
    color: #909399;
  }
</style>
