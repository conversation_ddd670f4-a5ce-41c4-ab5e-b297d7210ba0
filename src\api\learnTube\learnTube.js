/**
 * 市级服务商相关接口
 */
import request from '@/utils/request'

export default {
  // 分页查询
  saveOrUpdate(data) {
    return request({
      url: '/znyy/learnTube/saveNew',
      method: 'POST',
      data
    })
  },
  list(pageNum,pageSize,data){
    return request({
      url: '/znyy/learnTube/list/'+pageNum+"/"+pageSize,
      method: 'GET',
      params: data
    })
  },
  detail(id){
    return request({
      url: '/znyy/learnTube/detail',
      method: 'GET',
      params: {'id': id}
    })
  },
  detailByMerchantCode(merchantCode){
    return request({
      url: '/znyy/learnTube/detailByMerchantCode',
      method: 'GET',
      params: {'merchantCode': merchantCode}
    })
  },
  saveNew(data){
    return request({
      url: '/znyy/learnTube/saveNew',
      method: 'POST',
      data
    })
  },
  update(data){
    return request({
      url: '/znyy/learnTube/update',
      method: 'PUT',
      data: data
    })
  },
  listByDealer(data,pageNum,pageSize){
    return request({
      url: '/znyy/learnTube/listByDealer?pageNum='+pageNum+'&pageSize='+pageSize,
      method: 'GET',
      params: data
    })
  },
  updataStatus(merchantCode,isEnable){
    return request({
      url: '/znyy/learnTube/updataStatus',
      method: 'PUT',
      params:{
        merchantCode: merchantCode,
        isEnable: isEnable
      }
    })
  }
}
