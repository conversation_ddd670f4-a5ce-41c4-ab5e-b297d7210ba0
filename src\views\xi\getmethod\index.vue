<template>
  <div class="app-container">
    <el-form :inline="true" ref="queryForm" class="container-card" label-width="110px">
      <el-form-item label="状态：">
        <el-select v-model="dataQuery.enable" placeholder="请选择" clearable>
          <el-option label="启用" :value="true" />
          <el-option label="禁用" :value="false" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-re" @click="resetQuery()">重置</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="search()">搜索</el-button>
      </el-form-item>
    </el-form>
    <el-col :span="24" style="margin-bottom: 30px">
      <el-button type="success" icon="el-icon-plus" size="mini" @click="addBtn()">新增</el-button>
    </el-col>
    <el-table class="common-table" v-loading="tableLoading" :data="tableData" style="width: 100%;margin-bottom: 20px;"
      row-key="id" border default-expand-all>
      <el-table-column prop="id" label="ID"></el-table-column>
      <el-table-column prop="title" label="标题" />
      <el-table-column prop="withdrawnBonus" label="操作" width="210">
        <template slot-scope="scope">
          <el-button type="primary" size="mini" @click="editBtn(scope.row.id)">编辑
          </el-button>
          <el-button type="danger" size="mini" @click="handleDelete(scope.row.id)">删除
          </el-button>
        </template>
      </el-table-column>
      <el-table-column prop="subTitle" label="副标题"></el-table-column>
      <el-table-column prop="num" label="经验值"></el-table-column>
      <el-table-column prop="sortNum" label="序号"></el-table-column>
      <el-table-column prop="enable" label="状态">
        <template slot-scope="scope">
          <el-switch @change="changeEnable(scope.row)" v-model="scope.row.enable" active-color="#13ce66"
            inactive-color="#ff4949">
          </el-switch>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <el-col :span="24" style="margin-bottom: 20px">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>

    <!-- 修改或者新增弹窗 -->
    <el-dialog :title="form.id != null ? '编辑' : '添加'" :visible.sync="open" width="70%" @close="close">
      <el-form ref="form" :model="form" :rules="rules" label-width="120px" style="width: 70%;">
        <el-form-item label="标题：" prop="title">
          <el-input v-model="form.title" />
        </el-form-item>
        <el-form-item label="副标题：" prop="subTitle">
          <el-input v-model="form.subTitle" />
        </el-form-item>
        <el-form-item label="经验值：" prop="num">
          <el-input-number v-model="form.num" controls-position="right" :min="1" :step="1" />
        </el-form-item>
        <el-form-item label="跳转地址：" prop="routePath">
          <el-input v-model="form.routePath" />
        </el-form-item>
        <el-form-item label="序号：" prop="sortNum">
          <el-input-number v-model="form.sortNum" :min="1" :step="1" controls-position="right" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import getmethodApi from '@/api/xi/getmethod'
import { pageParamNames } from '@/utils/constants'

export default {
  name: 'getmethod',
  data() {
    return {
      dataQuery: {
        name: ''
      },
      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      open: false,
      tableData: [],
      form: {},
      // 表单校验
      rules: {
        title: [{ required: true, message: '请输入标题', trigger: 'blur' }],
        subTitle: [{ required: true, message: '请输入副标题', trigger: 'blur' }],
        num: [{ required: true, message: '请输入经验值', trigger: 'blur' }],
        routePath: [{ required: true, message: '请输入跳转地址', trigger: 'blur' }],
        sortNum: [{ required: true, message: '请输入序号', trigger: 'blur' }],
        enable: [{ required: true, message: '请选择', trigger: 'blur' }]
      }
    }
  },
  created() {
    this.getPageList()
  },
  methods: {
    changeEnable(row) {
      let text = row.enable ? '启用' : '禁用'
      this.$confirm(`确定要${text}吗?`, '', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        getmethodApi.saveOrUpdate(row).then(res => {
          this.$message.success("修改成功")
          this.getPageList();
        })
      })
    },
    search() {
      this.tablePage = {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      }
      this.getPageList()
    },
    handleDelete(id) {
      this.$confirm('确定要删除吗？', '删除段位', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        getmethodApi.delete(id).then(res => {
          this.$message.success('删除成功！')
          this.getPageList()
        })
      })
    },
    addBtn() {
      this.reset()
      this.open = true
    },
    editBtn(id) {
      this.reset()
      getmethodApi.detail(id).then(res => {
        this.form = res.data
        this.fileList = new Array(this.form.image)
        this.open = true
      })
    },
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          getmethodApi.saveOrUpdate(this.form).then(response => {
            this.$message.success('提交成功！')
            this.open = false
            this.getPageList()
          })
        }
      })
    },
    getPageList() {
      this.tableLoading = true
      this.dataQuery.pageNum = this.tablePage.currentPage
      this.dataQuery.pageSize = this.tablePage.size
      getmethodApi.list(this.dataQuery).then(res => {
        this.tableData = res.data.data
        this.tableLoading = false
        // 设置后台返回的分页参数
        pageParamNames.forEach(name =>
          this.$set(this.tablePage, name, parseInt(res.data[name]))
        )
      })
    },
    handlePage() {
      (this.tablePage = {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      }),
        this.getPageList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dataQuery = {
        name: '',
        enable: null
      }
      this.getPageList()
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val
      this.getPageList()
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val
      this.getPageList()
    },
    close() {
      this.open = false
      this.reset()
    },
    reset() {
      this.form = {
        id: null,
        title: null,
        subTitle: null,
        num: undefined,
        routePath: null,
        sortNum: undefined,
        enable: true
      }
      if (this.$refs['form']) {
        this.$refs['form'].resetFields()
      }
    }
  }
}
</script>

<style>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.period-table td,
.period-table th {
  text-align: center;
}

@media (max-width: 767px) {
  .el-message-box {
    width: 80% !important;
  }
}

.el-tooltip__popper {
  max-width: 800px;
}
</style>
