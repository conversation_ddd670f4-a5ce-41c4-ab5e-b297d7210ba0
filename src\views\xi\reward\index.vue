<template>
  <div class="app-container">
    <el-form :inline="true" ref="queryForm" class="container-card" label-width="110px">
      <el-form-item label="奖励模块：">
        <el-input v-model="dataQuery.gainWay" placeholder="模块名称" clearable />
      </el-form-item>
      <el-form-item label="操作人：">
        <el-input v-model="dataQuery.addUser" placeholder="操作人" clearable />
      </el-form-item>
      <el-form-item label="操作时间：">
        <el-date-picker v-model="operation_value1" type="datetimerange" start-placeholder="开始日期" end-placeholder="结束日期"
          @change="timechange" :default-time="['12:00:00']">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-re" @click="search()">查询</el-button>
      </el-form-item>

    </el-form>
    <el-col :span="24" style="margin-bottom: 30px">
      <el-button type="success" icon="el-icon-plus" size="mini" @click="addBtn()">新增</el-button>
    </el-col>
    <el-table class="common-table" v-loading="tableLoading" :data="tableData" style="width: 100%;margin-bottom: 20px;"
      row-key="id" border default-expand-all>
      <el-table-column prop="orderNum" type="index" label="序号" width="50"></el-table-column>
      <el-table-column prop="gainWay" label="奖励模块"></el-table-column>
      <el-table-column prop="withdrawnBonus" label="操作" width="220">
        <template slot-scope="scope">
          <el-button type="primary" size="mini" @click="update(scope.row.id)">修改
          </el-button>
          <el-button type="danger" size="mini" icon="el-icon-delete" @click="singleDelete(scope.row.id)">删除
          </el-button>
        </template>
      </el-table-column>
      <el-table-column prop="gainWayIdent" label="奖励模块标识"></el-table-column>
      <el-table-column prop="awardGold" label="奖励金币" />
      <el-table-column prop="awardExp" label="奖励经验值" show-overflow-tooltip></el-table-column>
      <el-table-column prop="awardCredits" label="奖励学分" show-overflow-tooltip></el-table-column>
      <el-table-column prop="awardRulesFre" label="奖励周期" show-overflow-tooltip></el-table-column>
      <el-table-column prop="awardRulesNum" label="奖励次数上限" show-overflow-tooltip></el-table-column>
      <el-table-column prop="createTime" label="操作时间" show-overflow-tooltip></el-table-column>
      <el-table-column prop="addUser" label="操作人" show-overflow-tooltip></el-table-column>

    </el-table>
    <!-- 分页 -->
    <el-col :span="24" style="margin-bottom: 20px">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>

    <!-- 修改或者新增弹窗 -->
    <el-dialog :title="prop_title" :visible.sync="open" width="70%" @close="close">
      <el-form ref="form" :model="form" :rules="rules" label-width="120px" style="width: 70%;">
        <div v-if="ischange">
          <el-form-item label="奖励模块：" prop="gainWay">
            <el-input v-model="form.gainWay" />
          </el-form-item>
          <el-form-item label="奖励模块标识：" prop="gainWayIdent">
            <el-input v-model="form.gainWayIdent" />
          </el-form-item>
        </div>
        <div v-else>
          <el-form-item label="奖励模块：" prop="gainWay">
            <el-input readonly="readonly" v-model="form.gainWay" />
          </el-form-item>
          <el-form-item label="奖励模块标识：" prop="gainWayIdent">
            <el-input readonly="readonly" v-model="form.gainWayIdent" />
          </el-form-item>
        </div>

        <el-form-item label="奖励周期：" prop="awardRulesFre">
          <el-radio-group v-model="form.awardRulesFre">
            <el-radio label="day">每天</el-radio>
            <el-radio label="week">每周</el-radio>
            <el-radio label="seme">每学期</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="奖励上限：" prop="awardRulesNum">
          <el-select v-model="form.awardRulesNum" placeholder="请选择">
            <el-option v-for="item in up_options" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="奖励金币：" prop="awardGold">
          <el-input v-model="form.awardGold" type="number"
            oninput="value=value.replace(/^(0+)|[^\d]+/g,'');if(value>999)value=999" />
        </el-form-item>
        <el-form-item label="奖励学分：" prop="awardCredits">
          <el-input v-model="form.awardCredits" type="number"
            oninput="value=value.replace(/^(0+)|[^\d]+/g,'');if(value>999)value=999" />
        </el-form-item>
        <el-form-item label="奖励经验值：" prop="awardExp">
          <el-input v-model="form.awardExp" type="number"
            oninput="value=value.replace(/^(0+)|[^\d]+/g,'');if(value>999)value=999" />
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import rewardApi from '@/api/xi/reward'
import { pageParamNames } from '@/utils/constants'


export default {
  name: 'reward',
  data() {
    return {
      prop_title: '',
      ischange: true,
      operation_value1: '',
      up_options: [{
        value: '1',
        label: '1次'
      }, {
        value: '2',
        label: '2次'
      }, {
        value: '3',
        label: '3次'
      }, {
        value: '4',
        label: '4次'
      }, {
        value: '5',
        label: '5次'
      }, {
        value: '10',
        label: '10次'
      }, {
        value: '999',
        label: '不限'
      }],
      value: '',
      dataQuery: {
        addUser: '',
        gainWay: '',
        startTime: '',
        endTime: ''
      },
      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      open: false,
      tableData: [],
      form: {},
      // 表单校验
      rules: {
        name: [{ required: true, message: '请输入任务名称', trigger: 'blur' }],
        credits: [{ required: true, message: '请输入学分', trigger: 'blur' }],
      }
    }
  },
  watch: {
    "operation_value1"(val) {
      console.log(val)
      if (!val) {
        this.dataQuery.startTime = ''
        this.dataQuery.endTime = ''
      }
    }
  },
  created() {
    this.getPageList()
  },
  methods: {
    // 单个删除
    singleDelete(id) {
      const that = this;
      console.log(id)
      this.$confirm('确定操作吗?', '删除状态', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        rewardApi.delete(id).then(res => {
          // that.$nextTick(() => that.fetchData())
          that.$message.success('删除成功!')
          this.getPageList()
        }).catch(err => {

        })
      }).catch(err => {

      })
    },
    search() {
      this.tablePage = {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      }
      this.getPageList()
      console.log()
    },

    addBtn() {
      this.reset();
      this.open = true;
      this.ischange = true;
      this.prop_title = '奖励新增'
    },
    update(id) {
      this.reset();
      rewardApi.detail(id).then(res => {
        this.form = res.data;
        this.open = true;
        this.prop_title = '奖励修改'
        this.ischange = false
      })
    },
    submitForm() {
      console.log(this.form)
      this.$refs['form'].validate(valid => {
        if (valid) {
          rewardApi.saveOrUpdate(this.form).then(response => {
            this.$message.success('提交成功！')
            this.open = false
            this.getPageList()
          })
        }
      })
    },
    getPageList() {
      this.tableLoading = true
      this.dataQuery.pageNum = this.tablePage.currentPage
      this.dataQuery.pageSize = this.tablePage.size
      rewardApi.list(this.dataQuery).then(res => {
        this.tableData = res.data.data
        this.tableLoading = false
        for (let index = 0; index < this.tableData.length; index++) {
          if (this.tableData[index].awardRulesFre == 'day') {
            this.tableData[index].awardRulesFre = '每天'
          } else if (this.tableData[index].awardRulesFre == 'week') {
            this.tableData[index].awardRulesFre = '每周'
          } else if (this.tableData[index].awardRulesFre == 'seme') {
            this.tableData[index].awardRulesFre = '每学期'
          }
        }
        // 设置后台返回的分页参数
        pageParamNames.forEach(name =>
          this.$set(this.tablePage, name, parseInt(res.data[name]))
        )
      })
    },
    handlePage() {
      (this.tablePage = {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      }),
        this.getPageList()
    },
    //   获取起始时间
    timechange(value) {
      let data = {
        'year': value[0].getFullYear(),
        'month': this.timeP(value[0].getMonth() + 1),
        'day': this.timeP(value[0].getDate()),
        'hour': this.timeP(value[0].getHours()),
        'min': this.timeP(value[0].getMinutes()),
        'sec': this.timeP(value[0].getSeconds()),
      }
      let endtime = {
        'year': value[1].getFullYear(),
        'month': this.timeP(value[1].getMonth() + 1),
        'day': this.timeP(value[1].getDate()),
        'hour': this.timeP(value[1].getHours()),
        'min': this.timeP(value[1].getMinutes()),
        'sec': this.timeP(value[1].getSeconds()),
      }
      this.dataQuery.startTime = data.year + '-' + data.month + '-' + data.day + " " + data.hour + ":" + data.min + ":" + data.sec
      this.dataQuery.endTime = endtime.year + '-' + endtime.month + '-' + endtime.day + " " + endtime.hour + ":" + endtime.min + ":" + endtime.sec
    },
    // 补0
    timeP(s) {
      return s < 10 ? '0' + s : s
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val
      this.getPageList()
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val
      this.getPageList()
    },
    close() {
      this.open = false
      this.reset()
    },
    reset() {
      this.form = {
        awardCredits: '',
        awardExp: undefined,
        gainWay: '',
        awardRulesNum: undefined,
        awardGold: undefined,
        awardRulesFre: ''
      }
      if (this.$refs['form']) {
        this.$refs['form'].resetFields();
      }
    }
  },
}
</script>

<style>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.period-table td,
.period-table th {
  text-align: center;
}

@media (max-width: 767px) {
  .el-message-box {
    width: 80% !important;
  }
}

.el-tooltip__popper {
  max-width: 800px;
}
</style>
