<template>
  <div>
    <el-card class="box-card">
      <div slot="header" class="courseBox">
        <div>
          <span class="blackHead">课程任务</span>
          <!-- <el-tag type="danger" size="small" style="margin-left: 20px">待完成</el-tag> -->
        </div>
        <div class="headTxt">
          <div>
            <span class="cir">1</span>
            <span>恭喜您加入鼎校平台，我们根据您的角色【{{ Info.roleTagName }}】定位，为您推荐了</span>
            <span class="blue">{{ Info.mustLearnCount }}</span>
            <span>个必学课程</span>
            <!-- <el-button type="text" style="margin-left: 16px; font-weight: bold">查看课程</el-button> -->
          </div>
          <div>
            <span class="cir">2</span>
            <span>课程学习完成后，还有</span>
            <span class="blue">{{ Info.needExamCount }}</span>
            <span>个试卷需要完成哦，考试通过后可以立即开展业务啦</span>
          </div>
        </div>
      </div>

      <div class="courseNav line">
        <div class="line" style="width: 72%">
          <span>全部课程分类：</span>
          <div v-for="item in courList" class="line">
            <span class="courseBtn" :class="{ 'is-active': selectedCourse === item }" @click="toggleCourse(item)">{{ item.dictLabel }}</span>
          </div>
        </div>
        <div class="line2">
          <el-button icon="el-icon-search" type="primary" @click="search(1)" style="margin-left: 20px">搜索</el-button>
          <el-button icon="el-icon-refresh" @click="resetQuery()">重置</el-button>

          <el-input v-model="form.courseName" style="width: 200px; margin-right: 12px" placeholder="请输入课程名称" clearable />
        </div>
      </div>

      <div v-loading="lessonLoading">
        <div class="gray" v-for="(item, index) in courList" v-if="selectedCourse === item">
          <div style="margin-bottom: 20px">
            <span class="blackHead">{{ item.dictLabel }}</span>
            共
            <span class="blue">{{ item.courseCount }}</span>
            个课程
            <span style="margin-left: 10px">{{ item.categoryDescription }}</span>
          </div>
          <span class="courseSort" :class="{ 'is-active2': activeTab === tabC }" @click="toggleTab(tabC)" v-for="tabC in item.childList">
            {{ tabC.dictLabel }}
          </span>
          <div v-for="(tabT, indexT) in courSortList" :key="indexT">
            <div class="blackNav2">{{ tabT.dictLabel }}</div>
            <div style="display: flex; flex-wrap: wrap" v-if="tabT.courseList">
              <div v-for="(tabD, indexD) in tabT.courseList" class="videoList" @click="getCourseDetail(tabD)">
                <div class="videoInfo">
                  <div class="imageDetail">
                    <!-- <el-image v-if="tabD.courseCover" style="width: 210px; height: 150px" :src="tabD.courseCover" fit="fill" lazy scroll-container=".course-area"></el-image> -->
                    <img v-lazy="tabD.courseCover" alt="" class="course-poster" />
                    <img
                      v-if="tabD.isMustLearn === 1"
                      style="width: 50px; position: absolute; top: 0; right: 0"
                      src="https://document.dxznjy.com/course/b62e99bf92254002aa21e22d622d4984.png"
                      alt=""
                    />
                    <img
                      v-if="tabD.isMustLearn === 0"
                      style="width: 50px; position: absolute; top: 0; right: 0"
                      src="https://document.dxznjy.com/course/146719c421d0491f9ca047ccc1a58c18.png"
                      alt=""
                    />
                  </div>
                  <div class="title_content_css">
                    <div style="display: flex; justify-content: space-between">
                      <el-popover trigger="hover" placement="top" style="width: 68%">
                        {{ tabD.courseName }}

                        <div slot="reference" class="name-wrapper">
                          <span style="width: 20px">{{ tabD.courseName }}</span>
                        </div>
                      </el-popover>
                      <div class="imgRed" v-if="tabD.isToLearn" style="width: 28%">{{ tabD.learningStatus }}</div>
                    </div>

                    <div class="classDetail">
                      <span style="font-size: 13px">
                        共
                        <span>{{ tabD.lessonCount }}</span>
                        课时
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="blackNav2" style="padding-left: 30px" v-else>暂无数据</div>
          </div>
        </div>
        <div v-if="courList.length == 0" style="text-align: center">
          <img src="https://document.dxznjy.com/course/c68cba1a8fa043b7baed0b121697c0f6.png" alt="" />
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
  import courseApi from '@/api/training/course';
  export default {
    name: 'course',
    components: {},
    data() {
      return {
        lessonLoading: false,
        userId: '',
        roleTag: '',
        Info: {},
        courList: [], //课程列表
        courSortList: [], //课程分类列表
        categoryCode: '', //测试Dict数据
        activeName: 'first',
        // 课程数据
        courses: [
          { dictLabel: '全部', isPartner: 1 },
          { dictLabel: '预备教练课程', isPartner: 1 },
          { dictLabel: '合伙人课程', isPartner: 1 },
          { dictLabel: '俱乐部课程', isPartner: 1 }
        ],
        selectedCourse: null, // 用于跟踪当前选中的课程
        activeTab: null, // 初始化为 null，表示没有活动的标签
        // 分页
        tablePage: {
          currentPage: 1,
          size: 20,
          totalPage: null,
          totalItems: 0
        },
        form: {
          courseName: ''
          // type: "",
        }
      };
    },
    created() {
      // 初始赋值 userId 和 roleTag
      let user = localStorage.getItem('sysUserRoles');
      user = JSON.parse(user);
      this.userId = user[0].userId;

      let role = localStorage.getItem('roleTag');
      this.roleTag = role;

      this.getCourse();
      this.getUserCourselnfo();
    },
    mounted() {},
    methods: {
      getUserCourselnfo() {
        // console.log('this.userId', this.userId);
        // console.log('this.roleTag', this.roleTag);
        courseApi.getUserCourselnfo({ userId: this.userId, roleTag: this.roleTag }).then((res) => {
          console.log('res', res);
          this.Info = res.data;
        });
      },

      // 获取课程分类
      getCourse() {
        courseApi.queryCourseCategory({ keyword: '', roleTag: this.roleTag }).then((res) => {
          // courseApi.queryCourseCategory({ keyword: '' }).then(res => {
          this.courList = res.data;

          // 在组件加载时 设置selectedCourse为第一个课程
          if (this.courList.length > 0) {
            this.selectedCourse = this.courList[0];
            // 在组件加载时 设置activeTab为第一个课程
            if (this.selectedCourse.childList && this.selectedCourse.childList.length > 0) {
              this.activeTab = this.selectedCourse.childList[0];
              this.categoryCoursePage(this.activeTab.dictCode);
            }
          }
        });
      },
      // 根据二级课程分类code获取三级分类及下课程列表
      categoryCoursePage(val) {
        console.log('选中二级级联数据', val);
        this.lessonLoading = true;
        this.courSortList = [];
        courseApi.categoryCoursePage({ categoryCode: val, userId: this.userId, roleTag: this.roleTag, courseName: this.form.courseName }).then((res) => {
          this.lessonLoading = false;
          this.courSortList = res.data;
        });
      },
      toggleCourse(val) {
        console.log('选中一级分类', val);
        this.selectedCourse = val;
        if (val && val.childList) {
          console.log('选中一级分类222', val);
          if (val.childList.length > 0) {
            this.activeTab = this.selectedCourse.childList[0];
            console.log('请求二级分类传的dictCode', this.activeTab.dictCode);

            this.categoryCoursePage(this.activeTab.dictCode);
          }
        } else {
          this.courSortList = [];
        }
      },
      toggleTab(val) {
        this.activeTab = val; //二级课程选中
        console.log('选中二级', val);
        this.categoryCode = val.dictCode;
        this.categoryCoursePage(this.categoryCode);
      },

      getCourseDetail(info) {
        let sysUserRoles = JSON.parse(window.localStorage.getItem('sysUserRoles'));
        console.log('info----', info);
        courseApi.Detail({ userId: sysUserRoles[0].userId, roleTag: window.localStorage.getItem('roleTag'), courseId: info.courseId }).then((res) => {
          this.courseDetail = res.data;
          if (res.code == 20000) {
            this.$router.push({ path: '/courseDetail' });
            window.localStorage.setItem('courseId', info.courseId);
          }
        });
        // this.$router.push({ path: '/courseDetail' })
        // window.localStorage.setItem("courseId", info.courseId)
      },
      // 搜索功能
      search(index) {
        if (index == 1) {
          courseApi.courseRelation({ courseName: this.form.courseName }).then((res) => {
            this.courList = res.data;
            if (this.courList.length > 0) {
              this.selectedCourse = this.courList[0];
              // 在组件加载时 设置activeTab为第一个课程
              if (this.selectedCourse.childList && this.selectedCourse.childList.length > 0) {
                this.activeTab = this.selectedCourse.childList[0];
              }
            }
          });
        } else {
          this.getCourse();
        }
      },

      handleClick(tab, event) {
        console.log(tab, event);
      },
      // 重置
      resetQuery() {
        this.form = {};
        this.search(2);
      }
    }
  };
</script>

<style lang="scss" scoped>
  .blackHead {
    color: #303133;
    font-size: 20px;
    font-weight: 700;
    margin-right: 14px;
    vertical-align: middle;
  }

  .blackNav {
    color: #303133;
    font-size: 20px;
    margin-right: 14px;
  }

  .blackNav2 {
    color: #303133;
    font-size: 18px;
    margin-right: 14px;
    margin-top: 20px;
    // border-bottom: 1px solid gray;
  }

  .gray {
    color: #000000;
    font-size: 14px;
  }

  .blue {
    color: #1890ff;
  }

  .disC {
    display: flex;
    justify-content: space-around;
    align-items: center;
  }

  .imgCode {
    display: inline-block;
    width: 200px;
    height: 200px;
  }

  ::v-deep .el-badge__content.is-fixed {
    right: 70px !important;
  }

  .cir {
    display: inline-block;
    color: white;
    font-weight: bold;
    width: 30px;
    height: 30px;
    text-align: center;
    line-height: 30px;
    border-radius: 50%;
    margin-right: 10px;
    background-color: #1890ff;
  }

  .courseBox:before,
  .courseBox:after {
    display: table;
    content: '';
  }

  .courseBox:after {
    clear: both;
  }

  .box-card {
    width: 98%;
    margin: 1%;
  }

  .headTxt {
    // height: 100px;
  }

  .headTxt div {
    margin: 20px 0;
  }

  .headTxt div:last-child {
    margin: 0;
  }

  .line {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
  }

  .line2 {
    display: flex;
    flex-wrap: wrap;
    // align-items: center;
    flex-direction: row-reverse;
    width: 28%;
  }

  .courseNav {
    justify-content: space-between;
    margin-bottom: 40px;
  }

  .courseSort {
    border: 1px solid #dcdfe6;
    border-color: #dcdfe6;
    color: #606266;
    margin-right: 40px;
    padding: 9px 15px;
    font-size: 16px;
    border-radius: 30px;
    font-weight: 700;
  }

  .courseSort:hover {
    cursor: pointer;
  }

  .courseSort active {
    background-color: #badeff;
    color: #1890ff;
  }

  .course-item {
    // margin-top: 10px;
    // margin-right: 40px;
  }

  .courseBtn {
    border: 1px solid #dcdfe6;
    border-color: #dcdfe6;
    color: #606266;
    margin-right: 40px;
    padding: 9px 15px;
    font-size: 12px;
    border-radius: 3px;
    font-weight: 700;
  }

  .courseBtn:hover {
    cursor: pointer;
  }

  .is-active {
    background-color: #e8f4ff;
    border-color: #1890ff;
    color: #1890ff;
  }

  .is-active2 {
    background-color: #f2f3f8;
    border-color: #f2f3f8;
    color: #1890ff;
  }

  // 课程分类样式
  .videoList {
    margin-top: 20px;
    //width: 100%;
    display: flex;
    justify-content: flex-start;
    flex-wrap: wrap;
  }

  .videoInfo {
    cursor: pointer;
    margin-bottom: 28px;
    width: 210px;
    margin-left: 40px;
    box-shadow: 1px 0px 4px rgba(0, 0, 0, 0.6);
    border-radius: 4px;
    overflow: hidden;
  }

  .imageDetail {
    height: 150px;
    position: relative;
  }

  .title_content_css {
    height: 120px;
    padding: 10px;
    padding-bottom: 20px;
  }

  .introduction {
    width: 200px;
    color: #303133;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    word-break: break-all;
    font-size: 16px;
    line-height: 24px;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .imgRed {
    color: red;
    // float: right;
    // margin-right: 10px;
    font-size: smaller;
  }

  .classDetail {
    margin-top: 8px;
    word-break: break-all;
    font-size: 15px;
  }

  .video_css_content {
    padding: 40px 0;
    text-align: center;
  }

  .name-wrapper {
    display: inline-block;
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .noLearn {
    // background-color:#dedede;
    background-color: #f8f8f8;
    // background-color: pink;
  }
  .course-poster {
    width: 210px;
    height: 150px;
    object-fit: fill;
  }
</style>
