<template>
  <!-- 指派 -->
  <el-dialog title="指派自学室" :visible.sync="open" width="60%" :close-on-click-modal="false" @close="close">
    <el-tabs  type="card" v-model="activeName" @tab-click="handleClick">
      <el-tab-pane v-for="item in roomList"  :key="item.id" :label="item.className" :name="item.id"></el-tab-pane>
    </el-tabs>
    <el-table class="common-table" v-loading="roomLoading" :data="roomTableData" border
              ref="singleTable"
              highlight-current-row
              @current-change="handleRowChange">
      <el-table-column prop="name" label="房间名"></el-table-column>
      <el-table-column prop="weeks" label="日期"></el-table-column>
      <el-table-column prop="times" label="时间"></el-table-column>
      <el-table-column prop="classNum" label="人数">
        <template slot-scope="scope">
          {{scope.row.joinUserCount}}
        </template>
      </el-table-column>
    </el-table>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="submitForm()">指派自学室</el-button>
    </div>
    <!-- 分页 -->
    <div style="padding-top: 10px">
      <el-col :span="24" style="margin-bottom: 20px">
        <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
                       layout="total, sizes, prev, pager, next, jumper"
                       :total="tablePage.totalItems" @size-change="handleRoomSizeChange"
                       @current-change="handleRoomCurrentChange"/>
      </el-col>
    </div>
  </el-dialog>
</template>

<script>
  import {pageParamNames} from "@/utils/constants";
  import studyScheduleApi from "@/api/studyroom/studySchedule";

  export default {
    name: 'roomList',
    props: {
      // 学委id
      committeemanId: {
        type: String
      }
    },
    data(){
      return {
        open:false,
        activeName: '',
        // 自学室分页
        tablePage: {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null
        },
        scheduleId:null,//scheduleId
        roomList:[],
        roomLoading:false,
        roomTableData:[],
      }
    },
    created(){
        this.getRoomList();
    },
    methods:{
      submitForm(){
        if (this.scheduleId==null){
          this.$message.error("请选择房间！")
          return ;
        }
        studyScheduleApi.appointCommittee(this.scheduleId,this.committeemanId).then(res=>{
          this.$message.success("指派成功！")
          this.open = false;
          this.$emit("ok");
        })
      },
      handleRowChange(row){
        if (row){
          this.scheduleId=row.scheduleId;
        }else {
          this.scheduleId=null;
        }
      },
      getRooms(id){
        studyScheduleApi.getRooms(id).then(res=>{
          this.roomTableData=res.data;
        })
      },
      getRoomList() {
        this.roomLoading=true;
        var dataQuery={};
        dataQuery.pageNum=this.tablePage.currentPage;
        dataQuery.pageSize=this.tablePage.size;
        studyScheduleApi.scheduleList(dataQuery).then(res => {
          this.roomList = res.data.data;
          console.log(this.roomList);
          if (this.roomList[0]) {
            this.activeName=this.roomList[0].id;
            this.getRooms(this.activeName);
          }
          this.roomLoading = false;
          // 设置后台返回的分页参数
          pageParamNames.forEach(name => this.$set(this.tablePage, name, parseInt(res.data[name])))
        })
      },
      handleClick(tab, event) {
        const {name}= tab;
        this.getRooms(name);
      },
      show() {
        this.reset();
        this.open = true;
      },
      close() {
        this.reset();
        this.setCurrent();
      },
      reset() {
        this.scheduleId=null;
      },
      // 自学室分页
      handleRoomSizeChange(val) {
        this.tablePage.size = val;
        this.getRoomList();
      },
      handleRoomCurrentChange(val) {
        this.tablePage.currentPage = val;
        this.getRoomList();
      },
      setCurrent(row) {
        this.$refs.singleTable.setCurrentRow(row);
      },
    }

  }

</script>
