/**
 * 学员管理相关接口
 */
import request from '@/utils/request'

export default {
  // 分页查询
  cardList(pageNum, pageSize, data) {
    return request({
      url: '/dzy/card/page/' + pageNum + '/' + pageSize,
      method: 'GET',
      params: data
    })
  },
  cardTypeSelect() {
    return request({
      url: '/dzy/card/type/select',
      method: 'GET',
    })
  },
  cardAdd(data) {
    return request({
      url: '/dzy/card',
      method: 'POST',
      data
    })
  },
  cardUpdate(data) {
    return request({
      url: '/dzy/card',
      method: 'PUT',
      data
    })
  },
  cardBuy(idType, count) {
    return request({
      url: '/dzy/card/claim/' + idType + "/" + count,
      method: 'PUT'
    })
  },
  buyMeeting(data){
    return request({
      url: '/dzy/card/buyMeeting',
      method: 'PUT',
      data
    })
  },
  getDetails(number){
    return request({
      url: '/dzy/card/getDetails',
      method: 'GET',
      params:{
        "number":number
      }
    })
  }
}
