<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="88px">
      <el-form-item label="字典名称">
        <el-input class="filter-item" v-model="queryParams.dictName" :clearable="true" placeholder="字典名称" />
      </el-form-item>
      <el-form-item>
        <el-button type="success" icon="el-icon-search" size="mini" @click="getPageList">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="10" style="margin-bottom: 30px;">
      <el-col :span="1.5">
        <el-button type="primary" size="mini" @click="onFormCreateDictClick()">
          新建
        </el-button>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24">
        <el-table ref="class" :data="tableList" size="mini" header-cell-class-name="table-header-gray">
          <el-table-column label="序号" header-align="center" align="center" type="index" width="55px"
            :index="tableList.getTableIndex" />
          <el-table-column label="字典名称" prop="dictName" />
          <el-table-column label="操作" fixed="right" width="150px">
            <template slot-scope="scope">
              <el-button class="table-btn success" @click.stop="onFormEditDictClick(scope.row)" type="text" size="mini">
                编辑
              </el-button>
              <el-button class="table-btn delete" @click.stop="onFormDeleteDictClick(scope.row)" type="text" size="mini">
                删除
              </el-button>
            </template>
          </el-table-column>
          <el-table-column label="字典类型">
            <template slot-scope="scope">
              <el-tag size="mini" :type="getDictTypeTagType(scope.row.dictType)">
                {{ SysOnlineDictType.getValue(scope.row.dictType) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="是否树字典">
            <template slot-scope="scope">
              <el-tag size="mini" :type="scope.row.treeFlag ? 'success' : 'danger'">
                {{ scope.row.treeFlag ? '是' : '否' }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>
        <!-- 分页 -->
        <el-col :span="20">
          <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
            layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
            @current-change="handleCurrentChange" />
        </el-col>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import '@/api/activiti/staticDict/onlineStaticDict';
import onlineDictApi from '@/api/activiti/onlineDict';
import EditOnlineDict from './editOnlineDict.vue';
import { pageParamNames } from "@/utils/constants";

export default {
  name: 'formOnlineDict',
  props: {
  },
  data() {
    return {
      queryParams: {
        dictName: null
      },
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      tableList: [],
    }
  },

  created() {
    this.getPageList();
  },

  methods: {
    getDictTypeTagType(type) {
      switch (type) {
        case this.SysOnlineDictType.TABLE: return 'success';
        case this.SysOnlineDictType.URL: return 'primary';
        case this.SysOnlineDictType.STATIC: return 'warning';
        case this.SysOnlineDictType.CUSTOM: return 'danger';
        default:
          return 'info';
      }
    },

    getPageList() {
      this.queryParams.pageNum = this.tablePage.currentPage
      this.queryParams.pageSize = this.tablePage.size
      onlineDictApi.list(this.queryParams).then(res => {
        this.tableList = res.data.data;
        this.loading = false;
        // 设置后台返回的分页参数
        pageParamNames.forEach(name => this.$set(this.tablePage, name, parseInt(res.data[name])))
      })

    },

    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = {
        dictName: null
      },
        this.getPageList()
    },

    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val
      this.getPageList()
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val
      this.getPageList()

    },


    /**
     * 获取动态表单字典列表
     */
    loadDynamicDictData(params) {
      params = {
        ...params,
        dictName: this.formOnlineDict.formFilterCopy.dictName
      }
      return new Promise((resolve, reject) => {
        OnlineDictController.list(params).then(res => {
          resolve({
            dataList: res.data.dataList,
            totalCount: res.data.totalCount
          });
        }).catch(e => {
          reject(e);
        });
      });
    },
    loadDynamicDictVerify() {
      this.formOnlineDict.formFilterCopy.dictName = this.formOnlineDict.formFilter.dictName;
      return true;
    },
    refreshOnlineDict(reloadData) {
      if (reloadData) {
        this.formOnlineDict.dict.impl.refreshTable(true, 1);
      } else {
        this.formOnlineDict.dict.impl.refreshTable();
      }
      if (!this.formOnlineDict.isInit) {
        // 初始化下拉数据
      }
      this.formOnlineDict.isInit = true;
    },
    onFormCreateDictClick() {
      this.$dialog.show('新建字典', EditOnlineDict, {
        area: '600px',
        offset: '100px'
      }).then(res => {
        this.formOnlineDict.dict.impl.refreshTable();
      }).catch(e => {
        console.log(e);
      });
    },
    onFormEditDictClick(row) {
      this.$dialog.show(row ? '编辑字典' : '新建字典', EditOnlineDict, {
        area: '650px',
        offset: '100px'
      }, {
        dictId: row.dictId
      }).then(res => {
        this.formOnlineDict.dict.impl.refreshTable();
      }).catch(e => {
      });
    },
    onFormDeleteDictClick(row) {
      this.$confirm('是否删除此字典？').then(res => {
        let params = {
          dictId: row.dictId
        }

        return onlineDictApi.delete(params);
      }).then(res => {
        this.$message.success('删除成功！');
        this.getPageList();
      }).catch(e => { });
    },
    onResume() {
      this.refreshOnlineDict();
    },
    initFormData() {
    },
    formInit() {
      this.refreshOnlineDict();
    }
  },

  mounted() {
    // 初始化页面数据
    // this.formInit();
  },
  watch: {
  }
}
</script>

<style></style>
