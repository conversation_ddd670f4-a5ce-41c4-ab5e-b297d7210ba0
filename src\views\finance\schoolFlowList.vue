<template>
  <div class="app-container">
    <el-form :inline="true" class="container-card" label-width="110px" label-position="left" :model="dataQuery"
      ref="dataQuery">
      <el-row>
        <el-col :span="8" :xs="24">
          <el-form-item label="交易流水号：">
            <el-input v-model="dataQuery.flowCode" name="id" placeholder="请输入交易流水号：" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="商户编号：">
            <el-input id="merchantCode" v-model="dataQuery.merchantCode" name="id" placeholder="请输入商户编号：" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="商户名称：">
            <el-input id="merchantName" v-model="dataQuery.merchantName" name="id" placeholder="请输入商户名称:" clearable />
          </el-form-item>
        </el-col>
      </el-row>


      <el-row>
        <el-col :span="8" :xs="24">
          <el-form-item label="商户类型：">
            <el-select v-model="dataQuery.RoleTagName" placeholder="全部" style="width: 185px;" clearable>
              <el-option v-for="item in [{ value: 'School', label: '门店' }, { value: 'Student', label: '学员' }]"
                :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="变动方式：">
            <el-select v-model="dataQuery.direction" placeholder="全部" style="width: 185px;" clearable>
              <el-option v-for="item in [{ value: 1, label: '入' }, { value: 0, label: '出' }]" :key="item.value"
                :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="变动类型：">
            <el-select v-model="dataQuery.courseAccountsType" filterable value-key="value" placeholder="请选择" clearable>
              <el-option v-for="(item, index) in financeAccountsType" :key="index" :label="item.label"
                :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>


      <el-row>
        <el-col :span="8">
          <el-form-item>
            <el-form-item label="变动时间：">
              <el-date-picker style="width: 100%;" v-model="RegTime" type="daterange" value-format="yyyy-MM-dd HH:mm:ss"
                align="right" unlink-panels range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" clearable>
              </el-date-picker>
            </el-form-item>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="区域：" prop='province'>
            <el-select v-model="dataQuery.province" filterable value-key="value" placeholder="请选择" clearable>
              <el-option v-for="(item, index) in provinceList" :key="index" :label="item.label" :value="item.name" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="fetchData01()">搜索</el-button>
            <el-button type="primary" icon="el-icon-search" @click="rest()">重置</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-form :inline="true" style="margin-bottom: 20px;">
      <el-button type="warning" icon="el-icon-document-copy" @click="exportFlow()" v-loading="exportLoading">导出
      </el-button>
    </el-form>

    <el-table class="period-table" v-loading="tableLoading" :data="tableData" style="width: 100%;margin-bottom: 20px;"
      row-key="id" border default-expand-all :tree-props="{ list: 'children', hasChildren: 'true' }">
      <el-table-column prop="flowCode" label="交易流水号" width="200" :show-overflow-tooltip="true"></el-table-column>
      <el-table-column prop="merchantCode" label="商户编号" width="120"></el-table-column>
      <el-table-column prop="merchantName" label="商户名称" width="200"></el-table-column>
      <el-table-column prop="roleTagName" label="商户类型" width="120"></el-table-column>
      <el-table-column prop="direction" label="变动方式" width="120">
      </el-table-column>
      <el-table-column prop="province" label="区域" width="120"></el-table-column>
      <el-table-column prop="courseAccountsTypeName" label="变动类型" width="120"></el-table-column>
      <el-table-column prop="flowBeforeCourse" v-if="currentAdmin.schoolType !== 3" label="变动前学时（节）"
        width="150"></el-table-column>
      <el-table-column prop="flowCourse" label="变动学时（节）" width="150"></el-table-column>
      <el-table-column prop="flowAfterCourse" label="变动后学时（节）" v-if="currentAdmin.schoolType !== 3"
        width="150"></el-table-column>
      <el-table-column prop="addTime" label="变动时间" width="200" :show-overflow-tooltip="true"></el-table-column>
      <el-table-column prop="description" label="变动描述" width="500" :show-overflow-tooltip="true"></el-table-column>
    </el-table>
    <!-- 分页 -->
    <el-col :span="24" style="overflow-x: auto;" :xs="24">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>
    <!--     @ApiModelProperty("变动前学时")
    private String classHoursBeforeTheChange;
    @ApiModelProperty("变动学时")
    private String changeOfClassHours;
    @ApiModelProperty("变动后学时")
    private String classHoursAfterTheChange; -->
    <el-col :span="24" class="mt20" v-if="path.indexOf('purchasedProduct') == -1 && currentAdmin.schoolType !== 3">
      本次总计变动前学时：{{ classHoursBeforeTheChange }}节，变动学时：{{
        changeOfClassHours
      }}节，变动后学时：{{ classHoursAfterTheChange }}节
    </el-col>
  </div>
</template>

<script>
import merchantAccountFlowApi from "@/api/merchantAccountFlow";
import enTypes from '@/api/bstatus'
import { pageParamNames } from "@/utils/constants";
import memberApi from "@/api/member";
import schoolList from "@/api/schoolList";
import systemApi from "@/api/systemConfiguration";

export default {
  name: 'schoolFlowList',
  data() {
    return {
      path: "",
      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      tableData: [{
        name: 1,
        title: '1111'
      }],
      RegTime: [],
      classHoursBeforeTheChange: 0,//变动前学时
      changeOfClassHours: 0,//变动学时
      classHoursAfterTheChange: 0,//变动后学时
      dataQuery: {},
      dialogVisible: false,
      updateMarketDate: {},
      addMarketDate: {},
      showLoginAccount: false,
      exportLoading: false,//导出加载
      financeAccountsType: [],
      provinceList: [], //省份,
      currentAdmin: ''
    };
  },
  created() {
    this.path = this.$route.path;
    this.getCurrentAdmin();
    this.fetchData();
    //获取变动类型
    this.getFinanceAccountsType();
    this.getProvinceList();
  },
  methods: {
    getCurrentAdmin() {
      schoolList.getCurrentAdmin().then((res) => {
        this.currentAdmin = res.data;
      })
    },
    fetchData01() {
      this.tablePage = {
        currentPage: 1,
        size: this.tablePage.size,
        totalPage: null,
        totalItems: null
      }
      this.fetchData();
    },
    // 查询提现列表
    fetchData() {
      const that = this;
      that.tableLoading = true
      var a = that.RegTime;
      if (a != null) {
        that.dataQuery.startDate = a[0];
        console.log(that.dataQuery.startDate = a[0]);
        that.dataQuery.endDate = a[1];
      } else {
        that.dataQuery.startDate = '';
        that.dataQuery.endDate = '';
      }
      if (this.path.indexOf("purchasedProduct") > -1) {
        that.dataQuery.isPurchasedProduct = true;
      } else {
        that.dataQuery.isPurchasedProduct = false;
      }

      merchantAccountFlowApi.schoolCourseList(
        that.tablePage.currentPage,
        that.tablePage.size,
        that.dataQuery
      ).then((res) => {
        that.tableData = res.data.data;
        that.tableLoading = false;
        // 设置后台返回的分页参数
        pageParamNames.forEach((name) =>
          that.$set(that.tablePage, name, parseInt(res.data[name]))
        );
      });
      merchantAccountFlowApi.merchantChange(that.tablePage.currentPage,
        that.tablePage.size,
        that.dataQuery).then(res => {
          that.classHoursBeforeTheChange = res.data.classHoursBeforeTheChange;
          that.changeOfClassHours = res.data.changeOfClassHours;
          that.classHoursAfterTheChange = res.data.classHoursAfterTheChange;
        })
    },
    //导出
    exportFlow() {
      const that = this;
      that.exportLoading = true;
      //
      merchantAccountFlowApi.simpleExecl(that.dataQuery).then(res => {
        //           this.$notify.error({
        //               title: "操作失败",
        //               message: "文件下载失败"
        //             });
        const url = window.URL.createObjectURL(res);
        const link = document.createElement("a");
        link.style.display = "none";
        link.href = url; // 获取服务器端的文件名
        link.setAttribute("download", "课程流水表.xls");
        document.body.appendChild(link);
        link.click();
        that.exportLoading = false;
      })
    },
    //新增操作
    clickAdd() {
      this.dialogVisible = true;
    },
    // 打开修改登陆账号
    openLogin() {
      this.showLoginAccount = true
    },
    closeLogin() {
      this.showLoginAccount = false
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val;
      this.fetchData();
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val;
      this.fetchData();
    },
    //获取变动类型
    getFinanceAccountsType() {
      var enType = "CourseAccountsType";
      //已购 产品订单
      if (this.path.indexOf("purchasedProduct") > -1) {
        enTypes.getPurchasedProduct(enType).then(res => {
          this.financeAccountsType = res.data;
        })
      } else {
        enTypes.getEnumerationAggregation(enType).then(res => {
          this.financeAccountsType = res.data;
        })
      }
    },
    //获取省份
    getProvinceList() {
      memberApi.getProvince(0).then(res => {
        this.provinceList = res.data
      })
    },

    //重置
    rest() {
      this.$refs.dataQuery.resetFields();
      this.RegTime = '';
      this.fetchData01();
    },
  }
};
</script>

<style>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.period-table td,
.period-table th {
  text-align: center;
}

.mt20 {
  margin-top: 20px;
}
</style>
