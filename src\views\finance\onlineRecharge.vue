<template>
  <div class="app-container">
    <el-row type="flex" justify="center">
      <el-col :xs="24" :lg="12">
        <el-form class="recharge-form" label-width="110px" label-position="right" :ref="'dataQuery'" :rules="rules"
          :model="dataQuery" style="width: 100%; margin: 0 auto">
          <el-form-item label="商户类型：" prop="roleTag">
            <el-select v-model="dataQuery.roleTag" placeholder="请选择" filterable style="width: 185px"
              @change="changeRoleTag(dataQuery.roleTag)">
              <el-option v-for="item in roleTagList" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="充值账户：" prop="merchantCode">
            <el-select v-model="dataQuery.merchantCode" placeholder="请选择" filterable style="width: 185px;"
              @change="changeMerchantCode(dataQuery.merchantCode)">
              <el-option v-for="item in merchantCodeType" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="充值渠道：" prop="channel">
            <el-select v-model="dataQuery.channel" placeholder="请选择" style="width: 185px"
              @change="changeChannel(dataQuery.channel)">
              <el-option v-for="item in channlType" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="账目类型：" prop="rechargeType">
            <el-select v-model="dataQuery.rechargeType" placeholder="请选择" style="width: 185px"
              @change="changeRechargeType(dataQuery.rechargeType)">
              <el-option v-for="item in accountType" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="充值金额：" prop="rechargeMoney">
            <el-col :xs="24" :lg="24">
              <el-input type="number" maxlength="20" isNumber2="true" min="1" id="rechargeMoney"
                v-model="dataQuery.rechargeMoney" name="id" placeholder="请输入充值金额："
                @blur="changeMoney(dataQuery.rechargeMoney)" />
            </el-col>
          </el-form-item>
          <el-form-item label="赠送金额：" prop="giveMoney" v-show="giveMoneyShow">
            <el-col :xs="24" :lg="24">
              <el-input type="number" maxlength="20" isNumber2="true" min="1" id="giveMoney" v-model="dataQuery.giveMoney"
                name="id" placeholder="请输入赠送金额：" @change="changeGiveMoney(dataQuery.giveMoney)" />
            </el-col>
          </el-form-item>
          <el-form-item label="返点金额：" v-show="backeMoneyShow" prop="rechargeBackMoney">
            <el-col :xs="24" :lg="24">
              <!--
              <el-input
                id="rechargeBackMoney"
                v-model="dataQuery.rechargeBackMoney"
                name="id"
                :disabled="disabled"
              />-->
              <span>{{ dataQuery.rechargeBackMoney }}</span>
            </el-col>
          </el-form-item>
          <el-form-item label="到账金额：" v-show="backeMoneyShow" prop="account">
            <el-col :xs="24" :lg="24">
              <!-- <el-input
                id="account"
                v-model="dataQuery.account"
                name="id"
                :disabled="disabled"
              /> -->
              <span>{{ dataQuery.account }}</span>
            </el-col>
          </el-form-item>
          <el-form-item label="兑换学时：" v-show="courseShow" prop="sumCoursePrice">
            <el-col :xs="24" :lg="24">
              <!-- <el-input
                id="sumCoursePrice"
                v-model="sumCoursePrice"
                name="id"
                :disabled="disabled"
              /> -->
              <span>{{ sumCoursePrice }}</span>
            </el-col>
          </el-form-item>
          <el-form-item label="充值说明：" prop="rechargeRemark">
            <el-col :xs="24" :lg="24">
              <el-input type="textarea" :rows="4" id="rechargeRemark" v-model="dataQuery.rechargeRemark" name="id"
                placeholder="请输入登录账号：" />
            </el-col>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="addAcount('dataQuery')">保存</el-button>
          </el-form-item>
        </el-form>
      </el-col>
    </el-row>
    <!-- 添加弹窗 -->
    <el-dialog class="remark-dialog" title="交易密码确认" :visible.sync="dialogVisible" width="20%"
      :close-on-click-modal="false" @close="close">
      <el-input v-model="secondPassWord" type="password" show-password></el-input>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="quertrue(secondPassWord)">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import merchantAccountFlowApi from "@/api/merchantAccountFlow";
import { parse } from "path-to-regexp";

export default {
  data() {
    return {
      dataQuery: {},
      secondPassWord: "", //交易密码
      sumCoursePrice: "",
      disabled: true,
      roleTagList: [],
      merchantCodeType: [], //充值账号集合
      channlType: [], //渠道类型
      accountType: [], //账目类型：
      courseShow: false, //学时现显
      backeMoneyShow: false, //返点金额
      giveMoneyShow: true, //赠送金额
      dialogVisible: false,
      queryTrueShow: true, //确认密码是否成功
      rules: {
        roleTag: [
          {
            required: true,
            message: "必填",
            trigger: "change",
          },
        ],
        rechargeType: [
          {
            required: true,
            message: "必填",
            trigger: "change",
          },
        ],
        merchantCode: [
          {
            required: true,
            message: "必填",
            trigger: "blur",
          },
        ],
        channel: [
          {
            required: true,
            message: "必填",
            trigger: "blur",
          },
        ],
        rechargeMoney: [
          {
            required: true,
            message: "必填",
            trigger: "blur",
          },
        ],
        rechargeBackMoney: [
          {
            required: true,
            message: "必填",
            trigger: "blur",
          },
        ],
        // giveMoney: [
        //   {
        //     required: true,
        //     message: "必填",
        //     trigger: "blur",
        //   },
        // ],
      },
    };
  },
  created() {
    this.getRoleTagLists();
  },
  methods: {
    //选择商户获取充值账户
    changeRoleTag(val) {
      if (val === "Promoter") {
        this.channlType = [
          {
            value: "sys",
            label: "系统",
          },
        ];
        this.accountType = [
          {
            value: "RechargeMoney",
            label: "充值资金",
          },
        ];
        this.dataQuery = {
          roleTag: "Promoter",
          rechargeType: "",
          merchantCode: "",
          channel: "",
          rechargeMoney: "",
          rechargeBackMoney: "",
          account: "",
        };
        this.courseShow = false;
        this.giveMoneyShow = true;
        this.backeMoneyShow = true;
      } else if (val === "Division") {
        this.channlType = [
          {
            value: "sys",
            label: "系统",
          },
          {
            value: "merchant",
            label: "分公司",
          },
        ];
        this.accountType = [
          {
            value: "RechargeMoney",
            label: "充值资金",
          },
        ];
        this.dataQuery = {
          roleTag: "Division",
          rechargeType: "",
          merchantCode: "",
          channel: "",
          rechargeMoney: "",
          rechargeBackMoney: "",
          account: "",
        };
        this.courseShow = false;
        this.giveMoneyShow = true;
        this.backeMoneyShow = true;
      } else if (val === "Company") {
        this.channlType = [
          {
            value: "sys",
            label: "系统",
          },
        ];
        this.accountType = [
          {
            value: "RechargeMoney",
            label: "充值资金",
          },
        ];
        this.dataQuery = {
          roleTag: "Company",
          rechargeType: "",
          merchantCode: "",
          channel: "",
          rechargeMoney: "",
          rechargeBackMoney: "",
          account: "",
        };
        this.courseShow = false;
        this.giveMoneyShow = true;
        this.backeMoneyShow = true;
      } else if (val === "Agent") {
        this.channlType = [
          {
            value: "sys",
            label: "系统",
          },
          {
            value: "merchant",
            label: "上级商户",
          },
        ];
        this.accountType = [
          {
            value: "RechargeMoney",
            label: "充值资金",
          },
        ];
        this.dataQuery = {
          roleTag: "Agent",
          rechargeType: "",
          merchantCode: "",
          channel: "",
          rechargeMoney: "",
          rechargeBackMoney: "",
          account: "",
        };
        this.giveMoneyShow = true;
        this.courseShow = false;
        this.backeMoneyShow = false;
      } else if (val === "School") {
        this.channlType = [
          {
            value: "sys",
            label: "系统",
          },
          {
            value: "merchant",
            label: "上级商户",
          },
        ];
        this.accountType = [
          {
            value: "RechargeCourse",
            label: "充值学时",
          },
        ];
        this.dataQuery = {
          roleTag: "School",
          rechargeType: "",
          merchantCode: "",
          channel: "",
          rechargeMoney: "",
          rechargeBackMoney: "",
          account: "",
        };
        this.giveMoneyShow = false;
        this.courseShow = true;
        this.backeMoneyShow = false;
      } else {
        this.channlType = [
          {
            value: "sys",
            label: "系统",
          },
          {
            value: "merchant",
            label: "上级商户",
          },
        ];
        this.accountType = [
          {
            value: "RechargeMoney",
            label: "充值资金",
          },
        ];
        this.dataQuery = {
          roleTag: "Dealer",
          rechargeType: "",
          merchantCode: "",
          channel: "",
          rechargeMoney: "",
          rechargeBackMoney: "",
          account: "",
        };
        this.giveMoneyShow = true;
        this.courseShow = false;
        this.backeMoneyShow = false;
      }
      merchantAccountFlowApi.getSelectResult(val).then((res) => {
        this.merchantCodeType = res.data.data;
      });
    },
    //增加操作
    addAcount(ele) {
      const that = this;
      if (that.dataQuery.roleTag === 'Dealer') {
        if (that.dataQuery.channel === 'merchant') {
          that.dataQuery.giveMoney = 0;
        }
      }
      if (that.dataQuery.giveMoney === "" || that.dataQuery.giveMoney === '' || that.dataQuery.giveMoney === 0) {
        that.dataQuery.giveMoney = 0;
      }
      if (that.queryTrueShow) {
        that.$refs[ele].validate((valid) => {
          // 表单验证
          if (valid) {
            that.dialogVisible = true;
            that.secondPassWord = "";
          } else {
            console.log("error submit!!");
            //loading.close();
            return false;
          }
        });
      }
    },
    //
    close() {
      this.dialogVisible = false;
    },
    //获取角色
    getRoleTagLists() {
      merchantAccountFlowApi.getRoleTagList().then(res => {
        this.roleTagList = res.data;
      })
    },
    //改变金额
    changeMoney(val) {
      if (this.dataQuery.rechargeType != null && this.dataQuery.merchantCode) {
        const money = {
          money: val,
          type: this.dataQuery.rechargeType,
          merchantCode: this.dataQuery.merchantCode,
        };
        if (this.dataQuery.roleTag === "Agent" || this.dataQuery.roleTag === "Promoter" || this.dataQuery.roleTag === "Division" || this.dataQuery.roleTag === "Company") {
          // if (val < 30000) {
          //   this.$message.info("市级服务商最低充值3万");
          //   this.dataQuery.rechargeMoney = "";
          //   this.dataQuery.account = "";
          //   this.dataQuery.rechargeBackMoney = "";
          // }
          if (this.dataQuery.rechargeMoney != null) {
            merchantAccountFlowApi.accordingToTheRole(money).then((res) => {
              this.dataQuery.rechargeBackMoney = res.data.data.money;
              this.dataQuery.account = res.data.data.finance;
            });
          }
        } else if (this.dataQuery.roleTag === "Dealer") {
          // if (val < 10000) {
          //   this.$message.info("托管中心最低充值1万");
          //   this.dataQuery.rechargeMoney = "";
          //   this.dataQuery.account = "";
          //   this.dataQuery.rechargeBackMoney = "";
          // }
          if (this.dataQuery.rechargeMoney != null) {
            merchantAccountFlowApi.accordingToTheRole(money).then((res) => {
              this.dataQuery.rechargeBackMoney = res.data.data.money;
              this.dataQuery.account = res.data.data.finance;
            });
          }
        } else {
          if (this.dataQuery.rechargeMoney % 20 === 0) {
            if (this.dataQuery.rechargeMoney != null) {
              merchantAccountFlowApi.accordingToTheRole(money).then((res) => {
                this.sumCoursePrice = res.data.data.finance;
              });
            }
            this.dataQuery.rechargeBackMoney = 0;
            this.dataQuery.giveMoney = 0;
          } else {
            this.$message.info("请输入20的倍数");
            this.dataQuery.rechargeBackMoney = 0;
            this.dataQuery.giveMoney = 0;
            this.dataQuery.rechargeMoney = "";
            this.sumCoursePrice = "";
          }
        }
      } else {
        this.$message.info("请先选择充值账户和账目类型");
        this.dataQuery.rechargeMoney = "";
      }
    },
    //选择充值渠道
    changeChannel(val) {
      if (
        this.dataQuery.roleTag === "" ||
        this.dataQuery.roleTag === null ||
        this.dataQuery.roleTag === ""
      ) {
        this.$message.info("请选择商户类型");
      } else {
        if (this.dataQuery.roleTag == "Dealer" || this.dataQuery.roleTag === "Agent") {
          if (val === "sys") {
            this.backeMoneyShow = true;
            this.giveMoneyShow = true;
          } else if (val === "merchant") {
            this.giveMoneyShow = false;
            this.backeMoneyShow = true;
          }
        }
      }
    },
    //改变赠送金额
    changeGiveMoney(val) {
      if (val != null && val !== "" && val !== '') {
        this.dataQuery.account =
          parseInt(this.dataQuery.account) + parseInt(val) + "";
      }
    },
    //充值账户选择器
    changeMerchantCode(val) {
      this.dataQuery = {
        roleTag: this.dataQuery.roleTag,
        merchantCode: val,
        channel: this.dataQuery.channel,
        rechargeMoney: "",
        rechargeBackMoney: "",
        account: "",
        rechargeType: "",
        giveMoney: "",
      };
      merchantAccountFlowApi.getDetails(val).then((res) => {
        if (res.data.data.paymentIsComplete !== null) {
          if (res.data.data.paymentIsComplete == 0) {
            this.$confirm('确定操作吗?', '该账户是未完款账户', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            }).then((res) => { }, s => {
              if (s === 'cancel') {
                this.dataQuery = {
                  roleTag: this.dataQuery.roleTag,
                  merchantCode: "",
                  channel: this.dataQuery.channel,
                  rechargeMoney: "",
                  rechargeBackMoney: "",
                  account: "",
                  rechargeType: "",
                  giveMoney: "",
                };
              }
            });
          }
        }
      });
    },
    //确认二级密码
    quertrue(secondPassWord) {
      this.$confirm('确定操作吗?', '在线充值', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const that = this;
        if (that.secondPassWord.length <= 0) {
          that.$message.info("交易密码不能为空");
        } else {
          merchantAccountFlowApi.checkSecondPwd(secondPassWord).then((res) => {
            console.log(res);
            if (!res.success) {
              that.dialogVisible = false;
              that.$message.info("验证失败");
            } else {
              if (that.dataQuery.roleTag === "Dealer") {
                if (that.dataQuery.channel === "merchant") {
                  that.dataQuery.giveMoney = 0;
                }
              }
              that.$refs["dataQuery"].validate((valid) => {
                // 表单验证
                if (valid) {
                  const loading = this.$loading({
                    lock: true,
                    text: "新增账户",
                    spinner: "el-icon-loading",
                    background: "rgba(0, 0, 0, 0.7)",
                  });
                  merchantAccountFlowApi
                    .onlineRecharge(that.dataQuery)
                    .then((res) => {
                      that.dataQuery = {
                        roleTag: "",
                        merchantCode: "",
                        channel: "",
                        rechargeMoney: "",
                        rechargeBackMoney: "",
                        account: "",
                        rechargeType: "",
                        giveMoney: "",
                      };
                      that.dialogVisible = false;
                      that.secondPassWord = "";
                      that.$message.success("在线充值成功");
                      loading.close();
                      that.$router.push({
                        path: "/finance/merchantFlowList"
                      });
                    })
                    .catch((err) => {
                      loading.close();
                    });
                } else {
                  console.log("error submit!!");
                  //loading.close();
                  return false;
                }
              });
            }
          });
        }
      })

    },
    //选择账目类型
    changeRechargeType(val) {
      if (this.dataQuery.merchantCode != null) {
        this.dataQuery = {
          roleTag: this.dataQuery.roleTag,
          merchantCode: this.dataQuery.merchantCode,
          channel: this.dataQuery.channel,
          rechargeMoney: "",
          rechargeBackMoney: "",
          account: "",
          rechargeType: val,
        };
      } else {
        this.$message.info("请先选择充值账户");
        this.dataQuery = {
          roleTag: this.dataQuery.roleTag,
          merchantCode: "",
          channel: this.dataQuery.channel,
          rechargeMoney: "",
          rechargeBackMoney: "",
          account: "",
          rechargeType: "",
        };
      }
    },
  },
};
</script>

<style>
@media (max-width:767px) {
  .el-message-box {
    width: 80% !important;
  }

  .el-select-dropdown {
    max-width: 200px !important;
  }
}
</style>
