<template>
  <div class="app-container">
    <el-form :inline="true" ref="queryForm" class="container-card" label-width="110px">

      <el-form-item v-for="item in gradeList" :key="item.id">
        <span :class="{ highlight: dataQuery.gradeId === item.id }" style="cursor:pointer; margin: 15px"
          @click="gradeQuery(item.id)">{{ item.name }}</span>
      </el-form-item>

      <el-form-item>
        <el-button icon="el-icon-re" @click="resetQuery()">重置</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="getPageList()">搜索</el-button>
      </el-form-item>
    </el-form>
    <el-col :span="24" style="margin-bottom: 30px">
      <!--      <el-button type="success" icon="el-icon-plus" size="mini" @click="addBtn()">新增</el-button>-->
      <router-link :to="{ path: '/paper/abilityReportSave' }" class="link-left">
        <el-button type="primary">添加</el-button>
      </router-link>
    </el-col>
    <el-table class="common-table" v-loading="tableLoading" :data="tableData" style="width: 100%;margin-bottom: 20px;"
      row-key="id" border default-expand-all>
      <el-table-column prop="id" label="ID"></el-table-column>
      <el-table-column prop="reportName" label="报告名称" />
      <el-table-column prop="withdrawnBonus" label="操作">
        <template slot-scope="scope">
          <el-button type="success" size="mini" icon="el-icon-edit-outline" @click="editBtn(scope.row.id)">编辑
          </el-button>
        </template>
      </el-table-column>
      <el-table-column prop="gradeName" label="学段"></el-table-column>
      <el-table-column prop="createTime" label="编辑时间"></el-table-column>
      <el-table-column prop="edition" label="版本">
        <template slot-scope="scope">
          {{ scope.row.edition === 1 ? '普通版本' : '详细版本' }}
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <el-col :span="24" style="margin-bottom: 20px">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>

    <!-- 修改或者新增弹窗 -->
    <el-dialog title="编辑学科" :visible.sync="open" width="70%" @close="close">
      <el-form ref="form" :model="form" :rules="rules" label-width="120px" style="width: 70%;">
        <el-form-item label="计划名称:" prop="grade">
          <el-select v-model="form.grade" placeholder="年级" learable>
            <el-option v-for="item in gradeList" :key="item.value" :value="item.value" :label="item.label"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="学科名称:" prop="name">
          <el-input v-model="form.name" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import subjectApi from "@/api/paper/subject";
import abilityReport from "@/api/paper/abilityReport";
import gradeApi from "@/api/paper/grade";
import { pageParamNames } from "@/utils/constants";

export default {
  name: "subject",
  data() {
    return {
      dataQuery: {
        gradeId: undefined,
        name: ""
      },
      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      submitFalse:false,
      open: false,
      tableData: [],
      form: {},
      // 表单校验
      rules: {
        grade: [{ required: true, message: "请选择年级", trigger: "blur" }],
        name: [{ required: true, message: "请输入学科名称", trigger: "blur" }]
      },
      gradeList: [
        {
          id: undefined,
          name: "全部"
        }
      ]
    };
  },
  created() {
    let gradeId = this.$route.query.gradeId;
    this.dataQuery.gradeId = gradeId
    gradeApi.pageList().then((res) => {
      console.log(res)
      this.gradeList = [...this.gradeList, ...res.data.data]
    })

    this.getPageList();
  },
  methods: {
    addBtn() {
      this.reset();
      this.open = true;
      this.submitFalse=false
    },
    editBtn(id) {
      this.$router.push({ path: '/paper/abilityReportSave', query: { id: id } })
    },
    submitForm() {
     
      this.$refs["form"].validate(valid => {
        if (valid) {
          if(this.submitFalse){
            return
          }
          this.submitFalse=true
          if (this.form.id) {
            subjectApi.edit(this.form).then(response => {
              this.$message.success("提交成功！");
              this.open = false;
              this.submitFalse=false
              this.getPageList();
            });
          } else {
            subjectApi.create(this.form).then(response => {
              this.$message.success("提交成功！");
              this.open = false;
              this.submitFalse=false
              this.getPageList();
            });
          }
        }
      });
    },
    getPageList() {
      this.tableLoading = true;
      this.dataQuery.pageNum = this.tablePage.currentPage;
      this.dataQuery.pageSize = this.tablePage.size;


      abilityReport.pageList(this.dataQuery).then(res => {
        console.log(res.data.data);
        this.tableData = res.data.data;
        this.tableLoading = false;
        // 设置后台返回的分页参数
        pageParamNames.forEach(name =>
          this.$set(this.tablePage, name, parseInt(res.data[name]))
        );
      });
    },
    handlePage() {
      (this.tablePage = {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      })
      this.getPageList();
    },
    /** 重置按钮操作 */
    resetQuery(id) {
      this.dataQuery = {
        gradeId: undefined,
        name: ""
      };
      this.getPageList();
    },
    /**
     * 根据学段查询列表
     * @param id
     */
    gradeQuery(id) {
      this.dataQuery.gradeId = id;
      this.getPageList();
    },

    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val;
      this.getPageList();
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val;
      this.getPageList();
    },
    close() {
      this.open = false;
      this.reset();
    },
    reset() {
      this.form = {
        grade: null,
        name: ""
      };
    }
  }
};
</script>

<style>
.highlight {
  color: #00a0e9;
}

.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.period-table td,
.period-table th {
  text-align: center;
}

@media (max-width: 767px) {
  .el-message-box {
    width: 80% !important;
  }
}

.el-tooltip__popper {
  max-width: 800px;
}
</style>
