/**
 * 听力相关接口
 */
import request from '@/utils/request'

export default {
  //听力分页查询
  courseListeningList(pageNum, pageSize, data) {
    return request({
      url: '/znyy/course/listening/list/' + pageNum + '/' + pageSize,
      method: 'GET',
      params: data
    })
  },
  // 听力新增
  addCourseListening(data) {
    return request({
      url: '/znyy/course/listening',
      method: 'POST',
      data
    })
  },
  // 听力
  updateListening(data) {
    return request({
      url: '/znyy/course/listening/update/listening',
      method: 'PUT',
      data
    })
  },
  // 听力的回显查询
  queryListening(id) {
    return request({
      url: '/znyy/course/listening/seeDetails/' + id,
      method: 'GET'
    })
  },
  //删除
  deleteCourseListening(id){
    return request({
      url : '/znyy/course/listening/delete/' +id,
      method: 'DELETE'
    })
  },
  //删除听力
  deleteListening(id){
return request({
  url : "/znyy/course/listening/delete/listening/" +id,
  method : 'DELETE'
})
  },
  // 听力开通与暂停
  updateStatus(id,status) {
    return request({
      url: '/znyy/course/listening/update/statue?id=' + id + '&isEnable=' + status,
      method: 'PUT'
    })
  },
  getCurrentTime() {
    var _this = this;
    let yy = new Date().getFullYear();
    let mm = new Date().getMonth() + 1;
    let dd = new Date().getDate();
    let hh = new Date().getHours();
    let mf = new Date().getMinutes() < 10 ? '0' + new Date().getMinutes() : new Date().getMinutes();
    let ss = new Date().getSeconds() < 10 ? '0' + new Date().getSeconds() : new Date().getSeconds();
    _this.gettime = yy + '-' + mm + '-' + dd + ' ' + hh + ':' + mf + ':' + ss;
    return _this.gettime
  }
}
