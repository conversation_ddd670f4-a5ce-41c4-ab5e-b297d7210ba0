/**
 * 托管中心列表相关接口
 */
import request from '@/utils/request'

export default {

  //托管中心强制通过审批
  coerceSuccess(id) {
    return request({
      url: '/znyy/dealer/coerceSuccess/' + id,
      method: 'POST'
    })
  },

  // 托管中心流程图开启
  startAndTakeUserTaskByAddDealer(data) {
    return request({
      url: '/activiti/flowOnlineOperation/startAndTakeUserTask/addDealer',
      method: 'POST',
      data
    })
  },
  // 托管中心新增
  addDealerList(data) {
    return request({
      url: '/znyy/dealer',
      method: 'POST',
      data
    })
  },
  //获取托管中心级别
  getSelectResult() {
    return request({
      url: '/znyy/dealer/rank',
      method: 'GET'
    })
  },
  //加载其他托管中心
  loadOtherDelaer() {
    return request({
      url: '/znyy/dealer/dealer/checkList',
      method: 'GET'
    })
  },
  //加载符近的托管中心
  loadOtherExperienceStores(lat, lon) {
    return request({
      url: '/znyy/dealer/load/dealer?lat=' + lat + '&lon=' + lon,
      method: 'GET'
    })
  },
  // 托管中心级别分页查询
  dealerList(pageNum, pageSize, data) {
    return request({
      url: '/znyy/dealer/list/' + pageNum + '/' + pageSize,
      method: 'GET',
      params: data
    })
  },
  // 托管中心编辑
  updateDealer(data) {
    return request({
      url: '/znyy/dealer',
      method: 'PUT',
      data
    })
  },
  // 托管中心查询
  queryActive(id) {
    return request({
      url: '/znyy/dealer/check/detail/' + id,
      method: 'GET'
    })
  },
  // 托管中心开通与暂停
  dealerStatus(id, status) {
    return request({
      url: '/znyy/dealer/activationAndSuspension?id=' + id + '&isEnable=' + status,
      method: 'PUT'
    })
  },
  updatePaymentIsComplete(id, paymentIsComplete) {
    return request({
      url: '/znyy/dealer/updatePaymentIsComplete?id=' + id + '&paymentIsComplete=' + paymentIsComplete,
      method: 'PUT'
    })
  },
  //托管中心开通和暂停二级推荐分润
  updateReProfitRank(id, reProfitRank) {
    return request({
      url: '/znyy/dealer/update/profit?id=' + id + '&reProfitRank=' + reProfitRank,
      method: 'PUT'
    })
  },
  //
  //托管中心审核
  isCheckStatus(data) {
    return request({
      url: '/znyy/dealer/check',
      method: 'PUT',
      data
    })
  },
  // 导出
  simpleMarketExecl(listQuery) {
    return request({
      url: '/znyy/dealer/to/excel',
      method: 'GET',
      responseType: 'blob',
      params: listQuery
    })
  },
  simpleMarketExecl1(listQuery) {
    return request({
      url: '/znyy/operate/dealer/query',
      method: 'GET',
      responseType: 'blob',
      params: listQuery
    })
  },
  getCurrentTime() {
    var _this = this;
    let yy = new Date().getFullYear();
    let mm = new Date().getMonth() + 1;
    let dd = new Date().getDate();
    let hh = new Date().getHours();
    let mf = new Date().getMinutes() < 10 ? '0' + new Date().getMinutes() : new Date().getMinutes();
    let ss = new Date().getSeconds() < 10 ? '0' + new Date().getSeconds() : new Date().getSeconds();
    _this.gettime = yy + '-' + mm + '-' + dd + ' ' + hh + ':' + mf + ':' + ss;
    return _this.gettime
  },
  getRefCode() {
    return request({
      url: '/znyy/areas/dealer/get/ref',
      method: 'GET',
    })
  }

}
