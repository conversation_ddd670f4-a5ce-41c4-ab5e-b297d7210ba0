import request from '@/utils/request'

export default {
  // 分页查询
  userList(data) {
    return request({
      url: '/xi/web/learn/userList',
      method: 'GET',
      params: data
    })
  },
  detail(id) {
    return request({
      url: '/xi/web/sysUser',
      method: 'GET',
      params: {
        id: id
      }
    })
  },
//修改
  saveOrUpdate(data) {
    return request({
      url: '/xi/web/sysUser',
      method: 'POST',
      data
    })
  },
  //删除
  delete(id) {
    return request({
      url: '/xi/web/sysUser',
      method: 'DELETE',
      params: {
        id: id
      }
    })
  },
  //学员列表
  studentList(data) {
    return request({
      url: '/xi/web/learn/list',
      method: 'GET',
      params: data
    })
  },
  //添加学员
  saveStudent(managerId, studentCode) {
    return request({
      url: '/xi/web/learn/create',
      method: 'POST',
      params: {
        managerId: managerId,
        studentCode: studentCode
      }
    })
  },
  studentDetail(id) {
    return request({
      url: '/xi/web/learn/detail',
      method: 'GET',
      params: {
        id: id
      }
    })
  },
  preview(studentCode) {
    return request({
      url: '/xi/web/learn/preview',
      method: 'GET',
      params: {
        studentCode: studentCode
      }
    })
  },
  deleteStudent(id){
    return request({
      url: '/xi/web/learn/delete',
      method: 'DELETE',
      params: {
        id: id
      }
    })
  }
}
