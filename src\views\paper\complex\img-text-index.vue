<template>
  <div class="app-container">
    <el-form :model="dataQuery" ref="queryForm" :inline="true">
      <!--      <el-form-item label="分类：">
        <el-select v-model="dataQuery.titleType" clearable>
          <el-option v-for="item in titleType" :key="item.value" :value="item.value" :label="item.label"></el-option>
        </el-select>
      </el-form-item>-->
      <el-form-item label="题型：">
        <el-select v-model="dataQuery.questionType" clearable>
          <el-option v-for="item in questionType" :key="item.value" :value="item.value" :label="item.label"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" style="marginRight: 5px" @click="submitForm">查询</el-button>
        <el-popover placement="bottom" trigger="click">
          <el-button type="warning" size="mini" v-for="item in editUrlEnum" :key="item.titleType + item.questionType"
            @click="$router.push({ path: item.value })">{{ item.name }}</el-button>
          <el-button slot="reference" type="primary" class="link-left">添加</el-button>
        </el-popover>
        <!--        <el-button type="primary" class="link-left" @click="$router.push({ path: '/paper/imgRepeat' })">添加</el-button>-->
      </el-form-item>
    </el-form>
    <el-table class="common-table" v-loading="listLoading" :data="tableData" style="width: 100%; margin-bottom: 20px"
      row-key="id" border default-expand-all>
      <el-table-column prop="id" label="Id" />
      <el-table-column prop="titleType" label="分类" :formatter="titleTypeFormatter" width="120px" />
      <el-table-column label="操作" align="center" width="220px">
        <template slot-scope="{ row }">
          <el-button size="mini" @click="editQuestion(row)">编辑</el-button>
          <el-button size="mini" type="danger" @click="deleteQuestion(row)" class="link-left">删除</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="questionType" label="题型" :formatter="questionTypeFormatter" width="120px" />
      <el-table-column prop="topic" label="题目" show-overflow-tooltip>
        <template slot-scope="scope">
          <el-popover v-if="scope.row.title.includes('img')" placement="right" title="题目" trigger="hover">
            <div v-html="scope.row.title"></div>
            <div style="width:100px;height: 100px;" slot="reference" v-html="scope.row.title"></div>
          </el-popover>
          <div v-else>
            <div v-html="scope.row.title"></div>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间" />
    </el-table>
    <!-- 分页 -->
    <el-col :span="24" style="margin-bottom: 20px">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>
  </div>
</template>

<script>
import complexApi from '@/api/paper/complex-question'
import { mapGetters, mapState } from 'vuex';
import { pageParamNames } from "@/utils/constants";
import '/public/components/ueditor/themes/iframe.css'

export default {
  data() {
    return {
      dataQuery: {
        id: null,
        titleType: "IMG_TEXT",
        questionType: null,
      },
      listLoading: true,
      tableData: [],
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null,
      },
      total: 0,
    };
  },
  created() {
    this.getPageList()
  },
  methods: {
    close() {
      this.importOpen = false;
      this.importFrom = {
        file: null
      }
    },
    titleTypeFormatter(row, column, cellValue, index) {
      return this.enumFormat(this.titleType, cellValue)
    },
    questionTypeFormatter(row, column, cellValue, index) {
      return this.enumFormat(this.questionType, cellValue)
    },
    submitForm() {
      this.getPageList()
    },
    getPageList() {
      this.listLoading = true
      this.dataQuery.pageNum = this.tablePage.currentPage;
      this.dataQuery.pageSize = this.tablePage.size;
      complexApi.pageList(this.dataQuery).then((res) => {
        this.tableData = res.data.data;
        this.listLoading = false;
        // 设置后台返回的分页参数
        pageParamNames.forEach((name) =>
          this.$set(this.tablePage, name, parseInt(res.data[name]))
        );
      });
    },
    editQuestion(row) {
      let url = this.complexFormat(this.editUrlEnum, row.titleType + row.questionType);
      this.$router.push({ path: url, query: { id: row.id } });
    },
    deleteQuestion(row) {
      let _this = this
      this.$confirm('是否删除该题目？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        complexApi.deleteQuestion(row.id).then(re => {
          if (re.success) {
            _this.$message.success(re.message)
            _this.getPageList()
          } else {
            _this.$message.error(re.message)
          }
        })
      })
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val;
      this.getPageList();
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val;
      this.getPageList();
    },
  },
  computed: {
    ...mapGetters('enumItem', ['enumFormat', 'complexFormat']),
    ...mapState('enumItem', {
      titleType: state => state.complexQuestion.titleType,
      questionType: state => state.complexQuestion.questionType,
      editUrlEnum: state => state.complexQuestion.editUrlEnum,
    }),
  }
}
</script>

<style lang="less" scoped>
/deep/.el-dialog__header {
  border-bottom: 1px solid #dfdfdf;
}
</style>
<style>
.el-tooltip__popper {
  max-width: 800px;
}
</style>
