import request from '@/utils/request'

export default {
  // 分页查询
  riskPageList(params) {
    return request({
      url: '/znyy/risk/pageList',
      method: 'GET',
      params: params
    })
  },
  // 风控列表统计
  statistics(params) {
    return request({
      url: '/znyy/risk/statistics',
      method: 'GET',
      params: {riskRank: params}
    })
  },
  // 获取风控详情
  getRiskInfo(id) {
    return request({
      url: '/znyy/risk/info/' + id,
      method: 'GET'
    })
  },
  //获取上级渠道列表
  getParentList(merchantCode) {
    return request({
      url: '/znyy/risk/getParentList/' + merchantCode,
      method: 'GET'
    })
  },
  // 风控处理列表
  disposeListPage(params) {
    return request({
      url: '/znyy/risk/disposeListPage',
      method: 'GET',
      params: params
    })
  },
  //新增风控处理信息
  addDisposeRecord(params) {
    return request({
      url: '/znyy/risk/addDisposeRecord',
      method: 'POST',
      params: params
    })
  },
  //风控确认
  notarize(data) {
    return request({
      url: '/znyy/risk/notarize',
      method: 'POST',
      params: data
    })
  },

}
