<template>
  <div>
    <el-card class="frame" shadow="never">
      <el-form label-width="80px">
        <!-- 1 -->
        <el-row type="flex" justify="space-around">
          <el-col :span="6">
            <el-form-item label="学员编号:">
              <el-input v-model="searchNum.studentCode" :disabled="!!stuudentCode" clearable placeholder="请输入"
                size="small" style="width: 13vw"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="14">
            <el-form-item label="时间筛选:">
              <el-date-picker v-model="timeAll" style="width: 18vw" size="small" format="yyyy-MM-dd HH:mm:ss"
                value-format="yyyy-MM-dd HH:mm:ss" :picker-options="pickerOptions" align="right" type="datetimerange"
                range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
            </el-form-item>
          </el-col>
          <!-- <el-col :span="6">
            <el-form-item label="审核状态:">
              <el-select
                v-model="searchNum.checkStatus"
                clearable
                placeholder="请选择"
                style="width: 13vw"
              >
                <el-option label="未审核" value="0"></el-option>
                <el-option label="已审核" value="1"></el-option>
              </el-select>
            </el-form-item>
          </el-col> -->
          <el-col :span="4" :offset="2">
            <el-button type="primary" icon="el-icon-search" size="mini" @click="initData01">查询</el-button>
            <el-button type="warning" icon="el-icon-download" size="mini" @click="exportExcel" :loading="exportLoading">
              导出
            </el-button>
          </el-col>
        </el-row>
        <!-- 2 -->
        <!-- <el-row type="flex" justify="space-around">
          <el-col :span="14">
            <el-form-item label="时间筛选:">
              <el-date-picker
                v-model="timeAll"
                style="width: 18vw"
                size="small"
                format="yyyy-MM-dd HH:mm"
                value-format="yyyy-MM-dd HH:mm"
                :picker-options="pickerOptions"
                align="right"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="4" :offset="2">
            <el-button type="primary" icon="el-icon-search" size="mini" @click="initData01">查询</el-button>
            <el-button type="warning" icon="el-icon-download" size="mini" @click="exportExcel" :loading="exportLoading">
              导出
            </el-button>
          </el-col>
        </el-row> -->
        <!-- 3 -->
      </el-form>
    </el-card>
    <!-- 表格 -->
    <el-table v-loading="tableLoading" :data="luyouclassCard" style="width: 100%" id="out-table"
      :header-cell-style="getRowClass" :cell-style="{ 'text-align': 'center' }">
      <el-table-column width="50" align="center"></el-table-column>
      <el-table-column prop="merchantCode" min-width="60" label="门店编号" header-align="center" />
      <el-table-column prop="merchantName" min-width="100" label="所属门店" header-align="center" />
      <el-table-column prop="address" label="操作" header-align="center" width="200">
        <template v-slot="{ row }">
          <el-button type="warning" size="mini" @click="cancel(row)" v-if="row.status===1||row.status===2">取消
          </el-button>
          <!-- <el-button type="success" size="mini" @click="LeaveBtn(row)">查看详情</el-button>
          <el-button type="primary" size="mini" v-else @click="paikeBtn(row)">调课</el-button>
          <el-button type="success" size="mini" @click="lookBtn(row)">数据查看</el-button>
          <el-button type="danger" size="mini" v-if="row.studyStatus === 0" @click="deleteStudy(row)">删除</el-button> -->
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间" width="180" header-align="center"></el-table-column>
      <el-table-column prop="studentName" label="学员姓名" width="80" header-align="center"></el-table-column>
      <el-table-column prop="studentCode" label="学员编号" width="150" header-align="center"></el-table-column>
      <el-table-column prop="levelName" label="阶段" width="80" header-align="center"></el-table-column>
      <el-table-column prop="remark" label="说明" width="380" header-align="center"></el-table-column>
      <el-table-column prop="status" label="订单状态" header-align="center" width="150" :formatter="status">
        <template slot-scope="{ row }">
          <span :class="orderStatusClass(row.status)">{{ orderStatus(row) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="status" label="退款状态" header-align="center" width="180" :formatter="status">
        <template slot-scope="{ row }">
          <span :class="statusClass(row.refundStatus)">{{ row.refundStatusName }}</span>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页器 -->
    <el-row type="flex" justify="center" align="middle" style="height: 60px">
      <!-- 3个变量：每页数量、页码数、总数  -->
      <!-- 2个事件：页码切换事件、每页数量切换事件-->
      <el-pagination v-if="tableIshow" @size-change="handleSizeChange" @current-change="handleCurrentChange"
        :current-page="searchNum.pageNum" :page-sizes="[10, 20, 30, 40, 50]" :page-size="searchNum.pageSize"
        layout="total, sizes, prev, pager, next, jumper" :total="total"></el-pagination>
    </el-row>
  </div>
</template>

<script>
import { getAdjustInfo, getTimetable, cancelRefund, studentStudyExport } from '@/api/rechargeCourseOrder'
import ls from '@/api/sessionStorage'
import schoolList from "@/api/schoolList";
import systemApi from "@/api/systemConfiguration";

export default {
  name: 'classCard',
  data() {
    return {
      stuudentCode: null,
      dataLookerStyle: false,
      // 日期组件
      pickerOptions: {
        shortcuts: [
          {
            text: '今天',
            onClick(picker) {
              // const end = new Date();
              // const start = new Date();
              // picker.$emit('pick', [start, end]);
              const temp = new Date()
              picker.$emit('pick', [
                new Date(temp.setHours(0, 0, 0, 0)),
                new Date(temp.setHours(23, 59, 59, 0))
              ])
            }
          },
          {
            text: '昨天',
            onClick(picker) {
              const temp = new Date()
              temp.setTime(temp.getTime() - 3600 * 1000 * 24)
              picker.$emit('pick', [
                new Date(temp.setHours(0, 0, 0, 0)),
                new Date(temp.setHours(23, 59, 59, 0))
              ])
            }
          },
          {
            text: '最近七天',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
              picker.$emit('pick', [start, end])
            }
          }
        ]
      },
      qjstartTime: '',
      qjendTime: '',
      currentAdmin: '',
      tableIshow: true,
      redLeave: [],
      lookstyle: false,
      LeaveStyle: false,
      classCardstyle: false, //抽屉状态
      LeaveId: '',
      searchNum: {
        checkStatus: '',
        name: '',
        endTime: '',
        startTime: '',
        teacherName: '',
        studentCode: '',
        studyStatus: '',
        lastStudyTime: '',
        pageNum: 1,
        pageSize: 10,
        planId: undefined
      }, //搜索参数
      tableLoading: false,
      direction: 'rtl',//超哪边打开
      teacher: '',
      timeAll: [],
      contentType: '',
      total: null,
      luyouclassCard: [],
      leaveApplication: {
        id: '',
        type: 1
      },
      getFeedback: {
        //详情的参数
        id: '',
        type: '1'
      },
      isAdmin: false,
      exportLoading: false
    }
  },
  created() {
    this.isAdmin = ls.getItem('rolesVal') === 'admin' || ls.getItem('rolesVal') === 'JiaofuManager'
    this.initData()
    this.getRoleTag()
    this.stuudentCode = this.$route.query.classCard
    this.searchNum.planId = this.$route.query.planId
    this.searchNum.studentCode = this.stuudentCode ? this.stuudentCode : ''
  },
  methods: {
    getRoleTag() {
      schoolList.getCurrentAdmin().then((res) => {
        this.currentAdmin = res.data;

      })
    },
    // 搜索
    initData01() {
      this.searchNum.pageNum = 1,
        this.searchNum.pageSize = 10,
        this.initData()
    },
    dataLooker(v) {
      this.dataLookerStyle = v
    },
    statusClass(status) {
      switch (status) {
        case 2:
          return 'error'
        case 1:
          return 'warning'
        case 9:
          return
        default:
          return ''
      }
    },
    orderStatusClass(status) {
      switch (status) {
        case 11:
          return 'error'
        case 9:
          return 'error'
        case 8:
          return 'error'
        case 3:
          return 'normal'
        case 2:
          return 'warning'
        default:
          return ''
      }
    },
    qjstartTime1(v) {
      this.qjstartTime = v
    },
    qjendTime1(v) {
      this.qjendTime = v
    },
    async cancel(row) {
      let { data } = await cancelRefund(row.id)
      this.$message.success("取消成功")
      this.initData()
    },
    // 学习课程表请假处理
    async LeaveBtn(row) {
      let { data } = await getTimetable(this.searchNum)
    },
    async initData() {
      // 判断为null的时候赋空
      if (!this.timeAll) {
        this.timeAll = []
      }
      this.tableLoading = true
      this.searchNum.status = null;
      this.searchNum.startTime = this.timeAll[0]
      this.searchNum.endTime = this.timeAll[1]
      this.tableLoading = true
      let { data } = await getTimetable(this.searchNum)
      this.tableLoading = false
      this.total = Number(data.totalItems)
      this.luyouclassCard = data.data
    },
    LeaveDialog(v) {
      this.LeaveStyle = v
    },
    changeDrawer(v) {
      this.classCardstyle = v
    },
    lookDrawer(v) {
      this.lookstyle = v
    },
    // 转文字
    teachingType(val) {
      if (val.teachingType == 1) {
        return '远程'
      } else if (val.teachingType == 2) {
        return '线下'
      } else if (val.teachingType == 3) {
        return '远程和线下'
      } else {
        return '暂无'
      }
    },
    courseType(val) {
      if (val.courseType == 1) {
        return '鼎英语'
      }
    },
    status(val) {
      switch (val.status) {
        case 8:
          return '已取消'
        case 9:
          return '已拒绝'
        case 11:
          return '待审核'
        default:
          return '已审核或未该审核'
      }
    },
    orderStatus(val) {
      switch (val.status) {
        case 8:
          return '已取消'
        case 9:
          return '已拒绝'
        case 10:
          return '部分支付失败'
        case 11:
          return '等待审核'
        case 3:
          return '支付成功'
        case 4:
          return '支付失败'
        case 15:
          return '等待收款订单完成'
        default:
          return '支付中'
      }
    },
    studyStatus(val) {
      if (val.studyStatus == 0) {
        return '未上课'
      } else if (val.studyStatus == 2) {
        return '已上课'
      }
    },
    // 动态class
    getRowClass({ rowIndex, columnIndex }) {
      if (rowIndex == 0) {
        return 'background:#f5f7fa'
      }
    },
    // 分页
    handleSizeChange(val) {
      this.searchNum.pageSize = val
      this.initData()
    },
    handleCurrentChange(val) {
      this.searchNum.pageNum = val
      this.initData()
    },
    // 编辑按钮
    async paikeBtn(row) {
      if (row) {
        let reslist = await getAdjustInfo(row.id)
        this.$refs.rowshuju.classCardnum = reslist.data
        this.$refs.rowshuju.studentList = row
        this.$refs.rowshuju.teacherId = reslist.data.teacherId
        this.classCardstyle = true
        this.$refs.rowshuju.planStudy.studentCode = row.studentCode
        this.$refs.rowshuju.getTeachlist()
      } else {
        let reslist = await getAdjustInfo(this.LeaveId)
        this.$refs.rowshuju.classCardnum = reslist.data
        this.$refs.rowshuju.studentList = this.redLeave
        this.$refs.rowshuju.teacherId = reslist.data.teacherId
        this.classCardstyle = true
        this.$refs.rowshuju.getTeachlist()
        if (this.qjstartTime !== '' && this.qjstartTime != undefined && this.qjstartTime != null) {
          this.$refs.rowshuju.classCardnum.startStudyTime = this.qjstartTime
        }
        if (this.qjendTime !== '' && this.qjendTime != undefined && this.qjendTime != null) {
          this.$refs.rowshuju.classCardnum.endStudyTime = this.qjendTime
        }
      }
      // let reslist = await getAdjustInfo(row.id);
      // this.$refs.rowshuju.classCardnum = reslist.data;
      // this.$refs.rowshuju.studentList = this.redLeave;
      // this.$refs.rowshuju.teacherId = reslist.data.teacherId;
      // this.classCardstyle = true;
      // this.$refs.rowshuju.getTeachlist();
    },
    // 数据查看按钮
    //定义导出Excel表格事件
    exportExcel() {
      this.exportLoading = true
      studentStudyExport(this.searchNum).then(res => {
        console.log(window.URL.createObjectURL(res))
        const url = window.URL.createObjectURL(res)
        const link = document.createElement('a')
        link.style.display = 'none'
        link.href = url//获取服务器端的文件名
        link.setAttribute('download', '学习课程表.xls')
        document.body.appendChild(link)
        link.click()
        this.exportLoading = false
      })
    },
    //删除学习课程
    async deleteStudy(row) {
      await this.$confirm('您确定要删除学习课程吗?')
      await deletePlanStudy(row.id)
      this.$message.success('删除成功')
      await this.initData()
    }
  }
}
</script>

<style scoped>
body {
  background-color: #f5f7fa;
}

.normal {
  color: rgb(28, 179, 28);
}

.error {
  color: rgba(234, 36, 36, 1);
}

.warning {
  color: rgb(234, 155, 36);
}

body {
  background-color: #f5f7fa;
}
</style>

<style lang="scss" scoped>
.frame {
  margin-top: 0.5vh;
  background-color: rgba(255, 255, 255);
}

// .el-button--success {
//   color: #ffffff;
//   background-color: #6ed7c4;
//   border-color: #6ed7c4;
// }
</style>
