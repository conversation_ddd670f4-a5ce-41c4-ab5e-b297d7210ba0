<template>
  <div class="app-container">
    <!-- 搜索 -->
    <el-form ref="form" :inline="true" class="SearchForm" label-width="100px">
      <el-row>
        <el-col :span="8" :xs="24">
          <el-form-item label="题目:">
            <el-input v-model="dataQuery.question" @keyup.enter.native="fetchData02()" style="width: 200px;"
              placeholder="请输题目：" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="知识点:">
            <el-input v-model="dataQuery.grammarName" @keyup.enter.native="fetchData02()" style="width: 200px;"
              placeholder="请输入知识点：" clearable></el-input>
          </el-form-item>
        </el-col>
        <!-- <el-col :span="5" :xs="24">
          <el-form-item label="检测:">
            <el-select v-model="dataQuery.exerciseType" filterable value-key="value" placeholder="请选择" clearable>
              <el-option v-for="(item, index) in [
                { value: 'Exercise', label: '知识点' },
                { value: 'Review ', label: '语法' }]" :key="index" :label="item.label"
                :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col> -->
      </el-row>
      <el-row>
        <el-col :span="8" :xs="24">
          <el-form-item label="阶段:">
            <el-select v-model="dataQuery.phase" filterable value-key="value" placeholder="请选择"
              @change="check(dataQuery.phase)" clearable>
              <el-option v-for="(item, index) in phaseTypeList" :key="index" :label="item.value" :value="item.type" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="题型:">
            <el-select v-model="dataQuery.quesType" filterable value-key="value" placeholder="请选择"
              @change="check(dataQuery.quesType)" clearable>
              <el-option v-for="(item, index) in quesTypeList" :key="index" :label="item.value" :value="item.type" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="5" :xs="24">
          <el-form-item label="检测:">
            <el-select v-model="dataQuery.exerciseType" filterable value-key="value" placeholder="请选择" clearable>
              <el-option v-for="(item, index) in [
                { value: 'Exercise', label: '练习' },
                { value: 'Review ', label: '复习' }]" :key="index" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="2" :xs="8" style="text-align: right;">
          <el-button type="primary" icon="el-icon-search" size="mini" @click="fetchData02()">搜索</el-button>
        </el-col>
      </el-row>
      <el-col :span="15" style="height: 1px;" />
    </el-form>

    <div class="SearchForm">
      <!-- 添加 -->
      <div class="btn-add" style="margin-bottom: 20px">
        <el-button size="small" type="primary" icon="el-icon-plus" @click="clickAdd">添加</el-button>
      </div>
      <!-- 表格 -->
      <el-table class="common-table" :data="tableData" stripe border
        :default-sort="{ prop: 'addTime', order: 'descending' }" v-loading="tableLoading">
        <el-table-column prop="phase" label="阶段" sortable></el-table-column>
        <el-table-column prop="quesType" label="题型" sortable> </el-table-column>
        <el-table-column prop="id" label="操作" sortable>
          <template slot-scope="scope">
            <el-button type="success" size="mini" icon="el-icon-edit-outline"
              @click="openEdit(scope.row.id)">编辑</el-button>
            <el-button type="danger" size="mini" icon="el-icon-delete"
              @click="deleteQuestion(scope.row.id)">删除</el-button>
          </template>
        </el-table-column>
        <el-table-column prop="question" label="题目" sortable></el-table-column>
        <el-table-column prop="grammarName" label="知识点" sortable></el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <el-col :span="20">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 50, 100, 150, 200]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>

    <!-- 添加弹窗 -->
    <el-dialog title="添加题目" :visible.sync="dialogVisible" width="70%" :close-on-click-modal="false" @close="close">
      <el-form ref="addCourseWord" :rules="rules" :model="addCourseWord" label-position="left" label-width="120px"
        style="width: 100%">
        <el-form-item label="Excel上传" v-if="isRouterAlive">
          <el-upload class="upload-demo" action="https://jsonplaceholder.typicode.com/posts/" :on-change="beforeUplpad01"
            multiple :limit="1" :on-exceed="handleExceed" :on-remove="handleRemoveDetail" :auto-upload="false"
            name="flie">
            <div class="el-upload__text">
              <el-button size="small" type="primary" style>点击上传</el-button>
            </div>
            <div slot="tip" class="el-upload__tip">只能上传execel文件</div>
          </el-upload>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button v-if="addOrUpdate" size="mini" type="primary" @click="addActiveFun('addCourseWord')">新增</el-button>
        <el-button size="mini" @click="close">关闭</el-button>
      </div>
    </el-dialog>

    <!-- 编辑弹窗 -->
    <el-dialog title="编辑题目" :visible.sync="showEdit" width="60%" :close-on-click-modal="false" @close="close">
      <el-form :ref="'updateQuestion'" :rules="updateSingle" :model="updateQuestion" label-position="left"
        label-width="80px" style="width: 100%">
        <el-form-item label="id" prop="id" v-show="false">
          <el-col :xs="24" :span="12">
            <el-input v-model="updateQuestion.id"></el-input>
          </el-col>
        </el-form-item>
        <el-form-item label="题目" prop="question">
          <el-col :xs="24" :span="12">
            <el-input type="textarea" resize="none" :rows="4" v-model="updateQuestion.question"></el-input>
          </el-col>
        </el-form-item>
        <el-form-item label="解析" prop="analysis">
          <el-col :xs="24" :span="12">
            <el-input type="textarea" resize="none" :rows="4" v-model="updateQuestion.analysis"></el-input>
          </el-col>
        </el-form-item>

        <el-form-item label="考点选项" prop="testOptions" v-show="this.updateQuestion.quesType === 'CHOICE'">
          <el-col :xs="24" :span="12">
            <div v-for="(item,index) in updateQuestion.testOptions" :key="index">
              <el-input v-model="item.value"></el-input>
            </div>
            <el-button @click="addDomain1">添加</el-button>
            <el-button @click.prevent="removeInput1()">删除</el-button>
          </el-col>
        </el-form-item>

        <el-form-item label="考点答案" prop="leftAns" v-show="this.updateQuestion.quesType === 'CHOICE'">
          <el-col :xs="24" :span="12">
            <el-input type="textarea" resize="none" :rows="4" v-model="updateQuestion.leftAns"></el-input>
          </el-col>
        </el-form-item>


        <el-form-item label="选项" prop="option" v-show="this.updateQuestion.quesType === 'CHOICE'">
          <el-col :xs="24" :span="12">
            <!--                            <el-radio-group v-for="item in options" v-model="updateQuestion.options">-->
            <!--                              <el-radio :label="item.value">{{item.value}}</el-radio>-->
            <!--                            </el-radio-group>-->
            <div v-for="(item,index) in updateQuestion.options" :key="index">
              <el-input v-model="item.value"></el-input>
            </div>
            <el-button @click="addDomain">添加</el-button>
            <el-button @click.prevent="removeInput()">删除</el-button>
          </el-col>
        </el-form-item>

        <el-form-item label="答案" prop="rigthAns" v-show="this.updateQuestion.quesType === 'CHOICE'">
          <el-col :xs="24" :span="12">
            <el-input type="textarea" resize="none" :rows="4" v-model="updateQuestion.rightAns"></el-input>
          </el-col>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="editQuestion('updateQuestion')">确定</el-button>
        <el-button size="mini" @click="closeEdit">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import grammarApi from "@/api/grammar";
import {
  pageParamNames
} from "@/utils/constants";
export default {
  data() {
    return {
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null,
      },
      tableLoading: false,
      dataQuery: {
        grammarName: "",
        question: "",
        quesType: "",
        phase: "",
        difficulty: "",
        grammarId: ""
      },
      isRouterAlive: true, //局部刷新
      tableData: [], //表格数据
      dialogVisible: false, // 修改弹窗是否展示
      updateQuestion: {}, //更新题目集合
      showEdit: false, //编辑弹窗
      rows: [], //选中的数据id存放处
      addOrUpdate: true, // 是新增还是修改
      addCourseWord: {}, // 新增单词
      updateCourseWord: {}, // 修改数据
      rules: {},
      phase: "",
      fd: "",
      updateSingle: {
        question: [{
          required: true,
          message: '必填',
          trigger: 'blur'
        }],
        difficulty: [{
          required: true,
          message: '必填',
          trigger: 'blur'
        }],
        analysis: [{
          required: true,
          message: '必填',
          trigger: 'blur'
        }]
      },
      phaseTypeList: [], //课程分类
      quesTypeList: [], //题型分类
    };
  },
  created() {
    this.fetchData01();
    // 获取所属分类
    this.getPhaseType();
    this.getQuesType();
  },
  methods: {
    removeInput(item) {
      var arr = this.updateQuestion.options;
      console.log(arr.length);
      var index = arr.length - 1;
      if (index !== -1) {
        this.updateQuestion.options.splice(index, 1)
      }
    },
    removeInput1(item) {
      var arr = this.updateQuestion.testOptions;
      console.log(arr.length);
      var index = arr.length - 1;
      if (index !== -1) {
        this.updateQuestion.testOptions.splice(index, 1)
      }
    },
    addDomain() {
      this.updateQuestion.options.push({
        value: '',
        key: Date.now()
      });
    },
    addDomain1() {
      this.updateQuestion.testOptions.push({
        value: '',
        key: Date.now()
      });
    },

    fetchData01() {
      this.tablePage = {
        currentPage: 1,
        size: this.tablePage.size,
        totalPage: null,
        totalItems: null
      }
      this.fetchData()
    },
    fetchData02() {
      this.tablePage = {
        currentPage: 1,
        size: this.tablePage.size,
        totalPage: null,
        totalItems: null
      }
      window.localStorage.setItem("grammarId", "");
      this.fetchData()
    },
    // 获取分类返回类型
    getPhaseType() {
      grammarApi.getPhaseType().then((res) => {
        this.phaseTypeList = res.data;
      });
    },
    getQuesType() {
      grammarApi.getQuesType().then((res) => {
        this.quesTypeList = res.data;
      });
    },

    // 查询表格列表
    fetchData() {
      const that = this;
      that.dataQuery.grammarId = window.localStorage.getItem("grammarId");
      grammarApi.getGrammarQuestion(
        that.tablePage.currentPage,
        that.tablePage.size,
        that.dataQuery,
      )
        .then((res) => {
          that.tableData = res.data.data;
          that.tableLoading = false;
          // 设置后台返回的分页参数
          pageParamNames.forEach((name) =>
            that.$set(that.tablePage, name, parseInt(res.data[name]))
          );
        });
    },
    //局部刷新
    reload() {
      this.isRouterAlive = false;
      this.$nextTick(function () {
        this.isRouterAlive = true;
      });
    },
    //添加操作
    clickAdd() {
      this.addCourseWord = {
        word: "",
        courseCode: this.$route.query.courseCode,
      };
      this.reload();
      this.fd = "";
      this.dialogVisible = true;
      this.addOrUpdate = true;
      this.$nextTick(() => this.$refs["addCourseWord"].clearValidate());
    },
    //删除操作题目
    deleteQuestion(id) {
      this.$confirm("确定操作吗?", "删除题目", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          grammarApi.deleteQuestion(id)
            .then((res) => {
              this.$nextTick(() => this.fetchData());
              this.$message.success("删除题目成功!");
            })
            .catch((err) => { });
        })
        .catch((err) => { });
    },
    // 打开编辑题目
    openEdit(id) {
      grammarApi.findOneById(id).then(res => {
        this.updateQuestion = res.data;
        this.updateQuestion.options = res.data.options != null ? res.data.options : [];
        this.updateQuestion.testOptions = res.data.testOptions != null ? res.data.testOptions : [];
        this.testOptions = res.data.testOptions;
        console.log(res.data);
      })
      this.showEdit = true;
    },
    //编辑单个题目
    editQuestion(ele) {
      const that = this;
      that.$refs[ele].validate((valid) => {
        // 表单验证
        if (valid) {
          const loading = this.$loading({
            lock: true,
            text: "编辑题目提交",
            spinner: "el-icon-loading",
            background: "rgba(0, 0, 0, 0.7)",
          });
          grammarApi.editGrammarQuestion(that.updateQuestion).then(() => {
            that.showEdit = false;
            loading.close();
            that.$nextTick(() => that.fetchData());
            that.$message.success("修改题目成功");
          })
            .catch((err) => {
              // 关闭提示弹框
              loading.close();
            });
        } else {
          console.log("error submit!!");
          // loading.close();
          return false;
        }
      });
    },
    closeEdit() {
      this.showEdit = false;
    },
    //编辑单个题目
    // 题目提交
    addActiveFun(ele) {
      const that = this;
      that.$refs[ele].validate((valid) => {
        // 表单验证
        if (valid) {
          if (that.fd != "") {
            const loading = this.$loading({
              lock: true,
              text: "新增题目",
              spinner: "el-icon-loading",
              background: "rgba(0, 0, 0, 0.7)",
            });
            //上传execel文件
            let file = new FormData();
            if (that.fd != "") {
              file.append("file", that.fd);
            }
            console.log(that.addCourseWord);
            grammarApi.addGrammarWords(file)
              .then((res) => {
                that.dialogVisible = false;
                loading.close();
                that.$nextTick(() => that.fetchData());
                that.$message.success(res.data);
              }, s => {
                if (s === "error") {
                  loading.close();
                }
              })
              .catch((err) => {
                loading.close();
              });
          } else {
            alert("题目必须要上传哦~")
          }
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    handleRemoveDetail() {
      this.fd = "";
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val;
      this.fetchData();
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val;
      this.fetchData();
    },
    // 关闭弹窗
    close() {
      this.dialogVisible = false;
    },
    //上传模板
    beforeUplpad01(file) {
      this.fd = file.raw;
    },
    //限制文件
    handleExceed(files, fileList) {
      this.$message.warning(
        `当前限制选择 1个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length
        } 个文件`
      );
    },
  },
  //生命周期结束 离开页面时销毁本地缓存
  beforeDestroy() {
    window.localStorage.setItem("grammarId", "");
  }
};
</script>

<style>
.lh36 {
  line-height: 36px;
  font-size: 14px;
}

.SearchForm {
  width: 100%;
  margin-bottom: 15px;
  background: #fff;
  border-radius: 6px;
  padding: 5px 0;
  padding-left: 5px;
  box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.2);
}

.btn-add {
  padding: 5px;
}

.red {
  color: red;
}

.green {
  color: green;
}

.course-table {
  text-align: center;
}

.course-table td,
.course-table th {
  padding: 5px 0;
  text-align: center;
}

.course-table button {
  padding: 2px;
}

.icon-stop {
  float: left;
  display: inline-block;
  width: 12px;
  height: 12px;
  background: url("../../icons/stop.png") no-repeat top center/contain;
}

.mt22 {
  margin-top: 22px;
}

.blue {
  margin-right: 50px;
  color: #409eff;
}

@media (max-width:767px) {
  .el-message-box {
    width: 80% !important;
  }
}</style>
