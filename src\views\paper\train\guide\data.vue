<template>
  <div class="app-container">

    <el-col :span="24" style="margin-bottom: 30px">
      <el-button type="success" icon="el-icon-plus" size="mini" @click="handleAdd()">新增</el-button>
      <el-button type="warning" icon="el-icon-back" size="mini" @click="goBack()">返回</el-button>
    </el-col>
    <el-table
      class="common-table"
      v-loading="tableLoading"
      :data="tableData"
      style="width: 100%;margin-bottom: 20px;"
      row-key="id"
      border
      default-expand-all
    >
      <el-table-column prop="id" label="ID"></el-table-column>
      <el-table-column prop="title" label="答案" show-overflow-tooltip></el-table-column>
      <el-table-column prop="withdrawnBonus" label="操作" width="480">
        <template slot-scope="scope">
          <el-button
            type="success"
            size="mini"
            icon="el-icon-edit-outline"
            @click="handleUpdate(scope.row.id)"
          >编辑</el-button>
          <el-button
            type="danger"
            size="mini"
            icon="el-icon-delete"
            @click="handleDelete(scope.row.id)"
          >删除</el-button>
        </template>
      </el-table-column>
      <el-table-column label="参数值" align="center" >
        <template slot-scope="scope">
          <span v-if="scope.row.isValue">{{scope.row.value}}</span>
          <span v-else>无</span>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <el-col :span="24" style="margin-bottom: 20px">
      <el-pagination
        :current-page="tablePage.currentPage"
        :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="tablePage.totalItems"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-col>

    <!-- 修改或者新增弹窗 -->
    <el-dialog title="编辑引导问题" :visible.sync="open" width="70%" @close="cancel">
      <el-form ref="form" :model="form" :rules="rules" label-width="120px" style="width: 70%;">
        <el-form-item label="答案:" prop="title">
          <el-input v-model="form.title" />
        </el-form-item>
        <el-form-item label="有无参数：" required>
          <el-radio-group v-model="form.isValue">
            <el-radio :label="false">否</el-radio>
            <el-radio :label="true">是</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="参数值：" v-if="form.isValue" required>
          <el-input v-model="form.value"/>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import guidePageApi from '@/api/paper/train/guidePage'
import { pageParamNames } from '@/utils/constants'

export default {
  data() {
    return {
      parentId:'',
      dataQuery: {
        parentId: null
      },
      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      open: false,
      tableData: [],
      form: {},
      // 表单校验
      rules: {
        title: [{ required: true, message: "请输入标题", trigger: "blur" }],
      }
    };
  },
  created() {
    this.parentId = this.$route.query.id;
    this.getList();
  },
  methods: {
    goBack(){
      this.$router.push("/train/guide")
    },

    handleUpdate(id){
      this.reset();
      guidePageApi.detail(id).then(res => {
        this.form = res.data;
        this.open = true;
      });
    },
    handleDelete(id){
      this.$confirm('确定要删除吗？', '删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        guidePageApi.delete(id).then(res => {
          this.$message.success('删除成功！')
          this.getList()
        })
      })
    },
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          guidePageApi.createOrUpdate(this.form).then(response => {
            this.$message.success("提交成功！");
            this.open = false;
            this.getList();
          });
        }
      });
    },

    getList() {
      this.loading = true;
      this.dataQuery.parentId = this.parentId;
      this.dataQuery.pageNum = this.tablePage.currentPage;
      this.dataQuery.pageSize = this.tablePage.size;
      guidePageApi.list(this.dataQuery).then(res => {
        this.tableData = res.data.data;
        this.loading = false;
        // 设置后台返回的分页参数
        pageParamNames.forEach(name =>
          this.$set(this.tablePage, name, parseInt(res.data[name]))
        );
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        parentId: this.parentId,
        title: '',
        remark: '',
        isValue: false,
        value: '',
        sort: 1
      };
    },

    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val;
      this.getList();
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val;
      this.getList();
    },
  }
};
</script>
