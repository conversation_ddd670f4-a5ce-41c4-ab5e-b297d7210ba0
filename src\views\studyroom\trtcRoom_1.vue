// 非固定
<template>
  <div class="center-page" v-loading="loading">
    <div class="header">
      <div class="header_left">
        <div class="header_item" @click="editClassRoomName('name')">
          <div class="room_name">{{ roomName }}</div>
          <div class="edit">
            <i class="el-icon-edit"></i>
          </div>
        </div>
        <div class="left_bottom">
          <div class="item" @click="changeScreen" style="cursor: pointer;">
            <img src="../../assets/images/studyRoom/change.png" alt="">
            <div>切换&nbsp;{{ screenChoose }}&nbsp;分屏</div>
          </div>
          <div class="item person">
            在线人数: {{ roomInfo.currentCount }}&nbsp;人
          </div>
        </div>
      </div>
      <div class="header_item">
        <div class="quit_box" @click="switchStream">
          <div class="share">
            <i class="el-icon-share"></i>
          </div>
          <div>{{ screen ? '切换为摄像头' : '共享屏幕' }}</div>
        </div>
        <div class="quit_box">
          <div class="quit" @click="leaveRoom">
            <i class="el-icon-switch-button"></i>
          </div>
          <div>退出</div>
        </div>
      </div>
    </div>
    <div class="content">
      <div class="video-box">
        <div class="on-box">
          <div id="remote-video-wrap" class="remote-video-wrap">
            <div :id="item.id" class="remoteVidwo video_box"
                 :style="`width:${videoSize.videoWidth};height:${videoSize.videoHeight}`"
                 v-for="(item,index) in videoList" :key="index"
            >
              <div class="name">{{ item.user.nickName }}</div>
              <div class="video_bottom">
                <div @click="changeOtherMode('chat',item.user.studentCode)">
                  <img
                    :src="getStatus(`${item.user.studentCode? item.user.studentCode:''}`,'disableMessage')? require('@/assets/images/studyRoom/chatClose.png'):require('@/assets/images/studyRoom/chatOpen.png')"
                  >
                </div>
                <div @click="changeOtherMode('mico',item.user.studentCode)">
                  <img
                    :src="getStatus(`${item.user.studentCode? item.user.studentCode:''}`,'disableMic')? require('@/assets/images/studyRoom/micoClose.png'):require('@/assets/images/studyRoom/micoOpen.png')"
                  >
                </div>
                <div @click="changeOtherMode('chatLine',item.user.studentCode)">
                  <img src="../../assets/images/studyRoom/chatLine.png">
                </div>
                <div @click="changeOtherMode('video',item.user.studentCode)">
                  <img
                    :src="getStatus(`${item.user.studentCode? item.user.studentCode:''}`,'disableCamera')? require('@/assets/images/studyRoom/videoClose.png'):require('@/assets/images/studyRoom/videoOpen.png')"
                  >
                </div>
                <div @click="changeOtherMode('like',item.user.studentCode,index)">
                  <img
                    :src="item.like? require('@/assets/images/studyRoom/like.png'):require('@/assets/images/studyRoom/noLike.png')"
                  >
                </div>
                <div @click="changeOtherMode('out',item.user.studentCode)">
                  <img src="../../assets/images/studyRoom/out.png">
                </div>
                <div @click="changeOtherMode('refresh',item.user.studentCode)">
                  <img style="width:20px" src="../../assets/images/studyRoom/refresh.png">
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="footer">
          <div class="handle">
            <div class="handle_item" @click="allMessageAble">{{ roomInfo.chatStatus == 1 ? '关闭' : '开启' }}评论</div>
            <!-- 系统课的固定时间才有pk -->
            <div class="handle_item" @click="allPKAble">{{ roomInfo.soloStatus == 1 ? '关闭' : '开启' }}PK</div>
            <div class="handle_item" @click="closeRoom">关闭自习室</div>
          </div>
          <el-pagination
            background
            layout="prev,total, sizes,pager, next"
            @size-change="sizeChange"
            @current-change="currentChange"
            :page-size="page.pageSize"
            :page-sizes="[4, 9, 12]"
            :current-page="page.pageNum"
            :page-count="page.pageTotal"
            :total="page.total"
          >
          </el-pagination>
        </div>
      </div>

      <!-- 进入房间人员 -->
      <div class="mine-box">
        <div id="local_stream" class="local_stream video_box" style="marginLeft:0">
          <div class="name">{{ userId }}</div>
          <div class="video_bottom">
            <div @click="getmicFlag">
              <img
                :src="micFlag? require('@/assets/images/studyRoom/micoOpen.png'):require('@/assets/images/studyRoom/micoClose.png')"
              >
            </div>
            <div>
              <img src="../../assets/images/studyRoom/chatOpen.png">
            </div>
            <div @click="getvideoFlag">
              <img
                :src="videoFlag? require('@/assets/images/studyRoom/videoOpen.png'):require('@/assets/images/studyRoom/videoClose.png')"
              >
            </div>
            <div><img style="width:20px" src="../../assets/images/studyRoom/refresh.png"></div>
          </div>
        </div>

        <!-- 公告 -->
        <div class="public">
          <div class="left">
            <img class="icon" src="../../assets/images/studyRoom/laba.png" alt="">
            <span style="color:#18b48e">&nbsp;&nbsp;公告:</span>
          </div>
          <div class="center" @click="editClassRoomName('public')">
            {{ publicNotice }} &nbsp;|
            <i class="el-icon-edit"></i>
          </div>
        </div>

        <div class="message_scroll" ref="message_scroll">
          <div class="message_list" ref="message_list">
            <div class="message_item" v-for="(item,index) in currentMessageList" :key="index">
              <span style="color:#409EFF">{{ item.nick }}</span>
              :{{ item.payload.text ? item.payload.text.replace('点##赞', '点赞') : item.payload.text }}
            </div>
          </div>
        </div>
        <div class="send_message_box">
          <div class="input_box">
            <el-input class="input_style" style="flex:1;" v-model="inputText" placeholder="说点什么"
                      @keyup.enter.native="sendText"
            ></el-input>
            <div @click="sendText">
              <i style="color:#666666" class="el-icon-s-promotion"></i>
            </div>
          </div>
          <el-popover
            placement="top"
            width="210"
            trigger="hover"
            v-model="visible"
          >
            <div class="emoji_scroll" v-if="emojiShow">
              <div class="emoji_list">
                <div class="emoji_item" v-for="(item,index) in emjiList" :key="index" @click="chooseEmoji(item)">
                  {{ item }}
                </div>
              </div>
            </div>
            <div slot="reference" style="marginRight:12px;marginLeft:10px" @click="emojiShow=true">
              <img style="width: 30px;height: 30px;" src="../../assets/images/studyRoom/emoji.png" alt="">
            </div>
          </el-popover>
        </div>
      </div>
      <!-- 发送协议 -->
      <protocol :protocol="protocol"></protocol>
    </div>

    <!-- 检测弹窗 -->
    <DeviceDetector
      :visible="handleDialogVisible"
      @onClose="handleClose"
      :hasNetworkDetect="true"
      :networkDetectInfo="networkDetectInfo"
    ></DeviceDetector>

    <!-- 呼叫弹窗 -->
    <div class="shape_dialog">
      <el-dialog
        :visible.sync="callDialogShow"
        :show-close="false"
        :close-on-click-modal="false"
        center
        width="20%"
      >
        <p style="color:#fff;fontSize:16px;textAlign:Center" v-if="!activeCall&&!nowCall.status">是否接听对方呼叫</p>
        <p style="color:#fff;fontSize:16px;textAlign:Center" v-if="nowCall.status == 2|| nowCall.status==3">
          {{ getCallStatus(nowCall.status) }}</p>
        <p style="color:#fff;fontSize:16px;textAlign:Center" v-else-if="activeCall">正在等待对方接听...</p>
        <p style="color:#fff;fontSize:16px;textAlign:Center" v-else-if="!activeCall">有学员正在呼叫...</p>

        <span slot="footer" class="dialog-footer">
          <el-button @click="endCall" v-if="nowCall.status == 2|| nowCall.status==3">结束</el-button>
          <el-button type="primary" v-else-if="!activeCall" @click="confirmCall">同意接听</el-button>
        </span>
      </el-dialog>
    </div>

    <!-- 编辑弹窗 -->
    <el-dialog
      :title="editTitle"
      :visible.sync="editDialogVisible"
      :close-on-click-modal="false"
      ref="editDialogRef"
      width="30%"
    >
      <el-input type="textarea" autosize v-model="editInputCon" placeholder="请输入内容" clearable></el-input>
      <span slot="footer">
        <el-button @click="editDialogVisible = false;">取 消</el-button>
        <el-button type="primary" @click="editRoomName">确 定</el-button>
      </span>
    </el-dialog>

  </div>

</template>
<script>
//导入sdk
import store from '@/store'
import studyRoomApi from '@/api/studyroom/studyRoom'
import TIM from 'tim-wx-sdk'
import $ from 'jquery'
import TRTC from 'trtc-js-sdk'
import RTCDetect from 'rtc-detect'
import DeviceDetector from 'rtc-device-detector-react'
import 'rtc-device-detector-react/dist/index.css'

export default {
  components: {
    DeviceDetector
  },
  data() {
    return {
      remoteClientMap: new Map(),
      userInfoMap: new Map(),
      statusInfoMap: new Map(),
      remoteClientPlayingArray: [],
      remoteClientNotPlayArray: [],
      networkDetectInfo: {
        uplinkUserInfo: {
          uplinkUserId: 'uplink_test',
          uplinkUserSig: ''
        },
        downlinkUserInfo: {
          downlinkUserId: 'downlink_test',
          downlinkUserSig: ''
        }
      },
      joinParam: {},
      timer: {},
      remoteCount: 0,
      nowCall: { 'status': 0 },
      myStatus: {},
      checkResult: {},
      sdkAppId: 0,
      forceFirstPlayUserCode: '',
      statusLoadDown: true,
      loading: false,
      published: false,
      userId: '', //用户id --可更改
      roomId: null,//房间号--加入相同房间才能聊
      groupId: '',//群组id
      client: '', //客户端服务
      screen: false,
      remoteStream: '', //远方播放流
      localStream: '', //本地流
      videoFlag: true, //视频开关
      micFlag: true, //麦克风开关
      allmicFlag: false, //全体禁言
      rtc: null,
      tim: {},
      callConfirm: true,
      protocol: false, //发送协议
      userSig: '', //签名
      userType: '', //id身份
      shareId: '',
      userName: '', //姓名
      meetingPerson: [], //远端人员
      meetingId: '', //会议ID
      userNames: '',
      inputText: '', // 文本输入
      messageList: [], // 消息列表
      emjiList: [
        '😀', '😄', '😅', '🤣', '😂', '😉', '😊', '😍', '😘', '😜',
        '😝', '😏', '😒', '🙄', '😔', '😴', '😷', '🤮', '🥵', '😎',
        '😮', '😰', '😭', '😱', '😩', '😡', '💀', '👽', '🤓', '🥳',
        '😺', '😹', '😻', '🤚', '💩', '👍', '👎', '👏', '🙏', '💪'
      ], // 表情包列表
      emojiShow: true,
      screenChoose: 4,
      videoSize: {
        videoWidth: '360px',
        videoHeight: '236px'
      },
      videoList: [], // 视频
      handleDialogVisible: false, // 操作弹窗显示
      page: {
        pageNum: 1,
        pageTotal: 0,
        pageSize: 4,
        total: 0
      },
      memberList: [], // 待添加的群成员数组
      memberInfo: {}, // 群组信息
      timSDKisReady: false,//tim是否准备好
      conversationID: '', //会话ID
      nextReqMessageID: '',//获取下一个消息列表的ID 目前默认一页15条
      isCompleted: false,//消息分页 是否到最后一页
      currentConversation: {},
      currentMessageList: [],//当前消息列表
      haveCurrentConversation: false,//判断是否获取了当前会话
      callDialogShow: false, // 呼叫弹窗
      detectPass: false,
      activeCall: false,
      roomName: '', // 房间名称
      editAble: true, // 编辑修改房间
      editDialogVisible: false,
      editTitle: '修改自习室名称',
      publicNotice: '',
      editInputCon: '',
      editType: '',
      roomInfo: {} // 房间信息
    }
  },
  // components: { protocol },
  async created() {
    if (this.$route.params && this.$route.params.id) {
      this.roomId = this.$route.params.id
    }
    this.userId = store.getters.code
    this.shareId = this.userId + '-share'

  },
  init() {

  },
  mounted() {
    //测试用，所以直接创建了，其他需求可自行更改
    // this.createClient();
    this.getRoomInfo()
    // this.refreshUserInformationPeriodically();
  },

  beforeDestroy() {
    this.leaveRoom()
    this.tim.off(TIM.EVENT.MESSAGE_RECEIVED, this.messageReceived)//收到新消息
    this.tim.off(TIM.EVENT.CONVERSATION_LIST_UPDATED, this.conversationListUpdated)//会话列表更新
    clearInterval(this.timer)
  },

  methods: {
    getRoomInfo() {
      studyRoomApi.getRoomInfo(this.roomId).then(res => {
        if (res.data) {
          this.roomId = Number.parseInt(res.data.studyRoomId)
          this.groupId = res.data.groupId
          this.publicNotice = res.data.announcement
          this.roomName = res.data.name
          this.roomInfo = res.data
          this.getSignParams()
        }
      })
    },
    confirmCall() {
      studyRoomApi.confirmCall(this.nowCall.id, this.callConfirm).then((res) => {
        if (!res.success) {
          this.$message.error(res.message)
        }
      })
    },

    endCall() {
      studyRoomApi.endCall(this.nowCall.id).then((res) => {
        if (!res.success) {
          this.$message.error(res.message)
        }
      })
      this.callDialogShow = false
    },

    confirmCount() {
      if (this.remoteClientPlayingArray.length === this.remoteCount || this.remoteCount < 1) {
        return
      }
      if (this.remoteClientPlayingArray.length < this.remoteCount) {
        if (this.remoteClientNotPlayArray.length < 1) {
          return
        }
        while ((this.remoteClientPlayingArray.length < this.remoteCount) && this.remoteClientNotPlayArray.length > 0) {
          this.remoteClientPlayingArray.push(this.remoteClientNotPlayArray[0])
          this.remoteClientNotPlayArray.splice(0, 1)
        }
      } else {
        while ((this.remoteClientPlayingArray.length > this.remoteCount) && this.remoteClientPlayingArray.length > 0) {
          this.remoteClientNotPlayArray.push(this.remoteClientPlayingArray[0])
          this.remoteClientPlayingArray.splice(0, 1)
        }
      }
      this.refreshVideoDocument()
    },
    getCallStatus(status) {
      if (status === 0) {
        return '未呼叫'
      }
      if (status === 1) {
        return '正在呼叫...'
      }
      if (status === 2) {
        return '正在通话中'
      }
      if (status === 3) {
        return '已拒绝通话'
      }
      return '未知'
    },
    //创建链接
    createClient() {
      this.loading = true
      var that = this
      // this.importMember() //后台进入房间时已经加群

      studyRoomApi.joinRoom(that.joinParam).then(() => {
        // if (res.code == 1000) {
        that.allmicFlag = true
        that.loading = false
        // 创建 SDK 实例，`TIM.create()`方法对于同一个 `sdkAppId` 只会返回同一份实例
        const appId = that.networkDetectInfo.sdkAppId

        that.tim = TIM.create({
          SDKAppID: appId  // 接入时需要将0替换为您的即时通信 IM 应用的 sdkAppId
        })
        let promise = that.tim.login({ userID: that.userId, userSig: that.networkDetectInfo.userSig })
        promise.then(function(imResponse) {
          console.log(imResponse.data) // 登录成功
          if (imResponse.data.repeatLogin === true) {
            // 标识账号已登录，本次登录操作为重复登录。v2.5.1 起支持
            console.log(imResponse.data.errorInfo)
          } else {
            that.$message.success('登录成功')
          }

        }).catch(function(imError) {
          console.warn('login error:', imError) // 登录失败的相关信息
        })

        // 绑定监听事件：
        this.bindEvents()

        that.client = TRTC.createClient({
          mode: 'rtc',
          sdkAppId: appId,
          userId: that.userId,
          shareId: that.shareId,
          userSig: that.networkDetectInfo.userSig
        })
        that.joinRoom(that.client, that.roomId, that.userId)
        //注册远程监听，要放在加入房间前--这里用了发布订阅模式
        that.subscribeStream(that.client)
      })
    },
    async getSignParams() {
      var that = this

      that.joinParam.studyRoomId = that.roomId
      that.joinParam.groupId = that.groupId
      await studyRoomApi.getUserSig(this.userId).then(async(res) => {
        studyRoomApi.getUserSig('testNetworkUser').then(async(res2) => {
          let role = store.getters.roles

          that.joinParam.academicCommittee = false
          that.networkDetectInfo.userSig = res.data.signValue
          that.networkDetectInfo.uplinkUserInfo.uplinkUserId = that.userId
          that.networkDetectInfo.uplinkUserInfo.uplinkUserSig = that.networkDetectInfo.userSig
          that.networkDetectInfo.downlinkUserInfo.downlinkUserId = 'testNetworkUser'
          that.networkDetectInfo.downlinkUserInfo.downlinkUserSig = res2.data.signValue
          that.networkDetectInfo.sdkAppId = Number.parseInt(res.data.sdkAppId)
          that.networkDetectInfo.roomId = that.roomId
          for (let key in role) {
            if ('Studycommittee' == role[key].val) {
              that.joinParam.academicCommittee = true
            }
          }
          that.handleDialogVisible = true
          // console.log(that.checkResult)
          // console.log("1")
          // if (that.micFlag && that.videoFlag && that.checkResult.speakers.length > 0) {
          //
          that.detectPass = true
        })

      })
    },
    initRoom() {
      this.createClient()
      this.scrollMessageListToButtom()
    },
    //加入房间
    joinRoom(client, roomId, userId) {
      let that = this
      client
        .join({ roomId })
        .catch((error) => {
          console.error('进房失败 ' + error)
          setTimeout((res) => {
            that.$router.replace({
              path: '/mediatorDetails/' + this.$route.query.id
            })
          }, 500)
        })
        .then(() => {
          console.log('进房成功')
          //创建本地流
          that.createStream(client, userId)
          //播放远端流
          that.playStream(client)
          that.refreshUserInformationPeriodically()
        })
    },

    //创建本地音视频流
    createStream(client, userId) {
      let that = this
      that.localStream = TRTC.createStream({
        userId,
        video: that.videoFlag,
        audio: that.micFlag
      })
      that.localStream
        .initialize()
        .catch((error) => {
          console.error('初始化本地流失败 ' + error)
          that.videoFlag = false
          that.micFlag = false
        })
        .then(() => {
          // 创建好后才能播放 本地流播放 local_stream 是div的id
          that.localStream
            .play('local_stream')
            .then(() => {
              // autoplay success
              //创建好后才能发布
              that.publishStream()
            })
            .catch((e) => {
              const errorCode = e.getCode()
              if (errorCode === 0x4043) {
                // PLAY_NOT_ALLOWED,引导用户手势操作恢复音视频播放
                that.localStream.resume()
              }
            })
        })
    },
    switchStream() {
      this.screen = !this.screen
      let that = this
      if (this.screen) {
        if (!TRTC.isScreenShareSupported()) {
          that.$message.error('当前设备不支持屏幕共享')
        }
      }

      if (this.published) {
        that.client.unpublish(that.localStream).then(() => {
          that.republish()

        })
      } else {
        that.republish()
      }

    },
    republish() {
      let that = this
      that.localStream.stop()
      that.localStream.close()
      that.localStream = null
      const userId = that.userId
      that.localStream = TRTC.createStream({
        userId,
        audio: that.micFlag,
        screen: that.screen,
        video: !that.screen
      })
      that.localStream
        .initialize()
        .catch((error) => {
          console.error('初始化本地流失败 ' + error)
          that.videoFlag = false
          that.micFlag = false
        })
        .then(() => {
          console.log('初始化本地流成功')
          that.publishStream(that.client, that.localStream)
          // 创建好后才能播放 本地流播放 local_stream 是div的id
          that.localStream
            .play('local_stream')
            .then(() => {
              // autoplay success
              //创建好后才能发布
            })
            .catch((e) => {
              const errorCode = e.getCode()
              if (errorCode === 0x4043) {
                // PLAY_NOT_ALLOWED,引导用户手势操作恢复音视频播放
                that.localStream.resume()
              }
            })
        })

    },

    //发布本地音视频流
    publishStream() {
      const that = this
      if (!that.localStream.hasVideo() && !that.localStream.hasAudio()) {
        return
      }
      that.client
        .publish(that.localStream)
        .catch((error) => {
          console.error('本地流发布失败 ' + error)
          that.videoFlag = false
          that.micFlag = false
        })
        .then(() => {

          that.published = true
          console.log('本地流发布成功')
        })
    },

    //发送协议
    getprotocol() {
      this.protocol = true
    },
    //麦克风
    getmicFlag() {
      if (this.micFlag) {
        this.localStream.muteAudio()
        this.micFlag = false
      } else {
        this.localStream.unmuteAudio()
        this.micFlag = true
      }
    },
    //全体禁言
    // getallmicFlag() {
    //   this.$post("/meeting/muteAll", {
    //     caseId: this.$route.query.id,
    //     meetingId: this.meetingId,
    //     status: this.allmicFlag,
    //   }).then((res) => {
    //     if (res.code == 1000) {
    //       this.$message.success(res.msg);
    //       this.allmicFlag = !this.allmicFlag;
    //     }
    //   });
    // },
    //视频
    getvideoFlag() {
      if (this.videoFlag) {
        this.localStream.muteVideo()
        this.videoFlag = false
      } else {
        this.localStream.unmuteVideo()
        this.videoFlag = true
      }
    },
    //订阅远端流--加入房间之前
    // 订阅远端流--加入房间之前 这里为订阅远端流 每当有一个视频加入的时候 便会调用此方法 大家可以打印试试
    subscribeStream(client) {
      let that = this
      client.on('stream-added', (event) => {
        const remoteStream = event.stream
        const remoteUserId = remoteStream.getUserId()
        if (remoteUserId === this.userId) {
          // 取消订阅自己的屏幕分享流
          client.unsubscribe(remoteStream)
        } else {
          //大于指定数缓存
          let veclient = {}
          veclient.stream = remoteStream
          veclient.joinTime = Date.parse(new Date())
          if (that.remoteCount > 1) {
            veclient.playing = false
            that.remoteClientNotPlayArray.push(veclient)
          } else {
            veclient.playing = false
            that.remoteClientPlayingArray.push(veclient)
            that.remoteCount++
          }
          that.remoteClientMap.set(remoteStream.getId(), veclient)
        }
        that.page.pageNum = 1
        that.pagination()
      })
    },

    //播放远端流
    playStream(client) {
      let that = this
      client.on('stream-subscribed', (event) => {
        const id = event.stream.userId_
        this.meetingPersonList(id)
        client.on('stream-removed', (event) => {
          const remoteStream = event.stream
          let veclient = that.remoteClientMap.get(remoteStream.getId())
          if (veclient) {
            if (veclient.playing) {
              that.remoteCount--
              let remoteId = 'remote' + remoteStream.getId()
              $('#' + remoteId).remove()
              let removeIndex = -1
              for (let i = 0; i < that.remoteClientPlayingArray.length; i++) {
                if (that.remoteClientPlayingArray[i].stream.id_ == veclient.stream.id_) {
                  removeIndex = i
                }
              }
              if (removeIndex == -1) {
                return
              }
              that.remoteClientPlayingArray.splice(removeIndex, 1)
              if (that.remoteClientNotPlayArray.length > 0) {
                let newclient = that.remoteClientNotPlayArray[0]
                newclient.playing = false
                that.remoteClientPlayingArray.push(newclient)
                that.remoteClientNotPlayArray.splice(0, 1)
              }
              that.remoteClientMap.delete(remoteStream.getId())
              that.pagination()
            } else {
              if (that.remoteClientNotPlayArray.length > 0) {
                let removeNotIndex = -1
                for (let i = 0; i < that.remoteClientNotPlayArray.length; i++) {
                  if (that.remoteClientNotPlayArray[i].stream.id_ == veclient.stream.id_) {
                    removeNotIndex = i
                  }
                }
                if (removeNotIndex == -1) {
                  return
                }
                that.remoteClientNotPlayArray.splice(removeNotIndex, 1)
              }
            }
          }
          that.pagination()
        })
      })

      client.on('client-banned', (error) => {
        console.error('client-banned observed: ' + error)
        // 退出刷新页面
        console.log('退出刷新页面')
      })
    },
    refreshVideoDocument() {
      let that = this
      this.remoteClientNotPlayArray.forEach((velient) => {
        if (velient.playing) {
          velient.playing = false
          velient.stream.stop()
          that.client.unsubscribe(velient.stream)
        }
        that.remoteClientMap.set(velient.stream._id, velient)
        const remoteId = 'remote' + velient.stream.id_
        var parent = document.getElementById('remote-video-wrap')
        var child = document.getElementById(remoteId)
        if (child) {
          parent.removeChild(child)
        }
      })

      if (!this.remoteClientPlayingArray || this.remoteClientPlayingArray.length < 1) {
        this.$message.warning('没有可播放的视频')
      }
      for (let i = 0; i < this.remoteClientPlayingArray.length; i++) {
        let clint = this.remoteClientPlayingArray[i]
        const remoteId = 'remote' + clint.stream.id_
        if (clint.playing) {
          continue
        }
        // 订阅其他一般远端流
        this.client.subscribe(clint.stream)
        clint.playing = true
        let userInfo = that.userInfoMap.get(clint.stream.userId_)
        if (!userInfo) {
          userInfo = {}
          userInfo.nickName = clint.stream.userId_
          userInfo.studentCode = clint.stream.userId_
        }
        that.videoList.push({ id: remoteId, user: userInfo, like: false })
        this.getRoomInfo()
        console.log(that.videoList)
        that.remoteClientMap.set(clint.stream._id, clint)
        //做了dom操作 需要使用$nextTick(),否则找不到创建的标签无法进行播放
        this.$nextTick(() => {
          //播放
          clint.stream.play(remoteId, { objectFit: 'contain' })
        })
      }
    },
    refreshUserInformationPeriodically() {
      var that = this
      that.timer = setInterval(function() {
        if (!that.statusLoadDown) {
          return
        }
        try {
          that.statusLoadDown = false
          let refresh = false
          studyRoomApi.getUserStatus(that.roomId, that.userId).then((res) => {
            that.myStatus = res.data[0]
            if (that.myStatus && that.myStatus.call) {
              for (let i = 0; i < that.myStatus.call.length; i++) {
                if (that.myStatus.call[i].status == 1) {
                  if (that.nowCall && that.nowCall.userCode !== that.myStatus.call[i].userCode) {
                    console.log('被呼叫需要刷新')
                    refresh = true
                    that.callDialogShow = true // 打开呼叫弹窗
                  }
                  that.nowCall = that.myStatus.call[i]
                } else if (that.nowCall && that.nowCall.id == that.myStatus.call[i].id) {
                  that.nowCall = that.myStatus.call[i]
                }
              }
            } else {
              if (that.nowCall) {
                that.nowCall = { 'status': 0 }
              }
            }
            if (that.remoteClientPlayingArray.length < 1) {
              that.statusLoadDown = true
              return
            }
            let allArray = []
            allArray = allArray.concat(that.remoteClientNotPlayArray).concat(that.remoteClientPlayingArray)
            let str = ''
            for (let i = 0; i < allArray.length; i++) {
              str += allArray[i].stream.userId_ + ','
            }
            studyRoomApi.getUserStatus(that.roomId, str.substring(0, str.length - 1)).then((res) => {
              if (res.success && res.data.length > 0) {
                for (let i = 0; i < res.data.length; i++) {
                  var datum = res.data[i]
                  if (that.nowCall && that.nowCall.userCode && that.nowCall.userCode.length > 0 && that.nowCall.userCode === datum.userCode) {
                    that.forceFirstPlayUserCode = datum.userCode
                  }
                  that.statusInfoMap.set(datum.userCode, datum)
                }
              }
              that.statusLoadDown = true
              if (refresh) {
                that.pagination()
              }
            })
          })
        } catch (e) {
          that.statusLoadDown = true
          console.log(e)
        }
      }, 5000)
    },
    //
    meetingPersonList(id) {
      for (let i = 0; i < this.meetingPerson.length; i++) {
        if (this.meetingPerson[i].userId == id) {
          this.userType = this.meetingPerson[i].userTypeDesc
          this.userName = this.meetingPerson[i].userName
        }
      }
    },
    //退出音视频
    leaveRoom() {
      const that = this
      this.client
        .leave()
        .then(() => {
          this.$message.success('退房成功')
          // 停止本地流，关闭本地流内部的音视频播放器
          this.localStream.stop()
          // 关闭本地流，释放摄像头和麦克风访问权限
          this.localStream.close()
          this.localStream = null
          this.client = null
          // 退房成功，可再次调用client.join重新进房开启新的通话。
          this.deleteMember()
        })
        .catch((error) => {
          console.error('退房失败 ' + error)
          // 错误不可恢复，需要刷新页面。
        })
      let promise = this.tim.logout()
      promise.then(function(imResponse) {
        console.log(imResponse.data) // 登出成功
        that.$message.success('退出聊天室成功')
      }).catch(function(imError) {
        console.warn('logout error:', imError)
      })
      let param = { studyRoomId: that.roomId }
      studyRoomApi.leaveRoom(param).then(res => {
        if (!res.success){
          console.warn('logout error:', res)
        }
      })
      this.$store.dispatch('tagsView/delView', this.$route)
      this.$router.push('/schedule/roomList')
    },

    // 加群
    importMember() {
      let data = {}
      let list = []
      list.push({ memberAccount: this.userId, role: 'Admin', unreadMsgNum: null })
      data.groupId = this.groupId
      data.memberList = list
      studyRoomApi.importMember(data).then(async(res) => {
        if (res.code == 20000) {
          this.$message.success('加入群聊成功!')
        }
      })
    },

    deleteMember() {
      let data = {}
      let accountList = []
      accountList.push(this.userId)
      data.groupId = this.groupId
      data.reason = '退出登录'
      data.silence = null
      data.accountList = accountList
      studyRoomApi.deleteMember(data).then(async(res) => {
        if (res.code == 20000) {
          this.$message.success('退出群聊成功!')
        }
      })
    },

    getStatus(key, type) {
      if (key && this.statusInfoMap !== null && this.statusInfoMap.size !== 0) {
        return this.statusInfoMap.get(key)[type]
      }
    },
    getStatusCode(key, type) {
      if (key && this.statusInfoMap !== null && this.statusInfoMap.size !== 0) {
        return this.statusInfoMap.get(key)[type] ? 1 : 2  // 0学委未操作 1学委强开 2学委强关
      }
    },

    //结束调解
    leaveRooms() {
      this.$post('/meeting/endMeeting/' + this.meetingId).then((res) => {
        if (res.code == 1000) {
          this.$message.success('会议已结束')
          this.client
            .leave()
            .then(() => {
              setTimeout((res) => {
                this.$router.replace({
                  path: '/mediatorDetails/' + this.$route.query.id
                })
              }, 500)
              // 停止本地流，关闭本地流内部的音视频播放器
              this.localStream.stop()
              // 关闭本地流，释放摄像头和麦克风访问权限
              this.localStream.close()
              this.localStream = null
              this.client = null
              // 退房成功，可再次调用client.join重新进房开启新的通话。
            })
            .catch((error) => {
              console.error('退房失败 ' + error)
              // 错误不可恢复，需要刷新页面。
            })
        }
      })
    },

    bindEvents() {//绑定tim的监听事件
      let self = this
      this.tim.on(TIM.EVENT.CONVERSATION_LIST_UPDATED, this.conversationListUpdated)//会话列表更新
      this.tim.on(TIM.EVENT.MESSAGE_RECEIVED, this.messageReceived)//收到新消息
      this.tim.on(TIM.EVENT.SDK_READY, function(event) {
        self.updateMyProfile()
        self.getMessageList()
      })
    },

    conversationListUpdated(event) {
      console.log('===会话列表更新===')
      if (!this.conversationID) {//如果还没拿到会话ID
        let arr = event.data.filter(item => item.type == 'GROUP')
        if (arr.length > 0) {
          this.conversationID = arr[0].conversationID
        }
      } else {
        if (!this.haveCurrentConversation) {//如果还未获取到会话资料 则获取一下
          this.tim.getConversationProfile(this.conversationID).then(({ data }) => {
            console.log('===获取会话资料成功===')
            this.haveCurrentConversation = true
            // 3.2 获取消息列表
            this.getMessageList()
          })
        }
      }
    },

    messageReceived({ data: messageList }) {
      this.pushCurrentMessageList(messageList, 'recive')//向消息列表添加消息
    },

    pushCurrentMessageList(data, type) {
      //向消息列表添加数据
      if (type == 'send') {
        this.currentMessageList = [...this.currentMessageList, data]
      } else {
        this.currentMessageList = [...this.currentMessageList, ...data]
      }
    },

    // 发送消息
    sendText() {
      let that = this
      if (!(that.inputText.trim())) {
        that.$message.warning('请先输入内容')
        that.inputText = ''//重置文本消息框
        return
      }
      let message = that.tim.createTextMessage({
        to: this.groupId.toString(),
        conversationType: TIM.TYPES.CONV_GROUP,
        payload: {
          text: that.inputText.trim()
        }
      })
      this.pushCurrentMessageList(message, 'send')
      this.scrollMessageListToButtom()
      // 2. 发送消息
      let promise = that.tim.sendMessage(message)
      that.inputText = ''
      promise.then(function(imResponse) {
        // 发送成功
        console.log(imResponse)
      }).catch(function(imError) {
        // 发送失败
        console.warn('sendMessage error:', imError)
      })
    },

    getMessageList() {
      let that = this
      //获取消息列表信息
      return new Promise(resolve => {
        that.tim.getMessageList({
          // conversationID: this.groupId.toString(),
          conversationID: this.conversationID,
          nextReqMessageID: this.nextReqMessageID,
          count: 15
        }).then(imReponse => {
          // 更新messageID，续拉时要用到
          that.nextReqMessageID = imReponse.data.nextReqMessageID
          that.isCompleted = imReponse.data.isCompleted
          // 更新当前消息列表，从头部插入
          that.currentMessageList = [...imReponse.data.messageList, ...that.currentMessageList]
          console.log('===获取会话列表成功===')
          this.scrollMessageListToButtom()
          resolve()
        })
      })
    },

    // 直接滚到底部
    scrollMessageListToButtom() {
      this.$nextTick(() => {
        let messageListNode = this.$refs['message_scroll']
        if (!messageListNode) {
          return
        }
        messageListNode.scrollTop = messageListNode.scrollHeight
      })
    },

    // 选择表情
    chooseEmoji(path) {
      this.inputText = this.inputText + path
    },

    // 切换分屏
    changeScreen() {
      this.screenChoose = this.screenChoose == 12 ? 4 : this.screenChoose == 4 ? 9 : 12
      if (this.screenChoose == 12) {
        this.videoSize.videoWidth = '180px'
        this.videoSize.videoHeight = '155px'
      } else if (this.screenChoose == 9) {
        this.videoSize.videoWidth = '245px'
        this.videoSize.videoHeight = '155px'
      } else {
        this.videoSize.videoWidth = '360px'
        this.videoSize.videoHeight = '236px'
      }

    },
    pagination() {
      let that = this
      that.page.total = that.remoteClientPlayingArray.length + that.remoteClientNotPlayArray.length
      that.page.pageTotal = Math.floor(that.page.total / that.page.pageSize)
      if (that.page.total % that.page.pageSize !== 0) {
        that.page.pageTotal++
      }
      if (that.page.pageNum > that.page.pageTotal) {
        that.page.pageNum = that.page.pageTotal
      }
      //todo  上线删除
      that.userInfoMap = new Map()
      let pageArray = []
      pageArray = pageArray.concat(that.remoteClientNotPlayArray).concat(that.remoteClientPlayingArray)
      pageArray.sort((a, b) => {
        return a.joinTime - b.joinTime
      })
      let startNum = (that.page.pageNum - 1) * that.page.pageSize
      let endNum = startNum + that.page.pageSize
      endNum = endNum > pageArray.length ? pageArray.length : endNum
      that.remoteClientPlayingArray = []
      that.remoteClientNotPlayArray = []
      let noUserInfoCodes = ''
      for (let i = 0; i < pageArray.length; i++) {
        if (i >= startNum && i < endNum) {
          let userInfo = that.userInfoMap.get(pageArray[i].stream.userId_)
          if (!userInfo) {
            noUserInfoCodes += pageArray[i].stream.userId_ + ','
          }
          that.remoteClientPlayingArray.push(pageArray[i])
        } else {
          if (that.forceFirstPlayUserCode === pageArray[i].stream.userId_) {
            if (that.remoteClientPlayingArray.length > 0) {
              that.remoteClientNotPlayArray.push(that.remoteClientPlayingArray[0])
              that.remoteClientPlayingArray[0] = pageArray[i]
            } else {
              that.remoteClientPlayingArray.push(pageArray[i])
              endNum--
            }
          }
          that.remoteClientNotPlayArray.push(pageArray[i])
        }
      }
      if (noUserInfoCodes.length > 1) {
        studyRoomApi.getuserInfo(noUserInfoCodes.substring(0, noUserInfoCodes.length - 1)).then((res) => {
          if (!res.success) {
            that.$message.error('获取自习室用户信息失败：' + res.message)
          }
          if (res.data.length > 0) {
            for (let i = 0; i < res.data.length; i++) {
              that.userInfoMap.set(res.data[i].studentCode, res.data[i])
            }
          }
          that.refreshVideoDocument()
        })
      } else {
        that.refreshVideoDocument()
      }

    },
    //分页
    sizeChange(val) {
      if (!val || this.page.pageSize == val) {
        return
      }
      this.page.pageSize = val
      this.pagination()
    },

    currentChange(val) {
      if (!val || this.page.pageNum == val) {
        return
      }
      this.page.pageNum = val
      this.pagination()
    },

    // 修改学员底部状态
    changeOtherMode(mode, code, index) {
      let that = this
      if (mode == 'chat') { // 聊天
        this.banChat(code, this.getStatusCode(code, 'disableMessage'))
      } else if (mode == 'mico') { // 语音
        this.banVoice(code, this.getStatusCode(code, 'disableMic'))
      } else if (mode == 'chatLine') { // 连麦
        this.activeCall = true // 我主动呼叫
        let dateNow = new Date()
        let callId = dateNow.getTime()
        studyRoomApi.roomCall(callId, code, this.roomId).then((res) => {
          if (res.success) {
            this.callDialogShow = true
          }
        })
      } else if (mode == 'video') { // 视频
        this.banVideo(code, this.getStatusCode(code, 'disableCamera'))
      } else if (mode == 'like') { // 点赞
        if (!this.videoList[index].like) {
          studyRoomApi.giveLike(this.userId, this.roomId, code).then((res) => {
            if (res.success) {
              this.$message.success('点赞成功~')
              this.inputText = `点##赞${this.videoList[index].user.nickName}`
              this.sendText()
            }
          })
          this.videoList[index].like = true
        }
      } else if (mode == 'out') { // 踢出
        studyRoomApi.kickOut(this.roomId, this.userId, code).then((res) => {
          if (res.success) {
            this.$message.success('学员踢出房间成功')
          }
        })
      } else if (mode == 'refresh') {
        this.banChat(code, 0)
        this.banVoice(code, 0)
        this.banVideo(code, 0)
      }
      studyRoomApi.getUserStatus(that.roomId, code).then((res) => {
        if (res.success && res.data.length > 0) {
          for (let i = 0; i < res.data.length; i++) {
            var datum = res.data[i]
            if (that.nowCall && that.nowCall.userCode && that.nowCall.userCode.length > 0 && that.nowCall.userCode === datum.userCode) {
              that.forceFirstPlayUserCode = datum.userCode
            }
            that.statusInfoMap.set(datum.userCode, datum)
            // list1.map(item => that.statusInfoMap.find(s => s.c === item.userCode.b)).filter(item => item)
          }
        }
        that.statusLoadDown = true
        that.pagination()
      })

    },

    // 禁用聊天
    banChat(code, status) {
      studyRoomApi.MessageAble(this.roomId, code, status).then((res) => {
        if (res.success) {
          this.$message.success('修改学员状态成功')
        }
      })
    },

    // 禁用语音
    banVoice(code, status) {
      studyRoomApi.micAble(this.roomId, code, status).then((res) => {
        if (res.success) {
          this.$message.success('修改学员状态成功')
        }
      })
    },

    // 禁言视频
    banVideo(code, status) {
      studyRoomApi.videoAble(this.roomId, code, status).then((res) => {
        if (res.success) {
          this.$message.success('修改学员状态成功')
        }
      })
    },

    // 关闭弹窗
    handleClose() {
      this.hndleDialogVisible = false
      this.initRoom()
      // this.getMessageList();
    },

    updateMyProfile() {
      // 修改个人标配资料
      let promise = this.tim.updateMyProfile({
        nick: '学委',
        avatar: 'http://dx-applet-mall.oss-cn-shanghai.aliyuncs.com/autoStudy/avatar/defaultAvatar.png'
      })
      promise.then(function(imResponse) {
        console.log(imResponse.data) // 更新资料成功
        console.log('更新资料成功----') //
      }).catch(function(imError) {
        console.warn('updateMyProfile error:', imError) // 更新资料失败的相关信息
      })
    },

    // 修改房间弹窗
    editClassRoomName(type) {
      this.editType = type
      this.editTitle = type == 'name' ? '修改自习室名称' : '修改公告'
      this.editInputCon = type == 'name' ? this.roomName : this.publicNotice
      this.editDialogVisible = true
    },

    // 修改房间
    editRoomName() {
      studyRoomApi.editName(
        this.editType == 'public' ? this.editInputCon : '',
        this.editType == 'name' ? this.editInputCon : '',
        this.roomId
      ).then(res => {
        if (res.success) {
          this.$message.success(`修改自习室${this.editType == 'public' ? '公告' : '名称'}成功`)
          this.editDialogVisible = false
          this.getRoomInfo()
        }
      })
    },

    allMessageAble() {
      // 0关闭状态==(打开) 1开启状态
      studyRoomApi.allMicAble(this.roomId, this.roomInfo.chatStatus == 0 ? true : false).then(res => {
        if (res.success) {
          this.$message.success(`${this.roomInfo.chatStatus == 0 ? '打开' : '关闭'}全体评论功能成功`)
          this.getRoomInfo()
        }
      })

    },
    allPKAble() {
      // 0关闭状态==(打开) 1开启状态
      studyRoomApi.allPKAble(this.roomId).then(res => {
        if (res.success) {
          this.$message.success(`${this.roomInfo.chatStatus == 0 ? '打开' : '关闭'}全体PK功能成功`)
          this.getRoomInfo()
        }
      })
    },

    // 关闭房间
    closeRoom() {
      studyRoomApi.dissolveRoom(this.roomId).then(res => {
        if (res.success) {
          this.$message.success('您已经成功解散房间')
        }
      })
    }
  }
}
</script>
<style lang="less" scoped>
@imgurl: "../../assets/images/studyRoom/";
.app-main > div {
  margin: 0;
  min-height: 90%;
}

.center-page {
  padding-top: 30px;
  background: url('@{imgurl}bg.png') no-repeat;
  background-size: cover;

  .header {
    display: flex;
    height: 40px;
    width: 100%;
    padding: 0 40px;
    justify-content: space-between;
    font-size: 18px;
    color: #fff;

    .header_left {
      display: flex;
      flex-direction: column;

      .left_bottom {
        margin-top: 8px;
        display: flex;

        .item {
          display: flex;
          margin-right: 16px;
          font-size: 14px;

          > img {
            width: 20px;
          }
        }

        .person {
          color: rgb(0, 255, 145);
        }
      }
    }

    .header_item {
      display: flex;
      align-items: center;
      cursor: pointer;

      .item {
        font-size: 14px;
      }

      .quit_box {
        display: flex;
        margin-left: 16px;
        vertical-align: text-top;

        > div {
          margin-left: 6px;
        }
      }

      .share {
        background-color: #409EFF;
      }

      .quit {
        background-color: #E94E70;
      }

      .edit {
        margin-left: 8px;
        padding-left: 2px;
        border-left: 1px solid #fff;
        font-size: 12px;
      }

      .quit,
      .share {
        width: 22px;
        height: 22px;
        border-radius: 50%;
        text-align: center;
        color: #006995;

        .el-icon-switch-button {
          font-size: 16px;
          font-weight: 600;
        }
      }
    }
  }

  .content {
    display: flex;
  }

}

.video-box {
  width: 80%;
  height: 540px;

  .footer {
    .handle {
      float: left;
      width: 280px;
      display: flex;
      justify-content: space-evenly;

      .handle_item {
        height: 30px;
        line-height: 30px;
        background-color: rgb(23, 179, 141);
        border-radius: 15px;
        font-size: 14px;
        padding: 0 8px;
        color: #fff;
        cursor: pointer;
      }

      .handle_item:hover {
        background-color: rgba(23, 179, 141, .6),;
      }
    }
  }
}

/deep/ .el-pagination {
  float: right;
}

//本地流
.on-box {
  padding: 16px;
  width: 100%;
  height: 570px;
  overflow-y: auto;
  position: relative;
}

.on-box::-webkit-scrollbar {
  width: 0 !important;
}

.message_scroll::-webkit-scrollbar {
  width: 0 !important;
}

.emoji_scroll::-webkit-scrollbar {
  width: 0 !important;
}

.local-stream {
  width: 100%;
  height: 246px;
}

.btn-chat {
  box-sizing: border-box;
  margin-top: 10px;
  width: 100%;
  height: 80px;
  display: inherit;
  align-items: center;
  justify-content: space-between;
  padding: 0 30px;
  cursor: pointer;

  > img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
  }

  p {
    margin-top: 14px;
    font-size: 13px;
    color: #ffffff;
  }
}

.l-box {
  display: flex;
  align-items: center;

  > div {
    text-align: center;
    margin-right: 57px;
  }
}

.close-btn {
  display: flex;
  align-content: center;
}

.item-close {
  text-align: center;
  margin-left: 60px;
}

//远端流
.mine-box {
  width: 406px;
  height: 740px;
  padding: 16px 16px 16px 0;
}

.remote-video-wrap {
  padding: 0 16px;
  display: flex;
  flex-wrap: wrap;

  .remoteVidwo {
    width: 100%;
  }

  .remoteVidwo:hover {
    border: 2px solid #f7faff;
  }
}

.video_box {
  width: 280px;
  height: 233px;
  margin: 10px;
  border-radius: 15px;
  position: relative;
  overflow: hidden;

  .name {
    position: absolute;
    top: 0;
    right: 0;
    width: 76px;
    height: 34px;
    line-height: 34px;
    text-align: center;
    background: rgba(#404040, 0.4);
    z-index: 99;
  }

  .video_bottom {
    position: absolute;
    bottom: 0;
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    background-color: rgba(#404040, 0.35);
    height: 43px;
    width: 100%;
    z-index: 99;
  }
}

.message_scroll {
  overflow-y: auto;
  // overflow: hidden;
  height: 250px;
}

.message_list {
  // overflow: auto;
  .message_item {
    width: fit-content;
    background-color: #202125;
    border-radius: 15px;
    color: #fff;
    margin-bottom: 10px;
    word-wrap: break-word;
    padding: 10px;
    font-size: 12px;
  }
}

.send_message_box {
  display: flex;
  height: 36px;

  .input_box {
    overflow: hidden;
    background-color: #202125;
    border-radius: 15px;
    display: flex;
    height: 30px;
    width: 290px;

    /deep/ .el-input--medium .el-input__inner {
      height: 28px;
      background-color: #202125;
      border: none !important;
      color: #999 !important;
    }

    > div {
      width: 30px;
      height: 30px;
      text-align: center;
      line-height: 30px;
    }
  }

  .look_icon {
    width: 30px;
    height: 36px;
    background-color: pink;
  }
}

.emoji_scroll {
  height: 130px;
  overflow-y: auto;
  background-color: #fff;
}

.emoji_list {
  display: flex;
  flex-wrap: wrap;
  padding: 4px;

  .emoji_item {
    padding: 4px;
    width: 25px;
    height: 25px;

    > img {
      width: 100%;
      height: 100%;
    }
  }
}

.public {
  background-color: #202125;
  border-radius: 15px;
  color: #fff;
  margin-bottom: 10px;
  font-size: 12px;
  padding: 6px;

  .left {
    float: left;
    width: 55px;

    .icon {
      width: 18px;
      display: inline-block;
      vertical-align: text-top;
    }
  }

  .center {
    cursor: pointer;
  }

}

.dialog_bg {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-evenly;

  .handle_item {
    font-size: 16px;
    color: #fff;
    margin-top: 20px;
    display: flex;

    img {
      margin-right: 4px;
    }
  }
}

.shape_dialog {
  /deep/ .el-dialog {
    height: 300px;
    background: url('@{imgurl}dialogBg.png') no-repeat;
    background-size: contain;
    background-color: transparent;
    padding: 0 30px;
  }

  /deep/ .el-dialog--center .el-dialog__body {
    padding: 25px 25px 15px;
  }

  .dialog-footer /deep/ .el-button {
    background: url('@{imgurl}dialogBtn.png') no-repeat;
    background-size: 150px;
    border: none;
    width: 150px;
    height: 48px;
    color: #fff;
  }
}


/deep/ video {
  width: 100% !important;
  height: auto !important;
}
</style>
