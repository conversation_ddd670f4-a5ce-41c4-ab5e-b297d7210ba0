<template>
  <div class="app-container">
    <el-form :inline="true" ref="queryForm" class="container-card" label-width="110px">
      <el-form-item label="内容：">
        <el-select v-model="dataQuery.type" placeholder="请选择">
          <el-option label="背景图" :value="1" />
          <el-option label="文本" :value="2" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-re" @click="resetQuery()">重置</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="search()">搜索</el-button>
      </el-form-item>
    </el-form>
    <el-col :span="24" style="margin-bottom: 30px">
      <el-button type="success" icon="el-icon-plus" size="mini" @click="addBtn()">新增</el-button>
    </el-col>
    <el-table class="common-table" v-loading="tableLoading" :data="tableData" style="width: 100%;margin-bottom: 20px;"
      row-key="id" border default-expand-all>
      <el-table-column prop="orderNum" type="index" label="序号" width="50"></el-table-column>
      <el-table-column prop="content" label="内容">
        <template slot-scope="scope">
          <div class="post_title">
            <div class="post_head" v-if="scope.row.type != 1">
              <span class="post_h1">{{ scope.row.content }}</span>
            </div>
            <el-image v-if="scope.row.type == 1" style="width: 60px; height: 50px" :src="scope.row.content"
              :preview-src-list="[scope.row.content]">
            </el-image>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="withdrawnBonus" label="操作" width="210">
        <template slot-scope="scope">
          <el-dropdown>
            <span class="el-dropdown-link" style="color:#108EE9">
              <el-button type="text">
                更多
                <i class="el-icon-arrow-down" />
              </el-button>
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item @click.native="editBtn(scope.row.id)">编辑</el-dropdown-item>
              <el-dropdown-item @click.native="singleDelete(scope.row.id)">删除</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
      <el-table-column prop="preview" label="预览图" v-if="tabPosition == 1">
        <template slot-scope="scope">
          <el-image style="width: 60px; height: 50px" :src="scope.row.preview" :preview-src-list="[scope.row.preview]"
            v-if="scope.row.preview != ''">
          </el-image>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <el-col :span="24" style="margin-bottom: 20px">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>

    <!-- 查看弹窗 -->
    <el-dialog :title="banner_titile" :visible.sync="open" width="50%" @close="close">
      <el-form ref="form" :model="form" label-width="120px">
        <el-form-item label="序号：" prop="orderNum" style="width:348px">
          <el-input v-model="form.orderNum" placeholder="请填写序号" type="number" />
        </el-form-item>

        <el-form-item label="打开方式：" prop="awardRulesFre">
          <el-radio-group v-model="tabPosition" @change="radio_change">
            <el-radio label="1">背景图</el-radio>
            <el-radio label="2">文本</el-radio>
          </el-radio-group>
        </el-form-item>


        <el-form-item label="内容图：" prop="gradingSmallPhoto" v-if="tabPosition == 1">
          <my-upload @handleSuccess="handleSuccess1" @handleRemove="handleRemove1" :file-list="fileList1" :showTip="false"
            :limit="1" :fullUrl="true" />
          <el-input type="hidden" v-model="form.content"></el-input>
        </el-form-item>
        <el-form-item label="预览图：" prop="gradingBigPhoto" v-if="tabPosition == 1">
          <my-upload @handleSuccess="handleSuccess2" @handleRemove="handleRemove2" :file-list="fileList2" :showTip="false"
            :limit="1" :fullUrl="true" />
          <el-input type="hidden" v-model="form.preview"></el-input>
        </el-form-item>

        <el-form-item label="内容：" prop="content" v-if="tabPosition == 2">
          <el-input v-model="form.content" />
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import posterbackgroundApi from "@/api/xi/community/posterbackground";
import MyUpload from "@/components/Upload/MyUpload";
import { ossPrClient } from '@/api/alibaba'
import { pageParamNames } from "@/utils/constants";

export default {
  name: "posterbackground",
  components: { MyUpload },
  data() {
    return {
      tabPosition: "1",
      fileList1: [],
      fileList2: [],
      flow: {
        tableLoading: false,
        tableData: [],
        dataQuery: {
          studentCode: "",
          pageNum: "",
          pageSize: ""
        },
        // 分页
        tablePage: {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null
        }
      },
      exchange: {
        tableLoading: false,
        tableData: [],
        dataQuery: {
          studentCode: "",
          pageNum: "",
          pageSize: ""
        },
        // 分页
        tablePage: {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null
        }
      },
      dataQuery: {
        name: "",
        type: 1
      },
      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      open: false,
      tableData: [],
      form: {
        orderNum: ''
      },
      banner_titile: ''
    };
  },
  created() {
    this.getPageList();
  },
  computed: {
    // 动态显示MB或者KB
    isKbOrMb() {
      return this.imgSize / 1024 / 1024 >= 1
        ? `${Number(this.imgSize / 1024 / 1024).toFixed(0)}MB`
        : `${Number(this.imgSize / 1024).toFixed(0)}KB`;
    },
  },
  beforeDestroy() {
    const editor = this.editor
    if (editor == null) return
    editor.destroy() // 组件销毁时，及时销毁编辑器
  },
  methods: {
    // 提交
    submitForm() {
      //  if(this.content){
      //     this.form.content=this.content
      //   }
      this.form.type = Number(this.tabPosition)
      posterbackgroundApi.saveOrUpdate(this.form).then(response => {
        this.$message.success('提交成功！')
        this.open = false
        this.getPageList()
        this.tabPosition = Number(this.form.type)
        this.$refs.upload.clearFiles()
      })
    },
    handleSuccess1(url) {
      this.form.content = url;
    },
    handleRemove1() {
      this.form.content = '';
    },
    handleSuccess2(url) {
      this.form.preview = url;
    },
    handleRemove2() {
      this.form.preview = '';
    },
    // 图片删除
    handleRemove(file) {
      console.log("前===", this.fileList)
      var index = this.fileList.findIndex(item => {
        if (item.uid === file.uid) {
          return true;
        }
      });
      this.fileList.splice(index, 1);
      console.log("后===", this.fileList)
      this.$emit("handleRemove", file);
    },
    // 图片删除
    handleRemove1(file) {
      var index = this.fileList.findIndex(item => {
        if (item.uid === file.uid) {
          return true;
        }
      });
      this.fileList1.splice(index, 1);
      this.$emit("handleRemove1", file);
    },
    handleExceed(files, fileList) {
      this.$message.warning(`只可选择一张图片`);
    },
    // 图片上传成功
    handleSuccess(res) {
      this.$emit("handleSuccess", res || "");
    },
    uploadHttp({ file }) {
      this.loading = true;
      let suf = file.name.substring(file.name.lastIndexOf("."));
      const fileName = 'manage/' + Date.parse(new Date()) + suf;
      ossPrClient().put(fileName, file).then(({ res, url, name }) => {
        if (res && res.status === 200) {
          console.log(`阿里云OSS上传图片成功回调1`, res, url, name);
          this.fileList1 = url
          if (this.fullUrl) {
            this.handleSuccess(this.aliUrl + name);
          } else {
            this.handleSuccess(name);
          }
          this.loading = false;
        }
      })
        .catch((err) => {
          this.$message.error('上传图片失败请检查网络或者刷新页面')
          console.log(`阿里云OSS上传图片失败回调`, err)
        })
    },
    uploadHttp01({ file }) {
      this.loading = true;
      let suf = file.name.substring(file.name.lastIndexOf("."));
      const fileName = 'manage/' + Date.parse(new Date()) + suf;
      ossPrClient().put(fileName, file).then(({ res, url, name }) => {
        if (res && res.status === 200) {
          console.log(`阿里云OSS上传图片成功回调1`, res, url, name);
          this.fileList = url
          if (this.fullUrl) {
            this.handleSuccess(this.aliUrl + name);
          } else {
            this.handleSuccess(name);
          }
          this.loading = false;
        }
      })
        .catch((err) => {
          this.$message.error('上传图片失败请检查网络或者刷新页面')
          console.log(`阿里云OSS上传图片失败回调`, err)
        })
    },
    radio_change(value) {
      if (value == 1) {
        this.form.type = 1
      } else {
        this.form.type = 2
      }
    },
    search() {
      this.tablePage = {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      };
      this.tabPosition = this.dataQuery.type
      this.getPageList();
    },
    // 单个删除
    singleDelete(id) {
      const that = this;
      this.$confirm("确定操作吗?", "删除状态", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          posterbackgroundApi
            .delete(id)
            .then(res => {
              that.$nextTick(() => that.fetchData());
              that.$message.success("删除成功!");
              this.getPageList();
            })
            .catch(err => { });
        })
        .catch(err => { });
    },
    addBtn() {
      this.reset();
      this.open = true;
      this.fileList2 = []
      this.fileList1 = []
      this.tabPosition = this.dataQuery.type.toString()
      this.banner_titile = "新增"
    },
    // 修改
    editBtn(id) {
      this.open = true;
      this.fileList2 = []
      this.fileList1 = []
      this.banner_titile = "修改"
      posterbackgroundApi.detail(id).then(res => {
        this.form = res.data;
        this.tabPosition = res.data.type.toString()
        if (res.data.content) {
          this.fileList1.push({ url: res.data.content })
        }
        if (res.data.preview) {
          this.fileList2.push({ url: res.data.preview })
        }
        if (res.data.type == 1) {
          this.form.type = 1
        } else {
          this.form.type = 2
        }
      })
    },
    // 列表数据
    getPageList() {
      this.tableLoading = true;
      this.dataQuery.pageNum = this.tablePage.currentPage;
      this.dataQuery.pageSize = this.tablePage.size;
      posterbackgroundApi.list(this.dataQuery).then(res => {
        this.tableData = res.data.data;
        this.tableLoading = false;
        // 设置后台返回的分页参数
        pageParamNames.forEach(name =>
          this.$set(this.tablePage, name, parseInt(res.data[name]))
        );
      });
    },
    handlePage() {
      (this.tablePage = {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      }),
        this.getPageList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dataQuery = {
        type: 1
      };
      this.getPageList();
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val;
      this.getPageList();
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val;
      this.getPageList();
    },
    close() {
      this.open = false;
      this.reset();
    },
    reset() {
      this.form = {
        id: null,
        num: undefined,
        content: null,
        preview: null
      };
      this.tabPosition = this.dataQuery.type.toString()
      if (this.$refs["form"]) {
        this.$refs["form"].resetFields();
      }
    }
  }
};
</script>

<style scoped>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.period-table td,
.period-table th {
  text-align: center;
}

@media (max-width: 767px) {
  .el-message-box {
    width: 80% !important;
  }
}

.el-tooltip__popper {
  max-width: 800px;
}

.el-upload-list--picture-card {
  display: block;
}

.el-upload {
  border: none;
  width: auto;
  height: 36px;
  line-height: 36px;
}

.el-upload button {
  height: 36px;
}

.el-tooltip__popper {
  max-width: 800px;
}

.el-upload-list {
  position: relative;
}
</style>
