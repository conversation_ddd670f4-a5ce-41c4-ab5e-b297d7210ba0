import request from '@/utils/request'

export default {
  //分页
  pageList(data) {
    return request({
      url: '/activiti/processDefinition/list',
      method: 'GET',
      params: data
    })
  },
  deleteRow(id){
    return request({
      url: '/activiti/processDefinition/delete',
      method: 'DELETE',
      params: {
        deploymentId: id
      }
    })
  },

  appSourceList(){
    return request({
      url: '/activiti/processDefinition/appSource',
      method: 'GET',
    })
  },
}
