<template>
  <div class="app-container">
    <el-form :model="form" ref="form" label-width="100px" v-loading="formLoading" :rules="rules">
      <el-form-item label="学段：" prop="gradeId" required>
        <el-select v-model="form.gradeId" placeholder="选择学段" @change="levelChange" clearable>
          <el-option v-for="item in gradeList" :key="item.id" :value="item.id" :label="item.name"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="维度：" prop="subjectId" required>
        <el-select v-model="form.subjectId" placeholder="选择维度" @change="getKonwTree">
          <el-option v-for="item in subjectFilter" :key="item.id" :value="item.id" :label="item.name"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="试卷类型：" prop="paperType" required>
        <el-select v-model="form.paperType" placeholder="试卷类型" disabled>
          <el-option v-for="item in paperTypeList" :key="item.value" :value="item.value" :label="item.label"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="时间限制：" required v-if="form.paperType === 2" prop="limitDateTime">
        <el-date-picker v-model="form.limitDateTime" value-format="yyyy-MM-dd HH:mm:ss" type="datetimerange"
          range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" readonly>
        </el-date-picker>
      </el-form-item>
      <el-form-item label="试卷名称：" prop="name" required>
        <el-input v-model="form.name" readonly />
      </el-form-item>
      <el-form-item :key="index" :label="'标题' + (index + 1) + '：'" required v-for="(titleItem, index) in form.titleItems">
        <el-input v-model="titleItem.name" style="width: 80%" readonly />
        <el-button type="text" class="link-left" size="mini" @click="form.titleItems.splice(index, 1)">删除</el-button>
        <el-card class="exampaper-item-box" v-if="titleItem.questionItems.length !== 0">
          <el-form-item :key="questionIndex" :label="'题目' + (questionIndex + 1) + '：'"
            v-for="(questionItem, questionIndex) in titleItem.questionItems" style="margin-bottom: 15px">
            <el-row>
              <el-col :span="23">
                <QuestionShow :qType="questionItem.questionType" :question="questionItem" />
              </el-col>
              <el-col :span="1">
                <el-button type="text" size="mini" @click="titleItem.questionItems.splice(questionIndex, 1)">删除
                </el-button>
              </el-col>
            </el-row>
          </el-form-item>
        </el-card>
      </el-form-item>
      <el-form-item label="建议学时：" prop="suggestTime" required>
        <el-input v-model="form.suggestTime" placeholder="分钟" readonly />
      </el-form-item>
      <el-form-item label="及格分数：" prop="passScore" required>
        <el-input-number v-model="form.passScore" :precision="0" :step="1" disabled />
      </el-form-item>
      <el-form-item label="良好分数：" prop="goodScore" required>
        <el-input-number v-model="form.goodScore" :precision="0" :step="1" disabled />
      </el-form-item>
      <el-form-item label="优秀分数：" prop="excellentScore" required>
        <el-input-number v-model="form.excellentScore" :precision="0" :step="1" disabled />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="close">关闭</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>

import { mapGetters, mapState } from 'vuex'
import Pagination from '@/components/Pagination'
import QuestionShow from '../question/components/Show'
import subjectApi from '@/api/paper/subject'
import paperApi from '@/api/paper/paper'

export default {
  components: { Pagination, QuestionShow },
  data() {
    return {
      gradeList: [],
      paperTypeList: [{ label: '固定试卷', value: 1 }, { label: '时段试卷', value: 2 }, { label: '任务试卷', value: 3 }],
      form: {
        id: null,
        gradeId: null,
        subjectId: null,
        paperType: null,
        limitDateTime: [],
        name: '',
        suggestTime: null,
        titleItems: [],
        typeInfo: null,
        passScore: undefined,
        goodScore: undefined,
        excellentScore: undefined
      },
      subjectList: [],
      subjectFilter: null,
      formLoading: false,
      rules: {
        gradeId: [
          { required: true, message: '请选择学段', trigger: 'change' }
        ],
        subjectId: [
          { required: true, message: '请选择维度', trigger: 'change' }
        ],
        paperType: [
          { required: true, message: '请选择试卷类型', trigger: 'change' }
        ],
        name: [
          { required: true, message: '请输入试卷名称', trigger: 'blur' }
        ],
        suggestTime: [
          { required: true, message: '请输入建议学时', trigger: 'blur' }
        ],
        passScore: [
          { required: true, message: '请输入及格分数线', trigger: 'blur' }
        ],
        goodScore: [
          { required: true, message: '请输入良好分数线', trigger: 'blur' }
        ],
        excellentScore: [
          { required: true, message: '请输入优秀分数线', trigger: 'blur' }
        ],
        limitDateTime: [
          { required: true, message: '请选择时间', trigger: 'blur' }
        ]
      },
      questionPage: {
        multipleSelection: [],
        showDialog: false,
        dataQuery: {
          id: null,
          questionType: null,
          level: null,
          subjectId: null,
        },
        // 分页
        tablePage: {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null
        },
        listLoading: true,
        tableData: [],
        total: 0
      },
      currentTitleItem: null
    }
  },
  created() {
    this.initGrade()
    this.initSubject();
    let id = this.$route.query.id
    let _this = this
    if (id && parseInt(id) !== 0) {
      _this.formLoading = true
      this.subjectFilter = this.subjectList
      paperApi.detail(id).then(re => {
        _this.form = re.data
        if (re.data.typeInfo) {
          _this.form.limitDateTime = re.data.typeInfo.split(",");
        }
        this.subjectFilter = this.subjectList.filter(data => data.gradeId === this.form.gradeId)
        _this.formLoading = false
      })
    }
  },
  methods: {
    initGrade() {
      subjectApi.gradeListAll().then(res => {
        this.gradeList = res.data;
      })
    },
    close() {
      this.$store.dispatch('delVisitedViews', this.$route);
      this.$router.push("/paper/paperList");
    },
    initSubject() {
      subjectApi.pageList().then(res => {
        this.subjectList = res.data.data
      })
    },
    levelChange() {
      this.form.subjectId = null
      this.subjectFilter = this.subjectList.filter(data => data.gradeId === this.form.gradeId)
    },
  },
  computed: {
    ...mapGetters('enumItem', ['enumFormat', 'subjectFormat', 'pageFormat']),
    ...mapState('enumItem', {
      gradeList: state => state.question.gradeList,
      questionType: state => state.question.typeList,
      editUrlEnum: state => state.question.editUrlEnum,
    }),
  }
}
</script>

<style lang="scss"></style>
