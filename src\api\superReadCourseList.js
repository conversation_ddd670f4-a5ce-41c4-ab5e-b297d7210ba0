/**
 * 超级阅读课程相关接口
 */
import request from '@/utils/request'

export default {
  // 课程分页查询
  courseList(data) {
    return request({
      url: "/znyy/superReadCourse/list",
      method: 'GET',
      params: data
    })
  },

  // 课程新增
  addCourse(data) {
    return request({
      url: '/znyy/superReadCourse/add',
      method: 'PUT',
      data
    })
  },

  // 课程编辑
  updateCourse(data) {
    return request({
      url: '/znyy/superReadCourse/edit',
      method: 'POST',
      data
    })
  },

  // 课程查询
  queryActive(id) {
    return request({
      url: '/znyy/superReadCourse/find?id=' + id,
      method: 'GET'
    })
  },
  // 课程开通与暂停
  updateStatus(id,status) {
    return request({
      url: '/znyy/superReadCourse/edit/status?id=' + id + '&status=' + status,
      method: 'POST'
    })
  },

  /**********************门店开通接口********************************/
  getCanOpenCourseList(data) {
    return request({
      url: "/znyy/super-read/student-open-course",
      method: 'GET',
      params: data
    })
  },

  openCourse(data) {
    return request({
      url: '/znyy/super-read/student-open-course',
      method: 'POST',
      data
    })
  },
}
