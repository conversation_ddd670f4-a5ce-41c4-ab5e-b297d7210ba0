<template>
  <div class="app-container">
    <el-form :model="dataQuery" ref="queryForm" :inline="true">
      <el-form-item label="学段：">
        <el-select v-model="dataQuery.grade" clearable placeholder="请选择学段">
          <el-option v-for="item in gradeList" :key="item" :value="item" :label="item"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="名称：">
        <el-input v-model="dataQuery.name" placeholder="请输入名称"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" style="marginRight: 5px" @click="seachForm">查询</el-button>
        <el-button type="success" style="marginRight: 5px" @click="resetQuery">重置</el-button>
        <el-button type="primary" @click="addBtn">新增</el-button>
      </el-form-item>
    </el-form>
    <el-table class="common-table" v-loading="tableLoading" :data="tableData" style="width: 100%;margin-bottom: 20px;"
      row-key="id" border default-expand-all>
      <el-table-column prop="id" label="ID"></el-table-column>
      <el-table-column prop="reportName" label="名称"></el-table-column>
<!--      0-评估，1-体验，2-正式-->
      <el-table-column prop="courseType" label="课程类型">
        <template slot-scope="scope">
          <span>{{scope.row.courseType == 0?"评估":(scope.row.courseType == 1?"体验":"正式")}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="grade" label="学段"></el-table-column>
      <el-table-column prop="withdrawnBonus" label="操作" width="480">
        <template slot-scope="scope">
          <el-button type="primary" size="mini" icon="el-icon-edit-outline" @click="editDetails(scope.row.id)">维度</el-button>
          <el-button type="success" size="mini" icon="el-icon-edit-outline" @click="editBtn(scope.row.id)">编辑</el-button>
          <el-button type="danger" size="mini" icon="el-icon-delete" @click="delBtn(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <el-col :span="24" style="margin-bottom: 20px">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>

    <!-- 修改或者新增弹窗 -->
    <el-dialog :title="isEdit?'编辑报告':'添加报告'" :visible.sync="open" width="50%" @close="close">
      <el-form ref="form" :model="form" :rules="rules" label-width="120px" style="width: 70%;">
        <el-form-item label="学段:" prop="grade">
          <el-select v-model="form.grade" clearable placeholder="请选择学段">
            <el-option v-for="item in gradeList" :key="item" :value="item" :label="item"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="课程类型:" prop="courseType">
          <el-select v-model="form.courseType" clearable placeholder="请选择课程类型">
            <el-option v-for="item in courseTypeList" :key="item.value" :value="item.value" :label="item.label"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="名称:" prop="reportName">
          <el-input v-model="form.reportName" placeholder="请输入报告名称"/>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import categoryApi from "@/api/abacusMentalCalc/category";
import reportApi from "@/api/abacusMentalCalc/report";
import { pageParamNames } from "@/utils/constants";
export default {
  name: "subject",
  data() {
    return {
      dataQuery: {
        grade: "",
        name: ""
      },
      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      open: false,
      tableData: [],
      form: {
        grade: "",
        reportName: "",
        courseType:null,
      },
      isEdit:false,

      gradeList:[], //学段
      courseTypeList:[
        {label:"评估",value:0},
        // {label:"体验",value:1},
        {label:"正式",value:2},
      ],

      // 表单校验
      rules: {
        reportName: [{ required: true, message: "请输入报告名称", trigger: "blur" }],
        grade: [{ required: true, message: "请选择学段", trigger: "blur" }],
        courseType: [{ required: true, message: "请选择课程类型", trigger: "blur" }],
      }
    };
  },
  created() {
    this.getGrade()
    this.getPageList();
  },
  methods: {
    addBtn() {
      this.reset();
      this.isEdit = false;
      this.open = true;
    },
    editBtn(id) {
      this.reset();
      this.isEdit = true;
      reportApi.detail(id).then(res => {
        this.form = res.data;
        this.open = true;
      });
    },
    editDetails(id){
      this.$router.push({ path: '/abacusMentalCalc/report', query: { id: id } })
    },
    delBtn(id) {
      this.$confirm('确定要删除吗？', '删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        reportApi.delete(id).then(res => {
          this.$message.success('删除成功！')
          this.getPageList()
        })
      })
    },
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          reportApi.saveOrUpdate(this.form).then(response => {
            this.$message.success("提交成功！");
            this.open = false;
            this.getPageList();
          });
        }
      });
    },
    seachForm(){
      this.handlePage();
    },
    getGrade(){
      categoryApi.getGrade().then(res => {
        this.gradeList = res.data
      });
    },
    getPageList() {
      this.tableLoading = true;
      this.dataQuery.pageNum = this.tablePage.currentPage;
      this.dataQuery.pageSize = this.tablePage.size;
      reportApi.list(this.dataQuery).then(res => {
        this.tableData = res.data.data;
        this.tableLoading = false;
        // 设置后台返回的分页参数
        pageParamNames.forEach(name =>
          this.$set(this.tablePage, name, parseInt(res.data[name]))
        );
      });
    },
    handlePage() {
      (this.tablePage = {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      }),
        this.getPageList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dataQuery = {
        grade: '',
        name: ""
      };
      this.getPageList();
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val;
      this.getPageList();
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val;
      this.getPageList();
    },
    close() {
      this.open = false;
      this.reset();
    },
    reset() {
      this.form = {
        grade: "",
        reportName: "",
        courseType:null,
      };
    },
  },
};
</script>

<style>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.period-table td,
.period-table th {
  text-align: center;
}

@media (max-width: 767px) {
  .el-message-box {
    width: 80% !important;
  }
}

.el-tooltip__popper {
  max-width: 800px;
}

.img-lg {
  width: 100px;
  height: 100px
}
</style>
