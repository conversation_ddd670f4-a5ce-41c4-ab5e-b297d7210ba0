<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="88px">
      <el-form-item label="表单类型">
        <el-select class="filter-item" v-model="queryParams.pageType" placeholder="表单类型" :clearable="true">
          <el-option v-for="item in SysOnlinePageType.getList()" :key="item.id" :value="item.id" :label="item.name" />
        </el-select>
      </el-form-item>
      <el-form-item label="表单名称">
        <el-input class="filter-item" v-model="queryParams.pageName" :clearable="true" placeholder="表单名称" />
      </el-form-item>
      <el-form-item>
        <el-button type="success" icon="el-icon-search" size="mini" @click="getPageList">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" style="margin-bottom: 30px;">
      <el-col :span="1.5">
        <el-button type="primary" size="mini" @click="onCreateOnlinePage()">
          新建
        </el-button>
      </el-col>
    </el-row>
    <el-table ref="class" :data="tableData" size="mini" header-cell-class-name="table-header-gray">
      <el-table-column label="序号" header-align="center" align="center" type="index" width="55px" />
      <el-table-column label="页面名称" prop="pageName" />
      <el-table-column label="操作" width="150px" fixed="right">
        <template slot-scope="scope">
          <el-button class="table-btn success" type="text" @click="onEditOnlinePage(scope.row)">编辑</el-button>
          <el-button class="table-btn delete" type="text" @click="onDeleteOnlinePage(scope.row)">删除</el-button>
        </template>
      </el-table-column>
      <el-table-column label="页面代码" prop="pageCode" />
      <el-table-column label="页面类型">
        <template slot-scope="scope">
          <el-tag size="mini" :type="scope.row.pageType === SysOnlinePageType.BIZ ? 'success' : 'primary'">
            {{ SysOnlinePageType.getValue(scope.row.pageType) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="页面状态" prop="statusDictMap.name">
        <template slot-scope="scope">
          <el-tag size="mini" :type="getPageStatusTagType(scope.row.status)">
            {{ SysOnlinePageStatus.getValue(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="发布状态">
        <template slot-scope="scope">
          <el-switch v-model="scope.row.published" @change="onUpdatePagePublished(scope.row)" />
        </template>
      </el-table-column>
      <el-table-column label="创建时间" prop="createTime" />

    </el-table>
    <!-- 分页 -->
    <el-col :span="20">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>

    <OnlinePageSetting v-if="showPageSetting" :pageId="(currentPage || {}).pageId" @close="onCloseSetting" />
  </div>
</template>

<script>

import { SysOnlinePageType } from '@/api/activiti/staticDict/onlineStaticDict'
import { mapGetters } from 'vuex'

import onlinePageApi from '@/api/activiti/onlinePage';
import { pageParamNames } from '@/utils/constants'
import OnlinePageSetting from './onlinePageSetting.vue';

export default {
  name: 'formOnlinePage',
  props: {},
  components: {
    OnlinePageSetting
  },
  data() {
    return {
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      // 查询参数
      queryParams: {
        pageType: null,
        pageName: null,
      },
      tableData: [],
      showPageSetting: false,
      currentPage: null,
    }
  },
  created() {
    this.getPageList();
  },

  methods: {
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = {
        pageType: null,
        pageName: null,
      },
        this.getPageList()
    },

    getPageList() {
      this.queryParams.pageNum = this.tablePage.currentPage
      this.queryParams.pageSize = this.tablePage.size
      onlinePageApi.pageList(this.queryParams).then(res => {
        this.tableData = res.data.data
        this.loading = false
        // 设置后台返回的分页参数
        pageParamNames.forEach(name => this.$set(this.tablePage, name, parseInt(res.data[name])))
      })

    },

    getPageStatusTagType(status) {
      switch (status) {
        case this.SysOnlinePageStatus.BASIC:
          return 'warning'
        case this.SysOnlinePageStatus.DATASOURCE:
          return 'primary'
        case this.SysOnlinePageStatus.DESIGNING:
          return 'success'
      }
    },

    loadOnlinePageVerify() {
      this.formOnlinePage.formFilterCopy.pageType = this.formOnlinePage.formFilter.pageType
      this.formOnlinePage.formFilterCopy.pageName = this.formOnlinePage.formFilter.pageName
      return true
    },
    onCreateOnlinePage() {
      this.$store.dispatch('hiddenMenu', { flag: true })
      this.currentPage = null
      this.showPageSetting = true
    },
    onEditOnlinePage(row) {
      this.$store.dispatch('hiddenMenu', { flag: true })
      this.currentPage = row
      this.showPageSetting = true
    },
    onDeleteOnlinePage(row) {
      this.$confirm('是否删除此页面？').then(res => {
        let params = {
          pageId: row.pageId
        }
        return onlinePageApi.delete(params)
      }).then(res => {
        this.$message.success('删除成功！')
        this.getPageList();
      }).catch(e => {
      })
    },
    onUpdatePagePublished(row) {
      let params = {
        pageId: row.pageId,
        published: row.published
      }

      onlinePageApi.updatePublished(params).catch(e => {
        // 恢复发布状态为更改之前
        row.published = !row.published
      })
    },
    onCloseSetting() {
      this.currentPage = null
      this.showPageSetting = false
    },
    refreshOnlinePage(reloadData) {
      if (reloadData) {
      } else {
      }
      if (!this.formOnlinePage.isInit) {
        // 初始化下拉数据
      }
      this.formOnlinePage.isInit = true
    },
    onResume() {
      this.refreshOnlinePage()
    },
    initFormData() {
    },
    formInit() {
      this.refreshOnlinePage()
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val
      this.getPageList()
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val
      this.getPageList()

    },
    computed: {
      ...mapGetters(['getClientHeight'])
    },
  }
}
</script>

<style></style>
