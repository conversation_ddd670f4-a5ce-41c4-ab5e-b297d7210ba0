<template>
  <div class="app-container" style="padding: 20px">
    <!-- 搜索表单区域 -->
    <el-form :inline="true" ref="SearchForm" :model="selectOperate" class="SearchForm">
      <el-row style="padding: 20px 20px 0 20px">
        <el-col :span="6" :xs="24">
          <el-form-item label="课程大类:">
            <el-select v-model="selectOperate.curriculumId" placeholder="请选择课程大类" @change="handleCurriculumChange">
              <el-option v-for="item in courseTypeOptions" :key="item.id" :label="item.enName" :value="item.id" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6" :xs="24">
          <el-form-item label="课程名称:">
            <el-input v-model.trim="selectOperate.courseName" placeholder="请输入课程名称搜索" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="类型:">
            <el-select v-model="selectOperate.courseType" placeholder="请选择">
              <el-option label="正课" value="1"></el-option>
              <el-option label="试课" value="0"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6" style="text-align: right; padding-right: 20px">
          <el-button plain icon="el-icon-refresh" @click="reset()">重置</el-button>
          <el-button type="primary" icon="el-icon-search" @click="handleSearch">搜索</el-button>
        </el-col>
      </el-row>
    </el-form>
    <div style="padding: 20px">
      <div style="display: flex; align-items: center; justify-content: space-between">
        <div style="display: flex; align-items: center; gap: 10px">
          <h3 style="margin: 0">课程列表</h3>
          <span style="font-size: 12px; color: #6c6c6c; margin-top: 8px">
            没有版本、学科、学段，
            <el-button type="text" @click="goConfig">去配置></el-button>
            。
            <!-- 没有权限请联系平台运营 -->
          </span>
        </div>
        <div>
          <el-button type="primary" icon="el-icon-plus" @click="handleAdd">新增课程</el-button>
        </div>
      </div>
    </div>
    <el-card class="app-container">
      <div class="content-container">
        <!-- 左侧树形结构 -->
        <div class="tree-container" v-loading="versionLoading">
          <el-select v-model="versionId" placeholder="请选择" @change="exchangeVersionId">
            <el-option v-for="item in versionOptions" :key="item.id" :label="item.versionName" :value="item.id" />
          </el-select>
          <div style="margin-top: 10px" v-if="versionOptions.length > 0">
            <Tree
              ref="tree"
              node-key="id"
              v-if="treeData.length"
              highlight-current
              :tree-data="treeData"
              :default-props="defaultProps"
              :default-expand-all="true"
              @node-click="handleNodeClick"
              :current-node-key="currentNodeKey"
              :expand-on-click-node="false"
            />
          </div>
          <div class="nomore" v-else>
            <el-image style="width: 100px; height: 100px" src="https://document.dxznjy.com/automation/1728442200000" />
            <div style="color: #999; margin-top: 20px">暂无数据</div>
          </div>
        </div>
        <!-- 右侧表格 -->
        <div class="table-container">
          <div class="table-scroll">
            <el-table v-loading="loading" :data="tableData" size="small" style="width: 100%" fit highlight-current-row :header-cell-style="getRowClass">
              <el-table-column type="index" label="序号" align="center" />
              <el-table-column align="center" prop="courseId" label="课程ID" width="100" />
              <el-table-column align="center" prop="courseName" label="课程名称" />
              <el-table-column align="center" prop="courseSort" label="排序" width="50" />
              <el-table-column align="center" prop="courseTypeStr" label="类型">
                <template slot-scope="{ row }">
                  {{ filterType(row.courseType) }}
                </template>
              </el-table-column>
              <el-table-column align="center" prop="courseVersionNodeId" label="版本">
                <template slot-scope="{ row }">
                  {{ filterVersionName(row.courseVersionNodeId) }}
                </template>
              </el-table-column>
              <el-table-column align="center" prop="courseSubjectNodeName" label="学科" />
              <el-table-column align="center" prop="coursePeriodNodeName" label="学段" />
              <el-table-column align="center" prop="courseFlagStr" label="状态" />
              <el-table-column align="center" prop="createTime" label="创建时间" />
              <el-table-column align="center" label="操作">
                <template slot-scope="scope">
                  <el-button @click="handleEdit(scope.row)" type="text" size="small">编辑</el-button>
                  <el-button type="text" size="small" @click="handleDelete(scope.row)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div class="pagination-container">
            <el-pagination
              background
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="pageParams.pageNum"
              :page-sizes="[10, 20, 30, 50]"
              :page-size="pageParams.pageSize"
              layout="total, sizes, prev, pager, next, jumper"
              :total="total"
            />
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
  import Tree from './components/tree.vue';
  import courseManagementAPI from '@/api/mathApi/courseManagementAPI';
  import videoManageMentAPI from '@/api/mathApi/videoManageMentAPI';
  import checkPermission from '@/utils/permission';
  export default {
    name: 'CourseManagementList',
    components: {
      Tree
    },
    data() {
      return {
        index: 1,
        courseTypes: [],
        // 查询条件
        selectOperate: {
          curriculumId: '', // 课程大类，默认选择第一项
          courseName: '', // 课程名称
          courseType: ''
        },
        // 树形数据
        treeData: [],
        // 课程大类选项
        courseTypeOptions: [],
        // 表格数据
        tableData: [],
        // 版本选项
        versionOptions: [],
        // 加载状态
        listLoading: false,

        defaultProps: {
          children: 'children',
          label: 'label'
        },
        // 默认展开的节点
        defaultExpandedKeys: [1, 2],
        // 表格数据
        tableData: [],
        loading: false,
        // 分页参数
        pageParams: {
          pageNum: 1,
          pageSize: 10
        },
        total: 0,
        versionLoading: false,
        currentNodeKey: '', // 当前点击节点的id:
        currentNodeLevel: '1', // 当前点击节点的分类层级
        versionId: '',
        currentNodeId: 2, // 默认选中学科节点
        childrenId: null
      };
    },
    created() {
      // 初始化时加载课程大类数据
      this.getCourseCategoriesAPI();
    },

    $route(to, from) {
      console.log(to, from, '路由变化了，重新加载数据1111');
      // if (to.name == 'vedioManage') {
      //   this.resetForm();
      //   sessionStorage.removeItem('videoData');
      // } else {
      //   this.videoForm = JSON.parse(sessionStorage.getItem('videoData'));
      // }
      // console.log(this.videoForm, 'this.videoForm');
      // this.curriculumId = this.videoForm.curriculumId;
      // if (this.videoForm && this.videoForm.id) {
      //   this.id = this.videoForm.id;
      //   this.getVideoDetail(this.videoForm.id);
      // }
    },
    methods: {
      checkPermission,
      goConfig() {
        let flag = checkPermission(['b:aaademo:pzqxCourse']);
        if (flag) {
          this.$router.push({ path: '/_aaa_demo/courseTypeConfig' });
        } else {
          return this.$message.warning('您没有权限');
        }
      },
      filterVersionName(str) {
        let arr = this.versionOptions.filter((i) => {
          if (i.id == str) {
            return i.versionName;
          }
        });
        if (arr.length > 0) {
          return arr[0].versionName;
        } else {
          return '';
        }
      },
      filterType(str) {
        console.log(str);
        let options = [
          { value: '1', label: '正课' },
          { value: '0', label: '试课' }
        ];
        let values = str.split(',');
        const labels = values
          .map((val) => options.find((option) => option.value === val)?.label)
          .filter(Boolean) // 过滤掉未匹配到的
          .join(',');

        console.log(labels);
        return labels;
      },
      exchangeVersionId() {
        this.getTableData();
        this.getTreeData();
      },
      // 获取课程大类
      getCourseCategoriesAPI() {
        courseManagementAPI.courseCategoriesAPI().then((res) => {
          if (res.success) {
            this.courseTypeOptions = res.data;
            if (this.courseTypeOptions && this.courseTypeOptions.length > 0) {
              // 设置默认选中的课程大类ID
              this.selectOperate.curriculumId = this.courseTypeOptions[0].id;
              // 存储到本地
              localStorage.setItem('curriculumId', this.selectOperate.curriculumId);
              // 自动执行一次搜索
              // this.handleSearch();
              this.getVersionId();
            }
          }
        });
      },
      // 获取版本id
      getVersionId() {
        videoManageMentAPI
          .getVersionIdAPI({
            curriculumId: this.selectOperate.curriculumId
          })
          .then((res) => {
            if (res.success) {
              this.versionOptions = res.data;
              this.versionId = this.versionOptions.length > 0 ? this.versionOptions[0].id : '';
              this.getTreeData();
            } else {
              this.versionOptions = [];
              this.versionId = '';
              // this.getTreeData();
            }
          });
      },

      deepReplace(array) {
        if (array instanceof Array && array.length >= 1) {
          return array.map((el) => {
            return {
              id: el.id,
              label: el.nodeName,
              children: this.deepReplace(el.childList),
              ...el
            };
          });
        } else {
          return [];
        }
      },
      // 搜索按钮点击事件
      handleSearch() {
        this.pageParams.pageNum = 1;
        this.getTableData();
      },
      // 获取树形数据
      getTreeData() {
        if (!this.selectOperate.curriculumId) return;
        if (!this.versionId) return;
        this.versionLoading = true;
        videoManageMentAPI
          .getTreeDataAPI({
            curriculumId: this.selectOperate.curriculumId,
            versionId: this.versionId,
            nodeLevel: 2
          })
          .then((res) => {
            if (res.success) {
              // 转换数据结构
              this.treeData = this.deepReplace(res.data);
              this.currentNodeKey = this.treeData[0].id;
              this.childrenId = this.treeData[0].id;
              this.parentId = this.treeData[0].children[0].id;

              // 自动执行一次搜索
              this.handleSearch();
            }
          })
          .finally(() => {
            this.versionLoading = false;
          });
      },
      // 获取表格数据
      getTableData() {
        this.loading = true;
        let queryData = {
          ...this.pageParams,
          ...this.selectOperate,
          curriculumId: this.selectOperate.curriculumId
        };
        // 根据节点层级设置不同的参数
        if (this.currentNodeLevel == 1) {
          // 版本节点
          queryData.courseVersionNodeId = this.versionId;
          queryData.courseSubjectNodeId = this.childrenId;
        } else {
          // 学科节点
          queryData.courseVersionNodeId = this.versionId;
          queryData.courseSubjectNodeId = this.parentId || null;
          queryData.coursePeriodNodeId = this.childrenId;
          console.log(queryData.courseVersionNodeId, queryData.courseSubjectNodeId, queryData.coursePeriodNodeId);
        }
        courseManagementAPI
          .listCourseAPI(queryData)
          .then((res) => {
            if (res.success) {
              this.tableData = res.data.data;
              this.total = res.data.totalItems * 1;
            }
          })
          .finally(() => {
            this.loading = false;
          });
      },
      // 课程大类选择改变时
      handleCurriculumChange() {
        // 重新获取树形数据
        // this.getTreeData();
        this.getVersionId();
        // 重新获取列表数据
        this.getTableData();
      },
      // 去配置
      courseConfig() {
        // 判断是否有权限有--去课程分类配置页面
        // if (接口权限) {
        //     this.$router.push('/course/courseConfig')
        // }else{
        this.$message({
          type: 'warning',
          message: '暂无权限，请联系平台运营'
        });
        // }
      },
      // 重置
      reset() {
        this.$refs.SearchForm.resetFields();
        // 获取默认的课程大类
        const defaultCurriculum = this.courseTypeOptions[0];
        if (defaultCurriculum) {
          this.selectOperate.curriculumId = defaultCurriculum.id;
          // 存储到本地
          localStorage.setItem('curriculumId', defaultCurriculum.id);
        }
        // 重置其他字段
        this.selectOperate.courseName = '';
        this.selectOperate.courseType = '';
        // 重置后自动执行一次搜索
        this.handleSearch();
      },
      // 新增课程
      handleAdd() {
        // 检查是否是学段节点
        if (!this.currentNodeData || this.currentNodeData.nodeLevel !== 2) {
          this.$message({
            type: 'warning',
            message: '请选择学段节点后再进行新增操作'
          });
          return;
        }
        let obj = {
          curriculumId: this.selectOperate.curriculumId,
          courseVersionNodeId: this.versionId,
          courseSubjectNodeId: this.parentId,
          coursePeriodNodeId: this.childrenId
        };
        sessionStorage.setItem('courseNodeData', JSON.stringify(obj));
        // sessionStorage.setItem('curriculumId', this.selectOperate.curriculumId);
        this.$router.push('/_aaa_demo/addCourse');
      },
      // 获取表头样式
      getRowClass() {
        return {
          background: '#f5f7fa',
          color: '#606266',
          fontWeight: 'bold'
        };
      },

      // 树节点点击事件
      handleNodeClick(data, node) {
        console.log(data, '点击的数据');
        console.log(node, '点击的节点');

        if (node.level == 1) {
          this.childrenId = node.data.id; // 当前节点的id
        } else if (node.level == 2) {
          this.childrenId = node.data.id; // 当前节点的id
          this.parentId = node.parent.data.id; // 子级
        }

        this.currentNodeLevel = data.nodeLevel;
        this.currentNodeData = data;
        // // 获取表格数据
        // this.getTableData();
        this.handleSearch();
      },
      // 分页大小改变
      handleSizeChange(val) {
        this.pageParams.pageSize = val;
        this.getTableData();
      },
      // 页码改变
      handleCurrentChange(val) {
        this.pageParams.pageNum = val;
        this.getTableData();
      },
      // 编辑
      handleEdit(row) {
        console.log('编辑', row);
        courseManagementAPI.getCourseDetail({ id: row.id }).then((res) => {
          console.log('编辑', res);
          if (res.success) {
            // 跳转前
            let obj = {
              curriculumId: this.selectOperate.curriculumId,
              courseVersionNodeId: row.courseVersionNodeId,
              courseSubjectNodeId: row.courseSubjectNodeId,
              coursePeriodNodeId: row.coursePeriodNodeId
            };
            let arr = JSON.parse(JSON.stringify(res.data));
            arr.courseProcessConfigCoList = JSON.parse(JSON.stringify(arr.courseProcessConfigVoList));
            arr.courseProcessUnlockVideoCoList = JSON.parse(JSON.stringify(arr.courseProcessUnlockVideoVoList));
            arr.courseKnowledgeCorrelationCoList = JSON.parse(JSON.stringify(arr.courseKnowledgeCorrelationVoList));
            arr.courseProcessConfigVoList.forEach((item) => {
              item.processQuestionTypeCoList = JSON.parse(JSON.stringify(item.processQuestionTypeVoList));
              item.courseProcessVideoCoList = JSON.parse(JSON.stringify(item.courseProcessVideoVoList));
            });
            sessionStorage.setItem('courseNodeData', JSON.stringify(obj));
            sessionStorage.setItem('courseData', JSON.stringify(arr));
            this.$router.push('/_aaa_demo/addCourse');
          }
        });
      },
      // 删除
      handleDelete(row) {
        this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            courseManagementAPI
              .delCourseAPI({
                id: row.id
              })
              .then((res) => {
                if (res.success) {
                  this.$message.success('删除成功');
                  this.getTableData();
                }
              });
          })
          .catch(() => {
            this.$message.info('已取消删除');
          });

        console.log('删除', row);
      }
    }
  };
</script>

<style scoped>
  .app-container {
    background-color: #fff;
    border-radius: 4px;
  }

  .SearchForm {
    background-color: #fff;
    border-radius: 4px;
  }

  .el-form-item {
    margin-bottom: 18px;
  }

  .table-operator {
    margin-bottom: 18px;
  }
  /* tree和table样式 */
  .app-container {
    height: auto;
    width: 100%;
    margin: 0;
    padding: 0;
    overflow: visible;
    display: flex;
    flex-direction: column;
  }

  .content-container {
    display: flex;
    min-height: calc(100vh - 180px);
  }

  .tree-container {
    border-right: 1px solid #dcdfe6;
    padding: 20px;
    flex-shrink: 0;
    /* width: 220px; */
    max-height: calc(100vh - 300px); /* 这里180px可根据实际页面头部高度调整 */
    overflow-y: auto;
  }

  .table-container {
    flex: 1;
    padding: 20px 20px 0 20px;
    display: flex;
    flex-direction: column;
    min-width: 0;
    overflow: visible;
  }

  .table-scroll {
    flex: 1;
    overflow: visible;
  }

  .pagination-container {
    display: flex;
    -webkit-box-pack: end;
    justify-content: flex-end;
    margin-top: 20px;
    padding: 10px 0;
  }

  .no-spinners input::-webkit-outer-spin-button,
  .no-spinners input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  .no-spinners input[type='number'] {
    -moz-appearance: textfield;
  }
  /deep/ .el-table {
    width: 100%;
    height: 100%;
  }
  .nomore {
    padding: 200px 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }
</style>
