<template>
  <div class="app-container">
    <el-form :model="dataQuery" ref="queryForm" :inline="true">
      <el-form-item label="类别：">
        <el-select v-model="dataQuery.categoryType" placeholder="全部" clearable>
          <el-option v-for="(item, index) in categoryList" :key="index" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="题型：">
        <el-select v-model="dataQuery.questionType" placeholder="全部" clearable>
          <el-option v-for="(item, index) in questionTypeFilter" :key="index" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="难度：">
        <el-select v-model="dataQuery.difficulty" placeholder="全部" clearable>
          <el-option v-for="(item, index) in difficultyList" :key="index" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" style="marginRight: 5px" @click="getPageList">查询</el-button>
        <el-popover placement="bottom" trigger="click">
          <el-button type="warning" size="mini" v-for="item in visualEditUrlEnum" :key="item.questionType"
            @click="$router.push({ path: item.value })">{{ item.name }}</el-button>
          <el-button slot="reference" type="primary" class="link-left">添加</el-button>
        </el-popover>
        <!--        <el-button type="primary" class="link-left" @click="$router.push({ path: '/paper/imgRepeat' })">添加</el-button>-->
      </el-form-item>
    </el-form>
    <el-table class="common-table" v-loading="listLoading" :data="tableData" style="width: 100%; margin-bottom: 20px"
      row-key="id" border default-expand-all>
      <el-table-column prop="id" label="Id" />
      <el-table-column prop="type" label="所属类型" :formatter="typeFormatter"></el-table-column>
      <el-table-column prop="categoryType" label="题目类型" >
        <!-- categoryType -->
         <template  slot-scope="{ row }">
            <span>{{ row.categoryType == 1 ? '正式题' : '附加题'}}</span>
         </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="260px">
        <template slot-scope="{ row }">
          <el-button size="mini" @click="editQuestion(row)">编辑</el-button>
          <el-button size="mini" type="danger" @click="deleteQuestion(row)" class="link-left">删除</el-button>
          <el-button size="mini" @click="handleEdit(row)" type="primary">系统反馈</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="questionType" label="题型" :formatter="questionTypeFormatter"></el-table-column>
      <el-table-column prop="difficulty" label="难度" :formatter="difficultyFormatter"></el-table-column>
      <el-table-column prop="title" label="题目" show-overflow-tooltip />
      <el-table-column prop="createTime" label="创建时间" />
    </el-table>
    <!-- 分页 -->
    <el-col :span="24" style="margin-bottom: 20px">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>
    <!-- 系统反馈设置 -->
    <el-dialog title="系统反馈设置" :visible.sync="open" width="70%">
      <el-form ref="form" :model="form" label-width="120px" style="width: 70%;">
        <el-form-item label="系统反馈：" prop="isValue">
          <el-radio-group v-model="form.isHave">
            <el-radio :label="true">有</el-radio>
            <el-radio :label="false">无</el-radio>
          </el-radio-group>
          <el-button type="success" @click="addValue" v-show="form.isHave" style="margin-left: 20px">添加</el-button>
        </el-form-item>
        <div v-for="(item, index) in form.feedback" :key="index" v-show="form.isHave">
          <el-form-item label="个数范围：">
            <el-input v-model="item.min" placeholder="没有可不填" style="width: 120px"
              oninput="value=value.replace(/[^\d]/g,'')" />
            <span style="margin-left: 2px;margin-right: 2px">至</span>
            <el-input v-model="item.max" placeholder="没有可不填" style="width: 120px"
              oninput="value=value.replace(/[^\d]/g,'')" />
          </el-form-item>
          <el-form-item label="提示语：">
            <el-input v-model="item.tips" style="width: 70%" />
            <el-button type="danger" size="mini" icon="el-icon-delete" @click="removeValue(index)"  style="margin-left: 5px" />
          </el-form-item>
        </div>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import questionApi from '@/api/paper/train/question'
import { mapGetters, mapState } from 'vuex';
import { pageParamNames } from "@/utils/constants";
import '/public/components/ueditor/themes/iframe.css'

export default {
  data() {
    return {
      open: false,
      form: {
        id: null,
        isHave: false,
        feedback: [
          {
            min: null,
            max: null,
            tips: null
          }
        ]
      },
      categoryList:[
        {label:'正式题',value:1},
        {label:'附加题',value:2}
      ],
      questionTypeFilter: [],
      dataQuery: {
        id: null,
        type: 'VISUAL_',
        questionType: null,
        difficulty: null,
      },
      listLoading: true,
      tableData: [],
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null,
      },
      total: 0,
    };
  },
  created() {
    this.getPageList();
    this.questionTypeFilter = this.trainQuestionType.filter(
      (data) => data.value.includes(this.dataQuery.type)
    )
  },
  methods: {
    // 表单重置
    reset() {
      this.form = {
        id: null,
        isHave: false,
        feedback: [
          {
            min: undefined,
            max: undefined,
            tips: null
          }
        ]
      }
    },

    handleEdit(row) {
      this.reset()
      this.form.id = row.id
      if (row.feedback != null) {
        this.form.feedback = row.feedback
        this.$set(this.form, 'isHave', row.feedback !== null)
      }
      this.open = true
    },
    addValue() {
      let item = {
        min: null,
        max: null,
        tips: null
      }
      this.form.feedback.push(item)
    },
    removeValue(index) {
      this.form.feedback.splice(index, 1)
    },
    submitForm() {
      console.log(this.form)
      if (this.form.isHave) {
        this.form.feedback.forEach(i => {
          if (i.tips === null) {
            this.$message.error("请输入提示语！")
            return false
          }
        })
      } else {
        this.form.feedback = []
      }
      questionApi.feedback(this.form).then(res => {
        this.$message.success("修改成功！")
        this.getPageList();
        this.open = false;
      })
    },
    close() {
      this.importOpen = false;
      this.importFrom = {
        file: null
      }
    },
    typeFormatter(row, column, cellValue, index) {
      return this.enumFormat(this.trainType, cellValue)
    },
    questionTypeFormatter(row, column, cellValue, index) {
      return this.enumFormat(this.trainQuestionType, cellValue)
    },
    difficultyFormatter(row, column, cellValue, index) {
      return this.enumFormat(this.difficultyList, cellValue)
    },
    getPageList() {
      this.listLoading = true
      this.dataQuery.pageNum = this.tablePage.currentPage;
      this.dataQuery.pageSize = this.tablePage.size;
      questionApi.list(this.dataQuery).then((res) => {
        this.tableData = res.data.data;
        this.listLoading = false;
        // 设置后台返回的分页参数
        pageParamNames.forEach((name) =>
          this.$set(this.tablePage, name, parseInt(res.data[name]))
        );
      });
    },
    editQuestion(row) {
      let url = this.trainUrlFormat(this.visualEditUrlEnum, row.questionType);
      this.$router.push({ path: url, query: { id: row.id } });
    },
    deleteQuestion(row) {
      let _this = this
      this.$confirm('是否删除该题目？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        questionApi.delete(row.id).then(re => {
          if (re.success) {
            _this.$message.success(re.message)
            _this.getPageList()
          } else {
            _this.$message.error(re.message)
          }
        })
      })
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val;
      this.getPageList();
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val;
      this.getPageList();
    },
  },
  computed: {
    ...mapGetters('enumItem', ['enumFormat', 'trainUrlFormat']),
    ...mapState('enumItem', {
      trainType: state => state.train.trainType,
      difficultyList: state => state.train.difficultyList,
      trainQuestionType: state => state.train.trainQuestionType,
      visualEditUrlEnum: state => state.train.visualEditUrlEnum
    }),
  }
}
</script>

<style lang="less" scoped>
/deep/.el-dialog__header {
  border-bottom: 1px solid #dfdfdf;
}
</style>
<style>
.el-tooltip__popper {
  max-width: 800px;
}
</style>
