<template>
  <div class="app-container">
    <el-form :inline="true" class="container-card" label-width="110px" label-position="left">
      <el-row>
        <el-col :span="8" :xs="24">
          <el-form-item label="学员编号：">
            <el-input v-model="dataQuery.studentCode" placeholder="请输入学员编号" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="登录账号：">
            <el-input v-model="dataQuery.loginName" placeholder="请输入登录账号" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="姓名：">
            <el-input v-model="dataQuery.realName" placeholder="请输入姓名" clearable />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8" :xs="24">
          <el-form-item label="销课类型：">
            <el-select v-model="dataQuery.status" clearable placeholder="请选择">
              <el-option label="门店" value="0" />
              <el-option label="交付中心" value="1" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item>
            <el-form-item label="销课时间：">
              <el-date-picker style="width: 100%;" value-format="yyyy-MM-dd hh:mm:ss" v-model="value1" clearable
                type="daterange" align="right" unlink-panels range-separator="至" start-placeholder="开始日期"
                end-placeholder="结束日期">
              </el-date-picker>
            </el-form-item>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item>
            <el-button type="primary" size="small" icon="el-icon-search" @click="fetchData01()">搜索</el-button>
          </el-form-item>
        </el-col>
      </el-row>
      <!-- <el-form-item>
        <el-button type="primary" icon="el-icon-search" v-loading="exportLoading " @click="exportFlow()" v>导出</el-button>
      </el-form-item> -->
    </el-form>

    <el-table class="common-table" v-loading="tableLoading" :data="tableData" style="width: 100%;margin-bottom: 20px;"
      row-key="id" border default-expand-all :tree-props="{ list: 'children', hasChildren: 'true' }">
      <el-table-column prop="studentCode" label="学员编号"></el-table-column>
      <el-table-column prop="loginName" label="登录账号"></el-table-column>
      <el-table-column prop="realName" label="姓名"></el-table-column>
      <el-table-column prop="startTime" label="开始时间"></el-table-column>
      <el-table-column prop="endTime" label="结束时间"></el-table-column>
      <el-table-column prop="beforeHours" label="账户学时（节）"></el-table-column>
      <el-table-column prop="userHours" label="本次销课（节）"></el-table-column>
      <el-table-column prop="afterHours" label="剩余学时（节）"></el-table-column>
      <el-table-column prop="status" label="销课类型">
        <template slot-scope="scope">
          <span v-if="scope.row.remark.includes('集中交付')">交付中心</span>
          <span v-else>门店</span>
        </template>
      </el-table-column>
      <el-table-column prop="remark" label="销课说明" width="350" show-overflow-tooltip="true"></el-table-column>
    </el-table>
    <!-- 分页 -->
    <el-col :span="24" style="overflow-x: auto;" :xs="24">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>
  </div>
</template>

<script>
import studentMarketingApi from "@/api/areasStudentCourseList";
import { pageParamNames } from "@/utils/constants";

export default {
  data() {
    return {
      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      tableData: [],
      dataQuery: {
        studentCode: '',
        merchantCode: '',
        startDate: '',
        endDate: '',
        loginName: '',
        realName: ''
      },
      value1: '',
      exportLoading: false,
    };
  },
  created() {
    this.fetchData01();
  },
  methods: {
    // 获取起始时间
    dateVal(e) {
      // console.log(e[0]);
      this.dataQuery.startDate = e[0]
      this.dataQuery.endDate = e[1]
    },
    fetchData01() {
      this.tablePage = {
        currentPage: 1,
        size: this.tablePage.size,
        totalPage: null,
        totalItems: null
      }
      this.fetchData();
    },
    // 查询提现列表
    fetchData() {
      const that = this;
      if (that.value1 != '' && that.value1 != null && that.value1 != undefined) {
        if (that.value1.length > 0) {
          that.dataQuery.startDate = that.value1[0]
          that.dataQuery.endDate = that.value1[1]
        } else {
          that.dataQuery.startDate = ''
          that.dataQuery.endDate = ''
        }
      } else {
        that.dataQuery.startDate = ''
        that.dataQuery.endDate = ''
      }
      that.tableLoading = true
      studentMarketingApi.salesRecord(that.tablePage.currentPage, that.tablePage.size, that.dataQuery).then(res => {
        that.tableData = res.data.data
        that.tableLoading = false
        // 设置后台返回的分页参数
        pageParamNames.forEach(name => that.$set(that.tablePage, name, parseInt(res.data[name])))
      })
    },
    //导出
    exportFlow() {
      const that = this;
      that.exportLoading = true;
      studentMarketingApi.exportAreasStudentRecord(that.dataQuery).then(res => {
        const url = window.URL.createObjectURL(res);
        const link = document.createElement("a");
        link.style.display = "none";
        link.href = url; // 获取服务器端的文件名
        link.setAttribute("download", "学员销毁学时表.xls");
        document.body.appendChild(link);
        link.click();
        that.exportLoading = false;
      })

    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val;
      this.fetchData();
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val;
      this.fetchData();
    },
  }
};
</script>

<style>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.period-table td,
.period-table th {
  text-align: center;
}

.mt20 {
  margin-top: 20px;
}

.red {
  color: red;
}

.green {
  color: green;
}
</style>
