<template>
  <div element-loading-spinner="el-icon-loading" element-loading-text="加载中..." v-loading="loading">
    <el-card shadow="never" class="card">
      <el-form ref="form" :model="searchForm" :inline="true">
        <el-form-item label="超级品牌编码">
          <el-input v-model="searchForm.merchantCode" placeholder="请输入超级品牌编码" size="small"></el-input>
        </el-form-item>
        <el-form-item label="品牌手机号">
          <el-input v-model="searchForm.phone" placeholder="请输入品牌手机号" size="small"></el-input>
        </el-form-item>
        <el-form-item label="品牌名称">
          <el-input v-model="searchForm.merchantName" placeholder="请输入品牌名称" size="small"></el-input>
        </el-form-item>
        <el-form-item label="品牌授权" label-width="80px">
          <el-select v-model="searchForm.tOpenSuccess" value-key="value" placeholder="请选择" clearable>
            <el-option
              v-for="(item, index) in [
                { value: 0, label: '未授权' },
                { value: 1, label: '已授权' }
              ]"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="渠道经理" v-if="havaChannelManagerPermission || isChannelManager">
          <el-input v-model="searchForm.channelManagerName" placeholder="请输入渠道经理姓名" size="small" clearable></el-input>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.isEnable" value-key="value" placeholder="请选择" clearable>
            <el-option
              v-for="(item, index) in [
                { value: 1, label: '开通' },
                { value: 2, label: '禁用' }
              ]"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="品牌授权">
          <el-input v-model="searchForm.tOpenSuccess" placeholder="请输入品牌名称" size="small"></el-input>
        </el-form-item> -->
        <el-form-item>
          <el-button type="primary" @click="onSubmit" size="small">查询</el-button>
          <el-button size="small" @click="onreset">重置</el-button>
        </el-form-item>
      </el-form>
      <el-button v-if="checkPermission(['b:merchant:brand:brandAdd'])" type="success" @click="clickAdd" style="margin-bottom: 20px" size="small">添加</el-button>
      <el-button type="primary" @click="batchBind" style="margin-bottom: 20px" v-if="checkPermission(['b:merchant:brand:brandBatchAdd'])" size="small">批量绑定俱乐部</el-button>
      <div id="charts_one" style="min-height: 80vh">
        <el-table :header-cell-style="{ background: '#eef1f6', color: '#606266' }" ref="multipleTable" border :data="list">
          <el-table-column align="center" property="merchantCode" label="编号" width="120"></el-table-column>
          <el-table-column align="center" property="phone" label="登录账号" width="180"></el-table-column>
          <el-table-column align="center" property="merchantName" label="品牌名称" width="180"></el-table-column>
          <el-table-column align="center" property="realName" label="负责人" width="180"></el-table-column>
          <el-table-column align="center" property="channelManagerName" label="渠道经理" width="180" v-if="havaChannelManagerPermission || isChannelManager"></el-table-column>
          <el-table-column align="center" property="studySystemCount" label="剩余合伙人智能学习管理系统数" width="180"></el-table-column>
          <!-- <el-table-column align="center" property="partnerInvitationCodeNum" label="剩余合伙人邀请码" width="180"></el-table-column> -->

          <el-table-column align="center" property="shareCode" label="推荐人编号" width="180"></el-table-column>
          <el-table-column align="center" property="address" label="所在地区" width="220"></el-table-column>
          <el-table-column align="center" property="subClubCount" label="下级俱乐部" width="120"></el-table-column>
          <el-table-column align="center" property="subStoreCount" label="下级合伙人" width=""></el-table-column>

          <el-table-column align="center" property="subMemberCount" label="下级会员" width=""></el-table-column>
          <!-- <el-table-column align="center" property="paymentStatus" label="是否完款">
            <template v-slot="{ row }">
              <span v-if="row.paymentStatus == 0">否</span>
              <span v-if="row.paymentStatus == 1">是</span>
            </template>
          </el-table-column> -->
          <el-table-column align="center" property="isEnable" label="状态" width="">
            <template v-slot="{ row }">
              <span v-if="row.isEnable == 1" style="color: green">开通</span>
              <span v-if="row.isEnable == 2" style="color: red">禁用</span>
            </template>
          </el-table-column>
          <el-table-column align="center" property="topenSuccess" label="是否授权">
            <template v-slot="{ row }">
              <span v-if="row.topenSuccess == 0">未授权</span>
              <span v-if="row.topenSuccess == 1">已授权</span>
            </template>
          </el-table-column>
          <el-table-column align="center" property="isCheck" label="审核状态">
            <template v-slot="{ row }">
              <span v-if="row.isCheck == 0">未审核</span>
              <span v-if="row.isCheck == 1">审核通过</span>
              <span v-if="row.isCheck == 2">审核驳回</span>
            </template>
          </el-table-column>
          <el-table-column align="center" property="applyTime" label="申请时间" :formatter="filterTime" width="180"></el-table-column>
          <el-table-column label="操作" fixed="right" align="center" width="420">
            <template slot-scope="scope">
              <div>
                <el-button size="mini" type="primary" @click="updateDealerList(scope.row.merchantId)">编辑</el-button>
                <el-button size="mini" type="success" @click="boundClub(scope.row.merchantCode)">绑定俱乐部</el-button>
                <el-button
                  size="mini"
                  type="danger"
                  v-if="isAdmin && scope.row.isEnable == 1 && checkPermission(['b:schoolList:enableAndDisable'])"
                  @click="disableAndOpenBtn(scope.row, 2)"
                >
                  <div style="display: flex; justify-content: center; align-items: center">
                    <img style="width: 10px; height: 10px; margin-right: 4px" src="./images/close.png" alt="" />
                    禁用
                  </div>
                </el-button>
                <el-button
                  size="mini"
                  type="success"
                  v-if="isAdmin && scope.row.isEnable == 2 && checkPermission(['b:schoolList:enableAndDisable'])"
                  @click="disableAndOpenBtn(scope.row, 1)"
                >
                  <div style="display: flex; justify-content: center; align-items: center">
                    <img style="width: 10px; height: 10px; margin-right: 4px" src="./images/open.png" alt="" />
                    启用
                  </div>
                </el-button>
                <el-button v-if="havaChannelManagerPermission || isChannelManager" type="primary" @click="editChannelManager(scope.row)">编辑渠道经理</el-button>
                <!-- <el-button size="mini" type="warning" v-if="scope.row.isCheck == 0" @click="examine(scope.row)">审核</el-button> -->
                <!-- <el-button v-handle="'brand:discount'" size="mini" type="success" v-if="scope.row.paymentStatus == 0 && scope.row.isCheck == 1" @click="Deduction(scope.row)">
                  抵扣
                </el-button> -->
                <!-- <el-button
                  size="mini"
                  type="success"
                  v-if="scope.row.paymentStatus == 0 && scope.row.isCheck == 1"
                  @click="Pay(scope.row.merchantId)"
                  >完款</el-button
                > -->
                <!-- <el-button v-handle="'brand:switch'" size="mini" v-if="scope.row.disabled == 0 && scope.row.isCheck == 1" type="danger" @click="suspend(scope.row)">暂停</el-button>
                <el-button v-handle="'brand:switch'" size="mini" v-if="scope.row.disabled == 1 && scope.row.isCheck == 1" type="primary" @click="enable(scope.row.merchantId)">
                  启用
                </el-button> -->
              </div>
            </template>
          </el-table-column>
        </el-table>
        <!-- 分页 -->
        <el-col :span="20">
          <el-pagination
            :current-page="page_data.page"
            :page-sizes="[10, 20, 30, 40, 50]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="page_data.total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
          <!-- <el-pagination :current-page="1" :page-sizes="[10, 20, 30, 40, 50]" layout="total, sizes, prev, pager, next, jumper" :total="100" /> -->
        </el-col>
        <!-- <Page :total="page_data.total" :page.sync="page_data.page" :limit.sync="page_data.pageSize" @pagination="index" /> -->
      </div>
      <el-dialog title="审核" :visible.sync="examinedialog" width="30%">
        <span>完成品牌信息审核？</span>
        <span slot="footer" class="dialog-footer">
          <el-button @click="exam(0)">驳 回</el-button>
          <el-button type="primary" @click="exam(1)">通 过</el-button>
        </span>
      </el-dialog>
      <el-dialog title="抵扣确认" :visible.sync="Deductiondialog" width="30%">
        <span>确定直接消耗邀请码库存？</span>
        <span slot="footer" class="dialog-footer">
          <el-button @click="Deductiondialog = false">取 消</el-button>
          <el-button type="primary" @click="Deduct">确 认</el-button>
        </span>
      </el-dialog>
      <el-dialog title="暂停确认" :visible.sync="suspenddialog" width="30%">
        <span>确认暂停该品牌账号的使用？暂停后品牌方则无法登录后台系统和小程序。</span>
        <span slot="footer" class="dialog-footer">
          <el-button @click="suspenddialog = false">取 消</el-button>
          <el-button type="primary" @click="suspendbtn">确 认</el-button>
        </span>
      </el-dialog>
      <!-- 批量绑定弹窗 -->
      <el-dialog title="批量绑定俱乐部" :visible.sync="batchBinddialog" width="40%">
        <div class="batch-bind-club" v-if="showClubList">
          <div class="batch-bind-club-item">
            <span>1.下载excel批量下载模板，填写信息</span>
            <div class="excel-img">
              <img src="../../assets/images/studyRoom/file.png" alt="" />
            </div>
            <el-button size="mini" icon="el-icon-download" v-loading="exportLoading" @click="exportList()">下载模板</el-button>
          </div>
          <div class="line"></div>
          <div class="batch-bind-club-item">
            <span>2.上传填写好的excel文件</span>
            <div class="excel-img">
              <img src="../../assets/images/studyRoom/file.png" alt="" />
            </div>
            <!-- :http-request="uploadFile" -->
            <el-upload
              action="#"
              :show-file-list="false"
              :limit="1"
              ref="upload"
              v-loading="upLoading"
              :before-upload="beforeAvatarUpload"
              :on-change="uploadExcel"
              :file-list="fileList"
              accept=".xlsx,.xls"
            >
              <el-button size="mini" icon="el-icon-upload2" type="primary">上传文件</el-button>
            </el-upload>
            <span class="s-font">请上传xls、xlsx文件，大小在5MB以内</span>
          </div>
        </div>

        <div id="club_list" v-else>
          <el-table :data="clubData" border style="width: 100%">
            <el-table-column prop="operationName" align="center" label="俱乐部名称" width="120"></el-table-column>
            <el-table-column prop="operationPhone" align="center" label="俱乐部账号" width="120"></el-table-column>
            <el-table-column prop="brandName" align="center" label="品牌名称" width="120"></el-table-column>
            <el-table-column prop="brandPhone" align="center" label="品牌账号" width="120"></el-table-column>
            <el-table-column prop="bindStatus" align="center" label="预绑定状态" width="120">
              <template v-slot="{ row }">
                <span v-if="row.checkStatus == 1" style="color: #13d188">成功</span>
                <span v-if="row.checkStatus == 0">失败</span>
              </template>
            </el-table-column>
            <el-table-column prop="errorMsg" align="center" label="失败原因" width="200">
              <template v-slot="{ row }">
                <span>{{ row.errorMsg }}</span>
              </template>
            </el-table-column>
          </el-table>
          <div class="club-pages">
            <span style="width: 30%; padding-top: 26px">共计{{ page_data2.total }}条，成功{{ clubSucceedCount }}条，失败{{ clubFailCount }}条</span>
            <Page :total="page_data2.total" :page.sync="page_data2.pageNum" :limit.sync="page_data2.pageSize" style="width: 80%" @pagination="getBindInfo" />
          </div>
          <div class="club-foot">
            <el-button size="small" @click="closeBing">取消</el-button>
            <el-button size="small" type="primary" @click="confirmBing">确定</el-button>
          </div>
        </div>
      </el-dialog>
      <!-- 单个绑定 -->
      <el-dialog title="绑定俱乐部" :visible.sync="singledialog" width="30%">
        <div>
          <span style="margin-right: 10px">选择俱乐部</span>
          <el-autocomplete
            clearable
            @clear="setBlur()"
            @input="handle"
            :fetch-suggestions="querySearch"
            :trigger-on-focus="false"
            @select="handleSelect"
            class="inline-input"
            v-model="searchInfo.merchantName"
            placeholder="输入俱乐部名称"
          ></el-autocomplete>
        </div>
        <div slot="footer">
          <el-button @click="closeSingle">取 消</el-button>
          <el-button type="primary" @click="singleBind">确 定</el-button>
        </div>
      </el-dialog>

      <!--   编辑渠道经理   -->
      <EditChannelManager ref="channelManager" :isShowChannelManager="isShowChannelManager" @handleOuterClose="handleOuterClose" />
    </el-card>
  </div>
</template>
<script>
  import store from '@/store';
  import { dxSource } from '@/utils/constants';
  import brandApi from '@/api/brands';
  // import { fetchMerchantList, fetchMerchantAudit, merchantPayment, merchantDeduction, partnerSuspend } from '@/api/admin/commissionManagement';
  import Page from '@/components/Pages/pages.vue';
  import { param2Obj } from '@/utils/index.js';
  import checkPermission from '@/utils/permission';
  import { lo } from 'pinyin/data/dict-zi-web';
  import { dir } from 'jszip/lib/defaults';
  import EditChannelManager from '@/views/merchantManagement/components/editChannelManager.vue';

  export default {
    name: 'activity',
    components: {
      EditChannelManager,
      Page
    },
    data() {
      return {
        restaurants: [],
        club: '',
        list: [],
        loading: false,
        examinedialog: false,
        Paydialog: false,
        Deductiondialog: false,
        suspenddialog: false,
        batchBinddialog: false,
        showClubList: true,
        singledialog: false,
        setpayUrl: store.getters.setpayUrl,
        examineData: {},
        page_data: {
          pageSize: 10,
          page: 1,
          total: 0
        },
        searchForm: {
          merchantCode: '',
          phone: '',
          merchantName: '',
          tOpenSuccess: ''
        },
        exportLoading: false,
        currentPage3: 3,
        clubData: [],
        fileList: [],
        clubFailCount: 0,
        clubSucceedCount: 0,
        page_data2: {
          pageSize: 10,
          pageNum: 1,
          total: 0
        },
        searchInfo: {
          isBind: 0,
          merchantName: ''
        },
        processingId: '',
        upLoading: false,
        brandCode: '',
        merchantCode: '',
        isShowChannelManager: false
      };
    },
    watch: {
      batchBinddialog() {
        this.showClubList = true;
      }
    },
    computed: {
      havaChannelManagerPermission() {
        return checkPermission(['admin', 'MerchantManager']);
      },
      isAdmin() {
        return checkPermission(['admin']);
      },
      isChannelManager() {
        return checkPermission(['channelManager']);
      }
    },
    methods: {
      checkPermission,
      parseTime1(time, pattern = 'YYYY-MM-DD HH:mm:ss') {
        if (!time) return '';
        //   console.log(time);
        const date = new Date(time); // 假设 time 是秒级时间戳
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从0开始，所以加1
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        const seconds = String(date.getSeconds()).padStart(2, '0');
        // 根据传入的 pattern 返回格式化的字符串
        return pattern.replace('YYYY', year).replace('MM', month).replace('DD', day).replace('HH', hours).replace('mm', minutes).replace('ss', seconds);
      },
      //新增操作
      clickAdd() {
        const that = this;
        window.localStorage.setItem('addBrand', JSON.stringify(true));
        window.localStorage.removeItem('brandId');
        that.$router.push({
          path: '/merchantManagement/addbrandList',
          query: {
            addOrUpdate: true
          }
        });
      },
      exportList() {
        const link = document.createElement('a');
        link.href = 'https://document.dxznjy.com/applet/model/批量绑定模版.xlsx';
        link.download = '批量绑定模版.xlsx'; // 指定下载后的文件名
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      },
      batchBind() {
        console.log('批量绑定俱乐部');
        let that = this;
        that.batchBinddialog = true;
      },
      //下载文件
      singleBind() {
        let data = {
          operationCode: this.merchantCode,
          brandCode: this.brandCode
        };
        brandApi.bindBrand(data).then((res) => {
          if (res.success) {
            this.singledialog = false;
            this.$message.success('绑定成功');
            this.searchInfo.merchantName = '';
          }
        });
      },
      //上传文件
      // uploadFile() {
      //   let that = this;
      //   that.showClubList = false;
      // },
      // uploadFile({ file }) {
      //   const formData = new FormData();
      //   formData.append('file', file);
      //   brandApi.bindBrandCheck(formData).then((res) => {
      //     if (res.success) {
      //       this.showClubList = false;
      //       checkPage().then((res) => {
      //         console.log(res);

      //       })
      //     }
      //   })

      // },
      uploadExcel(file, fileList) {
        // console.log(file, '>>>>>>>>>>>>>>>>>>>>');
        if (this.upLoading) return;
        this.upLoading = true;
        const fileTemp = file.raw;
        const fileName = file.raw.name;
        const fileType = fileName.substring(fileName.lastIndexOf('.') + 1);
        console.log(file.size / 1024 / 1024, '>>>>>>>>>>>>>>>>>>');

        if (file.size < 5 * 1024 * 1024) {
          if (fileTemp && (fileType === 'xlsx' || fileType === 'xls')) {
            const formData = new FormData();
            formData.append('file', file.raw);
            console.log(formData);

            brandApi
              .bindBrandCheck(formData)
              .then((res) => {
                this.showClubList = false;
                this.upLoading = false;
                if (res.success) {
                  this.processingId = res.data;
                  this.getBindInfo(res.data);
                } else {
                  this.$message.warning(res.message);
                }
              })
              .catch((err) => {
                this.upLoading = false;
              });
          } else {
            this.$message.warning('附件格式错误，请重新上传！');
            this.upLoading = false;
          }
        } else {
          this.$message.error('文件大小不能超过5MB!');
          this.upLoading = false;
        }
      },

      getBindInfo(id) {
        let data = {
          processingId: id ?? this.processingId
        };
        Object.assign(data, this.page_data2);
        brandApi.checkPage(data).then((res) => {
          this.clubData = res.data.records;
          this.page_data2.total = res.data.totalItems * 1;
          this.clubSucceedCount = res.data.succeedCount;
          this.clubFailCount = res.data.failCount;
        });
      },

      beforeAvatarUpload(res) {
        const isExcel = /\.(xlsx)$/.test(res.name);
        if (!isExcel) {
          toast.error('只能读取.xlsx文件,请另存为.xlsx文件!');
          return false;
        }
        const fileSuffix = res.name.substring(res.name.lastIndexOf('.') + 1);
        const list = ['exe'];
        if (list.indexOf(fileSuffix) >= 0) {
          toast.error(list.toString() + '文件类型限制传入!');
          return false;
        }
        console.log(res.size / 1024 / 1024, '>>>>>>>>>>>>>>>>>>');

        if (res.size / 1024 / 1024 > 5) {
          this.$message.error('文件大小不能超过5MB!');
          return false;
        }
        return true;
      },

      handleSizeChange2(val) {
        this.page_data2.pageSize = val;
        this.getBindInfo();
      },
      handleCurrentChange2(val) {
        this.page_data2.pageNum = val;
        this.getBindInfo();
      },
      confirmBing() {
        let that = this;
        // 调接口
        let data = {
          processingId: this.processingId
        };
        brandApi.determineBindBrand(data).then((res) => {
          if (res.success) {
            that.batchBinddialog = false;
            that.$message.success('批量绑定成功');
          } else {
            that.batchBinddialog = false;
            that.$message(res.message);
          }
        });
      },
      closeBing() {
        let that = this;
        that.searchInfo.merchantName = '';
        that.batchBinddialog = false;
      },
      boundClub(id) {
        let that = this;
        this.searchInfo.merchantName = ''; //清空输入框
        that.singledialog = true;
        this.brandCode = id;
      },

      /**
       * 禁用和开启
       */
      disableAndOpenBtn(row, number) {
        this.$confirm(number == 2 ? '禁用后，该品牌将无法登陆，收益将变为无限账期，无法提现，请确认是否禁用该品牌?' : '是否启用该品牌？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            const params = {
              id: row.merchantId,
              isEnable: number
            };
            brandApi
              .disableBrandApi(params)
              .then(() => {
                this.$message.success('操作成功');
                this.index();
              })
              .catch((error) => {
                console.error('🚀🥶💩~ error', error);
              });
          })
          .catch((error) => {
            console.error('🚀🥶💩~ error', error);
            this.$message({
              type: 'info',
              message: '已取消'
            });
          });
      },

      /**
       * 编辑渠道经理
       */
      editChannelManager(row) {
        this.isShowChannelManager = true;
        this.$nextTick(() => {
          this.$refs['channelManager'].setData(row);
        });
      },

      closeSingle() {
        this.searchInfo.merchantName = '';
        this.singledialog = false;
      },
      // 模糊事件
      searchClub() {
        // this.restaurants = this.loadAll();
      },

      setBlur() {
        //  在点击由 clearable 属性生成的清空按钮时，主动触发失去焦点，解决‘fetch-suggestions’输入建议不提示的bug
        document.activeElement.blur();
      },
      // 清空输入框页面重置
      handle(val) {
        if (val === '') {
          this.getData(); // 页面重置的代码
        }
      },
      // 过滤项目和class
      async querySearch(queryString, cb) {
        if (queryString && queryString.length > 0) {
          this.searchInfo.merchantName = queryString;
          try {
            const data = await brandApi.operationsList(this.searchInfo); // search定义在data里
            // 赋值给建议列表，渲染到页面
            var list = data.data;
            // 如果list.length等于0，则代表没有匹配到结果。手动给list添加一条提示信息
            if (!this.searchInfo.merchantName) {
              list.push({
                id: '-1',
                value: '无匹配结果'
              });
              // 调用 callback 返回建议列表的数据
              console.log(list, '>>>>>>>>>>>>>>>>>>>>');

              cb(list);
            } else {
              list = list.map((item) => {
                return {
                  value: `${item.merchantName}`,
                  id: `${item.merchantCode}`
                };
              });
              list = list.filter((item) => {
                return item.value.indexOf(this.searchInfo.merchantName) > -1;
              });
              // 调用 callback 返回建议列表的数据
              cb(list);
            }
          } catch (error) {
            console.log(error);
          }
        }
      },

      // loadAll() {
      //   brandApi.operationsList(this.searchInfo).then((res)=>{
      //     return res.data
      //   })
      // },
      // querySearch(queryString, cb) {
      //   var restaurants = (this.restaurants = this.loadAll());
      //   var results = queryString ? restaurants.filter(this.createFilter(queryString)) : restaurants;
      //   // 调用 callback 返回建议列表的数据
      //   cb(results);
      // },
      // createFilter(queryString) {
      //   return (restaurant) => {
      //     return restaurant.value.toLowerCase().indexOf(queryString.toLowerCase()) === 0;
      //   };
      // },
      handleSelect(item) {
        console.log(item, '....................');

        this.merchantCode = item.id;
      },

      filterTime(res) {
        // return parseTime1(res.applyTime);
        return res.applyTime;
      },
      //编辑
      updateDealerList(id, resubmit) {
        window.localStorage.setItem('brandId', id);
        window.localStorage.setItem('addBrand', JSON.stringify(false));
        const that = this;
        that.$router.push({
          path: '/merchantManagement/addbrandList',
          query: {
            addOrUpdate: false,
            id: id
          }
        });
      },
      // async Pay(merchantId) {
      //   // this.Paydialog = true;
      //   let res = await merchantPayment({ merchantId });
      //   if (res.success) {
      //     const split = dxSource.split('##');
      //     const token = res.data.token;
      //     res.data.order.dxSource = res.data.order.registerCallIInfo.appSource + '##' + split[1] + '##' + split[2];
      //     let params = JSON.stringify(res.data.order);
      //     let req = 'token=' + token + '&params=' + params + '&back=' + window.location.href;
      //     //需要编码两遍，避免出现+号等
      //     var encode = Base64.encode(Base64.encode(req));
      //     window.open(this.setpayUrl + '/product?' + encode, '_blank');
      //   }
      // },
      index() {
        console.log(this.$route.fullPath, 999, param2Obj(this.$route.fullPath));
        let param = {
          pageSize: this.page_data.pageSize,
          pageNum: this.page_data.page
        };
        Object.assign(param, this.searchForm);
        Object.assign(param, param2Obj(this.$route.fullPath));
        this.loading = true;
        brandApi.fetchMerchantList(param).then((res) => {
          this.list = res.data.data ? res.data.data : [];
          this.page_data.total = res.data.data ? Number(res.data.totalItems) : 0;
          this.loading = false;
          console.log(res, 888, this.list);
        });
      },
      // 抵扣
      async Deduct() {
        // const loading = this.$loading({
        //   lock: true,
        //   text: '抵扣中...',
        //   spinner: 'el-icon-loading',
        //   background: 'rgba(0, 0, 0, 0.7)'
        // });
        // let merchantId = this.examineData.merchantId;
        // try {
        //   await merchantDeduction({ merchantId });
        //   this.$message.success('操作成功');
        //   this.index();
        //   loading.close();
        //   this.Deductiondialog = false;
        // } catch (error) {
        //   loading.close();
        // }
      },
      // 暂停
      async suspendbtn() {
        // const loading = this.$loading({
        //   lock: true,
        //   text: '暂停中...',
        //   spinner: 'el-icon-loading',
        //   background: 'rgba(0, 0, 0, 0.7)'
        // });
        // try {
        //   await partnerSuspend({ merchantId: this.examineData.merchantId, disabled: 1 });
        //   this.$message.success('操作成功');
        //   this.index();
        //   this.suspenddialog = false;
        // } catch (error) {}
        // loading.close();
      },
      // 启用
      async enable(merchantId) {
        // const loading = this.$loading({
        //   lock: true,
        //   text: '启用中...',
        //   spinner: 'el-icon-loading',
        //   background: 'rgba(0, 0, 0, 0.7)'
        // });
        // try {
        //   await partnerSuspend({ merchantId, disabled: 0 });
        //   this.$message.success('操作成功');
        //   this.index();
        //   this.suspenddialog = false;
        // } catch (error) {}
        // loading.close();
      },
      async exam(i) {
        // let obj = {
        //   merchantId: this.examineData.merchantId,
        //   merchantType: '3'
        // };
        // if (i == 0) {
        //   obj.isCheck = 2;
        // } else {
        //   obj.isCheck = 1;
        // }
        // await fetchMerchantAudit(obj);
        // this.$message.success('审核成功');
        // this.examinedialog = false;
        // this.index();
      },
      //提交搜索
      onSubmit() {
        this.page_data.page = 1;
        this.index();
      },
      //重置
      onreset() {
        (this.searchForm = {
          merchantCode: '',
          phone: '',
          merchantName: '',
          channelManagerName: '',
          isEnable: ''
        }),
          (this.page_data = {
            pageSize: 10,
            page: 1,
            total: 0
          });
        this.index();
      },
      //审核
      examine(row) {
        this.examinedialog = true;
        this.examineData = row;
      },
      //抵扣
      Deduction(row) {
        this.Deductiondialog = true;
        this.examineData = row;
      },
      //暂停
      suspend(row) {
        this.suspenddialog = true;
        this.examineData = row;
      },
      handleSizeChange(val) {
        this.page_data.pageSize = val;
        this.index();
      },
      handleCurrentChange(val) {
        this.page_data.page = val;
        this.index();
      },
      handleOuterClose(val) {
        this.isShowChannelManager = false;
        val && this.index();
      }
    },
    mounted() {
      this.index();
    }
  };
</script>
<style lang="scss">
  .el-loading-spinner i,
  .el-loading-spinner .el-loading-text {
    color: rgb(26, 155, 103);
    font-size: 12px;
    padding-top: 3px;
  }

  .el-loading-spinner i {
    font-size: 18px;
  }

  .success {
    background-color: rgb(26, 155, 103);
  }

  .el-checkbox__input.is-checked .el-checkbox__inner,
  .el-checkbox__input.is-indeterminate .el-checkbox__inner {
    background-color: #1a9b67;
    border-color: #1a9b67;
  }

  .lf .el-button:focus,
  .lf .el-button:hover {
    background-color: transparent;
  }

  .main_select {
    margin-top: 8px;
    font-family: 'Source Han Sans SC';
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    font-size: 16px;
    color: #111;
    font-weight: bold;
    position: relative;
  }

  .main_select::before {
    margin-right: 8px;
    content: '';
    display: block;
    width: 6px;
    height: 16px;
    background-color: #0e6457;
  }

  .s-font {
    margin-top: 10px;
    font-size: 12px;
    color: #3a3939;
  }

  .club-pages {
    margin-top: 10px;
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .club-foot {
    margin-top: 10px;
    display: flex;
    justify-content: right;
    align-items: center;
  }

  .batch-bind-club {
    display: flex;
    justify-content: space-evenly;
    align-content: center;

    .line {
      width: 1px;
      height: 300px;
      background-color: #797979;
    }

    .batch-bind-club-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-direction: column;
      height: 100px;

      .excel-img {
        margin-top: 30px;
        margin-bottom: 30px;
        width: 150px;
        height: 150px;
      }

      .excel-img img {
        width: 150px;
        height: 150px;
      }
    }
  }
</style>
