<template>
  <div>
    <el-card class="frame" shadow="never">
      <el-form label-width="80px" :inline="true">
        <!-- 1 -->
        <el-row>
          <el-col :span="5" :xs="24">
            <el-form-item label="姓名:">
              <el-input clearable v-model="suggestList.name" placeholder="请输入姓名"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="5" :xs="24">
            <el-form-item label="联系方式:">
              <el-input clearable v-model="suggestList.phone" placeholder="请输入联系方式"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item label="反馈软件:">
              <el-select clearable v-model="suggestList.source" style="width: 9vw" placeholder="请选择">
                <el-option label="鼎校甄选" :value="1"/>
                <el-option label="鼎英语PC" :value="2"/>
                <el-option label="鼎英语PAD" :value="3"/>
                <el-option label="鼎英语学习机" :value="4"/>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8" :xs="24">
            <el-form-item label="反馈时间:" clearable>
              <el-date-picker v-model="dateArr" format="yyyy-MM-dd" value-format="yyyy-MM-dd" type="daterange"
                range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" style="width:90%">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="6" :xs="24">
            <el-button type="primary" icon="el-icon-search" @click="searchBtn" size="small">查询</el-button>
            <el-button icon="el-icon-search" @click="rest()" size="small">重置</el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>


    <el-table :data="tableList" style="width: 100%" id="out-table" :header-cell-style="getRowClass"
      :cell-style="{ 'text-align': 'center' }">
      <el-table-column prop="userCode" width="120px" label="编号" header-align="center"></el-table-column>
      <el-table-column prop="name" label="姓名" header-align="center">
        <template v-slot="{ row }">
          <span v-if="row.name">{{ row.name }}</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column prop="source" label="反馈板块" header-align="center">
        <template v-slot="{ row }">
          <span v-if="row.source === 1">鼎校甄选</span>
          <span v-else-if="row.source === 2">鼎英语PC</span>
          <span v-else-if="row.source === 3">鼎英语PAD</span>
          <span v-else-if="row.source === 4">鼎英语学习机</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column prop="phone" label="联系方式" header-align="center">
        <template v-slot="{ row }">
          <span v-if="row.phone">{{ row.phone }}</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="反馈提交时间" header-align="center"></el-table-column>
      <el-table-column prop="suggest" label="反馈内容"  width="600px"  header-align="center"></el-table-column>
    </el-table>
    <el-row type="flex" justify="center" align="middle" style="height: 60px">
      <!-- 3个变量：每页数量、页码数、总数  -->
      <!-- 2个事件：页码切换事件、每页数量切换事件-->
      <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
        :current-page="suggestList.pageNum" :page-sizes="[10, 20, 50]" :page-size="suggestList.pageSize"
        layout="total, sizes, prev, pager, next, jumper" :total="total">
      </el-pagination>
    </el-row>
  </div>
</template>

<script>
import {getSuggestList} from "@/api/studyroom/studentList"
export default {
  name: "suggest",
  data() {
    return {
      screenWidth: window.screen.width,
      suggestList: {
        name: '',
        phone: '',
        pageNum: 1,
        pageSize: 10,
        startTime: '',
        endTime:"",
        source:"",
      },
      tableList: [],
      total: null,
      dateArr: [],
    }
  },
  created() {
    this.initData();
    this.screenWidth = window.screen.width;
  },
  methods: {
    // 分页
    handleSizeChange(val) {
      this.suggestList.pageSize = val;
      this.initData()
    },
    handleCurrentChange(val) {
      this.suggestList.pageNum = val;
      this.initData()
    },
    async initData() {
      if (this.dateArr && this.dateArr.length > 0) {
        this.suggestList.startTime = this.dateArr[0]
        this.suggestList.endTime = this.dateArr[1]
      } else {
        this.suggestList.startTime = ''
        this.suggestList.endTime = ''
      }
      let { data } = await getSuggestList(this.suggestList.pageNum,this.suggestList.pageSize,this.suggestList)
      this.tableList = data.data
      this.total = Number(data.totalItems)
    },
    getRowClass({ rowIndex, columnIndex }) {
      if (rowIndex == 0) {
        return "background:#f5f7fa";
      }
    },
    //重置
    rest() {
      this.suggestList.name = '';
      this.suggestList.phone = '';
      this.dateArr = '';
      this.suggestList.source = '';
      this.initData()
    },
    async searchBtn() {
      await this.initData()
    },
  }
}
</script>

<style lang="scss">
.frame {
  padding-top: 13px;
  //margin: 15px;
}
</style>
