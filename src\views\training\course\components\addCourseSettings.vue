<template>
  <div class="add-video-dialog">
    <el-dialog width="70%" :visible.sync="dialogParam.visible" @closed="closeAddDialog" :close-on-click-modal="false" @submit.native.prevent>
      <div slot="title" class="dialog-title">
        <span v-if="dialogParam.type === 'add'">新增课程</span>
        <span v-if="dialogParam.type === 'edit'">编辑课程</span>
        <span v-if="dialogParam.type === 'detail'">课程详情</span>
      </div>
      <div>
        <el-form :model="labelForm" ref="addForm" :disabled="dialogParam.type === 'detail'" label-width="120px" label-position="right" :rules="formRules">
          <!-- 基础设置 -->
          <div class="content"><span class="contentTxt">基础设置</span></div>
          <el-form-item label="课程名称：" prop="courseName">
            <el-input style="width: 30%" placeholder="请输入课程名称" maxlength="100" v-model="labelForm.courseName"></el-input>
          </el-form-item>
          <el-form-item label="考核人员：" prop="examinee" required>
            <el-select style="width: 30%" v-model="labelForm.examinee" @change="getExaminee" multiple placeholder="请选择可以学习该课程的角色">
              <el-option v-for="item in examinerList" :key="item.id" :label="item.rname" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="封面上传：" prop="courseCover" required>
            <my-upload @handleSuccess="handlePosterSuccess" @handleRemove="handlePosterRemove" :fullUrl="true" :file-list="fileList" :imgSize="500 * 1024" />
          </el-form-item>
          <el-form-item label="课程简介：" prop="courseOverview">
            <el-input type="textarea" placeholder="请输入课程简介" v-model="labelForm.courseOverview" maxlength="255" show-word-limit rows="6" style="width: 60%"></el-input>
          </el-form-item>
          <!-- 课时设置 -->
          <div class="content"><span class="contentTxt">课时设置</span></div>
          <el-form-item prop="courseClass" label="课程课时：">
            <el-button type="primary" size="mini" :disabled="labelForm.lessonList.length == 30" @click="addLesson">添加课时</el-button>
          </el-form-item>
          <el-table :data="labelForm.lessonList" border style="width: 100%; margin-bottom: 10px">
            <el-table-column width="180" align="center" prop="">
              <template slot="header">
                <span class="required">*</span>
                <span class="label">课时名称</span>
              </template>
              <template slot-scope="scope">
                <el-form-item label-width="0" :prop="'lessonList.' + scope.$index + '.' + 'lessonName'" :rules="tableRules.lessonName">
                  <el-input v-model="scope.row.lessonName" maxlength="30" :placeholder="`课时名称${scope.$index + 1}`"></el-input>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column width="160" align="center">
              <template slot="header">
                <span class="required">*</span>
                <span class="label">课件类型</span>
              </template>
              <template slot-scope="scope">
                <el-form-item label-width="0" :prop="'lessonList.' + scope.$index + '.' + 'lessonType'" :rules="tableRules.lessonType">
                  <el-select v-model="scope.row.lessonType" placeholder="请选择" @change="handleLessonTypeChange(scope.$index)">
                    <el-option label="视频" :value="1"></el-option>
                    <el-option label="文本" :value="2"></el-option>
                  </el-select>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column align="center">
              <template slot="header">
                <span class="required">*</span>
                <span class="label">课件上传</span>
                <el-tooltip class="item" effect="dark" content="课件上传支持视频、文本文件上传。视频文件主要是播放为主，文本文件主要是查看为主" placement="top-start">
                  <span class="el-icon-info"></span>
                </el-tooltip>
              </template>
              <template slot-scope="scope">
                <el-form-item label-width="0" :prop="'lessonList.' + scope.$index + '.' + 'lessonFile.filePath'" :rules="tableRules.lessonFile">
                  <!-- 未选择课件类型时无法上传课件 -->
                  <el-button type="primary" size="small" v-if="scope.row.lessonType == '' && !scope.row.lessonFile.fileName" @click="disableChooseFile">选择课件</el-button>
                  <div v-else-if="scope.row.lessonFile.fileName">
                    <span class="fileName">{{ scope.row.lessonFile.fileName }}</span>
                    <el-button type="text" class="el-icon-delete" @click="delRowLessonFile(scope.$index)"></el-button>
                  </div>
                  <!-- 课件类型为文档 -->
                  <div v-else-if="scope.row.lessonType == 2">
                    <upload-file
                      btnTxt="选择课件"
                      :lessonIndex="scope.$index"
                      :fileList="scope.row.classFileList"
                      :accept="['pdf']"
                      @handleSuccess="handleSuccess"
                      @handleRemove="handleRemove"
                    ></upload-file>
                  </div>
                  <!-- 课件类型为视频 -->
                  <div v-else-if="scope.row.lessonType == 1">
                    <!-- :disabled="dialogParam.type === 'detail'" -->
                    <upload-video
                      ref="uploadVideoRef"
                      :lessonIndex="scope.$index"
                      :videoList="scope.row.videoList"
                      @videoRemove="videoRemove"
                      @videoSucceed="videoSucceed"
                    ></upload-video>
                  </div>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column label="学习资料" align="center">
              <template slot="header">
                <span class="label">学习资料</span>
                <el-tooltip class="item" effect="dark" content="学习资料仅支持zip、rar、7z、tar等压缩包上传" placement="top-start">
                  <span class="el-icon-info"></span>
                </el-tooltip>
              </template>
              <template slot-scope="scope">
                <el-form-item label-width="0">
                  <div v-if="scope.row.attachFile.fileName">
                    <span class="fileName">{{ scope.row.attachFile.fileName }}</span>
                    <el-button type="text" class="el-icon-delete" @click="delAttachFile(scope.$index)"></el-button>
                  </div>
                  <div v-else>
                    <upload-compressed-file
                      :fileList="scope.row.materialList"
                      :fullUrl="true"
                      :lessonIndex="scope.$index"
                      @handleSuccess="handleMaterialSuccess"
                      @handleRemove="handleMaterialRemove"
                    ></upload-compressed-file>
                  </div>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column label="学习资料下载" width="160" align="center">
              <template slot-scope="scope">
                <el-form-item label-width="0">
                  <el-select v-model="scope.row.isAllowDownloadDataFile" placeholder="请选择">
                    <el-option label="不支持" :value="0"></el-option>
                    <el-option label="支持" :value="1"></el-option>
                  </el-select>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column label="排序" width="180" align="center">
              <template slot-scope="scope">
                <el-form-item label-width="0">
                  <el-button type="text" :disabled="scope.$index == 0" @click="moveUpLesson(scope)">上移</el-button>
                  <el-button type="text" :disabled="scope.$index == labelForm.lessonList.length - 1" @click="moveDownLesson(scope)">下移</el-button>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center" width="120">
              <template slot-scope="scope">
                <el-form-item label-width="0">
                  <el-button size="mini" type="text" @click="handleDelete(scope.$index, scope.row)">删除</el-button>
                </el-form-item>
              </template>
            </el-table-column>
          </el-table>

          <!-- 高级设置 -->
          <div class="content"><span class="contentTxt">高级设置</span></div>
          <el-form-item prop="isMustLearn" label="必修/选修：">
            <el-radio-group v-model="labelForm.isMustLearn">
              <el-radio :label="1">必修</el-radio>
              <el-radio :label="0">选修</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item prop="paperConfigured" label="是否进行考试：">
            <el-radio-group v-model="labelForm.paperConfigured">
              <el-radio :label="1">是</el-radio>
              <el-radio :label="0">否</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button v-if="dialogParam.type === 'add' || dialogParam.type === 'edit'" v-loading="saveLoading" type="primary" @click="onSave">保 存</el-button>
        <el-button v-else-if="dialogParam.type === 'detail'" type="primary" @click="closeAddDialog('detail')">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import MyUpload from '@/components/Upload/MyUpload.vue';
  import UploadFile from '@/components/Upload/UploadFile.vue';
  import UploadVideo from '@/components/UploadVideo/singleUpload.vue';
  import UploadCompressedFile from '@/components/Upload/UploadCompressedFile.vue';
  import courseApi from '@/api/training/course';
  export default {
    name: 'AddCourseSettings',
    components: {
      MyUpload,
      UploadFile,
      UploadVideo,
      UploadCompressedFile
    },
    props: {
      dialogParam: {
        type: Object,
        default() {
          return {};
        }
      }
    },
    data() {
      const examineeValidate = (rule, value, callback) => {
        if (value instanceof Array && value.length == 0) {
          callback(new Error('请选择考核人员'));
        } else {
          callback();
        }
      };
      const posterValidate = (rule, value, callback) => {
        if (value === '') {
          callback(new Error('请上传封面'));
        } else {
          callback();
        }
      };
      return {
        saveLoading: false,
        fileList: [],
        labelForm: {
          courseName: '',
          examinee: [],
          courseCover: '',
          courseOverview: '',
          lessonList: [
            {
              lessonName: '',
              lessonFile: {
                filePath: '',
                fileName: ''
              },
              lessonType: '',
              attachFile: {
                filePath: '',
                fileName: ''
              },
              isAllowDownloadDataFile: 0,
              sort: 0,
              classFileList: [],
              videoList: [],
              materialList: []
            }
          ],
          isMustLearn: '',
          paperConfigured: ''
        },
        formRules: {
          courseName: [{ required: true, message: '请输入课程名称', trigger: 'blur' }],
          examinee: [{ validator: examineeValidate, trigger: 'change' }],
          courseCover: [{ validator: posterValidate, trigger: 'change' }],
          isMustLearn: [{ required: true, message: '请设置必修/选修', trigger: 'change' }],
          paperConfigured: [{ required: true, message: '请设置是否进行考试', trigger: 'change' }]
        },
        tableRules: {
          lessonName: [{ required: true, message: '请输入课时名称', trigger: 'blur' }],
          lessonType: [{ required: true, message: '请选择课件类型', trigger: 'change' }],
          lessonFile: [{ required: true, message: '请上传课件', trigger: 'change' }]
        },
        examinerList: []
      };
    },
    mounted() {
      this.getQueryRoles();
    },
    methods: {
      getExaminee(val) {
        this.labelForm.examinee = val;
      },
      getQueryRoles() {
        courseApi.dictQueryRoles().then((res) => {
          this.examinerList = res.data;
        });
      },
      open(id) {
        courseApi.courseDetail({ id: id }).then((res) => {
          this.fileList = [];

          this.labelForm = { ...this.labelForm, ...res.data };
          if (this.labelForm.courseCover) {
            this.fileList = [{ url: this.labelForm.courseCover }];
          }
          this.labelForm.paperConfigured = this.labelForm.needExam;
          this.labelForm.examinee = [];
          this.labelForm.examRoleList.forEach((item) => {
            this.labelForm.examinee.push(item.id);
          });
          this.labelForm.lessonList.forEach((item) => {
            item.classFileList = [];
            item.videoList = [];
            item.materialList = [];
            if (!item.attachFile) {
              item.attachFile = { filePath: '', fileName: '' };
            }
            if (!item.lessonFile) {
              item.lessonFile = { filePath: '', fileName: '' };
            }
          });
        });
      },
      // 封面上传成功、删除回调
      handlePosterSuccess(url) {
        this.labelForm.courseCover = url;
      },
      handlePosterRemove() {
        this.labelForm.courseCover = '';
      },
      // 添加课时
      addLesson() {
        const len = this.labelForm.lessonList.length;
        let pushSort = 0;
        if (len > 0) {
          pushSort = this.labelForm.lessonList[len - 1].sort + 1;
        }
        this.labelForm.lessonList.push({
          lessonName: '',
          lessonFile: {
            filePath: '',
            fileName: ''
          },
          lessonType: '',
          attachFile: {
            filePath: '',
            fileName: ''
          },
          isAllowDownloadDataFile: 0,
          sort: pushSort,
          classFileList: [],
          videoList: [],
          materialList: []
        });
      },
      // 删除课时
      handleDelete(index) {
        this.lessonList = [];
        this.labelForm.lessonList.splice(index, 1);
        // 重新校验删除后该行课时填写信息
        if (this.labelForm.lessonList.length != index) {
          this.$nextTick(() => {
            this.$refs.addForm.validateField([`lessonList.${index}.lessonName`, `lessonList.${index}.lessonType`, `lessonList.${index}.lessonFile.filePath`]);
          });
        }
      },
      // 上移课时
      moveUpLesson(scope) {
        let element = this.labelForm.lessonList[scope.$index];
        this.labelForm.lessonList.splice(scope.$index, 1);
        this.labelForm.lessonList.splice(scope.$index - 1, 0, element);
        // 交换sort值
        const tempSort = scope.row.sort;
        this.labelForm.lessonList[scope.$index - 1].sort = this.labelForm.lessonList[scope.$index].sort;
        this.labelForm.lessonList[scope.$index].sort = tempSort;
      },
      // 下移课时
      moveDownLesson(scope) {
        let element = this.labelForm.lessonList[scope.$index];
        this.labelForm.lessonList.splice(scope.$index, 1);
        this.labelForm.lessonList.splice(scope.$index + 1, 0, element);
        // 交换sort值
        const tempSort = scope.row.sort;
        this.labelForm.lessonList[scope.$index + 1].sort = this.labelForm.lessonList[scope.$index].sort;
        this.labelForm.lessonList[scope.$index].sort = tempSort;
        console.log(this.labelForm.lessonList);
      },
      // 课件类型变化
      handleLessonTypeChange(index) {
        this.labelForm.lessonList[index].lessonFile.filePath = '';
        this.labelForm.lessonList[index].lessonFile.fileName = '';
        this.labelForm.lessonList[index].classFileList = [];
        this.labelForm.lessonList[index].videoList = [];
      },
      // 删除已上传的课件
      delRowLessonFile(index) {
        this.labelForm.lessonList[index].lessonFile = {
          filePath: '',
          fileName: ''
        };
        this.labelForm.lessonList[index].classFileList = [];
        this.labelForm.lessonList[index].videoList = [];
      },
      disableChooseFile() {
        this.$message.warning('请先选择课件类型再上传课件！');
      },
      // 文本课件上传成功回调
      handleSuccess(res, fileName, file, lessonIndex) {
        console.log('res', res);

        this.labelForm.lessonList[lessonIndex].lessonFile.filePath = res;
        this.labelForm.lessonList[lessonIndex].lessonFile.fileName = fileName;
        // 校验是否上传
        this.$nextTick(() => {
          this.$refs.addForm.validateField(`lessonList.${lessonIndex}.lessonFile.filePath`);
        });
      },
      handleRemove(file, lessonIndex) {
        const index = this.labelForm.lessonList[lessonIndex].classFileList.findIndex((a) => a.url == file);
        if (index != -1) {
          this.labelForm.lessonList[lessonIndex].classFileList.splice(index, 1);
        }
      },
      // 学习资料上传成功回调
      handleMaterialSuccess(res, fileName, lessonIndex) {
        this.labelForm.lessonList[lessonIndex].attachFile.filePath = res;
        this.labelForm.lessonList[lessonIndex].attachFile.fileName = fileName;
        this.labelForm.lessonList[lessonIndex].materialList = [];
      },
      handleMaterialRemove(file) {
        console.log(file);
      },
      // 删除学习资料
      delAttachFile(index) {
        this.labelForm.lessonList[index].attachFile.filePath = '';
        this.labelForm.lessonList[index].attachFile.fileName = '';
        this.labelForm.lessonList[index].materialList = [];
      },
      // 上传视频课件成功回调
      videoSucceed(data, lessonIndex) {
        this.labelForm.lessonList[lessonIndex].videoList = [];
        data.forEach((item) => {
          this.labelForm.lessonList[lessonIndex].videoList.push({ fileName: item.fileName, filePath: item.filePath ? item.filePath : item.id });
          // if (!item.fileId && item.success) {
          //     this.videoRemove(item, 2)
          // }
        });
        this.labelForm.lessonList[lessonIndex].lessonFile.filePath = this.labelForm.lessonList[lessonIndex].videoList[0].filePath;
        this.labelForm.lessonList[lessonIndex].lessonFile.fileName = this.labelForm.lessonList[lessonIndex].videoList[0].fileName;

        this.labelForm.lessonList[lessonIndex].videoList = [];
        // 校验是否上传
        this.$nextTick(() => {
          this.$refs.addForm.validateField(`lessonList.${lessonIndex}.lessonFile.filePath`);
        });
      },
      videoRemove(info, key) {
        if (this.labelForm.id) {
          let param = {
            courseId: this.labelForm.id,
            operatorId: this.labelForm.operatorId
          };
          if (!key && !info.fileId) {
            return;
          }
          if (key == 2) {
            param.videoList = [
              {
                fileName: info.fileName,
                filePath: info.fileId ? info.filePath : info.id
              }
            ];
          } else {
            param.fileList = [
              {
                fileName: info.name,
                filePath: info.filePath
              }
            ];
          }
          if (info.fileId) {
            param.id = info.fileId;
          }
          // courseApi.courseAttachment(param).then(res => {
          // })

          // videoList的值
          this.videoList = this.labelForm.videoList.filter((item) => item.id !== info.id);
        }
      },
      // 保存课程
      onSave() {
        this.labelForm.lessonList.forEach((el) => {
          if (el.attachFile == null) {
            el.attachFile = { fileName: '', filePath: '' };
          }
        });
        this.$refs['addForm'].validate((valid) => {
          if (valid) {
            this.saveLoading = true;
            const params = { ...this.labelForm };
            params.lessonList = this.labelForm.lessonList.map((el) => {
              if (!el.attachFile.fileName && !el.attachFile.filePath) {
                el.attachFile = null;
              }
              const { classFileList, videoList, materialList, ...elRest } = el; // 文件列表不作入参提交
              return elRest;
            });
            // 角色字段处理
            params.examinee = this.labelForm.examinee.map((item) => {
              const index = this.examinerList.findIndex((a) => a.id == item);
              const targetItem = this.examinerList[index];
              return {
                id: targetItem.id,
                rname: targetItem.rname,
                rval: targetItem.rval
              };
            });

            courseApi
              .courseCreateOrUpdate(params)
              .then((res) => {
                this.$message.success('保存成功！');
                this.closeAddDialog();
              })
              .finally(() => {
                this.saveLoading = false;
              });
          }
        });
      },
      closeAddDialog(tag) {
        this.$emit('closeDialog', tag || '');
      }
    }
  };
</script>

<style scoped>
  ::v-deep .dialog-title {
    font-size: 22px;
  }
  .fileName {
    display: inline-block;
    margin-right: 4px;
  }
  .content {
    line-height: 30px;
    width: 100%;
    height: 30px;
    background-color: #f1f1f1;
    margin-bottom: 20px;
  }

  .contentTxt {
    margin-left: 10px;
    font-size: 16px;
  }

  .detailContent {
    width: 50%;
    display: inline-block;
    margin-bottom: 10px;
  }

  .downContent {
    margin-bottom: 10px;
  }
  .required {
    color: red;
    display: inline-block;
    margin-right: 4px;
  }
  ::v-deep .el-table__body .el-form-item {
    margin-bottom: 18px;
  }
  ::v-deep .el-table--medium td {
    padding-bottom: 0;
  }
  .dialog-footer {
    text-align: center;
  }
  .label {
    display: inline-block;
    margin-right: 4px;
  }
</style>
