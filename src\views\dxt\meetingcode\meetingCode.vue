<template>
  <div class="app-container">
    <!-- 新增按钮 -->
    <el-row class="container-card">
      <!-- <el-col :span="12">
        <el-col :span="6" class="marginbottom" style="line-height: 36px;">商户列表编号</el-col>
        <el-col :span="6">
          <el-input v-model="queryMerchantCode" name="id" placeholder="商户列表编号" />
        </el-col>
      </el-col> -->
      <el-col :span="24" style="text-align: right;">
        <el-button type="primary" icon="el-icon-search" @click="createCode()" size="mini">新增</el-button>
      </el-col>
    </el-row>


    <el-table v-loading="tableLoading" :data="tableData" border stripe style="width: 100%;margin-bottom: 30px;">
      <el-table-column prop="codeType" label="码的类型" align="center" />
      <el-table-column label="是否禁用" prop="isEnable" align="center">
        <template slot-scope="scope">
          <el-button :type="scope.row.isEnable == 1 ? 'primary' : 'danger'" size="mini" plain disabled>{{
            scope.row.isEnable == 1 ? '未禁用' : '禁用' }}</el-button>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" fixed="right">
        <template slot-scope="scope">
          <el-tooltip content="绑定退款码" placement="top">
            <el-button type="warning" @click="bang(scope.row.codeValue, scope.row.bindCodeValue)"
              size="mini">绑定退款码</el-button>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column prop="codeValue" label="二维码的值" align="center" />
      <el-table-column prop="codePath" label="码的图片链接" align="center" />
      <el-table-column prop="bindCodeValue" label="绑定退款码的值" align="center" :show-overflow-tooltip="true" />
      <el-table-column prop="createTime" label="创建时间" align="center" :show-overflow-tooltip="true" />
      <el-table-column label="会议名称" align="center" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <span>{{ meettingList[scope.row.bindCodeValue] }}</span>
          <!-- <el-select v-model="scope.row.bindCodeValue" disabled>
            <el-option v-for="item in meetingIdList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select> -->
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <el-col :span="20">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>

    <el-dialog :title="'绑定退款码'" :visible.sync="dialogVisible" width="70%" :close-on-click-modal="false" @close="close">
      <el-form :ref="'bingCodeDate'" :rules="rules" :model="bingCodeDate" label-position="left" label-width="120px">
        <el-row>
          <el-form-item label="物料码的码值" prop="itemCode">
            <el-input v-model="bingCodeDate.itemCode" disabled />
          </el-form-item>
          <el-col :span="24">
            <el-form-item label="会议退款码" prop="codeValue">
              <el-select v-model="bingCodeDate.codeValue" value-key="value" filterable placeholder="请选择"
                @change="changecodeValue(bingCodeDate.codeValue)">
                <el-option v-for="item in meetingIdList" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <div>
          </div>
        </el-row>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="warning" @click="close">关闭</el-button>
        <el-button size="mini" type="success" @click="bingCodeDateList('bingCodeDate')">确定</el-button>
      </div>
    </el-dialog>


  </div>
</template>

<script>
import meetingCodeApi from '@/api/dxt/meetingCode'
import {
  pageParamNames
} from '@/utils/constants'
// import { number } from 'echarts/lib/export'
// import meetingInvitationApi from '@/api/dxt/meetingInvitation'

export default {
  data() {
    return {
      meettingList: {},
      qrCodeCreate: {
        merchantCode: ''
      },
      signleCodeAddData: {
        count: ''
      },
      id: '',
      singleCodeData: {
        startDate: '',
        endDate: ''
      },
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      optionsAddressProps: {
        checkStrictly: true,
        value: 'name',
        label: 'name',
        children: 'children'
      },
      bingCodeDate: {
        codeValue: '',
        itemCode: ''
      },
      rules: {

      },
      updateMeetingInvitation: {},
      address: '',
      addressArray: [],
      optionsAddress: [],
      showAddressSelect: false,
      addressCreateLoad: false,
      queryMerchantCode: '',
      tableLoading: false,
      tableData: [],
      meetingIdList: [],
      dialogVisible: false,
      dialogRefereeVisible: false,   //生成0元订单弹窗
      addOrUpdate: false,
      dialogCodeVisible: false, // 校区二维码列表弹窗是否显示
      schoolCodeList: []
    }
  },
  created() {
    this.getList();
  },
  methods: {
    //新增物料二维码
    createCode() {
      this.$confirm('您确定要新增物料二维码？', '提示', confirm)
        .then(() => {
          meetingCodeApi.meetingCodeAdd().then(res => {
            this.$message.success("新增成功");
            this.fetchData();
          })
        })
        .catch(() => {
          this.$message.info('已取消新增')
        })
    },
    bang(codeValue, bindCodeValue) {
      const that = this;
      that.bingCodeDate.itemCode = codeValue;

      that.dialogVisible = true;
      that.bingCodeDate.codeValue = bindCodeValue;
      that.getList();
    },
    //获取码的值
    changecodeValue(ele) {
      console.log(ele);
    },
    //获取会议的退款码
    getList() {
      meetingCodeApi.getMeetingList().then(res => {
        this.meetingIdList = res.data;
        this.meetingIdList.forEach(i => {
          this.meettingList[i.value] = i.label;
        });
        this.fetchData();
      })
    },
    //绑定退款码
    bingCodeDateList(ele) {
      const that = this
      that.$refs[ele].validate((valid) => {
        // 表单验证
        if (valid) {
          const loading = this.$loading({
            lock: true,
            text: '绑定退款码',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          })
          meetingCodeApi
            .bingCode(that.bingCodeDate.itemCode, that.bingCodeDate.codeValue)
            .then(() => {
              that.dialogVisible = false
              loading.close()
              that.fetchData()
              that.$message.success('绑定退款码成功')
            })
            .catch((err) => {
              if (err === 'error') {
                that.$message.error('绑定退款码失败')
                loading.close()
              }
            })
        } else {
          that.$message.error('请填写必填项')
          console.log('error submit!!')
          loading.close();
          return false
        }
      })
    },
    close() {
      this.dialogVisible = false;
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val
      this.fetchData()
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val
      this.fetchData()
    },
    // 查询
    fetchData() {
      const that = this;
      meetingCodeApi.meetingCodeList(that.tablePage.currentPage, that.tablePage.size).then(res => {
        that.tableData = res.data.data
        that.tableLoading = false
        // 设置后台返回的分页参数
        pageParamNames.forEach(name => that.$set(that.tablePage, name, parseInt(res.data[name])))
      })
    },

  }

}

</script>

<style>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, .12), 0 0 6px rgba(0, 0, 0, .04)
}
</style>
