<template>
  <div class="app-container">
    <el-form :inline="true" class="container-card" label-width="96px">
      <el-form-item label="课程名称：">
        <el-select v-model="dataQuery.courseName">
          <el-option value="鼎英语" label="鼎英语"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="地区：">
        <el-cascader :options="addressOptions" :props="props" v-model="address" filterable></el-cascader>
      </el-form-item>
      <el-form-item label="录入时间：">
        <el-date-picker style="width: 100%;" v-model="createTime" type="daterange" value-format="yyyy-MM-dd HH:mm:ss"
          align="right" unlink-panels range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="状态：">
        <el-select v-model="dataQuery.status" filterable placeholder="请选择">
          <el-option label="已上架" value="1"></el-option>
          <el-option label="已下架" value="0"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="getList()">搜索</el-button>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-re" @click="resetQuery()">重置</el-button>
      </el-form-item>
    </el-form>
    <!-- 添加 -->
    <div class="btn-add" style="margin-bottom: 10px">
      <el-button size="small" type="primary" icon="el-icon-plus" @click="handleAdd">添加</el-button>
    </div>
    <el-table class="common-table" v-loading="tableLoading" :data="tableData" height="400px"
      style="width: 100%;margin-bottom: 20px;" row-key="id" border default-expand-all
      :tree-props="{ list: 'children', hasChildren: 'true' }">
      <el-table-column prop="id" label="课程ID"></el-table-column>
      <el-table-column prop="courseName" label="课程名称"></el-table-column>
      <el-table-column label="操作" align="center" width="280">
        <template slot-scope="scope">
          <el-button v-if="scope.row.status" type="danger" @click="handleChange(scope.row)" size="mini">下架</el-button>
          <el-button v-else type="warning" @click="handleChange(scope.row)" size="mini">上架</el-button>
          <el-button type="primary" @click="handleView(scope.row)" size="mini">查看</el-button>
          <el-button type="success" @click="handleUpload(scope.row)" size="mini">编辑</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="startTime" label="地区">
        <template slot-scope="scope">
          {{ scope.row.province }}-{{ scope.row.city }}
        </template>
      </el-table-column>
      <el-table-column prop="normalPic" label="阶段&价格">
        <template slot-scope="scope">
          <span>小学：{{ scope.row.primaryPrice }}元（1学时）</span><br>
          <span>初中：{{ scope.row.juniorPrice }}元（1学时）</span><br>
          <span>高中：{{ scope.row.seniorPrice }}元（1学时）</span>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="录入时间"></el-table-column>
      <el-table-column prop="status" label="状态">
        <template slot-scope="scope">
          <span v-if="scope.row.status">已上架</span>
          <span v-else>已下架</span>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog class="recharge-dialog" title="新建课程" :visible.sync="open" width="50%">
      <el-form ref="form" :model="form" label-width="96px" :rules="rules" style="width: 100%;" @close="reset">
        <el-form-item label="课程名称：" prop="courseName">
          <el-select v-model="form.courseName">
            <el-option value="鼎英语" label="鼎英语"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="地区：" prop="address">
          <el-cascader :options="addressOptions" :props="props" v-model="form.address" filterable></el-cascader>
        </el-form-item>
        <el-form-item label="价格：" prop="primaryPrice">
          <el-col :span="2"><el-radio>小学</el-radio></el-col>
          <el-col :span="6">
            <el-input-number v-model="form.primaryPrice" :min="0.01" :precision="2" :step="0.1"></el-input-number>
          </el-col>
          <el-col :span="2"><el-radio>初中</el-radio></el-col>
          <el-col :span="6">
            <el-input-number v-model="form.juniorPrice" :min="0.01" :precision="2" :step="0.1"></el-input-number>
          </el-col>
          <el-col :span="2"><el-radio>高中</el-radio></el-col>
          <el-col :span="4">
            <el-input-number v-model="form.seniorPrice" :min="0.01" :precision="2" :step="0.1"></el-input-number>
          </el-col>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button v-if="!isView" size="mini" type="primary" @click="handleSave()">确定</el-button>
        <el-button size="mini" @click="cancel()">关闭</el-button>
      </div>
    </el-dialog>

    <!-- 分页 -->
    <el-col :span="20" style="overflow-x: auto;" :xs="24">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>
  </div>
</template>

<script>

import coursePriceApi from '@/api/coursePrice'
import { pageParamNames } from "@/utils/constants";
export default {
  data() {
    return {
      isView: false,
      address: [],
      addressOptions: [],
      props: {
        value: 'name',
        label: 'name',
      },
      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      tableData: [],
      open: false,
      dataQuery: {
        status: null,
        courseName: null,
        addressCode: null,
        startTime: null,
        endTime: null
      },
      createTime: [],
      form: {},
      rules: {
        courseName: [
          { required: true, message: "请选择", trigger: "blur" }
        ],
        address: [
          { required: true, message: "请选择地区", trigger: "blur" }
        ],
        primaryPrice: [
          { required: true, message: "请输入价格", trigger: "blur" }
        ],
      }
    };
  },
  created() {
    coursePriceApi.getAllRegion().then(res => {
      this.addressOptions = res.data;
    });
    this.getList();
  },
  methods: {
    //上下架
    handleChange(row) {
      var status = 1;
      if (row.status) {
        status = 0;
      }
      coursePriceApi.coursePriceUpdateStatus(row.id, status).then(res => {
        this.getList();
        this.$message.success((row.status ? '下架' : '上架') + '成功!')
      })
    },
    handleSave() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.form.province = this.form.address[0]
          this.form.city = this.form.address[1]
          if (this.form.id === null) {
            coursePriceApi.coursePriceAdd(this.form).then(res => {
              this.$message.success('提交成功');
              this.open = false;
              this.getList();
            })
          } else {
            coursePriceApi.coursePriceUpdate(this.form).then(res => {
              this.$message.success('修改成功');
              this.open = false;
              this.getList();
            })
          }
        }
      });
    },
    testSave() {
      coursePriceApi.appraisal(this.form.id, arr).then(res => {
        this.open = false;
        this.getList();
        this.$message.success('增加成功!')
      }).catch(err => {
      })
    },
    //获取列表
    getList() {
      const that = this;
      var a = that.createTime;
      if (a != null) {
        that.dataQuery.startTime = a[0];
        that.dataQuery.endTime = a[1];
      } else {
        that.dataQuery.startTime = null;
        that.dataQuery.endTime = null;
      }
      that.tableLoading = true;
      var b = this.address;
      if (b != null) {
        that.dataQuery.province = b[0];
        that.dataQuery.city = b[1];
      } else {
        that.dataQuery.province = null;
        that.dataQuery.city = null;
      }
      coursePriceApi.coursePriceList(that.tablePage.currentPage, that.tablePage.size, that.dataQuery).then(res => {
        that.tableData = res.data.data
        that.tableLoading = false
        // 设置后台返回的分页参数
        pageParamNames.forEach(name => this.$set(this.tablePage, name, parseInt(res.data[name])))
      })
    },

    //重置按钮
    resetQuery() {
      this.createTime = [];
      this.address = [];
      this.dataQuery = {
        status: null,
        courseName: null,
        startTime: null,
        endTime: null,
        province: null,
        city: null
      };
      this.getList();
    },
    //添加按钮
    handleAdd() {
      this.reset();
      this.open = true;
      this.isView = false;
    },
    //查看按钮
    handleView(row) {
      this.reset();
      coursePriceApi.coursePriceDetail(row.id).then(response => {
        this.form = response.data;
        this.form.address = [this.form.province, this.form.city];
        this.open = true;
        this.isView = true;
      });
    },
    //编辑按钮
    handleUpload(row) {
      this.reset();
      coursePriceApi.coursePriceDetail(row.id).then(response => {
        this.form = response.data;
        this.form.address = [this.form.province, this.form.city];
        this.open = true;
        this.isView = false;
      });
    },
    reset() {
      this.form = {
        id: null,
        courseName: null,
        address: [],
        primaryPrice: null,
        juniorPrice: null,
        seniorPrice: null,
        province: null,
        city: null,
      };
      if (this.$refs['form']) {
        this.$refs['form'].resetFields();
      }
    },
    cancel() {
      this.open = false;
      this.reset();
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val;
      this.getList();
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val;
      this.getList();
    },
  }
};
</script>

<style>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

@media screen and (max-width:767px) {
  .recharge-dialog .el-dialog {
    width: 90% !important;
  }

  .el-message-box {
    width: 80% !important;
  }
}

.illegalPic {
  width: 50px;
  height: 50px;
  margin-right: 5px;
}
</style>
