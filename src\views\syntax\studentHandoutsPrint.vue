<template>
  <div class="app-container">
    <div ref="print">
      <body>
        <form id="print">
          <p v-html="handouts"></p>
        </form>
      </body>
    </div>
    <div class="buttom clearfix">
      <a style="background: #3dab93" @click="print()">打印</a>
      <a style="background: #f0ad4e; margin-left: 20px" @click="goBack()"
        >返回</a
      >
    </div>
  </div>
</template>

<script>
import { detailRowAPI } from "@/api/grammar/knowledgePoint";
import { updatePrintStatusAPI } from "@/api/grammar/phaseCredentialList";
import ls from "@/api/sessionStorage";
export default {
  data() {
    return {
      grammarId: "",
      handoutId: "",
      handouts: "",
    };
  },
  created() {
    this.grammarId = this.$route.query.grammarId;
    this.handoutId = this.$route.query.handoutId;
    this.getPrintClose();
  },
  methods: {
    getPrintClose() {
      detailRowAPI({ id: this.grammarId }).then((res) => {
        const handData = JSON.parse(res.data.note).noteData;
        const grammarName = res.data.name;

        if (handData && handData.content) {
          let formattedText = `<h2>${grammarName}</h2>\n`; // 添加语法名称作为标题

          handData.content.forEach((item) => {
            formattedText += `<h4>${item.topic}</h4>\n`;
            formattedText += `<p>${item.content.replace(/\n/g, "<br>")}</p>\n`;
          });

          this.handouts = formattedText;
        }
      });
    },

    print() {
      // this.$print(this.$refs.print)
      updatePrintStatusAPI({ id: this.handoutId, type: 2 }).then(() => {
        this.$print(this.$refs.print);
      });
    },
    //返回按钮
    goBack() {
      this.$store.dispatch("delVisitedViews", this.$route);
      this.$router.go(-1);
      this.$router.push({
        path: "/syntax/handoutsPrintList",
      });
    },
  },
};
</script>

<style scoped lang="scss">
.app-container {
  background-color: #f3f3f4;
}

.clearfix {
  zoom: 1;
}

.clearfix:before,
.clearfix:after {
  content: "";
  line-height: 0;
  display: table;
}

.clearfix:after {
  clear: both;
}

#print {
  width: 1124px;
  margin: 0 auto;
  position: relative;
  overflow: hidden;
}

.buttom {
  margin: 10px 50px 10px 0;
  color: #fff;
  cursor: pointer;
  text-align: right;
}

.buttom a {
  display: inline-block;
  color: #fff;
  width: 90px;
  height: 35px;
  border-radius: 5px;
  text-align: center;
  line-height: 35px;
}
</style>
