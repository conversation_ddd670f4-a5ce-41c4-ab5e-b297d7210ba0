import request from '@/utils/request'
export function sendContractVerifyCode(data) {
  return request({
    url: `znyy/contractInvolve/v2/sendContractVerifyCode?merchantCode=${data.merchantCode}`,
    method: 'POST',
    data
  });
}
export function verifyContractCode(data) {
  return request({
    url: `znyy/contractInvolve/v2/verifyContractCode?merchantCode=${data.merchantCode}` + `&code=${data.code}`,
    method: 'POST',
    data
  });
}

/**
 * 门店生成假合同
 *
*/
export function createFalseContract(data) {
  return request({
    url: `znyy/V2/merchant/createFalseContract?contractType=${data.contractType}`,
    method: 'POST',
    data
  });
}

/**
 * 俱乐部生成假合同
 *
*/
export function createFakeClubPersonContract(data) {
  return request({
    url: `znyy/operations/v2/createFakeClubPersonContract?contractType=${data.contractType}`,
    method: 'POST',
    data
  });
}
