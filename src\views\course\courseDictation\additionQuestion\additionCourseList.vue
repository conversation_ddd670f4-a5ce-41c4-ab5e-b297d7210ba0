<template>
  <div class="app-container">
    <!-- 搜索 -->
    <el-form :inline="true" ref="form" class="SearchForm" style="padding: 20px 30px 0" label-width="82px">
      <el-row>
        <el-col :span="8" :xs="24">
          <el-form-item label="课程ID:">
            <el-input v-model="searchData.courseCode" @keyup.enter.native="onSearch()" style="width: 200px" placeholder="请输入课程ID" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="课程名称:">
            <el-input v-model="searchData.courseName" @keyup.enter.native="onSearch()" style="width: 200px" placeholder="请输入课程名称" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="排序:">
            <el-input
              v-model="searchData.sortOrder"
              type="number"
              @input="validateSortNumInput"
              @keyup.enter.native="onSearch()"
              style="width: 200px"
              placeholder="请输入排序"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8" :xs="24">
          <el-form-item label="课程状态:">
            <el-select v-model="searchData.status" filterable value-key="value" placeholder="请选择" clearable>
              <el-option v-for="(item, index) in courceStatus" :key="index" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="等级分类:">
            <el-select v-model="searchData.courseLevel" filterable value-key="value" placeholder="请选择" clearable>
              <el-option v-for="(item, index) in courseLevelList" :key="index" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item>
            <el-button type="warning" size="small" icon="el-icon-search" @click="onSearch()">搜索</el-button>
            <el-button size="small" icon="el-icon-refresh" @click="rest()">重置</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <div class="SearchForm">
      <!-- 添加 -->
      <div class="btn-add">
        <el-button size="small" type="primary" icon="el-icon-plus" @click="getAddForm">添加</el-button>
      </div>
      <!-- 表格 -->
      <el-table class="common-table" :data="tableData" stripe border v-loading="tableLoading">
        <el-table-column prop="courseCode" label="课程ID" sortable></el-table-column>
        <el-table-column prop="sortOrder" label="排序" sortable :show-overflow-tooltip="true"></el-table-column>
        <el-table-column label="等级类型" :show-overflow-tooltip="true">
          <template slot-scope="{ row }">
            <div v-if="row.courseLevel">{{ courseLevelList.find((e) => e.value == row.courseLevel).label }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="courseName" label="课程名称" show-overflow-tooltip></el-table-column>
        <el-table-column prop="courseCover" label="课程封面" width="160">
          <template slot-scope="scope">
            <div class="demo-image__preview">
              <el-image v-if="scope.row.coverUrl" class="table_list_pic" :src="scope.row.coverUrl" @click="openImg(scope.row)"></el-image>
              <el-image v-else>
                <div slot="error" class="image-slot">
                  <i class="el-icon-picture-outline"></i>
                </div>
              </el-image>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="id" label="操作">
          <template slot-scope="{ row }">
            <el-button type="success" size="mini" icon="el-icon-edit-outline" @click="handleUpdate(row)">编辑</el-button>
            <el-button type="primary" size="mini" @click="courseMake(row)">制作课程</el-button>
            <el-button
              :type="row.status === 1 ? 'danger' : 'success'"
              :icon="row.status === 0 ? 'el-icon-video-play' : 'el-icon-video-pause'"
              size="mini"
              @click="courseStatus(row.id, row.status)"
            >
              {{ row.status === 1 ? '暂停' : '开通' }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- 分页 -->
    <el-col :span="20">
      <el-pagination
        :current-page="tablePage.currentPage"
        :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="totalItems"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-col>
    <!-- 编辑或者添加弹窗 -->
    <AdditionCourseAddForm :dialogVisible="dialogVisible" :updateId="updateId" @addDialogClose="addDialogClose"></AdditionCourseAddForm>
    <!-- 图片显示 -->
    <el-dialog title="" :visible.sync="dialogOpenimg" width="30%">
      <div class="coverimg">
        <el-image class="table_list_pic" :src="coverImg"></el-image>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import courseAdditionList from '@/api/courseAdditionList';
  import { mapGetters } from 'vuex';
  import AdditionCourseAddForm from '@/views/course/courseDictation/components/additionCourseAddForm.vue';
  export default {
    name: 'courseList',
    components: { AdditionCourseAddForm },
    data() {
      return {
        //课程状态
        courceStatus: [
          { value: 1, label: '开通' },
          { value: 0, label: '暂停' }
        ],
        courseLevelList: [
          { value: '0', label: '启蒙' },
          { value: '1', label: '基础' },
          { value: '2', label: '进阶' }
        ],
        searchData: {
          courseCode: '',
          courseName: '',
          sortOrder: '',
          courseLevel: '',
          status: ''
        },
        updateId: '',
        // 分页
        tablePage: {
          currentPage: 1,
          size: 10
        },
        totalItems: 0, //总条数
        tableLoading: false,
        tableData: [], //表格数据
        dialogVisible: false, // 修改弹窗是否展示
        coverImg: '',
        dialogOpenimg: false
      };
    },
    computed: {
      ...mapGetters(['roles'])
    },

    created() {
      if (window.localStorage.getItem('isJumpPY') && JSON.parse(window.localStorage.getItem('isJumpPY'))) {
        let searchDataTemp = window.localStorage.getItem('searchDataPY') ? JSON.parse(window.localStorage.getItem('searchDataPY')) : {};
        this.searchData = JSON.parse(JSON.stringify(searchDataTemp));
        this.tablePageTemp = window.localStorage.getItem('tablePagePY') ? JSON.parse(window.localStorage.getItem('tablePagePY')) : {};
        window.localStorage.removeItem('isJumpPY');
      }
      this.fetchData();
    },

    methods: {
      validateSortNumInput(event) {
        if (!/^[1-9]\d*$/.test(event)) {
          this.$message.error('请输入有效的数字');
          this.searchData.sortOrder = ''; // 清空输入框
        }
      },
      //新增弹窗关闭
      addDialogClose(val) {
        this.dialogVisible = val;
        this.updateId = '';
        this.fetchData();
      },

      //制作课程
      courseMake(data) {
        this.$router.push({
          name: 'makeAdditionCourseList',
          query: {
            courseData: JSON.stringify(data)
          }
        });
        window.localStorage.setItem('searchDataPY', JSON.stringify(this.searchData));
        window.localStorage.setItem('tablePagePY', JSON.stringify(this.tablePage));
      },
      //搜索
      onSearch() {
        this.tablePage.currentPage = 1;
        this.fetchData();
      },
      //重置
      rest() {
        this.searchData = {
          courseCode: '',
          courseName: '',
          sortOrder: '',
          courseLevel: '',
          status: ''
        };
        this.onSearch();
      },
      fetchData() {
        this.tableLoading = true;
        this.searchData.pageNum = this.tablePage.currentPage;
        this.searchData.pageSize = this.tablePage.size;
        courseAdditionList.pdCoursePage(this.searchData).then((res) => {
          this.tableLoading = false;
          if (res.code === 20000) {
            this.tableData = res.data.data;
            this.totalItems = Number(res.data.totalItems);
          }
        });
      },
      //添加操作
      getAddForm() {
        this.dialogVisible = true;
        this.updateId = '';
      },
      // 点击编辑按钮
      handleUpdate(data) {
        this.dialogVisible = true;
        this.updateId = JSON.stringify(data);
        console.log(this.updateId);
      },
      // 课程开通与暂停
      courseStatus(id, status) {
        this.$confirm('确定操作吗?', '修改状态', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          courseAdditionList.updateEnable({ id: id, status: status === 1 ? 0 : 1 }).then((res) => {
            if (res.code === 20000) {
              this.$message.success('修改成功');
              this.fetchData();
            }
          });
        });
      },
      // 分页
      handleSizeChange(val) {
        this.tablePage.size = val;
        this.fetchData();
      },
      handleCurrentChange(val) {
        this.tablePage.currentPage = val;
        this.fetchData();
      },
      openImg(row) {
        this.coverImg = row.coverUrl;
        this.dialogOpenimg = true;
      }
    }
  };
</script>

<style lang="less" scope="scope">
  ::v-deep .el-upload-list {
    width: auto !important;
  }
  .course_image_slot {
    height: 148px;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #c0c4cc;
  }
  .video_upload_table {
    width: 148px;
    height: 148px;
    border-radius: 8px;
    background-color: black;
  }
  .video_upload {
    position: absolute;
    z-index: 99999;
    width: 148px;
    height: 148px;
    border-radius: 8px;
    background-color: black;
  }
  .video_del {
    margin-left: 130px;
    position: absolute;
    z-index: 99999;
    color: white;
  }
  .isEnableRed {
    color: red;
  }
  .isEnableGreen {
    color: green;
  }
  .lh36 {
    line-height: 36px;
    font-size: 14px;
  }
  .marginRight {
    margin-right: 8px;
  }
  .marginLeft {
    margin-left: 8px;
  }
  .download {
    padding: 0 15px !important;
    margin: 0 !important;
    color: white !important;
  }
  .SearchForm {
    width: 100%;
    margin-bottom: 15px;
    background: #fff;
    border-radius: 6px;
    padding: 5px 0;
    padding-left: 5px;
    box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.2);
  }

  .btn-add {
    padding: 5px;
  }

  .red {
    color: red;
  }

  .green {
    color: green;
  }

  .course-table {
    text-align: center;
  }

  .course-table td,
  .course-table th {
    padding: 5px 0;
    text-align: center;
  }

  .course-table button {
    padding: 2px;
  }

  .mt22 {
    margin-top: 22px;
  }

  @media (max-width: 767px) {
    .el-message-box {
      width: 80% !important;
    }
  }
  /*当upLoadShow为true时，启用如下样式，即上传框的样式，若为false则不启用该样式*/
  .upLoadShow .el-upload {
    width: 10rem !important;
    height: 10rem !important;
    line-height: 10rem !important;
  }
  /*当upLoadHide为true时，启用如下样式，即缩略图的样式，若为false则不启用该样式*/
  .upLoadHide .el-upload-list--picture-card .el-upload-list__item {
    width: 10rem !important;
    height: 10rem !important;
    line-height: 10rem !important;
  }

  /*当upLoadHide为true时，启用如下样式，即上传框的样式，若为false则不启用该样式*/
  .upLoadHide .el-upload {
    display: none;
  }

  .coverimg {
    text-align: center !important;
    padding: 50px;
  }
  .imgDialog {
    .el-dialog {
      width: 30%;
    }

    .el-dialog__body {
      text-align: center !important;
    }
  }
</style>
