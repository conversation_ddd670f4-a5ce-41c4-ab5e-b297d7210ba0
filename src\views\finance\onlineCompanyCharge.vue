<template>
  <div class="app-container">
    <el-row type="flex" justify="center">
      <el-col :xs="24" :lg="12">
        <el-form class="recharge-form" label-width="110px" label-position="right" :ref="'dataQuery'" :rules="rules"
          :model="dataQuery" style="width: 100%;margin: 0 auto;">
          <el-form-item label="商户类型：" prop="roleTag">
            <el-select v-model="dataQuery.roleTag" placeholder="请选择" style="width: 185px" @change="changeRoleTag(dataQuery.roleTag)" filterable>
              <el-option v-for="item in roleTagList"
                :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="扣款账户：" prop="merchantCode">
            <el-select v-model="dataQuery.merchantCode" filterable placeholder="请选择" style="width: 185px" @change="changeMerchantCode(dataQuery.merchantCode)">
              <el-option v-for="item in merchantCodeType" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="账目类型：" prop="chargeType">
            <el-select v-model="dataQuery.chargeType" placeholder="请选择" style="width: 185px">
              <el-option v-for="item in accountType" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="账户余额" prop="symoney" v-show="backeMoneyShow">
            <el-input id="symoney" v-model="dataQuery.symoney" name="id" disabled />
          </el-form-item>
          <el-form-item label="剩余学时" prop="backCourse" v-show="chargeCourseShow">
            <el-input id="backCourse" v-model="dataQuery.backCourse" name="id" disabled />
          </el-form-item>
          <el-form-item label="扣除金额" v-show="backeMoneyShow" prop="chargeMoney">
            <el-input type="number" maxlength="20" isNumber2="true" min="1" id="chargeMoney" v-model="dataQuery.chargeMoney"
              name="id" :disabled="disabled" />
          </el-form-item>
          <el-form-item label="扣除学时：" v-show="chargeCourseShow" prop="chargeCourse">
            <el-input type="number" maxlength="20" isNumber2="true" min="1" id="chargeCourse" v-model="dataQuery.chargeCourse"
              name="id" :disabled="disabled" />
          </el-form-item>
          <el-form-item label="扣除说明：" prop="chargeRemark">
            <el-input type="textarea" :rows="4" id="chargeRemark" v-model="dataQuery.chargeRemark" name="id" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="addAcount('dataQuery')">保存</el-button>
          </el-form-item>
        </el-form>
      </el-col>
    </el-row>
    <!-- 添加弹窗 -->
    <el-dialog class="remark-dialog" title="交易密码确认" :visible.sync="dialogVisible" width="20%" :close-on-click-modal="false">
      <el-input v-model="secondPassWord" type="password" show-password></el-input>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="quertrue(secondPassWord)">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import merchantAccountFlowApi from "@/api/merchantAccountFlow";
  export default {
    data() {
      return {
        dataQuery: {},
        chargeCourseShow: false, //课程显示
        disabled: false,
        backeMoneyShow: false, //金额显示
        accountType: [], //扣除方式
        merchantCodeType: [], //账户方式
        queryTrueShow: true,
        dialogVisible: false, //密码弹框
        checkSecondPwdShow: false, //密码成功
        secondPassWord: "",
        roleTagList:[],
        backCourse: "", //剩余学时
        symoney: "", //账户余额
        // false: true,
        rules: {
          roleTag: [{
            required: true,
            message: "必填",
            trigger: "change",
          }, ],
          merchantCode: [{
            required: true,
            message: "必填",
            trigger: "change",
          }, ],
          chargeType: [{
            required: true,
            message: "必填",
            trigger: "blur",
          }, ],
          chargeRemark: [{
            required: true,
            message: "必填",
            trigger: "blur",
          }, ],
          chargeCourse: [{
            required: true,
            message: "必填",
            trigger: "blur",
          }, ],
          chargeMoney: [{
            required: true,
            message: "必填",
            trigger: "blur",
          }, ],
        },
      };
    },
    created() {
      this.getRoleTagLists();
    },
    methods: {
      //选择账户
      changeMerchantCode(val) {
        if (val != null) {
          merchantAccountFlowApi.checkOver(val).then((res) => {
            this.dataQuery.symoney = res.data.data.money;
            this.dataQuery.backCourse = res.data.data.money;
          });
        }
      },
      //增加操作
      addAcount(ele) {
        const that =this;
        if (that.dataQuery.roleTag !== "School") {
          that.dataQuery.chargeCourse = "0";
        } else {
          that.dataQuery.chargeMoney = "0";
        }
        if (that.queryTrueShow) {
          that.$refs[ele].validate((valid) => {
            // 表单验证
            if (valid) {
             that.dialogVisible = true;
           that.secondPassWord="";
            } else {
              console.log("error submit!!");
              //loading.close();
              return false;
            }
          });
        }
      },
      //密码确认
      quertrue(secondPassWord) {
        this.$confirm('确定操作吗?', '在线扣款', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
 const that = this;
        if (secondPassWord.length <= 0) {
          that.$message.info("交易密码不能为空");
        } else {
          merchantAccountFlowApi.checkSecondPwd(secondPassWord).then(res => {
            if (res.success) {
              if (that.dataQuery.roleTag !== "School") {
                that.dataQuery.chargeCourse = "0";
              } else {
                that.dataQuery.chargeMoney = "0";
              }
              that.$refs['dataQuery'].validate((valid) => {
                // 表单验证
                if (valid) {
                  const loading = this.$loading({
                    lock: true,
                    text: "扣除账户",
                    spinner: "el-icon-loading",
                    background: "rgba(0, 0, 0, 0.7)",
                  });
                  merchantAccountFlowApi
                    .onlineDeduction(that.dataQuery)
                    .then((res) => {
                      that.dataQuery = {
                        roleTag: "",
                        chargeType: "",
                        merchantCode: "",
                        symoney: "", //账户余额
                        chargeMoney: "", //扣除余额
                        courseTime: "", //剩余学时
                        chargeCourse: "", //扣除学时
                        chargeRemark: "",
                      };
                      that.secondPassWord = '';
                      that.backeMoneyShow = false;
                      that.chargeCourseShow = false;
                      that.dialogVisible = false;
                      that.$message.success("扣除账户成功");
                      loading.close();
                            that.$router.push({
          path: "/finance/merchantFlowList"
        });
                    })
                    .catch((err) => {
                      loading.close();
                    });
                } else {
                  console.log("error submit!!");
                  //loading.close();
                  return false;
                }
              });
            } else {
              that.dialogVisible = false;
              that.$message.info("验证失败");
            }
          })
        }
        })

      },
      //获取角色
      getRoleTagLists(){
        merchantAccountFlowApi.roleTagCompany().then(res=>{
          this.roleTagList=res.data;
        })
      },
      //改变角色
      changeRoleTag(val) {
        if (val !== "School") {
          this.accountType = [{
            value: "ChargeMoney",
            label: "扣除金额"
          }];
          this.dataQuery = {
            roleTag: val,
            chargeType: "",
            merchantCode: "",
            symoney: "", //账户余额
            chargeMoney: "", //扣除余额
            courseTime: "", //剩余学时
            chargeCourse: "", //扣除学时
            backCourse: "", //剩余学时
          };
          this.chargeCourseShow = false;
          this.backeMoneyShow = true;
          merchantAccountFlowApi.getBvMerchantList(val).then((res) => {
            this.merchantCodeType = res.data.data;
          });
        } else {
          this.accountType = [{
            value: "ChargeCourse",
            label: "扣款学时"
          }];
          this.dataQuery = {
            roleTag: val,
            chargeType: "",
            merchantCode: "",
            symoney: "", //账户余额
            chargeMoney: "", //扣除余额
            courseTime: "", //剩余学时
            chargeCourse: "", //扣除学时
            backCourse: "", //剩余学时
          };
          this.backeMoneyShow = false; //金额显示
          this.chargeCourseShow = true; //扣款学时显示
          merchantAccountFlowApi.getBvMerchantList(val).then((res) => {
            this.merchantCodeType = res.data.data;
          });
        }
      },
    },
  };
</script>

<style>
  .recharge-form input {
    /* width: 185px; */
  }

  .recharge-form textarea {
    /* width: 400px; */
  }

  .recharge-form button {
    padding: 10px 40px;
  }
  @media (max-width:767px) {
    .el-message-box{
      width: 80%!important;
    }
    .el-select-dropdown{
      max-width: 200px!important;
    }
  }

</style>
