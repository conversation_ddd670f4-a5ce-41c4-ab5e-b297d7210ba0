<template>
  <div>
    <el-dialog title="开通确认单" :center="true" :visible.sync="dialogVisible" width="500px" @close="handleOuterClose">
      <el-form ref="confirmFormRef" :model="form" label-width="120px">
        <el-form-item label="门店编号" prop="merchantCode">
          <el-input disabled v-model="form.merchantCode"></el-input>
        </el-form-item>
        <el-form-item label="门店名称" prop="merchantName">
          <el-input disabled v-model="form.merchantName"></el-input>
        </el-form-item>
        <el-form-item label="负责人" prop="realName">
          <el-input disabled type="person" v-model="form.realName"></el-input>
        </el-form-item>
        <el-form-item class="dialog-footer">
          <template>
            <el-button :loading="loading" type="primary" @click="handleConfirm">确认开通</el-button>
            <el-button @click="handleOuterClose">取 消</el-button>
          </template>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
  import schoolList from '@/api/schoolList';

  export default {
    name: 'confirmForm',
    props: {
      isShowForm: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        form: {
          merchantCode: '',
          merchantName: '',
          realName: ''
        },
        loading: false
      };
    },
    computed: {
      dialogVisible: {
        get() {
          return this.isShowForm;
        },
        set(val) {
          this.$emit('update:isShowCityDialog', val);
        }
      }
    },
    methods: {
      handleOuterClose() {
        this.reset();
        this.$emit('closeConfirmDialog');
      },
      async handleConfirm() {
        if (this.loading) return;
        this.loading = true;
        try {
          schoolList
            .openSchoolApi(this.form)
            .then(() => {
              this.$message.success('操作成功');
              this.loading = false;
              this.reset();
              this.$emit('closeConfirmDialog', true);
            })
            .catch((error) => {
              this.loading = false;
              this.$message.error(error.message);
            });
        } catch (error) {
          console.log('🚀 ~ handleConfirm ~ error:', error);
          this.$message.error('操作失败');
          this.loading = false;
        }
      },

      setData(data) {
        console.log('🚀 ~ setData ~ data:', data);
        this.form.merchantCode = data.merchantCode;
        this.form.realName = data.realName;
        this.form.merchantName = data.merchantName;
      },

      reset() {
        this.form = {
          merchantCode: '',
          merchantName: '',
          realName: ''
        };
        this.$refs.confirmFormRef.resetFields();
      }
    }
  };
</script>

<style scoped>
  .dialog-footer {
    text-align: right;
  }
</style>
