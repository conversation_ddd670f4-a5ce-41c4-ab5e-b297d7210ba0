
import request from '@/utils/request'

export default {

  // 分页查询固定班
  scheduleList(data) {
    return request({
      url: '/studyroom/webSchedule/list',
      method: 'GET',
      params: data
    })
  },
  //新增固定班
  addSchedule(data){
    return request({
      url: '/studyroom/webSchedule/addSchedule',
      method: 'POST',
      data
    })
  },
  //详情
  getSchedule(id){
    return request({
      url: '/studyroom/webSchedule/getSchedule',
      method: 'GET',
      params:{
        id:id
      }
    })
  },
  //编辑
  updateSchedule(data){
    return request({
      url: '/studyroom/webSchedule/updateSchedule',
      method: 'PUT',
      data
    })
  },
  //修改状态
  updateStatus(id,status){
    return request({
      url: '/studyroom/webSchedule/changeStatus',
      method: 'PUT',
      params:{
        scheduleId:id,
        status:status
      }
    })
  },
  //人员列表
  getStudents(data){
    return request({
      url: '/studyroom/webSchedule/getServiceStudents',
      method: 'GET',
      params: data
    })
  },
  //获取所有班级房间
  getRooms(id){
    return request({
      url: '/studyroom/webSchedule/getRooms',
      method: 'GET',
      params: {
        parentScheduleId:id
      }
    })
  },
  //指派学委
  appointCommittee(scheduleId,committeemanId){
    return request({
      url: '/studyroom/webSchedule/appointCommittee',
      method: 'PUT',
      params: {
        scheduleId:scheduleId,
        committeemanId:committeemanId
      }
    })
  },
  // 目标分页查询
  targetList(pageNum, pageSize, data) {
    return request({
      url: '/studyroom/webSchedule/targetList/' + pageNum + '/' + pageSize,
      method: 'GET',
      params: data
    })
  },
  //新增目标
  addTarget(data){
    return request({
      url: '/studyroom/webSchedule/addTarget',
      method: 'POST',
      data
    })
  },
  //目标详情
  getTarget(id){
    return request({
      url: '/studyroom/webSchedule/getTarget',
      method: 'GET',
      params:{
        targetId:id
      }
    })
  },
  //编辑目标
  editTarget(data){
    return request({
      url: '/studyroom/webSchedule/editTarget',
      method: 'PUT',
      data
    })
  },
  //新增非固定班
  createUnFix(data){
    return request({
      url: '/studyroom/webSchedule/createUnFixAndShare',
      method: 'POST',
      data
    })
  },
  //分页查询非固定班
  getUnFixList(data){
    return request({
      url: '/studyroom/webSchedule/getUnFixList',
      method: 'GET',
      params: data
    })
  },
  //学委管理的房间列表
  getRoomList(pageNum,pageSize){
    return request({
      url: '/studyroom/webSchedule/roomList/'+pageNum +"/"+pageSize,
      method: 'GET'
    })
  },
  //固定班下自学室
  getScheduleRooms(scheduleId){
    return request({
      url: '/studyroom/webSchedule/scheduleViewRooms',
      method: 'GET',
      params: {
        scheduleId:scheduleId
      }
    })
  },
  // 非固定班详情下房间
  getUnFixScheduleViewRooms(scheduleId){
    return request({
      url: '/studyroom/webSchedule/unFixScheduleViewRooms',
      method: 'GET',
      params: {
        scheduleId:scheduleId
      }
    })
  },
  //获取学员学段
  getStudentLevel(studentCode){
    return request({
      url: '/studyroom/webSchedule/getStudentLevel',
      method: 'GET',
      params: {
        studentCode:studentCode
      }
    })
  },
  //获取学员学号
  getStudentCode(phoneNum){
    return request({
      url: '/studyroom/webSchedule/getStudentsByPhoneNum',
      method: 'GET',
      params: {
        phoneNum:phoneNum
      }
    })
  }
}

