<template>
  <div>
    <el-card class="frame" shadow="never">
      <el-form label-width="80px">
        <!-- 1 -->
        <el-row type="flex">
          <el-col :span="8">
            <el-form-item label="学员编号:">
              <el-input
                v-model="searchNum.studentCode"
                clearable
                placeholder="请输入"
                size="small"
                style="width: 13vw"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="学员姓名:">
              <el-input
                v-model="searchNum.studentName"
                clearable
                placeholder="请输入"
                size="small"
                style="width: 13vw"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="订单编号:">
              <el-input
                v-model="searchNum.lineOrderId"
                clearable
                placeholder="请输入"
                size="small"
                style="width: 13vw"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="4" :offset="4">
            <el-button type="primary" size="mini" @click="initData1"
              >查询</el-button
            >
            <el-button size="mini" @click="resat"> 重置 </el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
    <!-- 表格 -->
    <el-table
      v-loading="tableLoading"
      :data="list"
      style="width: 100%"
      id="out-table"
      :header-cell-style="getRowClass"
      :cell-style="{ 'text-align': 'center' }"
    >
      <el-table-column
        prop="studentCode"
        label="学员编号"
        width="160"
        header-align="center"
      />
      <el-table-column
        prop="studentName"
        label="姓名"
        width="160"
        header-align="center"
      />
      <el-table-column
        prop="address"
        label="操作"
        header-align="center"
        width="100"
      >
        <template v-slot="{ row }">
          <el-button type="primary" size="mini" @click="edit(row)"
            >编辑
          </el-button>
        </template>
      </el-table-column>
      <el-table-column
        prop="merchantName"
        label="门店名称"
        header-align="center"
      ></el-table-column>
      <el-table-column
        prop="merchantPhone"
        label="门店手机号"
        header-align="center"
      ></el-table-column>
      <el-table-column
        prop="lineOrderId"
        label="订单编号"
        header-align="center"
      ></el-table-column>
      <el-table-column
        prop="sumCoursePrice"
        label="合计学时金额"
        header-align="center"
      >
      </el-table-column>
      <el-table-column
        prop="sumDeliverPrice"
        label="合计交付学时金额"
        header-align="center"
      ></el-table-column>
    </el-table>
    <el-dialog
      title="充值学时"
      :visible.sync="isShow"
      @close="close"
      width="50%"
    >
      <span>
        <el-form
          :model="form"
          ref="form"
          :rules="rules"
          label-width="150px"
          :inline="false"
          label-position="left"
          size="normal"
        >
          <el-form-item label="充值账户">
            <el-input v-model="form.studentCode" disabled></el-input>
          </el-form-item>
          <el-form-item label="学生姓名">
            <el-input v-model="form.studentName" disabled></el-input>
          </el-form-item>
          <el-form-item label="交付方式">
            <el-input
              :value="form.deliverMode == 'CENTER' ? '集中交付' : '自行交付'"
              disabled
            ></el-input>
          </el-form-item>
          <!-- <el-form-item label="课程类型">
            <el-input v-model="form.id" disabled></el-input>
          </el-form-item> -->
          <el-form-item label="充值学时">
            <el-input v-model="form.courseLength" disabled></el-input>
          </el-form-item>
          <el-form-item label="充值交付学时（节）">
            <el-input v-model="form.deliverCourseLength" disabled></el-input>
          </el-form-item>
          <!-- <el-form-item
            v-if="form.deliverMode != 'CENTER'"
            label="自行交付学时（节）"
          >
            <el-input v-model="form.deliverCourseLength" disabled></el-input>
          </el-form-item> -->
          <el-row>
            <el-col :span="12">
              <el-form-item label="学时单价" prop="coursePrice">
                <el-input v-model="form.coursePrice"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="11" :offset="1">
              <el-form-item label="合计学时金额">
                <el-input
                  :value="(form.coursePrice * form.courseLength).toFixed(2)"
                  disabled
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="交付学时单价" prop="deliverCoursePrice">
                <el-input v-model="form.deliverCoursePrice"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="11" :offset="1">
              <el-form-item label="合计交付学时金额">
                <el-input
                  :value="
                    (
                      form.deliverCoursePrice * form.deliverCourseLength
                    ).toFixed(2)
                  "
                  disabled
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item label="充值说明">
            <el-input
              type="textarea"
              :rows="3"
              v-model="form.description"
              disabled
            ></el-input>
          </el-form-item>
        </el-form>
      </span>
      <span slot="footer" style="display: flex; justify-content: center">
        <el-button type="primary" :disabled="disabledF" @click="setPayment"
          >确定</el-button
        >
        <el-button @click="isShow = false">取消</el-button>
      </span>
    </el-dialog>

    <!-- 分页器 -->
    <el-row type="flex" justify="center" align="middle" style="height: 60px">
      <!-- 3个变量：每页数量、页码数、总数  -->
      <!-- 2个事件：页码切换事件、每页数量切换事件-->
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="searchNum.pageNum"
        :page-sizes="[10, 20, 30, 40, 50]"
        :page-size="10"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      ></el-pagination>
    </el-row>
  </div>
</template>

<script>
import store from "@/store";
import { dxSource, pageParamNames } from "@/utils/constants";
import CoursependingPayment from "@/api/CoursependingPayment";
export default {
  name: "classCard",
  data() {
    var validatePass = (rule, value, callback) => {
      if (value < 0.01 && this.form.courseLength != 0) {
        callback(new Error("最低单价不能低于0.01"));
      } else {
        callback();
      }
    };
    var validatePass1 = (rule, value, callback) => {
      if (value < 0.01 && this.form.deliverCourseLength != 0) {
        callback(new Error("最低单价不能低于0.01"));
      } else {
        callback();
      }
    };
    return {
      searchNum: {
        studentCode: "",
        merchantCode: "",
        pageNum: 1,
        pageSize: 10,
      },
      token: store.getters.token,
      setpayUrl: store.getters.setpayUrl,
      total: 0,
      isShow: false,
      tableLoading: false,
      disabledF: false,
      form: {
        coursePrice: "",
        deliverCoursePrice: "",
      },
      list: [],
      rules: {
        deliverCoursePrice: [
          { required: true, message: "请输入交付学时单价", trigger: "blur" },
          { validator: validatePass1, trigger: "blur" },
        ],
        coursePrice: [
          { required: true, message: "请输入学时单价", trigger: "blur" },
          { validator: validatePass, trigger: "blur" },
        ],
      },
    };
  },
  created() {
    this.initData();
  },
  methods: {
    //查询列表
    initData1() {
      this.searchNum.pageNum = 1;
      this.searchNum.pageSize = 10;
      this.initData();
    },
    //初始化获取列表
    async initData() {
      this.tableLoading = true;
      let { data } = await CoursependingPayment.waitPayCourseList(
        this.searchNum
      );
      this.list = data.data;
      this.total = +data.totalItems;
      this.tableLoading = false;
    },
    // 编辑
    edit(row) {
      this.form = JSON.parse(JSON.stringify(row));
      this.isShow = true;
    },
    // 编辑确定
    async setPayment() {
      await this.$refs.form.validate();
      let obj = {
        coursePrice: this.form.coursePrice,
        deliverCoursePrice: this.form.deliverCoursePrice,
        deliverMode: this.form.deliverMode,
        orderId: this.form.lineOrderId,
      };
      let res = await CoursependingPayment.modifyWaitPayCourse0rder(obj);
      const split = dxSource.split("##");
      res.data.lineCollectInfo.dxSource =
        res.data.lineCollectInfo.registerCallIInfo.appSource +
        "##" +
        split[1] +
        "##" +
        split[2];
      let params = JSON.stringify(res.data.lineCollectInfo);
      let req =
        "token=" +
        this.token +
        "&params=" +
        params +
        "&back=" +
        window.location.href;
      console.log(req);
      this.disabledF = false;
      // window.open(
      //   "http://192.168.5.116:8001/product?" +
      //     Base64.encode(Base64.encode(req)),
      //   "_blank"
      // );
      window.open(
        this.setpayUrl + "product?" + Base64.encode(Base64.encode(req)),
        "_blank"
      );
      this.isShow = false;
      this.$message.success("操作成功");
      this.initData();
    },
    // 表单重置
    close() {
      this.$refs.form.clearValidate();
    },
    //重置
    resat() {
      this.searchNum = {
        checkStatus: "",
        name: "",
        endTime: "",
        startTime: "",
        teacherName: "",
        studentCode: "",
        studyStatus: "",
        lastStudyTime: "",
        pageNum: 1,
        pageSize: 10,
        planId: undefined,
      };
      this.initData();
    },
    // 分页
    handleSizeChange(val) {
      this.searchNum.pageSize = val;
      this.initData();
    },
    handleCurrentChange(val) {
      this.searchNum.pageNum = val;
      this.initData();
    },
    getRowClass({ rowIndex, columnIndex }) {
      if (rowIndex == 0) {
        return "background:#f5f7fa";
      }
    },
  },
};
</script>

<style lang="scss" scoped>
body {
  background-color: #f5f7fa;
}
.frame {
  margin-top: 0.5vh;
  background-color: rgba(255, 255, 255);
}

// .el-button--success {
//   color: #ffffff;
//   background-color: #6ed7c4;
//   border-color: #6ed7c4;
// }
</style>
