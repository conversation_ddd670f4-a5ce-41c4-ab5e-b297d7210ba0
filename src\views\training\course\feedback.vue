<template>
  <div class="app-container">
    <el-form :inline="true" :model="form" ref="searchForm" class="container-card" label-width="120px">
      <el-form-item label="课程名称：" prop="courseName">
        <el-input v-model="form.courseName" placeholder="请输入课程名称" maxlength="30" clearable />
      </el-form-item>
      <el-form-item label="课时名称：" prop="courseLessonName">
        <el-input v-model="form.courseLessonName" placeholder="请输入课时名称" maxlength="30" clearable />
      </el-form-item>
      <el-form-item label="反馈人姓名：" prop="userName">
        <el-input v-model="form.userName" placeholder="请输入反馈人姓名" maxlength="30" clearable />
      </el-form-item>
      <el-form-item label="反馈人手机号：" prop="userPhone">
        <el-input v-model="form.userPhone" placeholder="请输入反馈人手机号" maxlength="11" clearable @input="onPhoneInput" />
      </el-form-item>
      <el-form-item label="反馈时间：" prop="timeRange">
        <el-date-picker
          type="daterange"
          value-format="yyyy-MM-dd"
          v-model="form.timeRange"
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          placeholder="选择时间范围"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-refresh" @click="resetQuery()">重置</el-button>
        <el-button icon="el-icon-search" type="primary" @click="search(1)">查询</el-button>
      </el-form-item>
    </el-form>

    <!-- 反馈统计 -->
    <div class="summary">
      <ul class="data">
        <li class="one">
          <span class="label">非常好反馈数（条）：</span>
          <span class="value">{{ expectationForm.VERY_GOOD }}</span>
        </li>
        <li class="one">
          <span class="label">较好反馈数（条）：</span>
          <span class="value">{{ expectationForm.PREFERABLY }}</span>
        </li>
        <li class="one">
          <span class="label">一般反馈数（条）：</span>
          <span class="value">{{ expectationForm.COMMON }}</span>
        </li>
        <li class="one">
          <span class="label">不太好反馈数（条）：</span>
          <span class="value">{{ expectationForm.JUST_SO_SO }}</span>
        </li>
        <li class="one">
          <span class="label">较差反馈数（条）：</span>
          <span class="value">{{ expectationForm.POOR }}</span>
        </li>
      </ul>
    </div>

    <div class="table-area">
      <!-- 表格 -->
      <el-table v-loading.body="tableLoading" class="common-table" :data="tableData" stripe border :default-sort="{ prop: 'date', order: 'descending' }" empty-text="暂无数据">
        <el-table-column prop="learningExpectationName" label="学习预期"></el-table-column>
        <el-table-column prop="learningOpinion" label="其他的建议或意见" show-overflow-tooltip></el-table-column>
        <el-table-column prop="userName" label="反馈人名称"></el-table-column>
        <el-table-column prop="userPhone" label="反馈人手机号"></el-table-column>
        <el-table-column prop="userRoleTag" label="反馈人角色"></el-table-column>
        <el-table-column prop="courseName" label="课程名称"></el-table-column>
        <el-table-column prop="courseLessonName" label="课时名称"></el-table-column>
        <el-table-column prop="feedbackTime" label="反馈时间"></el-table-column>
        <el-table-column prop="remark" label="备注">
          <template slot-scope="scope">
            <el-popover placement="top-start" width="300" trigger="hover" :disabled="scope.row.remark.length <= 10">
              <div>{{ scope.row.remark }}</div>
              <span slot="reference" v-if="scope.row.remark.length <= 10">{{ scope.row.remark }}</span>
              <span slot="reference" v-if="scope.row.remark.length > 10">{{ scope.row.remark.substr(0, 10) + '...' }}</span>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column prop="id" label="操作" width="200">
          <template slot-scope="scope">
            <el-button type="text" size="mini" @click="handleRemark(scope.row)">{{ scope.row.remark ? '修改备注' : '添加备注' }}</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <el-col :span="24" style="margin-top: 20px; margin-bottom: 20px">
        <el-pagination
          :current-page="tablePage.currentPage"
          :page-sizes="[10, 20, 30, 40, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="tablePage.totalItems"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </el-col>
    </div>

    <!-- 添加/修改备注 -->
    <add-feedback ref="feedbackForm" :isEdit="isEdit" :dialogVisible="dialogVisible" @closeDialog="closeDialog"></add-feedback>
  </div>
</template>

<script>
  import courseApi from '@/api/training/course';
  import AddFeedback from './components/addFeedback.vue';
  export default {
    components: {
      AddFeedback
    },
    data() {
      return {
        form: {
          courseName: '',
          courseLessonName: '',
          userName: '',
          userPhone: '',
          timeRange: []
        },
        courseType: [],
        tableLoading: false,
        // 分页
        tablePage: {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: 1
        },
        expectationForm: {},
        tableData: [],
        dialogVisible: false,
        isEdit: false
      };
    },
    created() {
      // this.getQueryByType();
      this.search();
    },
    methods: {
      // 定义输入处理
      onPhoneInput(value) {
        this.form.userPhone = value.replace(/[^\d.]/g, '');
      },
      // getQueryByType(){
      //     courseApi.dictQueryByType({dictType:'paper_course_category'}).then(res => {
      //         this.courseType=res.data
      //     })
      // },
      resetQuery() {
        this.$refs['searchForm'].resetFields();
        this.tablePage = {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: 0
        };
        this.search();
      },
      // 搜索
      search(page) {
        let params = {
          courseName: '',
          courseLessonName: '',
          userName: '',
          userPhone: '',
          feedbackTimeStart: '',
          feedbackTimeEnd: ''
        };

        // 查询预期反馈数据
        for (const key in this.form) {
          if (Object.prototype.hasOwnProperty.call(params, key)) {
            const element = this.form[key];
            params[key] = element;
          }
          if (key == 'timeRange' && this.form.timeRange instanceof Array && this.form.timeRange[0]) {
            params['feedbackTimeStart'] = this.form.timeRange[0];
          }
          if (key == 'timeRange' && this.form.timeRange instanceof Array && this.form.timeRange[1]) {
            params['feedbackTimeEnd'] = this.form.timeRange[1];
          }
        }
        courseApi.courseLearningCount(params).then((res) => {
          this.expectationForm = res.data;
        });
        // 查询表格数据
        this.tableLoading = true;
        if (typeof page == 'number' && page > 0) {
          this.tablePage.currentPage = page;
        }
        let pagingParams = { ...params, pageSize: this.tablePage.size, pageNum: page || this.tablePage.currentPage };
        courseApi
          .courseFeedbackPage(pagingParams)
          .then((res) => {
            this.tableData = res.data;
            this.tablePage.totalItems = Number(res.total);
          })
          .finally(() => {
            this.tableLoading = false;
          });
      },
      handleSizeChange(val) {
        this.tablePage.size = val;
        this.search();
      },
      handleCurrentChange(val) {
        this.tablePage.currentPage = val;
        this.search();
      },
      handleRemark(row) {
        if (row.remark) {
          this.isEdit = true;
        } else {
          this.isEdit = false;
        }
        this.dialogVisible = true;
        this.$nextTick(() => {
          this.$refs['feedbackForm'].open(row);
        });
      },
      closeDialog(info) {
        this.dialogVisible = false;
        if (info == 'success') {
          this.search();
        }
      }
    }
  };
</script>

<style scoped>
  .summary {
    width: 100%;
    background-color: #f5f5f5;
    border-radius: 4px;
  }
  .summary .data {
    display: flex;
    justify-content: flex-start;
    flex-wrap: wrap;
    list-style: none;
    font-size: 14px;
    color: #555;
    padding: 15px 20px;
  }
  .summary .data .one {
    margin-right: 20px;
  }
  .summary .data .value {
    color: #333;
    font-size: 16px;
  }
  .table-area {
    padding: 10px;
    box-sizing: border-box;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  }
</style>
