<template>
  <div class="training-container">
    <!-- 首页信息 -->
    <div class="analysis">
      <div class="analysisBox"></div>
      <span class="analysisTxt">统计分析</span>
    </div>
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
      <el-row id="container" :gutter="24">
        <el-col :span="8">
          <el-card shadow="always" body-style="padding:5px">
            <div @click="openDialogVisible('完成课程人数',1)">
              <div class="ibox-title">
                <h2>完成课程人数</h2>
              </div>
              <div class="ibox-content">
                <span style="font-size: 30px">{{ homeCouresInfo.coursesCompletedNum||0 }}</span>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="8" >
          <el-card shadow="always"  body-style="padding:5px">
            <div @click="openDialogVisible('参与课程人数',2)">
              <div class="ibox-title">
                <h2>参与课程人数</h2>
              </div>
              <div class="ibox-content">
                <span style="font-size: 30px">{{ homeCouresInfo.coursesParticipateNum||0 }}</span>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card shadow="always" body-style="padding:5px">
            <div  @click="openDialogVisible('合格率',3)">
              <div class="ibox-title">
                <h2>合格率</h2>
              </div>
              <div class="ibox-content">
                <span style="font-size: 30px">{{ homeCouresInfo.qualificationRate||0 }}%</span>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
    <el-dialog width="40%" :title="dialogTitle" :visible.sync="dialogVisible" @closed="closeAddDialog()" :close-on-click-modal="false">
        <div v-if="dialogType==1">
          <el-table class="common-table"  :data="courseData" stripe border :default-sort="{ prop: 'date', order: 'descending' }" >
            <el-table-column type="index" width="100" label="序号"></el-table-column>
            <el-table-column prop="categoryCode" label="姓名"></el-table-column>
            <el-table-column prop="categoryCode" label="所属部门"></el-table-column>
            <el-table-column prop="categoryCode" label="课程信息"></el-table-column>
            <el-table-column prop="categoryCode" label="考试分数"></el-table-column>
            <el-table-column prop="id" label="操作" width="200">
              <template slot-scope="scope">
                <el-button  type="text" size="mini"  icon="el-icon-edit-outline" @click="handleDetail(scope.row.id)" >详情</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div v-if="dialogType==2">
          <el-table class="common-table"  :data="courseData" stripe border :default-sort="{ prop: 'date', order: 'descending' }" >
            <el-table-column type="index" width="100" label="序号"></el-table-column>
            <el-table-column prop="categoryCode" label="姓名"></el-table-column>
            <el-table-column prop="categoryCode" label="所属部门"></el-table-column>
            <el-table-column prop="categoryCode" label="参与课程"></el-table-column>
            <el-table-column prop="categoryCode" label="报名时间"></el-table-column>
          </el-table>
        </div>
        <div v-if="dialogType==3">
          <el-table class="common-table"  :data="courseData" stripe border :default-sort="{ prop: 'date', order: 'descending' }" >
            <el-table-column type="index" width="100" label="序号"></el-table-column>
            <el-table-column prop="categoryCode" label="姓名"></el-table-column>
            <el-table-column prop="categoryCode" label="所属部门"></el-table-column>
            <el-table-column prop="categoryCode" label="上传时间"></el-table-column>
            <el-table-column prop="categoryCode" label="课程信息"></el-table-column>
            <el-table-column prop="categoryCode" label="合格率"></el-table-column>
          </el-table>
        </div>
        <!-- 分页 -->
        <div style="margin-top:10px;">
          <el-pagination
            :current-page="tablePage.currentPage"
            :page-sizes="[10, 20, 30, 40, 50]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="tablePage.totalItems"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"  />
        </div>
        <div slot="footer" class="dialog-footer">
          <el-button  @click="closeAddDialog()">关闭</el-button>
        </div>
    </el-dialog>
  </div>
</template>
<script>
// statisticsHomePage
import courseApi from '@/api/training/course'
export default {
  name: "home",
  data() {
    return {
      completeNum: "11",
      joinNum: "22",
      passRate: "33",
      dialogVisible:false,
      dialogTitle:'',
      dialogType:1,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 20,
        totalPage: null,
        totalItems: 20,
      },
      courseData:[],
      // participationCourseData:[],
      // qualificationRateData:[],
      homeCouresInfo:{},
    };
  },
  created(){
    this.getHomePage()
  },
  methods: {
    getHomePage(){
      courseApi.statisticsHomePage().then(res => {
        this.homeCouresInfo=res.data
      })
    },
    closeAddDialog(){
      this.dialogVisible=false
    },
    openDialogVisible(title,key){
      this.dialogTitle=title
      this.dialogType=key
      let couresTitle=''
      // if(this.dialogType==1){
      //   couresTitle="statisticsRecordPage"
      // }
      // let param = {pageSize:this.tablePage.size,pageNum:this.tablePage.currentPage}
      // // param={...this.form,...param}
      // courseApi[couresTitle](param).then(res => {
      //   console.log(res)
      // })
     // this.dialogVisible=true
    },
    handleSizeChange(val){},
    handleCurrentChange(val){},
  }
};
</script>
<style lang="scss" scoped>
.training-container {
  background-color: #fff;
  color: #676a6c;
  min-height: 100vh;
  padding: 20px 20px 0px;
}
.analysis {
    display: flex;
    margin-bottom: 10px;
  }
  .analysisBox {
      margin-right: 20px;
      width: 5px;
      height: 40px;
      background-color: #d7d7d7;
    }
    .analysisTxt {
      line-height: 3;
    }
  .ibox-content {
    text-align: center;
    background-color: #ffffff;
    color: inherit;
    padding: 20px;
    border-color: #e7eaec;
    -webkit-border-image: none;
    -o-border-image: none;
    border-image: none;
    border-style: solid solid none;
    border-width: 1px 0px;

  }
  h1 {
    font-weight: normal;
  }
  .ibox-title {
    -moz-border-bottom-colors: none;
    -moz-border-left-colors: none;
    -moz-border-right-colors: none;
    -moz-border-top-colors: none;
    background-color: #ffffff;
    border-color: #e7eaec;
    -webkit-border-image: none;
    -o-border-image: none;
    border-image: none;
    border-style: solid solid none;
    border-width: 0px 0px 0;
    color: inherit;
    margin-bottom: 0;
    padding: 14px 15px 7px;
    min-height: 40px;
    font-weight: normal;
  }
</style>
