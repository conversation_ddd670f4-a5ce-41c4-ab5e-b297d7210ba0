import request from '@/utils/request'

export default {
  // 分页查询
  pageList(data) {
    return request({
      url: '/paper/web/abilityReport/list',
      method: 'GET',
      params: data
    })
  },
  // 新增/保存
  save(data) {
    return request({
      url: '/paper/web/abilityReport/save',
      method: 'POST',
      data: data
    })
  },
  //根据报告id获取详情
  detail(id) {
    return request({
      url: '/paper/web/abilityReport/detail',
      method: 'GET',
      params: {
        id: id
      }
    })
  },
  //根据学段id获取详情
  detailByGradeId(gradeId, type) {
    return request({
      url: '/paper/web/abilityReport/detailByGradeId',
      method: 'GET',
      params: {
        gradeId: gradeId,
        type: type
      }
    })
  },
  //评估报告列表
  assessReportList(data) {
    return request({
      url: '/paper/web/assessReport/list',
      method: 'GET',
      params: data
    })
  },
 //根据评估报告id获取详情
 assessReportDetail(id) {
  return request({
    url: '/paper/web/assessReport/detail',
    method: 'GET',
    params: {
      id: id
    }
  })
},
// /web/assessReport/save
assessReportSave(data) {
  return request({
    url: '/paper/web/assessReport/save',
    method: 'POST',
    data: data
  })
},
// /web/assessReport/getReportByGradeId
 //根据学段id获取详情
 existReportByGradeId(gradeId) {
  return request({
    url: '/paper/web/assessReport/existReportByGradeId',
    method: 'GET',
    params: {
      gradeId: gradeId,
    }
  })
},
// /web/assessReport/existReportByGradeId
}
