<template>
  <div class="app-container">
    <!-- 搜索 -->
    <el-form :inline="true" ref="form" class="SearchForm" style="padding-top: 30px">
      <el-form-item label="题目序号:" style="margin-right: 80px">
        <el-col :span="24">
          <el-input placeholder="请输入" @input="handleInput(dataQuery.questionOrder)" clearable v-model="dataQuery.questionOrder" @keyup.enter.native="fetchData01()"></el-input>
        </el-col>
      </el-form-item>
      <el-form-item label="题目标题:" style="margin-right: 80px">
        <el-input placeholder="请输入" clearable v-model="dataQuery.questionTitle" @keyup.enter.native="fetchData01()"></el-input>
      </el-form-item>
      <el-button type="primary" icon="el-icon-search" @click="fetchData01()">搜索</el-button>
      <el-button type="info" icon="el-icon-refresh" @click="fetchDataReset()">重置</el-button>
    </el-form>

    <div class="SearchForm">
      <!-- 添加 -->
      <div class="btn-add">
        <el-button size="small" type="success" @click="goBack()">返回听力列表</el-button>
        <el-button size="small" type="primary" icon="el-icon-plus" @click="clickAdd">添加</el-button>
      </div>
      <!-- 表格 -->
      <el-table class="course-table" :data="tableData" stripe border :default-sort="{ prop: 'addTime', order: 'descending' }" v-loading="tableLoading">
        <el-table-column prop="questionCode" label="题目编码"></el-table-column>
        <el-table-column prop="questionNumber" label="题目序号"></el-table-column>
        <el-table-column prop="id" label="操作" width="300">
          <template slot-scope="scope">
            <el-button type="success" size="large" @click="openEdit(scope.row.id)">编辑</el-button>
            <el-button type="danger" size="large" @click="deleteReading(scope.row.id)">删除</el-button>
          </template>
        </el-table-column>
        <el-table-column prop="questionText" label="题目标题" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column prop="correctAnswer" label="题目答案"></el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <el-col :span="20">
      <el-pagination
        :current-page.sync="tablePage.currentPage"
        :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="tablePage.totalItems == '' ? 0 : tablePage.totalItems"
        @size-change="handleSizeChange"
        :page-size="tablePage.size"
        @current-change="handleCurrentChange"
      />
    </el-col>
  </div>
</template>

<script>
  import listenQuestionAPI from '@/api/omnipotentListening/listenQuestionAPI';
  import { pageParamNames } from '@/utils/constants';
  export default {
    data() {
      return {
        categoryType: [], // 所属分类

        // 分页
        tablePage: {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null
        },
        tableLoading: false,
        dataQuery: {
          questionOrder: '',
          questionTitle: ''
        },
        isRouterAlive: true, //局部刷新
        tableData: [], //表格数据
        addOrUpdate: true, // 是新增还是修改
        listeningName: '',
        courseId: '',
        materialsId: '',
        numberCheck: null
      };
    },
    created() {
      this.listeningName = window.localStorage.getItem('listeningName');
      this.materialsId = window.localStorage.getItem('materialsId');
      console.log(this.listeningName, '1111111111111111111111111');
      this.courseId = window.localStorage.getItem('courseId');
      this.fetchData();
    },
    methods: {
      handleInput(item) {
        this.numberCheck = item;
      },
      fetchData01() {
        if (this.numberCheck === '' || this.numberCheck === '0' || !/^[1-9]\d*$/.test(this.numberCheck)) {
          if (this.numberCheck === '' || this.numberCheck === null) {
            this.tablePage = {
              currentPage: 1,
              size: 10,
              totalPage: null,
              totalItems: null
            };

            this.fetchData();
          } else if (this.numberCheck === '0') {
            this.$message.error('请输入大于 0 的整数');
          } else {
            this.$message.error('请输入正整数');
          }
          return false;
        }

        this.tablePage = {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null
        };

        this.fetchData();
      },
      fetchDataReset() {
        this.tablePage = {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null
        };
        this.dataQuery.questionTitle = '';
        this.dataQuery.questionOrder = '';
        this.fetchData();
      },
      // 查询表格列表
      fetchData() {
        const that = this;
        that.tableLoading = true;
        let params = {
          pageNum: that.tablePage.currentPage,
          pageSize: that.tablePage.size,
          materialsId: that.materialsId,
          questionNumber: that.dataQuery.questionOrder,
          questionText: that.dataQuery.questionTitle
        };
        listenQuestionAPI.listListeningQuestion(params).then((res) => {
          that.tableData = res.data.data;
          that.tableLoading = false;
          pageParamNames.forEach((name) => that.$set(that.tablePage, name, parseInt(res.data[name] || 0)));
        });
      },

      //局部刷新
      reload() {
        this.isRouterAlive = false;
        this.$nextTick(function () {
          this.isRouterAlive = true;
        });
      },
      //添加操作
      clickAdd() {
        window.localStorage.setItem('materialsId', this.materialsId);
        window.localStorage.setItem('courseId', this.courseId);
        window.localStorage.setItem('listeningName', this.listeningName);
        this.$router.push({
          path: '/course/listenQuestionDialog',
          query: {
            addOrUpdate: true,
            materialsId: this.materialsId,
            courseId: this.courseId,
            courseName: this.listeningName
          }
        });
      },

      //删除听力
      deleteReading(id) {
        const that = this;
        this.$confirm('确定操作吗?', '删除数据', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            listenQuestionAPI.deleteListeningQuestion({ id: id }).then((res) => {
              that.$message.success('删除成功！');
              that.fetchData();
            });
          })
          .catch((err) => {});
      },

      // 编辑题目
      openEdit(id) {
        console.log('点击编辑', id);
        window.localStorage.setItem('courseId', this.courseId);
        window.localStorage.setItem('editId', id);
        this.$router.push({
          path: '/course/listenQuestionDialog',
          query: {
            addOrUpdate: false,
            editId: id,
            courseId: this.courseId
          }
        });
      },

      // 分页
      handleSizeChange(val) {
        this.tablePage.size = val;
        this.fetchData();
      },
      handleCurrentChange(val) {
        this.tablePage.currentPage = val;
        this.fetchData();
      },

      // 返回听力列表
      goBack() {
        this.$store.dispatch('delVisitedViews', this.$route);
        this.$router.push({
          path: '/course/newListenList'
        });
      },
      //新增
      handleAddFilterAttr() {
        this.filterProductAttrList.push({
          value: null,
          key: Date.now()
        });
      }
    }
  };
</script>

<style>
  .lh36 {
    line-height: 36px;
    font-size: 14px;
  }

  .SearchForm {
    width: 100%;
    margin-bottom: 15px;
    background: #fff;
    border-radius: 6px;
    padding: 5px 0;
    padding-left: 5px;
    box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.2);
  }

  .btn-add {
    padding: 5px;
    margin: 15px 0;
  }

  .red {
    color: red;
  }

  .green {
    color: green;
  }

  .course-table {
    text-align: center;
  }

  .course-table td,
  .course-table th {
    padding: 5px 0;
    text-align: center;
  }

  .course-table button {
    padding: 2px;
  }

  .icon-stop {
    float: left;
    display: inline-block;
    width: 12px;
    height: 12px;
    /* background: url("../../icons/stop.png") no-repeat top center/contain; */
  }

  .mt22 {
    margin-top: 22px;
  }

  .blue {
    margin-right: 50px;
    color: #409eff;
  }

  .clearfix {
    color: red;
  }

  @media (max-width: 767px) {
    .el-message-box {
      width: 80% !important;
    }
  }
</style>
