<template>
  <div>
    <!-- 查询 -->
    <el-form
      :model="dataQuery"
      ref="queryForm"
      :inline="true"
      label-width="70px"
    >
      <el-row type="flex" justify="center">
        <el-col :span="7">
          <el-form-item label="题目ID：">
            <el-input
              v-model="dataQuery.id"
              placeholder="请输入题目id"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="7">
          <el-form-item label="题型：">
            <el-select v-model="dataQuery.questionType" clearable>
              <el-option
                v-for="item in questionType"
                :key="item.value"
                :value="item.value"
                :label="item.label"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="知识点：">
            <treeselect
              v-model="dataQuery.knowledgePoints"
              :multiple="true"
              :value-consists-of="valueConsistsOf"
              :options="konwledgeTree"
              :normalizer="normalizer"
              noOptionsText="无可用知识点"
              placeholder="请选择知识点"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item>
        <el-button type="primary" style="marginright: 5px" @click="initData"
          >查询</el-button
        >
      </el-form-item>
    </el-form>
    <!-- 表格 -->
    <el-table
      ref="multipleTable"
      :data="tableData"
      tooltip-effect="dark"
      border
      style="width: 100%; margin-bottom: 20px"
      :expand-row-keys="expands"
      :row-key="getRowKeys"
      :reserve-selection="true"
      @selection-change="handleSelectionChange"
      @expand-change="showQuestion"
    >
      <el-table-column
        type="selection"
        width="50"
        align="center"
      ></el-table-column>
      <el-table-column type="expand">
        <template slot-scope="props">
          <QuestionShow
            v-if="!questionShow.loading"
            :qType="props.row.questionType"
            :question="questionShow.question"
            :qLoading="questionShow.loading"
          />
        </template>
      </el-table-column>
      <el-table-column prop="id" label="Id" width="200" align="center" />
      <el-table-column
        prop="questionType"
        label="题型"
        :formatter="questionTypeFormatter"
        width="120"
        align="center"
      />
      <el-table-column prop="topic" label="题目" show-overflow-tooltip>
        <template slot-scope="scope">
          <div v-html="scope.row.topic"></div>
        </template>
      </el-table-column>
      <el-table-column prop="score" label="分数" width="60" align="center" />
      <el-table-column
        prop="difficulty"
        label="难度"
        width="60"
        align="center"
      />
    </el-table>
    <!-- 分页 -->
    <el-pagination
      :current-page="tablePage.currentPage"
      :page-sizes="[10, 20, 30, 40, 50]"
      layout="total, sizes, prev, pager, next, jumper"
      :total="tablePage.totalItems"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<script>
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import "/public/components/ueditor/themes/iframe.css";
import QuestionShow from "../components/Show";
import { pageParamNames } from "@/utils/constants";
import questionApi from "@/api/paper/question";
import { mapGetters, mapState } from "vuex";

export default {
  name: "QuestionItemAdd",
  components: {
    Treeselect,
    QuestionShow,
  },
  props: {
    konwledgeTree: {
      type: Array,
      default: function () {
        return [];
      },
    },
    grade: {
      type: Number,
      default: function () {
        return 0;
      },
    },
    subjectId: {
      type: String,
      default: function () {
        return "";
      },
    },
    sonSelectData: {
      type: Array,
      default: function () {
        return [];
      },
    },
  },
  data() {
    return {
      valueConsistsOf: "LEAF_PRIORITY",
      dataQuery: {
        id: null,
        questionType: null,
        knowledgePoints: null,
        subjectId: null,
        grade: null,
      },
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null,
      },
      questionShow: {
        qType: 0,
        dialog: false,
        question: null,
        loading: false,
      },
      tableData: [], // 表格数据
      expands: [], // 扩展
    };
  },
  created() {
    this.initData();
  },
  updated() {

  },
  methods: {
    // 初始化数据
    initData() {
      this.dataQuery.subjectId = this.subjectId;
      this.dataQuery.grade = this.grade;
      this.dataQuery.pageNum = this.tablePage.currentPage;
      this.dataQuery.pageSize = this.tablePage.size;
      questionApi.pageList(this.dataQuery).then((res) => {
        this.tableData = res.data.data;

        this.$nextTick(() => {
        //   this.tableData.forEach((item) => {
        //     if (condition) {
        //     }
        //   });
        this.toggleSelection(this.sonSelectData)
        });

        // 设置后台返回的分页参数
        pageParamNames.forEach((name) =>
          this.$set(this.tablePage, name, parseInt(res.data[name]))
        );
      });
    },

    toggleSelection(rows) {
      if (rows) {
        rows.forEach((row) => {
          this.tableData.forEach((item) => {
            if (item.id === row.id) {

              this.$refs.multipleTable.toggleRowSelection(row, true);
            }
          });
        });
      }
    },
    // 树形选择器default
    normalizer(node) {
      return {
        id: node.id,
        label: node.name,
        children: node.child,
      };
    },
    getRowKeys(row) {
      return row.id;
    },
    // expand题目预览
    showQuestion(row, expandedRows) {
      let _this = this;
      if (expandedRows.length) {
        _this.expands = [];
        if (row) {
          _this.expands.push(row.id);
        }
      } else {
        _this.expands = [];
      }
      this.questionShow.dialog = true;
      this.questionShow.loading = true;
      questionApi.select(row.id).then((re) => {
        _this.questionShow.qType = re.data.questionType;
        _this.questionShow.question = re.data;
        _this.questionShow.loading = false;
      });
    },
    // 选中子题
    handleSelectionChange(val) {
      this.sonSelectData = val;
      this.$message.success("操作成功");
      this.$emit("selectionChange", this.sonSelectData);
    },
    // 取消选中子题
    toggleSelection(rows) {
      if (rows) {
        rows.forEach((row) => {
          this.$refs.multipleTable.toggleRowSelection(row);
        });
      } else {
        this.$refs.multipleTable.clearSelection();
      }
    },
    // 题型
    questionTypeFormatter(row, column, cellValue, index) {
      return this.enumFormat(this.questionType, cellValue);
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val;
      this.getPageList();
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val;
      this.getPageList();
    },
  },
  computed: {
    ...mapGetters("enumItem", ["enumFormat", "subjectFormat", "pageFormat"]),
    ...mapState("enumItem", {
      gradeList: (state) => state.question.gradeList,
      questionType: (state) => state.question.typeList,
      editUrlEnum: (state) => state.question.editUrlEnum,
    }),
  },
};
</script>

<style lang="less">
.vue-treeselect {
  width: 400px;
}
.el-dialog--center .el-dialog__body {
  padding: 0px 25px 30px;
}
</style>
