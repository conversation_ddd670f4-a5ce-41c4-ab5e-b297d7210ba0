<template>
  <div v-if="datalist.children !== null" style="width: 100%;" :style="{ height: chartHeight + 'px' }">
    <div id="myElement" class="container" style="width: 100%;height: 100%;">
      <div style="display: flex;justify-content: end;">
        <el-button type="primary" size="small" @click="expandAll">全部展开</el-button>
        <i v-if="!screenfullBut" class="iconfont icon-quanping" style="font-size: 34px;margin-left: 20px;"
          @click="fullscreen"></i>
        <i v-if="screenfullBut" class="iconfont icon-tuichuquanping" style="font-size: 34px;margin-left: 20px;"
          @click="fullscreen"></i>
      </div>
      <div id="main" style="width: 100%; height:50vw" ref="chart"></div>
    </div>
  </div>

  <div v-else style="margin: 20px;">
    <el-card class="box-cards">
      <img src="@/assets/no_data.png" class="image">
      <div class="title-toast">暂无下级，快去推荐吧</div>
    </el-card>
  </div>
</template>
  
<script>
import * as echarts from "echarts";
// import screenfull from 'screenfull'
import recommendApi from '@/api/recommendationChain'

export default {
  name: 'MyData',
  data() {
    return {
      treeData: [],
      treelist: {},
      treeOptions: {},
      screenfullBut: false,
      // showNum: 100,
      chart: null,
      zoom: 1,
      datalist: {},
      list: {},
      chartHeight: 0,
    };
  },
  async mounted() {
    await this.getRecommendlist();
  },
  methods: {
    echartsInit() {
      this.screenHeight = window.innerHeight - 80;
      // console.log(this.screenHeight)
      // const myEcharts = echarts.init(this.$refs.chart);
      this.chart.resize();
      // 监听窗口大小变化，并重置图表大小
      window.addEventListener('resize', () => {
        this.chart.resize();
      })
      this.chart.on('zoom', function (params) {
        console.log('11111111111111111')
        console.log('图表被放大/缩小:', params);
        var option = this.chart.getOption();
        this.zoom = option.series[0].zoom || 1; // 获取当前缩放级别，默认为 1
        // 根据滚轮事件的 deltaY 属性来判断滚动方向
        if (params.event.wheelDelta > 0) {
          // 滚轮向上滚动，放大图表
          if (this.zoom >= 3) {
            // 阻止默认滚轮事件
            params.event.preventDefault();
            return;
          }
          this.zoom += 0.1;
        } else {
          if (this.zoom <= 0.5) {
            // 阻止默认滚轮事件
            params.event.preventDefault();
            return;
          }
          // 滚轮向下滚动，缩小图表
          this.zoom -= 0.1;
        }
        // 设置新的缩放级别
        option.series[0].zoom = zoom;
        this.chart.setOption(option);
      });

      this.chart.setOption(this.treeOptions, true);
      // echarts.dispose();
      // this.updateZoom();
    },


    fullscreen() {
      var element = document.getElementById('myElement');
      // 检查浏览器是否支持全屏切换
      if (element.requestFullscreen) {
        // 切换到全屏模式
        this.screenfullBut = !this.screenfullBut;
        if (this.screenfullBut) {
          this.chart.resize();
          // 监听窗口大小变化，并重置图表大小
          window.addEventListener('resize', () => {
            this.chart.resize()
          })
          element.requestFullscreen();
        } else {
          // 若要关闭全屏，可以调用以下方法
          document.exitFullscreen();
          this.chart.resize();
          // 监听窗口大小变化，并重置图表大小
          window.addEventListener('resize', () => {
            this.chart.resize()
          })
        }
      } else {
        this.$message.error('您的浏览器无法使用全屏功能，请更换谷歌浏览器或者请手动点击F11按钮全屏展示！');
        return false;
      }
    },

    // 放大
    // zoomIn() {
    //   console.log(this.chart)
    //   this.zoom += 0.2;
    //   // this.zoom = this.zoom + 0.2;
    //   this.showNum = this.zoom * 100;
    //   // this.chart.resize()
    //   this.updateZoomLevel();

    // },
    // 缩小
    // zoomOut() {
    //   // this.chart.zoom(0.8);
    //   // this.zoom = this.zoom - 0.2;
    //   this.zoom -= 0.2;
    //   this.showNum = this.zoom * 100;
    //   // this.chart.resize()
    //   this.updateZoomLevel();
    // },


    updateZoomLevel() {
      var option = this.chart.getOption();
      option.series[0].zoom = this.zoom;
      this.chart.setOption(option, true);
    },

    getRecommendlist() {
      let that = this;
      const loading = this.$loading({
          lock: true,
          text: '加载中...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        })
      recommendApi.recommendList().then(res => {
        that.datalist = res.data;
        if(that.datalist.children==null){
          loading.close()
        }
        if (that.datalist.merchantName != '') {
          that.datalist.name = that.datalist.merchantName;
          delete that.datalist.merchantName;
        } else {
          that.datalist.name = that.datalist.roleTag;
          delete that.datalist.roleTag;
        }
        that.datalist.value = that.datalist.id;
        delete that.datalist.id;
        const replaceKey = (obj, keyMap) => {
          for (const key in obj) {
            if (keyMap.hasOwnProperty(key)) {
              obj[keyMap[key]] = obj[key];
              delete obj[key];
            } else if (typeof obj[key] === "object") {
              obj[key] = replaceKey(obj[key], keyMap);
            }
          }
          return obj;
        };
        const keyMap = {
          "id": "value",
          "merchantName": "name"
        };
        const transformedData = that.datalist.children.map(item => replaceKey(item, keyMap));
        that.datalist.children = transformedData;
        that.treelist = that.datalist;
        that.treeOptions = {
          tooltip: {
            trigger: 'item',
            triggerOn: 'mousemove',
            formatter: '{b0}'
          },
          series: [
            {
              type: 'tree',
              id: 0,
              name: 'tree1',
              data: [that.treelist],
              top: '1%',
              left: '5%',
              bottom: '10%',
              right: '10%',
              symbolSize: 10,
              zoom: 1, //当前视角的缩放比例
              roam: true, //是否开启平游或缩放
              scaleLimit: { //滚轮缩放的极限控制
                min: 0.5,
                max: 3
              },
              edgeShape: 'polyline',
              edgeForkPosition: '63%',
              initialTreeDepth: 3,
              lineStyle: {
                width: 2
              },
              label: {
                backgroundColor: '#fff',
                position: 'left',
                verticalAlign: 'middle',
                align: 'right',
                emphasis: {
                  show: false // 取消高亮时的标签展示
                }
              },
              leaves: {
                label: {
                  position: 'right',
                  verticalAlign: 'middle',
                  align: 'left'
                }
              },
              symbolSize: [15, 15],
              symbol: function (params, params1) {
                // 判断当前节点是否折叠
                if (params1.collapsed == true && params1.data.children) {
                  let data = 'image://https://bpic.588ku.com/element_origin_min_pic/01/37/77/94573c60c2ecc1e.jpg'
                  return data
                } else {
                  // echarts.dispose();
                  // console.log('false')
                  // if (params1.data.children != null) {
                  //   let data = 'image://https://bpic.588ku.com/element_origin_min_pic/01/35/82/55573bf7289c556.jpg'
                  //   return data
                  // }
                }
              },
              // emphasis: {
              //   focus: 'descendant'
              // },
              expandAndCollapse: true,
              animationDuration: 500,
              animationDurationUpdate: 750
            }
          ]
        }
        // 获取 DOM 元素
        const chartDom = that.$refs.chart;
        // 初始化 ECharts 实例
        that.chart = echarts.init(chartDom);
        that.chart.setOption(that.treeOptions, true)
        const eleArr = Array.from(new Set(that.chart._chartsViews[0]._data._graphicEls))
        // const treeDepLength = myChart._chartsViews[0]._data.tree.root.height                            // 获取树的高度
        // const itemHeight = 100       // 设置每层树的间距（垂直型树高）
        // let currentHeight = itemHeight * (treeDepLength + 1) || itemHeight
        const itemHeight = 45
        const currentHeight = itemHeight * (eleArr.length - 1) || itemHeight
        const newHeight = Math.max(currentHeight, itemHeight)
        this.chartHeight = newHeight
        that.chart.resize({
          height: newHeight
        })
        loading.close()
        // that.chart.on("click", this.treeNodeclick);
      }).catch(err=>{
        loading.close()
      })
    },

    expandAll() {
      const loading = this.$loading({
        lock: true,
        text: '加载中...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      this.treeOptions = {
        tooltip: {
          trigger: 'item',
          triggerOn: 'mousemove',
          formatter: '{b0}'
        },
        series: [
          {
            type: 'tree',
            id: 0,
            name: 'tree1',
            data: [this.treelist],
            top: '1%',
            left: '5%',
            bottom: '10%',
            right: '10%',
            symbolSize: 10,
            zoom: 1, //当前视角的缩放比例
            roam: true, //是否开启平游或缩放
            scaleLimit: { //滚轮缩放的极限控制
              min: 0.5,
              max: 3
            },
            edgeShape: 'polyline',
            edgeForkPosition: '63%',
            initialTreeDepth: -1,
            lineStyle: {
              width: 2
            },
            label: {
              backgroundColor: '#fff',
              position: 'left',
              verticalAlign: 'middle',
              align: 'right',
              emphasis: {
                show: false // 取消高亮时的标签展示
              }
            },
            leaves: {
              label: {
                position: 'right',
                verticalAlign: 'middle',
                align: 'left'
              }
            },
            symbolSize: [15, 15],
            symbol: function (params, params1) {
              // 自定义图片后，不知道是否还有子节点未展开
              // 通过打印，发现没有展开的节点，和最后一层子节点的collapsed属性都为true
              // 所以判断collapsed为true，并且没有孩子节点的时候，就是还有没展开的子节点
              // 修改label的样式，来判断是否还存在子节点没展开
              if (params1.collapsed == true && params1.data.children) {
                let data = 'image://https://bpic.588ku.com/element_origin_min_pic/01/37/77/94573c60c2ecc1e.jpg'
                return data
              } else {
                // echarts.dispose();
                // console.log('false')
                // if (params1.data.children != null) {
                //   let data = 'image://https://bpic.588ku.com/element_origin_min_pic/01/35/82/55573bf7289c556.jpg'
                //   return data
                // }
              }
            },
            // emphasis: {
            //   focus: 'descendant'
            // },
            expandAndCollapse: true,
            animationDuration: 550,
            animationDurationUpdate: 750
          }
        ]
      }
      const eleArr = Array.from(new Set(this.chart._chartsViews[0]._data._graphicEls))
      // const treeDepLength = myChart._chartsViews[0]._data.tree.root.height                            // 获取树的高度
      // const itemHeight = 100       // 设置每层树的间距（垂直型树高）
      // let currentHeight = itemHeight * (treeDepLength + 1) || itemHeight
      const itemHeight = 45
      const currentHeight = itemHeight * (eleArr.length - 1) || itemHeight
      const newHeight = Math.max(currentHeight, itemHeight)
      this.chartHeight = newHeight
      this.chart.resize({
        height: newHeight
      })
      this.chart.clear();	//清除画布
      window.onresize = this.chart.resize;  //防止越界，重绘canvas
      this.treeOptions && this.chart.setOption(this.treeOptions, true);
      loading.close()
    },

    // 节点点击事件
    treeNodeclick(param) {
      /* true 代表点击的是圆点
         fasle 表示点击的是当前节点的文本
      */
      if (param.event.target.culling === true) {
        // this.chart.setOption(this.treeOptions,true);
        // this.chart.resize();
      } else if (param.event.target.culling === false) {
        return
      }
    },


    // updateZoom() {
    //   debugger
    //   // this.chart.on('click', function (params) {
    //   //   window.open('https://www.baidu.com/s?wd=' + encodeURIComponent(params.name));
    //   // });
    //   this.chart.on('zoom', function (params) {
    //     console.log('11111111111111111')
    //     console.log('图表被放大/缩小:', params);
    //     var option = this.chart.getOption();
    //     this.zoom = option.series[0].zoom || 1; // 获取当前缩放级别，默认为 1
    //     // 根据滚轮事件的 deltaY 属性来判断滚动方向
    //     if (params.event.wheelDelta > 0) {
    //       // 滚轮向上滚动，放大图表
    //       if(this.zoom>=3){
    //         // 阻止默认滚轮事件
    //         params.event.preventDefault();
    //         return;
    //       }
    //       this.zoom += 0.1;
    //     } else {
    //       if(this.zoom<=0.5){
    //         // 阻止默认滚轮事件
    //         params.event.preventDefault();
    //         return;
    //       }
    //       // 滚轮向下滚动，缩小图表
    //       this.zoom -= 0.1;
    //     }
    //     // 设置新的缩放级别
    //     option.series[0].zoom = zoom;
    //     this.chart.setOption(option);
    //   });
    // }



    // 全屏功能
    // fullscreen() {
    //   if (!screenfull.isEnabled) {
    //     // this.$notification.open({
    //     //   message: '温馨提示',
    //     //   description:
    //     //     '您的浏览器无法使用全屏功能，请更换谷歌浏览器或者请手动点击F11按钮全屏展示！',
    //     //   duration: 10,
    //     //   placement: 'bottomLeft',
    //     // });
    //     this.$message.error('您的浏览器无法使用全屏功能，请更换谷歌浏览器或者请手动点击F11按钮全屏展示！');
    //     return false;
    //   }
    //   screenfull.toggle();
    //   if(screenfull.isFullscreen){
    //     this.screenfullBut = false;
    //   }else{
    //     this.screenfullBut = true;
    //   }
    // },
  },
};
</script>
<style lang="scss" scoped>
.period-table td,
.period-table th {
  text-align: center;
}

.mt20 {
  margin-top: 20px;
}

.container {
  position: relative;
  background-color: #fff !important;
  padding: 0 40px !important;
}

.app-main {
  padding-top: 20px;
  height: 100% !important;
}

.operate {
  display: flex;
  align-items: center;
  position: absolute;
  bottom: 20px;
  right: 40px;
}

.el-icon-minus {
  font-size: 26px;
  margin-left: 30px;
  margin-right: 15px;
}

.el-icon-plus {
  font-size: 26px;
  margin-left: 15px;
}

.box-cards {
  width: 100%;
  height: 40vw;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.image {
  width: 140px;
  height: 140px;
}

.title-toast {
  margin-top: 20px;
  color: #6c6b6b;
}
</style>
  