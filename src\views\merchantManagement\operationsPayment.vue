<template>
  <div class="app-container" v-loading="loading">
    <div class="title">
      <el-steps :active="active - 1" finish-status="success" process-status="finish">
        <!-- <el-step title="签署合同" description="查看手机短信访问链接或扫码签署开通合同"></el-step> -->
        <el-step :title="`选择${isPay == 1 ? '补款' : '完款'}类型及方式`" :description="`选择以何种类型和方式${isPay == 1 ? '补款' : '完款'}开通俱乐部`"></el-step>
        <el-step :title="`进行${isPay == 1 ? '补款' : '完款'}`" :description="`${isPay == 1 ? '补款' : '完款'}进行中`"></el-step>
        <el-step :title="isPay == 1 ? '补款成功' : '俱乐部开通成功'" description="已经完成了"></el-step>
      </el-steps>
    </div>
    <div class="content" v-if="active === 0">
      <div class="red" style="margin-bottom: 10px">请使用企业/个体工商户身份签署，如未完成签署，在30天内补签</div>
      <div style="margin-bottom: 10px">邀请该俱乐部负责人,使用手机扫码签署</div>
      <!-- <div style="margin-bottom: 20px">《学习管理系统合同》</div> -->
      <div style="margin-bottom: 20px">《鼎校智能学习管理系统经销商销售合同》</div>
      <div class="code" v-if="stduyCode">
        <el-image :src="stduyCode.qrUrl" style="width: 100%; height: 100%" :lazy="true"></el-image>
        <!-- <el-image @click="changeSign()" src="https://document.dxznjy.com/course/bf188f4751c84e94b0678b463f7054a4.png" style="width: 100%; height: 100%" :lazy="true"></el-image> -->
        <div class="codeSuccess" v-if="contractflag">
          <i class="el-icon-circle-check" style="color: #13ce66; font-size: 25px"></i>
        </div>
      </div>
    </div>

    <div class="content" v-if="active === 1">
      <el-radio-group v-model="paymentType" @change="payTyoeChange" v-if="isPay == 0">
        <el-radio-button :label="1">全款完款</el-radio-button>
        <el-radio-button :label="2" :disabled="hasDepositOperations == 0">定金完款</el-radio-button>
      </el-radio-group>
      <div class="paymentType">
        <div :style="isPay == 1 ? '' : 'margin-right: 180px'" v-if="paymentType == 1">
          <div class="img" :class="payType === 1 ? 'checek' : ''" @click="payType = 1">
            <i class="el-icon-circle-check right" v-if="payType === 1"></i>
            <el-image src="https://document.dxznjy.com/course/fb823bf6e1b8460daea232ebe883f88d.png" style="width: 170px; height: 170px" :lazy="true"></el-image>
            <div>线上支付{{ paymentType == 2 && isPay == 0 ? '定金' : '' }}{{ isPay == 1 ? '补款' : '完款' }}</div>
          </div>
        </div>
        <div v-if="isPay == 0">
          <div class="img" :class="payType === 2 ? 'checek' : ''" @click="payType = 2">
            <i class="el-icon-circle-check right" v-if="payType === 2"></i>
            <el-image src="https://document.dxznjy.com/course/fb390e3770a14074892dc95c2133e011.png" style="height: 100px; margin-top: 40px" :lazy="true"></el-image>
            <div style="margin-top: 40px">{{ paymentType == 1 ? '全款' : '定金' }}俱乐部智能学习管理系统开通抵扣</div>
          </div>
        </div>
      </div>
    </div>
    <div class="sucontent" v-if="active === 4">
      <i class="el-icon-circle-check success"></i>
      <!-- <div style="line-height: 30px">恭喜您，俱乐部开通成功！请您继续完成以下合同签署</div> -->
      <div style="line-height: 30px">恭喜您，俱乐部开通成功！</div>
      <!-- <div class="line"></div>
      <div class="successCodes">
        <div class="sucontent" style="margin-right: 100px" v-if="isBrandRoleTag == 0">
          <div style="line-height: 30px; font-size: 14px">邀请该俱乐部的渠道合作伙伴负责人，使用手机扫码签署</div>
          <div style="line-height: 30px; font-size: 14px; margin-bottom: 10px">《渠道合作伙伴协议》</div>
          <div class="code">
            <el-image :src="codeList[0].qrUrl" style="width: 100%; height: 100%" :lazy="true"></el-image>
            <div class="codeSuccess" v-if="codeList[0].signStatus == 2">
              <i class="el-icon-circle-check" style="color: #13ce66; font-size: 25px"></i>
            </div>
          </div>
        </div>

        <div class="sucontent">
          <div style="line-height: 30px; font-size: 14px">邀请该俱乐部负责人，使用手机扫码签署</div>
          <div style="line-height: 30px; font-size: 14px; margin-bottom: 10px">《渠道合作伙伴协议》</div>
          <div class="code">
            <el-image :src="codeList[1].qrUrl" style="width: 100%; height: 100%" :lazy="true"></el-image>
            <div class="codeSuccess" v-if="codeList[1].signStatus == 2">
              <i class="el-icon-circle-check" style="color: #13ce66; font-size: 25px"></i>
            </div>
          </div>
        </div>
      </div> -->
    </div>
    <div class="bottom">
      <el-button size="medium" @click="back">返回</el-button>
      <el-button v-if="active == 0" size="medium" @click="skip">跳过</el-button>
      <!-- TODO: 6.13隐藏 -->
      <!-- <el-button v-if="active == 4" size="medium" @click="back">跳过</el-button> -->
      <!-- end -->
      <el-button v-if="active != 4" type="primary" size="medium" @click="goNext">下一步</el-button>
    </div>
    <!-- 俱乐部完款/补款弹窗 -->
    <el-dialog title="选择定金俱乐部智能学习管理系统" :visible.sync="earnestDialog" width="30%" @close="earnestCencel">
      <!-- <el-form label-width="150px" label-position="right" style="text-align: left"> -->
      <!-- <el-form-item> -->
      <el-radio-group v-model="depositType" style="margin: 0 auto">
        <el-radio
          style="height: 40px; margin: 0 auto 5px; padding: 0 0 0; width: 100%; text-align: center"
          :label="item.paymentType"
          border
          v-for="item in queryDepositDetail"
          :key="item.type"
        >
          <div>
            {{ item.name }}
            <span style="margin-left: 20px">X{{ item.count }}</span>
          </div>
        </el-radio>
      </el-radio-group>
      <!-- </el-form-item> -->
      <!-- 提示 -->
      <!-- </el-form> -->
      <span slot="footer">
        <el-button @click="earnestDialog = false">取消</el-button>
        <el-button type="primary" @click="earnestbtnOK">确定</el-button>
      </span>
    </el-dialog>

    <!-- 俱乐部支付 -->
    <el-dialog title="请完成俱乐部开通支付" :visible.sync="payOrSuccess" width="30%" :close-on-click-modal="false" :close-on-press-escape="false" :show-close="false">
      <span></span>
      <span slot="footer">
        <el-button @click="paySrtInt(2)">已完成支付</el-button>
        <el-button type="primary" @click="payCancel">未完成支付</el-button>
      </span>
    </el-dialog>
    <!-- 抵扣弹窗 -->
    <el-dialog :visible.sync="deductionIsShow" width="30%" center :close-on-click-modal="false" :close-on-press-escape="false" :show-close="false">
      <template slot="title">
        <div style="font-size: 14px; text-align: center; margin: 0 20px; line-height: 20px">{{ deductionTitle }}</div>
      </template>
      <div style="text-align: center; color: red; font-size: 14px">
        {{ merchantName }}
      </div>
      <span slot="footer">
        <el-button @click="deductionCancel">取消</el-button>
        <el-button type="primary" @click="deductionOK" :disabled="deloading">确定</el-button>
      </span>
    </el-dialog>
    <!-- 补款弹框 -->
    <el-dialog :visible.sync="makeUpPayment" width="25%" center title="补款信息确认">
      <el-form :model="makeUpForm" ref="makeUpForm" label-width="120px" :inline="false" size="normal">
        <el-form-item label="俱乐部名称">
          {{ makeUpForm.operationsName }}
        </el-form-item>
        <el-form-item label="完款金额">
          {{ makeUpForm.paymentAmount * 0.01 }}
        </el-form-item>
        <el-form-item label="定金抵扣">
          {{ makeUpForm.depositDeduction * 0.01 }}
        </el-form-item>
        <el-form-item label="剩余完款金额">
          {{ makeUpForm.remainingAmount * 0.01 }}
        </el-form-item>
        <el-form-item></el-form-item>
      </el-form>

      <span slot="footer">
        <el-button @click="makeUpCancel">取消</el-button>
        <el-button type="primary" @click="makeUpOK">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
  import dealerPaylist from '@/api/operationsPayment';
  import store from '@/store';
  import dealerListApi from '@/api/operationsList';
  import { mapGetters } from 'vuex';
  import { dxSource } from '@/utils/constants';
  export default {
    // name: "dealerList",
    data() {
      return {
        makeUpPayment: false,
        deloading: false,
        QrPayCode: '',
        loading: false,
        id: '',
        makeUpForm: {},
        active: 1,
        signObj: null,
        paymentType: 1,
        depositType: 0,
        payType: 1,
        earnestDialog: false,
        queryDepositDetail: [], //定金抵扣类型
        payStatus: 1,
        isPay: 0,
        earnest: {
          amount: '',
          count: ''
        },
        codeList: [],
        isBrandRoleTag: 1,
        deductionIsShow: false,
        stduyCode: null,
        deductionTitle: '请确定是否分配一套俱乐部智能学习学习管理系统，并支付10000元技术服务费，为以下俱乐部开通智能学习管理系统账号，开通后将无法撤回',
        payOrSuccess: false,
        merchantName: '', //俱乐部名称
        contractTime: null,
        sourceOrderId: 0,
        merchantCode: '',
        contractTemp: false, //学习管理合同轮询结束
        contractTimes: 0, //学习管理合同轮询时间，
        contractflag: false, //学习管理合同签署状态
        addDialogVisible: false, //购买采购单弹框，
        count: 0,
        hasDepositOperations: 0, //是否有定金完款学习管理系统
        flowId: '',
        templateType: ''
      };
    },
    created() {
      this.queryDepositDetail = [
        {
          name: '定金：60000元 含6套合伙人智能学习管理系统',
          count: 0,
          type: 6,
          paymentType: 3
        },
        {
          name: '定金：70000元 含7套合伙人智能学习管理系统',
          count: 0,
          type: 7,
          paymentType: 4
        },
        {
          name: '定金：80000元 含8套合伙人智能学习管理系统',
          count: 0,
          type: 8,
          paymentType: 5
        },
        {
          name: '定金：90000元 含9套合伙人智能学习管理系统',
          count: 0,
          type: 9,
          paymentType: 6
        },
        {
          name: '定金：100000元 含10套合伙人智能学习管理系统',
          count: 0,
          type: 10,
          paymentType: 7
        },
        {
          name: '定金：110000元 含11套合伙人智能学习管理系统',
          count: 0,
          type: 11,
          paymentType: 8
        }
      ];
      this.merchantName = this.$route.query.merchantName;
      this.id = this.$route.query.id;
      this.merchantCode = this.$route.query.merchantCode;
      if (this.merchantCode) {
        sessionStorage.setItem('merchantCode', this.merchantCode);
      } else {
        this.merchantCode = sessionStorage.getItem('merchantCode');
      }
      this.isPay = this.$route.query.isPay;
      if (this.isPay == 1) {
        this.active = 1;
        this.paymentType = 1;
        this.payStatus = 5;
        this.arginInit(2);
      } else {
        this.arginInit(1);
        this.init();
        this.clearContractTime();
      }
    },
    watch: {
      payType: function (val) {
        if (this.paymentType == 1 && val == 1) {
          this.payStatus = 1;
        } else if (this.paymentType == 1 && val == 2) {
          this.payStatus = 2;
        } else if (this.paymentType == 2 && val == 1) {
          this.payStatus = 3;
        } else {
          this.payStatus = 4;
        }
      }
    },
    computed: {
      ...mapGetters(['setpayUrl', 'token'])
    },
    beforeDestroy() {
      this.clearContractTime();
    },
    mounted() {},
    methods: {
      async changeSign() {
        let data = {
          firstSignStatus: 2,
          secondSignStatus: 2,
          templateType: this.templateType,
          signSource: 1,
          flowId: this.flowId
        };
        const res = await dealerPaylist.compensationSignStatus(data);
        if (res.sccess) {
          this.$message.success('签署成功');
        }
      },
      makeUpCancel() {
        this.makeUpPayment = false;
      },
      makeUpOK() {
        let that = this;
        let query = {
          merchantCode: this.merchantCode,
          paymentMethod: 2,
          paymentType: 9
        };
        dealerPaylist.operationsPayment(query).then((res) => {
          that.makeUpPayment = false;
          that.sourceOrderId = res.data.sourceOrderId;
          const split = dxSource.split('##');
          res.data.dxSource = res.data.registerCallIInfo.appSource + '##' + split[1] + '##' + split[2];
          let params = JSON.stringify(res.data);
          let req = 'token=' + that.token + '&params=' + params + '&back=' + window.location.href;
          //需要编码两遍，避免出现+号等
          var encode = Base64.encode(Base64.encode(req));
          that.disabledF = false;
          window.open(that.setpayUrl + 'product?' + encode, '_blank');
          that.clearContractTime();
          that.contractInterval(that.paySrtInt);
          this.payOrSuccess = true;
          that.active = 2;
        });
      },
      async arginInit(i) {
        let obj;
        if (i == 1) {
          obj = { merchantCode: this.merchantCode, paymentType: 1 };
        } else {
          obj = { merchantCode: this.merchantCode, paymentType: 2 };
        }
        let { data } = await dealerPaylist.amountConfig(obj);
        if (i == 1) {
          this.count = data.operationsOnlineSysNum;
          // 获取商户抵扣完款应付金额
          this.operationsDeductionLowest = data.operationsDeductionLowest * 0.01;
          this.hasDepositOperations = data.hasDepositOperations || 0;
        } else {
          this.makeUpForm = {
            paymentAmount: data.paymentAmount,
            depositDeduction: data.depositDeduction,
            remainingAmount: data.remainingAmount,
            operationsName: data.operationsName
          };
          this.count = data.remainderSysNum;
        }
      },
      back() {
        this.clearContractTime();
        this.$router.back();
      },
      payTyoeChange(e) {
        this.payType = 1;
        if (e == 1) {
          this.payStatus = 1;
        } else {
          this.payStatus = 3;
        }
      },
      payCancel() {
        this.clearContractTime();
        this.active = 1;
        this.payOrSuccess = false;
      },
      // 上传图片数量超限
      justPictureNum(file, fileList) {
        this.$message.warning(`当前限制选择1个文件`);
      },
      // 删除图片
      handleRemoveDetailContract(file, fileList) {
        const that = this;
        that.imageList = fileList;
      },
      // 上传图片预览
      handlePictureCardPreviewContract(file) {
        this.dialogImageUrl = file.url;
        this.dialogVisible = true;
      },

      // 上传图片
      uploadDetailHttpContract({ file }) {
        this.uploadLoading = true;
        const that = this;
        const fileName = 'manage/' + Date.parse(new Date());
        that.$nextTick(function () {
          ossPrClient()
            .put(fileName, file)
            .then(({ res, url, name }) => {
              if (res && res.status === 200) {
                console.log(`阿里云OSS上传图片成功回调1`, res, url, name);
                that.imageList.push({
                  uid: file.uid,
                  url: url
                });
                that.$nextTick(() => {
                  that.uploadLoading = false;
                });
              }
            })
            .catch((err) => {
              that.$message.error('上传图片失败请检查网络或者刷新页面');
              console.log(`阿里云OSS上传图片失败回调`, err);
              that.uploadLoading = false;
            });
        });
      },
      beforeAvatarUpload(file) {
        const isJPG = file.type === 'image/png' || file.type === 'image/jpeg';
        const isLt2M = file.size / 1024 / 1024 < 10;

        if (!isJPG) {
          this.$message.error('上传头像图片只能是 JPG 格式!');
        }
        if (!isLt2M) {
          this.$message.error('上传头像图片大小不能超过 10MB!');
        }
        return isJPG && isLt2M;
      },
      changeAmount(e) {
        if (this.earnest.amount < 70000 && this.payStatus == 3 && this.isPay == 0) {
          this.$message.warning('首次定金金额不可小于70000元');
          this.earnest.amount = null;
        }
        if (this.earnest.amount > 210001 && this.payStatus == 3 && this.isPay == 0) {
          this.$message.warning('首次定金金额不可大于210000元');
          this.earnest.amount = null;
        }
      },
      skip() {
        this.clearContractTime();
        this.active++;
      },
      deductionCancel() {
        this.active--;
        this.deductionIsShow = false;
      },
      clearContractTime() {
        clearInterval(this.contractTime);
        this.contractTime = null;
        this.contractTimes = 0;
        this.contractTemp = false;
      },
      contractStart() {
        if (this.contractflag) {
          this.active++;
          this.clearContractTime();
        } else {
          this.qrSrtInt();
          this.$message.warning('请完成合同签署后重试');
        }
      },
      async init() {
        this.loading = true;
        let res = await dealerPaylist.operationsPaymentQuery({
          merchantCode: this.merchantCode
        });
        if (res.data.secondApproveStatus == 2) {
          this.loading = false;
          this.active++;
        } else {
          let obj = {
            flowId: res.data.signFlowId,
            templateType: res.data.templateType,
            signSource: res.data.signSource,
            isQrLink: 1
          };
          this.templateType = res.data.templateType;
          this.flowId = res.data.signFlowId;
          try {
            if (obj.flowId) {
              let { data } = await dealerPaylist.QRlink(obj);
              this.stduyCode = data.find((e) => e.isFirstParty == 1);
              this.loading = false;
              this.clearContractTime();
              this.contractInterval(this.qrSrtInt);
            }
            this.loading = false;
          } catch (error) {
            this.loading = false;
          }
        }
      },
      async qrSrtInt() {
        let res = await dealerPaylist.operationsPaymentQuery({
          merchantCode: this.merchantCode
        });
        if (res.data.secondApproveStatus == 2) {
          this.clearContractTime();
          this.contractflag = true;
          this.active++;
        } else {
        }
      },
      contractInterval(res) {
        let that = this;

        this.contractTime = setInterval(() => {
          console.log(that.contractTimes);
          if (this.contractTimes > 300) {
            that.clearContractTime();
          } else {
            res();
            that.contractTimes = that.contractTimes + 15;
          }
        }, 15000);
      },
      earnestCencel() {
        if (this.active == 1) {
          this.depositType = 0;
        }
      },
      async earnestbtnOK() {
        if (!this.depositType) return this.$message.warning('请选择定金俱乐部智能学习管理系统方式');
        this.deductionTitle = '请确定是否分配一套俱乐部智能学习学习管理系统，并支付10000元技术服务费，为以下俱乐部开通智能学习管理系统账号，开通后将无法撤回';
        this.deductionIsShow = true;
        this.earnestDialog = false;
        this.active++;
      },
      goNext() {
        if (this.active === 0) {
          this.contractStart();
        } else if (this.active === 1) {
          this.payAount();
        } else if (this.active === 4) {
          this.getQrlin(1);
        }
      },
      async paySrtInt(res) {
        try {
          let { data } = await dealerPaylist.judgeOrderStatus({ sourceOrderId: this.sourceOrderId });
          if (data == 3) {
            if (this.isPay == 1) {
              this.$message.success('抵扣成功');
              store.dispatch('getContract'); // 刷新合同数
              this.$router.back();
            } else {
              this.active = 4;
              // TODO: 6.13隐藏
              // this.Contract();
              // // end
              store.dispatch('getContract'); // 刷新合同数
              this.payOrSuccess = false;
            }
          } else {
            if (res) {
              this.$message.warning('暂未查询到支付结果，请以实际支付扣款结果为准');
            }
            this.payOrSuccess = true;
          }
        } catch (error) {
          this.$message.warning(error.message);
        }

        // if (data.paymentIsComplete == 1) {
        //   this.clearContractTime();
        //   this.active++;
        // } else if (data.paymentIsComplete == 0 && data.isPay == 1) {
        //   this.clearContractTime();
        //   this.active++;
        // } else {
        //   this.payOrSuccess = true;
        // }
      },
      async Contract(id) {
        let { data } = await dealerListApi.queryActiveV2(this.id);
        let obj = {
          flowId: data.signFlowId,
          templateType: data.templateType,
          signSource: data.signSource,
          isQrLink: 1
        };
        this.isBrandRoleTag = data.isBrandRoleTag;
        this.signObj = obj;
        this.getQrlin();
        this.clearContractTime();
        this.contractInterval(this.getQrlin);
      },
      async getQrlin(i) {
        if (this.codeList.length) {
          this.signObj.isQrLink = 0;
        }
        let res = await dealerPaylist.QRlink(this.signObj);
        if (this.codeList.length) {
          this.codeList[0].signStatus = res.data.find((e) => e.isFirstParty == 0).signStatus;
          this.codeList[1].signStatus = res.data.find((e) => e.isFirstParty == 1).signStatus;
        } else {
          this.codeList = [];
          this.codeList.push(res.data.find((e) => e.isFirstParty == 0));
          this.codeList.push(res.data.find((e) => e.isFirstParty == 1));
          this.active = 4;
        }
        if (this.codeList.find((e) => e.signStatus != 2)) {
          if (i) {
            this.$message.warning('还未签署，请先签署合同');
          }
        } else {
          this.$message.success('合同签署成功');
          this.clearContractTime();
          this.$router.back();
        }
      },
      async payAount() {
        let that = this;

        if (!this.paymentType) return this.$message.warning('请选择完款类型');
        if (this.isPay == 1) {
          this.makeUpPayment = true;
          return;
        }
        if (this.payStatus == 1) {
          let query = {
            merchantCode: this.merchantCode,
            paymentMethod: this.paymentType,
            paymentType: this.payType
          };
          dealerPaylist
            .operationsPayment(query)
            .then((res) => {
              that.sourceOrderId = res.data.sourceOrderId;
              const split = dxSource.split('##');
              res.data.dxSource = res.data.registerCallIInfo.appSource + '##' + split[1] + '##' + split[2];
              let params = JSON.stringify(res.data);
              let req = 'token=' + that.token + '&params=' + params + '&back=' + window.location.href;
              //需要编码两遍，避免出现+号等
              var encode = Base64.encode(Base64.encode(req));
              that.disabledF = false;
              window.open(that.setpayUrl + 'product?' + encode, '_blank');
              that.clearContractTime();
              that.contractInterval(that.paySrtInt);
              this.payOrSuccess = true;
              store.dispatch('getContract'); // 刷新合同数
              that.active++;
            })
            .catch((res) => {
              let arr = res.message.split('@');
              let type = arr[1];
              if (type == 1) {
                this.$confirm(arr[0], '提示', {
                  confirmButtonText: '去采购',
                  cancelButtonText: '取消',
                  type: 'none'
                })
                  .then(() => {
                    this.$router.replace({
                      path: '/purchase/purchaseApply'
                    });
                  })
                  .catch(() => {
                    // that.$message({
                    //   type: 'info',
                    //   message: '已取消删除'
                    // });
                  });
              } else {
                that.$message.warning(res.message);
              }
            });
        }
        if (this.payStatus == 2) {
          console.log('pppppppppppppppp');

          this.deductionTitle = '请确定是否分配一套俱乐部智能学习学习管理系统，并支付10000元技术服务费，为以下俱乐部开通智能学习管理系统账号，开通后将无法撤回';
          this.deductionIsShow = true;
          this.active++;
        }
        if (this.payStatus == 3 || this.payStatus == 4) {
          let { data } = await dealerPaylist.queryDepositDetail({ merchantCode: this.merchantCode });
          console.log(data, 'data');
          console.log(this.queryDepositDetail, 'this.queryDepositDetail');

          data.forEach((e) => {
            this.queryDepositDetail.forEach((o) => {
              if (e.depositType == o.type) {
                console.log(e.quantity, 'oooooooooooooo');

                o.count = e.quantity;
                console.log(o);
              }
            });
          });
          console.log(this.queryDepositDetail, 'iiiiiiiii');
          this.queryDepositDetail = this.queryDepositDetail.filter((e) => e.count > 0);
          if (this.queryDepositDetail.length == 1) {
            this.depositType = this.queryDepositDetail[0].paymentType;
            this.deductionIsShow = true;
            // this.earnestDialog = false;
            this.active++;
            return;
          }
          this.earnestDialog = true;
          return;
        }
      },
      // 抵扣确定
      deductionOK() {
        let that = this;
        var query;
        if (this.isPay == 1) {
          query = {
            merchantCode: this.merchantCode,
            paymentMethod: this.paymentType,
            paymentType: this.payType
          };
          this.deloading = true;
          dealerPaylist
            .operationsPayment(query)
            .then((res) => {
              this.$message.success('抵扣成功');
              store.dispatch('getContract'); // 刷新合同数
              this.$router.back();
              this.deloading = false;
            })
            .catch((err) => {
              console.log(err);

              this.deloading = false;
              let arr = err.message.split('@');
              let type = arr[1];
              if (type == 3) {
                this.$confirm(arr[0], '提示', {
                  confirmButtonText: '去采购',
                  cancelButtonText: '取消',
                  type: 'none'
                })
                  .then(() => {
                    this.deductionIsShow = false;
                    this.$router.replace({
                      path: '/purchase/purchaseApply'
                    });
                  })
                  .catch(() => {
                    this.active--;
                    this.deductionIsShow = false;
                    that.$message({
                      type: 'info',
                      message: '已取消'
                    });
                  });
              } else {
                this.deductionCancel();
                this.$message.warning(res.message);
              }
            });
        } else {
          // 抵扣也要支付10000
          if (this.payStatus == 2) {
            query = {
              merchantCode: this.merchantCode,
              paymentMethod: this.paymentType,
              paymentType: this.payType
            };
          } else {
            // this.payStatus == 4
            query = {
              merchantCode: this.merchantCode,
              paymentMethod: this.paymentType,
              paymentType: this.depositType
            };
          }
          this.deloading = true;
          dealerPaylist
            .operationsPayment(query)
            .then((res) => {
              that.sourceOrderId = res.data.sourceOrderId;
              const split = dxSource.split('##');
              res.data.dxSource = res.data.registerCallIInfo.appSource + '##' + split[1] + '##' + split[2];
              let params = JSON.stringify(res.data);
              let req = 'token=' + that.token + '&params=' + params + '&back=' + window.location.href;
              //需要编码两遍，避免出现+号等
              var encode = Base64.encode(Base64.encode(req));
              that.disabledF = false;
              this.deloading = false;
              window.open(that.setpayUrl + 'product?' + encode, '_blank');
              that.clearContractTime();
              that.contractInterval(that.paySrtInt);
              this.payOrSuccess = true;
              this.deductionIsShow = false; // 关闭抵扣弹窗
              store.dispatch('getContract'); // 刷新合同数
              that.active++;
            })
            .catch((res) => {
              this.deloading = false;
              this.deductionIsShow = false; // 关闭抵扣弹窗

              let arr = res.message.split('@');
              let type = arr[1];
              if (type == 3) {
                this.$confirm(arr[0], '提示', {
                  confirmButtonText: '去采购',
                  cancelButtonText: '取消',
                  type: 'none'
                })
                  .then(() => {
                    this.$router.replace({
                      path: '/purchase/purchaseApply'
                    });
                  })
                  .catch(() => {
                    this.active--;
                    that.$message({
                      type: 'info',
                      message: '已取消'
                    });
                  });
              } else {
                this.deductionCancel();
                this.$message.warning(res.message);
              }
            });
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .success {
    color: #13ce66;
    font-size: 40px;
    margin-bottom: 20px;
  }
  .sucontent {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding-top: 30px;
  }
  ::v-deep .el-radio__input {
    display: none !important;
  }
  ::v-deep .is-finish {
    .is-text {
      color: #fff;
      background-color: #1890ff;
    }
  }
  .lable {
    display: flex;
    justify-content: end;
    margin-right: 10px;
    align-items: center;

    width: 200px;
  }
  .title {
    width: 1200px;
    margin: 0 auto;
  }
  .successCodes {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  ::v-deep .el-radio-button__inner {
    width: 170px !important;
    height: 40px;
    padding: 0 !important;

    line-height: 40px;
  }
  .line {
    width: 1400px;
    height: 1px;
    background-color: #bbb;
    margin-bottom: 20px;
  }
  .img {
    cursor: pointer;
    position: relative;
    width: 240px;
    height: 240px;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    margin-bottom: 5px;
  }
  .right {
    position: absolute;
    right: 0;
    top: 0;
    font-size: 30px;
    color: #13ce66;
    /* background-color: #13ce66; */
  }
  .paymentType {
    font-size: 12px;
    margin-top: 40px;
    height: 200px;
    display: flex;
    justify-content: center;
  }
  .checek {
    background-color: #efefef;
  }
  .content {
    width: 1200px;
    height: 400px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
  }
  .bottom {
    width: 1200px;
    margin: 30px auto 0;
    display: flex;
    justify-content: center;
  }
  .codeSuccess {
    position: absolute;
    top: 0;
    left: 0;
    width: 200px;
    height: 200px;
    background: rgba(255, 255, 255, 0.85);
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .code {
    position: relative;
    width: 200px;
    height: 200px;
    /* background-color: skyblue; */
  }
  .app-container {
    /* background-color: #f5f5f5; */
    padding: 40px;
  }
  @media (max-width: 767px) {
    .el-message-box {
      width: 80% !important;
    }
  }
</style>
