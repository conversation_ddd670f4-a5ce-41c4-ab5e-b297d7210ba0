<template>
  <div class="app-container">
    <!-- 搜索 -->
    <el-form :inline="true" ref="dataQuery" class="SearchForm" style="padding: 20px 30px 0">
      <el-row>
        <el-col :span="8" :xs="24">
          <el-form-item label="课程编号:">
            <el-input v-model="dataQuery.courseCode" @keyup.enter.native="fetchData()" style="width: 200px" placeholder="请输入课程编号" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="课程名称:">
            <el-input v-model="dataQuery.courseName" @keyup.enter.native="fetchData()" style="width: 200px" placeholder="请输入课程名称" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="课程学段:">
            <el-select v-model="dataQuery.courseStage" filterable value-key="value" placeholder="请选择" clearable>
              <el-option v-for="(item, index) in courseStageType" :key="index" :label="item.label" :value="item.idx" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8" :xs="24">
          <el-form-item label="题目类型:">
            <el-select v-model="dataQuery.courseTypeDetail" filterable value-key="value" placeholder="请选择" clearable>
              <el-option v-for="(item, index) in courseTypeDetails" :key="index" :label="item.label" :value="item.idx" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="课程状态:">
            <el-select v-model="dataQuery.status" filterable value-key="value" placeholder="请选择" clearable>
              <el-option v-for="(item, index) in statusList" :key="index" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="课程类型:">
            <el-select v-model="dataQuery.courseType" filterable value-key="value" placeholder="请选择" clearable>
              <el-option v-for="(item, index) in courseTypes" :key="index" :label="item.label" :value="item.idx" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="16">
          <el-form-item label="添加时间:">
            <el-date-picker
              style="width: 100%"
              v-model="RegTime"
              type="datetimerange"
              value-format="yyyy-MM-dd HH:mm:ss"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              clearable
            ></el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item>
            <el-button type="warning" size="small" icon="el-icon-search" @click="fetchData01()">搜索</el-button>
            <el-button size="small" icon="el-icon-refresh" @click="rest()">重置</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <div class="SearchForm">
      <!-- 添加 -->
      <div class="btn-add">
        <el-button size="small" type="primary" icon="el-icon-plus" @click="clickAdd">添加</el-button>
      </div>
      <!-- 表格 -->
      <el-table class="common-table" :data="tableData" stripe border :default-sort="{ prop: 'date', order: 'descending' }" v-loading="tableLoading">
        <el-table-column prop="courseCode" label="课程编号" width="140" sortable></el-table-column>
        <el-table-column prop="courseName" label="课程名称" width="180" sortable show-overflow-tooltip></el-table-column>
        <el-table-column prop="id" label="操作" sortable width="300">
          <template slot-scope="scope">
            <el-button type="success" size="mini" icon="el-icon-edit-outline" v-if="scope.row.roleTag === role || roleName === true" @click="handleUpdate(scope.row.id)">
              编辑
            </el-button>
            <el-button type="success" size="mini" icon="el-icon-edit-outline" v-if="scope.row.roleTag === role || roleName === true" @click="enterChildrenList(scope.row)">
              制作文章
            </el-button>
            <el-button
              type="warning"
              size="mini"
              icon="el-icon-switch-button"
              v-if="scope.row.status === 0 && (scope.row.roleTag === role || roleName === true)"
              @click="courseStatus(scope.row.id, scope.row.status)"
            >
              开通
            </el-button>
            <el-button
              type="danger"
              size="mini"
              icon="el-icon-video-pause"
              v-if="scope.row.status === 1 && (scope.row.roleTag === role || roleName === true)"
              @click="courseStatus(scope.row.id, scope.row.status)"
            >
              暂停
            </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="coverUrl" label="课程封面" width="100" sortable>
          <template slot-scope="scope">
            <div class="demo-image__preview">
              <el-image v-if="scope.row.coverUrl" class="table_list_pic" :src="scope.row.coverUrl" @click="openImg(scope.row)"></el-image>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="courseTypeDetailRemark" label="题目类型" width="110" sortable></el-table-column>
        <el-table-column prop="courseStageName" label="课程学段" width="110" sortable></el-table-column>
        <el-table-column prop="courseTypeRemark" label="课程类型" width="110" sortable></el-table-column>
        <el-table-column prop="textbookVersion" label="教材版本" width="110" sortable></el-table-column>
        <el-table-column prop="createTime" label="添加时间" width="180" sortable :show-overflow-tooltip="true"></el-table-column>
        <el-table-column prop="updateTime" label="最后编辑时间" width="180" sortable :show-overflow-tooltip="true"></el-table-column>
        <el-table-column prop="status" label="状态" width="110" sortable>
          <template slot-scope="scope">
            <span :style="scope.row.status == 1 ? 'color:#67c23a' : 'color:#f66c81'">
              {{ scope.row.status == 1 ? '开通' : '暂停' }}
            </span>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- 分页 -->
    <el-col :span="20">
      <el-pagination
        :page-size="tablePage.size"
        layout="total, sizes, prev, pager, next, jumper"
        :total="tablePage.totalItems"
        :page-sizes="[10, 20, 30, 40, 50]"
        @size-change="handleSizeChange"
        :current-page.sync="tablePage.currentPage"
        @current-change="handleCurrentChange"
      />
    </el-col>

    <!-- 编辑或者添加弹窗 -->
    <el-dialog :title="addOrUpdate ? '添加课程' : '编辑课程'" :visible.sync="dialogVisible" width="70%" :close-on-click-modal="false" @close="close">
      <el-form ref="courseData" :rules="rules" :model="courseData" label-position="left" label-width="120px" style="width: 100%">
        <el-form-item label="课程分类" prop="courseCategory">
          <el-col :xs="24" :lg="18">
            <el-input v-model="courseCategory" disabled></el-input>
          </el-col>
        </el-form-item>
        <el-form-item label="题目类型" prop="courseTypeDetail">
          <el-col :xs="24" :lg="18">
            <el-select style="width: 100%" v-model="courseData.courseTypeDetail" filterable value-key="value" placeholder="请选择">
              <el-option v-for="(item, index) in courseTypeDetails" :key="index" :label="item.label" :value="item.idx" />
            </el-select>
          </el-col>
        </el-form-item>
        <el-form-item label="课程学段" prop="courseStage">
          <el-col :xs="24" :lg="18">
            <el-select style="width: 100%" v-model="courseData.courseStage" filterable @change="choseCourseStage()" value-key="value" placeholder="请选择">
              <el-option v-for="(item, index) in courseStageType" :key="index" :label="item.label" :value="item.idx" />
            </el-select>
          </el-col>
        </el-form-item>
        <el-form-item label="课程名称" prop="courseName">
          <el-col :xs="24" :span="18">
            <el-input v-model="courseData.courseName" placeholder="请输入" maxlength="50" show-word-limit></el-input>
          </el-col>
        </el-form-item>
        <el-form-item label="教材版本" prop="textbookVersion">
          <el-col :xs="24" :lg="18">
            <el-input v-model="textbookVersion" disabled></el-input>
          </el-col>
        </el-form-item>
        <el-form-item label="课程类型" prop="courseType">
          <template>
            <el-radio v-model="courseData.courseType" :label="1">系统课</el-radio>
            <el-radio v-model="courseData.courseType" :label="0">体验课</el-radio>
          </template>
        </el-form-item>
        <el-form-item label="课程封面上传" prop="coverUrl">
          <my-upload :showTip="false" :isKbOrMb="300" @handleSuccess="handlePicSuccess" @handleRemove="handlePicRemove" :fullUrl="true" :file-list="fileList" :limit="1" />
          <div class="tips" style="font-size: 12px">建议尺寸:690*280,支持格式：JPG、PNG格式 支持尺寸：{{ '<' }}300KB</div>
        </el-form-item>
        <el-form-item label="课程介绍" prop="introduction">
          <el-col :xs="24" :lg="18">
            <el-input type="textarea" resize="none" :rows="4" v-model="courseData.introduction" />
          </el-col>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <template>
            <el-radio v-model="isEnable" label="1" @change="changeStatus(isEnable)">开通</el-radio>
            <el-radio v-model="isEnable" label="0" @change="changeStatus(isEnable)">暂停</el-radio>
          </template>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button v-if="addOrUpdate" size="mini" type="primary" @click="addActiveFun('courseData')">新增</el-button>
        <el-button v-if="!addOrUpdate" size="mini" type="primary" @click="updateActiveFun('courseData')">修改</el-button>
        <el-button size="mini" @click="close">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import superReadCourseApi from '@/api/superReadCourseList';
  import enTypes from '@/api/bstatus';
  import { mapGetters } from 'vuex';
  import { pageParamNames } from '@/utils/constants';
  import { ossPrClient } from '@/api/alibaba';
  import MyUpload from '@/components/Upload/MyUpload.vue';

  export default {
    name: 'courseList',
    components: { MyUpload },
    data() {
      return {
        roleName: '',
        role: '',

        // 分页
        tablePage: {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null
        },
        RegTime: '',
        dataQuery: {
          courseCode: '',
          courseName: '',
          courseTypeDetail: '',
          courseStage: '',
          courseType: '',
          status: ''
        },
        tableLoading: false,
        tableData: [], //表格数据

        dialogVisible: false, // 修改弹窗是否展示
        addOrUpdate: true, // 是新增还是修改

        courseCategory: '英语',
        textbookVersion: '总部版本',
        courseData: {}, // 新增/修改课程
        isEnable: '', //单选框状态 值必须是字符串
        fileList: [], // 上传图片已有图片列表
        uploadLoading: false, // 上传图片加载按钮

        statusList: [
          { label: '开通', value: 1 },
          { label: '暂停', value: 0 }
        ],
        courseTypeDetails: [], //题目类型
        courseStageType: [], //课程学段
        courseTypes: [], //课程类型

        rules: {
          // 表单提交规则
          courseType: [
            {
              required: true,
              message: '必填',
              trigger: 'blur'
            }
          ],
          courseStage: [
            {
              required: true,
              message: '必填',
              trigger: 'blur'
            }
          ],
          introduction: [
            {
              required: true,
              message: '必填',
              trigger: 'change'
            }
          ],
          courseName: [
            {
              required: true,
              message: '必填',
              trigger: 'change'
            }
          ],
          coverUrl: [
            {
              required: true,
              message: '必填',
              trigger: 'change'
            }
          ],
          status: [
            {
              required: true,
              message: '必填',
              trigger: 'change'
            }
          ],
          courseTypeDetail: [
            {
              required: true,
              message: '必填',
              trigger: 'change'
            }
          ]
        }
      };
    },
    computed: {
      ...mapGetters(['roles'])
    },
    activated() {},
    created() {
      this.fetchData();
      ossPrClient();
      this.getCourseStage();
      this.getCourseType();
      this.getCourseTypeDetails();

      var jsonData = JSON.stringify(this.roles);
      var s = JSON.stringify(this.roles).includes('admin');
      var y = JSON.stringify(this.roles).includes('教研');
      var obj = eval('(' + jsonData + ')');
      this.roleName = s || y;
      console.log(this.roleName, 'roleName');
      this.role = obj[0].val;
      console.log(this.role, 'role');
      console.log(obj[0].val);
    },
    methods: {
      //获取学段
      getCourseStage() {
        enTypes.getEnumerationAggregation('CJYDCourseStage').then((res) => {
          this.courseStageType = res.data;
        });
      },
      //获取课程类型 正式/体验
      getCourseType() {
        enTypes.getEnumerationAggregation('CJYDCourseType').then((res) => {
          this.courseTypes = res.data;
        });
      },
      //获取题目类型 阅读/完形
      getCourseTypeDetails() {
        enTypes.getEnumerationAggregation('CJYDCourseTypeDetail').then((res) => {
          this.courseTypeDetails = res.data;
        });
      },

      //重置
      rest() {
        this.dataQuery = {
          courseCode: '',
          courseName: '',
          courseTypeDetail: '',
          courseStage: '',
          courseType: '',
          status: ''
        };
        this.RegTime = '';
        this.fetchData01();
      },

      // 查询+搜索课程列表
      fetchData01() {
        this.tablePage = {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null
        };
        this.fetchData();
      },
      fetchData() {
        const that = this;
        that.tableLoading = true;
        var a = that.RegTime;
        if (a != null) {
          that.dataQuery.startTime = a[0];
          console.log((that.dataQuery.startTime = a[0]));
          that.dataQuery.endTime = a[1];
        }
        that.dataQuery.pageNum = that.tablePage.currentPage;
        that.dataQuery.pageSize = that.tablePage.size;
        superReadCourseApi.courseList(that.dataQuery).then((res) => {
          that.tableData = res.data.data;
          that.tableLoading = false;
          // 设置后台返回的分页参数
          pageParamNames.forEach((name) => that.$set(that.tablePage, name, parseInt(res.data[name])));
        });
      },

      //添加操作
      clickAdd() {
        this.fileList = [];
        this.courseData = {
          courseCategory: '',
          courseType: '',
          courseStage: '',
          courseName: '',
          textbookVersion: '',
          coverUrl: '',
          introduction: '',
          courseTypeDetail: '',
          courseStageName: '',
          status: ''
        };
        this.dialogVisible = true;
        this.addOrUpdate = true;
        setTimeout(() => {
          this.$refs.courseData.resetFields();
          this.courseCategory = '英语';
          this.textbookVersion = '总部版本';
          this.isEnable = '';
        }, 50);
      },

      // 新增课程提交
      addActiveFun(ele) {
        const that = this;
        that.$refs[ele].validate((valid) => {
          // 表单验证
          if (valid) {
            const loading = this.$loading({
              lock: true,
              text: '新增课程',
              spinner: 'el-icon-loading',
              background: 'rgba(0, 0, 0, 0.7)'
            });
            this.courseData.courseCategory = this.courseCategory;
            this.courseData.textbookVersion = this.textbookVersion;
            superReadCourseApi
              .addCourse(that.courseData)
              .then(() => {
                that.dialogVisible = false;
                loading.close();
                this.$refs.courseData.resetFields();
                that.fileList = [];
                that.courseData.coverUrl = '';
                that.fetchData();
                that.$message.success('新增课程成功');
              })
              .catch((err) => {
                loading.close();
              });
          } else {
            console.log('error submit!!');
            return false;
          }
        });
      },
      // 点击编辑按钮
      handleUpdate(id) {
        const that = this;
        that.dialogVisible = true;
        that.addOrUpdate = false;
        that.fileList = [];
        this.$nextTick(() => {
          this.$refs.courseData.resetFields();
        });
        superReadCourseApi
          .queryActive(id)
          .then((res) => {
            console.log(res);
            that.courseData = res.data;
            that.fileList.push({
              name: that.courseData.filePath,
              url: that.courseData.coverUrl
            });
            that.courseCategory = that.courseData.courseCategory;
            that.textbookVersion = that.courseData.textbookVersion;
            that.isEnable = that.courseData.status.toString(); //状态回显
          })
          .catch((err) => {});
      },

      // 修改课程提交
      updateActiveFun(ele) {
        const that = this;
        that.$refs[ele].validate((valid) => {
          // 表单验证
          if (valid) {
            that.courseData.status = that.isEnable;
            const loading = this.$loading({
              lock: true,
              text: '修改课程信息提交',
              spinner: 'el-icon-loading',
              background: 'rgba(0, 0, 0, 0.7)'
            });
            superReadCourseApi
              .updateCourse(that.courseData)
              .then(() => {
                that.dialogVisible = false;
                loading.close();
                that.fileList = [];
                that.courseData.coverUrl = '';
                this.$refs.courseData.resetFields();
                that.fetchData();
                that.$message.success('修改课程成功');
              })
              .catch((err) => {
                // 关闭提示弹框
                loading.close();
              });
          } else {
            console.log('error submit!!');
            return false;
          }
        });
      },
      // 课程开通与暂停
      courseStatus(id, status) {
        if (status == 0) {
          status = 1;
        } else {
          status = 0;
        }
        const that = this;
        this.$confirm('确定操作吗?', '修改状态', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            superReadCourseApi
              .updateStatus(id, status)
              .then((res) => {
                that.$nextTick(() => that.fetchData());
                that.$message.success('修改成功!');
              })
              .catch((err) => {});
          })
          .catch((err) => {});
      },

      // 分页
      handleSizeChange(val) {
        this.tablePage.size = val;
        this.fetchData();
      },
      handleCurrentChange(val) {
        this.tablePage.currentPage = val;
        this.fetchData();
      },
      handlePicSuccess(url) {
        this.courseData.coverUrl = url;
      },
      handlePicRemove() {
        this.courseData.coverUrl = '';
      },
      choseCourseStage() {
        let result = this.courseStageType.find((item) => {
          return item.idx === this.courseData.courseStage;
        });
        this.courseData.courseStageName = result.label;
        console.log(result);
        console.log(this.courseData.courseStageName);
      },
      // 状态改变事件
      changeStatus(radio) {
        if (radio == '1') {
          this.courseData.status = 1;
        } else {
          this.courseData.status = 0;
        }
      },
      // 关闭弹窗
      close() {
        this.dialogVisible = false;
        this.fileList = [];
        this.$refs.courseData.resetFields();
      },

      //进入子类
      enterChildrenList(row) {
        console.log(row, '2222222');
        const that = this;
        if (row.courseTypeDetailRemark === '阅读理解') {
          window.localStorage.setItem('courseNameReading', row.courseName);
          window.localStorage.setItem('courseIdReading', row.id);
          window.localStorage.setItem('courseContentTypeReading', row.courseTypeDetailRemark);
          that.$router.push({
            path: '/course/articleList',
            query: {
              id: row.id,
              courseContentType: row.courseTypeDetailRemark,
              courseName: row.courseName
            }
          });
        } else if (row.courseTypeDetailRemark === '完型填空') {
          window.localStorage.setItem('courseNameReading', row.courseName);
          window.localStorage.setItem('courseIdReading', row.id);
          window.localStorage.setItem('courseContentTypeReading', row.courseTypeDetailRemark);
          that.$router.push({
            path: '/course/articleList',
            query: {
              id: row.id,
              courseContentType: row.courseTypeDetailRemark,
              courseName: row.courseName
            }
          });
        }
      }
    }
  };
</script>

<style scoped>
  .SearchForm {
    width: 100%;
    margin-bottom: 15px;
    background: #fff;
    border-radius: 6px;
    padding: 5px 0;
    padding-left: 5px;
    box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.2);
  }

  .btn-add {
    padding: 5px;
  }

  .course-table td,
  .course-table th {
    padding: 5px 0;
    text-align: center;
  }

  .course-table button {
    padding: 2px;
  }

  @media (max-width: 767px) {
    .el-message-box {
      width: 80% !important;
    }
  }
</style>
