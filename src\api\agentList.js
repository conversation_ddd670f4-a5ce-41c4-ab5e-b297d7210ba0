/**
 * 市级服务商相关接口
 */
import request from '@/utils/request'

export default {
  // 分页查询
  agentList(pageNum, pageSize, data) {
    return request({
      url: '/znyy/agent/list/' + pageNum + '/' + pageSize,
      method: 'GET',
      params: data
    })
  },
  getRecharge() {
    return request({
      url: '/znyy/areas/dealer/recharge/detail',
      method: 'GET',
    })
  },
  //根据角色获取充值返点
  getRechargeBackMoney(merchantCode, money) {
    return request({
      url: '/znyy/areas/dealer/get/recharge/back/money?merchantCode=' + merchantCode + '&money=' + money,
      method: 'GET',
    })
  },
  //市级服务商充值
  submitRecharge(data) {
    return request({
      url: '/znyy/agent/online/charge',
      method: 'POST',
      data
    })

  },
  // 导出
  agentExport(listQuery) {
    return request({
      url: '/znyy/agent/execle',
      method: 'GET',
      responseType: 'blob',
      params: listQuery
    })
  },
  // 导出
  agentExport1(listQuery) {
    return request({
      url: '/znyy/operate/agent/query',
      method: 'GET',
      responseType: 'blob',
      params: listQuery
    })
  },
  // 开通与暂停
  updateStatus(id, status) {
    return request({
      url: '/znyy/agent/updateStatus?id=' + id + '&isEnable=' + status,
      method: 'PUT'
    })
  },

  updatePaymentIsComplete(id, paymentIsComplete) {
    return request({
      url: '/znyy/agent/updatePaymentIsComplete?id=' + id + '&paymentIsComplete=' + paymentIsComplete,
      method: 'PUT'
    })
  },

  // 开通与暂停推荐二级分润
  updateReProfitRank(id, reProfitRank) {
    return request({
      url: '/znyy/agent/update/profit?id=' + id + '&reProfitRank=' + reProfitRank,
      method: 'PUT'
    })
  },
  // 审核
  examine(id, checkReason, isCheck) {
    return request({
      url: '/znyy/agent/checkStatus?id=' + id + '&checkReason=' + checkReason + '&isCheck=' + isCheck,
      method: 'PUT'
    })
  },
  //开通市级服务商附属事业部
  openDivision(merchantCode){
    return request({
      url: '/znyy/agent/openDivision?merchantCode=' + merchantCode,
      method: 'POST'
    })
  }
}
