/**
 * 单词水平相关接口
 */
import request from '@/utils/request';

export default {
  schoolPage(pageNum, pageSize, data) {
    return request({
      url: '/znyy/areas/school/page/' + pageNum + '/' + pageSize,
      method: 'GET',
      params: data
    });
  },
  agentSchoolPage(pageNum, pageSize, data) {
    return request({
      url: '/znyy/areas/school/agent/de/page/' + pageNum + '/' + pageSize,
      method: 'GET',
      params: data
    });
  },
  getMerchantAccount() {
    return request({
      url: '/znyy/areas/school/recharge/detail',
      method: 'GET'
    });
  },
  openEnable(id, isEnable, isEnableNew) {
    return request({
      url: '/znyy/school/update/enable/status/' + id + '/' + isEnable + '/' + isEnableNew,
      method: 'PUT'
    });
  },
  //根据角色获取充值返课程
  getRechargeBackCourse(merchantCode, money) {
    return request({
      url: '/znyy/areas/school/get/recharge/back/course?merchantCode=' + merchantCode + '&money=' + money,
      method: 'GET'
    });
  },

  //充值提交
  submitRechargeCourse(data) {
    return request({
      url: '/znyy/areas/school/submit/school/recharge',
      method: 'POST',
      data
    });
  },
  exportAreasSchool(data) {
    return request({
      url: '/znyy/areas/school/to/excel',
      method: 'GET',
      params: data,
      responseType: 'blob'
    });
  },
  exportAgentAreasSchool(data) {
    return request({
      url: '/znyy/areas/school/to/agent/excel',
      method: 'GET',
      params: data,
      responseType: 'blob'
    });
  },
  getCurrentAdmin() {
    return request({
      url: '/znyy/school/currentAdmin',
      method: 'GET'
    });
  },
  allGone(id, goneType, renew) {
    return request({
      url: '/znyy/school/allGone/' + id,
      method: 'POST',
      params: { goneType: goneType, renew: renew }
    });
  },
  // 重新创建e签宝合同
  getEsignCode(id) {
    return request({
      url: '/znyy/fundConversion/bv-esign/flow-restart',
      method: 'GET',
      params: {
        id: id
      }
    });
  },
  // 查询手机号是否签过合同
  getMerchantPhoneCheck(phone) {
    return request({
      url: '/znyy/V2/merchant/merchantPhoneCheck',
      method: 'GET',
      params: {
        phone
      }
    });
  }
};
