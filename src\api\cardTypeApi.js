/**
 * 学员管理相关接口
 */
import request from '@/utils/request'

export default {
  // 分页查询
  cardList(pageNum, pageSize, data) {
    return request({
      url: '/dzy/card/type/page/' + pageNum + '/' + pageSize,
      method: 'GET',
      params: data
    })
  },
  cardAdd(data) {
    return request({
      url: '/dzy/card/type',
      method: 'POST',
      data
    })
  },
  cardUpdate(data) {
    return request({
      url: '/dzy/card/type',
      method: 'PUT',
      data
    })
  },

  cardById(id) {
    return request({
      url: '/dzy/card/type/' + id,
      method: 'GET',
    })
  },
}
