<template>
  <div class="app-container totalBox">
    <div class="container_box">
      <el-button type="success" style="margin-bottom: 10px" @click="goBack" icon="el-icon-back" size="mini">返回课程列表</el-button>
      <el-table :data="tableData" class="common-table" stripe border v-loading="tableLoading">
        <el-table-column prop="courseCode" label="课程ID" width="240" sortable></el-table-column>
        <el-table-column prop="sortOrder" label="排序" width="160" sortable></el-table-column>
        <el-table-column prop="courseName" label="课程名称" width="240" sortable></el-table-column>
        <el-table-column prop="id" label="操作" sortable>
          <template>
            <el-button type="primary" size="mini" icon="el-icon-edit" @click="getList('3')">上传单词</el-button>
            <el-button type="primary" size="mini" icon="el-icon-edit" @click="getList('4')">上传音频</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div v-if="couresListShow === '3'">
      <div class="item_list"></div>
      <AdditionSpellList></AdditionSpellList>
    </div>
    <div v-if="couresListShow === '4'">
      <div class="item_list">上传音频</div>
      <AdditionUploadAudio></AdditionUploadAudio>
    </div>
  </div>
</template>
<script>
  import AdditionSpellList from '@/views/course/courseDictation/makeCourseList/additionSpellList';
  import AdditionUploadAudio from '@/views/course/courseDictation/makeCourseList/additionuploadAudio.vue';
  import courseAdditionList from '@/api/courseAdditionList';
  export default {
    components: {
      AdditionSpellList,
      AdditionUploadAudio
    },
    data() {
      return {
        uploadData: [
          { word: 'red', status: 0, id: '0000000' },
          { word: 'blue', status: 0, id: '1111111' },
          { word: 'white', status: 0, id: '2222222' }
        ], //上传音频弹窗
        audioFileList: [], //已上传音频列表
        couresListShow: '',

        tableData: [],
        tableLoading: false
      };
    },

    created() {
      let courseData = this.$route.query.courseData;
      if (courseData) {
        this.tableData.push(JSON.parse(courseData));
      }
    },

    methods: {
      getList(index) {
        this.couresListShow = index;
      },

      //返回课程列表
      goBack() {
        this.$router.push({ name: 'additionCourseList' });
        window.localStorage.setItem('isJumpPY', 'true');
      }
    }
  };
</script>
<style lang="less" scope="scope">
  .totalBox {
    min-height: 720px;
  }
  ::v-deep .el-upload-list {
    width: auto !important;
  }
  .course_image_slot {
    height: 148px;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #c0c4cc;
  }
  .container_box {
    width: 100%;
    margin-bottom: 15px;
    background: #fff;
    border-radius: 6px;
    padding: 5px;
    padding-left: 5px;
    box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.2);
  }
  .item_list {
    font-weight: bold;
    padding: 20px 0;
    color: #909399;
  }
  .video_upload_table {
    width: 128px;
    height: 128px;
    border-radius: 8px;
    background-color: black;
  }
  .video_upload {
    position: absolute;
    z-index: 99999;
    width: 148px;
    height: 148px;
    border-radius: 8px;
    background-color: black;
  }
  .video_del {
    margin-left: 130px;
    position: absolute;
    z-index: 99999;
    color: white;
  }
  .videoDialog {
    z-index: 9999;
    position: absolute;
    top: 10vh;
    left: -105px;
    transform: translateX(50%);
    // background-color: rgba(0, 0, 0, .3);
    .closeVideo {
      position: absolute;
      top: 10px;
      left: 920px;
      transform: translateX(50%);
      z-index: 99999;
      color: #ffffff;
      font-size: 20px;
      cursor: pointer;
      &:hover {
        color: #409eff;
      }
    }
  }
</style>
