<template>
  <div class="app-container">
    <el-form :inline="true" style="margin-bottom: 20px;" v-if="checkPermission(['b:dzy:card:type:add'])">
      <el-button type="success" @click="popUps_show = true">新增
      </el-button>
    </el-form>

    <el-table class="common-table" v-loading="tableLoading" :data="tableData" style="margin-bottom: 20px;" row-key="id"
      stripe border default-expand-all :tree-props="{ list: 'children', hasChildren: 'true' }" :xs="24">
      <el-table-column prop="id" label="编号" width="250px"></el-table-column>
      <el-table-column prop="name" label="名字" width="250px"></el-table-column>
      <el-table-column prop="withdrawnBonus" label="操作" v-if="checkPermission(['b:dzy:card:type:update'])" width="250px">
        <template slot-scope="scope">
          <!-- 更新状态 -->
          <el-button type="warning" size="mini" icon="el-icon-switch-button" v-if="!scope.row.enable"
            @click="changeStatus(scope.row, 1)">开通
          </el-button>
          <el-button type="danger" size="mini" icon="el-icon-video-pause" v-if="scope.row.enable"
            @click="changeStatus(scope.row, 0)">暂停
          </el-button>
        </template>
      </el-table-column>
      <el-table-column prop="priceCourse" label="卡单价" width="250px"></el-table-column>
      <el-table-column prop="waringNumber" label="库存告警数量" v-if="checkPermission(['b:dzy:card:type:add'])"
        width="200px"></el-table-column>
      <el-table-column prop="enable" label="是否可用" width="250px">
        <template slot-scope="scope">
          <span class="green" v-if="scope.row.enable">可用</span>
          <span class="red" v-else>不可用</span>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <el-col :span="24" :xs="24">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>
    <el-dialog title="卡类型" :visible.sync="popUps_show" :before-close="handleClose" width="90%">
      <el-form :model="form" label-width="120px">
        <el-col :xs="24">
          <el-form-item label="卡名称">
            <el-input v-model="form.name"></el-input>
          </el-form-item>
        </el-col>
        <el-col :xs="24">
          <el-form-item label="库存告警数量">
            <el-input v-model="form.waringNumber" type="number" @blur="BlurText1(form.waringNumber)"></el-input>
          </el-form-item>
        </el-col>
        <el-col :xs="24">
          <el-form-item label="卡单价">
            <el-input v-model="form.priceCourse" type="number" @blur="BlurText(form.priceCourse)"></el-input>
          </el-form-item>
          <el-form-item label="产品类型">
            <el-input v-model="form.productType"></el-input>
          </el-form-item>
        </el-col>
        <el-col :xs="24">
          <el-form-item label="是否可用">
            <el-switch v-model="form.enable" active-text="可用" inactive-text="不可用">
            </el-switch>
          </el-form-item>
        </el-col>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="onAdd">立即创建</el-button>
        <el-button @click="popUps_show = false">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>

import checkPermission from '@/utils/permission'
import cardTypeApi from "@/api/cardTypeApi";
import Tinymce from "@/components/Tinymce";
import { pageParamNames } from "@/utils/constants";
import schoolApi from "@/api/areasSchoolList";

export default {
  name: 'studentList',
  data() {
    return {
      popUps_show: false,
      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      merchantCode: '',
      form: {},
      tableData: {},
      dataQuery: {
        studentCode: '',
        startDate: '',
        endDate: '',
        isEnable: '',
        isFormal: '',
        loginName: '',
        realName: '',
        memberCode: '',
        memberPhone: '',
        restrictedUse: ''
      },
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }]
      },
      useTime: '',
    };
  },
  created() {
    this.fetchData();
  },
  methods: {
    checkPermission,
    changeStatus(type, state) {
      type.enable = state;
      type.priceCourse = Number(type.priceCourse);
      cardTypeApi.cardUpdate(type).then(res => {
        if (res.success) {
          this.fetchData();
        } else {
          this.$message.error(res.message);
        }
      })
    },
    onAdd() {
      cardTypeApi.cardAdd(this.form).then(res => {
        if (res.success) {
          this.popUps_show = false;
          this.fetchData()
        } else {
          this.$message.error(res.message);
        }
      })
    },
    BlurText(e) {
      if (e != undefined && e != '' && e != "" && e != null && e != 0) {
        let boolean = new RegExp("^[1-9][0-9]*$").test(e);
        if (!boolean) {
          this.$message.warning("请输入正整数");
          this.form.priceCourse = "";
        };
      }
    },
    BlurText1(e) {
      if (e != undefined && e != '' && e != "" && e != null) {
        let boolean = new RegExp("^[1-9][0-9]*$").test(e);
        if (!boolean) {
          this.$message.warning("请输入正整数");
          this.form.waringNumber = "";
        };
      }
    },
    handleClose(done) {
      this.$confirm('确认关闭？')
        .then(_ => {
          this.popUps_show = false;
        })
        .catch(_ => {
        });
    },
    useTimeChange(value) {
      // console.log(e[0]);
      this.dataQuery.useTimeMin = value[0]
      this.dataQuery.useTimeMax = value[1]
      console.log(this.dataQuery.useTimeMax)
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val;
      this.fetchData();
    },

    handleCurrentChange(val) {
      this.tablePage.currentPage = val;
      this.fetchData();
    },
    // 查询提现列表
    fetchData() {
      const that = this;
      that.tableLoading = true
      cardTypeApi.cardList(that.tablePage.currentPage, that.tablePage.size, that.dataQuery).then(res => {
        console.log(res);
        that.tableData = res.data.data
        that.tableLoading = false
        // 设置后台返回的分页参数
        pageParamNames.forEach(name => that.$set(that.tablePage, name, parseInt(res.data[name])))
      })
    },
  }
};
</script>

<style>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.period-table td,
.period-table th {
  text-align: center;
}

.mt20 {
  margin-top: 20px;
}

.red {
  color: red;
}

.green {
  color: green;
}

@media (max-width: 767px) {
  .el-message-box {
    width: 80% !important;
  }
}
</style>
