/**
 * 会员充值订单相关接口
 */
import request from '@/utils/request'

export default {
  // 分页查询
  memberOrder(pageNum, pageSize, data) {
    return request({
      url: '/znyy/order/vip/page/' + pageNum + '/' + pageSize,
      method: 'GET',
      params: data
    })
  },
  // 获取订单状态
  orderType(enType){
    return request({
      url: '/znyy/bvstatus/' + enType,
      method: 'GET'
    })
  },
  // 导出
  orderExport(listQuery) {
    return request({
      url: '/znyy/order/vip/to/excel',
      method: 'GET',
      responseType: 'blob',
      params:listQuery
    })
  },

}
