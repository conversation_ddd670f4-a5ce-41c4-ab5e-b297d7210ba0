/**
 * 推荐人分润列表
 */
import request from '@/utils/request'

export default {
  //推荐人的商户资金流水
  profitAccountFlow(pageNum, pageSize,data) {
    return request({
      url: '/znyy/areas/merchant/profit/account/flow/' + pageNum + '/' + pageSize,
      method: 'GET',
      params: data
    })
  },
 //推荐人的商户资金流水
 profitAccountFlowList(pageNum, pageSize,data) {
    return request({
      url: '/znyy/areas/merchant/bvAdmin/profit/account/flow/' + pageNum + '/' + pageSize,
      method: 'GET',
      params: data
    })
  }
  ,
  withdrawApplication(id, bonusStatus) {
    return request({
      url: '/znyy/profit/account/withdrawApplication/' + id + '/' + bonusStatus,
      method: 'POST'
    })
  }


}
