/**
 * 单词本查看相关接口
 */
import request from '@/utils/request'

export default {
  // 分页查询
  wordBook(pageNum, pageSize, data) {
    return request({
      url: '/znyy/student/list/wordBookView/' + pageNum + '/' + pageSize,
      method: 'GET',
      params: data
    })
  },
   // 导出
   wordExport(listQuery) {
    return request({
      url: '/znyy/student/list/wordBook/export',
      method: 'GET',
      responseType: 'blob',
      params:listQuery
    })
  },
}
