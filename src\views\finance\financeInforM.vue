<template>
  <div>
    <el-card class="frame" shadow="never">
      <el-form label-width="120px">
        <!-- 1 -->
        <el-row type="flex">
          <el-col :span="8">
            <el-form-item label="门店编号:">
              <el-input v-model="searchNum.merchantCode" clearable placeholder="请选择" size="small"
                style="width: 13vw"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="订单号:">
              <el-input v-model="searchNum.orderId" clearable placeholder="请选择" size="small"
                style="width: 13vw"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="学员账号:">
              <el-input v-model="searchNum.studentCode" clearable placeholder="请选择" size="small"
                style="width: 13vw"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <!-- 3 -->
        <el-row type="flex">
          <el-col :span="8">
            <el-form-item label="支付时间:">
              <el-date-picker v-model="timeAll" style="width: 18vw" size="small" format="yyyy-MM-dd HH:mm"
                value-format="yyyy-MM-dd HH:mm" :picker-options="pickerOptions" align="right" type="datetimerange"
                range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="交付中心商户号:">
              <el-input v-model="searchNum.deliverMerchant" clearable placeholder="请选择" size="small"
                style="width: 13vw"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8" style="padding-left: 3vw;">
            <el-button type="primary" icon="el-icon-search" @click="initData01">查询</el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
    <el-card class="frame" shadow="never">
      <el-row type="flex" justify="end">
        <span style="margin-right: 8vw"></span>
      </el-row>
    </el-card>
    <!-- 表格 -->
    <el-table v-loading="tableLoading" :data="reviewLister" style="width: 100%" id="out-table"
      :header-cell-style="getRowClass" :cell-style="{ 'text-align': 'center' }">
      <el-table-column width="50" align="center"></el-table-column>
      <el-table-column prop="orderId" label="订单号" header-align="center"></el-table-column>
      <el-table-column prop="merchantCode" label="门店编号" header-align="center"></el-table-column>
      <el-table-column prop="merchantName" label="门店名称" header-align="center"></el-table-column>
      <el-table-column prop="merchantMobile" label="门店账号" header-align="center"></el-table-column>
      <el-table-column prop="deliverMerchantName" label="交付方" header-align="center"></el-table-column>
      <el-table-column prop="studentCode" label="学员账号" header-align="center"></el-table-column>
      <el-table-column prop="studentName" label="学员名称" header-align="center"></el-table-column>
      <el-table-column prop="hours" width="80" label="学时数" header-align="center"></el-table-column>
      <el-table-column prop="amount" label="总收入" header-align="center"></el-table-column>
      <el-table-column prop="deliverAmount" label="交付中心收入" header-align="center"></el-table-column>
      <el-table-column prop="fee" label="总部收入" header-align="center"></el-table-column>
      <el-table-column prop="createTime" label="交付时间" min-width="110px" header-align="center"></el-table-column>
    </el-table>
    <!-- 分页器 -->
    <el-row type="flex" justify="center" align="middle" style="height: 60px">
      <!-- 3个变量：每页数量、页码数、总数  -->
      <!-- 2个事件：页码切换事件、每页数量切换事件-->
      <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
        :current-page="searchNum.pageNum" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="total"></el-pagination>
    </el-row>
    <div style="display: flex;">
      <div>总金额：{{ moontAll.amount }}</div>
      <div style="margin-left:2vw">交付中心收入：{{ moontAll.deliverAmount }}</div>
      <div style="margin-left:2vw">总部收入:{{ moontAll.feeAmount }}</div>
    </div>
  </div>
</template>

<script>
import { getDeliverOrderPage, getDeliverOrderStat } from "@/api/FinanceApi/Finance";
import FileSaver from "file-saver";
import XLSX from "xlsx";
export default {
  data() {
    return {
      // 日期组件
      pickerOptions: {
        shortcuts: [
          {
            text: "今天",
            onClick(picker) {
              // const end = new Date();
              // const start = new Date();
              // picker.$emit('pick', [start, end]);
              const temp = new Date();
              picker.$emit("pick", [
                new Date(temp.setHours(0, 0, 0, 0)),
                new Date(temp.setHours(23, 59, 59, 0))
              ]);
            }
          },
          {
            text: "昨天",
            onClick(picker) {
              const temp = new Date();
              temp.setTime(temp.getTime() - 3600 * 1000 * 24);
              picker.$emit("pick", [
                new Date(temp.setHours(0, 0, 0, 0)),
                new Date(temp.setHours(23, 59, 59, 0))
              ]);
            }
          },
          {
            text: "最近七天",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit("pick", [start, end]);
            }
          }
        ]
      },
      searchNum: {
        endTime: "",
        startTime: "",
        merchantCode: "",
        studentCode: '',
        deliverMerchant: '',
        orderId: "",
        pageNum: 1,
        pageSize: 10
      }, //搜索参数
      tableLoading: false,
      timeAll: [],
      total: null,
      reviewLister: [],
      moontAll: '',
    };
  },
  created() {
    this.initData();
  },
  methods: {
    // 搜索
    initData01() {
      (this.searchNum.pageNum = 1),
        (this.searchNum.pageSize = 10),
        this.initData();
    },
    // 列表数据
    async initData() {
      // 判断为null的时候赋空
      if (!this.timeAll) {
        this.timeAll = [];
      }
      this.tableLoading = true;
      this.searchNum.startTime = this.timeAll[0];
      this.searchNum.endTime = this.timeAll[1];
      let { data } = await getDeliverOrderPage(this.searchNum);
      this.tableLoading = false;
      this.total = Number(data.totalItems);
      this.reviewLister = data.data;
      // 金额汇总
      let res = await getDeliverOrderStat(this.searchNum)
      this.moontAll = res.data
    },
    LeaveDialog(v) {
      this.LeaveViewStyle = v;
    },
    reviewDialog(v) {
      this.reviewStyle = v;
    },
    // 动态class
    getRowClass({ rowIndex, columnIndex }) {
      if (rowIndex == 0) {
        return "background:#f5f7fa";
      }
    },
    // 分页
    handleSizeChange(val) {
      this.searchNum.pageSize = val;
      this.initData();
    },
    handleCurrentChange(val) {
      this.searchNum.pageNum = val;
      this.initData();
    },
    //定义导出Excel表格事件
    exportExcel() {
      /* 从表生成工作簿对象 */
      var wb = XLSX.utils.table_to_book(document.querySelector("#out-table"));
      /* 获取二进制字符串作为输出 */
      var wbout = XLSX.write(wb, {
        bookType: "xlsx",
        bookSST: true,
        type: "array"
      });
      try {
        FileSaver.saveAs(
          //Blob 对象表示一个不可变、原始数据的类文件对象。
          //Blob 表示的不一定是JavaScript原生格式的数据。
          //File 接口基于Blob，继承了 blob 的功能并将其扩展使其支持用户系统上的文件。
          //返回一个新创建的 Blob 对象，其内容由参数中给定的数组串联组成。
          new Blob([wbout], { type: "application/octet-stream" }),
          //设置导出文件名称
          "sheetjs.xlsx"
        );
      } catch (e) {
        if (typeof console !== "undefined") console.log(e, wbout);
      }
      return wbout;
    }
  }
};
</script>

<style>
body {
  background-color: #f5f7fa;
}

.normal {
  color: rgb(28, 179, 28);
}

.error {
  color: rgba(234, 36, 36, 1);
}
</style>

<style lang="scss" scoped>
.frame {
  margin-top: 0.5vh;
  background-color: rgba(255, 255, 255);
}
</style>
