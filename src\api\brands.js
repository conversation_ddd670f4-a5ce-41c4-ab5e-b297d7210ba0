/**
 * 品牌列表相关接口
 */
import request from '@/utils/request';

export default {
  fetchMerchantList(params) {
    return request({
      // url: '/znyy/brand/page?pageNum=' + params.pageNum + '&pageSize=' + params.pageSize + '&merchantType=3',
      url: '/znyy/brand/page',
      method: 'GET',
      params
    });
  },
  //获取相应套餐价格
  fetchMealInfoByType(params) {
    return request({
      url: '/zxAdminCourse/web/commissionMeal/mealInfoByType',
      method: 'get',
      params
    });
  },
  // 图片上传
  fileUpoload(data, config) {
    return request({
      url: '/zxAdminCourse/common/uploadFile',
      method: 'POST',
      data,
      headers: config.headers
    });
  },
  // 详情（回显）
  fetchMerchantInfo(params) {
    return request({
      url: '/znyy//brand/detail',
      method: 'get',
      params
    });
  },
  // 新增编辑
  updateMerchantManage(data) {
    return request({
      url: '/znyy/brand/manage',
      method: 'POST',
      data
    });
  },
  // /brand/manage
  //   fetchMerchantAudit(params) {
  //     return request({ url: '/web/piMerchant/merchantAudit', method: 'post', data: params });
  //   }
  //   // 新增
  //   addAgent(data) {
  //     return request({
  //       url: '/znyy/agent',
  //       method: 'POST',
  //       data
  //     });
  //   },
  //   // 修改回显
  //   echoAgent(id) {
  //     return request({
  //       url: '/znyy/agent/modifyEcho/' + id,
  //       method: 'PUT'
  //     });
  //   },
  //   // 编辑
  //   updateAgent(data) {
  //     return request({
  //       url: '/znyy/agent/updateContent',
  //       method: 'PUT',
  //       data
  //     });
  //   },
  //   // 获取银行分类列表
  //   categoryType(bankType) {
  //     return request({
  //       url: '/znyy/bvstatus/' + bankType,
  //       method: 'GET'
  //     });
  //   }
  //批量上传预计检   /brand/bindBrandCheck
  bindBrandCheck(data) {
    return request({
      url: '/znyy/brand/bindBrandCheck',
      method: 'POST',
      data
    })
  },
  //查询预检结果 
  checkPage(params) {
    return request({
      url: '/znyy/brand/checkPage',
      method: 'GET',
      params
    })
  },

  //品牌批量绑定  
  determineBindBrand(data) {
    return request({
      // url: '/znyy/brand/determineBindBrand?pageNum=' + data.pageNum',
      url: '/znyy/brand/determineBindBrand?processingId=' + data.processingId ,
      method: 'POST',
      data
    })
  },
  //品牌单个绑定
  bindBrand(data) {
    return request({
      url: '/znyy/brand/bindBrand?operationCode='+data.operationCode+'&brandCode='+data.brandCode,
      method: 'POST',
      data
    })
  },
  //搜索查询   /operations/v2/operationsList
  operationsList(params){
    return request({
      url: '/znyy/operations/v2/operationsList',
      method: 'GET',
      params
    })
  }

};
