<template>
  <div class="app-container">
    <el-card class="filter-options">
      <el-form :inline="true" :model="filterForm" ref="filterForm" class="demo-form-inline" label-width="100px">
        <el-form-item label="是否邀请进群" prop="status">
          <el-select v-model="filterForm.status" placeholder="请选择" @change="statusChange">
            <el-option label="是" :value="1"></el-option>
            <el-option label="否" :value="0"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="原因" prop="reason">
          <el-select :disabled="filterForm.status === 1" v-model="filterForm.reason" placeholder="请选择">
            <el-option label="未加入企业微信" value="未加入企业微信"></el-option>
            <el-option label="门店未开通" value="门店未开通"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button icon="el-icon-search" type="primary" @click="searchData">查询</el-button>
          <el-button icon="el-icon-refresh-left" @click="resetData">重置</el-button>
          <el-button icon="el-icon-back" @click="goBack">返回</el-button>
        </el-form-item>
      </el-form>
    </el-card>
    <el-table class="common-table" v-loading="tableLoading" :data="tableData" style="width: 100%; margin-bottom: 20px" border>
      <el-table-column prop="merchantCode" label="门店编号"></el-table-column>
      <el-table-column prop="merchantName" label="门店名称"></el-table-column>
      <el-table-column prop="realName" label="负责人"></el-table-column>
      <el-table-column prop="name" label="手机号"></el-table-column>
      <el-table-column prop="status" label="是否邀请进群">
        <template slot-scope="scope">
          <span v-if="!iscreated">-</span>
          <span v-else>{{ scope.row.status === 1 ? '是' : '否' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="remark" label="原因">
        <template slot-scope="scope">
          <span v-if="!iscreated">-</span>
          <span v-else>{{ scope.row.remark }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="id" label="操作">
        <template slot-scope="scope">
          <el-button type="text" v-if="scope.row.status === 0" @click="editGroupChat(scope.row)">更换群聊</el-button>
          <el-button type="text" v-if="!iscreated" @click="removePartner(scope.row)">移除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <el-col :span="24" style="overflow-x: auto" :xs="24">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="tablePage.currentPage"
        :page-sizes="[10, 20, 30, 40, 50]"
        :page-size="tablePage.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="tablePage.totalItems"
      ></el-pagination>
    </el-col>

    <!-- 更换群聊弹窗 -->
    <el-dialog title="更换群聊" :visible.sync="changeChatVisible" width="500px">
      <el-form :inline="true" :model="changeChatForm" class="demo-form-inline" label-width="100px">
        <el-form-item label="群聊名称">
          <el-select v-model="changeChatForm" filterable placeholder="请输入群聊名称">
            <el-option v-for="(item, index) in changeChatFormList" :key="index" :label="item.groupChatName" :value="item.id" @change="changeChatForm = item.id"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="changeChatVisible = false">取 消</el-button>
        <el-button :loading="changeChatLoading" type="primary" @click="submitChangeGroupChat">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
  import groupChatApi from '@/api/newPartner/groupChat';
  export default {
    data() {
      return {
        filterForm: {
          status: '',
          reason: ''
        },
        tableData: [],
        tablePage: {
          currentPage: 1,
          pageSize: 10,
          totalItems: 0
        },
        changeChatVisible: false,
        changeChatForm: '',
        changeChatFormList: [],
        tableLoading: false,
        bindVisible: false,
        groupChatId: '', // 群聊管理列表id
        chatUserId: '', // 合伙人列表详情id
        iscreated: false, // 是否已建群
        changeChatLoading: false
      };
    },
    created() {
      const { groupChatId, iscreated } = this.$route.query;
      if (groupChatId) {
        this.groupChatId = groupChatId ? groupChatId : '';
        this.iscreated = iscreated === 0 ? false : true;
        const params = {
          groupChatId: this.groupChatId,
          iscreated: iscreated
        };
        localStorage.setItem('partnerDetail', JSON.stringify(params));
      } else {
        let partnerDetail = JSON.parse(localStorage.getItem('partnerDetail'));
        this.groupChatId = partnerDetail.groupChatId ? partnerDetail.groupChatId : '';
        this.iscreated = partnerDetail.iscreated === 0 ? false : true;
      }

      this.getPartnerList();
    },
    methods: {
      getPartnerList() {
        this.tableLoading = true;
        const params = {
          pageNum: this.tablePage.currentPage,
          pageSize: this.tablePage.pageSize,
          groupChatId: this.groupChatId,
          status: this.filterForm.status,
          reason: this.filterForm.reason
        };
        groupChatApi
          .partnerList(params)
          .then((res) => {
            if (res.success && res.code === 20000) {
              this.tableData = res.data.data;
              this.tablePage.totalItems = Number(res.data.totalItems);
            }
          })
          .finally(() => {
            this.tableLoading = false;
          });
      },
      searchData() {
        this.tablePage.currentPage = 1;
        this.getPartnerList();
      },
      resetData() {
        this.$refs['filterForm'].resetFields();
        this.searchData();
      },
      goBack() {
        this.$store.dispatch('delVisitedViews', this.$route);
        this.$router.go(-1);
      },
      editGroupChat(item) {
        this.changeChatForm = '';
        this.changeChatFormList = [];
        this.changeChatVisible = true;
        this.chatUserId = item.id;
        groupChatApi.queryChatList({ chatUserId: this.chatUserId }).then((res) => {
          this.changeChatFormList = res.data;
        });
      },
      // 更换群聊
      submitChangeGroupChat() {
        const params = {
          chatUserId: this.chatUserId, // 合伙人列表id
          targetGroupChatId: this.changeChatForm // 目标群聊id
        };
        this.changeChatLoading = true;
        groupChatApi
          .changePartnerChat(params)
          .then((res) => {
            this.$message.success('更换群聊成功!');
            this.changeChatVisible = false;
            this.getPartnerList();
          })
          .finally(() => {
            this.changeChatLoading = false;
          });
      },
      removePartner(row) {
        this.bindVisible = true;
        const that = this;
        this.$confirm('请确认是否将该合伙人移除当前群聊?', '', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          showClose: false
        }).then(() => {
          const params = {
            chatUserId: row.id
          };
          groupChatApi.removePartner(params).then((res) => {
            that.$message({
              type: 'success',
              message: '已成功移除群聊!'
            });
            if (that.tablePage.currentPage > 1 && that.tableData.length === 1) {
              that.tablePage.currentPage = that.tablePage.currentPage - 1;
            }
            that.getPartnerList();
          });
        });
      },
      handleSizeChange(e) {
        this.tablePage.pageSize = e;
        this.getPartnerList();
      },
      handleCurrentChange(e) {
        this.tablePage.currentPage = e;
        this.getPartnerList();
      },
      // 是否邀请进群筛选项（若选择是，则清除原因筛选项）
      statusChange(e) {
        if (e === 1) {
          this.filterForm.reason = '';
        }
      }
    }
    // beforeDestroy() {
    //   let visitedViews = this.$store.getters.visitedViews;
    //   let i = visitedViews.findIndex((item) => item.path === this.$route.path);
    //   if (i === -1) {
    //     localStorage.removeItem('partnerDetail');
    //   }
    // }
  };
</script>

<style lang="scss" scoped>
  .filter-options {
    margin-bottom: 20px;
  }
  .el-form-item {
    margin-bottom: 0;
  }
</style>
