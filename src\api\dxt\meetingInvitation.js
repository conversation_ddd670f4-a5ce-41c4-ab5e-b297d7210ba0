/**
 * 邀请语相关接口
 */
import request from '@/utils/request'

export default {
  // 新增
  addMeetingInvitationData(data) {
    return request({
      url: '/dxt/web/meeting/invitation/add',
      method: 'POST',
      data
    })
  },
  // 修改回显
  detailMeetingInvitation(id) {
    return request({
      url: '/dxt/web/meeting/invitation/detail/' + id,
      method: 'GET'
    })
  },
  // 删除邀请语
  deleteMeetingInvitation(id) {
    return request({
      url: '/dxt/web/meeting/invitation/delete/' + id,
      method: 'DELETE'
    })
  },
  // 编辑
  updateMeetingInvitation(data) {
    return request({
      url: '/dxt/web/meeting/invitation/update',
      method: 'PUT',
      data
    })
  },
  // 获取
  presenterType() {
    return request({
      url: '/dxt/presenter/get/presenter',
      method: 'GET'
    })
  },
  // 分页查询
  meetingInvitationList(pageNum, pageSize, data) {
    return request({
      url: '/dxt/web/meeting/invitation/list/' + pageNum + '/' + pageSize,
      method: 'GET',
      params: data
    })
  },


}

