<template>
  <div class="app-container">
    <el-col :span="24" style="margin-bottom: 30px">
      <el-button type="success" icon="el-icon-plus" size="mini" @click="addBtn()">新增</el-button>
    </el-col>
    <el-table class="common-table" v-loading="tableLoading" :data="tableData" style="width: 100%;margin-bottom: 20px;"
      row-key="id" border default-expand-all>
      <el-table-column prop="id" label="ID"></el-table-column>
      <el-table-column prop="name" label="字典名称" />
      <el-table-column prop="withdrawnBonus" label="操作" width="480">
        <template slot-scope="scope">
          <el-button type="primary" size="mini" @click="editBtn(scope.row.id)">编辑
          </el-button>
          <el-button type="danger" size="mini" @click="handleDelete(scope.row.id)">删除
          </el-button>
        </template>
      </el-table-column>
      <el-table-column prop="label" label="唯一标识" show-overflow-tooltip />
    </el-table>
    <!-- 分页 -->
    <el-col :span="24" style="margin-bottom: 20px">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>

    <!-- 修改或者新增弹窗 -->
    <el-dialog title="编辑字典" :visible.sync="open" width="70%" @close="close">
      <el-form ref="form" :model="form" :rules="rules" label-width="130px" style="width: 70%;">
        <el-form-item label="字典名称：" prop="name">
          <el-input v-model="form.name" placeholder="字典名称" />
        </el-form-item>
        <el-form-item label="唯一标识：" prop="label">
          <el-input v-model="form.label" placeholder="字典唯一标识" :disabled="form.id != null" />
        </el-form-item>
        <el-form-item label="字典值：" prop="value">
          <el-input type="textarea" v-model="form.value" placeholder="字典值" :autosize="{ minRows: 6, maxRows: 10 }" />
        </el-form-item>
        <el-form-item label="音频：" prop="isAudio">
          <el-radio-group v-model="form.isAudio">
            <el-radio v-for="item in audioList" :label="item.value">{{ item.label }}</el-radio>
          </el-radio-group>
          <el-link type="primary" :underline="false" target="_blank" v-if="form.isAudio !== 0 && form.audioUrl !== ''"
            :href="form.audioUrl" style="font-size:12px;vertical-align: baseline;margin-left: 20px">预览
          </el-link>
        </el-form-item>
        <el-form-item v-show="form.isAudio === 2">
          <upload-file @handleSuccess="handleAudioSuccess" @handleRemove="handleAudioRemove" :file-list="audioFileList" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import commondictApi from '@/api/xi/commondict'
import { pageParamNames } from '@/utils/constants'
import UploadFile from '@/components/Upload/UploadFile'

export default {
  components: {
    UploadFile
  },
  name: 'commondict',
  data() {
    return {
      audioFileList: [],
      audioList: [{ label: '无', value: 0 }, { label: '合成', value: 1 }, { label: '文件上传', value: 2 }],
      dataQuery: {},
      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      open: false,
      tableData: [],
      form: {},
      // 表单校验
      rules: {
        name: [{ required: true, message: '请输入字典名称', trigger: 'blur' }],
        label: [{ required: true, message: '请输入唯一标识', trigger: 'blur' }],
        value: [{ required: true, message: '请输入字典值', trigger: 'blur' }],
        isAudio: [{ required: true, message: '请选择', trigger: 'blur' }]
      }
    }
  },
  created() {
    this.getPageList()
  },
  methods: {
    handleAudioSuccess(url, fileName) {
      this.form.audioUrl = url
    },
    handleAudioRemove() {
      this.form.audioUrl = ''
    },

    handleDelete(id) {
      this.$confirm('确定要删除吗？', '删除字典', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        commondictApi.delete(id).then(res => {
          this.$message.success('删除成功！')
          this.getPageList()
        })
      })
    },
    addBtn() {
      this.reset()
      this.open = true
    },
    editBtn(id) {
      this.reset()
      commondictApi.detail(id).then(res => {
        this.form = res.data
        this.open = true
      })
    },
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          commondictApi.saveOrUpdate(this.form).then(response => {
            this.$message.success('提交成功！')
            this.open = false
            this.getPageList()
          })
        }
      })
    },
    getPageList() {
      this.tableLoading = true
      this.dataQuery.pageNum = this.tablePage.currentPage
      this.dataQuery.pageSize = this.tablePage.size
      commondictApi.list(this.dataQuery).then(res => {
        this.tableData = res.data.data
        this.tableLoading = false
        // 设置后台返回的分页参数
        pageParamNames.forEach(name =>
          this.$set(this.tablePage, name, parseInt(res.data[name]))
        )
      })
    },
    handlePage() {
      (this.tablePage = {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      }),
        this.getPageList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dataQuery = {
        name: '',
        enable: null
      }
      this.getPageList()
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val
      this.getPageList()
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val
      this.getPageList()
    },
    close() {
      this.open = false
      this.reset()
    },
    reset() {
      this.audioFileList = []
      this.form = {
        id: null,
        name: null,
        label: null,
        value: null,
        isAudio: 0,
        audioUrl: null
      }
      if (this.$refs['form']) {
        this.$refs['form'].resetFields()
      }
    }
  }
}
</script>

<style>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.period-table td,
.period-table th {
  text-align: center;
}

@media (max-width: 767px) {
  .el-message-box {
    width: 80% !important;
  }
}

.el-tooltip__popper {
  max-width: 800px;
}
</style>
