<template>
  <div class="app-container">
    <el-form :inline="true" ref="queryForm" class="container-card" label-width="110px">
      <el-form-item label="学号：">
        <el-input v-model="dataQuery.studentCode" placeholder="请输入学号" clearable />
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-refresh" @click="resetQuery()">重置</el-button>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" type="primary" @click="search()">搜索</el-button>
      </el-form-item>
    </el-form>
    <el-table class="common-table" v-loading="tableLoading" :data="tableData" style="width: 100%;margin-bottom: 20px;"
      row-key="id" border default-expand-all>
      <el-table-column prop="id" label="ID"></el-table-column>
      <el-table-column prop="studentCode" label="学员编号" />
      <el-table-column prop="goodsId" label="商品id" />
      <el-table-column prop="goodsDto.name" label="商品名称" />
      <el-table-column prop="amount" label="兑换金币数" />
      <el-table-column prop="createTime" label="兑换时间" />
      <el-table-column prop="status" label="支付状态" sortable>
        <template slot-scope="scope">
          <span v-if="scope.row.status == 1" class="1">已支付</span>
          <span v-else>未支付</span>
        </template>
      </el-table-column>
      <el-table-column prop="payType" label="支付方式" sortable>
        <template slot-scope="scope">
          <span v-if="scope.row.payType === 1" class="1">金币支付</span>
          <span v-else class="2">其他</span>
        </template>
      </el-table-column>
      <el-table-column prop="validDays" label="有效期">
        <template slot-scope="scope">
          {{ scope.row.goodsDto.isAll ? '永久' : scope.row.goodsDto.validDays + '天' }}
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <el-col :span="24" style="margin-bottom: 20px">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>

    <!-- 修改或者新增弹窗 -->
    <el-dialog title="编辑兑换记录" :visible.sync="open" width="70%" @close="close">
      <el-form ref="form" :model="form" :rules="rules" label-width="130px" style="width: 70%;">
        <el-form-item label="兑换方式：" prop="type">
          <el-input v-model="form.type" placeholder="兑换方式" />
        </el-form-item>
        <el-form-item label="兑换金币数：" prop="amount">
          <el-input v-model="form.amount" placeholder="兑换金币数" />
        </el-form-item>
        <el-form-item label="商品id：" prop="goodsId">
          <el-input v-model="form.goodsId" placeholder="商品id" />
        </el-form-item>
        <el-form-item label="支付状态：" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :label="1">已支付</el-radio>
            <el-radio :label="2">未支付</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="支付方式：" prop="payType">
          <el-radio-group v-model="form.payType">
            <el-radio :label="1">金币支付</el-radio>
            <el-radio :label="2">其他</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="学员编号：" prop="studentCode">
          <el-input v-model="form.studentCode" placeholder="学员编号" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import exchangerecordApi from '@/api/xi/exchangerecord'
import { pageParamNames } from '@/utils/constants'

export default {
  name: 'exchangerecord',
  data() {
    return {
      dataQuery: {},
      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      open: false,
      tableData: [],
      form: {},
      // 表单校验
      rules: {
        type: [{ required: true, message: '请输入兑换方式', trigger: 'blur' }],
        amount: [{ required: true, message: '请输入兑换金币数', trigger: 'blur' }],
        goodsId: [{ required: true, message: '请输入商品id', trigger: 'blur' }],
        status: [{ required: true, message: '请选择支付状态', trigger: 'blur' }],
        payType: [{ required: true, message: '请选择支付方式', trigger: 'blur' }]
      }
    }
  },
  created() {
    this.getPageList()
  },
  methods: {
    /** 重置按钮操作 */
    resetQuery() {
      this.dataQuery = {
        studentCode: ''
      }
      this.getPageList()
    },
    search() {
      this.tablePage.currentPage = 1
      this.tablePage.size = 10
      this.getPageList()
    },
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          exchangerecordApi.saveOrUpdate(this.form).then(response => {
            this.$message.success('提交成功！')
            this.open = false
            this.getPageList()
          })
        }
      })
    },
    getPageList() {
      this.tableLoading = true
      this.dataQuery.pageNum = this.tablePage.currentPage
      this.dataQuery.pageSize = this.tablePage.size
      exchangerecordApi.list(this.dataQuery).then(res => {
        this.tableData = res.data.data
        this.tableLoading = false
        // 设置后台返回的分页参数
        pageParamNames.forEach(name =>
          this.$set(this.tablePage, name, parseInt(res.data[name]))
        )
      })
    },
    handlePage() {
      (this.tablePage = {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      }),
        this.getPageList()
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val
      this.getPageList()
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val
      this.getPageList()
    },
    close() {
      this.open = false
      this.reset()
    },
    reset() {
      this.form = {
        id: null,
        type: null,
        amount: null,
        goodsId: null,
        status: null,
        payType: null,
        studentCode: null
      }
      if (this.$refs['form']) {
        this.$refs['form'].resetFields()
      }
    }
  }
}
</script>

<style>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.period-table td,
.period-table th {
  text-align: center;
}

@media (max-width: 767px) {
  .el-message-box {
    width: 80% !important;
  }
}

.el-tooltip__popper {
  max-width: 800px;
}
</style>
