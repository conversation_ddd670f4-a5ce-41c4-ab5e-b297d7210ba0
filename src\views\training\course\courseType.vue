<template>
  <div class="app-container">
    <!-- <el-form :inline="true" ref="queryForm" class="container-card" label-width="110px" >
      <el-form-item label="课程分类名称：">
          <el-input v-model="form.dictLabel"  placeholder="请输入课程分类名称" clearable  /> 
      </el-form-item>
      <el-form-item label=" ">
        <el-button icon="el-icon-refresh" size="small" @click="resetQuery()">重置</el-button>
        <el-button icon="el-icon-search" size="small" type="primary" @click="search(1)" >搜索</el-button>
      </el-form-item>
    </el-form> -->
    <div class="container">
      <div class="courseType">
        <div class="tree-title">
          <span>课程全部</span>
          <el-button type="text" @click="clickAdd">添加</el-button>
        </div>
        <div class="search-input">
          <el-input
            clearable
            placeholder="请输入关键词"
            prefix-icon="el-icon-search"
            v-model="filterOptions.keyword"
            @clear="getCategoryTree"
            @keyup.enter.native="getCategoryTree"
          ></el-input>
        </div>
        <el-tree
          ref="categoryTree"
          v-loading="treeLoading"
          :data="categoryTreeData"
          node-key="id"
          default-expand-all
          highlight-current
          :expand-on-click-node="false"
          :current-node-key="currentNodeKey"
          @node-click="categoryClick"
        >
          <span class="custom-tree-node" slot-scope="{ node, data }">
            <i class="el-icon-document"></i>
            <span class="tree-label">{{ node.label }}</span>
            <span style="display: none" class="btns">
              <span class="el-icon-edit" @click.stop="handleUpdate(node, data)"></span>
              <span class="el-icon-delete" @click.stop="handleTypeDel(node, data)"></span>
              <el-button type="text" class="el-icon-top" :disabled="getActionState(node, data, 'up')" @click.stop="upClass(node, data)"></el-button>
              <el-button type="text" class="el-icon-bottom" :disabled="getActionState(node, data, 'down')" @click.stop="downClass(node, data)"></el-button>
            </span>
          </span>
        </el-tree>
      </div>

      <!-- <div class="SearchForm"> -->
      <!-- 添加 -->
      <!-- <div class="btn-add" style="margin-bottom: 10px">
          <el-button size="small" type="primary"  icon="el-icon-plus"  @click="clickAdd"  >添加</el-button >
        </div> -->
      <!-- 表格 -->
      <!-- <el-table  class="common-table" :data="tableData"  stripe border :default-sort="{ prop: 'date', order: 'descending' }" >
          <el-table-column type="index" width="100" label="序号"></el-table-column>
          <el-table-column prop="dictLabel" label="课程分类"></el-table-column>
          <el-table-column prop="remark" label="备注"></el-table-column>
          <el-table-column prop="id" label="操作" width="200">
            <template slot-scope="scope">
              <el-button type="success" size="mini" icon="el-icon-edit-outline" @click="handleUpdate(scope.row)" >编辑</el-button>
              <el-button @click="handleDel(scope.row.dictCode)" type="danger" size="mini" icon="el-icon-view" >删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <addCourseType ref="addCourseType" v-if="dialogFrom.visible" :dialog-param="dialogFrom" @closeDialog="closeDialog"  ></addCourseType>
      </div> -->
      <div class="list-area" v-if="currentNodeLevel == '3'">
        <div class="add-course-btn">
          <el-button type="primary" @click="selectCourse">添加课程</el-button>
        </div>
        <el-table class="common-table" :data="tableData" v-loading.body="tableLoading" :default-sort="{ prop: 'sort', order: 'descending' }">
          <el-table-column prop="courseCode" label="课程号"></el-table-column>
          <el-table-column prop="courseName" label="课程名称"></el-table-column>
          <el-table-column prop="remark" label="排序">
            <template slot-scope="scope">
              <el-button type="text" :class="{ disabled: scope.$index == 0 }" :disabled="scope.$index == 0" @click="courseCustomSort(scope.$index, 'up')">上移</el-button>
              <el-button
                type="text"
                :class="{ disabled: scope.$index == tableData.length - 1 }"
                :disabled="scope.$index == tableData.length - 1"
                @click="courseCustomSort(scope.$index, 'down')"
              >
                下移
              </el-button>
            </template>
          </el-table-column>
          <el-table-column prop="id" label="操作" width="200">
            <template slot-scope="scope">
              <el-button type="text" @click="handelCourseDel(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <!-- 分页 -->
        <el-col :span="24" style="margin-bottom: 20px; margin-top: 20px">
          <el-pagination
            :current-page="tablePage.currentPage"
            :page-sizes="[10, 20, 30, 40, 50]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="tablePage.totalItems"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </el-col>
      </div>
      <div class="list-area" v-else-if="currentNodeLevel == '1' || currentNodeLevel == '2'">
        <p class="toast">该分类为{{ currentNodeLevel == '1' ? '一' : '二' }}级分类，请至三级分类下绑定课程~</p>
      </div>
      <div class="list-area" v-else>
        <p class="toast">暂无数据~</p>
      </div>
    </div>
    <!-- 新建分类弹窗 -->
    <addCourseType ref="addCourseType" v-if="dialogFrom.visible" :dialog-param="dialogFrom" @closeDialog="closeDialog" @updateTree="getCategoryTree"></addCourseType>
    <!-- 添加课程弹窗 -->
    <select-course ref="addCourse" v-if="courseParams.showDialog" :coursePage="courseParams" @closeDialog="closeSelectCourse" @updateTable="search"></select-course>
  </div>
</template>

<script>
  import addCourseType from './components/addCourseType.vue';
  import SelectCourse from './components/selectCourse.vue';
  import courseApi from '@/api/training/course';
  export default {
    name: 'courseType',
    components: {
      addCourseType,
      SelectCourse
    },
    data() {
      return {
        filterOptions: {
          keyword: ''
        },
        treeLoading: false,
        tableLoading: false,
        dialogFrom: {
          visible: false,
          type: 'add'
        },
        // 分页
        tablePage: {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: 0
        },
        form: {
          // courseName: "",
          // type: "全部",
          categoryCode: ''
        },
        tableData: [],
        dictType: 'paper_course_category',
        categoryTreeData: [],
        courseParams: {
          showDialog: false
        },
        currentNodeKey: '',
        currentNodeLevel: '' // 当前点击节点的分类层级
      };
    },
    created() {
      // this.search(1)
      this.getCategoryTree();
    },
    methods: {
      // 获取课程分类树
      getCategoryTree() {
        this.treeLoading = true;
        this.categoryTreeData = [];
        const that = this;
        courseApi
          .queryCategoryTree(this.filterOptions)
          .then((res) => {
            that.categoryTreeData = that.deepReplace(res.data);

            if (that.categoryTreeData instanceof Array && that.categoryTreeData.length > 0) {
              that.currentNodeKey = that.categoryTreeData[0].id;
              that.currentNodeLevel = that.categoryTreeData[0].dictLevel;
              that.$nextTick(function () {
                that.$refs['categoryTree'].setCurrentKey(that.currentNodeKey);
              });
              // 查询第一个一级分类下绑定的课程
              that.form.categoryCode = that.categoryTreeData[0].id;
              that.search();
            }
            that.treeLoading = false;
          })
          .catch((err) => {
            that.treeLoading = false;
          });
      },
      deepReplace(array) {
        if (array instanceof Array && array.length >= 1) {
          return array.map((el) => {
            return {
              id: el.dictCode,
              label: el.dictLabel,
              children: this.deepReplace(el.childList),
              ...el
            };
          });
        } else {
          return [];
        }
      },
      getActionState(node, data, action) {
        const parent = node.parent;
        const children = parent.data.children || parent.data;
        const index = children.findIndex((el) => el.id == data.id);
        const len = children.length;
        if (index == 0 && action == 'up') {
          return true;
        } else if (index == len - 1 && action == 'down') {
          return true;
        }
        return false;
      },
      // 课程分类自定义排序
      upClass(node, data) {
        const parent = node.parent;
        const children = parent.data.children || parent.data;
        const currentIndex = children.findIndex((el) => el.id == data.id);
        let currentItem = children[currentIndex];
        let targetItem = children[currentIndex - 1];
        let tempSort = targetItem.dictSort;
        targetItem.dictSort = currentItem.dictSort;
        currentItem.dictSort = tempSort;

        let paramsArray = [currentItem, targetItem];
        paramsArray = paramsArray.map((item) => {
          return {
            dictCode: item.dictCode,
            dictLabel: item.dictLabel,
            dictValue: item.dictValue,
            dictType: item.dictType,
            remark: item.remark,
            roleTagList: item.roleTagList,
            roleTag: item.roleTag,
            categoryDescription: item.categoryDescription,
            dictLevel: item.dictLevel,
            parentCode: item.parentCode,
            dictSort: item.dictSort
          };
        });
        courseApi.courseCategorySort(paramsArray).then((res) => {
          this.$message.success('操作成功！');
          this.getCategoryTree();
        });
      },
      downClass(node, data) {
        const parent = node.parent;
        const children = parent.data.children || parent.data;
        const currentIndex = children.findIndex((el) => el.id == data.id);
        let currentItem = children[currentIndex];
        let targetItem = children[currentIndex + 1];
        let tempSort = targetItem.dictSort;
        targetItem.dictSort = currentItem.dictSort;
        currentItem.dictSort = tempSort;

        let paramsArray = [targetItem, currentItem];
        paramsArray = paramsArray.map((item) => {
          return {
            dictCode: item.dictCode,
            dictLabel: item.dictLabel,
            dictValue: item.dictValue,
            dictType: item.dictType,
            remark: item.remark,
            roleTagList: item.roleTagList,
            roleTag: item.roleTag,
            categoryDescription: item.categoryDescription,
            dictLevel: item.dictLevel,
            parentCode: item.parentCode,
            dictSort: item.dictSort
          };
        });
        courseApi.courseCategorySort(paramsArray).then((res) => {
          this.$message.success('操作成功！');
          this.getCategoryTree();
        });
      },
      // 分页
      handleSizeChange(val) {
        this.tablePage.currentPage = 1;
        this.tablePage.size = val;
        this.search();
      },

      handleCurrentChange(val) {
        this.tablePage.currentPage = val;
        this.search();
      },
      // 重置
      resetQuery() {
        this.tablePage.currentPage = 1;
        this.form = {};
        this.search();
      },

      // 搜索
      search(page) {
        const that = this;
        that.tableLoading = true;
        let param = { pageSize: this.tablePage.size, pageNum: page || this.tablePage.currentPage };
        param = { ...this.form, ...param };
        courseApi
          .coursePageByCategoryCode(param)
          .then((res) => {
            that.tableData = res.data ? res.data : [];
            that.tablePage.totalItems = Number(res.total);
          })
          .finally(() => {
            that.tableLoading = false;
          });
      },
      // 点击分类节点
      categoryClick(data) {
        this.form.categoryCode = data.id;
        this.currentNodeKey = data.id;
        this.currentNodeLevel = data.dictLevel;
        this.search();
      },

      // 新增
      clickAdd() {
        this.dialogFrom.visible = true;
        this.dialogFrom.type = 'add';
      },

      // 编辑
      handleUpdate(node, data) {
        this.dialogFrom.visible = true;
        this.dialogFrom.type = 'edit';
        this.$nextTick(() => {
          this.$refs.addCourseType.open(data);
        });
      },
      // 课程分类-绑定课程-自定义排序
      courseCustomSort(index, actionType) {
        let currentItem = this.tableData[index];
        let targetItem = null;
        let paramArray = [];
        if (actionType == 'up') {
          targetItem = this.tableData[index - 1];
          let tempSort = targetItem.sort;
          targetItem.sort = currentItem.sort;
          currentItem.sort = tempSort;
          [currentItem, targetItem].forEach((el) => {
            paramArray.push({
              id: el.id,
              sort: el.sort
            });
          });
        } else if (actionType == 'down') {
          targetItem = this.tableData[index + 1];
          let tempSort = targetItem.sort;
          targetItem.sort = currentItem.sort;
          currentItem.sort = tempSort;
          [targetItem, currentItem].forEach((el) => {
            paramArray.push({
              id: el.id,
              sort: el.sort
            });
          });
        }
        courseApi.categoryCourseRelationSort(paramArray).then((res) => {
          this.$message.success('操作成功！');
          this.search();
        });
      },
      handleTypeDel(node, info) {
        const that = this;
        this.$confirm('确认删除课程分类？删除分类前系统会检查分类下是否有子分类和课程，若存在，则不允许删除。', '课程分类删除确认', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            const formData = new FormData();
            formData.append('code', info.id);
            courseApi.categoryDelete(formData).then((res) => {
              that.$message.success('删除成功！');
              that.getCategoryTree();
            });
          })
          .catch(() => {
            this.$message({
              type: 'info',
              message: '已取消删除'
            });
          });
      },
      handleDel(id) {
        this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            courseApi.courseTypeDelete({ id: id }).then((res) => {
              this.resetQuery();
            });
          })
          .catch(() => {
            this.$message({
              type: 'info',
              message: '已取消删除'
            });
          });
      },
      handelCourseDel(item) {
        this.$confirm('此操作将会删除分类下该课程, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            courseApi.categoryCourseRelationDelete({ id: item.id }).then((res) => {
              this.$message.success('删除成功！');
              this.search();
            });
          })
          .catch(() => {
            this.$message({
              type: 'info',
              message: '已取消删除'
            });
          });
      },
      closeDialog() {
        this.dialogFrom.visible = false;
      },
      selectCourse() {
        this.courseParams.showDialog = true;
        this.$nextTick(() => {
          this.$refs['addCourse'].open(this.currentNodeKey);
        });
      },
      closeSelectCourse() {
        this.courseParams.showDialog = false;
      }
    }
  };
</script>

<style scoped>
  .SearchForm {
    width: 100%;
    margin-bottom: 15px;
    background: #fff;
    border-radius: 6px;
    padding: 5px 0;
    padding-left: 5px;
    box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.2);
  }
  .container-card {
    padding: 15px 15px 15px 15px;
    margin-bottom: 15px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  }

  .period-table td,
  .period-table th {
    text-align: center;
  }

  @media (max-width: 767px) {
    .el-message-box {
      width: 80% !important;
    }
  }

  .el-tooltip__popper {
    max-width: 800px;
  }

  /* 树形控件样式 */
  .el-icon-document {
    display: inline-block;
    margin-right: 6px;
  }
  ::v-deep .el-tree {
    max-height: 75vh;
    overflow-y: auto;
  }
  ::v-deep .el-tree::-webkit-scrollbar {
    width: 4px !important;
  }
  /* 滑块样式 */
  ::v-deep .el-tree::-webkit-scrollbar-thumb {
    background-color: #aaa;
    border-radius: 10px;
  }
  /* 滚动条轨道样式 */
  ::v-deep .el-tree::-webkit-scrollbar-track {
    background-color: #fafafa;
    border-radius: 10px;
  }

  ::v-deep .el-tree .el-tree-node__content:hover {
    color: #1890ff;
  }
  ::v-deep .el-tree .el-tree-node__content:hover .btns {
    display: inline-block !important;
    margin-left: 20px;
  }
  ::v-deep .el-tree .el-tree-node__content:hover .btns span {
    display: inline-block;
    margin-right: 6px;
    font-weight: 500;
  }
  ::v-deep .el-tree .tree-label {
    font-size: 15px;
  }
  .container {
    width: 100%;
    height: 100%;
    display: flex;
    box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.2);
    padding: 30px;
    box-sizing: border-box;
    border-radius: 6px;
  }
  .courseType {
    border: 1px solid #eee;
    flex: 1;
  }
  .search-input {
    padding-top: 20px;
    padding-left: 20px;
    padding-right: 20px;
    box-sizing: border-box;
  }
  .tree-title {
    width: 100%;
    background-color: #f0f0f0;
    border-bottom: 1px solid #eee;
    padding: 5px 20px;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 16px;
  }
  ::v-deep .courseType .el-tree {
    padding: 20px;
    box-sizing: border-box;
  }
  .list-area {
    flex: 4;
    margin-left: 10px;
  }
  .list-area .toast {
    color: #555;
    font-size: 22px;
    margin-top: 20%;
    transform: translateY(-50%);
    text-align: center;
  }

  /* 表格区域样式 */
  .add-course-btn {
    margin-bottom: 20px;
    display: flex;
    justify-content: flex-end;
  }
  ::v-deep .common-table .el-table__header th {
    background-color: #f0f0f0;
  }
  ::v-deep .el-pagination {
    text-align: right;
  }
  .disabled {
    color: #aaa;
  }
</style>
