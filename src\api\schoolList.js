/**
 * 单词水平相关接口
 */
import request from '@/utils/request';

const prefix = '/znyy';

export default {
  schoolPage(pageNum, pageSize, data) {
    return request({
      // url: '/znyy/school/page/' + pageNum + '/' + pageSize, // 旧
      // url: '/znyy/V2/merchant/page/' + pageNum + '/' + pageSize,
      url: '/znyy/V2/merchant/page',
      method: 'GET',
      params: {
        pageNum,
        pageSize,
        ...data
      }
    });
  },
  convertToOperationsSchool(merchantCode) {
    return request({
      url: '/znyy/school/update/school/type',
      method: 'PUT',
      params: { merchantCode: merchantCode }
    });
  },
  openEnable(id, isEnable, isEnableNew) {
    return request({
      url: '/znyy/school/update/enable/status/' + id + '/' + isEnable + '/' + isEnableNew,
      method: 'PUT'
    });
  },
  openEnableV2(id, isEnable, isEnableNew) {
    return request({
      // url: '/znyy/V2/merchant/update/enable/status/' + id + '/' + isEnable + '/' + isEnableNew,
      url: '/znyy/V2/merchant/update/enable/status',
      method: 'PUT',
      params: { id, isEnable, isEnableNew }
    });
  },
  rollback(id) {
    return request({
      url: '/znyy/course/rollback',
      method: 'PUT',
      params: { id: id }
    });
  },
  schoolCheck(data) {
    return request({
      url: '/znyy/school/update/check/status',
      method: 'POST',
      data
    });
  },

  schoolDetail(id) {
    return request({
      url: '/znyy/school/detail/' + id,
      method: 'GET'
    });
  },
  // 直营门店详情 新
  schoolDetailV2(id) {
    return request({
      url: '/znyy/V2/merchant/detail',
      method: 'GET',
      params: { id }
    });
  },
  schoolUpdateLogin(data) {
    return request({
      url: '/znyy/bSysConfig/update/account',
      method: 'PUT',
      data
    });
  },
  //直营门店新增
  addSchool(data) {
    return request({
      // url: '/znyy/school/save',// 旧
      url: '/znyy/V2/merchant/saveMerchant',
      method: 'POST',
      data
    });
  },
  //批量新增
  moreAddSchool(data) {
    return request({
      url: '/znyy/school/saveBatch',
      method: 'POST',
      data
    });
  },

  // 直营门店流程图开启
  startAndTakeUserTaskByAddSchool(data) {
    return request({
      url: '/activiti/flowOnlineOperation/startAndTakeUserTask/newAddDirectSchool',
      method: 'POST',
      data
    });
  },

  getCurrentAdmin() {
    return request({
      url: '/znyy/school/currentAdmin',
      method: 'GET'
    });
  },
  exportSchool(data) {
    return request({
      // url: '/znyy/school/to/excel', // 旧
      url: '/znyy/V2/merchant/export',
      method: 'GET',
      params: data,
      responseType: 'blob'
    });
  },
  openAccountAmount(id) {
    return request({
      url: '/znyy/school/openAccountAmount/' + id,
      method: 'PUT'
    });
  },

  // 获取指派中心
  belongDeliverAndAllDeliver() {
    return request({
      // url: '/znyy/school/queryDeliverList', // 旧
      url: '/znyy/V2/merchant/queryDeliverList',
      method: 'GET'
    });
  },

  // 指派门店所属交付中心
  assignDeliverAndAllDeliver(data) {
    return request({
      // url: '/znyy/school/assign/school/deliver', // 旧
      url: '/znyy/V2/merchant/assign/deliver',
      method: 'PUT',
      params: data
    });
  },

  // 获取门店指派交付中心记录列表
  deliverBindRecordList(data) {
    return request({
      // url: '/znyy/school/querySchoolDeliverBindRecordList', // 旧
      url: '/znyy/V2/merchant/querySchoolDeliverBindRecordList',
      method: 'GET',
      params: data
    });
  },
  // 获取e签宝二维码
  getEsignCode(id) {
    return request({
      url: '/znyy/fundConversion/bv-esign/flow-start',
      method: 'GET',
      params: {
        id: id
      }
    });
  },
  // 查询轮询时间
  getEsignPollingTime() {
    return request({
      url: '/znyy/fundConversion/bv-esign/polling-time',
      method: 'GET'
    });
  },
  // 查询e签宝签署状态
  getEsignStatus(id) {
    return request({
      url: '/znyy/fundConversion/bv-esign/flow-status',
      method: 'GET',
      params: {
        id: id
      }
    });
  },
  // 获取接口开放时间
  getTrainingOpenTime() {
    return request({
      url: '/znyy/trainingFeeEsign/time',
      method: 'GET'
    });
  },
  // 获取门店培训费合同二维码
  createTraingFeeCode(storeId) {
    return request({
      url: '/znyy/fundConversion/bv-esign/start',
      method: 'GET',
      params: { id: storeId }
    });
  },
  // 重新创建培训合同
  reCreateTrainingFeeCode(storeId) {
    return request({
      url: '/znyy/fundConversion/bv-esign/restart',
      method: 'GET',
      params: { id: storeId }
    });
  },
  // 查询门店培训费合同签署状态
  getTrainingContractStatus(id) {
    return request({
      url: '/znyy/fundConversion/bv-esign/status',
      method: 'GET',
      params: { id }
    });
  },
  // 查询培训合同轮询时间
  getContractPolling() {
    return request({
      url: '/znyy/fundConversion/bv-esign/polling',
      method: 'GET'
    });
  },
  // 培训合同-支付
  getTrainingPayInfo(merchantCode) {
    return request({
      url: '/znyy/trainingFeeEsign/trainingPay',
      method: 'GET',
      params: { merchantCode }
    });
  },
  // 根据创建合同流程id获取合同二维码
  getContractCode(params) {
    return request({
      url: '/znyy/fundConversion/bv-esign/contract',
      method: 'GET',
      params
    });
  },
  // 门店续费
  schoolRenewal(data) {
    return request({
      url: '/znyy/V2/merchant/merchantRenew',
      method: 'POST',
      params: data
    });
  },
  // 查看二维码及其状态
  fetchContractQrLink(data) {
    return request({
      url: '/znyy/sign/contract/qr-link',
      method: 'GET',
      params: data
    });
  },
  //查询未签署推广协议的门店数
  fetchUnsignContractCount() {
    return request({
      url: '/znyy/V2/merchant/judgeMerchantsNum',
      method: 'GET'
    });
  },
  // 门店申请退费
  applyRefund(data) {
    return request({
      url: '/znyy/V2/merchant/applyRefund',
      method: 'POST',
      params: data
    });
  },
  //禁用门店
  disabledSchool(data) {
    return request({
      url: '/znyy/V2/merchant/disableOrEnableSchool',
      method: 'PUT',
      params: data
    });
  },
  //   开通门店
  openSchoolApi(data) {
    return request({
      url: '/znyy/V2/merchant/openSchoolManual',
      method: 'POST',
      params: data
    });
  },

  /**
   * @returns {AxiosPromise}
   */
  checkMerchantCanChange(data) {
    return request({
      url: '/znyy/merchant/change/checkMerchantCanChange',
      method: 'GET',
      params: data
    });
  },

  /**
   * @returns {AxiosPromise}
   */
  changeMerchant(data) {
    return request({
      url: '/merchant/change/changeMerchant',
      method: 'POST',
      params: data
    });
  },

  // 查询门店俱乐部信息
  getClubInfo(merchantCode) {
    return request({
      url: '/znyy/V2/merchant/getClubInfo',
      params: { merchantCode },
      method: 'GET'
    });
  },

  editClubApi(data) {
    return request({
      url: '/znyy/V2/merchant/editClub',
      method: 'POST',
      params: data
    });
  },
  /**
   * 根据merchantCode修改申请退费状态为失败
   * @param {*} merchantCode
   */
  editRefundStatus(merchantCode) {
    return request({
      url: '/znyy/V2/merchant/updateRefundStatus',
      method: 'GET',
      params: { merchantCode }
    });
  },

  /**
   * 根据merchantCode获取名称
   * @param data
   * @returns {*}
   */
  getMerchantNameApi(data) {
    return request({
      url: '/znyy/merchant/change/getMerchantName',
      method: 'GET',
      params: data
    });
  },

  /**
   * 校验
   */
  checkNewClubAndReferenceApi(data) {
    return request({
      url: '/znyy/merchant/change/checkNewClubAndReference',
      method: 'GET',
      params: data
    });
  },
  /**
   * 确认更换俱乐部
   * @param data
   */
  confirmChangeClubApi(data) {
    return request({
      url: '/znyy/merchant/change/confirmChangeClub',
      method: 'GET',
      params: data
    });
  },

  /**
   * 校验是否可以更换门店推广大使
   * @param data
   * @returns {*}
   */
  checkMarketingAmbassadorApi(data) {
    return request({
      url: prefix + '/merchant/change/checkReferenceCanChange',
      method: 'GET',
      params: data
    });
  },

  /**
   * 校验门店新推广大使信息
   * @param data
   * @returns {*}
   */
  checkNewReferenceApi(data) {
    return request({
      url: prefix + '/merchant/change/checkNewReference',
      method: 'GET',
      params: data
    });
  },

  /**
   * 确认更换俱乐部与推广大使
   * @param data
   * @returns {*}
   */
  confirmChangeClubAndReferenceApi(data) {
    return request({
      url: prefix + '/merchant/change/confirmChangeReference',
      method: 'GET',
      params: data
    });
  }
};
