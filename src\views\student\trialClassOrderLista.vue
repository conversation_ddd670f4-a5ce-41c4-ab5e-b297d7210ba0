<!-- 待完善上课信息表-->
<template>
  <div class="app-container">
    <el-form :inline="true" class="container-card" label-width="110px" label-position="left" ref="dataQuery" :model="dataQuery">
      <el-row>
        <el-col :span="8" :xs="24">
          <el-form-item label="姓名：" prop="studentName">
            <el-input v-model="dataQuery.studentName" placeholder="请输入姓名" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="学员编号：" prop="studentCode">
            <el-input v-model="dataQuery.studentCode" placeholder="请输入学员编号" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="fetchData01()">搜索</el-button>
            <el-button icon="el-icon-refresh" @click="rest()">重置</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <el-table show-header v-loading="tableLoading" :data="tableData" style="width: 100%; margin-bottom: 20px" row-key="id" :tree-props="{ list: 'children', hasChildren: 'true' }">
      <el-table-column prop="orderNo" label="订单编号" width="" align="center"></el-table-column>
      <el-table-column prop="studentName" label="学员姓名" align="center"></el-table-column>
      <el-table-column prop="withdrawnBonus" label="操作" width="" align="center">
        <template slot-scope="scope">
          <el-button v-if="scope.row.isSubmit == 0" type="primary" size="mini" icon="el-icon-edit-outline" @click="fillinOutside(scope.row)">填写上课信息对接表</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="studentCode" label="学员编号" width="" align="center"></el-table-column>
      <el-table-column prop="phone" label="学员手机号" width="" align="center"></el-table-column>
      <el-table-column prop="rechargeHour" label="充值交付学时" width="" align="center"></el-table-column>
      <el-table-column prop="createTime" label="购买时间" width="" :show-overflow-tooltip="true" align="center"></el-table-column>
    </el-table>

    <!-- 分页 -->
    <el-col :span="24" style="margin-bottom: 20px">
      <el-pagination
        :current-page="tablePage.currentPage"
        :page-sizes="[10, 20, 30, 40, 50]"
        :page-size="tablePage.size"
        layout="total, sizes, prev, pager, next, jumper"
        :total="tablePage.totalItems"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-col>

    <el-dialog title="填写上课信息对接表" :visible.sync="dialogVisible" :close-on-click-modal="false" :before-close="handleClose">
      <el-form ref="abutmentList" :model="abutmentList" label-width="120px" :rules="rules">
        <el-form-item label="学员姓名" prop="studentName" style="width: 50%">
          <el-input v-model="abutmentList.studentName" placeholder="" disabled />
        </el-form-item>
        <el-form-item label="学员编号" prop="studentCode" style="width: 50%">
          <el-input v-model="abutmentList.studentCode" placeholder="" disabled />
        </el-form-item>
        <el-form-item label="联系方式" prop="phone" style="width: 50%">
          <el-input v-model="abutmentList.mobile" placeholder="" disabled />
        </el-form-item>
        <el-form-item label="年级" prop="grade" style="width: 50%">
          <el-select v-model="abutmentList.grade" placeholder="" disabled>
            <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="充值交付学时" prop="rechargeHour" style="width: 50%">
          <el-input v-model="abutmentList.rechargeHour" placeholder="" disabled />
        </el-form-item>
        <el-form-item label="课程规划" prop="courseProject" style="width: 90%">
          <el-row v-for="(item, index) in abutmentList.courseProject" :key="index">
            <el-col :span="12" :xs="24">
              <el-form-item :prop="`courseProject.${index}.courseName`" :rules="rules.courseName">
                <el-select
                  v-el-select-loadmore="handleLoadmore"
                  :loading="loadingShip"
                  :filter-method="filterValue"
                  filterable
                  remote
                  reserve-keyword
                  v-model="abutmentList.courseProject[index].courseName"
                  style="width: 17.4vw"
                  placeholder="请选择、可输入关键字"
                  @visible-change="courseVisible"
                >
                  <el-option
                    v-for="(childItem, childIndex) in option"
                    :key="childItem.id"
                    :label="childItem.courseName"
                    :value="childItem.id"
                    @click.native="clickCourseName(childItem, index)"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-row>
                <el-col :span="6">
                  <div v-if="index === abutmentList.courseProject.length - 1">
                    <el-button v-if="index != 0" @click="deleteCourse(index)" type="danger" icon="el-icon-minus" size="small" circle style="margin-bottom: 1vw"></el-button>
                    <el-button v-if="abutmentList.courseProject.length < 3" size="small" @click="addCourse" type="primary" icon="el-icon-plus" circle></el-button>
                  </div>
                  <el-button v-else @click="deleteCourse(index)" type="danger" icon="el-icon-minus" size="small" circle style="margin-bottom: 1vw"></el-button>
                </el-col>
                <el-col :span="18" v-if="item.courseName">
                  <el-button v-if="courseId == item.id" @click="setupThesaurus(item)" size="small" style="margin-bottom: 1vw" type="primary">
                    {{ show ? '取消首节课词库' : '设为首节课词库' }}
                  </el-button>
                  <el-button v-if="!courseId && !show" @click="setupThesaurus(item)" size="small" style="margin-bottom: 1vw" type="primary">
                    {{ show ? '取消首节课词库' : '设为首节课词库' }}
                  </el-button>
                </el-col>
              </el-row>
            </el-col>
          </el-row>
        </el-form-item>
        <el-form-item label="上课时间" prop="studyTimeList" style="width: 100%">
          <el-row v-for="(item, index) in abutmentList.studyTimeList" :key="index">
            <el-col :span="5" :xs="24">
              <el-form-item :prop="`studyTimeList.${index}.usableWeek`" :rules="rules.usableWeek">
                <el-select v-model="item.usableWeek" style="width: 7vw; margin-right: 1vw" placeholder="请选择星期几" @change="studyWeekChange(item.usableWeek, index)">
                  <el-option v-for="(week, val) in weeklist" :key="val" :label="week.label" :value="week.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="4" :xs="24" style="margin-right: 10px">
              <el-form-item :prop="`studyTimeList.${index}.startTime`" :rules="rules.startTime">
                <el-time-picker
                  class="time_style"
                  format="HH:mm"
                  value-format="HH:mm"
                  v-model="abutmentList.studyTimeList[index].startTime"
                  style="width: 100%"
                  :clearable="false"
                  @change="studyTimeChange(item.startTime, index)"
                  :picker-options="{ selectableRange: '00:00:00 - 23:59:00' }"
                  placeholder="开始时间"
                ></el-time-picker>
              </el-form-item>
            </el-col>
            <el-col :span="4" :xs="24" style="margin-right: 10px">
              <el-input-number
                @change="changeHour($event, item, index)"
                v-model="abutmentList.studyTimeList[index].hour"
                :min="1"
                :max="2"
                label="小时"
                size="mini"
                style="margin-right: 10px"
              ></el-input-number>
            </el-col>
            <el-col :span="4" :xs="24" style="margin-right: 10px">
              <el-time-picker
                disabled="true"
                format="HH:mm"
                value-format="HH:mm"
                style="width: 100%"
                v-model="abutmentList.studyTimeList[index].endTime"
                placeholder="结束时间"
              ></el-time-picker>
            </el-col>
            <el-col :span="5">
              <div v-if="index === abutmentList.studyTimeList.length - 1">
                <el-button v-if="index != 0" @click="deleteTime(index)" type="danger" icon="el-icon-minus" size="small" circle style="margin-bottom: 1vw"></el-button>
                <el-button size="small" @click="addTime" type="primary" icon="el-icon-plus" circle></el-button>
              </div>
              <el-button v-else @click="deleteTime(index)" type="danger" icon="el-icon-minus" size="small" circle style="margin-bottom: 1vw"></el-button>
            </el-col>
          </el-row>
        </el-form-item>
        <el-form-item label="复习时间" prop style="width: 90%; margin-top: 10px">
          <el-form-item prop="reviewWeek">
            <el-checkbox-group v-model="abutmentList.reviewWeek" size="medium" @change="reviewWeekChange">
              <el-checkbox-button v-for="item in weeklist" :label="item.value" :key="item.value" :vaule="item.value">
                {{ item.label }}
              </el-checkbox-button>
            </el-checkbox-group>
          </el-form-item>
          <el-form-item style="width: 40%; margin-top: 1vw" prop="reviewTime" :rules="rules.reviewTime">
            <el-time-picker v-model="abutmentList.reviewTime" placeholder="开始时间" format="HH:mm"></el-time-picker>
          </el-form-item>
        </el-form-item>
        <!--              <el-form-item label="首次上课时间" prop="rechargeHour" style="width: 50%;" >-->
        <!--                <el-input v-model="abutmentList.rechargeHour" placeholder="请先选择上课时间" disabled/>-->
        <!--                <div style="color: #999999;font-size: 11px;line-height: 14px">限制24小时之后</div>-->
        <!--              </el-form-item>-->
        <div style="position: relative; display: flex; margin-bottom: 22px; align-items: center">
          <div style="width: 120px; text-align: end; padding-right: 12px">
            首次上课时间
            <div style="color: #999999; font-size: 11px">限制24小时之后</div>
          </div>
          <div class="timeClass" style="margin-left: 0; line-height: 36px; width: 37%">
            <span style="margin: 0 15px; color: #000000" v-if="abutmentList.firstStudyTime">
              {{ abutmentList.firstStudyTime }}
            </span>
            <span v-else style="margin: 0 15px; color: #999999">请先选择上课时间</span>
          </div>
        </div>
        <el-form-item label="是否试课" prop="isExp" style="width: 50%">
          <el-radio-group v-model="abutmentList.isExp" @input="onTestChange">
            <el-radio :label="1">是</el-radio>
            <el-radio :label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="词汇量检测" prop="wordBase" style="width: 50%" v-if="abutmentList.isExp === 1">
          <el-input v-model="abutmentList.wordBase" placeholder="" disabled></el-input>
        </el-form-item>

        <el-form-item label="是否新生" prop="isNewStudent" style="width: 50%">
          <el-radio-group v-model="abutmentList.isNewStudent">
            <el-radio :label="1">是</el-radio>
            <el-radio :label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark" style="width: 70%">
          <el-input v-model="abutmentList.remark" type="textarea" placeholder="请输入" :rows="5" maxlength="200" />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
  import dayjs from 'dayjs';
  import studentApi from '@/api/studentList';
  import { pageParamNames } from '@/utils/constants';
  import courseApi from '@/api/courseList';
  import schoolList from '@/api/schoolList';
  import systemApi from '@/api/systemConfiguration';
  export default {
    name: 'studentList',
    directives: {
      'el-select-loadmore': {
        bind(el, binding) {
          const SELECTWRAP_DOM = el.querySelector('.el-select-dropdown .el-select-dropdown__wrap');
          SELECTWRAP_DOM.addEventListener('scroll', function () {
            //临界值的判断滑动到底部就触发
            const condition = this.scrollHeight - this.scrollTop <= this.clientHeight;
            if (condition) {
              binding.value();
            }
          });
        }
      }
    },
    data() {
      return {
        tableLoading: false,
        // 分页
        tablePage: {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null
        },
        exportLoading: false,
        tableData: [],
        dataQuery: {
          studentName: '',
          studentCode: ''
        },

        dialogVisible: false,
        abutmentList: {
          id: '',
          studentName: '',
          studentCode: '',
          mobile: '',
          grade: '',
          rechargeHour: '',
          courseProject: [
            {
              courseName: ''
            }
          ],
          reviewWeek: [],
          reviewTime: '',
          studyTimeList: [
            {
              usableWeek: '',
              endTime: '',
              startTime: '',
              usableHourEnd: 0,
              usableMinuteEnd: 0,
              usableHourStart: 0,
              usableMinuteStart: 0
            }
          ],
          isExp: 0,
          isNewStudent: 0,
          isSubmit: 0,
          wordBase: 0,
          remark: '',
          firstStudyTime: ''
        },

        //年纪
        options: [
          { grade: '18', value: '幼儿园' },
          {
            value: 1,
            label: '一年级'
          },
          {
            value: 2,
            label: '二年级'
          },
          {
            value: 3,
            label: '三年级'
          },
          {
            value: 4,
            label: '四年级'
          },
          {
            value: 5,
            label: '五年级'
          },
          {
            value: 6,
            label: '六年级'
          },
          {
            value: 7,
            label: '初一'
          },
          {
            value: 8,
            label: '初二'
          },
          {
            value: 9,
            label: '初三'
          },
          {
            value: 10,
            label: '高一'
          },
          {
            value: 11,
            label: '高二'
          },
          {
            value: 12,
            label: '高三'
          },
          {
            value: 13,
            label: '大一'
          },
          {
            value: 14,
            label: '大二'
          },
          {
            value: 15,
            label: '大三'
          },
          {
            value: 16,
            label: '大四'
          },
          {
            value: 17,
            label: '其他'
          }
        ],
        //课程
        option: [],
        loadingShip: false,
        selectObj: {
          pageNum: 1,
          pageSize: 10,
          name: ''
        },
        weeklist: [
          {
            value: 0,
            label: '星期一'
          },
          {
            value: 1,
            label: '星期二'
          },
          {
            value: 2,
            label: '星期三'
          },
          {
            value: 3,
            label: '星期四'
          },
          {
            value: 4,
            label: '星期五'
          },
          {
            value: 5,
            label: '星期六'
          },
          {
            value: 6,
            label: '星期天'
          }
        ],

        rules: {
          // courseProject: [
          //     { type: 'array', required: true, message: '请选择课程规划', trigger: 'change' }
          // ],
          courseName: [{ required: true, message: '请选择课程规划', trigger: 'change' }],
          usableWeek: {
            required: true,
            message: '请选择上课星期',
            trigger: 'change'
          },
          startTime: {
            required: true,
            message: '请选择上课时间',
            trigger: 'change'
          },
          reviewWeek: {
            type: 'array',
            required: true,
            message: '请选择复习星期',
            trigger: 'change'
          },
          reviewTime: {
            required: true,
            message: '请选择复习时间',
            trigger: 'change'
          },
          isExp: [{ required: true, message: '请选择是否试课', trigger: 'change' }],
          isNewStudent: [{ required: true, message: '请选择是否新生', trigger: 'change' }]
        },

        show: false, // 是否为首节课词库
        courseId: '', // 课程id
        currentAdmin: ''
      };
    },
    created() {
      this.fetchData();
      this.getCurrentAdmin();
      this.getCourseList();
    },
    watch: {
      'abutmentList.studyTimeList': {
        handler(n, o) {
          this.getFirstStudyTime();
        },
        immediate: true,
        deep: true
      }
    },
    methods: {
      // 设置首节课词库
      setupThesaurus(val) {
        // if(!val.courseName){
        //   this.$message.error("请选择课程");
        //   return;
        // }
        this.show = !this.show;
        this.courseId = val.id;
        if (!this.show) {
          this.courseId = '';
        }
      },
      getCurrentAdmin() {
        schoolList.getCurrentAdmin().then((res) => {
          this.currentAdmin = res.data;
        });
      },
      addCourse() {
        this.abutmentList.courseProject.push({
          courseName: ''
        });
      },

      deleteCourse(index) {
        this.abutmentList.courseProject.splice(index, 1);
        this.courseId = '';
        this.show = false;
      },
      /**
       * 下拉加载
       */
      handleLoadmore() {
        if (!this.loadingShip) {
          this.selectObj.pageNum++;
          this.getCourseList();
        }
      },

      filterValue(value) {
        console.log(value);
        this.option = [];
        this.selectObj.pageNum = 1;
        this.selectObj.name = value;
        this.getCourseList();
      },

      //重置
      rest() {
        this.$refs.dataQuery.resetFields();
        this.fetchData01();
      },
      fetchData01() {
        this.tablePage = {
          currentPage: 1,
          size: this.tablePage.size,
          totalPage: null,
          totalItems: null
        };
        this.fetchData();
      },
      fetchData() {
        const that = this;
        that.tableLoading = true;
        that.dataQuery.pageNum = that.tablePage.currentPage;
        that.dataQuery.pageSize = that.tablePage.size;
        studentApi.selStudentContactInfoPage(that.dataQuery).then((res) => {
          console.log(res);
          that.tableData = res.data.data;
          that.tableLoading = false;
          // 设置后台返回的分页参数 totalItems totalItems
          pageParamNames.forEach((name) => that.$set(that.tablePage, name, res.data[name] === '' ? 0 : parseInt(res.data[name])));
        });
      },

      getCourseList() {
        let that = this;
        that.loadingShip = true;
        let dataQuery = {
          courseCode: '',
          courseName: that.selectObj.name,
          categoryCode: '',
          bigClassCode: '',
          courseStage: '',
          courseLevel: '',
          courseType: 'Sys',
          courseEdition: '',
          isEnable: '1'
        };
        courseApi.courseList(that.selectObj.pageNum, that.selectObj.pageSize, dataQuery).then((res) => {
          console.log(res);
          that.option = that.option.concat(res.data.data);
          that.loadingShip = false;
        });
      },

      fillTableNormalData(item) {
        const that = this;
        let data = {
          id: item.id
        };
        studentApi.getStudentContactInfoDetail(data).then((res) => {
          that.abutmentList = res.data;
          that.abutmentList.isExp = '';
          that.abutmentList.isNewStudent = '';
          that.abutmentList.courseProject = [
            {
              courseName: ''
            }
          ];
          that.abutmentList.reviewWeek = [];
          that.abutmentList.studyTimeList = [];
          that.abutmentList.studyTimeList.push({
            usableWeek: '',
            endTime: '',
            startTime: '',
            hour: 1
          });
          console.log(that.abutmentList);
        });
      },
      // 分页
      handleSizeChange(val) {
        this.tablePage.size = val;
        this.fetchData();
      },
      handleCurrentChange(val) {
        this.tablePage.currentPage = val;
        this.fetchData();
      },

      fillinOutside(row) {
        this.fillTableNormalData(row);
        this.dialogVisible = true;
        // setTimeout(()=>{
        //   this.$refs['abutmentList'].resetFields();
        // },100)
      },

      addTime() {
        this.abutmentList.studyTimeList.push({
          usableWeek: '',
          endTime: '',
          startTime: '',
          hour: 1
        });
      },

      deleteTime(index) {
        this.abutmentList.studyTimeList.splice(index, 1);
      },

      courseVisible(e) {
        console.log(e);
        if (e) {
          this.option = [];
          this.selectObj.pageNum = 1;
          this.selectObj.name = '';
          this.getCourseList();
        }
      },

      clickCourseName(childItem, index) {
        if (this.getRepeatCourse(childItem) !== -1) {
          this.$message.error('已选择该课程');
          this.abutmentList.courseProject[index].courseName = '';
          this.abutmentList.courseProject[index].id = '';
          return;
        }
        if (this.abutmentList.courseProject[index]) {
          this.abutmentList.courseProject.splice(index, 1, childItem);
        } else {
          this.abutmentList.courseProject.push(childItem);
        }
        console.log('this.abutmentList.courseProject');
        console.log(this.abutmentList.courseProject);
        console.log('this.abutmentList.courseProject');
      },

      getRepeatCourse(item) {
        for (let i = 0; i < this.abutmentList.courseProject.length; i++) {
          if (this.abutmentList.courseProject[i].id === item.id) {
            return i;
          }
        }
        return -1;
      },

      submitForm() {
        const that = this;
        let reviewTime = that.getReviewTime(that.abutmentList.reviewTime);
        if (reviewTime.includes('NaN')) {
          this.$message.error('复习开始时间格式错误~');
          return;
        }
        if (!that.courseId) {
          this.$message.error('请选择首节课词库');
          return;
        }
        that.$refs['abutmentList'].validate((valid) => {
          console.log(valid);
          if (valid) {
            let data = {
              courseProject: JSON.stringify(that.getServerCourseProject()),
              id: that.abutmentList.id,
              isExp: that.abutmentList.isExp,
              isNewStudent: that.abutmentList.isNewStudent,
              remark: that.abutmentList.remark,
              reviewWeek: JSON.stringify(that.abutmentList.reviewWeek),
              reviewTime: reviewTime,
              studyTimeList: that.getServiceStudyTimeList(that.abutmentList.studyTimeList),
              wordBase: that.abutmentList.wordBase,
              firstTime: that.abutmentList.firstTime,
              firstWeek: that.abutmentList.firstWeek
            };
            console.log(data);
            studentApi.submitStudentContactInfo(data).then((res) => {
              this.courseId = '';
              this.show = false;
              // this.$refs['abutmentList'].resetFields();
              this.dialogVisible = false;
              that.fetchData01();
              this.abutmentList = {};
              this.$message.success('提交成功');
            });
          } else {
            return false;
          }
        });
      },

      getReviewTime(time) {
        let date = new Date(time);
        let hour = date.getHours() > 9 ? date.getHours() : '0' + date.getHours();
        let minutes = date.getMinutes() > 9 ? date.getMinutes() : '0' + date.getMinutes();
        return `${hour}:${minutes}`;
      },

      getServerCourseProject() {
        let arr = [];
        for (let i = 0; i < this.abutmentList.courseProject.length; i++) {
          if (this.abutmentList.courseProject[i].id) {
            let data = {
              courseName: this.abutmentList.courseProject[i].courseName,
              courseId: this.abutmentList.courseProject[i].id
            };
            arr.push(data);
          }
        }
        arr = arr.map((item) => ({
          ...item,
          isFirstWordBase: this.courseId == item.courseId
        }));
        return arr;
      },

      getServiceStudyTimeList(list) {
        for (let i = 0; i < list.length; i++) {
          let data = list[i];
          let [startHours, startMinutes] = data.startTime.split(':').map(Number);
          let [endHours, endMinutes] = data.endTime.split(':').map(Number);
          data.usableHourEnd = endHours;
          data.usableMinuteEnd = endMinutes;
          data.usableHourStart = startHours;
          data.usableMinuteStart = startMinutes;
        }
        return list;
      },

      reviewWeekChange(val) {
        console.log(val);
      },

      studyWeekChange(e, index) {
        if (e != null) {
          if (this.abutmentList.studyTimeList[index].startTime && this.abutmentList.studyTimeList[index].endTime) {
            if (this.judgeAllBetween(this.abutmentList.studyTimeList[index].startTime, this.abutmentList.studyTimeList[index].endTime, index)) {
              this.$message.error('选择时间段与其他时间段重叠');
              this.abutmentList.studyTimeList[index].usableWeek = '';
              return;
            }
          }
        }
      },

      changeHour(e, item, index) {
        if (item.startTime == '') {
          return;
        }
        item.endTime = this.getAutoEndTime(item.startTime, item.hour);
        if (item.startTime != '' && item.endTime != '') {
          if (this.judgeAllBetween(item.startTime, item.endTime, index)) {
            item.startTime = '';
            item.endTime = '';
            this.$message.error('选择时间段与其他时间段重叠');
          }
        }
      },

      //自动获取结束时间
      getAutoEndTime(startTime, addHour) {
        let timeParts = startTime.split(':');
        let hours = parseInt(timeParts[0], 10);
        let minutes = parseInt(timeParts[1], 10);

        hours += addHour;

        if (hours >= 24) {
          hours -= 24;
        }
        let endTime = `${hours < 10 ? '0' + hours : hours}:${minutes < 10 ? '0' + minutes : minutes}`;
        return endTime;
      },
      studyTimeChange(e, index) {
        console.log(e);
        if (e) {
          this.abutmentList.studyTimeList[index].endTime = this.getAutoEndTime(e, this.abutmentList.studyTimeList[index].hour);
          if (this.judgeAllBetween(this.abutmentList.studyTimeList[index].startTime, this.abutmentList.studyTimeList[index].endTime, index)) {
            this.$message.error('选择时间段与其他时间段重叠');
            this.abutmentList.studyTimeList[index].startTime = '';
            this.abutmentList.studyTimeList[index].endTime = '';
            return;
          }
        }
      },

      judgeAllBetween(start, end, index) {
        console.log(index);
        let week = this.abutmentList.studyTimeList[index].usableWeek;
        if (week.toString().length == 0) {
          return false;
        }
        let s1 = this.getMinutesForTime(start);
        let e1 = this.getMinutesForTime(end);
        console.log(this.abutmentList.studyTimeList);
        for (let i = 0; i < this.abutmentList.studyTimeList.length; i++) {
          if (index == i || this.abutmentList.studyTimeList[i].usableWeek != week) {
            continue;
          }
          let s2 = this.getMinutesForTime(this.abutmentList.studyTimeList[i].startTime);
          let e2 = this.getMinutesForTime(this.abutmentList.studyTimeList[i].endTime);
          if (s1 <= s2 && e1 >= e2) {
            return true;
          }
          if (s1 >= s2 && e1 <= e2) {
            return true;
          }
          if (s1 <= s2 && e1 <= e2 && s2 <= e1) {
            return true;
          }
          if (s1 >= s2 && e1 >= e2 && s1 <= e2) {
            return true;
          }
        }
        return false;
      },

      getMinutesForTime(time) {
        let [hours, minutes] = time.split(':').map(Number);
        return hours * 60 + minutes;
      },

      onTestChange(val) {
        console.log(val);
        if (val == 1 && this.abutmentList.wordBase == 0) {
          this.getVocabulary();
        }
      },

      getVocabulary() {
        let that = this;
        let data = {
          studentCode: that.abutmentList.studentCode
        };
        studentApi.getTestVocabularyet(data).then((res) => {
          console.log(res);
          that.abutmentList.wordBase = res.data.vocabulary;
        });
      },

      handleClose() {
        this.courseId = '';
        this.show = false;
        // this.$refs['abutmentList'].resetFields();
        this.abutmentList = {};
        this.dialogVisible = false;
      },

      ////////首次上课时间///////////
      getFirstStudyTime() {
        console.log(this.abutmentList.studyTimeList.length);
        if (this.abutmentList.studyTimeList.length === 0) {
          this.abutmentList.firstWeek = '';
          this.abutmentList.firstTime = '';
          this.abutmentList.firstStudyTime = '';
          return;
        }
        let dateArr = [];
        let dateMinArr = [];
        let nowDate = dayjs().format('YYYY-MM-DD HH:mm');
        for (let i = 0; i < this.abutmentList.studyTimeList.length; i++) {
          if (this.abutmentList.studyTimeList[i].startTime == '') {
            continue;
          }
          let date = this.getChoseDataToOption(this.abutmentList.studyTimeList[i]);
          dateArr.push(date);
          let dayjs1 = dayjs(date);
          dateMinArr.push(dayjs1.diff(nowDate, 'minute'));
        }
        if (dateArr.length === 0) {
          this.abutmentList.firstWeek = '';
          this.abutmentList.firstTime = '';
          this.abutmentList.firstStudyTime = '';
          return;
        }
        console.log(dateArr);
        console.log(dateMinArr);
        let needIndex = -1;
        let minMinVal = Infinity;
        for (let i = 0; i < dateMinArr.length; i++) {
          if (dateMinArr[i] > 24 * 60 && dateMinArr[i] < minMinVal) {
            needIndex = i;
            minMinVal = dateMinArr[i];
          }
        }
        console.log(dateArr);
        console.log(needIndex);
        if (needIndex != -1) {
          this.abutmentList.firstStudyTime = this.getFormatToShow(dateArr[needIndex]);
        } else {
          let minIndex = dateMinArr.indexOf(Math.min(...dateMinArr));
          let lastDate = dayjs(dateArr[minIndex]).add(7, 'day');
          this.abutmentList.firstStudyTime = this.getFormatToShow(lastDate);
        }
      },

      getFormatToShow(date) {
        let str = dayjs(date).format('MM月DD日& HH:mm');
        let weekIndex = this.getLocalTypeWeek(dayjs(date).day());
        this.abutmentList.firstTime = dayjs(date).format('YYYY-MM-DD HH:mm');
        this.abutmentList.firstWeek = weekIndex;
        let allStr = str.replace('&', this.getWeekName(weekIndex));
        return allStr;
      },

      getWeekName(week) {
        for (let i = 0; i < this.weeklist.length; i++) {
          if (this.weeklist[i].value == week) {
            return this.weeklist[i].label;
          }
        }
        return '';
      },

      getChoseDataToOption(data) {
        let date = this.getDateForWeek(data.usableWeek);
        return date + ' ' + data.startTime;
      },

      getDateForWeek(week) {
        let nowWeek = this.getLocalTypeWeek(dayjs().day());
        let diff = week - nowWeek;
        let date = '';
        if (diff >= 0) {
          date = dayjs().add(Math.abs(diff), 'day').format('YYYY-MM-DD');
        } else {
          date = dayjs().subtract(Math.abs(diff), 'day').format('YYYY-MM-DD');
        }
        return date;
      },

      getLocalTypeWeek(nowWeek) {
        return (nowWeek + 6) % 7;
      }
    }
  };
</script>

<style>
  .container-card {
    padding: 15px 15px 15px 15px;
    margin-bottom: 15px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  }

  ::v-deep .el-table th {
    text-align: center;
    background-color: #6b9fff !important;
    border-right: 1px solid #fff;
    color: #ffffff;
  }

  .timeClass {
    border: 1px solid #dfe4ed;
    border-radius: 5px;
    background-color: #fff;
    box-sizing: border-box;
    margin-left: 20px;
  }
</style>
