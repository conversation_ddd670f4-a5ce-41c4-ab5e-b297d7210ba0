<template>
  <div class="app-container">
    <el-form class="container-card" :model="query" ref="form" label-width="80px" :inline="true" size="small">
      <el-row type="flex" style="flex-wrap: wrap">
        <!-- 采购订单编号 -->
        <el-col :span="5" :xs="24">
          <el-form-item label="订单号">
            <el-input v-model="query.sourceOrderId" placeholder="请输入订单号"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="5" :xs="24" v-if="hasNoPermissionField('门店名称')">
          <el-form-item label="门店名称">
            <el-input v-model="query.merchantName" placeholder="请输入门店名称"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="5" :xs="24">
          <el-form-item label="交易类型">
            <el-select v-model="query.payType" clearable placeholder="请选择" @change="tradeChange">
              <el-option v-for="item in options1" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="5" :xs="24">
          <el-form-item label="收入类型">
            <el-select v-model="query.businessTag" clearable placeholder="请选择" @change="handleChange">
              <el-option v-for="item in optionService" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="5" :xs="24">
          <el-form-item label="状态">
            <el-select v-model="query.status" clearable placeholder="请选择" @change="stateChange">
              <el-option v-for="item in optionList" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="5" :xs="24">
          <el-form-item label="创建时间">
            <el-date-picker
              v-model="date"
              @change="dateChange"
              value-format="yyyy-MM-dd"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            ></el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="3" :xs="20" style="margin-left: auto">
          <el-form-item>
            <el-button type="primary" @click="serach">搜索</el-button>
            <el-button @click="reset">重置</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-row style="display: flex; margin: 10px 0; align-items: center; font-size: 14px" v-if="isSchool">
      <div>
        <span style="margin-right: 5px">门店总收入：￥{{ totalMoney || 0 }}</span>
        <span style="margin-right: 5px">账期内余额：￥{{ paymentIn || 0 }}</span>
        <span style="margin-right: 5px">已分润总金额：￥{{ waitAllocateFunds || 0 }}</span>
        <span style="margin-right: 5px">可提现金额：￥{{ availableCashAmount || 0 }}</span>
      </div>
      <el-button type="primary" size="mini" @click="Payouts">提现</el-button>
      <el-button type="success" size="mini" @click="gopay">实名认证</el-button>
    </el-row>

    <el-table
      v-loading="tableLoading"
      element-loading-text="数据加载中..."
      border
      max-height="450px"
      :data="list"
      stripe
      :header-cell-style="{ background: '#eef1f6', color: '#606266' }"
    >
      <el-table-column v-for="col in columns" :prop="col.id" :key="col.id" :label="col.label" :width="col.width" align="center">
        <template v-slot="{ row }">
          <!-- 针对金额字段加样式 -->
          <span v-if="col.id === 'amount'" class="amount">{{ (row.amount * 0.01).toFixed(2) }}</span>
          <span v-else>
            {{ row[col.id] }}
          </span>
        </template>
      </el-table-column>
    </el-table>
    <el-row>
      <el-col :span="20">
        <Page :total="page_data.total" :page.sync="page_data.page" :limit.sync="page_data.pageSize" @pagination="index" />
      </el-col>
    </el-row>
    <el-dialog title="提现" :visible.sync="payoutsDialogVisible" width="30%" :before-close="closePopup">
      <div>
        <el-form ref="form" :model="withdrawal" label-width="100px">
          <el-form-item label="可提现余额:">{{ availableCashAmount }}元</el-form-item>
          <el-form-item label="提现金额:">
            <el-input placeholder="请输入提现金额" v-model="withdrawal.amount" type="number"></el-input>
          </el-form-item>
          <el-form-item label="备注:">
            <el-input type="textarea" v-model="withdrawal.remark"></el-input>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="closePopup">取 消</el-button>
        <el-button type="primary" @click="ensurePayouts">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog :visible.sync="signContract" width="80" :before-close="closeSignContract">
      <div class="signContent">
        <div class="signTitle">提现须知</div>
        <div>
          <iframe :src="agreementUrl" frameborder="0" style="width: 28vw; height: 70vh"></iframe>
        </div>
        <el-button type="primary" plain @click="seeKnow">同意</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import { mapGetters } from 'vuex';
  import store from '@/store';
  import { merchantEarnings, merchantBalance, businessTag, orderStatus, getMerchantBalance, checkedSign, signTime, cashWithdrawal } from '@/api/storeRevenue';
  import Page from '@/components/Pages/pages.vue';
  import checkPermission from '@/utils/permission';
  import { number } from 'echarts';
  export default {
    components: {
      Page
    },
    data() {
      return {
        tableLoading: false, // 表格加载状态
        isSchool: false, // 是否是门店
        isAdmin: false, // 是否是超级管理员
        waitAllocateFunds: '',
        availableCashAmount: '',
        totalMoney: '',
        paymentIn: '',
        token: store.getters.token,
        query: {
          payType: '',
          businessTag: '',
          merchantName: '',
          status: '',
          endTime: '',
          startTime: ''
        },

        list: [],
        date: [],
        options1: [
          {
            value: 'INNER_AGENT_PAY',
            label: '结算'
          },
          {
            value: 'INNER_AGENT_COLLECTION',
            label: '代收'
          },
          {
            value: 'INNER_TRANSFER',
            label: '转账'
          },
          {
            value: 'INNER_WITHDRAW',
            label: '提现'
          },
          {
            value: 'INNER_REFUND',
            label: '退款'
          },
          {
            value: 'INNER_AGENT_WITHDRAW',
            label: '结算到提现余额'
          }
        ],
        optionService: [], // 业务类型
        optionList: [
          { value: '7', label: '支付成功发生退款', ext: '', children: null, idx: 0 },

          { value: '9', label: '已拒绝', ext: '', children: null, idx: 0 },

          { value: '11', label: '待审核', ext: '', children: null, idx: 0 },

          { value: '12', label: '等待账期结束', ext: '', children: null, idx: 0 },

          { value: '13', label: '等待分账确认', ext: '', children: null, idx: 0 },

          { value: '14', label: '分账已确认确认', ext: '', children: null, idx: 0 },

          { value: '15', label: '等待另一笔订单完成', ext: '', children: null, idx: 0 },

          { value: '8', label: '已取消', ext: '', children: null, idx: 0 }
        ], // 交易状态
        loginMerchantCode: localStorage.getItem('loginMerchantCode'),
        columns: [
          { id: 'sourceOrderId', label: '订单号' },
          { id: 'merchantName', label: '门店名称' },
          { id: 'payTypeName', label: '交易类型' },
          { id: 'amount', label: '应到金额' },
          { id: 'paymentTime', label: '账期时间' },
          { id: 'businessTagName', label: '收入类型' },
          { id: 'sucAmount', label: '实到金额（元）' },
          { id: 'statusName', label: '状态' },
          // { id: '6', label: '退款金额（元）' },
          { id: 'createTime', label: '创建时间' },
          { id: 'remark', label: '备注' },
          { id: 'errMsg', label: '错误信息' }
        ],
        page_data: {
          pageSize: 10,
          page: 1,
          total: 0
        },
        payoutsDialogVisible: false,
        withdrawal: {},
        signContract: false,
        agreementUrl: ''
      };
    },

    created() {},
    computed: {
      ...mapGetters(['setpayUrl'])
    },
    mounted() {
      // 管理员(admin)还是门店(Shool)
      this.isAdmin = checkPermission(['admin']); // 超级管理员
      this.isSchool = checkPermission(['School']); // 门店
      if (this.isSchool) {
        // 删除一些字段
        this.removeNoPermissionField(['门店名称', '退款金额（元）']);
        this.getMerchantBalance(); // 获取门店金额信息 总收入/账期内余额/可提现金额/已分润总金额
      }
      if (!this.isAdmin && !this.isSchool) {
        // 只有 管理员 和 门店 可以访问
        this.$message.warning('请联系管理员获取权限');
        this.columns = [];
        return;
      }
      this.index();
      this.getRevenue();
      this.getbusinessTag(); // 获取业务类型/收入类型
      this.getorderStatus(); // 获取支付状态/状态
    },
    methods: {
      // 查看表头列表是否有该字段
      hasNoPermissionField(field) {
        let has = this.columns.some((i) => {
          if (i.prop == field || i.label == field) {
            return true;
          }
          return false;
        });
        return has;
      },
      // 移除掉没有权限的字段 或只保留 有权限的字段
      /**
       *移除掉没有权限的字段 或只保留 有权限的字段
       * @param array 需要移除或保留的字段
       * @param how true:保留 false:移除
       */
      removeNoPermissionField(array, how = false) {
        this.columns = this.columns.filter((i) => {
          for (let j = 0; j < array.length; j++) {
            if (i.prop == array[j] || i.label == array[j]) {
              return how;
            }
          }
          return !how;
        });
      },
      //查看相关金额
      async getMerchantBalance() {
        await getMerchantBalance().then((res) => {
          this.totalMoney = res.data.totalMoney / 100;
          this.paymentIn = res.data.paymentIn / 100;
          this.availableCashAmount = res.data.availableCashAmount / 100;
          this.waitAllocateFunds = res.data.waitAllocateFunds / 100;
        });
      },

      // 获取业务类型
      async getbusinessTag() {
        await businessTag().then((res) => {
          console.log(res);
          this.optionService = res.data;
          // console.log(this.optionService)
          // let list = Object.keys(res);
          // let lists = Object.values(res);

          // for(let key in list) {
          //   let value = list[key];
          //   let label = '';
          //   this.optionService.push({value,label})
          // }

          // for (let j = 0; j < lists.length; j++) {
          //   this.optionService[j].label = lists[j];
          // }
        });
      },

      // 获取支付状态
      async getorderStatus() {
        await orderStatus().then((res) => {
          this.optionList = res.data;
        });
      },
      //交易类型
      tradeChange(value) {
        console.log(`selected ${value}`);
        this.query.payTypeList = value;
      },

      //收入类型
      handleChange(value) {
        console.log(`selected ${value}`);

        this.query.businessTag = value;
      },
      stateChange(value) {
        console.log(`selected ${value}`);
        this.query.status = value;
      },
      onChange(value, dateString) {
        console.log('Selected Time: ', value);
        console.log('Formatted Selected Time: ', dateString);
        this.queryParams.startTime = dateString[0];
        this.queryParams.endTime = dateString[1];
        console.log(this.queryParams);
      },
      closePopup() {
        this.payoutsDialogVisible = false;
        this.$ref.withdrawal.resetFields();
      },
      ensurePayouts() {
        //调用提现接口
        // if( Number(this.withdrawal.amount) > this.availableCashAmount){

        // }else{

        // }

        if (!this.withdrawal.amount) {
          this.$message.error('提现金额不能为空');
          return false;
        }

        if (Number(this.withdrawal.amount) > this.availableCashAmount) {
          this.$message.error('可提现余额不足');
          return false;
        }
        if (Number(this.withdrawal.amount) < 0.01) {
          this.$message.error('提现金额不得低于1元');
          return false;
        }
        let data = {
          userCode: this.loginMerchantCode,
          withdrawAmount: Number(this.withdrawal.amount),
          remark: this.withdrawal.remark
        };
        cashWithdrawal(data).then((res) => {
          console.log(res);
          if (res.success) {
            this.$message.success('提现成功');
            this.index();
            this.getRevenue();
            this.closePopup();
          } else {
            this.closePopup();
            this.$message.success(res.message);
          }
        });
      },

      async Payouts() {
        let data = {
          userType: 'Merchant',
          userCode: this.loginMerchantCode
        };

        //校验用户是否签约，没有签约则出现弹窗签约，若已签约则直接提现
        await checkedSign(data).then((res) => {
          if (res.success) {
            //签约弹窗
            if (res.data.signStatus == 1) {
              this.payoutsDialogVisible = true;
            } else {
              this.agreementUrl = res.data.agreementUrl;
              this.signContract = true;
            }
          }
        });
      },
      async seeKnow() {
        let data = {
          userCode: this.loginMerchantCode
        };
        let res = await signTime(data);
        if (res.success) {
          this.signContract = true;
        }
      },
      closeSignContract() {
        this.signContract = false;
      },

      getRevenue() {
        merchantBalance().then((res) => {
          console.log(res);
        });
      },
      async index() {
        this.tableLoading = true;
        let param = { pageSize: this.page_data.pageSize, pageNum: this.page_data.page };
        Object.assign(param, this.query);
        let { data } = await merchantEarnings(param);
        const formatter = new Intl.NumberFormat('zh-CN', {
          style: 'decimal',
          minimumFractionDigits: 2,
          maximumFractionDigits: 2
        });

        this.list = data.data.map((item) => ({
          ...item,
          sucAmount: item.sucAmount / 100
        }));
        this.tableLoading = false;
        // 设置分页
        this.page_data.pageSize = data.size * 1;
        this.page_data.page = data.currentPage * 1;
        this.page_data.total = data.totalItems * 1;
      },
      dateChange(e) {
        console.log(e);
        if (!e) {
          this.query.startTime = '';
          this.query.endTime = '';
          return;
        }
        this.query.startTime = e[0];
        this.query.endTime = e[1];
      },

      gopay() {
        let req = 'token=' + this.token + '&back=' + window.location.href;
        //需要编码两遍，避免出现+号等
        var encode = Base64.encode(Base64.encode(req));
        window.open(this.setpayUrl + 'unifiedCollectList?' + encode, '_blank');
      },
      serach() {
        this.index();
      },
      reset() {
        this.query = {};
        this.date = [];
        this.index();
      },

      handleSizeChange(val) {
        this.query.pageSize = val;
        // this.index();
      },
      handleCurrentChange(val) {
        this.query.page = val;
        //   this.index();
      }
    }
  };
</script>

<style lang="scss" scoped>
  .container-card {
    padding: 15px 15px 15px 15px;
    margin-bottom: 15px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  }

  .red {
    color: red;
  }

  .green {
    color: green;
  }

  .blue {
    color: blue;
  }
  .signContent {
    height: 82vh;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-direction: column;
  }
  .signTitle {
    font-size: 20px;
    font-weight: bold;
  }
  @media (max-width: 767px) {
    .el-message-box {
      width: 80% !important;
    }
  }
  :deep(.el-col-5) {
    width: 20%;
  }
  :deep(.el-form-item--small.el-form-item) {
    display: flex;
    overflow: hidden;
  }
  :deep(.el-form-item--small .el-form-item__label) {
    line-height: 32px;
    flex-shrink: 0;
  }
  :deep(.el-form-item--small .el-form-item__content) {
    flex: 1;
  }
  :deep(.el-range-editor--small.el-input__inner) {
    width: auto;
  }
</style>
