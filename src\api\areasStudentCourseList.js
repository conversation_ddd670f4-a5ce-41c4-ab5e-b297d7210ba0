/**
 * 学员管理相关接口
 */
import request from '@/utils/request'

export default {
  profitPrice() {
    return request({
      url: '/znyy/bSysConfig/course/profit/price',
      method: 'GET'
    })
  },
  //购买课程包
  buyCoursePackage(data) {
    return request({
      url: '/znyy/course/product/buy',
      method: 'POST',
      data
    })
  },

  //获取课程信息
  getProductCateGory(id) {
    return request({
      url: '/znyy/course/product/category?id=' + id,
      method: 'GET'
    })
  },
  //根据目录获取课程产品
  getProductVo(categoryId) {
    return request({
      url: '/znyy/course/product/by/categoryId?categoryId=' + categoryId,
      method: 'GET'
    })
  },
  //根据课程获取明细
  getCourseProductSpecVo(productId) {
    return request({
      url: '/znyy/course/product/sku?productId=' + productId,
      method: 'GET'
    })
  },

  // 分页查询
  studentList(pageNum, pageSize, data, phone) {
    return request({
      url: '/znyy/areas/student/course/list/' + pageNum + '/' + pageSize,
      method: 'GET',
      params: data,
      headers: {
        'User-Phone': phone
      }
    })
  },

  courseList(pageNum, pageSize, data) {
    return request({
      url: '/znyy/areas/course/list/' + pageNum + '/' + pageSize,
      method: 'GET',
      params: data
    })
  },
  courseRecordList(pageNum, pageSize, data) {
    return request({
      url:
        '/znyy/areas/student/course/progress/list/' + pageNum + '/' + pageSize,
      method: 'GET',
      params: data
    })
  },
  updateStatus(id, status) {
    return request({
      url: '/znyy/areas/student/update/course/schedule/enable/' + id + '/' + status,
      method: 'PUT'
    })

  },
  regainArchive(id, status) {
    return request({
      url: '/znyy/archive/regain/student/word/' + id + '/' + status,
      method: 'PUT'
    })

  },
  getSchoolAccount() {
    return request({
      url: '/znyy/areas/student/get/account',
      method: 'GET'
    })
  },

  submitRecharge(data) {
    return request({
      url: '/znyy/areas/student/charge/course/save',
      method: 'POST',
      data
    })
  },
  //根据课程分类查子类
  checkNeedLineCollect(studentCode) {
    return request({
      url: '/znyy/areas/student/line/collect?studentCode=' + studentCode,
      method: 'GET'
    })
  },
  getBackRecharge(studentCode, refundDeliver) {
    return request({
      url: '/znyy/areas/student/tui/course/' + studentCode,
      method: 'GET',
      params: { 'refundDeliver': refundDeliver }
    })
  },
  getBackRechargePage(pageNum, pageSize, studentCode, refundDeliver) {
    return request({
      url: '/znyy/areas/student/tui/course/' + studentCode,
      method: 'GET',
      params: { 'refundDeliver': refundDeliver, 'pageNum': pageNum, 'pageSize': pageSize }
    })
  },
  //编辑学员信息
  editStudentInfo(data) {
    return request({
      url: '/znyy/areas/student/updateStudent',
      method: 'PUT',
      data
    })
  },
  //获取门店基本信息
  getMerchantInfo(name, value) {
    return request({
      url: `/znyy/areas/student/getMerchantInfo?${name}=${value}`,
      method: 'GET'
    })
  },
  //转移学员
  moveStudent(studentCode, merchantCode, toMerchant) {
    return request({
      url: `/znyy/areas/student/transfer/${studentCode}/${merchantCode}/${toMerchant}`,
      method: 'PUT'
    })
  },
  //根据学生code或者手机号获取学生基本信息
  getStudentInfo(value) {
    return request({
      url: `/znyy/areas/student/getStudentInfo?value=${value}`,
      method: 'GET'
    })
  },

  submitBackRecharge(data) {
    return request({
      url: '/znyy/areas/student/tui/course/save',
      method: 'POST',
      data
    })
  },
  cancel(orderId) {
    return request({
      url: '/znyy/order/cancel',
      method: 'put',
      params: { 'orderId': orderId }
    })
  },
  moveSelfToHave(studentCode, quantity) {
    return request({
      url:
        '/znyy/student/list/move/self/have?studentCode=' +
        studentCode +
        '&quantity=' +
        quantity,
      method: 'PUT'
    })
  },

  unarchive(studentCode) {
    return request({
      url: '/znyy/archive/unarchive?studentCode=' + studentCode,
      method: 'PUT'
    })
  },
  //根据课程分类查子类
  checkClassification(categoryCode) {
    return request({
      url: '/znyy/course/big/class/type/' + categoryCode,
      method: 'GET'
    })
  },

  submitOpenCourse(data) {
    return request({
      url: '/znyy/areas/course/batch/add/student/course',
      method: 'POST',
      data
    })
  },

  // 分页查询
  salesRecord(pageNum, pageSize, data) {
    return request({
      url: '/znyy/areas/student/sales/record/' + pageNum + '/' + pageSize,
      method: 'GET',
      params: data
    })
  },
  exportAreasStudentRecord(data) {
    return request({
      url: '/znyy/areas/student/studentCourseProgressList/to/excel',
      method: 'GET',
      params: data,
      responseType: 'blob'
    })
  },
  findStudent(phoneNum) {
    return request({
      url: 'znyy/grammmar/consume/hours/select/student?loginName=' + phoneNum,
      method: 'GET'
    })
  },

  payExpReward(data) {
    return request({
      url: '/deliver/web/experience/getPayExpReward',
      method: 'GET',
      params: data
    })
  },

  expRewardSave(data) {
    return request({
      url: '/znyy/areas/student/charge/expReward/save',
      method: 'POST',
      data
    })
  },
  getStudentMiniCoursePrice(studentCode, merchantCode) {
    return request({
      url: '/znyy/areas/student/course/renewal/price',
      method: 'GET',
      params: { 'studentCode': studentCode, 'merchantCode': merchantCode }
    })

  },
  //查询是否是是资转学员且180天内
  getStudentIsTransfer({ studentCode, merchantCode }) {
    return request({
      url: '/znyy/areas/student/query/zihzuan/student',
      method: 'GET',
      params: { 'studentCode': studentCode, 'merchantCode': merchantCode }
    })

  }
}
