<template>
  <div class="app-container">
    <el-form :model="dataQuery" ref="queryForm" :inline="true">
      <el-form-item label="类别：">
        <el-select v-model="dataQuery.categoryType" placeholder="全部" clearable>
          <el-option v-for="(item, index) in categoryList" :key="index" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="题型：">
        <el-select v-model="dataQuery.questionType" placeholder="全部" clearable>
          <el-option v-for="(item, index) in questionTypeFilter" :key="index" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="难度：">
        <el-select v-model="dataQuery.difficulty" placeholder="全部" clearable>
          <el-option v-for="(item, index) in difficultyList" :key="index" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" style="marginRight: 5px" @click="submitForm">查询</el-button>
        <el-popover placement="bottom" trigger="click">
          <el-button type="warning" size="mini" v-for="item in uploadEditUrlEnum" :key="item.questionType"
            @click="$router.push({ path: item.value })">{{ item.name }}</el-button>
          <el-button slot="reference" type="primary" class="link-left">添加</el-button>
        </el-popover>
        <!--        <el-button type="primary" class="link-left" @click="$router.push({ path: '/paper/imgRepeat' })">添加</el-button>-->
      </el-form-item>
    </el-form>
    <el-table class="common-table" v-loading="listLoading" :data="tableData" style="width: 100%; margin-bottom: 20px"
      row-key="id" border default-expand-all>
      <el-table-column prop="id" label="Id" />
      <el-table-column prop="type" label="所属类型" :formatter="typeFormatter"></el-table-column>
      <el-table-column prop="categoryType" label="题目类型" >
        <!-- categoryType -->
         <template  slot-scope="{ row }">
            <span>{{ row.categoryType == 1 ? '正式题' : '附加题'}}</span>
         </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="220px">
        <template slot-scope="{ row }">
          <el-button size="mini" @click="editQuestion(row)">编辑</el-button>
          <el-button size="mini" type="danger" @click="deleteQuestion(row)" class="link-left">删除</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="questionType" label="题型" :formatter="questionTypeFormatter"></el-table-column>
      <el-table-column prop="difficulty" label="难度" :formatter="difficultyFormatter"></el-table-column>
      <el-table-column prop="title" label="题目" show-overflow-tooltip />
      <el-table-column prop="createTime" label="创建时间" />

    </el-table>
    <!-- 分页 -->
    <el-col :span="24" style="margin-bottom: 20px">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>
  </div>
</template>

<script>
import questionApi from '@/api/paper/train/question'
import { mapGetters, mapState } from 'vuex';
import { pageParamNames } from "@/utils/constants";
import '/public/components/ueditor/themes/iframe.css'

export default {
  data() {
    return {
      questionTypeFilter: [],
      dataQuery: {
        id: null,
        type: 'UPLOAD_',
        questionType: null,
        difficulty: null,
      },
      listLoading: true,
      tableData: [],
      categoryList:[
        {label:'正式题',value:1},
        {label:'附加题',value:2}
      ],
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null,
      },
      total: 0,
    };
  },
  created() {
    this.getPageList();
    this.questionTypeFilter = this.trainQuestionType.filter(
      (data) => data.value.includes(this.dataQuery.type)
    )
  },
  methods: {
    close() {
      this.importOpen = false;
      this.importFrom = {
        file: null
      }
    },
    typeFormatter(row, column, cellValue, index) {
      return this.enumFormat(this.trainType, cellValue)
    },
    questionTypeFormatter(row, column, cellValue, index) {
      return this.enumFormat(this.trainQuestionType, cellValue)
    },
    difficultyFormatter(row, column, cellValue, index) {
      return this.enumFormat(this.difficultyList, cellValue)
    },
    submitForm() {
      this.getPageList()
    },
    getPageList() {
      this.listLoading = true
      this.dataQuery.pageNum = this.tablePage.currentPage;
      this.dataQuery.pageSize = this.tablePage.size;
      questionApi.list(this.dataQuery).then((res) => {
        this.tableData = res.data.data;
        this.listLoading = false;
        // 设置后台返回的分页参数
        pageParamNames.forEach((name) =>
          this.$set(this.tablePage, name, parseInt(res.data[name]))
        );
      });
    },
    editQuestion(row) {
      let url = this.trainUrlFormat(this.uploadEditUrlEnum, row.questionType);
      this.$router.push({ path: url, query: { id: row.id } });
    },
    deleteQuestion(row) {
      let _this = this
      this.$confirm('是否删除该题目？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        questionApi.delete(row.id).then(re => {
          if (re.success) {
            _this.$message.success(re.message)
            _this.getPageList()
          } else {
            _this.$message.error(re.message)
          }
        })
      })
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val;
      this.getPageList();
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val;
      this.getPageList();
    },
  },
  computed: {
    ...mapGetters('enumItem', ['enumFormat', 'trainUrlFormat']),
    ...mapState('enumItem', {
      trainType: state => state.train.trainType,
      difficultyList: state => state.train.difficultyList,
      trainQuestionType: state => state.train.trainQuestionType,
      uploadEditUrlEnum: state => state.train.uploadEditUrlEnum
    }),
  }
}
</script>

<style lang="less" scoped>
/deep/.el-dialog__header {
  border-bottom: 1px solid #dfdfdf;
}
</style>
<style>
.el-tooltip__popper {
  max-width: 800px;
}
</style>
