<!--特征找图形题-->
<template>
  <div class="app-container">
    <el-form :model="form" ref="form" label-width="100px" v-loading="formLoading" :rules="rules">
      <el-form-item label="所属类型:" prop="type">
        <el-select v-model="form.type" placeholder="全部" disabled>
          <el-option v-for="(item, index) in trainType" :key="index" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="题型:" prop="questionType">
        <el-select v-model="form.questionType" placeholder="全部" disabled>
          <el-option v-for="(item, index) in trainQuestionType" :key="index" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="年级：" prop="grade" v-if="form.courseType == 1">
        <el-select v-model="form.grade" placeholder="" clearable>
          <el-option v-for="item in gradeList" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="选择难度：" prop="difficulty">
        <el-select v-model="form.difficulty" placeholder="全部" clearable @change="handleChange">
          <el-option v-for="(item, index) in difficultyList" :key="index" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="选择维度：" prop="categoryId">
        <el-select v-model="form.categoryId" placeholder="全部" clearable>
          <el-option v-for="(item, index) in categoryList" :key="index" :label="item.name" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="选择类别：" prop="categoryType">
        <el-select v-model="form.categoryType" placeholder="全部" clearable>
          <el-option v-for="(item, index) in typeList" :key="index" :label="item.label" :value="item.id"></el-option>
        </el-select>
      </el-form-item> -->
      <el-row>
        <el-col :xs="24" :lg="16">
          <el-row>
            <el-col :span="18">
              <el-form-item label="题干：" prop="title">
                <el-input placeholder="请输入" v-model="form.title" />
              </el-form-item>

              <div v-for="(item, index) in mainFileList" :key="item.id">
                <el-form-item label="主图" required prop="customInfo">
                  <feature-graphics-upload @handleSuccess="handleSuccess"  @handleRemove="handleRemove"
                    :fullUrl="true"  :file-list="mainFileList[index]"  :limit="2" :showTip="false" :index="index"  />
                </el-form-item>
                <el-form-item label="展示图" required prop="answer">
                  <f-g-upload-with-select  @handleSuccess="handleShowPicSuccess"  @handleRemove="handleShowPicRemove"  :limit="8"
                    @handleCheck="handleCheck" :fullUrl="true" :file-list="showFileList[index]" :selectFileUrl="selectFileUrl" :index="index" />
                </el-form-item>
              </div>
              <el-form-item>
                <el-button type="primary" @click="addGroup(undefined)">添加分组</el-button>
                <el-button type="primary" @click="deleteGroup(undefined)">删除分组</el-button>
              </el-form-item>
              <el-form-item label="题数：" required>
                <el-input-number v-model="form.score" :precision="0" :step="1" :min="1" :max="999" @input.native="(value) => handleInput(value)"></el-input-number>
              </el-form-item>
            </el-col>
          </el-row>
        </el-col>
      </el-row>
      <div v-for="(question, qIndex) in form.childQuestions" :key="question.title" :class="['question-item', { 'question-item-gray': qIndex % 2 === 0 }]">
        <el-row type="flex" justify="flex-start" v-if="showChildQuestion">
          <el-col :xs="24" :lg="16">
            <el-row>
              <el-col :span="18">
                <el-form-item label="题干：" :prop="'childQuestions.' + qIndex + '.title'" :rules="rules.title">
                  <el-input placeholder="请输入" v-model="question.title" />
                </el-form-item>
                <div v-for="(item, index) in question.mainFileList" :key="item.id">
                  <el-form-item label="主图" required :prop="'childQuestions.' + qIndex + '.mainFileList.' + index">
                    <feature-graphics-upload  @handleSuccess="handleSuccessChild" @handleRemove="handleRemoveChild"
                      :fullUrl="true"  :file-list="question.mainFileList[index]"  :limit="2" :showTip="false" :index="index" />
                  </el-form-item>
                  <el-form-item label="展示图" required :prop="'childQuestions.' + qIndex + '.showFileList.' + index">
                    <f-g-upload-with-select  @handleSuccess="handleShowPicSuccessChild"  @handleRemove="handleShowPicRemoveChild" :limit="8"
                      @handleCheck="handleCheckChild" :fullUrl="true" :file-list="question.showFileList[index]"  :selectFileUrl="selectFileUrl" :index="index"  />
                  </el-form-item>
                </div>
                <el-form-item>
                  <el-button type="primary" @click="addGroup(qIndex)">添加分组</el-button>
                  <el-button type="primary" @click="deleteGroup(qIndex)">删除分组</el-button>
                </el-form-item>

                <el-form-item label="题数：" required>
                  <el-input-number
                    v-model="question.score"
                    :precision="0"
                    :step="1"
                    :min="1"
                    :max="999"
                    @input.native="(value) => handleInputChild(value, qIndex)"
                  ></el-input-number>
                </el-form-item>
              </el-col>
              <el-col :span="2" :offset="1">
                <el-button type="danger" icon="el-icon-close" @click="removeQuestion(qIndex)">删除</el-button>
              </el-col>
            </el-row>
          </el-col>
        </el-row>
      </div>
      <el-form-item>
        <el-button type="primary" @click="submitForm">提交</el-button>
        <el-button v-show="form.id === null" @click="resetForm">重置</el-button>
        <el-button type="success" v-if="!editId" @click="addQuestion" :disabled="!canAddQuestion || childQuestionsExceedLimit">添加小题</el-button>
      </el-form-item>
      <el-alert v-if="childQuestionsExceedLimit" title="题目数量不能超过10题" type="warning" show-icon></el-alert>
    </el-form>

    <el-dialog :visible.sync="imagePage.showDialog" width="70%">
      <el-form :model="imagePage.dataQuery" ref="queryForm" :inline="true">
        <el-form-item label="名称：">
          <el-input v-model="imagePage.dataQuery.name" clearable></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="search">查询</el-button>
        </el-form-item>
      </el-form>
      <el-table  v-loading="imagePage.listLoading"  :data="imagePage.tableData"  ref="multipleTable"  @current-change="handleSelectionChange"
        :row-class-name="tableRowClassName"  border  fit  highlight-current-row  style="width: 100%" >
        <el-table-column prop="questionType" label="题型" :formatter="questionTypeFormatter"></el-table-column>
        <el-table-column prop="difficulty" label="难度" :formatter="difficultyFormatter"></el-table-column>
        <el-table-column prop="name" label="名称"></el-table-column>
        <el-table-column prop="image" label="图片" align="center">
          <template slot-scope="scope">
            <el-image v-for="(item, index) in scope.row.fileList" :key="index" :src="item" :preview-src-list="[item]" class="img-lg" />
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <el-col :span="24" style="margin-bottom: 20px">
        <el-pagination
          :current-page="imagePage.tablePage.currentPage"
          :page-sizes="[10, 20, 30, 40, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="imagePage.tablePage.totalItems"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </el-col>
      <span slot="footer" class="dialog-footer">
        <el-button @click="imagePage.showDialog = false">取 消</el-button>
        <el-button type="primary" @click="confirmImageSelect">确定</el-button>
      </span>
    </el-dialog>
    <el-dialog :visible.sync="richEditor.dialogVisible" append-to-body :close-on-click-modal="false" style="width: 100%; height: 100%" :show-close="false" center>
      <Ueditor @ready="editorReady" />
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="editorConfirm">确 定</el-button>
        <el-button @click="richEditor.dialogVisible = false">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
  import questionApi from '@/api/paper/train/question';
  import categoryApi from '@/api/paper/train/category';
  import difficultyApi from '@/api/paper/train/difficulty';
  import { mapGetters, mapState } from 'vuex';
  import FeatureGraphicsUpload from '@/components/Upload/FeatureGraphicsUpload';
  import { pageParamNames } from '@/utils/constants';
  import imageApi from '@/api/paper/train/image';
  import Ueditor from '@/components/Ueditor';
  import FGUploadWithSelect from '@/components/Upload/FGUploadWithSelect.vue';
  import { Message, MessageBox } from 'element-ui';

  export default {
    components: {
      FeatureGraphicsUpload,
      FGUploadWithSelect,
      Ueditor
    },
    data() {
      var validateCustomInfo = (rule, value, callback) => {
        if (value.length == 0) {
          return callback(new Error('请上传主图！'));
        }
        callback();
      };
      var validateAnswer = (rule, value, callback) => {
        if (value.length == 0) {
          return callback(new Error('请上传展示图！'));
        }
        callback();
      };
      return {
        editId: '',
        richEditor: {
          dialogVisible: false,
          object: null,
          parameterName: '',
          instance: null
        },
        imagePage: {
          showDialog: false,
          listLoading: false,
          tableData: [],
          selectData: null,
          dataQuery: {
            questionType: null,
            difficulty: null
          },
          // 分页
          tablePage: {
            currentPage: 1,
            size: 10,
            totalPage: null,
            totalItems: null
          }
        },
        categoryList: [],
        fileList: [],
        typeList:[
          {label:'正式题',id:1},
          {label:'附加题',id:2}
        ],
        importFrom: {
          file: null
        },
        randomLength: undefined,
        form: {
          id: null,
          childQuestions: [],
          title: '',
          grade: '',
          score: undefined,
          categoryId: '',
          categoryType:'',
          difficulty: '',
          difficultyId: '',
          type: 'VISUAL_',
          questionType: 'VISUAL_FEATURE_GRAPHICS',
          customInfo: [],
          answer: [],
          isRandom: false,
          analysis: '',
          badge: 3,
          courseType: 0
        },
        answerRadio: [],
        mainFileList: [],
        showFileList: [],
        showChildQuestion: false,
        formLoading: false,
        rules: {
          type: [{ required: true, message: '请选择', trigger: 'blur' }],
          questionType: [{ required: true, message: '请选择', trigger: 'blur' }],
          grade: [{ required: true, message: '请选择', trigger: 'blur' }],
          title: [{ required: true, message: '请输入', trigger: 'change' }],
          difficulty: [{ required: true, message: '请选择', trigger: 'blur' }],
          categoryId: [{ required: true, message: '请选择', trigger: 'blur' }],
          categoryType: [{ required: true, message: '请选择', trigger: 'blur' }],
          customInfo: [{ required: true, validator: validateCustomInfo, trigger: 'change' }],
          answer: [{ required: true, validator: validateAnswer, trigger: 'change' }]
        },
        currentAnswerItem: null,
        selectFileUrl: '',
        gradeList: [
          { label: '1-3年级', value: 1 },
          { label: '4-6年级', value: 2 },
          { label: '初高中', value: 3 }
        ]
      };
    },
    created() {
      this.getCategoryList();
      this.editId = this.$route.query.id;
      this.form.courseType = this.$route.query.courseType ? 1 : 0;
      if (this.editId && parseInt(this.editId) !== 0) {
        this.formLoading = true;
        questionApi.detail(this.editId).then((re) => {
          this.form = re.data;
          this.form.grade = this.form.grade == 0 ? '' : this.form.grade;
          this.form.grade = this.form.grade == 0 ? '' : this.form.grade;
          for (let i = 0; i < this.form.customInfo.length; i++) {
            let value = JSON.parse(this.form.customInfo[i].value);
            this.form.customInfo[i].value = value;
            this.mainFileList.push(
              value.map((el) => {
                return { url: el };
              })
            );
          }

          for (let i = 0; i < this.form.answer.length; i++) {
            let value = JSON.parse(this.form.answer[i].value);
            this.form.answer[i].value = value;
            this.showFileList.push(
              value.map((el) => {
                return { url: el.value, selected: false };
              })
            );
          }
          for (let i = 0; i < this.form.answer.length; i++) {
            let answerIndex = this.form.answer[i].value.findIndex((a) => a.selected && a.selected == true);
            this.showFileList[i][answerIndex].selected = true;
            this.answerRadio.push(answerIndex);
          }
          this.handleChange();
          this.formLoading = false;
        });
      }
    },
    methods: {
      handleInput(value) {
        let inputValue = value.data?.toString().replace(/^(0+)|[^\d]+/g, '');
        this.form.score.push(inputValue);
        if (value.data === undefined || value.data === null || value.data === '') {
          return this.$message.error('请输入数字');
        }
      },
      handleInputChild(value, qIndex) {
        let inputValue = value.data?.toString().replace(/^(0+)|[^\d]+/g, '');
        this.form.childQuestions[qIndex].score.push(inputValue);
        if (value.data === undefined || value.data === null || value.data === '') {
          return this.$message.error('请输入数字');
        }
      },
      addGroup(qIndex) {
        if (qIndex !== undefined) {
          const formData = this.form.childQuestions[qIndex];
          if (formData.mainFileList.length >= 6) return this.$message.error('最多只能添加6组！');
          formData.mainFileList.push([]);
          formData.showFileList.push([]);
          formData.customInfo.push({ label: formData.customInfo.length.toString(), value: [] });
          formData.answer.push({ label: formData.answer.length.toString(), value: [] });
          formData.answerRadio.push(-1);
        } else {
          if (this.mainFileList.length >= 6) return this.$message.error('最多只能添加6组！');
          this.mainFileList.push([]);
          this.showFileList.push([]);
          this.form.customInfo.push({ label: this.form.customInfo.length.toString(), value: [] });
          this.form.answer.push({ label: this.form.answer.length.toString(), value: [] });
          this.answerRadio.push(-1);
        }
      },
      deleteGroup(qIndex) {
        if (qIndex !== undefined) {
          const formDataDel = this.form.childQuestions[qIndex];
          formDataDel.mainFileList.pop();
          formDataDel.showFileList.pop();
          formDataDel.customInfo.pop();
          formDataDel.answer.pop();
          formDataDel.answerRadio.pop();
        } else {
          this.mainFileList.pop();
          this.showFileList.pop();
          this.form.customInfo.pop();
          this.form.answer.pop();
          this.answerRadio.pop();
        }
      },
      // 选择展示图
      handleCheck(file, resultIndex, groupIndex) {
        let value = this.form.answer[groupIndex].value;

        // 如果 value 是字符串，反序列化为数组或对象
        if (typeof value === 'string') {
          try {
            value = JSON.parse(value);
          } catch (e) {
            return;
          }
        }
        value.forEach((el) => {
          el.selected = false;
        });
        value[resultIndex].selected = true;
        this.answerRadio[groupIndex] = resultIndex;
        this.$forceUpdate(); // 强制更新视图
      },

      handleCheckChild(file, resultIndex, groupIndex) {
        this.form.childQuestions.forEach((item) => {
          let value = item.answer[groupIndex].value;
          // 如果 value 是字符串，反序列化为数组或对象
          if (typeof value === 'string') {
            try {
              value = JSON.parse(value);
            } catch (e) {
              return;
            }
          }
          // 确保 value 是数组或对象
          if (Array.isArray(value)) {
            value.forEach((el) => {
              el.selected = false;
            });
            value[resultIndex].selected = true;
            item.answerRadio[groupIndex] = resultIndex;
          }
        });
        this.$forceUpdate(); // 强制更新视图
      },

      editorReady(instance) {
        this.richEditor.instance = instance;
        let currentContent = this.richEditor.object[this.richEditor.parameterName];
        this.richEditor.instance.setContent(currentContent);
        // 光标定位到Ueditor
        this.richEditor.instance.focus(true);
      },
      inputClick(object, parameterName) {
        this.richEditor.object = object;
        this.richEditor.parameterName = parameterName;
        this.richEditor.dialogVisible = true;
      },
      editorConfirm() {
        let content = this.richEditor.instance.getContent();
        this.richEditor.object[this.richEditor.parameterName] = content;
        this.richEditor.dialogVisible = false;
      },

      choiceImage() {
        if (!this.form.difficulty) {
          this.$message.error('请选择难度！');
          return false;
        }
        this.imagePage.showDialog = true;
        this.search();
      },
      search() {
        this.imagePage.dataQuery.questionType = this.form.questionType;
        this.imagePage.dataQuery.difficulty = this.form.difficulty;
        this.imagePage.listLoading = true;
        this.imagePage.dataQuery.pageNum = this.imagePage.tablePage.currentPage;
        this.imagePage.dataQuery.pageSize = this.imagePage.tablePage.size;
        imageApi.list(this.imagePage.dataQuery).then((res) => {
          this.imagePage.tableData = res.data.data;
          this.imagePage.listLoading = false;
          // 设置后台返回的分页参数
          pageParamNames.forEach((name) => this.$set(this.imagePage.tablePage, name, parseInt(res.data[name])));
        });
      },
      handleSelectionChange(val) {
        if (val) {
          this.imagePage.selectData = val;
        } else {
          this.imagePage.selectData = null;
        }
      },
      tableRowClassName({ row, rowIndex }) {
        if (row.id === this.form.paperId) {
          return 'current-row';
        }
        return '';
      },
      confirmImageSelect() {
        if (this.imagePage.selectData) {
          this.form.customInfo = [];
          let items = this.imagePage.selectData.fileList;
          let newLastPrefix = 'A';
          items.forEach((a) => {
            this.form.customInfo.push({ label: newLastPrefix, value: a });
            newLastPrefix = String.fromCharCode(newLastPrefix.charCodeAt() + 1);
          });
        }
        this.imagePage.showDialog = false;
      },
      questionTypeFormatter(row, column, cellValue, index) {
        return this.enumFormat(this.trainQuestionType, cellValue);
      },
      difficultyFormatter(row, column, cellValue, index) {
        return this.enumFormat(this.difficultyList, cellValue);
      },

      getCategoryList() {
        categoryApi.list().then((res) => {
          this.categoryList = res.data.data;
        });
      },
      serialization() {
        for (let i = 0; i < this.form.answer.length; i++) {
          let value = this.form.answer[i]['value'];
          // 如果 value 是字符串，反序列化为数组或对象
          if (typeof value === 'string') {
            try {
              value = JSON.parse(value);
            } catch (e) {
              continue; // 跳过当前循环
            }
          }
          value.forEach((el) => {
            el.selected = false;
          });
          value[this.answerRadio[i]]['selected'] = true;
          // 序列化 value
          this.form.answer[i]['value'] = JSON.stringify(value);
        }
        for (let i = 0; i < this.form.customInfo.length; i++) {
          this.form.customInfo[i].value = JSON.stringify(this.form.customInfo[i].value);
        }
        // 处理 childQuestions
        if (!this.editId) {
          this.form.childQuestions.forEach((item) => {
            for (let i = 0; i < item.answer.length; i++) {
              let value = item.answer[i]['value'];
              // 如果 value 是字符串，反序列化为数组或对象
              if (typeof value === 'string') {
                try {
                  value = JSON.parse(value);
                } catch (e) {
                  continue; // 跳过当前循环
                }
              }
              value.forEach((el) => {
                el.selected = false;
              });
              value[item.answerRadio[i]]['selected'] = true;
              // 序列化 value
              item.answer[i]['value'] = JSON.stringify(value);
            }
          });
          this.form.childQuestions.forEach((item) => {
            for (let i = 0; i < item.customInfo.length; i++) {
              item.customInfo[i].value = JSON.stringify(item.customInfo[i].value);
            }
          });
        }
      },

      checkSelect(arr) {
        if (!Array.isArray(arr)) {
          try {
            arr = JSON.parse(arr);
          } catch (e) {
            return false;
          }
        }
        for (let i = 0; i < arr.length; i++) {
          if (arr[i].selected == true) {
            return true; //有选中
          }
        }
        return false;
      },
      handleChange() {
        if (!this.form.difficulty) {
          this.difficultyInfo = null;
          return;
        }
        let query = {};
        query.type = this.form.type;
        query.questionType = this.form.questionType;
        query.difficulty = this.form.difficulty;
        difficultyApi
          .isSetting(query)
          .then((res) => {
            this.difficultyInfo = res.data;
          })
          .catch((e) => {
            this.difficultyInfo = null;
            this.fileList = [];
          });
      },
      submitForm() {
        this.$refs.form.validate((valid) => {
          if (valid) {
            // 确保 form 中的数据结构完整
            if (!Array.isArray(this.form.answer) || !Array.isArray(this.form.customInfo)) {
              this.$message.error('表单数据结构不完整，请检查！');
              return false;
            }

            // 检查每个 answer 是否有选中的展示图
            for (let i = 0; i < this.form.answer.length; i++) {
              let isSelected = this.checkSelect(this.form.answer[i]?.value);
              if (!isSelected) {
                this.$message.error('请选择正确的展示图！');
                return false;
              }
            }
            if (this.form.score == undefined || this.form.score == null || this.form.score == '') {
              this.$message.error('请输入题数');
              return false;
            }
            if (!this.editId) {
              // 检查 childQuestions 中的分组和展示图
              for (let i = 0; i < this.form.childQuestions.length; i++) {
                let childQuestion = this.form.childQuestions[i];
                if (!Array.isArray(childQuestion.answer) || !Array.isArray(childQuestion.customInfo)) {
                  this.$message.error(`子问题 ${i + 1} 数据结构不完整，请检查！`);
                  return false;
                }
                for (let j = 0; j < childQuestion.answer.length; j++) {
                  let isSelected = this.checkSelect(childQuestion.answer[j]?.value);
                  if (!isSelected) {
                    this.$message.error(`子问题 ${i + 1} 请选择正确的展示图！`);
                    return false;
                  }
                }
                if (childQuestion.customInfo.length === 0 || childQuestion.answer.length === 0) {
                  this.$message.error(`子问题 ${i + 1} 请添加分组！`);
                  return false;
                }
                if (childQuestion.score == undefined || childQuestion.score == null || childQuestion.score == '') {
                  this.$message.error(`子题目 ${i + 1}：请输入题数`);
                  return false;
                }
              }
            }
            // 检查是否有分组
            if (this.form.customInfo.length === 0 || this.form.answer.length === 0) {
              this.$message.error('请添加分组！');
              return false;
            }

            this.serialization(); //序列化this.form.answer
            this.formLoading = true;
            questionApi
              .saveOrUpdate(this.form)
              .then((re) => {
                if (re.success) {
                  this.$message.success(re.message);
                  this.formLoading = false;
                  this.$router.push('/train/trainAfterCourse');
                } else {
                  this.$message.error(re.message);
                  this.formLoading = false;
                }
              })
              .catch((e) => {
                this.formLoading = false;
              });
          } else {
            return false;
          }
        });
      },
      resetForm() {
        this.showChildQuestion = false;
        let lastId = this.form.id;
        let lastCourseType = this.form.id;
        this.$refs['form'].resetFields();
        this.form = {
          id: null,
          title: '',
          categoryId: '',
          difficulty: '',
          difficultyId: '',
          type: 'VISUAL_',
          questionType: 'VISUAL_FEATURE_GRAPHICS',
          customInfo: [],
          answer: [],
          childQuestions: [],
          isRandom: false,
          grade: ''
        };
        this.form.id = lastId;
        this.form.courseType = lastCourseType;
        this.mainFileList = [];
        this.showFileList = [];
      },
      // 分页
      handleSizeChange(val) {
        this.imagePage.tablePage.size = val;
        this.search();
      },
      handleCurrentChange(val) {
        this.imagePage.tablePage.currentPage = val;
        this.search();
      },
      // 主图上传成功回调
      handleSuccess(url, label) {
        let val = { label: label.toString(), value: [url] };
        let item = this.form.customInfo.find((el) => el.label == label.toString());
        if (item) {
          item.value.push(url);
        }
        // else {
        //   this.form.customInfo.push(val);
        // }
      },
      // 删除主图回调
      handleRemove(file, groupIndex) {
        var index = this.form.customInfo.findIndex((item) => {
          if (item.value === file.url) {
            return true;
          }
        });
        this.form.customInfo[groupIndex].value.splice(index, 1);
      },
      handleSuccessChild(url, label) {
        this.form.childQuestions.forEach((childQuestion) => {
          let val = { label: label.toString(), value: [url] };
          let item = childQuestion.customInfo.find((el) => el.label == label.toString());
          if (item) {
            item.value.push(url);
          }
          // else {
          //   childQuestion.customInfo.push(val);
          // }
        });
      },
      // 删除主图回调
      handleRemoveChild(file, groupIndex) {
        this.form.childQuestions.forEach((childQuestion) => {
          var index = childQuestion.customInfo.findIndex((item) => {
            if (item.value === file.url) {
              return true;
            }
          });
          childQuestion.customInfo[groupIndex].value.splice(index, 1);
        });
      },
      // 展示图上传成功回调
      handleShowPicSuccess(url, label) {
        let item = this.form.answer.find((el) => el.label == label);
        if (item) {
          let val = { label: item.value.length.toString(), value: url };
          item.value.push(val);
        }
        //  else {
        //   let val = { label: '0', value: url };
        //   let el = { label: label.toString(), value: [val] };
        //   this.form.answer.push(el);
        // }
      },
      // 删除展示图回调
      handleShowPicRemove(file, groupIndex) {
        var index = this.form.answer[groupIndex].value.findIndex((item) => {
          if (item.value === file.url) {
            return true;
          }
        });
        this.form.answer[groupIndex].value.splice(index, 1);
      },
      handleShowPicSuccessChild(url, label) {
        this.form.childQuestions.forEach((childQuestion) => {
          let item = childQuestion.answer.find((el) => el.label == label);
          if (item) {
            let val = { label: item.value.length.toString(), value: url };
            item.value.push(val);
          }
          // else {
          //   let val = { label: '0', value: url };
          //   let el = { label: label.toString(), value: [val] };
          //   childQuestion.answer.push(el);
          // }
        });
      },
      handleShowPicRemoveChild(file, groupIndex) {
        this.form.childQuestions.forEach((childQuestion) => {
          var index = childQuestion.answer[groupIndex].value.findIndex((item) => {
            if (item.value === file.url) {
              return true;
            }
          });
          childQuestion.answer[groupIndex].value.splice(index, 1);
        });
      },
      addQuestion() {
        if (this.childQuestionsExceedLimit) return;
        if (this.form.childQuestions.length > 0) {
          const lastQuestion = this.form.childQuestions[this.form.childQuestions.length - 1];
          if (!lastQuestion.title || !lastQuestion.mainFileList.length || !lastQuestion.showFileList.length || !lastQuestion.score) {
            this.$message.error('请填写完整当前题目的内容！');
            return;
          }
        } else {
          if (!this.form.title || !this.mainFileList.length || !this.showFileList.length || !this.form.score) {
            this.$message.error('请填写完整当前题目的内容！');
            return;
          }
        }

        this.showChildQuestion = true;
        this.form.childQuestions.push({
          title: '',
          mainFileList: [],
          showFileList: [],
          customInfo: [], // 新增
          answer: [], // 新增
          answerRadio: [], // 新增
          score: undefined
        });
      },
      removeQuestion(index) {
        this.$confirm('请确定是否需要删除已添加的题目，删除后不可恢复', '删除题目', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.form.childQuestions.splice(index, 1);
        });
      }
    },
    computed: {
      ...mapGetters('enumItem', ['enumFormat']),
      ...mapState('enumItem', {
        trainType: (state) => state.train.trainType,
        difficultyList: (state) => state.train.difficultyList,
        trainQuestionType: (state) => state.train.trainQuestionType
      }),
      canAddQuestion() {
        if (this.editId) return false;
        if (this.form.childQuestions.length > 0) {
          const lastQuestion = this.form.childQuestions[this.form.childQuestions.length - 1];
          if (!lastQuestion) return false;
          const { title, mainFileList, showFileList, score } = lastQuestion;
          // 检查主图列表和展示图列表是否都有内容
          const hasMainFiles = mainFileList.every((subList) => subList.length > 0);
          const hasShowFiles = showFileList.every((subList) => subList.length > 0);
          return title && hasMainFiles && hasShowFiles && score;
        } else {
          const { title, score } = this.form;
          // 检查主图列表和展示图列表是否都有内容
          const hasMainFiles = this.mainFileList.every((subList) => subList.length > 0);
          const hasShowFiles = this.showFileList.every((subList) => subList.length > 0);
          return title && hasMainFiles && hasShowFiles && score;
        }
      },
      childQuestionsExceedLimit() {
        if (this.editId) return false;
        return this.form.childQuestions.length >= 9;
      }
    }
  };
</script>

<style lang="less" scoped>
  .question-item-label {
    display: flex;
    margin-bottom: 12px;
  }

  .drawing-item {
    overflow: hidden;
    white-space: normal;
    word-break: break-all;
  }

  .img-lg {
    width: 60px;
    height: 60px;
  }
  .score-label {
    font-size: 14px;
    color: #606266;
    margin-left: 6px;
  }
  .question-item {
    padding: 10px;
    margin-bottom: 10px;
    border-radius: 4px;
  }

  .question-item-gray {
    background-color: #f9f9f9;
  }
</style>
