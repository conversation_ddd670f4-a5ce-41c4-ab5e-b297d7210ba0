{"name": "vue-element-admin", "version": "4.4.0", "description": "A magical vue admin. An out-of-box UI solution for enterprise applications. Newest development stack of vue. Lots of awesome features", "author": "Pan <<EMAIL>>", "scripts": {"dev": "vue-cli-service serve", "lint": "eslint --ext .js,.vue src", "build:prod": "cross-env NODE_OPTIONS= && vue-cli-service build", "build:stage": "vue-cli-service build --mode staging", "preview": "node build/index.js --preview", "svgo": "svgo -f src/icons/svg --config=src/icons/svgo.yml", "test:unit": "jest --clearCache && vue-cli-service test:unit", "test:ci": "npm run lint && npm run test:unit"}, "dependencies": {"@babel/plugin-transform-react-jsx": "^7.16.5", "@polyv/vod-upload-js-sdk": "^1.6.0", "@riophae/vue-treeselect": "^0.4.0", "@toast-ui/editor": "^3.1.3", "@wangeditor/editor": "^5.1.18", "@wangeditor/editor-for-vue": "^1.0.2", "@wangeditor/plugin-formula": "^1.0.11", "@wangeditor/plugin-mention": "^0.0.5", "ali-oss": "^6.10.0", "axios": "0.18.1", "babel-polyfill": "^6.26.0", "bpmn-js-token-simulation": "^0.10.0", "clipboard": "2.0.4", "codemirror": "5.45.0", "core-js": "^3.36.1", "cos-js-sdk-v5": "^0.1.3", "cos-wx-sdk-v5": "^0.7.11", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "driver.js": "0.9.5", "dropzone": "5.5.1", "echarts": "^5.3.2", "element-china-area-data": "^6.1.0", "element-ui": "2.13.2", "file-saver": "2.0.1", "fuse.js": "3.4.4", "highlight.js": "^10.5.0", "html2canvas": "^1.0.0-rc.7", "jquery": "^3.6.0", "js-base64": "^3.7.2", "js-cookie": "2.2.0", "js-md5": "^0.8.3", "jsmind": "^0.8.5", "jsonlint": "1.6.3", "jspdf": "^2.3.1", "jszip": "3.2.1", "katex": "^0.15.6", "layui-layer": "^1.0.9", "less": "^3.12.2", "less-loader": "^7.1.0", "mathjax": "^3.2.2", "md5": "^2.3.0", "music-metadata-browser": "^2.5.11", "normalize.css": "7.0.0", "nprogress": "0.2.0", "path-to-regexp": "2.4.0", "pinyin": "2.9.0", "print-js": "^1.6.0", "react": "^17.0.2", "react-dom": "^17.0.2", "rtc-detect": "0.0.4", "rtc-device-detector-react": "^1.0.4", "screenfull": "4.2.0", "script-loader": "0.7.2", "sortablejs": "1.8.4", "tim-wx-sdk": "^2.15.0", "timezone": "^1.0.23", "trtc-js-sdk": "^4.11.8", "vue": "^2.6.14", "vue-amap": "^0.5.10", "vue-count-to": "1.0.13", "vue-i18n": "7.3.2", "vue-jsmind": "^1.5.0", "vue-json-viewer": "^2.2.22", "vue-router": "3.0.2", "vue-social-captcha": "^0.1.1", "vue-splitpane": "1.0.4", "vue-video-player": "^5.0.1", "vuedraggable": "^2.24.3", "vuera": "^0.2.7", "vuex": "3.1.0", "vuex-persistedstate": "^4.1.0", "wangeditor": "^4.7.15", "xlsx": "0.14.1", "xml-js": "^1.6.11"}, "devDependencies": {"@vue/cli-plugin-babel": "4.4.4", "@vue/cli-plugin-eslint": "4.4.4", "@vue/cli-plugin-unit-jest": "4.4.4", "@vue/cli-service": "4.4.4", "@vue/test-utils": "1.0.0-beta.29", "autoprefixer": "9.5.1", "babel-eslint": "10.1.0", "babel-jest": "23.6.0", "babel-plugin-dynamic-import-node": "2.3.3", "bpmn-js": "^7.4.0", "bpmn-js-properties-panel": "^0.37.2", "chalk": "2.4.2", "chokidar": "2.1.5", "connect": "3.6.6", "cross-env": "^7.0.3", "eslint": "6.7.2", "eslint-plugin-vue": "6.2.2", "html-webpack-plugin": "3.2.0", "husky": "1.3.1", "lint-staged": "8.1.5", "mockjs": "1.0.1-beta3", "plop": "2.3.0", "runjs": "4.3.2", "sass": "1.26.2", "sass-loader": "8.0.2", "script-ext-html-webpack-plugin": "2.1.3", "serve-static": "1.13.2", "svg-sprite-loader": "4.1.3", "svgo": "1.2.0", "vue-loader": "^15.11.1", "vue-template-compiler": "2.6.14"}, "browserslist": ["> 1%", "last 2 versions"], "bugs": {"url": "https://github.com/PanJiaChen/vue-element-admin/issues"}, "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "keywords": ["vue", "admin", "dashboard", "element-ui", "boilerplate", "admin-template", "management-system"], "license": "MIT", "lint-staged": {"src/**/*.{js,vue}": ["eslint --fix", "git add"]}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "repository": {"type": "git", "url": "git+https://github.com/PanJiaChen/vue-element-admin.git"}}