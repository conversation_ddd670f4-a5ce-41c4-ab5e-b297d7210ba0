<template>
  <!-- 门店 -->
  <div class="app-container">
    <el-form :inline="true" class="container-card" label-width="100px" label-position="left">
      <el-col :span="8" :xs="24">
        <el-form-item label="门店名称" prop="merchantName">
          <el-input v-model="dataQuery.merchantName" placeholder="请输入门店名称" clearable />
        </el-form-item>
      </el-col>
      <el-col :span="8" :xs="24">
        <el-form-item label="负责人姓名" prop="realName">
          <el-input v-model="dataQuery.realName" placeholder="请输入负责人姓名" clearable />
        </el-form-item>
      </el-col>
      <el-col :span="8" :xs="24">
        <el-form-item label="登录账号" prop="name">
          <el-input v-model="dataQuery.name" placeholder="请输入登录账号" clearable />
        </el-form-item>
      </el-col>
      <el-col :span="8" :xs="24" v-if="isAdmin">
        <el-form-item label="所属俱乐部" prop="operationsName">
          <el-input v-model="dataQuery.operationsName" placeholder="请输入所属俱乐部" clearable />
        </el-form-item>
      </el-col>
      <el-col :span="8" :xs="24">
        <el-form-item label="课程推广大使" prop="refereeName">
          <el-input v-model="dataQuery.refereeName" placeholder="请输入课程推广大使" clearable />
        </el-form-item>
      </el-col>
      <el-col :span="8" :xs="24">
        <el-form-item label="业务开展地：" prop="selectCity">
          <el-cascader placeholder="请选择业务开展地" :options="regionData" v-model="selectCity" :props="{ value: 'label' }" clearable></el-cascader>
        </el-form-item>
      </el-col>
      <el-col :span="8" :xs="24">
        <el-form-item label="审核状态" prop="currentAuditStatus">
          <el-select v-model="dataQuery.currentAuditStatus" placeholder="请选择" clearable>
            <el-option label="审核中" value="1"></el-option>
            <el-option label="已通过" value="2"></el-option>
            <el-option label="已驳回" value="3"></el-option>
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="8" :xs="24" v-if="isAdmin">
        <el-form-item label="业务开展地是否一致" label-width="140px" prop="isBusinessArea">
          <el-select v-model="dataQuery.isBusinessArea" style="width: 210px" placeholder="请选择业务开展地是否一致" clearable>
            <el-option label="是" value="1"></el-option>
            <el-option label="否" value="0"></el-option>
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="isAdmin ? 4 : 8" :xs="24" style="margin-left: 100px">
        <el-button type="primary" @click="fetchData01">搜索</el-button>
        <el-button type="primary" @click="reset">重置</el-button>
      </el-col>
    </el-form>
    <el-table
      class="period-table"
      v-loading="tableLoading"
      :data="tableData"
      style="width: 100%; margin-bottom: 20px"
      row-key="id"
      :header-cell-style="{ background: '#eef1f6', color: '#606266' }"
      default-expand-all
      :tree-props="{ list: 'children', hasChildren: 'true' }"
    >
      <el-table-column prop="auditCode" label="编号" width="100" align="center"></el-table-column>
      <el-table-column prop="merchantName" label="门店名称" width="150" align="center"></el-table-column>
      <el-table-column prop="realName" label="负责人姓名" width="120" align="center"></el-table-column>
      <el-table-column prop="name" label="登录账号" align="center"></el-table-column>
      <el-table-column prop="operationsName" label="所属俱乐部" align="center" v-if="isAdmin"></el-table-column>
      <el-table-column prop="refereeName" label="课程推广大使" width="120" align="center"></el-table-column>
      <el-table-column prop="businessArea" label="业务开展地" align="center"></el-table-column>
      <el-table-column prop="isBusinessArea" label="业务开展地是否一致" width="150" align="center">
        <template slot-scope="scope">
          <span v-if="scope.row.isBusinessArea === 0">否</span>
          <span v-if="scope.row.isBusinessArea === 1">是</span>
        </template>
      </el-table-column>
      <el-table-column prop="currentAuditStatus" label="审核状态" align="center">
        <template slot-scope="scope">
          <span v-if="scope.row.currentAuditStatus === 1" style="color: #46a6ff">审核中</span>
          <span v-if="scope.row.currentAuditStatus === 2" style="color: green">已通过</span>
          <span v-if="scope.row.currentAuditStatus === 3" style="color: red">已驳回</span>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间" width="150" align="center"></el-table-column>
      <el-table-column label="操作" width="150px" align="center">
        <template slot-scope="scope">
          <el-button size="mini" type="primary" effect="dark" @click="handleView(scope.row.id, '审批', '门店')" v-if="!isAdmin && scope.row.currentAuditStatus == 1">
            审核
          </el-button>
          <el-button size="mini" type="success" effect="plain" @click="handleView(scope.row.id, '查看', '门店')" v-if="isAdmin || scope.row.currentAuditStatus != 1">
            查看
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <el-col :span="24" style="overflow-x: auto" :xs="24">
      <el-pagination
        :current-page="tablePage.currentPage"
        :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="tablePage.totalItems"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-col>

    <!-- 审核弹框 -->
    <ReviewInforDialog ref="ReviewInforDialog" @refresh-list="handleRefreshList" />
  </div>
</template>

<script>
  import registrationCodeApi from '@/api/registrationCodeApi/registrationCodeApi';
  import { pageParamNames } from '@/utils/constants';
  import { regionData } from 'element-china-area-data';
  import ReviewInforDialog from './dialogs/reviewInforDialog.vue';
  import checkPermission from '@/utils/permission';
  export default {
    name: 'merchantFlowList',
    components: {
      ReviewInforDialog
    },
    data() {
      return {
        regionData,
        tableLoading: false,
        // 分页
        tablePage: {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null
        },
        tableData: [],
        dataQuery: {},
        reviewDialogVisible: false,
        currentReviewData: {},
        isAdmin: false,
        selectCity: []
      };
    },
    created() {
      this.fetchData();
      this.isAdmin = checkPermission(['admin']);
    },
    methods: {
      fetchData01() {
        this.tablePage.currentPage = 1;
        const that = this;
        that.dataQuery.province = that.selectCity[0];
        if (that.selectCity && that.selectCity[1] == '市辖区') {
          that.dataQuery.city = that.selectCity[0];
        } else {
          that.dataQuery.city = that.selectCity[1];
        }
        that.dataQuery.area = that.selectCity[2];
        this.fetchData();
      },
      // 查询俱乐部审核列表
      fetchData() {
        this.tableLoading = true;
        const params = {
          pageNum: this.tablePage.currentPage,
          pageSize: this.tablePage.size,
          ...this.dataQuery
        };
        registrationCodeApi
          .StoreListData(params)
          .then((res) => {
            this.tableData = res.data.data;
            this.tableLoading = false;
            pageParamNames.forEach((name) => this.$set(this.tablePage, name, parseInt(res.data[name])));
          })
          .catch(() => {
            this.tableLoading = false;
          });
      },
      // 分页
      handleSizeChange(val) {
        this.tablePage.size = val;
        this.fetchData();
      },
      handleCurrentChange(val) {
        this.tablePage.currentPage = val;
        this.fetchData();
      },
      reset() {
        this.tablePage.currentPage = 1;
        this.dataQuery = {};
        this.selectCity = [];
        this.fetchData();
      },
      handleView(id, title, storeClubType) {
        this.$refs.ReviewInforDialog.open(id, title, storeClubType);
      },
      handleRefreshList() {
        this.fetchData01();
      }
    }
  };
</script>

<style scoped lang="less">
  .container-card {
    padding: 15px;
    margin-bottom: 15px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
    border-radius: 4px;
    overflow: hidden;
  }
</style>
