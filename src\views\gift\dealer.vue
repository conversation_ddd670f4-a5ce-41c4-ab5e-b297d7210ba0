<template>
  <div class="app-container">
    <!-- 新增按钮 -->
    <el-row class="container-card" style="margin-bottom: 30px;">
      <el-col :span="6">
        <el-col :span="8" class="marginbottom" style="line-height: 36px;">订单类型</el-col>
        <el-col :span="15">

          <el-select v-model="dataQuery.orderType" filterable value-key="value" placeholder="请选择">
            <el-option v-for="(item,index) in orderType" :key="index" :label="item.label" :value="item.value" />
          </el-select>

        </el-col>
      </el-col>
      <el-col :span="1" style="height: 1px" />
      <el-col :span="6">
        <el-col :span="8" class="marginbottom" style="line-height: 36px;">订单状态</el-col>
        <el-col :span="15">

          <el-select
            v-model="dataQuery.orderStatus"
            filterable
            value-key="value"
            placeholder="请选择"
          >
            <el-option
              v-for="(item,index) in orderStatus"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </el-select>

        </el-col>
      </el-col>

      <el-col :span="11" style="text-align: right;">
        <el-button type="primary" icon="el-icon-search" @click="fetchData()" size="mini">搜索</el-button>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24" style="text-align: right;margin-bottom: 30px;">
        <el-button type="success" icon="el-icon-plus" @click="clickAdd" size="mini">新增经销商</el-button>
      </el-col>
    </el-row>

    <el-table v-loading="tableLoading" :data="tableData" border style="width: 100%;margin-bottom: 30px;">
      <el-table-column prop="id" label="订单id" align="center" width="180"/>
      <el-table-column prop="orderTypeName" label="订单类型" align="center" :show-overflow-tooltip="true"/>
      <el-table-column label="操作" align="center" width="200">
        <template slot-scope="scope">
          <el-tooltip content="赠送码" placement="top">
            <el-button size="mini" type="warning" :disabled="scope.row.orderStatus === '已支付'||scope.row.orderStatus === '已完成'? false : true" @click="giftDetail(scope.row.id,scope.row.memberId)">赠送码详情</el-button>
          </el-tooltip>
          <el-tooltip content="详情" placement="top">
            <el-button size="mini" type="warning" @click="detail(scope.row.id)">详情</el-button>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column prop="memberId" label="用户code" align="center" :show-overflow-tooltip="true"/>
      <el-table-column prop="priceDeal" label="订单成交价(扣除手续费)" align="center" :show-overflow-tooltip="true"/>
      <el-table-column prop="priceConfirm" label="用户支付价格" align="center" :show-overflow-tooltip="true"/>
<!--      <el-table-column prop="orderStatus" label="订单状态" align="center" :show-overflow-tooltip="true"/>-->
      <el-table-column prop="orderStatus" label="订单状态"  align="center">
        <template slot-scope="scope">
          <span :style="scope.row.orderStatus === '超时取消'||scope.row.orderStatus === '已取消' ||scope.row.orderStatus === '已退款'||scope.row.orderStatus === '未知'? 'color:#f66c81' : (scope.row.orderStatus === '已完成' ? 'color:#67c23a' : (scope.row.orderStatus === '待支付' ? 'color:#e6a23c':'color:#409eff'))" >{{scope.row.orderStatus}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="purchaseCount" label="购买数量" align="center" :show-overflow-tooltip="true"/>
      <el-table-column prop="createTime" label="创建时间" align="center" :show-overflow-tooltip="true"/>
    </el-table>

    <!-- 分页 -->
    <el-col :span="20">
      <el-pagination
        :current-page="tablePage.currentPage"
        :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="tablePage.totalItems"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-col>

    <!--  新增经销商资格  -->
    <el-dialog title="新增经销商" :visible.sync="dialogDealerVisible">
      <el-form ref="addDealerData" :rules="rules" :model="addDealerData">
        <el-form-item label="用户code" :label-width="formLabelWidth" prop="memberId">
          <el-input v-model="addDealerData.memberId" />
        </el-form-item>
        <el-form-item label="活动" prop="activeId" :label-width="formLabelWidth">
          <el-select v-model="addDealerData.activeId" filterable value-key="value" placeholder="请选择">
            <el-option v-for="(item,index) in activeList" :key="index" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button type="warning" size="mini" @click="dialogDealerVisible=false">取消</el-button>
        <el-button type="success" size="mini" @click="addDealerFun('addDealerData')">新增</el-button>
      </div>

    </el-dialog>

    <el-dialog title="订单详情" :visible.sync="dialogDetailVisible">
      <el-form :model="orderDetail">
        <el-form-item label="赠品" :label-width="formLabelWidth" prop="freebievos">
          <el-input v-model="orderDetail.freebievos" readonly="readonly" autocomplete="off" />
        </el-form-item>
        <el-form-item label="活动" :label-width="formLabelWidth" prop="active">
          <el-input v-model="orderDetail.active" autocomplete="off" />
        </el-form-item>
        <el-form-item label="地址" :label-width="formLabelWidth" prop="receivingAddress">
          <el-input v-model="orderDetail.receivingAddress" autocomplete="off" />
        </el-form-item>
        <el-form-item label="号码" :label-width="formLabelWidth" prop="receivingPhone">
          <el-input v-model="orderDetail.receivingPhone" autocomplete="off" />
        </el-form-item>
        <el-form-item label="赠予人" :label-width="formLabelWidth" prop="receivingName">
          <el-input v-model="orderDetail.receivingName" autocomplete="off" />
        </el-form-item>
      </el-form>
    </el-dialog>

    <!-- 二维码列表 -->
    <el-dialog v-loading="tableLoading" title="咨询师码" :visible.sync="dialogCodeVisible" width="70%" :close-on-click-modal="false" @close="dialogCodeVisible=false">
      <el-table v-loading.body="tableLoading" :data="schoolCodeList" border fit highlight-current-row style="width: 100%">
        <el-table-column label="是否使用" prop="isUsed" align="center">
          <template slot-scope="scope">
            <el-button :type="scope.row.isUsed==0? 'primary':'default'" size="mini" plain disabled>{{ scope.row.isUsed==0? '未使用':'使用' }}</el-button>
          </template>
        </el-table-column>
        <el-table-column label="二维码" prop="qrcodePath" align="center">
          <template slot-scope="scope"><img width="40px" :src="scope.row.qrcodePath"></template>
        </el-table-column>
        <el-table-column prop="id" label="二维码编号" align="center" width="180" />
        <el-table-column prop="memberName" label="使用人" align="center" :show-overflow-tooltip="true"/>
        <el-table-column prop="memberMobile" label="联系方式" align="center" :show-overflow-tooltip="true"/>
        <el-table-column prop="useTime" label="使用时间" align="center" :show-overflow-tooltip="true"/>
      </el-table>
    </el-dialog>
    <el-dialog
      title="请选择咨询师地址"
      :visible.sync="showAddressSelect"
      width="30%"
      :show-close="false"
    >
      <span>
        <div class="block">
          <span class="grid-content">地址:</span>
          <el-cascader
            placeholder="试试搜索：庐阳区"
            :props="optionsAddressProps"
            :options="optionsAddress"
            filterable

            @change="addressCheck"
          />
        </div>

      </span>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="addressCreateLoad" @click="addressConfirm">确 定</el-button>
      </span>
    </el-dialog>
  </div>

</template>

<script>
import orderApi from '@/api/order'
import activeApi from '@/api/active'

import {
  pageParamNames
} from '@/utils/constants'
import experienceCodeApi from '@/api/experienceCode'

export default {
  components: {},
  data() {
    return {
      dialogDetailVisible: false,
      formLabelWidth: '120px',
      orderDetail: {
        id: '',
        freebievos: '',
        active: '',
        receivingAddress: '',
        receivingPhone: '',
        receivingName: ''

      },
      optionsAddressProps: {
        checkStrictly: true,
        value: 'name',
        label: 'name',
        children: 'children'
      },
      address: '',
      addressArray: [],
      optionsAddress: [],
      showAddressSelect: false,
      addressCreateLoad: false,
      orderType: [], // 活动类型
      orderStatus: [],
      tableLoading: false,
      dataQuery: {
        orderType: '',
        orderSource:'GIVE_DIS'
      },
      options: [{
        value: 'MACHINE',
        label: '学习机'
      }, {
        value: 'COLLAGE',
        label: '拼团课'
      }, {
        value: 'EXPERIENCE',
        label: '体验课'
      }, {
        value: '',
        label: '全部'
      }],
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      tableData: [],

      addDealerData: { // 新增经销商
        memberId: '',
        activeId: ''
      },
      dialogDealerVisible: false,
      activeList: [], // 活动列表

      rules: {
        memberId: [{
          required: true,
          message: '必填',
          trigger: 'blur'
        }],
        activeId: [{
          required: true,
          message: '必填',
          trigger: 'change'
        }]
      },

      dialogCodeVisible: false, // 校区二维码列表弹窗是否显示
      schoolCodeList: [],
      logisticsType: [] // 物流状态

    }
  },
  created() {
    this.fetchData()
    this.queryOrderType()
    this.queryOrderStatus()
    this.queryAllRegionEx()
  },
  methods: {
    queryAllRegionEx() {
      experienceCodeApi.queryAllRegion().then(res => {
        this.optionsAddress = res.data
      })
    },
    addressCheck(value) {
      this.address = value
    },
    addressConfirm() {
      if (this.address.length < 3 || this.address[2] === undefined || this.address[2] == null) {
        this.$message.error('请选择地址')
      } else {
        this.addressCreateLoad = true
        this.addDealerData.province = this.address[0]
        this.addDealerData.city = this.address[1]
        this.addDealerData.area = this.address[2]
        orderApi.addMachineOrder(this.addDealerData).then(res => {
          this.$nextTick(() => this.fetchData())
          this.$message.success('赠送成功')
          this.addressCreateLoad = false
          this.showAddressSelect = false
          this.dialogCodeVisible = false
        }).catch(res => {
          this.addressCreateLoad = false
        })
      }
    },
    // 获取所有学习机活动
    getActiveList() {
      activeApi.queryActiveType('MACHINE').then(res => {
        this.activeList = res.data
      })
    },

    // 获取活动返回类型
    queryOrderType() {
      orderApi.queryOrderType().then(res => {
        this.orderType = res.data
      })
    },

    queryOrderStatus() {
      orderApi.queryOrderStatus().then(res => {
        this.orderStatus = res.data
      })
    },

    detail(id) {
      this.dialogDetailVisible = true
      orderApi.orderDetail(id).then(res => {
        res.data.orderDetail.orderExtend
        var ex = JSON.parse(res.data.orderDetail.orderExtend)
        var freebieVo = ''
        for (var i = 0; i < ex.freebieVos.length; i++) {
          freebieVo += '名称:' + ex.freebieVos[i].name + '  数量:' + ex.freebieVos[i].num + '  '
        }
        this.orderDetail.freebievos = freebieVo
        this.orderDetail.active = ex.activeName
        this.orderDetail.receivingAddress = res.data.receivingAddress
        this.orderDetail.receivingName = res.data.receivingName
        this.orderDetail.receivingPhone = res.data.receivingPhone
      })
    },

    // 点击新增经销商
    clickAdd() {
      this.addDealerData = {
        memberId: '',
        activeId: ''
      }
      this.dialogDealerVisible = true
      this.getActiveList()
    },
    // 确认新增经销商
    addDealerFun(ele) {
      console.log(this.addDealerData)
      const that = this
      experienceCodeApi.checkMemberAddress('member', that.addDealerData.memberId).then(res => {
        if (!res.data) {
          that.showAddressSelect = true
        } else {
          that.addressCreateLoad = true
          that.$refs[ele].validate(valid => { // 表单验证
            if (valid) {
              const loading = this.$loading({
                lock: true,
                text: '新增经销商资格提交',
                spinner: 'el-icon-loading',
                background: 'rgba(0, 0, 0, 0.7)'
              })
              orderApi.addMachineOrder(that.addDealerData).then(res => {
                that.dialogDealerVisible = false
                loading.close()
                that.$nextTick(() => that.fetchData())
                that.$message.success('新增经销商资格成功')
                that.addressCreateLoad = false
                that.showAddressSelect = false
              }).catch(res => {
                loading.close()
              })
            }
          })
        }
      })
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val
      this.fetchData()
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val
      this.fetchData()
    },

    // 查询
    fetchData() {
      const that = this
      that.tableLoading = true
      console.log(that.dataQuery)
      orderApi.queryOrderPage(that.tablePage.currentPage, that.tablePage.size, that.dataQuery).then(res => {
        for (let i = 0; i < res.data.data.length; i++) {
          that.orderStatus.forEach(item => {
            if (res.data.data[i].orderStatus === item.value) {
              res.data.data[i].orderStatus = item.label
            }
          })

          that.logisticsType.forEach(item => {
            if (res.data.data[i].logisticsStatus === item.value) {
              res.data.data[i].logisticsStatus = item.label
            }
          })
        }

        that.tableData = res.data.data
        that.tableLoading = false
        // 设置后台返回的分页参数
        pageParamNames.forEach(name => that.$set(that.tablePage, name, parseInt(res.data[name])))
      })
    },
    //  赠送码详情点击
    giftDetail(id, memberId) {
      const that = this
      that.tableLoading = true
      orderApi.queryQrcode(id, memberId).then(res => {
        that.dialogCodeVisible = true
        that.tableLoading = false
        that.schoolCodeList = res.data
      })
    }

  }
}
</script>
<style>
  .container-card {
    padding: 15px 15px 15px 15px;
    margin-bottom: 15px ;
    box-shadow: 0 2px 4px rgba(0, 0, 0, .12), 0 0 6px rgba(0, 0, 0, .04)
  }
</style>
