<template>
  <div class="app-container">
    <!--    /merchantManagement/agentList-->
    <el-form :inline="true" class="container-card" label-width="110px" label-position="left">
      <el-form-item label="服务商编号：">
        <el-input v-model="dataQuery.merchantCode" placeholder="请输入服务商编号" clearable />
      </el-form-item>
      <el-form-item label="登录账号：">
        <el-input v-model="dataQuery.name" placeholder="请输入登录账号" clearable />
      </el-form-item>
      <el-form-item label="服务商名称：">
        <el-input v-model="dataQuery.merchantName" placeholder="请输入服务商名称" clearable />
      </el-form-item>
      <el-form-item label="负责人：">
        <el-input v-model="dataQuery.realName" placeholder="请输入负责人" clearable />
      </el-form-item>
      <el-form-item label="添加时间：">
        <el-date-picker style="width: 100%;" v-model="value1" value-format="yyyy-MM-dd HH:mm:ss" type="daterange"
          align="right" unlink-panels range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" clearable />
      </el-form-item>
      <el-form-item label="推荐人编号：">
        <el-input v-model="dataQuery.marketPartner" placeholder="请输入推荐人编号" clearable />
      </el-form-item>
      <el-form-item label="状态：">
        <el-select v-model="dataQuery.isEnable" value-key="value" placeholder="请选择" style="width: 200px" clearable>
          <el-option v-for="(item, index) in [
            { label: '开通', value: 1 },
            { label: '暂停', value: 0 },
            { label: '终止', value: -3 },
            { label: '系统关闭', value: -1 },
          ]" :key="index" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="是否完款：">
        <el-select v-model="dataQuery.paymentIsComplete" value-key="value" placeholder="请选择" clearable>
          <el-option v-for="(item, index) in [
            { value: '1', label: '完款' },
            { value: '0', label: '未完款' }
          ]" :key="index" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="所在地区：">
        <el-input v-model="dataQuery.address" placeholder="请输入所在地区" clearable />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="fetchData01()">搜索</el-button>
      </el-form-item>
    </el-form>

    <el-col :span="24" style="margin-bottom: 30px">
      <el-button type="success" icon="el-icon-plus" size="mini" v-if="checkPermission(['b:merchant:agentList:add'])"
        @click="clickAdd">添加</el-button>
      <el-button type="warning" icon="el-icon-document-copy" size="mini"
        v-if="checkPermission(['b:merchant:agentList:export'])" v-loading="exportLoading" @click="exportList()">导出
      </el-button>
      <el-button type="warning" icon="el-icon-document-copy" size="mini" v-loading="exportLoading1"
        v-if="checkPermission(['b:merchant:agentList:exportList'])" @click="exportList1()">导出市级服务商运行数据
      </el-button>
    </el-col>
    <!-- 渲染表格 -->
    <el-table v-loading="tableLoading" class="common-table" :data="tableData" hstyle="width: 100%; margin-bottom: 20px"
      row-key="id" border default-expand-all :tree-props="{ list: 'children', hasChildren: 'true' }">
      <el-table-column prop="merchantCode" label="编号" align="center" width="100px" />
      <el-table-column prop="name" label="登陆账号" align="center" width="120px" />
      <el-table-column label="操作" align="center" width="320px">
        <template slot-scope="scope">
          <el-button type="success" v-if="checkPermission(['b:merchant:agentList:edit'])" icon="el-icon-edit-outline"
            size="mini" @click="update(scope.row.id, false)">编辑
          </el-button>
          <el-button type="success"
            v-if="checkPermission(['b:merchant:agentList:edit']) && scope.row.isOldData == 0 && scope.row.flowIsEnd == 1 && scope.row.flowEndStatus == 0"
            icon="el-icon-edit-outline" size="mini" @click="update(scope.row.id, true)">重新提交
          </el-button>
          <!--          <el-button
                      type="primary"
                      icon="el-icon-edit-outline"
                      size="mini"
                      v-if="scope.row.isCheck == 0 && roleTag==='admin'"
                      @click="openExamine(scope.row.id)"
                    >审核</el-button>
                    <el-button
                      type="primary"
                      icon="el-icon-edit-outline"
                      size="mini"
                      v-if="scope.row.isCheck == 4 && roleTag==='Company'"
                      @click="openExamine(scope.row.id)"
                    >审核</el-button>-->
          <el-button type="warning" size="mini" icon="el-icon-switch-button"
            v-if="(scope.row.isEnable === 0 && scope.row.isCheck === 1) || (scope.row.isEnable === 0 && scope.row.flowEndStatus === 1) || scope.row.isEnable === -3"
            @click="agentStatus(scope.row.id, 1)">开通
          </el-button>
          <el-button type="danger" size="mini" icon="el-icon-video-pause"
            v-if="(scope.row.isEnable === 1 && scope.row.isCheck === 1) || (scope.row.isEnable === 1 && scope.row.flowEndStatus === 1)"
            @click="agentStatus(scope.row.id, 0)">暂停
          </el-button>
          <el-button type="danger" size="mini" icon="el-icon-video-pause"
            v-if="checkPermission(['b:merchant:agentList:termination']) && ((scope.row.isEnable === 1 && scope.row.isCheck === 1) || (scope.row.isEnable === 1 && scope.row.flowEndStatus === 1))"
            @click="agentStatus(scope.row.id, -3)">终止
          </el-button>
          <el-button type="danger" size="mini" icon="el-icon-circle-check"
            v-if="checkPermission(['b:risk:user:clear']) && (scope.row.isEnable === -1 || scope.row.isEnable === -2)"
            @click="agentStatus(scope.row.id, 1)">解封
          </el-button>
          <el-button type="success" size="mini" icon="el-icon-video-pause" v-if="scope.row.paymentIsComplete === 0"
            @click="agentPaymentIsComplete(scope.row.id, scope.row.isEnable)">完款
          </el-button>
          <el-button type="warning" size="mini" icon="el-icon-link"
            v-if="false && (checkPermission(['b:merchant:agentList:assignDelivery'])) && ((scope.row.isOldData === 1 && scope.row.isCheck === 1) || (scope.row.isOldData === 0 && scope.row.flowEndStatus === 1))"
            @click="openAssignDelivery(scope.row.merchantCode)">指定交付中心
          </el-button>
          <el-button type="warning" size="mini" icon="el-icon-link"
            v-if="false && (checkPermission(['b:merchant:agentList:liftDelivery'])) && ((scope.row.isOldData === 1 && scope.row.isCheck === 1) || (scope.row.isOldData === 0 && scope.row.flowEndStatus === 1))"
            @click="liftDelivery(scope.row.merchantCode)">解除交付中心
          </el-button>
        </template>
      </el-table-column>
      <el-table-column prop="merchantName" label="名称" align="center" min-width="120px" />
      <el-table-column prop="realName" label="负责人" align="center" width="100px" />
      <el-table-column prop="marketPartner" label="推荐人编号" align="center" width="100px"></el-table-column>
      <el-table-column prop="refereeCode" label="上级编号" align="center" width="100px"></el-table-column>
      <el-table-column prop="sumChargeMoney" label="累计充值金额（元）" align="center" width="110px">
        <template slot-scope="scope">
          <span v-if="scope.row.sumChargeMoney === null ||
            scope.row.sumChargeMoney === ''
            ">0.00</span>
          <span v-else>{{ scope.row.sumChargeMoney }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="sumRebateMoney" label="累计返点金额（元）" align="center" width="110px">
        <template slot-scope="scope">
          <span v-if="scope.row.sumRebateMoney === null ||
            scope.row.sumRebateMoney === ''
            ">0.00</span>
          <span v-else>{{ scope.row.sumRebateMoney }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="sumGiveMoney" label="累计赠送金额（元）" align="center" width="110px">
        <template slot-scope="scope">
          <span v-if="scope.row.sumGiveMoney === null || scope.row.sumGiveMoney === ''
            ">0.00</span>
          <span v-else>{{ scope.row.sumGiveMoney }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="accountMoney" label="账户余额（元）" align="center" width="110px">
        <template slot-scope="scope">
          <span v-if="scope.row.accountMoney === null || scope.row.accountMoney === ''
            ">0.00</span>
          <span v-else>{{ scope.row.accountMoney }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="address" label="所在地区" align="center" />
      <el-table-column prop="subDealerCount" label="下级托管中心" align="center" />
      <el-table-column prop="subSchoolCount" label="下级门店" align="center" />
      <el-table-column prop="paymentIsComplete" label="是否完款" align="center">
        <template slot-scope="scope">
          <span v-if="scope.row.paymentIsComplete === 1" class="green">完款</span>
          <span v-else class="red">未完款</span>
        </template>
      </el-table-column>
      <el-table-column prop="channelManagerName" label="渠道管理员" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.channelManagerName ? scope.row.channelManagerName : '' }}</span>
          <el-link v-if="scope.row.channelManagerName" type="primary"
            @click="showChannelManagerDetail(scope.row)">详情</el-link>
        </template>
      </el-table-column>
      <el-table-column prop="flowStatus" label="流程状态" align="center"></el-table-column>
      <el-table-column label="审核状态" align="center">
        <template slot-scope="scope">
          <span v-if="scope.row.isOldData === 1 && scope.row.isCheck === 2" class="red">未通过</span>
          <span v-if="scope.row.isOldData === 1 && scope.row.isCheck === 4" class="red">等待分公司审核</span>
          <span v-if="scope.row.isOldData === 1 && scope.row.isCheck === -4" class="red">未通过总公司审核</span>
          <span v-if="scope.row.isOldData === 1 && scope.row.isCheck === 1" class="green">通过</span>
          <span v-if="scope.row.isOldData === 1 && scope.row.isCheck === 0" class="red">等待总部审核</span>
          <span class="green" v-if="scope.row.isOldData === 0 && scope.row.flowIsEnd === 1">审核流程结束</span>
          <span class="red" v-if="scope.row.isOldData === 0 && scope.row.flowIsEnd === 0">审核中</span>
          <el-button
            v-if="(scope.row.isCheck == 2 || scope.row.isCheck == -3 || scope.row.isCheck == -4) && scope.row.chenckReason != ''"
            type="text" @click="open(scope.row.chenckReason)" showCancelButton="false">查看详情
          </el-button>
        </template>
      </el-table-column>
      <el-table-column prop="isEnable" label="账户状态" align="center">
        <template slot-scope="scope">
          <span v-if="scope.row.isEnable === -1" class="red">系统关闭</span>
          <span v-else-if="scope.row.isEnable === -3" class="red">终止</span>
          <span v-else-if="scope.row.isEnable === 1" class="green">开通</span>
          <span v-else class="red">暂停</span>
        </template>
      </el-table-column>
      <el-table-column prop="regTime" label="添加时间" align="center" />

    </el-table>


    <!--弹出窗口：指派交付中心-->
    <el-dialog :title="textMap[dialogStatu]" :visible.sync="dialogFormDelivery" width="40%" :close-on-click-modal='false'
      @close="dialogFormDelivery = false">
      <template>
        <el-input placeholder="请输入编号" style="width: 40%" v-model="searchMerchantCode" clearable>
        </el-input>
        <el-button type="primary" mini style="margin-left: 10px" title="搜索" @click="searchChannel()">搜索</el-button>

        <el-table :data="tableDataDelivery" v-loading="tableLoading2" highlight-current-row ref="singleTable" height="550"
          border style="width: 100%">
          <el-table-column type="index" width="50"></el-table-column>
          <el-table-column sortable prop="merchantCode" label="编号" width="180"></el-table-column>
          <el-table-column sortable prop="merchantName" label="名称" width="180"></el-table-column>
          <el-table-column label="操作">
            <template slot-scope="scope">
              <el-button type="primary" mini title="指派至该交付中心"
                @click="assignDelivery(scope.row.merchantCode)">指派</el-button>
            </template>
          </el-table-column>
          <el-table-column sortable prop="regTime" label="添加时间">
          </el-table-column>】
        </el-table>
      </template>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormDelivery = false">取消</el-button>
      </div>
    </el-dialog>

    <!--渠道管理员信息-->
    <el-dialog title="渠道管理员信息" :visible.sync="dialogVisibleForChannelManager" width="30%">
      <el-form ref="temp" label-position="left" label-width="120px">
        <el-form-item label="名称: ">
          <el-input disabled v-model="channelManager.realName" placeholder="请输入内容"></el-input>
        </el-form-item>
        <el-form-item label="编号: ">
          <el-input disabled v-model="channelManager.channelManagerCode" placeholder="请输入内容"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisibleForChannelManager = false">关 闭</el-button>
      </span>
    </el-dialog>

    <!-- 审核页面 -->
    <el-dialog title="审核" :visible.sync="showExamine" width="70%" :close-on-click-modal="false" @close="closeExamine">
      <el-form :ref="examine" :rules="rules" :model="examine" label-position="left" label-width="100px"
        style="width: 100%">
        <el-form-item label="是否通过：" prop="isCheck">
          <template>
            <el-radio v-model="isCheck" label="1" @change="change(isCheck)">通过</el-radio>
            <el-radio v-model="isCheck" label="0" @change="change(isCheck)">不通过</el-radio>
          </template>
        </el-form-item>
        <el-form-item label="审核意见">
          <el-col :xs="24" :span="18">
            <el-input type="textarea" resize="none" :rows="4" v-model="examine.checkReason" />
          </el-col>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="
          toExamine(examine.id, examine.checkReason, examine.isCheck, examine)
          ">确定
        </el-button>
        <el-button size="mini" @click="closeExamine">关闭</el-button>
      </div>
    </el-dialog>
    <!-- 审核页面 -->
    <el-dialog title="审核" :visible.sync="showExamineCompany" width="70%" :close-on-click-modal="false"
      @close="closeExamine">
      <el-form :ref="examineCompany" :rules="ruleList" :model="examineCompany" label-position="left" label-width="100px"
        style="width: 100%">
        <el-form-item label="是否通过：" prop="isCheckCompany">
          <template>
            <el-radio v-model="isCheckCompany" label="4" @change="change(isCheckCompany)">通过</el-radio>
            <el-radio v-model="isCheckCompany" label="-4" @change="change(isCheckCompany)">不通过</el-radio>
          </template>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="
          toExamineComapny(examineCompany.id, examineCompany.isCheckCompany, examineCompany)
          ">确定
        </el-button>
        <el-button size="mini" @click="closeExamine">关闭</el-button>
      </div>
    </el-dialog>
    <!-- 分页 -->
    <el-col :span="20">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>
  </div>
</template>

<script>
import checkPermission from "@/utils/permission";
import agentApi from "@/api/agentList";
import { pageParamNames } from "@/utils/constants";
import { ossPrClient } from "@/api/alibaba";
import ls from "@/api/sessionStorage";
import authenticationApi from "@/api/authentication";
import deliveryCenterApi from "@/api/delivery/deliveryCenter";

export default {
  // name: "agentList",
  data() {
    return {
      channelManager: {},
      dialogVisibleForChannelManager: false,
      searchMerchantCode: "",
      branchOfficeMerchantCode: "",
      dialogFormDelivery: false,
      textMap: {
        assign: '指定交付中心',
      },
      tableDataDelivery: [],
      tableLoading2: false,
      dialogStatu: 'assign',
      tableLoading: false,
      roleTag: "",
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      isCheckCompany: "",
      uploadLoading: true,
      addOrUpdate: true, // true为添加，false为修改
      showExamineCompany: false,
      dataQuery: {
        merchantCode: "",
        name: "",
        realName: "",
        txtStartTime: "",
        txtEndRegTime: "",
        marketPartner: "",
        address: "",
        isEnable: ""
      },
      value1: "",
      dialogVisible: false,
      tableData: [],
      updateData: {}, // 更新的数据
      addData: {}, // 新增的数据
      rules: {
        isCheck: [
          {
            required: true,
            message: "请选择是否通过",
            trigger: "change"
          }
        ]
      },
      ruleList: {
        isCheckCompany: [
          {
            required: true,
            message: "请选择是否通过",
            trigger: "change"
          }
        ]
      },
      content: "",
      isUploadSuccess: true, // 是否上传成功
      showLoginAccount: false,
      showExamine: false, //审核页面
      examineCompany: {
        isCheck: "",
        id: ""
      },
      //审核字段
      examine: {
        isCheck: "",
        checkReason: "",
        id: ""
      },
      isCheck: "",
      exportLoading: false, //导出加载
      exportLoading1: false
    };
  },
  created() {
    ossPrClient();
    this.fetchData01();
  },
  mounted() {
    authenticationApi.checkAccountBalance().then(res => {
      this.roleTag = res.data.data.roleTag;
    });
  },
  methods: {
    checkPermission,
    fetchData01() {
      this.tablePage = {
        currentPage: 1,
        size: this.tablePage.size,
        totalPage: null,
        totalItems: null
      };
      this.fetchData();
    },
    showChannelManagerDetail(row) {
      this.channelManager.realName = row.channelManagerRealName;
      this.channelManager.channelManagerCode = row.channelManagerCode;
      this.dialogVisibleForChannelManager = true;
    },
    // 查询+搜索课程列表
    fetchData() {
      const that = this;
      if (that.value1 != null) {
        that.dataQuery.txtStartTime = that.value1[0];
        that.dataQuery.txtEndRegTime = that.value1[1];
      } else {
        that.dataQuery.txtStartTime = "";
        that.dataQuery.txtEndRegTime = "";
      }
      that.tableLoading = true;
      agentApi
        .agentList(
          that.tablePage.currentPage,
          that.tablePage.size,
          that.dataQuery
        )
        .then(res => {
          that.tableData = res.data.data;
          for (let i = 0; i < that.tableData.length; i++) {
            if (that.tableData[i].name.substring(0, 1) == "A") {
              that.tableData[i].name = that.tableData[i].name.substring(1, 12);
            }
          }
          that.tableLoading = false;
          // 设置后台返回的分页参数
          pageParamNames.forEach(name =>
            that.$set(that.tablePage, name, parseInt(res.data[name]))
          );
        });
    },
    //打开指定交付中心窗口
    openAssignDelivery(merchantCode) {
      this.dialogFormDelivery = true;
      this.branchOfficeMerchantCode = merchantCode;
      //查询所有交付中心
      deliveryCenterApi.allList().then(res => {
        this.tableDataDelivery = res.data;
      }).catch(err => { })
    },
    //表格数据选中和跳转到指定位置
    searchChannel() {
      for (let i = 0; i < this.tableDataDelivery.length; i++) {
        if (this.tableDataDelivery[i].merchantCode === this.searchMerchantCode) {
          if (!this.$refs['singleTable']) return //不存在这个表格则返回
          let elTable = this.$refs['singleTable'].$el
          if (!elTable) return
          const scrollParent = elTable.querySelector('.el-table__body-wrapper')
          const targetTop = elTable.querySelectorAll('.el-table__body tr')[i].getBoundingClientRect().top //该行的位置
          const containerTop = elTable.querySelector('.el-table__body').getBoundingClientRect().top //body的位置
          //跳转
          scrollParent.scrollTop = targetTop - containerTop; //跳转到存下编辑行的ScrollTop
          this.$refs.singleTable.setCurrentRow(this.tableDataDelivery[i]);
        }
      }
    },
    assignDelivery(merchantCode) {
      this.tableLoading2 = true;
      deliveryCenterApi.assignDelivery(this.branchOfficeMerchantCode, merchantCode).then(res => {
        this.tableLoading2 = false;
        this.$message.success("指派成功")
        this.dialogFormDelivery = false;
        this.fetchData();
      }).catch(err => { });
    },
    liftDelivery(merchantCode) {
      this.$confirm('解除通过该账户绑定的交付中心?')
        .then(_ => {
          deliveryCenterApi.liftDelivery(merchantCode).then(res => {
            this.$message.success("解除绑定成功");
            this.fetchData();
          }).catch(err => { })
        })
        .catch(_ => {
        });
    },
    // 获取起始时间
    dateVal(e) {
      // console.log(e[0]);
      if (e != null) {
        if (e.length > 0) {
          this.dataQuery.txtStartTime = e[0];
          this.dataQuery.txtEndRegTime = e[1];
        } else {
          this.dataQuery.txtStartTime = "";
          this.dataQuery.txtEndRegTime = "";
        }
      }
    },
    // 进入添加页面
    clickAdd() {
      const that = this;
      that.addOrUpdate = true;
      // localStorage.setItem("agentId", "");
      // localStorage.setItem("addOrUpdate", JSON.stringify(that.addOrUpdate));

      ls.setItem("addOrUpdate", that.addOrUpdate);
      ls.removeItem("agentId");

      that.$router.push({
        path: "/merchantManagement/agentAddList",
        query: {
          addOrUpdate: that.addOrUpdate
        }
      });
    },
    //审核理由
    open(chenckReason) {
      const h = this.$createElement;
      this.$msgbox({
        title: "审核理由",
        message: h("p", null, [
          h("i", { style: "color: #FF0802" }, chenckReason)
        ]),
        showCancelButton: false,
        confirmButtonText: "确定"
      });
    },
    // 进入编辑页面
    update(id, isReSubmit) {
      const that = this;
      that.addOrUpdate = false;
      // localStorage.setItem("agentId", id);
      // localStorage.setItem("addOrUpdate", JSON.stringify(that.addOrUpdate));

      ls.setItem("agentId", id);
      ls.setItem("addOrUpdate", that.addOrUpdate);

      that.$router.push({
        path: "/merchantManagement/agentAddList",
        query: {
          addOrUpdate: that.addOrUpdate,
          id: id,
          isReSubmit: isReSubmit
        }
      });
    },

    // 分页
    handleSizeChange(val) {
      console.log(val);
      this.tablePage.size = val;
      this.fetchData();
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val;
      this.fetchData();
    },
    exportList1() {
      const that = this;
      if (
        that.dataQuery.txtStartTime === "" ||
        that.dataQuery.txtStartTime === "" ||
        that.dataQuery.txtStartTime === undefined
      ) {
        that.$message.info("开始时间不能为空");
        return false;
      }
      if (
        that.dataQuery.txtEndRegTime === "" ||
        that.dataQuery.txtEndRegTime === "" ||
        that.dataQuery.txtEndRegTime === undefined
      ) {
        that.$message.info("结束时间不能为空");
        return false;
      }
      that.exportLoading1 = true;
      const data = {
        startDate: that.dataQuery.txtStartTime,
        endDate: that.dataQuery.txtEndRegTime
      };
      agentApi.agentExport1(data).then(
        response => {
          console.log(response);
          if (!response) {
            this.$notify.error({
              title: "操作失败",
              message: "文件下载失败"
            });
          }
          const url = window.URL.createObjectURL(response);
          const link = document.createElement("a");
          link.style.display = "none";
          link.href = url; // 获取服务器端的文件名
          link.setAttribute("download", "市级服务商运营数据表.xls");
          document.body.appendChild(link);
          link.click();
          this.exportLoading1 = false;
        },
        s => {
          if (s === "error") {
            this.exportLoading1 = false;
          }
        }
      );
    },
    // 导出
    exportList() {
      const that = this;
      that.exportLoading = true;
      agentApi.agentExport(that.dataQuery).then(response => {
        console.log(response);
        if (!response) {
          this.$notify.error({
            title: "操作失败",
            message: "文件下载失败"
          });
        }
        const url = window.URL.createObjectURL(response);
        const link = document.createElement("a");
        link.style.display = "none";
        link.href = url; // 获取服务器端的文件名
        link.setAttribute("download", "市级服务商表.xls");
        document.body.appendChild(link);
        link.click();
        this.exportLoading = false;
      });
    },
    agentPaymentIsComplete(id, paymentIsComplete) {
      if (paymentIsComplete == 0) {
        paymentIsComplete = 1;
      }
      const that = this;
      this.$confirm("确定操作吗？", "完款", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        agentApi.updatePaymentIsComplete(id, paymentIsComplete).then(res => {
          if (res.success) {
            that.fetchData01();
            that.$message.success("操作成功");
          }
        });
      });
    },
    // 开通与暂停
    agentStatus(id, status) {
      const that = this;
      this.$confirm("确定操作吗?", "修改状态", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        agentApi.updateStatus(id, status).then(res => {
          if (res.success) {
            that.fetchData01();
            that.$message.success("操作成功");
          }
        });
      })
    },

    updateReProfitRank(id, reProfitRank) {
      if (reProfitRank === 0) {
        reProfitRank = 1;
      } else {
        reProfitRank = 0;
      }
      const that = this;
      this.$confirm("确定操作吗?", "开通推荐二级分润", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          agentApi.updateReProfitRank(id, reProfitRank).then(res => {
            if (res.success) {
              that.fetchData01();
              that.$message.success("操作成功");
            }
          });
        })
        .catch(err => {
        });
    },
    // 打开审核弹框
    openExamine(id) {
      this.showExamine = true;
      this.examine.id = id;
      this.isCheck = "";
    },
    openExamineCompany() {
      this.showExamineCompany = true;
      this.examineCompany.id = id;
      this.isCheckCompany = "";
    },
    // 关闭审核弹框
    closeExamine() {
      this.showExamine = false;
      this.examine.id = "";
    },
    // 状态改变事件
    change(radio) {
      if (radio == "1") {
        this.examine.isCheck = 1;
      } else {
        this.examine.isCheck = 0;
      }
    },
    changeCompany(radio) {
      if (radio == "4") {
        this.examineCompany.isCheck = 4;
      } else {
        this.examineCompany.isCheck = -4;
      }
    },
    // 审核
    toExamine(id, checkReason, isCheck, ele) {
      // console.log(typeof long)
      const that = this;
      that.$refs[ele].validate(valid => {
        // 表单验证
        if (valid) {
          const loading = this.$loading({
            lock: true,
            text: "审核中",
            spinner: "el-icon-loading",
            background: "rgba(0, 0, 0, 0.7)"
          });
          agentApi
            .examine(id, checkReason, isCheck)
            .then(res => {
              if (res.success) {
                loading.close();
                that.showExamine = false;
                that.$nextTick(() => that.fetchData());
                that.$message.success("审核成功！");
              } else {
                that.$message.warning(res.message);
              }
            })
            .catch(err => {
              loading.close();
            });
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    }
  }
};
</script>

<style>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

@media (max-width: 767px) {
  .el-message-box {
    width: 80% !important;
  }
}
</style>
