import request from '@/utils/request'

export default {
  pageList(data){
    return request({
      url: '/activiti/flowCategory/list',
      method: 'GET',
      params: data
    })
  },
  delete(data){
    return request({
      url: '/activiti/flowCategory/delete',
      method: 'DELETE',
      params: data
    })
  },
  add(data){
    return request({
      url: '/activiti/flowCategory/saveNew',
      method: 'POST',
      data
    })
  },
  view(data){
    return request({
      url: '/activiti/flowCategory/view',
      method: 'GET',
      params: data
    })
  },
  update(data){
    return request({
      url: '/activiti/flowCategory/update',
      method: 'POST',
      data
    })
  },

}
