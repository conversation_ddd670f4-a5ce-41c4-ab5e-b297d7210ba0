/**
 * 学员管理相关接口
 */
import request from '@/utils/request'

export default {
  // 分页查询
  testResultList(pageNum, pageSize, data) {
    return request({
      url: '/znyy/areas/student/experience/page/' + pageNum + '/' + pageSize,
      method: 'GET',
      params: data
    })
  },



  // detail(id) {
  //   return request({
  //     url: '/znyy/student/print/detail?printCode=' + id +"&studentName=zhangsan"+"&type=Word",
  //     method: 'GET'
  //   })
  // },

  detail(id,realName) {
    return request({
      url: '/znyy/areas/student/experience/detail?printCode=' + id +'&studentName='+realName,
      method: 'GET'
    })
  },
  editDetail(id) {
    return request({
      url: '/znyy/areas/student/experience/edit/detail?printCode=' + id,
      method: 'GET'
    })
  },

  download(id,realName) {
    return request({
      url: '/znyy/areas/student/experience/download/file?printCode=' + id +"&fileType=png"+"&contentType=experience"+"&studentName="+realName,
      method: 'GET'
    })
  },
  // 新增
  addOrUpdate(data) {
    return request({
      url: '/znyy/areas/student/experience',
      method: 'POST',
      data
    })
  },

}
