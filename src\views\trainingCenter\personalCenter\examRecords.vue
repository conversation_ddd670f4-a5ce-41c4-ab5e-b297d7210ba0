<template>
  <div class="app-container">
    <el-form :inline="true" ref="queryForm" class="container-card" label-width="110px">
      <!-- <el-col :span="5">
                <el-form-item label="试卷名称：">
                    <el-input v-model="form.examName"  placeholder="请输入试卷名称"  clearable /> 
                </el-form-item>
                </el-col>
                <el-col :span="5">
                    <el-form-item label="课程名称：">
                        <el-input v-model="form.courseName"  placeholder="请输入课程名称"  clearable /> 
                    </el-form-item>
                </el-col> -->
      <el-form-item label="考试时间：">
        <el-date-picker
          v-model="form.examTime"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button size="small" icon="el-icon-refresh" @click="resetQuery()">重置</el-button>
        <el-button size="small" icon="el-icon-search" type="primary" @click="search(1)">搜索</el-button>
      </el-form-item>
    </el-form>
    <!-- 表格 -->
    <div class="table-container">
      <el-table class="common-table" v-loading.body="tableLoading" :data="tableData" stripe border :default-sort="{ prop: 'date', order: 'descending' }">
        <el-table-column prop="examId" label="试卷编号"></el-table-column>
        <el-table-column prop="examName" label="试卷名称" width="260"></el-table-column>
        <el-table-column prop="courseName" label="对应课程"></el-table-column>
        <el-table-column prop="passFlag" label="考核状态">
          <template slot-scope="scope">
            <!-- 0未通过，1已通过 -->
            <span>{{ scope.row.passFlag == 0 ? '未通过' : '已通过' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="examFullMarks" label="试卷总分">
          <template slot-scope="scope">
            <span>{{ scope.row.examFullMarks }}分</span>
          </template>
        </el-table-column>
        <el-table-column prop="examPassScore" label="及格分数">
          <template slot-scope="scope">
            <span>{{ scope.row.examPassScore }}分</span>
          </template>
        </el-table-column>
        <el-table-column prop="examScore" label="考核成绩">
          <template slot-scope="scope">
            <span>{{ scope.row.examScore }}分</span>
          </template>
        </el-table-column>
        <el-table-column prop="examTime" label="考试时间"></el-table-column>
        <el-table-column prop="id" label="操作">
          <template slot-scope="scope">
            <el-button type="text" @click="showPaperDetail(scope.row)">试卷详情</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <el-col :span="24" style="margin-bottom: 20px; margin-top: 20px">
        <el-pagination
          :current-page="tablePage.currentPage"
          :page-sizes="[10, 20, 30, 40, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="tablePage.totalItems"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </el-col>
    </div>
    <!-- 试卷详情 -->
    <paper-detail ref="paperDetailRef" :dialogVisible="dialogVisible" @closeDialog="handleClose"></paper-detail>
  </div>
</template>

<script>
  import PaperDetail from './components/paperDetail.vue';
  import examApi from '@/api/training/exam';
  export default {
    components: {
      PaperDetail
    },
    data() {
      return {
        form: {
          examTime: []
        },
        tableData: [],
        tablePage: {
          currentPage: 1,
          totalItems: 1,
          size: 10,
          totalPage: 1
        },
        dialogVisible: false,
        courseId: '',
        tableLoading: false
      };
    },
    created() {
      const { courseId } = this.$route.query;
      console.log('courseId', courseId);

      this.courseId = courseId ? courseId : '';
      this.search();
    },
    methods: {
      // 考试记录查询
      search(page) {
        if (typeof page == 'number' && page > 0) {
          this.tablePage.currentPage = page;
        }
        let sysUserRoles = JSON.parse(window.localStorage.getItem('sysUserRoles'));
        let params = {
          userId: sysUserRoles[0].userId,
          courseId: this.courseId,
          pageNum: this.tablePage.currentPage,
          pageSize: this.tablePage.size,
          examTimeStart: this.form.examTime[0] ? this.form.examTime[0] + ' 00:00:00' : '',
          examTimeEnd: this.form.examTime[1] ? this.form.examTime[1] + ' 23:59:59' : ''
        };
        this.tableLoading = true;
        examApi
          .courseExamRecordPage(params)
          .then((res) => {
            this.tableData = res.data.data;
            this.tablePage.totalItems = Number(res.data.totalItems);
          })
          .catch((err) => {
            console.log('请求失败' + err);
          })
          .finally(() => {
            this.tableLoading = false;
          });
      },
      // 重置查询
      resetQuery() {
        this.form.examTime = [];
        this.search();
      },
      handleSizeChange(val) {
        this.tablePage.size = val;
        this.search();
      },
      handleCurrentChange(val) {
        this.tablePage.currentPage = val;
        this.search();
      },
      showPaperDetail(item) {
        const examRecordId = item.id;
        this.dialogVisible = true;
        this.$nextTick(() => {
          this.$refs['paperDetailRef'].open(examRecordId);
        });
      },
      handleClose() {
        this.dialogVisible = false;
      }
    }
  };
</script>

<style lang="less" scoped>
  .table-container {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
    padding: 10px;
    box-sizing: border-box;
  }
  .el-pagination {
    text-align: right;
  }
</style>
