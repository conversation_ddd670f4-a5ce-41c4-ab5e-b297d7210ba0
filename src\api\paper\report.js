
import request from '@/utils/request'

export default {
  // 分页查询
  pageList(data) {
    return request({
      url: '/paper/web/report/list',
      method: 'GET',
      params: data
    })
  },
  //添加、编辑
  edit(data) {
    return request({
      url: '/paper/web/report',
      method: 'POST',
      data
    })
  },
  detail(paperId,type) {
    return request({
      url: '/paper/web/report',
      method: 'GET',
      params:{
        paperId:paperId,
        type:type
      }
    })
  },
  //模板参考
  refer(data) {
    return request({
      url: '/paper/web/report/refer',
      method: 'GET',
      params: data
    })
  },
}
