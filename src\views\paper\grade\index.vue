<template>
  <div class="app-container">
    <el-form :inline="true" ref="queryForm" class="container-card" label-width="110px">
      <el-form-item label="学段名称：">
        <el-input v-model="dataQuery.name" placeholder="请输入名称" clearable />
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-re" @click="resetQuery()">重置</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="getPageList()">搜索</el-button>
      </el-form-item>
    </el-form>
    <el-col :span="24" style="margin-bottom: 30px">
      <el-button type="success" icon="el-icon-plus" size="mini" @click="addBtn()">新增</el-button>
    </el-col>
    <el-table class="common-table" v-loading="tableLoading" :data="tableData" style="width: 100%;margin-bottom: 20px;"
      row-key="id" border default-expand-all>
      <el-table-column prop="id" label="ID"></el-table-column>
      <el-table-column prop="name" label="学段名称" />
      <el-table-column prop="withdrawnBonus" label="操作" width="480">
        <template slot-scope="scope">
          <el-button type="success" size="mini" icon="el-icon-edit-outline"
            @click="editBtn(scope.row.id)">编辑</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="subjectNum" label="维度数量"></el-table-column>
      <!--      <el-table-column prop="status" label="编辑时间"></el-table-column>-->
    </el-table>
    <!-- 分页 -->
    <el-col :span="24" style="margin-bottom: 20px">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>

    <!-- 修改或者新增弹窗 -->
    <el-dialog title="编辑学段" :visible.sync="open" width="70%" @close="close"  v-loading="formLoading">
      <el-form ref="form" :model="form" :rules="rules" label-width="120px" style="width: 70%;">
        <el-form-item label="学段名称:" prop="name">
          <el-input v-model="form.name" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import gradeApi from "@/api/paper/grade";
import { pageParamNames } from "@/utils/constants";
import { mapGetters, mapState } from "vuex";

export default {
  name: "subject",
  data() {
    return {
      dataQuery: {
        grade: null,
        name: ""
      },
      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      formLoading:false,
      open: false,
      tableData: [],
      form: {},
      // 表单校验
      rules: {
        name: [{ required: true, message: "请输入学段名称", trigger: "blur" }]
      }
    };
  },
  created() {
    this.getPageList();
  },
  methods: {
    gradeFormatter(row, column, cellValue, index) {
      return this.enumFormat(this.gradeList, cellValue);
    },
    addBtn() {
      this.reset();
      this.open = true;
    },
    editBtn(id) {
      this.reset();
      gradeApi.detail(id).then(res => {
        this.form = res.data;
        this.open = true;
      });
    },
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.formLoading = true;
          gradeApi.saveOrUpdate(this.form).then(response => {
            this.formLoading = false;
            this.$message.success("提交成功！");
            this.open = false;
            this.getPageList();
          });
        }
      });
    },
    getPageList() {
      this.tableLoading = true;
      this.dataQuery.pageNum = this.tablePage.currentPage;
      this.dataQuery.pageSize = this.tablePage.size;
      gradeApi.pageList(this.dataQuery).then(res => {
        console.log(res.data.data);
        this.tableData = res.data.data;
        this.tableLoading = false;
        // 设置后台返回的分页参数
        pageParamNames.forEach(name =>
          this.$set(this.tablePage, name, parseInt(res.data[name]))
        );
      });
    },
    handlePage() {
      (this.tablePage = {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      }),
        this.getPageList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dataQuery = {
        name: ""
      };
      this.getPageList();
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val;
      this.getPageList();
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val;
      this.getPageList();
    },
    close() {
      this.open = false;
      this.reset();
    },
    reset() {
      this.form = {
        grade: null,
        name: ""
      };
    }
  },
  computed: {
    ...mapGetters("enumItem", ["enumFormat"]),
    ...mapState("enumItem", {
      gradeList: state => state.question.gradeList
    })
  }
};
</script>

<style>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.period-table td,
.period-table th {
  text-align: center;
}

@media (max-width: 767px) {
  .el-message-box {
    width: 80% !important;
  }
}

.el-tooltip__popper {
  max-width: 800px;
}
</style>
