<!--招商数据-->
<template>
  <div>
    <el-row :gutter="10" style="height: 400px;padding-top: 10px;">
      <el-col :span="12" style="height: 100%">
        <el-card shadow="always" body-style="padding:5px">
          <div>
            <el-col :span="16">
              <div>
                <h3>门店招生数据</h3>
              </div>
            </el-col>
            <el-col :span="8"><div>
              <el-radio-group style="float: right;margin-top: 5%" v-model="radio" @change = "initData(radio)">
                <el-radio-button label="day">日</el-radio-button>
                <el-radio-button label="week">周</el-radio-button>
                <el-radio-button label="month">月</el-radio-button>
              </el-radio-group>
            </div>
            </el-col>
            <el-table
              :data="tableData"
              v-loading="tableLoading"
              height="320"
              border
              style="width: 100%">
              <el-table-column
                prop="date"
                label="日期">
              </el-table-column>
              <el-table-column
                prop="regStudentsNum"
                label="注册学员(位)">
              </el-table-column>
              <el-table-column
                prop="formalStudentNum"
                label="正式学员(位)">
              </el-table-column>
              <el-table-column
                prop="conversionRate"
                label="转化率(%)">
              </el-table-column>
            </el-table>
          </div>
        </el-card>
      </el-col>
      <el-col :span="12" style="height: 100%">
        <el-card shadow="always" body-style="padding:5px;height:85%" style="height: 100%">
          <h3>门店招生数据图表</h3>
          <div id="main3" :style="{width: '100%', height: '100%'}">
          </div>
        </el-card>
      </el-col>
    </el-row>  </div>
</template>

<script>
import elasticSearchApi from "@/api/elasticSearch";

export default {
  name: "admissionsDataForm",
  props: {
    // 表单
    form: {
      type: Object,
    },
  },
  data(){
    return {
      radio: "day",
      tableLoading: false,
      tableData: [],
      option:{},
      xAxisData:[],
      app:{},
    }
  },
  mounted() {
    this.initEcharts();
    this.initData()
  },
  methods:{
    initData2(){
      this.$message.success("123")
    },
    initEcharts(){
      var chartDom = document.getElementById('main3');
      var myChart = this.$echarts.init(chartDom);
      this.option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            crossStyle: {
              color: '#999'
            }
          }
        },
        toolbox: {
          feature: {
            dataView: { show: true, readOnly: false },
            magicType: { show: true, type: ['line', 'bar'] },
            restore: { show: true },
            saveAsImage: { show: true }
          }
        },
        legend: {
          data: ['注册学员', '正式学员', '转化率']
        },
        xAxis: [
          {
            type: 'category',
            data: [],
            axisPointer: {
              type: 'shadow'
            },
            axisLabel:{
              showMaxLabel: true,
              interval: 0,
              rotate: 0
            }
          }
        ],
        yAxis: [
          {
            type: 'value',
            name: '学员数量',
            axisLabel: {
              formatter: '{value} 位'
            }
          },
          {
            type: 'value',
            name: '转化率',
            max: 100,
            axisLabel: {
              formatter: '{value} %'
            }
          }
        ],
        series: []
      };
      this.option && myChart.setOption(this.option);
    },
    initData(type){
      if(!type){
        type = 'day';
      }
      this.tableLoading = true;
      elasticSearchApi.initAdmissionsData(type).then(res=>{
        this.tableLoading = false;
        this.tableData = res.data.data.data;
        let data = res.data.data;
        this.option.xAxis[0].data = data.dates;

        let chartDtos = data.chartDtos;
        this.option.series = [];
        for (let i = 0; i < chartDtos.length; i++) {
          let ser = {};
          if(chartDtos[i].name === 'formalStudentNum'){
            ser.name = '正式学员';
          }else if(chartDtos[i].name === 'regStudentsNum'){
            ser.name = '注册学员';
          }else if(chartDtos[i].name === 'conversionRate'){
            ser.name = '转化率';
          }
          ser.data = chartDtos[i].data;
          this.option.legend.data.push(ser.name);
          ser.type = 'bar';
          if(chartDtos[i].name === 'conversionRate'){
            ser.type = 'line';
            ser.yAxisIndex = 1;
          }
          this.option.series.push(ser);
        }



        let chartDom = document.getElementById('main3');
        var myChart = this.$echarts.init(chartDom);
        myChart.clear();
        myChart.setOption(this.option,true);
      }).catch(err=>{})
    },
  }
}
</script>

<style scoped>

</style>
