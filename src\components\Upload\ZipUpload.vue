<template>
  <div>
    <el-upload
      class="upload-demo"
      accept=".zip"
      action
      :before-upload="handleBeforeUpload"
      :on-remove="handleRemove"
      :http-request="uploadHttp"
      :on-error="handleError"
      multiple
      :limit="limit"
      :on-exceed="onExceed"
      :file-list="fileList"
    >
      <el-button
        :disabled="fileList.length == 1"
        slot="trigger"
        size="small"
        type="primary"
        >点击上传</el-button
      >
      <!-- :disabled="fileList.length == 1" -->
      <div  slot="file"  v-if="fileList.length == 1" @click.stop="click" style="position: relative;z-index: 99;">
        <img
      
        src="../../assets/zip.png"
        style="display:inline-block; width: 70px; height: 70px"
      ></img>
      <i class="el-icon-close icon" @click.stop="handleRemove" ></i>
      </div>

      <div v-if="showTip" slot="tip" class="el-upload__tip">
        <div style="color:red">请联系总部获取</div>
        <div v-if="tipText">{{ tipText }}</div>
        <div v-else>
          只能上传JPG、JPEG、PNG、GIF文件，且不超过 {{ this.isKbOrMb }}
        </div>
      </div>
    </el-upload>
    <!-- <el-upload
      class="upload-demo"
      action
      accept=".zip"
      v-loading="loading"
      :http-request="uploadHttp"
      :before-upload="handleBeforeUpload"
      :file-list="fileList"
      :limit="1"
      :on-error="handleError"
      :on-exceed="onExceed"
      :on-remove="handleRemove"
      :on-success="handleSuccess"
      name="file"
    >
      <el-button size="small" type="primary">点击上传</el-button>
      <div v-if="showTip" slot="tip" class="el-upload__tip">
        <div v-if="tipText">{{ tipText }}</div>
        <div v-else>
          只能上传JPG、JPEG、PNG、GIF文件，且不超过 {{ this.isKbOrMb }}
        </div>
      </div>
    </el-upload> -->
  </div>
</template>

<script>
import { ossPrClient } from "@/api/alibaba";

export default {
  name: "MyUpload",
  props: {
    //点击错误提示信息
    errMsg: {
      type: String,
      default: undefined,
    },
    // 图片上传数量
    limit: {
      type: [Number, String],
      default: 1,
    },
    tipText: {
      type: String,
      default: "",
    },
    // 图片大小尺寸
    imgSize: {
      type: Number,
      default: 5 * 1024 * 1024, // 5M=>5*1024*1024 500KB=>500*1024
    },
    zipName: {
      type: String,
      default: "",
      require: true,
    },
    // 是否显示图片的tip
    showTip: {
      type: Boolean,
      default: true,
    },
    // 展示的图片列表
    fileList: {
      type: Array,
      default() {
        return [];
      },
    },
    fullUrl: {
      type: Boolean,
      default: false,
    },
    // versions: {
    //   type: Number,
    //   default: 1,
    // },
    dialogVisible: false,
  },
  data() {
    return {
      loading: false,
    };
  },
  computed: {
    // 动态显示MB或者KB
    isKbOrMb() {
      return this.imgSize / 1024 / 1024 >= 1
        ? `${Number(this.imgSize / 1024 / 1024).toFixed(0)}MB`
        : `${Number(this.imgSize / 1024).toFixed(0)}KB`;
    },
  },
  created() {
    ossPrClient();
  },
  methods: {
    click() {},
    uploadHttp({ file }) {
      this.loading = true;
      const fileName =
        "manage/" +
        this.zipName +
        "_" +
        Date.parse(new Date()) +
        // "_" +
        // this.versions +
        ".zip";
      ossPrClient()
        .put(fileName, file)
        .then(({ res, url, name }) => {
          if (res && res.status === 200) {
            console.log(`阿里云OSS上传图片成功回调1`, res, url, name);
            this.fileList.push({
              uid: file.uid,
              url: this.aliUrl + name,
              name: name.split("/")[1],
            });
            if (this.fullUrl) {
              this.handleSuccess(this.aliUrl + name);
            } else {
              this.handleSuccess(name);
            }
            this.loading = false;
          }
        })
        .catch((err) => {
          this.$message.error("上传图片失败请检查网络或者刷新页面");
          console.log(`阿里云OSS上传图片失败回调`, err);
        });
    },
    /* 上传图片开始 */
    // 图片上传之前的校验
    handleBeforeUpload(file) {
      console.log(22222);
      const a = file.name.split(".");
      const isZip = a[a.length - 1] == "zip";
      console.log(isZip);
      // if (this.errMsg) {
      //   this.$message.error(this.errMsg);
      //   return false;
      // }

      // const isJPG = file.type === "image/jpeg";
      // const isPNG = file.type === "image/png";
      // const isGIF = file.type === "image/gif";
      // const isSize = file.size < this.imgSize; // 图片是否小于限定的尺寸
      // if (this.tipText) {
      if (!isZip) {
        this.$message.error("上传的文件只能是zip格式");
        return false;
      }
      // } else {
      //   if (!isJPG && !isPNG && !isGIF) {
      //     this.$message.error("上传的图片只能是 JPG、JPEG、PNG、GIF 格式");
      //     return false;
      //   }
      // }

      // 图片是否小于限定的尺寸
      // if (!isSize) {
      //   this.$message.error(`上传的图片大小不能超过 ${this.isKbOrMb}`);
      //   return false;
      // }
      return isZip;
    },
    // 图片上传失败
    handleError(err) {
      this.$message.error("图片上传失败");
      console.log(err);
    },
    // 文件超过上传个数
    onExceed() {
      this.$message.error(`最多只能上传 ${this.limit} 张图片`);
      return false;
    },
    // 图片删除
    handleRemove(file) {
      console.log("前===", this.fileList);
      var index = this.fileList.findIndex((item) => {
        if (item.uid === file.uid) {
          return true;
        }
      });
      this.fileList.splice(index, 1);
      console.log("后===", this.fileList);
      this.$emit("handleRemove", file);
    },
    // 图片上传成功
    handleSuccess(res) {
      this.$emit("handleSuccess", res || "");
    },
    /* 上传图片结束 */
  },
};
</script>
<style scoped>
.upload .el-upload--picture-card {
  display: none !important;
}
.el-upload-list {
  /* display: none !important; */
  width: 70px !important;
}
.icon {
  position: absolute;
  width: 20px;
  height: 20px;
  color: #fff !important;
  display: flex !important;
  justify-content: center;
  align-items: center;
  border-radius: 20px;
  background-color: rgba(0, 0, 0, 0.7);
  top: -5px !important;
  right: -5px !important;
}
</style>
