import request from '@/utils/request'

// 课程表列表
export const getTimetable = (data) => {
  return request({
    url: '/znyy/order/getCheckList',
    method: 'GET',
    params: data
  })
}
// 课程表列表
export const getRefundInfo = (orderId) => {
  return request({
    url: '/znyy/order/refund/info',
    method: 'GET',
    params: { orderId: orderId }
  })
}
// 课程表导出
export const studentStudyExport = (data) => {
  return request({
    url: '/deliver/web/learnManager/studentStudyExport',
    method: 'GET',
    responseType: 'blob',
    params: data
  })
}

// 课程表导出
export const cancelRefund = (orderId) => {
  return request({
    url: '/znyy/order/cancel',
    method: 'PUT',
    params: { 'orderId': orderId }
  })
}

// 获取学员课程表内容
export const getAdjustInfo = (id) => {
  return request({
    url: '/deliver/web/learnManager/getAdjustInfo',
    method: 'GET',
    params: { id: id }
  })
}
// 调课接口
export const adjustPlanStudy = (data) => {
  return request({
    url: '/deliver/web/learnManager/adjustPlanStudy',
    method: 'post',
    data: data
  })
}
// 新增学员课程表
export const checkOrderPass = (orderId, pass) => {
  return request({
    url: '/znyy/order/check',
    method: 'put',
    params: { 'orderId': orderId, 'pass': pass }
  })
}

// 回显新增试课记录表
export const getTableInfo = (data) => {
  return request({
    url: '/deliver/web/common/getTableInfo',
    method: 'POST',
    params: data
  })
}

// 新增试课记录表
export const addTrialClassRecord = (data) => {
  return request({
    url: '/deliver/web/common/addTrialClassRecord',
    method: 'POST',
    data: data
  })
}

// 课程表列表
export const getTimetableReview = (data) => {
  return request({
    url: '/deliver/web/learnManager/getTimetableReview',
    method: 'GET',
    params: data
  })
}

// 课程表-获取请假申请详情
export const getVacationInfo = (data) => {
  return request({
    url: '/deliver/web/learnManager/getVacationInfo',
    method: 'GET',
    params: data
  })
}

// 数据查看-根据年月获取课程日期列表
export const getStudyDateList = (data) => {
  return request({
    url: '/deliver/web/learnManager/getStudyDateList',
    method: 'GET',
    params: data
  })
}

// 获取学管师列表
export const getLearnTubeList = (data) => {
  return request({
    url: '/deliver/web/sysUser/getLearnTubeList',
    method: 'GET',
    params: data
  })
}

// 预警相关-学员分配学管师
export const allotLearnTube = (data) => {
  return request({
    url: '/deliver/web/warning/allotLearnTube',
    method: 'post',
    params: data
  })
}

// 获取指派中心
export const belongDeliverAndAllDeliver = (data) => {
  return request({
    url: '/deliver/web/learnManager/belongDeliverAndAllDeliver?id=' + data,
    method: 'get'
  })
}

// 修改学员列表状态
export const updateStudentIsEnable = (id, isEnable) => {
  return request({
    url: '/deliver/web/learnManager/updateStudentIsEnable?id=' + id + '&isEnable=' + isEnable,
    method: 'put'
  })
}

// 重新指派
export const submitAssign = (code, id) => {
  return request({
    url: '/deliver/web/learnManager/assign?deliverMerchantCode=' + code + '&id=' + id,
    method: 'put'
  })
}
