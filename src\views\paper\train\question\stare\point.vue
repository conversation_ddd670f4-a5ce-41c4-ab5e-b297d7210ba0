<template>
  <div class="app-container">
    <el-form ref="form" :model="form" :rules="rules" label-width="120px"  v-loading="formLoading"  style="width: 70%">
      <el-form-item label="所属类型:" prop="type">
        <el-select v-model="form.type" placeholder="全部" disabled>
          <el-option v-for="(item, index) in trainType" :key="index" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="题型:" prop="questionType">
        <el-select v-model="form.questionType" placeholder="全部" disabled>
          <el-option v-for="(item, index) in trainQuestionType" :key="index" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="年级：" prop="grade" v-if="form.courseType == 1">
        <el-select v-model="form.grade" placeholder="" clearable>
          <el-option v-for="item in gradeList" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="选择维度：" prop="categoryId">
        <el-select v-model="form.categoryId" placeholder="全部" clearable>
          <el-option v-for="(item, index) in categoryList" :key="index" :label="item.name" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="名称：" prop="title">
        <el-input v-model="form.title" />
      </el-form-item>
      <el-form-item label="内容：" required>
        <el-input type="textarea" :rows="10" :autosize="{ minRows: 6, maxRows: 10 }" v-model="form.customInfo[0].label" />
      </el-form-item>
      <el-form-item label="示例图：" required>
        <my-upload @handleSuccess="handleSuccess" @handleRemove="handleRemove" :fullUrl="true" :file-list="fileList" :showTip="false" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="submitForm">提交</el-button>
        <el-button v-show="form.id === null" @click="resetForm">重置</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
  import questionApi from '@/api/paper/train/question';
  import { pageParamNames } from '@/utils/constants';
  import { mapGetters, mapState } from 'vuex';
  import MyUpload from '@/components/Upload/MyUpload';
  import categoryApi from '@/api/paper/train/category';

  export default {
    components: { MyUpload },
    data() {
      return {
        categoryList: [],
        fileList: [],
        form: {
          id: null,
          title: '',
          type: 'STARE_',
          questionType: 'STARE_POINT',
          customInfo: [
            {
              label: '',
              value: ''
            }
          ],
          answer: [],
          isRandom: true,
          expandInfo: '',
          isStep: false,
          courseType: 0,
          grade: ''
        },
        // 表单校验
        rules: {
          categoryId: [{ required: true, message: '请选择', trigger: 'blur' }],
          type: [{ required: true, message: '请选择', trigger: 'blur' }],
          grade: [{ required: true, message: '请选择', trigger: 'blur' }],
          title: [{ required: true, message: '请输入', trigger: 'blur' }]
        },
        formLoading:false,
        gradeList: [
          { label: '1-3年级', value: 1 },
          { label: '4-6年级', value: 2 },
          { label: '初高中', value: 3 }
        ]
      };
    },
    created() {
      this.getCategoryList();
      let id = this.$route.query.id;
      this.form.courseType = this.$route.query.courseType ? 1 : 0;
      if (id && parseInt(id) !== 0) {
        this.formLoading = true;
        questionApi.detail(id).then((re) => {
          this.form = re.data;
          this.form.grade = this.form.grade == 0 ? '' : this.form.grade;
          this.fileList.push({ url: this.form.expandInfo });
          this.formLoading = false;
        });
      }
    },
    methods: {
      getCategoryList() {
        categoryApi.list().then((res) => {
          this.categoryList = res.data.data;
        });
      },

      submitForm() {
        if (this.form.isRandom && this.form.expandInfo === '') {
          this.$message.error('请上传示例图！');
          return false;
        }
        this.$refs['form'].validate((valid) => {
          if (valid) {
            this.formLoading = true;
            questionApi.saveOrUpdate(this.form).then((response) => {
              this.formLoading = false;
              this.$message.success('提交成功！');
              if (this.form.courseType == 1) {
                this.$router.push('/train/trainAfterCourse');
              } else {
                this.$router.push('/train/stare');
              }
            });
          }
        });
      },

      resetForm() {
        let lastId = this.form.id;
        let lastCourseType = this.form.courseType;
        if (this.$refs['form']) {
          this.$refs['form'].resetFields();
        }
        this.form = {
          id: null,
          title: '',
          type: 'STARE_',
          questionType: 'STARE_POINT',
          customInfo: [
            {
              label: '',
              value: '' //label标题，此处无，value步骤
            }
          ],
          isRandom: true,
          expandInfo: '', //示例图地址
          isStep: false,
          grade: ''
        };
        this.form.id = lastId;
        this.form.courseType = lastCourseType;
        this.fileList = [];
      },

      handleSuccess(url) {
        this.form.expandInfo = url;
      },
      handleRemove(file) {
        this.form.expandInfo = '';
      }
    },
    computed: {
      ...mapGetters('enumItem', ['enumFormat']),
      ...mapState('enumItem', {
        trainType: (state) => state.train.trainType,
        trainQuestionType: (state) => state.train.trainQuestionType
      })
    }
  };
</script>
