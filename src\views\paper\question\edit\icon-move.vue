<!--硬币移动题-->
<template>
  <div class="app-container">
    <el-form :model="form" ref="form" label-width="120px" v-loading="formLoading" :rules="rules">
      <el-form-item label="选择学段：" prop="gradeId" required>
        <el-select v-model="form.gradeId" placeholder="选择学段" @change="levelChange" clearable>
          <el-option v-for="item in gradeList" :key="item.id" :value="item.id" :label="item.name"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="选择维度：" prop="subjectId" required>
        <el-select v-model="form.subjectId" placeholder="选择维度" @change="getKonwTree">
          <el-option v-for="item in subjectFilter" :key="item.id" :value="item.id" :label="item.name"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="有标题：" required>
        <el-radio-group v-model="form.expandInfo.titleFlag">
          <el-radio :label="false">否</el-radio>
          <el-radio :label="true">是</el-radio>
        </el-radio-group>
        <el-form-item v-show="form.expandInfo.titleFlag">
          <el-input v-model="form.expandInfo.titleInfo" placeholder="输入标题"/>
        </el-form-item>
      </el-form-item>
      <el-form-item label="有图片：" required>
        <el-radio-group v-model="form.expandInfo.imgFlag">
          <el-radio :label="false">否</el-radio>
          <el-radio :label="true">是</el-radio>
        </el-radio-group>
        <el-form-item v-show="form.expandInfo.imgFlag">
          <my-upload @handleSuccess="handleSuccess" @handleRemove="handleRemove" :fullUrl="true" :file-list="fileList"
                     :showTip="false"
          />
          <el-input type="hidden" v-model="form.expandInfo.imgInfo"/>
        </el-form-item>
      </el-form-item>
      <el-form-item label="有音频：" required>
        <el-radio-group v-model="form.expandInfo.audioFlag">
          <el-radio :label="false">否</el-radio>
          <el-radio :label="true">是</el-radio>
        </el-radio-group>
        <el-form-item v-show="form.expandInfo.audioFlag">
          <upload-file @handleSuccess="handleAudioSuccess" @handleRemove="handleAudioRemove"
                       :file-list="audioFileList"
          />
          <el-input type="hidden" v-model="form.expandInfo.audioInfo"/>
        </el-form-item>
      </el-form-item>
      <el-form-item label="有视频：" required>
        <el-radio-group v-model="form.expandInfo.videoFlag">
          <el-radio :label="false">否</el-radio>
          <el-radio :label="true">是</el-radio>
        </el-radio-group>
        <el-form-item v-show="form.expandInfo.videoFlag">
          <upload-file @handleSuccess="handleVideoSuccess" @handleRemove="handleVideoRemove"
                       :file-list="videoFileList"
          />
          <el-input type="hidden" v-model="form.expandInfo.videoInfo"/>
        </el-form-item>
      </el-form-item>
      <el-form-item label="题干：" prop="title" required>
        <el-input v-model="form.title">
          <!--          <i @click="inputClick(form,'title')" slot="suffix" class="el-icon-edit-outline"-->
          <!--             style="line-height: 36px;font-size: 20px;cursor: pointer;"></i>-->
        </el-input>
      </el-form-item>
      <el-form-item label="题目json数据：" required>
        <json-upload @handleSuccess="handleJsonSuccess" @handleRemove="handleJsonRemove"
                     :file-list="jsonFileList"
        />

      </el-form-item>
      <el-form-item label="题目示例图片：" required>
        <my-upload @handleSuccess="titlePicSuccess" @handleRemove="titlePicRemove" :fullUrl="true"
                   :file-list="titleFileList" :showTip="false"
        />
      </el-form-item>
      <el-form-item label="答案示例图片：" required>
        <my-upload @handleSuccess="answerPicSuccess" @handleRemove="answerPicRemove" :fullUrl="true"
                   :file-list="answerFileList" :showTip="false"
        />
      </el-form-item>
      <el-form-item label="分数：" prop="score">
        <el-input-number v-model="form.score" :precision="0" :step="1" :min='0' :max="100"></el-input-number>
      </el-form-item>
      <el-form-item label="徽章：" prop="badge">
        <el-input-number v-model="form.badge" :precision="0" :step="1" :min="0" :max="100"></el-input-number>
      </el-form-item>
      <el-form-item label="计时器：" prop="openTimer" required>
        <el-radio-group v-model="form.openTimer">
          <el-radio :label="false">关闭</el-radio>
          <el-radio :label="true">开启</el-radio>
        </el-radio-group>
        <el-form-item v-show="form.openTimer" :key="item.label" v-for="(item,index) in form.openTimerData">
          <div class="question-item-label">
            <el-input v-model="item.min" onkeyup="value=value.replace(/[^\d]/g,'')"
                      style="width:50px;marginRight:5px;"
            />
            分钟-
            <el-input v-model="item.max" style="width:50px;marginRight:5px;"/>
            分钟，完成答题 分值
            <el-select v-model="item.operator" style="width:80px;marginRight:5px;">
              <el-option v-for="item in operatorList" :key="item.value" :value="item.value" :label="item.label"
              ></el-option>
            </el-select>
            <el-input v-model="item.score" style="width:50px;marginRight:5px;"/>
          </div>
        </el-form-item>
        <el-button v-show="form.openTimer" icon="el-icon-circle-plus-outline" @click="addOpenTimerData"/>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="submitForm">提交</el-button>
        <el-button @click="resetForm">重置</el-button>
      </el-form-item>
    </el-form>
    <el-dialog :visible.sync="richEditor.dialogVisible" append-to-body :close-on-click-modal="false"
               style="width: 100%;height: 100%" :show-close="false" center
    >
      <Ueditor @ready="editorReady"/>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="editorConfirm">确 定</el-button>
        <el-button @click="richEditor.dialogVisible = false">取 消</el-button>
      </span>
    </el-dialog>
    <div class="q-dailog">
      <el-dialog :visible.sync="questionShow.dialog" style="width: 100%;height: 100%" title="题目预览">
        <QuestionShow
          :qType="questionShow.qType"
          :question="questionShow.question"
          :qLoading="questionShow.loading"
        />
      </el-dialog>
    </div>
  </div>
</template>

<script>
import QuestionShow from '../components/Show'
import Ueditor from '@/components/Ueditor'
import { mapGetters, mapState, mapActions } from 'vuex'
import questionApi from '@/api/paper/question'
import subjectApi from '@/api/paper/subject'
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import MyUpload from '@/components/Upload/MyUpload'
import UploadFile from '@/components/Upload/UploadFile'
import JsonUpload from '@/components/Upload/JsonUpload'

export default {
  components: {
    JsonUpload,
    UploadFile,
    Treeselect,
    Ueditor, QuestionShow,
    MyUpload
  },
  data() {
    return {
      answer: [
        { label: '', value: '' }
      ],
      titleFileList: [],
      answerFileList: [],
      limitInfo: {
        titlePic: '',
        answerPic: ''
      },
      jsonFileList: [],
      fileList: [],
      videoFileList: [],
      audioFileList: [],

      gradeList: [],
      valueConsistsOf: 'LEAF_PRIORITY',
      subjectList: [],
      form: {
        id: null,
        questionType: 'ICON_MOVE',
        isSub: false,
        gradeId: null,
        subjectId: null,
        title: '',
        customInfo: null,
        analysis: '',
        answer: '',
        score: undefined,
        mediaType: 'NO',
        difficulty: 0,
        dxSource: 'PAPER',
        status: 1,
        knowledgePoints: null,
        openTimer: false,
        openTimerData: [
          { min: '', max: '', operator: '+', score: '' }
        ],
        expandInfo: {
          titleFlag: false,
          titleInfo: '',
          imgFlag: false,
          imgInfo: '',
          videoFlag: false,
          videoInfo: '',
          videoName: '',
          audioFlag: false,
          audioInfo: '',
          audioName: ''
        },
        limitInfo: '',
        badge:3,
      },
      subjectFilter: null,
      formLoading: false,
      rules: {
        gradeId: [
          { required: true, message: '请选择学段', trigger: 'change' }
        ],
        subjectId: [
          { required: true, message: '请选择维度', trigger: 'change' }
        ],
        title: [
          { required: true, message: '请输入题干', trigger: 'blur' }
        ],
        analysis: [
          { required: true, message: '请输入解析', trigger: 'blur' }
        ],
        score: [
          { required: true, message: '请输入分数', trigger: 'blur' }
        ],
        badge: [
          { required: true, message: '请输入徽章', trigger: 'blur' }
        ],
        answer: [
          { required: true, message: '请选择正确答案', trigger: 'change' }
        ],
        difficulty: [
          { required: true, message: '请选择难度', trigger: 'change' }
        ]
      },
      richEditor: {
        dialogVisible: false,
        object: null,
        parameterName: '',
        instance: null
      },
      questionShow: {
        qType: 0,
        dialog: false,
        question: null,
        loading: false
      },
      konwledgeTree: [], // 知识点树数据
      defaultProps: {
        children: 'child',
        label: 'name'
      },
      normalizer(node) {
        if (node.child == null || node.child.length === 0) {
          return {
            id: node.id,
            label: node.name
          }
        }
        return {
          id: node.id,
          label: node.name,
          children: node.child
        }
      },
      mineStatusValue: []
    }
  },
  created() {
    this.initGrade()
    this.initSubject()
    let id = this.$route.query.id
    let _this = this
    if (id && parseInt(id) !== 0) {
      _this.formLoading = true
      this.subjectFilter = this.subjectList
      questionApi.select(id).then(re => {
        _this.form = re.data
        this.getKonwTree()
        this.subjectFilter = this.subjectList.filter(data => data.gradeId === this.form.gradeId)
        if (_this.form.knowledgePoints != null && _this.form.knowledgePoints !== '') {
          this.form.knowledgePoints = _this.form.knowledgePoints.split(',')
        }
        if (_this.form.expandInfo) {
          if (this.form.expandInfo.imgFlag) {
            this.fileList.push({ url: this.form.expandInfo.imgInfo })
          }
          if (this.form.expandInfo.videoFlag) {
            this.videoFileList.push({ name: this.form.expandInfo.videoName, url: this.form.expandInfo.videoInfo })
          }
          if (this.form.expandInfo.audioFlag) {
            this.audioFileList.push({ name: this.form.expandInfo.audioName, url: this.form.expandInfo.audioInfo })
          }
          if (this.form.answer) {
            this.answer = JSON.parse(this.form.answer)
            this.jsonFileList.push({ name: this.answer[0].label, url: this.answer[0].value })
          }
          if (this.form.limitInfo) {
            this.limitInfo = JSON.parse(this.form.limitInfo)
            this.titleFileList.push({ url: this.limitInfo.titlePic })
            this.answerFileList.push({ url: this.limitInfo.answerPic })
          }
        }
        _this.formLoading = false
      })
    }
  },
  methods: {
    titlePicSuccess(url) {
      this.limitInfo.titlePic = url
    },
    titlePicRemove() {
      this.limitInfo.titlePic = ''
    },
    answerPicSuccess(url) {
      this.limitInfo.answerPic = url
    },
    answerPicRemove() {
      this.limitInfo.answerPic = ''
    },
    handleJsonSuccess(url, fileName) {

      this.answer[0].label = fileName
      this.answer[0].value = url
    },
    handleJsonRemove() {
      this.answer[0].label = ''
      this.answer[0].value = ''
    },
    handleAudioSuccess(url, fileName) {
      this.form.expandInfo.audioInfo = url
      this.form.expandInfo.audioName = fileName
    },
    handleAudioRemove() {
      this.form.expandInfo.audioInfo = ''
      this.form.expandInfo.audioName = ''
    },
    handleVideoSuccess(url, fileName) {
      this.form.expandInfo.videoInfo = url
      this.form.expandInfo.videoName = fileName
    },
    handleVideoRemove() {
      this.form.expandInfo.videoInfo = ''
      this.form.expandInfo.videoName = ''
    },
    handleSuccess(url) {
      this.form.expandInfo.imgInfo = url
    },
    handleRemove() {
      this.form.expandInfo.imgInfo = ''
    },
    addOpenTimerData() {
      let items = this.form.openTimerData
      let p = this.operatorList[items.length].value
      items.push({ min: '', max: '', operator: p, score: '' })
    },
    initGrade() {
      subjectApi.gradeListAll().then(res => {
        this.gradeList = res.data
      })
    },
    initSubject() {
      subjectApi.pageList().then(res => {
        this.subjectList = res.data.data
      })
    },

    // 获取知识点树
    getKonwTree() {
      questionApi.konwledgeTree(this.form.gradeId, this.form.subjectId).then(re => {
        this.konwledgeTree = re.data
      })
    },
    levelChange() {
      this.form.subjectId = null
      this.subjectFilter = this.subjectList.filter(data => data.gradeId === this.form.gradeId)
    },
    editorReady(instance) {
      this.richEditor.instance = instance
      let currentContent = this.richEditor.object[this.richEditor.parameterName]
      this.richEditor.instance.setContent(currentContent)
      // 光标定位到Ueditor
      this.richEditor.instance.focus(true)
    },
    inputClick(object, parameterName) {
      this.richEditor.object = object
      this.richEditor.parameterName = parameterName
      this.richEditor.dialogVisible = true
    },
    editorConfirm() {
      let content = this.richEditor.instance.getContent()
      this.richEditor.object[this.richEditor.parameterName] = content
      this.richEditor.dialogVisible = false
    },
    submitForm() {
      let _this = this
      if (this.limitInfo.titlePic === '') {
        return _this.$message.error('请上传题目示例图！')
      }
      if (this.limitInfo.answerPic === '') {
        return _this.$message.error('请上传答案示例图！')
      }
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.formLoading = true
          delete this.form.createTime
          delete this.form.createUserCode
          delete this.form.createUserRole
          if (this.form.knowledgePoints) {
            this.form.knowledgePoints = this.form.knowledgePoints.join(',')
          }
          this.form.limitInfo = JSON.stringify(this.limitInfo)
          this.form.answer = JSON.stringify(this.answer)
          questionApi.edit(this.form).then(re => {
            if (re.success) {
              _this.$message.success(re.message)
              _this.$router.push({ path: '/paper/index' })
            } else {
              _this.$message.error(re.message)
              this.formLoading = false
            }
          }).catch(e => {
            this.formLoading = false
          })
        } else {
          return false
        }
      })
    },
    resetForm() {
      let lastId = this.form.id
      this.$refs['form'].resetFields()
      this.answer = [
        { label: '', value: '' }
      ]
      this.titleFileList = []
      this.answerFileList = []
      this.limitInfo = {
        titlePic: '',
        answerPic: ''
      }
      this.jsonFileList = []
      this.fileList = []
      this.videoFileList = []
      this.audioFileList = []
      this.form = {
        id: null,
        questionType: 'ICON_MOVE',
        isSub: false,
        gradeId: null,
        subjectId: null,
        title: '',
        customInfo: null,
        analysis: '',
        answer: '',
        score: undefined,
        mediaType: 'NO',
        difficulty: 0,
        dxSource: 'PAPER',
        status: 1,
        knowledgePoints: null,
        openTimer: false,
        openTimerData: [
          { min: '', max: '', operator: '+', score: '' }
        ],
        expandInfo: {
          titleFlag: false,
          titleInfo: '',
          imgFlag: false,
          imgInfo: '',
          videoFlag: false,
          videoInfo: '',
          videoName: '',
          audioFlag: false,
          audioInfo: '',
          audioName: ''
        },
        limitInfo: ''
      }
      this.form.id = lastId
    },

    showQuestion() {
      this.questionShow.dialog = true
      this.questionShow.qType = this.form.questionType
      this.questionShow.question = this.form
    }
  },
  computed: {
    ...mapGetters('enumItem', ['enumFormat', 'subjectFormat']),
    ...mapState('enumItem', {
      // gradeList: state => state.question.gradeList,
      questionType: state => state.question.typeList,
      editUrlEnum: state => state.question.editUrlEnum,
      operatorList: state => state.question.operatorList
    })
  }
}
</script>

<style lang="less" scoped>
.question-item-label {
  display: flex;
  margin-bottom: 12px;
}

.q-dailog {
  /deep/ .el-dialog__header {
    border-bottom: 1px solid #dfdfdf;
  }
}
</style>
