import request from '@/utils/request'

// 上传视频之前获取 token
export function getUploadTokenAPI() {
  return request({
    url: '/media/web/video/getUploadToken',
    method: 'get',
  });
}

// 保存修改
export function saveAPI(data) {
  return request({
    url: '/media/web/video/save',
    method: 'post',
    data: data
  });
}

//  根据 vid 查询视频相关信息
export function detailAPI(vid) {
  return request({
    url: `/media/web/math/video/details/${vid}`,
    method: 'get',
  });
}

