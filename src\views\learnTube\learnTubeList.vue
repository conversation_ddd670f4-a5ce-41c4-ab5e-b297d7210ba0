<template>
  <div class="app-container">
    <!--查询  -->
    <el-row>
      <el-input style="width:200px;margin-right: 15px" v-model="tableQuery.loginName" placeholder="登录名"></el-input>
      <el-input style="width:200px;margin-right: 15px" v-model="tableQuery.realName" placeholder="用户姓名"></el-input>
      <span style="margin-right: 15px;"></span>
      <el-tooltip class="item" content="搜索" placement="top">
        <el-button icon="el-icon-search" circle @click="fetchData1()"></el-button>
      </el-tooltip>
      <el-button v-if="checkPermission(['b:learnTube:learnTubeList:add'])" type="primary" icon="el-icon-plus" size="mini"
        @click="handleCreate">创建学管师
      </el-button>
    </el-row>
    <div style="margin-bottom: 30px;"></div>
    <!--    <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate" v-perm="'b:user:add'">-->
    <!--      {{ textMap.create }}-->
    <!--    </el-button>-->
    <div style="margin-bottom: 30px;"></div>
    <!--列表-->
    <el-table style="width: 100%" :data="tableData" v-loading.body="tableLoading" element-loading-text="加载中" border fit
      highlight-current-row>
      <el-table-column prop="merchantCode" label="用户id" width="120"></el-table-column>
      <el-table-column prop="name" label="登录名" width="150"></el-table-column>
      <el-table-column label="操作">
        <template slot-scope="scope">
          <el-button type="success" icon="el-icon-edit-outline" size="mini"
            v-if="checkPermission(['b:learnTube:learnTubeList:edit'])"
            @click="handleUpdate(scope.$index, scope.row, false)">编辑</el-button>
          <el-button type="success" icon="el-icon-edit-outline" size="mini"
            v-if="checkPermission(['b:learnTube:learnTubeList:edit']) && scope.row.flowIsEnd == 1 && scope.row.flowEndStatus == 0"
            @click="handleUpdate(scope.$index, scope.row, true)">重新提交</el-button>
          <el-button type="warning" size="mini" icon="el-icon-switch-button"
            v-if="scope.row.isEnable === 0 && scope.row.flowEndStatus === 1"
            @click="updataStatus(scope.row.merchantCode, scope.row.isEnable)">开通</el-button>
          <el-button type="success" icon="el-icon-edit-outline" size="mini"
            v-if="checkPermission(['b:learnTube:learnTubeList:edit'])"
            @click="handleUpdate(scope.$index, scope.row)">编辑</el-button>
          <el-button type="warning" size="mini" icon="el-icon-switch-button"
            v-if="(scope.row.isEnable === 0 && scope.row.flowEndStatus === 1) || scope.row.isEnable === -3"
            @click="updataStatus(scope.row.merchantCode, 1)">开通</el-button>
          <el-button type="danger" size="mini" icon="el-icon-video-pause"
            v-if="scope.row.isEnable === 1 && scope.row.isEnable === 1"
            @click="updataStatus(scope.row.merchantCode, 0)">暂停</el-button>
          <el-button type="danger" size="mini" icon="el-icon-video-pause"
            v-if="checkPermission(['b:learnTube:learnTubeList:termination']) && scope.row.isEnable === 1 && scope.row.isEnable === 1"
            @click="updataStatus(scope.row.merchantCode, -3)">终止</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="realName" label="用户姓名" width="150"></el-table-column>
      <!--      <el-table-column prop="merchantName" label="商户名" width="310"></el-table-column>-->
      <!--      <el-table-column label="角色">
        <template slot-scope="scope">
          <el-tag style="margin: 2px;" v-for="role in scope.row.roles" :key="role.val">{{ role.name }}</el-tag>
        </template>
      </el-table-column>-->
      <el-table-column prop="flowIsEnd" label="审核状态">
        <template slot-scope="scope">
          <span class="green" v-if="scope.row.flowIsEnd === 1">审核流程结束</span>
          <span class="red" v-if="scope.row.flowIsEnd === 0">审核中</span>
        </template>
      </el-table-column>
      <el-table-column prop="flowStatus" label="流程状态"></el-table-column>
      <el-table-column prop="isEnable" label="账户状态" align="center">
        <template slot-scope="scope">
          <span class="green" v-if="scope.row.isEnable === 1">开通</span>
          <span class="red" v-else-if="scope.row.isEnable === -3">终止</span>
          <span class="red" v-else-if="scope.row.isEnable === 0">暂停</span>
        </template>
      </el-table-column>
      <el-table-column prop="time" label="创建时间">
        <template slot-scope="scope">
          <span v-text="parseTime(scope.row.createTime)"></span>
        </template>
      </el-table-column>
      <!-- <el-table-column prop="time" label="更新时间">
        <template slot-scope="scope">
          <span v-text="parseTime(scope.row.updated)"></span>
        </template>
      </el-table-column> -->
      
    </el-table>
    <div style="margin-bottom: 30px;"></div>
    <!--分页-->
    <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
      :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
      layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems">
    </el-pagination>
    <!--弹出窗口：新增/编辑用户-->
    <el-dialog :title="textStudyMap[dialogStatus]" :visible.sync="dialogFormVisible" width="60%"
      :close-on-click-modal='false' @close="close">
      <el-form :rules="rules" ref="dataForm" :model="temp" label-position="left" label-width="120px">

        <el-form-item label="手机号" prop="mobile">
          <el-input v-model="temp.mobile" :disabled="dialogStatus != 'create'"></el-input>
        </el-form-item>
        <el-form-item label="真实姓名" prop="realName">
          <el-input v-model="temp.realName"></el-input>
        </el-form-item>
        <el-form-item label="密码" prop="password" v-if="dialogStatus == 'create'">
          <el-input type="password" v-model="temp.password" auto-complete="new-password"></el-input>
        </el-form-item>

        <el-form-item label="确认密码" prop="password2" v-if="dialogStatus == 'create'">
          <el-input type="password" v-model="temp.password2"></el-input>
        </el-form-item>
        <el-form-item label="学管师身份证号：" prop="idCard">
          <el-col :xs="24" :span="18">
            <el-input v-model="temp.idCard" />
          </el-col>
        </el-form-item>
        <el-form-item label="学管师身份证照片:" prop="idCardPhoto">
          <el-col :span="20">
            <el-upload ref="clearupload" v-loading="uploadLoadingIdCard" list-type="picture-card" action=""
              element-loading-text="图片上传中" :limit="10" :on-exceed="justPictureNumIdCard" :file-list="fileList"
              :http-request="uploadIdCardDetailHttp" :on-preview="handlePictureCardPreview"
              :on-remove="handleRemoveDetailIdCard">
              <i class="el-icon-plus" />
            </el-upload>
          </el-col>
          <el-col :xs="24" :span="4">(*支持多张)</el-col>
        </el-form-item>
        <el-form-item label="所在地区：" prop="name">
          <el-col :xs="24" :span="18">
            <el-row :gutter="10">
              <el-col :xs="24" :span="8">
                <el-input placeholder="安徽省" disabled v-if="addOrUpdate" v-model="temp.province" />
                <el-input placeholder="安徽省" v-if="!addOrUpdate" disabled v-model="updateTemp.province" />
              </el-col>
              <el-col :xs="24" :span="8">
                <el-input placeholder="合肥市" v-if="addOrUpdate" disabled v-model="temp.city" />
                <el-input placeholder="合肥市" v-if="!addOrUpdate" disabled v-model="updateTemp.city" />
              </el-col>
              <el-col :xs="24" :span="8">
                <el-input placeholder="包河区" v-if="addOrUpdate" disabled v-model="temp.area" />
                <el-input placeholder="包河区" v-if="!addOrUpdate" disabled v-model="updateTemp.area" />
              </el-col>
            </el-row>
          </el-col>
        </el-form-item>
        <el-form-item label="详细地址" prop="address">
          <el-col :xs="24" :span="18">
            <el-input v-model="temp.address" />
          </el-col>
        </el-form-item>

        <el-form-item label="地图标记：" prop="isEnable">
          <el-col :span="24">
            <div class="amap-page-container">
              <div :style="{ width: '100%', height: '450px' }" class="map-box">
                <el-amap-search-box class="search-box" :search-option="searchOption"
                  :on-search-result="onSearchResult"></el-amap-search-box>
                <el-amap vid="amap" :plugin="plugin" :center="center" class="amap-demo" :events="events">
                  <el-amap-circle v-for="(circle, index) in circles" :key="index" :center="circle.center"
                    :radius="circle.radius" :fill-opacity="circle.fillOpacity" :events="circle.events"></el-amap-circle>
                  <!-- 定位点标注 -->
                  <el-amap-marker vid="component-marker" :position="center"></el-amap-marker>
                  <!-- 搜索结果标注 -->
                  <el-amap-marker v-for="(marker, index) in markers2" :key='index' :position="marker.position"
                    :events="marker.events"></el-amap-marker>
                  <el-amap-info-window v-if="window" :position="window.position" :visible="window.visible"
                    :content="window.content"></el-amap-info-window>
                </el-amap>
                <!-- 搜索结果右侧列表 -->
                <div class="result" v-if="result != ''">
                  <el-table class="search-table" :data="result.slice(
                    (this.pageNum - 1) * this.pageSize,
                    this.pageNum * this.pageSize
                  )
                    " style="margin-bottom: 20px">
                    <el-table-column prop="name,address">
                      <template slot-scope="scope">
                        <div class="result-list" @click="
                          markList(
                            scope.row.lng,
                            scope.row.lat,
                            scope.$index
                          )
                          " :class="[
    currentResult == scope.$index ? 'active' : '',
  ]">
                          <label>{{ scope.$index + 1 }}</label>
                          <div class="list-right">
                            <div class="name">{{ scope.row.name }}</div>
                            <div class="address">{{ scope.row.address }}</div>
                          </div>
                        </div>
                      </template>
                    </el-table-column>
                  </el-table>
                  <el-pagination background :current-page.sync="pageNum" :page-size="pageSize"
                    layout="total,prev, pager, next" :total="result.length" @current-change="handleCurrentChange"
                    @size-change="changeSizeHandler" size="small" style="text-align: right">
                  </el-pagination>
                </div>
              </div>
              <!-- <div class="toolbar">
            <span v-if="loaded">
              location: lng = {{ lng }} lat = {{ lat }}
            </span>
            <span v-else>正在定位</span>
          </div>
          <div v-on:click="req_post()">查询周边</div> -->
            </div>
          </el-col>
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button v-if="dialogStatus == 'create'" type="primary" @click="createData">创建</el-button>
        <el-button v-else-if="!isReSubmit" type="primary" @click="updateData">确定</el-button>
        <el-button v-else type="primary" @click="updateData">重提</el-button>
      </div>
    </el-dialog>
    <!--弹出窗口：修改用户角色-->
    <el-dialog title="修改用户的角色" :visible.sync="editRolesDialogVisible" width="60%" :close-on-click-modal='false'>
      <div>
        <el-checkbox :indeterminate="isIndeterminate" v-model="checkAll" @change="handleCheckAllChange">全选</el-checkbox>
        <div style="margin: 15px 0;"></div>
        <el-checkbox-group v-model="updateUserRolesData.rids">
          <el-checkbox class="role-checkbox" v-for="role in roleOptions" :label="role.id" :key="role.id">
            {{ role.val }}
          </el-checkbox>
        </el-checkbox-group>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="editRolesDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="checkUpdateUserRolesData">确定</el-button>
      </div>
    </el-dialog>
    <el-dialog :visible.sync="dialogUploadVisible" :close-on-click-modal="false">
      <img width="100%" :src="dialogImageUrl" alt />
    </el-dialog>
  </div>
</template>

<script>

import optionApi from '@/api/option'
import userApi from '@/api/user'
import { parseTime, resetTemp } from '@/utils'
import { root, confirm, pageParamNames } from '@/utils/constants'
import debounce from 'lodash/debounce'
import bvadminAPi from '@/api/bvAdminApi'
import { ossPrClient, sendSmsCode } from "@/api/alibaba";
import learnTubeApi from "@/api/learnTube/learnTube";
import flowOnlineOperationApi from "@/api/activiti/flowOnlineOperation";
import checkPermission from '@/utils/permission'
import { isvalidPhone } from "@/utils/validate";


export default {

  name: 'studycommitte',

  data() {

    //手机号验证
    var validPhone = (rule, value, callback) => {
      if (!value) {
        callback(new Error("请输入电话号码"));
      } else if (!isvalidPhone(value)) {
        callback(new Error("请输入正确的11位手机号码"));
      } else {
        callback();
      }
    };

    let validateName = (rule, value, callback) => {
      if (this.dialogStatus == 'create' && value === '') {
        callback(new Error('必填'));
      } else {
        callback();
      }
    };

    let validatePass = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请输入密码'));
      } else {
        if (this.temp.password2 !== '') {
          this.$refs.dataForm.validateField('password2');
        }
        callback();
      }
    };

    let validatePass2 = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请再次输入密码'));
      } else if (value !== this.temp.password) {
        callback(new Error('两次输入密码不一致!'));
      } else {
        callback();
      }
    };
    const self = this;
    return {
      isReSubmit: false,
      addOrUpdate: true,
      fileList: [],
      buttonName: "发送短信",
      isSmsDisabled: false,
      time: 60,
      uploadLoadingIdCard: false,
      window: "",
      result: [],
      markers2: [],
      // 地图搜索分页相关参数
      pageNum: 1,
      pageSize: 5,
      currentResult: -1,
      parseTime: parseTime,
      tableLoading: false,
      searchOption: {
        city: "全国",
        citylimit: false, //是否限制城市内搜索
      },
      dialogImageUrl: '',
      disabled: true,
      tableData: [],
      tableQuery: {
        loginName: null,
        realName: null,
        merchantName: null
      },
      // tablePage: {
      //   current: null,
      //   pages: null,
      //   size: null,
      //   total: null
      // },
      dialogUploadVisible: false,
      tablePage: {
        currentPage: 1,
        totalPage: null,
        size: 10,
        totalItems: null
      },
      dialogFormVisible: false,
      editRolesDialogVisible: false,
      dialogStatus: '',
      // temp: {
      //   idx: null, //tableData中的下标
      //   uid: null,
      //   uname: null,
      //   nick: null,
      //   pwd: null,
      //   pwd2: null,
      //   created: null,
      //   updated: null
      // },
      temp: {
        id: null, //tableData中的下标
        mobile: null,
        smsCode: '',
        password: null,
        realName: null,
        latitude: 0,
        longitude: 0,
        address: '',
        city: '',
        area: '',
        province: '',
        idCardFilePaths: []
      },
      password2: null,       //新建用户确认密码
      textMap: {
        update: '查看用户',
        create: '新增用户'
      },
      textStudyMap: {
        update: '编辑学管师信息',
        create: '新增学管师'
      },
      rules: {
        mobile: [{ required: true, validator: validPhone, trigger: "blur" }],
        smsCode: [{ required: true, message: "必填", trigger: "blur" }],
        realName: [{ required: true, message: "必填", trigger: "blur" }],
        idCard: [{ required: true, message: "必填", trigger: "blur" }],
        password: [{ required: true, message: "必填", trigger: "blur" }],
        password2: [{ required: true, validator: validatePass2, trigger: 'blur' }],
        address: [{ required: true, message: "必填", trigger: "blur" }],
      },
      checkAll: false,
      isIndeterminate: true,
      //所有角色(管理员除外)
      roleOptions: [],
      roleMap: new Map(),
      // 更新用户的角色的数据
      updateUserRolesData: {
        idx: null,
        id: null,
        rids: []
      },
      currentWindow: {
        position: [0, 0],
        content: "",
        events: {},
        visible: false,
      },
      center: [117.26696, 31.87869],
      zoom: 12,
      lng: 0,
      lat: 0,
      circles: [
        {
          center: [117.26696, 31.87869],
          radius: 100,
          fillOpacity: 0.5,
          events: {
            click(e) {
              let { lng, lat } = e.lnglat;
              self.lng = lng;
              self.lat = lat;
              console.log(e);
              // 这里通过高德 SDK 完成。
            },
          },
        },
      ],
      events: {
        click(e) {
          let { lng, lat } = e.lnglat;
          self.lng = lng;
          self.lat = lat;
          console.log(e);
          // 这里通过高德 SDK 完成。
          var geocoder = new AMap.Geocoder({
            radius: 1000,
            extensions: "all",
          });
          self.showWindow = false;
          geocoder.getAddress(
            [e.lnglat.lng, e.lnglat.lat],
            function (status, result) {
              if (status === "complete" && result.info === "OK") {
                if (result && result.regeocode) {
                  if (self.dialogStatus == 'create') {
                    // 具体地址
                    self.temp.longitude = lng;
                    self.temp.latitude = lat;
                    self.temp.address =
                      result.regeocode.formattedAddress;
                    self.temp.address = result.regeocode.formattedAddress;
                    // 省
                    self.temp.province =
                      result.regeocode.addressComponent.province;
                    //       if(result.regeocode.addressComponent.province=='重庆市' || result.regeocode.addressComponent.province=='天津市' || result.regeocode.addressComponent.province=='北京市' || result.regeocode.addressComponent.province=='上海市'){
                    //    self.addMarketDate.city =
                    //   result.regeocode.addressComponent.province;
                    //  }else{
                    // // 市
                    //  self.addMarketDate.city =
                    //   result.regeocode.addressComponent.city;
                    //  }
                    var reg = RegExp(/省/);
                    if (
                      self.temp.province.match(reg) &&
                      result.regeocode.addressComponent.city == ""
                    ) {
                      self.temp.city =
                        result.regeocode.addressComponent.district;
                    } else {
                      if (
                        result.regeocode.addressComponent.province ==
                        "重庆市" ||
                        result.regeocode.addressComponent.province ==
                        "天津市" ||
                        result.regeocode.addressComponent.province ==
                        "北京市" ||
                        result.regeocode.addressComponent.province ==
                        "上海市"
                      ) {
                        self.temp.city =
                          result.regeocode.addressComponent.province;
                      } else {
                        // 市
                        self.temp.city =
                          result.regeocode.addressComponent.city;
                      }
                    }
                    // if (result.regeocode.addressComponent.city == "") {
                    //   self.addMarketDate.city =
                    //     result.regeocode.addressComponent.province;
                    // }
                    // // 市
                    // self.addMarketDate.city =
                    //   result.regeocode.addressComponent.city;
                    // 区
                    self.temp.area =
                      result.regeocode.addressComponent.district;
                  } else {
                    self.temp.latitude = lat;
                    self.temp.longitude = lng;
                    self.temp.address =
                      result.regeocode.formattedAddress;
                    self.temp.address = result.regeocode.formattedAddress;
                    // 省
                    self.temp.province =
                      result.regeocode.addressComponent.province;

                    var reg = RegExp(/省/);
                    if (
                      self.temp.province.match(reg) &&
                      result.regeocode.addressComponent.city == ""
                    ) {
                      self.temp.city =
                        result.regeocode.addressComponent.district;
                    } else {
                      if (
                        result.regeocode.addressComponent.province ==
                        "重庆市" ||
                        result.regeocode.addressComponent.province ==
                        "天津市" ||
                        result.regeocode.addressComponent.province ==
                        "北京市" ||
                        result.regeocode.addressComponent.province ==
                        "上海市"
                      ) {
                        self.temp.city =
                          result.regeocode.addressComponent.province;
                      } else {
                        // 市
                        self.temp.city =
                          result.regeocode.addressComponent.city;
                      }
                    }
                    // if (result.regeocode.addressComponent.city == "") {
                    //   self.updateMarketDate.city =
                    //     result.regeocode.addressComponent.province;
                    // }
                    // // 市
                    // self.updateMarketDate.city =
                    //   result.regeocode.addressComponent.city;
                    // 区
                    self.temp.area =
                      result.regeocode.addressComponent.district;
                  }
                  self.$nextTick();
                }
              } else {
                alert("地址获取失败");
              }
            }
          );
        },
      },
      init: (o) => {
        this.$nextTick(() => {
          // 获取当前城市的城市名
          let geocoder = new AMap.Geocoder({
            radius: 1000,
            extensions: "all",
          });
          geocoder.getAddress(
            [o.Ce.center.lng, o.Ce.center.lat],
            (status, result) => {
              if (status === "complete" && result.info === "OK") {
                if (result && result.regeocode) {
                  console.log(result);
                  if (self.dialogStatus == 'create') {
                    self.center = [o.Ce.center.lng, o.Ce.center.lat];
                    // self.searchOption.city = result.regeocode.addressComponent.city
                  }
                }
              }
            }
          );
        });
      },
      plugin: [
        {
          enableHighAccuracy: true, //是否使用高精度定位，默认:true
          timeout: 100, //超过10秒后停止定位，默认：无穷大
          maximumAge: 0, //定位结果缓存0毫秒，默认：0
          convert: true, //自动偏移坐标，偏移后的坐标为高德坐标，默认：true
          showButton: true, //显示定位按钮，默认：true
          buttonPosition: "RB", //定位按钮停靠位置，默认：'LB'，左下角
          showMarker: true, //定位成功后在定位到的位置显示点标记，默认：true
          showCircle: true, //定位成功后用圆圈表示定位精度范围，默认：true
          panToLocation: true, //定位成功后将定位到的位置作为地图中心点，默认：true
          zoomToAccuracy: true, //定位成功后调整地图视野范围使定位位置及精度范围视野内可见，默认：f
          extensions: "all",
          expandZoomRange: true,
          keyboardEnable: true,
          pName: "Geolocation",
          campus: [],
          events: {
            init(o) {
              // o 是高德地图定位插件实例
              o.getCityInfo((status, result) => {
                if (result != null && result.center != null && result.center.length > 0) {
                  self.lng = result.center[0];
                  self.lat = result.center[1];
                } else {
                  self.lng = 117.283042;
                  self.lat = 31.86119;
                }
                self.center = [self.lng, self.lat];
                self.loaded = true;
                self.$nextTick();
              });
            },
          },
        },
      ],
    }

  },

  created() {
    ossPrClient();
    this.initData()
    this.fetchData()
  },

  watch: {
    //延时查询
    'tableQuery.nick': debounce(function () {
      this.fetchData()
    }, 500)
  },//watch

  methods: {
    checkPermission,
    initData() {
      //所有角色选项
      var that = this;
      optionApi.listRoleOptions().then(res => {
        res.data.forEach(obj => {
          //
          if (obj.value != root.rval) {//排除管理员
            var rd = { "id": obj.value, "val": obj.label }
            that.roleOptions.push(rd)
            that.roleMap.set(obj.value, obj.label)
          }
        })
      })
    },
    onSearchResult(pois) {
      let latSum = 0;
      let lngSum = 0;
      if (pois.length > 0) {
        pois.forEach((poi) => {
          let { lng, lat } = poi;
          lngSum += lng;
          latSum += lat;
        });
        let center = {
          lng: lngSum / pois.length,
          lat: latSum / pois.length,
        };
        this.center = [center.lng, center.lat];
      }
    },
    hasAdminRole(row) {
      if (row && row.roles) {
        return row.roles.some(role => role.rval == root.rval)
      }
      return false
    },

    //全选
    handleCheckAllChange(val) {
      let allRids = this.roleOptions.map(role => role.id)
      this.updateUserRolesData.rids = val ? allRids : [];
      this.isIndeterminate = false;
    },

    //分页
    handleSizeChange(val) {
      this.tablePage.size = val;
      this.fetchData();
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val;
      this.fetchData();
    },
    changeSizeHandler(size) {
      this.pageSize = size;
    },
    // 标注列表
    markList(lng, lat, index) {
      if (this.currentResult != index) {
        this.currentResult = index;
      } else {
        this.currentResult = -1;
      }
      this.getMarkAddress(lng, lat, index);
    },
    //证件照片开始
    //证件照上传
    // 上传图片预览
    handlePictureCardPreview(file) {

      this.dialogImageUrl = file.url;
      this.dialogUploadVisible = true;
    },
    uploadIdCardDetailHttp({ file }) {
      this.uploadLoadingIdCard = true;
      const that = this;
      const fileName = "idcard/study/learnTube/" + Date.parse(new Date());
      that.$nextTick(function () {
        ossPrClient()
          .put(fileName, file)
          .then(({ res, url, name }) => {
            if (res && res.status === 200) {
              console.log(`阿里云OSS上传图片成功回调1`, res, url, name);
              if (!that.temp.idCardFilePaths) {
                that.temp.idCardFilePaths = []
              }
              this.fileList.push({ uid: file.uid, url: url })
              that.temp.idCardFilePaths.push(name);
              that.$nextTick(() => {
                that.uploadLoadingIdCard = false;
              });
            }
          })
          .catch((err) => {
            that.$message.error("上传图片失败请检查网络或者刷新页面");
            that.uploadLoadingIdCard = false
            console.log(`阿里云OSS上传图片失败回调`, err);
          });
      });
    },
    //照片限制
    // 上传图片数量超限
    justPictureNumIdCard(file, fileList) {
      this.$message.warning(`当前限制选择10个文件`);
    },
    handleRemoveDetailIdCard(file, fileList) {
      var index = this.fileList.findIndex(item => {
        if (item.uid === file.uid) {
          return true;
        }
      });
      this.fileList.splice(index, 1);
      this.temp.idCardFilePaths.splice(index, 1);
      console.log(this.fileList)
      console.log(this.temp)
    },
    //证件照片的结束
    //查询
    fetchData1() {
      this.tablePage = {
        currentPage: 1,
        size: this.tablePage.size,
        totalPage: null,
        totalItems: null
      }
      this.fetchData();
    },
    fetchData() {
      console.log(this.tablePage)
      console.log(this.tablePage.currentPage)
      console.log(this.tablePage.size)
      this.tableLoading = true
      learnTubeApi.listByDealer(this.tableQuery, this.tablePage.currentPage, this.tablePage.size).then(res => {
        this.tableData = res.data.data.reverse();
        this.tableLoading = false
        this.tablePage.currentPage = res.data.currentPage;
        this.tablePage.size = res.data.size;
        this.tablePage.totalItems = parseInt(res.data.totalItems);
        this.tablePage.totalPage = parseInt(res.data.totalPage);
        //pageParamNames.forEach(name => this.$set(this.tablePage, name, Number(res.data[name])))
      })
    },
    bindCheck() {
      console.log(this.updateUserRolesData);
    },
    //更新
    handleUpdate(idx, row, isReSubmit) {
      let that = this;
      this.fileList = [];
      this.isReSubmit = isReSubmit;
      learnTubeApi.detailByMerchantCode(row.merchantCode).then((res) => {
        that.temp = res.data;
        that.temp.mobile = res.data.name;
        if (res.data.idCardPhoto && res.data.idCardPhoto.length > 0) {
          if (!that.temp.idCardFilePaths) {
            that.temp.idCardFilePaths = [];
            that.fileList = [];
          }
          for (let i = 0; i < res.data.idCardPhoto.length; i++) {
            that.fileList.push({ 'uid': i, 'url': 'https://document.dxznjy.com/' + res.data.idCardPhoto[i] });
            that.temp.idCardFilePaths.push(res.data.idCardPhoto[i]);
          }
        }
      })
      this.temp = Object.assign({}, row) // copy obj
      // this.temp.idx = idx
      this.temp.password = null
      this.temp.password2 = null
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => this.$refs['dataForm'].clearValidate())
    },
    updataStatus(merchantCode, isEnable) {
      const that = this;
      learnTubeApi.updataStatus(merchantCode, isEnable).then(res => {
        if (res.success) {
          that.$message.success("修改成功");
          this.fetchData()
        }
      }).catch(err => { })
    },
    updateData() {
      let that = this;
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) return;
        if (that.temp.idCardFilePaths == undefined || that.temp.idCardFilePaths.length <= 0) {
          that.$message.error("请上传身份证照片！")
          return;
        }
        if (that.temp.province === null || that.temp.province === '' || that.temp.city === null || that.temp.city === '' || that.temp.area === null || that.temp.area === '') {
          that.$message.error("请选择地址！")
          return;
        }
        learnTubeApi.update(that.temp).then((res) => {
          if (res.success) {
            //重新提交,添加学管师工作流
            if (this.isReSubmit) {
              flowOnlineOperationApi.startAndTakeUserTask("openLearnTube",
                {
                  "approvalType": "agree",
                  "masterData": {
                    "open_type": "LearnTube",
                    "relation_id": res.data,
                    "create_time": this.dateFormat(new Date(), 'yyyy-mm-dd hh:MM:ss')
                  }
                }).then((res) => {
                  that.fetchData();
                  that.$message.success("学管师审批流程开启")
                }).catch((err) => {
                  loading.close();
                });
            }

            that.dialogFormVisible = false
            that.$message.success("修改成功");
            that.fetchData()
          }
        })
      })
    },


    //更新用户的角色
    handleUpdateUserRoles(idx, row) {
      // 显示用户的角色
      this.updateUserRolesData = {
        idx: idx,
        id: row.id,
        rids: row.roles.map(role => role.val)
      }
      // 显示弹窗
      this.editRolesDialogVisible = true
    },

    checkUpdateUserRolesData() {
      const noRolesSelected = this.updateUserRolesData && this.updateUserRolesData.rids && this.updateUserRolesData.rids.length == 0;

      if (noRolesSelected) {
        this.$confirm('当前没有选中任何角色，会清除该用户已有的角色, 是否继续?', '提示', confirm).then(() => {
          this.invokeUpdateUserRolesApi()
        }).catch(() => {
          this.$message("已取消编辑用户角色");
        });
      } else {
        this.invokeUpdateUserRolesApi()
      }
    },
    invokeUpdateUserRolesApi() {
      // console.log('修改用户角色')
      // console.log(this.updateUserRolesData.rids.length)
      // if (this.updateUserRolesData.rids.length >= 2) {
      //   this.$message.info("只限制选择一个用户角色");
      //   return false;
      // }
      userApi.updateUserRoles(this.updateUserRolesData).then(res => {
        let newRoles = this.updateUserRolesData.rids.map(rid => {
          let rname = this.roleMap.get(rid);
          if (rname) return { rid, rname }
        })
        this.tableData[this.updateUserRolesData.idx].roles = newRoles
        this.editRolesDialogVisible = false
        this.fetchData()
        this.$message.success("更新成功")
      })
    },

    //删除
    handleDelete(idx, row) {
      this.$confirm('您确定要永久删除该用户？', '提示', confirm).then(() => {
        userApi.deleteUser({ id: row.id }).then(res => {
          this.tableData.splice(idx, 1)
          --this.tablePage.totalItems
          this.dialogFormVisible = false
          this.$message.success("删除成功")
        })
      }).catch(() => {
        this.$message.info("已取消操作")
      });

    },

    //新增
    handleCreate() {
      resetTemp(this.temp)
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    dateFormat(date, fmt) {
      let ret;
      const opt = {
        "y+": date.getFullYear().toString(),        // 年
        "m+": (date.getMonth() + 1).toString(),     // 月
        "d+": date.getDate().toString(),            // 日
        "h+": date.getHours().toString(),           // 时
        "M+": date.getMinutes().toString(),         // 分
        "s+": date.getSeconds().toString()          // 秒
        // 有其他格式化字符需求可以继续添加，必须转化成字符串
      };
      for (let k in opt) {
        ret = new RegExp("(" + k + ")").exec(fmt);
        if (ret) {
          fmt = fmt.replace(ret[1], (ret[1].length == 1) ? (opt[k]) : (opt[k].padStart(ret[1].length, "0")))
        };
      };
      return fmt;
    },
    createData() {
      let that = this;
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) return;
        if (that.temp.idCardFilePaths == undefined || that.temp.idCardFilePaths.length <= 0) {
          that.$message.error("请上传身份证照片！")
          return;
        }
        if (that.temp.province === null || that.temp.province === '' || that.temp.city === null || that.temp.city === '' || that.temp.area === null || that.temp.area === '') {
          that.$message.error("请输入地址！")
          return;
        }
        learnTubeApi.saveNew(that.temp).then(response => {

          this.$message.success("提交成功！");
          that.dialogFormVisible = false;
          //添加学管师工作流
          flowOnlineOperationApi.startAndTakeUserTask("openLearnTube",
            {
              "approvalType": "agree",
              "masterData": {
                "open_type": "LearnTube",
                "relation_id": response.data,
                "create_time": this.dateFormat(new Date(), 'yyyy-mm-dd hh:MM:ss')
              }
            }).then((res) => {
              that.fetchData();
              that.$message.success("学管师审批流程开启")
            }).catch((err) => {
              loading.close();
            });
          this.$emit("ok");
        });
        // bvadminAPi.createStudyCommitteeMan(that.temp).then((res) => {
        //   if (res.success) {
        //     that.dialogFormVisible = false
        //     that.$message.success("添加成功")
        //     that.fetchData()
        //   }
        // })
      })
    },
    close() {
      this.temp.idCardFilePaths = []
      this.fileList = [];
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.role-checkbox {
  margin-left: 0px;
  margin-right: 15px;
}
</style>
