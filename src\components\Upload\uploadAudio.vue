<template>
  <div>
    <el-upload
      class="upload"
      ref="upload"
      action=""
      :show-file-list="false"
      :on-success="handleSuccess"
      :http-request="uploadVideoHandle"
      :file-list="tableData"
      :limit="1"
      :before-upload="validateAndUploadVideo"
      :accept="videoAcceptTypes"
      :on-exceed="doUpload"
    >
      <el-button slot="trigger" type="primary" size="small">选取文件</el-button>
      <div slot="tip" class="el-upload__tip">支持格式：MP3</div>
    </el-upload>

    <!-- 展示上传进度等信息的表格 -->
    <el-table :data="tableData" border style="width: 100%">
      <el-table-column prop="id" label="ID" align="center"></el-table-column>
      <el-table-column prop="fileName" label="文件名" align="center"></el-table-column>
      <el-table-column label="进度" align="center">
        <template slot-scope="scope">
          <div>
            <el-progress :text-inside="true" :stroke-width="15" :percentage="scope.row.progress"></el-progress>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="progress" label="操作" align="center">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="remove(scope.row.id, scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
<script>
  import { ossPrClient } from '@/api/alibaba';
  import { parseBlob } from 'music-metadata-browser';
  export default {
    name: 'VideoUpload',
    props: {
      videoShow: {
        type: Boolean,
        default: false
      },
      videoTableData: {
        type: Array,
        default: false
      }
    },
    watch: {
      videoTableData: {
        immediate: true,
        handler(value) {
          this.tableData = [...value];
        }
      },
      videoShow: function (value) {
        if (!value) this.tableData = [];
        this.$refs.upload.value = '';
      }
    },
    data() {
      return {
        tableData: [],
        duration: '', // 所有音频时长
        // 可接受的音频文件类型
        videoAcceptTypes: 'audio/mpeg',
        // 音频文件大小限制，示例设为500MB
        videoSizeLimit: 500 * 1024 * 1024
      };
    },
    created() {
      ossPrClient();
    },
    methods: {
      doUpload() {
        if (this.tableData.length === 0) return;
        this.$message.error('只能上传一个音频，请先删除已有的音频');
        return false;
      },

      validateAndUploadVideo(file) {
        if (!this.isVideoFormatValid(file) || file.type === '') {
          this.$message.error('只能上传 MP3 格式的音频，请转换后进行上传');
          return false;
        }
        if (file.size > this.videoSizeLimit) {
          this.$message.error(`上传的音频大小不能超过 ${this.getVideoSizeLimitText()}`);
          return false;
        }
      },
      isVideoFormatValid(file) {
        const acceptedVideoTypes = this.videoAcceptTypes.split(',');

        return acceptedVideoTypes.includes(file.type);
      },
      async getFileDuration(file) {
        if (file.type === 'audio/mpeg') {
          return new Promise((resolve, reject) => {
            const audio = new Audio();
            const fileURL = URL.createObjectURL(file); // 创建临时文件 URL
            audio.src = fileURL;

            audio.addEventListener('loadedmetadata', () => {
              this.duration = audio.duration.toFixed(3); // 获取音频时长（秒）
              URL.revokeObjectURL(fileURL); // 释放临时 URL
              resolve(this.duration);
            });
            console.log('audio.duration', this.duration);
            audio.addEventListener('error', (error) => {
              console.error('Error loading audio metadata:', error);
              URL.revokeObjectURL(fileURL); // 释放临时 URL
              reject(error);
            });
          });
        }
      },
      async uploadVideoHandle(file) {
        await this.getFileDuration(file.file);
        const newRow = {
          id: Date.now(),
          fileName: file.file.name,
          filePath: '',
          progress: 0,
          duration: this.duration
        };
        this.tableData.push(newRow);
        console.log(`时长数据时长数据时长数据时长数据时长数据时长数据`, this.tableData);
        let suf = file.file.name.substring(file.file.name.lastIndexOf('.'));
        const fileName = 'videos/' + Date.parse(new Date()) + suf;
        ossPrClient()
          .multipartUpload(fileName, file.file, {
            // 进度条的配置项
            progress: (percentage) => {
              this.$emit('uploadVideo', 'uploading');
              // 获取进度条的值
              this.tableData[0].progress = Math.floor(percentage * 100);
              if (this.tableData[0].progress === 100) this.$emit('uploadVideo', 'success');
            }
          })
          .then(({ res }) => {
            if (res && res.status === 200) {
              // 更新上传成功后的进度为100%
              const index = this.tableData.findIndex((item) => item.id === newRow.id);
              if (index !== -1) {
                const baseUrl = res.requestUrls[0].split('?')[0];
                this.tableData[index].filePath = baseUrl;
              }
              this.handleSuccess(this.tableData);
            }
          })
          .catch((err) => {
            this.$message.error('上传音频失败请检查网络或者刷新页面');
            console.log(`阿里云OSS上传音频失败回调`, err);
          });
      },
      // 上传成功
      handleSuccess(res) {
        this.$emit('videoSucceed', res || '');
      },
      remove(id, row) {
        console.log('row', row);
        const index = this.tableData.findIndex((item) => item.id === id);
        if (index !== -1) {
          this.tableData.splice(index, 1);
        }
        this.$emit('uploadVideo', 'delete');
        this.$emit('videoSucceed', this.tableData);
        this.$refs.upload.clearFiles();
      }
    }
  };
</script>

<style></style>
