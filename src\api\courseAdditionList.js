/**
 *自然拼写课程
 */
import request from '@/utils/request';
export default {
  pdCoursePage(data) {
    return request({
      url: `/znyy/pd/attach/course/list`,
      method: 'GET',
      params: data
    });
  },

  //课程启用禁用状态
  updateEnable(data) {
    return request({
      url: `/znyy/pd/attach/course/edit/status`,
      method: 'POST',
      data
    });
  },
  //编辑课程
  editCourse(data) {
    return request({
      url: `/znyy/pd/attach/course/edit`,
      method: 'POST',
      data
    });
  },

  //新增课程
  addCourse(data) {
    return request({
      url: `/znyy/pd/attach/course/add`,
      method: 'PUT',
      data
    });
  },
  /////////////////////////////////////////////////////////////制作课程///////////////////////////////////////////
  //单词分页查询
  searchWord(data) {
    return request({
      url: `/znyy/pd/attach/course/word/list`,
      method: 'GET',
      params: data
    });
  },

  ///增加单词
  addWord(data) {
    return request({
      url: `/znyy/pd/attach/course/word/add`,
      method: 'PUT',
      data
    });
  },
  //修改单词
  editWord(data) {
    return request({
      url: `/znyy/pd/attach/course/word/edit`,
      method: 'POST',
      data
    });
  },
  //删除单词
  deleteWord(data) {
    return request({
      url: `/znyy/pd/attach/course/word/delete`,
      method: 'DELETE',
      data
    });
  },
  //上传音频
  uploadAudio(data) {
    return request({
      url: `/znyy/pd/attach/course/word/audio/upload`,
      method: 'POST',
      data: data
    });
  },

  //查询音频
  searchAudio(data) {
    return request({
      url: `/znyy/pd/attach/course/word/audio/list`,
      method: 'get',
      params: data
    });
  },
  //查询音频
  AnalysisFile(data) {
    return request({
      url: `/znyy/pd/attach/course/word/AnalysisFile`,
      method: 'post',
      data: data
    });
  }
};
