<template>
  <div class="container">
    <el-form :inline="true" ref="dataQuery" class="SearchForm" style="padding: 20px 30px 0" label-width="120px">
      <el-row>
        <el-col :span="5" :xs="24">
          <el-form-item label="合伙人名称:">
            <el-input v-model="dataQuery.realName" @keyup.enter.native="fetchData()" style="width: 200px" placeholder="请输入合伙人名称" clearable class="partner-name"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="5" :xs="24">
          <el-form-item label="合伙人联系方式:">
            <el-input
              @input="handleInput(dataQuery.mobile)"
              v-model="dataQuery.mobile"
              @keyup.enter.native="fetchData()"
              style="width: 200px"
              placeholder="请输入合伙人联系方式"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <!-- <el-col :span="8" :xs="24">
          <el-form-item label="培训开始时间:">
            <el-date-picker
              v-model="dataQuery.dateRange"
              type="daterange"
              value-format="yyyy-MM-dd"
              align="right"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            ></el-date-picker>
          </el-form-item>
        </el-col> -->
        <el-col :span="6" :xs="24">
          <el-form-item>
            <el-button type="primary" size="small" icon="el-icon-search" @click="searchData()">搜索</el-button>
            <el-button size="small" icon="el-icon-refresh" @click="resetData()">重置</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div class="SearchForm">
      <!-- 表格 -->
      <el-table class="common-table" :data="tableData" border :default-sort="{ prop: 'date', order: 'descending' }" v-loading="tableLoading">
        <el-table-column prop="realName" label="合伙人名称"></el-table-column>
        <el-table-column prop="mobile" label="合伙人联系方式"></el-table-column>
        <el-table-column label="课程学习进度">
          <template slot-scope="scope">
            <el-button type="text" size="mini" @click="detailData(scope.row)">详情</el-button>
          </template>
        </el-table-column>
        <el-table-column label="培训开始日期">
          <template slot-scope="scope">
            <span v-if="scope.row.startTime">{{ scope.row.startTime }}</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- 分页 -->
    <el-col :span="20">
      <el-pagination
        :page-size="tablePage.size"
        layout="total, sizes, prev, pager, next, jumper"
        :total="tablePage.totalItems"
        :page-sizes="[10, 20, 30, 40, 50]"
        @size-change="handleSizeChange"
        :current-page.sync="tablePage.currentPage"
        @current-change="handleCurrentChange"
      />
    </el-col>
    <!-- 详情弹窗 -->
    <el-dialog title="课程学习进度" :visible.sync="dialogDetail" width="60%">
      <el-table :data="tableDetail">
        <el-table-column prop="courseName" label="课程名称"></el-table-column>
        <el-table-column prop="lessonCount" label="总课时"></el-table-column>
        <el-table-column prop="learnedLessonCount" label="已学课时数"></el-table-column>
        <el-table-column prop="notLearnedLessonCount" label="未学课时数"></el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
  import partnerSelfStudyAPI from '@/api/newPartner/partnerSelfStudy';
  export default {
    data() {
      return {
        dialogDetail: false,
        radio: '', // 单选按钮的选中值
        // 查询条件
        dataQuery: {
          realName: '',
          mobile: '',
          dateRange: [],
          startTime: '',
          endTime: ''
        },
        numberCheck: null, // 手机号输入框的值
        // 分页
        tablePage: {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null
        },
        tableLoading: false,
        //表格数据
        tableData: [],
        tableDetail: []
      };
    },
    created() {
      this.fetchData();
    },
    methods: {
      fetchData() {
        const that = this;
        that.tableLoading = true;
        that.dataQuery.pageNum = that.tablePage.currentPage;
        that.dataQuery.pageSize = that.tablePage.size;
        if (this.dataQuery.dateRange.length !== 0) {
          this.dataQuery.startTime = this.dataQuery.dateRange[0];
          this.dataQuery.endTime = this.dataQuery.dateRange[1];
        } else {
          this.dataQuery.startTime = '';
          this.dataQuery.endTime = '';
        }
        partnerSelfStudyAPI.selfStudyProgressPageList(that.dataQuery).then((res) => {
          that.tableData = res.data.data;
          that.tableLoading = false;
          that.tablePage.totalItems = parseInt(res.data.totalItems);
        });
      },
      handleInput(item) {
        this.numberCheck = item;
      },
      searchData() {
        if (this.numberCheck === '' || this.numberCheck === null) {
          this.tablePage = {
            currentPage: 1,
            size: 10,
            totalPage: null,
            totalItems: null
          };
          this.fetchData();
          return;
        }

        if (this.numberCheck === '0') {
          this.$message.warning('合伙人联系方式请输入大于 0 的整数');
          return;
        }

        if (!/^[1-9]\d*$/.test(this.numberCheck)) {
          this.$message.warning('合伙人联系方式请输入正整数');
          return;
        }

        if (this.numberCheck.length > 11) {
          this.$message.warning('合伙人联系方式请输入不超过 11 位的正整数');
          return;
        }

        this.tablePage = {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null
        };

        this.fetchData();
      },
      resetData() {
        this.dataQuery = {
          merchantName: '',
          realName: '',
          mobile: '',
          dateRange: [],
          startTime: '',
          endTime: ''
        };
        this.fetchData();
      },
      detailData(row) {
        partnerSelfStudyAPI.selfStudyProgressRecordList({ mobile: row.mobile }).then((res) => {
          this.tableDetail = res.data;
          if (this.tableDetail.length > 0) {
            this.dialogDetail = true;
          } else {
            this.$message.warning('暂无详情数据');
          }
        });
      },
      // 分页
      handleSizeChange(val) {
        this.tablePage.size = val;
        this.fetchData(); // 分页数据
      },
      handleCurrentChange(val) {
        this.tablePage.currentPage = val;
        this.fetchData(); // 分页数据
      }
    }
  };
</script>

<style lang="scss" scoped>
  .textStatus {
    font-size: 12px;
    margin-right: 10px;
  }
  ::v-deep .el-input .el-input__count .el-input__count-inner {
    padding-left: 0;
    padding-right: 0;
  }
  ::v-deep .partner-name.el-input--suffix .el-input__inner {
    padding-right: 55px;
  }
  ::v-deep .el-dialog__body {
    padding-bottom: 60px;
  }
</style>
