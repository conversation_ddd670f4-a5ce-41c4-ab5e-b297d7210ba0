<template>
  <div class="app-container">
    <el-form :inline="true" class="container-card" label-width="110px" label-position="left">

      <el-form-item label="登录账号：">
        <el-input v-model="dataQuery.name" placeholder="请输入登录账号：" clearable />
      </el-form-item>
      <el-form-item label="名称：">
        <el-input v-model="dataQuery.applicantsName" placeholder="请输入名称：" clearable />
      </el-form-item>
      <el-form-item label="签约时间：">
        <el-date-picker style="width: 100%;" v-model="value1" value-format="yyyy-MM-dd HH:mm:ss" type="daterange"
          align="right" unlink-panels range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" />
      </el-form-item>

      <el-form-item label="账户状态：">
        <el-select v-model="dataQuery.isEnable" value-key="value" placeholder="请选择" style="width: 200px" clearable>
          <el-option v-for="(item, index) in [
            { label: '开通', value: 1 },
            { label: '暂停', value: 0 },
          ]" :key="index" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="fetchData01()">搜索</el-button>
      </el-form-item>
    </el-form>

    <el-col :span="24" style="margin-bottom: 30px">
      <el-button type="success" icon="el-icon-plus" size="mini" @click="clickAdd">添加</el-button>
      <!--      <el-button type="warning" icon="el-icon-document-copy" size="mini" v-loading="exportLoading" @click="exportList()">导出</el-button>-->
    </el-col>
    <!-- 渲染表格 -->
    <el-table v-loading="tableLoading" class="common-table" :data="tableData" hstyle="width: 100%; margin-bottom: 20px"
      row-key="id" border default-expand-all :tree-props="{ list: 'children', hasChildren: 'true' }">
      <el-table-column prop="merchantCode" label="高报师编号" align="center" width="120px" />
      <el-table-column prop="name" label="登陆账号" align="center" width="120px" />
      <el-table-column label="操作" align="center" width="300">
        <template slot-scope="scope">
          <el-button type="success" icon="el-icon-edit-outline" size="mini" @click="update(scope.row.id)">编辑</el-button>
          <el-button type="warning" size="mini" icon="el-icon-switch-button"
            v-if="scope.row.isEnable == '0' || scope.row.isEnable == '2'"
            @click="agentStatus(scope.row.id, scope.row.isEnable, scope.row.merchantCode)">开通</el-button>
          <el-button type="danger" size="mini" icon="el-icon-video-pause" v-if="scope.row.isEnable == '1'"
            @click="agentStatus(scope.row.id, scope.row.isEnable, scope.row.merchantCode)">暂停</el-button>
          <el-button type="success" size="mini" icon="el-icon-edit"
            @click="openBigRecharge(scope.row.merchantCode, scope.row.isEnable)">充值
          </el-button>
        </template>
      </el-table-column>
      <el-table-column prop="applicantsName" label="名称" align="center" min-width="120px" />
      <!--      <el-table-column prop="phone" label="手机号"  align="center"  width="110px"/>-->

      <el-table-column prop="amountTotal" label="累计充值金额（元）" align="center" width="110px">
        <template slot-scope="scope">
          <span v-if="scope.row.amountTotal === null ||
            scope.row.amountTotal === ''
            ">0.00</span>
          <span v-else>{{ scope.row.amountTotal }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="balance" label="账户余额（元）" align="center" width="110px">
        <template slot-scope="scope">
          <span v-if="scope.row.balance === null || scope.row.balance === ''
            ">0.00</span>
          <span v-else>{{ scope.row.balance }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="yourArea" label="所在地区" align="center" />
      <el-table-column prop="signupDate" label="签约时间" width="300" />
      <el-table-column prop="isEnable" label="账户状态" align="center">
        <template slot-scope="scope">
          <span v-if="scope.row.isEnable == '1'" class="green">开通</span>
          <span v-else class="red">暂停</span>
        </template>
      </el-table-column>
    </el-table>

    <!-- 审核页面 -->
    <el-dialog title="审核" :visible.sync="showExamine" width="70%" :close-on-click-modal="false" @close="closeExamine">
      <el-form :ref="examine" :rules="rules" :model="examine" label-position="left" label-width="100px"
        style="width: 100%">
        <el-form-item label="是否通过：" prop="isCheck">
          <template>
            <el-radio v-model="isCheck" label="1" @change="change(isCheck)">通过</el-radio>
            <el-radio v-model="isCheck" label="0" @change="change(isCheck)">不通过</el-radio>
          </template>
        </el-form-item>
        <el-form-item label="审核意见">
          <el-col :xs="24" :span="18">
            <el-input type="textarea" resize="none" :rows="4" v-model="examine.checkReason" />
          </el-col>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="
          toExamine(examine.id, examine.checkReason, examine.isCheck, examine)
          ">确定</el-button>
        <el-button size="mini" @click="closeExamine">关闭</el-button>
      </div>
    </el-dialog>


    <el-dialog class="recharge-dialog" title="充值" :visible.sync="showRecharge" width="70%" :close-on-click-modal="false">
      <el-form v-model="recharge" label-position="left" label-width="150px" style="width: 100%;">
        <el-form-item label="账户余额(元)：" prop="course">
          <el-input v-model="recharge.balance" readonly='readonly' disabled />
        </el-form-item>
        <el-form-item label="充值账户：" prop="merchantCode">
          <el-input v-model="recharge.merchantCode" readonly="readonly" disabled />
        </el-form-item>
        <el-form-item label="充值金额(元): " prop="studentCode">
          <el-input v-model="recharge.rechargeMoney" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="superiorRecharge()">确定</el-button>
      </div>
    </el-dialog>


    <el-dialog class="remark-dialog" title="交易密码确认" :visible.sync="dialogVisible" width="20%"
      :close-on-click-modal="false" @close="close">
      <el-input v-model="secondPassWord" type="password" id="password" show-password></el-input>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="submitRecharge(secondPassWord)">确认</el-button>
      </div>
    </el-dialog>

    <!-- 分页 -->
    <el-col :span="20">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>
  </div>
</template>

<script>
import agentApi from "@/api/agentList";
import { pageParamNames } from "@/utils/constants";
import { ossPrClient } from "@/api/alibaba";
import promoterApi from "@/api/promoterList";
import authenticationApi from "@/api/authentication";
import expertsApplicantsApi from "@/api/expertsApplicantsList"
import ls from '@/api/sessionStorage'
import studentApi from "@/api/areasStudentCourseList";
import store from "@/store";
import dealerListApi from "@/api/areasDealerList";
import merchantAccountFlowApi from "@/api/merchantAccountFlow";
export default {
  data() {
    return {
      token: store.getters.token,
      tableLoading: false,
      roleTag: "",
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null,
      },
      uploadLoading: true,
      addOrUpdate: true, // true为添加，false为修改
      dataQuery: {
        name: "",
        applicantsName: "",
        txtStartTime: "",
        txtEndRegTime: "",
        isEnable: "",
        isArrea: "",  //1欠费,0不欠费
      },
      value1: "",

      tableData: [],
      updateData: {}, // 更新的数据
      addData: {}, // 新增的数据
      rules: {
        isCheck: [{
          required: true,
          message: "请选择是否通过",
          trigger: "change",
        },],
      },
      content: "",
      isUploadSuccess: true, // 是否上传成功
      showLoginAccount: false,
      showExamine: false, //审核页面
      //审核字段
      examine: {
        isCheck: "",
        checkReason: "",
        id: "",
      },
      isCheck: "",
      exportLoading: false, //导出加载
      exportLoading1: false,
      showRecharge: false,
      wechatRecharge: false,
      dialogVisible: false,
      secondPassWord: "",
      recharge: {  //充值
        rechargeMoney: 0,
        totalAmount: 0,
        amountReceived: 0,

      },
    };
  },
  created() {
    ossPrClient();
    this.getRoleTag();
    this.fetchData01();
  },
  methods: {
    fetchData01() {
      this.tablePage = {
        currentPage: 1,
        size: this.tablePage.size,
        totalPage: null,
        totalItems: null
      }
      this.fetchData();
    },
    getRoleTag() {
      authenticationApi.checkAccountBalance().then(res => {

        if (res.data.data.roleTag = 'ExpertsFill') {
          this.roleTag = res.data.data.roleTag;
        }
      })
    },
    // 查询+搜索
    fetchData() {
      const that = this;
      that.tableLoading = true;

      // if (this.roleTag='ExpertsFill'){
      //   that.dataQuery.name=this.roleTag;
      // }
      if (that.value1 != null) {
        if (that.value1.length > 0) {
          this.dataQuery.txtStartTime = that.value1[0];
          this.dataQuery.txtEndRegTime = that.value1[1];
        } else {
          this.dataQuery.txtStartTime = '';
          this.dataQuery.txtEndRegTime = '';
        }
      } else {
        this.dataQuery.txtStartTime = '';
        this.dataQuery.txtEndRegTime = '';
      }
      expertsApplicantsApi.expertsApplicantsList(
        that.tablePage.currentPage,
        that.tablePage.size,
        that.dataQuery
      )
        .then((res) => {
          that.tableData = res.data.data;
          that.tableLoading = false;
          // 设置后台返回的分页参数
          pageParamNames.forEach((name) =>
            that.$set(that.tablePage, name, parseInt(res.data[name]))
          );
        });
    },
    // 获取起始时间
    dateVal(e) {
      // console.log(e[0]);
      if (e != null) {
        if (e.length > 0) {
          this.dataQuery.txtStartTime = e[0];
          this.dataQuery.txtEndRegTime = e[1];
        } else {
          this.dataQuery.txtStartTime = '';
          this.dataQuery.txtEndRegTime = '';
        }
      }


    },
    // 进入添加页面
    clickAdd() {
      const that = this;
      that.addOrUpdate = true;
      window.localStorage.setItem("addOrUpdate", JSON.stringify(that.addOrUpdate));
      window.localStorage.removeItem('applicantsId');
      that.$router.push({
        path: "/dzy/card/expertsApplicantsAdd",
        query: {
          addOrUpdate: that.addOrUpdate,
        },
      });
    },

    // 进入编辑页面
    update(id) {
      const that = this;
      that.addOrUpdate = false;

      window.localStorage.setItem("addOrUpdate", JSON.stringify(that.addOrUpdate));
      window.localStorage.setItem("applicantsId", id);
      that.$router.push({
        path: "/dzy/card/expertsApplicantsAdd",
        query: {
          addOrUpdate: that.addOrUpdate,
          id: id,
        },
      });
    },

    // 分页
    handleSizeChange(val) {
      console.log(val);
      this.tablePage.size = val;
      this.fetchData();
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val;
      this.fetchData();
    },
    exportList1() {
      const that = this;

      if (that.dataQuery.txtStartTime === "" || that.dataQuery.txtStartTime === '') {
        that.$message.info("开始时间不能为空");
        return false;
      }
      if (that.dataQuery.txtEndRegTime === '' || that.dataQuery.txtEndRegTime === "") {
        that.$message.info("结束时间不能为空");
        return false;
      }
      that.exportLoading1 = true;
      const data = {
        'startDate': that.dataQuery.txtStartTime,
        'endDate': that.dataQuery.txtEndRegTime
      }
      promoterApi.promoterExport(data).then((response) => {
        console.log(response);
        if (!response) {
          this.$notify.error({
            title: "操作失败",
            message: "文件下载失败",
          });
        }
        const url = window.URL.createObjectURL(response);
        const link = document.createElement("a");
        link.style.display = "none";
        link.href = url; // 获取服务器端的文件名
        link.setAttribute("download", "市级服务商运营数据表.xls");
        document.body.appendChild(link);
        link.click();
        this.exportLoading1 = false;
      });
    },
    // 导出
    exportList() {
      const that = this;
      that.exportLoading = true;
      expertsApplicantsApi.companyExport(that.dataQuery).then((response) => {
        console.log(response);
        if (!response) {
          this.$notify.error({
            title: "操作失败",
            message: "文件下载失败",
          });
        }
        const url = window.URL.createObjectURL(response);
        const link = document.createElement("a");
        link.style.display = "none";
        link.href = url; // 获取服务器端的文件名
        link.setAttribute("download", "分公司表.xls");
        document.body.appendChild(link);
        link.click();
        this.exportLoading = false;
      });
    },
    agentPaymentIsComplete(id, paymentIsComplete) {
      if (paymentIsComplete == 0) {
        paymentIsComplete = 1;
      }
      const that = this;
      this.$confirm("确定操作吗？", "完款", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        agentApi.updatePaymentIsComplete(id, paymentIsComplete).then((res) => {
          if (res.success) {
            that.fetchData01();
            that.$message.success("操作成功");
          }
        });
      })
    },
    // 开通与暂停
    agentStatus(id, status, merchantCode) {
      var message = "请确认信息无误?";
      if (status == 0 || status == 2) {
        if (status == 2) {
          message = "请确认信息无误，首次开通后需扣除账户资金?"
        }
        status = 1;
      } else {
        status = 0;
      }
      const that = this;


      this.$confirm(message, "确认信息", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          expertsApplicantsApi.updateIsEnable(id, status, merchantCode).then((res) => {
            if (res.success) {
              that.fetchData01();
              that.$message.success("操作成功");
            }
          });


        })
        .catch((err) => { });
    },

    //上级给充值弹框
    openBigRecharge(merchantCode, isEnable) {
      if (isEnable == 2 || isEnable == 0) {
        this.$message.error("此账户未开通,无法充值")
        return false;
      }
      expertsApplicantsApi.getRecharge().then(res => {
        this.recharge.balance = res.data.balance
        this.recharge.merchantCode = merchantCode

        this.showRecharge = true;
        this.recharge = {
          balance: this.recharge.balance,
          merchantCode: this.recharge.merchantCode,
          rechargeMoney: '',
        }
      })
    },

    //上级充值
    superiorRecharge() {
      if (this.recharge.rechargeMoney <= 0) {
        this.$message.error("充值金额必须大于0")
        return false;
      }
      this.dialogVisible = true;

    },
    submitRecharge(secondPassWord) {
      if (!secondPassWord) {
        this.$message.error('二级密码不能为空')
        return false;
      }
      merchantAccountFlowApi.checkSecondPwd(secondPassWord).then(res => {
        if (!res.success) {
          this.$message.error('二级密码验证失败')
          return false;
        }
        this.$confirm('确定操作吗?', '提交充值', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          expertsApplicantsApi.expertsRecharge(this.recharge).then(res => {
            if (!res.success) {
              this.$message.error(res.message)
              return false
            }
            this.$nextTick(() => this.fetchData())
            this.$message.success('充值成功')
            this.showRecharge = false;
            this.dialogVisible = false;

          })

        })
      })
    },

    //
    // //上级充值
    // superiorRecharge(){
    //   if(this.recharge.rechargeMoney<=0){
    //     this.$message.error("充值金额必须大于0")
    //     return false;
    //   }
    //   this.$confirm('确定操作吗?', '提交充值', {
    //     confirmButtonText: '确定',
    //     cancelButtonText: '取消',
    //     type: 'warning'
    //   }).then(() => {
    //     expertsApplicantsApi.expertsRecharge(this.recharge).then(res => {
    //       if(!res.success){
    //         this.$message.error(res.message)
    //         return false
    //       }
    //       this.$nextTick(() => this.fetchData())
    //       this.$message.success('充值成功')
    //
    //     })
    //     this.showRecharge = false;
    //
    //   })
    // },



    // 打开审核弹框
    openExamine(id) {
      this.showExamine = true;
      this.examine.id = id;
      this.isCheck = "";
    },
    // 关闭审核弹框
    closeExamine() {
      this.showExamine = false;
      this.examine.id = "";
    },
    // 状态改变事件
    change(radio) {
      if (radio == "1") {
        this.examine.isCheck = 1;
      } else {
        this.examine.isCheck = 0;
      }
    },
    // 审核
    toExamine(id, checkReason, isCheck, ele) {
      // console.log(typeof long)
      const that = this;
      that.$refs[ele].validate((valid) => {
        // 表单验证
        if (valid) {
          const loading = this.$loading({
            lock: true,
            text: "审核中",
            spinner: "el-icon-loading",
            background: "rgba(0, 0, 0, 0.7)",
          });
          expertsApplicantsApi
            .examine(id, checkReason, isCheck)
            .then((res) => {
              if (res.success) {
                loading.close();
                that.showExamine = false;
                that.$nextTick(() => that.fetchData());
                that.$message.success("审核成功！");
              } else {
                that.$message.warning(res.message);
              }
            })
            .catch((err) => {
              loading.close();
            });
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
  },
};
</script>

<style>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

@media (max-width:767px) {
  .el-message-box {
    width: 80% !important;
  }
}
</style>
