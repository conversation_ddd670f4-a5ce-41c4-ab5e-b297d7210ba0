<!--文字找重题-->
<template>
  <div class="app-container">
    <el-form :model="form" ref="form" label-width="100px" v-loading="formLoading" :rules="rules">
      <el-form-item label="所属类型:" prop="type">
        <el-select v-model="form.type" placeholder="全部" disabled>
          <el-option v-for="(item, index) in trainType" :key="index" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="题型:" prop="questionType">
        <el-select v-model="form.questionType" placeholder="全部" disabled>
          <el-option v-for="(item, index) in trainQuestionType" :key="index" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="年级：" prop="grade" v-if="form.courseType == 1">
        <el-select v-model="form.grade" placeholder="" clearable>
          <el-option v-for="item in gradeList" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="选择难度：" prop="difficulty">
        <el-select v-model="form.difficulty" placeholder="全部" clearable @change="handleChange">
          <el-option v-for="(item, index) in difficultyList" :key="index" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="选择维度：" prop="categoryId">
        <el-select v-model="form.categoryId" placeholder="全部" clearable>
          <el-option v-for="(item, index) in categoryList" :key="index" :label="item.name" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item v-if="form.courseType!=1" label="选择类别：" prop="categoryType">
        <el-select v-model="form.categoryType" :disabled="questionlnCourseFalse&&form.categoryType==1" placeholder="全部" clearable>
          <el-option v-for="(item, index) in typeList" :key="index" :label="item.label" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="题干：" prop="title">
        <el-input placeholder="请输入" v-model="form.title" />
      </el-form-item>
      <el-form-item label="答案：" required>
        <div :key="index" v-for="(item, index) in form.answer">
          <el-input v-model="item.label" style="width: 100px; margin-right: 10px; margin-bottom: 10px" placeholder="输入文字" />
          <el-input-number v-model="item.value" placeholder="输入个数" controls-position="right" :min="1" :step="1" style="width: 150px; margin-right: 5px; margin-bottom: 2px" @input.native="(value) => handleInputValue(value, 0, undefined)" />
          <el-button type="success" v-if="index === form.answer.length - 1" @click="answerItemAdd(undefined)" style="margin-top: 10px">添加文字</el-button>
          <el-button type="danger" v-if="index === form.answer.length - 1 && form.answer.length > 1" @click="answerItemRemove(undefined)" style="margin-top: 10px">
            删除文字
          </el-button>
        </div>
      </el-form-item>
      <el-form-item label="题数：" required>
        <el-input-number v-model="form.score" :precision="0" :step="1" :min="0" :max="100" @input.native="(value) => handleInput(value)"></el-input-number>
      </el-form-item>
      <div v-for="(question, qIndex) in form.childQuestions" :key="qIndex" :class="['question-item', { 'question-item-gray': qIndex % 2 === 0 }]">
        <el-row type="flex" justify="flex-start" v-if="showChildQuestions">
          <el-col :xs="24" :lg="16">
            <el-row>
              <el-col :span="20">
                <el-form-item label="题干：" :prop="'childQuestions.' + qIndex + '.title'" :rules="rules.title">
                  <el-input placeholder="请输入" v-model="question.title" />
                </el-form-item>
                <el-form-item label="答案：" required>
                  <div :key="index" v-for="(item, index) in question.answer">
                    <el-input v-model="item.label" style="width: 100px; margin-right: 10px; margin-bottom: 10px" placeholder="输入文字" />
                    <el-input-number v-model="item.value" placeholder="输入个数" controls-position="right" :min="1" :step="1" style="width: 150px; margin-right: 5px; margin-bottom: 2px" @input.native="(value) => handleInputValue(value, index, qIndex)" />
                    <el-button type="success" v-if="index === question.answer.length - 1" @click="answerItemAdd(qIndex)" style="margin-top: 10px">添加文字</el-button>
                    <el-button type="danger" v-if="index === question.answer.length - 1 && question.answer.length > 1" @click="answerItemRemove(qIndex)" style="margin-top: 10px">
                      删除文字
                    </el-button>
                  </div>
                </el-form-item>
                <el-form-item label="题数：" required>
                  <el-input-number v-model="question.score" :precision="0" :step="1" :min="0" :max="100" @input.native="(value) => handleInputChild(value, qIndex)"></el-input-number>
                </el-form-item>
              </el-col>
              <el-col :span="2" :offset="1">
                <el-button type="danger" icon="el-icon-close" @click="removeQuestion(qIndex)">删除</el-button>
              </el-col>
            </el-row>
          </el-col>
        </el-row>
      </div>
      <el-form-item label="徽章：" prop="badge">
        <el-input-number v-model="form.badge" :precision="0" :step="1" :min="0" :max="100" @input.native="(value) => validateForm(value)"></el-input-number>
      </el-form-item>
      <el-form-item label="解析：">
        <el-input v-model="form.analysis">
          <i @click="inputClick(form, 'analysis')" slot="suffix" class="el-icon-edit-outline" style="line-height: 36px; font-size: 20px; cursor: pointer"></i>
        </el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="submitForm">提交</el-button>
        <el-button v-show="form.id === null" @click="resetForm">重置</el-button>
        <el-button type="success" v-if="!editId" @click="addQuestion" :disabled="!canAddQuestion || childQuestionsExceedLimit">添加小题</el-button>
      </el-form-item>
      <el-alert v-if="childQuestionsExceedLimit" title="题目数量不能超过10题" type="warning" show-icon></el-alert>
    </el-form>
    <el-dialog :visible.sync="richEditor.dialogVisible" append-to-body :close-on-click-modal="false" style="width: 100%; height: 100%" :show-close="false" center>
      <Ueditor @ready="editorReady" />
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="editorConfirm">确 定</el-button>
        <el-button @click="richEditor.dialogVisible = false">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import questionApi from '@/api/paper/train/question';
import difficultyApi from '@/api/paper/train/difficulty';
import categoryApi from '@/api/paper/train/category';
import { mapGetters, mapState } from 'vuex';
import MultipleUpload from '@/components/Upload/MultipleUpload';
import Ueditor from '@/components/Ueditor';

export default {
  components: {
    MultipleUpload,
    Ueditor
  },

  data() {
    return {
      editId: '',
      richEditor: {
        dialogVisible: false,
        object: null,
        parameterName: '',
        instance: null
      },
      difficultyInfo: null,
      categoryList: [],
      fileList: [],
      typeList: [
        { label: '正式题', id: 1 },
        { label: '附加题', id: 2 }
      ],
      importFrom: {
        file: null
      },
      randomLength: undefined,
      form: {
        id: null,
        categoryId: '',
        categoryType: '',
        difficulty: '',
        difficultyId: '',
        type: 'VISUAL_',
        questionType: 'VISUAL_TEXT_REPEAT',
        answer: [{ label: '', value: undefined }],
        title: '',
        score: undefined,
        childQuestions: [],
        isRandom: false,
        analysis: '',
        badge: 3,
        courseType: 0,
        grade: ''
      },
      showChildQuestions: false,
      formLoading: false,
      rules: {
        type: [{ required: true, message: '请选择', trigger: 'blur' }],
        questionType: [{ required: true, message: '请选择', trigger: 'blur' }],
        grade: [{ required: true, message: '请选择', trigger: 'blur' }],
        title: [{ required: true, message: '请输入', trigger: 'change' }],
        difficulty: [{ required: true, message: '请选择', trigger: 'blur' }],
        categoryId: [{ required: true, message: '请选择', trigger: 'blur' }],
        categoryType: [{ required: true, message: '请选择', trigger: 'blur' }],
        badge: [{ required: true, message: '请输入徽章', trigger: 'blur' }]
      },
      gradeList: [
        { label: '1-3年级', value: 1 },
        { label: '4-6年级', value: 2 },
        { label: '初高中', value: 3 }
      ],
      questionlnCourseFalse: false,
    };
  },
  created() {
    this.getCategoryList();
    this.editId = this.$route.query.id;
    this.form.courseType = this.$route.query.courseType ? 1 : 0;
    if (this.editId && parseInt(this.editId) !== 0) {
      this.formLoading = true;
      questionApi.detail(this.editId).then((re) => {
        this.form = re.data;
        this.form.grade = this.form.grade == 0 ? '' : this.form.grade;
        this.handleChange();
        this.formLoading = false;
      });
      questionApi.checkQuestionlnCourse(id).then((res) => {
        this.questionlnCourseFalse = res.data
      });
    }
  },
  methods: {
    validateForm(value) {
      let inputValue = value.data?.toString().replace(/^(0+)|[^\d]+/g, '');
      this.form.badge.push(inputValue);
      this.$refs.form.validateField('badge'); // 手动触发校验
    },
    handleInput(value) {
      let inputValue = value.data?.toString().replace(/^(0+)|[^\d]+/g, '');
      this.form.score.push(inputValue);
      if (value.data === undefined || value.data === null || value.data === '') {
        return this.$message.error('请输入数字');
      }
    },
    handleInputChild(value, qIndex) {
      let inputValue = value.data?.toString().replace(/^(0+)|[^\d]+/g, '');
      this.form.childQuestions[qIndex].score.push(inputValue);
      if (value.data === undefined || value.data === null || value.data === '') {
        return this.$message.error('请输入数字');
      }
    },
    handleInputValue(value, index, itemIndex) {
      let inputValue = value.data?.toString().replace(/^(0+)|[^\d]+/g, '');
      if (itemIndex === undefined) {
        this.form.answer[index].value.push(inputValue);
      } else {
        this.form.childQuestions[itemIndex].answer[index].value.push(inputValue);
      }
    },
    answerItemAdd(qIndex) {
      let item = { label: '', value: undefined };
      if (qIndex === undefined) {
        this.form.answer.push(item);
      } else {
        this.form.childQuestions[qIndex].answer.push(item);
      }
    },
    answerItemRemove(qIndex) {
      if (qIndex === undefined) {
        this.form.answer.splice(this.form.answer.length - 1, 1);
      } else {
        this.form.childQuestions[qIndex].answer.splice(this.form.childQuestions[qIndex].answer.length - 1, 1);
      }
    },
    addQuestion() {
      if (this.childQuestionsExceedLimit) return;
      this.showChildQuestions = true;
      if (this.form.childQuestions.length > 0) {
        const lastQuestion = this.form.childQuestions[this.form.childQuestions.length - 1];
        if (!lastQuestion.title || !lastQuestion.answer.length || !lastQuestion.score) {
          this.$message.error('请填写完整当前题目的内容！');
          return;
        }
      } else {
        if (!this.form.title || !this.form.answer.length || !this.form.score) {
          this.$message.error('请填写完整当前题目的内容！');
          return;
        }
      }
      this.form.childQuestions.push({
        title: '',
        answer: [{ label: '', value: undefined }],
        score: undefined
      });
    },
    removeQuestion(index) {
      this.$confirm('请确定是否需要删除已添加的题目，删除后不可恢复', '删除题目', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.form.childQuestions.splice(index, 1);
      });
    },
    editorReady(instance) {
      this.richEditor.instance = instance;
      let currentContent = this.richEditor.object[this.richEditor.parameterName];
      this.richEditor.instance.setContent(currentContent);
      // 光标定位到Ueditor
      this.richEditor.instance.focus(true);
    },
    inputClick(object, parameterName) {
      this.richEditor.object = object;
      this.richEditor.parameterName = parameterName;
      this.richEditor.dialogVisible = true;
    },
    editorConfirm() {
      let content = this.richEditor.instance.getContent();
      this.richEditor.object[this.richEditor.parameterName] = content;
      this.richEditor.dialogVisible = false;
    },

    getCategoryList() {
      categoryApi.list().then((res) => {
        this.categoryList = res.data.data;
      });
    },
    handleChange() {
      if (!this.form.difficulty) {
        this.difficultyInfo = null;
        return;
      }
      let query = {};
      query.type = this.form.type;
      query.questionType = this.form.questionType;
      query.difficulty = this.form.difficulty;
      difficultyApi
        .isSetting(query)
        .then((res) => {
          this.difficultyInfo = res.data;
        })
        .catch((e) => {
          this.difficultyInfo = null;
          this.fileList = [];
        });
    },
    validateAnswers(answers) {
      const labelPattern = /^[a-zA-Z0-9\u4e00-\u9fa5]+$/; // 允许的字符：数字、字母和汉字
      const valuePattern = /^[0-9]+$/; // 仅允许数字

      for (let i = 0; i < answers.length; i++) {
        const answer = answers[i];
        if (!labelPattern.test(answer.label)) {
          return `答案项 ${i + 1} 的“问题”只能包含数字、字母和汉字`;
        }
        if (!valuePattern.test(answer.value)) {
          return `答案项 ${i + 1} 的“答案”必须是数字`;
        }
      }
      return null;
    },
    submitForm() {
      if (this.difficultyInfo === null) {
        this.$message.error('请选择难度！');
        return false;
      }
      // 校验 form 中的 answer
      let errorMessage = this.validateAnswers(this.form.answer);
      if (errorMessage) {
        this.$message.error(errorMessage);
        return false;
      }
      if (this.form.score == undefined || this.form.score == null || this.form.score == '') {
        this.$message.error('请输入题数');
        return false;
      }
      if (!this.editId) {
        // 校验 childQuestions 中的 answer
        for (let i = 0; i < this.form.childQuestions.length; i++) {
          const childQuestion = this.form.childQuestions[i];
          errorMessage = this.validateAnswers(childQuestion.answer);
          if (errorMessage) {
            this.$message.error(`子题目 ${i + 1}：${errorMessage}`);
            return false;
          }
          if (childQuestion.score == undefined || childQuestion.score == null || childQuestion.score == '') {
            this.$message.error(`子题目 ${i + 1}：请输入题数`);
            return false;
          }
        }
      }
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.formLoading = true;
          questionApi
            .saveOrUpdate(this.form)
            .then((re) => {
              if (re.success) {
                this.$message.success(re.message);
                this.formLoading = false;
                this.$router.push('/train/trainAfterCourse');
              } else {
                this.$message.error(re.message);
                this.formLoading = false;
              }
            })
            .catch((e) => {
              this.formLoading = false;
            });
        } else {
          return false;
        }
      });
    },
    resetForm() {
      this.showChildQuestions = false;
      let lastId = this.form.id;
      let lastCourseType = this.form.courseType;
      this.$refs['form'].resetFields();
      this.form = {
        id: null,
        title: '',
        categoryId: '',
        difficulty: '',
        difficultyId: '',
        type: 'VISUAL_',
        questionType: 'VISUAL_TEXT_REPEAT',
        customInfo: [{ label: '', value: '' }],
        answer: [{ label: '', value: undefined }],
        isRandom: false,
        analysis: '',
        childQuestions: [],
        grade: ''
      };
      this.form.id = lastId;
      this.form.courseType = lastCourseType;
    }
  },
  computed: {
    ...mapGetters('enumItem', ['enumFormat']),
    ...mapState('enumItem', {
      trainType: (state) => state.train.trainType,
      difficultyList: (state) => state.train.difficultyList,
      trainQuestionType: (state) => state.train.trainQuestionType
    }),
    canAddQuestion() {
      if (this.editId) return false;
      const lastQuestion = this.form.childQuestions.length > 0 ? this.form.childQuestions[this.form.childQuestions.length - 1] : this.form;
      if (!lastQuestion) return false;
      const { title, answer, score } = lastQuestion;
      // 检查 title 是否为空
      if (!title) return false;
      // 检查 answer 数组是否为空
      if (!Array.isArray(answer) || answer.length === 0) return false;
      // 检查每个 answer 项中的 label 和 value 是否已填写且非空
      for (const item of answer) {
        if (!item.label || item.value === undefined || item.value === null || item.value <= 0) {
          return false;
        }
      }
      // 检查 score 是否已填写
      if (!score) return false;
      return true;
    },
    childQuestionsExceedLimit() {
      if (this.editId) return false;
      return this.form.childQuestions.length >= 9;
    }
  }
};
</script>

<style lang="less" scoped>
.question-item-label {
  display: flex;
  margin-bottom: 12px;
}

.img-lg {
  width: 60px;
  height: 60px;
}
.question-item {
  padding: 10px;
  margin-bottom: 10px;
  border-radius: 4px;
}

.question-item-gray {
  background-color: #f9f9f9;
}
</style>
