<template>
  <div class="app-container">
    <el-form :inline="true" class="container-card" label-width="96px" label-position="left">
      <el-form-item label="状态:">
        <el-select v-model="dataQuery.status" filterable value-key="value" placeholder="请选择">
          <el-option v-for="(item, index) in [{ value: 0, label: '进行中' }, { value: 1, label: '已处理' }]" :key="index"
            :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="教练 名称：">
        <el-input v-model="dataQuery.tutorName" placeholder="请输入教练 名称" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="getList()">搜索</el-button>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-re" @click="resetQuery()">重置</el-button>
      </el-form-item>
    </el-form>
    <el-table class="common-table" v-loading="tableLoading" :data="tableData" height="400px"
      style="width: 100%;margin-bottom: 20px;" row-key="id" border default-expand-all
      :tree-props="{ list: 'children', hasChildren: 'true' }">
      <el-table-column prop="id" label="绩效ID"></el-table-column>
      <el-table-column prop="tutorName" label="被评审人"></el-table-column>
      <el-table-column label="操作" align="center" width="200">
        <template slot-scope="scope">
          <el-button type="primary" @click="handleView(scope.row)" size="mini">查看</el-button>
          <el-button type="danger" @click="handleDel(scope.row)" size="mini">删除</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="courseScore" label="课程考勤">
        <template slot-scope="scope">
          {{ scope.row.courseScore }}分
        </template>
      </el-table-column>
      <el-table-column prop="classScore" label="上课行为">
        <template slot-scope="scope">
          {{ scope.row.classScore }}分
        </template>
      </el-table-column>
      <el-table-column prop="reportScore" label="填写学情报告进度">
        <template slot-scope="scope">
          {{ scope.row.reportScore }}分
        </template>
      </el-table-column>
      <el-table-column prop="appraiseScore" label="家长评价">
        <template slot-scope="scope">
          {{ scope.row.appraiseScore }}分
        </template>
      </el-table-column>
      <el-table-column prop="serveScore" label="服务商评分">
        <template slot-scope="scope">
          <span v-if="scope.row.serveScore > 0">{{ scope.row.serveScore }}分</span>
          <el-button v-else type="success" @click="handleView(scope.row)" size="mini">评分</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态">
        <template slot-scope="scope">
          <span v-if="scope.row.status == 1">已结算</span>
          <span v-if="scope.row.status == 2">待结算</span>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog class="recharge-dialog" title="绩效详情" :visible.sync="open" width="70%" :close-on-click-modal="false">
      <el-form ref="form" :model="form" label-width="150px" :rules="rules" style="width: 100%;" @close="reset">
        <el-form-item label="被评审人：" prop="tutorName">
          {{ form.tutorName }}
        </el-form-item>
        <el-form-item label="联系电话：" prop="tutorTel">
          {{ form.tutorTel }}
        </el-form-item>
        <el-form-item label="课程考勤 ：" prop="courseScore">
          {{ form.courseScore }}分
          {{ form.courseRemark }}
        </el-form-item>
        <el-form-item label="上课行为 ：" prop="classScore">
          {{ form.classScore }}分
          <br />
          <el-image v-for="img in files" :key="img" style="width: 100px; height: 100px;margin-right: 10px" :src="img"
            :preview-src-list="files"></el-image>
        </el-form-item>
        <el-form-item label="填写学情报告进度 ：" prop="reportScore">
          {{ form.reportScore }}分
          <br />
          {{ form.reportRemark }}
        </el-form-item>
        <el-form-item label="家长评价 ：" prop="appraiseScore">
          {{ form.appraiseScore }}分
        </el-form-item>
        <el-form-item label="服务商评分 ：" prop="serveScore">
          {{ form.serveScore }}
        </el-form-item>
        <el-form-item label="处理状态 ：" prop="status">
          {{ statusList[form.status] }}
        </el-form-item>
      </el-form>
      <!-- <div slot="footer" class="dialog-footer">
        <el-button v-if="form.status===0" size="mini" type="primary" @click="handleSave()">确定</el-button>
        <el-button v-if="form.status===1" size="mini" @click="cancel()">关闭</el-button>
      </div>-->
    </el-dialog>

    <!-- 分页 -->
    <el-col :span="20" style="overflow-x: auto;" :xs="24">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>
  </div>
</template>

<script>

import performanceApi from '@/api/performance'
import { pageParamNames } from "@/utils/constants";
export default {
  data() {
    return {
      files: [],
      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      tableData: [],
      open: false,
      dataQuery: {
        status: null,
        tutorName: null,
        startTime: null,
        endTime: null
      },
      createTime: [],
      form: {},
      statusList: {
        1: "已结算",
        2: "待结算"
      },
      rules: {
        handingResult: [
          { required: true, message: "请输入处理方案", trigger: "blur" }
        ],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    //获取列表
    getList() {
      const that = this;
      var a = that.createTime;
      if (a != null) {
        that.dataQuery.startTime = a[0];
        that.dataQuery.endTime = a[1];
      } else {
        that.dataQuery.startTime = null;
        that.dataQuery.endTime = null;
      }
      that.tableLoading = true;
      performanceApi.performanceList(that.tablePage.currentPage, that.tablePage.size, that.dataQuery).then(res => {
        that.tableData = res.data.data
        console.log(res.data.data);
        that.tableLoading = false
        // 设置后台返回的分页参数
        pageParamNames.forEach(name => this.$set(this.tablePage, name, parseInt(res.data[name])))
      })
    },

    //重置按钮
    resetQuery() {
      this.createTime = [];
      this.dataQuery = {
        status: null,
        tutorName: null,
        startTime: null,
        endTime: null
      };
      this.getList();
    },
    //查看按钮
    handleView(row) {
      this.reset();
      performanceApi.performanceDetail(row.id).then(response => {
        this.form = response.data;
        this.files = response.data.files;
        this.open = true;
      });
    },
    //删除按钮
    handleDel(row) {
      this.$confirm('确定要删除该绩效吗?', '删除绩效', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        performanceApi.performanceDel(row.id).then(res => {
          this.getList();
          this.$message.success('删除成功!')
        }).catch(err => {
        })
      }).catch(err => {
      })
    },
    reset() {
      this.form = {
        id: null,
        tutorName: null,
        tutorTel: null,
        courseScore: null,
        courseRemark: null,
        classScore: null,
        reportScore: null,
        reportRemark: null,
        appraiseScore: null,
        serveScore: null,
        status: null,
      };
    },
    cancel() {
      this.open = false;
      this.reset();
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val;
      this.getList();
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val;
      this.getList();
    },
  }
};
</script>

<style>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

@media screen and (max-width: 767px) {
  .recharge-dialog .el-dialog {
    width: 90% !important;
  }

  .el-message-box {
    width: 80% !important;
  }
}
</style>
