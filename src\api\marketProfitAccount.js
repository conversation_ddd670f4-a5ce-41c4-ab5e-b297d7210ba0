
/**
 *
 */
import request from '@/utils/request'

export default {
  // 推荐人新增
  addMarketProfitAmount(data) {
    return request({
      url: '/znyy/market/profit/amount/add/profit',
      method: 'POST',
      data
    })
  },
  // 推荐人编辑
  updateMarketProfitAmount(data) {
    return request({
      url: '/znyy/market/profit/amount/add/profit',
      method: 'POST',
      data
    })
  },

  //推荐人导出
  simpleMarketExecl(listQuery){
    return request({
      url: '/znyy/market/execl',
      method: 'GET',
      responseType: 'blob',
      params:listQuery
    })
  },
//修改登录账号
  markertUpdateLogin(data){
    return request({
      url: '/znyy/bSysConfig/update/account',
      method: 'PUT',
      data
    })
  },

  // 推荐人查询
  queryActive(id) {
    return request({
      url: '/znyy/market/profit/amount/check/detail/' + id,
      method: 'GET'
    })
  },
  deleteProfitAccount(id) {
    return request({
      url: '/znyy/market/profit/amount/delete/' + id,
      method: 'DELETE'
    })
  },
  // 托管中心开通与暂停
  marketStatus(id,status) {
    return request({
      url: '/znyy/market/updateIsEnable?id=' + id + '&isEnable=' + status,
      method: 'PUT'
    })
  },

  //
  //托管中心审核
  isCheckStatus(data){
    return request({
      url:'/znyy/dealer/check',
      method:'PUT',
      data
    })
  },

  //其他时间角色不是bvaadmin/areas/merchant
  // 推荐人分页查询
  marketProfitAccountList(pageNum, pageSize, data) {
    return request({
      url: '/znyy/market/profit/amount/list/' + pageNum + '/' + pageSize,
      method: 'GET',
      params: data
    })
  },
  profitAccountList(pageNum, pageSize, data) {
    return request({
      url: '/znyy/profit/account/list/' + pageNum + '/' + pageSize,
      method: 'GET',
      params: data
    })
  },

  getCurrentTime() {
    var _this = this;
    let yy = new Date().getFullYear();
    let mm = new Date().getMonth() + 1;
    let dd = new Date().getDate();
    let hh = new Date().getHours();
    let mf = new Date().getMinutes() < 10 ? '0' + new Date().getMinutes() : new Date().getMinutes();
    let ss = new Date().getSeconds() < 10 ? '0' + new Date().getSeconds() : new Date().getSeconds();
    _this.gettime = yy + '-' + mm + '-' + dd + ' ' + hh + ':' + mf + ':' + ss;
    return _this.gettime
  }
}
