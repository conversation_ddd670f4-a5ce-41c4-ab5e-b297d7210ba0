<template>
  <div class="app-container">
    <div class="SearchForm">
      <!-- 表格 -->
      <el-table class="common-table" :data="tableData" v-loading="tableLoading">
        <el-table-column prop="name" label="掌握度" sortable></el-table-column>
        <el-table-column prop="oneValue" label="最低正确率" sortable></el-table-column>
        <el-table-column prop="id" label="操作" sortable>
          <template slot-scope="scope">
            <el-button type="success" size="mini" icon="el-icon-edit-outline"
              @click="openEdit(scope.row.id)">编辑</el-button>
          </template>
        </el-table-column>
        <el-table-column prop="twoValue" label="最高正确率" sortable></el-table-column>
      </el-table>
    </div>

    <el-dialog title="掌握度配置" :visible.sync="showEdit" width="45%" :close-on-click-modal="false" @close="close">
      <el-form :ref="'updateGrammar'" :rules="updateSingle" :model="updateGrammar" label-position="left"
        label-width="80px" style="width: 100%">
        <el-form-item label="id" prop="id" v-show="false">
          <el-col :xs="24" :span="12">
            <el-input v-model="updateGrammar.id"></el-input>
          </el-col>
        </el-form-item>
        <el-form-item label="最低正确率" prop="oneValue">
          <el-col :xs="24" :span="12">
            <el-input v-model="updateGrammar.oneValue" type="number"></el-input>
          </el-col>
        </el-form-item>
        <el-form-item label="最高正确率" prop="twoValue">
          <el-col :xs="24" :span="12">
            <el-input v-model="updateGrammar.twoValue" type="number"></el-input>
          </el-col>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="editConfig('updateGrammar')">确定</el-button>
        <el-button size="mini" @click="closeEdit">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import grammarApi from "@/api/grammar";
import {
  pageParamNames
} from "@/utils/constants";
export default {
  data() {
    return {
      tableLoading: false,
      updateSingle: {
        oneValue: [{
          required: true,
          message: '必填',
          trigger: 'blur'
        }],
        twoValue: [{
          required: true,
          message: '必填',
          trigger: 'blur'
        }]
      },
      tableData: [], //表格数据
      showEdit: false, //编辑弹窗
      updateGrammar: {}, // 修改数据
    };
  },
  created() {
    this.fetchData();
  },
  methods: {
    // 查询表格列表
    fetchData() {
      const that = this;
      grammarApi.getStudyRateConfig().then((res) => {
        that.tableData = res.data;
        that.tableLoading = false;
      });
    },
    openEdit(id) {
      grammarApi.getConfigById(id).then(res => {
        this.updateGrammar = res.data;
      })
      this.showEdit = true;
    },
    editConfig(ele) {
      const that = this;
      that.$refs[ele].validate((valid) => {
        // 表单验证
        if (valid) {
          const loading = this.$loading({
            lock: true,
            text: "提交掌握度配置",
            spinner: "el-icon-loading",
            background: "rgba(0, 0, 0, 0.7)",
          });
          grammarApi.editGrammarConfig(that.updateGrammar).then(() => {
            that.showEdit = false;
            loading.close();
            that.$nextTick(() => that.fetchData());
            that.$message.success("掌握度配置成功");
          })
            .catch((err) => {
              // 关闭提示弹框
              loading.close();
            });
        } else {
          console.log("error submit!!");
          // loading.close();
          return false;
        }
      });
    },
    closeEdit() {
      this.showEdit = false;
    },
  },
};
</script>

<style>
.lh36 {
  line-height: 36px;
  font-size: 14px;
}

.SearchForm {
  width: 100%;
  margin-bottom: 15px;
  background: #fff;
  border-radius: 6px;
  padding: 5px 0;
  padding-left: 5px;
  box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.2);
}

.btn-add {
  padding: 5px;
}

.red {
  color: red;
}

.green {
  color: green;
}

.course-table {
  text-align: center;
}

.course-table td,
.course-table th {
  padding: 5px 0;
  text-align: center;
}

.course-table button {
  padding: 2px;
}

.icon-stop {
  float: left;
  display: inline-block;
  width: 12px;
  height: 12px;
  background: url("../../icons/stop.png") no-repeat top center/contain;
}

.mt22 {
  margin-top: 22px;
}

.blue {
  margin-right: 50px;
  color: #409eff;
}

@media (max-width:767px) {
  .el-message-box {
    width: 80% !important;
  }
}
</style>
