<template>
  <!-- 流程实例 -->
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="88px">
      <el-form-item label="流程名称">
        <el-input class="filter-item" v-model="queryParams.processDefinitionName" :clearable="true" placeholder="流程名称" />
      </el-form-item>
      <el-form-item label="发起人">
        <el-input class="filter-item" v-model="queryParams.startUser" :clearable="true" placeholder="发起人" />
      </el-form-item>
      <el-form-item label="发起时间">
        <date-range class="filter-item" v-model="queryParams.createDate" :clearable="true" :allowTypes="['day']"
          align="left" range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" format="yyyy-MM-dd"
          value-format="yyyy-MM-dd HH:mm:ss" />
      </el-form-item>
      <el-form-item>
        <el-button type="success" icon="el-icon-search" size="mini" @click="getPageList">查询</el-button>
      </el-form-item>
    </el-form>
    <el-row>
      <el-col :span="24">
        <el-table ref="teacher" :data="tableList" size="mini" header-cell-class-name="table-header-gray">
          <el-table-column label="序号" header-align="center" align="center" type="index" width="55px"
            :index="tableList.getTableIndex" />
          <el-table-column label="流程名称" prop="processDefinitionName" />
          <el-table-column label="操作" width="150px">
            <template slot-scope="scope">
              <el-button class="table-btn success" size="mini" type="text" @click="onShowProcessViewer(scope.row)">流程图
              </el-button>
              <el-button class="table-btn primary" size="mini" type="text"
                :disabled="scope.row.endTime != null && scope.row.endTime != ''" @click="onStopTask(scope.row)">
                终止
              </el-button>
              <el-button class="table-btn delete" size="mini" type="text"
                :disabled="scope.row.endTime == null || scope.row.endTime == ''" @click="onDeleteTask(scope.row)">
                删除
              </el-button>
            </template>
          </el-table-column>
          <el-table-column label="流程标识" prop="processDefinitionKey" />
          <el-table-column label="任务发起人" prop="startUserId" />
          <el-table-column label="任务发起时间" prop="startTime" />
          <el-table-column label="任务结束时间" prop="endTime" />
        </el-table>
        <!-- 分页 -->
        <el-col :span="20">
          <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
            layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
            @current-change="handleCurrentChange" />
        </el-col>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import '@/api/activiti/staticDict/flowStaticDict.js';
import FormTaskProcessViewer from '@/views/activiti/workFlow/taskManager/formTaskProcessViewer';
import StopTask from '@/views/activiti/workFlow/taskManager/stopTask';
import { pageParamNames } from "@/utils/constants";
import flowOperationAPi from "@/api/activiti/flowOperation";

export default {
  name: 'formAllInstance',
  props: {},
  data() {
    return {
      queryParams: {
        processDefinitionName: undefined,
        startUser: undefined,
        createDate: []
      },
      formFilterCopy: {
        processDefinitionName: undefined,
        startUser: undefined,
        createDate: []
      },
      isInit: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      tableList: [],
    }
  },

  created() {
    this.getPageList();
  },
  methods: {
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val
      this.getPageList()
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val
      this.getPageList()

    },
    /**
     * 获取所有流程实例
     */
    getPageList() {
      let params = {
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.size,
        beginDate: this.queryParams.createDate[0],
        endDate: this.queryParams.createDate[1],
        processDefinitionName: this.queryParams.processDefinitionName,
        startUser: this.queryParams.startUser
      }
      flowOperationAPi.list(params).then(res => {
        this.tableList = res.data.data;
        this.loading = false;
        // 设置后台返回的分页参数
        pageParamNames.forEach(name => this.$set(this.tablePage, name, parseInt(res.data[name])))
      });
    },
    onShowProcessViewer(row) {
      this.$dialog.show('流程图', FormTaskProcessViewer, {
        area: ['90vw', '90vh']
      }, {
        processDefinitionId: row.processDefinitionId,
        processInstanceId: row.processInstanceId
      }).catch(e => {
      });
    },
    onStopTask(row) {
      this.$dialog.show('终止任务', StopTask, {
        area: '500px'
      }, {
        processInstanceId: row.processInstanceId,
        taskId: row.taskId
      }).catch(e => {
      });
    },
    onDeleteTask(row) {
      this.$confirm('是否删除此流程实例？').then(res => {
        return flowOperationAPi.deleteProcessInstance({
          processInstanceId: row.processInstanceId
        });
      }).then(res => {
        this.$message.success('删除成功');
      }).catch(e => {
      });
    },
    initFormData() {
    }
  },
}
</script>

<style></style>
