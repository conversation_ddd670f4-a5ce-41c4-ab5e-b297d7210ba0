/**
 * 渠道分润配置相关接口
 */
import request from '@/utils/request'

export default {
  // 分页查询
  pageList(params) {
    return request({
      url: '/znyy//merProfit/config/pageList',
      method: 'GET',
      params: params
    })
  },
  //根据id查询渠道分润信息
  getMerProfitConfigById(id) {
    return request({
      url: '/znyy//merProfit/config/getMerProfitConfigById/' + id,
      method: 'GET'
    })
  },
  // 获取渠道分润产品类型列表
  getProductTypeList() {
    return request({
      url: '/znyy//merProfit/config/getProductTypeList',
      method: 'GET'
    })
  },
  // 获取分润方列表
  getProfitPartyList() {
    return request({
      url: '/znyy//merProfit/config/getProfitPartyList',
      method: 'GET'
    })
  },
  // 根据分润方和产品类型获取分润配置
  getProfitPartyByProfitPartyAndProductType(profitParty, productType) {
    return request({
      url: '/znyy//merProfit/config/getProfitParty',
      method: 'GET',
      params: {
        profitParty: profitParty,
        productType: productType
      }
    })
  },
  // 保存/新增渠道分润配置
  save(data) {
    return request({
      url: '/znyy//merProfit/config/save',
      method: 'POST',
      data: data
    })
  },
  //删除
  delete(id) {
    return request({
      url: '/znyy//merProfit/config/delete/' + id,
      method: 'POST'
    })
  }
}
