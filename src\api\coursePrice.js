/**
 * 课程设置相关接口
 */
import request from '@/utils/request'

export default {
  // 分页查询
  coursePriceList(pageNum, pageSize, data) {
    return request({
      url: '/cousys/web/coursePrice/list/' + pageNum + '/' + pageSize,
      method: 'GET',
      params: data
    })
  },
  //添加
  coursePriceAdd(data) {
    return request({
      url: '/cousys/web/coursePrice/add',
      method: 'POST',
      data
    })
  },
  //修改状态
  coursePriceUpdateStatus(id,status) {
    return request({
      url: '/cousys/web/coursePrice/updateStatus/'+id,
      method: 'GET',
      params:{
        status:status
      }
    })
  },

  // 详情
  coursePriceDetail(id) {
    return request({
      url: '/cousys/web/coursePrice/view/' + id,
      method: 'GET'
    })
  },
  //编辑
  coursePriceUpdate(data) {
    return request({
      url: '/cousys/web/coursePrice/update',
      method: 'PUT',
      data
    })
  },
  //查询省市区
  getProvince(pid){
    return request({
      url: '/v2/mall/queryProvinceRegion/two/level',
      method: 'GET',
      params: {
        pid:pid
      }
    })
  },
  getAllRegion(){
    return request({
      url: '/v2/mall/queryAllRegion/two/level',
      method: 'GET'
    })
  }

}

