/**
 * 上课行为接口
 */
import request from '@/utils/request'

export default {
  //分页查询
  classRecordList(pageNum, pageSize, data) {
    return request({
      url: '/cousys/web/classRecord/list/' + pageNum + '/' + pageSize,
      method: 'GET',
      params: data
    })
  },
  // 详情
  classRecordDetail(id) {
    return request({
      url: '/cousys/web/classRecord/detail/' + id,
      method: 'GET'
    })
  },
  //删除
  classRecordDel(id){
    return request({
      url: '/cousys/web/classRecord/delete/' + id,
      method: 'GET'
    })
  },
  //评审
  appraisal(id, data) {
    return request({
      url: '/cousys/web/classRecord/appraisal/' + id,
      method: 'POST',
      data
    })
  },
}

