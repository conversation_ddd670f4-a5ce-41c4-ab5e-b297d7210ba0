<template>
  <div class="app-container">
    <el-form
      :inline="true"
      class="container-card"
      label-width="110px"
      label-position="left"
    >
      <el-row style="margin-bottom: 30px">
        <el-col :span="8" :xs="24">
          <el-form-item label="学员编码：">
            <el-input
              size="medium"
              v-model="dataQuery.studentCode"
              placeholder="请输入学员编码"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="阶段:" prop="phase">
            <el-select
              size="medium"
              v-model="dataQuery.phase"
              filterable
              value-key="value"
              placeholder="请选择"
              clearable
            >
              <el-option
                v-for="(item, index) in phaseTypeList"
                :key="index"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="打印状态：">
            <el-select
              size="medium"
              v-model="dataQuery.status"
              placeholder="全部"
              style="width: 185px"
            >
              <el-option
                v-for="(item, index) in [
                  { value: 1, label: '已打印' },
                  { value: 0, label: '未打印' },
                ]"
                :key="index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="16" :xs="24">
          <el-form-item>
            <el-form-item label="提交时间：">
              <el-date-picker
                size="medium"
                style="width: 100%"
                value-format="yyyy-MM-dd hh:mm:ss"
                clearable
                v-model="value1"
                type="daterange"
                align="right"
                unlink-panels
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              >
              </el-date-picker>
            </el-form-item>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item>
            <el-button
              type="primary"
              size="medium"
              icon="el-icon-search"
              @click="fetchData01()"
              >搜索</el-button
            >
            <el-button
              type="primary"
              size="medium"
              icon="el-icon-refresh"
              @click="fetchData03()"
              >重置</el-button
            >
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <el-table
      class="common-table"
      v-loading="tableLoading"
      :data="tableData"
      style="width: 100%; margin-bottom: 20px"
      row-key="id"
      border
      default-expand-all
      :tree-props="{ list: 'children', hasChildren: 'true' }"
    >
      <el-table-column prop="studentCode" label="学员编码"></el-table-column>
      <el-table-column prop="studentName" label="姓名"></el-table-column>
      <el-table-column prop="withdrawnBonus" label="操作">
        <template slot-scope="scope">
          <el-button
            type="success"
            size="medium"
            icon="el-icon-edit-outline"
            @click="jumpOpenCourse(scope.row)"
            >打印</el-button
          >
        </template>
      </el-table-column>
      <el-table-column prop="phase" label="阶段"></el-table-column>
      <el-table-column prop="createTime" label="完成时间"></el-table-column>
      <el-table-column prop="status" label="状态">
        <template slot-scope="scope">
          <span class="blue" v-if="scope.row.status === 1">已打印</span>
          <span class="red" v-else>未打印</span>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <el-col :span="24" style="overflow-x: auto" :xs="24">
      <el-pagination
        :current-page="tablePage.currentPage"
        :page-sizes="[10, 20, 30, 40, 50]"
        :page-size="tablePage.size"
        layout="total, sizes, prev, pager, next, jumper"
        :total="tablePage.totalItems"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-col>
  </div>
</template>

<script>
import { pageAPI } from "@/api/grammar/phaseCredentialList";
import { queryByTypeAPI } from "@/api/grammar/knowledgePoint";
import { pageParamNames } from "@/utils/constants";
import ls from "@/api/sessionStorage";
export default {
  name: "areasStudentWordPrintList",
  data() {
    return {
      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null,
      },
      tableData: [],
      phaseTypeList: [], // 阶段
      dataQuery: {
        studentCode: "",
        phase: "",
        status: "",
        startTime: "",
        endTime: "",
        type: "PhaseCredential",
      },
      studyRank: [],
      value1: "",
      exportLoading: false,
    };
  },
  created() {
    this.getType();
  },
  methods: {
    // 获取分类返回类型
    getType() {
      queryByTypeAPI({ dictType: "grammar_phase" }).then((response) => {
        this.phaseTypeList = response.data.map((item) => ({
          id: item.dictValue,
          name: `${item.dictLabel}` + "语法",
        }));
      });
      this.fetchData();
    },

    fetchData01() {
      this.tablePage = {
        currentPage: 1,
        size: this.tablePage.size,
        totalPage: null,
        totalItems: null,
      };
      if (!this.dataQuery.phase) {
        this.dataQuery.phase = "";
      }
      this.fetchData();
    },
    fetchData03() {
      // this.tablePage = {
      //   currentPage: 1,
      //   size: 10,
      //   totalPage: null,
      //   totalItems: null,
      // };
      this.$set(this.tablePage, "currentPage", 1);
      this.$set(this.tablePage, "size", 10);
      this.dataQuery = {
        studentCode: "",
        status: "",
        phase: "",
        startTime: "",
        endTime: "",
        type: "PhaseCredential",
      };
      this.value1 = "";
      console.log(this.tablePage);
      this.fetchData();
    },

    // 查询提现列表
    fetchData() {
      if (this.value1) {
        if (this.value1.length > 0) {
          this.dataQuery.startTime = this.value1[0];
          this.dataQuery.endTime = this.value1[1];
        } else {
          this.dataQuery.startTime = "";
          this.dataQuery.endTime = "";
        }
      } else {
        this.dataQuery.startTime = "";
        this.dataQuery.endTime = "";
      }
      this.tableLoading = true;
      const pageParam = {
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.size,
      };
      pageAPI(this.dataQuery, pageParam, this.dataQuery.phase).then((res) => {
        this.tableData = res.data.data;
        res.data.data.forEach((item) => {
          const foundPhase = this.phaseTypeList.find(
            (phaseItem) => phaseItem.id === item.phase
          );
          if (foundPhase) {
            item.phase = foundPhase.name;
          }
        });
        this.tableLoading = false;
        // 设置后台返回的分页参数
        pageParamNames
          .filter((i) => i !== "currentPage")
          .forEach((name) =>
            this.$set(this.tablePage, name, parseInt(res.data[name]))
          );
      });
    },
    // 分页
    handleSizeChange(val) {
      console.log("1111", val);
      this.tablePage.size = val;
      this.fetchData();
    },
    handleCurrentChange(val) {
      console.log(val, "currentPage");
      this.tablePage.currentPage = val;
      this.fetchData();
    },
    // 跳转到打印页面
    jumpOpenCourse(row) {
      this.$router.push({
        path: "/syntax/phaseCreadentialPrint",
        query: {
          phaseCreadentialPrintId: row.id,
          phase: row.phase,
          studentName: row.studentName,
          studentCode: row.studentCode,
          addTime: row.createTime,
          grammarCount: row.grammarNum,
          knowledgeCount: row.knowledgeNum,
        },
      });
    },
  },
};
</script>

<style scoped lang="scss">
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.period-table td,
.period-table th {
  text-align: center;
}

.mt20 {
  margin-top: 20px;
}

.red {
  color: red;
}

.green {
  color: green;
}

.blue {
  color: blue;
}
</style>
