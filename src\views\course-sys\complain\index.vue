<template>
  <div class="app-container">
    <el-form :inline="true" class="container-card" label-width="96px" label-position="left">
      <el-form-item label="起止时间">
        <el-date-picker style="width: 100%;" v-model="createTime" type="daterange" value-format="yyyy-MM-dd HH:mm:ss"
          align="right" unlink-panels range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="状态:">
        <el-select v-model="dataQuery.status" filterable value-key="value" placeholder="请选择">
          <el-option v-for="(item, index) in [{ value: 0, label: '进行中' }, { value: 1, label: '已处理' }]" :key="index"
            :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="教练 名称：">
        <el-input v-model="dataQuery.tutorName" placeholder="请输入教练 名称" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="getList()">搜索</el-button>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-re" @click="resetQuery()">重置</el-button>
      </el-form-item>
    </el-form>
    <el-table class="common-table" v-loading="tableLoading" :data="tableData" height="400px"
      style="width: 100%;margin-bottom: 20px;" row-key="id" border default-expand-all
      :tree-props="{ list: 'children', hasChildren: 'true' }">
      <el-table-column prop="id" label="投诉ID"></el-table-column>
      <el-table-column prop="parentName" label="投诉人"></el-table-column>
      <el-table-column label="操作" align="center" width="200">
        <template slot-scope="scope">
          <el-button type="primary" icon="el-icon-view" @click="handleView(scope.row)" size="mini">查看</el-button>
          <el-button type="danger" icon="el-icon-del" @click="handleDel(scope.row)" size="mini">删除</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="tutorName" label="被投诉人"></el-table-column>
      <el-table-column prop="reason" label="投诉原因" show-overflow-tooltip></el-table-column>
      <el-table-column prop="remark" label="问题描述" show-overflow-tooltip></el-table-column>
      <el-table-column prop="createTime" label="投诉时间"></el-table-column>
      <el-table-column prop="status" label="状态">
        <template slot-scope="scope">
          <span v-if="scope.row.status == 0">进行中</span>
          <span v-if="scope.row.status == 1">已处理</span>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog class="recharge-dialog" title="投诉详情" :visible.sync="open" width="70%" :close-on-click-modal="false">
      <el-form ref="form" :model="form" label-width="130px" :rules="rules" style="width: 100%;" @close="reset">
        <el-form-item label="投诉人：" prop="parentName">
          {{ form.parentName }}
        </el-form-item>
        <el-form-item label="联系电话：" prop="parentTel">
          {{ form.parentTel }}
        </el-form-item>
        <el-form-item label="被投诉人 ：" prop="tutorName">
          {{ form.tutorName }}
        </el-form-item>
        <el-form-item label="投诉原因 ：" prop="reason">
          {{ form.reason }}
        </el-form-item>
        <el-form-item label="问题描述 ：" prop="remark">
          {{ form.remark }}
        </el-form-item>
        <el-form-item label="投诉时间 ：" prop="createTime">
          {{ form.createTime }}
        </el-form-item>
        <el-form-item label="投诉截图 ：" prop="pic">
          <el-image v-for="img in files" :key="img" style="width: 100px; height: 100px;margin-right: 10px" :src="img"
            :preview-src-list="files"></el-image>
        </el-form-item>
        <el-form-item label="处理状态 ：" prop="status">
          {{ statusList[form.status] }}
        </el-form-item>
        <el-divider></el-divider>
        <el-form-item label="处理方案 ：" prop="handingResult">
          <el-input type="textarea" :rows="5" v-model="form.handingResult" placeholder="请输入处理方案" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button v-if="form.status === 0" size="mini" type="primary" @click="handleSave()">确定</el-button>
        <el-button v-if="form.status === 1" size="mini" @click="cancel()">关闭</el-button>
      </div>
    </el-dialog>

    <!-- 分页 -->
    <el-col :span="20" style="overflow-x: auto;" :xs="24">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>
  </div>
</template>

<script>

import complainApi from '@/api/complain'
import { pageParamNames } from "@/utils/constants";
export default {
  data() {
    return {
      files: [],
      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      tableData: [],
      open: false,
      dataQuery: {
        status: null,
        tutorName: null,
        startTime: null,
        endTime: null
      },
      createTime: [],
      form: {},
      statusList: {
        0: "进行中",
        1: "已处理"
      },
      rules: {
        handingResult: [
          { required: true, message: "请输入处理方案", trigger: "blur" }
        ],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    //获取列表
    getList() {
      const that = this;
      var a = that.createTime;
      if (a != null) {
        that.dataQuery.startTime = a[0];
        that.dataQuery.endTime = a[1];
      } else {
        that.dataQuery.startTime = null;
        that.dataQuery.endTime = null;
      }
      that.tableLoading = true;
      complainApi.complainList(that.tablePage.currentPage, that.tablePage.size, that.dataQuery).then(res => {
        that.tableData = res.data.data
        console.log(res.data.data);
        that.tableLoading = false
        // 设置后台返回的分页参数
        pageParamNames.forEach(name => this.$set(this.tablePage, name, parseInt(res.data[name])))
      })
    },

    //重置按钮
    resetQuery() {
      this.createTime = [];
      this.dataQuery = {
        status: null,
        tutorName: null,
        startTime: null,
        endTime: null
      };
      this.getList();
    },
    //查看按钮
    handleView(row) {
      this.reset();
      complainApi.complainDetail(row.id).then(response => {
        this.form = response.data;
        response.data.files.forEach(item => {
          this.files.push(this.aliUrl + item);
        });
        console.log(response.data);
        this.open = true;
      });
    },
    //提交处理方案
    handleSave() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.status !== 1) {
            complainApi.complainUpdate(this.form.id, this.form).then(res => {
              this.$message.success('提交成功');
              this.open = false;
              this.getList();
            })
          }
        }
      });
    },
    //删除按钮
    handleDel(row) {
      this.$confirm('确定要删除该反馈吗?', '删除反馈', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        complainApi.complainDel(row.id).then(res => {
          this.getList();
          this.$message.success('删除成功!')
        }).catch(err => {
        })
      }).catch(err => {
      })
    },
    reset() {
      this.files = [];
      this.form = {
        id: null,
        parentName: null,
        parentTel: null,
        tutorName: null,
        status: null,
        reason: null,
        remark: null,
        pic: null,
        handingResult: null,
      };
      if (this.$refs['form']) {
        this.$refs['form'].resetFields();
      }
    },
    cancel() {
      this.open = false;
      this.reset();
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val;
      this.getList();
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val;
      this.getList();
    },
  }
};
</script>

<style>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

@media screen and (max-width: 767px) {
  .recharge-dialog .el-dialog {
    width: 90% !important;
  }

  .el-message-box {
    width: 80% !important;
  }
}
</style>
