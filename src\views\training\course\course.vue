<template>
  <div class="app-container">
    <el-form :inline="true" ref="queryForm" class="container-card" label-width="90px">
      <el-form-item label="课程名称：">
        <el-input v-model="form.courseName" placeholder="请输入" clearable />
      </el-form-item>
      <el-form-item label="创建时间：">
        <el-date-picker
          v-model="form.createTime"
          value-format="yyyy-MM-dd"
          type="daterange"
          align="right"
          unlink-panels
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <!-- <el-form-item label="类型：">
        <el-select v-model="form.courseType" clearable placeholder="请选择">
          <el-option v-for="item in courseType" :key="item.dictCode" :label="item.dictLabel" :value="item.dictValue"></el-option>
        </el-select>
      </el-form-item> -->
      <el-form-item>
        <el-button icon="el-icon-refresh" @click="resetQuery()">重置</el-button>
        <el-button icon="el-icon-search" type="primary" @click="search(1)">查询</el-button>
      </el-form-item>

      <!-- 添加 -->
      <div class="btn-add" style="margin-bottom: 10px">
        <el-button type="primary" icon="el-icon-plus" @click="clickAdd">新建课程</el-button>
      </div>
    </el-form>

    <div class="SearchForm">
      <!-- 表格 -->
      <el-table
        class="common-table"
        :data="tableData"
        stripe
        border
        :default-sort="{ prop: 'createTime', order: 'descending' }"
        v-loading.body="tableLoading"
        element-loading-text="加载中"
      >
        <!-- <el-table-column type="index" width="100" label="序号"> </el-table-column> -->
        <!-- <el-table-column prop="categoryCode"  label="创建人"></el-table-column> -->
        <el-table-column prop="courseName" label="课程名称">
          <template slot-scope="scope">
            <el-popover placement="top-start" width="300" trigger="hover" :disabled="scope.row.courseName.length <= 10">
              <div>{{ scope.row.courseName }}</div>
              <span slot="reference" v-if="scope.row.courseName.length <= 10">{{ scope.row.courseName }}</span>
              <span slot="reference" v-if="scope.row.courseName.length > 10">{{ scope.row.courseName.substr(0, 10) + '...' }}</span>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column prop="lessonCount" label="课时数"></el-table-column>
        <el-table-column prop="courseNo" label="课程号"></el-table-column>
        <!-- <el-table-column prop="categoryCode" label="培训角色"></el-table-column> -->
        <!-- <el-table-column prop="courseType" label="类型">
          <template slot-scope="scope">
            <span>{{ scope.row.courseType | queryByType(courseType, scope.row.courseType) }}</span>
          </template> -->
        <!-- queryByType -->
        <!-- </el-table-column> -->
        <!-- <el-table-column prop="categoryCode" label="试卷操作" ></el-table-column> -->
        <el-table-column prop="isPaperConfigured" label="是否需要考试">
          <template slot-scope="scope">
            <span>{{ scope.row.isPaperConfigured == 1 ? '是' : '否' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="courseStatus" label="试卷配置">
          <template slot-scope="scope">
            <span v-if="scope.row.isPaperConfigured == 0">-</span>
            <span v-else>{{ scope.row.courseStatus == 0 ? '未配置' : '已配置' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="courseOverview" label="课程简介">
          <template slot-scope="scope">
            <el-popover placement="top-start" width="300" trigger="hover" :disabled="scope.row.courseOverview.length <= 10">
              <div>{{ scope.row.courseOverview }}</div>
              <span slot="reference" v-if="scope.row.courseOverview.length <= 10">{{ scope.row.courseOverview }}</span>
              <span slot="reference" v-if="scope.row.courseOverview.length > 10">{{ scope.row.courseOverview.substr(0, 10) + '...' }}</span>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间"></el-table-column>
        <!-- <el-table-column prop="categoryCode" label="满意度调查" ></el-table-column> -->
        <el-table-column label="操作" width="280px">
          <template slot-scope="scope">
            <el-button size="mini" @click="handleDetail(scope.row.id)">查看</el-button>
            <el-button type="success" size="mini" icon="el-icon-edit-outline" @click="handleUpdate(scope.row.id)">编辑</el-button>
            <el-button @click="handleDel(scope.row.id)" type="danger" size="mini" icon="el-icon-view">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- <addCourse ref="addCourseRefs" v-if="dialogFrom.visible" :dialog-param="dialogFrom" @closeDialog="closeDialog" ></addCourse> -->
      <add-course-setting ref="addCourseRefs" v-if="dialogFrom.visible" :dialog-param="dialogFrom" @closeDialog="closeDialog"></add-course-setting>
    </div>
    <!-- 分页 -->
    <el-col :span="24" style="margin-bottom: 20px">
      <el-pagination
        :current-page="page_data.currentPage"
        :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="page_data.totalItems"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-col>
  </div>
</template>

<script>
  import addCourse from './components/addCourse.vue';
  import addCourseSetting from './components/addCourseSettings.vue';
  import courseApi from '@/api/training/course';
  // courseList
  export default {
    name: 'course',
    components: {
      addCourse,
      addCourseSetting
    },
    filters: {
      queryByType(type, list) {
        var index = list.findIndex((item) => {
          if (item.dictValue == type) {
            return true;
          }
        });
        if (index != -1) {
          return list[index].dictLabel;
        } else {
          return '未知';
        }
      }
    },
    data() {
      return {
        tableLoading: false,
        dialogFrom: {
          visible: false,
          type: 'add'
        },
        // 分页
        page_data: {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: 0
        },
        form: {
          courseName: '',
          // type: "",
          createTime: []
        },
        tableData: [],
        courseType: []
      };
    },
    created() {
      this.getQueryByType();
      this.search(1);
    },
    methods: {
      // 分页
      handleSizeChange(val) {
        this.page_data.size = val;
        this.search();
      },

      handleCurrentChange(val) {
        this.page_data.currentPage = val;
        this.search();
      },
      getQueryByType() {
        courseApi.dictQueryByType({ dictType: 'paper_course_category' }).then((res) => {
          this.courseType = res.data;
        });
      },
      // 重置
      resetQuery() {
        this.form.courseName = '';
        this.form.createTime = [];
        this.page_data = {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: 0
        };
        this.search();
      },

      // 搜索
      search(page) {
        if (typeof page == 'number' && page > 0) {
          this.page_data.currentPage = page;
        }
        this.tableLoading = true;
        let param = { pageSize: this.page_data.size, pageNum: page || this.page_data.currentPage };
        let creatTime = {
          creatTimeStart: this.form.createTime[0] ? this.form.createTime[0] + ' 00:00:00' : '',
          creatTimeEnd: this.form.createTime[1] ? this.form.createTime[1] + ' 23:59:59' : ''
        };
        param = { courseName: this.form.courseName, ...creatTime, ...param };
        this.tableData = [];
        courseApi.courseList(param).then((res) => {
          this.tableData = res.data.data;
          this.page_data.totalItems = Number(res.data.totalItems);
          this.tableLoading = false;
        });
      },

      // 新增
      clickAdd() {
        this.dialogFrom.visible = true;
        this.dialogFrom.type = 'add';
      },
      // 查看
      handleDetail(id) {
        this.dialogFrom.visible = true;
        this.dialogFrom.type = 'detail';
        this.$nextTick(() => {
          this.$refs.addCourseRefs.open(id);
        });
      },
      // 编辑
      handleUpdate(id) {
        this.dialogFrom.visible = true;
        this.dialogFrom.type = 'edit';
        this.$nextTick(() => {
          this.$refs.addCourseRefs.open(id);
        });
      },

      // 删除
      handleDel(id) {
        this.$confirm('课程删除后，将会影响课程的观看和考试入口的开放，请确认是否删除？', '课程删除确认', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            courseApi.courseDelete({ id: id }).then((res) => {
              this.$message({
                type: 'success',
                message: '删除成功'
              });
              this.resetQuery();
            });
          })
          .catch(() => {
            this.$message({
              type: 'info',
              message: '已取消删除'
            });
          });
      },
      closeDialog(info) {
        this.dialogFrom.visible = false;
        if (!info || info !== 'detail') {
          this.search();
        }
      }
    }
  };
</script>

<style>
  .SearchForm {
    width: 100%;
    margin-bottom: 15px;
    background: #fff;
    border-radius: 6px;
    padding: 5px 0;
    padding-left: 5px;
    box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.2);
  }
  .container-card {
    padding: 15px 15px 15px 15px;
    margin-bottom: 15px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
    position: relative;
  }
  .btn-add {
    position: absolute;
    top: 50%;
    right: 20px;
    transform: translateY(-70%);
  }

  .period-table td,
  .period-table th {
    text-align: center;
  }

  @media (max-width: 767px) {
    .el-message-box {
      width: 80% !important;
    }
  }

  .el-tooltip__popper {
    max-width: 800px;
  }
</style>
