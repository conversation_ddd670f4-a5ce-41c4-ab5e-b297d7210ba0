<!-- 体验课拼读拼写列表 -->
<template>
  <div class="container_box">
    <el-button type="primary" @click="onAddspell" style="margin-bottom: 20px" icon="el-icon-plus" size="mini">添加</el-button>
    <el-table :data="spellData" class="common-table" stripe border v-loading="spellLoading">
      <el-table-column label="单词ID" prop="id" width="200" sortable>
        <template slot-scope="{ row }">
          <span>{{ row.id }}</span>
        </template>
      </el-table-column>
      <el-table-column label="单词" prop="word" width="160" sortable></el-table-column>
      <el-table-column label="音节" prop="syllable" width="160" sortable></el-table-column>
      <el-table-column label="单词拆分" prop="split" sortable></el-table-column>
      <el-table-column label="重音" prop="accent" width="160" sortable></el-table-column>
      <el-table-column v-if="courseLevel == 2" label="次重音" prop="secondaryAccent" width="160" sortable></el-table-column>
      <el-table-column label="单词分类" prop="type" width="160" sortable>
        <template slot-scope="{ row }">
          <span>拼读词</span>
          <span>{{ row.isWriteSelect == '1' ? '拼写词' : '' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="单词检测分类" prop="type" width="160" sortable>
        <template slot-scope="{ row }">
          <span>{{ row.isSelect == '1' ? '学后读' : row.isSelect == '2' ? '学后写' : row.isSelect == '3' ? '学后读写' : '' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" prop="id" width="350" sortable>
        <template slot-scope="{ row }">
          <div style="text-align: left">
            <el-button type="primary" size="mini" icon="el-icon-edit" @click="editspellItem(row.id, row.type, row.wordCode)">编辑</el-button>
            <el-button type="danger" size="mini" icon="el-icon-delete" @click="delSyllableItem(row.id)">删除</el-button>
            <el-button class="botton_focus" size="mini" icon="el-icon-top" v-if="row.previousId" @click="moveUpItem(row)">上移</el-button>
            <el-button class="botton_focus" size="mini" icon="el-icon-bottom" v-if="row.nextId" @click="moveDownItem(row)">下移</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <ExperienceStepForm :spellId="spellId" :code="code" :spellType="spellType" @spellDialogClose="spellDialogClose" :spellDialogVisible="spellDialogVisible"></ExperienceStepForm>
  </div>
</template>
<script>
  import StepForm from '@/views/course/courseDictation/components/StepForm.vue';
  import ExperienceStepForm from '../components/experienceStepForm.vue';
  import AddDictationWord from '@/views/course/courseDictation/addDictationWord.vue';
  import courseDictationListApi from '@/api/courseDictationList';
  export default {
    components: { AddDictationWord, ExperienceStepForm },
    data() {
      return {
        spellData: [],
        spellLoading: false,
        spellDialogVisible: false,
        isSpell: true,
        courseCode: '',
        courseId: this.$route.query.id,
        spellId: '',
        code: '',
        spellType: 0,
        courseLevel: ''
      };
    },

    created() {
      this.courseLevel = this.$route.query.courseLevel;
      this.courseCode = this.$route.query.courseCode;
      this.getTableData();
    },
    methods: {
      //获取列表
      getTableData() {
        this.spellLoading = true;
        courseDictationListApi.getReadAndWriteList(this.courseCode).then((res) => {
          if (res.code === 20000) {
            this.spellData = res.data.data;
            this.spellLoading = false;
          }
        });
      },
      //添加划拼音规则
      onAddspell() {
        this.spellId = '';
        this.code = '';
        this.spellDialogVisible = true;
        this.$bus.$emit('clearData');
      },
      //编辑划音节
      editspellItem(id, type, code) {
        this.spellDialogVisible = true;
        this.spellId = id;
        this.spellType = type;
        this.code = code;
      },
      //删除
      delSyllableItem(id) {
        courseDictationListApi.deleteWordById(id, 2).then((res) => {
          if (res.code === 20000) {
            this.$message.success('删除成功');
            this.getTableData();
          }
        });
      },
      //新增/编辑
      onSubmit() {},
      //关闭弹窗
      spellDialogClose() {
        this.spellDialogVisible = false;
        this.spellId = '';
        this.code = '';
        this.getTableData();
      },
      // 上移
      moveUpItem(row) {
        console.log('row-------------shangyi', row);
        courseDictationListApi
          .wordUp({
            currentId: row.id,
            nextId: row.previousId
          })
          .then((res) => {
            if (res.code === 20000) {
              this.$message.success(`${row.word}` + '上移成功');
              this.getTableData();
            } else {
              this.$message.error(`${res.message}`);
            }
          });
      },
      // 下移
      moveDownItem(row) {
        courseDictationListApi
          .wordDown({
            currentId: row.id,
            nextId: row.nextId
          })
          .then((res) => {
            if (res.code === 20000) {
              this.$message.success(`${row.word}` + '下移成功');
              this.getTableData();
            } else {
              this.$message.error(`${res.message}`);
            }
          });
      }
    }
  };
</script>
<style lang="less" scoped>
  .botton_focus:focus {
    color: #606266 !important;
    background-color: transparent !important;
    border-color: #dcdfe6 !important;
  }
  .botton_focus:hover {
    color: #1890ff !important;
    background-color: #e8f4ff !important;
    border-color: #badeff !important;
  }
</style>
