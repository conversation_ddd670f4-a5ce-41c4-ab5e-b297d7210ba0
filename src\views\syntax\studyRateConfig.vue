<template>
  <div class="app-container">
    <div class="flex-container">
      <span class="red-star">*</span>
      <span style="margin-bottom: 18px; color: #8a8c92">
        Tips： 配置语法点需要注意数据断层</span
      >
    </div>
    <div class="SearchForm">
      <!-- 表格 -->
      <el-table class="common-table" :data="tableData" v-loading="tableLoading">
        <el-table-column prop="name" label="掌握度"></el-table-column>
        <el-table-column prop="firstValue" label="最低正确率"></el-table-column>
        <el-table-column
          prop="secondValue"
          label="最高正确率"
        ></el-table-column>
        <el-table-column prop="id" label="操作">
          <template slot-scope="scope">
            <el-button
              type="success"
              size="medium"
              icon="el-icon-edit-outline"
              @click="openEdit(scope.row)"
              >编辑</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </div>

    <el-dialog
      title="掌握度配置"
      :visible.sync="showEdit"
      width="45%"
      :close-on-click-modal="false"
      @close="showEdit = false"
    >
      <el-form
        :ref="'updateGrammar'"
        :rules="updateSingle"
        :model="updateGrammar"
        label-position="left"
        label-width="80px"
        style="width: 100%"
      >
        <el-form-item label="id" prop="id" v-show="false">
          <el-col :xs="24" :span="12">
            <el-input v-model="updateGrammar.id"></el-input>
          </el-col>
        </el-form-item>
        <el-form-item
          label="最低正确率"
          prop="firstValue"
          label-width="120px"
          style="margin-bottom: 30px"
        >
          <el-col :xs="24" :span="12">
            <el-input v-model="updateGrammar.firstValue"></el-input>
          </el-col>
        </el-form-item>
        <el-form-item label="最高正确率" prop="secondValue" label-width="120px">
          <el-col :xs="24" :span="12">
            <el-input v-model="updateGrammar.secondValue"></el-input>
          </el-col>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button
          size="medium"
          type="primary"
          @click="editConfig('updateGrammar')"
          >确定</el-button
        >
        <el-button size="medium" @click="closeEdit">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listAPI, updateAPI } from "@/api/grammar/configUration";
export default {
  data() {
    return {
      tableLoading: false,
      updateSingle: {
        firstValue: [
          {
            required: true,
            message: "必填",
            trigger: "blur",
          },
        ],
        secondValue: [
          {
            required: true,
            message: "必填",
            trigger: "blur",
          },
        ],
      },
      tableData: [], //表格数据
      showEdit: false, //编辑弹窗
      updateGrammar: {}, // 修改数据
      editId: "", // 修改id
    };
  },
  created() {
    this.fetchData();
  },
  methods: {
    // 查询表格列表
    fetchData() {
      listAPI({ configType: 3 }).then((res) => {
        this.tableData = res.data;
      });
    },
    openEdit(row) {
      this.showEdit = true;
      this.editId = row.id;
      // 使用深拷贝创建临时数据对象
      this.updateGrammar = JSON.parse(JSON.stringify(row));
    },
    editConfig(ele) {
      this.$refs[ele].validate((valid) => {
        if (valid) {
          updateAPI({
            id: Number(this.editId),
            firstValue: this.updateGrammar.firstValue,
            secondValue: this.updateGrammar.secondValue,
          })
            .then((res) => {
              this.showEdit = false;
              // 更新列表数据
              const index = this.tableData.findIndex(
                (item) => item.id === this.editId
              );
              if (index !== -1) {
                // 使用扩展运算符确保只更新修改的属性
                this.tableData[index] = {
                  ...this.tableData[index],
                  ...this.updateGrammar,
                };
              }
              this.fetchData();
              this.$message.success("语法配置成功");
            })
            .catch(() => {
              this.$message.error("语法配置失败");
              return false;
            });
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    closeEdit() {
      this.showEdit = false;
    },
  },
};
</script>

<style scoped lang="scss">
.lh36 {
  line-height: 36px;
  font-size: 14px;
}
.flex-container {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  margin-left: 20px;
}

.red-star {
  color: red;
  font-size: 40px;
  margin-right: 5px;
}
.SearchForm {
  width: 100%;
  margin-bottom: 15px;
  background: #fff;
  border-radius: 6px;
  padding: 5px 0;
  padding-left: 5px;
  box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.2);
}

.btn-add {
  padding: 5px;
}

.red {
  color: red;
}

.green {
  color: green;
}

.course-table {
  text-align: center;
}

.course-table td,
.course-table th {
  padding: 5px 0;
  text-align: center;
}

.course-table button {
  padding: 2px;
}

.icon-stop {
  float: left;
  display: inline-block;
  width: 12px;
  height: 12px;
  background: url("../../icons/stop.png") no-repeat top center/contain;
}

.mt22 {
  margin-top: 22px;
}

.blue {
  margin-right: 50px;
  color: #409eff;
}

@media (max-width: 767px) {
  .el-message-box {
    width: 80% !important;
  }
}
</style>
