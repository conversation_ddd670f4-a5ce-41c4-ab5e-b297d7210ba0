<template>
  <div>
    <div element-loading-spinner="el-icon-loading" element-loading-text="加载中..." v-loading="loading">
      <el-card shadow="never" class="card">
        <el-form ref="searchForm" :model="searchForm" :inline="true">
          <el-form-item :label="phoneName" prop="partnerMobile">
            <el-input v-model="searchForm.partnerMobile" placeholder="请输入推荐人手机号" clearable maxlength="11"></el-input>
          </el-form-item>
          <el-form-item label="合同状态" prop="contractStatus">
            <el-select v-model="searchForm.contractStatus" clearable placeholder="请选择">
              <el-option v-for="item in options1" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item :label="stateName" prop="clubStatus">
            <el-select v-model="searchForm.clubStatus" clearable placeholder="请选择">
              <el-option v-for="item in options2" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item>
            <el-button type="primary" @click="onSubmit()" size="small">搜索</el-button>
            <el-button size="small" @click="reset">重置</el-button>
          </el-form-item>
        </el-form>
        <div class="new-channel">
          <el-button type="primary" size="mini" @click="openAddChannel">{{ roleTag === 'Operations' ? '新增渠道伙伴' : '新增推广大使' }}</el-button>
          <img slot="reference" class="img-question" src="../../assets/images/studyRoom/question.png" @click="openFlowChart()" alt="" />
        </div>
        <div id="charts_one" style="min-height: 80vh">
          <el-table :header-cell-style="{ background: '#eef1f6', color: '#606266' }" ref="multipleTable" border :data="list">
            <el-table-column v-if="roleTag == 'Operations'" align="center" property="enterpriseName" label="企业名称" width="220"></el-table-column>
            <el-table-column align="center" property="partnerMobile" :label="phoneName" width="220"></el-table-column>
            <el-table-column align="center" property="name" label="姓名" width="220"></el-table-column>
            <el-table-column align="center" property="contractStatus" label="合同状态" width="220">
              <template v-slot="{ row }">
                <span v-if="row.contractStatus == 1">未签署</span>
                <span v-if="row.contractStatus == 2" style="color: #32ce66">已签署</span>
                <span v-if="row.contractStatus == 3" style="color: #32ce66">签署失败</span>
              </template>
            </el-table-column>
            <el-table-column align="center" property="status" :label="stateName" width="220">
              <template v-slot="{ row }">
                <span v-if="row.status == 0">未创建</span>
                <span v-if="row.status == 1">已创建</span>
              </template>
            </el-table-column>
            <el-table-column align="center" property="affiliatedBrand" :label="brand" width="220">
              <template v-slot="{ row }">
                <span v-if="row.affiliatedBrand">{{ row.affiliatedBrand }}</span>
                <span v-if="row.affiliatedBrand == ''">-</span>
              </template>
            </el-table-column>
            <el-table-column align="center" property="crateTime" label="创建时间"></el-table-column>
            <el-table-column label="操作" fixed="right" align="center">
              <template #header>
                操作
                <el-tooltip placement="bottom">
                  <div slot="content">
                    若该新增的手机号已与其他
                    <br />
                    俱乐部签署协议，则无法再
                    <br />
                    查看合同二维码
                  </div>
                  <i class="el-icon-question"></i>
                </el-tooltip>
              </template>
              <template #default="scope">
                <div v-if="JudgeContract(scope.row)">
                  <el-button size="mini" type="primary" @click="viewQR(scope.row)">查看合同二维码</el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
          <!-- 分页 -->
          <Page :total="page_data.total" :page.sync="page_data.page" :limit.sync="page_data.pageSize" @pagination="index" />
        </div>
      </el-card>
      <el-dialog :title="title1" :visible.sync="channelDialogVisible" width="30%" style="" :before-close="handleAddClose">
        <div>
          <el-form ref="inputForm" :rules="rules" label-position="left" :model="inputForm" label-width="30%">
            <el-form-item prop="enterprise_name" v-if="roleTag === 'Operations'">
              <span slot="label">
                企业名称
                <el-tooltip placement="bottom">
                  <div slot="content">
                    支持公司或个体工商户,
                    <br />
                    如未创建可先在此输入名称
                    <br />
                    后再注册该名称的企业
                  </div>
                  <i class="el-icon-question"></i>
                </el-tooltip>
              </span>
              <!-- <el-input type="phone" v-model="inputForm.enterprise_name" placeholder="请输入该俱乐部的企业名称"></el-input> -->
              <el-autocomplete style="width: 300px" clearable @clear="setBlur()" @input="handle" :fetch-suggestions="querySearch" :trigger-on-focus="false" @select="handleSelect" class="inline-input" v-model="inputForm.enterprise_name" placeholder="请输入该俱乐部的企业名称"></el-autocomplete>
            </el-form-item>
            <el-form-item label="姓名" prop="name" label-width="30%">
              <el-input type="phone" v-model="inputForm.name" placeholder="请输入真实姓名"></el-input>
            </el-form-item>
            <el-form-item :label="phoneName" prop="partnerMobile" label-width="30%">
              <el-input type="phone" v-model="inputForm.partnerMobile" placeholder="请输入手机号" maxlength="11"></el-input>
            </el-form-item>
          </el-form>
        </div>
        <div slot="footer">
          <el-button @click="closePopup()">取 消</el-button>
          <el-button type="primary" @click="ensure(inputForm)">确 定</el-button>
        </div>
      </el-dialog>
      <el-dialog :visible.sync="flowDialogVisible" width="40%">
        <div>
          <div class="f-c title">{{ roleTag === 'Operations' ? '新增渠道伙伴' : '新增推广大使' }}流程图</div>
          <el-steps :active="3" process-status="finish" align-center>
            <el-step :title="title2" description="填写手机号生成合同链接及二维码"></el-step>
            <el-step title="签署合同" :description="description1"></el-step>
            <el-step :title="title3" :description="description2"></el-step>
          </el-steps>
        </div>
      </el-dialog>
      <el-dialog title="签署合同" :visible.sync="QRdialogVisible" width="40%" v-loading="watchQR" :before-close="handleClose">
        <div class="f-c-d">
          <div class="title1">签署合同后，该{{ roleTag === 'Operations' ? '俱乐部' : '门店' }}将成为您的{{ roleTag === 'Operations' ? '渠道合作伙伴' : '课程推广大使' }}</div>
          <div class="img-box">
            <div class="f-c-d">
              <div>请您使用手机扫码签署</div>
              <div class="m-t m-b">《{{ roleTag === 'Operations' ? '渠道合作伙伴' : '课程推广大使' }}协议》</div>

              <div class="box-mask">
                <img class="img-QR" :src="QRImg1" alt="" />
                <div v-if="QRImgStatus1 != 1" class="mask-overlay">
                  <span class="signed-text">{{ QRImgStatus1 == 2 ? '已签约' : '已失效' }}</span>
                </div>
              </div>
            </div>
            <div class="f-c-d">
              <div>邀请该{{ roleTag === 'Operations' ? '俱乐部' : '门店' }}负责人，使用手机扫码签署</div>
              <div class="m-t m-b">《{{ roleTag === 'Operations' ? '渠道合作伙伴' : '课程推广大使' }}协议》</div>
              <div class="box-mask">
                <img class="img-QR" :src="QRImg0" alt="" />
                <div v-if="QRImgStatus0 != 1" class="mask-overlay">
                  <span class="signed-text">{{ QRImgStatus0 == 2 ? '已签约' : '已失效' }}</span>
                </div>
              </div>
            </div>
          </div>
          <el-button type="primary" @click="flishQR()">签署完成</el-button>
          <span class="m-t">签署完成后，请点击该按钮</span>
        </div>
      </el-dialog>
      <el-dialog :visible.sync="contractDialogVisible" width="25%" :close-on-click-modal="false">
        <div>
          <div v-if="contractTotal == 0 && show">您的剩余合同数量不足，如需新增，请购买合同后重试</div>
          <div v-else>该合同已失效，如果查看合同二维码，需要再次消耗1个合同数，请确定是否查看</div>
        </div>
        <div slot="footer">
          <el-button @click="closeContract()">取 消</el-button>
          <el-button type="primary" v-if="contractTotal == 0 && show" @click="ensureContract(2)">去购买</el-button>

          <el-button type="primary" v-else @click="ensureContract(1)">确 定</el-button>
        </div>
      </el-dialog>
      <!-- <el-dialog :visible.sync="toastDialogVisible" width="25%" :close-on-click-modal="false">
                <div>
                    当前合作伙伴已被签约
                </div>
                <div slot="footer">

                    <el-button type="primary" v-if="contractStatus == 1" @click=IKonw>知道了</el-button>

                </div>
            </el-dialog> -->
      <purchaseContract ref="spurchaseDialogVisible"></purchaseContract>
    </div>
  </div>
</template>
<script>
import { mapGetters } from 'vuex';
import store from '@/store';
import purchaseContract from '@/components/purchaseContract/index.vue';
import {
  addCompanion,
  companionList,
  contractsNumber,
  qryContractStatus,
  addPartnerPromotion,
  partnerPromotionList,
  fetchContractQrLink,
  contractFailure,
  buttonStatus,
  businessLicense
} from '@/api/partnerPromotion';
import Page from '@/components/Pages/pages.vue';
import checkPermission from '@/utils/permission';
export default {
  components: {
    purchaseContract,
    Page
  },
  data() {
    return {
      toastDialogVisible: false,
      loading: false,
      channelDialogVisible: false,
      flowDialogVisible: false,
      QRdialogVisible: false,
      contractDialogVisible: false,
      contractTotal: '',
      searchForm: {
        partnerMobile: '',
        contractStatus: null,
        clubStatus: null
      },
      inputForm: {
        partnerMobile: '',
        enterprise_name: '',
        name: ''
      },
      page_data: {
        pageSize: 10,
        page: 1,
        total: 0
      },
      rules: {
        partnerMobile: [
          { required: true, message: '请输入手机号', trigger: 'blur' },
          { pattern: /^1[3456789]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
        ],
        enterprise_name: [{ required: true, message: '仅可选择已注册的企业名称，请如实选择', trigger: 'blur' }],
        name: [{ required: true, message: '请输入真实姓名', trigger: 'blur' }]
      },
      options1: [
        {
          value: '2',
          label: '已签署'
        },
        {
          value: '1',
          label: '未签署'
        }
      ],
      options2: [
        {
          value: '1',
          label: '已创建'
        },
        {
          value: '0',
          label: '未创建'
        }
      ],
      list: [],
      show: false,
      brand: '所属品牌',
      phoneName: '伙伴手机号',
      stateName: '俱乐部状态',
      title1: '新增渠道伙伴',
      title2: '填写推荐人手机号',
      title3: '创建俱乐部',
      description1: '渠道伙伴签署合作伙伴协议',
      description2: '品牌创建俱乐部时填写推荐人的俱乐部编号',
      QRImg0: '',
      QRImg1: '',
      QRImgStatus0: '',
      QRImgStatus1: '',
      info: {},
      watchQR: false,
      pollInterval: null
    };
  },
  mounted() {


    // 俱乐部(Operations)还是品牌(zxBrand)
    this.isOperations = checkPermission(['Operations']);
    this.isSchool = checkPermission(['School']);
    // 品牌
    if (!this.isSchool && !this.isOperations) {
      // 只有俱乐部和品牌可以访问
      // this.$alert('权限不足，无法访问');
      this.$message.warning('权限不足，无法访问');
      this.loading = true
      return;
    } else {
      this.getDiffName();
      this.index();
      clearTimeout(this.pollInterval);
      this.pollInterval = null;
    }



  },
  methods: {
    setBlur() {
      //  在点击由 clearable 属性生成的清空按钮时，主动触发失去焦点，解决‘fetch-suggestions’输入建议不提示的bug
      document.activeElement.blur();
    },
    // 清空输入框页面重置
    handle(val) {
      if (val === '') {
        // this.getData(); // 页面重置的代码
      }
    },
    // 过滤项目和class
    async querySearch(queryString, cb) {
      if (queryString && queryString.length > 0) {
        let data1 = {
          keyword: queryString,
          pageNo: 1,
          pageSize: 10
        }

        try {
          const data = await businessLicense(data1); // search定义在data里
          // 赋值给建议列表，渲染到页面

          var list = data.data.data.data;
          // 如果list.length等于0，则代表没有匹配到结果。手动给list添加一条提示信息
          if (!queryString) {
            list.push({
              id: '-1',
              value: '无匹配结果'
            });
            // 调用 callback 返回建议列表的数据

            cb(list);
          } else {
            list = list.map((item) => {
              return {
                value: `${item.companyName}`,
                id: `${item.creditNo}`
              };
            });
            list = list.filter((item) => {
              return item.value.indexOf(queryString) > -1;
            });
            // 调用 callback 返回建议列表的数据
            cb(list);
          }
        } catch (error) {
          console.log(error);
        }
      }
    },
    handleSelect(item) {
      console.log(item, '....................');
    },
    handleAddClose() {
      this.channelDialogVisible = false;
      this.$refs.inputForm.resetFields();
    },
    handleClose() {
      this.QRdialogVisible = false;
      this.index();
      clearTimeout(this.pollInterval);
      this.pollInterval = null;
    },

    getDiffName() {
      this.roleTag = localStorage.getItem('roleTag');
      if (this.roleTag === 'Operations') {
        this.phoneName = '渠道伙伴手机号';
        this.stateName = '俱乐部状态';
        this.title1 = '新增渠道伙伴';
        this.title2 = '填写推荐人手机号';
        this.title3 = '创建俱乐部';
        this.description1 = '渠道伙伴签署合作伙伴协议';
        this.description2 = '品牌创建俱乐部时填写推荐人的俱乐部编号';
        this.brand = '所属品牌';
      }
      if (this.roleTag === 'School') {
        this.phoneName = '推广大使手机号';
        this.stateName = '门店状态';
        this.title1 = '新增推广大使';
        this.title2 = '推广大使手机号';
        this.title3 = '创建门店';
        this.description1 = '推广大使签署推广协议';
        this.description2 = '俱乐部创建门店';
        this.brand = '所属俱乐部';
      }
    },

    index() {
      this.loading = true;
      let param = { pageSize: this.page_data.pageSize, pageNum: this.page_data.page };
      Object.assign(param, this.searchForm);
      if (this.roleTag === 'Operations') {
        companionList(param).then((res) => {
          this.list = res.data.data;
          this.page_data.total = res.data.data ? res.data.totalItems * 1 : 0;
          this.loading = false;
          store.dispatch('getContract').then(() => {
            // console.log('Action 执行完成');
          });
          store.dispatch('getSystem').then(() => {
            // console.log('Action 执行完成');
          });
        });
      } else {
        partnerPromotionList(param).then((res) => {
          this.list = res.data.data;
          this.page_data.total = res.data.data ? res.data.totalItems * 1 : 0;
          this.loading = false;
          store.dispatch('getContract').then(() => {
            // console.log('Action 执行完成');
          });
          store.dispatch('getSystem').then(() => {
            // console.log('Action 执行完成');
          });
        });
      }
    },

    // 提交搜索
    async onSubmit() {
      this.index();
      // this.$refs.searchForm.resetFields();
    },

    // 重置表单
    reset() {
      this.$refs.searchForm.resetFields();
      this.index();
    },
    openAddChannel() {
      let that = this;
      // that.channelDialogVisible = true
      contractsNumber().then((res) => {
        if (res.data >= 1) {
          that.channelDialogVisible = true;
        } else {
          that.contractDialogVisible = true;
          that.show = true;
        }
      });
    },
    async ensure() {
      let that = this;
      this.$refs.inputForm.validate((valid) => {
        if (valid) {
          let data = {
            partnerMobile: this.inputForm.partnerMobile.toString(),
            name: this.inputForm.name,
            enterpriseName: this.inputForm.enterprise_name
          };
          //二次校验商户名称
          let data1 = {
            keyword: this.inputForm.name,
            pageNo: 1,
            pageSize: 10
          }

          businessLicense(data1).then((res) => {
            if (res.data.data.data.length != 1) {
              if (this.roleTag === 'Operations') { }
              this.$message.error('请输入正确的企业名称');
            } else {
              if (this.roleTag === 'Operations') {
                addCompanion(data).then((res) => {
                  if (res.success) {
                    this.index();
                    that.closePopup();
                  } else {
                    that.closePopup();
                    that.$message.error(res.message);
                  }
                });
              } else {
                addPartnerPromotion(data).then((res) => {
                  if (res.success) {
                    that.closePopup();
                    this.index();
                  } else {
                    that.$message.error(res.message);
                    that.closePopup();
                  }
                });
              }
            }
          });






        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    closePopup() {
      let that = this;
      that.channelDialogVisible = false;

      that.$refs.inputForm.resetFields();
    },
    openFlowChart() {
      let that = this;
      that.flowDialogVisible = true;
    },

    viewQR(row) {
      let that = this;
      that.info = row;
      that.watchQR = true;
      let data = {
        partnerMobile: row.partnerMobile
      };
      let idData = {
        id: row.id
      };

      let linkData = {
        flowId: row.signFlowId,
        templateType: row.templateType,
        signSource: row.signSource,
        isQrLink: 1
      };
      let linkData2 = {
        flowId: row.signFlowId,
        templateType: row.templateType,
        signSource: row.signSource,
        isQrLink: 0
      };
      //   查看合同状态-》判断是否失效 -》展示二维码
      qryContractStatus(data).then((res) => {
        if (!res.data) {
          //判断是否失效  + 校验门店是否相同
          buttonStatus(idData).then((res) => {
            if (res.data) {
              contractFailure(idData).then((res) => {
                if (!res.data) {
                  //调用获取二维码接口
                  that.fetchData1(linkData);

                  that.pollInterval = setInterval(() => {
                    that.fetchData(linkData2);
                  }, 15000);
                } else {
                  that.show = false;
                  that.contractDialogVisible = true;
                  that.watchQR = false;
                }
              });
            } else {
              // 失效弹窗
              this.contractDialogVisible = true;
              this.show = false;
              that.watchQR = false;
            }
          });
        } else {
          that.$message.error('已被他人签署');
          that.index();
          that.watchQR = false;
        }
      });
    },
    //合同状态判断
    JudgeContract(row) {
      if (row.contractStatus == 2 || row.contractStatus == 3) {
        return false;
      } else {
        if (row.parentCode == row.affiliationCode) {
          if (row.signatureStatus == 0) {
            return true;
          } else {
            false;
          }
        } else {
          return false;
        }
      }
    },
    fetchData1(data) {
      let that = this;
      fetchContractQrLink(data).then((res) => {
        res.data.forEach((item) => {
          if (item.isFirstParty === 0) {
            that.QRImg1 = item.qrUrl;
            that.QRImgStatus1 = item.signStatus;
          }
          if (item.isFirstParty === 1) {
            that.QRImg0 = item.qrUrl;
            that.QRImgStatus0 = item.signStatus;
          }
        });
        that.QRdialogVisible = true;
        that.watchQR = false;
      });
    },
    fetchData(data) {
      let that = this;
      fetchContractQrLink(data).then((res) => {
        res.data.forEach((item) => {
          //乙方
          if (item.isFirstParty === 0) {
            that.QRImgStatus1 = item.signStatus;
            console.log(item.signStatus);
          }
          //甲方
          if (item.isFirstParty === 1) {
            that.QRImgStatus0 = item.signStatus;
            console.log(item.signStatus);
          }
        });
      });
    },
    flishQR() {
      let that = this;
      if (that.QRImgStatus1 == 2 && that.QRImgStatus0 == 2) {
        that.QRdialogVisible = false;
        clearTimeout(this.pollInterval);
        this.pollInterval = null;
        that.index();
      } else {
        that.$message.info('请尽快签署合同');
      }
    },
    closeContract() {
      this.contractDialogVisible = false;
      this.show = false;
    },
    ensureContract(id) {
      if (id == 1) {
        this.show = true;
        if (this.roleTag === 'Operations') {
          addCompanion(this.info).then((res) => {
            if (res.data == 0) {
              this.contractDialogVisible = true;
              this.show = false;
              this.index();
            } else {
              this.index();
              this.openFlowChart();
            }
          });
        } else {
          addPartnerPromotion(this.info).then((res) => {
            if (res.data == 0) {
              this.index();
              this.contractDialogVisible = true;
              this.show = false;
            } else {
              this.index();
              this.openFlowChart();
            }
          });
        }
      }
      if (id == 2) {
        this.$refs.spurchaseDialogVisible.open();
      }
      this.closeContract();
    }
  }
};
</script>
<style lang="scss" scoped>
.new-channel {
  display: flex;
  margin-bottom: 20px;
}

.img-question {
  width: 30px;
  height: 30px;
}

.f-c {
  display: flex;
  justify-content: center;
  align-items: center;
}

.f-c-d {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}

.m-t {
  margin-top: 20px;
}

.m-b {
  margin-bottom: 20px;
}

.title {
  font-size: 20px;
  margin-bottom: 40px;
}

.title1 {
  font-size: 16px;
  font-weight: 400;
  color: #000;
  margin-bottom: 40px;
}

.img-box {
  display: flex;
  justify-content: space-between;
  align-content: center;
  width: 560px;
  margin-bottom: 40px;
}

.img-QR {
  width: 200px;
  height: 200px;
}
.box-mask {
  position: relative; /* 使蒙层可以相对于此定位 */
  display: inline-block; /* 根据内容调整大小 */
  width: 200px;
  height: 200px;
}

.mask-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8); /* 半透明黑色蒙层 */
  display: flex;
  justify-content: center;
  align-items: center;
}

.signed-text {
  color: white;
  font-size: 24px;
  font-weight: bold;
  /* 可以根据需要添加其他文字样式 */
}

.img-QR {
  display: block; /* 防止图片下方有间隙 */
  width: 100%; /* 根据需要调整 */
  height: auto;
}
::v-deep .is-finish {
  .is-text {
    color: #fff;
    background-color: #1890ff;
  }
}
:deep(.el-step__description) {
  color: #a7a2a2; /* 设置描述文字颜色 */
}
:deep(.el-step__description.is-wait) {
  color: #c0c4cc;
}
:deep(.el-step__line) {
  background-color: #1890ff; /* 设置步骤条颜色 */
}
</style>
