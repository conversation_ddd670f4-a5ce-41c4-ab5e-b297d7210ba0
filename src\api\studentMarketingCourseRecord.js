/**
 * 学员销课记录相关接口
 */
import request from '@/utils/request'

export default {
  // 分页查询
  studentMarketing(pageNum, pageSize, data) {
    return request({
      url: '/znyy/student/list/studentSalesRecord/' + pageNum + '/' + pageSize,
      method: 'GET',
      params: data
    })
  },
     // 导出
     studentMarketExport(listQuery) {
      return request({
        url: '/znyy/student/list/studentSalesExport',
        method: 'GET',
        responseType: 'blob',
        params:listQuery
      })
    },
}
