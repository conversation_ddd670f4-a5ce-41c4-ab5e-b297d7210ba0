  <template>
  <div class="app-container">
    <el-form :inline="true" class="container-card" label-width="130px" label-position="left">
      <el-row>
        <el-col :span="8" :xs="24">
          <el-form-item label="可提现金额：">
            {{ courseProfit.availableCashAmount / 100 }}元
          </el-form-item>
        </el-col>
        <el-col :span="10" :xs="24">
          <el-form-item label="提现金额（元）：">
            <el-input id="merchantCode" v-model="withdrawalAmount" name="id" placeholder="请输入提现金额" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="4" :xs="24" style="text-align: right">
          <el-form-item>
            <el-button type="primary" size="small" @click="withdrawIncome()">提现</el-button>
          </el-form-item>
        </el-col>
        <el-col :span="4" :xs="24" style="text-align: right">
          <el-form-item>
            <el-button type="primary" size="small" @click="realName()">已实名</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <el-table class="period-table" v-loading="tableLoading" :data="tableData" style="width: 100%; margin-bottom: 20px"
      border default-expand-all :tree-props="{ list: 'children', hasChildren: 'true' }">
      <el-table-column prop="id" label="ID" :show-overflow-tooltip="true"></el-table-column>

      <el-table-column prop="money" label="金额" :show-overflow-tooltip="true">
        <template slot-scope="scope"> {{ scope.row.money / 100 }}元 </template>
      </el-table-column>
      <el-table-column label="手续费">1元</el-table-column>
      <el-table-column prop="createTime" label="提现时间"></el-table-column>
      <el-table-column label="状态">
        <template slot-scope="scope">
          {{
            scope.row.status == 3
              ? "提现中"
              : scope.row.status == 4
              ? "已提现"
              : scope.row.status == 5
              ? "提现失败"
              : "未知状态"
          }}
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <el-col :span="24" style="overflow-x: auto" :xs="24">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>
  </div>
</template>

<script>
import coursePackageProfitRankFlowsApi from "@/api/coursePackage";

import { pageParamNames } from "@/utils/constants";
import store from "@/store";

export default {
  name: "merchantFlowList",
  data() {
    return {
      token: store.getters.token,
      setpayUrl: store.getters.setpayUrl,
      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null,
      },
      tableData: [],
      courseProfit: {
        availableCashAmount: "",
        totalMoney: "",
      },
      withdrawableCash: 100, //可提现金额
      withdrawalAmount: 0, //输入提现金额
    };
  },
  watch: {
    withdrawalAmount(value) {
      if (value > this.courseProfit.availableCashAmount / 100) {
        alert("已超出可提现金额");
        this.withdrawalAmount = this.courseProfit.availableCashAmount;
      }
    },
  },
  created() {
    let req = "token=" + this.token;
    //需要编码两遍，避免出现+号等
    let encode = Base64.encode(Base64.encode(req));
    this.disabledF = false;
    window.open(this.setpayUrl + "pay/account?" + encode, "_blank");
    this.$store.dispatch("delVisitedViews", this.$route);
    this.$router.go(-1);

    // this.fetchData01();
    // this.totalMoney();
  },
  methods: {
    fetchData01() {
      this.tablePage = {
        currentPage: 1,
        size: this.tablePage.size,
        totalPage: null,
        totalItems: null,
      };
      this.fetchData();

      this.totalMoney();
    },

    //初始获取数据
    totalMoney() {
      const that = this;
      coursePackageProfitRankFlowsApi
        .coursePackageProfitAccountFlow()
        .then((res) => {
          console.log(res);
          that.courseProfit = res.data;
          that.courseProfit.availableCashAmount == ""
            ? (that.courseProfit.availableCashAmount = 0)
            : "";
        });
    },

    // 查询提现列表
    fetchData() {
      const that = this;
      that.tableLoading = true;
      coursePackageProfitRankFlowsApi
        .coursePackageFlow(that.tablePage.currentPage, that.tablePage.size)
        .then((res) => {
          that.tableData = res.data.data;
          pageParamNames.forEach((name) =>
            that.$set(that.tablePage, name, parseInt(res.data[name]))
          );
          that.tableLoading = false;
        });
    },

    //点击提现
    withdrawIncome() {
      const that = this;
      if (that.withdrawalAmount == 0) {
        alert("请输入提现金额");
        return;
      }
      coursePackageProfitRankFlowsApi
        .coursePackageWithdraw(that.withdrawalAmount)
        .then((res) => {
          console.log(res);
          if (res.success) {
            alert("提现成功");
          }
        });
    },
    realName() {
      coursePackageProfitRankFlowsApi.realName().then((res) => {
        console.log(res);
        if (res.success) {
          alert("分润成功");
        }
      });
    },

    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val;
      this.fetchData();
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val;
      this.fetchData();
    },
  },
};
</script>

<style>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.period-table td,
.period-table th {
  text-align: center;
}

.mt20 {
  margin-top: 20px;
}
</style>
