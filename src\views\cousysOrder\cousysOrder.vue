<template>
  <div class="app-container">
    <el-form :inline="true" class="container-card" label-width="96px" label-position="left">


      <el-form-item>
        <el-form-item label="下单时间：" clearable>
          <el-date-picker style="width: 100%;" value-format="yyyy-MM-dd" clearable v-model="orderTime" type="daterange"
                          align="right" unlink-panels range-separator="至"
                          start-placeholder="开始时间" end-placeholder="结束时间"
          >
          </el-date-picker>
        </el-form-item>
      </el-form-item>

      <el-form-item label="订单金额：" clearable>
        <el-input v-model="dataQuery.lowMoney" oninput="value=value.replace(/[^\d]/g,'')" placeholder="最低金额" clearable/>
      </el-form-item>
      <el-form-item  clearable>
        <el-input v-model="dataQuery.highMoney" oninput="value=value.replace(/[^\d]/g,'')" placeholder="最高金额" clearable/>
      </el-form-item>


      <el-form-item label="校区:" clearable>
        <el-select v-model="dataQuery.merchantCode" filterable value-key="value" placeholder="请选择" clearable>
          <el-option v-for="(item,index) in merchantList" :key="index"
                     :label="item.label" :value="item.value"/>
        </el-select>
      </el-form-item>

      <el-form-item label="订单状态:" clearable>
        <el-select v-model="dataQuery.orderStatus" filterable value-key="value" placeholder="请选择" clearable>
          <el-option v-for="(item,index) in [{ value :0,label:'待支付'},{value: 1, label: '已支付'},{value: 2, label: '已取消'}]" :key="index"
                     :label="item.label" :value="item.value"/>
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="getList()">搜索</el-button>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-re" @click="resetQuery()">重置</el-button>
      </el-form-item>
    </el-form>
    总学时 {{detailList.totalCourse}}
    总金额 {{detailList.totalPrice}}
    总学员 {{detailList.studentCount}}
    <el-table class="common-table" v-loading="tableLoading" :data="tableData" style="width: 100%;margin-bottom: 20px;"
              row-key="id" border default-expand-all :tree-props="{list: 'children', hasChildren: 'true'}">
      <el-table-column prop="orderId" label="订单ID" sortable></el-table-column>
      <el-table-column prop="realName" label="家长姓名" sortable></el-table-column>
      <el-table-column prop="phoneNumber" label="家长电话" sortable></el-table-column>
      <el-table-column prop="totalPrice" label="订单金额" sortable></el-table-column>
      <el-table-column prop="merchantName" label="校区" sortable></el-table-column>
      <el-table-column prop="totalCourse" label="学时数" sortable></el-table-column>
      <el-table-column prop="orderTime" label="下单时间" sortable></el-table-column>
      <el-table-column prop="orderType" label="订单状态" sortable></el-table-column>
    </el-table>

    <!-- 分页 -->
    <el-col :span="20" style="overflow-x: auto;" :xs="24">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
                     layout="total, sizes, prev, pager, next, jumper"
                     :total="tablePage.totalItems" @size-change="handleSizeChange" @current-change="handleCurrentChange"
      />
    </el-col>
  </div>
</template>

<script>

import courseOrderApi from '@/api/courseOrder'
import { pageParamNames } from '@/utils/constants'
import cousysApi from '@/api/cousys'

export default {
  data() {
    return {
      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      tableData: [],
      detailList: {},
      dataQuery: {
        startTime: '',
        endTime: '',
        lowMoney: '',
        highMoney: '',
        merchantCode: '',
        orderStatus:'',
      },
      orderTime: []
    }
  },
  created() {
    this.getList()
    this.getDetailList();
  },
  methods: {
    //获取列表
    getList() {
      const that = this
      if (that.orderTime != '' && that.orderTime != null && that.orderTime != undefined) {
        if (that.orderTime.length > 0) {
          that.dataQuery.startTime = that.orderTime[0]
          that.dataQuery.endTime = that.orderTime[1]
        } else {
          that.dataQuery.startTime = ''
          that.dataQuery.endTime = ''
        }
      } else {
        that.dataQuery.startTime = ''
        that.dataQuery.endTime = ''
      }
      that.tableLoading = true
      courseOrderApi.getTutorClockList(that.tablePage.currentPage, that.tablePage.size, that.dataQuery).then(res => {
        that.tableData = res.data.data
        console.log(res.data.data)
        that.tableLoading = false
        // 设置后台返回的分页参数
        pageParamNames.forEach(name => this.$set(this.tablePage, name, parseInt(res.data[name])))
      })
    },

    //获取总学时 总学员 总金额
    getDetailList() {
      const that = this
      that.tableLoading = true
      courseOrderApi.getDetailList().then(res => {
        that.detailList = res.data
        that.tableLoading = false
      })
    },

    //重置按钮
    resetQuery() {
      this.orderTime = []
      this.dataQuery = {
        startTime: '',
        endTime: '',
        lowMoney: '',
        highMoney: '',
        merchantCode: '',
        orderStatus:'',
      },
        this.getList()
    },
    reset() {
      this.tutorInfoVo = {}
    },
    cancel() {
      this.reset()
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val
      this.getList()
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val
      this.getList()
    }
  }
}
</script>

<style>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

@media screen and (max-width: 767px) {
  .recharge-dialog .el-dialog {
    width: 90% !important;
  }

  .el-message-box {
    width: 80% !important;
  }
}
</style>
