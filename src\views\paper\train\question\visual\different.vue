<!--数字找不同题-->
<template>
  <div class="app-container">
    <el-form :model="form" ref="form" label-width="100px" v-loading="formLoading" :rules="rules">
      <el-form-item label="所属类型:" prop="type">
        <el-select v-model="form.type" placeholder="全部" disabled>
          <el-option v-for="(item, index) in trainType" :key="index" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="题型:" prop="questionType">
        <el-select v-model="form.questionType" placeholder="全部" disabled>
          <el-option v-for="(item, index) in trainQuestionType" :key="index" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="年级：" prop="grade" v-if="form.courseType == 1">
        <el-select v-model="form.grade" placeholder="" clearable>
          <el-option v-for="item in gradeList" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="选择难度：" prop="difficulty">
        <el-select v-model="form.difficulty" placeholder="全部" clearable @change="handleChange">
          <el-option v-for="(item, index) in difficultyList" :key="index" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="选择维度：" prop="categoryId">
        <el-select v-model="form.categoryId" placeholder="全部" clearable>
          <el-option v-for="(item, index) in categoryList" :key="index" :label="item.name" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item v-if="form.courseType!=1" label="选择类别：" prop="categoryType">
        <el-select v-model="form.categoryType" :disabled="questionlnCourseFalse&&form.categoryType==1" placeholder="全部" clearable>
          <el-option v-for="(item, index) in typeList" :key="index" :label="item.label" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="题干：" prop="title">
        <el-input placeholder="请输入" v-model="form.title" />
      </el-form-item>
      <!-- <el-form-item label="自动生成" required>
        <el-radio-group v-model="form.isRandom">
          <el-radio :label="true">是</el-radio>
          <el-radio :label="false">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="题目：" required>
        <el-tooltip content="字符串长度" placement="top">
          <el-link type="primary" :underline="false"
                   style="font-size:12px;vertical-align: baseline;"
          >
            <span v-if="difficultyInfo!=null"
            ><span v-show="form.isRandom">提交后随机生成</span>长度为{{
                difficultyInfo.questionMin
              }}-{{ difficultyInfo.questionMax }}的数字字符串
            ，{{ difficultyInfo.answerMin }}-{{ difficultyInfo.answerMax }}不同
            </span>
            <span v-else>
              请先选择难度
            </span>
          </el-link>
        </el-tooltip>
        <el-form-item v-for="cus in form.customInfo">
          <el-input style="width: 30%;" placeholder="题目" v-model="cus.label"
                    oninput="value=value.replace(/[^\d]/g,'')"
                    :minlength="difficultyInfo!=null?difficultyInfo.questionMin:0"
                    :maxlength="difficultyInfo!=null?difficultyInfo.questionMax:0"
                    :readonly="form.isRandom"
                    @blur="customInfoChange()"
          />
          <el-input style="width: 30%;padding-left: 5px" placeholder="题目" v-model="cus.value"
                    oninput="value=value.replace(/[^\d]/g,'')"
                    :minlength="difficultyInfo!=null?difficultyInfo.questionMin:0"
                    :maxlength="difficultyInfo!=null?difficultyInfo.questionMax:0"
                    :readonly="form.isRandom"
                    @blur="customInfoChange()"
          />
        </el-form-item>
      </el-form-item> -->

      <el-form-item label="数字串" prop="customInfo">
        <div class="numeric-string1">
          <el-input-number v-for="(item, index) in numbericStringList1" :key="index" v-model="numbericStringList1[index]" :controls="false" :precision="0" :min="0" :max="9" @blur="handleStringChange"></el-input-number>
          <div class="action-btns">
            <i class="el-icon-circle-plus" @click="addNumber"></i>
            <i class="el-icon-remove" @click="removeNumber"></i>
          </div>
        </div>
        <div class="numeric-string2">
          <el-input-number v-for="(item, index) in numbericStringList2" :key="index" v-model="numbericStringList2[index]" :controls="false" :precision="0" :min="0" :max="9" @blur="handleStringChange"></el-input-number>
          <div class="action-btns">
            <i class="el-icon-circle-plus" @click="addNumber"></i>
            <i class="el-icon-remove" @click="removeNumber"></i>
          </div>
        </div>
      </el-form-item>
      <el-form-item label="答案：" required>
        <el-tooltip content="答案" placement="top">
          <el-link type="primary" :underline="false" style="font-size: 12px; vertical-align: baseline">
            <span>输入数字串提交后自动生成，下标从1开始</span>
          </el-link>
        </el-tooltip>
        <el-form-item :key="index" v-for="(item, index) in form.answer">
          <div class="question-item-label">
            <el-input v-model="item.label" placeholder="输入数字串自动生成" readonly style="width: 180px; marginright: 5px" />
          </div>
        </el-form-item>
      </el-form-item>
      <el-form-item label="题数：" prop="score">
        <el-input-number v-model="form.score" :precision="0" :step="1" :min="0" :max="100"></el-input-number>
      </el-form-item>
      <!-- <el-form-item label="徽章：" prop="badge">
        <el-input-number v-model="form.badge" :precision="0" :step="1" :min="0" :max="100"></el-input-number>
      </el-form-item>
      <el-form-item label="解析："  required>
        <el-input v-model="form.analysis">
          <i @click="inputClick(form,'analysis')" slot="suffix" class="el-icon-edit-outline"
             style="line-height: 36px;font-size: 20px;cursor: pointer;"
          ></i>
        </el-input>
      </el-form-item> -->
      <el-form-item>
        <el-button type="primary" @click="submitForm">提交</el-button>
        <el-button v-show="form.id === null" @click="resetForm">重置</el-button>
      </el-form-item>
    </el-form>

    <el-dialog :visible.sync="richEditor.dialogVisible" append-to-body :close-on-click-modal="false" style="width: 100%; height: 100%" :show-close="false" center>
      <Ueditor @ready="editorReady" />
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="editorConfirm">确 定</el-button>
        <el-button @click="richEditor.dialogVisible = false">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import questionApi from '@/api/paper/train/question';
import difficultyApi from '@/api/paper/train/difficulty';
import categoryApi from '@/api/paper/train/category';
import { mapGetters, mapState } from 'vuex';
import MyUpload from '@/components/Upload/MyUpload';
import Ueditor from '@/components/Ueditor';

export default {
  components: {
    MyUpload,
    Ueditor
  },
  data() {
    var validateString = (rule, value, callback) => {
      setTimeout(() => {
        if (!value[0].label || !value[0].value) {
          return callback(new Error('请将数字串填写完整'));
        } else {
          const string1 = this.numbericStringList1.join('');
          const string2 = this.numbericStringList2.join('');

          if (string1.length != string2.length || string1.length != this.numbericStringList1.length || string2.length != this.numbericStringList2.length) {
            return callback(new Error('请将数字串填写完整并保持长度一致'));
          }
          callback();
        }
      }, 500);
    };
    return {
      richEditor: {
        dialogVisible: false,
        object: null,
        parameterName: '',
        instance: null
      },
      difficultyInfo: null,
      categoryList: [],
      fileList: [],
      typeList: [
        { label: '正式题', id: 1 },
        { label: '附加题', id: 2 }
      ],
      importFrom: {
        file: null
      },
      randomLength: undefined,
      form: {
        id: null,
        title: '',
        categoryId: '',
        categoryType: '',
        difficulty: '',
        difficultyId: '',
        type: 'VISUAL_',
        questionType: 'VISUAL_DIFFERENT',
        customInfo: [{ label: '', value: '' }],
        answer: [],
        isRandom: false,
        // analysis:'',
        score: undefined,
        // badge:3,
        courseType: 0,
        grade: ''
      },
      formLoading: false,
      rules: {
        type: [{ required: true, message: '请选择', trigger: 'blur' }],
        questionType: [{ required: true, message: '请选择', trigger: 'blur' }],
        title: [{ required: true, message: '请输入', trigger: 'blur' }],
        difficulty: [{ required: true, message: '请选择', trigger: 'blur' }],
        categoryId: [{ required: true, message: '请选择', trigger: 'blur' }],
        categoryType: [{ required: true, message: '请选择', trigger: 'blur' }],
        customInfo: [{ required: true, validator: validateString, trigger: 'blur' }],
        score: [{ required: true, message: '请输入题数', trigger: 'blur' }],
        badge: [{ required: true, message: '请输入徽章', trigger: 'blur' }]
      },
      questionlnCourseFalse: false,
      currentAnswerItem: null,
      numbericStringList1: [undefined],
      numbericStringList2: [undefined],
      gradeList: [
        { label: '1-3年级', value: 1 },
        { label: '4-6年级', value: 2 },
        { label: '初高中', value: 3 }
      ]
    };
  },
  created() {
    this.getCategoryList();
    let id = this.$route.query.id;
    this.form.courseType = this.$route.query.courseType ? 1 : 0;
    if (id && parseInt(id) !== 0) {
      this.formLoading = true;
      questionApi.detail(id).then((re) => {
        this.form = re.data;
        this.form.grade = this.form.grade == 0 ? '' : this.form.grade;
        const stringObj = re.data.customInfo[0];
        this.numbericStringList1 = stringObj.label.split('');
        this.numbericStringList2 = stringObj.value.split('');
        this.handleChange();
        this.formLoading = false;

        let answerLabel = this.form.answer[0].label;
        let answerLabelArr = answerLabel.split(',');
        for (let i = 0; i < answerLabelArr.length; i++) {
          answerLabelArr[i] = Number(answerLabelArr[i]) + 1;
        }
        this.form.answer[0].label = answerLabelArr.join(',');
      });
      questionApi.checkQuestionlnCourse(id).then((res) => {
        this.questionlnCourseFalse = res.data
      });
    }
  },
  methods: {
    editorReady(instance) {
      this.richEditor.instance = instance;
      let currentContent = this.richEditor.object[this.richEditor.parameterName];
      this.richEditor.instance.setContent(currentContent);
      // 光标定位到Ueditor
      this.richEditor.instance.focus(true);
    },
    inputClick(object, parameterName) {
      this.richEditor.object = object;
      this.richEditor.parameterName = parameterName;
      this.richEditor.dialogVisible = true;
    },
    editorConfirm() {
      let content = this.richEditor.instance.getContent();
      this.richEditor.object[this.richEditor.parameterName] = content;
      this.richEditor.dialogVisible = false;
    },
    customInfoChange() {
      let cus = this.form.customInfo[0];
      if (cus.label !== '' && cus.value !== '') {
        if (cus.label.length !== cus.value.length) {
          this.form.customInfo[0] = cus;
          this.$message.error('长度不同！');
          return false;
        }
        let arr = [];
        this.form.answer = [];
        for (var i = 0; i < cus.label.length; i++) {
          if (cus.label.charAt(i) !== cus.value.charAt(i)) {
            arr.push({ label: i, value: '' });
          }
        }
        if (arr.length > this.difficultyInfo.answerMax || arr.length < this.difficultyInfo.answerMin) {
          this.$message.error(`答案个数在${this.difficultyInfo.answerMin}-${this.difficultyInfo.answerMax}！`);
          return false;
        }
        this.form.answer = arr;
      }
    },
    // 数字串输入变化
    handleStringChange() {
      const string1 = this.numbericStringList1.filter((el) => typeof el != 'undefined').join('');
      const string2 = this.numbericStringList2.filter((el) => typeof el != 'undefined').join('');
      this.form.customInfo[0].label = string1;
      this.form.customInfo[0].value = string2;
    },
    itemAdd() {
      if (this.difficultyInfo === null) {
        this.$message.error('请选择难度！');
        return false;
      }
      if (this.form.answer.length >= this.difficultyInfo.answerMax) {
        this.$message.error(`答案个数在${this.difficultyInfo.answerMin}-${this.difficultyInfo.answerMax}！`);
        return false;
      }
      let item = { label: '', value: '' };
      this.form.answer.push(item);
    },
    itemRemove(item) {
      item.arr.splice(item.arr.length - 1, 1);
      this.answerNum--;
    },
    getCategoryList() {
      categoryApi.list().then((res) => {
        this.categoryList = res.data.data;
      });
    },
    handleChange() {
      if (!this.form.difficulty) {
        this.difficultyInfo = null;
        return;
      }
      let query = {};
      query.type = this.form.type;
      query.questionType = this.form.questionType;
      query.difficulty = this.form.difficulty;
      difficultyApi
        .isSetting(query)
        .then((res) => {
          this.difficultyInfo = res.data;
        })
        .catch((e) => {
          this.difficultyInfo = null;
          this.fileList = [];
        });
    },
    submitForm() {
      if (this.difficultyInfo === null) {
        this.$message.error('请选择难度！');
        return false;
      }
      let cus = this.form.customInfo[0];
      // if (cus.label === '' || cus.value === '') {
      //   this.$message.error('输入题目！')
      //   return false
      // }
      // if (cus.label.length !== cus.value.length) {
      //   this.$message.error('长度不同！')
      //   return false
      // }
      // if (this.form.answer.length > this.difficultyInfo.answerMax || this.form.answer.length <this.difficultyInfo.answerMin) {
      //   this.$message.error(`答案个数在${this.difficultyInfo.answerMin}-${this.difficultyInfo.answerMax}！`)
      //   return false
      // }
      if (cus.label == cus.value) {
        this.$message.error('请勿输入完全相同的数字串！');
        return false;
      }
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.formLoading = true;
          questionApi
            .saveOrUpdate(this.form)
            .then((re) => {
              if (re.success) {
                this.$message.success(re.message);
                this.formLoading = false;
                if (this.form.courseType == 1) {
                  this.$router.push('/train/trainAfterCourse');
                } else {
                  this.$router.push('/train/visual');
                }
              } else {
                this.$message.error(re.message);
                this.formLoading = false;
              }
            })
            .catch((e) => {
              this.formLoading = false;
            });
        } else {
          return false;
        }
      });
    },
    resetForm() {
      let lastId = this.form.id;
      let lastCourseType = this.form.courseType;
      this.$refs['form'].resetFields();
      this.form = {
        id: null,
        title: '',
        categoryId: '',
        difficulty: '',
        difficultyId: '',
        type: 'VISUAL_',
        questionType: 'VISUAL_DIFFERENT',
        customInfo: [{ label: '', value: '' }],
        answer: [],
        isRandom: false,
        grade: ''
        // analysis:''
      };
      this.numbericStringList1 = [undefined];
      this.numbericStringList2 = [undefined];
      this.form.id = lastId;
      this.form.courseType = lastCourseType;
    },
    // 数字串添加、删除
    addNumber() {
      const len = this.numbericStringList1.length;
      if (len < 9) {
        this.numbericStringList1.push(undefined);
        this.numbericStringList2.push(undefined);
      } else {
        this.$message.error('数字串最多展示9个数字！');
        return false;
      }
    },
    removeNumber() {
      const len = this.numbericStringList1.length;
      if (len > 1) {
        this.numbericStringList1.pop();
        this.numbericStringList2.pop();
      } else {
        this.$message.error('数字串至少展示一个数字！');
        return false;
      }
    }
  },
  computed: {
    ...mapGetters('enumItem', ['enumFormat']),
    ...mapState('enumItem', {
      trainType: (state) => state.train.trainType,
      difficultyList: (state) => state.train.difficultyList,
      trainQuestionType: (state) => state.train.trainQuestionType
    })
  }
};
</script>

<style lang="less" scoped>
.question-item-label {
  display: flex;
  margin-bottom: 12px;
}

.drawing-item {
  overflow: hidden;
  white-space: normal;
  word-break: break-all;
}
.numeric-string1,
.numeric-string2 {
  display: flex;
  ::v-deep .el-input-number,
  ::v-deep input.el-input__inner {
    width: 36px;
  }
  ::v-deep .el-input-number.is-without-controls .el-input__inner {
    padding-left: 5px;
    padding-right: 5px;
  }
}
.numeric-string1 {
  margin-bottom: 16px;
}
.action-btns {
  display: flex;
  flex-direction: column;
  // align-items: center;
  justify-content: space-between;
  margin-left: 5px;
}
</style>
