<template>
  <div class="app-container">
    <el-form ref="examinationRef" label-position="right" label-width="110px" :model="examinationData" style="width: 100%; margin: 0 auto" class="mb20">
      <div v-for="(item, index) in examinationData.items" :key="index">
        <el-card style="background-color: aliceblue; margin-bottom: 20px">
          <div style="margin-bottom: 20px; margin-right: 50px; display: flex; justify-content: center">{{ courseName }}</div>
          <el-row type="flex" justify="center">
            <el-col :xs="24" :lg="16">
              <el-row>
                <el-col :span="20">
                  <el-form-item
                    v-if="courseContentType === '阅读理解'"
                    label="题目类型"
                    :prop="'items.' + index + '.questionType'"
                    :rules="[
                      {
                        required: true,
                        message: '必填',
                        trigger: 'blur'
                      }
                    ]"
                  >
                    <el-select
                      :disabled="!addOrUpdate"
                      @change="changeFill($event, index)"
                      style="width: 100%"
                      v-model="item.questionType"
                      filterable
                      value-key="value"
                      placeholder="请选择"
                    >
                      <el-option v-for="(item, index) in optionsQuestionType" :key="index" :label="item.label" :value="item.value" />
                    </el-select>
                  </el-form-item>
                  <el-form-item
                    v-if="courseContentType === '完型填空'"
                    label="题目类型"
                    :prop="'items.' + index + '.questionType'"
                    :rules="[
                      {
                        required: true,
                        message: '必填',
                        trigger: 'blur'
                      }
                    ]"
                  >
                    <el-select @change="changeFill($event, index)" style="width: 100%" v-model="item.questionType" filterable value-key="value" placeholder="请选择">
                      <el-option v-for="(item, index) in optionsQuestionType1" :key="index" :label="item.label" :value="item.value" />
                    </el-select>
                  </el-form-item>
                  <el-form-item
                    label="题目编号"
                    :prop="'items.' + index + '.questionNumber'"
                    :rules="[
                      {
                        required: true,
                        message: '必填',
                        trigger: 'change'
                      }
                    ]"
                  >
                    <el-input type="number" @input="handleInput(item)" v-model="item.questionNumber"></el-input>
                  </el-form-item>
                  <el-form-item label="题目" :prop="'items.' + index + '.questionText'">
                    <el-input v-model="item.questionText" type="textarea" max="99" min="1"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="2" :offset="1">
                  <el-button v-if="addOrUpdate" type="danger" icon="el-icon-close" @click="updateDeleteForm(item)">删除</el-button>
                </el-col>
              </el-row>
              <!--  选项表格 -->
              <el-row>
                <el-form v-if="item.optionOrBlanks === 1" label-width="110px">
                  <el-form-item :prop="item.statusButton ? 'items.' + index + '.tableStatus' : 'items.' + index + '.tableCheckList'">
                    <template #label>
                      <span style="color: red">*</span>
                      选项答案
                    </template>
                    <el-row>
                      <el-button style="color: #4c8eff; border-color: #4c8eff; margin-right: 20px" size="medium" icon="el-icon-plus" @click="confirmOrigin(index)">
                        添加选项
                      </el-button>
                      <el-radio-group v-model="item.statusButton" v-if="addOrUpdate">
                        <el-button :disabled="!addOrUpdate" :style="item.statusButton ? 'color: #4c8eff; border-color: #4c8eff;' : ''" size="medium" @click="singleHandle(index)">
                          单选
                        </el-button>
                        <el-button
                          :disabled="!addOrUpdate"
                          style="margin-left: 0px"
                          size="medium"
                          @click="multipleHandle(index)"
                          :style="!item.statusButton ? 'color: #4c8eff; border-color: #4c8eff;' : ''"
                        >
                          多选
                        </el-button>
                      </el-radio-group>
                      <el-radio-group v-model="item.statusButton" v-if="!addOrUpdate">
                        <el-button :disabled="!addOrUpdate" :style="item.statusButton ? 'color: #4c8eff; border-color: #4c8eff;' : ''" size="medium" @click="singleHandle(index)">
                          单选
                        </el-button>
                        <el-button
                          :disabled="!addOrUpdate"
                          style="margin-left: 0px"
                          size="medium"
                          @click="multipleHandle(index)"
                          :style="!item.statusButton ? 'color: #4c8eff; border-color: #4c8eff;' : ''"
                        >
                          多选
                        </el-button>
                      </el-radio-group>
                    </el-row>
                    <el-radio-group v-if="item.statusButton" v-model="item.status" :value="item.status" @input="groupChanged(index, $event)">
                      <el-table
                        class="common-table"
                        style="margin-bottom: 30px; width: 580px"
                        :data="item.tableStatus.filter((status) => status.isDeleted !== 1)"
                        stripe
                        border
                        max-height="500"
                        :default-sort="{
                          prop: 'addTime',
                          order: 'descending'
                        }"
                      >
                        <el-table-column prop="choiceOption" label="勾选正确答案">
                          <template slot-scope="scope">
                            <el-radio :key="scope.row.id" :label="scope.$index" :value="scope.row.optionIsAnswer">{{ scope.row.choiceOption }}</el-radio>
                          </template>
                        </el-table-column>
                        <el-table-column prop="content" label="选项内容">
                          <template slot-scope="scope">
                            <el-input v-if="scope.row.tableEditing" v-model="scope.row.content" placeholder="请输入内容"></el-input>
                            <span v-else-if="scope.row.isDeleted !== 1">{{ scope.row.content }}</span>
                          </template>
                        </el-table-column>
                        <el-table-column prop="id" label="操作" fixed="right" width="200px">
                          <template slot-scope="scope" v-if="scope.row.isDeleted !== 1">
                            <el-button v-if="scope.row.tableEditing" type="text" size="medium" @click="confirmTable(index, scope.$index)">确定</el-button>
                            <el-button v-if="!scope.row.tableEditing" type="text" size="medium" @click="confirmTableEdit(index, scope.$index, scope.row)">编辑</el-button>
                            <el-button type="text" size="medium" style="color: red" @click="deleteOption(index, scope.$index)">删除</el-button>
                          </template>
                        </el-table-column>
                      </el-table>
                    </el-radio-group>
                    <el-checkbox-group v-if="!item.statusButton" v-model="item.checkList" @change="groupChangedCheck(index, $event)">
                      <el-table
                        class="common-table"
                        style="margin-bottom: 30px; width: 790px"
                        :data="item.tableCheckList.filter((status) => status.isDeleted !== 1)"
                        stripe
                        border
                        max-height="500"
                        :default-sort="{
                          prop: 'addTime',
                          order: 'descending'
                        }"
                      >
                        <el-table-column prop="choiceOption" label="勾选正确答案">
                          <template slot-scope="scope">
                            <el-checkbox :key="scope.row.id" :label="scope.$index" :value="scope.row.optionIsAnswer">{{ scope.row.choiceOption }}</el-checkbox>
                          </template>
                        </el-table-column>
                        <el-table-column prop="content" label="选项内容">
                          <template slot-scope="scope">
                            <el-input v-if="scope.row.tableEditing" v-model="scope.row.content" placeholder="请输入内容"></el-input>
                            <span v-else-if="scope.row.isDeleted !== 1">{{ scope.row.content }}</span>
                          </template>
                        </el-table-column>
                        <el-table-column prop="id" label="操作" fixed="right" width="200px">
                          <template slot-scope="scope" v-if="scope.row.isDeleted !== 1">
                            <el-button v-if="scope.row.tableEditing" type="text" size="medium" @click="confirmCheck(index, scope.$index)">确定</el-button>
                            <el-button v-if="!scope.row.tableEditing" type="text" size="medium" @click="confirmCheckEdit(index, scope.$index, scope.row)">编辑</el-button>
                            <el-button type="text" size="medium" style="color: red" @click="deleteCheck(index, scope.$index)">删除</el-button>
                          </template>
                        </el-table-column>
                      </el-table>
                    </el-checkbox-group>
                  </el-form-item>
                </el-form>
                <el-form width="100%" v-if="item.optionOrBlanks === 2 && courseContentType === '阅读理解'" label-width="110px">
                  <el-form-item :prop="'items.' + index + '.tableBlanks'">
                    <template #label>
                      <span style="color: red">*</span>
                      选项答案
                    </template>
                    <el-row>
                      <el-button style="color: #4c8eff; border-color: #4c8eff; margin-right: 20px" size="medium" icon="el-icon-plus" @click="confirmBlankOrigin(index)">
                        添加选项
                      </el-button>
                    </el-row>
                    <el-table
                      class="common-table"
                      style="margin-bottom: 30px; width: 580px"
                      :data="item.tableBlanks.filter((status) => status.isDeleted !== 1)"
                      stripe
                      border
                      max-height="500"
                      :default-sort="{ prop: 'addTime', order: 'descending' }"
                    >
                      <el-table-column prop="content" label="选项内容">
                        <template slot-scope="scope">
                          <el-input v-if="scope.row.tableEditing" v-model="scope.row.content" placeholder="请输入内容"></el-input>
                          <span v-else-if="scope.row.isDeleted !== 1">{{ scope.row.content }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column prop="id" label="操作" fixed="right" width="200px">
                        <template slot-scope="scope" v-if="scope.row.isDeleted !== 1">
                          <el-button v-if="scope.row.tableEditing" type="text" size="medium" @click="confirmBlank(index, scope.$index)">确定</el-button>
                          <el-button v-if="!scope.row.tableEditing" type="text" size="medium" @click="confirmBlankEdit(index, scope.$index, scope.row)">编辑</el-button>
                          <el-button type="text" size="medium" style="color: red" @click="deleteBlank(index, scope.$index)">删除</el-button>
                        </template>
                      </el-table-column>
                    </el-table>
                  </el-form-item>
                </el-form>
              </el-row>
              <!-- 解析 -->
              <el-row>
                <el-col :span="20">
                  <el-form-item
                    label="解析"
                    :prop="'items.' + index + '.analysis'"
                    :rules="[
                      {
                        required: true,
                        message: '必填',
                        trigger: 'blur'
                      }
                    ]"
                  >
                    <el-col :span="24">
                      <el-input type="textarea" class="reading-textarea" v-model="item.analysis"></el-input>
                    </el-col>
                  </el-form-item>
                </el-col>
                <el-col :span="3" :offset="1">
                  <el-button v-if="addOrUpdate && !item.hasPerformedAdd" type="primary" icon="el-icon-plus" @click="addForm">新增</el-button>
                </el-col>
              </el-row>
            </el-col>
          </el-row>
        </el-card>
      </div>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-row>
        <el-col :span="4" :offset="6">
          <el-button v-if="addOrUpdate" type="primary" @click="addActiveFun()">确定</el-button>
          <el-button v-if="!addOrUpdate" type="primary" @click="updateReadingFun()">确定</el-button>
        </el-col>
        <el-col :span="4" :offset="4">
          <el-button type="primary" @click="getList">关闭</el-button>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
  import { addQuestionAPI, editQuestionAPI, findQuestionAPI } from '@/api/superApi/question';
  export default {
    data() {
      return {
        // 分页
        updateReadingData: {},
        courseName: '',
        courseCode: '',
        courseContentType: '',
        addOrUpdate: true,
        optionsQuestionType: [
          {
            value: 2,
            label: '填空题'
          },
          {
            value: 1,
            label: '选择题'
          }
        ],
        optionsQuestionType1: [
          {
            value: 1,
            label: '选择题'
          }
        ],
        examinationRef: '',
        //增加内容
        examinationData: {
          items: [
            {
              articleId: '',
              courseId: '',
              questionNumber: '',
              questionType: '',
              choiceQuestionType: '',
              questionText: '',
              status: null,
              statusButton: true,
              checkList: [],
              tableStatus: [],
              tableCheckList: [],
              tableBlanks: [],
              optionOrBlanks: null,
              analysis: '',
              correctAnswer: '', // 选项答案
              hasPerformedAdd: false
            }
          ],
          editAnswer: {}
        },
        editId: '',
        questionEditId: '',
        selectedArray: []
      };
    },
    created() {
      this.articleId = window.localStorage.getItem('articleId');
      this.courseId = window.localStorage.getItem('courseId');
      this.courseName = window.localStorage.getItem('courseName');
      this.courseContentType = window.localStorage.getItem('courseContentType');
      this.editId = window.localStorage.getItem('id');
      this.addOrUpdate = this.$route.query.addOrUpdate;
      this.openEdit();
      if (!this.addOrUpdate) {
        this.setTitle('编辑试题');
      } else {
        this.setTitle('添加试题');
      }
    },
    methods: {
      // 动态设置标签页标题
      setTitle(title) {
        let i = 0;
        let visitedViews = this.$store.getters.visitedViews;
        visitedViews.forEach((route, index) => {
          if (this.$route.path == route.path) {
            i = index;
          }
        });
        this.$route.meta.title = title;
        visitedViews[i].title = title;
      },
      // 切换题目类型
      changeFill(e, index) {
        this.examinationData.items[index].optionOrBlanks = e;
        const item = this.examinationData.items[index];
        if (item.optionOrBlanks === 2) {
          item.tableBlanks = [];
        } else {
          item.statusButton ? (item.tableStatus = []) : (item.tableCheckList = []);
        }
      },
      handleInput(item) {
        let numberItem = item;
        // 去除前导零和非数字字符
        numberItem.questionNumber = numberItem.questionNumber.replace(/^(0+)|[^\d]+/g, '');

        // 限制最大值为 100
        if (parseInt(numberItem.questionNumber, 10) > 100) {
          this.$message.warning('试题题目编号不能超过100');
          numberItem.questionNumber = '100';
        }
      },
      // 点击添加表格选项
      confirmOrigin(index) {
        const item = this.examinationData.items[index];
        // 检查选项数量是否超过 26 个
        const currentLength = item.statusButton ? item.tableStatus.length : item.tableCheckList.length;
        if (currentLength >= 26) {
          this.$message.warning('选项数量已达最大限制');
          return;
        }
        const newNum = String.fromCharCode(65 + currentLength); // 从 A 开始
        const newOption = {
          index: item.statusButton ? item.tableStatus.length : item.tableCheckList.length,
          choiceOption: newNum,
          content: '',
          optionIsAnswer: 0, // 是否是答案
          id: '', // 编辑 id
          questionId: '', // 编辑 questionId
          isDeleted: '', // 编辑删除选项 isDeleted
          tableEditing: true // 新增时默认进入编辑状态
        };
        if (item.statusButton) {
          let isAnySelected = false;
          for (const item of item.tableStatus) {
            if (item.optionIsAnswer === 1) {
              isAnySelected = true;
              break;
            }
          }
          if (!isAnySelected) {
            newOption.optionIsAnswer = 0;
          }
          item.tableStatus.push(newOption);
        } else {
          item.tableCheckList.push(newOption);
        }
      },
      // 单选
      confirmTable(index, tableIndex) {
        const item = this.examinationData.items[index];
        if (item.tableStatus[tableIndex].content === '') {
          this.$message.error('选项内容不能为空');
          return;
        } else {
          item.tableStatus[tableIndex].tableEditing = false;
        }
      },
      confirmTableEdit(index, tableIndex, row) {
        this.$set(row, 'tableEditing', false);
        this.examinationData.items[index].tableStatus[tableIndex].content = row.content;
        this.$set(this.examinationData.items[index].tableStatus[tableIndex], 'tableEditing', true);
      },
      // 单选删除
      deleteOption(index, tableIndex) {
        const item = this.examinationData.items[index];
        if (!this.addOrUpdate && item.tableStatus[tableIndex].id !== '') {
          item.tableStatus[tableIndex].isDeleted = 1;
          this.selectedArray.push(item.tableStatus[tableIndex]);
          this.editAnswer = this.selectedArray;
        }
        console.log(this.editAnswer, '选中数组');
        item.tableStatus.splice(tableIndex, 1);
        // 重新排序选项
        item.tableStatus.forEach((item, idx) => {
          item.choiceOption = String.fromCharCode(65 + idx); // 从 A 开始
          item.tableIndex = idx; // 从0开始编号
        });
        // 更新选中状态
        if (item.status === tableIndex) {
          item.status = null;
        } else if (item.status > index) {
          item.status = item.status;
        }
      },
      // 多选
      confirmCheck(index, checkIndex) {
        const item = this.examinationData.items[index];
        if (item.tableCheckList[checkIndex].content === '') {
          this.$message.error('选项内容不能为空');
          return;
        } else {
          item.tableCheckList[checkIndex].tableEditing = false;
        }
      },
      confirmCheckEdit(index, checkIndex, row) {
        const item = this.examinationData.items[index];

        // 使用 this.$set 更新每个 itemCheckList 的 tableEditing 属性
        item.tableCheckList.forEach((itemCheckList, indexCheckList) => {
          this.$set(itemCheckList, 'tableEditing', false);
        });

        // 使用 this.$set 更新特定项的 tableEditing 属性
        this.$set(item.tableCheckList[checkIndex], 'tableEditing', true);
        this.$set(item.tableCheckList[checkIndex], 'content', row.content);
      },
      // 多选删除
      deleteCheck(index, checkIndex) {
        const item = this.examinationData.items[index];
        if (!this.addOrUpdate && item.tableCheckList[checkIndex].id !== '') {
          item.tableCheckList[checkIndex].isDeleted = 1;
          this.selectedArray.push(item.tableCheckList[checkIndex]);

          this.editAnswer = this.selectedArray;
        }

        console.log(this.editAnswer, '选中数组');
        item.tableCheckList.splice(checkIndex, 1);
        // 重新排序选项
        item.tableCheckList.forEach((item, idx) => {
          item.choiceOption = String.fromCharCode(65 + idx); // 从 A 开始
          item.checkIndex = idx; // 从0开始编号
        });
        // 更新选中状态
        item.checkList = item.checkList.filter((item) => item !== checkIndex);
        item.checkList = item.checkList.map((item) => (item > checkIndex ? item - 1 : item));
      },
      // 填空
      confirmBlankOrigin(index) {
        const item = this.examinationData.items[index];
        if (item.tableBlanks.length >= 1) {
          this.$message.error('只能添加一个填空~');
          return;
        }
        const newOption = {
          index: item.tableBlanks.length,
          id: '',
          questionId: '',
          isDeleted: '',
          content: '',
          optionIsAnswer: 1, // 是否是答案
          tableEditing: true // 新增时默认进入编辑状态
        };
        item.tableBlanks.push(newOption);
      },
      confirmBlank(index, blankIndex) {
        const item = this.examinationData.items[index];
        if (item.tableBlanks[blankIndex].content === '') {
          this.$message.error('选项内容不能为空');
          return;
        } else {
          item.tableBlanks[blankIndex].tableEditing = false;
          item.correctAnswer = item.tableBlanks[blankIndex].content;
        }
      },
      confirmBlankEdit(index, blankIndex, row) {
        const item = this.examinationData.items[index];
        // 使用 this.$set 更新每个 itemBlankEdit 的 tableEditing 属性
        item.tableBlanks.forEach((itemBlankEdit, indexBlankEdit) => {
          this.$set(itemBlankEdit, 'tableEditing', false);
        });
        // 使用 this.$set 更新特定项的 tableEditing 属性
        this.$set(item.tableBlanks[blankIndex], 'tableEditing', true);
        this.$set(item.tableBlanks[blankIndex], 'content', row.content);
      },
      // 填空表格删除
      deleteBlank(index, blankIndex) {
        const item = this.examinationData.items[index];
        if (!this.addOrUpdate && item.tableBlanks[blankIndex].id !== '') {
          item.tableBlanks[blankIndex].isDeleted = 1;
          this.selectedArray.push(item.tableBlanks[blankIndex]);
          this.editAnswer = this.selectedArray;
        }
        console.log(this.editAnswer, '选中数组');
        item.tableBlanks.splice(blankIndex, 1);
      },
      groupChanged(index, e) {
        const item = this.examinationData.items[index];
        item.correctAnswer = String.fromCharCode(65 + e);
        item.tableStatus.forEach((subItem, subIndex) => {
          subItem.optionIsAnswer = subIndex === e ? 1 : 0;
        });
      },
      groupChangedCheck(index, val) {
        const item = this.examinationData.items[index];
        const sortedVal = val.sort((a, b) => a - b);
        item.correctAnswer = sortedVal.map((index) => String.fromCharCode(65 + index)).join(',');
        item.tableCheckList.forEach((subItem, subIndex) => {
          subItem.optionIsAnswer = val.includes(subIndex) ? 1 : 0;
        });
      },
      singleHandle(index) {
        const item = this.examinationData.items[index];
        item.statusButton = true;
        item.tableCheckList = [];
        item.checkList = [];
      },

      multipleHandle(index) {
        const item = this.examinationData.items[index];
        item.statusButton = false;
        item.tableStatus = [];
        item.status = null;
      },
      // 查询+搜索课程列表
      //删除表单
      updateDeleteForm: function (ele) {
        if (this.examinationData.items.length === 1) {
          this.$message({
            message: '至少要留一个',
            type: 'warning',
            duration: 1000
          });
          return;
        }
        var index = this.examinationData.items.indexOf(ele);
        if (index !== -1) {
          this.examinationData.items.splice(index, 1);
          this.$message({
            message: '删除成功',
            type: 'warning',
            duration: 1000
          });
        }
        this.examinationData.items.forEach((item, idx) => {
          if (index - 1 === idx) {
            this.$set(item, 'hasPerformedAdd', false);
          }
        });
      },
      //增加表单
      addForm: function () {
        this.examinationData.items.push({
          questionNumber: '',
          questionType: '',
          questionText: '',
          status: null,
          statusButton: true,
          checkList: [],
          tableStatus: [],
          tableCheckList: [],
          tableBlanks: [],
          optionOrBlanks: null,
          analysis: ''
        });
        // 获取刚刚添加的那一项的索引
        const newItemIndex = this.examinationData.items.length - 2;
        // 将刚刚添加的那一项的hasPerformedAdd属性设置为true
        this.examinationData.items[newItemIndex].hasPerformedAdd = true;
        this.examinationData.items.forEach((val) => {
          // console.log(val + "新增表单");
        });
      },
      // 打开编辑阅读理解和完型填空
      openEdit() {
        if (!this.addOrUpdate) {
          findQuestionAPI({ id: this.editId }).then((res) => {
            if (res.success) {
              const data = res.data;
              this.setCommonFields(data);
              this.setTypeSpecificFields(data);
            }
          });
        }
      },
      setCommonFields(data) {
        this.examinationData.items[0].questionText = data.questionText;
        this.examinationData.items[0].questionType = data.questionType;
        this.examinationData.items[0].questionNumber = data.questionNumber;
        this.examinationData.items[0].analysis = data.analysis;
        this.questionEditId = data.id;
      },
      setTypeSpecificFields(data) {
        switch (data.questionType) {
          case 1:
            this.examinationData.items[0].optionOrBlanks = 1;
            if (data.choiceQuestionType === 0) {
              // 单选
              this.examinationData.items[0].statusButton = true;
              this.examinationData.items[0].tableStatus = data.superReadOptionVoList;
              const statusIndex = this.examinationData.items[0].tableStatus.findIndex((subItem) => subItem.optionIsAnswer === 1);
              this.examinationData.items[0].status = statusIndex;
              this.examinationData.items[0].correctAnswer = data.correctAnswer;
            } else if (data.choiceQuestionType === 1) {
              // 多选
              this.examinationData.items[0].statusButton = false;
              this.examinationData.items[0].tableCheckList = data.superReadOptionVoList;
              const statusIndices = this.examinationData.items[0].tableCheckList
                .map((subItem, subIndex) => (subItem.optionIsAnswer === 1 ? subIndex : -1))
                .filter((index) => index !== -1);
              this.examinationData.items[0].checkList = statusIndices;
              this.examinationData.items[0].correctAnswer = data.correctAnswer;
            }
            break;
          case 2:
            this.examinationData.items[0].optionOrBlanks = 2;
            this.examinationData.items[0].tableBlanks = data.superReadOptionVoList;
            this.examinationData.items[0].correctAnswer = data.correctAnswer;
            break;
        }
      },

      // 阅读理解和完型填空提交
      addActiveFun() {
        const that = this;
        let isValidationPassed = true;
        that.$refs['examinationRef'].validate((valid) => {
          if (!valid) {
            return false;
          }
          // 检查是否至少填写一项填空或选择题
          if (that.examinationData.items.some((item) => item.tableBlanks.length === 0 && item.tableStatus.length === 0 && item.tableCheckList.length === 0)) {
            isValidationPassed = false; // 设置校验状态为不通过
            that.$message.error('请至少填写一项填空或选择题');
            return;
          }
          // 检查是否有未保存的编辑状态
          that.examinationData.items.forEach((item) => {
            if (item.optionOrBlanks === 1) {
              if ((item.statusButton && item.tableStatus.some((row) => row.tableEditing)) || (!item.statusButton && item.tableCheckList.some((row) => row.tableEditing))) {
                that.$message.error('请点击单选表格确定选项内容');
                isValidationPassed = false; // 设置校验状态为不通过
                return;
              } else if (!item.statusButton && item.tableCheckList.some((row) => row.tableEditing)) {
                that.$message.error('请点击多选表格确定选项内容');
                isValidationPassed = false; // 设置校验状态为不通过
                return;
              }
            } else {
              if (item.tableBlanks.some((row) => row.tableEditing)) {
                that.$message.error('请点击填空表格确定选项内容');
                isValidationPassed = false; // 设置校验状态为不通过
                return;
              }
            }
          });
          if (
            that.examinationData.items.some((item) => {
              if (item.optionOrBlanks === 1) {
                if (
                  (item.statusButton && !item.tableStatus.some((row) => row.optionIsAnswer === 1)) ||
                  (!item.statusButton && !item.tableCheckList.some((row) => row.optionIsAnswer === 1))
                ) {
                  that.$message.error('请选择正确的答案');
                  isValidationPassed = false; // 设置校验状态为不通过
                  return true;
                }
              }
              return false;
            })
          ) {
            // 检查是否有未选择的正确答案
            isValidationPassed = false; // 设置校验状态为不通过
            return;
          }
          const paramsList = that.examinationData.items.map((item) => {
            let params = {
              articleId: that.articleId,
              courseId: that.courseId,
              choiceQuestionType: item.statusButton ? 0 : 1,
              questionNumber: item.questionNumber,
              questionType: item.questionType,
              questionText: item.questionText,
              correctAnswer: item.correctAnswer,
              analysis: item.analysis,
              superReadOptionCoList: []
            };
            if (item.optionOrBlanks === 1) {
              if (item.statusButton) {
                params.superReadOptionCoList = item.tableStatus;
              } else {
                params.superReadOptionCoList = item.tableCheckList;
              }
            } else {
              params.superReadOptionCoList = item.tableBlanks;
            }
            return params;
          });

          if (isValidationPassed) {
            console.log(paramsList, '新增表单校验数据');
            const loading = this.$loading({
              lock: true,
              text: '新增试题',
              spinner: 'el-icon-loading',
              background: 'rgba(0, 0, 0, 0.7)'
            });
            // 新增 api 请求
            addQuestionAPI(paramsList)
              .then((res) => {
                if (res.success) {
                  that.$message.success('添加成功');
                  this.$store.dispatch('delVisitedViews', this.$route);
                  that.$router.push({ path: '/course/testQuestionList' });
                  loading.close();
                }
              })
              .catch((err) => {
                loading.close();
              });
          } else {
            return;
          }
        });
      },
      // 编辑阅读理解和完型填空提交
      updateReadingFun() {
        let isValidationPassed = true;
        const that = this;
        that.$refs['examinationRef'].validate((valid) => {
          if (!valid) {
            return false;
          }
          // 检查是否至少填写一项填空或选择题
          if (that.examinationData.items.some((item) => item.tableBlanks.length === 0 && item.tableStatus.length === 0 && item.tableCheckList.length === 0)) {
            isValidationPassed = false; // 设置校验状态为不通过
            that.$message.error('请至少填写一项填空或选择题');
            return;
          }
          // 检查是否有未保存的编辑状态
          that.examinationData.items.forEach((item) => {
            if (item.optionOrBlanks === 1) {
              if ((item.statusButton && item.tableStatus.some((row) => row.tableEditing)) || (!item.statusButton && item.tableCheckList.some((row) => row.tableEditing))) {
                isValidationPassed = false; // 设置校验状态为不通过
                that.$message.error('请点击单选表格确定选项内容');
                return;
              } else if (!item.statusButton && item.tableCheckList.some((row) => row.tableEditing)) {
                isValidationPassed = false; // 设置校验状态为不通过
                that.$message.error('请点击多选表格确定选项内容');
                return;
              }
            } else {
              if (item.tableBlanks.some((row) => row.tableEditing)) {
                isValidationPassed = false; // 设置校验状态为不通过
                that.$message.error('请点击填空表格确定选项内容');
                return;
              }
            }
          });
          // 检查是否有未选择的正确答案
          if (
            that.examinationData.items.some((item) => {
              if (item.optionOrBlanks === 1) {
                if (
                  (item.statusButton && !item.tableStatus.some((row) => row.optionIsAnswer === 1)) ||
                  (!item.statusButton && !item.tableCheckList.some((row) => row.optionIsAnswer === 1))
                ) {
                  isValidationPassed = false; // 设置校验状态为不通过
                  that.$message.error('请选择正确的答案');
                  return true;
                }
              }
              return false;
            })
          ) {
            return;
          }
          const paramsList = that.examinationData.items.map((item) => {
            let params = {
              id: that.questionEditId,
              articleId: that.articleId,
              courseId: that.courseId,
              choiceQuestionType: item.statusButton ? 0 : 1,
              questionNumber: item.questionNumber,
              questionType: item.questionType,
              questionText: item.questionText,
              correctAnswer: item.correctAnswer,
              analysis: item.analysis,
              superReadOptionCoList: []
            };

            if (item.optionOrBlanks === 1) {
              if (item.statusButton) {
                if (this.editAnswer && isValidationPassed) {
                  if (Array.isArray(this.editAnswer)) {
                    item.tableStatus.push(...this.editAnswer);
                  } else {
                    item.tableStatus.push(this.editAnswer);
                  }
                  params.superReadOptionCoList = item.tableStatus;
                } else {
                  params.superReadOptionCoList = item.tableStatus;
                }
              } else {
                if (this.editAnswer && isValidationPassed) {
                  if (Array.isArray(this.editAnswer)) {
                    item.tableCheckList.push(...this.editAnswer);
                  } else {
                    item.tableCheckList.push(this.editAnswer);
                  }
                  params.superReadOptionCoList = item.tableCheckList;
                } else {
                  params.superReadOptionCoList = item.tableCheckList;
                }
              }
            } else {
              if (this.editAnswer && isValidationPassed) {
                console.log(this.editAnswer, '编辑填空');
                if (Array.isArray(this.editAnswer)) {
                  item.tableBlanks.push(...this.editAnswer);
                } else {
                  item.tableBlanks.push(this.editAnswer);
                }
                console.log(item.tableBlanks, '编辑填空');
                params.superReadOptionCoList = item.tableBlanks;
              } else {
                params.superReadOptionCoList = item.tableBlanks;
              }
            }

            return params;
          });

          if (isValidationPassed) {
            console.log(paramsList, '编辑表单校验数据');
            const loading = this.$loading({
              lock: true,
              text: '编辑试题',
              spinner: 'el-icon-loading',
              background: 'rgba(0, 0, 0, 0.7)'
            });
            editQuestionAPI(paramsList)
              .then((res) => {
                this.$message.success('编辑成功');
                this.$store.dispatch('delVisitedViews', this.$route);
                that.$router.push({ path: '/course/testQuestionList' });
                loading.close();
              })
              .catch((err) => {
                loading.close();
              });
          } else {
            return;
          }
        });
      },

      getList() {
        this.$store.dispatch('delVisitedViews', this.$route);
        this.$router.go(-1);
      }
    }
  };
</script>

<style>
  .lh36 {
    line-height: 36px;
    font-size: 14px;
  }

  .SearchForm {
    width: 100%;
    margin-bottom: 15px;
    background: #fff;
    border-radius: 6px;
    padding: 5px 0;
    padding-left: 5px;
    box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.2);
  }

  .btn-add {
    padding: 5px;
  }

  .red {
    color: red;
  }

  .green {
    color: green;
  }

  .course-table {
    text-align: center;
  }

  .course-table td,
  .course-table th {
    padding: 5px 0;
    text-align: center;
  }

  .course-table button {
    padding: 2px;
  }

  .icon-stop {
    float: left;
    display: inline-block;
    width: 12px;
    height: 12px;
    /* background: url('../../icons/stop.png') no-repeat top center/contain; */
  }

  .mt22 {
    margin-top: 22px;
  }

  .mb20 {
    margin-bottom: 20px;
  }

  .pd10 {
    padding: 10px;
  }
  .reading-textarea textarea {
    height: 200px;
  }
</style>
