//检测图片是否有效
const imgIsAvailable = function(url) {
  return new Promise((resolve) => {
      let img = new Image()
      img.onload = function () {
          if (this.complete == true){
              resolve(true)
              img = null
          }
      }
      img.onerror = function () {
          resolve(false)
          img = null
      }
      img.src = url
  })
}
export default async function(el, binding) {
  let url = binding.value,available = await imgIsAvailable(url)
  if (available) {
      el.setAttribute('src', url)
  }
}
